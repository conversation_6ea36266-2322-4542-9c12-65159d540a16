#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>
#include <string.h>
#include "mosquitto.h"
#include "source/mqtt.h"
#include "source/file_monitor.h"
#include <sqlite3.h>
#include "source/tcp.h"
#include <math.h>
#include <stdarg.h>
#include "source/activation.h"
#include "source/utils.h"
#include "source/file.h"
#include <signal.h>
#include <errno.h>
#include <netdb.h>
#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>

// 添加全局变量存储监控实例
static struct file_monitor *points_monitor = NULL;
static struct file_monitor *video_monitor = NULL;

//全局变量设备激活状态，默认为未激活状态
int activated_status = 0;
//tcp_client全局变量
struct tcp_client *g_tcp_client = NULL;
// 添加 mosquitto 全局变量
static struct mosquitto *g_mosq = NULL;

// 添加对utils.c中全局变量的外部引用
extern FILE *log_file;
extern FILE *error_file;

// 添加全局变量以控制程序运行
volatile bool running = true;

// 信号处理函数
void signal_handler(int signum) {
    print_with_time("接收到信号 %d，准备退出程序\n", signum);
    running = false;
}

// 添加新的回调函数处理 point_displacement.txt 的变化
void file_changed_callback(const char* file_path) {
    print_with_time("文件变更: %s\n", file_path);
    if (handle_file_change(file_path) != 0) {
        print_with_time("处理文件变更失败\n");
    }
}
// 添加新的回调函数处理 video_displacement.txt 的变化
void video_file_changed_callback(const char *file_path) {
    if (access(file_path, F_OK) != 0) {
        return;
    }

    // 等待1秒，确保文件写入完成
    //usleep(1000000);  // 1000000微秒 = 1秒

    printf("Reading video displacement file: %s\n", file_path);
    char *content = read_file(file_path);
    if (content == NULL) {
        print_error_with_time("Failed to read video displacement file: %s\n", file_path);
        return;
    }

    // 将内容编码为 base64
    size_t input_length = strlen(content);
    size_t base64_size;
    char *base64_data = base64_encode((const unsigned char *)content, input_length, &base64_size);
    
    if (base64_data != NULL) {
        // 发布消息
        publish_msg(DEVICE_ID, g_mosq, "videoDisplacement", base64_data, base64_size, NULL, 0, NULL);
        free(base64_data);
    }
    free(content);
}

// 添加网络检查函数
bool check_network_availability(const char *host, int port) {
    int sockfd;
    struct sockaddr_in servaddr;
    struct hostent *he;
    
    // 创建套接字
    sockfd = socket(AF_INET, SOCK_STREAM, 0);
    if (sockfd < 0) {
        print_error_with_time("套接字创建失败: %s\n", strerror(errno));
        return false;
    }
    
    // 设置超时
    struct timeval timeout;
    timeout.tv_sec = 2;  // 2秒超时
    timeout.tv_usec = 0;
    setsockopt(sockfd, SOL_SOCKET, SO_RCVTIMEO, (const char*)&timeout, sizeof(timeout));
    setsockopt(sockfd, SOL_SOCKET, SO_SNDTIMEO, (const char*)&timeout, sizeof(timeout));
    
    // 解析主机名
    he = gethostbyname(host);
    if (!he) {
        print_error_with_time("无法解析主机: %s\n", host);
        close(sockfd);
        return false;
    }
    
    // 设置服务器地址
    memset(&servaddr, 0, sizeof(servaddr));
    servaddr.sin_family = AF_INET;
    servaddr.sin_port = htons(port);
    memcpy(&servaddr.sin_addr, he->h_addr, he->h_length);
    
    // 尝试连接
    int result = connect(sockfd, (struct sockaddr*)&servaddr, sizeof(servaddr));
    close(sockfd);
    
    if (result < 0) {
        print_error_with_time("连接服务器失败: %s\n", strerror(errno));
        return false;
    }
    
    return true;
}

// 添加MQTT错误恢复函数
bool recover_mqtt_connection(struct mosquitto *mosq) {
    print_with_time("尝试恢复MQTT连接...\n");
    
    // 首先检查网络是否可达
    if (!check_network_availability(HOST, PORT)) {
        print_error_with_time("MQTT服务器不可达，网络可能不可用\n");
        return false;
    }
    
    // 断开之前的连接
    mosquitto_disconnect(mosq);
    
    // 等待一段时间
    usleep(500000);  // 500毫秒
    
    // 尝试重新连接
    int rc = mosquitto_reconnect(mosq);
    if (rc != MOSQ_ERR_SUCCESS) {
        print_error_with_time("MQTT重连失败: %s\n", mosquitto_strerror(rc));
        return false;
    }  
    // 重新订阅主题
    rc = mosquitto_subscribe(mosq, NULL, DOWN_TOPIC, 2);
    if (rc != MOSQ_ERR_SUCCESS) {
        print_error_with_time("重新订阅主题失败: %s\n", mosquitto_strerror(rc));
        return false;
    }
    
    print_with_time("MQTT连接恢复成功\n");
    return true;
}

int main(int argc, char *argv[])
{
    // 检查参数数量
    if (argc < 6) {
        fprintf(stderr, "用法: %s <程序目录> <设备ID> <主机> <MQTT端口> <TCP端口>\n", argv[0]);
        return 1;
    }

    // 设置程序目录
    strncpy(g_program_dir, argv[1], sizeof(g_program_dir) - 1);
    g_program_dir[sizeof(g_program_dir) - 1] = '\0';

    // 初始化文件路径
    init_file_paths();

    // 初始化日志文件
    reopen_log_file();

    if (LOG_FILE == NULL) {
        print_error_with_time("Failed to open log file");
        return 1;
    }
    if(ERROR_LOG_FILE == NULL){
        print_error_with_time("Failed to open error file");
        return 1;
    }
    // 解析配置文件
    if (parse_config_file(CFG_INI) < 0) {
        print_error_with_time("读取cfg.ini配置文件失败");
        // 继续执行，使用默认值
    } else {
        // 更新 MQTT 配置
        update_mqtt_config();
    }

    // 参数现在从argv[2]开始
    DEVICE_ID = argv[2];
    HOST = argv[3];
    sscanf(argv[4], "%d", &PORT);
    sscanf(argv[5], "%d", &TCP_PORT);
    strcat(UP_TOPIC, DEVICE_ID);
    strcat(DOWN_TOPIC, DEVICE_ID);
    strcat(RESPOND_TOPIC, DEVICE_ID);

    // 如果点位文件存在，先读取并插入数据库
    if (access(CHANGE_DATA_FILE, F_OK) == 0) {
        //print_with_time("Initial reading of displacement file...\n");
        file_changed_callback(CHANGE_DATA_FILE);
    }

    struct mosquitto *mosq, *mosq_sub;
            
    if(mosquitto_lib_init()){
        print_with_time("Init lib error!\n");
        exit(-1);
    }
    
    char name[20] = "demo_";
    strcat(name, DEVICE_ID);
    printf("name: %s\n", name);
    //创建一个新的 MQTT 客户端实例
    mosq = mosquitto_new(name, true, NULL);
    if(mosq == NULL){
        print_with_time("New sub_test error!\n");
        mosquitto_lib_cleanup();
        exit(-1);
    }

    g_mosq = mosq;
  //创建一个tcp的客户端实例
 // g_tcp_client = tcp_client_new(HOST, TCP_PORT);
 // printf("g_tcp_client: %s %d \n", g_tcp_client->server_ip, g_tcp_client->server_port   );
//  if(g_tcp_client == NULL){
//     print_with_time("New tcp_client error!\n");
//     mosquitto_lib_cleanup();
//     exit(-1);
// }

// 连接 TCP
// if (!tcp_client_connect(g_tcp_client)) {
//     print_with_time("%s,%d,TCP client connect failed!\n", g_tcp_client->server_ip, g_tcp_client->server_port);
//     tcp_client_destroy(g_tcp_client);
//     mosquitto_lib_cleanup();
//     exit(-1);
// } 
   // mosquitto_user_data_set(mosq, g_tcp_client);
    mosquitto_connect_callback_set(mosq, sub_connect_callback);
    mosquitto_disconnect_callback_set(mosq, sub_disconnect_callback);
    mosquitto_subscribe_callback_set(mosq, subscribe_callback);
    mosquitto_message_callback_set(mosq, message_callback);
    mosquitto_publish_callback_set(mosq, publish_callback);
    // 启动对 points_displacement.txt 的监控

    for (int retry = 0; retry < 3; retry++) {
        points_monitor = start_file_monitor(CHANGE_DATA_FILE, file_changed_callback);
        print_with_time("Starting file monitor for: %s\n", CHANGE_DATA_FILE);
        if (points_monitor) break;
        print_with_time("Retry %d/3...\n", retry + 1);
        if(retry == 2){
            print_with_time("Failed to start points displacement file monitor.\n");
        }
        sleep(1);
    }
    
    if (!points_monitor) {
        mosquitto_destroy(mosq);
        mosquitto_lib_cleanup();
        return 1;
    }

    // 启动对 video_displacement.txt 的监控
    video_monitor = start_file_monitor(VIDEO_DISPLACEMENT_FILE, video_file_changed_callback);
    
        // 启动对 video_displacement.txt 的监控
        for (int retry = 0; retry < 3; retry++) {
            video_monitor = start_file_monitor(VIDEO_DISPLACEMENT_FILE, video_file_changed_callback);
            if (video_monitor) break;
            print_with_time("Retry starting video monitor %d/3...\n", retry + 1);
            sleep(1);
        }

    if (!video_monitor) {
        print_with_time("Failed to start video displacement file monitor.\n");
    }
  // 检查并清理旧日志
    cleanup_old_logs();
    // 设置信号处理
    signal(SIGINT, signal_handler);
    signal(SIGTERM, signal_handler);   
    int ret = mosquitto_connect(mosq, HOST, PORT, KEEP_ALIVE);
    int reconnect_timeout = 0;//重连等待时间
    int max_reconnect_timeout = 10; // 最大重连等待时间(秒) 
    // 替换无限循环
    while(running) {
        if(ret) {
            print_with_time("连接服务器失败: %s\n", mosquitto_strerror(ret));          
            // 检查网络状态
            bool network_available = check_network_availability(HOST, PORT);
            if (!network_available) {
                print_error_with_time("网络连接不可用，等待恢复...\n");
                // 使用更长的等待时间
                reconnect_timeout = (reconnect_timeout == 0) ? 2 : 
                                  (reconnect_timeout * 2 > max_reconnect_timeout ? 
                                   max_reconnect_timeout : reconnect_timeout * 2);
            } else {
                // 网络正常但连接失败，使用标准退避算法
                reconnect_timeout = (reconnect_timeout == 0) ? 1 : 
                                  (reconnect_timeout * 2 > max_reconnect_timeout ? 
                                   max_reconnect_timeout : reconnect_timeout * 2);
            }           
            print_with_time("等待 %d 秒后重新连接...\n", reconnect_timeout);           
            // 分段休眠，允许信号中断
            for(int i = 0; i < reconnect_timeout && running; i++) {
                sleep(1);
            }        
            if(!running) break; // 如果收到退出信号，跳出循环            
            ret = mosquitto_connect(mosq, HOST, PORT, KEEP_ALIVE);
            continue;
        }
        
        // 使用有超时的loop，而不是-1无限阻塞
        int loop_result = mosquitto_loop(mosq, 500, 1);        
        // 添加更多错误处理逻辑
        if(loop_result != MOSQ_ERR_SUCCESS) {
            print_error_with_time("MQTT loop失败: %s\n", mosquitto_strerror(loop_result));
            
            // 尝试修复连接
            if (loop_result == MOSQ_ERR_NO_CONN || 
                loop_result == MOSQ_ERR_CONN_LOST ||
                loop_result == MOSQ_ERR_CONN_REFUSED) {
                
                bool recovered = recover_mqtt_connection(mosq);
                if (!recovered) {
                    ret = loop_result; // 设置重连标志
                } else {
                    // 连接已恢复，继续循环
                    continue;
                }
            } else {
                ret = loop_result; // 设置重连标志
            }          
            // 避免CPU占用过高
            usleep(100000); // 休眠100ms
        }
    }

    // 清理资源 - 这段代码现在可以正常执行了
    print_with_time("程序正在退出，清理资源...\n");
    stop_file_monitor(points_monitor);
    stop_file_monitor(video_monitor);
   // tcp_client_disconnect(g_tcp_client); // 先断开连接
    mosquitto_disconnect(mosq);         // 主动断开MQTT连接
    
    // mqtt连接成功后，检查设备激活状态
    if (!check_activation_status()) {
        print_with_time("设备未激活或已过期！\n");
       // tcp_client_destroy(g_tcp_client);
        mosquitto_lib_cleanup();
        return 0;
    }
    else{
        print_with_time("设备已激活！\n");
        activated_status = 1;
    }   
    close_log_files();// 关闭日志文件
    return 0;
}
