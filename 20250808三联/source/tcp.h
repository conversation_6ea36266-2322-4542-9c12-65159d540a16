#ifndef TCP_H
#define TCP_H

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include <netdb.h>
#include <errno.h>
#include <pthread.h>
#include "cJSON.h"
#include <stdbool.h>
#include <stdarg.h>
#include "utils.h"

#define BUFFER_SIZE 8192

// TCP 客户端结构体
struct tcp_client {
    int sockfd;
    char *server_ip;
    int server_port;
    bool connected;
    pthread_mutex_t mutex;
    void (*message_callback)(const char *message);
};

// 函数声明
struct tcp_client* tcp_client_new(const char *server_ip, int server_port);
bool tcp_client_connect(struct tcp_client *client);
bool tcp_client_send(struct tcp_client *client, const char *message);
void tcp_client_set_callback(struct tcp_client *client, void (*callback)(const char *));
void tcp_client_disconnect(struct tcp_client *client);
void tcp_client_destroy(struct tcp_client *client);

#endif 