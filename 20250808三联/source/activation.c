#include "activation.h"
#include <stdio.h>
#include <string.h>
#include <stdlib.h>
#include <unistd.h>
#include "utils.h"
#include "file.h"
#include <openssl/aes.h>
#include <openssl/evp.h>
#include "cJSON.h"
#include <errno.h>
#define AES_KEY "XDYGXDYGXDYGXDYG"
#define AES_IV  "2025202520252025"




static ActivationInfo g_activation_info;
static char g_expiry_date_str[32];


// 从配置文件读取注册码
const char* get_reg_code(void) {
    static char reg_code[32] = {0};
    if (reg_code[0] == '\0') {
        FILE* fp = fopen(CFG_INI, "r");
        if (fp) {
            char line[256];
            while (fgets(line, sizeof(line), fp)) {
                if (strstr(line, "RegCode")) {
                    sscanf(line, "RegCode = %s", reg_code);
                    break;
                }
            }
            fclose(fp);
        }
    }
    return reg_code;
}

// 初始化激活信息
static void init_activation_info(void) {
    memset(&g_activation_info, 0, sizeof(ActivationInfo));
    strncpy(g_activation_info.reg_code, get_reg_code(), sizeof(g_activation_info.reg_code) - 1);
    g_activation_info.start_time = time(NULL);
    g_activation_info.trial_end_time = g_activation_info.start_time + (TRIAL_PERIOD_DAYS * 24 * 60 * 60);
    g_activation_info.is_activated = 0;
    
    // 保存初始化的激活信息
    FILE* fp = fopen(ACTIVATION_FILE, "wb");
    if (fp) {
        fwrite(&g_activation_info, sizeof(ActivationInfo), 1, fp);
        fclose(fp);
    }
}

// 加载激活信息
static bool load_activation_info(void) {
    FILE* fp = fopen(ACTIVATION_FILE, "rb");
    if (!fp) {
        print_with_time("激活文件不存在，初始化激活信息");
        init_activation_info();
        return true;
    }
    
    size_t read_size = fread(&g_activation_info, sizeof(ActivationInfo), 1, fp);
    fclose(fp);
    
    return (read_size == 1);
}

// 检查激活状态
int check_activation_status(void) {
    if (!load_activation_info()) {
        print_error_with_time("加载激活信息失败\n");
        return STATUS_NEED_ACTIVATE;
    }
    
    time_t current_time = time(NULL);
    
    print_with_time("当前激活状态检查:\n");
    print_with_time("is_activated: %d\n", g_activation_info.is_activated);
    print_with_time("active_type: %d\n", g_activation_info.active_type);
    print_with_time("current_time: %s", ctime(&current_time));
    print_with_time("expire_time: %s", ctime(&g_activation_info.expire_time));
    
    if (g_activation_info.is_activated) {
        if (g_activation_info.active_type == 0) {
            print_with_time("永久激活\n");
            return STATUS_PERMANENT;
        }
        if (current_time < g_activation_info.expire_time) {
            print_with_time("在有效期内\n");
            return STATUS_ACTIVATED;
        }
        print_with_time("需要续期\n");
        return STATUS_NEED_RENEWAL;
    }

    print_with_time("未激活，检查试用期\n");
    if (current_time < g_activation_info.trial_end_time) {
        print_with_time("在试用期内\n");
        return STATUS_IN_TRIAL;
    }
    
    print_with_time("需要激活\n");
    return STATUS_NEED_ACTIVATE;
}

// 获取剩余试用天数
int get_remaining_trial_days(void) {
    if (!load_activation_info()) {
        return 0;
    }
    
    time_t current_time = time(NULL);
    if (current_time >= g_activation_info.trial_end_time) {
     
        return 0;
    }
    print_with_time("试用期结束时间：%s",ctime(&g_activation_info.trial_end_time));
    print_with_time("安装时间：%s",ctime(&g_activation_info.start_time));
    return (g_activation_info.trial_end_time - current_time) / (24 * 60 * 60);
}

// 获取过期时间字符串
const char* get_expiry_date_string(void) {
    if (!g_activation_info.is_activated) {
        return "未激活";
    }
    
    if (g_activation_info.active_type == 0) {
        return "永久有效";
    }
    
    struct tm *tm_info = localtime(&g_activation_info.expire_time);
    strftime(g_expiry_date_str, sizeof(g_expiry_date_str), 
             "%Y-%m-%d %H:%M:%S", tm_info);
    
    return g_expiry_date_str;
}

// 提取 Base64 部分
static char* extract_base64(const char* cryptojs_str) {
    // CryptoJS 输出格式：前面可能有 salt，中间有实际的 Base64 数据
    char* base64_start = NULL;
    char* base64_end = NULL;
    char* result = NULL;
    
    // 跳过可能的 salt 部分
    base64_start = strstr(cryptojs_str, "base64,");
    if (base64_start) {
        base64_start += 7;  // 跳过 "base64,"
    } else {
        base64_start = (char*)cryptojs_str;  // 假设整个字符串就是 Base64
    }
    
    // 找结束位置
    base64_end = strchr(base64_start, '"');
    if (!base64_end) {
        base64_end = base64_start + strlen(base64_start);
    }
    
    // 提取 Base64 部分
    size_t len = base64_end - base64_start;
    result = (char*)malloc(len + 1);
    if (result) {
        strncpy(result, base64_start, len);
        result[len] = '\0';
    }
    
    return result;
}

// 使用自定义 XOR 解密（简单的替代方案）
static char* simple_decrypt(const char* input) {
    size_t len = strlen(input);
    char* output = (char*)malloc(len + 1);
    if (!output) return NULL;
    
    const char* key = AES_KEY AES_IV;  // 组合密钥和IV
    size_t key_len = strlen(key);
    
    for (size_t i = 0; i < len; i++) {
        output[i] = input[i] ^ key[i % key_len];
    }
    output[len] = '\0';
    
    return output;
}

int verify_activation_code(const char* encrypted_data, int encrypted_data_len) {
    // 初始化密钥和IV
    unsigned char key[16] = {0};
    unsigned char iv[16] = {0};
    memcpy(key, AES_KEY, 16);
    memcpy(iv, AES_IV, 16);

    // 预留足够的缓冲区存放解密后的明文
    unsigned char decryptedtext[256] = {0};
    int decryptedtext_len = aes_decrypt(encrypted_data, encrypted_data_len, key, iv, decryptedtext);
    if (decryptedtext_len <= 0) {
        print_error_with_time("解密失败\n");
        return 0;
    }
    decryptedtext[decryptedtext_len] = '\0';

    print_with_time("解密后的数据: %s\n", decryptedtext);

    // 使用cJSON解析数据
    cJSON *root = cJSON_Parse((char *)decryptedtext);
    if (!root) {
        print_with_time("JSON解析失败\n");
        return 0;
    }

    // 获取deviceId和expiryDate
    cJSON *deviceId = cJSON_GetObjectItem(root, "deviceId");
    cJSON *expiryDate = cJSON_GetObjectItem(root, "expiryDate");
    
    if (!deviceId || !deviceId->valuestring || 
        !expiryDate || !expiryDate->valuestring) {
        print_with_time("缺少必要的JSON字段\n");
        cJSON_Delete(root);
        return 0;
    }

    print_with_time("设备ID: %s, 过期时间: %s\n", 
                    deviceId->valuestring, 
                    expiryDate->valuestring);

    // 验证设备ID
    // const char* reg_code = get_reg_code();
    // if (strcmp(deviceId->valuestring, reg_code) != 0) {
    //     print_with_time("设备ID不匹配\n");
    //     cJSON_Delete(root);
    //     return 0;
    // }

    // 解析过期时间
    struct tm expire_tm = {0};
    const char* date_str = expiryDate->valuestring;  // 格式: "20260327"
    
    // 解析YYYYMMDD格式
    int year, month, day;
    if (sscanf(date_str, "%4d%2d%2d", &year, &month, &day) != 3) {
        print_error_with_time("日期格式错误: %s\n", date_str);
        cJSON_Delete(root);
        return 0;
    }
      
    if (year < 2000 || year > 2100 || 
        month < 1 || month > 12 || 
        day < 1 || day > 31) {
        cJSON_Delete(root);
        return 0;
    }
    
    // 设置时间结构
    expire_tm.tm_year = year - 1900;
    expire_tm.tm_mon = month - 1;
    expire_tm.tm_mday = day;
    expire_tm.tm_hour = 23;
    expire_tm.tm_min = 59;
    expire_tm.tm_sec = 59;
    expire_tm.tm_isdst = -1;
    
    // 转换为时间戳
    g_activation_info.expire_time = mktime(&expire_tm);
    
    if (g_activation_info.expire_time == -1) {
        print_with_time("日期转换失败\n");
        cJSON_Delete(root);
        return 0;
    }
    
    // 验证转换结果
    char time_str[64];
    struct tm *check_tm = localtime(&g_activation_info.expire_time);
    if (!check_tm) {
        print_with_time("时间戳转换回本地时间失败\n");
        cJSON_Delete(root);
        return 0;
    }
    
    strftime(time_str, sizeof(time_str), "%Y-%m-%d %H:%M:%S", check_tm);
    print_with_time("最终设置的过期时间: %s\n", time_str);
    
    // 计算激活类型
    time_t now = time(NULL);
    double diff_seconds = difftime(g_activation_info.expire_time, now);
    int years = (int)(diff_seconds / (365.25 * 24 * 60 * 60));
    
    print_with_time("距离过期还有 %.2f 天\n", diff_seconds / (24 * 60 * 60));
    
    // 设置激活信息
    g_activation_info.active_time = now;
    strncpy(g_activation_info.active_code, (char *)decryptedtext, 
            sizeof(g_activation_info.active_code) - 1);
    
    // 根据年数设置激活类型
    if (years >= 99) {
        g_activation_info.active_type = 0;  // 永久激活
    } else if (years >= 5) {
        g_activation_info.active_type = 5;
    } else if (years >= 4) {
        g_activation_info.active_type = 4;
    } else if (years >= 3) {
        g_activation_info.active_type = 3;
    } else if (years >= 2) {
        g_activation_info.active_type = 2;
    } else {
        g_activation_info.active_type = 1;
    }
    
    g_activation_info.is_activated = 1;

    print_with_time("激活类型: %d年%s\n", 
                    g_activation_info.active_type == 0 ? 99 : g_activation_info.active_type,
                    g_activation_info.active_type == 0 ? "(永久)" : "");

    // 保存激活信息
    FILE* fp = fopen(ACTIVATION_FILE, "wb");
    if (!fp) {
        print_with_time("保存激活信息失败\n");
        cJSON_Delete(root);
        return 0;
    }

    int result = (fwrite(&g_activation_info, sizeof(ActivationInfo), 1, fp) == 1);
    fclose(fp);

    if (result) {
        print_with_time("设备已成功激活，有效期至：%s\n", time_str);
    } else {
        print_with_time("保存激活信息时发生错误\n");
    }

    cJSON_Delete(root);
    return result;
}

// 错误处理函数
void handleErrors(void) {
    ERR_print_errors_fp(stderr);
    abort();
}

// AES-128-CBC 解密函数
int aes_decrypt(const unsigned char *ciphertext, int ciphertext_len,
                const unsigned char *key, const unsigned char *iv,
                unsigned char *plaintext)
{
    EVP_CIPHER_CTX *ctx;
    int len;
    int plaintext_len;

    if (!(ctx = EVP_CIPHER_CTX_new()))
        handleErrors();

    if (1 != EVP_DecryptInit_ex(ctx, EVP_aes_128_cbc(), NULL, key, iv))
        handleErrors();

    if (1 != EVP_DecryptUpdate(ctx, plaintext, &len, ciphertext, ciphertext_len))
        handleErrors();
    plaintext_len = len;

    if (1 != EVP_DecryptFinal_ex(ctx, plaintext + len, &len))
        handleErrors();
    plaintext_len += len;

    EVP_CIPHER_CTX_free(ctx);

    return plaintext_len;
}

char* get_activation_info(void) {
    // 使用动态分配的内存而不是静态数组
    char* activation_info = malloc(1024);  // 分配足够大的空间
    if (!activation_info) {
        print_error_with_time("内存分配失败\n");
        return strdup("内存分配失败");
    }
    
    // 读取激活信息
    FILE* fp = fopen(ACTIVATION_FILE, "rb");
    if (fp) {
        if (fread(&g_activation_info, sizeof(ActivationInfo), 1, fp) == 1) {
            // 打印原始激活信息结构体内容
            print_with_time("激活信息结构体内容:\n");
            print_with_time("注册码: %s\n", g_activation_info.reg_code);
            print_with_time("安装时间: %s", ctime(&g_activation_info.start_time));
            print_with_time("试用期结束时间: %s", ctime(&g_activation_info.trial_end_time));
            print_with_time("激活时间: %s", ctime(&g_activation_info.active_time));
            print_with_time("过期时间: %s", ctime(&g_activation_info.expire_time));
            print_with_time("激活类型: %d\n", g_activation_info.active_type);
            print_with_time("是否激活: %d\n", g_activation_info.is_activated);
            print_with_time("激活码: %s\n", g_activation_info.active_code);
            
            // 格式化激活信息到字符串
            snprintf(activation_info, 1024,
                    "注册码: %s\n"
                    "安装时间: %s"
                    "试用期结束: %s"
                    "激活时间: %s"
                    "过期时间: %s"
                    "激活类型: %d年%s\n"
                    "激活状态: %s\n"
                    "激活码: %s\n",
                    g_activation_info.reg_code,
                    ctime(&g_activation_info.start_time),
                    ctime(&g_activation_info.trial_end_time),
                    ctime(&g_activation_info.active_time),
                    ctime(&g_activation_info.expire_time),
                    g_activation_info.active_type == 0 ? 99 : g_activation_info.active_type,
                    g_activation_info.active_type == 0 ? "(永久)" : "",
                    g_activation_info.is_activated ? "已激活" : "未激活",
                    g_activation_info.active_code);
        } else {
            print_with_time("读取激活信息失败\n");
            snprintf(activation_info, 1024, "读取激活信息失败");
        }
        fclose(fp);
    } else {
        print_with_time("无法打开激活信息文件\n");
        snprintf(activation_info, 1024, "未找到激活信息");
    }
    
    return activation_info;  // 调用者负责释放这个内存
}
