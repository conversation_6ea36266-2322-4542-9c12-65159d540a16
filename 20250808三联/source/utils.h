#ifndef UTILS_H
#define UTILS_H

#include <stdio.h>
#include <time.h>
#include <stdarg.h>

// 通用缓冲区大小定义
#define MAX_ROUND_STR 32  // 分段信息缓冲区大小
#define MAX_CONFIG_LEN 128  // 配置项最大长度

// 设备配置信息结构体
typedef struct {
    // 设备信息
    char ssid[MAX_CONFIG_LEN];
    char camera[MAX_CONFIG_LEN];
    char app_version[MAX_CONFIG_LEN];
    char hardware_version[MAX_CONFIG_LEN];
    int device_type;
    
    // 计算控制
    char calculation_frequency[MAX_CONFIG_LEN];
    
    // 传输控制
    char transmission_frequency[MAX_CONFIG_LEN];
    int image_protocol;
    char server_ip[MAX_CONFIG_LEN];
    int mqtt_port;
    int tcp_port;
    char backup_ip[MAX_CONFIG_LEN];
    int backup_mqtt_port;
    int backup_tcp_port;
    
    // 电机控制
    int max_speed;
    
    // 激活信息
    char reg_code[MAX_CONFIG_LEN];
    char expiry_date[MAX_CONFIG_LEN];
    
    // 设备状态
    char ex_voltage[MAX_CONFIG_LEN];
    char ex_storage[MAX_CONFIG_LEN];
    char apn[MAX_CONFIG_LEN];
    char network_signal[MAX_CONFIG_LEN];
} DeviceConfig;

// 全局配置变量声明
extern DeviceConfig g_device_config;

// 打印带时间戳的日志信息
void print_with_time(const char* format, ...);
void print_error_with_time(const char* format, ...);
// 重新打开日志文件
int reopen_log_file(void);

// 关闭日志文件
void close_log_files(void);

// 解析配置文件
int parse_config_file(const char* config_file);

// 打印设备配置信息
void print_device_config(void);

// 声明 MQTT 配置更新函数
void update_mqtt_config(void);

// UTILS_H

//重启设备
void reboot_device(void);

//获取设备信息
char* get_device_info(void);

// 清理旧日志文件
void cleanup_old_logs(void);

#endif