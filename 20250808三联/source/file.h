#ifndef FILE_H
#define FILE_H

#include <limits.h>

#define KEEP_ALIVE 5
#define IMAGE_TYPE "image"
#define CHANGE_TYPE "displacement"
#define TRIAL_PERIOD_DAYS 30 //试用期天数

// 声明全局变量用于存储动态路径
extern char g_program_dir[PATH_MAX];
extern char g_datafile_dir[PATH_MAX];

// 动态路径变量声明
extern char DIC_FOLDER[PATH_MAX];
extern char IMAGE_LIST_FILE[PATH_MAX];
extern char CAPTURE_SINGLE[PATH_MAX];
extern char HOST_PORT_FILE[PATH_MAX];
extern char POINT_LIST_FILE[PATH_MAX];
extern char IMAGE_FOLDER[PATH_MAX];
extern char MASTER_IMAGE_FOLDER[PATH_MAX];
extern char MULTIMASTER_IMAGE_FOLDER[PATH_MAX];
extern char CHANGE_DATA_FILE[PATH_MAX];
extern char VIDEO_DISPLACEMENT_FILE[PATH_MAX];
extern char LOG_FILE[PATH_MAX];
extern char ERROR_LOG_FILE[PATH_MAX];
extern char mqttDemo[PATH_MAX];
extern char HIMIX_PUB_LOG_FILE[PATH_MAX];
extern char LOG_DIR[PATH_MAX];
extern char CFG_INI[PATH_MAX];
extern char ACTIVATION_FILE[PATH_MAX];
extern char DB_PATH[PATH_MAX];
extern char REBOOT_SCRIPT[PATH_MAX];

// 函数声明
void init_file_paths(void);

#endif // FILE_H