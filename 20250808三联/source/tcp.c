#include "tcp.h"
#include <time.h>

// 添加函数前向声明
static void *receive_thread(void *arg);

struct tcp_client* tcp_client_new(const char *server_ip, int server_port) {
    struct tcp_client *client = malloc(sizeof(struct tcp_client));
    if (!client) return NULL;
    
    client->server_ip = strdup(server_ip);
    client->server_port = server_port;
    client->connected = false;
    client->message_callback = NULL;
    pthread_mutex_init(&client->mutex, NULL);
    
    return client;
}

bool tcp_client_connect(struct tcp_client *client) {
    client->sockfd = socket(AF_INET, SOCK_STREAM, 0);
    if (client->sockfd < 0) {
        print_with_time("%s,%d,socket creation failed\n", client->server_ip, client->server_port);
        return false;
    }
    
    struct sockaddr_in server_addr;
    memset(&server_addr, 0, sizeof(server_addr));
    server_addr.sin_family = AF_INET;
    server_addr.sin_port = htons(client->server_port);
    
    // 添加DNS解析调试信息
    struct hostent *he = gethostbyname(client->server_ip);
    if (he == NULL) {
        print_with_time("%s,%d,DNS resolution failed: %s\n", 
            client->server_ip, client->server_port, hstrerror(h_errno));
        return false;
    }
    
    // 打印解析到的IP地址
    char ip_str[INET_ADDRSTRLEN];
    inet_ntop(AF_INET, he->h_addr, ip_str, sizeof(ip_str));
    print_with_time("Resolved IP: %s\n", ip_str);
    
    // 使用解析后的IP地址
    memcpy(&server_addr.sin_addr, he->h_addr, he->h_length);
    
    if (connect(client->sockfd, (struct sockaddr *)&server_addr, sizeof(server_addr)) < 0) {
        print_with_time("%s,%d,connection failed: %s\n", 
            client->server_ip, client->server_port, strerror(errno));
        close(client->sockfd);
        return false;
    }
    
    client->connected = true;
    //print_with_time("%s,%d,Successfully connected\n", client->server_ip, client->server_port);
    
    // 创建接收线程
    pthread_t tid;
    pthread_create(&tid, NULL, receive_thread, client);
    pthread_detach(tid);
    
    return true;
}

bool tcp_client_send(struct tcp_client *client, const char *message) {
    if (!client->connected) return false;
    
    size_t total_len = strlen(message);
    size_t chunk_size = 1024 * 4; // 每次发送4KB
    size_t sent = 0;
    
    pthread_mutex_lock(&client->mutex);
    
    while (sent < total_len) {
        size_t remaining = total_len - sent;
        size_t to_send = (remaining < chunk_size) ? remaining : chunk_size;
        
        int ret = send(client->sockfd, message + sent, to_send, 0);
        if (ret <= 0) {
            pthread_mutex_unlock(&client->mutex);
            return false;
        }
        
        sent += ret;
        usleep(1000); // 添加1ms延时，避免发送过快
    }
    
    pthread_mutex_unlock(&client->mutex);
    return true;
}

void tcp_client_set_callback(struct tcp_client *client, void (*callback)(const char *)) {
    client->message_callback = callback;
}

void tcp_client_disconnect(struct tcp_client *client) {
    if (client->connected) {
        client->connected = false;
        close(client->sockfd);
    }
}

void tcp_client_destroy(struct tcp_client *client) {
    if (!client) return;
    
    tcp_client_disconnect(client);
    free(client->server_ip);
    pthread_mutex_destroy(&client->mutex);
    free(client);
}

static void *receive_thread(void *arg) {
    struct tcp_client *client = (struct tcp_client *)arg;
    char buffer[BUFFER_SIZE];
    
    while (client->connected) {
        memset(buffer, 0, BUFFER_SIZE);
        int n = recv(client->sockfd, buffer, BUFFER_SIZE-1, 0);
        if (n <= 0) {
            print_with_time("Connection closed by server\n");
            client->connected = false;
            break;
        }
        
        if (client->message_callback) {
            client->message_callback(buffer);
        }
    }
    
    return NULL;
}