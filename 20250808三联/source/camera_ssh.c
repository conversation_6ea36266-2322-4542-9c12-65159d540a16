#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <signal.h>
#include <sys/types.h>
#include <time.h>
#include <sys/stat.h>
#include <errno.h>
#include "file.h"
#include "camera_ssh.h"


#define MAX_LINE 256
#define SSH_TIMEOUT 900  // 15分钟 = 900秒

// 获取配置文件中的相机类型
char* get_camera_type() {
    FILE *fp;
    char line[MAX_LINE];
    static char camera_type[10];
    
    // 打印当前进程的用户ID和组ID
    print_with_time("当前进程: UID=%d, GID=%d\n", getuid(), getgid());
    
    // 检查文件状态
    struct stat file_stat;
    if (stat(CFG_INI, &file_stat) == 0) {
        print_with_time("配置文件信息:\n");
        print_with_time("所有者: UID=%d\n", file_stat.st_uid);
        print_with_time("组: GID=%d\n", file_stat.st_gid);
        print_with_time("权限: %o\n", file_stat.st_mode & 0777);
    }
    
    // 尝试打开文件并读取内容
    print_with_time("尝试打开配置文件: %s\n", CFG_INI);
    fp = fopen(CFG_INI, "r");
    if (fp == NULL) {
        print_with_time("打开文件失败: %s\n", strerror(errno));
        return NULL;
    }
    
    // 读取文件的前几行进行调试
    int line_count = 0;
    print_with_time("文件内容预览:\n");
    while (fgets(line, MAX_LINE, fp) && line_count < 5) {
        print_with_time("行 %d: %s", ++line_count, line);
    }
    
    // 重置文件指针到开始
    rewind(fp);
    
    // 查找相机类型
    while (fgets(line, MAX_LINE, fp)) {
        if (strncmp(line, "Camera = ", 9) == 0) {
            strncpy(camera_type, line + 9, sizeof(camera_type) - 1);
            camera_type[strcspn(camera_type, "\n")] = 0;
            print_with_time("找到相机类型: %s\n", camera_type);
            fclose(fp);
            return camera_type;
        }
    }
    
    print_with_time("未找到相机类型配置，使用默认值 JY\n");
    fclose(fp);
    strcpy(camera_type, "JY");
    return camera_type;
}

// 启动SSH隧道
int start_ssh_tunnel() {
    char *camera_type = get_camera_type();
    if (camera_type == NULL) {
        print_with_time("获取相机类型失败\n");
        return 0;  // 失败
    }
    
    char command[512];
    const char *target_ip = strcmp(camera_type, "JY") == 0 ? "*************" : "*************";
    
    snprintf(command, sizeof(command),
        "ssh -i /home/<USER>/id_ed25519 "
        "-o TCPKeepAlive=yes "
        "-o ServerAliveInterval=30 "
        "-o ServerAliveCountMax=5 "
        "-C -R 0.0.0.0:9000:%s:80 "
        "root@*********** -p 2222 -Nf",
        target_ip);
    
    int result = system(command);
    if (result != 0) {
        ("SSH隧道启动失败\n");
        return 0;  // 失败
    }
    
    print_with_time("SSH隧道已启动，目标IP: %s\n", target_ip);
    
    // 设置定时器在15分钟后关闭
    pid_t pid = fork();
    if (pid < 0) {
        printf("创建定时器进程失败\n");
        stop_ssh_tunnel();
        return 0;  // 失败
    }
    if (pid == 0) {
        sleep(SSH_TIMEOUT);
        system("pkill -f 'ssh.*9000'");
        exit(0);
    }
    
    return 1;  // 成功
}

// 停止SSH隧道
void stop_ssh_tunnel() {
    system("pkill -f 'ssh.*9000'");
    printf("SSH隧道已关闭\n");
}

