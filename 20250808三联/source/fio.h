#ifndef FIO_H
#define FIO_H

#include<stdio.h>
#include<stdlib.h>
#include<string.h>
#include <dirent.h> // 包含目录操作相关的头文件
#include <unistd.h> // 包含系统调用相关的头文件
#include <stdbool.h>
#include <sqlite3.h>
#include "utils.h"
#include "file.h"
#include <time.h>
#include <math.h>
#include <pthread.h>
#include <cjson/cJSON.h>
#include <sys/wait.h>  // for waitpid
#include <openssl/evp.h>  // for EVP_DecodeBlock

// 如果DT_REG未定义，定义它
#ifndef DT_REG
#define DT_REG 8  // 普通文件的常量值通常为8
#endif

#define MAX_SIZE 1024
#define MAX_IP_SUM 50
#define MAX_HOST_LENGTH 128  // 增加主机名最大长度
struct capture_thread_data {
    char *params;
    bool result;
    pthread_mutex_t mutex;
    pthread_cond_t cond;
    bool completed;
};

// 在文件开头的函数声明部分添加
time_t convert_time_to_timestamp(const char* time_str);
time_t convert_formatted_time_to_timestamp(const char* time_str);

// 读取整个文件
char* read_file(const char* file_name)
{   

	FILE* fp = fopen(file_name, "r");
  
	if (fp == NULL)
	{
		return NULL;
	}

	//文件标记定位到末尾
	fseek(fp, 0, SEEK_END);
	//获取文件长度
	size_t length = ftell(fp);
	//文件标记定位到开头
	fseek(fp, 0, SEEK_SET);

	char *result;
	result = (char*)malloc((length + 1)* sizeof(char));
	if (result == NULL) 
	{
		fclose(fp);
		print_error_with_time("Memory allocation error");
		return NULL;
	}
	//读取文件内容
	fread(result, sizeof(char), length, fp);

	//添加字符串结束符
	result[length] = '\0';

	fclose(fp);
	return result;
}
//读取文件后面50行
char* read_last_50_lines(const char* file_name)
{
    FILE* fp = fopen(file_name, "r");
    if (fp == NULL) {
        print_with_time("Error opening file: %s\n", file_name);
        return NULL;
    }

    // 创建一个循环缓冲区来存储最后50行
    char* lines[50] = {NULL};
    size_t line_lengths[50] = {0};
    int current = 0;
    int count = 0;
    char buffer[MAX_SIZE];

    // 读取文件的每一行
    while (fgets(buffer, sizeof(buffer), fp)) {
        size_t len = strlen(buffer);
        
        // 释放将被覆盖的行的内存
        if (lines[current] != NULL) {
            free(lines[current]);
        }
        
        // 为新行分配内存并复制内容
        lines[current] = (char*)malloc(len + 1);
        if (lines[current] == NULL) {
            print_with_time("Memory allocation failed\n");
            fclose(fp);
            // 清理已分配的内存
            for (int i = 0; i < 50; i++) {
                if (lines[i] != NULL) {
                    free(lines[i]);
                }
            }
            return NULL;
        }
        
        strcpy(lines[current], buffer);
        line_lengths[current] = len;
        
        current = (current + 1) % 50;
        if (count < 50) count++;
    }
    
    fclose(fp);

    // 计算需要的总内存大小
    size_t total_size = 1; // 为最后的 \0 预留空间
    for (int i = 0; i < count; i++) {
        total_size += line_lengths[(current + i) % 50];
    }

    // 分配结果缓冲区
    char* result = (char*)malloc(total_size);
    if (result == NULL) {
        print_with_time("Memory allocation failed\n");
        // 清理已分配的内存
        for (int i = 0; i < 50; i++) {
            if (lines[i] != NULL) {
                free(lines[i]);
            }
        }
        return NULL;
    }

    // 拼接最后50行（或更少）
    result[0] = '\0';
    for (int i = 0; i < count; i++) {
        strcat(result, lines[(current + i) % 50]);
    }

    // 清理行缓冲区
    for (int i = 0; i < 50; i++) {
        if (lines[i] != NULL) {
            free(lines[i]);
        }
    }

    return result;
}
// 清除文件
bool clear_file(const char* file_name)
{
    FILE* fp = fopen(file_name, "w");
    if (fp == NULL) {
        print_with_time("Error opening file for clearing: %s\n", file_name);
        return false;
    }
    fputs("", fp);  // 写入空字符串
    print_with_time("File cleared successfully: %s\n", file_name);
    return true;
}


// 修改返回类型为 char*，这样可以返回字符串形式的文件大小
char* get_file_size(const char* file_name)
{
    FILE* fp = fopen(file_name, "r");
    if (fp == NULL)
    {
        print_with_time("Error opening file: %s\n", file_name);
        return "0";
    }
    
    fseek(fp, 0, SEEK_END);
    long size = ftell(fp);
    fclose(fp);
    
    // 分配内存存储字符串形式的文件大小
    char* size_str = (char*)malloc(32);
    if (size_str == NULL) {
        return "0";
    }
    
    // 将文件大小转换为可读格式
    if (size < 1024) {
        sprintf(size_str, "%ld B", size);
    } else if (size < 1024*1024) {
        sprintf(size_str, "%.2f KB", size/1024.0);
    } else if (size < 1024*1024*1024) {
        sprintf(size_str, "%.2f MB", size/(1024.0*1024.0));
    } else {
        sprintf(size_str, "%.2f GB", size/(1024.0*1024.0*1024.0));
    }
    
    return size_str;
}
// 读取文件第一行
char* read_first_row(const char* file_name)
{
	
	FILE* fp = fopen(file_name, "r");
	if (fp == NULL)
	{
		print_error_with_time("Error opening file");
		return NULL;
	}
	char *result = (char*)malloc(MAX_SIZE* sizeof(char));
	fgets(result , MAX_SIZE, fp);
	fclose(fp);
	if(result[strlen(result)-1]=='\n')
		result[strlen(result )-1]='\0';
	else
		result[strlen(result)] = '\0';

	return result;
}

// 读取文件最后一行
char* read_last_row(const char* file_name)
{
    FILE* fp = fopen(file_name, "r");
    if (fp == NULL)
    {
        print_error_with_time("Error opening file");
        return NULL;
    }
    char *result = (char*)malloc(MAX_SIZE* sizeof(char));
    char *row_data = (char*)malloc(MAX_SIZE* sizeof(char));
    while(fgets(row_data, MAX_SIZE, fp) != NULL)
        result = row_data;
    
    fclose(fp);
    if(result[strlen(result)-1]=='\n')
        result[strlen(result )-1]='\0';
    else
        result[strlen(result)] = '\0';

    return result;
}

// 删除文件夹下的所有文件
bool remove_all_file(char* folder_name)
{
	char file_path[MAX_SIZE];
	DIR* dir = opendir(folder_name);
	if (dir == NULL) 
	{
        print_error_with_time("Error opening directory");
        return false;
    }

    struct dirent* entry;
    while ((entry = readdir(dir)) != NULL) 
    {
    	// DT_REG 表示普通文件
        if (entry->d_type == DT_REG) 
        { 
        	// 拼接文件名称
            strcat(strcpy(file_path,folder_name), entry->d_name);
            
            if (remove(file_path) == 0) 
                print_with_time("Removed: %s\n", file_path);
			else
                print_error_with_time("Error removing file");
        }
    }

    closedir(dir);
    return true;
}

// 返回文件夹下第一个文件的文件名
char* get_first_file_name(char* folder_name)
{
    char file_path[MAX_SIZE];
    DIR* dir = opendir(folder_name);
    if (dir == NULL) 
    {
        print_error_with_time("Error opening directory");
        return NULL;
    }

    struct dirent* entry;
    while ((entry = readdir(dir)) != NULL) 
    {
        // DT_REG 表示普通文件
        if (entry->d_type == DT_REG) 
        { 
            // 拼接文件名称
            strcat(strcpy(file_path,folder_name), entry->d_name);
            closedir(dir);
            // 主影像文件夹下只有一张图像，遍历的第一个结果直接返回即可
            return entry->d_name;
        }
    }
    closedir(dir);
    return NULL;
}

// 复制图片
bool copy_image(const char* from_image_name, const char* to_image_name) 
{
	FILE* fp;
    size_t image_size;
    unsigned char* image_data;

    // 打开图片文件
    fp = fopen(from_image_name, "rb");
    if (fp == NULL) 
    {
        print_error_with_time("Error opening file");
        return false;
    }

    // 获取图片文件大小
    fseek(fp, 0, SEEK_END);
    image_size = ftell(fp);
    fseek(fp, 0, SEEK_SET);

    // 分配内存来存储图片数据
    image_data = (unsigned char*)malloc(image_size);
    if (image_data == NULL) 
    {
        print_error_with_time("Memory allocation error");
        fclose(fp);
        return false;
    }

    // 读取图片数据
    fread(image_data, 1, image_size, fp);
    fclose(fp);

    fp = fopen(to_image_name, "wb");
    if (fp == NULL) {
        print_error_with_time("Error opening file");
        return false;
    }
    fwrite(image_data, 1, image_size, fp);

    fclose(fp);
    free(image_data);

    return true;
}

// 向文件末尾插入一行数据
bool write_last_row(const char* file_name, char* data)
{
    FILE* fp = fopen(file_name, "a+");
    if (fp == NULL) {
        print_error_with_time("Error opening file");
        return false;
    }
    fseek(fp, 0, SEEK_END);
    if (ftell(fp) > 0) 
    {
        fseek(fp, -1, SEEK_CUR);
        if (fgetc(fp) != '\n')
            fputc('\n', fp);
    }
    fwrite(data, 1, strlen(data), fp);

    fseek(fp, 0, SEEK_END);
    if (fgetc(fp) != '\n')
        fputc('\n', fp);

    fclose(fp);
    return true;
}

// 获取文件末尾最后一行序号，并带序号向末尾插入一行数据
bool write_last_row_include_index(const char* file_name, char* data)
{
    FILE* fp = fopen(file_name, "a+");
    if (fp == NULL) {
        print_error_with_time("Error opening file");
        return false;
    }
    char result[MAX_SIZE];
    fseek(fp, 0, SEEK_END);
    long size = ftell(fp);
    fseek(fp, 0, SEEK_SET);
    if (size == 0) {
        // 将数字和字符串拼接到 result 中，如果文件为空，从序号1开始
        snprintf(result, sizeof(result), "1\t%s", data);
    } else {
        char last_line[MAX_SIZE];
        while(fgets(last_line, MAX_SIZE, fp) != NULL)
            ;
        int count;
        // 获取文件最后一行的序号
        sscanf(last_line, "%d", &count);
        // 当前序号为文件最后一行序号+1
        snprintf(result, sizeof(result), "%d\t%s", count+1, data);
    }
    fclose(fp);
    // 写入文件最后一行
    write_last_row(file_name, result);
    return true;
}

// 向文件重新写入数据
bool write_file(const char* file_name, char* data)
{
    FILE* fp = fopen(file_name, "w");
    if (fp == NULL) {
        print_error_with_time("Error opening file");
        return false;
    }
    fwrite(data, 1, strlen(data), fp);
    fclose(fp);
    return true;
}


// 存储IP、端口号
struct Ip
{
        char host[MAX_HOST_LENGTH];  // 使用新的更大的缓冲区大小
        int port;
};

int ip_count = 0;
// 获取mqtt服务器全部IP、端口号
struct Ip* get_host_port(const char* file_name)
{
    char data[MAX_IP_SUM][MAX_SIZE] = { '\0' };
    FILE* fp = fopen(file_name, "r");
    if (fp == NULL) {
        print_error_with_time("Error opening file");
        return NULL;
    }

    int i = 0;
    while (i < MAX_IP_SUM && fgets(data[i], MAX_SIZE, fp) != NULL) {
        // 去除末尾的回车换行符和其他不可见字符
        size_t len = strlen(data[i]);
        while (len > 0 && (data[i][len-1] == '\n' || 
                          data[i][len-1] == '\r' || 
                          data[i][len-1] < 32)) {
            data[i][--len] = '\0';
        }
        
        // 检查行是否为空
        if (len > 0) {
            i++;
        }
    }
    ip_count = i;

    struct Ip* result = malloc(sizeof(struct Ip) * MAX_IP_SUM);
    if (result == NULL) {
        fclose(fp);
        return NULL;
    }
    memset(result, 0, sizeof(struct Ip) * MAX_IP_SUM);

    for (i = 0; i < ip_count; i++) {
        char *host_str = strtok(data[i], " \t");
        char *port_str = strtok(NULL, " \t");
        
        if (host_str && port_str) {
            // 使用更大的缓冲区大小复制主机名
            strncpy(result[i].host, host_str, MAX_HOST_LENGTH - 1);
            result[i].host[MAX_HOST_LENGTH - 1] = '\0';
            
            // 转换端口号
            result[i].port = atoi(port_str);
            
            // 打印读取到的配置进行验证
            print_with_time("读取到服务器配置: %s:%d\n", 
                result[i].host, result[i].port);
        }
    }

    fclose(fp);
    return result;
}

char* concatenate_files(const char *path) {
    struct dirent *entry;
    DIR *dp = opendir(path);
    if (dp == NULL) {
        print_error_with_time("opendir");
        return NULL;
    }

    char *result = NULL;
    size_t result_size = 0;

    // 为result分配初始内存
    result = malloc(1);
    if (result == NULL) {
        print_error_with_time("malloc");
        closedir(dp);
        return NULL;
    }
    result[0] = '\0';

    while ((entry = readdir(dp))) {
        // 跳过 . 和 .. 目录
        if (entry->d_name[0] != '.') {
            size_t entry_len = strlen(entry->d_name);
            result = realloc(result, result_size + entry_len + 2); 
            if (result == NULL) {
                print_error_with_time("realloc");
                closedir(dp);
                return NULL;
            }
            strcat(result, entry->d_name);
            strcat(result, "\n");
            result_size += entry_len + 1;
        }
    }

    closedir(dp);
    return result;
}

// 添加新函数：读取数据库中的记录，返回SQL插入语句
char* read_db_records(const char* db_path, int limit) {
    sqlite3 *db;
    char *err_msg = 0;
    sqlite3_stmt *stmt;
    char *result = NULL;
    size_t result_size = 0;
    
    int rc = sqlite3_open(db_path, &db);
    if (rc != SQLITE_OK) {
        print_with_time("Cannot open database: %s\n", sqlite3_errmsg(db));
        return NULL;
    }
    
    // 构造SQL语句，如果limit > 0则限制返回记录数
    const char *sql = (limit > 0) 
        ? "SELECT * FROM target_data ORDER BY tm DESC LIMIT ?"
        : "SELECT * FROM target_data ORDER BY tm DESC";
        
    rc = sqlite3_prepare_v2(db, sql, -1, &stmt, 0);
    if (rc != SQLITE_OK) {
        print_with_time("Failed to fetch data: %s\n", sqlite3_errmsg(db));
        sqlite3_close(db);
        return NULL;
    }
    
    if (limit > 0) {
        sqlite3_bind_int(stmt, 1, limit);
    }
    
    // 分配初始内存
    result = malloc(1);
    result[0] = '\0';
    result_size = 1;
    
    // 添加创建表的SQL语句
    const char *create_table = "CREATE TABLE IF NOT EXISTS target_data ("
        "id INTEGER, hp REAL,vp REAL, dh REAL, dv REAL, coef REAL, "
        "target_size INTEGER, col_width INTEGER, row_width INTEGER, "
        "is_ref_point INTEGER, tm TEXT, target_no TEXT, "
        "is_manual INTEGER, temp REAL);\n\n";
    
    result = realloc(result, strlen(create_table) + 1);
    strcpy(result, create_table);
    result_size = strlen(create_table) + 1;
    
    while (sqlite3_step(stmt) == SQLITE_ROW) {
        char line[2048];
        snprintf(line, sizeof(line), 
            "INSERT INTO target_data (id, hp, vp, dh, dv, coef, target_size, "
            "col_width, row_width, is_ref_point, tm, target_no, is_manual, temp) "
            "VALUES (%d, %.2f, %.2f, %.2f, %.2f, %.2f, %d, %d, %d, %d, '%s', '%s', %d, %.2f);\n",
            sqlite3_column_int(stmt, 0), // id
            sqlite3_column_double(stmt, 1), // hp
            sqlite3_column_double(stmt, 2), // vp
            sqlite3_column_double(stmt, 16), // dh
            sqlite3_column_double(stmt, 17), // dv
            sqlite3_column_double(stmt, 5), // coef
            sqlite3_column_int(stmt, 6),    // target_size
            sqlite3_column_int(stmt, 7),    // col_width
            sqlite3_column_int(stmt, 8),    // row_width
            sqlite3_column_int(stmt, 9),    // is_ref_point
            sqlite3_column_text(stmt, 10),   // tm
            sqlite3_column_text(stmt, 11),   // target_no
            sqlite3_column_int(stmt, 12),   // is_manual
            sqlite3_column_double(stmt, 13) // temp
        );
        
        result = realloc(result, result_size + strlen(line));
        strcat(result, line);
        result_size += strlen(line);
    }
    
    sqlite3_finalize(stmt);
    sqlite3_close(db);
    return result;
}

// 清空数据库表
bool clear_db_table(const char* db_path) {
    sqlite3 *db;
    char *err_msg = 0;
    
    int rc = sqlite3_open(db_path, &db);
    if (rc != SQLITE_OK) {
        print_with_time("Cannot open database: %s\n", sqlite3_errmsg(db));
        return false;
    }
    
    char *sql = "DELETE FROM target_data";
    rc = sqlite3_exec(db, sql, 0, 0, &err_msg);
    
    if (rc != SQLITE_OK) {
        print_with_time("SQL error: %s\n", err_msg);
        sqlite3_free(err_msg);
        sqlite3_close(db);
        return false;
    }
    
    sqlite3_close(db);
    return true;
}
//同步数据库
char* sync_db(cJSON* start_time, cJSON* end_time) {
    sqlite3 *db;
    sqlite3_stmt *stmt;
    char *result = NULL;
    
    print_with_time("开始同步数据库操作\n");
    
    // 将输入的时间字符串转换为时间戳
    time_t start_timestamp = convert_formatted_time_to_timestamp(start_time->valuestring);
    time_t end_timestamp = convert_formatted_time_to_timestamp(end_time->valuestring);
    
    char sql[512];
    snprintf(sql, sizeof(sql), 
        "SELECT * FROM target_data WHERE tm BETWEEN %ld AND %ld ORDER BY tm",
        start_timestamp, end_timestamp);
    
    print_with_time("执行SQL查询: %s\n", sql);

    int rc = sqlite3_open(DB_PATH, &db);
    if (rc != SQLITE_OK) {
        print_with_time("数据库打开失败: %s\n", sqlite3_errmsg(db));
        return strdup("数据库打开失败");
    }

    rc = sqlite3_prepare_v2(db, sql, -1, &stmt, 0);//-1表示sql语句长度不受限制
    if (rc != SQLITE_OK) {
        print_with_time("SQL准备失败: %s\n", sqlite3_errmsg(db));
        sqlite3_close(db);
        return strdup("SQL准备失败");
    }

    result = strdup("");
    int record_count = 0;
    
    while (sqlite3_step(stmt) == SQLITE_ROW) {
        // 直接获取dh和dv的值并打印调试信息
        double dh = sqlite3_column_double(stmt, 16);  // dh列
        double dv = sqlite3_column_double(stmt, 17);  // dv列
        //print_with_time("读取记录 - dh: %.2f, dv: %.2f\n", dh, dv);
        
        char line[1024];
        snprintf(line, sizeof(line),
            "%d,%.2f,%.2f,%.2f,%.2f,%.2f,%d,%d,%d,%d,%lld,%s,%d,%.2f\n",
            sqlite3_column_int(stmt, 0),     // id
            sqlite3_column_double(stmt, 1),  // hp
            sqlite3_column_double(stmt, 2),  // vp
            dh,                             // dh
            dv,                             // dv
            sqlite3_column_double(stmt, 5),  // coef
            sqlite3_column_int(stmt, 6),     // target_size
            sqlite3_column_int(stmt, 7),     // col_width
            sqlite3_column_int(stmt, 8),     // row_width
            sqlite3_column_int(stmt, 9),     // is_ref_point
            sqlite3_column_int64(stmt, 10),  // tm  
            sqlite3_column_text(stmt, 11),   // target_no
            sqlite3_column_int(stmt, 12),    // is_manual
            sqlite3_column_double(stmt, 13)  // temp
        );
        
        // 检查内存分配
        char *temp = realloc(result, strlen(result) + strlen(line) + 1);
        if (temp == NULL) {
            print_with_time("内存分配失败\n");
            free(result);
            sqlite3_finalize(stmt);
            sqlite3_close(db);
            return strdup("内存分配失败");
        }
        result = temp;
        strcat(result, line);
        record_count++;
        
        // 打印生成的行，用于调试
        //print_with_time("生成的数据行: %s", line);
    }
    
    print_with_time("数据库同步完成，共处理 %d 条记录\n", record_count);
    
    sqlite3_finalize(stmt);
    sqlite3_close(db);
    
    if (record_count == 0) {
        print_with_time("警告：没有找到符合条件的记录\n");
    }

    return result ? result : strdup("");
}
// 抓拍线程函数
void *capture_thread_function(void *arg) {
    struct capture_thread_data *data = (struct capture_thread_data *)arg;
    
    // 在这个线程中执行抓拍
    pid_t pid = fork();
    
    if (pid < 0) {
        // fork失败
        print_with_time("Failed to fork process for capture\n");
        data->result = false;
    } else if (pid == 0) {
        // 子进程
        if (data->params != NULL && strlen(data->params) > 0) {
            execl(CAPTURE_SINGLE, CAPTURE_SINGLE, data->params, NULL);
        } else {
            execl(CAPTURE_SINGLE, CAPTURE_SINGLE, NULL);
        }
        // 如果execl失败，会执行以下代码
        exit(1);
    } else {
        // 父进程
        int status;
        waitpid(pid, &status, 0);
        
        if (WIFEXITED(status) && WEXITSTATUS(status) == 0) {
            print_with_time("Photo captured successfully\n");
            data->result = true;
        } else {
            print_with_time("capture_single execution failed with status: %d\n", status);
            data->result = false;
        }
    }
    
    // 标记完成并发出信号
    pthread_mutex_lock(&data->mutex);
    data->completed = true;
    pthread_cond_signal(&data->cond);
    pthread_mutex_unlock(&data->mutex);
    
    return NULL;
}

// 修改后的take_photo_with_capture_single函数
bool take_photo_with_capture_single(const char* params) {
    // 创建线程数据
    struct capture_thread_data *data = malloc(sizeof(struct capture_thread_data));
    if (!data) {
        print_with_time("Failed to allocate memory for capture thread data\n");
        return false;
    }
    
    // 初始化数据
    data->params = params ? strdup(params) : NULL;
    data->result = false;
    data->completed = false;
    pthread_mutex_init(&data->mutex, NULL);
    pthread_cond_init(&data->cond, NULL);
    
    // 创建线程
    pthread_t thread;
    if (pthread_create(&thread, NULL, capture_thread_function, data) != 0) {
        print_with_time("Failed to create capture thread\n");
        if (data->params) free(data->params);
        free(data);
        return false;
    }
    
    // 等待线程完成
    pthread_mutex_lock(&data->mutex);
    while (!data->completed) {
        pthread_cond_wait(&data->cond, &data->mutex);
    }
    pthread_mutex_unlock(&data->mutex);
    
    // 获取结果
    bool result = data->result;
    
    // 清理
    if (data->params) free(data->params);
    pthread_mutex_destroy(&data->mutex);
    pthread_cond_destroy(&data->cond);
    free(data);
    
    pthread_detach(thread);  // 分离线程，让系统自动回收资源
    
    return result;
}
// 辅助函数：将"20250322113634"格式的时间转换为时间戳
time_t convert_time_to_timestamp(const char* time_str) {
    struct tm tm_time = {0};
    char year_str[5] = {0};
    char month_str[3] = {0};
    char day_str[3] = {0};
    char hour_str[3] = {0};
    char min_str[3] = {0};
    char sec_str[3] = {0};
    
    // 分割时间字符串
    strncpy(year_str, time_str, 4);
    strncpy(month_str, time_str + 4, 2);
    strncpy(day_str, time_str + 6, 2);
    strncpy(hour_str, time_str + 8, 2);
    strncpy(min_str, time_str + 10, 2);
    strncpy(sec_str, time_str + 12, 2);
    
    // 转换为整数并设置tm结构
    tm_time.tm_year = atoi(year_str) - 1900;  // 年份从1900年开始
    tm_time.tm_mon = atoi(month_str) - 1;     // 月份从0开始
    tm_time.tm_mday = atoi(day_str);
    tm_time.tm_hour = atoi(hour_str);
    tm_time.tm_min = atoi(min_str);
    tm_time.tm_sec = atoi(sec_str);
    
    // 转换为时间戳
    return mktime(&tm_time);
}

// 将"2025-03-21 00:00:00"格式的时间转换为时间戳
time_t convert_formatted_time_to_timestamp(const char* time_str) {
    struct tm tm_time = {0};
    sscanf(time_str, "%d-%d-%d %d:%d:%d",
           &tm_time.tm_year, &tm_time.tm_mon, &tm_time.tm_mday,
           &tm_time.tm_hour, &tm_time.tm_min, &tm_time.tm_sec);
    
    tm_time.tm_year -= 1900;  // 年份从1900年开始
    tm_time.tm_mon -= 1;      // 月份从0开始
    
    return mktime(&tm_time);
}

// 添加新函数来处理point_displacement.txt变更时的数据库操作
int handle_file_change(const char* file_path) {
    if (access(file_path, F_OK) != 0) {
        print_with_time("File deleted: %s\n", file_path);
        return -1;
    }
    
    char *content = read_file(file_path);
    if (content == NULL) {
        print_error_with_time("Failed to read file: %s\n", file_path);
        return -1;
    }

    // 解析文件内容
    char *line = strtok(content, "\n");
    if (line == NULL) {
        free(content);
        return -1;
    }

    // 解析第一行获取时间和温度
    char time_str[15];
    float temp;
    sscanf(line, "%s %f", time_str, &temp);
    
    // 将时间字符串转换为时间戳
    time_t timestamp = convert_time_to_timestamp(time_str);

    // 连接数据库
    sqlite3 *db;
    int rc = sqlite3_open(DB_PATH, &db);
    if (rc) {
        print_error_with_time("Can't open database: %s\n", sqlite3_errmsg(db));
        free(content);
        return -1;
    }

    // 检查数据库文件权限
    if (access(DB_PATH, W_OK) != 0) {
        print_error_with_time("Database file is not writable: %s\n", DB_PATH);
        char cmd[256];
        snprintf(cmd, sizeof(cmd), "chmod 666 %s", DB_PATH);
        system(cmd);
    }

    // 准备SQL语句
    sqlite3_stmt *stmt;
    const char *sql = "INSERT INTO target_data (hp,vp, dh, dv, coef, target_size, "
                     "col_width, row_width, is_ref_point, is_manual, tm, temp, target_no) "
                     "VALUES (?,?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
                     
    rc = sqlite3_prepare_v2(db, sql, -1, &stmt, NULL);
    if (rc != SQLITE_OK) {
        print_error_with_time("Failed to prepare statement: %s\n", sqlite3_errmsg(db));
        sqlite3_close(db);
        free(content);
        return -1;
    }

    // 读取并插入每个点的数据
    while ((line = strtok(NULL, "\n")) != NULL) {
        int no, col, row, target_size, col_width, row_width, is_ref_point, is_manual;
        float dh, dv, coef;
             
        rc = sscanf(line, "%d %d %d %f %f %f %d %d %d %d %d",
                   &no, &col, &row, &dh, &dv, &coef, 
                   &target_size, &col_width, &row_width, 
                   &is_ref_point, &is_manual);
        
        if (rc == 11) {  // 确保读取了所有字段
            
            sqlite3_bind_double(stmt, 1, col);
            sqlite3_bind_double(stmt, 2, row);
            sqlite3_bind_double(stmt, 3, dh);
            sqlite3_bind_double(stmt, 4, dv);
            sqlite3_bind_double(stmt, 5, coef);
            sqlite3_bind_int(stmt, 6, target_size);
            sqlite3_bind_int(stmt, 7, col_width);
            sqlite3_bind_int(stmt, 8, row_width);
            sqlite3_bind_int(stmt, 9, is_ref_point);
            sqlite3_bind_int(stmt, 10, is_manual);
            sqlite3_bind_int64(stmt, 11, timestamp);  // 使用时间戳
            char target_no_str[10];
            sprintf(target_no_str, "%d", no);
            sqlite3_bind_double(stmt, 12, temp);
            sqlite3_bind_text(stmt, 13, target_no_str, -1, SQLITE_STATIC);
        



            rc = sqlite3_step(stmt);
            if (rc != SQLITE_DONE) {
                print_error_with_time("Failed to insert data: %s\n", sqlite3_errmsg(db));
            }
            sqlite3_reset(stmt);
        }
    }

    sqlite3_finalize(stmt);
    sqlite3_close(db);
    free(content);
    print_with_time("解算结果入库执行完成\n");
    return 0;
}
#endif // FIO_H