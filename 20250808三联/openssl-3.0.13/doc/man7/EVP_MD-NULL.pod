=pod

=head1 NAME

EVP_MD-NULL - The NULL EVP_MD implementation

=head1 DESCRIPTION

Support for a NULL digest through the B<EVP_MD> API.
This algorithm does nothing and returns 1 for its init,
update and final methods.

=head2 Algorithm Name

The following algorithm is available in the default provider:

=over 4

=item "NULL"

=back

=head2 Gettable Parameters

This implementation supports the common gettable parameters described
in L<EVP_MD-common(7)>.

=head1 SEE ALSO

L<EVP_MD_CTX_set_params(3)>, L<provider-digest(7)>,
L<OSSL_PROVIDER-default(7)>

=head1 COPYRIGHT

Copyright 2023 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
