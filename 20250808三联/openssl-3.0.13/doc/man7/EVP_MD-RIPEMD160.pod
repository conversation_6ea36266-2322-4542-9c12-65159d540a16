=pod

=head1 NAME

EVP_MD-RIPEMD160 - The RIPEMD160 EVP_MD implementation

=head1 DESCRIPTION

Support for computing RIPEMD160 digests through the B<EVP_MD> API.

=head2 Identities

This implementation is available in both the default and legacy providers, and is
identified with any of the names "RIPEMD-160", "RIPEMD160", "RIPEMD" and
"RMD160".

=head2 Gettable Parameters

This implementation supports the common gettable parameters described
in L<EVP_MD-common(7)>.

=head1 SEE ALSO

L<provider-digest(7)>, L<OSSL_PROVIDER-default(7)>

=head1 HISTORY

This digest was added to the default provider in OpenSSL 3.0.7.

=head1 COPYRIGHT

Copyright 2020-2022 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
