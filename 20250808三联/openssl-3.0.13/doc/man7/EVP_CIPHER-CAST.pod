=pod

=head1 NAME

EVP_CIPHER-CAST - The CAST EVP_CIPHER implementations

=head1 DESCRIPTION

Support for CAST symmetric encryption using the B<EVP_CIPHER> API.

=head2 Algorithm Names

The following algorithms are available in the legacy provider:

=over 4

=item "CAST-128-CBC", "CAST-192-CBC" and  "CAST-256-CBC"

=item "CAST-128-CFB", "CAST-192-CFB", "CAST-256-CFB"

=item "CAST-128-ECB", "CAST-192-ECB" and "CAST-256-ECB"

=item "CAST-192-OFB", "CAST-128-OFB" and "CAST-256-OFB"

=back

=head2 Parameters

This implementation supports the parameters described in
L<EVP_EncryptInit(3)/PARAMETERS>.

=head1 SEE ALSO

L<provider-cipher(7)>, L<OSSL_PROVIDER-legacy(7)>

=head1 COPYRIGHT

Copyright 2021 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
