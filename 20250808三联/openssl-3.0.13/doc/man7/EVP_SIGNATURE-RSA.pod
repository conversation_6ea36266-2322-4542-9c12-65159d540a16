=pod

=head1 NAME

EVP_SIGNATURE-RSA
- The EVP_PKEY RSA signature implementation

=head1 DESCRIPTION

Support for computing RSA signatures.
See L<EVP_PKEY-RSA(7)> for information related to RSA keys.

=head2 Signature Parameters

The following signature parameters can be set using EVP_PKEY_CTX_set_params().
This may be called after EVP_PKEY_sign_init() or EVP_PKEY_verify_init(),
and before calling EVP_PKEY_sign() or EVP_PKEY_verify().

=over 4

=item "digest" (B<OSSL_SIGNATURE_PARAM_DIGEST>) <UTF8 string>

=item "properties" (B<OSSL_SIGNATURE_PARAM_PROPERTIES>) <UTF8 string>

These common parameters are described in L<provider-signature(7)>.

=item "pad-mode" (B<OSSL_SIGNATURE_PARAM_PAD_MODE>) <UTF8 string>

The type of padding to be used. Its value can be one of the following:

=over 4

=item "none" (B<OSSL_PKEY_RSA_PAD_MODE_NONE>)

=item "pkcs1" (B<OSSL_PKEY_RSA_PAD_MODE_PKCSV15>)

=item "x931" (B<OSSL_PKEY_RSA_PAD_MODE_X931>)

=item "pss" (B<OSSL_PKEY_RSA_PAD_MODE_PSS>)

=back

=item "mgf1-digest" (B<OSSL_SIGNATURE_PARAM_MGF1_DIGEST>) <UTF8 string>

The digest algorithm name to use for the maskGenAlgorithm used by "pss" mode.

=item "mgf1-properties" (B<OSSL_SIGNATURE_PARAM_MGF1_PROPERTIES>) <UTF8 string>

Sets the name of the property query associated with the "mgf1-digest" algorithm.
NULL is used if this optional value is not set.

=item "saltlen" (B<OSSL_SIGNATURE_PARAM_PSS_SALTLEN>) <integer> or <UTF8 string>

The "pss" mode minimum salt length. The value can either be an integer,
a string value representing a number or one of the following string values:

=over 4

=item "digest" (B<OSSL_PKEY_RSA_PSS_SALT_LEN_DIGEST>)

Use the same length as the digest size.

=item "max" (B<OSSL_PKEY_RSA_PSS_SALT_LEN_MAX>)

Use the maximum salt length.

=item "auto" (B<OSSL_PKEY_RSA_PSS_SALT_LEN_AUTO>)

Auto detect the salt length.

=back

=back

The following signature parameters can be retrieved using
EVP_PKEY_CTX_get_params().

=over 4

=item "algorithm-id" (B<OSSL_SIGNATURE_PARAM_ALGORITHM_ID>) <octet string>

This common parameter is described in L<provider-signature(7)>.

=item "digest" (B<OSSL_SIGNATURE_PARAM_DIGEST>) <UTF8 string>

=item "pad-mode" (B<OSSL_SIGNATURE_PARAM_PAD_MODE>) <UTF8 string>

=item "mgf1-digest" (B<OSSL_SIGNATURE_PARAM_MGF1_DIGEST>) <UTF8 string>

=item "saltlen" (B<OSSL_SIGNATURE_PARAM_PSS_SALTLEN>) <integer> or <UTF8 string>

These parameters are as described above.

=back

=head1 SEE ALSO

L<EVP_PKEY_CTX_set_params(3)>,
L<EVP_PKEY_sign(3)>,
L<EVP_PKEY_verify(3)>,
L<provider-signature(7)>,

=head1 COPYRIGHT

Copyright 2020-2022 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
