=pod

=head1 NAME

RAND
- the OpenSSL random generator

=head1 DESCRIPTION

Random numbers are a vital part of cryptography, they are needed to provide
unpredictability for tasks like key generation, creating salts, and many more.
Software-based generators must be seeded with external randomness before they
can be used as a cryptographically-secure pseudo-random number generator
(CSPRNG).
The availability of common hardware with special instructions and
modern operating systems, which may use items such as interrupt jitter
and network packet timings, can be reasonable sources of seeding material.

OpenSSL comes with a default implementation of the RAND API which is based on
the deterministic random bit generator (DRBG) model as described in
[NIST SP 800-90A Rev. 1]. The default random generator will initialize
automatically on first use and will be fully functional without having
to be initialized ('seeded') explicitly.
It seeds and reseeds itself automatically using trusted random sources
provided by the operating system.

As a normal application developer, you do not have to worry about any details,
just use L<RAND_bytes(3)> to obtain random data.
Having said that, there is one important rule to obey: Always check the error
return value of L<RAND_bytes(3)> and do not take randomness for granted.
Although (re-)seeding is automatic, it can fail because no trusted random source
is available or the trusted source(s) temporarily fail to provide sufficient
random seed material.
In this case the CSPRNG enters an error state and ceases to provide output,
until it is able to recover from the error by reseeding itself.
For more details on reseeding and error recovery, see L<EVP_RAND(7)>.

For values that should remain secret, you can use L<RAND_priv_bytes(3)>
instead.
This method does not provide 'better' randomness, it uses the same type of
CSPRNG.
The intention behind using a dedicated CSPRNG exclusively for private
values is that none of its output should be visible to an attacker (e.g.,
used as salt value), in order to reveal as little information as
possible about its internal state, and that a compromise of the "public"
CSPRNG instance will not affect the secrecy of these private values.

In the rare case where the default implementation does not satisfy your special
requirements, the default RAND internals can be replaced by your own
L<EVP_RAND(3)> objects.

Changing the default random generator should be necessary
only in exceptional cases and is not recommended, unless you have a profound
knowledge of cryptographic principles and understand the implications of your
changes.

=head1 DEFAULT SETUP

The default OpenSSL RAND method is based on the EVP_RAND deterministic random
bit generator (DRBG) classes.
A DRBG is a certain type of cryptographically-secure pseudo-random
number generator (CSPRNG), which is described in [NIST SP 800-90A Rev. 1].


=head1 SEE ALSO

L<RAND_bytes(3)>,
L<RAND_priv_bytes(3)>,
L<EVP_RAND(3)>,
L<RAND_get0_primary(3)>,
L<EVP_RAND(7)>

=head1 COPYRIGHT

Copyright 2018-2021 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
