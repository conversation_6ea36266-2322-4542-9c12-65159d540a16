=pod

=head1 NAME

EVP_KDF-PBKDF1 - The PBKDF1 EVP_KDF implementation

=head1 DESCRIPTION

Support for computing the B<PBKDF1> password-based KDF through the B<EVP_KDF>
API.

The EVP_KDF-PBKDF1 algorithm implements the PBKDF1 password-based key
derivation function, as described in RFC 8018; it derives a key from a password
using a salt and iteration count.

=head2 Identity

"PBKDF1" is the name for this implementation; it
can be used with the EVP_KDF_fetch() function.

=head2 Supported parameters

The supported parameters are:

=over 4

=item "pass" (B<OSSL_KDF_PARAM_PASSWORD>) <octet string>

=item "salt" (B<OSSL_KDF_PARAM_SALT>) <octet string>

=item "iter" (B<OSSL_KDF_PARAM_ITER>) <unsigned integer>

This parameter has a default value of 0 and should be set.

=item "properties" (B<OSSL_KDF_PARAM_PROPERTIES>) <UTF8 string>

=item "digest" (B<OSSL_KDF_PARAM_DIGEST>) <UTF8 string>

These parameters work as described in L<EVP_KDF(3)/PARAMETERS>.

=back

=head1 NOTES

A typical application of this algorithm is to derive keying material for an
encryption algorithm from a password in the "pass", a salt in "salt",
and an iteration count.

Increasing the "iter" parameter slows down the algorithm which makes it
harder for an attacker to perform a brute force attack using a large number
of candidate passwords.

No assumption is made regarding the given password; it is simply treated as a
byte sequence.

=head1 CONFORMING TO

RFC 8018

=head1 SEE ALSO

L<EVP_KDF(3)>,
L<EVP_KDF_CTX_new(3)>,
L<EVP_KDF_CTX_free(3)>,
L<EVP_KDF_CTX_set_params(3)>,
L<EVP_KDF_derive(3)>,
L<EVP_KDF(3)/PARAMETERS>

=head1 HISTORY

This functionality was added in OpenSSL 3.0.

=head1 COPYRIGHT

Copyright 2021 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
