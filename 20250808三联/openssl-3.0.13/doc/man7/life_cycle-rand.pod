=pod

=head1 NAME

life_cycle-rand - The RAND algorithm life-cycle

=head1 DESCRIPTION

All random number generator (RANDs)
go through a number of stages in their life-cycle:

=over 4

=item start

This state represents the RAND before it has been allocated.  It is the
starting state for any life-cycle transitions.

=item newed

This state represents the RAND after it has been allocated but unable to
generate any output.

=item instantiated

This state represents the RAND when it is set up and capable of generating
output.

=item uninstantiated

This state represents the RAND when it has been shutdown and it is no longer
capable of generating output.

=item freed

This state is entered when the RAND is freed.  It is the terminal state
for all life-cycle transitions.

=back

=head2 State Transition Diagram

The usual life-cycle of a RAND is illustrated:

=begin man

                        +-------------------------+
                        |          start          |
                        +-------------------------+
                          |
                          | EVP_RAND_CTX_new
                          v
                        +-------------------------+
                        |          newed          |
                        +-------------------------+
                          |
                          | EVP_RAND_instantiate
                          v
    EVP_RAND_generate   +-------------------------+
  +-------------------- |                         |
  |                     |      instantiated       |
  +-------------------> |                         | <+
                        +-------------------------+  '
                          |                          '
                          | EVP_RAND_uninstantiate   ' EVP_RAND_instantiate
                          v                          '
                        +-------------------------+  '
                        |     uninstantiated      | -+
                        +-------------------------+
                          |
                          | EVP_RAND_CTX_free
                          v
                        +-------------------------+
                        |          freed          |
                        +-------------------------+

=end man

=for html <img src="img/rand.png">

=head2 Formal State Transitions

This section defines all of the legal state transitions.
This is the canonical list.

=begin man

 Function Call              ------------------ Current State ------------------
                            start   newed   instantiated  uninstantiated  freed
 EVP_RAND_CTX_new           newed
 EVP_RAND_instantiate            instantiated
 EVP_RAND_generate                          instantiated
 EVP_RAND_uninstantiate                    uninstantiated
 EVP_RAND_CTX_free          freed   freed      freed          freed
 EVP_RAND_CTX_get_params            newed   instantiated  uninstantiated  freed
 EVP_RAND_CTX_set_params            newed   instantiated  uninstantiated  freed
 EVP_RAND_CTX_gettable_params       newed   instantiated  uninstantiated  freed
 EVP_RAND_CTX_settable_params       newed   instantiated  uninstantiated  freed

=end man

=begin html

<table style="border:1px solid; border-collapse:collapse">
<tr><th style="border:1px solid" align="left">Function Call</th>
    <th style="border:1px solid" colspan="5">Current State</th></tr>
<tr><th style="border:1px solid"></th>
    <th style="border:1px solid" align="center">start</th>
    <th style="border:1px solid" align="center">newed</th>
    <th style="border:1px solid" align="center">instantiated</th>
    <th style="border:1px solid" align="center">uninstantiated</th>
    <th style="border:1px solid" align="center">freed</th></tr>
<tr><th style="border:1px solid" align="left">EVP_RAND_CTX_new</th>
    <td style="border:1px solid" align="center">newed</td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td></tr>
<tr><th style="border:1px solid" align="left">EVP_RAND_instantiate</th>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center">instantiated</td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td></tr>
<tr><th style="border:1px solid" align="left">EVP_RAND_generate</th>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center">instantiated</td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td></tr>
<tr><th style="border:1px solid" align="left">EVP_RAND_uninstantiate</th>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center">uninstantiated</td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td></tr>
<tr><th style="border:1px solid" align="left">EVP_RAND_CTX_free</th>
    <td style="border:1px solid" align="center">freed</td>
    <td style="border:1px solid" align="center">freed</td>
    <td style="border:1px solid" align="center">freed</td>
    <td style="border:1px solid" align="center">freed</td>
    <td style="border:1px solid" align="center"></td></tr>
<tr><th style="border:1px solid" align="left">EVP_RAND_CTX_get_params</th>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center">newed</td>
    <td style="border:1px solid" align="center">instantiated</td>
    <td style="border:1px solid" align="center">uninstantiated</td>
    <td style="border:1px solid" align="center"></td></tr>
<tr><th style="border:1px solid" align="left">EVP_RAND_CTX_set_params</th>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center">newed</td>
    <td style="border:1px solid" align="center">instantiated</td>
    <td style="border:1px solid" align="center">uninstantiated</td>
    <td style="border:1px solid" align="center"></td></tr>
<tr><th style="border:1px solid" align="left">EVP_RAND_CTX_gettable_params</th>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center">newed</td>
    <td style="border:1px solid" align="center">instantiated</td>
    <td style="border:1px solid" align="center">uninstantiated</td>
    <td style="border:1px solid" align="center"></td></tr>
<tr><th style="border:1px solid" align="left">EVP_RAND_CTX_settable_params</th>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center">newed</td>
    <td style="border:1px solid" align="center">instantiated</td>
    <td style="border:1px solid" align="center">uninstantiated</td>
    <td style="border:1px solid" align="center"></td></tr>
</table>

=end html

=head1 NOTES

At some point the EVP layer will begin enforcing the transitions described
herein.

=head1 SEE ALSO

L<provider-rand(7)>, L<EVP_RAND(3)>.

=head1 HISTORY

The provider RAND interface was introduced in OpenSSL 3.0.

=head1 COPYRIGHT

Copyright 2021 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
