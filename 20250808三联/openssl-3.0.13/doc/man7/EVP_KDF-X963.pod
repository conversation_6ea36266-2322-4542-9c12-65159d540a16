=pod

=head1 NAME

EVP_KDF-X963 - The X9.63-2001 EVP_KDF implementation

=head1 DESCRIPTION

The EVP_KDF-X963 algorithm implements the key derivation function (X963KDF).
X963KDF is used by Cryptographic Message Syntax (CMS) for EC KeyAgreement, to
derive a key using input such as a shared secret key and shared info.

=head2 Identity

"X963KDF" is the name for this implementation; it
can be used with the EVP_KDF_fetch() function.

=head2 Supported parameters

The supported parameters are:

=over 4

=item "properties" (B<OSSL_KDF_PARAM_PROPERTIES>) <UTF8 string>

=item "digest" (B<OSSL_KDF_PARAM_DIGEST>) <UTF8 string>

These parameters work as described in L<EVP_KDF(3)/PARAMETERS>.

=item "key" (B<OSSL_KDF_PARAM_KEY>) <octet string>

The shared secret used for key derivation.
This parameter sets the secret.

=item "info" (B<OSSL_KDF_PARAM_INFO>) <octet string>

This parameter specifies an optional value for shared info.

=back

=head1 NOTES

X963KDF is very similar to the SSKDF that uses a digest as the auxiliary function,
X963KDF appends the counter to the secret, whereas SSKDF prepends the counter.

A context for X963KDF can be obtained by calling:

 EVP_KDF *kdf = EVP_KDF_fetch(NULL, "X963KDF", NULL);
 EVP_KDF_CTX *kctx = EVP_KDF_CTX_new(kdf);

The output length of an X963KDF is specified via the I<keylen>
parameter to the L<EVP_KDF_derive(3)> function.

=head1 EXAMPLES

This example derives 10 bytes, with the secret key "secret" and sharedinfo
value "label":

 EVP_KDF *kdf;
 EVP_KDF_CTX *kctx;
 unsigned char out[10];
 OSSL_PARAM params[4], *p = params;

 kdf = EVP_KDF_fetch(NULL, "X963KDF", NULL);
 kctx = EVP_KDF_CTX_new(kdf);
 EVP_KDF_free(kdf);

 *p++ = OSSL_PARAM_construct_utf8_string(OSSL_KDF_PARAM_DIGEST,
                                         SN_sha256, strlen(SN_sha256));
 *p++ = OSSL_PARAM_construct_octet_string(OSSL_KDF_PARAM_SECRET,
                                          "secret", (size_t)6);
 *p++ = OSSL_PARAM_construct_octet_string(OSSL_KDF_PARAM_INFO,
                                          "label", (size_t)5);
 *p = OSSL_PARAM_construct_end();
 if (EVP_KDF_derive(kctx, out, sizeof(out), params) <= 0) {
     error("EVP_KDF_derive");
 }

 EVP_KDF_CTX_free(kctx);

=head1 CONFORMING TO

"SEC 1: Elliptic Curve Cryptography"

=head1 SEE ALSO

L<EVP_KDF(3)>,
L<EVP_KDF_CTX_new(3)>,
L<EVP_KDF_CTX_free(3)>,
L<EVP_KDF_CTX_set_params(3)>,
L<EVP_KDF_CTX_get_kdf_size(3)>,
L<EVP_KDF_derive(3)>,
L<EVP_KDF(3)/PARAMETERS>

=head1 HISTORY

This functionality was added in OpenSSL 3.0.

=head1 COPYRIGHT

Copyright 2019-2021 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
