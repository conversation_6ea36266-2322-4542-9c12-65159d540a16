=pod

=head1 NAME

openssl-env - OpenSSL environment variables

=head1 DESCRIPTION

The OpenSSL libraries use environment variables to override the
compiled-in default paths for various data.
To avoid security risks, the environment is usually not consulted when
the executable is set-user-ID or set-group-ID.

=over 4

=item B<CTLOG_FILE>

Specifies the path to a certificate transparency log list.
See L<CTLOG_STORE_new(3)>.

=item B<OPENSSL>

Specifies the path to the B<openssl> executable. Used by
the B<rehash> script (see L<openssl-rehash(1)/Script Configuration>)
and by the B<CA.pl> script (see L<CA.pl(1)/NOTES>

=item B<OPENSSL_CONF>, B<OPENSSL_CONF_INCLUDE>

Specifies the path to a configuration file and the directory for
included files.
See L<config(5)>.

=item B<OPENSSL_CONFIG>

Specifies a configuration option and filename for the B<req> and B<ca>
commands invoked by the B<CA.pl> script.
See L<CA.pl(1)>.

=item B<OPENSSL_ENGINES>

Specifies the directory from which dynamic engines are loaded.
See L<openssl-engine(1)>.

=item B<OPENSSL_MALLOC_FD>, B<OPENSSL_MALLOC_FAILURES>

If built with debugging, this allows memory allocation to fail.
See L<OPENSSL_malloc(3)>.

=item B<OPENSSL_MODULES>

Specifies the directory from which cryptographic providers are loaded.
Equivalently, the generic B<-provider-path> command-line option may be used.

=item B<OPENSSL_WIN32_UTF8>

If set, then L<UI_OpenSSL(3)> returns UTF-8 encoded strings, rather than
ones encoded in the current code page, and
the L<openssl(1)> program also transcodes the command-line parameters
from the current code page to UTF-8.
This environment variable is only checked on Microsoft Windows platforms.

=item B<RANDFILE>

The state file for the random number generator.
This should not be needed in normal use.
See L<RAND_load_file(3)>.

=item B<SSL_CERT_DIR>, B<SSL_CERT_FILE>

Specify the default directory or file containing CA certificates.
See L<SSL_CTX_load_verify_locations(3)>.

=item B<TSGET>

Additional arguments for the L<tsget(1)> command.

=item B<OPENSSL_ia32cap>, B<OPENSSL_sparcv9cap>, B<OPENSSL_ppccap>, B<OPENSSL_armcap>, B<OPENSSL_s390xcap>

OpenSSL supports a number of different algorithm implementations for
various machines and, by default, it determines which to use based on the
processor capabilities and run time feature enquiry.  These environment
variables can be used to exert more control over this selection process.
See L<OPENSSL_ia32cap(3)>, L<OPENSSL_s390xcap(3)>.

=item B<NO_PROXY>, B<HTTPS_PROXY>, B<HTTP_PROXY>

Specify a proxy hostname.
See L<OSSL_HTTP_parse_url(3)>.

=back

=head1 COPYRIGHT

Copyright 2019-2021 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
