=pod

=head1 NAME

EVP_KDF-SS - The Single Step / One Step EVP_KDF implementation

=head1 DESCRIPTION

The EVP_KDF-SS algorithm implements the Single Step key derivation function (SSKDF).
SSKDF derives a key using input such as a shared secret key (that was generated
during the execution of a key establishment scheme) and fixedinfo.
SSKDF is also informally referred to as 'Concat KDF'.

=head2 Auxiliary function

The implementation uses a selectable auxiliary function H, which can be one of:

=over 4

=item B<H(x) = hash(x, digest=md)>

=item B<H(x) = HMAC_hash(x, key=salt, digest=md)>

=item B<H(x) = KMACxxx(x, key=salt, custom="KDF", outlen=mac_size)>

=back

Both the HMAC and KMAC implementations set the key using the 'salt' value.
The hash and HMAC also require the digest to be set.

=head2 Identity

"SSKDF" is the name for this implementation; it
can be used with the EVP_KDF_fetch() function.

=head2 Supported parameters

The supported parameters are:

=over 4

=item "properties" (B<OSSL_KDF_PARAM_PROPERTIES>) <UTF8 string>

=item "digest" (B<OSSL_KDF_PARAM_DIGEST>) <UTF8 string>

This parameter is ignored for KMAC.

=item "mac" (B<OSSL_KDF_PARAM_MAC>) <UTF8 string>

=item "maclen" (B<OSSL_KDF_PARAM_MAC_SIZE>) <unsigned integer>

=item "salt" (B<OSSL_KDF_PARAM_SALT>) <octet string>

These parameters work as described in L<EVP_KDF(3)/PARAMETERS>.

=item "key" (B<OSSL_KDF_PARAM_SECRET>) <octet string>

This parameter set the shared secret that is used for key derivation.

=item "info" (B<OSSL_KDF_PARAM_INFO>) <octet string>

This parameter sets an optional value for fixedinfo, also known as otherinfo.

=back

=head1 NOTES

A context for SSKDF can be obtained by calling:

 EVP_KDF *kdf = EVP_KDF_fetch(NULL, "SSKDF", NULL);
 EVP_KDF_CTX *kctx = EVP_KDF_CTX_new(kdf);

The output length of an SSKDF is specified via the I<keylen>
parameter to the L<EVP_KDF_derive(3)> function.

=head1 EXAMPLES

This example derives 10 bytes using H(x) = SHA-256, with the secret key "secret"
and fixedinfo value "label":

 EVP_KDF *kdf;
 EVP_KDF_CTX *kctx;
 unsigned char out[10];
 OSSL_PARAM params[4], *p = params;

 kdf = EVP_KDF_fetch(NULL, "SSKDF", NULL);
 kctx = EVP_KDF_CTX_new(kdf);
 EVP_KDF_free(kdf);

 *p++ = OSSL_PARAM_construct_utf8_string(OSSL_KDF_PARAM_DIGEST,
                                         SN_sha256, strlen(SN_sha256));
 *p++ = OSSL_PARAM_construct_octet_string(OSSL_KDF_PARAM_KEY,
                                          "secret", (size_t)6);
 *p++ = OSSL_PARAM_construct_octet_string(OSSL_KDF_PARAM_INFO,
                                          "label", (size_t)5);
 *p = OSSL_PARAM_construct_end();
 if (EVP_KDF_derive(kctx, out, sizeof(out), params) <= 0) {
     error("EVP_KDF_derive");
 }

 EVP_KDF_CTX_free(kctx);

This example derives 10 bytes using H(x) = HMAC(SHA-256), with the secret key "secret",
fixedinfo value "label" and salt "salt":

 EVP_KDF *kdf;
 EVP_KDF_CTX *kctx;
 unsigned char out[10];
 OSSL_PARAM params[6], *p = params;

 kdf = EVP_KDF_fetch(NULL, "SSKDF", NULL);
 kctx = EVP_KDF_CTX_new(kdf);
 EVP_KDF_free(kdf);

 *p++ = OSSL_PARAM_construct_utf8_string(OSSL_KDF_PARAM_MAC,
                                         SN_hmac, strlen(SN_hmac));
 *p++ = OSSL_PARAM_construct_utf8_string(OSSL_KDF_PARAM_DIGEST,
                                         SN_sha256, strlen(SN_sha256));
 *p++ = OSSL_PARAM_construct_octet_string(OSSL_KDF_PARAM_SECRET,
                                          "secret", (size_t)6);
 *p++ = OSSL_PARAM_construct_octet_string(OSSL_KDF_PARAM_INFO,
                                          "label", (size_t)5);
 *p++ = OSSL_PARAM_construct_octet_string(OSSL_KDF_PARAM_SALT,
                                          "salt", (size_t)4);
 *p = OSSL_PARAM_construct_end();
 if (EVP_KDF_derive(kctx, out, sizeof(out), params) <= 0) {
     error("EVP_KDF_derive");
 }

 EVP_KDF_CTX_free(kctx);

This example derives 10 bytes using H(x) = KMAC128(x,salt,outlen), with the secret key "secret"
fixedinfo value "label", salt of "salt" and KMAC outlen of 20:

 EVP_KDF *kdf;
 EVP_KDF_CTX *kctx;
 unsigned char out[10];
 OSSL_PARAM params[6], *p = params;

 kdf = EVP_KDF_fetch(NULL, "SSKDF", NULL);
 kctx = EVP_KDF_CTX_new(kdf);
 EVP_KDF_free(kdf);

 *p++ = OSSL_PARAM_construct_utf8_string(OSSL_KDF_PARAM_MAC,
                                         SN_kmac128, strlen(SN_kmac128));
 *p++ = OSSL_PARAM_construct_octet_string(OSSL_KDF_PARAM_SECRET,
                                          "secret", (size_t)6);
 *p++ = OSSL_PARAM_construct_octet_string(OSSL_KDF_PARAM_INFO,
                                          "label", (size_t)5);
 *p++ = OSSL_PARAM_construct_octet_string(OSSL_KDF_PARAM_SALT,
                                          "salt", (size_t)4);
 *p++ = OSSL_PARAM_construct_size_t(OSSL_KDF_PARAM_MAC_SIZE, (size_t)20);
 *p = OSSL_PARAM_construct_end();
 if (EVP_KDF_derive(kctx, out, sizeof(out), params) <= 0) {
     error("EVP_KDF_derive");
 }

 EVP_KDF_CTX_free(kctx);

=head1 CONFORMING TO

NIST SP800-56Cr1.

=head1 SEE ALSO

L<EVP_KDF(3)>,
L<EVP_KDF_CTX_new(3)>,
L<EVP_KDF_CTX_free(3)>,
L<EVP_KDF_CTX_set_params(3)>,
L<EVP_KDF_CTX_get_kdf_size(3)>,
L<EVP_KDF_derive(3)>,
L<EVP_KDF(3)/PARAMETERS>

=head1 HISTORY

This functionality was added in OpenSSL 3.0.

=head1 COPYRIGHT

Copyright 2019-2023 The OpenSSL Project Authors. All Rights Reserved.  Copyright
(c) 2019, Oracle and/or its affiliates.  All rights reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
