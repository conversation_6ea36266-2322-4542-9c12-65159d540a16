=pod

=head1 NAME

EVP_PKEY-SM2, <PERSON><PERSON>_KEYMGMT-SM2, SM2
- EVP_PKEY keytype support for the Chinese SM2 signature and encryption algorithms

=head1 DESCRIPTION

The B<SM2> algorithm was first defined by the Chinese national standard GM/T
0003-2012 and was later standardized by ISO as ISO/IEC 14888. B<SM2> is actually
an elliptic curve based algorithm. The current implementation in OpenSSL supports
both signature and encryption schemes via the EVP interface.

When doing the B<SM2> signature algorithm, it requires a distinguishing identifier
to form the message prefix which is hashed before the real message is hashed.

=head2 Common SM2 parameters

SM2 uses the parameters defined in L<EVP_PKEY-EC(7)/Common EC parameters>.
The following parameters are different:

=over 4

=item "cofactor" (B<OSSL_PKEY_PARAM_EC_COFACTOR>) <unsigned integer>

This parameter is ignored for B<SM2>.

=item (B<OSSL_PKEY_PARAM_DEFAULT_DIGEST>) <UTF8 string>

Getter that returns the default digest name.
(Currently returns "SM3" as of OpenSSL 3.0).

=back

=head1 NOTES

B<SM2> signatures can be generated by using the 'DigestSign' series of APIs, for
instance, EVP_DigestSignInit(), EVP_DigestSignUpdate() and EVP_DigestSignFinal().
Ditto for the verification process by calling the 'DigestVerify' series of APIs.

Before computing an B<SM2> signature, an B<EVP_PKEY_CTX> needs to be created,
and an B<SM2> ID must be set for it, like this:

 EVP_PKEY_CTX_set1_id(pctx, id, id_len);

Before calling the EVP_DigestSignInit() or EVP_DigestVerifyInit() functions,
that B<EVP_PKEY_CTX> should be assigned to the B<EVP_MD_CTX>, like this:

 EVP_MD_CTX_set_pkey_ctx(mctx, pctx);

There is normally no need to pass a B<pctx> parameter to EVP_DigestSignInit()
or EVP_DigestVerifyInit() in such a scenario.

SM2 can be tested with the L<openssl-speed(1)> application since version 3.0.
Currently, the only valid algorithm name is B<sm2>.

Since version 3.0, SM2 keys can be generated and loaded only when the domain
parameters specify the SM2 elliptic curve.

=head1 EXAMPLES

This example demonstrates the calling sequence for using an B<EVP_PKEY> to verify
a message with the SM2 signature algorithm and the SM3 hash algorithm:

 #include <openssl/evp.h>

 /* obtain an EVP_PKEY using whatever methods... */
 mctx = EVP_MD_CTX_new();
 pctx = EVP_PKEY_CTX_new(pkey, NULL);
 EVP_PKEY_CTX_set1_id(pctx, id, id_len);
 EVP_MD_CTX_set_pkey_ctx(mctx, pctx);
 EVP_DigestVerifyInit(mctx, NULL, EVP_sm3(), NULL, pkey);
 EVP_DigestVerifyUpdate(mctx, msg, msg_len);
 EVP_DigestVerifyFinal(mctx, sig, sig_len)

=head1 SEE ALSO

L<EVP_PKEY_CTX_new(3)>,
L<EVP_DigestSignInit(3)>,
L<EVP_DigestVerifyInit(3)>,
L<EVP_PKEY_CTX_set1_id(3)>,
L<EVP_MD_CTX_set_pkey_ctx(3)>

=head1 COPYRIGHT

Copyright 2018-2021 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
