=pod

=head1 NAME

EVP_SIGNATURE-DSA
- The B<EVP_PKEY> DSA signature implementation

=head1 DESCRIPTION

Support for computing DSA signatures.
See L<EVP_PKEY-DSA(7)> for information related to DSA keys.

=head2 Signature Parameters

The following signature parameters can be set using EVP_PKEY_CTX_set_params().
This may be called after EVP_PKEY_sign_init() or EVP_PKEY_verify_init(),
and before calling EVP_PKEY_sign() or EVP_PKEY_verify().

=over 4

=item "digest" (B<OSSL_SIGNATURE_PARAM_DIGEST>) <UTF8 string>

=item "properties" (B<OSSL_SIGNATURE_PARAM_PROPERTIES>) <UTF8 string>

The settable parameters are described in L<provider-signature(7)>.

=back

The following signature parameters can be retrieved using
EVP_PKEY_CTX_get_params().

=over 4

=item "algorithm-id" (B<OSSL_SIGNATURE_PARAM_ALGORITHM_ID>) <octet string>

=item "digest" (B<OSSL_SIGNATURE_PARAM_DIGEST>) <UTF8 string>

The gettable parameters are described in L<provider-signature(7)>.

=back

=head1 SEE ALSO

L<EVP_PKEY_CTX_set_params(3)>,
L<EVP_PKEY_sign(3)>,
L<EVP_PKEY_verify(3)>,
L<provider-signature(7)>,

=head1 COPYRIGHT

Copyright 2020-2021 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
