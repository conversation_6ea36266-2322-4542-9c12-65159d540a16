=pod

=head1 NAME

evp - high-level cryptographic functions

=head1 SYNOPSIS

 #include <openssl/evp.h>

=head1 DESCRIPTION

The EVP library provides a high-level interface to cryptographic
functions.

The L<B<EVP_Seal>I<XXX>|EVP_SealInit(3)> and L<B<EVP_Open>I<XXX>|EVP_OpenInit(3)>
functions provide public key encryption and decryption to implement digital "envelopes".

The L<B<EVP_DigestSign>I<XXX>|EVP_DigestSignInit(3)> and
L<B<EVP_DigestVerify>I<XXX>|EVP_DigestVerifyInit(3)> functions implement
digital signatures and Message Authentication Codes (MACs). Also see the older
L<B<EVP_Sign>I<XXX>|EVP_SignInit(3)> and L<B<EVP_Verify>I<XXX>|EVP_VerifyInit(3)>
functions.

Symmetric encryption is available with the L<B<EVP_Encrypt>I<XXX>|EVP_EncryptInit(3)>
functions.  The L<B<EVP_Digest>I<XXX>|EVP_DigestInit(3)> functions provide message digests.

The B<EVP_PKEY>I<XXX> functions provide a high-level interface to
asymmetric algorithms. To create a new EVP_PKEY see
L<EVP_PKEY_new(3)>. EVP_PKEYs can be associated
with a private key of a particular algorithm by using the functions
described on the L<EVP_PKEY_fromdata(3)> page, or
new keys can be generated using L<EVP_PKEY_keygen(3)>.
EVP_PKEYs can be compared using L<EVP_PKEY_eq(3)>, or printed using
L<EVP_PKEY_print_private(3)>. L<EVP_PKEY_todata(3)> can be used to convert a
key back into an L<OSSL_PARAM(3)> array.

The EVP_PKEY functions support the full range of asymmetric algorithm operations:

=over 4

=item For key agreement see L<EVP_PKEY_derive(3)>

=item For signing and verifying see L<EVP_PKEY_sign(3)>,
L<EVP_PKEY_verify(3)> and L<EVP_PKEY_verify_recover(3)>.
However, note that
these functions do not perform a digest of the data to be signed. Therefore,
normally you would use the L<EVP_DigestSignInit(3)>
functions for this purpose.

=item For encryption and decryption see L<EVP_PKEY_encrypt(3)>
and L<EVP_PKEY_decrypt(3)> respectively. However, note that
these functions perform encryption and decryption only. As public key
encryption is an expensive operation, normally you would wrap
an encrypted message in a "digital envelope" using the L<EVP_SealInit(3)> and
L<EVP_OpenInit(3)> functions.

=back

The L<EVP_BytesToKey(3)> function provides some limited support for password
based encryption. Careful selection of the parameters will provide a PKCS#5 PBKDF1 compatible
implementation. However, new applications should not typically use this (preferring, for example,
PBKDF2 from PCKS#5).

The L<B<EVP_Encode>I<XXX>|EVP_EncodeInit(3)> and
L<B<EVP_Decode>I<XXX>|EVP_EncodeInit(3)> functions implement base 64 encoding
and decoding.

All the symmetric algorithms (ciphers), digests and asymmetric algorithms
(public key algorithms) can be replaced by ENGINE modules providing alternative
implementations. If ENGINE implementations of ciphers or digests are registered
as defaults, then the various EVP functions will automatically use those
implementations automatically in preference to built in software
implementations. For more information, consult the engine(3) man page.

Although low-level algorithm specific functions exist for many algorithms
their use is discouraged. They cannot be used with an ENGINE and ENGINE
versions of new algorithms cannot be accessed using the low-level functions.
Also makes code harder to adapt to new algorithms and some options are not
cleanly supported at the low-level and some operations are more efficient
using the high-level interface.

=head1 SEE ALSO

L<EVP_DigestInit(3)>,
L<EVP_EncryptInit(3)>,
L<EVP_OpenInit(3)>,
L<EVP_SealInit(3)>,
L<EVP_DigestSignInit(3)>,
L<EVP_SignInit(3)>,
L<EVP_VerifyInit(3)>,
L<EVP_EncodeInit(3)>,
L<EVP_PKEY_new(3)>,
L<EVP_PKEY_fromdata(3)>,
L<EVP_PKEY_todata(3)>,
L<EVP_PKEY_keygen(3)>,
L<EVP_PKEY_print_private(3)>,
L<EVP_PKEY_decrypt(3)>,
L<EVP_PKEY_encrypt(3)>,
L<EVP_PKEY_sign(3)>,
L<EVP_PKEY_verify(3)>,
L<EVP_PKEY_verify_recover(3)>,
L<EVP_PKEY_derive(3)>,
L<EVP_BytesToKey(3)>,
L<ENGINE_by_id(3)>

=head1 COPYRIGHT

Copyright 2000-2021 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
