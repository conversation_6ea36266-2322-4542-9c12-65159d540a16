=pod

=head1 NAME

EVP_MAC-HMAC - The HMAC EVP_MAC implementation

=head1 DESCRIPTION

Support for computing HMAC MACs through the B<EVP_MAC> API.

This implementation uses EVP_MD functions to get access to the underlying
digest.

=head2 Identity

This implementation is identified with this name and properties, to be
used with EVP_MAC_fetch():

=over 4

=item "HMAC", "provider=default" or "provider=fips"

=back

=head2 Supported parameters

The general description of these parameters can be found in
L<EVP_MAC(3)/PARAMETERS>.

The following parameter can be set with EVP_MAC_CTX_set_params():

=over 4

=item "key" (B<OSSL_MAC_PARAM_KEY>) <octet string>

Sets the MAC key.
Setting this parameter is identical to passing a I<key> to L<EVP_MAC_init(3)>.

=item "digest" (B<OSSL_MAC_PARAM_DIGEST>) <UTF8 string>

Sets the name of the underlying digest to be used.

=item "properties" (B<OSSL_MAC_PARAM_PROPERTIES>) <UTF8 string>

Sets the properties to be queried when trying to fetch the underlying digest.
This must be given together with the digest naming parameter ("digest", or
B<OSSL_MAC_PARAM_DIGEST>) to be considered valid.

=item "digest-noinit" (B<OSSL_MAC_PARAM_DIGEST_NOINIT>) <integer>

A flag to set the MAC digest to not initialise the implementation
specific data.
The value 0 or 1 is expected.

=item "digest-oneshot" (B<OSSL_MAC_PARAM_DIGEST_ONESHOT>) <integer>

A flag to set the MAC digest to be a one-shot operation.
The value 0 or 1 is expected.

=item "tls-data-size" (B<OSSL_MAC_PARAM_TLS_DATA_SIZE>) <unsigned integer>

=back

=for comment The "flags" parameter is passed directly to HMAC_CTX_set_flags().

The following parameter can be retrieved with EVP_MAC_CTX_get_params():

=over 4

=item "size" (B<OSSL_MAC_PARAM_SIZE>) <unsigned integer>

The "size" parameter can also be retrieved with EVP_MAC_CTX_get_mac_size().
The length of the "size" parameter is equal to that of an B<unsigned int>.

=back

=over 4

=item "block-size" (B<OSSL_MAC_PARAM_BLOCK_SIZE>) <unsigned integer>

Gets the MAC block size.  The "block-size" parameter can also be retrieved with
EVP_MAC_CTX_get_block_size().

=back

=head1 SEE ALSO

L<EVP_MAC_CTX_get_params(3)>, L<EVP_MAC_CTX_set_params(3)>,
L<EVP_MAC(3)/PARAMETERS>, L<OSSL_PARAM(3)>, L<HMAC(3)>

=head1 COPYRIGHT

Copyright 2018-2021 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
