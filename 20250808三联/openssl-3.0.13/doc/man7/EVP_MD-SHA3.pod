=pod

=head1 NAME

EVP_MD-SHA3 - The SHA3 EVP_MD implementations

=head1 DESCRIPTION

Support for computing SHA3 digests through the B<EVP_MD> API.

=head2 Identities

This implementation is available with the FIPS provider as well as the
default provider, and includes the following varieties:

=over 4

=item "SHA3-224"

=item "SHA3-256"

=item "SHA3-384"

=item "SHA3-512"

=back

=head2 Gettable Parameters

This implementation supports the common gettable parameters described
in L<EVP_MD-common(7)>.

=head1 SEE ALSO

L<provider-digest(7)>, L<OSSL_PROVIDER-FIPS(7)>, L<OSSL_PROVIDER-default(7)>

=head1 COPYRIGHT

Copyright 2020 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
