<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>fips_config</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:root@localhost" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#NOTES">NOTES</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#HISTORY">HISTORY</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>fips_config - OpenSSL FIPS configuration</p>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>A separate configuration file, using the OpenSSL <a href="../man5/config.html">config(5)</a> syntax, is used to hold information about the FIPS module. This includes a digest of the shared library file, and status about the self-testing. This data is used automatically by the module itself for two purposes:</p>

<dl>

<dt id="Run-the-startup-FIPS-self-test-known-answer-tests-KATS">- Run the startup FIPS self-test known answer tests (KATS).</dt>
<dd>

<p>This is normally done once, at installation time, but may also be set up to run each time the module is used.</p>

</dd>
<dt id="Verify-the-modules-checksum">- Verify the module&#39;s checksum.</dt>
<dd>

<p>This is done each time the module is used.</p>

</dd>
</dl>

<p>This file is generated by the <a href="../man1/openssl-fipsinstall.html">openssl-fipsinstall(1)</a> program, and used internally by the FIPS module during its initialization.</p>

<p>The following options are supported. They should all appear in a section whose name is identified by the <b>fips</b> option in the <b>providers</b> section, as described in <a href="../man5/config.html">&quot;Provider Configuration Module&quot; in config(5)</a>.</p>

<dl>

<dt id="activate"><b>activate</b></dt>
<dd>

<p>If present, the module is activated. The value assigned to this name is not significant.</p>

</dd>
<dt id="install-version"><b>install-version</b></dt>
<dd>

<p>A version number for the fips install process. Should be 1.</p>

</dd>
<dt id="conditional-errors"><b>conditional-errors</b></dt>
<dd>

<p>The FIPS module normally enters an internal error mode if any self test fails. Once this error mode is active, no services or cryptographic algorithms are accessible from this point on. Continuous tests are a subset of the self tests (e.g., a key pair test during key generation, or the CRNG output test). Setting this value to <code>0</code> allows the error mode to not be triggered if any continuous test fails. The default value of <code>1</code> will trigger the error mode. Regardless of the value, the operation (e.g., key generation) that called the continuous test will return an error code if its continuous test fails. The operation may then be retried if the error mode has not been triggered.</p>

</dd>
<dt id="security-checks"><b>security-checks</b></dt>
<dd>

<p>This indicates if run-time checks related to enforcement of security parameters such as minimum security strength of keys and approved curve names are used. A value of &#39;1&#39; will perform the checks, otherwise if the value is &#39;0&#39; the checks are not performed and FIPS compliance must be done by procedures documented in the relevant Security Policy.</p>

</dd>
<dt id="module-mac"><b>module-mac</b></dt>
<dd>

<p>The calculated MAC of the FIPS provider file.</p>

</dd>
<dt id="install-status"><b>install-status</b></dt>
<dd>

<p>An indicator that the self-tests were successfully run. This should only be written after the module has successfully passed its self tests during installation. If this field is not present, then the self tests will run when the module loads.</p>

</dd>
<dt id="install-mac"><b>install-mac</b></dt>
<dd>

<p>A MAC of the value of the <b>install-status</b> option, to prevent accidental changes to that value. It is written-to at the same time as <b>install-status</b> is updated.</p>

</dd>
</dl>

<p>For example:</p>

<pre><code>[fips_sect]
activate = 1
install-version = 1
conditional-errors = 1
security-checks = 1
module-mac = 41:D0:FA:C2:5D:41:75:CD:7D:C3:90:55:6F:A4:DC
install-mac = FE:10:13:5A:D3:B4:C7:82:1B:1E:17:4C:AC:84:0C
install-status = INSTALL_SELF_TEST_KATS_RUN</code></pre>

<h1 id="NOTES">NOTES</h1>

<p>When using the FIPS provider, it is recommended that the <b>config_diagnostics</b> option is enabled to prevent accidental use of non-FIPS validated algorithms via broken or mistaken configuration. See <a href="../man5/config.html">config(5)</a>.</p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man5/config.html">config(5)</a> <a href="../man1/openssl-fipsinstall.html">openssl-fipsinstall(1)</a></p>

<h1 id="HISTORY">HISTORY</h1>

<p>This functionality was added in OpenSSL 3.0.</p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2019-2021 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the Apache License 2.0 (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


