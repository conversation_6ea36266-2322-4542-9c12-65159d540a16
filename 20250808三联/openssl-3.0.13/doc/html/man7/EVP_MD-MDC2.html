<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>EVP_MD-MDC2</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:root@localhost" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a>
    <ul>
      <li><a href="#Identity">Identity</a></li>
      <li><a href="#Gettable-Parameters">Gettable Parameters</a></li>
      <li><a href="#Settable-Context-Parameters">Settable Context Parameters</a></li>
    </ul>
  </li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>EVP_MD-MDC2 - The MDC2 EVP_MD implementation</p>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>Support for computing MDC2 digests through the <b>EVP_MD</b> API.</p>

<h2 id="Identity">Identity</h2>

<p>This implementation is only available with the legacy provider, and is identified with the name &quot;MDC2&quot;.</p>

<h2 id="Gettable-Parameters">Gettable Parameters</h2>

<p>This implementation supports the common gettable parameters described in <a href="../man7/EVP_MD-common.html">EVP_MD-common(7)</a>.</p>

<h2 id="Settable-Context-Parameters">Settable Context Parameters</h2>

<p>This implementation supports the following <a href="../man3/OSSL_PARAM.html">OSSL_PARAM(3)</a> entries, settable for an <b>EVP_MD_CTX</b> with <a href="../man3/EVP_MD_CTX_set_params.html">EVP_MD_CTX_set_params(3)</a>:</p>

<dl>

<dt id="pad-type-OSSL_DIGEST_PARAM_PAD_TYPE-unsigned-integer">&quot;pad-type&quot; (<b>OSSL_DIGEST_PARAM_PAD_TYPE</b>) &lt;unsigned integer&gt;</dt>
<dd>

<p>Sets the padding type to be used. Normally the final MDC2 block is padded with zeros. If the pad type is set to 2 then the final block is padded with 0x80 followed by zeros.</p>

</dd>
</dl>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man3/EVP_MD_CTX_set_params.html">EVP_MD_CTX_set_params(3)</a>, <a href="../man7/provider-digest.html">provider-digest(7)</a>, <a href="../man7/OSSL_PROVIDER-legacy.html">OSSL_PROVIDER-legacy(7)</a></p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2020-2021 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the Apache License 2.0 (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


