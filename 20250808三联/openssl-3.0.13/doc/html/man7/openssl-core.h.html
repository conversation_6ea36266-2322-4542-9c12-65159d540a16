<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>openssl-core.h</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:root@localhost" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#HISTORY">HISTORY</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>openssl/core.h - OpenSSL Core types</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/core.h&gt;</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>The <i>&lt;openssl/core.h&gt;</i> header defines a number of public types that are used to communicate between the OpenSSL libraries and implementation providers. These types are designed to minimise the need for intimate knowledge of internal structures between the OpenSSL libraries and the providers.</p>

<p>The types are:</p>

<dl>

<dt id="OSSL_DISPATCH-3"><a href="../man3/OSSL_DISPATCH.html">OSSL_DISPATCH(3)</a></dt>
<dd>

</dd>
<dt id="OSSL_ITEM-3"><a href="../man3/OSSL_ITEM.html">OSSL_ITEM(3)</a></dt>
<dd>

</dd>
<dt id="OSSL_ALGORITHM-3"><a href="../man3/OSSL_ALGORITHM.html">OSSL_ALGORITHM(3)</a></dt>
<dd>

</dd>
<dt id="OSSL_PARAM-3"><a href="../man3/OSSL_PARAM.html">OSSL_PARAM(3)</a></dt>
<dd>

</dd>
<dt id="OSSL_CALLBACK-3"><a href="../man3/OSSL_CALLBACK.html">OSSL_CALLBACK(3)</a></dt>
<dd>

</dd>
<dt id="OSSL_PASSPHRASE_CALLBACK-3"><a href="../man3/OSSL_PASSPHRASE_CALLBACK.html">OSSL_PASSPHRASE_CALLBACK(3)</a></dt>
<dd>

</dd>
</dl>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man7/openssl-core_dispatch.h.html">openssl-core_dispatch.h(7)</a></p>

<h1 id="HISTORY">HISTORY</h1>

<p>The types described here were added in OpenSSL 3.0.</p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2019-2021 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the Apache License 2.0 (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


