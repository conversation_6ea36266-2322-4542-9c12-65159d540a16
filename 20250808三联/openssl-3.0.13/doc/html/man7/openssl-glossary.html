<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>openssl-glossary</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:root@localhost" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#HISTORY">HISTORY</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>openssl-glossary - An OpenSSL Glossary</p>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<dl>

<dt id="Algorithm">Algorithm</dt>
<dd>

<p>Cryptographic primitives such as the SHA256 digest, or AES encryption are referred to in OpenSSL as &quot;algorithms&quot;. There can be more than one implementation for any given algorithm available for use.</p>

<p><a href="../man7/crypto.html">crypto(7)</a></p>

</dd>
<dt id="ASN.1-ASN1">ASN.1, ASN1</dt>
<dd>

<p>ASN.1 (&quot;Abstract Syntax Notation One&quot;) is a notation for describing abstract types and values. It is defined in the ITU-T documents X.680 to X.683:</p>

<p><a href="https://www.itu.int/rec/T-REC-X.680">https://www.itu.int/rec/T-REC-X.680</a>, <a href="https://www.itu.int/rec/T-REC-X.681">https://www.itu.int/rec/T-REC-X.681</a>, <a href="https://www.itu.int/rec/T-REC-X.682">https://www.itu.int/rec/T-REC-X.682</a>, <a href="https://www.itu.int/rec/T-REC-X.683">https://www.itu.int/rec/T-REC-X.683</a></p>

</dd>
<dt id="Base-Provider">Base Provider</dt>
<dd>

<p>An OpenSSL Provider that contains encoders and decoders for OpenSSL keys. All the algorithm implementations in the Base Provider are also available in the Default Provider.</p>

<p><a href="../man7/OSSL_PROVIDER-base.html">OSSL_PROVIDER-base(7)</a></p>

</dd>
<dt id="Decoder">Decoder</dt>
<dd>

<p>A decoder is a type of algorithm used for decoding keys and parameters from some external format such as PEM or DER.</p>

<p><a href="../man3/OSSL_DECODER_CTX_new_for_pkey.html">OSSL_DECODER_CTX_new_for_pkey(3)</a></p>

</dd>
<dt id="Default-Provider">Default Provider</dt>
<dd>

<p>An OpenSSL Provider that contains the most common OpenSSL algorithm implementations. It is loaded by default if no other provider is available. All the algorithm implementations in the Base Provider are also available in the Default Provider.</p>

<p><a href="../man7/OSSL_PROVIDER-default.html">OSSL_PROVIDER-default(7)</a></p>

</dd>
<dt id="DER-Distinguished-Encoding-Rules">DER (&quot;Distinguished Encoding Rules&quot;)</dt>
<dd>

<p>DER is a binary encoding of data, structured according to an ASN.1 specification. This is a common encoding used for cryptographic objects such as private and public keys, certificates, CRLs, ...</p>

<p>It is defined in ITU-T document X.690:</p>

<p><a href="https://www.itu.int/rec/T-REC-X.690">https://www.itu.int/rec/T-REC-X.690</a></p>

</dd>
<dt id="Encoder">Encoder</dt>
<dd>

<p>An encoder is a type of algorithm used for encoding keys and parameters to some external format such as PEM or DER.</p>

<p><a href="../man3/OSSL_ENCODER_CTX_new_for_pkey.html">OSSL_ENCODER_CTX_new_for_pkey(3)</a></p>

</dd>
<dt id="Explicit-Fetching">Explicit Fetching</dt>
<dd>

<p>Explicit Fetching is a type of Fetching (see Fetching). Explicit Fetching is where a function call is made to obtain an algorithm object representing an implementation such as <a href="../man3/EVP_MD_fetch.html">EVP_MD_fetch(3)</a> or <a href="../man3/EVP_CIPHER_fetch.html">EVP_CIPHER_fetch(3)</a></p>

</dd>
<dt id="Fetching">Fetching</dt>
<dd>

<p>Fetching is the process of looking through the available algorithm implementations, applying selection criteria (via a property query string), and finally choosing the implementation that will be used.</p>

<p>Also see Explicit Fetching and Implicit Fetching.</p>

<p><a href="../man7/crypto.html">crypto(7)</a></p>

</dd>
<dt id="FIPS-Provider">FIPS Provider</dt>
<dd>

<p>An OpenSSL Provider that contains OpenSSL algorithm implementations that have been validated according to the FIPS 140-2 standard.</p>

<p><a href="../man7/OSSL_PROVIDER-FIPS.html">OSSL_PROVIDER-FIPS(7)</a></p>

</dd>
<dt id="Implicit-Fetching">Implicit Fetching</dt>
<dd>

<p>Implicit Fetching is a type of Fetching (see Fetching). Implicit Fetching is where an algorithm object with no associated implementation is used such as the return value from <a href="../man3/EVP_sha256.html">EVP_sha256(3)</a> or <a href="../man3/EVP_aes_128_cbc.html">EVP_aes_128_cbc(3)</a>. With implicit fetching an implementation is fetched automatically using default selection criteria the first time the algorithm is used.</p>

</dd>
<dt id="Legacy-Provider">Legacy Provider</dt>
<dd>

<p>An OpenSSL Provider that contains algorithm implementations that are considered insecure or are no longer in common use.</p>

<p><a href="../man7/OSSL_PROVIDER-legacy.html">OSSL_PROVIDER-legacy(7)</a></p>

</dd>
<dt id="Library-Context">Library Context</dt>
<dd>

<p>A Library Context in OpenSSL is represented by the type <b>OSSL_LIB_CTX</b>. It can be thought of as a scope within which configuration options apply. If an application does not explicitly create a library context then the &quot;default&quot; one is used. Many OpenSSL functions can take a library context as an argument. A NULL value can always be passed to indicate the default library context.</p>

<p><a href="../man3/OSSL_LIB_CTX.html">OSSL_LIB_CTX(3)</a></p>

</dd>
<dt id="MSBLOB">MSBLOB</dt>
<dd>

<p>MSBLOB is a Microsoft specific binary format for RSA and DSA keys, both private and public. This form is never passphrase protected.</p>

</dd>
<dt id="Null-Provider">Null Provider</dt>
<dd>

<p>An OpenSSL Provider that contains no algorithm implementations. This can be useful to prevent the default provider from being automatically loaded in a library context.</p>

<p><a href="../man7/OSSL_PROVIDER-null.html">OSSL_PROVIDER-null(7)</a></p>

</dd>
<dt id="Operation">Operation</dt>
<dd>

<p>An operation is a group of OpenSSL functions with a common purpose such as encryption, or digesting.</p>

<p><a href="../man7/crypto.html">crypto(7)</a></p>

</dd>
<dt id="PEM-Privacy-Enhanced-Message">PEM (&quot;Privacy Enhanced Message&quot;)</dt>
<dd>

<p>PEM is a format used for encoding of binary content into a mail and ASCII friendly form. The content is a series of base64-encoded lines, surrounded by begin/end markers each on their own line. For example:</p>

<pre><code>-----BEGIN PRIVATE KEY-----
MIICdg....
... bhTQ==
-----END PRIVATE KEY-----</code></pre>

<p>Optional header line(s) may appear after the begin line, and their existence depends on the type of object being written or read.</p>

<p>For all OpenSSL uses, the binary content is expected to be a DER encoded structure.</p>

<p>This is defined in IETF RFC 1421:</p>

<p><a href="https://tools.ietf.org/html/rfc1421">https://tools.ietf.org/html/rfc1421</a></p>

</dd>
<dt id="PKCS-8">PKCS#8</dt>
<dd>

<p>PKCS#8 is a specification of ASN.1 structures that OpenSSL uses for storing or transmitting any private key in a key type agnostic manner. There are two structures worth noting for OpenSSL use, one that contains the key data in unencrypted form (known as &quot;PrivateKeyInfo&quot;) and an encrypted wrapper structure (known as &quot;EncryptedPrivateKeyInfo&quot;).</p>

<p>This is specified in RFC 5208:</p>

<p><a href="https://tools.ietf.org/html/rfc5208">https://tools.ietf.org/html/rfc5208</a></p>

</dd>
<dt id="Property">Property</dt>
<dd>

<p>A property is a way of classifying and selecting algorithm implementations. A property is a key/value pair expressed as a string. For example all algorithm implementations in the default provider have the property &quot;provider=default&quot;. An algorithm implementation can have multiple properties defined against it.</p>

<p>Also see Property Query String.</p>

<p><a href="../man7/property.html">property(7)</a></p>

</dd>
<dt id="Property-Query-String">Property Query String</dt>
<dd>

<p>A property query string is a string containing a sequence of properties that can be used to select an algorithm implementation. For example the query string &quot;provider=example,foo=bar&quot; will select algorithms from the &quot;example&quot; provider that have a &quot;foo&quot; property defined for them with a value of &quot;bar&quot;.</p>

<p>Property Query Strings are used during fetching. See Fetching.</p>

<p><a href="../man7/property.html">property(7)</a></p>

</dd>
<dt id="Provider">Provider</dt>
<dd>

<p>A provider in OpenSSL is a component that groups together algorithm implementations. Providers can come from OpenSSL itself or from third parties.</p>

<p><a href="../man7/provider.html">provider(7)</a></p>

</dd>
<dt id="PVK">PVK</dt>
<dd>

<p>PVK is a Microsoft specific binary format for RSA and DSA private keys. This form may be passphrase protected.</p>

</dd>
<dt id="SubjectPublicKeyInfo">SubjectPublicKeyInfo</dt>
<dd>

<p>SubjectPublicKeyInfo is an ASN.1 structure that OpenSSL uses for storing and transmitting any public key in a key type agnostic manner.</p>

<p>This is specified as part of the specification for certificates, RFC 5280:</p>

<p><a href="https://tools.ietf.org/html/rfc5280">https://tools.ietf.org/html/rfc5280</a></p>

</dd>
</dl>

<h1 id="HISTORY">HISTORY</h1>

<p>This glossary was added in OpenSSL 3.0.</p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2020-2023 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the Apache License 2.0 (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


