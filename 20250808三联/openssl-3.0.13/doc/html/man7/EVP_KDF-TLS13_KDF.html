<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>EVP_KDF-TLS13_KDF</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:root@localhost" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a>
    <ul>
      <li><a href="#Identity">Identity</a></li>
      <li><a href="#Supported-parameters">Supported parameters</a></li>
    </ul>
  </li>
  <li><a href="#NOTES">NOTES</a></li>
  <li><a href="#CONFORMING-TO">CONFORMING TO</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#HISTORY">HISTORY</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>EVP_KDF-TLS13_KDF - The TLS 1.3 EVP_KDF implementation</p>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>Support for computing the TLS 1.3 version of the <b>HKDF</b> KDF through the <b>EVP_KDF</b> API.</p>

<p>The EVP_KDF-TLS13_KDF algorithm implements the HKDF key derivation function as used by TLS 1.3.</p>

<h2 id="Identity">Identity</h2>

<p>&quot;TLS13-KDF&quot; is the name for this implementation; it can be used with the EVP_KDF_fetch() function.</p>

<h2 id="Supported-parameters">Supported parameters</h2>

<p>The supported parameters are:</p>

<dl>

<dt id="properties-OSSL_KDF_PARAM_PROPERTIES-UTF8-string">&quot;properties&quot; (<b>OSSL_KDF_PARAM_PROPERTIES</b>) &lt;UTF8 string&gt;</dt>
<dd>

</dd>
<dt id="digest-OSSL_KDF_PARAM_DIGEST-UTF8-string">&quot;digest&quot; (<b>OSSL_KDF_PARAM_DIGEST</b>) &lt;UTF8 string&gt;</dt>
<dd>

</dd>
<dt id="key-OSSL_KDF_PARAM_KEY-octet-string">&quot;key&quot; (<b>OSSL_KDF_PARAM_KEY</b>) &lt;octet string&gt;</dt>
<dd>

</dd>
<dt id="salt-OSSL_KDF_PARAM_SALT-octet-string">&quot;salt&quot; (<b>OSSL_KDF_PARAM_SALT</b>) &lt;octet string&gt;</dt>
<dd>

<p>These parameters work as described in <a href="../man3/EVP_KDF.html">&quot;PARAMETERS&quot; in EVP_KDF(3)</a>.</p>

</dd>
<dt id="prefix-OSSL_KDF_PARAM_PREFIX-octet-string">&quot;prefix&quot; (<b>OSSL_KDF_PARAM_PREFIX</b>) &lt;octet string&gt;</dt>
<dd>

<p>This parameter sets the label prefix on the specified TLS 1.3 KDF context. For TLS 1.3 this should be set to the ASCII string &quot;tls13 &quot; without a trailing zero byte. Refer to RFC 8446 section 7.1 &quot;Key Schedule&quot; for details.</p>

</dd>
<dt id="label-OSSL_KDF_PARAM_LABEL-octet-string">&quot;label&quot; (<b>OSSL_KDF_PARAM_LABEL</b>) &lt;octet string&gt;</dt>
<dd>

<p>This parameter sets the label on the specified TLS 1.3 KDF context. Refer to RFC 8446 section 7.1 &quot;Key Schedule&quot; for details.</p>

</dd>
<dt id="data-OSSL_KDF_PARAM_DATA-octet-string">&quot;data&quot; (<b>OSSL_KDF_PARAM_DATA</b>) &lt;octet string&gt;</dt>
<dd>

<p>This parameter sets the context data on the specified TLS 1.3 KDF context. Refer to RFC 8446 section 7.1 &quot;Key Schedule&quot; for details.</p>

</dd>
<dt id="mode-OSSL_KDF_PARAM_MODE-UTF8-string-or-integer">&quot;mode&quot; (<b>OSSL_KDF_PARAM_MODE</b>) &lt;UTF8 string&gt; or &lt;integer&gt;</dt>
<dd>

<p>This parameter sets the mode for the TLS 1.3 KDF operation. There are two modes that are currently defined:</p>

<dl>

<dt id="EXTRACT_ONLY-or-EVP_KDF_HKDF_MODE_EXTRACT_ONLY">&quot;EXTRACT_ONLY&quot; or <b>EVP_KDF_HKDF_MODE_EXTRACT_ONLY</b></dt>
<dd>

<p>In this mode calling <a href="../man3/EVP_KDF_derive.html">EVP_KDF_derive(3)</a> will just perform the extract operation. The value returned will be the intermediate fixed-length pseudorandom key K. The <i>keylen</i> parameter must match the size of K, which can be looked up by calling EVP_KDF_CTX_get_kdf_size() after setting the mode and digest.</p>

<p>The digest, key and salt values must be set before a key is derived otherwise an error will occur.</p>

</dd>
<dt id="EXPAND_ONLY-or-EVP_KDF_HKDF_MODE_EXPAND_ONLY">&quot;EXPAND_ONLY&quot; or <b>EVP_KDF_HKDF_MODE_EXPAND_ONLY</b></dt>
<dd>

<p>In this mode calling <a href="../man3/EVP_KDF_derive.html">EVP_KDF_derive(3)</a> will just perform the expand operation. The input key should be set to the intermediate fixed-length pseudorandom key K returned from a previous extract operation.</p>

<p>The digest, key and info values must be set before a key is derived otherwise an error will occur.</p>

</dd>
</dl>

</dd>
</dl>

<h1 id="NOTES">NOTES</h1>

<p>This KDF is intended for use by the TLS 1.3 implementation in libssl. It does not support all the options and capabilities that HKDF does.</p>

<p>The <i>OSSL_PARAM</i> array passed to <a href="../man3/EVP_KDF_derive.html">EVP_KDF_derive(3)</a> or <a href="../man3/EVP_KDF_CTX_set_params.html">EVP_KDF_CTX_set_params(3)</a> must specify all of the parameters required. This KDF does not support a piecemeal approach to providing these.</p>

<p>A context for a TLS 1.3 KDF can be obtained by calling:</p>

<pre><code>EVP_KDF *kdf = EVP_KDF_fetch(NULL, &quot;TLS13-KDF&quot;, NULL);
EVP_KDF_CTX *kctx = EVP_KDF_CTX_new(kdf);</code></pre>

<p>The output length of a TLS 1.3 KDF expand operation is specified via the <i>keylen</i> parameter to the <a href="../man3/EVP_KDF_derive.html">EVP_KDF_derive(3)</a> function. When using EVP_KDF_HKDF_MODE_EXTRACT_ONLY the <i>keylen</i> parameter must equal the size of the intermediate fixed-length pseudorandom key otherwise an error will occur. For that mode, the fixed output size can be looked up by calling EVP_KDF_CTX_get_kdf_size() after setting the mode and digest on the <b>EVP_KDF_CTX</b>.</p>

<h1 id="CONFORMING-TO">CONFORMING TO</h1>

<p>RFC 8446</p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man3/EVP_KDF.html">EVP_KDF(3)</a>, <a href="../man3/EVP_KDF_CTX_new.html">EVP_KDF_CTX_new(3)</a>, <a href="../man3/EVP_KDF_CTX_free.html">EVP_KDF_CTX_free(3)</a>, <a href="../man3/EVP_KDF_CTX_get_kdf_size.html">EVP_KDF_CTX_get_kdf_size(3)</a>, <a href="../man3/EVP_KDF_CTX_set_params.html">EVP_KDF_CTX_set_params(3)</a>, <a href="../man3/EVP_KDF_derive.html">EVP_KDF_derive(3)</a>, <a href="../man3/EVP_KDF.html">&quot;PARAMETERS&quot; in EVP_KDF(3)</a>, <a href="../man7/EVP_KDF-HKDF.html">EVP_KDF-HKDF(7)</a></p>

<h1 id="HISTORY">HISTORY</h1>

<p>This functionality was added in OpenSSL 3.0.</p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2021 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the Apache License 2.0 (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


