<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>EVP_MD-common</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:root@localhost" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>EVP_MD-common - The OpenSSL EVP_MD implementations, common things</p>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>All the OpenSSL EVP_MD implementations understand the following <a href="../man3/OSSL_PARAM.html">OSSL_PARAM(3)</a> entries that are gettable with <a href="../man3/EVP_MD_get_params.html">EVP_MD_get_params(3)</a>, as well as these:</p>

<dl>

<dt id="blocksize-OSSL_DIGEST_PARAM_BLOCK_SIZE-unsigned-integer">&quot;blocksize&quot; (<b>OSSL_DIGEST_PARAM_BLOCK_SIZE</b>) &lt;unsigned integer&gt;</dt>
<dd>

<p>The digest block size. The length of the &quot;blocksize&quot; parameter should not exceed that of a <b>size_t</b>.</p>

<p>This value can also be retrieved with <a href="../man3/EVP_MD_get_block_size.html">EVP_MD_get_block_size(3)</a>.</p>

</dd>
<dt id="size-OSSL_DIGEST_PARAM_SIZE-unsigned-integer">&quot;size&quot; (<b>OSSL_DIGEST_PARAM_SIZE</b>) &lt;unsigned integer&gt;</dt>
<dd>

<p>The digest output size. The length of the &quot;size&quot; parameter should not exceed that of a <b>size_t</b>.</p>

<p>This value can also be retrieved with <a href="../man3/EVP_MD_get_size.html">EVP_MD_get_size(3)</a>.</p>

</dd>
<dt id="flags-OSSL_DIGEST_PARAM_FLAGS-unsigned-integer">&quot;flags&quot; (<b>OSSL_DIGEST_PARAM_FLAGS</b>) &lt;unsigned integer&gt;</dt>
<dd>

<p>Diverse flags that describe exceptional behaviour for the digest. These flags are described in <a href="../man3/EVP_MD_meth_set_flags.html">&quot;DESCRIPTION&quot; in EVP_MD_meth_set_flags(3)</a>.</p>

<p>The length of the &quot;flags&quot; parameter should equal that of an <b>unsigned long int</b>.</p>

<p>This value can also be retrieved with <a href="../man3/EVP_MD_get_flags.html">EVP_MD_get_flags(3)</a>.</p>

</dd>
</dl>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man3/EVP_MD_get_params.html">EVP_MD_get_params(3)</a>, <a href="../man7/provider-digest.html">provider-digest(7)</a></p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2020-2021 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the Apache License 2.0 (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


