<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>EVP_MAC-Siphash</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:root@localhost" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a>
    <ul>
      <li><a href="#Identity">Identity</a></li>
      <li><a href="#Supported-parameters">Supported parameters</a></li>
    </ul>
  </li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>EVP_MAC-Siphash - The Siphash EVP_MAC implementation</p>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>Support for computing Siphash MACs through the <b>EVP_MAC</b> API.</p>

<h2 id="Identity">Identity</h2>

<p>This implementation is identified with this name and properties, to be used with EVP_MAC_fetch():</p>

<dl>

<dt id="SIPHASH-provider-default">&quot;SIPHASH&quot;, &quot;provider=default&quot;</dt>
<dd>

</dd>
</dl>

<h2 id="Supported-parameters">Supported parameters</h2>

<p>The general description of these parameters can be found in <a href="../man3/EVP_MAC.html">&quot;PARAMETERS&quot; in EVP_MAC(3)</a>.</p>

<p>All these parameters can be set with EVP_MAC_CTX_set_params(). Furthermore, the &quot;size&quot; parameter can be retrieved with EVP_MAC_CTX_get_params(), or with EVP_MAC_CTX_get_mac_size(). The length of the &quot;size&quot; parameter should not exceed that of a <b>size_t</b>.</p>

<dl>

<dt id="key-OSSL_MAC_PARAM_KEY-octet-string">&quot;key&quot; (<b>OSSL_MAC_PARAM_KEY</b>) &lt;octet string&gt;</dt>
<dd>

<p>Sets the MAC key. Setting this parameter is identical to passing a <i>key</i> to <a href="../man3/EVP_MAC_init.html">EVP_MAC_init(3)</a>.</p>

</dd>
<dt id="size-OSSL_MAC_PARAM_SIZE-unsigned-integer">&quot;size&quot; (<b>OSSL_MAC_PARAM_SIZE</b>) &lt;unsigned integer&gt;</dt>
<dd>

<p>Sets the MAC size.</p>

</dd>
<dt id="c-rounds-OSSL_MAC_PARAM_C_ROUNDS-unsigned-integer">&quot;c-rounds&quot; (<b>OSSL_MAC_PARAM_C_ROUNDS</b>) &lt;unsigned integer&gt;</dt>
<dd>

<p>Specifies the number of rounds per message block. By default this is <i>2</i>.</p>

</dd>
<dt id="d-rounds-OSSL_MAC_PARAM_D_ROUNDS-unsigned-integer">&quot;d-rounds&quot; (<b>OSSL_MAC_PARAM_D_ROUNDS</b>) &lt;unsigned integer&gt;</dt>
<dd>

<p>Specifies the number of finalisation rounds. By default this is <i>4</i>.</p>

</dd>
</dl>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man3/EVP_MAC_CTX_get_params.html">EVP_MAC_CTX_get_params(3)</a>, <a href="../man3/EVP_MAC_CTX_set_params.html">EVP_MAC_CTX_set_params(3)</a>, <a href="../man3/EVP_MAC.html">&quot;PARAMETERS&quot; in EVP_MAC(3)</a>, <a href="../man3/OSSL_PARAM.html">OSSL_PARAM(3)</a></p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2018-2021 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the Apache License 2.0 (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


