<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>EVP_ASYM_CIPHER-SM2</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:root@localhost" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a>
    <ul>
      <li><a href="#SM2-Asymmetric-Cipher-parameters">SM2 Asymmetric Cipher parameters</a></li>
    </ul>
  </li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>EVP_ASYM_CIPHER-SM2 - SM2 Asymmetric Cipher algorithm support</p>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>Asymmetric Cipher support for the <b>SM2</b> key type.</p>

<h2 id="SM2-Asymmetric-Cipher-parameters">SM2 Asymmetric Cipher parameters</h2>

<dl>

<dt id="digest-OSSL_ASYM_CIPHER_PARAM_DIGEST-UTF8-string">&quot;digest&quot; (<b>OSSL_ASYM_CIPHER_PARAM_DIGEST</b>) &lt;UTF8 string&gt;</dt>
<dd>

</dd>
<dt id="digest-props-OSSL_ASYM_CIPHER_PARAM_DIGEST_PROPS-UTF8-string">&quot;digest-props&quot; (<b>OSSL_ASYM_CIPHER_PARAM_DIGEST_PROPS</b>) &lt;UTF8 string&gt;</dt>
<dd>

<p>See <a href="../man7/provider-asym_cipher.html">&quot;Asymmetric Cipher Parameters&quot; in provider-asym_cipher(7)</a>.</p>

</dd>
</dl>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man7/EVP_PKEY-SM2.html">EVP_PKEY-SM2(7)</a>, <a href="../man3/EVP_PKEY.html">EVP_PKEY(3)</a>, <a href="../man7/provider-asym_cipher.html">provider-asym_cipher(7)</a>, <a href="../man7/provider-keymgmt.html">provider-keymgmt(7)</a>, <a href="../man7/OSSL_PROVIDER-default.html">OSSL_PROVIDER-default(7)</a></p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2020 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the Apache License 2.0 (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


