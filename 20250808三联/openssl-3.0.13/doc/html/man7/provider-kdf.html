<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>provider-kdf</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:root@localhost" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a>
    <ul>
      <li><a href="#Context-Management-Functions">Context Management Functions</a></li>
      <li><a href="#Encryption-Decryption-Functions">Encryption/Decryption Functions</a></li>
      <li><a href="#KDF-Parameters">KDF Parameters</a></li>
    </ul>
  </li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#NOTES">NOTES</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#HISTORY">HISTORY</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>provider-kdf - The KDF library &lt;-&gt; provider functions</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/core_dispatch.h&gt;
#include &lt;openssl/core_names.h&gt;

/*
 * None of these are actual functions, but are displayed like this for
 * the function signatures for functions that are offered as function
 * pointers in OSSL_DISPATCH arrays.
 */

/* Context management */
void *OSSL_FUNC_kdf_newctx(void *provctx);
void OSSL_FUNC_kdf_freectx(void *kctx);
void *OSSL_FUNC_kdf_dupctx(void *src);

/* Encryption/decryption */
int OSSL_FUNC_kdf_reset(void *kctx);
int OSSL_FUNC_kdf_derive(void *kctx, unsigned char *key, size_t keylen,
                         const OSSL_PARAM params[]);

/* KDF parameter descriptors */
const OSSL_PARAM *OSSL_FUNC_kdf_gettable_params(void *provctx);
const OSSL_PARAM *OSSL_FUNC_kdf_gettable_ctx_params(void *kcxt, void *provctx);
const OSSL_PARAM *OSSL_FUNC_kdf_settable_ctx_params(void *kcxt, void *provctx);

/* KDF parameters */
int OSSL_FUNC_kdf_get_params(OSSL_PARAM params[]);
int OSSL_FUNC_kdf_get_ctx_params(void *kctx, OSSL_PARAM params[]);
int OSSL_FUNC_kdf_set_ctx_params(void *kctx, const OSSL_PARAM params[]);</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>This documentation is primarily aimed at provider authors. See <a href="../man7/provider.html">provider(7)</a> for further information.</p>

<p>The KDF operation enables providers to implement KDF algorithms and make them available to applications via the API functions <a href="../man3/EVP_KDF_CTX_reset.html">EVP_KDF_CTX_reset(3)</a>, and <a href="../man3/EVP_KDF_derive.html">EVP_KDF_derive(3)</a>.</p>

<p>All &quot;functions&quot; mentioned here are passed as function pointers between <i>libcrypto</i> and the provider in <a href="../man3/OSSL_DISPATCH.html">OSSL_DISPATCH(3)</a> arrays via <a href="../man3/OSSL_ALGORITHM.html">OSSL_ALGORITHM(3)</a> arrays that are returned by the provider&#39;s provider_query_operation() function (see <a href="../man7/provider-base.html">&quot;Provider Functions&quot; in provider-base(7)</a>).</p>

<p>All these &quot;functions&quot; have a corresponding function type definition named <b>OSSL_FUNC_{name}_fn</b>, and a helper function to retrieve the function pointer from an <a href="../man3/OSSL_DISPATCH.html">OSSL_DISPATCH(3)</a> element named <b>OSSL_FUNC_{name}</b>. For example, the &quot;function&quot; OSSL_FUNC_kdf_newctx() has these:</p>

<pre><code>typedef void *(OSSL_FUNC_kdf_newctx_fn)(void *provctx);
static ossl_inline OSSL_FUNC_kdf_newctx_fn
    OSSL_FUNC_kdf_newctx(const OSSL_DISPATCH *opf);</code></pre>

<p><a href="../man3/OSSL_DISPATCH.html">OSSL_DISPATCH(3)</a> array entries are identified by numbers that are provided as macros in <a href="../man7/openssl-core_dispatch.h.html">openssl-core_dispatch.h(7)</a>, as follows:</p>

<pre><code>OSSL_FUNC_kdf_newctx               OSSL_FUNC_KDF_NEWCTX
OSSL_FUNC_kdf_freectx              OSSL_FUNC_KDF_FREECTX
OSSL_FUNC_kdf_dupctx               OSSL_FUNC_KDF_DUPCTX

OSSL_FUNC_kdf_reset                OSSL_FUNC_KDF_RESET
OSSL_FUNC_kdf_derive               OSSL_FUNC_KDF_DERIVE

OSSL_FUNC_kdf_get_params           OSSL_FUNC_KDF_GET_PARAMS
OSSL_FUNC_kdf_get_ctx_params       OSSL_FUNC_KDF_GET_CTX_PARAMS
OSSL_FUNC_kdf_set_ctx_params       OSSL_FUNC_KDF_SET_CTX_PARAMS

OSSL_FUNC_kdf_gettable_params      OSSL_FUNC_KDF_GETTABLE_PARAMS
OSSL_FUNC_kdf_gettable_ctx_params  OSSL_FUNC_KDF_GETTABLE_CTX_PARAMS
OSSL_FUNC_kdf_settable_ctx_params  OSSL_FUNC_KDF_SETTABLE_CTX_PARAMS</code></pre>

<p>A KDF algorithm implementation may not implement all of these functions. In order to be a consistent set of functions, at least the following functions must be implemented: OSSL_FUNC_kdf_newctx(), OSSL_FUNC_kdf_freectx(), OSSL_FUNC_kdf_set_ctx_params(), OSSL_FUNC_kdf_derive(). All other functions are optional.</p>

<h2 id="Context-Management-Functions">Context Management Functions</h2>

<p>OSSL_FUNC_kdf_newctx() should create and return a pointer to a provider side structure for holding context information during a KDF operation. A pointer to this context will be passed back in a number of the other KDF operation function calls. The parameter <i>provctx</i> is the provider context generated during provider initialisation (see <a href="../man7/provider.html">provider(7)</a>).</p>

<p>OSSL_FUNC_kdf_freectx() is passed a pointer to the provider side KDF context in the <i>kctx</i> parameter. If it receives NULL as <i>kctx</i> value, it should not do anything other than return. This function should free any resources associated with that context.</p>

<p>OSSL_FUNC_kdf_dupctx() should duplicate the provider side KDF context in the <i>kctx</i> parameter and return the duplicate copy.</p>

<h2 id="Encryption-Decryption-Functions">Encryption/Decryption Functions</h2>

<p>OSSL_FUNC_kdf_reset() initialises a KDF operation given a provider side KDF context in the <i>kctx</i> parameter.</p>

<p>OSSL_FUNC_kdf_derive() performs the KDF operation after processing the <i>params</i> as per OSSL_FUNC_kdf_set_ctx_params(). The <i>kctx</i> parameter contains a pointer to the provider side context. The resulting key of the desired <i>keylen</i> should be written to <i>key</i>. If the algorithm does not support the requested <i>keylen</i> the function must return error.</p>

<h2 id="KDF-Parameters">KDF Parameters</h2>

<p>See <a href="../man3/OSSL_PARAM.html">OSSL_PARAM(3)</a> for further details on the parameters structure used by these functions.</p>

<p>OSSL_FUNC_kdf_get_params() gets details of parameter values associated with the provider algorithm and stores them in <i>params</i>.</p>

<p>OSSL_FUNC_kdf_set_ctx_params() sets KDF parameters associated with the given provider side KDF context <i>kctx</i> to <i>params</i>. Any parameter settings are additional to any that were previously set. Passing NULL for <i>params</i> should return true.</p>

<p>OSSL_FUNC_kdf_get_ctx_params() retrieves gettable parameter values associated with the given provider side KDF context <i>kctx</i> and stores them in <i>params</i>. Passing NULL for <i>params</i> should return true.</p>

<p>OSSL_FUNC_kdf_gettable_params(), OSSL_FUNC_kdf_gettable_ctx_params(), and OSSL_FUNC_kdf_settable_ctx_params() all return constant <a href="../man3/OSSL_PARAM.html">OSSL_PARAM(3)</a> arrays as descriptors of the parameters that OSSL_FUNC_kdf_get_params(), OSSL_FUNC_kdf_get_ctx_params(), and OSSL_FUNC_kdf_set_ctx_params() can handle, respectively. OSSL_FUNC_kdf_gettable_ctx_params() and OSSL_FUNC_kdf_settable_ctx_params() will return the parameters associated with the provider side context <i>kctx</i> in its current state if it is not NULL. Otherwise, they return the parameters associated with the provider side algorithm <i>provctx</i>.</p>

<p>Parameters currently recognised by built-in KDFs are as follows. Not all parameters are relevant to, or are understood by all KDFs:</p>

<dl>

<dt id="size-OSSL_KDF_PARAM_SIZE-unsigned-integer">&quot;size&quot; (<b>OSSL_KDF_PARAM_SIZE</b>) &lt;unsigned integer&gt;</dt>
<dd>

<p>Gets the output size from the associated KDF ctx. If the algorithm produces a variable amount of output, SIZE_MAX should be returned. If the input parameters required to calculate the fixed output size have not yet been supplied, 0 should be returned indicating an error.</p>

</dd>
<dt id="key-OSSL_KDF_PARAM_KEY-octet-string">&quot;key&quot; (<b>OSSL_KDF_PARAM_KEY</b>) &lt;octet string&gt;</dt>
<dd>

<p>Sets the key in the associated KDF ctx.</p>

</dd>
<dt id="secret-OSSL_KDF_PARAM_SECRET-octet-string">&quot;secret&quot; (<b>OSSL_KDF_PARAM_SECRET</b>) &lt;octet string&gt;</dt>
<dd>

<p>Sets the secret in the associated KDF ctx.</p>

</dd>
<dt id="pass-OSSL_KDF_PARAM_PASSWORD-octet-string">&quot;pass&quot; (<b>OSSL_KDF_PARAM_PASSWORD</b>) &lt;octet string&gt;</dt>
<dd>

<p>Sets the password in the associated KDF ctx.</p>

</dd>
<dt id="cipher-OSSL_KDF_PARAM_CIPHER-UTF8-string">&quot;cipher&quot; (<b>OSSL_KDF_PARAM_CIPHER</b>) &lt;UTF8 string&gt;</dt>
<dd>

</dd>
<dt id="digest-OSSL_KDF_PARAM_DIGEST-UTF8-string">&quot;digest&quot; (<b>OSSL_KDF_PARAM_DIGEST</b>) &lt;UTF8 string&gt;</dt>
<dd>

</dd>
<dt id="mac-OSSL_KDF_PARAM_MAC-UTF8-string">&quot;mac&quot; (<b>OSSL_KDF_PARAM_MAC</b>) &lt;UTF8 string&gt;</dt>
<dd>

<p>Sets the name of the underlying cipher, digest or MAC to be used. It must name a suitable algorithm for the KDF that&#39;s being used.</p>

</dd>
<dt id="maclen-OSSL_KDF_PARAM_MAC_SIZE-octet-string">&quot;maclen&quot; (<b>OSSL_KDF_PARAM_MAC_SIZE</b>) &lt;octet string&gt;</dt>
<dd>

<p>Sets the length of the MAC in the associated KDF ctx.</p>

</dd>
<dt id="properties-OSSL_KDF_PARAM_PROPERTIES-UTF8-string">&quot;properties&quot; (<b>OSSL_KDF_PARAM_PROPERTIES</b>) &lt;UTF8 string&gt;</dt>
<dd>

<p>Sets the properties to be queried when trying to fetch the underlying algorithm. This must be given together with the algorithm naming parameter to be considered valid.</p>

</dd>
<dt id="iter-OSSL_KDF_PARAM_ITER-unsigned-integer">&quot;iter&quot; (<b>OSSL_KDF_PARAM_ITER</b>) &lt;unsigned integer&gt;</dt>
<dd>

<p>Sets the number of iterations in the associated KDF ctx.</p>

</dd>
<dt id="mode-OSSL_KDF_PARAM_MODE-UTF8-string">&quot;mode&quot; (<b>OSSL_KDF_PARAM_MODE</b>) &lt;UTF8 string&gt;</dt>
<dd>

<p>Sets the mode in the associated KDF ctx.</p>

</dd>
<dt id="pkcs5-OSSL_KDF_PARAM_PKCS5-integer">&quot;pkcs5&quot; (<b>OSSL_KDF_PARAM_PKCS5</b>) &lt;integer&gt;</dt>
<dd>

<p>Enables or disables the SP800-132 compliance checks. A mode of 0 enables the compliance checks.</p>

<p>The checks performed are:</p>

<dl>

<dt id="the-iteration-count-is-at-least-1000">- the iteration count is at least 1000.</dt>
<dd>

</dd>
<dt id="the-salt-length-is-at-least-128-bits">- the salt length is at least 128 bits.</dt>
<dd>

</dd>
<dt id="the-derived-key-length-is-at-least-112-bits">- the derived key length is at least 112 bits.</dt>
<dd>

</dd>
</dl>

</dd>
<dt id="ukm-OSSL_KDF_PARAM_UKM-octet-string">&quot;ukm&quot; (<b>OSSL_KDF_PARAM_UKM</b>) &lt;octet string&gt;</dt>
<dd>

<p>Sets an optional random string that is provided by the sender called &quot;partyAInfo&quot;. In CMS this is the user keying material.</p>

</dd>
<dt id="cekalg-OSSL_KDF_PARAM_CEK_ALG-UTF8-string">&quot;cekalg&quot; (<b>OSSL_KDF_PARAM_CEK_ALG</b>) &lt;UTF8 string&gt;</dt>
<dd>

<p>Sets the CEK wrapping algorithm name in the associated KDF ctx.</p>

</dd>
<dt id="n-OSSL_KDF_PARAM_SCRYPT_N-unsigned-integer">&quot;n&quot; (<b>OSSL_KDF_PARAM_SCRYPT_N</b>) &lt;unsigned integer&gt;</dt>
<dd>

<p>Sets the scrypt work factor parameter N in the associated KDF ctx.</p>

</dd>
<dt id="r-OSSL_KDF_PARAM_SCRYPT_R-unsigned-integer">&quot;r&quot; (<b>OSSL_KDF_PARAM_SCRYPT_R</b>) &lt;unsigned integer&gt;</dt>
<dd>

<p>Sets the scrypt work factor parameter r in the associated KDF ctx.</p>

</dd>
<dt id="p-OSSL_KDF_PARAM_SCRYPT_P-unsigned-integer">&quot;p&quot; (<b>OSSL_KDF_PARAM_SCRYPT_P</b>) &lt;unsigned integer&gt;</dt>
<dd>

<p>Sets the scrypt work factor parameter p in the associated KDF ctx.</p>

</dd>
<dt id="maxmem_bytes-OSSL_KDF_PARAM_SCRYPT_MAXMEM-unsigned-integer">&quot;maxmem_bytes&quot; (<b>OSSL_KDF_PARAM_SCRYPT_MAXMEM</b>) &lt;unsigned integer&gt;</dt>
<dd>

<p>Sets the scrypt work factor parameter maxmem in the associated KDF ctx.</p>

</dd>
<dt id="prefix-OSSL_KDF_PARAM_PREFIX-octet-string">&quot;prefix&quot; (<b>OSSL_KDF_PARAM_PREFIX</b>) &lt;octet string&gt;</dt>
<dd>

<p>Sets the prefix string using by the TLS 1.3 version of HKDF in the associated KDF ctx.</p>

</dd>
<dt id="label-OSSL_KDF_PARAM_LABEL-octet-string">&quot;label&quot; (<b>OSSL_KDF_PARAM_LABEL</b>) &lt;octet string&gt;</dt>
<dd>

<p>Sets the label string using by the TLS 1.3 version of HKDF in the associated KDF ctx.</p>

</dd>
<dt id="data-OSSL_KDF_PARAM_DATA-octet-string">&quot;data&quot; (<b>OSSL_KDF_PARAM_DATA</b>) &lt;octet string&gt;</dt>
<dd>

<p>Sets the context string using by the TLS 1.3 version of HKDF in the associated KDF ctx.</p>

</dd>
<dt id="info-OSSL_KDF_PARAM_INFO-octet-string">&quot;info&quot; (<b>OSSL_KDF_PARAM_INFO</b>) &lt;octet string&gt;</dt>
<dd>

<p>Sets the optional shared info in the associated KDF ctx.</p>

</dd>
<dt id="seed-OSSL_KDF_PARAM_SEED-octet-string">&quot;seed&quot; (<b>OSSL_KDF_PARAM_SEED</b>) &lt;octet string&gt;</dt>
<dd>

<p>Sets the IV in the associated KDF ctx.</p>

</dd>
<dt id="xcghash-OSSL_KDF_PARAM_SSHKDF_XCGHASH-octet-string">&quot;xcghash&quot; (<b>OSSL_KDF_PARAM_SSHKDF_XCGHASH</b>) &lt;octet string&gt;</dt>
<dd>

<p>Sets the xcghash in the associated KDF ctx.</p>

</dd>
<dt id="session_id-OSSL_KDF_PARAM_SSHKDF_SESSION_ID-octet-string">&quot;session_id&quot; (<b>OSSL_KDF_PARAM_SSHKDF_SESSION_ID</b>) &lt;octet string&gt;</dt>
<dd>

<p>Sets the session ID in the associated KDF ctx.</p>

</dd>
<dt id="type-OSSL_KDF_PARAM_SSHKDF_TYPE-UTF8-string">&quot;type&quot; (<b>OSSL_KDF_PARAM_SSHKDF_TYPE</b>) &lt;UTF8 string&gt;</dt>
<dd>

<p>Sets the SSH KDF type parameter in the associated KDF ctx. There are six supported types:</p>

<dl>

<dt id="EVP_KDF_SSHKDF_TYPE_INITIAL_IV_CLI_TO_SRV">EVP_KDF_SSHKDF_TYPE_INITIAL_IV_CLI_TO_SRV</dt>
<dd>

<p>The Initial IV from client to server. A single char of value 65 (ASCII char &#39;A&#39;).</p>

</dd>
<dt id="EVP_KDF_SSHKDF_TYPE_INITIAL_IV_SRV_TO_CLI">EVP_KDF_SSHKDF_TYPE_INITIAL_IV_SRV_TO_CLI</dt>
<dd>

<p>The Initial IV from server to client A single char of value 66 (ASCII char &#39;B&#39;).</p>

</dd>
<dt id="EVP_KDF_SSHKDF_TYPE_ENCRYPTION_KEY_CLI_TO_SRV">EVP_KDF_SSHKDF_TYPE_ENCRYPTION_KEY_CLI_TO_SRV</dt>
<dd>

<p>The Encryption Key from client to server A single char of value 67 (ASCII char &#39;C&#39;).</p>

</dd>
<dt id="EVP_KDF_SSHKDF_TYPE_ENCRYPTION_KEY_SRV_TO_CLI">EVP_KDF_SSHKDF_TYPE_ENCRYPTION_KEY_SRV_TO_CLI</dt>
<dd>

<p>The Encryption Key from server to client A single char of value 68 (ASCII char &#39;D&#39;).</p>

</dd>
<dt id="EVP_KDF_SSHKDF_TYPE_INTEGRITY_KEY_CLI_TO_SRV">EVP_KDF_SSHKDF_TYPE_INTEGRITY_KEY_CLI_TO_SRV</dt>
<dd>

<p>The Integrity Key from client to server A single char of value 69 (ASCII char &#39;E&#39;).</p>

</dd>
<dt id="EVP_KDF_SSHKDF_TYPE_INTEGRITY_KEY_SRV_TO_CLI">EVP_KDF_SSHKDF_TYPE_INTEGRITY_KEY_SRV_TO_CLI</dt>
<dd>

<p>The Integrity Key from client to server A single char of value 70 (ASCII char &#39;F&#39;).</p>

</dd>
</dl>

</dd>
<dt id="constant-OSSL_KDF_PARAM_CONSTANT-octet-string">&quot;constant&quot; (<b>OSSL_KDF_PARAM_CONSTANT</b>) &lt;octet string&gt;</dt>
<dd>

<p>Sets the constant value in the associated KDF ctx.</p>

</dd>
<dt id="id-OSSL_KDF_PARAM_PKCS12_ID-integer">&quot;id&quot; (<b>OSSL_KDF_PARAM_PKCS12_ID</b>) &lt;integer&gt;</dt>
<dd>

<p>Sets the intended usage of the output bits in the associated KDF ctx. It is defined as per RFC 7292 section B.3.</p>

</dd>
</dl>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>OSSL_FUNC_kdf_newctx() and OSSL_FUNC_kdf_dupctx() should return the newly created provider side KDF context, or NULL on failure.</p>

<p>OSSL_FUNC_kdf_derive(), OSSL_FUNC_kdf_get_params(), OSSL_FUNC_kdf_get_ctx_params() and OSSL_FUNC_kdf_set_ctx_params() should return 1 for success or 0 on error.</p>

<p>OSSL_FUNC_kdf_gettable_params(), OSSL_FUNC_kdf_gettable_ctx_params() and OSSL_FUNC_kdf_settable_ctx_params() should return a constant <a href="../man3/OSSL_PARAM.html">OSSL_PARAM(3)</a> array, or NULL if none is offered.</p>

<h1 id="NOTES">NOTES</h1>

<p>The KDF life-cycle is described in <a href="../man7/life_cycle-kdf.html">life_cycle-kdf(7)</a>. Providers should ensure that the various transitions listed there are supported. At some point the EVP layer will begin enforcing the listed transitions.</p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man7/provider.html">provider(7)</a>, <a href="../man7/life_cycle-kdf.html">life_cycle-kdf(7)</a>, <a href="../man3/EVP_KDF.html">EVP_KDF(3)</a>.</p>

<h1 id="HISTORY">HISTORY</h1>

<p>The provider KDF interface was introduced in OpenSSL 3.0.</p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2020-2023 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the Apache License 2.0 (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


