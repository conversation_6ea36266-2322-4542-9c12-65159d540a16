<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>OSSL_PROVIDER-FIPS</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:root@localhost" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a>
    <ul>
      <li><a href="#Properties">Properties</a></li>
    </ul>
  </li>
  <li><a href="#OPERATIONS-AND-ALGORITHMS">OPERATIONS AND ALGORITHMS</a>
    <ul>
      <li><a href="#Hashing-Algorithms-Message-Digests">Hashing Algorithms / Message Digests</a></li>
      <li><a href="#Symmetric-Ciphers">Symmetric Ciphers</a></li>
      <li><a href="#Message-Authentication-Code-MAC">Message Authentication Code (MAC)</a></li>
      <li><a href="#Key-Derivation-Function-KDF">Key Derivation Function (KDF)</a></li>
      <li><a href="#Key-Exchange">Key Exchange</a></li>
      <li><a href="#Asymmetric-Signature">Asymmetric Signature</a></li>
      <li><a href="#Asymmetric-Cipher">Asymmetric Cipher</a></li>
      <li><a href="#Asymmetric-Key-Encapsulation">Asymmetric Key Encapsulation</a></li>
      <li><a href="#Asymmetric-Key-Management">Asymmetric Key Management</a></li>
      <li><a href="#Random-Number-Generation">Random Number Generation</a></li>
    </ul>
  </li>
  <li><a href="#SELF-TESTING">SELF TESTING</a></li>
  <li><a href="#EXAMPLES">EXAMPLES</a></li>
  <li><a href="#NOTES">NOTES</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#HISTORY">HISTORY</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>OSSL_PROVIDER-FIPS - OpenSSL FIPS provider</p>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>The OpenSSL FIPS provider is a special provider that conforms to the Federal Information Processing Standards (FIPS) specified in FIPS 140-2. This &#39;module&#39; contains an approved set of cryptographic algorithms that is validated by an accredited testing laboratory.</p>

<h2 id="Properties">Properties</h2>

<p>The implementations in this provider specifically have these properties defined:</p>

<dl>

<dt id="provider-fips">&quot;provider=fips&quot;</dt>
<dd>

</dd>
<dt id="fips-yes">&quot;fips=yes&quot;</dt>
<dd>

</dd>
</dl>

<p>It may be used in a property query string with fetching functions such as <a href="../man3/EVP_MD_fetch.html">EVP_MD_fetch(3)</a> or <a href="../man3/EVP_CIPHER_fetch.html">EVP_CIPHER_fetch(3)</a>, as well as with other functions that take a property query string, such as <a href="../man3/EVP_PKEY_CTX_new_from_name.html">EVP_PKEY_CTX_new_from_name(3)</a>.</p>

<p>It isn&#39;t mandatory to query for any of these properties, except to make sure to get implementations of this provider and none other.</p>

<p>The &quot;fips=yes&quot; property can be use to make sure only FIPS approved implementations are used for crypto operations. This may also include other non-crypto support operations that are not in the FIPS provider, such as asymmetric key encoders, see <a href="../man7/OSSL_PROVIDER-default.html">&quot;Asymmetric Key Management&quot; in OSSL_PROVIDER-default(7)</a>.</p>

<h1 id="OPERATIONS-AND-ALGORITHMS">OPERATIONS AND ALGORITHMS</h1>

<p>The OpenSSL FIPS provider supports these operations and algorithms:</p>

<h2 id="Hashing-Algorithms-Message-Digests">Hashing Algorithms / Message Digests</h2>

<dl>

<dt id="SHA1-see-EVP_MD-SHA1-7">SHA1, see <a href="../man7/EVP_MD-SHA1.html">EVP_MD-SHA1(7)</a></dt>
<dd>

</dd>
<dt id="SHA2-see-EVP_MD-SHA2-7">SHA2, see <a href="../man7/EVP_MD-SHA2.html">EVP_MD-SHA2(7)</a></dt>
<dd>

</dd>
<dt id="SHA3-see-EVP_MD-SHA3-7">SHA3, see <a href="../man7/EVP_MD-SHA3.html">EVP_MD-SHA3(7)</a></dt>
<dd>

</dd>
<dt id="KECCAK-KMAC-see-EVP_MD-KECCAK-KMAC-7">KECCAK-KMAC, see <a href="../man7/EVP_MD-KECCAK-KMAC.html">EVP_MD-KECCAK-KMAC(7)</a></dt>
<dd>

</dd>
</dl>

<h2 id="Symmetric-Ciphers">Symmetric Ciphers</h2>

<dl>

<dt id="AES-see-EVP_CIPHER-AES-7">AES, see <a href="../man7/EVP_CIPHER-AES.html">EVP_CIPHER-AES(7)</a></dt>
<dd>

</dd>
<dt id="DES-EDE3-TripleDES-see-EVP_CIPHER-DES-7">DES-EDE3 (TripleDES), see <a href="../man7/EVP_CIPHER-DES.html">EVP_CIPHER-DES(7)</a></dt>
<dd>

</dd>
</dl>

<h2 id="Message-Authentication-Code-MAC">Message Authentication Code (MAC)</h2>

<dl>

<dt id="CMAC-see-EVP_MAC-CMAC-7">CMAC, see <a href="../man7/EVP_MAC-CMAC.html">EVP_MAC-CMAC(7)</a></dt>
<dd>

</dd>
<dt id="GMAC-see-EVP_MAC-GMAC-7">GMAC, see <a href="../man7/EVP_MAC-GMAC.html">EVP_MAC-GMAC(7)</a></dt>
<dd>

</dd>
<dt id="HMAC-see-EVP_MAC-HMAC-7">HMAC, see <a href="../man7/EVP_MAC-HMAC.html">EVP_MAC-HMAC(7)</a></dt>
<dd>

</dd>
<dt id="KMAC-see-EVP_MAC-KMAC-7">KMAC, see <a href="../man7/EVP_MAC-KMAC.html">EVP_MAC-KMAC(7)</a></dt>
<dd>

</dd>
</dl>

<h2 id="Key-Derivation-Function-KDF">Key Derivation Function (KDF)</h2>

<dl>

<dt id="HKDF-see-EVP_KDF-HKDF-7">HKDF, see <a href="../man7/EVP_KDF-HKDF.html">EVP_KDF-HKDF(7)</a></dt>
<dd>

</dd>
<dt id="TLS13-KDF-see-EVP_KDF-TLS13_KDF-7">TLS13-KDF, see <a href="../man7/EVP_KDF-TLS13_KDF.html">EVP_KDF-TLS13_KDF(7)</a></dt>
<dd>

</dd>
<dt id="SSKDF-see-EVP_KDF-SS-7">SSKDF, see <a href="../man7/EVP_KDF-SS.html">EVP_KDF-SS(7)</a></dt>
<dd>

</dd>
<dt id="PBKDF2-see-EVP_KDF-PBKDF2-7">PBKDF2, see <a href="../man7/EVP_KDF-PBKDF2.html">EVP_KDF-PBKDF2(7)</a></dt>
<dd>

</dd>
<dt id="SSHKDF-see-EVP_KDF-SSHKDF-7">SSHKDF, see <a href="../man7/EVP_KDF-SSHKDF.html">EVP_KDF-SSHKDF(7)</a></dt>
<dd>

</dd>
<dt id="TLS1-PRF-see-EVP_KDF-TLS1_PRF-7">TLS1-PRF, see <a href="../man7/EVP_KDF-TLS1_PRF.html">EVP_KDF-TLS1_PRF(7)</a></dt>
<dd>

</dd>
<dt id="KBKDF-see-EVP_KDF-KB-7">KBKDF, see <a href="../man7/EVP_KDF-KB.html">EVP_KDF-KB(7)</a></dt>
<dd>

</dd>
<dt id="X942KDF-ASN1-see-EVP_KDF-X942-ASN1-7">X942KDF-ASN1, see <a href="../man7/EVP_KDF-X942-ASN1.html">EVP_KDF-X942-ASN1(7)</a></dt>
<dd>

</dd>
<dt id="X942KDF-CONCAT-see-EVP_KDF-X942-CONCAT-7">X942KDF-CONCAT, see <a href="../man7/EVP_KDF-X942-CONCAT.html">EVP_KDF-X942-CONCAT(7)</a></dt>
<dd>

</dd>
<dt id="X963KDF-see-EVP_KDF-X963-7">X963KDF, see <a href="../man7/EVP_KDF-X963.html">EVP_KDF-X963(7)</a></dt>
<dd>

</dd>
</dl>

<h2 id="Key-Exchange">Key Exchange</h2>

<dl>

<dt id="DH-see-EVP_KEYEXCH-DH-7">DH, see <a href="../man7/EVP_KEYEXCH-DH.html">EVP_KEYEXCH-DH(7)</a></dt>
<dd>

</dd>
<dt id="ECDH-see-EVP_KEYEXCH-ECDH-7">ECDH, see <a href="../man7/EVP_KEYEXCH-ECDH.html">EVP_KEYEXCH-ECDH(7)</a></dt>
<dd>

</dd>
<dt id="X25519-see-EVP_KEYEXCH-X25519-7">X25519, see <a href="../man7/EVP_KEYEXCH-X25519.html">EVP_KEYEXCH-X25519(7)</a></dt>
<dd>

</dd>
<dt id="X448-see-EVP_KEYEXCH-X448-7">X448, see <a href="../man7/EVP_KEYEXCH-X448.html">EVP_KEYEXCH-X448(7)</a></dt>
<dd>

</dd>
</dl>

<h2 id="Asymmetric-Signature">Asymmetric Signature</h2>

<dl>

<dt id="RSA-see-EVP_SIGNATURE-RSA-7">RSA, see <a href="../man7/EVP_SIGNATURE-RSA.html">EVP_SIGNATURE-RSA(7)</a></dt>
<dd>

</dd>
<dt id="X25519-see-EVP_SIGNATURE-ED25519-7">X25519, see <a href="../man7/EVP_SIGNATURE-ED25519.html">EVP_SIGNATURE-ED25519(7)</a></dt>
<dd>

</dd>
<dt id="X448-see-EVP_SIGNATURE-ED448-7">X448, see <a href="../man7/EVP_SIGNATURE-ED448.html">EVP_SIGNATURE-ED448(7)</a></dt>
<dd>

</dd>
<dt id="HMAC-see-EVP_SIGNATURE-HMAC-7">HMAC, see <a href="../man7/EVP_SIGNATURE-HMAC.html">EVP_SIGNATURE-HMAC(7)</a></dt>
<dd>

</dd>
<dt id="CMAC-see-EVP_SIGNATURE-CMAC-7">CMAC, see <a href="../man7/EVP_SIGNATURE-CMAC.html">EVP_SIGNATURE-CMAC(7)</a></dt>
<dd>

</dd>
</dl>

<h2 id="Asymmetric-Cipher">Asymmetric Cipher</h2>

<dl>

<dt id="RSA-see-EVP_ASYM_CIPHER-RSA-7">RSA, see <a href="../man7/EVP_ASYM_CIPHER-RSA.html">EVP_ASYM_CIPHER-RSA(7)</a></dt>
<dd>

</dd>
</dl>

<h2 id="Asymmetric-Key-Encapsulation">Asymmetric Key Encapsulation</h2>

<dl>

<dt id="RSA-see-EVP_KEM-RSA-7">RSA, see <a href="../man7/EVP_KEM-RSA.html">EVP_KEM-RSA(7)</a></dt>
<dd>

</dd>
</dl>

<h2 id="Asymmetric-Key-Management">Asymmetric Key Management</h2>

<dl>

<dt id="DH-see-EVP_KEYMGMT-DH-7">DH, see <a href="../man7/EVP_KEYMGMT-DH.html">EVP_KEYMGMT-DH(7)</a></dt>
<dd>

</dd>
<dt id="DHX-see-EVP_KEYMGMT-DHX-7">DHX, see <a href="../man7/EVP_KEYMGMT-DHX.html">EVP_KEYMGMT-DHX(7)</a></dt>
<dd>

</dd>
<dt id="DSA-see-EVP_KEYMGMT-DSA-7">DSA, see <a href="../man7/EVP_KEYMGMT-DSA.html">EVP_KEYMGMT-DSA(7)</a></dt>
<dd>

</dd>
<dt id="RSA-see-EVP_KEYMGMT-RSA-7">RSA, see <a href="../man7/EVP_KEYMGMT-RSA.html">EVP_KEYMGMT-RSA(7)</a></dt>
<dd>

</dd>
<dt id="EC-see-EVP_KEYMGMT-EC-7">EC, see <a href="../man7/EVP_KEYMGMT-EC.html">EVP_KEYMGMT-EC(7)</a></dt>
<dd>

</dd>
<dt id="X25519-see-EVP_KEYMGMT-X25519-7">X25519, see <a href="../man7/EVP_KEYMGMT-X25519.html">EVP_KEYMGMT-X25519(7)</a></dt>
<dd>

</dd>
<dt id="X448-see-EVP_KEYMGMT-X448-7">X448, see <a href="../man7/EVP_KEYMGMT-X448.html">EVP_KEYMGMT-X448(7)</a></dt>
<dd>

</dd>
</dl>

<h2 id="Random-Number-Generation">Random Number Generation</h2>

<dl>

<dt id="CTR-DRBG-see-EVP_RAND-CTR-DRBG-7">CTR-DRBG, see <a href="../man7/EVP_RAND-CTR-DRBG.html">EVP_RAND-CTR-DRBG(7)</a></dt>
<dd>

</dd>
<dt id="HASH-DRBG-see-EVP_RAND-HASH-DRBG-7">HASH-DRBG, see <a href="../man7/EVP_RAND-HASH-DRBG.html">EVP_RAND-HASH-DRBG(7)</a></dt>
<dd>

</dd>
<dt id="HMAC-DRBG-see-EVP_RAND-HMAC-DRBG-7">HMAC-DRBG, see <a href="../man7/EVP_RAND-HMAC-DRBG.html">EVP_RAND-HMAC-DRBG(7)</a></dt>
<dd>

</dd>
<dt id="TEST-RAND-see-EVP_RAND-TEST-RAND-7">TEST-RAND, see <a href="../man7/EVP_RAND-TEST-RAND.html">EVP_RAND-TEST-RAND(7)</a></dt>
<dd>

<p>TEST-RAND is an unapproved algorithm.</p>

</dd>
</dl>

<h1 id="SELF-TESTING">SELF TESTING</h1>

<p>One of the requirements for the FIPS module is self testing. An optional callback mechanism is available to return information to the user using <a href="../man3/OSSL_SELF_TEST_set_callback.html">OSSL_SELF_TEST_set_callback(3)</a>.</p>

<p>The parameters passed to the callback are described in <a href="../man3/OSSL_SELF_TEST_new.html">OSSL_SELF_TEST_new(3)</a></p>

<p>The OpenSSL FIPS module uses the following mechanism to provide information about the self tests as they run. This is useful for debugging if a self test is failing. The callback also allows forcing any self test to fail, in order to check that it operates correctly on failure. Note that all self tests run even if a self test failure occurs.</p>

<p>The FIPS module passes the following type(s) to OSSL_SELF_TEST_onbegin().</p>

<dl>

<dt id="Module_Integrity-OSSL_SELF_TEST_TYPE_MODULE_INTEGRITY">&quot;Module_Integrity&quot; (<b>OSSL_SELF_TEST_TYPE_MODULE_INTEGRITY</b>)</dt>
<dd>

<p>Uses HMAC SHA256 on the module file to validate that the module has not been modified. The integrity value is compared to a value written to a configuration file during installation.</p>

</dd>
<dt id="Install_Integrity-OSSL_SELF_TEST_TYPE_INSTALL_INTEGRITY">&quot;Install_Integrity&quot; (<b>OSSL_SELF_TEST_TYPE_INSTALL_INTEGRITY</b>)</dt>
<dd>

<p>Uses HMAC SHA256 on a fixed string to validate that the installation process has already been performed and the self test KATS have already been tested, The integrity value is compared to a value written to a configuration file after successfully running the self tests during installation.</p>

</dd>
<dt id="KAT_Cipher-OSSL_SELF_TEST_TYPE_KAT_CIPHER">&quot;KAT_Cipher&quot; (<b>OSSL_SELF_TEST_TYPE_KAT_CIPHER</b>)</dt>
<dd>

<p>Known answer test for a symmetric cipher.</p>

</dd>
<dt id="KAT_AsymmetricCipher-OSSL_SELF_TEST_TYPE_KAT_ASYM_CIPHER">&quot;KAT_AsymmetricCipher&quot; (<b>OSSL_SELF_TEST_TYPE_KAT_ASYM_CIPHER</b>)</dt>
<dd>

<p>Known answer test for a asymmetric cipher.</p>

</dd>
<dt id="KAT_Digest-OSSL_SELF_TEST_TYPE_KAT_DIGEST">&quot;KAT_Digest&quot; (<b>OSSL_SELF_TEST_TYPE_KAT_DIGEST</b>)</dt>
<dd>

<p>Known answer test for a digest.</p>

</dd>
<dt id="KAT_Signature-OSSL_SELF_TEST_TYPE_KAT_SIGNATURE">&quot;KAT_Signature&quot; (<b>OSSL_SELF_TEST_TYPE_KAT_SIGNATURE</b>)</dt>
<dd>

<p>Known answer test for a signature.</p>

</dd>
<dt id="PCT_Signature-OSSL_SELF_TEST_TYPE_PCT_SIGNATURE">&quot;PCT_Signature&quot; (<b>OSSL_SELF_TEST_TYPE_PCT_SIGNATURE</b>)</dt>
<dd>

<p>Pairwise Consistency check for a signature.</p>

</dd>
<dt id="KAT_KDF-OSSL_SELF_TEST_TYPE_KAT_KDF">&quot;KAT_KDF&quot; (<b>OSSL_SELF_TEST_TYPE_KAT_KDF</b>)</dt>
<dd>

<p>Known answer test for a key derivation function.</p>

</dd>
<dt id="KAT_KA-OSSL_SELF_TEST_TYPE_KAT_KA">&quot;KAT_KA&quot; (<b>OSSL_SELF_TEST_TYPE_KAT_KA</b>)</dt>
<dd>

<p>Known answer test for key agreement.</p>

</dd>
<dt id="DRBG-OSSL_SELF_TEST_TYPE_DRBG">&quot;DRBG&quot; (<b>OSSL_SELF_TEST_TYPE_DRBG</b>)</dt>
<dd>

<p>Known answer test for a Deterministic Random Bit Generator.</p>

</dd>
<dt id="Conditional_PCT-OSSL_SELF_TEST_TYPE_PCT">&quot;Conditional_PCT&quot; (<b>OSSL_SELF_TEST_TYPE_PCT</b>)</dt>
<dd>

<p>Conditional test that is run during the generation of key pairs.</p>

</dd>
<dt id="Continuous_RNG_Test-OSSL_SELF_TEST_TYPE_CRNG">&quot;Continuous_RNG_Test&quot; (<b>OSSL_SELF_TEST_TYPE_CRNG</b>)</dt>
<dd>

<p>Continuous random number generator test.</p>

</dd>
</dl>

<p>The &quot;Module_Integrity&quot; self test is always run at startup. The &quot;Install_Integrity&quot; self test is used to check if the self tests have already been run at installation time. If they have already run then the self tests are not run on subsequent startups. All other self test categories are run once at installation time, except for the &quot;Pairwise_Consistency_Test&quot;.</p>

<p>There is only one instance of the &quot;Module_Integrity&quot; and &quot;Install_Integrity&quot; self tests. All other self tests may have multiple instances.</p>

<p>The FIPS module passes the following descriptions(s) to OSSL_SELF_TEST_onbegin().</p>

<dl>

<dt id="HMAC-OSSL_SELF_TEST_DESC_INTEGRITY_HMAC">&quot;HMAC&quot; (<b>OSSL_SELF_TEST_DESC_INTEGRITY_HMAC</b>)</dt>
<dd>

<p>&quot;Module_Integrity&quot; and &quot;Install_Integrity&quot; use this.</p>

</dd>
<dt id="RSA-OSSL_SELF_TEST_DESC_PCT_RSA_PKCS1">&quot;RSA&quot; (<b>OSSL_SELF_TEST_DESC_PCT_RSA_PKCS1</b>)</dt>
<dd>

</dd>
<dt id="ECDSA-OSSL_SELF_TEST_DESC_PCT_ECDSA">&quot;ECDSA&quot; (<b>OSSL_SELF_TEST_DESC_PCT_ECDSA</b>)</dt>
<dd>

</dd>
<dt id="DSA-OSSL_SELF_TEST_DESC_PCT_DSA">&quot;DSA&quot; (<b>OSSL_SELF_TEST_DESC_PCT_DSA</b>)</dt>
<dd>

<p>Key generation tests used with the &quot;Pairwise_Consistency_Test&quot; type.</p>

</dd>
<dt id="RSA_Encrypt-OSSL_SELF_TEST_DESC_ASYM_RSA_ENC">&quot;RSA_Encrypt&quot; (<b>OSSL_SELF_TEST_DESC_ASYM_RSA_ENC</b>)</dt>
<dd>

</dd>
<dt id="RSA_Decrypt-OSSL_SELF_TEST_DESC_ASYM_RSA_DEC">&quot;RSA_Decrypt&quot; (<b>OSSL_SELF_TEST_DESC_ASYM_RSA_DEC</b>)</dt>
<dd>

<p>&quot;KAT_AsymmetricCipher&quot; uses this to indicate an encrypt or decrypt KAT.</p>

</dd>
<dt id="AES_GCM-OSSL_SELF_TEST_DESC_CIPHER_AES_GCM">&quot;AES_GCM&quot; (<b>OSSL_SELF_TEST_DESC_CIPHER_AES_GCM</b>)</dt>
<dd>

</dd>
<dt id="AES_ECB_Decrypt-OSSL_SELF_TEST_DESC_CIPHER_AES_ECB">&quot;AES_ECB_Decrypt&quot; (<b>OSSL_SELF_TEST_DESC_CIPHER_AES_ECB</b>)</dt>
<dd>

</dd>
<dt id="TDES-OSSL_SELF_TEST_DESC_CIPHER_TDES">&quot;TDES&quot; (<b>OSSL_SELF_TEST_DESC_CIPHER_TDES</b>)</dt>
<dd>

<p>Symmetric cipher tests used with the &quot;KAT_Cipher&quot; type.</p>

</dd>
<dt id="SHA1-OSSL_SELF_TEST_DESC_MD_SHA1">&quot;SHA1&quot; (<b>OSSL_SELF_TEST_DESC_MD_SHA1</b>)</dt>
<dd>

</dd>
<dt id="SHA2-OSSL_SELF_TEST_DESC_MD_SHA2">&quot;SHA2&quot; (<b>OSSL_SELF_TEST_DESC_MD_SHA2</b>)</dt>
<dd>

</dd>
<dt id="SHA3-OSSL_SELF_TEST_DESC_MD_SHA3">&quot;SHA3&quot; (<b>OSSL_SELF_TEST_DESC_MD_SHA3</b>)</dt>
<dd>

<p>Digest tests used with the &quot;KAT_Digest&quot; type.</p>

</dd>
<dt id="DSA-OSSL_SELF_TEST_DESC_SIGN_DSA">&quot;DSA&quot; (<b>OSSL_SELF_TEST_DESC_SIGN_DSA</b>)</dt>
<dd>

</dd>
<dt id="RSA-OSSL_SELF_TEST_DESC_SIGN_RSA">&quot;RSA&quot; (<b>OSSL_SELF_TEST_DESC_SIGN_RSA</b>)</dt>
<dd>

</dd>
<dt id="ECDSA-OSSL_SELF_TEST_DESC_SIGN_ECDSA">&quot;ECDSA&quot; (<b>OSSL_SELF_TEST_DESC_SIGN_ECDSA</b>)</dt>
<dd>

<p>Signature tests used with the &quot;KAT_Signature&quot; type.</p>

</dd>
<dt id="ECDH-OSSL_SELF_TEST_DESC_KA_ECDH">&quot;ECDH&quot; (<b>OSSL_SELF_TEST_DESC_KA_ECDH</b>)</dt>
<dd>

</dd>
<dt id="DH-OSSL_SELF_TEST_DESC_KA_DH">&quot;DH&quot; (<b>OSSL_SELF_TEST_DESC_KA_DH</b>)</dt>
<dd>

<p>Key agreement tests used with the &quot;KAT_KA&quot; type.</p>

</dd>
<dt id="HKDF-OSSL_SELF_TEST_DESC_KDF_HKDF">&quot;HKDF&quot; (<b>OSSL_SELF_TEST_DESC_KDF_HKDF</b>)</dt>
<dd>

</dd>
<dt id="TLS13_KDF_EXTRACT-OSSL_SELF_TEST_DESC_KDF_TLS13_EXTRACT">&quot;TLS13_KDF_EXTRACT&quot; (<b>OSSL_SELF_TEST_DESC_KDF_TLS13_EXTRACT</b>)</dt>
<dd>

</dd>
<dt id="TLS13_KDF_EXPAND-OSSL_SELF_TEST_DESC_KDF_TLS13_EXPAND">&quot;TLS13_KDF_EXPAND&quot; (<b>OSSL_SELF_TEST_DESC_KDF_TLS13_EXPAND</b>)</dt>
<dd>

</dd>
<dt id="SSKDF-OSSL_SELF_TEST_DESC_KDF_SSKDF">&quot;SSKDF&quot; (<b>OSSL_SELF_TEST_DESC_KDF_SSKDF</b>)</dt>
<dd>

</dd>
<dt id="X963KDF-OSSL_SELF_TEST_DESC_KDF_X963KDF">&quot;X963KDF&quot; (<b>OSSL_SELF_TEST_DESC_KDF_X963KDF</b>)</dt>
<dd>

</dd>
<dt id="X942KDF-OSSL_SELF_TEST_DESC_KDF_X942KDF">&quot;X942KDF&quot; (<b>OSSL_SELF_TEST_DESC_KDF_X942KDF</b>)</dt>
<dd>

</dd>
<dt id="PBKDF2-OSSL_SELF_TEST_DESC_KDF_PBKDF2">&quot;PBKDF2&quot; (<b>OSSL_SELF_TEST_DESC_KDF_PBKDF2</b>)</dt>
<dd>

</dd>
<dt id="SSHKDF-OSSL_SELF_TEST_DESC_KDF_SSHKDF">&quot;SSHKDF&quot; (<b>OSSL_SELF_TEST_DESC_KDF_SSHKDF</b>)</dt>
<dd>

</dd>
<dt id="TLS12_PRF-OSSL_SELF_TEST_DESC_KDF_TLS12_PRF">&quot;TLS12_PRF&quot; (<b>OSSL_SELF_TEST_DESC_KDF_TLS12_PRF</b>)</dt>
<dd>

</dd>
<dt id="KBKDF-OSSL_SELF_TEST_DESC_KDF_KBKDF">&quot;KBKDF&quot; (<b>OSSL_SELF_TEST_DESC_KDF_KBKDF</b>)</dt>
<dd>

<p>Key Derivation Function tests used with the &quot;KAT_KDF&quot; type.</p>

</dd>
<dt id="CTR-OSSL_SELF_TEST_DESC_DRBG_CTR">&quot;CTR&quot; (<b>OSSL_SELF_TEST_DESC_DRBG_CTR</b>)</dt>
<dd>

</dd>
<dt id="HASH-OSSL_SELF_TEST_DESC_DRBG_HASH">&quot;HASH&quot; (<b>OSSL_SELF_TEST_DESC_DRBG_HASH</b>)</dt>
<dd>

</dd>
<dt id="HMAC-OSSL_SELF_TEST_DESC_DRBG_HMAC">&quot;HMAC&quot; (<b>OSSL_SELF_TEST_DESC_DRBG_HMAC</b>)</dt>
<dd>

<p>DRBG tests used with the &quot;DRBG&quot; type.</p>

<p>= item &quot;RNG&quot; (<b>OSSL_SELF_TEST_DESC_RNG</b>)</p>

<p>&quot;Continuous_RNG_Test&quot; uses this.</p>

</dd>
</dl>

<h1 id="EXAMPLES">EXAMPLES</h1>

<p>A simple self test callback is shown below for illustrative purposes.</p>

<pre><code>#include &lt;openssl/self_test.h&gt;

static OSSL_CALLBACK self_test_cb;

static int self_test_cb(const OSSL_PARAM params[], void *arg)
{
  int ret = 0;
  const OSSL_PARAM *p = NULL;
  const char *phase = NULL, *type = NULL, *desc = NULL;

  p = OSSL_PARAM_locate_const(params, OSSL_PROV_PARAM_SELF_TEST_PHASE);
  if (p == NULL || p-&gt;data_type != OSSL_PARAM_UTF8_STRING)
      goto err;
  phase = (const char *)p-&gt;data;

  p = OSSL_PARAM_locate_const(params, OSSL_PROV_PARAM_SELF_TEST_DESC);
  if (p == NULL || p-&gt;data_type != OSSL_PARAM_UTF8_STRING)
      goto err;
  desc = (const char *)p-&gt;data;

  p = OSSL_PARAM_locate_const(params, OSSL_PROV_PARAM_SELF_TEST_TYPE);
  if (p == NULL || p-&gt;data_type != OSSL_PARAM_UTF8_STRING)
      goto err;
  type = (const char *)p-&gt;data;

  /* Do some logging */
  if (strcmp(phase, OSSL_SELF_TEST_PHASE_START) == 0)
      BIO_printf(bio_out, &quot;%s : (%s) : &quot;, desc, type);
  if (strcmp(phase, OSSL_SELF_TEST_PHASE_PASS) == 0
          || strcmp(phase, OSSL_SELF_TEST_PHASE_FAIL) == 0)
      BIO_printf(bio_out, &quot;%s\n&quot;, phase);

  /* Corrupt the SHA1 self test during the &#39;corrupt&#39; phase by returning 0 */
  if (strcmp(phase, OSSL_SELF_TEST_PHASE_CORRUPT) == 0
          &amp;&amp; strcmp(desc, OSSL_SELF_TEST_DESC_MD_SHA1) == 0) {
      BIO_printf(bio_out, &quot;%s %s&quot;, phase, desc);
      return 0;
  }
  ret = 1;
err:
  return ret;
}</code></pre>

<h1 id="NOTES">NOTES</h1>

<p>Some released versions of OpenSSL do not include a validated FIPS provider. To determine which versions have undergone the validation process, please refer to the <a href="https://www.openssl.org/source/">OpenSSL Downloads page</a>. If you require FIPS-approved functionality, it is essential to build your FIPS provider using one of the validated versions listed there. Normally, it is possible to utilize a FIPS provider constructed from one of the validated versions alongside <i>libcrypto</i> and <i>libssl</i> compiled from any release within the same major release series. This flexibility enables you to address bug fixes and CVEs that fall outside the FIPS boundary.</p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man1/openssl-fipsinstall.html">openssl-fipsinstall(1)</a>, <a href="../man5/fips_config.html">fips_config(5)</a>, <a href="../man3/OSSL_SELF_TEST_set_callback.html">OSSL_SELF_TEST_set_callback(3)</a>, <a href="../man3/OSSL_SELF_TEST_new.html">OSSL_SELF_TEST_new(3)</a>, <a href="../man3/OSSL_PARAM.html">OSSL_PARAM(3)</a>, <a href="../man7/openssl-core.h.html">openssl-core.h(7)</a>, <a href="../man7/openssl-core_dispatch.h.html">openssl-core_dispatch.h(7)</a>, <a href="../man7/provider.html">provider(7)</a>, <a href="https://www.openssl.org/source/">https://www.openssl.org/source/</a></p>

<h1 id="HISTORY">HISTORY</h1>

<p>This functionality was added in OpenSSL 3.0.</p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2019-2023 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the Apache License 2.0 (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


