<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>EVP_MD-BLAKE2</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:root@localhost" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a>
    <ul>
      <li><a href="#Identities">Identities</a></li>
      <li><a href="#Gettable-Parameters">Gettable Parameters</a></li>
    </ul>
  </li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>EVP_MD-BLAKE2 - The BLAKE2 EVP_MD implementation</p>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>Support for computing BLAKE2 digests through the <b>EVP_MD</b> API.</p>

<h2 id="Identities">Identities</h2>

<p>This implementation is only available with the default provider, and includes the following varieties:</p>

<dl>

<dt id="BLAKE2S-256">BLAKE2S-256</dt>
<dd>

<p>Known names are &quot;BLAKE2S-256&quot; and &quot;BLAKE2s256&quot;.</p>

</dd>
<dt id="BLAKE2B-512">BLAKE2B-512</dt>
<dd>

<p>Known names are &quot;BLAKE2B-512&quot; and &quot;BLAKE2b512&quot;.</p>

</dd>
</dl>

<h2 id="Gettable-Parameters">Gettable Parameters</h2>

<p>This implementation supports the common gettable parameters described in <a href="../man7/EVP_MD-common.html">EVP_MD-common(7)</a>.</p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man7/provider-digest.html">provider-digest(7)</a>, <a href="../man7/OSSL_PROVIDER-default.html">OSSL_PROVIDER-default(7)</a></p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2020-2022 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the Apache License 2.0 (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


