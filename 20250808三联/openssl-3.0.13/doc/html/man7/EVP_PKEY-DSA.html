<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>EVP_PKEY-DSA</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:root@localhost" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a>
    <ul>
      <li><a href="#DSA-parameters">DSA parameters</a></li>
      <li><a href="#DSA-key-generation-parameters">DSA key generation parameters</a></li>
      <li><a href="#DSA-key-validation">DSA key validation</a></li>
    </ul>
  </li>
  <li><a href="#EXAMPLES">EXAMPLES</a></li>
  <li><a href="#CONFORMING-TO">CONFORMING TO</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>EVP_PKEY-DSA, EVP_KEYMGMT-DSA - EVP_PKEY DSA keytype and algorithm support</p>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>For <b>DSA</b> the FIPS186-4 standard specifies that the values used for FFC parameter generation are also required for parameter validation. This means that optional FFC domain parameter values for <i>seed</i>, <i>pcounter</i> and <i>gindex</i> may need to be stored for validation purposes. For <b>DSA</b> these fields are not stored in the ASN1 data so they need to be stored externally if validation is required.</p>

<h2 id="DSA-parameters">DSA parameters</h2>

<p>The <b>DSA</b> key type supports the FFC parameters (see <a href="../man7/EVP_PKEY-FFC.html">&quot;FFC parameters&quot; in EVP_PKEY-FFC(7)</a>).</p>

<h2 id="DSA-key-generation-parameters">DSA key generation parameters</h2>

<p>The <b>DSA</b> key type supports the FFC key generation parameters (see <a href="../man7/EVP_PKEY-FFC.html">&quot;FFC key generation parameters&quot; in EVP_PKEY-FFC(7)</a></p>

<p>The following restrictions apply to the &quot;pbits&quot; field:</p>

<p>For &quot;fips186_4&quot; this must be either 2048 or 3072. For &quot;fips186_2&quot; this must be 1024. For &quot;group&quot; this can be any one of 2048, 3072, 4096, 6144 or 8192.</p>

<h2 id="DSA-key-validation">DSA key validation</h2>

<p>For DSA keys, <a href="../man3/EVP_PKEY_param_check.html">EVP_PKEY_param_check(3)</a> behaves in the following way: The OpenSSL FIPS provider conforms to the rules within the FIPS186-4 standard for FFC parameter validation. For backwards compatibility the OpenSSL default provider uses a much simpler check (see below) for parameter validation, unless the seed parameter is set.</p>

<p>For DSA keys, <a href="../man3/EVP_PKEY_param_check_quick.html">EVP_PKEY_param_check_quick(3)</a> behaves in the following way: A simple check of L and N and partial g is performed. The default provider also supports validation of legacy &quot;fips186_2&quot; keys.</p>

<p>For DSA keys, <a href="../man3/EVP_PKEY_public_check.html">EVP_PKEY_public_check(3)</a>, <a href="../man3/EVP_PKEY_private_check.html">EVP_PKEY_private_check(3)</a> and <a href="../man3/EVP_PKEY_pairwise_check.html">EVP_PKEY_pairwise_check(3)</a> the OpenSSL default and FIPS providers conform to the rules within SP800-56Ar3 for public, private and pairwise tests respectively.</p>

<h1 id="EXAMPLES">EXAMPLES</h1>

<p>An <b>EVP_PKEY</b> context can be obtained by calling:</p>

<pre><code>EVP_PKEY_CTX *pctx = EVP_PKEY_CTX_new_from_name(NULL, &quot;DSA&quot;, NULL);</code></pre>

<p>The <b>DSA</b> domain parameters can be generated by calling:</p>

<pre><code>unsigned int pbits = 2048;
unsigned int qbits = 256;
int gindex = 1;
OSSL_PARAM params[5];
EVP_PKEY *param_key = NULL;
EVP_PKEY_CTX *pctx = NULL;

pctx = EVP_PKEY_CTX_new_from_name(NULL, &quot;DSA&quot;, NULL);
EVP_PKEY_paramgen_init(pctx);

params[0] = OSSL_PARAM_construct_uint(&quot;pbits&quot;, &amp;pbits);
params[1] = OSSL_PARAM_construct_uint(&quot;qbits&quot;, &amp;qbits);
params[2] = OSSL_PARAM_construct_int(&quot;gindex&quot;, &amp;gindex);
params[3] = OSSL_PARAM_construct_utf8_string(&quot;digest&quot;, &quot;SHA384&quot;, 0);
params[4] = OSSL_PARAM_construct_end();
EVP_PKEY_CTX_set_params(pctx, params);

EVP_PKEY_generate(pctx, &amp;param_key);
EVP_PKEY_CTX_free(pctx);

EVP_PKEY_print_params(bio_out, param_key, 0, NULL);</code></pre>

<p>A <b>DSA</b> key can be generated using domain parameters by calling:</p>

<pre><code>EVP_PKEY *key = NULL;
EVP_PKEY_CTX *gctx = NULL;

gctx = EVP_PKEY_CTX_new_from_pkey(NULL, param_key, NULL);
EVP_PKEY_keygen_init(gctx);
EVP_PKEY_generate(gctx, &amp;key);
EVP_PKEY_CTX_free(gctx);
EVP_PKEY_print_private(bio_out, key, 0, NULL);</code></pre>

<h1 id="CONFORMING-TO">CONFORMING TO</h1>

<p>The following sections of FIPS186-4:</p>

<dl>

<dt id="A.1.1.2-Generation-of-Probable-Primes-p-and-q-Using-an-Approved-Hash-Function">A.1.1.2 Generation of Probable Primes p and q Using an Approved Hash Function.</dt>
<dd>

</dd>
<dt id="A.2.3-Generation-of-canonical-generator-g">A.2.3 Generation of canonical generator g.</dt>
<dd>

</dd>
<dt id="A.2.1-Unverifiable-Generation-of-the-Generator-g">A.2.1 Unverifiable Generation of the Generator g.</dt>
<dd>

</dd>
</dl>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man7/EVP_PKEY-FFC.html">EVP_PKEY-FFC(7)</a>, <a href="../man7/EVP_SIGNATURE-DSA.html">EVP_SIGNATURE-DSA(7)</a> <a href="../man3/EVP_PKEY.html">EVP_PKEY(3)</a>, <a href="../man7/provider-keymgmt.html">provider-keymgmt(7)</a>, <a href="../man3/EVP_KEYMGMT.html">EVP_KEYMGMT(3)</a>, <a href="../man7/OSSL_PROVIDER-default.html">OSSL_PROVIDER-default(7)</a>, <a href="../man7/OSSL_PROVIDER-FIPS.html">OSSL_PROVIDER-FIPS(7)</a></p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2020-2022 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the Apache License 2.0 (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


