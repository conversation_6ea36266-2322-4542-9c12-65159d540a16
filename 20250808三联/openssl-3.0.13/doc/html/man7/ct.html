<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>ct</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:root@localhost" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#HISTORY">HISTORY</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>ct - Certificate Transparency</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/ct.h&gt;</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>This library implements Certificate Transparency (CT) verification for TLS clients, as defined in RFC 6962. This verification can provide some confidence that a certificate has been publicly logged in a set of CT logs.</p>

<p>By default, these checks are disabled. They can be enabled using <a href="../man3/SSL_CTX_enable_ct.html">SSL_CTX_enable_ct(3)</a> or <a href="../man3/SSL_enable_ct.html">SSL_enable_ct(3)</a>.</p>

<p>This library can also be used to parse and examine CT data structures, such as Signed Certificate Timestamps (SCTs), or to read a list of CT logs. There are functions for: - decoding and encoding SCTs in DER and TLS wire format. - printing SCTs. - verifying the authenticity of SCTs. - loading a CT log list from a CONF file.</p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man3/d2i_SCT_LIST.html">d2i_SCT_LIST(3)</a>, <a href="../man3/CTLOG_STORE_new.html">CTLOG_STORE_new(3)</a>, <a href="../man3/CTLOG_STORE_get0_log_by_id.html">CTLOG_STORE_get0_log_by_id(3)</a>, <a href="../man3/SCT_new.html">SCT_new(3)</a>, <a href="../man3/SCT_print.html">SCT_print(3)</a>, <a href="../man3/SCT_validate.html">SCT_validate(3)</a>, <a href="../man3/SCT_validate.html">SCT_validate(3)</a>, <a href="../man3/CT_POLICY_EVAL_CTX_new.html">CT_POLICY_EVAL_CTX_new(3)</a>, <a href="../man3/SSL_CTX_set_ct_validation_callback.html">SSL_CTX_set_ct_validation_callback(3)</a></p>

<h1 id="HISTORY">HISTORY</h1>

<p>The ct library was added in OpenSSL 1.1.0.</p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2016-2017 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the Apache License 2.0 (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


