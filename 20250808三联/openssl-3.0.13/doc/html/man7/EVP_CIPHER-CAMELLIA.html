<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>EVP_CIPHER-CAMELLIA</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:root@localhost" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a>
    <ul>
      <li><a href="#Algorithm-Names">Algorithm Names</a></li>
      <li><a href="#Parameters">Parameters</a></li>
    </ul>
  </li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>EVP_CIPHER-CAMELLIA - The CAMELLIA EVP_CIPHER implementations</p>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>Support for CAMELLIA symmetric encryption using the <b>EVP_CIPHER</b> API.</p>

<h2 id="Algorithm-Names">Algorithm Names</h2>

<p>The following algorithms are available in the default provider:</p>

<dl>

<dt id="CAMELLIA-128-CBC-CAMELLIA-192-CBC-and-CAMELLIA-256-CBC">&quot;CAMELLIA-128-CBC&quot;, &quot;CAMELLIA-192-CBC&quot; and &quot;CAMELLIA-256-CBC&quot;</dt>
<dd>

</dd>
<dt id="CAMELLIA-128-CBC-CTS-CAMELLIA-192-CBC-CTS-and-CAMELLIA-256-CBC-CTS">&quot;CAMELLIA-128-CBC-CTS&quot;, &quot;CAMELLIA-192-CBC-CTS&quot; and &quot;CAMELLIA-256-CBC-CTS&quot;</dt>
<dd>

</dd>
<dt id="CAMELLIA-128-CFB-CAMELLIA-192-CFB-CAMELLIA-256-CFB-CAMELLIA-128-CFB1-CAMELLIA-192-CFB1-CAMELLIA-256-CFB1-CAMELLIA-128-CFB8-CAMELLIA-192-CFB8-and-CAMELLIA-256-CFB8">&quot;CAMELLIA-128-CFB&quot;, &quot;CAMELLIA-192-CFB&quot;, &quot;CAMELLIA-256-CFB&quot;, &quot;CAMELLIA-128-CFB1&quot;, &quot;CAMELLIA-192-CFB1&quot;, &quot;CAMELLIA-256-CFB1&quot;, &quot;CAMELLIA-128-CFB8&quot;, &quot;CAMELLIA-192-CFB8&quot; and &quot;CAMELLIA-256-CFB8&quot;</dt>
<dd>

</dd>
<dt id="CAMELLIA-128-CTR-CAMELLIA-192-CTR-and-CAMELLIA-256-CTR">&quot;CAMELLIA-128-CTR&quot;, &quot;CAMELLIA-192-CTR&quot; and &quot;CAMELLIA-256-CTR&quot;</dt>
<dd>

</dd>
<dt id="CAMELLIA-128-ECB-CAMELLIA-192-ECB-and-CAMELLIA-256-ECB">&quot;CAMELLIA-128-ECB&quot;, &quot;CAMELLIA-192-ECB&quot; and &quot;CAMELLIA-256-ECB&quot;</dt>
<dd>

</dd>
<dt id="CAMELLIA-192-OFB-CAMELLIA-128-OFB-and-CAMELLIA-256-OFB">&quot;CAMELLIA-192-OFB&quot;, &quot;CAMELLIA-128-OFB&quot; and &quot;CAMELLIA-256-OFB&quot;</dt>
<dd>

</dd>
</dl>

<h2 id="Parameters">Parameters</h2>

<p>This implementation supports the parameters described in <a href="../man3/EVP_EncryptInit.html">&quot;PARAMETERS&quot; in EVP_EncryptInit(3)</a>.</p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man7/provider-cipher.html">provider-cipher(7)</a>, <a href="../man7/OSSL_PROVIDER-default.html">OSSL_PROVIDER-default(7)</a></p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2021 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the Apache License 2.0 (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


