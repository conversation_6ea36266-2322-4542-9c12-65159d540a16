<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>crypto</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:root@localhost" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a>
    <ul>
      <li><a href="#Algorithms">Algorithms</a></li>
      <li><a href="#Operations">Operations</a></li>
      <li><a href="#Providers">Providers</a></li>
      <li><a href="#Library-contexts">Library contexts</a></li>
      <li><a href="#Multi-threaded-applications">Multi-threaded applications</a></li>
    </ul>
  </li>
  <li><a href="#ALGORITHM-FETCHING">ALGORITHM FETCHING</a>
    <ul>
      <li><a href="#Property-query-strings">Property query strings</a></li>
      <li><a href="#Explicit-fetching">Explicit fetching</a></li>
      <li><a href="#Implicit-fetching">Implicit fetching</a></li>
      <li><a href="#Performance">Performance</a></li>
    </ul>
  </li>
  <li><a href="#FETCHING-EXAMPLES">FETCHING EXAMPLES</a></li>
  <li><a href="#OPENSSL-PROVIDERS">OPENSSL PROVIDERS</a>
    <ul>
      <li><a href="#Default-provider">Default provider</a></li>
      <li><a href="#Base-provider">Base provider</a></li>
      <li><a href="#FIPS-provider">FIPS provider</a></li>
      <li><a href="#Legacy-provider">Legacy provider</a></li>
      <li><a href="#Null-provider">Null provider</a></li>
    </ul>
  </li>
  <li><a href="#USING-ALGORITHMS-IN-APPLICATIONS">USING ALGORITHMS IN APPLICATIONS</a></li>
  <li><a href="#CONFIGURATION">CONFIGURATION</a></li>
  <li><a href="#ENCODING-AND-DECODING-KEYS">ENCODING AND DECODING KEYS</a></li>
  <li><a href="#LIBRARY-CONVENTIONS">LIBRARY CONVENTIONS</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>crypto - OpenSSL cryptographic library</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<p>See the individual manual pages for details.</p>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>The OpenSSL crypto library (<code>libcrypto</code>) implements a wide range of cryptographic algorithms used in various Internet standards. The services provided by this library are used by the OpenSSL implementations of TLS and CMS, and they have also been used to implement many other third party products and protocols.</p>

<p>The functionality includes symmetric encryption, public key cryptography, key agreement, certificate handling, cryptographic hash functions, cryptographic pseudo-random number generators, message authentication codes (MACs), key derivation functions (KDFs), and various utilities.</p>

<h2 id="Algorithms">Algorithms</h2>

<p>Cryptographic primitives such as the SHA256 digest, or AES encryption are referred to in OpenSSL as &quot;algorithms&quot;. Each algorithm may have multiple implementations available for use. For example the RSA algorithm is available as a &quot;default&quot; implementation suitable for general use, and a &quot;fips&quot; implementation which has been validated to FIPS standards for situations where that is important. It is also possible that a third party could add additional implementations such as in a hardware security module (HSM).</p>

<h2 id="Operations">Operations</h2>

<p>Different algorithms can be grouped together by their purpose. For example there are algorithms for encryption, and different algorithms for digesting data. These different groups are known as &quot;operations&quot; in OpenSSL. Each operation has a different set of functions associated with it. For example to perform an encryption operation using AES (or any other encryption algorithm) you would use the encryption functions detailed on the <a href="../man3/EVP_EncryptInit.html">EVP_EncryptInit(3)</a> page. Or to perform a digest operation using SHA256 then you would use the digesting functions on the <a href="../man3/EVP_DigestInit.html">EVP_DigestInit(3)</a> page.</p>

<h2 id="Providers">Providers</h2>

<p>A provider in OpenSSL is a component that collects together algorithm implementations. In order to use an algorithm you must have at least one provider loaded that contains an implementation of it. OpenSSL comes with a number of providers and they may also be obtained from third parties. If you don&#39;t load a provider explicitly (either in program code or via config) then the OpenSSL built-in &quot;default&quot; provider will be automatically loaded.</p>

<h2 id="Library-contexts">Library contexts</h2>

<p>A library context can be thought of as a &quot;scope&quot; within which configuration options take effect. When a provider is loaded, it is only loaded within the scope of a given library context. In this way it is possible for different components of a complex application to each use a different library context and have different providers loaded with different configuration settings.</p>

<p>If an application does not explicitly create a library context then the &quot;default&quot; library context will be used.</p>

<p>Library contexts are represented by the <b>OSSL_LIB_CTX</b> type. Many OpenSSL API functions take a library context as a parameter. Applications can always pass <b>NULL</b> for this parameter to just use the default library context.</p>

<p>The default library context is automatically created the first time it is needed. This will automatically load any available configuration file and will initialise OpenSSL for use. Unlike in earlier versions of OpenSSL (prior to 1.1.0) no explicit initialisation steps need to be taken.</p>

<p>Similarly when the application exits the default library context is automatically destroyed. No explicit de-initialisation steps need to be taken.</p>

<p>See <a href="../man3/OSSL_LIB_CTX.html">OSSL_LIB_CTX(3)</a> for more information about library contexts. See also <a href="#ALGORITHM-FETCHING">&quot;ALGORITHM FETCHING&quot;</a>.</p>

<h2 id="Multi-threaded-applications">Multi-threaded applications</h2>

<p>As long as OpenSSL has been built with support for threads (the default case on most platforms) then most OpenSSL <i>functions</i> are thread-safe in the sense that it is safe to call the same function from multiple threads at the same time. However most OpenSSL <i>data structures</i> are not thread-safe. For example the <a href="../man3/BIO_write.html">BIO_write(3)</a> and <a href="../man3/BIO_read.html">BIO_read(3)</a> functions are thread safe. However it would not be thread safe to call BIO_write() from one thread while calling BIO_read() in another where both functions are passed the same <b>BIO</b> object since both of them may attempt to make changes to the same <b>BIO</b> object.</p>

<p>There are exceptions to these rules. A small number of functions are not thread safe at all. Where this is the case this restriction should be noted in the documentation for the function. Similarly some data structures may be partially or fully thread safe. For example it is safe to use an <b>OSSL_LIB_CTX</b> in multiple threads.</p>

<p>See <a href="../man7/openssl-threads.html">openssl-threads(7)</a> for a more detailed discussion on OpenSSL threading support.</p>

<h1 id="ALGORITHM-FETCHING">ALGORITHM FETCHING</h1>

<p>In order to use an algorithm an implementation for it must first be &quot;fetched&quot;. Fetching is the process of looking through the available implementations, applying selection criteria (via a property query string), and finally choosing the implementation that will be used.</p>

<p>Two types of fetching are supported by OpenSSL - explicit fetching and implicit fetching.</p>

<h2 id="Property-query-strings">Property query strings</h2>

<p>When fetching an algorithm it is possible to specify a property query string to guide the selection process. For example a property query string of &quot;provider=default&quot; could be used to force the selection to only consider algorithm implementations in the default provider.</p>

<p>Property query strings can be specified explicitly as an argument to a function. It is also possible to specify a default property query string for the whole library context using the <a href="../man3/EVP_set_default_properties.html">EVP_set_default_properties(3)</a> or <a href="../man3/EVP_default_properties_enable_fips.html">EVP_default_properties_enable_fips(3)</a> functions. Where both default properties and function specific properties are specified then they are combined. Function specific properties will override default properties where there is a conflict.</p>

<p>See <a href="../man7/property.html">property(7)</a> for more information about properties.</p>

<h2 id="Explicit-fetching">Explicit fetching</h2>

<p>Users of the OpenSSL libraries never query a provider directly for an algorithm implementation. Instead, the diverse OpenSSL APIs often have explicit fetching functions that do the work, and they return an appropriate algorithm object back to the user. These functions usually have the name <code>APINAME_fetch</code>, where <code>APINAME</code> is the name of the operation. For example <a href="../man3/EVP_MD_fetch.html">EVP_MD_fetch(3)</a> can be used to explicitly fetch a digest algorithm implementation. The user is responsible for freeing the object returned from the <code>APINAME_fetch</code> function using <code>APINAME_free</code> when it is no longer needed.</p>

<p>These fetching functions follow a fairly common pattern, where three arguments are passed:</p>

<dl>

<dt id="The-library-context">The library context</dt>
<dd>

<p>See <a href="../man3/OSSL_LIB_CTX.html">OSSL_LIB_CTX(3)</a> for a more detailed description. This may be NULL to signify the default (global) library context, or a context created by the user. Only providers loaded in this library context (see <a href="../man3/OSSL_PROVIDER_load.html">OSSL_PROVIDER_load(3)</a>) will be considered by the fetching function. In case no provider has been loaded in this library context then the default provider will be loaded as a fallback (see <a href="../man7/OSSL_PROVIDER-default.html">OSSL_PROVIDER-default(7)</a>).</p>

</dd>
<dt id="An-identifier">An identifier</dt>
<dd>

<p>For all currently implemented fetching functions this is the algorithm name.</p>

</dd>
<dt id="A-property-query-string">A property query string</dt>
<dd>

<p>The property query string used to guide selection of the algorithm implementation.</p>

</dd>
</dl>

<p>The algorithm implementation that is fetched can then be used with other diverse functions that use them. For example the <a href="../man3/EVP_DigestInit_ex.html">EVP_DigestInit_ex(3)</a> function takes as a parameter an <b>EVP_MD</b> object which may have been returned from an earlier call to <a href="../man3/EVP_MD_fetch.html">EVP_MD_fetch(3)</a>.</p>

<h2 id="Implicit-fetching">Implicit fetching</h2>

<p>OpenSSL has a number of functions that return an algorithm object with no associated implementation, such as <a href="../man3/EVP_sha256.html">EVP_sha256(3)</a>, <a href="../man3/EVP_aes_128_cbc.html">EVP_aes_128_cbc(3)</a>, <a href="../man3/EVP_get_cipherbyname.html">EVP_get_cipherbyname(3)</a> or <a href="../man3/EVP_get_digestbyname.html">EVP_get_digestbyname(3)</a>. These are present for compatibility with OpenSSL before version 3.0 where explicit fetching was not available.</p>

<p>When they are used with functions like <a href="../man3/EVP_DigestInit_ex.html">EVP_DigestInit_ex(3)</a> or <a href="../man3/EVP_CipherInit_ex.html">EVP_CipherInit_ex(3)</a>, the actual implementation to be used is fetched implicitly using default search criteria.</p>

<p>In some cases implicit fetching can also occur when a NULL algorithm parameter is supplied. In this case an algorithm implementation is implicitly fetched using default search criteria and an algorithm name that is consistent with the context in which it is being used.</p>

<p>Functions that revolve around <b>EVP_PKEY_CTX</b> and <a href="../man3/EVP_PKEY.html">EVP_PKEY(3)</a>, such as <a href="../man3/EVP_DigestSignInit.html">EVP_DigestSignInit(3)</a> and friends, all fetch the implementations implicitly. Because these functions involve both an operation type (such as <a href="../man3/EVP_SIGNATURE.html">EVP_SIGNATURE(3)</a>) and an <a href="../man3/EVP_KEYMGMT.html">EVP_KEYMGMT(3)</a> for the <a href="../man3/EVP_PKEY.html">EVP_PKEY(3)</a>, they try the following:</p>

<ol>

<li><p>Fetch the operation type implementation from any provider given a library context and property string stored in the <b>EVP_PKEY_CTX</b>.</p>

<p>If the provider of the operation type implementation is different from the provider of the <a href="../man3/EVP_PKEY.html">EVP_PKEY(3)</a>&#39;s <a href="../man3/EVP_KEYMGMT.html">EVP_KEYMGMT(3)</a> implementation, try to fetch a <a href="../man3/EVP_KEYMGMT.html">EVP_KEYMGMT(3)</a> implementation in the same provider as the operation type implementation and export the <a href="../man3/EVP_PKEY.html">EVP_PKEY(3)</a> to it (effectively making a temporary copy of the original key).</p>

<p>If anything in this step fails, the next step is used as a fallback.</p>

</li>
<li><p>As a fallback, try to fetch the operation type implementation from the same provider as the original <a href="../man3/EVP_PKEY.html">EVP_PKEY(3)</a>&#39;s <a href="../man3/EVP_KEYMGMT.html">EVP_KEYMGMT(3)</a>, still using the property string from the <b>EVP_PKEY_CTX</b>.</p>

</li>
</ol>

<h2 id="Performance">Performance</h2>

<p>If you perform the same operation many times then it is recommended to use <a href="#Explicit-fetching">&quot;Explicit fetching&quot;</a> to prefetch an algorithm once initially, and then pass this created object to any operations that are currently using <a href="#Implicit-fetching">&quot;Implicit fetching&quot;</a>. See an example of Explicit fetching in <a href="#USING-ALGORITHMS-IN-APPLICATIONS">&quot;USING ALGORITHMS IN APPLICATIONS&quot;</a>.</p>

<p>Prior to OpenSSL 3.0, constant method tables (such as EVP_sha256()) were used directly to access methods. If you pass one of these convenience functions to an operation the fixed methods are ignored, and only the name is used to internally fetch methods from a provider.</p>

<p>If the prefetched object is not passed to operations, then any implicit fetch will use the internally cached prefetched object, but it will still be slower than passing the prefetched object directly.</p>

<p>Fetching via a provider offers more flexibility, but it is slower than the old method, since it must search for the algorithm in all loaded providers, and then populate the method table using provider supplied methods. Internally OpenSSL caches similar algorithms on the first fetch (so loading a digest caches all digests).</p>

<p>The following methods can be used for prefetching:</p>

<dl>

<dt id="EVP_MD_fetch-3"><a href="../man3/EVP_MD_fetch.html">EVP_MD_fetch(3)</a></dt>
<dd>

</dd>
<dt id="EVP_CIPHER_fetch-3"><a href="../man3/EVP_CIPHER_fetch.html">EVP_CIPHER_fetch(3)</a></dt>
<dd>

</dd>
<dt id="EVP_KDF_fetch-3"><a href="../man3/EVP_KDF_fetch.html">EVP_KDF_fetch(3)</a></dt>
<dd>

</dd>
<dt id="EVP_MAC_fetch-3"><a href="../man3/EVP_MAC_fetch.html">EVP_MAC_fetch(3)</a></dt>
<dd>

</dd>
<dt id="EVP_KEM_fetch-3"><a href="../man3/EVP_KEM_fetch.html">EVP_KEM_fetch(3)</a></dt>
<dd>

</dd>
<dt id="OSSL_ENCODER_fetch-3"><a href="../man3/OSSL_ENCODER_fetch.html">OSSL_ENCODER_fetch(3)</a></dt>
<dd>

</dd>
<dt id="OSSL_DECODER_fetch-3"><a href="../man3/OSSL_DECODER_fetch.html">OSSL_DECODER_fetch(3)</a></dt>
<dd>

</dd>
<dt id="EVP_RAND_fetch-3"><a href="../man3/EVP_RAND_fetch.html">EVP_RAND_fetch(3)</a></dt>
<dd>

</dd>
</dl>

<p>The following methods are used internally when performing operations:</p>

<dl>

<dt id="EVP_KEYMGMT_fetch-3"><a href="../man3/EVP_KEYMGMT_fetch.html">EVP_KEYMGMT_fetch(3)</a></dt>
<dd>

</dd>
<dt id="EVP_KEYEXCH_fetch-3"><a href="../man3/EVP_KEYEXCH_fetch.html">EVP_KEYEXCH_fetch(3)</a></dt>
<dd>

</dd>
<dt id="EVP_SIGNATURE_fetch-3"><a href="../man3/EVP_SIGNATURE_fetch.html">EVP_SIGNATURE_fetch(3)</a></dt>
<dd>

</dd>
<dt id="OSSL_STORE_LOADER_fetch-3"><a href="../man3/OSSL_STORE_LOADER_fetch.html">OSSL_STORE_LOADER_fetch(3)</a></dt>
<dd>

</dd>
</dl>

<p>See <a href="../man7/OSSL_PROVIDER-default.html">OSSL_PROVIDER-default(7)</a>, &lt;OSSL_PROVIDER-fips(7)&gt; and &lt;OSSL_PROVIDER-legacy(7)&gt;for a list of algorithm names that can be fetched.</p>

<h1 id="FETCHING-EXAMPLES">FETCHING EXAMPLES</h1>

<p>The following section provides a series of examples of fetching algorithm implementations.</p>

<p>Fetch any available implementation of SHA2-256 in the default context. Note that some algorithms have aliases. So &quot;SHA256&quot; and &quot;SHA2-256&quot; are synonymous:</p>

<pre><code>EVP_MD *md = EVP_MD_fetch(NULL, &quot;SHA2-256&quot;, NULL);
...
EVP_MD_free(md);</code></pre>

<p>Fetch any available implementation of AES-128-CBC in the default context:</p>

<pre><code>EVP_CIPHER *cipher = EVP_CIPHER_fetch(NULL, &quot;AES-128-CBC&quot;, NULL);
...
EVP_CIPHER_free(cipher);</code></pre>

<p>Fetch an implementation of SHA2-256 from the default provider in the default context:</p>

<pre><code>EVP_MD *md = EVP_MD_fetch(NULL, &quot;SHA2-256&quot;, &quot;provider=default&quot;);
...
EVP_MD_free(md);</code></pre>

<p>Fetch an implementation of SHA2-256 that is not from the default provider in the default context:</p>

<pre><code>EVP_MD *md = EVP_MD_fetch(NULL, &quot;SHA2-256&quot;, &quot;provider!=default&quot;);
...
EVP_MD_free(md);</code></pre>

<p>Fetch an implementation of SHA2-256 from the default provider in the specified context:</p>

<pre><code>EVP_MD *md = EVP_MD_fetch(ctx, &quot;SHA2-256&quot;, &quot;provider=default&quot;);
...
EVP_MD_free(md);</code></pre>

<p>Load the legacy provider into the default context and then fetch an implementation of WHIRLPOOL from it:</p>

<pre><code>/* This only needs to be done once - usually at application start up */
OSSL_PROVIDER *legacy = OSSL_PROVIDER_load(NULL, &quot;legacy&quot;);

EVP_MD *md = EVP_MD_fetch(NULL, &quot;WHIRLPOOL&quot;, &quot;provider=legacy&quot;);
...
EVP_MD_free(md);</code></pre>

<p>Note that in the above example the property string &quot;provider=legacy&quot; is optional since, assuming no other providers have been loaded, the only implementation of the &quot;whirlpool&quot; algorithm is in the &quot;legacy&quot; provider. Also note that the default provider should be explicitly loaded if it is required in addition to other providers:</p>

<pre><code>/* This only needs to be done once - usually at application start up */
OSSL_PROVIDER *legacy = OSSL_PROVIDER_load(NULL, &quot;legacy&quot;);
OSSL_PROVIDER *default = OSSL_PROVIDER_load(NULL, &quot;default&quot;);

EVP_MD *md_whirlpool = EVP_MD_fetch(NULL, &quot;whirlpool&quot;, NULL);
EVP_MD *md_sha256 = EVP_MD_fetch(NULL, &quot;SHA2-256&quot;, NULL);
...
EVP_MD_free(md_whirlpool);
EVP_MD_free(md_sha256);</code></pre>

<h1 id="OPENSSL-PROVIDERS">OPENSSL PROVIDERS</h1>

<p>OpenSSL comes with a set of providers.</p>

<p>The algorithms available in each of these providers may vary due to build time configuration options. The <a href="../man1/openssl-list.html">openssl-list(1)</a> command can be used to list the currently available algorithms.</p>

<p>The names of the algorithms shown from <a href="../man1/openssl-list.html">openssl-list(1)</a> can be used as an algorithm identifier to the appropriate fetching function. Also see the provider specific manual pages linked below for further details about using the algorithms available in each of the providers.</p>

<p>As well as the OpenSSL providers third parties can also implement providers. For information on writing a provider see <a href="../man7/provider.html">provider(7)</a>.</p>

<h2 id="Default-provider">Default provider</h2>

<p>The default provider is built in as part of the <i>libcrypto</i> library and contains all of the most commonly used algorithm implementations. Should it be needed (if other providers are loaded and offer implementations of the same algorithms), the property query string &quot;provider=default&quot; can be used as a search criterion for these implementations. The default provider includes all of the functionality in the base provider below.</p>

<p>If you don&#39;t load any providers at all then the &quot;default&quot; provider will be automatically loaded. If you explicitly load any provider then the &quot;default&quot; provider would also need to be explicitly loaded if it is required.</p>

<p>See <a href="../man7/OSSL_PROVIDER-default.html">OSSL_PROVIDER-default(7)</a>.</p>

<h2 id="Base-provider">Base provider</h2>

<p>The base provider is built in as part of the <i>libcrypto</i> library and contains algorithm implementations for encoding and decoding for OpenSSL keys. Should it be needed (if other providers are loaded and offer implementations of the same algorithms), the property query string &quot;provider=base&quot; can be used as a search criterion for these implementations. Some encoding and decoding algorithm implementations are not FIPS algorithm implementations in themselves but support algorithms from the FIPS provider and are allowed for use in &quot;FIPS mode&quot;. The property query string &quot;fips=yes&quot; can be used to select such algorithms.</p>

<p>See <a href="../man7/OSSL_PROVIDER-base.html">OSSL_PROVIDER-base(7)</a>.</p>

<h2 id="FIPS-provider">FIPS provider</h2>

<p>The FIPS provider is a dynamically loadable module, and must therefore be loaded explicitly, either in code or through OpenSSL configuration (see <a href="../man5/config.html">config(5)</a>). It contains algorithm implementations that have been validated according to the FIPS 140-2 standard. Should it be needed (if other providers are loaded and offer implementations of the same algorithms), the property query string &quot;provider=fips&quot; can be used as a search criterion for these implementations. All approved algorithm implementations in the FIPS provider can also be selected with the property &quot;fips=yes&quot;. The FIPS provider may also contain non-approved algorithm implementations and these can be selected with the property &quot;fips=no&quot;.</p>

<p>See <a href="../man7/OSSL_PROVIDER-FIPS.html">OSSL_PROVIDER-FIPS(7)</a> and <a href="../man7/fips_module.html">fips_module(7)</a>.</p>

<h2 id="Legacy-provider">Legacy provider</h2>

<p>The legacy provider is a dynamically loadable module, and must therefore be loaded explicitly, either in code or through OpenSSL configuration (see <a href="../man5/config.html">config(5)</a>). It contains algorithm implementations that are considered insecure, or are no longer in common use such as MD2 or RC4. Should it be needed (if other providers are loaded and offer implementations of the same algorithms), the property &quot;provider=legacy&quot; can be used as a search criterion for these implementations.</p>

<p>See <a href="../man7/OSSL_PROVIDER-legacy.html">OSSL_PROVIDER-legacy(7)</a>.</p>

<h2 id="Null-provider">Null provider</h2>

<p>The null provider is built in as part of the <i>libcrypto</i> library. It contains no algorithms in it at all. When fetching algorithms the default provider will be automatically loaded if no other provider has been explicitly loaded. To prevent that from happening you can explicitly load the null provider.</p>

<p>See <a href="../man7/OSSL_PROVIDER-null.html">OSSL_PROVIDER-null(7)</a>.</p>

<h1 id="USING-ALGORITHMS-IN-APPLICATIONS">USING ALGORITHMS IN APPLICATIONS</h1>

<p>Cryptographic algorithms are made available to applications through use of the &quot;EVP&quot; APIs. Each of the various operations such as encryption, digesting, message authentication codes, etc., have a set of EVP function calls that can be invoked to use them. See the <a href="../man7/evp.html">evp(7)</a> page for further details.</p>

<p>Most of these follow a common pattern. A &quot;context&quot; object is first created. For example for a digest operation you would use an <b>EVP_MD_CTX</b>, and for an encryption/decryption operation you would use an <b>EVP_CIPHER_CTX</b>. The operation is then initialised ready for use via an &quot;init&quot; function - optionally passing in a set of parameters (using the <a href="../man3/OSSL_PARAM.html">OSSL_PARAM(3)</a> type) to configure how the operation should behave. Next data is fed into the operation in a series of &quot;update&quot; calls. The operation is finalised using a &quot;final&quot; call which will typically provide some kind of output. Finally the context is cleaned up and freed.</p>

<p>The following shows a complete example for doing this process for digesting data using SHA256. The process is similar for other operations such as encryption/decryption, signatures, message authentication codes, etc.</p>

<pre><code>#include &lt;stdio.h&gt;
#include &lt;openssl/evp.h&gt;
#include &lt;openssl/bio.h&gt;
#include &lt;openssl/err.h&gt;

int main(void)
{
    EVP_MD_CTX *ctx = NULL;
    EVP_MD *sha256 = NULL;
    const unsigned char msg[] = {
        0x00, 0x01, 0x02, 0x03
    };
    unsigned int len = 0;
    unsigned char *outdigest = NULL;
    int ret = 1;

    /* Create a context for the digest operation */
    ctx = EVP_MD_CTX_new();
    if (ctx == NULL)
        goto err;

    /*
     * Fetch the SHA256 algorithm implementation for doing the digest. We&#39;re
     * using the &quot;default&quot; library context here (first NULL parameter), and
     * we&#39;re not supplying any particular search criteria for our SHA256
     * implementation (second NULL parameter). Any SHA256 implementation will
     * do.
     * In a larger application this fetch would just be done once, and could
     * be used for multiple calls to other operations such as EVP_DigestInit_ex().
     */
    sha256 = EVP_MD_fetch(NULL, &quot;SHA256&quot;, NULL);
    if (sha256 == NULL)
        goto err;

   /* Initialise the digest operation */
   if (!EVP_DigestInit_ex(ctx, sha256, NULL))
       goto err;

    /*
     * Pass the message to be digested. This can be passed in over multiple
     * EVP_DigestUpdate calls if necessary
     */
    if (!EVP_DigestUpdate(ctx, msg, sizeof(msg)))
        goto err;

    /* Allocate the output buffer */
    outdigest = OPENSSL_malloc(EVP_MD_get_size(sha256));
    if (outdigest == NULL)
        goto err;

    /* Now calculate the digest itself */
    if (!EVP_DigestFinal_ex(ctx, outdigest, &amp;len))
        goto err;

    /* Print out the digest result */
    BIO_dump_fp(stdout, outdigest, len);

    ret = 0;

 err:
    /* Clean up all the resources we allocated */
    OPENSSL_free(outdigest);
    EVP_MD_free(sha256);
    EVP_MD_CTX_free(ctx);
    if (ret != 0)
       ERR_print_errors_fp(stderr);
    return ret;
}</code></pre>

<h1 id="CONFIGURATION">CONFIGURATION</h1>

<p>By default OpenSSL will load a configuration file when it is first used. This will set up various configuration settings within the default library context. Applications that create their own library contexts may optionally configure them with a config file using the <a href="../man3/OSSL_LIB_CTX_load_config.html">OSSL_LIB_CTX_load_config(3)</a> function.</p>

<p>The configuration file can be used to automatically load providers and set up default property query strings.</p>

<p>For information on the OpenSSL configuration file format see <a href="../man5/config.html">config(5)</a>.</p>

<h1 id="ENCODING-AND-DECODING-KEYS">ENCODING AND DECODING KEYS</h1>

<p>Many algorithms require the use of a key. Keys can be generated dynamically using the EVP APIs (for example see <a href="../man3/EVP_PKEY_Q_keygen.html">EVP_PKEY_Q_keygen(3)</a>). However it is often necessary to save or load keys (or their associated parameters) to or from some external format such as PEM or DER (see <a href="../man7/openssl-glossary.html">openssl-glossary(7)</a>). OpenSSL uses encoders and decoders to perform this task.</p>

<p>Encoders and decoders are just algorithm implementations in the same way as any other algorithm implementation in OpenSSL. They are implemented by providers. The OpenSSL encoders and decoders are available in the default provider. They are also duplicated in the base provider.</p>

<p>For information about encoders see <a href="../man3/OSSL_ENCODER_CTX_new_for_pkey.html">OSSL_ENCODER_CTX_new_for_pkey(3)</a>. For information about decoders see <a href="../man3/OSSL_DECODER_CTX_new_for_pkey.html">OSSL_DECODER_CTX_new_for_pkey(3)</a>.</p>

<h1 id="LIBRARY-CONVENTIONS">LIBRARY CONVENTIONS</h1>

<p>Many OpenSSL functions that &quot;get&quot; or &quot;set&quot; a value follow a naming convention using the numbers <b>0</b> and <b>1</b>, i.e. &quot;get0&quot;, &quot;get1&quot;, &quot;set0&quot; and &quot;set1&quot;. This can also apply to some functions that &quot;add&quot; a value to an existing set, i.e. &quot;add0&quot; and &quot;add1&quot;.</p>

<p>For example the functions:</p>

<pre><code>int X509_CRL_add0_revoked(X509_CRL *crl, X509_REVOKED *rev);
int X509_add1_trust_object(X509 *x, const ASN1_OBJECT *obj);</code></pre>

<p>In the <b>0</b> version the ownership of the object is passed to (for an add or set) or retained by (for a get) the parent object. For example after calling the X509_CRL_add0_revoked() function above, ownership of the <i>rev</i> object is passed to the <i>crl</i> object. Therefore, after calling this function <i>rev</i> should not be freed directly. It will be freed implicitly when <i>crl</i> is freed.</p>

<p>In the <b>1</b> version the ownership of the object is not passed to or retained by the parent object. Instead a copy or &quot;up ref&quot; of the object is performed. So after calling the X509_add1_trust_object() function above the application will still be responsible for freeing the <i>obj</i> value where appropriate.</p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man1/openssl.html">openssl(1)</a>, <a href="../man7/ssl.html">ssl(7)</a>, <a href="../man7/evp.html">evp(7)</a>, <a href="../man3/OSSL_LIB_CTX.html">OSSL_LIB_CTX(3)</a>, <a href="../man7/openssl-threads.html">openssl-threads(7)</a>, <a href="../man7/property.html">property(7)</a>, <a href="../man7/OSSL_PROVIDER-default.html">OSSL_PROVIDER-default(7)</a>, <a href="../man7/OSSL_PROVIDER-base.html">OSSL_PROVIDER-base(7)</a>, <a href="../man7/OSSL_PROVIDER-FIPS.html">OSSL_PROVIDER-FIPS(7)</a>, <a href="../man7/OSSL_PROVIDER-legacy.html">OSSL_PROVIDER-legacy(7)</a>, <a href="../man7/OSSL_PROVIDER-null.html">OSSL_PROVIDER-null(7)</a>, <a href="../man7/openssl-glossary.html">openssl-glossary(7)</a>, <a href="../man7/provider.html">provider(7)</a></p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2000-2023 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the Apache License 2.0 (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


