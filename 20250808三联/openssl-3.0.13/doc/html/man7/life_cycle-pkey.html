<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>life_cycle-pkey</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:root@localhost" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a>
    <ul>
      <li><a href="#State-Transition-Diagram">State Transition Diagram</a></li>
      <li><a href="#Formal-State-Transitions">Formal State Transitions</a></li>
    </ul>
  </li>
  <li><a href="#NOTES">NOTES</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#HISTORY">HISTORY</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>life_cycle-pkey - The PKEY algorithm life-cycle</p>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>All public keys (PKEYs) go through a number of stages in their life-cycle:</p>

<dl>

<dt id="start">start</dt>
<dd>

<p>This state represents the PKEY before it has been allocated. It is the starting state for any life-cycle transitions.</p>

</dd>
<dt id="newed">newed</dt>
<dd>

<p>This state represents the PKEY after it has been allocated.</p>

</dd>
<dt id="decapsulate">decapsulate</dt>
<dd>

<p>This state represents the PKEY when it is ready to perform a private key decapsulation operation.</p>

</dd>
<dt id="decrypt">decrypt</dt>
<dd>

<p>This state represents the PKEY when it is ready to decrypt some ciphertext.</p>

</dd>
<dt id="derive">derive</dt>
<dd>

<p>This state represents the PKEY when it is ready to derive a shared secret.</p>

</dd>
<dt id="digest-sign">digest sign</dt>
<dd>

<p>This state represents the PKEY when it is ready to perform a private key signature operation.</p>

</dd>
<dt id="encapsulate">encapsulate</dt>
<dd>

<p>This state represents the PKEY when it is ready to perform a public key encapsulation operation.</p>

</dd>
<dt id="encrypt">encrypt</dt>
<dd>

<p>This state represents the PKEY when it is ready to encrypt some plaintext.</p>

</dd>
<dt id="key-generation">key generation</dt>
<dd>

<p>This state represents the PKEY when it is ready to generate a new public/private key.</p>

</dd>
<dt id="parameter-generation">parameter generation</dt>
<dd>

<p>This state represents the PKEY when it is ready to generate key parameters.</p>

</dd>
<dt id="verify">verify</dt>
<dd>

<p>This state represents the PKEY when it is ready to verify a public key signature.</p>

</dd>
<dt id="verify-recover">verify recover</dt>
<dd>

<p>This state represents the PKEY when it is ready to recover a public key signature data.</p>

</dd>
<dt id="freed">freed</dt>
<dd>

<p>This state is entered when the PKEY is freed. It is the terminal state for all life-cycle transitions.</p>

</dd>
</dl>

<h2 id="State-Transition-Diagram">State Transition Diagram</h2>

<p>The usual life-cycle of a PKEY object is illustrated:</p>

<img src="img/pkey.png">

<h2 id="Formal-State-Transitions">Formal State Transitions</h2>

<p>This section defines all of the legal state transitions. This is the canonical list.</p>

<table style="border:1px solid; border-collapse:collapse">
<tr><th style="border:1px solid" align="left">Function Call</th>
    <th style="border:1px solid" colspan="13">Current State</th></tr>
<tr><th style="border:1px solid"></th>
    <th style="border:1px solid" align="center">start</th>
    <th style="border:1px solid" align="center">newed</th>
    <th style="border:1px solid" align="center">digest<br>sign</th>
    <th style="border:1px solid" align="center">verify</th>
    <th style="border:1px solid" align="center">verify<br>recover</th>
    <th style="border:1px solid" align="center">encrypt</th>
    <th style="border:1px solid" align="center">decrypt</th>
    <th style="border:1px solid" align="center">derive</th>
    <th style="border:1px solid" align="center">encapsulate</th>
    <th style="border:1px solid" align="center">decapsulate</th>
    <th style="border:1px solid" align="center">parameter<br>generation</th>
    <th style="border:1px solid" align="center">key<br>generation</th>
    <th style="border:1px solid" align="center">freed</th>
</tr>

<tr><th style="border:1px solid" align="left">EVP_PKEY_CTX_new</th>
    <td style="border:1px solid" align="center">newed</td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
</tr>
<tr><th style="border:1px solid" align="left">EVP_PKEY_CTX_new_id</th>
    <td style="border:1px solid" align="center">newed</td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
</tr>
<tr><th style="border:1px solid" align="left">EVP_PKEY_CTX_new_from_name</th>
    <td style="border:1px solid" align="center">newed</td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
</tr>
<tr><th style="border:1px solid" align="left">EVP_PKEY_CTX_new_from_pkey</th>
    <td style="border:1px solid" align="center">newed</td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
</tr>
<tr><th style="border:1px solid" align="left">EVP_PKEY_sign_init</th>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center">digest<br>sign</td>
    <td style="border:1px solid" align="center">digest<br>sign</td>
    <td style="border:1px solid" align="center">digest<br>sign</td>
    <td style="border:1px solid" align="center">digest<br>sign</td>
    <td style="border:1px solid" align="center">digest<br>sign</td>
    <td style="border:1px solid" align="center">digest<br>sign</td>
    <td style="border:1px solid" align="center">digest<br>sign</td>
    <td style="border:1px solid" align="center">digest<br>sign</td>
    <td style="border:1px solid" align="center">digest<br>sign</td>
    <td style="border:1px solid" align="center">digest<br>sign</td>
    <td style="border:1px solid" align="center">digest<br>sign</td>
    <td style="border:1px solid" align="center"></td>
</tr>
<tr><th style="border:1px solid" align="left">EVP_PKEY_sign</th>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center">digest<br>sign</td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
</tr>
<tr><th style="border:1px solid" align="left">EVP_PKEY_verify_init</th>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center">verify</td>
    <td style="border:1px solid" align="center">verify</td>
    <td style="border:1px solid" align="center">verify</td>
    <td style="border:1px solid" align="center">verify</td>
    <td style="border:1px solid" align="center">verify</td>
    <td style="border:1px solid" align="center">verify</td>
    <td style="border:1px solid" align="center">verify</td>
    <td style="border:1px solid" align="center">verify</td>
    <td style="border:1px solid" align="center">verify</td>
    <td style="border:1px solid" align="center">verify</td>
    <td style="border:1px solid" align="center">verify</td>
    <td style="border:1px solid" align="center"></td>
</tr>
<tr><th style="border:1px solid" align="left">EVP_PKEY_verify</th>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center">verify</td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
</tr>
<tr><th style="border:1px solid" align="left">EVP_PKEY_verify_recover_init</th>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center">verify<br>recover</td>
    <td style="border:1px solid" align="center">verify<br>recover</td>
    <td style="border:1px solid" align="center">verify<br>recover</td>
    <td style="border:1px solid" align="center">verify<br>recover</td>
    <td style="border:1px solid" align="center">verify<br>recover</td>
    <td style="border:1px solid" align="center">verify<br>recover</td>
    <td style="border:1px solid" align="center">verify<br>recover</td>
    <td style="border:1px solid" align="center">verify<br>recover</td>
    <td style="border:1px solid" align="center">verify<br>recover</td>
    <td style="border:1px solid" align="center">verify<br>recover</td>
    <td style="border:1px solid" align="center">verify<br>recover</td>
    <td style="border:1px solid" align="center"></td>
</tr>
<tr><th style="border:1px solid" align="left">EVP_PKEY_verify_recover</th>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center">verify<br>recover</td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
</tr>
<tr><th style="border:1px solid" align="left">EVP_PKEY_encrypt_init</th>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center">encrypt</td>
    <td style="border:1px solid" align="center">encrypt</td>
    <td style="border:1px solid" align="center">encrypt</td>
    <td style="border:1px solid" align="center">encrypt</td>
    <td style="border:1px solid" align="center">encrypt</td>
    <td style="border:1px solid" align="center">encrypt</td>
    <td style="border:1px solid" align="center">encrypt</td>
    <td style="border:1px solid" align="center">encrypt</td>
    <td style="border:1px solid" align="center">encrypt</td>
    <td style="border:1px solid" align="center">encrypt</td>
    <td style="border:1px solid" align="center">encrypt</td>
    <td style="border:1px solid" align="center"></td>
</tr>
<tr><th style="border:1px solid" align="left">EVP_PKEY_encrypt</th>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center">encrypt</td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
</tr>
<tr><th style="border:1px solid" align="left">EVP_PKEY_decrypt_init</th>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center">decrypt</td>
    <td style="border:1px solid" align="center">decrypt</td>
    <td style="border:1px solid" align="center">decrypt</td>
    <td style="border:1px solid" align="center">decrypt</td>
    <td style="border:1px solid" align="center">decrypt</td>
    <td style="border:1px solid" align="center">decrypt</td>
    <td style="border:1px solid" align="center">decrypt</td>
    <td style="border:1px solid" align="center">decrypt</td>
    <td style="border:1px solid" align="center">decrypt</td>
    <td style="border:1px solid" align="center">decrypt</td>
    <td style="border:1px solid" align="center">decrypt</td>
    <td style="border:1px solid" align="center"></td>
</tr>
<tr><th style="border:1px solid" align="left">EVP_PKEY_decrypt</th>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center">decrypt</td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
</tr>
<tr><th style="border:1px solid" align="left">EVP_PKEY_derive_init</th>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center">derive</td>
    <td style="border:1px solid" align="center">derive</td>
    <td style="border:1px solid" align="center">derive</td>
    <td style="border:1px solid" align="center">derive</td>
    <td style="border:1px solid" align="center">derive</td>
    <td style="border:1px solid" align="center">derive</td>
    <td style="border:1px solid" align="center">derive</td>
    <td style="border:1px solid" align="center">derive</td>
    <td style="border:1px solid" align="center">derive</td>
    <td style="border:1px solid" align="center">derive</td>
    <td style="border:1px solid" align="center">derive</td>
    <td style="border:1px solid" align="center"></td>
</tr>
<tr><th style="border:1px solid" align="left">EVP_PKEY_derive_set_peer</th>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center">derive</td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
</tr>
<tr><th style="border:1px solid" align="left">EVP_PKEY_derive</th>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center">derive</td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
</tr>
<tr><th style="border:1px solid" align="left">EVP_PKEY_encapsulate_init</th>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center">encapsulate</td>
    <td style="border:1px solid" align="center">encapsulate</td>
    <td style="border:1px solid" align="center">encapsulate</td>
    <td style="border:1px solid" align="center">encapsulate</td>
    <td style="border:1px solid" align="center">encapsulate</td>
    <td style="border:1px solid" align="center">encapsulate</td>
    <td style="border:1px solid" align="center">encapsulate</td>
    <td style="border:1px solid" align="center">encapsulate</td>
    <td style="border:1px solid" align="center">encapsulate</td>
    <td style="border:1px solid" align="center">encapsulate</td>
    <td style="border:1px solid" align="center">encapsulate</td>
    <td style="border:1px solid" align="center"></td>
</tr>
<tr><th style="border:1px solid" align="left">EVP_PKEY_encapsulate</th>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center">encapsulate</td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
</tr>
<tr><th style="border:1px solid" align="left">EVP_PKEY_decapsulate_init</th>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center">decapsulate</td>
    <td style="border:1px solid" align="center">decapsulate</td>
    <td style="border:1px solid" align="center">decapsulate</td>
    <td style="border:1px solid" align="center">decapsulate</td>
    <td style="border:1px solid" align="center">decapsulate</td>
    <td style="border:1px solid" align="center">decapsulate</td>
    <td style="border:1px solid" align="center">decapsulate</td>
    <td style="border:1px solid" align="center">decapsulate</td>
    <td style="border:1px solid" align="center">decapsulate</td>
    <td style="border:1px solid" align="center">decapsulate</td>
    <td style="border:1px solid" align="center">decapsulate</td>
    <td style="border:1px solid" align="center"></td>
</tr>
<tr><th style="border:1px solid" align="left">EVP_PKEY_decapsulate</th>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center">decapsulate</td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
</tr>
<tr><th style="border:1px solid" align="left">EVP_PKEY_paramgen_init</th>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center">parameter<br>generation</td>
    <td style="border:1px solid" align="center">parameter<br>generation</td>
    <td style="border:1px solid" align="center">parameter<br>generation</td>
    <td style="border:1px solid" align="center">parameter<br>generation</td>
    <td style="border:1px solid" align="center">parameter<br>generation</td>
    <td style="border:1px solid" align="center">parameter<br>generation</td>
    <td style="border:1px solid" align="center">parameter<br>generation</td>
    <td style="border:1px solid" align="center">parameter<br>generation</td>
    <td style="border:1px solid" align="center">parameter<br>generation</td>
    <td style="border:1px solid" align="center">parameter<br>generation</td>
    <td style="border:1px solid" align="center">parameter<br>generation</td>
    <td style="border:1px solid" align="center"></td>
</tr>
<tr><th style="border:1px solid" align="left">EVP_PKEY_paramgen</th>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center">parameter<br>generation</td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
</tr>
<tr><th style="border:1px solid" align="left">EVP_PKEY_keygen_init</th>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center">key<br>generation</td>
    <td style="border:1px solid" align="center">key<br>generation</td>
    <td style="border:1px solid" align="center">key<br>generation</td>
    <td style="border:1px solid" align="center">key<br>generation</td>
    <td style="border:1px solid" align="center">key<br>generation</td>
    <td style="border:1px solid" align="center">key<br>generation</td>
    <td style="border:1px solid" align="center">key<br>generation</td>
    <td style="border:1px solid" align="center">key<br>generation</td>
    <td style="border:1px solid" align="center">key<br>generation</td>
    <td style="border:1px solid" align="center">key<br>generation</td>
    <td style="border:1px solid" align="center">key<br>generation</td>
    <td style="border:1px solid" align="center"></td>
</tr>
<tr><th style="border:1px solid" align="left">EVP_PKEY_keygen</th>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center">key<br>generation</td>
    <td style="border:1px solid" align="center"></td>
</tr>
<tr><th style="border:1px solid" align="left">EVP_PKEY_gen</th>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center">parameter<br>generation</td>
    <td style="border:1px solid" align="center">key<br>generation</td>
    <td style="border:1px solid" align="center"></td>
</tr>
<tr><th style="border:1px solid" align="left">EVP_PKEY_CTX_get_params</th>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center">newed</td>
    <td style="border:1px solid" align="center">digest<br>sign</td>
    <td style="border:1px solid" align="center">verify</td>
    <td style="border:1px solid" align="center">verify<br>recover</td>
    <td style="border:1px solid" align="center">encrypt</td>
    <td style="border:1px solid" align="center">decrypt</td>
    <td style="border:1px solid" align="center">derive</td>
    <td style="border:1px solid" align="center">encapsulate</td>
    <td style="border:1px solid" align="center">decapsulate</td>
    <td style="border:1px solid" align="center">parameter<br>generation</td>
    <td style="border:1px solid" align="center">key<br>generation</td>
    <td style="border:1px solid" align="center"></td>
</tr>
<tr><th style="border:1px solid" align="left">EVP_PKEY_CTX_set_params</th>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center">newed</td>
    <td style="border:1px solid" align="center">digest<br>sign</td>
    <td style="border:1px solid" align="center">verify</td>
    <td style="border:1px solid" align="center">verify<br>recover</td>
    <td style="border:1px solid" align="center">encrypt</td>
    <td style="border:1px solid" align="center">decrypt</td>
    <td style="border:1px solid" align="center">derive</td>
    <td style="border:1px solid" align="center">encapsulate</td>
    <td style="border:1px solid" align="center">decapsulate</td>
    <td style="border:1px solid" align="center">parameter<br>generation</td>
    <td style="border:1px solid" align="center">key<br>generation</td>
    <td style="border:1px solid" align="center"></td>
</tr>
<tr><th style="border:1px solid" align="left">EVP_PKEY_CTX_gettable_params</th>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center">newed</td>
    <td style="border:1px solid" align="center">digest<br>sign</td>
    <td style="border:1px solid" align="center">verify</td>
    <td style="border:1px solid" align="center">verify<br>recover</td>
    <td style="border:1px solid" align="center">encrypt</td>
    <td style="border:1px solid" align="center">decrypt</td>
    <td style="border:1px solid" align="center">derive</td>
    <td style="border:1px solid" align="center">encapsulate</td>
    <td style="border:1px solid" align="center">decapsulate</td>
    <td style="border:1px solid" align="center">parameter<br>generation</td>
    <td style="border:1px solid" align="center">key<br>generation</td>
    <td style="border:1px solid" align="center"></td>
</tr>
<tr><th style="border:1px solid" align="left">EVP_PKEY_CTX_settable_params</th>
    <td style="border:1px solid" align="center"></td>
    <td style="border:1px solid" align="center">newed</td>
    <td style="border:1px solid" align="center">digest<br>sign</td>
    <td style="border:1px solid" align="center">verify</td>
    <td style="border:1px solid" align="center">verify<br>recover</td>
    <td style="border:1px solid" align="center">encrypt</td>
    <td style="border:1px solid" align="center">decrypt</td>
    <td style="border:1px solid" align="center">derive</td>
    <td style="border:1px solid" align="center">encapsulate</td>
    <td style="border:1px solid" align="center">decapsulate</td>
    <td style="border:1px solid" align="center">parameter<br>generation</td>
    <td style="border:1px solid" align="center">key<br>generation</td>
    <td style="border:1px solid" align="center"></td>
</tr>
<tr><th style="border:1px solid" align="left">EVP_PKEY_CTX_free</th>
    <td style="border:1px solid" align="center">freed</td>
    <td style="border:1px solid" align="center">freed</td>
    <td style="border:1px solid" align="center">freed</td>
    <td style="border:1px solid" align="center">freed</td>
    <td style="border:1px solid" align="center">freed</td>
    <td style="border:1px solid" align="center">freed</td>
    <td style="border:1px solid" align="center">freed</td>
    <td style="border:1px solid" align="center">freed</td>
    <td style="border:1px solid" align="center">freed</td>
    <td style="border:1px solid" align="center">freed</td>
    <td style="border:1px solid" align="center">freed</td>
    <td style="border:1px solid" align="center">freed</td>
    <td style="border:1px solid" align="center"></td>
</tr>
</table>

<h1 id="NOTES">NOTES</h1>

<p>At some point the EVP layer will begin enforcing the transitions described herein.</p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man3/EVP_PKEY_new.html">EVP_PKEY_new(3)</a>, <a href="../man3/EVP_PKEY_decapsulate.html">EVP_PKEY_decapsulate(3)</a>, <a href="../man3/EVP_PKEY_decrypt.html">EVP_PKEY_decrypt(3)</a>, <a href="../man3/EVP_PKEY_encapsulate.html">EVP_PKEY_encapsulate(3)</a>, <a href="../man3/EVP_PKEY_encrypt.html">EVP_PKEY_encrypt(3)</a>, <a href="../man3/EVP_PKEY_derive.html">EVP_PKEY_derive(3)</a>, <a href="../man3/EVP_PKEY_keygen.html">EVP_PKEY_keygen(3)</a>, <a href="../man3/EVP_PKEY_sign.html">EVP_PKEY_sign(3)</a>, <a href="../man3/EVP_PKEY_verify.html">EVP_PKEY_verify(3)</a>, <a href="../man3/EVP_PKEY_verify_recover.html">EVP_PKEY_verify_recover(3)</a></p>

<h1 id="HISTORY">HISTORY</h1>

<p>The provider PKEY interface was introduced in OpenSSL 3.0.</p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2021-2023 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the Apache License 2.0 (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


