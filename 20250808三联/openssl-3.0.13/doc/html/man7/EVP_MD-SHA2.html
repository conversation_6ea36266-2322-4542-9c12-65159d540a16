<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>EVP_MD-SHA2</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:root@localhost" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a>
    <ul>
      <li><a href="#Identities">Identities</a></li>
      <li><a href="#Gettable-Parameters">Gettable Parameters</a></li>
    </ul>
  </li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>EVP_MD-SHA2 - The SHA2 EVP_MD implementation</p>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>Support for computing SHA2 digests through the <b>EVP_MD</b> API.</p>

<h2 id="Identities">Identities</h2>

<p>This implementation includes the following varieties:</p>

<ul>

<li><p>Available with the FIPS provider as well as the default provider:</p>

<dl>

<dt id="SHA2-224">SHA2-224</dt>
<dd>

<p>Known names are &quot;SHA2-224&quot;, &quot;SHA-224&quot; and &quot;SHA224&quot;.</p>

</dd>
<dt id="SHA2-256">SHA2-256</dt>
<dd>

<p>Known names are &quot;SHA2-256&quot;, &quot;SHA-256&quot; and &quot;SHA256&quot;.</p>

</dd>
<dt id="SHA2-384">SHA2-384</dt>
<dd>

<p>Known names are &quot;SHA2-384&quot;, &quot;SHA-384&quot; and &quot;SHA384&quot;.</p>

</dd>
<dt id="SHA2-512">SHA2-512</dt>
<dd>

<p>Known names are &quot;SHA2-512&quot;, &quot;SHA-512&quot; and &quot;SHA512&quot;.</p>

</dd>
</dl>

</li>
<li><p>Available with the default provider:</p>

<dl>

<dt id="SHA2-512-224">SHA2-512/224</dt>
<dd>

<p>Known names are &quot;SHA2-512/224&quot;, &quot;SHA-512/224&quot; and &quot;SHA512-224&quot;.</p>

</dd>
<dt id="SHA2-512-256">SHA2-512/256</dt>
<dd>

<p>Known names are &quot;SHA2-512/256&quot;, &quot;SHA-512/256&quot; and &quot;SHA512-256&quot;.</p>

</dd>
</dl>

</li>
</ul>

<h2 id="Gettable-Parameters">Gettable Parameters</h2>

<p>This implementation supports the common gettable parameters described in <a href="../man7/EVP_MD-common.html">EVP_MD-common(7)</a>.</p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man7/provider-digest.html">provider-digest(7)</a>, <a href="../man7/OSSL_PROVIDER-FIPS.html">OSSL_PROVIDER-FIPS(7)</a>, <a href="../man7/OSSL_PROVIDER-default.html">OSSL_PROVIDER-default(7)</a></p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2020 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the Apache License 2.0 (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


