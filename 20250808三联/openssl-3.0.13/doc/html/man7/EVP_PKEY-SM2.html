<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>EVP_PKEY-SM2</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:root@localhost" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a>
    <ul>
      <li><a href="#Common-SM2-parameters">Common SM2 parameters</a></li>
    </ul>
  </li>
  <li><a href="#NOTES">NOTES</a></li>
  <li><a href="#EXAMPLES">EXAMPLES</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>EVP_PKEY-SM2, EVP_KEYMGMT-SM2, SM2 - EVP_PKEY keytype support for the Chinese SM2 signature and encryption algorithms</p>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>The <b>SM2</b> algorithm was first defined by the Chinese national standard GM/T 0003-2012 and was later standardized by ISO as ISO/IEC 14888. <b>SM2</b> is actually an elliptic curve based algorithm. The current implementation in OpenSSL supports both signature and encryption schemes via the EVP interface.</p>

<p>When doing the <b>SM2</b> signature algorithm, it requires a distinguishing identifier to form the message prefix which is hashed before the real message is hashed.</p>

<h2 id="Common-SM2-parameters">Common SM2 parameters</h2>

<p>SM2 uses the parameters defined in <a href="../man7/EVP_PKEY-EC.html">&quot;Common EC parameters&quot; in EVP_PKEY-EC(7)</a>. The following parameters are different:</p>

<dl>

<dt id="cofactor-OSSL_PKEY_PARAM_EC_COFACTOR-unsigned-integer">&quot;cofactor&quot; (<b>OSSL_PKEY_PARAM_EC_COFACTOR</b>) &lt;unsigned integer&gt;</dt>
<dd>

<p>This parameter is ignored for <b>SM2</b>.</p>

</dd>
<dt id="OSSL_PKEY_PARAM_DEFAULT_DIGEST-UTF8-string">(<b>OSSL_PKEY_PARAM_DEFAULT_DIGEST</b>) &lt;UTF8 string&gt;</dt>
<dd>

<p>Getter that returns the default digest name. (Currently returns &quot;SM3&quot; as of OpenSSL 3.0).</p>

</dd>
</dl>

<h1 id="NOTES">NOTES</h1>

<p><b>SM2</b> signatures can be generated by using the &#39;DigestSign&#39; series of APIs, for instance, EVP_DigestSignInit(), EVP_DigestSignUpdate() and EVP_DigestSignFinal(). Ditto for the verification process by calling the &#39;DigestVerify&#39; series of APIs.</p>

<p>Before computing an <b>SM2</b> signature, an <b>EVP_PKEY_CTX</b> needs to be created, and an <b>SM2</b> ID must be set for it, like this:</p>

<pre><code>EVP_PKEY_CTX_set1_id(pctx, id, id_len);</code></pre>

<p>Before calling the EVP_DigestSignInit() or EVP_DigestVerifyInit() functions, that <b>EVP_PKEY_CTX</b> should be assigned to the <b>EVP_MD_CTX</b>, like this:</p>

<pre><code>EVP_MD_CTX_set_pkey_ctx(mctx, pctx);</code></pre>

<p>There is normally no need to pass a <b>pctx</b> parameter to EVP_DigestSignInit() or EVP_DigestVerifyInit() in such a scenario.</p>

<p>SM2 can be tested with the <a href="../man1/openssl-speed.html">openssl-speed(1)</a> application since version 3.0. Currently, the only valid algorithm name is <b>sm2</b>.</p>

<p>Since version 3.0, SM2 keys can be generated and loaded only when the domain parameters specify the SM2 elliptic curve.</p>

<h1 id="EXAMPLES">EXAMPLES</h1>

<p>This example demonstrates the calling sequence for using an <b>EVP_PKEY</b> to verify a message with the SM2 signature algorithm and the SM3 hash algorithm:</p>

<pre><code>#include &lt;openssl/evp.h&gt;

/* obtain an EVP_PKEY using whatever methods... */
mctx = EVP_MD_CTX_new();
pctx = EVP_PKEY_CTX_new(pkey, NULL);
EVP_PKEY_CTX_set1_id(pctx, id, id_len);
EVP_MD_CTX_set_pkey_ctx(mctx, pctx);
EVP_DigestVerifyInit(mctx, NULL, EVP_sm3(), NULL, pkey);
EVP_DigestVerifyUpdate(mctx, msg, msg_len);
EVP_DigestVerifyFinal(mctx, sig, sig_len)</code></pre>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man3/EVP_PKEY_CTX_new.html">EVP_PKEY_CTX_new(3)</a>, <a href="../man3/EVP_DigestSignInit.html">EVP_DigestSignInit(3)</a>, <a href="../man3/EVP_DigestVerifyInit.html">EVP_DigestVerifyInit(3)</a>, <a href="../man3/EVP_PKEY_CTX_set1_id.html">EVP_PKEY_CTX_set1_id(3)</a>, <a href="../man3/EVP_MD_CTX_set_pkey_ctx.html">EVP_MD_CTX_set_pkey_ctx(3)</a></p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2018-2021 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the Apache License 2.0 (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


