<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>EVP_KDF-TLS1_PRF</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:root@localhost" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a>
    <ul>
      <li><a href="#Identity">Identity</a></li>
      <li><a href="#Supported-parameters">Supported parameters</a></li>
    </ul>
  </li>
  <li><a href="#NOTES">NOTES</a></li>
  <li><a href="#EXAMPLES">EXAMPLES</a></li>
  <li><a href="#CONFORMING-TO">CONFORMING TO</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#HISTORY">HISTORY</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>EVP_KDF-TLS1_PRF - The TLS1 PRF EVP_KDF implementation</p>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>Support for computing the <b>TLS1</b> PRF through the <b>EVP_KDF</b> API.</p>

<p>The EVP_KDF-TLS1_PRF algorithm implements the PRF used by TLS versions up to and including TLS 1.2.</p>

<h2 id="Identity">Identity</h2>

<p>&quot;TLS1-PRF&quot; is the name for this implementation; it can be used with the EVP_KDF_fetch() function.</p>

<h2 id="Supported-parameters">Supported parameters</h2>

<p>The supported parameters are:</p>

<dl>

<dt id="properties-OSSL_KDF_PARAM_PROPERTIES-UTF8-string">&quot;properties&quot; (<b>OSSL_KDF_PARAM_PROPERTIES</b>) &lt;UTF8 string&gt;</dt>
<dd>

</dd>
<dt id="digest-OSSL_KDF_PARAM_DIGEST-UTF8-string">&quot;digest&quot; (<b>OSSL_KDF_PARAM_DIGEST</b>) &lt;UTF8 string&gt;</dt>
<dd>

<p>These parameters work as described in <a href="../man3/EVP_KDF.html">&quot;PARAMETERS&quot; in EVP_KDF(3)</a>.</p>

<p>The <b>OSSL_KDF_PARAM_DIGEST</b> parameter is used to set the message digest associated with the TLS PRF. EVP_md5_sha1() is treated as a special case which uses the PRF algorithm using both <b>MD5</b> and <b>SHA1</b> as used in TLS 1.0 and 1.1.</p>

</dd>
<dt id="secret-OSSL_KDF_PARAM_SECRET-octet-string">&quot;secret&quot; (<b>OSSL_KDF_PARAM_SECRET</b>) &lt;octet string&gt;</dt>
<dd>

<p>This parameter sets the secret value of the TLS PRF. Any existing secret value is replaced.</p>

</dd>
<dt id="seed-OSSL_KDF_PARAM_SEED-octet-string">&quot;seed&quot; (<b>OSSL_KDF_PARAM_SEED</b>) &lt;octet string&gt;</dt>
<dd>

<p>This parameter sets the context seed. The length of the context seed cannot exceed 1024 bytes; this should be more than enough for any normal use of the TLS PRF.</p>

</dd>
</dl>

<h1 id="NOTES">NOTES</h1>

<p>A context for the TLS PRF can be obtained by calling:</p>

<pre><code>EVP_KDF *kdf = EVP_KDF_fetch(NULL, &quot;TLS1-PRF&quot;, NULL);
EVP_KDF_CTX *kctx = EVP_KDF_CTX_new(kdf);</code></pre>

<p>The digest, secret value and seed must be set before a key is derived otherwise an error will occur.</p>

<p>The output length of the PRF is specified by the <i>keylen</i> parameter to the EVP_KDF_derive() function.</p>

<h1 id="EXAMPLES">EXAMPLES</h1>

<p>This example derives 10 bytes using SHA-256 with the secret key &quot;secret&quot; and seed value &quot;seed&quot;:</p>

<pre><code>EVP_KDF *kdf;
EVP_KDF_CTX *kctx;
unsigned char out[10];
OSSL_PARAM params[4], *p = params;

kdf = EVP_KDF_fetch(NULL, &quot;TLS1-PRF&quot;, NULL);
kctx = EVP_KDF_CTX_new(kdf);
EVP_KDF_free(kdf);

*p++ = OSSL_PARAM_construct_utf8_string(OSSL_KDF_PARAM_DIGEST,
                                        SN_sha256, strlen(SN_sha256));
*p++ = OSSL_PARAM_construct_octet_string(OSSL_KDF_PARAM_SECRET,
                                         &quot;secret&quot;, (size_t)6);
*p++ = OSSL_PARAM_construct_octet_string(OSSL_KDF_PARAM_SEED,
                                         &quot;seed&quot;, (size_t)4);
*p = OSSL_PARAM_construct_end();
if (EVP_KDF_derive(kctx, out, sizeof(out), params) &lt;= 0) {
    error(&quot;EVP_KDF_derive&quot;);
}
EVP_KDF_CTX_free(kctx);</code></pre>

<h1 id="CONFORMING-TO">CONFORMING TO</h1>

<p>RFC 2246, RFC 5246 and NIST SP 800-135 r1</p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man3/EVP_KDF.html">EVP_KDF(3)</a>, <a href="../man3/EVP_KDF_CTX_new.html">EVP_KDF_CTX_new(3)</a>, <a href="../man3/EVP_KDF_CTX_free.html">EVP_KDF_CTX_free(3)</a>, <a href="../man3/EVP_KDF_CTX_set_params.html">EVP_KDF_CTX_set_params(3)</a>, <a href="../man3/EVP_KDF_derive.html">EVP_KDF_derive(3)</a>, <a href="../man3/EVP_KDF.html">&quot;PARAMETERS&quot; in EVP_KDF(3)</a></p>

<h1 id="HISTORY">HISTORY</h1>

<p>This functionality was added in OpenSSL 3.0.</p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2018-2021 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the Apache License 2.0 (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


