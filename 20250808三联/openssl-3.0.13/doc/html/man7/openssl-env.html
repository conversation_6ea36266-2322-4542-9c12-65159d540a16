<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>openssl-env</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:root@localhost" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>openssl-env - OpenSSL environment variables</p>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>The OpenSSL libraries use environment variables to override the compiled-in default paths for various data. To avoid security risks, the environment is usually not consulted when the executable is set-user-ID or set-group-ID.</p>

<dl>

<dt id="CTLOG_FILE"><b>CTLOG_FILE</b></dt>
<dd>

<p>Specifies the path to a certificate transparency log list. See <a href="../man3/CTLOG_STORE_new.html">CTLOG_STORE_new(3)</a>.</p>

</dd>
<dt id="OPENSSL"><b>OPENSSL</b></dt>
<dd>

<p>Specifies the path to the <b>openssl</b> executable. Used by the <b>rehash</b> script (see <a href="../man1/openssl-rehash.html">&quot;Script Configuration&quot; in openssl-rehash(1)</a>) and by the <b>CA.pl</b> script (see <a href="../man1/CA.pl.html">&quot;NOTES&quot; in CA.pl(1)</a></p>

</dd>
<dt id="OPENSSL_CONF-OPENSSL_CONF_INCLUDE"><b>OPENSSL_CONF</b>, <b>OPENSSL_CONF_INCLUDE</b></dt>
<dd>

<p>Specifies the path to a configuration file and the directory for included files. See <a href="../man5/config.html">config(5)</a>.</p>

</dd>
<dt id="OPENSSL_CONFIG"><b>OPENSSL_CONFIG</b></dt>
<dd>

<p>Specifies a configuration option and filename for the <b>req</b> and <b>ca</b> commands invoked by the <b>CA.pl</b> script. See <a href="../man1/CA.pl.html">CA.pl(1)</a>.</p>

</dd>
<dt id="OPENSSL_ENGINES"><b>OPENSSL_ENGINES</b></dt>
<dd>

<p>Specifies the directory from which dynamic engines are loaded. See <a href="../man1/openssl-engine.html">openssl-engine(1)</a>.</p>

</dd>
<dt id="OPENSSL_MALLOC_FD-OPENSSL_MALLOC_FAILURES"><b>OPENSSL_MALLOC_FD</b>, <b>OPENSSL_MALLOC_FAILURES</b></dt>
<dd>

<p>If built with debugging, this allows memory allocation to fail. See <a href="../man3/OPENSSL_malloc.html">OPENSSL_malloc(3)</a>.</p>

</dd>
<dt id="OPENSSL_MODULES"><b>OPENSSL_MODULES</b></dt>
<dd>

<p>Specifies the directory from which cryptographic providers are loaded. Equivalently, the generic <b>-provider-path</b> command-line option may be used.</p>

</dd>
<dt id="OPENSSL_WIN32_UTF8"><b>OPENSSL_WIN32_UTF8</b></dt>
<dd>

<p>If set, then <a href="../man3/UI_OpenSSL.html">UI_OpenSSL(3)</a> returns UTF-8 encoded strings, rather than ones encoded in the current code page, and the <a href="../man1/openssl.html">openssl(1)</a> program also transcodes the command-line parameters from the current code page to UTF-8. This environment variable is only checked on Microsoft Windows platforms.</p>

</dd>
<dt id="RANDFILE"><b>RANDFILE</b></dt>
<dd>

<p>The state file for the random number generator. This should not be needed in normal use. See <a href="../man3/RAND_load_file.html">RAND_load_file(3)</a>.</p>

</dd>
<dt id="SSL_CERT_DIR-SSL_CERT_FILE"><b>SSL_CERT_DIR</b>, <b>SSL_CERT_FILE</b></dt>
<dd>

<p>Specify the default directory or file containing CA certificates. See <a href="../man3/SSL_CTX_load_verify_locations.html">SSL_CTX_load_verify_locations(3)</a>.</p>

</dd>
<dt id="TSGET"><b>TSGET</b></dt>
<dd>

<p>Additional arguments for the <a href="../man1/tsget.html">tsget(1)</a> command.</p>

</dd>
<dt id="OPENSSL_ia32cap-OPENSSL_sparcv9cap-OPENSSL_ppccap-OPENSSL_armcap-OPENSSL_s390xcap"><b>OPENSSL_ia32cap</b>, <b>OPENSSL_sparcv9cap</b>, <b>OPENSSL_ppccap</b>, <b>OPENSSL_armcap</b>, <b>OPENSSL_s390xcap</b></dt>
<dd>

<p>OpenSSL supports a number of different algorithm implementations for various machines and, by default, it determines which to use based on the processor capabilities and run time feature enquiry. These environment variables can be used to exert more control over this selection process. See <a href="../man3/OPENSSL_ia32cap.html">OPENSSL_ia32cap(3)</a>, <a href="../man3/OPENSSL_s390xcap.html">OPENSSL_s390xcap(3)</a>.</p>

</dd>
<dt id="NO_PROXY-HTTPS_PROXY-HTTP_PROXY"><b>NO_PROXY</b>, <b>HTTPS_PROXY</b>, <b>HTTP_PROXY</b></dt>
<dd>

<p>Specify a proxy hostname. See <a href="../man3/OSSL_HTTP_parse_url.html">OSSL_HTTP_parse_url(3)</a>.</p>

</dd>
</dl>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2019-2021 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the Apache License 2.0 (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


