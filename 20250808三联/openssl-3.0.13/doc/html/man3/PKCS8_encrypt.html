<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>PKCS8_encrypt</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:root@localhost" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#CONFORMING-TO">CONFORMING TO</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#HISTORY">HISTORY</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>PKCS8_decrypt, PKCS8_decrypt_ex, PKCS8_encrypt, PKCS8_encrypt_ex, PKCS8_set0_pbe, PKCS8_set0_pbe_ex - PKCS8 encrypt/decrypt functions</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/x509.h&gt;

PKCS8_PRIV_KEY_INFO *PKCS8_decrypt(const X509_SIG *p8, const char *pass,
                                   int passlen);
PKCS8_PRIV_KEY_INFO *PKCS8_decrypt_ex(const X509_SIG *p8, const char *pass,
                                      int passlen, OSSL_LIB_CTX *ctx,
                                      const char *propq);
X509_SIG *PKCS8_encrypt(int pbe_nid, const EVP_CIPHER *cipher,
                        const char *pass, int passlen, unsigned char *salt,
                        int saltlen, int iter, PKCS8_PRIV_KEY_INFO *p8);
X509_SIG *PKCS8_encrypt_ex(int pbe_nid, const EVP_CIPHER *cipher,
                           const char *pass, int passlen, unsigned char *salt,
                           int saltlen, int iter, PKCS8_PRIV_KEY_INFO *p8,
                           OSSL_LIB_CTX *ctx, const char *propq);
X509_SIG *PKCS8_set0_pbe(const char *pass, int passlen,
                        PKCS8_PRIV_KEY_INFO *p8inf, X509_ALGOR *pbe);
X509_SIG *PKCS8_set0_pbe_ex(const char *pass, int passlen,
                            PKCS8_PRIV_KEY_INFO *p8inf, X509_ALGOR *pbe,
                            OSSL_LIB_CTX *ctx);</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>PKCS8_encrypt() and PKCS8_encrypt_ex() perform encryption of an object <i>p8</i> using the password <i>pass</i> of length <i>passlen</i>, salt <i>salt</i> of length <i>saltlen</i> and iteration count <i>iter</i>. The resulting <b>X509_SIG</b> contains the encoded algorithm parameters and encrypted key.</p>

<p>PKCS8_decrypt() and PKCS8_decrypt_ex() perform decryption of an <b>X509_SIG</b> in <i>p8</i> using the password <i>pass</i> of length <i>passlen</i> along with algorithm parameters obtained from the <i>p8</i>.</p>

<p>PKCS8_set0_pbe() and PKCS8_set0_pbe_ex() perform encryption of the <i>p8inf</i> using the password <i>pass</i> of length <i>passlen</i> and parameters <i>pbe</i>.</p>

<p>Functions ending in _ex() allow for a library context <i>ctx</i> and property query <i>propq</i> to be used to select algorithm implementations.</p>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>PKCS8_encrypt(), PKCS8_encrypt_ex(), PKCS8_set0_pbe() and PKCS8_set0_pbe_ex() return an encrypted key in a <b>X509_SIG</b> structure or NULL if an error occurs.</p>

<p>PKCS8_decrypt() and PKCS8_decrypt_ex() return a <b>PKCS8_PRIV_KEY_INFO</b> or NULL if an error occurs.</p>

<h1 id="CONFORMING-TO">CONFORMING TO</h1>

<p>IETF RFC 7292 (<a href="https://tools.ietf.org/html/rfc7292">https://tools.ietf.org/html/rfc7292</a>)</p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man7/crypto.html">crypto(7)</a></p>

<h1 id="HISTORY">HISTORY</h1>

<p>PKCS8_decrypt_ex(), PKCS8_encrypt_ex() and PKCS8_set0_pbe_ex() were added in OpenSSL 3.0.</p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2021 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the Apache License 2.0 (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


