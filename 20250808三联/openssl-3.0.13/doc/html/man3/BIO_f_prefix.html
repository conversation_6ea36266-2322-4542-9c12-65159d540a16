<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>BIO_f_prefix</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:root@localhost" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#NOTES">NOTES</a></li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>BIO_f_prefix, BIO_set_prefix, BIO_set_indent, BIO_get_indent - prefix BIO filter</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/bio.h&gt;

const BIO_METHOD *BIO_f_prefix(void);
long BIO_set_prefix(BIO *b, const char *prefix);
long BIO_set_indent(BIO *b, long indent);
long BIO_get_indent(BIO *b);</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>BIO_f_cipher() returns the prefix BIO method. This is a filter for text output, where each line gets automatically prefixed and indented according to user input.</p>

<p>The prefix and the indentation are combined. For each line of output going through this filter, the prefix is output first, then the amount of additional spaces indicated by the indentation, and then the line itself.</p>

<p>By default, there is no prefix, and indentation is set to 0.</p>

<p>BIO_set_prefix() sets the prefix to be used for future lines of text, using <i>prefix</i>. <i>prefix</i> may be NULL, signifying that there should be no prefix. If <i>prefix</i> isn&#39;t NULL, this function makes a copy of it.</p>

<p>BIO_set_indent() sets the indentation to be used for future lines of text, using <i>indent</i>. Negative values are not allowed.</p>

<p>BIO_get_indent() gets the current indentation.</p>

<h1 id="NOTES">NOTES</h1>

<p>BIO_set_prefix(), BIO_set_indent() and BIO_get_indent() are implemented as macros.</p>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>BIO_f_prefix() returns the prefix BIO method.</p>

<p>BIO_set_prefix() returns 1 if the prefix was correctly set, or &lt;=0 on failure.</p>

<p>BIO_set_indent() returns 1 if the prefix was correctly set, or &lt;=0 on failure.</p>

<p>BIO_get_indent() returns the current indentation, or a negative value for failure.</p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man7/bio.html">bio(7)</a></p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2019-2021 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the Apache License 2.0 (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


