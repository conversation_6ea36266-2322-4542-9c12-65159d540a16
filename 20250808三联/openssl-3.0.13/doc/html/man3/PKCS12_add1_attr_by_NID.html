<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>PKCS12_add1_attr_by_NID</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:root@localhost" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#NOTES">NOTES</a></li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>PKCS12_add1_attr_by_NID, PKCS12_add1_attr_by_txt - Add an attribute to a PKCS#12 safeBag structure</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/pkcs12.h&gt;

int PKCS12_add1_attr_by_NID(PKCS12_SAFEBAG *bag, int nid, int type,
                            const unsigned char *bytes, int len);
int PKCS12_add1_attr_by_txt(PKCS12_SAFEBAG *bag, const char *attrname, int type,
                            const unsigned char *bytes, int len);</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>These functions add a PKCS#12 Attribute to the Attribute Set of the <b>bag</b>.</p>

<p>PKCS12_add1_attr_by_NID() adds an attribute of type <b>nid</b> with a value of ASN1 type <b>type</b> constructed using <b>len</b> bytes from <b>bytes</b>.</p>

<p>PKCS12_add1_attr_by_txt() adds an attribute of type <b>attrname</b> with a value of ASN1 type <b>type</b> constructed using <b>len</b> bytes from <b>bytes</b>.</p>

<h1 id="NOTES">NOTES</h1>

<p>These functions do not check whether an existing attribute of the same type is present. There can be multiple attributes with the same type assigned to a safeBag.</p>

<p>Both functions were added in OpenSSL 3.0.</p>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>A return value of 1 indicates success, 0 indicates failure.</p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man3/PKCS12_create.html">PKCS12_create(3)</a></p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2020 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the Apache License 2.0 (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


