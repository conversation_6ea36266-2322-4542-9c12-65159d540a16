<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>EVP_DigestSignInit</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:root@localhost" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#NOTES">NOTES</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#HISTORY">HISTORY</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>EVP_DigestSignInit_ex, EVP_DigestSignInit, EVP_DigestSignUpdate, EVP_DigestSignFinal, EVP_DigestSign - EVP signing functions</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/evp.h&gt;

int EVP_DigestSignInit_ex(EVP_MD_CTX *ctx, EVP_PKEY_CTX **pctx,
                          const char *mdname, OSSL_LIB_CTX *libctx,
                          const char *props, EVP_PKEY *pkey,
                          const OSSL_PARAM params[]);
int EVP_DigestSignInit(EVP_MD_CTX *ctx, EVP_PKEY_CTX **pctx,
                       const EVP_MD *type, ENGINE *e, EVP_PKEY *pkey);
int EVP_DigestSignUpdate(EVP_MD_CTX *ctx, const void *d, size_t cnt);
int EVP_DigestSignFinal(EVP_MD_CTX *ctx, unsigned char *sig, size_t *siglen);

int EVP_DigestSign(EVP_MD_CTX *ctx, unsigned char *sigret,
                   size_t *siglen, const unsigned char *tbs,
                   size_t tbslen);</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>The EVP signature routines are a high-level interface to digital signatures. Input data is digested first before the signing takes place.</p>

<p>EVP_DigestSignInit_ex() sets up signing context <i>ctx</i> to use a digest with the name <i>mdname</i> and private key <i>pkey</i>. The name of the digest to be used is passed to the provider of the signature algorithm in use. How that provider interprets the digest name is provider specific. The provider may implement that digest directly itself or it may (optionally) choose to fetch it (which could result in a digest from a different provider being selected). If the provider supports fetching the digest then it may use the <i>props</i> argument for the properties to be used during the fetch. Finally, the passed parameters <i>params</i>, if not NULL, are set on the context before returning.</p>

<p>The <i>pkey</i> algorithm is used to fetch a <b>EVP_SIGNATURE</b> method implicitly, to be used for the actual signing. See <a href="../man7/provider.html">&quot;Implicit fetch&quot; in provider(7)</a> for more information about implicit fetches.</p>

<p>The OpenSSL default and legacy providers support fetching digests and can fetch those digests from any available provider. The OpenSSL FIPS provider also supports fetching digests but will only fetch digests that are themselves implemented inside the FIPS provider.</p>

<p><i>ctx</i> must be created with EVP_MD_CTX_new() before calling this function. If <i>pctx</i> is not NULL, the EVP_PKEY_CTX of the signing operation will be written to <i>*pctx</i>: this can be used to set alternative signing options. Note that any existing value in <i>*pctx</i> is overwritten. The EVP_PKEY_CTX value returned must not be freed directly by the application if <i>ctx</i> is not assigned an EVP_PKEY_CTX value before being passed to EVP_DigestSignInit_ex() (which means the EVP_PKEY_CTX is created inside EVP_DigestSignInit_ex() and it will be freed automatically when the EVP_MD_CTX is freed). If the EVP_PKEY_CTX to be used is created by EVP_DigestSignInit_ex then it will use the <b>OSSL_LIB_CTX</b> specified in <i>libctx</i> and the property query string specified in <i>props</i>.</p>

<p>The digest <i>mdname</i> may be NULL if the signing algorithm supports it. The <i>props</i> argument can always be NULL.</p>

<p>No <b>EVP_PKEY_CTX</b> will be created by EVP_DigestSignInit_ex() if the passed <i>ctx</i> has already been assigned one via <a href="../man3/EVP_MD_CTX_set_pkey_ctx.html">EVP_MD_CTX_set_pkey_ctx(3)</a>. See also <a href="../man7/SM2.html">SM2(7)</a>.</p>

<p>Only EVP_PKEY types that support signing can be used with these functions. This includes MAC algorithms where the MAC generation is considered as a form of &quot;signing&quot;. Built-in EVP_PKEY types supported by these functions are CMAC, Poly1305, DSA, ECDSA, HMAC, RSA, SipHash, Ed25519 and Ed448.</p>

<p>Not all digests can be used for all key types. The following combinations apply.</p>

<dl>

<dt id="DSA">DSA</dt>
<dd>

<p>Supports SHA1, SHA224, SHA256, SHA384 and SHA512</p>

</dd>
<dt id="ECDSA">ECDSA</dt>
<dd>

<p>Supports SHA1, SHA224, SHA256, SHA384, SHA512 and SM3</p>

</dd>
<dt id="RSA-with-no-padding">RSA with no padding</dt>
<dd>

<p>Supports no digests (the digest <i>type</i> must be NULL)</p>

</dd>
<dt id="RSA-with-X931-padding">RSA with X931 padding</dt>
<dd>

<p>Supports SHA1, SHA256, SHA384 and SHA512</p>

</dd>
<dt id="All-other-RSA-padding-types">All other RSA padding types</dt>
<dd>

<p>Support SHA1, SHA224, SHA256, SHA384, SHA512, MD5, MD5_SHA1, MD2, MD4, MDC2, SHA3-224, SHA3-256, SHA3-384, SHA3-512</p>

</dd>
<dt id="Ed25519-and-Ed448">Ed25519 and Ed448</dt>
<dd>

<p>Support no digests (the digest <i>type</i> must be NULL)</p>

</dd>
<dt id="HMAC">HMAC</dt>
<dd>

<p>Supports any digest</p>

</dd>
<dt id="CMAC-Poly1305-and-SipHash">CMAC, Poly1305 and SipHash</dt>
<dd>

<p>Will ignore any digest provided.</p>

</dd>
</dl>

<p>If RSA-PSS is used and restrictions apply then the digest must match.</p>

<p>EVP_DigestSignInit() works in the same way as EVP_DigestSignInit_ex() except that the <i>mdname</i> parameter will be inferred from the supplied digest <i>type</i>, and <i>props</i> will be NULL. Where supplied the ENGINE <i>e</i> will be used for the signing and digest algorithm implementations. <i>e</i> may be NULL.</p>

<p>EVP_DigestSignUpdate() hashes <i>cnt</i> bytes of data at <i>d</i> into the signature context <i>ctx</i>. This function can be called several times on the same <i>ctx</i> to include additional data.</p>

<p>Unless <i>sig</i> is NULL EVP_DigestSignFinal() signs the data in <i>ctx</i> and places the signature in <i>sig</i>. Otherwise the maximum necessary size of the output buffer is written to the <i>siglen</i> parameter. If <i>sig</i> is not NULL then before the call the <i>siglen</i> parameter should contain the length of the <i>sig</i> buffer. If the call is successful the signature is written to <i>sig</i> and the amount of data written to <i>siglen</i>.</p>

<p>EVP_DigestSign() signs <i>tbslen</i> bytes of data at <i>tbs</i> and places the signature in <i>sig</i> and its length in <i>siglen</i> in a similar way to EVP_DigestSignFinal(). In the event of a failure EVP_DigestSign() cannot be called again without reinitialising the EVP_MD_CTX. If <i>sig</i> is NULL before the call then <i>siglen</i> will be populated with the required size for the <i>sig</i> buffer. If <i>sig</i> is non-NULL before the call then <i>siglen</i> should contain the length of the <i>sig</i> buffer.</p>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>EVP_DigestSignInit(), EVP_DigestSignUpdate(), EVP_DigestSignFinal() and EVP_DigestSign() return 1 for success and 0 for failure.</p>

<p>The error codes can be obtained from <a href="../man3/ERR_get_error.html">ERR_get_error(3)</a>.</p>

<h1 id="NOTES">NOTES</h1>

<p>The <b>EVP</b> interface to digital signatures should almost always be used in preference to the low-level interfaces. This is because the code then becomes transparent to the algorithm used and much more flexible.</p>

<p>EVP_DigestSign() is a one shot operation which signs a single block of data in one function. For algorithms that support streaming it is equivalent to calling EVP_DigestSignUpdate() and EVP_DigestSignFinal(). For algorithms which do not support streaming (e.g. PureEdDSA) it is the only way to sign data.</p>

<p>In previous versions of OpenSSL there was a link between message digest types and public key algorithms. This meant that &quot;clone&quot; digests such as EVP_dss1() needed to be used to sign using SHA1 and DSA. This is no longer necessary and the use of clone digest is now discouraged.</p>

<p>For some key types and parameters the random number generator must be seeded. If the automatic seeding or reseeding of the OpenSSL CSPRNG fails due to external circumstances (see <a href="../man7/RAND.html">RAND(7)</a>), the operation will fail.</p>

<p>The call to EVP_DigestSignFinal() internally finalizes a copy of the digest context. This means that calls to EVP_DigestSignUpdate() and EVP_DigestSignFinal() can be called later to digest and sign additional data.</p>

<p>EVP_DigestSignInit() and EVP_DigestSignInit_ex() functions can be called multiple times on a context and the parameters set by previous calls should be preserved if the <i>pkey</i> parameter is NULL. The call then just resets the state of the <i>ctx</i>.</p>

<p>Ignoring failure returns of EVP_DigestSignInit() and EVP_DigestSignInit_ex() functions can lead to subsequent undefined behavior when calling EVP_DigestSignUpdate(), EVP_DigestSignFinal(), or EVP_DigestSign().</p>

<p>The use of EVP_PKEY_get_size() with these functions is discouraged because some signature operations may have a signature length which depends on the parameters set. As a result EVP_PKEY_get_size() would have to return a value which indicates the maximum possible signature for any set of parameters.</p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man3/EVP_DigestVerifyInit.html">EVP_DigestVerifyInit(3)</a>, <a href="../man3/EVP_DigestInit.html">EVP_DigestInit(3)</a>, <a href="../man7/evp.html">evp(7)</a>, <a href="../man3/HMAC.html">HMAC(3)</a>, <a href="../man3/MD2.html">MD2(3)</a>, <a href="../man3/MD5.html">MD5(3)</a>, <a href="../man3/MDC2.html">MDC2(3)</a>, <a href="../man3/RIPEMD160.html">RIPEMD160(3)</a>, <a href="../man3/SHA1.html">SHA1(3)</a>, <a href="../man1/openssl-dgst.html">openssl-dgst(1)</a>, <a href="../man7/RAND.html">RAND(7)</a></p>

<h1 id="HISTORY">HISTORY</h1>

<p>EVP_DigestSignInit(), EVP_DigestSignUpdate() and EVP_DigestSignFinal() were added in OpenSSL 1.0.0.</p>

<p>EVP_DigestSignInit_ex() was added in OpenSSL 3.0.</p>

<p>EVP_DigestSignUpdate() was converted from a macro to a function in OpenSSL 3.0.</p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2006-2023 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the Apache License 2.0 (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


