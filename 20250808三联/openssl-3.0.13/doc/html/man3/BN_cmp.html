<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>BN_cmp</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:root@localhost" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#HISTORY">HISTORY</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>BN_cmp, BN_ucmp, BN_is_zero, BN_is_one, BN_is_word, BN_abs_is_word, BN_is_odd - BIGNUM comparison and test functions</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/bn.h&gt;

int BN_cmp(const BIGNUM *a, const BIGNUM *b);
int BN_ucmp(const BIGNUM *a, const BIGNUM *b);

int BN_is_zero(const BIGNUM *a);
int BN_is_one(const BIGNUM *a);
int BN_is_word(const BIGNUM *a, const BN_ULONG w);
int BN_abs_is_word(const BIGNUM *a, const BN_ULONG w);
int BN_is_odd(const BIGNUM *a);</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>BN_cmp() compares the numbers <i>a</i> and <i>b</i>. BN_ucmp() compares their absolute values.</p>

<p>BN_is_zero(), BN_is_one(), BN_is_word() and BN_abs_is_word() test if <i>a</i> equals 0, 1, <i>w</i>, or |<i>w</i>| respectively. BN_is_odd() tests if <i>a</i> is odd.</p>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>BN_cmp() returns -1 if <i>a</i> &lt; <i>b</i>, 0 if <i>a</i> == <i>b</i> and 1 if <i>a</i> &gt; <i>b</i>. BN_ucmp() is the same using the absolute values of <i>a</i> and <i>b</i>.</p>

<p>BN_is_zero(), BN_is_one() BN_is_word(), BN_abs_is_word() and BN_is_odd() return 1 if the condition is true, 0 otherwise.</p>

<h1 id="HISTORY">HISTORY</h1>

<p>Prior to OpenSSL 1.1.0, BN_is_zero(), BN_is_one(), BN_is_word(), BN_abs_is_word() and BN_is_odd() were macros.</p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2000-2021 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the Apache License 2.0 (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


