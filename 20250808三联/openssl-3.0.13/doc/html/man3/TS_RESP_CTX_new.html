<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>TS_RESP_CTX_new</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:root@localhost" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#HISTORY">HISTORY</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>TS_RESP_CTX_new_ex, TS_RESP_CTX_new, TS_RESP_CTX_free - Timestamp response context object creation</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/ts.h&gt;

TS_RESP_CTX *TS_RESP_CTX_new_ex(OSSL_LIB_CTX *libctx, const char *propq);
TS_RESP_CTX *TS_RESP_CTX_new(void);
void TS_RESP_CTX_free(TS_RESP_CTX *ctx);</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>Creates a response context that can be used for generating responses.</p>

<p>TS_RESP_CTX_new_ex() allocates and initializes a TS_RESP_CTX structure with a library context of <i>libctx</i> and a property query of <i>propq</i>. The library context and property query can be used to select which providers supply the fetched algorithms.</p>

<p>TS_RESP_CTX_new() is similar to TS_RESP_CTX_new_ex() but sets the library context and property query to NULL. This results in the default (NULL) library context being used for any operations requiring algorithm fetches.</p>

<p>TS_RESP_CTX_free() frees the <b>TS_RESP_CTX</b> object <i>ctx</i>.</p>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>If the allocation fails, TS_RESP_CTX_new_ex() and TS_RESP_CTX_new() return NULL, otherwise it returns a pointer to the newly allocated structure.</p>

<h1 id="HISTORY">HISTORY</h1>

<p>The function TS_RESP_CTX_new_ex() was added in OpenSSL 3.0.</p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2021 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the Apache License 2.0 (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


