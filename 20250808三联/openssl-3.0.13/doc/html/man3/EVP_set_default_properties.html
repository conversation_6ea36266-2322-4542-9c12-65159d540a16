<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>EVP_set_default_properties</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:root@localhost" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#NOTES">NOTES</a></li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#HISTORY">HISTORY</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>EVP_set_default_properties, EVP_default_properties_enable_fips, EVP_default_properties_is_fips_enabled - Set default properties for future algorithm fetches</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/evp.h&gt;

int EVP_set_default_properties(OSSL_LIB_CTX *libctx, const char *propq);
int EVP_default_properties_enable_fips(OSSL_LIB_CTX *libctx, int enable);
int EVP_default_properties_is_fips_enabled(OSSL_LIB_CTX *libctx);</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>EVP_set_default_properties() sets the default properties for all future EVP algorithm fetches, implicit as well as explicit. See <a href="../man7/crypto.html">&quot;ALGORITHM FETCHING&quot; in crypto(7)</a> for information about implicit and explicit fetching.</p>

<p>EVP_set_default_properties stores the properties given with the string <i>propq</i> among the EVP data that&#39;s been stored in the library context given with <i>libctx</i> (NULL signifies the default library context).</p>

<p>Any previous default property for the specified library context will be dropped.</p>

<p>EVP_default_properties_enable_fips() sets the &#39;fips=yes&#39; to be a default property if <i>enable</i> is non zero, otherwise it clears &#39;fips&#39; from the default property query for the given <i>libctx</i>. It merges the fips default property query with any existing query strings that have been set via EVP_set_default_properties().</p>

<p>EVP_default_properties_is_fips_enabled() indicates if &#39;fips=yes&#39; is a default property for the given <i>libctx</i>.</p>

<h1 id="NOTES">NOTES</h1>

<p>EVP_set_default_properties() and EVP_default_properties_enable_fips() are not thread safe. They are intended to be called only during the initialisation phase of a <i>libctx</i>.</p>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>EVP_set_default_properties() and EVP_default_properties_enable_fips() return 1 on success, or 0 on failure. An error is placed on the error stack if a failure occurs.</p>

<p>EVP_default_properties_is_fips_enabled() returns 1 if the &#39;fips=yes&#39; default property is set for the given <i>libctx</i>, otherwise it returns 0.</p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man3/EVP_MD_fetch.html">EVP_MD_fetch(3)</a></p>

<h1 id="HISTORY">HISTORY</h1>

<p>The functions described here were added in OpenSSL 3.0.</p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2019-2022 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the Apache License 2.0 (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


