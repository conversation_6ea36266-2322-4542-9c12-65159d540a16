<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>EVP_blake2b512</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:root@localhost" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#NOTES">NOTES</a></li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#CONFORMING-TO">CONFORMING TO</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>EVP_blake2b512, EVP_blake2s256 - BLAKE2 For EVP</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/evp.h&gt;

const EVP_MD *EVP_blake2b512(void);
const EVP_MD *EVP_blake2s256(void);</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>BLAKE2 is an improved version of BLAKE, which was submitted to the NIST SHA-3 algorithm competition. The BLAKE2s and BLAKE2b algorithms are described in RFC 7693.</p>

<dl>

<dt id="EVP_blake2s256">EVP_blake2s256()</dt>
<dd>

<p>The BLAKE2s algorithm that produces a 256-bit output from a given input.</p>

</dd>
<dt id="EVP_blake2b512">EVP_blake2b512()</dt>
<dd>

<p>The BLAKE2b algorithm that produces a 512-bit output from a given input.</p>

</dd>
</dl>

<h1 id="NOTES">NOTES</h1>

<p>Developers should be aware of the negative performance implications of calling these functions multiple times and should consider using <a href="../man3/EVP_MD_fetch.html">EVP_MD_fetch(3)</a> with <a href="../man7/EVP_MD-BLAKE2.html">EVP_MD-BLAKE2(7)</a> instead. See <a href="../man7/crypto.html">&quot;Performance&quot; in crypto(7)</a> for further information.</p>

<p>While the BLAKE2b and BLAKE2s algorithms supports a variable length digest, this implementation outputs a digest of a fixed length (the maximum length supported), which is 512-bits for BLAKE2b and 256-bits for BLAKE2s.</p>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>These functions return a <b>EVP_MD</b> structure that contains the implementation of the message digest. See <a href="../man3/EVP_MD_meth_new.html">EVP_MD_meth_new(3)</a> for details of the <b>EVP_MD</b> structure.</p>

<h1 id="CONFORMING-TO">CONFORMING TO</h1>

<p>RFC 7693.</p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man7/evp.html">evp(7)</a>, <a href="../man3/EVP_DigestInit.html">EVP_DigestInit(3)</a></p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2017-2023 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the Apache License 2.0 (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


