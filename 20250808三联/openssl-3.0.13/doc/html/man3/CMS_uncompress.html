<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>CMS_uncompress</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:root@localhost" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#NOTES">NOTES</a></li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#BUGS">BUGS</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>CMS_uncompress - uncompress a CMS CompressedData structure</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/cms.h&gt;

int CMS_uncompress(CMS_ContentInfo *cms, BIO *dcont, BIO *out, unsigned int flags);</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>CMS_uncompress() extracts and uncompresses the content from a CMS CompressedData structure <b>cms</b>. <b>data</b> is a BIO to write the content to and <b>flags</b> is an optional set of flags.</p>

<p>The <b>dcont</b> parameter is used in the rare case where the compressed content is detached. It will normally be set to NULL.</p>

<h1 id="NOTES">NOTES</h1>

<p>The only currently supported compression algorithm is zlib: if the structure indicates the use of any other algorithm an error is returned.</p>

<p>If zlib support is not compiled into OpenSSL then CMS_uncompress() will always return an error.</p>

<p>The following flags can be passed in the <b>flags</b> parameter.</p>

<p>If the <b>CMS_TEXT</b> flag is set MIME headers for type <b>text/plain</b> are deleted from the content. If the content is not of type <b>text/plain</b> then an error is returned.</p>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>CMS_uncompress() returns either 1 for success or 0 for failure. The error can be obtained from ERR_get_error(3)</p>

<h1 id="BUGS">BUGS</h1>

<p>The lack of single pass processing and the need to hold all data in memory as mentioned in CMS_verify() also applies to CMS_decompress().</p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man3/ERR_get_error.html">ERR_get_error(3)</a>, <a href="../man3/CMS_compress.html">CMS_compress(3)</a></p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2008-2016 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the Apache License 2.0 (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


