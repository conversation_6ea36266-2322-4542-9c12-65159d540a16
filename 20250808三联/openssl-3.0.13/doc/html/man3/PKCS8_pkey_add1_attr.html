<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>PKCS8_pkey_add1_attr</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:root@localhost" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#NOTES">NOTES</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>PKCS8_pkey_get0_attrs, PKCS8_pkey_add1_attr, PKCS8_pkey_add1_attr_by_NID, PKCS8_pkey_add1_attr_by_OBJ - PKCS8 attribute functions</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/x509.h&gt;

const STACK_OF(X509_ATTRIBUTE) *
PKCS8_pkey_get0_attrs(const PKCS8_PRIV_KEY_INFO *p8);
int PKCS8_pkey_add1_attr(PKCS8_PRIV_KEY_INFO *p8, X509_ATTRIBUTE *attr);
int PKCS8_pkey_add1_attr_by_NID(PKCS8_PRIV_KEY_INFO *p8, int nid, int type,
                                const unsigned char *bytes, int len);
int PKCS8_pkey_add1_attr_by_OBJ(PKCS8_PRIV_KEY_INFO *p8, const ASN1_OBJECT *obj,
                               int type, const unsigned char *bytes, int len);</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>PKCS8_pkey_get0_attrs() returns a const STACK of X509_ATTRIBUTE present in the passed const PKCS8_PRIV_KEY_INFO structure <b>p8</b>.</p>

<p>PKCS8_pkey_add1_attr() adds a constructed X509_ATTRIBUTE <b>attr</b> to the existing PKCS8_PRIV_KEY_INFO structure <b>p8</b>.</p>

<p>PKCS8_pkey_add1_attr_by_NID() and PKCS8_pkey_add1_attr_by_OBJ() construct a new X509_ATTRIBUTE from the passed arguments and add it to the existing PKCS8_PRIV_KEY_INFO structure <b>p8</b>.</p>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>PKCS8_pkey_add1_attr(), PKCS8_pkey_add1_attr_by_NID(), and PKCS8_pkey_add1_attr_by_OBJ() return 1 for success and 0 for failure.</p>

<h1 id="NOTES">NOTES</h1>

<p>STACK of X509_ATTRIBUTE is present in many X509-related structures and some of them have the corresponding set of similar functions.</p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man7/crypto.html">crypto(7)</a></p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2020 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the Apache License 2.0 (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


