<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>SSL_SESSION_set1_id</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:root@localhost" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#HISTORY">HISTORY</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>SSL_SESSION_get_id, SSL_SESSION_set1_id - get and set the SSL session ID</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/ssl.h&gt;

const unsigned char *SSL_SESSION_get_id(const SSL_SESSION *s,
                                        unsigned int *len);
int SSL_SESSION_set1_id(SSL_SESSION *s, const unsigned char *sid,
                        unsigned int sid_len);</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>SSL_SESSION_get_id() returns a pointer to the internal session id value for the session <b>s</b>. The length of the id in bytes is stored in <b>*len</b>. The length may be 0. The caller should not free the returned pointer directly.</p>

<p>SSL_SESSION_set1_id() sets the session ID for the <b>ssl</b> SSL/TLS session to <b>sid</b> of length <b>sid_len</b>.</p>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>SSL_SESSION_get_id() returns a pointer to the session id value. SSL_SESSION_set1_id() returns 1 for success and 0 for failure, for example if the supplied session ID length exceeds <b>SSL_MAX_SSL_SESSION_ID_LENGTH</b>.</p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man7/ssl.html">ssl(7)</a></p>

<h1 id="HISTORY">HISTORY</h1>

<p>The SSL_SESSION_set1_id() function was added in OpenSSL 1.1.0.</p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2015-2020 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the Apache License 2.0 (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


