<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>EVP_rc5_32_12_16_cbc</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:root@localhost" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#NOTES">NOTES</a></li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>EVP_rc5_32_12_16_cbc, EVP_rc5_32_12_16_cfb, EVP_rc5_32_12_16_cfb64, EVP_rc5_32_12_16_ecb, EVP_rc5_32_12_16_ofb - EVP RC5 cipher</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/evp.h&gt;

const EVP_CIPHER *EVP_rc5_32_12_16_cbc(void);
const EVP_CIPHER *EVP_rc5_32_12_16_cfb(void);
const EVP_CIPHER *EVP_rc5_32_12_16_cfb64(void);
const EVP_CIPHER *EVP_rc5_32_12_16_ecb(void);
const EVP_CIPHER *EVP_rc5_32_12_16_ofb(void);</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>The RC5 encryption algorithm for EVP.</p>

<dl>

<dt id="EVP_rc5_32_12_16_cbc-EVP_rc5_32_12_16_cfb-EVP_rc5_32_12_16_cfb64-EVP_rc5_32_12_16_ecb-EVP_rc5_32_12_16_ofb">EVP_rc5_32_12_16_cbc(), EVP_rc5_32_12_16_cfb(), EVP_rc5_32_12_16_cfb64(), EVP_rc5_32_12_16_ecb(), EVP_rc5_32_12_16_ofb()</dt>
<dd>

<p>RC5 encryption algorithm in CBC, CFB, ECB and OFB modes respectively. This is a variable key length cipher with an additional &quot;number of rounds&quot; parameter. By default the key length is set to 128 bits and 12 rounds. Alternative key lengths can be set using <a href="../man3/EVP_CIPHER_CTX_set_key_length.html">EVP_CIPHER_CTX_set_key_length(3)</a>. The maximum key length is 2040 bits.</p>

<p>The following rc5 specific <i>ctrl</i>s are supported (see <a href="../man3/EVP_CIPHER_CTX_ctrl.html">EVP_CIPHER_CTX_ctrl(3)</a>).</p>

<dl>

<dt id="EVP_CIPHER_CTX_ctrl-ctx-EVP_CTRL_SET_RC5_ROUNDS-rounds-NULL">EVP_CIPHER_CTX_ctrl(ctx, EVP_CTRL_SET_RC5_ROUNDS, rounds, NULL)</dt>
<dd>

<p>Sets the number of rounds to <b>rounds</b>. This must be one of RC5_8_ROUNDS, RC5_12_ROUNDS or RC5_16_ROUNDS.</p>

</dd>
<dt id="EVP_CIPHER_CTX_ctrl-ctx-EVP_CTRL_GET_RC5_ROUNDS-0-rounds">EVP_CIPHER_CTX_ctrl(ctx, EVP_CTRL_GET_RC5_ROUNDS, 0, &amp;rounds)</dt>
<dd>

<p>Stores the number of rounds currently configured in <b>*rounds</b> where <b>*rounds</b> is an int.</p>

</dd>
</dl>

</dd>
</dl>

<h1 id="NOTES">NOTES</h1>

<p>Developers should be aware of the negative performance implications of calling these functions multiple times and should consider using <a href="../man3/EVP_CIPHER_fetch.html">EVP_CIPHER_fetch(3)</a> with <a href="../man7/EVP_CIPHER-RC5.html">EVP_CIPHER-RC5(7)</a> instead. See <a href="../man7/crypto.html">&quot;Performance&quot; in crypto(7)</a> for further information.</p>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>These functions return an <b>EVP_CIPHER</b> structure that contains the implementation of the symmetric cipher. See <a href="../man3/EVP_CIPHER_meth_new.html">EVP_CIPHER_meth_new(3)</a> for details of the <b>EVP_CIPHER</b> structure.</p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man7/evp.html">evp(7)</a>, <a href="../man3/EVP_EncryptInit.html">EVP_EncryptInit(3)</a>, <a href="../man3/EVP_CIPHER_meth_new.html">EVP_CIPHER_meth_new(3)</a></p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2017-2023 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the Apache License 2.0 (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


