<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>X509V3_set_ctx</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:root@localhost" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#HISTORY">HISTORY</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>X509V3_set_ctx, X509V3_set_issuer_pkey - X.509 v3 extension generation utilities</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/x509v3.h&gt;

void X509V3_set_ctx(X509V3_CTX *ctx, X509 *issuer, X509 *subject,
                    X509_REQ *req, X509_CRL *crl, int flags);
int X509V3_set_issuer_pkey(X509V3_CTX *ctx, EVP_PKEY *pkey);</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>X509V3_set_ctx() fills in the basic fields of <i>ctx</i> of type <b>X509V3_CTX</b>, providing details potentially needed by functions producing X509 v3 extensions, e.g., to look up values for filling in authority key identifiers. Any of <i>subject</i>, <i>req</i>, or <i>crl</i> may be provided, pointing to a certificate, certification request, or certificate revocation list, respectively. When constructing the subject key identifier of a certificate by computing a hash value of its public key, the public key is taken from <i>subject</i> or <i>req</i>. Similarly, when constructing subject alternative names from any email addresses contained in a subject DN, the subject DN is taken from <i>subject</i> or <i>req</i>. If <i>subject</i> or <i>crl</i> is provided, <i>issuer</i> should point to its issuer, for instance to help generating an authority key identifier extension. Note that if <i>subject</i> is provided, <i>issuer</i> may be the same as <i>subject</i>, which means that <i>subject</i> is self-issued (or even self-signed). <i>flags</i> may be 0 or contain <b>X509V3_CTX_TEST</b>, which means that just the syntax of extension definitions is to be checked without actually producing an extension, or <b>X509V3_CTX_REPLACE</b>, which means that each X.509v3 extension added as defined in some configuration section shall replace any already existing extension with the same OID.</p>

<p>X509V3_set_issuer_pkey() explicitly sets the issuer private key of the certificate that has been provided in <i>ctx</i>. This should be done for self-issued certificates (which may be self-signed or not) to provide fallback data for the authority key identifier extension.</p>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>X509V3_set_ctx() and X509V3_set_issuer_pkey() return 1 on success and 0 on error.</p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man3/X509_add_ext.html">X509_add_ext(3)</a></p>

<h1 id="HISTORY">HISTORY</h1>

<p>X509V3_set_issuer_pkey() was added in OpenSSL 3.0.</p>

<p>CTX_TEST was deprecated in OpenSSL 3.0; use X509V3_CTX_TEST instead.</p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2015-2021 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the Apache License 2.0 (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


