<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>BN_mod_mul_montgomery</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:root@localhost" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#WARNINGS">WARNINGS</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#HISTORY">HISTORY</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>BN_mod_mul_montgomery, BN_MONT_CTX_new, BN_MONT_CTX_free, BN_MONT_CTX_set, BN_MONT_CTX_copy, BN_from_montgomery, BN_to_montgomery - Montgomery multiplication</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/bn.h&gt;

BN_MONT_CTX *BN_MONT_CTX_new(void);
void BN_MONT_CTX_free(BN_MONT_CTX *mont);

int BN_MONT_CTX_set(BN_MONT_CTX *mont, const BIGNUM *m, BN_CTX *ctx);
BN_MONT_CTX *BN_MONT_CTX_copy(BN_MONT_CTX *to, BN_MONT_CTX *from);

int BN_mod_mul_montgomery(BIGNUM *r, BIGNUM *a, BIGNUM *b,
                          BN_MONT_CTX *mont, BN_CTX *ctx);

int BN_from_montgomery(BIGNUM *r, BIGNUM *a, BN_MONT_CTX *mont,
                       BN_CTX *ctx);

int BN_to_montgomery(BIGNUM *r, BIGNUM *a, BN_MONT_CTX *mont,
                     BN_CTX *ctx);</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>These functions implement Montgomery multiplication. They are used automatically when <a href="../man3/BN_mod_exp.html">BN_mod_exp(3)</a> is called with suitable input, but they may be useful when several operations are to be performed using the same modulus.</p>

<p>BN_MONT_CTX_new() allocates and initializes a <b>BN_MONT_CTX</b> structure.</p>

<p>BN_MONT_CTX_set() sets up the <i>mont</i> structure from the modulus <i>m</i> by precomputing its inverse and a value R.</p>

<p>BN_MONT_CTX_copy() copies the <b>BN_MONT_CTX</b> <i>from</i> to <i>to</i>.</p>

<p>BN_MONT_CTX_free() frees the components of the <b>BN_MONT_CTX</b>, and, if it was created by BN_MONT_CTX_new(), also the structure itself. If <b>mont</b> is NULL, nothing is done.</p>

<p>BN_mod_mul_montgomery() computes Mont(<i>a</i>,<i>b</i>):=<i>a</i>*<i>b</i>*R^-1 and places the result in <i>r</i>.</p>

<p>BN_from_montgomery() performs the Montgomery reduction <i>r</i> = <i>a</i>*R^-1.</p>

<p>BN_to_montgomery() computes Mont(<i>a</i>,R^2), i.e. <i>a</i>*R. Note that <i>a</i> must be nonnegative and smaller than the modulus.</p>

<p>For all functions, <i>ctx</i> is a previously allocated <b>BN_CTX</b> used for temporary variables.</p>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>BN_MONT_CTX_new() returns the newly allocated <b>BN_MONT_CTX</b>, and NULL on error.</p>

<p>BN_MONT_CTX_free() has no return value.</p>

<p>For the other functions, 1 is returned for success, 0 on error. The error codes can be obtained by <a href="../man3/ERR_get_error.html">ERR_get_error(3)</a>.</p>

<h1 id="WARNINGS">WARNINGS</h1>

<p>The inputs must be reduced modulo <b>m</b>, otherwise the result will be outside the expected range.</p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man3/ERR_get_error.html">ERR_get_error(3)</a>, <a href="../man3/BN_add.html">BN_add(3)</a>, <a href="../man3/BN_CTX_new.html">BN_CTX_new(3)</a></p>

<h1 id="HISTORY">HISTORY</h1>

<p>BN_MONT_CTX_init() was removed in OpenSSL 1.1.0</p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2000-2020 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the Apache License 2.0 (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


