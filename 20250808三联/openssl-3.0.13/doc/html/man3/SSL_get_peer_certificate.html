<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>SSL_get_peer_certificate</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:root@localhost" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#NOTES">NOTES</a></li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#HISTORY">HISTORY</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>SSL_get_peer_certificate, SSL_get0_peer_certificate, SSL_get1_peer_certificate - get the X509 certificate of the peer</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/ssl.h&gt;

X509 *SSL_get0_peer_certificate(const SSL *ssl);
X509 *SSL_get1_peer_certificate(const SSL *ssl);</code></pre>

<p>The following function has been deprecated since OpenSSL 3.0, and can be hidden entirely by defining <b>OPENSSL_API_COMPAT</b> with a suitable version value, see <a href="../man7/openssl_user_macros.html">openssl_user_macros(7)</a>:</p>

<pre><code>X509 *SSL_get_peer_certificate(const SSL *ssl);</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>These functions return a pointer to the X509 certificate the peer presented. If the peer did not present a certificate, NULL is returned.</p>

<h1 id="NOTES">NOTES</h1>

<p>Due to the protocol definition, a TLS/SSL server will always send a certificate, if present. A client will only send a certificate when explicitly requested to do so by the server (see <a href="../man3/SSL_CTX_set_verify.html">SSL_CTX_set_verify(3)</a>). If an anonymous cipher is used, no certificates are sent.</p>

<p>That a certificate is returned does not indicate information about the verification state, use <a href="../man3/SSL_get_verify_result.html">SSL_get_verify_result(3)</a> to check the verification state.</p>

<p>The reference count of the X509 object returned by SSL_get1_peer_certificate() is incremented by one, so that it will not be destroyed when the session containing the peer certificate is freed. The X509 object must be explicitly freed using X509_free().</p>

<p>The reference count of the X509 object returned by SSL_get0_peer_certificate() is not incremented, and must not be freed.</p>

<p>SSL_get_peer_certificate() is an alias of SSL_get1_peer_certificate().</p>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>The following return values can occur:</p>

<dl>

<dt id="NULL">NULL</dt>
<dd>

<p>No certificate was presented by the peer or no connection was established.</p>

</dd>
<dt id="Pointer-to-an-X509-certificate">Pointer to an X509 certificate</dt>
<dd>

<p>The return value points to the certificate presented by the peer.</p>

</dd>
</dl>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man7/ssl.html">ssl(7)</a>, <a href="../man3/SSL_get_verify_result.html">SSL_get_verify_result(3)</a>, <a href="../man3/SSL_CTX_set_verify.html">SSL_CTX_set_verify(3)</a></p>

<h1 id="HISTORY">HISTORY</h1>

<p>SSL_get0_peer_certificate() and SSL_get1_peer_certificate() were added in 3.0.0. SSL_get_peer_certificate() was deprecated in 3.0.0.</p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2000-2024 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the Apache License 2.0 (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


