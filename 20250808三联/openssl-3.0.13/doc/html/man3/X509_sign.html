<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>X509_sign</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:root@localhost" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#NOTES">NOTES</a></li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#HISTORY">HISTORY</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>X509_sign, X509_sign_ctx, X509_REQ_sign, X509_REQ_sign_ctx, X509_CRL_sign, X509_CRL_sign_ctx - sign certificate, certificate request, or CRL signature</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/x509.h&gt;

int X509_sign(X509 *x, EVP_PKEY *pkey, const EVP_MD *md);
int X509_sign_ctx(X509 *x, EVP_MD_CTX *ctx);

int X509_REQ_sign(X509_REQ *x, EVP_PKEY *pkey, const EVP_MD *md);
int X509_REQ_sign_ctx(X509_REQ *x, EVP_MD_CTX *ctx);

int X509_CRL_sign(X509_CRL *x, EVP_PKEY *pkey, const EVP_MD *md);
int X509_CRL_sign_ctx(X509_CRL *x, EVP_MD_CTX *ctx);</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>X509_sign() signs certificate <i>x</i> using private key <i>pkey</i> and message digest <i>md</i> and sets the signature in <i>x</i>. X509_sign_ctx() also signs certificate <i>x</i> but uses the parameters contained in digest context <i>ctx</i>.</p>

<p>X509_REQ_sign(), X509_REQ_sign_ctx(), X509_CRL_sign(), and X509_CRL_sign_ctx() sign certificate requests and CRLs, respectively.</p>

<h1 id="NOTES">NOTES</h1>

<p>X509_sign_ctx() is used where the default parameters for the corresponding public key and digest are not suitable. It can be used to sign keys using RSA-PSS for example.</p>

<p>For efficiency reasons and to work around ASN.1 encoding issues the encoding of the signed portion of a certificate, certificate request and CRL is cached internally. If the signed portion of the structure is modified the encoding is not always updated meaning a stale version is sometimes used. This is not normally a problem because modifying the signed portion will invalidate the signature and signing will always update the encoding.</p>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>All functions return the size of the signature in bytes for success and zero for failure.</p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man3/ERR_get_error.html">ERR_get_error(3)</a>, <a href="../man3/X509_NAME_add_entry_by_txt.html">X509_NAME_add_entry_by_txt(3)</a>, <a href="../man3/X509_new.html">X509_new(3)</a>, <a href="../man3/X509_verify_cert.html">X509_verify_cert(3)</a>, <a href="../man3/X509_verify.html">X509_verify(3)</a>, <a href="../man3/X509_REQ_verify_ex.html">X509_REQ_verify_ex(3)</a>, <a href="../man3/X509_REQ_verify.html">X509_REQ_verify(3)</a>, <a href="../man3/X509_CRL_verify.html">X509_CRL_verify(3)</a></p>

<h1 id="HISTORY">HISTORY</h1>

<p>The X509_sign(), X509_REQ_sign() and X509_CRL_sign() functions are available in all versions of OpenSSL.</p>

<p>The X509_sign_ctx(), X509_REQ_sign_ctx() and X509_CRL_sign_ctx() functions were added in OpenSSL 1.0.1.</p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2015-2020 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the Apache License 2.0 (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


