<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>BIO_new</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:root@localhost" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#NOTES">NOTES</a></li>
  <li><a href="#HISTORY">HISTORY</a></li>
  <li><a href="#EXAMPLES">EXAMPLES</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>BIO_new_ex, BIO_new, BIO_up_ref, BIO_free, BIO_vfree, BIO_free_all - BIO allocation and freeing functions</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/bio.h&gt;

BIO *BIO_new_ex(OSSL_LIB_CTX *libctx, const BIO_METHOD *type);
BIO *BIO_new(const BIO_METHOD *type);
int BIO_up_ref(BIO *a);
int BIO_free(BIO *a);
void BIO_vfree(BIO *a);
void BIO_free_all(BIO *a);</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>The BIO_new_ex() function returns a new BIO using method <b>type</b> associated with the library context <i>libctx</i> (see OSSL_LIB_CTX(3)). The library context may be NULL to indicate the default library context.</p>

<p>The BIO_new() is the same as BIO_new_ex() except the default library context is always used.</p>

<p>BIO_up_ref() increments the reference count associated with the BIO object.</p>

<p>BIO_free() frees up a single BIO, BIO_vfree() also frees up a single BIO but it does not return a value. If <b>a</b> is NULL nothing is done. Calling BIO_free() may also have some effect on the underlying I/O structure, for example it may close the file being referred to under certain circumstances. For more details see the individual BIO_METHOD descriptions.</p>

<p>BIO_free_all() frees up an entire BIO chain, it does not halt if an error occurs freeing up an individual BIO in the chain. If <b>a</b> is NULL nothing is done.</p>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>BIO_new_ex() and BIO_new() return a newly created BIO or NULL if the call fails.</p>

<p>BIO_up_ref() and BIO_free() return 1 for success and 0 for failure.</p>

<p>BIO_free_all() and BIO_vfree() do not return values.</p>

<h1 id="NOTES">NOTES</h1>

<p>If BIO_free() is called on a BIO chain it will only free one BIO resulting in a memory leak.</p>

<p>Calling BIO_free_all() on a single BIO has the same effect as calling BIO_free() on it other than the discarded return value.</p>

<h1 id="HISTORY">HISTORY</h1>

<p>BIO_set() was removed in OpenSSL 1.1.0 as BIO type is now opaque.</p>

<p>BIO_new_ex() was added in OpenSSL 3.0.</p>

<h1 id="EXAMPLES">EXAMPLES</h1>

<p>Create a memory BIO:</p>

<pre><code>BIO *mem = BIO_new(BIO_s_mem());</code></pre>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2000-2021 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the Apache License 2.0 (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


