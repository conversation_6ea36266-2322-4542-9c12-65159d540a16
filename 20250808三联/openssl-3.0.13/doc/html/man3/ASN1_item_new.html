<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>ASN1_item_new</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:root@localhost" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#HISTORY">HISTORY</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>ASN1_item_new_ex, ASN1_item_new - create new ASN.1 values</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/asn1.h&gt;

ASN1_VALUE *ASN1_item_new_ex(const ASN1_ITEM *it, OSSL_LIB_CTX *libctx,
                             const char *propq);
ASN1_VALUE *ASN1_item_new(const ASN1_ITEM *it);</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>ASN1_item_new_ex() creates a new <b>ASN1_VALUE</b> structure based on the <b>ASN1_ITEM</b> template given in the <i>it</i> parameter. If any algorithm fetches are required during the process then they will use the <b>OSSL_LIB_CTX</b> provided in the <i>libctx</i> parameter and the property query string in <i>propq</i>. See <a href="../man7/crypto.html">&quot;ALGORITHM FETCHING&quot; in crypto(7)</a> for more information about algorithm fetching.</p>

<p>ASN1_item_new() is the same as ASN1_item_new_ex() except that the default <b>OSSL_LIB_CTX</b> is used (i.e. NULL) and with a NULL property query string.</p>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>ASN1_item_new_ex() and ASN1_item_new() return a pointer to the newly created <b>ASN1_VALUE</b> or NULL on error.</p>

<h1 id="HISTORY">HISTORY</h1>

<p>The function ASN1_item_new_ex() was added in OpenSSL 3.0.</p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2021 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the Apache License 2.0 (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


