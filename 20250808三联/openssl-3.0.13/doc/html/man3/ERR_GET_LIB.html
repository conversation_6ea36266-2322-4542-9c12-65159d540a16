<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>ERR_GET_LIB</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:root@localhost" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#NOTES">NOTES</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#HISTORY">HISTORY</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>ERR_GET_LIB, ERR_GET_REASON, ERR_FATAL_ERROR - get information from error codes</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/err.h&gt;

int ERR_GET_LIB(unsigned long e);

int ERR_GET_REASON(unsigned long e);

int ERR_FATAL_ERROR(unsigned long e);</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>The error code returned by ERR_get_error() consists of a library number and reason code. ERR_GET_LIB() and ERR_GET_REASON() can be used to extract these.</p>

<p>ERR_FATAL_ERROR() indicates whether a given error code is a fatal error.</p>

<p>The library number describes where the error occurred, the reason code is the information about what went wrong.</p>

<p>Each sub-library of OpenSSL has a unique library number; the reason code is unique within each sub-library. Note that different libraries may use the same value to signal different reasons.</p>

<p><b>ERR_R_...</b> reason codes such as <b>ERR_R_MALLOC_FAILURE</b> are globally unique. However, when checking for sub-library specific reason codes, be sure to also compare the library number.</p>

<p>ERR_GET_LIB(), ERR_GET_REASON(), and ERR_FATAL_ERROR() are macros.</p>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>The library number, reason code, and whether the error is fatal, respectively. Starting with OpenSSL 3.0.0, the function code is always set to zero.</p>

<h1 id="NOTES">NOTES</h1>

<p>Applications should not make control flow decisions based on specific error codes. Error codes are subject to change at any time (even in patch releases of OpenSSL). A particular error code can only be considered meaningful for control flow decisions if it is explicitly documented as such. New failure codes may still appear at any time.</p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man3/ERR_get_error.html">ERR_get_error(3)</a></p>

<h1 id="HISTORY">HISTORY</h1>

<p>ERR_GET_LIB() and ERR_GET_REASON() are available in all versions of OpenSSL.</p>

<p>ERR_GET_FUNC() was removed in OpenSSL 3.0.</p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2000-2023 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the Apache License 2.0 (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


