<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>X509_REQ_get_attr</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:root@localhost" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#NOTES">NOTES</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>X509_REQ_get_attr_count, X509_REQ_get_attr_by_NID, X509_REQ_get_attr_by_OBJ, X509_REQ_get_attr, X509_REQ_delete_attr, X509_REQ_add1_attr, X509_REQ_add1_attr_by_OBJ, X509_REQ_add1_attr_by_NID, X509_REQ_add1_attr_by_txt - <b>X509_ATTRIBUTE</b> support for signed certificate requests</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/x509.h&gt;

int X509_REQ_get_attr_count(const X509_REQ *req);
int X509_REQ_get_attr_by_NID(const X509_REQ *req, int nid, int lastpos);
int X509_REQ_get_attr_by_OBJ(const X509_REQ *req, const ASN1_OBJECT *obj,
                             int lastpos);
X509_ATTRIBUTE *X509_REQ_get_attr(const X509_REQ *req, int loc);
X509_ATTRIBUTE *X509_REQ_delete_attr(X509_REQ *req, int loc);
int X509_REQ_add1_attr(X509_REQ *req, X509_ATTRIBUTE *attr);
int X509_REQ_add1_attr_by_OBJ(X509_REQ *req,
                              const ASN1_OBJECT *obj, int type,
                              const unsigned char *bytes, int len);
int X509_REQ_add1_attr_by_NID(X509_REQ *req,
                              int nid, int type,
                              const unsigned char *bytes, int len);
int X509_REQ_add1_attr_by_txt(X509_REQ *req,
                              const char *attrname, int type,
                              const unsigned char *bytes, int len);</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>X509_REQ_get_attr_by_OBJ() finds the location of the first matching object <i>obj</i> in the <i>req</i> attribute list. The search starts at the position after <i>lastpos</i>. If the returned value is positive then it can be used on the next call to X509_REQ_get_attr_by_OBJ() as the value of <i>lastpos</i> in order to iterate through the remaining attributes. <i>lastpos</i> can be set to any negative value on the first call, in order to start searching from the start of the attribute list.</p>

<p>X509_REQ_get_attr_by_NID() is similar to X509_REQ_get_attr_by_OBJ() except that it passes the numerical identifier (NID) <i>nid</i> associated with the object. See &lt;openssl/obj_mac.h&gt; for a list of NID_*.</p>

<p>X509_REQ_get_attr() returns the <b>X509_ATTRIBUTE</b> object at index <i>loc</i> in the <i>req</i> attribute list. <i>loc</i> should be in the range from 0 to X509_REQ_get_attr_count() - 1.</p>

<p>X509_REQ_delete_attr() removes the <b>X509_ATTRIBUTE</b> object at index <i>loc</i> in the <i>req</i> objects list of attributes. An error occurs if <i>req</i> is NULL.</p>

<p>X509_REQ_add1_attr() pushes a copy of the passed in <b>X509_ATTRIBUTE</b> <i></i>attr&gt; to the <i>req</i> object&#39;s attribute list. An error will occur if either the attribute list is NULL or the attribute already exists.</p>

<p>X509_REQ_add1_attr_by_OBJ() creates a new <b>X509_ATTRIBUTE</b> using X509_ATTRIBUTE_set1_object() and X509_ATTRIBUTE_set1_data() to assign a new <i>obj</i> with type <i>type</i> and data <i>bytes</i> of length <i>len</i> and then pushes it to the <i>req</i> object&#39;s attribute list. <i>req</i> must be non NULL or an error will occur. If <i>obj</i> already exists in the attribute list then an error occurs.</p>

<p>X509_REQ_add1_attr_by_NID() is similar to X509_REQ_add1_attr_by_OBJ() except that it passes the numerical identifier (NID) <i>nid</i> associated with the object. See &lt;openssl/obj_mac.h&gt; for a list of NID_*.</p>

<p>X509_REQ_add1_attr_by_txt() is similar to X509_REQ_add1_attr_by_OBJ() except that it passes a name <i>attrname</i> associated with the object. See &lt;openssl/obj_mac.h&gt; for a list of SN_* names.</p>

<p>Refer to <a href="../man3/X509_ATTRIBUTE.html">X509_ATTRIBUTE(3)</a> for information related to attributes.</p>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>X509_REQ_get_attr_count() returns the number of attributes in the <i>req</i> object attribute list or -1 if the attribute list is NULL.</p>

<p>X509_REQ_get_attr_by_OBJ() returns -1 if either the <i>req</i> object&#39;s attribute list is empty OR <i>obj</i> is not found, otherwise it returns the location of the <i>obj</i> in the attribute list.</p>

<p>X509_REQ_get_attr_by_NID() is similar to X509_REQ_get_attr_by_OBJ(), except that it returns -2 if the <i>nid</i> is not known by OpenSSL.</p>

<p>X509_REQ_get_attr() returns either an <b>X509_ATTRIBUTE</b> or NULL on error.</p>

<p>X509_REQ_delete_attr() returns either the removed <b>X509_ATTRIBUTE</b> or NULL if there is a error.</p>

<p>X509_REQ_add1_attr(), X509_REQ_add1_attr_by_OBJ(), X509_REQ_add1_attr_by_NID() and X509_REQ_add1_attr_by_txt() return 1 on success or 0 on error.</p>

<h1 id="NOTES">NOTES</h1>

<p>Any functions that modify the attributes (add or delete) internally set a flag to indicate the ASN.1 encoding has been modified.</p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man3/X509_ATTRIBUTE.html">X509_ATTRIBUTE(3)</a></p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2023-2024 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the Apache License 2.0 (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


