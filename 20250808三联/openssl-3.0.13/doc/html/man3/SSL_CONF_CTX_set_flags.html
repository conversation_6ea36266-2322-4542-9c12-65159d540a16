<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>SSL_CONF_CTX_set_flags</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:root@localhost" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#NOTES">NOTES</a></li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#HISTORY">HISTORY</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>SSL_CONF_CTX_set_flags, SSL_CONF_CTX_clear_flags - Set or clear SSL configuration context flags</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/ssl.h&gt;

unsigned int SSL_CONF_CTX_set_flags(SSL_CONF_CTX *cctx, unsigned int flags);
unsigned int SSL_CONF_CTX_clear_flags(SSL_CONF_CTX *cctx, unsigned int flags);</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>The function SSL_CONF_CTX_set_flags() sets <b>flags</b> in the context <b>cctx</b>.</p>

<p>The function SSL_CONF_CTX_clear_flags() clears <b>flags</b> in the context <b>cctx</b>.</p>

<h1 id="NOTES">NOTES</h1>

<p>The flags set affect how subsequent calls to SSL_CONF_cmd() or SSL_CONF_argv() behave.</p>

<p>Currently the following <b>flags</b> values are recognised:</p>

<dl>

<dt id="SSL_CONF_FLAG_CMDLINE-SSL_CONF_FLAG_FILE">SSL_CONF_FLAG_CMDLINE, SSL_CONF_FLAG_FILE</dt>
<dd>

<p>recognise options intended for command line or configuration file use. At least one of these flags must be set.</p>

</dd>
<dt id="SSL_CONF_FLAG_CLIENT-SSL_CONF_FLAG_SERVER">SSL_CONF_FLAG_CLIENT, SSL_CONF_FLAG_SERVER</dt>
<dd>

<p>recognise options intended for use in SSL/TLS clients or servers. One or both of these flags must be set.</p>

</dd>
<dt id="SSL_CONF_FLAG_CERTIFICATE">SSL_CONF_FLAG_CERTIFICATE</dt>
<dd>

<p>recognise certificate and private key options.</p>

</dd>
<dt id="SSL_CONF_FLAG_REQUIRE_PRIVATE">SSL_CONF_FLAG_REQUIRE_PRIVATE</dt>
<dd>

<p>If this option is set then if a private key is not specified for a certificate it will attempt to load a private key from the certificate file when SSL_CONF_CTX_finish() is called. If a key cannot be loaded from the certificate file an error occurs.</p>

</dd>
<dt id="SSL_CONF_FLAG_SHOW_ERRORS">SSL_CONF_FLAG_SHOW_ERRORS</dt>
<dd>

<p>indicate errors relating to unrecognised options or missing arguments in the error queue. If this option isn&#39;t set such errors are only reflected in the return values of SSL_CONF_set_cmd() or SSL_CONF_set_argv()</p>

</dd>
</dl>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>SSL_CONF_CTX_set_flags() and SSL_CONF_CTX_clear_flags() returns the new flags value after setting or clearing flags.</p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man7/ssl.html">ssl(7)</a>, <a href="../man3/SSL_CONF_CTX_new.html">SSL_CONF_CTX_new(3)</a>, <a href="../man3/SSL_CONF_CTX_set_ssl_ctx.html">SSL_CONF_CTX_set_ssl_ctx(3)</a>, <a href="../man3/SSL_CONF_CTX_set1_prefix.html">SSL_CONF_CTX_set1_prefix(3)</a>, <a href="../man3/SSL_CONF_cmd.html">SSL_CONF_cmd(3)</a>, <a href="../man3/SSL_CONF_cmd_argv.html">SSL_CONF_cmd_argv(3)</a></p>

<h1 id="HISTORY">HISTORY</h1>

<p>These functions were added in OpenSSL 1.0.2.</p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2012-2016 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the Apache License 2.0 (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


