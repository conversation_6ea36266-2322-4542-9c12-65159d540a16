<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>BUF_MEM_new</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:root@localhost" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#HISTORY">HISTORY</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>BUF_MEM_new, BUF_MEM_new_ex, BUF_MEM_free, BUF_MEM_grow, BUF_MEM_grow_clean, BUF_reverse - simple character array structure</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/buffer.h&gt;

BUF_MEM *BUF_MEM_new(void);

BUF_MEM *BUF_MEM_new_ex(unsigned long flags);

void BUF_MEM_free(BUF_MEM *a);

int BUF_MEM_grow(BUF_MEM *str, int len);
size_t BUF_MEM_grow_clean(BUF_MEM *str, size_t len);

void BUF_reverse(unsigned char *out, const unsigned char *in, size_t size);</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>The buffer library handles simple character arrays. Buffers are used for various purposes in the library, most notably memory BIOs.</p>

<p>BUF_MEM_new() allocates a new buffer of zero size.</p>

<p>BUF_MEM_new_ex() allocates a buffer with the specified flags. The flag <b>BUF_MEM_FLAG_SECURE</b> specifies that the <b>data</b> pointer should be allocated on the secure heap; see <a href="../man3/CRYPTO_secure_malloc.html">CRYPTO_secure_malloc(3)</a>.</p>

<p>BUF_MEM_free() frees up an already existing buffer. The data is zeroed before freeing up in case the buffer contains sensitive data.</p>

<p>BUF_MEM_grow() changes the size of an already existing buffer to <b>len</b>. Any data already in the buffer is preserved if it increases in size.</p>

<p>BUF_MEM_grow_clean() is similar to BUF_MEM_grow() but it sets any free&#39;d or additionally-allocated memory to zero.</p>

<p>BUF_reverse() reverses <b>size</b> bytes at <b>in</b> into <b>out</b>. If <b>in</b> is NULL, the array is reversed in-place.</p>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>BUF_MEM_new() returns the buffer or NULL on error.</p>

<p>BUF_MEM_free() has no return value.</p>

<p>BUF_MEM_grow() and BUF_MEM_grow_clean() return zero on error or the new size (i.e., <b>len</b>).</p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man7/bio.html">bio(7)</a>, <a href="../man3/CRYPTO_secure_malloc.html">CRYPTO_secure_malloc(3)</a>.</p>

<h1 id="HISTORY">HISTORY</h1>

<p>The BUF_MEM_new_ex() function was added in OpenSSL 1.1.0.</p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2000-2018 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the Apache License 2.0 (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


