<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>X509_load_http</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:root@localhost" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#HISTORY">HISTORY</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>X509_load_http, X509_http_nbio, X509_CRL_load_http, X509_CRL_http_nbio - certificate and CRL loading functions</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/x509.h&gt;

X509 *X509_load_http(const char *url, BIO *bio, BIO *rbio, int timeout);
X509_CRL *X509_CRL_load_http(const char *url, BIO *bio, BIO *rbio, int timeout);</code></pre>

<p>The following macros have been deprecated since OpenSSL 3.0, and can be hidden entirely by defining <b>OPENSSL_API_COMPAT</b> with a suitable version value, see <a href="../man7/openssl_user_macros.html">openssl_user_macros(7)</a>:</p>

<pre><code>#define X509_http_nbio(rctx, pcert)
#define X509_CRL_http_nbio(rctx, pcrl)</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>X509_load_http() and X509_CRL_load_http() loads a certificate or a CRL, respectively, in ASN.1 format using HTTP from the given <b>url</b>.</p>

<p>If <b>bio</b> is given and <b>rbio</b> is NULL then this BIO is used instead of an internal one for connecting, writing the request, and reading the response. If both <b>bio</b> and <b>rbio</b> are given (which may be memory BIOs, for instance) then no explicit connection is attempted, <b>bio</b> is used for writing the request, and <b>rbio</b> for reading the response.</p>

<p>If the <b>timeout</b> parameter is &gt; 0 this indicates the maximum number of seconds to wait until the transfer is complete. A value of 0 enables waiting indefinitely, while a value &lt; 0 immediately leads to a timeout condition.</p>

<p>X509_http_nbio() and X509_CRL_http_nbio() are macros for backward compatibility that have the same effect as the functions above but with infinite timeout and without the possibility to specify custom BIOs.</p>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>On success the function yield the loaded value, else NULL. Error conditions include connection/transfer timeout, parse errors, etc.</p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man3/OSSL_HTTP_get.html">OSSL_HTTP_get(3)</a></p>

<h1 id="HISTORY">HISTORY</h1>

<p>X509_load_http() and X509_CRL_load_http() were added in OpenSSL 3.0. X509_http_nbio() and X509_CRL_http_nbio() were deprecated in OpenSSL 3.0.</p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2019-2021 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the Apache License 2.0 (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


