<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>EVP_KEYMGMT</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:root@localhost" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#NOTES">NOTES</a></li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#HISTORY">HISTORY</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>EVP_KEYMGMT, EVP_KEYMGMT_fetch, EVP_KEYMGMT_up_ref, EVP_KEYMGMT_free, EVP_KEYMGMT_get0_provider, EVP_KEYMGMT_is_a, EVP_KEYMGMT_get0_description, EVP_KEYMGMT_get0_name, EVP_KEYMGMT_do_all_provided, EVP_KEYMGMT_names_do_all, EVP_KEYMGMT_gettable_params, EVP_KEYMGMT_settable_params, EVP_KEYMGMT_gen_settable_params - EVP key management routines</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/evp.h&gt;

typedef struct evp_keymgmt_st EVP_KEYMGMT;

EVP_KEYMGMT *EVP_KEYMGMT_fetch(OSSL_LIB_CTX *ctx, const char *algorithm,
                               const char *properties);
int EVP_KEYMGMT_up_ref(EVP_KEYMGMT *keymgmt);
void EVP_KEYMGMT_free(EVP_KEYMGMT *keymgmt);
const OSSL_PROVIDER *EVP_KEYMGMT_get0_provider(const EVP_KEYMGMT *keymgmt);
int EVP_KEYMGMT_is_a(const EVP_KEYMGMT *keymgmt, const char *name);
const char *EVP_KEYMGMT_get0_name(const EVP_KEYMGMT *keymgmt);
const char *EVP_KEYMGMT_get0_description(const EVP_KEYMGMT *keymgmt);

void EVP_KEYMGMT_do_all_provided(OSSL_LIB_CTX *libctx,
                                 void (*fn)(EVP_KEYMGMT *keymgmt, void *arg),
                                 void *arg);
int EVP_KEYMGMT_names_do_all(const EVP_KEYMGMT *keymgmt,
                             void (*fn)(const char *name, void *data),
                             void *data);
const OSSL_PARAM *EVP_KEYMGMT_gettable_params(const EVP_KEYMGMT *keymgmt);
const OSSL_PARAM *EVP_KEYMGMT_settable_params(const EVP_KEYMGMT *keymgmt);
const OSSL_PARAM *EVP_KEYMGMT_gen_settable_params(const EVP_KEYMGMT *keymgmt);</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p><b>EVP_KEYMGMT</b> is a method object that represents key management implementations for different cryptographic algorithms. This method object provides functionality to have providers import key material from the outside, as well as export key material to the outside. Most of the functionality can only be used internally and has no public interface, this object is simply passed into other functions when needed.</p>

<p>EVP_KEYMGMT_fetch() looks for an algorithm within the provider that has been loaded into the <b>OSSL_LIB_CTX</b> given by <i>ctx</i>, having the name given by <i>algorithm</i> and the properties given by <i>properties</i>.</p>

<p>EVP_KEYMGMT_up_ref() increments the reference count for the given <b>EVP_KEYMGMT</b> <i>keymgmt</i>.</p>

<p>EVP_KEYMGMT_free() decrements the reference count for the given <b>EVP_KEYMGMT</b> <i>keymgmt</i>, and when the count reaches zero, frees it.</p>

<p>EVP_KEYMGMT_get0_provider() returns the provider that has this particular implementation.</p>

<p>EVP_KEYMGMT_is_a() checks if <i>keymgmt</i> is an implementation of an algorithm that&#39;s identifiable with <i>name</i>.</p>

<p>EVP_KEYMGMT_get0_name() returns the algorithm name from the provided implementation for the given <i>keymgmt</i>. Note that the <i>keymgmt</i> may have multiple synonyms associated with it. In this case the first name from the algorithm definition is returned. Ownership of the returned string is retained by the <i>keymgmt</i> object and should not be freed by the caller.</p>

<p>EVP_KEYMGMT_names_do_all() traverses all names for the <i>keymgmt</i>, and calls <i>fn</i> with each name and <i>data</i>.</p>

<p>EVP_KEYMGMT_get0_description() returns a description of the <i>keymgmt</i>, meant for display and human consumption. The description is at the discretion of the <i>keymgmt</i> implementation.</p>

<p>EVP_KEYMGMT_do_all_provided() traverses all key keymgmt implementations by all activated providers in the library context <i>libctx</i>, and for each of the implementations, calls <i>fn</i> with the implementation method and <i>data</i> as arguments.</p>

<p>EVP_KEYMGMT_gettable_params() and EVP_KEYMGMT_settable_params() return a constant <a href="../man3/OSSL_PARAM.html">OSSL_PARAM(3)</a> array that describes the names and types of key parameters that can be retrieved or set. EVP_KEYMGMT_gettable_params() is used by <a href="../man3/EVP_PKEY_gettable_params.html">EVP_PKEY_gettable_params(3)</a>.</p>

<p>EVP_KEYMGMT_gen_settable_params() returns a constant <a href="../man3/OSSL_PARAM.html">OSSL_PARAM(3)</a> array that describes the names and types of key generation parameters that can be set via <a href="../man3/EVP_PKEY_CTX_set_params.html">EVP_PKEY_CTX_set_params(3)</a>.</p>

<h1 id="NOTES">NOTES</h1>

<p>EVP_KEYMGMT_fetch() may be called implicitly by other fetching functions, using the same library context and properties. Any other API that uses keys will typically do this.</p>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>EVP_KEYMGMT_fetch() returns a pointer to the key management implementation represented by an EVP_KEYMGMT object, or NULL on error.</p>

<p>EVP_KEYMGMT_up_ref() returns 1 on success, or 0 on error.</p>

<p>EVP_KEYMGMT_names_do_all() returns 1 if the callback was called for all names. A return value of 0 means that the callback was not called for any names.</p>

<p>EVP_KEYMGMT_free() doesn&#39;t return any value.</p>

<p>EVP_KEYMGMT_get0_provider() returns a pointer to a provider object, or NULL on error.</p>

<p>EVP_KEYMGMT_is_a() returns 1 of <i>keymgmt</i> was identifiable, otherwise 0.</p>

<p>EVP_KEYMGMT_get0_name() returns the algorithm name, or NULL on error.</p>

<p>EVP_KEYMGMT_get0_description() returns a pointer to a description, or NULL if there isn&#39;t one.</p>

<p>EVP_KEYMGMT_gettable_params(), EVP_KEYMGMT_settable_params() and EVP_KEYMGMT_gen_settable_params() return a constant <a href="../man3/OSSL_PARAM.html">OSSL_PARAM(3)</a> array or NULL on error.</p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man3/EVP_MD_fetch.html">EVP_MD_fetch(3)</a>, <a href="../man3/OSSL_LIB_CTX.html">OSSL_LIB_CTX(3)</a></p>

<h1 id="HISTORY">HISTORY</h1>

<p>The functions described here were added in OpenSSL 3.0.</p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2019-2023 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the Apache License 2.0 (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


