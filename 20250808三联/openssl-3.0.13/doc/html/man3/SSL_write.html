<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>SSL_write</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:root@localhost" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#NOTES">NOTES</a></li>
  <li><a href="#WARNINGS">WARNINGS</a></li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#HISTORY">HISTORY</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>SSL_write_ex, SSL_write, SSL_sendfile - write bytes to a TLS/SSL connection</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/ssl.h&gt;

ossl_ssize_t SSL_sendfile(SSL *s, int fd, off_t offset, size_t size, int flags);
int SSL_write_ex(SSL *s, const void *buf, size_t num, size_t *written);
int SSL_write(SSL *ssl, const void *buf, int num);</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>SSL_write_ex() and SSL_write() write <b>num</b> bytes from the buffer <b>buf</b> into the specified <b>ssl</b> connection. On success SSL_write_ex() will store the number of bytes written in <b>*written</b>.</p>

<p>SSL_sendfile() writes <b>size</b> bytes from offset <b>offset</b> in the file descriptor <b>fd</b> to the specified SSL connection <b>s</b>. This function provides efficient zero-copy semantics. SSL_sendfile() is available only when Kernel TLS is enabled, which can be checked by calling BIO_get_ktls_send(). It is provided here to allow users to maintain the same interface. The meaning of <b>flags</b> is platform dependent. Currently, under Linux it is ignored.</p>

<h1 id="NOTES">NOTES</h1>

<p>In the paragraphs below a &quot;write function&quot; is defined as one of either SSL_write_ex(), or SSL_write().</p>

<p>If necessary, a write function will negotiate a TLS/SSL session, if not already explicitly performed by <a href="../man3/SSL_connect.html">SSL_connect(3)</a> or <a href="../man3/SSL_accept.html">SSL_accept(3)</a>. If the peer requests a re-negotiation, it will be performed transparently during the write function operation. The behaviour of the write functions depends on the underlying BIO.</p>

<p>For the transparent negotiation to succeed, the <b>ssl</b> must have been initialized to client or server mode. This is being done by calling <a href="../man3/SSL_set_connect_state.html">SSL_set_connect_state(3)</a> or SSL_set_accept_state() before the first call to a write function.</p>

<p>If the underlying BIO is <b>blocking</b>, the write functions will only return, once the write operation has been finished or an error occurred.</p>

<p>If the underlying BIO is <b>nonblocking</b> the write functions will also return when the underlying BIO could not satisfy the needs of the function to continue the operation. In this case a call to <a href="../man3/SSL_get_error.html">SSL_get_error(3)</a> with the return value of the write function will yield <b>SSL_ERROR_WANT_READ</b> or <b>SSL_ERROR_WANT_WRITE</b>. As at any time a re-negotiation is possible, a call to a write function can also cause read operations! The calling process then must repeat the call after taking appropriate action to satisfy the needs of the write function. The action depends on the underlying BIO. When using a nonblocking socket, nothing is to be done, but select() can be used to check for the required condition. When using a buffering BIO, like a BIO pair, data must be written into or retrieved out of the BIO before being able to continue.</p>

<p>The write functions will only return with success when the complete contents of <b>buf</b> of length <b>num</b> has been written. This default behaviour can be changed with the SSL_MODE_ENABLE_PARTIAL_WRITE option of <a href="../man3/SSL_CTX_set_mode.html">SSL_CTX_set_mode(3)</a>. When this flag is set the write functions will also return with success when a partial write has been successfully completed. In this case the write function operation is considered completed. The bytes are sent and a new write call with a new buffer (with the already sent bytes removed) must be started. A partial write is performed with the size of a message block, which is 16kB.</p>

<h1 id="WARNINGS">WARNINGS</h1>

<p>When a write function call has to be repeated because <a href="../man3/SSL_get_error.html">SSL_get_error(3)</a> returned <b>SSL_ERROR_WANT_READ</b> or <b>SSL_ERROR_WANT_WRITE</b>, it must be repeated with the same arguments. The data that was passed might have been partially processed. When <b>SSL_MODE_ACCEPT_MOVING_WRITE_BUFFER</b> was set using <a href="../man3/SSL_CTX_set_mode.html">SSL_CTX_set_mode(3)</a> the pointer can be different, but the data and length should still be the same.</p>

<p>You should not call SSL_write() with num=0, it will return an error. SSL_write_ex() can be called with num=0, but will not send application data to the peer.</p>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>SSL_write_ex() will return 1 for success or 0 for failure. Success means that all requested application data bytes have been written to the SSL connection or, if SSL_MODE_ENABLE_PARTIAL_WRITE is in use, at least 1 application data byte has been written to the SSL connection. Failure means that not all the requested bytes have been written yet (if SSL_MODE_ENABLE_PARTIAL_WRITE is not in use) or no bytes could be written to the SSL connection (if SSL_MODE_ENABLE_PARTIAL_WRITE is in use). Failures can be retryable (e.g. the network write buffer has temporarily filled up) or non-retryable (e.g. a fatal network error). In the event of a failure call <a href="../man3/SSL_get_error.html">SSL_get_error(3)</a> to find out the reason which indicates whether the call is retryable or not.</p>

<p>For SSL_write() the following return values can occur:</p>

<dl>

<dt id="pod0">&gt; 0</dt>
<dd>

<p>The write operation was successful, the return value is the number of bytes actually written to the TLS/SSL connection.</p>

</dd>
<dt id="pod-0">&lt;= 0</dt>
<dd>

<p>The write operation was not successful, because either the connection was closed, an error occurred or action must be taken by the calling process. Call SSL_get_error() with the return value <b>ret</b> to find out the reason.</p>

<p>Old documentation indicated a difference between 0 and -1, and that -1 was retryable. You should instead call SSL_get_error() to find out if it&#39;s retryable.</p>

</dd>
</dl>

<p>For SSL_sendfile(), the following return values can occur:</p>

<dl>

<dt id="pod-01">&gt;= 0</dt>
<dd>

<p>The write operation was successful, the return value is the number of bytes of the file written to the TLS/SSL connection. The return value can be less than <b>size</b> for a partial write.</p>

</dd>
<dt id="pod01">&lt; 0</dt>
<dd>

<p>The write operation was not successful, because either the connection was closed, an error occurred or action must be taken by the calling process. Call SSL_get_error() with the return value to find out the reason.</p>

</dd>
</dl>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man3/SSL_get_error.html">SSL_get_error(3)</a>, <a href="../man3/SSL_read_ex.html">SSL_read_ex(3)</a>, <a href="../man3/SSL_read.html">SSL_read(3)</a> <a href="../man3/SSL_CTX_set_mode.html">SSL_CTX_set_mode(3)</a>, <a href="../man3/SSL_CTX_new.html">SSL_CTX_new(3)</a>, <a href="../man3/SSL_connect.html">SSL_connect(3)</a>, <a href="../man3/SSL_accept.html">SSL_accept(3)</a> <a href="../man3/SSL_set_connect_state.html">SSL_set_connect_state(3)</a>, <a href="../man3/BIO_ctrl.html">BIO_ctrl(3)</a>, <a href="../man7/ssl.html">ssl(7)</a>, <a href="../man7/bio.html">bio(7)</a></p>

<h1 id="HISTORY">HISTORY</h1>

<p>The SSL_write_ex() function was added in OpenSSL 1.1.1. The SSL_sendfile() function was added in OpenSSL 3.0.</p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2000-2021 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the Apache License 2.0 (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


