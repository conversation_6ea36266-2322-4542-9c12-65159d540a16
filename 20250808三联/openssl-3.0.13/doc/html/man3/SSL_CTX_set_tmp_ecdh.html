<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>SSL_CTX_set_tmp_ecdh</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:root@localhost" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>SSL_CTX_set_tmp_ecdh, SSL_set_tmp_ecdh, SSL_CTX_set_ecdh_auto, SSL_set_ecdh_auto - handle ECDH keys for ephemeral key exchange</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/ssl.h&gt;

long SSL_CTX_set_tmp_ecdh(SSL_CTX *ctx, const EC_KEY *ecdh);
long SSL_set_tmp_ecdh(SSL *ssl, const EC_KEY *ecdh);

long SSL_CTX_set_ecdh_auto(SSL_CTX *ctx, int state);
long SSL_set_ecdh_auto(SSL *ssl, int state);</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>SSL_CTX_set_tmp_ecdh() sets ECDH parameters to be used to be <b>ecdh</b>. The key is inherited by all <b>ssl</b> objects created from <b>ctx</b>. This macro is deprecated in favor of <a href="../man3/SSL_CTX_set1_groups.html">SSL_CTX_set1_groups(3)</a>.</p>

<p>SSL_set_tmp_ecdh() sets the parameters only for <b>ssl</b>. This macro is deprecated in favor of <a href="../man3/SSL_set1_groups.html">SSL_set1_groups(3)</a>.</p>

<p>SSL_CTX_set_ecdh_auto() and SSL_set_ecdh_auto() are deprecated and have no effect.</p>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>SSL_CTX_set_tmp_ecdh() and SSL_set_tmp_ecdh() return 1 on success and 0 on failure.</p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man7/ssl.html">ssl(7)</a>, <a href="../man3/SSL_CTX_set1_curves.html">SSL_CTX_set1_curves(3)</a>, <a href="../man3/SSL_CTX_set_cipher_list.html">SSL_CTX_set_cipher_list(3)</a>, <a href="../man3/SSL_CTX_set_options.html">SSL_CTX_set_options(3)</a>, <a href="../man3/SSL_CTX_set_tmp_dh_callback.html">SSL_CTX_set_tmp_dh_callback(3)</a>, <a href="../man1/openssl-ciphers.html">openssl-ciphers(1)</a>, <a href="../man1/openssl-ecparam.html">openssl-ecparam(1)</a></p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2018 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the Apache License 2.0 (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


