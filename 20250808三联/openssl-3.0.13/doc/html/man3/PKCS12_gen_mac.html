<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>PKCS12_gen_mac</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:root@localhost" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#NOTES">NOTES</a></li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#CONFORMING-TO">CONFORMING TO</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>PKCS12_gen_mac, PKCS12_setup_mac, PKCS12_set_mac, PKCS12_verify_mac - Functions to create and manipulate a PKCS#12 structure</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/pkcs12.h&gt;

int PKCS12_gen_mac(PKCS12 *p12, const char *pass, int passlen,
                   unsigned char *mac, unsigned int *maclen);
int PKCS12_verify_mac(PKCS12 *p12, const char *pass, int passlen);
int PKCS12_set_mac(PKCS12 *p12, const char *pass, int passlen,
                   unsigned char *salt, int saltlen, int iter,
                   const EVP_MD *md_type);
int PKCS12_setup_mac(PKCS12 *p12, int iter, unsigned char *salt,
                     int saltlen, const EVP_MD *md_type);</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>PKCS12_gen_mac() generates an HMAC over the entire PKCS#12 object using the supplied password along with a set of already configured parameters. The default key generation mechanism used is PKCS12KDF.</p>

<p>PKCS12_verify_mac() verifies the PKCS#12 object&#39;s HMAC using the supplied password.</p>

<p>PKCS12_setup_mac() sets the MAC part of the PKCS#12 structure with the supplied parameters.</p>

<p>PKCS12_set_mac() sets the MAC and MAC parameters into the PKCS#12 object.</p>

<p><i>pass</i> is the passphrase to use in the HMAC. <i>salt</i> is the salt value to use, <i>iter</i> is the iteration count and <i>md_type</i> is the message digest function to use.</p>

<h1 id="NOTES">NOTES</h1>

<p>If <i>salt</i> is NULL then a suitable salt will be generated and used.</p>

<p>If <i>iter</i> is 1 then an iteration count will be omitted from the PKCS#12 structure.</p>

<p>PKCS12_gen_mac(), PKCS12_verify_mac() and PKCS12_set_mac() make assumptions regarding the encoding of the given passphrase. See <a href="../man7/passphrase-encoding.html">passphrase-encoding(7)</a> for more information.</p>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>All functions return 1 on success and 0 if an error occurred.</p>

<h1 id="CONFORMING-TO">CONFORMING TO</h1>

<p>IETF RFC 7292 (<a href="https://tools.ietf.org/html/rfc7292">https://tools.ietf.org/html/rfc7292</a>)</p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man3/d2i_PKCS12.html">d2i_PKCS12(3)</a>, <a href="../man7/EVP_KDF-PKCS12KDF.html">EVP_KDF-PKCS12KDF(7)</a>, <a href="../man3/PKCS12_create.html">PKCS12_create(3)</a>, <a href="../man7/passphrase-encoding.html">passphrase-encoding(7)</a></p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2021-2023 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the Apache License 2.0 (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


