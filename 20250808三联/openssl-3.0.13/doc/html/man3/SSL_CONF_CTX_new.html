<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>SSL_CONF_CTX_new</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:root@localhost" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#HISTORY">HISTORY</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>SSL_CONF_CTX_new, SSL_CONF_CTX_free - SSL configuration allocation functions</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/ssl.h&gt;

SSL_CONF_CTX *SSL_CONF_CTX_new(void);
void SSL_CONF_CTX_free(SSL_CONF_CTX *cctx);</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>The function SSL_CONF_CTX_new() allocates and initialises an <b>SSL_CONF_CTX</b> structure for use with the SSL_CONF functions.</p>

<p>The function SSL_CONF_CTX_free() frees up the context <b>cctx</b>. If <b>cctx</b> is NULL nothing is done.</p>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>SSL_CONF_CTX_new() returns either the newly allocated <b>SSL_CONF_CTX</b> structure or <b>NULL</b> if an error occurs.</p>

<p>SSL_CONF_CTX_free() does not return a value.</p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man7/ssl.html">ssl(7)</a>, <a href="../man3/SSL_CONF_CTX_set_flags.html">SSL_CONF_CTX_set_flags(3)</a>, <a href="../man3/SSL_CONF_CTX_set_ssl_ctx.html">SSL_CONF_CTX_set_ssl_ctx(3)</a>, <a href="../man3/SSL_CONF_CTX_set1_prefix.html">SSL_CONF_CTX_set1_prefix(3)</a>, <a href="../man3/SSL_CONF_cmd.html">SSL_CONF_cmd(3)</a>, <a href="../man3/SSL_CONF_cmd_argv.html">SSL_CONF_cmd_argv(3)</a></p>

<h1 id="HISTORY">HISTORY</h1>

<p>These functions were added in OpenSSL 1.0.2.</p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2012-2016 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the Apache License 2.0 (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


