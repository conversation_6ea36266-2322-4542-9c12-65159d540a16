<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>EVP_PKEY_get_group_name</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:root@localhost" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#NOTES">NOTES</a></li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#HISTORY">HISTORY</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>EVP_PKEY_get_group_name - get group name of a key</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/evp.h&gt;

int EVP_PKEY_get_group_name(EVP_PKEY *pkey, char *gname, size_t gname_sz,
                            size_t *gname_len);</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>EVP_PKEY_get_group_name() fills in the group name of the <i>pkey</i> into <i>gname</i>, up to at most <i>gname_sz</i> bytes including the ending NUL byte and assigns <i>*gname_len</i> the actual length of the name not including the NUL byte, if <i>pkey</i>&#39;s key type supports it. <i>gname</i> as well as <i>gname_len</i> may individually be NULL, and won&#39;t be filled in or assigned in that case.</p>

<h1 id="NOTES">NOTES</h1>

<p>Among the standard OpenSSL key types, this is only supported for DH, EC and SM2 keys. Other providers may support this for additional key types.</p>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>EVP_PKEY_get_group_name() returns 1 if the group name could be filled in, otherwise 0.</p>

<h1 id="HISTORY">HISTORY</h1>

<p>This function was added in OpenSSL 3.0.</p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2020-2021 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the Apache License 2.0 (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


