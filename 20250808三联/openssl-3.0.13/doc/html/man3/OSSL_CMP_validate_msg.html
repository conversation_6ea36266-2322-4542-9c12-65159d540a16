<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>OSSL_CMP_validate_msg</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:root@localhost" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#NOTES">NOTES</a></li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#HISTORY">HISTORY</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>OSSL_CMP_validate_msg, OSSL_CMP_validate_cert_path - functions for verifying CMP message protection</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/cmp.h&gt;
int OSSL_CMP_validate_msg(OSSL_CMP_CTX *ctx, OSSL_CMP_MSG *msg);
int OSSL_CMP_validate_cert_path(const OSSL_CMP_CTX *ctx,
                                X509_STORE *trusted_store, X509 *cert);</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>This is the API for validating the protection of CMP messages, which includes validating CMP message sender certificates and their paths while optionally checking the revocation status of the certificates(s).</p>

<p>OSSL_CMP_validate_msg() validates the protection of the given <i>msg</i>, which must be signature-based or using password-based MAC (PBM). In the former case a suitable trust anchor must be given in the CMP context <i>ctx</i>, and in the latter case the matching secret must have been set there using <a href="../man3/OSSL_CMP_CTX_set1_secretValue.html">OSSL_CMP_CTX_set1_secretValue(3)</a>.</p>

<p>In case of signature algorithm, the certificate to use for the signature check is preferably the one provided by a call to <a href="../man3/OSSL_CMP_CTX_set1_srvCert.html">OSSL_CMP_CTX_set1_srvCert(3)</a>. If no such sender cert has been pinned then candidate sender certificates are taken from the list of certificates received in the <i>msg</i> extraCerts, then any certificates provided before via <a href="../man3/OSSL_CMP_CTX_set1_untrusted.html">OSSL_CMP_CTX_set1_untrusted(3)</a>, and then all trusted certificates provided via <a href="../man3/OSSL_CMP_CTX_set0_trustedStore.html">OSSL_CMP_CTX_set0_trustedStore(3)</a>, where a candidate is acceptable only if has not expired, its subject DN matches the <i>msg</i> sender DN (as far as present), and its subject key identifier is present and matches the senderKID (as far as the latter present). Each acceptable cert is tried in the given order to see if the message signature check succeeds and the cert and its path can be verified using any trust store set via <a href="../man3/OSSL_CMP_CTX_set0_trustedStore.html">OSSL_CMP_CTX_set0_trustedStore(3)</a>.</p>

<p>If the option OSSL_CMP_OPT_PERMIT_TA_IN_EXTRACERTS_FOR_IR was set by calling <a href="../man3/OSSL_CMP_CTX_set_option.html">OSSL_CMP_CTX_set_option(3)</a>, for an Initialization Response (IP) message any self-issued certificate from the <i>msg</i> extraCerts field may also be used as trust anchor for the path verification of an acceptable cert if it can be used also to validate the issued certificate returned in the IP message. This is according to TS 33.310 [Network Domain Security (NDS); Authentication Framework (AF)] document specified by the The 3rd Generation Partnership Project (3GPP).</p>

<p>Any cert that has been found as described above is cached and tried first when validating the signatures of subsequent messages in the same transaction.</p>

<p>OSSL_CMP_validate_cert_path() attempts to validate the given certificate and its path using the given store of trusted certs (possibly including CRLs and a cert verification callback) and non-trusted intermediate certs from the <i>ctx</i>.</p>

<h1 id="NOTES">NOTES</h1>

<p>CMP is defined in RFC 4210 (and CRMF in RFC 4211).</p>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>OSSL_CMP_validate_msg() and OSSL_CMP_validate_cert_path() return 1 on success, 0 on error or validation failed.</p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man3/OSSL_CMP_CTX_new.html">OSSL_CMP_CTX_new(3)</a>, <a href="../man3/OSSL_CMP_exec_certreq.html">OSSL_CMP_exec_certreq(3)</a>, <a href="../man3/OSSL_CMP_CTX_set1_secretValue.html">OSSL_CMP_CTX_set1_secretValue(3)</a>, <a href="../man3/OSSL_CMP_CTX_set1_srvCert.html">OSSL_CMP_CTX_set1_srvCert(3)</a>, <a href="../man3/OSSL_CMP_CTX_set1_untrusted.html">OSSL_CMP_CTX_set1_untrusted(3)</a>, <a href="../man3/OSSL_CMP_CTX_set0_trustedStore.html">OSSL_CMP_CTX_set0_trustedStore(3)</a></p>

<h1 id="HISTORY">HISTORY</h1>

<p>The OpenSSL CMP support was added in OpenSSL 3.0.</p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2007-2021 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the Apache License 2.0 (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


