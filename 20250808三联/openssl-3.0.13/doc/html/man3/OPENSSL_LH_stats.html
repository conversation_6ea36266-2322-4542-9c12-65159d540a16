<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>OPENSSL_LH_stats</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:root@localhost" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#NOTE">NOTE</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>OPENSSL_LH_stats, OPENSSL_LH_node_stats, OPENSSL_LH_node_usage_stats, OPENSSL_LH_stats_bio, OPENSSL_LH_node_stats_bio, OPENSSL_LH_node_usage_stats_bio - LHASH statistics</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/lhash.h&gt;

void OPENSSL_LH_stats(LHASH *table, FILE *out);
void OPENSSL_LH_node_stats(LHASH *table, FILE *out);
void OPENSSL_LH_node_usage_stats(LHASH *table, FILE *out);

void OPENSSL_LH_stats_bio(LHASH *table, BIO *out);
void OPENSSL_LH_node_stats_bio(LHASH *table, BIO *out);
void OPENSSL_LH_node_usage_stats_bio(LHASH *table, BIO *out);</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>The <b>LHASH</b> structure records statistics about most aspects of accessing the hash table.</p>

<p>OPENSSL_LH_stats() prints out statistics on the size of the hash table and how many entries are in it. For historical reasons, this function also outputs a number of additional statistics, but the tracking of these statistics is no longer supported and these statistics are always reported as zero.</p>

<p>OPENSSL_LH_node_stats() prints the number of entries for each &#39;bucket&#39; in the hash table.</p>

<p>OPENSSL_LH_node_usage_stats() prints out a short summary of the state of the hash table. It prints the &#39;load&#39; and the &#39;actual load&#39;. The load is the average number of data items per &#39;bucket&#39; in the hash table. The &#39;actual load&#39; is the average number of items per &#39;bucket&#39;, but only for buckets which contain entries. So the &#39;actual load&#39; is the average number of searches that will need to find an item in the hash table, while the &#39;load&#39; is the average number that will be done to record a miss.</p>

<p>OPENSSL_LH_stats_bio(), OPENSSL_LH_node_stats_bio() and OPENSSL_LH_node_usage_stats_bio() are the same as the above, except that the output goes to a <b>BIO</b>.</p>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>These functions do not return values.</p>

<h1 id="NOTE">NOTE</h1>

<p>These calls should be made under a read lock. Refer to <a href="../man3/OPENSSL_LH_COMPFUNC.html">&quot;NOTE&quot; in OPENSSL_LH_COMPFUNC(3)</a> for more details about the locks required when using the LHASH data structure.</p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man7/bio.html">bio(7)</a>, <a href="../man3/OPENSSL_LH_COMPFUNC.html">OPENSSL_LH_COMPFUNC(3)</a></p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2000-2022 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the Apache License 2.0 (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


