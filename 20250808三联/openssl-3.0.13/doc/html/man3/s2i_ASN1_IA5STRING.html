<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>s2i_ASN1_IA5STRING</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:root@localhost" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#NOTES">NOTES</a></li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#HISTORY">HISTORY</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>i2s_ASN1_IA5STRING, s2i_ASN1_IA5STRING, i2s_ASN1_INTEGER, s2i_ASN1_INTEGER, i2s_ASN1_OCTET_STRING, s2i_ASN1_OCTET_STRING, i2s_ASN1_ENUMERATED, i2s_ASN1_ENUMERATED_TABLE, i2s_ASN1_UTF8STRING, s2i_ASN1_UTF8STRING - convert objects from/to ASN.1/string representation</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/x509v3.h&gt;

char *i2s_ASN1_IA5STRING(X509V3_EXT_METHOD *method, ASN1_IA5STRING *ia5);
ASN1_IA5STRING *s2i_ASN1_IA5STRING(X509V3_EXT_METHOD *method,
                                  X509V3_CTX *ctx, const char *str);
char *i2s_ASN1_INTEGER(X509V3_EXT_METHOD *method, const ASN1_INTEGER *a);
ASN1_INTEGER *s2i_ASN1_INTEGER(X509V3_EXT_METHOD *method, const char *value);
char *i2s_ASN1_OCTET_STRING(X509V3_EXT_METHOD *method,
                           const ASN1_OCTET_STRING *oct);
ASN1_OCTET_STRING *s2i_ASN1_OCTET_STRING(X509V3_EXT_METHOD *method,
                                        X509V3_CTX *ctx, const char *str);
char *i2s_ASN1_ENUMERATED(X509V3_EXT_METHOD *method, const ASN1_ENUMERATED *a);
char *i2s_ASN1_ENUMERATED_TABLE(X509V3_EXT_METHOD *method,
                               const ASN1_ENUMERATED *e);

char *i2s_ASN1_UTF8STRING(X509V3_EXT_METHOD *method,
                          ASN1_UTF8STRING *utf8);
ASN1_UTF8STRING *s2i_ASN1_UTF8STRING(X509V3_EXT_METHOD *method,
                                     X509V3_CTX *ctx, const char *str);</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>These functions convert OpenSSL objects to and from their ASN.1/string representation. This function is used for <b>X509v3</b> extensions.</p>

<h1 id="NOTES">NOTES</h1>

<p>The letters <b>i</b> and <b>s</b> in <b>i2s</b> and <b>s2i</b> stand for &quot;internal&quot; (that is, an internal C structure) and string respectively. So <b>i2s_ASN1_IA5STRING</b>() converts from internal to string.</p>

<p>It is the caller&#39;s responsibility to free the returned string. In the <b>i2s_ASN1_IA5STRING</b>() function the string is copied and the ownership of the original string remains with the caller.</p>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p><b>i2s_ASN1_IA5STRING</b>() returns the pointer to a IA5 string or NULL if an error occurs.</p>

<p><b>s2i_ASN1_IA5STRING</b>() return a valid <b>ASN1_IA5STRING</b> structure or NULL if an error occurs.</p>

<p><b>i2s_ASN1_INTEGER</b>() return a valid string or NULL if an error occurs.</p>

<p><b>s2i_ASN1_INTEGER</b>() returns the pointer to a <b>ASN1_INTEGER</b> structure or NULL if an error occurs.</p>

<p><b>i2s_ASN1_OCTET_STRING</b>() returns the pointer to a OCTET_STRING string or NULL if an error occurs.</p>

<p><b>s2i_ASN1_OCTET_STRING</b>() return a valid <b>ASN1_OCTET_STRING</b> structure or NULL if an error occurs.</p>

<p><b>i2s_ASN1_ENUMERATED</b>() return a valid string or NULL if an error occurs.</p>

<p><b>s2i_ASN1_ENUMERATED</b>() returns the pointer to a <b>ASN1_ENUMERATED</b> structure or NULL if an error occurs.</p>

<p><b>s2i_ASN1_UTF8STRING</b>() return a valid <b>ASN1_UTF8STRING</b> structure or NULL if an error occurs.</p>

<p><b>i2s_ASN1_UTF8STRING</b>() returns the pointer to a UTF-8 string or NULL if an error occurs.</p>

<h1 id="HISTORY">HISTORY</h1>

<p>i2s_ASN1_UTF8STRING() and s2i_ASN1_UTF8STRING() were made public in OpenSSL 3.0.</p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2020-2021 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the Apache License 2.0 (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


