<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>DSA_do_sign</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:root@localhost" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#HISTORY">HISTORY</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>DSA_do_sign, DSA_do_verify - raw DSA signature operations</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/dsa.h&gt;</code></pre>

<p>The following functions have been deprecated since OpenSSL 3.0, and can be hidden entirely by defining <b>OPENSSL_API_COMPAT</b> with a suitable version value, see <a href="../man7/openssl_user_macros.html">openssl_user_macros(7)</a>:</p>

<pre><code>DSA_SIG *DSA_do_sign(const unsigned char *dgst, int dlen, DSA *dsa);

int DSA_do_verify(const unsigned char *dgst, int dgst_len,
                  DSA_SIG *sig, DSA *dsa);</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>All of the functions described on this page are deprecated. Applications should instead use <a href="../man3/EVP_PKEY_sign_init.html">EVP_PKEY_sign_init(3)</a>, <a href="../man3/EVP_PKEY_sign.html">EVP_PKEY_sign(3)</a>, <a href="../man3/EVP_PKEY_verify_init.html">EVP_PKEY_verify_init(3)</a> and <a href="../man3/EVP_PKEY_verify.html">EVP_PKEY_verify(3)</a>.</p>

<p>DSA_do_sign() computes a digital signature on the <b>len</b> byte message digest <b>dgst</b> using the private key <b>dsa</b> and returns it in a newly allocated <b>DSA_SIG</b> structure.</p>

<p><a href="../man3/DSA_sign_setup.html">DSA_sign_setup(3)</a> may be used to precompute part of the signing operation in case signature generation is time-critical.</p>

<p>DSA_do_verify() verifies that the signature <b>sig</b> matches a given message digest <b>dgst</b> of size <b>len</b>. <b>dsa</b> is the signer&#39;s public key.</p>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>DSA_do_sign() returns the signature, NULL on error. DSA_do_verify() returns 1 for a valid signature, 0 for an incorrect signature and -1 on error. The error codes can be obtained by <a href="../man3/ERR_get_error.html">ERR_get_error(3)</a>.</p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man3/DSA_new.html">DSA_new(3)</a>, <a href="../man3/ERR_get_error.html">ERR_get_error(3)</a>, <a href="../man3/RAND_bytes.html">RAND_bytes(3)</a>, <a href="../man3/DSA_SIG_new.html">DSA_SIG_new(3)</a>, <a href="../man3/DSA_sign.html">DSA_sign(3)</a></p>

<h1 id="HISTORY">HISTORY</h1>

<p>All of these functions were deprecated in OpenSSL 3.0.</p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2000-2021 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the Apache License 2.0 (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


