<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>PKCS12_SAFEBAG_create_cert</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:root@localhost" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#NOTES">NOTES</a></li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#CONFORMING-TO">CONFORMING TO</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#HISTORY">HISTORY</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>PKCS12_SAFEBAG_create_cert, PKCS12_SAFEBAG_create_crl, PKCS12_SAFEBAG_create_secret, PKCS12_SAFEBAG_create0_p8inf, PKCS12_SAFEBAG_create0_pkcs8, PKCS12_SAFEBAG_create_pkcs8_encrypt, PKCS12_SAFEBAG_create_pkcs8_encrypt_ex - Create PKCS#12 safeBag objects</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/pkcs12.h&gt;

PKCS12_SAFEBAG *PKCS12_SAFEBAG_create_cert(X509 *x509);
PKCS12_SAFEBAG *PKCS12_SAFEBAG_create_crl(X509_CRL *crl);
PKCS12_SAFEBAG *PKCS12_SAFEBAG_create_secret(int type, int vtype,
                                             const unsigned char* value,
                                             int len);
PKCS12_SAFEBAG *PKCS12_SAFEBAG_create0_p8inf(PKCS8_PRIV_KEY_INFO *p8);
PKCS12_SAFEBAG *PKCS12_SAFEBAG_create0_pkcs8(X509_SIG *p8);
PKCS12_SAFEBAG *PKCS12_SAFEBAG_create_pkcs8_encrypt(int pbe_nid,
                                                    const char *pass,
                                                    int passlen,
                                                    unsigned char *salt,
                                                    int saltlen, int iter,
                                                    PKCS8_PRIV_KEY_INFO *p8inf);
PKCS12_SAFEBAG *PKCS12_SAFEBAG_create_pkcs8_encrypt_ex(int pbe_nid,
                                                       const char *pass,
                                                       int passlen,
                                                       unsigned char *salt,
                                                       int saltlen, int iter,
                                                       PKCS8_PRIV_KEY_INFO *p8inf,
                                                       OSSL_LIB_CTX *ctx,
                                                       const char *propq);</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>PKCS12_SAFEBAG_create_cert() creates a new <b>PKCS12_SAFEBAG</b> of type <b>NID_certBag</b> containing the supplied certificate.</p>

<p>PKCS12_SAFEBAG_create_crl() creates a new <b>PKCS12_SAFEBAG</b> of type <b>NID_crlBag</b> containing the supplied crl.</p>

<p>PKCS12_SAFEBAG_create_secret() creates a new <b>PKCS12_SAFEBAG</b> of type corresponding to a PKCS#12 <b>secretBag</b>. The <b>secretBag</b> contents are tagged as <i>type</i> with an ASN1 value of type <i>vtype</i> constructed using the bytes in <i>value</i> of length <i>len</i>.</p>

<p>PKCS12_SAFEBAG_create0_p8inf() creates a new <b>PKCS12_SAFEBAG</b> of type <b>NID_keyBag</b> containing the supplied PKCS8 structure.</p>

<p>PKCS12_SAFEBAG_create0_pkcs8() creates a new <b>PKCS12_SAFEBAG</b> of type <b>NID_pkcs8ShroudedKeyBag</b> containing the supplied PKCS8 structure.</p>

<p>PKCS12_SAFEBAG_create_pkcs8_encrypt() creates a new <b>PKCS12_SAFEBAG</b> of type <b>NID_pkcs8ShroudedKeyBag</b> by encrypting the supplied PKCS8 <i>p8inf</i>. If <i>pbe_nid</i> is 0, a default encryption algorithm is used. <i>pass</i> is the passphrase and <i>iter</i> is the iteration count. If <i>iter</i> is zero then a default value of 2048 is used. If <i>salt</i> is NULL then a salt is generated randomly.</p>

<p>PKCS12_SAFEBAG_create_pkcs8_encrypt_ex() is identical to PKCS12_SAFEBAG_create_pkcs8_encrypt() but allows for a library context <i>ctx</i> and property query <i>propq</i> to be used to select algorithm implementations.</p>

<h1 id="NOTES">NOTES</h1>

<p>PKCS12_SAFEBAG_create_pkcs8_encrypt() makes assumptions regarding the encoding of the given pass phrase. See <a href="../man7/passphrase-encoding.html">passphrase-encoding(7)</a> for more information.</p>

<p>PKCS12_SAFEBAG_create_secret() was added in OpenSSL 3.0.</p>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>All of these functions return a valid <b>PKCS12_SAFEBAG</b> structure or NULL if an error occurred.</p>

<h1 id="CONFORMING-TO">CONFORMING TO</h1>

<p>IETF RFC 7292 (<a href="https://tools.ietf.org/html/rfc7292">https://tools.ietf.org/html/rfc7292</a>)</p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man3/PKCS12_create.html">PKCS12_create(3)</a>, <a href="../man3/PKCS12_add_safe.html">PKCS12_add_safe(3)</a>, <a href="../man3/PKCS12_add_safes.html">PKCS12_add_safes(3)</a></p>

<h1 id="HISTORY">HISTORY</h1>

<p>PKCS12_SAFEBAG_create_pkcs8_encrypt_ex() was added in OpenSSL 3.0.</p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2019-2021 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the Apache License 2.0 (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


