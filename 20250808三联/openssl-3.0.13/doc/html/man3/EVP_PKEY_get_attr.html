<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>EVP_PKEY_get_attr</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:root@localhost" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#NOTES">NOTES</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>EVP_PKEY_get_attr, EVP_PKEY_get_attr_count, EVP_PKEY_get_attr_by_NID, EVP_PKEY_get_attr_by_OBJ, EVP_PKEY_delete_attr, EVP_PKEY_add1_attr, EVP_PKEY_add1_attr_by_OBJ, EVP_PKEY_add1_attr_by_NID, EVP_PKEY_add1_attr_by_txt - EVP_PKEY <b>X509_ATTRIBUTE</b> functions</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/x509.h&gt;

int EVP_PKEY_get_attr_count(const EVP_PKEY *key);
int EVP_PKEY_get_attr_by_NID(const EVP_PKEY *key, int nid, int lastpos);
int EVP_PKEY_get_attr_by_OBJ(const EVP_PKEY *key, const ASN1_OBJECT *obj,
                             int lastpos);
X509_ATTRIBUTE *EVP_PKEY_get_attr(const EVP_PKEY *key, int loc);
X509_ATTRIBUTE *EVP_PKEY_delete_attr(EVP_PKEY *key, int loc);
int EVP_PKEY_add1_attr(EVP_PKEY *key, X509_ATTRIBUTE *attr);
int EVP_PKEY_add1_attr_by_OBJ(EVP_PKEY *key,
                              const ASN1_OBJECT *obj, int type,
                              const unsigned char *bytes, int len);
int EVP_PKEY_add1_attr_by_NID(EVP_PKEY *key,
                              int nid, int type,
                              const unsigned char *bytes, int len);
int EVP_PKEY_add1_attr_by_txt(EVP_PKEY *key,
                              const char *attrname, int type,
                              const unsigned char *bytes, int len);</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>These functions are used by <b>PKCS12</b>.</p>

<p>EVP_PKEY_get_attr_by_OBJ() finds the location of the first matching object <i>obj</i> in the <i>key</i> attribute list. The search starts at the position after <i>lastpos</i>. If the returned value is positive then it can be used on the next call to EVP_PKEY_get_attr_by_OBJ() as the value of <i>lastpos</i> in order to iterate through the remaining attributes. <i>lastpos</i> can be set to any negative value on the first call, in order to start searching from the start of the attribute list.</p>

<p>EVP_PKEY_get_attr_by_NID() is similar to EVP_PKEY_get_attr_by_OBJ() except that it passes the numerical identifier (NID) <i>nid</i> associated with the object. See &lt;openssl/obj_mac.h&gt; for a list of NID_*.</p>

<p>EVP_PKEY_get_attr() returns the <b>X509_ATTRIBUTE</b> object at index <i>loc</i> in the <i>key</i> attribute list. <i>loc</i> should be in the range from 0 to EVP_PKEY_get_attr_count() - 1.</p>

<p>EVP_PKEY_delete_attr() removes the <b>X509_ATTRIBUTE</b> object at index <i>loc</i> in the <i>key</i> attribute list.</p>

<p>EVP_PKEY_add1_attr() pushes a copy of the passed in <b>X509_ATTRIBUTE</b> object to the <i>key</i> attribute list. A new <i>key</i> attribute list is created if required. An error occurs if either <i>attr</i> is NULL, or the attribute already exists.</p>

<p>EVP_PKEY_add1_attr_by_OBJ() creates a new <b>X509_ATTRIBUTE</b> using X509_ATTRIBUTE_set1_object() and X509_ATTRIBUTE_set1_data() to assign a new <i>obj</i> with type <i>type</i> and data <i>bytes</i> of length <i>len</i> and then pushes it to the <i>key</i> object&#39;s attribute list. If <i>obj</i> already exists in the attribute list then an error occurs.</p>

<p>EVP_PKEY_add1_attr_by_NID() is similar to EVP_PKEY_add1_attr_by_OBJ() except that it passes the numerical identifier (NID) <i>nid</i> associated with the object. See &lt;openssl/obj_mac.h&gt; for a list of NID_*.</p>

<p>EVP_PKEY_add1_attr_by_txt() is similar to EVP_PKEY_add1_attr_by_OBJ() except that it passes a name <i>attrname</i> associated with the object. See &lt;openssl/obj_mac.h&gt; for a list of SN_* names.</p>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>EVP_PKEY_get_attr_count() returns the number of attributes in the <i>key</i> object attribute list or -1 if the attribute list is NULL.</p>

<p>EVP_PKEY_get_attr_by_OBJ() returns -1 if either the list is empty OR the object is not found, otherwise it returns the location of the object in the list.</p>

<p>EVP_PKEY_get_attr_by_NID() is similar to EVP_PKEY_get_attr_by_OBJ(), except that it returns -2 if the <i>nid</i> is not known by OpenSSL.</p>

<p>EVP_PKEY_get_attr() returns either a <b>X509_ATTRIBUTE</b> or NULL if there is a error.</p>

<p>EVP_PKEY_delete_attr() returns either the removed <b>X509_ATTRIBUTE</b> or NULL if there is a error.</p>

<p>EVP_PKEY_add1_attr(), EVP_PKEY_add1_attr_by_OBJ(), EVP_PKEY_add1_attr_by_NID() and EVP_PKEY_add1_attr_by_txt() return 1 on success or 0 otherwise.</p>

<h1 id="NOTES">NOTES</h1>

<p>A <b>EVP_PKEY</b> object&#39;s attribute list is initially NULL. All the above functions listed will return an error unless EVP_PKEY_add1_attr() is called. All functions listed assume that the <i>key</i> is not NULL.</p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man3/X509_ATTRIBUTE.html">X509_ATTRIBUTE(3)</a></p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2023-2024 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the Apache License 2.0 (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


