<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>SSL_get_peer_tmp_key</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:root@localhost" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#NOTES">NOTES</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>SSL_get_peer_tmp_key, SSL_get_server_tmp_key, SSL_get_tmp_key - get information about temporary keys used during a handshake</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/ssl.h&gt;

long SSL_get_peer_tmp_key(SSL *ssl, EVP_PKEY **key);
long SSL_get_server_tmp_key(SSL *ssl, EVP_PKEY **key);
long SSL_get_tmp_key(SSL *ssl, EVP_PKEY **key);</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>SSL_get_peer_tmp_key() returns the temporary key provided by the peer and used during key exchange. For example, if ECDHE is in use, then this represents the peer&#39;s public ECDHE key. On success a pointer to the key is stored in <b>*key</b>. It is the caller&#39;s responsibility to free this key after use using <a href="../man3/EVP_PKEY_free.html">EVP_PKEY_free(3)</a>.</p>

<p>SSL_get_server_tmp_key() is a backwards compatibility alias for SSL_get_peer_tmp_key(). Under that name it worked just on the client side of the connection, its behaviour on the server end is release-dependent.</p>

<p>SSL_get_tmp_key() returns the equivalent information for the local end of the connection.</p>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>All these functions return 1 on success and 0 otherwise.</p>

<h1 id="NOTES">NOTES</h1>

<p>This function is implemented as a macro.</p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man7/ssl.html">ssl(7)</a>, <a href="../man3/EVP_PKEY_free.html">EVP_PKEY_free(3)</a></p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2017-2018 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the Apache License 2.0 (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


