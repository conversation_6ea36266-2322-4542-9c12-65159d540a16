<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>EVP_PKEY_digestsign_supports_digest</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:root@localhost" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#HISTORY">HISTORY</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>EVP_PKEY_digestsign_supports_digest - indicate support for signature digest</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/evp.h&gt;
int EVP_PKEY_digestsign_supports_digest(EVP_PKEY *pkey, OSSL_LIB_CTX *libctx,
                                        const char *name, const char *propq);</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>The EVP_PKEY_digestsign_supports_digest() function queries whether the message digest <i>name</i> is supported for public key signature operations associated with key <i>pkey</i>. The query is done within an optional library context <i>libctx</i> and with an optional property query <i>propq</i>.</p>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>The EVP_PKEY_digestsign_supports_digest() function returns 1 if the message digest algorithm identified by <i>name</i> can be used for public key signature operations associated with key <i>pkey</i> and 0 if it cannot be used. It returns a negative value for failure.</p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man3/EVP_DigestSignInit_ex.html">EVP_DigestSignInit_ex(3)</a>,</p>

<h1 id="HISTORY">HISTORY</h1>

<p>The EVP_PKEY_digestsign_supports_digest() function was added in OpenSSL 3.0.</p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2021 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the Apache License 2.0 (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


