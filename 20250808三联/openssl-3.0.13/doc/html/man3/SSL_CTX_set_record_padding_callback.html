<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>SSL_CTX_set_record_padding_callback</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:root@localhost" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#NOTES">NOTES</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#HISTORY">HISTORY</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>SSL_CTX_set_record_padding_callback, SSL_set_record_padding_callback, SSL_CTX_set_record_padding_callback_arg, SSL_set_record_padding_callback_arg, SSL_CTX_get_record_padding_callback_arg, SSL_get_record_padding_callback_arg, SSL_CTX_set_block_padding, SSL_set_block_padding - install callback to specify TLS 1.3 record padding</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/ssl.h&gt;

void SSL_CTX_set_record_padding_callback(SSL_CTX *ctx, size_t (*cb)(SSL *s, int type, size_t len, void *arg));
int SSL_set_record_padding_callback(SSL *ssl, size_t (*cb)(SSL *s, int type, size_t len, void *arg));

void SSL_CTX_set_record_padding_callback_arg(SSL_CTX *ctx, void *arg);
void *SSL_CTX_get_record_padding_callback_arg(const SSL_CTX *ctx);

void SSL_set_record_padding_callback_arg(SSL *ssl, void *arg);
void *SSL_get_record_padding_callback_arg(const SSL *ssl);

int SSL_CTX_set_block_padding(SSL_CTX *ctx, size_t block_size);
int SSL_set_block_padding(SSL *ssl, size_t block_size);</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>SSL_CTX_set_record_padding_callback() or SSL_set_record_padding_callback() can be used to assign a callback function <i>cb</i> to specify the padding for TLS 1.3 records. The value set in <b>ctx</b> is copied to a new SSL by SSL_new(). Kernel TLS is not possible if the record padding callback is set, and the callback function cannot be set if Kernel TLS is already configured for the current SSL object.</p>

<p>SSL_CTX_set_record_padding_callback_arg() and SSL_set_record_padding_callback_arg() assign a value <b>arg</b> that is passed to the callback when it is invoked. The value set in <b>ctx</b> is copied to a new SSL by SSL_new().</p>

<p>SSL_CTX_get_record_padding_callback_arg() and SSL_get_record_padding_callback_arg() retrieve the <b>arg</b> value that is passed to the callback.</p>

<p>SSL_CTX_set_block_padding() and SSL_set_block_padding() pads the record to a multiple of the <b>block_size</b>. A <b>block_size</b> of 0 or 1 disables block padding. The limit of <b>block_size</b> is SSL3_RT_MAX_PLAIN_LENGTH.</p>

<p>The callback is invoked for every record before encryption. The <b>type</b> parameter is the TLS record type that is being processed; may be one of SSL3_RT_APPLICATION_DATA, SSL3_RT_HANDSHAKE, or SSL3_RT_ALERT. The <b>len</b> parameter is the current plaintext length of the record before encryption. The <b>arg</b> parameter is the value set via SSL_CTX_set_record_padding_callback_arg() or SSL_set_record_padding_callback_arg().</p>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>The SSL_CTX_get_record_padding_callback_arg() and SSL_get_record_padding_callback_arg() functions return the <b>arg</b> value assigned in the corresponding set functions.</p>

<p>The SSL_CTX_set_block_padding() and SSL_set_block_padding() functions return 1 on success or 0 if <b>block_size</b> is too large.</p>

<p>The <b>cb</b> returns the number of padding bytes to add to the record. A return of 0 indicates no padding will be added. A return value that causes the record to exceed the maximum record size (SSL3_RT_MAX_PLAIN_LENGTH) will pad out to the maximum record size.</p>

<p>The SSL_CTX_get_record_padding_callback_arg() function returns 1 on success or 0 if the callback function is not set because Kernel TLS is configured for the SSL object.</p>

<h1 id="NOTES">NOTES</h1>

<p>The default behavior is to add no padding to the record.</p>

<p>A user-supplied padding callback function will override the behavior set by SSL_set_block_padding() or SSL_CTX_set_block_padding(). Setting the user-supplied callback to NULL will restore the configured block padding behavior.</p>

<p>These functions only apply to TLS 1.3 records being written.</p>

<p>Padding bytes are not added in constant-time.</p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man7/ssl.html">ssl(7)</a>, <a href="../man3/SSL_new.html">SSL_new(3)</a></p>

<h1 id="HISTORY">HISTORY</h1>

<p>The record padding API was added for TLS 1.3 support in OpenSSL 1.1.1.</p>

<p>The return type of SSL_CTX_set_record_padding_callback() function was changed to int in OpenSSL 3.0.</p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2017-2020 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the Apache License 2.0 (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


