<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>BIO_s_fd</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:root@localhost" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#NOTES">NOTES</a></li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#EXAMPLES">EXAMPLES</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>BIO_s_fd, BIO_set_fd, BIO_get_fd, BIO_new_fd - file descriptor BIO</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/bio.h&gt;

const BIO_METHOD *BIO_s_fd(void);

int BIO_set_fd(BIO *b, int fd, int c);
int BIO_get_fd(BIO *b, int *c);

BIO *BIO_new_fd(int fd, int close_flag);</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>BIO_s_fd() returns the file descriptor BIO method. This is a wrapper round the platforms file descriptor routines such as read() and write().</p>

<p>BIO_read_ex() and BIO_write_ex() read or write the underlying descriptor. BIO_puts() is supported but BIO_gets() is not.</p>

<p>If the close flag is set then close() is called on the underlying file descriptor when the BIO is freed.</p>

<p>BIO_reset() attempts to change the file pointer to the start of file such as by using <b>lseek(fd, 0, 0)</b>.</p>

<p>BIO_seek() sets the file pointer to position <b>ofs</b> from start of file such as by using <b>lseek(fd, ofs, 0)</b>.</p>

<p>BIO_tell() returns the current file position such as by calling <b>lseek(fd, 0, 1)</b>.</p>

<p>BIO_set_fd() sets the file descriptor of BIO <b>b</b> to <b>fd</b> and the close flag to <b>c</b>.</p>

<p>BIO_get_fd() places the file descriptor of BIO <b>b</b> in <b>c</b> if it is not NULL. It also returns the file descriptor.</p>

<p>BIO_new_fd() returns a file descriptor BIO using <b>fd</b> and <b>close_flag</b>.</p>

<h1 id="NOTES">NOTES</h1>

<p>The behaviour of BIO_read_ex() and BIO_write_ex() depends on the behavior of the platforms read() and write() calls on the descriptor. If the underlying file descriptor is in a non blocking mode then the BIO will behave in the manner described in the <a href="../man3/BIO_read_ex.html">BIO_read_ex(3)</a> and <a href="../man3/BIO_should_retry.html">BIO_should_retry(3)</a> manual pages.</p>

<p>File descriptor BIOs should not be used for socket I/O. Use socket BIOs instead.</p>

<p>BIO_set_fd() and BIO_get_fd() are implemented as macros.</p>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>BIO_s_fd() returns the file descriptor BIO method.</p>

<p>BIO_set_fd() returns 1 on success or &lt;=0 for failure.</p>

<p>BIO_get_fd() returns the file descriptor or -1 if the BIO has not been initialized. It also returns zero and negative values if other error occurs.</p>

<p>BIO_new_fd() returns the newly allocated BIO or NULL is an error occurred.</p>

<h1 id="EXAMPLES">EXAMPLES</h1>

<p>This is a file descriptor BIO version of &quot;Hello World&quot;:</p>

<pre><code>BIO *out;

out = BIO_new_fd(fileno(stdout), BIO_NOCLOSE);
BIO_printf(out, &quot;Hello World\n&quot;);
BIO_free(out);</code></pre>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man3/BIO_seek.html">BIO_seek(3)</a>, <a href="../man3/BIO_tell.html">BIO_tell(3)</a>, <a href="../man3/BIO_reset.html">BIO_reset(3)</a>, <a href="../man3/BIO_read_ex.html">BIO_read_ex(3)</a>, <a href="../man3/BIO_write_ex.html">BIO_write_ex(3)</a>, <a href="../man3/BIO_puts.html">BIO_puts(3)</a>, <a href="../man3/BIO_gets.html">BIO_gets(3)</a>, <a href="../man3/BIO_printf.html">BIO_printf(3)</a>, <a href="../man3/BIO_set_close.html">BIO_set_close(3)</a>, <a href="../man3/BIO_get_close.html">BIO_get_close(3)</a></p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2000-2021 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the Apache License 2.0 (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


