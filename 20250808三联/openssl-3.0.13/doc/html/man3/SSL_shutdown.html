<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>SSL_shutdown</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:root@localhost" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a>
    <ul>
      <li><a href="#First-to-close-the-connection">First to close the connection</a></li>
      <li><a href="#Peer-closes-the-connection">Peer closes the connection</a></li>
    </ul>
  </li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>SSL_shutdown - shut down a TLS/SSL connection</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/ssl.h&gt;

int SSL_shutdown(SSL *ssl);</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>SSL_shutdown() shuts down an active TLS/SSL connection. It sends the close_notify shutdown alert to the peer.</p>

<p>SSL_shutdown() tries to send the close_notify shutdown alert to the peer. Whether the operation succeeds or not, the SSL_SENT_SHUTDOWN flag is set and a currently open session is considered closed and good and will be kept in the session cache for further reuse.</p>

<p>Note that SSL_shutdown() must not be called if a previous fatal error has occurred on a connection i.e. if SSL_get_error() has returned SSL_ERROR_SYSCALL or SSL_ERROR_SSL.</p>

<p>The shutdown procedure consists of two steps: sending of the close_notify shutdown alert, and reception of the peer&#39;s close_notify shutdown alert. The order of those two steps depends on the application.</p>

<p>It is acceptable for an application to only send its shutdown alert and then close the underlying connection without waiting for the peer&#39;s response. This way resources can be saved, as the process can already terminate or serve another connection. This should only be done when it is known that the other side will not send more data, otherwise there is a risk of a truncation attack.</p>

<p>When a client only writes and never reads from the connection, and the server has sent a session ticket to establish a session, the client might not be able to resume the session because it did not received and process the session ticket from the server. In case the application wants to be able to resume the session, it is recommended to do a complete shutdown procedure (bidirectional close_notify alerts).</p>

<p>When the underlying connection shall be used for more communications, the complete shutdown procedure must be performed, so that the peers stay synchronized.</p>

<p>SSL_shutdown() only closes the write direction. It is not possible to call SSL_write() after calling SSL_shutdown(). The read direction is closed by the peer.</p>

<p>The behaviour of SSL_shutdown() additionally depends on the underlying BIO. If the underlying BIO is <b>blocking</b>, SSL_shutdown() will only return once the handshake step has been finished or an error occurred.</p>

<p>If the underlying BIO is <b>nonblocking</b>, SSL_shutdown() will also return when the underlying BIO could not satisfy the needs of SSL_shutdown() to continue the handshake. In this case a call to SSL_get_error() with the return value of SSL_shutdown() will yield <b>SSL_ERROR_WANT_READ</b> or <b>SSL_ERROR_WANT_WRITE</b>. The calling process then must repeat the call after taking appropriate action to satisfy the needs of SSL_shutdown(). The action depends on the underlying BIO. When using a nonblocking socket, nothing is to be done, but select() can be used to check for the required condition. When using a buffering BIO, like a BIO pair, data must be written into or retrieved out of the BIO before being able to continue.</p>

<p>After SSL_shutdown() returned 0, it is possible to call SSL_shutdown() again to wait for the peer&#39;s close_notify alert. SSL_shutdown() will return 1 in that case. However, it is recommended to wait for it using SSL_read() instead.</p>

<p>SSL_shutdown() can be modified to only set the connection to &quot;shutdown&quot; state but not actually send the close_notify alert messages, see <a href="../man3/SSL_CTX_set_quiet_shutdown.html">SSL_CTX_set_quiet_shutdown(3)</a>. When &quot;quiet shutdown&quot; is enabled, SSL_shutdown() will always succeed and return 1. Note that this is not standard compliant behaviour. It should only be done when the peer has a way to make sure all data has been received and doesn&#39;t wait for the close_notify alert message, otherwise an unexpected EOF will be reported.</p>

<p>There are implementations that do not send the required close_notify alert. If there is a need to communicate with such an implementation, and it&#39;s clear that all data has been received, do not wait for the peer&#39;s close_notify alert. Waiting for the close_notify alert when the peer just closes the connection will result in an error being generated. The error can be ignored using the <b>SSL_OP_IGNORE_UNEXPECTED_EOF</b>. For more information see <a href="../man3/SSL_CTX_set_options.html">SSL_CTX_set_options(3)</a>.</p>

<h2 id="First-to-close-the-connection">First to close the connection</h2>

<p>When the application is the first party to send the close_notify alert, SSL_shutdown() will only send the alert and then set the SSL_SENT_SHUTDOWN flag (so that the session is considered good and will be kept in the cache). If successful, SSL_shutdown() will return 0.</p>

<p>If a unidirectional shutdown is enough (the underlying connection shall be closed anyway), this first successful call to SSL_shutdown() is sufficient.</p>

<p>In order to complete the bidirectional shutdown handshake, the peer needs to send back a close_notify alert. The SSL_RECEIVED_SHUTDOWN flag will be set after receiving and processing it.</p>

<p>The peer is still allowed to send data after receiving the close_notify event. When it is done sending data, it will send the close_notify alert. SSL_read() should be called until all data is received. SSL_read() will indicate the end of the peer data by returning &lt;= 0 and SSL_get_error() returning SSL_ERROR_ZERO_RETURN.</p>

<h2 id="Peer-closes-the-connection">Peer closes the connection</h2>

<p>If the peer already sent the close_notify alert <b>and</b> it was already processed implicitly inside another function (<a href="../man3/SSL_read.html">SSL_read(3)</a>), the SSL_RECEIVED_SHUTDOWN flag is set. SSL_read() will return &lt;= 0 in that case, and SSL_get_error() will return SSL_ERROR_ZERO_RETURN. SSL_shutdown() will send the close_notify alert, set the SSL_SENT_SHUTDOWN flag. If successful, SSL_shutdown() will return 1.</p>

<p>Whether SSL_RECEIVED_SHUTDOWN is already set can be checked using the SSL_get_shutdown() (see also <a href="../man3/SSL_set_shutdown.html">SSL_set_shutdown(3)</a> call.</p>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>The following return values can occur:</p>

<dl>

<dt id="pod0">0</dt>
<dd>

<p>The shutdown is not yet finished: the close_notify was sent but the peer did not send it back yet. Call SSL_read() to do a bidirectional shutdown.</p>

<p>Unlike most other function, returning 0 does not indicate an error. <a href="../man3/SSL_get_error.html">SSL_get_error(3)</a> should not get called, it may misleadingly indicate an error even though no error occurred.</p>

</dd>
<dt id="pod1">1</dt>
<dd>

<p>The shutdown was successfully completed. The close_notify alert was sent and the peer&#39;s close_notify alert was received.</p>

</dd>
<dt id="pod01">&lt;0</dt>
<dd>

<p>The shutdown was not successful. Call <a href="../man3/SSL_get_error.html">SSL_get_error(3)</a> with the return value <b>ret</b> to find out the reason. It can occur if an action is needed to continue the operation for nonblocking BIOs.</p>

<p>It can also occur when not all data was read using SSL_read().</p>

</dd>
</dl>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man3/SSL_get_error.html">SSL_get_error(3)</a>, <a href="../man3/SSL_connect.html">SSL_connect(3)</a>, <a href="../man3/SSL_accept.html">SSL_accept(3)</a>, <a href="../man3/SSL_set_shutdown.html">SSL_set_shutdown(3)</a>, <a href="../man3/SSL_CTX_set_quiet_shutdown.html">SSL_CTX_set_quiet_shutdown(3)</a>, <a href="../man3/SSL_CTX_set_options.html">SSL_CTX_set_options(3)</a> <a href="../man3/SSL_clear.html">SSL_clear(3)</a>, <a href="../man3/SSL_free.html">SSL_free(3)</a>, <a href="../man7/ssl.html">ssl(7)</a>, <a href="../man7/bio.html">bio(7)</a></p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2000-2020 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the Apache License 2.0 (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


