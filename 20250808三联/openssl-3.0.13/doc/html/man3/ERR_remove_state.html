<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>ERR_remove_state</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:root@localhost" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#HISTORY">HISTORY</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>ERR_remove_thread_state, ERR_remove_state - DEPRECATED</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<p>The following function has been deprecated since OpenSSL 1.0.0, and can be hidden entirely by defining <b>OPENSSL_API_COMPAT</b> with a suitable version value, see <a href="../man7/openssl_user_macros.html">openssl_user_macros(7)</a>:</p>

<pre><code>void ERR_remove_state(unsigned long tid);</code></pre>

<p>The following function has been deprecated since OpenSSL 1.1.0, and can be hidden entirely by defining <b>OPENSSL_API_COMPAT</b> with a suitable version value, see <a href="../man7/openssl_user_macros.html">openssl_user_macros(7)</a>:</p>

<pre><code>void ERR_remove_thread_state(void *tid);</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>ERR_remove_state() frees the error queue associated with the specified thread, identified by <b>tid</b>. ERR_remove_thread_state() does the same thing, except the identifier is an opaque pointer.</p>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>ERR_remove_state() and ERR_remove_thread_state() return no value.</p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p>L<a href="../man3/OPENSSL_init_crypto.html">OPENSSL_init_crypto(3)</a></p>

<h1 id="HISTORY">HISTORY</h1>

<p>ERR_remove_state() was deprecated in OpenSSL 1.0.0 and ERR_remove_thread_state() was deprecated in OpenSSL 1.1.0; these functions and should not be used.</p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2000-2021 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the Apache License 2.0 (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


