<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>SSL_library_init</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:root@localhost" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#NOTES">NOTES</a></li>
  <li><a href="#WARNINGS">WARNINGS</a></li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#HISTORY">HISTORY</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>SSL_library_init, OpenSSL_add_ssl_algorithms - initialize SSL library by registering algorithms</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/ssl.h&gt;

int SSL_library_init(void);

int OpenSSL_add_ssl_algorithms(void);</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>SSL_library_init() registers the available SSL/TLS ciphers and digests.</p>

<p>OpenSSL_add_ssl_algorithms() is a synonym for SSL_library_init() and is implemented as a macro.</p>

<h1 id="NOTES">NOTES</h1>

<p>SSL_library_init() must be called before any other action takes place. SSL_library_init() is not reentrant.</p>

<h1 id="WARNINGS">WARNINGS</h1>

<p>SSL_library_init() adds ciphers and digests used directly and indirectly by SSL/TLS.</p>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>SSL_library_init() always returns &quot;1&quot;, so it is safe to discard the return value.</p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man7/ssl.html">ssl(7)</a>, <a href="../man3/RAND_add.html">RAND_add(3)</a></p>

<h1 id="HISTORY">HISTORY</h1>

<p>The SSL_library_init() and OpenSSL_add_ssl_algorithms() functions were deprecated in OpenSSL 1.1.0 by OPENSSL_init_ssl().</p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2000-2016 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the Apache License 2.0 (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


