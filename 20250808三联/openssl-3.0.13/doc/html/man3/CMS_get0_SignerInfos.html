<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>CMS_get0_SignerInfos</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:root@localhost" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#NOTES">NOTES</a></li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>CMS_SignerInfo_set1_signer_cert, CMS_get0_SignerInfos, CMS_SignerInfo_get0_signer_id, CMS_SignerInfo_get0_signature, CMS_SignerInfo_cert_cmp - CMS signedData signer functions</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/cms.h&gt;

STACK_OF(CMS_SignerInfo) *CMS_get0_SignerInfos(CMS_ContentInfo *cms);

int CMS_SignerInfo_get0_signer_id(CMS_SignerInfo *si, ASN1_OCTET_STRING **keyid,
                                  X509_NAME **issuer, ASN1_INTEGER **sno);
ASN1_OCTET_STRING *CMS_SignerInfo_get0_signature(CMS_SignerInfo *si);
int CMS_SignerInfo_cert_cmp(CMS_SignerInfo *si, X509 *cert);
void CMS_SignerInfo_set1_signer_cert(CMS_SignerInfo *si, X509 *signer);</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>The function CMS_get0_SignerInfos() returns all the CMS_SignerInfo structures associated with a CMS signedData structure.</p>

<p>CMS_SignerInfo_get0_signer_id() retrieves the certificate signer identifier associated with a specific CMS_SignerInfo structure <b>si</b>. Either the keyidentifier will be set in <b>keyid</b> or <b>both</b> issuer name and serial number in <b>issuer</b> and <b>sno</b>.</p>

<p>CMS_SignerInfo_get0_signature() retrieves the signature associated with <b>si</b> in a pointer to an ASN1_OCTET_STRING structure. This pointer returned corresponds to the internal signature value if <b>si</b> so it may be read or modified.</p>

<p>CMS_SignerInfo_cert_cmp() compares the certificate <b>cert</b> against the signer identifier <b>si</b>. It returns zero if the comparison is successful and non zero if not.</p>

<p>CMS_SignerInfo_set1_signer_cert() sets the signers certificate of <b>si</b> to <b>signer</b>.</p>

<h1 id="NOTES">NOTES</h1>

<p>The main purpose of these functions is to enable an application to lookup signers certificates using any appropriate technique when the simpler method of CMS_verify() is not appropriate.</p>

<p>In typical usage and application will retrieve all CMS_SignerInfo structures using CMS_get0_SignerInfo() and retrieve the identifier information using CMS. It will then obtain the signer certificate by some unspecified means (or return and error if it cannot be found) and set it using CMS_SignerInfo_set1_signer_cert().</p>

<p>Once all signer certificates have been set CMS_verify() can be used.</p>

<p>Although CMS_get0_SignerInfos() can return NULL if an error occurs <b>or</b> if there are no signers this is not a problem in practice because the only error which can occur is if the <b>cms</b> structure is not of type signedData due to application error.</p>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>CMS_get0_SignerInfos() returns all CMS_SignerInfo structures, or NULL there are no signers or an error occurs.</p>

<p>CMS_SignerInfo_get0_signer_id() returns 1 for success and 0 for failure.</p>

<p>CMS_SignerInfo_cert_cmp() returns 0 for a successful comparison and non zero otherwise.</p>

<p>CMS_SignerInfo_set1_signer_cert() does not return a value.</p>

<p>Any error can be obtained from <a href="../man3/ERR_get_error.html">ERR_get_error(3)</a></p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man3/ERR_get_error.html">ERR_get_error(3)</a>, <a href="../man3/CMS_verify.html">CMS_verify(3)</a></p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2008-2018 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the Apache License 2.0 (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


