<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>OSSL_PROVIDER</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:root@localhost" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a>
    <ul>
      <li><a href="#Functions">Functions</a></li>
    </ul>
  </li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#EXAMPLES">EXAMPLES</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#HISTORY">HISTORY</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>OSSL_PROVIDER_set_default_search_path, OSSL_PROVIDER, OSSL_PROVIDER_load, OSSL_PROVIDER_try_load, OSSL_PROVIDER_unload, OSSL_PROVIDER_available, OSSL_PROVIDER_do_all, OSSL_PROVIDER_gettable_params, OSSL_PROVIDER_get_params, OSSL_PROVIDER_query_operation, OSSL_PROVIDER_unquery_operation, OSSL_PROVIDER_get0_provider_ctx, OSSL_PROVIDER_get0_dispatch, OSSL_PROVIDER_add_builtin, OSSL_PROVIDER_get0_name, OSSL_PROVIDER_get_capabilities, OSSL_PROVIDER_self_test - provider routines</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/provider.h&gt;

typedef struct ossl_provider_st OSSL_PROVIDER;

int OSSL_PROVIDER_set_default_search_path(OSSL_LIB_CTX *libctx,
                                          const char *path);

OSSL_PROVIDER *OSSL_PROVIDER_load(OSSL_LIB_CTX *libctx, const char *name);
OSSL_PROVIDER *OSSL_PROVIDER_try_load(OSSL_LIB_CTX *libctx, const char *name,
                                      int retain_fallbacks);
int OSSL_PROVIDER_unload(OSSL_PROVIDER *prov);
int OSSL_PROVIDER_available(OSSL_LIB_CTX *libctx, const char *name);
int OSSL_PROVIDER_do_all(OSSL_LIB_CTX *ctx,
                         int (*cb)(OSSL_PROVIDER *provider, void *cbdata),
                         void *cbdata);

const OSSL_PARAM *OSSL_PROVIDER_gettable_params(OSSL_PROVIDER *prov);
int OSSL_PROVIDER_get_params(OSSL_PROVIDER *prov, OSSL_PARAM params[]);

const OSSL_ALGORITHM *OSSL_PROVIDER_query_operation(const OSSL_PROVIDER *prov,
                                                    int operation_id,
                                                    int *no_cache);
void OSSL_PROVIDER_unquery_operation(const OSSL_PROVIDER *prov,
                                     int operation_id,
                                     const OSSL_ALGORITHM *algs);
void *OSSL_PROVIDER_get0_provider_ctx(const OSSL_PROVIDER *prov);
const OSSL_DISPATCH *OSSL_PROVIDER_get0_dispatch(const OSSL_PROVIDER *prov);

int OSSL_PROVIDER_add_builtin(OSSL_LIB_CTX *libctx, const char *name,
                              ossl_provider_init_fn *init_fn);

const char *OSSL_PROVIDER_get0_name(const OSSL_PROVIDER *prov);

int OSSL_PROVIDER_get_capabilities(const OSSL_PROVIDER *prov,
                                   const char *capability,
                                   OSSL_CALLBACK *cb,
                                   void *arg);
int OSSL_PROVIDER_self_test(const OSSL_PROVIDER *prov);</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p><b>OSSL_PROVIDER</b> is a type that holds internal information about implementation providers (see <a href="../man7/provider.html">provider(7)</a> for information on what a provider is). A provider can be built in to the application or the OpenSSL libraries, or can be a loadable module. The functions described here handle both forms.</p>

<p>Some of these functions operate within a library context, please see <a href="../man3/OSSL_LIB_CTX.html">OSSL_LIB_CTX(3)</a> for further details.</p>

<h2 id="Functions">Functions</h2>

<p>OSSL_PROVIDER_set_default_search_path() specifies the default search <i>path</i> that is to be used for looking for providers in the specified <i>libctx</i>. If left unspecified, an environment variable and a fall back default value will be used instead.</p>

<p>OSSL_PROVIDER_add_builtin() is used to add a built in provider to <b>OSSL_PROVIDER</b> store in the given library context, by associating a provider name with a provider initialization function. This name can then be used with OSSL_PROVIDER_load().</p>

<p>OSSL_PROVIDER_load() loads and initializes a provider. This may simply initialize a provider that was previously added with OSSL_PROVIDER_add_builtin() and run its given initialization function, or load a provider module with the given name and run its provider entry point, <code>OSSL_provider_init</code>. The <i>name</i> can be a path to a provider module, in that case the provider name as returned by OSSL_PROVIDER_get0_name() will be the path. Interpretation of relative paths is platform dependent and they are relative to the configured &quot;MODULESDIR&quot; directory or the path set in the environment variable OPENSSL_MODULES if set.</p>

<p>OSSL_PROVIDER_try_load() functions like OSSL_PROVIDER_load(), except that it does not disable the fallback providers if the provider cannot be loaded and initialized or if <i>retain_fallbacks</i> is nonzero. If the provider loads successfully and <i>retain_fallbacks</i> is zero, the fallback providers are disabled.</p>

<p>OSSL_PROVIDER_unload() unloads the given provider. For a provider added with OSSL_PROVIDER_add_builtin(), this simply runs its teardown function.</p>

<p>OSSL_PROVIDER_available() checks if a named provider is available for use.</p>

<p>OSSL_PROVIDER_do_all() iterates over all loaded providers, calling <i>cb</i> for each one, with the current provider in <i>provider</i> and the <i>cbdata</i> that comes from the caller. If no other provider has been loaded before calling this function, the default provider is still available as fallback. See <a href="../man7/OSSL_PROVIDER-default.html">OSSL_PROVIDER-default(7)</a> for more information on this fallback behaviour.</p>

<p>OSSL_PROVIDER_gettable_params() is used to get a provider parameter descriptor set as a constant <a href="../man3/OSSL_PARAM.html">OSSL_PARAM(3)</a> array.</p>

<p>OSSL_PROVIDER_get_params() is used to get provider parameter values. The caller must prepare the <a href="../man3/OSSL_PARAM.html">OSSL_PARAM(3)</a> array before calling this function, and the variables acting as buffers for this parameter array should be filled with data when it returns successfully.</p>

<p>OSSL_PROVIDER_self_test() is used to run a provider&#39;s self tests on demand. If the self tests fail then the provider will fail to provide any further services and algorithms. <a href="../man3/OSSL_SELF_TEST_set_callback.html">OSSL_SELF_TEST_set_callback(3)</a> may be called beforehand in order to display diagnostics for the running self tests.</p>

<p>OSSL_PROVIDER_query_operation() calls the provider&#39;s <i>query_operation</i> function (see <a href="../man7/provider.html">provider(7)</a>), if the provider has one. It returns an array of <i>OSSL_ALGORITHM</i> for the given <i>operation_id</i> terminated by an all NULL OSSL_ALGORITHM entry. This is considered a low-level function that most applications should not need to call.</p>

<p>OSSL_PROVIDER_unquery_operation() calls the provider&#39;s <i>unquery_operation</i> function (see <a href="../man7/provider.html">provider(7)</a>), if the provider has one. This is considered a low-level function that most applications should not need to call.</p>

<p>OSSL_PROVIDER_get0_provider_ctx() returns the provider context for the given provider. The provider context is an opaque handle set by the provider itself and is passed back to the provider by libcrypto in various function calls.</p>

<p>OSSL_PROVIDER_get0_dispatch() returns the provider&#39;s dispatch table as it was returned in the <i>out</i> parameter from the provider&#39;s init function. See <a href="../man7/provider-base.html">provider-base(7)</a>.</p>

<p>If it is permissible to cache references to this array then <i>*no_store</i> is set to 0 or 1 otherwise. If the array is not cacheable then it is assumed to have a short lifetime.</p>

<p>OSSL_PROVIDER_get0_name() returns the name of the given provider.</p>

<p>OSSL_PROVIDER_get_capabilities() provides information about the capabilities supported by the provider specified in <i>prov</i> with the capability name <i>capability</i>. For each capability of that name supported by the provider it will call the callback <i>cb</i> and supply a set of <a href="../man3/OSSL_PARAM.html">OSSL_PARAM(3)</a>s describing the capability. It will also pass back the argument <i>arg</i>. For more details about capabilities and what they can be used for please see <a href="../man7/provider-base.html">&quot;CAPABILTIIES&quot; in provider-base(7)</a>.</p>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>OSSL_PROVIDER_set_default_search_path(), OSSL_PROVIDER_add(), OSSL_PROVIDER_unload(), OSSL_PROVIDER_get_params() and OSSL_PROVIDER_get_capabilities() return 1 on success, or 0 on error.</p>

<p>OSSL_PROVIDER_load() and OSSL_PROVIDER_try_load() return a pointer to a provider object on success, or NULL on error.</p>

<p>OSSL_PROVIDER_do_all() returns 1 if the callback <i>cb</i> returns 1 for every provider it is called with, or 0 if any provider callback invocation returns 0; callback processing stops at the first callback invocation on a provider that returns 0.</p>

<p>OSSL_PROVIDER_available() returns 1 if the named provider is available, otherwise 0.</p>

<p>OSSL_PROVIDER_gettable_params() returns a pointer to an array of constant <a href="../man3/OSSL_PARAM.html">OSSL_PARAM(3)</a>, or NULL if none is provided.</p>

<p>OSSL_PROVIDER_get_params() and returns 1 on success, or 0 on error.</p>

<p>OSSL_PROVIDER_query_operation() returns an array of OSSL_ALGORITHM or NULL on error.</p>

<p>OSSL_PROVIDER_self_test() returns 1 if the self tests pass, or 0 on error.</p>

<h1 id="EXAMPLES">EXAMPLES</h1>

<p>This demonstrates how to load the provider module &quot;foo&quot; and ask for its build information.</p>

<pre><code>#include &lt;openssl/params.h&gt;
#include &lt;openssl/provider.h&gt;
#include &lt;openssl/err.h&gt;

OSSL_PROVIDER *prov = NULL;
const char *build = NULL;
OSSL_PARAM request[] = {
    { &quot;buildinfo&quot;, OSSL_PARAM_UTF8_PTR, &amp;build, 0, 0 },
    { NULL, 0, NULL, 0, 0 }
};

if ((prov = OSSL_PROVIDER_load(NULL, &quot;foo&quot;)) != NULL
    &amp;&amp; OSSL_PROVIDER_get_params(prov, request))
    printf(&quot;Provider &#39;foo&#39; buildinfo: %s\n&quot;, build);
else
    ERR_print_errors_fp(stderr);</code></pre>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man7/openssl-core.h.html">openssl-core.h(7)</a>, <a href="../man3/OSSL_LIB_CTX.html">OSSL_LIB_CTX(3)</a>, <a href="../man7/provider.html">provider(7)</a></p>

<h1 id="HISTORY">HISTORY</h1>

<p>The type and functions described here were added in OpenSSL 3.0.</p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2019-2023 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the Apache License 2.0 (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


