<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>ERR_load_strings</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:root@localhost" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>ERR_load_strings, ERR_PACK, ERR_get_next_error_library - load arbitrary error strings</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/err.h&gt;

int ERR_load_strings(int lib, ERR_STRING_DATA *str);

int ERR_get_next_error_library(void);

unsigned long ERR_PACK(int lib, int func, int reason);</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>ERR_load_strings() registers error strings for library number <b>lib</b>.</p>

<p><b>str</b> is an array of error string data:</p>

<pre><code>typedef struct ERR_string_data_st
{
    unsigned long error;
    char *string;
} ERR_STRING_DATA;</code></pre>

<p>The error code is generated from the library number and a function and reason code: <b>error</b> = ERR_PACK(<b>lib</b>, <b>func</b>, <b>reason</b>). ERR_PACK() is a macro.</p>

<p>The last entry in the array is {0,0}.</p>

<p>ERR_get_next_error_library() can be used to assign library numbers to user libraries at run time.</p>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>ERR_load_strings() returns 1 for success and 0 for failure. ERR_PACK() returns the error code. ERR_get_next_error_library() returns zero on failure, otherwise a new library number.</p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man3/ERR_load_strings.html">ERR_load_strings(3)</a></p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2000-2021 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the Apache License 2.0 (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


