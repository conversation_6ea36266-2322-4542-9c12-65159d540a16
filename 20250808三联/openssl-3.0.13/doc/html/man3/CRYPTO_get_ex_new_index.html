<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>CRYPTO_get_ex_new_index</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:root@localhost" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a>
    <ul>
      <li><a href="#Callback-Functions">Callback Functions</a></li>
    </ul>
  </li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#HISTORY">HISTORY</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>CRYPTO_EX_new, CRYPTO_EX_free, CRYPTO_EX_dup, CRYPTO_free_ex_index, CRYPTO_get_ex_new_index, CRYPTO_alloc_ex_data, CRYPTO_set_ex_data, CRYPTO_get_ex_data, CRYPTO_free_ex_data, CRYPTO_new_ex_data - functions supporting application-specific data</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/crypto.h&gt;

int CRYPTO_get_ex_new_index(int class_index,
                            long argl, void *argp,
                            CRYPTO_EX_new *new_func,
                            CRYPTO_EX_dup *dup_func,
                            CRYPTO_EX_free *free_func);

typedef void CRYPTO_EX_new(void *parent, void *ptr, CRYPTO_EX_DATA *ad,
                           int idx, long argl, void *argp);
typedef void CRYPTO_EX_free(void *parent, void *ptr, CRYPTO_EX_DATA *ad,
                            int idx, long argl, void *argp);
typedef int CRYPTO_EX_dup(CRYPTO_EX_DATA *to, const CRYPTO_EX_DATA *from,
                          void **from_d, int idx, long argl, void *argp);

int CRYPTO_new_ex_data(int class_index, void *obj, CRYPTO_EX_DATA *ad);

int CRYPTO_alloc_ex_data(int class_index, void *obj, CRYPTO_EX_DATA *ad,
                         int idx);

int CRYPTO_set_ex_data(CRYPTO_EX_DATA *r, int idx, void *arg);

void *CRYPTO_get_ex_data(const CRYPTO_EX_DATA *r, int idx);

void CRYPTO_free_ex_data(int class_index, void *obj, CRYPTO_EX_DATA *r);

int CRYPTO_free_ex_index(int class_index, int idx);</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>Several OpenSSL structures can have application-specific data attached to them, known as &quot;exdata.&quot; The specific structures are:</p>

<pre><code>BIO
DH
DSA
EC_KEY
ENGINE
EVP_PKEY
RSA
SSL
SSL_CTX
SSL_SESSION
UI
UI_METHOD
X509
X509_STORE
X509_STORE_CTX</code></pre>

<p>In addition, the <b>APP</b> name is reserved for use by application code.</p>

<p>Each is identified by an <b>CRYPTO_EX_INDEX_xxx</b> define in the header file <i>&lt;openssl/crypto.h&gt;</i>. In addition, <b>CRYPTO_EX_INDEX_APP</b> is reserved for applications to use this facility for their own structures.</p>

<p>The API described here is used by OpenSSL to manipulate exdata for specific structures. Since the application data can be anything at all it is passed and retrieved as a <b>void *</b> type.</p>

<p>The <b>CRYPTO_EX_DATA</b> type is opaque. To initialize the exdata part of a structure, call CRYPTO_new_ex_data(). This is only necessary for <b>CRYPTO_EX_INDEX_APP</b> objects.</p>

<p>Exdata types are identified by an <b>index</b>, an integer guaranteed to be unique within structures for the lifetime of the program. Applications using exdata typically call <b>CRYPTO_get_ex_new_index</b> at startup, and store the result in a global variable, or write a wrapper function to provide lazy evaluation. The <b>class_index</b> should be one of the <b>CRYPTO_EX_INDEX_xxx</b> values. The <b>argl</b> and <b>argp</b> parameters are saved to be passed to the callbacks but are otherwise not used. In order to transparently manipulate exdata, three callbacks must be provided. The semantics of those callbacks are described below.</p>

<p>When copying or releasing objects with exdata, the callback functions are called in increasing order of their <b>index</b> value.</p>

<p>If a dynamic library can be unloaded, it should call CRYPTO_free_ex_index() when this is done. This will replace the callbacks with no-ops so that applications don&#39;t crash. Any existing exdata will be leaked.</p>

<p>To set or get the exdata on an object, the appropriate type-specific routine must be used. This is because the containing structure is opaque and the <b>CRYPTO_EX_DATA</b> field is not accessible. In both API&#39;s, the <b>idx</b> parameter should be an already-created index value.</p>

<p>When setting exdata, the pointer specified with a particular index is saved, and returned on a subsequent &quot;get&quot; call. If the application is going to release the data, it must make sure to set a <b>NULL</b> value at the index, to avoid likely double-free crashes.</p>

<p>The function <b>CRYPTO_free_ex_data</b> is used to free all exdata attached to a structure. The appropriate type-specific routine must be used. The <b>class_index</b> identifies the structure type, the <b>obj</b> is a pointer to the actual structure, and <b>r</b> is a pointer to the structure&#39;s exdata field.</p>

<h2 id="Callback-Functions">Callback Functions</h2>

<p>This section describes how the callback functions are used. Applications that are defining their own exdata using <b>CYPRTO_EX_INDEX_APP</b> must call them as described here.</p>

<p>When a structure is initially allocated (such as RSA_new()) then the new_func() is called for every defined index. There is no requirement that the entire parent, or containing, structure has been set up. The new_func() is typically used only to allocate memory to store the exdata, and perhaps an &quot;initialized&quot; flag within that memory. The exdata value may be allocated later on with CRYPTO_alloc_ex_data(), or may be set by calling CRYPTO_set_ex_data().</p>

<p>When a structure is free&#39;d (such as SSL_CTX_free()) then the free_func() is called for every defined index. Again, the state of the parent structure is not guaranteed. The free_func() may be called with a NULL pointer.</p>

<p>Both new_func() and free_func() take the same parameters. The <b>parent</b> is the pointer to the structure that contains the exdata. The <b>ptr</b> is the current exdata item; for new_func() this will typically be NULL. The <b>r</b> parameter is a pointer to the exdata field of the object. The <b>idx</b> is the index and is the value returned when the callbacks were initially registered via CRYPTO_get_ex_new_index() and can be used if the same callback handles different types of exdata.</p>

<p>dup_func() is called when a structure is being copied. This is only done for <b>SSL</b>, <b>SSL_SESSION</b>, <b>EC_KEY</b> objects and <b>BIO</b> chains via BIO_dup_chain(). The <b>to</b> and <b>from</b> parameters are pointers to the destination and source <b>CRYPTO_EX_DATA</b> structures, respectively. The <b>*from_d</b> parameter is a pointer to the source exdata. When the dup_func() returns, the value in <b>*from_d</b> is copied to the destination ex_data. If the pointer contained in <b>*pptr</b> is not modified by the dup_func(), then both <b>to</b> and <b>from</b> will point to the same data. The <b>idx</b>, <b>argl</b> and <b>argp</b> parameters are as described for the other two callbacks. If the dup_func() returns <b>0</b> the whole CRYPTO_dup_ex_data() will fail.</p>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>CRYPTO_get_ex_new_index() returns a new index or -1 on failure.</p>

<p>CRYPTO_free_ex_index(), CRYPTO_alloc_ex_data() and CRYPTO_set_ex_data() return 1 on success or 0 on failure.</p>

<p>CRYPTO_get_ex_data() returns the application data or NULL on failure; note that NULL may be a valid value.</p>

<p>dup_func() should return 0 for failure and 1 for success.</p>

<h1 id="HISTORY">HISTORY</h1>

<p>CRYPTO_alloc_ex_data() was added in OpenSSL 3.0.</p>

<p>The signature of the dup_func() callback was changed in OpenSSL 3.0 to use the type <b>void **</b> for <b>from_d</b>. Previously this parameter was of type <b>void *</b>.</p>

<p>Support for ENGINE &quot;exdata&quot; was deprecated in OpenSSL 3.0.</p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2015-2021 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the Apache License 2.0 (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


