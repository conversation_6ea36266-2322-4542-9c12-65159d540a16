<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>X509_get0_distinguishing_id</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:root@localhost" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>X509_get0_distinguishing_id, X509_set0_distinguishing_id, X509_REQ_get0_distinguishing_id, X509_REQ_set0_distinguishing_id - get or set the Distinguishing ID for certificate operations</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/x509.h&gt;

ASN1_OCTET_STRING *X509_get0_distinguishing_id(X509 *x);
void X509_set0_distinguishing_id(X509 *x, ASN1_OCTET_STRING *distid);
ASN1_OCTET_STRING *X509_REQ_get0_distinguishing_id(X509_REQ *x);
void X509_REQ_set0_distinguishing_id(X509_REQ *x, ASN1_OCTET_STRING *distid);</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>The Distinguishing ID is defined in FIPS 196 as follows:</p>

<dl>

<dt id="Distinguishing-identifier"><i>Distinguishing identifier</i></dt>
<dd>

<p>Information which unambiguously distinguishes an entity in the authentication process.</p>

</dd>
</dl>

<p>The SM2 signature algorithm requires a Distinguishing ID value when generating and verifying a signature, but the Ddistinguishing ID may also find other uses. In the context of SM2, the Distinguishing ID is often referred to as the &quot;SM2 ID&quot;.</p>

<p>For the purpose off verifying a certificate or a certification request, a Distinguishing ID may be attached to it, so functions like <a href="../man3/X509_verify.html">X509_verify(3)</a> or <a href="../man3/X509_REQ_verify.html">X509_REQ_verify(3)</a> have easy access to that identity for signature verification.</p>

<p>X509_get0_distinguishing_id() gets the Distinguishing ID value of a certificate <b>x</b> by returning an <b>ASN1_OCTET_STRING</b> object which should not be freed by the caller.</p>

<p>X509_set0_distinguishing_id() assigns <b>distid</b> to the certificate <b>x</b>. Calling this function transfers the memory management of the value to the X509 object, and therefore the value that has been passed in should not be freed by the caller after this function has been called.</p>

<p>X509_REQ_get0_distinguishing_id() and X509_REQ_set0_distinguishing_id() have the same functionality as X509_get0_distinguishing_id() and X509_set0_distinguishing_id() except that they deal with <b>X509_REQ</b> objects instead of <b>X509</b>.</p>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>X509_set0_distinguishing_id() and X509_REQ_set0_distinguishing_id() do not return a value.</p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man3/X509_verify.html">X509_verify(3)</a>, <a href="../man7/SM2.html">SM2(7)</a></p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2019-2020 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the Apache License 2.0 (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


