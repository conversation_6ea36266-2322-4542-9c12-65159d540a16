<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>SSL_CONF_CTX_set1_prefix</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:root@localhost" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#NOTES">NOTES</a></li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#HISTORY">HISTORY</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>SSL_CONF_CTX_set1_prefix - Set configuration context command prefix</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/ssl.h&gt;

unsigned int SSL_CONF_CTX_set1_prefix(SSL_CONF_CTX *cctx, const char *prefix);</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>The function SSL_CONF_CTX_set1_prefix() sets the command prefix of <b>cctx</b> to <b>prefix</b>. If <b>prefix</b> is <b>NULL</b> it is restored to the default value.</p>

<h1 id="NOTES">NOTES</h1>

<p>Command prefixes alter the commands recognised by subsequent SSL_CONF_cmd() calls. For example for files, if the prefix &quot;SSL&quot; is set then command names such as &quot;SSLProtocol&quot;, &quot;SSLOptions&quot; etc. are recognised instead of &quot;Protocol&quot; and &quot;Options&quot;. Similarly for command lines if the prefix is &quot;--ssl-&quot; then &quot;--ssl-no_tls1_2&quot; is recognised instead of &quot;-no_tls1_2&quot;.</p>

<p>If the <b>SSL_CONF_FLAG_CMDLINE</b> flag is set then prefix checks are case sensitive and &quot;-&quot; is the default. In the unlikely even an application explicitly wants to set no prefix it must be explicitly set to &quot;&quot;.</p>

<p>If the <b>SSL_CONF_FLAG_FILE</b> flag is set then prefix checks are case insensitive and no prefix is the default.</p>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>SSL_CONF_CTX_set1_prefix() returns 1 for success and 0 for failure.</p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man7/ssl.html">ssl(7)</a>, <a href="../man3/SSL_CONF_CTX_new.html">SSL_CONF_CTX_new(3)</a>, <a href="../man3/SSL_CONF_CTX_set_flags.html">SSL_CONF_CTX_set_flags(3)</a>, <a href="../man3/SSL_CONF_CTX_set_ssl_ctx.html">SSL_CONF_CTX_set_ssl_ctx(3)</a>, <a href="../man3/SSL_CONF_cmd.html">SSL_CONF_cmd(3)</a>, <a href="../man3/SSL_CONF_cmd_argv.html">SSL_CONF_cmd_argv(3)</a></p>

<h1 id="HISTORY">HISTORY</h1>

<p>These functions were added in OpenSSL 1.0.2.</p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2012-2016 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the Apache License 2.0 (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


