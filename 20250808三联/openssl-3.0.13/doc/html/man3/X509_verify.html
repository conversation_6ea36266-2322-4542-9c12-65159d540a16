<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>X509_verify</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:root@localhost" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#HISTORY">HISTORY</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>X509_verify, X509_self_signed, X509_REQ_verify_ex, X509_REQ_verify, X509_CRL_verify - verify certificate, certificate request, or CRL signature</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/x509.h&gt;

int X509_verify(X509 *x, EVP_PKEY *pkey);
int X509_self_signed(X509 *cert, int verify_signature);

int X509_REQ_verify_ex(X509_REQ *a, EVP_PKEY *pkey, OSSL_LIB_CTX *libctx,
                       const char *propq);
int X509_REQ_verify(X509_REQ *a, EVP_PKEY *r);
int X509_CRL_verify(X509_CRL *a, EVP_PKEY *r);</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>X509_verify() verifies the signature of certificate <i>x</i> using public key <i>pkey</i>. Only the signature is checked: no other checks (such as certificate chain validity) are performed.</p>

<p>X509_self_signed() checks whether certificate <i>cert</i> is self-signed. For success the issuer and subject names must match, the components of the authority key identifier (if present) must match the subject key identifier etc. The signature itself is actually verified only if <b>verify_signature</b> is 1, as for explicitly trusted certificates this verification is not worth the effort.</p>

<p>X509_REQ_verify_ex(), X509_REQ_verify() and X509_CRL_verify() verify the signatures of certificate requests and CRLs, respectively.</p>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>X509_verify(), X509_REQ_verify_ex(), X509_REQ_verify() and X509_CRL_verify() return 1 if the signature is valid and 0 if the signature check fails. If the signature could not be checked at all because it was ill-formed, the certificate or the request was not complete or some other error occurred then -1 is returned.</p>

<p>X509_self_signed() returns the same values but also returns 1 if all respective fields match and <b>verify_signature</b> is 0.</p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man3/d2i_X509.html">d2i_X509(3)</a>, <a href="../man3/ERR_get_error.html">ERR_get_error(3)</a>, <a href="../man3/X509_CRL_get0_by_serial.html">X509_CRL_get0_by_serial(3)</a>, <a href="../man3/X509_get0_signature.html">X509_get0_signature(3)</a>, <a href="../man3/X509_get_ext_d2i.html">X509_get_ext_d2i(3)</a>, <a href="../man3/X509_get_extension_flags.html">X509_get_extension_flags(3)</a>, <a href="../man3/X509_get_pubkey.html">X509_get_pubkey(3)</a>, <a href="../man3/X509_get_subject_name.html">X509_get_subject_name(3)</a>, <a href="../man3/X509_get_version.html">X509_get_version(3)</a>, <a href="../man3/X509_NAME_ENTRY_get_object.html">X509_NAME_ENTRY_get_object(3)</a>, <a href="../man3/X509_NAME_get_index_by_NID.html">X509_NAME_get_index_by_NID(3)</a>, <a href="../man3/X509_NAME_print_ex.html">X509_NAME_print_ex(3)</a>, <a href="../man3/X509V3_get_d2i.html">X509V3_get_d2i(3)</a>, <a href="../man3/X509_verify_cert.html">X509_verify_cert(3)</a>, <a href="../man3/OSSL_LIB_CTX.html">OSSL_LIB_CTX(3)</a></p>

<h1 id="HISTORY">HISTORY</h1>

<p>The X509_verify(), X509_REQ_verify(), and X509_CRL_verify() functions are available in all versions of OpenSSL.</p>

<p>X509_REQ_verify_ex(), and X509_self_signed() were added in OpenSSL 3.0.</p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2015-2021 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the Apache License 2.0 (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


