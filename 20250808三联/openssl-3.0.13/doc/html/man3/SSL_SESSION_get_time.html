<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>SSL_SESSION_get_time</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:root@localhost" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#NOTES">NOTES</a></li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>SSL_SESSION_get_time, SSL_SESSION_set_time, SSL_SESSION_get_timeout, SSL_SESSION_set_timeout, SSL_get_time, SSL_set_time, SSL_get_timeout, SSL_set_timeout - retrieve and manipulate session time and timeout settings</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/ssl.h&gt;

long SSL_SESSION_get_time(const SSL_SESSION *s);
long SSL_SESSION_set_time(SSL_SESSION *s, long tm);
long SSL_SESSION_get_timeout(const SSL_SESSION *s);
long SSL_SESSION_set_timeout(SSL_SESSION *s, long tm);

long SSL_get_time(const SSL_SESSION *s);
long SSL_set_time(SSL_SESSION *s, long tm);
long SSL_get_timeout(const SSL_SESSION *s);
long SSL_set_timeout(SSL_SESSION *s, long tm);</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>SSL_SESSION_get_time() returns the time at which the session <b>s</b> was established. The time is given in seconds since the Epoch and therefore compatible to the time delivered by the time() call.</p>

<p>SSL_SESSION_set_time() replaces the creation time of the session <b>s</b> with the chosen value <b>tm</b>.</p>

<p>SSL_SESSION_get_timeout() returns the timeout value set for session <b>s</b> in seconds.</p>

<p>SSL_SESSION_set_timeout() sets the timeout value for session <b>s</b> in seconds to <b>tm</b>.</p>

<p>The SSL_get_time(), SSL_set_time(), SSL_get_timeout(), and SSL_set_timeout() functions are synonyms for the SSL_SESSION_*() counterparts.</p>

<h1 id="NOTES">NOTES</h1>

<p>Sessions are expired by examining the creation time and the timeout value. Both are set at creation time of the session to the actual time and the default timeout value at creation, respectively, as set by <a href="../man3/SSL_CTX_set_timeout.html">SSL_CTX_set_timeout(3)</a>. Using these functions it is possible to extend or shorten the lifetime of the session.</p>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>SSL_SESSION_get_time() and SSL_SESSION_get_timeout() return the currently valid values.</p>

<p>SSL_SESSION_set_time() and SSL_SESSION_set_timeout() return 1 on success.</p>

<p>If any of the function is passed the NULL pointer for the session <b>s</b>, 0 is returned.</p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man7/ssl.html">ssl(7)</a>, <a href="../man3/SSL_CTX_set_timeout.html">SSL_CTX_set_timeout(3)</a>, <a href="../man3/SSL_get_default_timeout.html">SSL_get_default_timeout(3)</a></p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2001-2016 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the Apache License 2.0 (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


