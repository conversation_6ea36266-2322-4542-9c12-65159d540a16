<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>X509_STORE_new</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:root@localhost" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#HISTORY">HISTORY</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>X509_STORE_new, X509_STORE_up_ref, X509_STORE_free, X509_STORE_lock,X509_STORE_unlock - X509_STORE allocation, freeing and locking functions</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/x509_vfy.h&gt;

X509_STORE *X509_STORE_new(void);
void X509_STORE_free(X509_STORE *v);
int X509_STORE_lock(X509_STORE *v);
int X509_STORE_unlock(X509_STORE *v);
int X509_STORE_up_ref(X509_STORE *v);</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>The X509_STORE_new() function returns a new X509_STORE.</p>

<p>X509_STORE_up_ref() increments the reference count associated with the X509_STORE object.</p>

<p>X509_STORE_lock() locks the store from modification by other threads, X509_STORE_unlock() unlocks it.</p>

<p>X509_STORE_free() frees up a single X509_STORE object.</p>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>X509_STORE_new() returns a newly created X509_STORE or NULL if the call fails.</p>

<p>X509_STORE_up_ref(), X509_STORE_lock() and X509_STORE_unlock() return 1 for success and 0 for failure.</p>

<p>X509_STORE_free() does not return values.</p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man3/X509_STORE_set_verify_cb_func.html">X509_STORE_set_verify_cb_func(3)</a> <a href="../man3/X509_STORE_get0_param.html">X509_STORE_get0_param(3)</a></p>

<h1 id="HISTORY">HISTORY</h1>

<p>The X509_STORE_up_ref(), X509_STORE_lock() and X509_STORE_unlock() functions were added in OpenSSL 1.1.0.</p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2016-2020 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the Apache License 2.0 (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


