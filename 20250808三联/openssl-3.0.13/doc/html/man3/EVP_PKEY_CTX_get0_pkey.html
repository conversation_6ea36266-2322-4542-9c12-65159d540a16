<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>EVP_PKEY_CTX_get0_pkey</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:root@localhost" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>EVP_PKEY_CTX_get0_pkey, EVP_PKEY_CTX_get0_peerkey - functions for accessing the EVP_PKEY associated with an EVP_PKEY_CTX</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/evp.h&gt;

EVP_PKEY *EVP_PKEY_CTX_get0_pkey(EVP_PKEY_CTX *ctx);
EVP_PKEY *EVP_PKEY_CTX_get0_peerkey(EVP_PKEY_CTX *ctx);</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>EVP_PKEY_CTX_get0_pkey() is used to access the <b>EVP_PKEY</b> associated with the given <b>EVP_PKEY_CTX</b> <i>ctx</i>. The <b>EVP_PKEY</b> obtained is the one used for creating the <b>EVP_PKEY_CTX</b> using either <a href="../man3/EVP_PKEY_CTX_new.html">EVP_PKEY_CTX_new(3)</a> or <a href="../man3/EVP_PKEY_CTX_new_from_pkey.html">EVP_PKEY_CTX_new_from_pkey(3)</a>.</p>

<p>EVP_PKEY_CTX_get0_peerkey() is used to access the peer <b>EVP_PKEY</b> associated with the given <b>EVP_PKEY_CTX</b> <i>ctx</i>. The peer <b>EVP_PKEY</b> obtained is the one set using either <a href="../man3/EVP_PKEY_derive_set_peer.html">EVP_PKEY_derive_set_peer(3)</a> or <a href="../man3/EVP_PKEY_derive_set_peer_ex.html">EVP_PKEY_derive_set_peer_ex(3)</a>.</p>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>EVP_PKEY_CTX_get0_pkey() returns the <b>EVP_PKEY</b> associated with the EVP_PKEY_CTX or NULL if it is not set.</p>

<p>EVP_PKEY_CTX_get0_peerkey() returns the peer <b>EVP_PKEY</b> associated with the EVP_PKEY_CTX or NULL if it is not set.</p>

<p>The returned EVP_PKEY objects are owned by the EVP_PKEY_CTX, and therefore should not explicitly be freed by the caller.</p>

<p>These functions do not affect the EVP_PKEY reference count. They merely act as getter functions, and should be treated as such.</p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man3/EVP_PKEY_CTX_new.html">EVP_PKEY_CTX_new(3)</a>, <a href="../man3/EVP_PKEY_CTX_new_from_pkey.html">EVP_PKEY_CTX_new_from_pkey(3)</a>, <a href="../man3/EVP_PKEY_derive_set_peer.html">EVP_PKEY_derive_set_peer(3)</a>, <a href="../man3/EVP_PKEY_derive_set_peer_ex.html">EVP_PKEY_derive_set_peer_ex(3)</a></p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2022-2023 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the Apache License 2.0 (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


