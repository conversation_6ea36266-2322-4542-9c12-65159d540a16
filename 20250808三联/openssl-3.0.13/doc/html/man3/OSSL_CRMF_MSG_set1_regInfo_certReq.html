<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>OSSL_CRMF_MSG_set1_regInfo_certReq</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:root@localhost" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#NOTES">NOTES</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#HISTORY">HISTORY</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>OSSL_CRMF_MSG_get0_regInfo_utf8Pairs, OSSL_CRMF_MSG_set1_regInfo_utf8Pairs, OSSL_CRMF_MSG_get0_regInfo_certReq, OSSL_CRMF_MSG_set1_regInfo_certReq - functions getting or setting CRMF Registration Info</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/crmf.h&gt;

ASN1_UTF8STRING
    *OSSL_CRMF_MSG_get0_regInfo_utf8Pairs(const OSSL_CRMF_MSG *msg);
int OSSL_CRMF_MSG_set1_regInfo_utf8Pairs(OSSL_CRMF_MSG *msg,
                                         const ASN1_UTF8STRING *utf8pairs);
OSSL_CRMF_CERTREQUEST
    *OSSL_CRMF_MSG_get0_regInfo_certReq(const OSSL_CRMF_MSG *msg);
int OSSL_CRMF_MSG_set1_regInfo_certReq(OSSL_CRMF_MSG *msg,
                                       const OSSL_CRMF_CERTREQUEST *cr);</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>OSSL_CRMF_MSG_get0_regInfo_utf8Pairs() returns the first utf8Pairs regInfo in the given <i>msg</i>, if present.</p>

<p>OSSL_CRMF_MSG_set1_regInfo_utf8Pairs() adds a copy of the given <i>utf8pairs</i> value as utf8Pairs regInfo to the given <i>msg</i>. See RFC 4211 section 7.1.</p>

<p>OSSL_CRMF_MSG_get0_regInfo_certReq() returns the first certReq regInfo in the given <i>msg</i>, if present.</p>

<p>OSSL_CRMF_MSG_set1_regInfo_certReq() adds a copy of the given <i>cr</i> value as certReq regInfo to the given <i>msg</i>. See RFC 4211 section 7.2.</p>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>All get0_*() functions return the respective pointer value, NULL if not present.</p>

<p>All set1_*() functions return 1 on success, 0 on error.</p>

<h1 id="NOTES">NOTES</h1>

<p>Calling the set1_*() functions multiple times adds multiple instances of the respective control to the regInfo structure of the given <i>msg</i>. While RFC 4211 expects multiple utf8Pairs in one regInfo structure, it does not allow multiple certReq.</p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p>RFC 4211</p>

<h1 id="HISTORY">HISTORY</h1>

<p>The OpenSSL CRMF support was added in OpenSSL 3.0.</p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2007-2021 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the Apache License 2.0 (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


