<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>EVP_PKEY_settable_params</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:root@localhost" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#NOTES">NOTES</a></li>
  <li><a href="#RETURN-VALUES">RETURN VALUES</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#HISTORY">HISTORY</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>EVP_PKEY_settable_params, EVP_PKEY_set_params, EVP_PKEY_set_int_param, EVP_PKEY_set_size_t_param, EVP_PKEY_set_bn_param, EVP_PKEY_set_utf8_string_param, EVP_PKEY_set_octet_string_param - set key parameters into a key</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<pre><code>#include &lt;openssl/evp.h&gt;

const OSSL_PARAM *EVP_PKEY_settable_params(const EVP_PKEY *pkey);
int EVP_PKEY_set_params(EVP_PKEY *pkey, OSSL_PARAM params[]);
int EVP_PKEY_set_int_param(EVP_PKEY *pkey, const char *key_name, int in);
int EVP_PKEY_set_size_t_param(EVP_PKEY *pkey, const char *key_name, size_t in);
int EVP_PKEY_set_bn_param(EVP_PKEY *pkey, const char *key_name,
                          const BIGNUM *bn);
int EVP_PKEY_set_utf8_string_param(EVP_PKEY *pkey, const char *key_name,
                                   const char *str);
int EVP_PKEY_set_octet_string_param(EVP_PKEY *pkey, const char *key_name,
                                    const unsigned char *buf, size_t bsize);</code></pre>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>These functions can be used to set additional parameters into an existing <b>EVP_PKEY</b>.</p>

<p>EVP_PKEY_set_params() sets one or more <i>params</i> into a <i>pkey</i>. See <a href="../man3/OSSL_PARAM.html">OSSL_PARAM(3)</a> for information about parameters.</p>

<p>EVP_PKEY_settable_params() returns a constant list of <i>params</i> indicating the names and types of key parameters that can be set. See <a href="../man3/OSSL_PARAM.html">OSSL_PARAM(3)</a> for information about parameters.</p>

<p>EVP_PKEY_set_int_param() sets an integer value <i>in</i> into a key <i>pkey</i> for the associated field <i>key_name</i>.</p>

<p>EVP_PKEY_set_size_t_param() sets an size_t value <i>in</i> into a key <i>pkey</i> for the associated field <i>key_name</i>.</p>

<p>EVP_PKEY_set_bn_param() sets the BIGNUM value <i>bn</i> into a key <i>pkey</i> for the associated field <i>key_name</i>.</p>

<p>EVP_PKEY_set_utf8_string_param() sets the UTF8 string <i>str</i> into a key <i>pkey</i> for the associated field <i>key_name</i>.</p>

<p>EVP_PKEY_set_octet_string_param() sets the octet string value <i>buf</i> with a size <i>bsize</i> into a key <i>pkey</i> for the associated field <i>key_name</i>.</p>

<h1 id="NOTES">NOTES</h1>

<p>These functions only work for <b>EVP_PKEY</b>s that contain a provider side key.</p>

<h1 id="RETURN-VALUES">RETURN VALUES</h1>

<p>EVP_PKEY_settable_params() returns NULL on error or if it is not supported,</p>

<p>All other methods return 1 if a value was successfully set, or 0 if there was an error.</p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man3/EVP_PKEY_gettable_params.html">EVP_PKEY_gettable_params(3)</a>, <a href="../man3/EVP_PKEY_CTX_new.html">EVP_PKEY_CTX_new(3)</a>, <a href="../man7/provider-keymgmt.html">provider-keymgmt(7)</a>, <a href="../man3/OSSL_PARAM.html">OSSL_PARAM(3)</a>,</p>

<h1 id="HISTORY">HISTORY</h1>

<p>These functions were added in OpenSSL 3.0.</p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2020-2021 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the Apache License 2.0 (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


