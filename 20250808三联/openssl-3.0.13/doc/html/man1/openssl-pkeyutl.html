<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>openssl-pkeyutl</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:root@localhost" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#OPTIONS">OPTIONS</a></li>
  <li><a href="#NOTES">NOTES</a></li>
  <li><a href="#RSA-ALGORITHM">RSA ALGORITHM</a></li>
  <li><a href="#RSA-PSS-ALGORITHM">RSA-PSS ALGORITHM</a></li>
  <li><a href="#DSA-ALGORITHM">DSA ALGORITHM</a></li>
  <li><a href="#DH-ALGORITHM">DH ALGORITHM</a></li>
  <li><a href="#EC-ALGORITHM">EC ALGORITHM</a></li>
  <li><a href="#X25519-AND-X448-ALGORITHMS">X25519 AND X448 ALGORITHMS</a></li>
  <li><a href="#ED25519-AND-ED448-ALGORITHMS">ED25519 AND ED448 ALGORITHMS</a></li>
  <li><a href="#SM2">SM2</a></li>
  <li><a href="#EXAMPLES">EXAMPLES</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#HISTORY">HISTORY</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>openssl-pkeyutl - public key algorithm command</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<p><b>openssl</b> <b>pkeyutl</b> [<b>-help</b>] [<b>-in</b> <i>file</i>] [<b>-rawin</b>] [<b>-digest</b> <i>algorithm</i>] [<b>-out</b> <i>file</i>] [<b>-sigfile</b> <i>file</i>] [<b>-inkey</b> <i>filename</i>|<i>uri</i>] [<b>-keyform</b> <b>DER</b>|<b>PEM</b>|<b>P12</b>|<b>ENGINE</b>] [<b>-passin</b> <i>arg</i>] [<b>-peerkey</b> <i>file</i>] [<b>-peerform</b> <b>DER</b>|<b>PEM</b>|<b>P12</b>|<b>ENGINE</b>] [<b>-pubin</b>] [<b>-certin</b>] [<b>-rev</b>] [<b>-sign</b>] [<b>-verify</b>] [<b>-verifyrecover</b>] [<b>-encrypt</b>] [<b>-decrypt</b>] [<b>-derive</b>] [<b>-kdf</b> <i>algorithm</i>] [<b>-kdflen</b> <i>length</i>] [<b>-pkeyopt</b> <i>opt</i>:<i>value</i>] [<b>-pkeyopt_passin</b> <i>opt</i>[:<i>passarg</i>]] [<b>-hexdump</b>] [<b>-asn1parse</b>] [<b>-engine</b> <i>id</i>] [<b>-engine_impl</b>] [<b>-rand</b> <i>files</i>] [<b>-writerand</b> <i>file</i>] [<b>-provider</b> <i>name</i>] [<b>-provider-path</b> <i>path</i>] [<b>-propquery</b> <i>propq</i>] [<b>-config</b> <i>configfile</i>]</p>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>This command can be used to perform low-level public key operations using any supported algorithm.</p>

<h1 id="OPTIONS">OPTIONS</h1>

<dl>

<dt id="help"><b>-help</b></dt>
<dd>

<p>Print out a usage message.</p>

</dd>
<dt id="in-filename"><b>-in</b> <i>filename</i></dt>
<dd>

<p>This specifies the input filename to read data from or standard input if this option is not specified.</p>

</dd>
<dt id="rawin"><b>-rawin</b></dt>
<dd>

<p>This indicates that the input data is raw data, which is not hashed by any message digest algorithm. The user can specify a digest algorithm by using the <b>-digest</b> option. This option can only be used with <b>-sign</b> and <b>-verify</b> and must be used with the Ed25519 and Ed448 algorithms.</p>

</dd>
<dt id="digest-algorithm"><b>-digest</b> <i>algorithm</i></dt>
<dd>

<p>This specifies the digest algorithm which is used to hash the input data before signing or verifying it with the input key. This option could be omitted if the signature algorithm does not require one (for instance, EdDSA). If this option is omitted but the signature algorithm requires one, a default value will be used. For signature algorithms like RSA, DSA and ECDSA, SHA-256 will be the default digest algorithm. For SM2, it will be SM3. If this option is present, then the <b>-rawin</b> option must be also specified.</p>

</dd>
<dt id="out-filename"><b>-out</b> <i>filename</i></dt>
<dd>

<p>Specifies the output filename to write to or standard output by default.</p>

</dd>
<dt id="sigfile-file"><b>-sigfile</b> <i>file</i></dt>
<dd>

<p>Signature file, required for <b>-verify</b> operations only</p>

</dd>
<dt id="inkey-filename-uri"><b>-inkey</b> <i>filename</i>|<i>uri</i></dt>
<dd>

<p>The input key, by default it should be a private key.</p>

</dd>
<dt id="keyform-DER-PEM-P12-ENGINE"><b>-keyform</b> <b>DER</b>|<b>PEM</b>|<b>P12</b>|<b>ENGINE</b></dt>
<dd>

<p>The key format; unspecified by default. See <a href="../man1/openssl-format-options.html">openssl-format-options(1)</a> for details.</p>

</dd>
<dt id="passin-arg"><b>-passin</b> <i>arg</i></dt>
<dd>

<p>The input key password source. For more information about the format of <i>arg</i> see <a href="../man1/openssl-passphrase-options.html">openssl-passphrase-options(1)</a>.</p>

</dd>
<dt id="peerkey-file"><b>-peerkey</b> <i>file</i></dt>
<dd>

<p>The peer key file, used by key derivation (agreement) operations.</p>

</dd>
<dt id="peerform-DER-PEM-P12-ENGINE"><b>-peerform</b> <b>DER</b>|<b>PEM</b>|<b>P12</b>|<b>ENGINE</b></dt>
<dd>

<p>The peer key format; unspecified by default. See <a href="../man1/openssl-format-options.html">openssl-format-options(1)</a> for details.</p>

</dd>
<dt id="pubin"><b>-pubin</b></dt>
<dd>

<p>The input file is a public key.</p>

</dd>
<dt id="certin"><b>-certin</b></dt>
<dd>

<p>The input is a certificate containing a public key.</p>

</dd>
<dt id="rev"><b>-rev</b></dt>
<dd>

<p>Reverse the order of the input buffer. This is useful for some libraries (such as CryptoAPI) which represent the buffer in little endian format.</p>

</dd>
<dt id="sign"><b>-sign</b></dt>
<dd>

<p>Sign the input data (which must be a hash) and output the signed result. This requires a private key.</p>

</dd>
<dt id="verify"><b>-verify</b></dt>
<dd>

<p>Verify the input data (which must be a hash) against the signature file and indicate if the verification succeeded or failed.</p>

</dd>
<dt id="verifyrecover"><b>-verifyrecover</b></dt>
<dd>

<p>Verify the input data (which must be a hash) and output the recovered data.</p>

</dd>
<dt id="encrypt"><b>-encrypt</b></dt>
<dd>

<p>Encrypt the input data using a public key.</p>

</dd>
<dt id="decrypt"><b>-decrypt</b></dt>
<dd>

<p>Decrypt the input data using a private key.</p>

</dd>
<dt id="derive"><b>-derive</b></dt>
<dd>

<p>Derive a shared secret using the peer key.</p>

</dd>
<dt id="kdf-algorithm"><b>-kdf</b> <i>algorithm</i></dt>
<dd>

<p>Use key derivation function <i>algorithm</i>. The supported algorithms are at present <b>TLS1-PRF</b> and <b>HKDF</b>. Note: additional parameters and the KDF output length will normally have to be set for this to work. See <a href="../man3/EVP_PKEY_CTX_set_hkdf_md.html">EVP_PKEY_CTX_set_hkdf_md(3)</a> and <a href="../man3/EVP_PKEY_CTX_set_tls1_prf_md.html">EVP_PKEY_CTX_set_tls1_prf_md(3)</a> for the supported string parameters of each algorithm.</p>

</dd>
<dt id="kdflen-length"><b>-kdflen</b> <i>length</i></dt>
<dd>

<p>Set the output length for KDF.</p>

</dd>
<dt id="pkeyopt-opt:value"><b>-pkeyopt</b> <i>opt</i>:<i>value</i></dt>
<dd>

<p>Public key options specified as opt:value. See NOTES below for more details.</p>

</dd>
<dt id="pkeyopt_passin-opt-:passarg"><b>-pkeyopt_passin</b> <i>opt</i>[:<i>passarg</i>]</dt>
<dd>

<p>Allows reading a public key option <i>opt</i> from stdin or a password source. If only <i>opt</i> is specified, the user will be prompted to enter a password on stdin. Alternatively, <i>passarg</i> can be specified which can be any value supported by <a href="../man1/openssl-passphrase-options.html">openssl-passphrase-options(1)</a>.</p>

</dd>
<dt id="hexdump"><b>-hexdump</b></dt>
<dd>

<p>hex dump the output data.</p>

</dd>
<dt id="asn1parse"><b>-asn1parse</b></dt>
<dd>

<p>Parse the ASN.1 output data, this is useful when combined with the <b>-verifyrecover</b> option when an ASN1 structure is signed.</p>

</dd>
<dt id="engine-id"><b>-engine</b> <i>id</i></dt>
<dd>

<p>See <a href="../man1/openssl.html">&quot;Engine Options&quot; in openssl(1)</a>. This option is deprecated.</p>

</dd>
<dt id="engine_impl"><b>-engine_impl</b></dt>
<dd>

<p>When used with the <b>-engine</b> option, it specifies to also use engine <i>id</i> for crypto operations.</p>

</dd>
<dt id="rand-files--writerand-file"><b>-rand</b> <i>files</i>, <b>-writerand</b> <i>file</i></dt>
<dd>

<p>See <a href="../man1/openssl.html">&quot;Random State Options&quot; in openssl(1)</a> for details.</p>

</dd>
<dt id="provider-name"><b>-provider</b> <i>name</i></dt>
<dd>

</dd>
<dt id="provider-path-path"><b>-provider-path</b> <i>path</i></dt>
<dd>

</dd>
<dt id="propquery-propq"><b>-propquery</b> <i>propq</i></dt>
<dd>

<p>See <a href="../man1/openssl.html">&quot;Provider Options&quot; in openssl(1)</a>, <a href="../man7/provider.html">provider(7)</a>, and <a href="../man7/property.html">property(7)</a>.</p>

</dd>
<dt id="config-configfile"><b>-config</b> <i>configfile</i></dt>
<dd>

<p>See <a href="../man1/openssl.html">&quot;Configuration Option&quot; in openssl(1)</a>.</p>

</dd>
</dl>

<h1 id="NOTES">NOTES</h1>

<p>The operations and options supported vary according to the key algorithm and its implementation. The OpenSSL operations and options are indicated below.</p>

<p>Unless otherwise mentioned all algorithms support the <b>digest:</b><i>alg</i> option which specifies the digest in use for sign, verify and verifyrecover operations. The value <i>alg</i> should represent a digest name as used in the EVP_get_digestbyname() function for example <b>sha1</b>. This value is not used to hash the input data. It is used (by some algorithms) for sanity-checking the lengths of data passed in and for creating the structures that make up the signature (e.g. <b>DigestInfo</b> in RSASSA PKCS#1 v1.5 signatures).</p>

<p>This command does not hash the input data (except where -rawin is used) but rather it will use the data directly as input to the signature algorithm. Depending on the key type, signature type, and mode of padding, the maximum acceptable lengths of input data differ. The signed data can&#39;t be longer than the key modulus with RSA. In case of ECDSA and DSA the data shouldn&#39;t be longer than the field size, otherwise it will be silently truncated to the field size. In any event the input size must not be larger than the largest supported digest size.</p>

<p>In other words, if the value of digest is <b>sha1</b> the input should be the 20 bytes long binary encoding of the SHA-1 hash function output.</p>

<h1 id="RSA-ALGORITHM">RSA ALGORITHM</h1>

<p>The RSA algorithm generally supports the encrypt, decrypt, sign, verify and verifyrecover operations. However, some padding modes support only a subset of these operations. The following additional <b>pkeyopt</b> values are supported:</p>

<dl>

<dt id="rsa_padding_mode:mode"><b>rsa_padding_mode:</b><i>mode</i></dt>
<dd>

<p>This sets the RSA padding mode. Acceptable values for <i>mode</i> are <b>pkcs1</b> for PKCS#1 padding, <b>none</b> for no padding, <b>oaep</b> for <b>OAEP</b> mode, <b>x931</b> for X9.31 mode and <b>pss</b> for PSS.</p>

<p>In PKCS#1 padding, if the message digest is not set, then the supplied data is signed or verified directly instead of using a <b>DigestInfo</b> structure. If a digest is set, then the <b>DigestInfo</b> structure is used and its length must correspond to the digest type.</p>

<p>For <b>oaep</b> mode only encryption and decryption is supported.</p>

<p>For <b>x931</b> if the digest type is set it is used to format the block data otherwise the first byte is used to specify the X9.31 digest ID. Sign, verify and verifyrecover are can be performed in this mode.</p>

<p>For <b>pss</b> mode only sign and verify are supported and the digest type must be specified.</p>

</dd>
<dt id="rsa_pss_saltlen:len"><b>rsa_pss_saltlen:</b><i>len</i></dt>
<dd>

<p>For <b>pss</b> mode only this option specifies the salt length. Three special values are supported: <b>digest</b> sets the salt length to the digest length, <b>max</b> sets the salt length to the maximum permissible value. When verifying <b>auto</b> causes the salt length to be automatically determined based on the <b>PSS</b> block structure.</p>

</dd>
<dt id="rsa_mgf1_md:digest"><b>rsa_mgf1_md:</b><i>digest</i></dt>
<dd>

<p>For PSS and OAEP padding sets the MGF1 digest. If the MGF1 digest is not explicitly set in PSS mode then the signing digest is used.</p>

</dd>
<dt id="rsa_oaep_md:digest"><b>rsa_oaep_md:</b><i>digest</i></dt>
<dd>

<p>Sets the digest used for the OAEP hash function. If not explicitly set then SHA1 is used.</p>

</dd>
</dl>

<h1 id="RSA-PSS-ALGORITHM">RSA-PSS ALGORITHM</h1>

<p>The RSA-PSS algorithm is a restricted version of the RSA algorithm which only supports the sign and verify operations with PSS padding. The following additional <b>-pkeyopt</b> values are supported:</p>

<dl>

<dt id="rsa_padding_mode:mode-rsa_pss_saltlen:len-rsa_mgf1_md:digest"><b>rsa_padding_mode:</b><i>mode</i>, <b>rsa_pss_saltlen:</b><i>len</i>, <b>rsa_mgf1_md:</b><i>digest</i></dt>
<dd>

<p>These have the same meaning as the <b>RSA</b> algorithm with some additional restrictions. The padding mode can only be set to <b>pss</b> which is the default value.</p>

<p>If the key has parameter restrictions than the digest, MGF1 digest and salt length are set to the values specified in the parameters. The digest and MG cannot be changed and the salt length cannot be set to a value less than the minimum restriction.</p>

</dd>
</dl>

<h1 id="DSA-ALGORITHM">DSA ALGORITHM</h1>

<p>The DSA algorithm supports signing and verification operations only. Currently there are no additional <b>-pkeyopt</b> options other than <b>digest</b>. The SHA1 digest is assumed by default.</p>

<h1 id="DH-ALGORITHM">DH ALGORITHM</h1>

<p>The DH algorithm only supports the derivation operation and no additional <b>-pkeyopt</b> options.</p>

<h1 id="EC-ALGORITHM">EC ALGORITHM</h1>

<p>The EC algorithm supports sign, verify and derive operations. The sign and verify operations use ECDSA and derive uses ECDH. SHA1 is assumed by default for the <b>-pkeyopt</b> <b>digest</b> option.</p>

<h1 id="X25519-AND-X448-ALGORITHMS">X25519 AND X448 ALGORITHMS</h1>

<p>The X25519 and X448 algorithms support key derivation only. Currently there are no additional options.</p>

<h1 id="ED25519-AND-ED448-ALGORITHMS">ED25519 AND ED448 ALGORITHMS</h1>

<p>These algorithms only support signing and verifying. OpenSSL only implements the &quot;pure&quot; variants of these algorithms so raw data can be passed directly to them without hashing them first. The option <b>-rawin</b> must be used with these algorithms with no <b>-digest</b> specified. Additionally OpenSSL only supports &quot;oneshot&quot; operation with these algorithms. This means that the entire file to be signed/verified must be read into memory before processing it. Signing or Verifying very large files should be avoided. Additionally the size of the file must be known for this to work. If the size of the file cannot be determined (for example if the input is stdin) then the sign or verify operation will fail.</p>

<h1 id="SM2">SM2</h1>

<p>The SM2 algorithm supports sign, verify, encrypt and decrypt operations. For the sign and verify operations, SM2 requires an Distinguishing ID string to be passed in. The following <b>-pkeyopt</b> value is supported:</p>

<dl>

<dt id="distid:string"><b>distid:</b><i>string</i></dt>
<dd>

<p>This sets the ID string used in SM2 sign or verify operations. While verifying an SM2 signature, the ID string must be the same one used when signing the data. Otherwise the verification will fail.</p>

</dd>
<dt id="hexdistid:hex_string"><b>hexdistid:</b><i>hex_string</i></dt>
<dd>

<p>This sets the ID string used in SM2 sign or verify operations. While verifying an SM2 signature, the ID string must be the same one used when signing the data. Otherwise the verification will fail. The ID string provided with this option should be a valid hexadecimal value.</p>

</dd>
</dl>

<h1 id="EXAMPLES">EXAMPLES</h1>

<p>Sign some data using a private key:</p>

<pre><code>openssl pkeyutl -sign -in file -inkey key.pem -out sig</code></pre>

<p>Recover the signed data (e.g. if an RSA key is used):</p>

<pre><code>openssl pkeyutl -verifyrecover -in sig -inkey key.pem</code></pre>

<p>Verify the signature (e.g. a DSA key):</p>

<pre><code>openssl pkeyutl -verify -in file -sigfile sig -inkey key.pem</code></pre>

<p>Sign data using a message digest value (this is currently only valid for RSA):</p>

<pre><code>openssl pkeyutl -sign -in file -inkey key.pem -out sig -pkeyopt digest:sha256</code></pre>

<p>Derive a shared secret value:</p>

<pre><code>openssl pkeyutl -derive -inkey key.pem -peerkey pubkey.pem -out secret</code></pre>

<p>Hexdump 48 bytes of TLS1 PRF using digest <b>SHA256</b> and shared secret and seed consisting of the single byte 0xFF:</p>

<pre><code>openssl pkeyutl -kdf TLS1-PRF -kdflen 48 -pkeyopt md:SHA256 \
   -pkeyopt hexsecret:ff -pkeyopt hexseed:ff -hexdump</code></pre>

<p>Derive a key using <b>scrypt</b> where the password is read from command line:</p>

<pre><code>openssl pkeyutl -kdf scrypt -kdflen 16 -pkeyopt_passin pass \
   -pkeyopt hexsalt:aabbcc -pkeyopt N:16384 -pkeyopt r:8 -pkeyopt p:1</code></pre>

<p>Derive using the same algorithm, but read key from environment variable MYPASS:</p>

<pre><code>openssl pkeyutl -kdf scrypt -kdflen 16 -pkeyopt_passin pass:env:MYPASS \
   -pkeyopt hexsalt:aabbcc -pkeyopt N:16384 -pkeyopt r:8 -pkeyopt p:1</code></pre>

<p>Sign some data using an <a href="../man7/SM2.html">SM2(7)</a> private key and a specific ID:</p>

<pre><code>openssl pkeyutl -sign -in file -inkey sm2.key -out sig -rawin -digest sm3 \
   -pkeyopt distid:someid</code></pre>

<p>Verify some data using an <a href="../man7/SM2.html">SM2(7)</a> certificate and a specific ID:</p>

<pre><code>openssl pkeyutl -verify -certin -in file -inkey sm2.cert -sigfile sig \
   -rawin -digest sm3 -pkeyopt distid:someid</code></pre>

<p>Decrypt some data using a private key with OAEP padding using SHA256:</p>

<pre><code>openssl pkeyutl -decrypt -in file -inkey key.pem -out secret \
   -pkeyopt rsa_padding_mode:oaep -pkeyopt rsa_oaep_md:sha256</code></pre>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man1/openssl.html">openssl(1)</a>, <a href="../man1/openssl-genpkey.html">openssl-genpkey(1)</a>, <a href="../man1/openssl-pkey.html">openssl-pkey(1)</a>, <a href="../man1/openssl-rsautl.html">openssl-rsautl(1)</a> <a href="../man1/openssl-dgst.html">openssl-dgst(1)</a>, <a href="../man1/openssl-rsa.html">openssl-rsa(1)</a>, <a href="../man1/openssl-genrsa.html">openssl-genrsa(1)</a>, <a href="../man1/openssl-kdf.html">openssl-kdf(1)</a> <a href="../man3/EVP_PKEY_CTX_set_hkdf_md.html">EVP_PKEY_CTX_set_hkdf_md(3)</a>, <a href="../man3/EVP_PKEY_CTX_set_tls1_prf_md.html">EVP_PKEY_CTX_set_tls1_prf_md(3)</a>,</p>

<h1 id="HISTORY">HISTORY</h1>

<p>The <b>-engine</b> option was deprecated in OpenSSL 3.0.</p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2006-2021 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the Apache License 2.0 (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


