<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>openssl-enc</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:root@localhost" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#OPTIONS">OPTIONS</a></li>
  <li><a href="#NOTES">NOTES</a></li>
  <li><a href="#SUPPORTED-CIPHERS">SUPPORTED CIPHERS</a></li>
  <li><a href="#EXAMPLES">EXAMPLES</a></li>
  <li><a href="#BUGS">BUGS</a></li>
  <li><a href="#HISTORY">HISTORY</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>openssl-enc - symmetric cipher routines</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<p><b>openssl</b> <b>enc</b>|<i>cipher</i> [<b>-<i>cipher</i></b>] [<b>-help</b>] [<b>-list</b>] [<b>-ciphers</b>] [<b>-in</b> <i>filename</i>] [<b>-out</b> <i>filename</i>] [<b>-pass</b> <i>arg</i>] [<b>-e</b>] [<b>-d</b>] [<b>-a</b>] [<b>-base64</b>] [<b>-A</b>] [<b>-k</b> <i>password</i>] [<b>-kfile</b> <i>filename</i>] [<b>-K</b> <i>key</i>] [<b>-iv</b> <i>IV</i>] [<b>-S</b> <i>salt</i>] [<b>-salt</b>] [<b>-nosalt</b>] [<b>-z</b>] [<b>-md</b> <i>digest</i>] [<b>-iter</b> <i>count</i>] [<b>-pbkdf2</b>] [<b>-p</b>] [<b>-P</b>] [<b>-bufsize</b> <i>number</i>] [<b>-nopad</b>] [<b>-v</b>] [<b>-debug</b>] [<b>-none</b>] [<b>-engine</b> <i>id</i>] [<b>-rand</b> <i>files</i>] [<b>-writerand</b> <i>file</i>] [<b>-provider</b> <i>name</i>] [<b>-provider-path</b> <i>path</i>] [<b>-propquery</b> <i>propq</i>]</p>

<p><b>openssl</b> <i>cipher</i> [<b>...</b>]</p>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>The symmetric cipher commands allow data to be encrypted or decrypted using various block and stream ciphers using keys based on passwords or explicitly provided. Base64 encoding or decoding can also be performed either by itself or in addition to the encryption or decryption.</p>

<h1 id="OPTIONS">OPTIONS</h1>

<dl>

<dt id="cipher"><b>-<i>cipher</i></b></dt>
<dd>

<p>The cipher to use.</p>

</dd>
<dt id="help"><b>-help</b></dt>
<dd>

<p>Print out a usage message.</p>

</dd>
<dt id="list"><b>-list</b></dt>
<dd>

<p>List all supported ciphers.</p>

</dd>
<dt id="ciphers"><b>-ciphers</b></dt>
<dd>

<p>Alias of -list to display all supported ciphers.</p>

</dd>
<dt id="in-filename"><b>-in</b> <i>filename</i></dt>
<dd>

<p>The input filename, standard input by default.</p>

</dd>
<dt id="out-filename"><b>-out</b> <i>filename</i></dt>
<dd>

<p>The output filename, standard output by default.</p>

</dd>
<dt id="pass-arg"><b>-pass</b> <i>arg</i></dt>
<dd>

<p>The password source. For more information about the format of <i>arg</i> see <a href="../man1/openssl-passphrase-options.html">openssl-passphrase-options(1)</a>.</p>

</dd>
<dt id="e"><b>-e</b></dt>
<dd>

<p>Encrypt the input data: this is the default.</p>

</dd>
<dt id="d"><b>-d</b></dt>
<dd>

<p>Decrypt the input data.</p>

</dd>
<dt id="a"><b>-a</b></dt>
<dd>

<p>Base64 process the data. This means that if encryption is taking place the data is base64 encoded after encryption. If decryption is set then the input data is base64 decoded before being decrypted.</p>

</dd>
<dt id="base64"><b>-base64</b></dt>
<dd>

<p>Same as <b>-a</b></p>

</dd>
<dt id="A"><b>-A</b></dt>
<dd>

<p>If the <b>-a</b> option is set then base64 process the data on one line.</p>

</dd>
<dt id="k-password"><b>-k</b> <i>password</i></dt>
<dd>

<p>The password to derive the key from. This is for compatibility with previous versions of OpenSSL. Superseded by the <b>-pass</b> argument.</p>

</dd>
<dt id="kfile-filename"><b>-kfile</b> <i>filename</i></dt>
<dd>

<p>Read the password to derive the key from the first line of <i>filename</i>. This is for compatibility with previous versions of OpenSSL. Superseded by the <b>-pass</b> argument.</p>

</dd>
<dt id="md-digest"><b>-md</b> <i>digest</i></dt>
<dd>

<p>Use the specified digest to create the key from the passphrase. The default algorithm is sha-256.</p>

</dd>
<dt id="iter-count"><b>-iter</b> <i>count</i></dt>
<dd>

<p>Use a given number of iterations on the password in deriving the encryption key. High values increase the time required to brute-force the resulting file. This option enables the use of PBKDF2 algorithm to derive the key.</p>

</dd>
<dt id="pbkdf2"><b>-pbkdf2</b></dt>
<dd>

<p>Use PBKDF2 algorithm with a default iteration count of 10000 unless otherwise specified by the <b>-iter</b> command line option.</p>

</dd>
<dt id="nosalt"><b>-nosalt</b></dt>
<dd>

<p>Don&#39;t use a salt in the key derivation routines. This option <b>SHOULD NOT</b> be used except for test purposes or compatibility with ancient versions of OpenSSL.</p>

</dd>
<dt id="salt"><b>-salt</b></dt>
<dd>

<p>Use salt (randomly generated or provide with <b>-S</b> option) when encrypting, this is the default.</p>

</dd>
<dt id="S-salt"><b>-S</b> <i>salt</i></dt>
<dd>

<p>The actual salt to use: this must be represented as a string of hex digits. If this option is used while encrypting, the same exact value will be needed again during decryption.</p>

</dd>
<dt id="K-key"><b>-K</b> <i>key</i></dt>
<dd>

<p>The actual key to use: this must be represented as a string comprised only of hex digits. If only the key is specified, the IV must additionally specified using the <b>-iv</b> option. When both a key and a password are specified, the key given with the <b>-K</b> option will be used and the IV generated from the password will be taken. It does not make much sense to specify both key and password.</p>

</dd>
<dt id="iv-IV"><b>-iv</b> <i>IV</i></dt>
<dd>

<p>The actual IV to use: this must be represented as a string comprised only of hex digits. When only the key is specified using the <b>-K</b> option, the IV must explicitly be defined. When a password is being specified using one of the other options, the IV is generated from this password.</p>

</dd>
<dt id="p"><b>-p</b></dt>
<dd>

<p>Print out the key and IV used.</p>

</dd>
<dt id="P"><b>-P</b></dt>
<dd>

<p>Print out the key and IV used then immediately exit: don&#39;t do any encryption or decryption.</p>

</dd>
<dt id="bufsize-number"><b>-bufsize</b> <i>number</i></dt>
<dd>

<p>Set the buffer size for I/O.</p>

</dd>
<dt id="nopad"><b>-nopad</b></dt>
<dd>

<p>Disable standard block padding.</p>

</dd>
<dt id="v"><b>-v</b></dt>
<dd>

<p>Verbose print; display some statistics about I/O and buffer sizes.</p>

</dd>
<dt id="debug"><b>-debug</b></dt>
<dd>

<p>Debug the BIOs used for I/O.</p>

</dd>
<dt id="z"><b>-z</b></dt>
<dd>

<p>Compress or decompress encrypted data using zlib after encryption or before decryption. This option exists only if OpenSSL was compiled with the zlib or zlib-dynamic option.</p>

</dd>
<dt id="none"><b>-none</b></dt>
<dd>

<p>Use NULL cipher (no encryption or decryption of input).</p>

</dd>
<dt id="rand-files--writerand-file"><b>-rand</b> <i>files</i>, <b>-writerand</b> <i>file</i></dt>
<dd>

<p>See <a href="../man1/openssl.html">&quot;Random State Options&quot; in openssl(1)</a> for details.</p>

</dd>
<dt id="provider-name"><b>-provider</b> <i>name</i></dt>
<dd>

</dd>
<dt id="provider-path-path"><b>-provider-path</b> <i>path</i></dt>
<dd>

</dd>
<dt id="propquery-propq"><b>-propquery</b> <i>propq</i></dt>
<dd>

<p>See <a href="../man1/openssl.html">&quot;Provider Options&quot; in openssl(1)</a>, <a href="../man7/provider.html">provider(7)</a>, and <a href="../man7/property.html">property(7)</a>.</p>

</dd>
<dt id="engine-id"><b>-engine</b> <i>id</i></dt>
<dd>

<p>See <a href="../man1/openssl.html">&quot;Engine Options&quot; in openssl(1)</a>. This option is deprecated.</p>

</dd>
</dl>

<h1 id="NOTES">NOTES</h1>

<p>The program can be called either as <code>openssl <i>cipher</i></code> or <code>openssl enc -<i>cipher</i></code>. The first form doesn&#39;t work with engine-provided ciphers, because this form is processed before the configuration file is read and any ENGINEs loaded. Use the <a href="../man1/openssl-list.html">openssl-list(1)</a> command to get a list of supported ciphers.</p>

<p>Engines which provide entirely new encryption algorithms (such as the ccgost engine which provides gost89 algorithm) should be configured in the configuration file. Engines specified on the command line using <b>-engine</b> option can only be used for hardware-assisted implementations of ciphers which are supported by the OpenSSL core or another engine specified in the configuration file.</p>

<p>When the enc command lists supported ciphers, ciphers provided by engines, specified in the configuration files are listed too.</p>

<p>A password will be prompted for to derive the key and IV if necessary.</p>

<p>The <b>-salt</b> option should <b>ALWAYS</b> be used if the key is being derived from a password unless you want compatibility with previous versions of OpenSSL.</p>

<p>Without the <b>-salt</b> option it is possible to perform efficient dictionary attacks on the password and to attack stream cipher encrypted data. The reason for this is that without the salt the same password always generates the same encryption key.</p>

<p>When the salt is generated at random (that means when encrypting using a passphrase without explicit salt given using <b>-S</b> option), the first bytes of the encrypted data are reserved to store the salt for later decrypting.</p>

<p>Some of the ciphers do not have large keys and others have security implications if not used correctly. A beginner is advised to just use a strong block cipher, such as AES, in CBC mode.</p>

<p>All the block ciphers normally use PKCS#5 padding, also known as standard block padding. This allows a rudimentary integrity or password check to be performed. However, since the chance of random data passing the test is better than 1 in 256 it isn&#39;t a very good test.</p>

<p>If padding is disabled then the input data must be a multiple of the cipher block length.</p>

<p>All RC2 ciphers have the same key and effective key length.</p>

<p>Blowfish and RC5 algorithms use a 128 bit key.</p>

<p>Please note that OpenSSL 3.0 changed the effect of the <b>-S</b> option. Any explicit salt value specified via this option is no longer prepended to the ciphertext when encrypting, and must again be explicitly provided when decrypting. Conversely, when the <b>-S</b> option is used during decryption, the ciphertext is expected to not have a prepended salt value.</p>

<p>When using OpenSSL 3.0 or later to decrypt data that was encrypted with an explicit salt under OpenSSL 1.1.1 do not use the <b>-S</b> option, the salt will then be read from the ciphertext. To generate ciphertext that can be decrypted with OpenSSL 1.1.1 do not use the <b>-S</b> option, the salt will be then be generated randomly and prepended to the output.</p>

<h1 id="SUPPORTED-CIPHERS">SUPPORTED CIPHERS</h1>

<p>Note that some of these ciphers can be disabled at compile time and some are available only if an appropriate engine is configured in the configuration file. The output when invoking this command with the <b>-list</b> option (that is <code>openssl enc -list</code>) is a list of ciphers, supported by your version of OpenSSL, including ones provided by configured engines.</p>

<p>This command does not support authenticated encryption modes like CCM and GCM, and will not support such modes in the future. This is due to having to begin streaming output (e.g., to standard output when <b>-out</b> is not used) before the authentication tag could be validated. When this command is used in a pipeline, the receiving end will not be able to roll back upon authentication failure. The AEAD modes currently in common use also suffer from catastrophic failure of confidentiality and/or integrity upon reuse of key/iv/nonce, and since <b>openssl enc</b> places the entire burden of key/iv/nonce management upon the user, the risk of exposing AEAD modes is too great to allow. These key/iv/nonce management issues also affect other modes currently exposed in this command, but the failure modes are less extreme in these cases, and the functionality cannot be removed with a stable release branch. For bulk encryption of data, whether using authenticated encryption modes or other modes, <a href="../man1/openssl-cms.html">openssl-cms(1)</a> is recommended, as it provides a standard data format and performs the needed key/iv/nonce management.</p>

<pre><code>base64             Base 64

bf-cbc             Blowfish in CBC mode
bf                 Alias for bf-cbc
blowfish           Alias for bf-cbc
bf-cfb             Blowfish in CFB mode
bf-ecb             Blowfish in ECB mode
bf-ofb             Blowfish in OFB mode

cast-cbc           CAST in CBC mode
cast               Alias for cast-cbc
cast5-cbc          CAST5 in CBC mode
cast5-cfb          CAST5 in CFB mode
cast5-ecb          CAST5 in ECB mode
cast5-ofb          CAST5 in OFB mode

chacha20           ChaCha20 algorithm

des-cbc            DES in CBC mode
des                Alias for des-cbc
des-cfb            DES in CFB mode
des-ofb            DES in OFB mode
des-ecb            DES in ECB mode

des-ede-cbc        Two key triple DES EDE in CBC mode
des-ede            Two key triple DES EDE in ECB mode
des-ede-cfb        Two key triple DES EDE in CFB mode
des-ede-ofb        Two key triple DES EDE in OFB mode

des-ede3-cbc       Three key triple DES EDE in CBC mode
des-ede3           Three key triple DES EDE in ECB mode
des3               Alias for des-ede3-cbc
des-ede3-cfb       Three key triple DES EDE CFB mode
des-ede3-ofb       Three key triple DES EDE in OFB mode

desx               DESX algorithm.

gost89             GOST 28147-89 in CFB mode (provided by ccgost engine)
gost89-cnt         GOST 28147-89 in CNT mode (provided by ccgost engine)

idea-cbc           IDEA algorithm in CBC mode
idea               same as idea-cbc
idea-cfb           IDEA in CFB mode
idea-ecb           IDEA in ECB mode
idea-ofb           IDEA in OFB mode

rc2-cbc            128 bit RC2 in CBC mode
rc2                Alias for rc2-cbc
rc2-cfb            128 bit RC2 in CFB mode
rc2-ecb            128 bit RC2 in ECB mode
rc2-ofb            128 bit RC2 in OFB mode
rc2-64-cbc         64 bit RC2 in CBC mode
rc2-40-cbc         40 bit RC2 in CBC mode

rc4                128 bit RC4
rc4-64             64 bit RC4
rc4-40             40 bit RC4

rc5-cbc            RC5 cipher in CBC mode
rc5                Alias for rc5-cbc
rc5-cfb            RC5 cipher in CFB mode
rc5-ecb            RC5 cipher in ECB mode
rc5-ofb            RC5 cipher in OFB mode

seed-cbc           SEED cipher in CBC mode
seed               Alias for seed-cbc
seed-cfb           SEED cipher in CFB mode
seed-ecb           SEED cipher in ECB mode
seed-ofb           SEED cipher in OFB mode

sm4-cbc            SM4 cipher in CBC mode
sm4                Alias for sm4-cbc
sm4-cfb            SM4 cipher in CFB mode
sm4-ctr            SM4 cipher in CTR mode
sm4-ecb            SM4 cipher in ECB mode
sm4-ofb            SM4 cipher in OFB mode

aes-[128|192|256]-cbc  128/192/256 bit AES in CBC mode
aes[128|192|256]       Alias for aes-[128|192|256]-cbc
aes-[128|192|256]-cfb  128/192/256 bit AES in 128 bit CFB mode
aes-[128|192|256]-cfb1 128/192/256 bit AES in 1 bit CFB mode
aes-[128|192|256]-cfb8 128/192/256 bit AES in 8 bit CFB mode
aes-[128|192|256]-ctr  128/192/256 bit AES in CTR mode
aes-[128|192|256]-ecb  128/192/256 bit AES in ECB mode
aes-[128|192|256]-ofb  128/192/256 bit AES in OFB mode

aria-[128|192|256]-cbc  128/192/256 bit ARIA in CBC mode
aria[128|192|256]       Alias for aria-[128|192|256]-cbc
aria-[128|192|256]-cfb  128/192/256 bit ARIA in 128 bit CFB mode
aria-[128|192|256]-cfb1 128/192/256 bit ARIA in 1 bit CFB mode
aria-[128|192|256]-cfb8 128/192/256 bit ARIA in 8 bit CFB mode
aria-[128|192|256]-ctr  128/192/256 bit ARIA in CTR mode
aria-[128|192|256]-ecb  128/192/256 bit ARIA in ECB mode
aria-[128|192|256]-ofb  128/192/256 bit ARIA in OFB mode

camellia-[128|192|256]-cbc  128/192/256 bit Camellia in CBC mode
camellia[128|192|256]       Alias for camellia-[128|192|256]-cbc
camellia-[128|192|256]-cfb  128/192/256 bit Camellia in 128 bit CFB mode
camellia-[128|192|256]-cfb1 128/192/256 bit Camellia in 1 bit CFB mode
camellia-[128|192|256]-cfb8 128/192/256 bit Camellia in 8 bit CFB mode
camellia-[128|192|256]-ctr  128/192/256 bit Camellia in CTR mode
camellia-[128|192|256]-ecb  128/192/256 bit Camellia in ECB mode
camellia-[128|192|256]-ofb  128/192/256 bit Camellia in OFB mode</code></pre>

<h1 id="EXAMPLES">EXAMPLES</h1>

<p>Just base64 encode a binary file:</p>

<pre><code>openssl base64 -in file.bin -out file.b64</code></pre>

<p>Decode the same file</p>

<pre><code>openssl base64 -d -in file.b64 -out file.bin</code></pre>

<p>Encrypt a file using AES-128 using a prompted password and PBKDF2 key derivation:</p>

<pre><code>openssl enc -aes128 -pbkdf2 -in file.txt -out file.aes128</code></pre>

<p>Decrypt a file using a supplied password:</p>

<pre><code>openssl enc -aes128 -pbkdf2 -d -in file.aes128 -out file.txt \
   -pass pass:&lt;password&gt;</code></pre>

<p>Encrypt a file then base64 encode it (so it can be sent via mail for example) using AES-256 in CTR mode and PBKDF2 key derivation:</p>

<pre><code>openssl enc -aes-256-ctr -pbkdf2 -a -in file.txt -out file.aes256</code></pre>

<p>Base64 decode a file then decrypt it using a password supplied in a file:</p>

<pre><code>openssl enc -aes-256-ctr -pbkdf2 -d -a -in file.aes256 -out file.txt \
   -pass file:&lt;passfile&gt;</code></pre>

<h1 id="BUGS">BUGS</h1>

<p>The <b>-A</b> option when used with large files doesn&#39;t work properly.</p>

<p>The <b>openssl enc</b> command only supports a fixed number of algorithms with certain parameters. So if, for example, you want to use RC2 with a 76 bit key or RC4 with an 84 bit key you can&#39;t use this program.</p>

<h1 id="HISTORY">HISTORY</h1>

<p>The default digest was changed from MD5 to SHA256 in OpenSSL 1.1.0.</p>

<p>The <b>-list</b> option was added in OpenSSL 1.1.1e.</p>

<p>The <b>-ciphers</b> and <b>-engine</b> options were deprecated in OpenSSL 3.0.</p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2000-2023 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the Apache License 2.0 (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


