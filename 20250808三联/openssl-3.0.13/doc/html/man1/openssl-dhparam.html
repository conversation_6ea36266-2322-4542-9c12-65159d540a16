<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>openssl-dhparam</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:root@localhost" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#OPTIONS">OPTIONS</a></li>
  <li><a href="#NOTES">NOTES</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#HISTORY">HISTORY</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>openssl-dhparam - DH parameter manipulation and generation</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<p><b>openssl dhparam</b> [<b>-help</b>] [<b>-inform</b> <b>DER</b>|<b>PEM</b>] [<b>-outform</b> <b>DER</b>|<b>PEM</b>] [<b>-in</b> <i>filename</i>] [<b>-out</b> <i>filename</i>] [<b>-dsaparam</b>] [<b>-check</b>] [<b>-noout</b>] [<b>-text</b>] [<b>-2</b>] [<b>-3</b>] [<b>-5</b>] [<b>-engine</b> <i>id</i>] [<b>-rand</b> <i>files</i>] [<b>-writerand</b> <i>file</i>] [<b>-provider</b> <i>name</i>] [<b>-provider-path</b> <i>path</i>] [<b>-propquery</b> <i>propq</i>] [<i>numbits</i>]</p>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>This command is used to manipulate DH parameter files.</p>

<p>See <a href="../man1/openssl-genpkey.html">&quot;EXAMPLES&quot; in openssl-genpkey(1)</a> for examples on how to generate a key using a named safe prime group without generating intermediate parameters.</p>

<h1 id="OPTIONS">OPTIONS</h1>

<dl>

<dt id="help"><b>-help</b></dt>
<dd>

<p>Print out a usage message.</p>

</dd>
<dt id="inform-DER-PEM--outform-DER-PEM"><b>-inform</b> <b>DER</b>|<b>PEM</b>, <b>-outform</b> <b>DER</b>|<b>PEM</b></dt>
<dd>

<p>The input format and output format; the default is <b>PEM</b>. The object is compatible with the PKCS#3 <b>DHparameter</b> structure. See <a href="../man1/openssl-format-options.html">openssl-format-options(1)</a> for details.</p>

</dd>
<dt id="in-filename"><b>-in</b> <i>filename</i></dt>
<dd>

<p>This specifies the input filename to read parameters from or standard input if this option is not specified.</p>

</dd>
<dt id="out-filename"><b>-out</b> <i>filename</i></dt>
<dd>

<p>This specifies the output filename parameters to. Standard output is used if this option is not present. The output filename should <b>not</b> be the same as the input filename.</p>

</dd>
<dt id="dsaparam"><b>-dsaparam</b></dt>
<dd>

<p>If this option is used, DSA rather than DH parameters are read or created; they are converted to DH format. Otherwise, &quot;strong&quot; primes (such that (p-1)/2 is also prime) will be used for DH parameter generation.</p>

<p>DH parameter generation with the <b>-dsaparam</b> option is much faster, and the recommended exponent length is shorter, which makes DH key exchange more efficient. Beware that with such DSA-style DH parameters, a fresh DH key should be created for each use to avoid small-subgroup attacks that may be possible otherwise.</p>

</dd>
<dt id="check"><b>-check</b></dt>
<dd>

<p>Performs numerous checks to see if the supplied parameters are valid and displays a warning if not.</p>

</dd>
<dt id="pod-2--3--5"><b>-2</b>, <b>-3</b>, <b>-5</b></dt>
<dd>

<p>The generator to use, either 2, 3 or 5. If present then the input file is ignored and parameters are generated instead. If not present but <i>numbits</i> is present, parameters are generated with the default generator 2.</p>

</dd>
<dt id="numbits"><i>numbits</i></dt>
<dd>

<p>This option specifies that a parameter set should be generated of size <i>numbits</i>. It must be the last option. If this option is present then the input file is ignored and parameters are generated instead. If this option is not present but a generator (<b>-2</b>, <b>-3</b> or <b>-5</b>) is present, parameters are generated with a default length of 2048 bits. The minimum length is 512 bits. The maximum length is 10000 bits.</p>

</dd>
<dt id="noout"><b>-noout</b></dt>
<dd>

<p>This option inhibits the output of the encoded version of the parameters.</p>

</dd>
<dt id="text"><b>-text</b></dt>
<dd>

<p>This option prints out the DH parameters in human readable form.</p>

</dd>
<dt id="engine-id"><b>-engine</b> <i>id</i></dt>
<dd>

<p>See <a href="../man1/openssl.html">&quot;Engine Options&quot; in openssl(1)</a>. This option is deprecated.</p>

</dd>
<dt id="rand-files--writerand-file"><b>-rand</b> <i>files</i>, <b>-writerand</b> <i>file</i></dt>
<dd>

<p>See <a href="../man1/openssl.html">&quot;Random State Options&quot; in openssl(1)</a> for details.</p>

</dd>
<dt id="provider-name"><b>-provider</b> <i>name</i></dt>
<dd>

</dd>
<dt id="provider-path-path"><b>-provider-path</b> <i>path</i></dt>
<dd>

</dd>
<dt id="propquery-propq"><b>-propquery</b> <i>propq</i></dt>
<dd>

<p>See <a href="../man1/openssl.html">&quot;Provider Options&quot; in openssl(1)</a>, <a href="../man7/provider.html">provider(7)</a>, and <a href="../man7/property.html">property(7)</a>.</p>

</dd>
</dl>

<h1 id="NOTES">NOTES</h1>

<p>This command replaces the <b>dh</b> and <b>gendh</b> commands of previous releases.</p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man1/openssl.html">openssl(1)</a>, <a href="../man1/openssl-pkeyparam.html">openssl-pkeyparam(1)</a>, <a href="../man1/openssl-dsaparam.html">openssl-dsaparam(1)</a>, <a href="../man1/openssl-genpkey.html">openssl-genpkey(1)</a>.</p>

<h1 id="HISTORY">HISTORY</h1>

<p>The <b>-engine</b> option was deprecated in OpenSSL 3.0.</p>

<p>The <b>-C</b> option was removed in OpenSSL 3.0.</p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2000-2023 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the Apache License 2.0 (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


