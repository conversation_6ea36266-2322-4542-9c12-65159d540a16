<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>openssl-errstr</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:root@localhost" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#OPTIONS">OPTIONS</a></li>
  <li><a href="#EXAMPLES">EXAMPLES</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>openssl-errstr - lookup error codes</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<p><b>openssl errstr</b> [<b>-help</b>] <i>error_code...</i></p>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>Sometimes an application will not load error message texts and only numerical forms will be available. This command can be used to display the meaning of the hex code. The hex code is the hex digits after the second colon.</p>

<h1 id="OPTIONS">OPTIONS</h1>

<dl>

<dt id="help"><b>-help</b></dt>
<dd>

<p>Display a usage message.</p>

</dd>
</dl>

<h1 id="EXAMPLES">EXAMPLES</h1>

<p>The error code:</p>

<pre><code>27594:error:2006D080:lib(32)::reason(128)::107:</code></pre>

<p>can be displayed with:</p>

<pre><code>openssl errstr 2006D080</code></pre>

<p>to produce the error message:</p>

<pre><code>error:2006D080:BIO routines::no such file</code></pre>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2004-2020 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the Apache License 2.0 (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


