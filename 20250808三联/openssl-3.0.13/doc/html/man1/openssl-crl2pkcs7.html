<?xml version="1.0" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>openssl-crl2pkcs7</title>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link rev="made" href="mailto:root@localhost" />
</head>

<body>



<ul id="index">
  <li><a href="#NAME">NAME</a></li>
  <li><a href="#SYNOPSIS">SYNOPSIS</a></li>
  <li><a href="#DESCRIPTION">DESCRIPTION</a></li>
  <li><a href="#OPTIONS">OPTIONS</a></li>
  <li><a href="#EXAMPLES">EXAMPLES</a></li>
  <li><a href="#NOTES">NOTES</a></li>
  <li><a href="#SEE-ALSO">SEE ALSO</a></li>
  <li><a href="#COPYRIGHT">COPYRIGHT</a></li>
</ul>

<h1 id="NAME">NAME</h1>

<p>openssl-crl2pkcs7 - Create a PKCS#7 structure from a CRL and certificates</p>

<h1 id="SYNOPSIS">SYNOPSIS</h1>

<p><b>openssl</b> <b>crl2pkcs7</b> [<b>-help</b>] [<b>-inform</b> <b>DER</b>|<b>PEM</b>] [<b>-outform</b> <b>DER</b>|<b>PEM</b>] [<b>-in</b> <i>filename</i>] [<b>-out</b> <i>filename</i>] [<b>-certfile</b> <i>filename</i>] [<b>-nocrl</b>] [<b>-provider</b> <i>name</i>] [<b>-provider-path</b> <i>path</i>] [<b>-propquery</b> <i>propq</i>]</p>

<h1 id="DESCRIPTION">DESCRIPTION</h1>

<p>This command takes an optional CRL and one or more certificates and converts them into a PKCS#7 degenerate &quot;certificates only&quot; structure.</p>

<h1 id="OPTIONS">OPTIONS</h1>

<dl>

<dt id="help"><b>-help</b></dt>
<dd>

<p>Print out a usage message.</p>

</dd>
<dt id="inform-DER-PEM"><b>-inform</b> <b>DER</b>|<b>PEM</b></dt>
<dd>

<p>The input format of the CRL; the default is <b>PEM</b>. See <a href="../man1/openssl-format-options.html">openssl-format-options(1)</a> for details.</p>

</dd>
<dt id="outform-DER-PEM"><b>-outform</b> <b>DER</b>|<b>PEM</b></dt>
<dd>

<p>The output format of the PKCS#7 object; the default is <b>PEM</b>. See <a href="../man1/openssl-format-options.html">openssl-format-options(1)</a> for details.</p>

</dd>
<dt id="in-filename"><b>-in</b> <i>filename</i></dt>
<dd>

<p>This specifies the input filename to read a CRL from or standard input if this option is not specified.</p>

</dd>
<dt id="out-filename"><b>-out</b> <i>filename</i></dt>
<dd>

<p>Specifies the output filename to write the PKCS#7 structure to or standard output by default.</p>

</dd>
<dt id="certfile-filename"><b>-certfile</b> <i>filename</i></dt>
<dd>

<p>Specifies a filename containing one or more certificates in <b>PEM</b> format. All certificates in the file will be added to the PKCS#7 structure. This option can be used more than once to read certificates from multiple files.</p>

</dd>
<dt id="nocrl"><b>-nocrl</b></dt>
<dd>

<p>Normally a CRL is included in the output file. With this option no CRL is included in the output file and a CRL is not read from the input file.</p>

</dd>
<dt id="provider-name"><b>-provider</b> <i>name</i></dt>
<dd>

</dd>
<dt id="provider-path-path"><b>-provider-path</b> <i>path</i></dt>
<dd>

</dd>
<dt id="propquery-propq"><b>-propquery</b> <i>propq</i></dt>
<dd>

<p>See <a href="../man1/openssl.html">&quot;Provider Options&quot; in openssl(1)</a>, <a href="../man7/provider.html">provider(7)</a>, and <a href="../man7/property.html">property(7)</a>.</p>

</dd>
</dl>

<h1 id="EXAMPLES">EXAMPLES</h1>

<p>Create a PKCS#7 structure from a certificate and CRL:</p>

<pre><code>openssl crl2pkcs7 -in crl.pem -certfile cert.pem -out p7.pem</code></pre>

<p>Creates a PKCS#7 structure in DER format with no CRL from several different certificates:</p>

<pre><code>openssl crl2pkcs7 -nocrl -certfile newcert.pem
       -certfile demoCA/cacert.pem -outform DER -out p7.der</code></pre>

<h1 id="NOTES">NOTES</h1>

<p>The output file is a PKCS#7 signed data structure containing no signers and just certificates and an optional CRL.</p>

<p>This command can be used to send certificates and CAs to Netscape as part of the certificate enrollment process. This involves sending the DER encoded output as MIME type application/x-x509-user-cert.</p>

<p>The <b>PEM</b> encoded form with the header and footer lines removed can be used to install user certificates and CAs in MSIE using the Xenroll control.</p>

<h1 id="SEE-ALSO">SEE ALSO</h1>

<p><a href="../man1/openssl.html">openssl(1)</a>, <a href="../man1/openssl-pkcs7.html">openssl-pkcs7(1)</a></p>

<h1 id="COPYRIGHT">COPYRIGHT</h1>

<p>Copyright 2000-2021 The OpenSSL Project Authors. All Rights Reserved.</p>

<p>Licensed under the Apache License 2.0 (the &quot;License&quot;). You may not use this file except in compliance with the License. You can obtain a copy in the file LICENSE in the source distribution or at <a href="https://www.openssl.org/source/license.html">https://www.openssl.org/source/license.html</a>.</p>


</body>

</html>


