=pod

=head1 NAME

ossl_cmp_certresponse_get1_cert,
ossl_cmp_pkisi_get_status,
ossl_cmp_PKIStatus_to_string,
ossl_cmp_pkisi_get0_statusString,
ossl_cmp_pkisi_get_pkifailureinfo,
ossl_cmp_pkisi_check_pkifailureinfo
- functions for managing PKI status information

=head1 SYNOPSIS

  #include "cmp.h"

# define OSSL_CMP_PKIFAILUREINFO_badAlg               0
# define OSSL_CMP_PKIFAILUREINFO_badMessageCheck      1
# define OSSL_CMP_PKIFAILUREINFO_badRequest           2
# define OSSL_CMP_PKIFAILUREINFO_badTime              3
# define OSSL_CMP_PKIFAILUREINFO_badCertId            4
# define OSSL_CMP_PKIFAILUREINFO_badDataFormat        5
# define OSSL_CMP_PKIFAILUREINFO_wrongAuthority       6
# define OSSL_CMP_PKIFAILUREINFO_incorrectData        7
# define OSSL_CMP_PKIFAILUREINFO_missingTimeStamp     8
# define OSSL_CMP_PKIFAILUREINFO_badPOP               9
# define OSSL_CMP_PKIFAILUREINFO_certRevoked         10
# define OSSL_CMP_PKIFAILUREINFO_certConfirmed       11
# define OSSL_CMP_PKIFAILUREINFO_wrongIntegrity      12
# define OSSL_CMP_PKIFAILUREINFO_badRecipientNonce   13
# define OSSL_CMP_PKIFAILUREINFO_timeNotAvailable    14
# define OSSL_CMP_PKIFAILUREINFO_unacceptedPolicy    15
# define OSSL_CMP_PKIFAILUREINFO_unacceptedExtension 16
# define OSSL_CMP_PKIFAILUREINFO_addInfoNotAvailable 17
# define OSSL_CMP_PKIFAILUREINFO_badSenderNonce      18
# define OSSL_CMP_PKIFAILUREINFO_badCertTemplate     19
# define OSSL_CMP_PKIFAILUREINFO_signerNotTrusted    20
# define OSSL_CMP_PKIFAILUREINFO_transactionIdInUse  21
# define OSSL_CMP_PKIFAILUREINFO_unsupportedVersion  22
# define OSSL_CMP_PKIFAILUREINFO_notAuthorized       23
# define OSSL_CMP_PKIFAILUREINFO_systemUnavail       24
# define OSSL_CMP_PKIFAILUREINFO_systemFailure       25
# define OSSL_CMP_PKIFAILUREINFO_duplicateCertReq    26
# define OSSL_CMP_PKIFAILUREINFO_MAX                 26

  X509 *ossl_cmp_certresponse_get1_cert(const OSSL_CMP_CTX *ctx,
                                        const OSSL_CMP_CERTRESPONSE *crep);
  int ossl_cmp_pkisi_get_status(const OSSL_CMP_PKISI *si);
  const char *ossl_cmp_PKIStatus_to_string(int status);
  OSSL_CMP_PKIFREETEXT *ossl_cmp_pkisi_get0_statusString(const OSSL_CMP_PKISI *si);
  int ossl_cmp_pkisi_get_pkifailureinfo(const OSSL_CMP_PKISI *si);
  int ossl_cmp_pkisi_check_pkifailureinfo(const OSSL_CMP_PKISI *si, int index);

=head1 DESCRIPTION

ossl_cmp_certresponse_get1_cert() returns a pointer to a copy of the newly
enrolled certificate from the given certResponse I<crep>, or NULL on error.
Uses data from I<ctx>, which in case of indirect POPO includes the private key.

ossl_cmp_pkisi_get_status() returns the PKIStatus of I<si>, or -1 on error.

ossl_cmp_PKIStatus_to_string() returns a human-readable string representing
the PKIStatus values as specified in RFC 4210, Appendix F.

ossl_cmp_pkisi_get0_statusString() returns a direct pointer to the statusString
field contained in I<si>.

ossl_cmp_pkisi_get_pkifailureinfo() returns the PKIFailureInfo bits
of I<si>, encoded as integer, or -1 on error.

ossl_cmp_pkisi_check_pkifailureinfo() returns the state of the bit (0 or 1)
with index I<index> in the PKIFailureInfo of the I<si>, or -1 on error.

=head1 NOTES

CMP is defined in RFC 4210 (and CRMF in RFC 4211).

=head1 RETURN VALUES

See the individual functions above.

=head1 SEE ALSO

L<OSSL_CMP_CTX_new(3)>, L<ossl_cmp_certreq_new(3)>

=head1 HISTORY

The OpenSSL CMP support was added in OpenSSL 3.0.

=head1 COPYRIGHT

Copyright 2007-2023 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
