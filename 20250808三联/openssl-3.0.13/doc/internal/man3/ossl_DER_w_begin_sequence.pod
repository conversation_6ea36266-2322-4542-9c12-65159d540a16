=pod

=head1 NAME

ossl_DER_w_begin_sequence, ossl_DER_w_end_sequence
- internal DER writers for DER constructed elements

=head1 SYNOPSIS

 #include "internal/der.h"

 int ossl_DER_w_begin_sequence(WPACKET *pkt, int tag);
 int ossl_DER_w_end_sequence(WPACKET *pkt, int tag);

=head1 DESCRIPTION

All functions described here are wrappers for constructed structures,
i.e. the ASN.1 SEQUENCE, SET and CHOICE specifications.  They all come
in pairs, as noted by the function names containing the words C<begin>
and B<end>.

When using these, special care must be taken to ensure that the ASN.1 tag
value I<tag> is the same in the matching C<begin> and C<end> function calls.

ossl_DER_w_begin_sequence() and ossl_DER_w_end_sequence() begins and ends a
SEQUENCE.

=head1 RETURN VALUES

All the functions return 1 on success and 0 on failure.  Failure may
mean that the buffer held by the I<pkt> is too small, but may also
mean that the values given to the functions are invalid, such as the provided
I<tag> value being too large for the implementation.

=head1 SEE ALSO

L<DERlib(7)>

=head1 COPYRIGHT

Copyright 2020 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
