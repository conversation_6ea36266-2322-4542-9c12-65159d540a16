=pod

=head1 NAME

OSSL_METHOD_CONSTRUCT_METHOD, ossl_method_construct
- generic method constructor

=head1 SYNOPSIS

 #include "internal/core.h"

 struct ossl_method_construct_method_st {
     /* Get a temporary store */
     void *(*get_tmp_store)(void *data);
     /* Get an already existing method from a store */
     void *(*get)(void *store, const OSSL_PROVIDER *prov, void *data);
     /* Store a method in a store */
     int (*put)(void *store, void *method, const OSSL_PROVIDER *prov,
                const char *name, const char *propdef, void *data);
     /* Construct a new method */
     void *(*construct)(const OSSL_ALGORITHM *algodef, OSSL_PROVIDER *prov,
                        void *data);
     /* Destruct a method */
     void (*destruct)(void *method, void *data);
 };
 typedef struct ossl_method_construct_method OSSL_METHOD_CONSTRUCT_METHOD;

 void *ossl_method_construct(OSSL_LIB_CTX *ctx, int operation_id,
                             OSSL_PROVIDER *prov, int force_cache,
                             OSSL_METHOD_CONSTRUCT_METHOD *mcm, void *mcm_data);


=head1 DESCRIPTION

All libcrypto subsystems that want to create their own methods based
on provider dispatch tables need to do so in exactly the same way.
ossl_method_construct() does this while leaving it to the subsystems
to define more precisely how the methods are created, stored, etc.

It's important to keep in mind that a method is identified by three things:

=over 4

=item The operation identity

=item The name of the algorithm

=item The properties associated with the algorithm implementation

=back

=head2 Functions

ossl_method_construct() creates a method by asking all available
providers for a dispatch table given an I<operation_id>, and then
calling the appropriate functions given by the subsystem specific
method creator through I<mcm> and the data in I<mcm_data> (which is
passed by ossl_method_construct()).
If I<prov> is not NULL, only that provider is considered, which is
useful in the case a method must be found in that particular
provider.

This function assumes that the subsystem method creator implements
reference counting and acts accordingly (i.e. it will call the
subsystem destruct() method to decrement the reference count when
appropriate).

=head2 Structures

A central part of constructing a subsystem specific method is to give
ossl_method_construct a set of functions, all in the
B<OSSL_METHOD_CONSTRUCT_METHOD> structure, which holds the following
function pointers:

=over 4

=item get_tmp_store()

Create a temporary method store in the scope of the library context I<ctx>.
This store is used to temporarily store methods for easier lookup, for
when the provider doesn't want its dispatch table stored in a longer
term cache.

=item get()

Look up an already existing method from a store by name.

The store may be given with I<store>.
NULL is a valid value and means that a subsystem default store
must be used.
This default store should be stored in the library context I<libctx>.

The method to be looked up should be identified with data found in I<data>
(which is the I<mcm_data> that was passed to ossl_construct_method()).
In other words, the ossl_method_construct() caller is entirely responsible
for ensuring the necesssary data is made available.

Optionally, I<prov> may be given as a search criterion, to narrow down the
search of a method belonging to just one provider.

This function is expected to increment the resulting method's reference count.

=item put()

Places the I<method> created by the construct() function (see below)
in a store.

The store may be given with I<store>.
NULL is a valid value and means that a subsystem default store
must be used.
This default store should be stored in the library context I<libctx>.

The method should be associated with the given provider I<prov>,
I<name> and property definition I<propdef> as well as any
identification data given through I<data> (which is the I<mcm_data>
that was passed to ossl_construct_method()).

This function is expected to increment the I<method>'s reference count.

=item construct()

Constructs a subsystem method for the given I<name> and the given
dispatch table I<fns>.

The associated provider object I<prov> is passed as well, to make
it possible for the subsystem constructor to keep a reference, which
is recommended.
If such a reference is kept, the I<provider object> reference counter
must be incremented, using ossl_provider_up_ref().

This function is expected to set the method's reference count to 1.

=item destruct()

Decrement the I<method>'s reference count, and destruct it when
the reference count reaches zero.

=back

=head1 RETURN VALUES

ossl_method_construct() returns a constructed method on success, or
NULL on error.

=head1 HISTORY

This functionality was added to OpenSSL 3.0.

=head1 COPYRIGHT

Copyright 2019-2021 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use this
file except in compliance with the License.  You can obtain a copy in the file
LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
