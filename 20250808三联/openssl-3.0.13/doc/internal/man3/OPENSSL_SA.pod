=pod

=head1 NAME

OPENSSL_SA, ossl_sa_TYPE_new, ossl_sa_TYPE_free,
ossl_sa_TYPE_free_leaves, ossl_sa_TYPE_num, ossl_sa_TYPE_doall,
ossl_sa_TYPE_doall_arg, ossl_sa_TYPE_get, ossl_sa_TYPE_set
- sparse array container

=head1 SYNOPSIS

 #include "crypto/sparse_array.h"

 typedef struct sparse_array_st OPENSSL_SA;

 SPARSE_ARRAY_OF(TYPE)
 DEFINE_SPARSE_ARRAY_OF(TYPE)

 SPARSE_ARRAY_OF(TYPE) *ossl_sa_TYPE_new(void);
 void ossl_sa_TYPE_free(const SPARSE_ARRAY_OF(TYPE) *sa);
 void ossl_sa_TYPE_free_leaves(const SPARSE_ARRAY_OF(TYPE) *sa);
 size_t ossl_sa_TYPE_num(const SPARSE_ARRAY_OF(TYPE) *sa);
 void ossl_sa_TYPE_doall(const OPENSSL_SA *sa, void (*leaf)(ossl_uintmax_t,
                                                            void *));
 void ossl_sa_TYPE_doall_arg(const OPENSSL_SA *sa,
                             void (*leaf)(ossl_uintmax_t, void *, void *),
                             void *arg);
 TYPE *ossl_sa_TYPE_get(const SPARSE_ARRAY_OF(TYPE) *sa, ossl_uintmax_t idx);
 int ossl_sa_TYPE_set(SPARSE_ARRAY_OF(TYPE) *sa, ossl_uintmax_t idx,
                      TYPE *value);

=head1 DESCRIPTION

=begin comment

POD is pretty good at recognising function names and making them appropriately
bold...  however, when part of the function name is variable, we have to help
the processor along

=end comment

SPARSE_ARRAY_OF() returns the name for a sparse array of the specified
B<I<TYPE>>.  DEFINE_SPARSE_ARRAY_OF() creates set of functions for a sparse
array of B<I<TYPE>>. This will mean that a pointer to type B<I<TYPE>>
is stored in each element of a sparse array, the type is referenced by
B<SPARSE_ARRAY_OF>(B<I<TYPE>>) and each function name begins with
B<ossl_sa_I<TYPE>_>. For example:

 TYPE *ossl_sa_TYPE_get(SPARSE_ARRAY_OF(TYPE) *sa, ossl_uintmax_t idx);

B<ossl_sa_I<TYPE>_num>() returns the number of elements in I<sa> or 0 if I<sa>
is NULL.

B<ossl_sa_I<TYPE>_get>() returns element I<idx> in I<sa>, where I<idx> starts
at zero. If I<idx> refers to a value that has not been set then NULL is
returned.

B<ossl_sa_I<TYPE>_set>() sets element I<idx> in I<sa> to I<value>, where I<idx>
starts at zero. The sparse array will be resized as required.

B<ossl_sa_I<TYPE>_new>() allocates a new empty sparse array.

B<ossl_sa_I<TYPE>_free>() frees up the I<sa> structure. It does I<not> free up any
elements of I<sa>. After this call I<sa> is no longer valid.

B<ossl_sa_I<TYPE>_free_leaves>() frees up the I<sa> structure and all of its
elements.  After this call I<sa> is no longer valid.

B<ossl_sa_I<TYPE>_doall>() calls the function I<leaf> for each element in I<sa>
in ascending index order. The index position, within the sparse array,
of each item is passed as the first argument to the leaf function and a
pointer to the associated value is passed as the second argument.

B<ossl_sa_I<TYPE>_doall_arg>() calls the function I<leaf> for each element in
I<sa> in ascending index order. The index position, within the sparse
array, of each item is passed as the first argument to the leaf function,
a pointer to the associated value is passed as the second argument and
the third argument is the user supplied I<arg>.


=head1 NOTES

Sparse arrays are an internal data structure and should B<not> be used by user
applications.

Care should be taken when accessing sparse arrays in multi-threaded
environments.  The B<ossl_sa_I<TYPE>_set>() operation can cause the internal
structure of the sparse array to change which causes race conditions if the
sparse array is accessed in a different thread.

SPARSE_ARRAY_OF() and DEFINE_SPARSE_ARRAY_OF() are implemented as macros.

The underlying utility B<OPENSSL_SA_> API should not be used directly.  It
defines these functions: OPENSSL_SA_doall, OPENSSL_SA_doall_arg,
OPENSSL_SA_free, OPENSSL_SA_free_leaves, OPENSSL_SA_get, OPENSSL_SA_new,
OPENSSL_SA_num and OPENSSL_SA_set.

=head1 RETURN VALUES

B<ossl_sa_I<TYPE>_num>() returns the number of elements in the sparse array or
B<0> if the passed sparse array is NULL.

B<ossl_sa_I<TYPE>_get>() returns a pointer to a sparse array element or NULL if
the element has not be set.

B<ossl_sa_I<TYPE>_set>() return B<1> on success and B<0> on error. In the latter
case, the elements of the sparse array remain unchanged, although the internal
structures might have.

B<ossl_sa_I<TYPE>_new>() returns an empty sparse array or NULL if an error
occurs.

B<ossl_sa_I<TYPE>_doall>(), B<ossl_sa_I<TYPE>_doall_arg>(),
B<ossl_sa_I<TYPE>_free>() and B<ossl_sa_I<TYPE>_free_leaves>()
do not return values.

=head1 HISTORY

This functionality was added to OpenSSL 3.0.

=head1 COPYRIGHT

Copyright 2019-2021 The OpenSSL Project Authors. All Rights Reserved.  Copyright
(c) 2019, Oracle and/or its affiliates.  All rights reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use this
file except in compliance with the License.  You can obtain a copy in the file
LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
