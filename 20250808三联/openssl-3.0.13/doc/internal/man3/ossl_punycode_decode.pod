=pod

=head1 NAME

ossl_punycode_decode, ossl_a2ulabel, ossl_a2ucompare
- internal punycode-related functions

=head1 SYNOPSIS

  #include "crypto/punycode.h"

  int ossl_punycode_decode(const char *pEncoded, const size_t enc_len,
                           unsigned int *pDecoded, unsigned int *pout_length);

  int ossl_a2ulabel(const char *in, char *out, size_t *outlen);

  int ossl_a2ucompare(const char *a, const char *u);

=head1 DESCRIPTION

PUNYCODE encoding introduced in RFCs 3490-3492 is widely used for
representation of host names in ASCII-only format. Some specifications,
such as RFC 8398, require comparison of host names encoded in UTF-8 charset.

ossl_a2ulabel() decodes NUL-terminated hostname from PUNYCODE to UTF-8,
using a provided buffer for output.

ossl_a2ucompare() accepts two NUL-terminated hostnames, decodes the 1st
from PUNYCODE to UTF-8 and compares it with the 2nd one as is.

ossl_punycode_decode() decodes one label (one dot-separated part) from
a hostname, with stripped PUNYCODE marker I<xn-->.

=head1 RETURN VALUES

ossl_a2ulabel() returns 1 on success, 0 on not enough buf passed,
-1 on invalid PUNYCODE string passed. When valid string is provided, it sets the
I<*outlen> to the length of required buffer to perform correct decoding.

ossl_a2ucompare() returns 1 on non-equal strings, 0 on equal strings,
-1 when invalid PUNYCODE string passed.

ossl_punycode_decode() returns 1 on success, 0 on error. On success,
*pout_length contains the number of codepoints decoded.

=head1 HISTORY

The functions described here were all added in OpenSSL 3.0.

=head1 COPYRIGHT

Copyright 2019-2021 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut

