=pod

=head1 NAME

ossl_namemap_new, ossl_namemap_free, ossl_namemap_stored, ossl_namemap_empty,
ossl_namemap_add_name, ossl_namemap_add_name_n, ossl_namemap_add_names,
ossl_namemap_name2num, ossl_namemap_name2num_n,
ossl_namemap_doall_names
- internal number E<lt>-E<gt> name map

=head1 SYNOPSIS

 #include "internal/cryptlib.h"

 OSSL_NAMEMAP *ossl_namemap_stored(OSSL_LIB_CTX *libctx);

 OSSL_NAMEMAP *ossl_namemap_new(void);
 void ossl_namemap_free(OSSL_NAMEMAP *namemap);
 int ossl_namemap_empty(OSSL_NAMEMAP *namemap);

 int ossl_namemap_add_name(OSSL_NAMEMAP *namemap, int number, const char *name);
 int ossl_namemap_add_name_n(OSSL_NAMEMAP *namemap, int number,
                             const char *name, size_t name_len);

 int ossl_namemap_name2num(const OSSL_NAMEMAP *namemap, const char *name);
 int ossl_namemap_name2num_n(const OSSL_NAMEMAP *namemap,
                             const char *name, size_t name_len);
 int ossl_namemap_doall_names(const OSSL_NAMEMAP *namemap, int number,
                              void (*fn)(const char *name, void *data),
                              void *data);

 int ossl_namemap_add_names(OSSL_NAMEMAP *namemap, int number,
                            const char *names, const char separator);

=head1 DESCRIPTION

A B<OSSL_NAMEMAP> is a one-to-many number E<lt>-E<gt> names map, which
can be used to give any arbitrary set of names (any string) a unique
dynamic identity that is valid throughout the lifetime of the associated
library context.

ossl_namemap_new() and ossl_namemap_free() construct and destruct a
new B<OSSL_NAMEMAP>.
This is suitable to use when the B<OSSL_NAMEMAP> is embedded in other
structures, or should be independent for any reason.

ossl_namemap_empty() checks if the given B<OSSL_NAMEMAP> is empty or
not.

ossl_namemap_stored() finds or auto-creates the default namemap in the
given library context.
The returned B<OSSL_NAMEMAP> can't be destructed using
ossl_namemap_free().

ossl_namemap_add_name() adds a new name to the namemap if it's not already
present.
If the given I<number> is zero, a new number will be allocated to
identify this I<name>.
If the given I<number> is nonzero, the I<name> is added to the set of
names already associated with that number.

ossl_namemap_name2num() finds the number corresponding to the given
I<name>.

ossl_namemap_add_name_n() and ossl_namemap_name2num_n() do the same thing
as ossl_namemap_add_name() and ossl_namemap_name2num(), but take a string
length I<name_len> as well, allowing the caller to use a fragment of
a string as a name.

ossl_namemap_doall_names() walks through all names associated with
I<number> in the given I<namemap> and calls the function I<fn> for
each of them.
I<fn> is also passed the I<data> argument, which allows any caller to
pass extra data for that function to use.

ossl_namemap_add_names() divides up a set of names given in I<names>,
separated by I<separator>, and adds each to the I<namemap>, all with
the same number.  If some of them already exist in the I<namemap>,
they must all have the same associated number, which will be adopted
for any name that doesn't exist yet.

=head1 RETURN VALUES

ossl_namemap_new() and ossl_namemap_stored() return the pointer to a
B<OSSL_NAMEMAP>, or NULL on error.

ossl_namemap_empty() returns 1 if the B<OSSL_NAMEMAP> is NULL or
empty, 0 if it's not empty, or -1 on internal error (such as inability
to lock).

ossl_namemap_add_name() and ossl_namemap_add_name_n() return the number
associated with the added string, or zero on error.

ossl_namemap_num2names() returns a pointer to a NULL-terminated list of
pointers to the names corresponding to the given number, or NULL if
it's undefined in the given B<OSSL_NAMEMAP>.

ossl_namemap_name2num() and ossl_namemap_name2num_n() return the number
corresponding to the given name, or 0 if it's undefined in the given
B<OSSL_NAMEMAP>.

ossl_namemap_doall_names() returns 1 if the callback was called for all names. A
return value of 0 means that the callback was not called for any names.

ossl_namemap_add_names() returns the number associated with the added
names, or zero on error.

=head1 NOTES

The result from ossl_namemap_num2names() isn't thread safe, other threads
dealing with the same namemap may cause the list of names to change
location.
It is therefore strongly recommended to only use the result in code
guarded by a thread lock.

=head1 HISTORY

The functions described here were all added in OpenSSL 3.0.

=head1 COPYRIGHT

Copyright 2019-2021 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
