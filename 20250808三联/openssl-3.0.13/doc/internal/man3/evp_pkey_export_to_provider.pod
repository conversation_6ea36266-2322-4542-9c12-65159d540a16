=pod

=head1 NAME

evp_pkey_export_to_provider, evp_pkey_copy_downgraded, evp_pkey_get_legacy
- internal EVP_PKEY support functions for providers

=head1 SYNOPSIS

 /* Only for EVP source */
 #include "evp_local.h"

 void *evp_pkey_export_to_provider(EVP_PKEY *pk, OSSL_LIB_CTX *libctx,
                                   EVP_KEYMGMT **keymgmt,
                                   const char *propquery);
 int evp_pkey_copy_downgraded(EVP_PKEY **dest, const EVP_PKEY *src);
 void *evp_pkey_get_legacy(EVP_PKEY *pk);

=head1 DESCRIPTION

This manual uses the term "origin", which is explained in internal
L<EVP_PKEY(7)>.

evp_pkey_export_to_provider() exports the "origin" key contained in I<pk>
to its operation cache to make it suitable for an B<EVP_KEYMGMT> given either
with I<*keymgmt> or with an implicit fetch using I<libctx> (NULL means the
default context), the name of the legacy type of I<pk>, and the I<propquery>
(NULL means the default property query settings).

If I<keymgmt> isn't NULL but I<*keymgmt> is, and the "origin" was successfully
exported, then I<*keymgmt> is assigned the implicitly fetched B<EVP_KEYMGMT>.

evp_pkey_copy_downgraded() makes a copy of I<src> in legacy form into I<*dest>,
if there's a corresponding legacy implementation.  This should be used if the
use of a downgraded key is temporary.
For example, L<PEM_write_bio_PrivateKey_traditional(3)> uses this to try its
best to get "traditional" PEM output even if the input B<EVP_PKEY> has a
provider-native internal key.

evp_pkey_get_legacy() obtains and returns a legacy key structure. If the
EVP_PKEY already contains a legacy key then it is simply returned. If it is a
provider based key, then a new legacy key is constructed based on the provider
key. The legacy key is cached inside the EVP_PKEY and its value returned from
this function. Subsequent calls to evp_pkey_get_legacy() will return the cached
key. Subsequent changes to the provider key are not reflected back in the
legacy key. Similarly changes to the legacy key are not reflected back in the
provider key.

=head1 RETURN VALUES

evp_pkey_export_to_provider() returns the provider key data if there was any
allocated.  It also either sets I<*keymgmt> to the B<EVP_KEYMGMT> associated
with the returned key data, or NULL on error.

evp_pkey_get_legacy() returns the legacy key or NULL on error.

=head1 NOTES

Some functions calling evp_pkey_export_to_provider() may have received a const
key, and may therefore have to cast the key to non-const form to call this
function.  Since B<EVP_PKEY> is always dynamically allocated, this is OK.

=head1 SEE ALSO

L<OSSL_LIB_CTX(3)>, L<EVP_KEYMGMT(3)>

=head1 HISTORY

The functions described here were all added in OpenSSL 3.0.

=head1 COPYRIGHT

Copyright 2020-2021 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
