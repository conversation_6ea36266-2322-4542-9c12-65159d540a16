=pod

=head1 NAME

OSSL_CMP_PKIBODY_IR,
OSSL_CMP_PKIBODY_IP,
OSSL_CMP_PKIBODY_CR,
OSSL_CMP_PKIBODY_CP,
OSSL_CMP_PKIBODY_P10CR,
OSSL_CMP_PKIBODY_POPDECC,
OSSL_CMP_PKIBODY_POPDECR,
OSSL_CMP_PKIBODY_KUR,
OSSL_CMP_PKIBODY_KUP,
OSSL_CMP_PKIBODY_KRR,
OSSL_CMP_PKIBODY_KRP,
OSSL_CMP_PKIBODY_RR,
OSSL_CMP_PKIBODY_RP,
OSSL_CMP_PKIBODY_CCR,
OSSL_CMP_PKIBODY_CCP,
OSSL_CMP_PKIBODY_CKUANN,
OSSL_CMP_PKIBODY_CANN,
OSSL_CMP_PKIBODY_RANN,
OSSL_CMP_PKIBODY_CRLANN,
OSSL_CMP_PKIBODY_PKICONF,
OSSL_CMP_PKIBODY_NESTED,
OSSL_CMP_PKIBODY_GENM,
OSSL_CMP_PKIBODY_GENP,
OSSL_CMP_PKIBODY_ERROR,
OSSL_CMP_PKIBODY_CERTCONF,
OSSL_CMP_PKIBODY_POLLREQ,
OSSL_CMP_PKIBODY_POLLREP,
ossl_cmp_bodytype_to_string,
ossl_cmp_msg_get_bodytype,
ossl_cmp_msg_set_bodytype,
ossl_cmp_msg_create,
ossl_cmp_msg_gen_ITAV_push0,
ossl_cmp_msg_gen_ITAVs_push1
- functions handling CMP messages

=head1 SYNOPSIS

 #include "cmp_local.h"

 #define OSSL_CMP_PKIBODY_IR        0
 #define OSSL_CMP_PKIBODY_IP        1
 #define OSSL_CMP_PKIBODY_CR        2
 #define OSSL_CMP_PKIBODY_CP        3
 #define OSSL_CMP_PKIBODY_P10CR     4
 #define OSSL_CMP_PKIBODY_POPDECC   5
 #define OSSL_CMP_PKIBODY_POPDECR   6
 #define OSSL_CMP_PKIBODY_KUR       7
 #define OSSL_CMP_PKIBODY_KUP       8
 #define OSSL_CMP_PKIBODY_KRR       9
 #define OSSL_CMP_PKIBODY_KRP      10
 #define OSSL_CMP_PKIBODY_RR       11
 #define OSSL_CMP_PKIBODY_RP       12
 #define OSSL_CMP_PKIBODY_CCR      13
 #define OSSL_CMP_PKIBODY_CCP      14
 #define OSSL_CMP_PKIBODY_CKUANN   15
 #define OSSL_CMP_PKIBODY_CANN     16
 #define OSSL_CMP_PKIBODY_RANN     17
 #define OSSL_CMP_PKIBODY_CRLANN   18
 #define OSSL_CMP_PKIBODY_PKICONF  19
 #define OSSL_CMP_PKIBODY_NESTED   20
 #define OSSL_CMP_PKIBODY_GENM     21
 #define OSSL_CMP_PKIBODY_GENP     22
 #define OSSL_CMP_PKIBODY_ERROR    23
 #define OSSL_CMP_PKIBODY_CERTCONF 24
 #define OSSL_CMP_PKIBODY_POLLREQ  25
 #define OSSL_CMP_PKIBODY_POLLREP  26

  const char *ossl_cmp_bodytype_to_string(int type);
  int ossl_cmp_msg_get_bodytype(const OSSL_CMP_MSG *msg);
  int ossl_cmp_msg_set_bodytype( OSSL_CMP_MSG *msg, int type);
  OSSL_CMP_MSG *ossl_cmp_msg_create(OSSL_CMP_CTX *ctx, int bodytype);
  int ossl_cmp_msg_gen_ITAV_push0(OSSL_CMP_MSG *msg, OSSL_CMP_ITAV *itav);
  int ossl_cmp_msg_gen_ITAVs_push1(OSSL_CMP_MSG *msg,
                                    STACK_OF(OSSL_CMP_ITAV) *itavs);

=head1 DESCRIPTION

ossl_cmp_bodytype_to_string() returns the name of the given body type as string,
or "illegal body type" on error.

ossl_cmp_msg_get_bodytype() returns the body type of the given PKIMessage,
or -1 on error.

ossl_cmp_msg_set_bodytype() sets the type of the message contained in
the PKIMessage body field.
Returns 1 on success, 0 on error.

ossl_cmp_msg_create() creates and initializes an B<OSSL_CMP_MSG> structure,
using fields of B<ctx> for the header and B<bodytype> for the body.
If the current B<transactionID> field in I<ctx> indicates that there is no
current transaction, it creates and stores a random one with 128 bits length.
Thus, the I<ctx> may be modified by this and related ossl_cmp_*_new() functions.
Returns pointer to created B<OSSL_CMP_MSG> on success, NULL on error.

ossl_cmp_msg_gen_ITAV_push0() pushes the B<itav> to the body of the
PKIMessage B<msg> of GenMsg or GenRep type. Consumes the B<itavs> pointer.
Returns 1 on success, 0 on error.

ossl_cmp_msg_gen_ITAVs_push1() adds a copy of the B<itavs> stack to the body
of the PKIMessage B<msg> of GenMsg or GenRep type.
Does not consume the B<itavs> pointer nor its elements.
Returns 1 on success, 0 on error.

=head1 NOTES

CMP is defined in RFC 4210 (and CRMF in RFC 4211).

=head1 RETURN VALUES

See the individual functions above.

=head1 SEE ALSO

L<ossl_cmp_hdr_init(3)>,
L<OSSL_CMP_CTX_new(3)>, L<OSSL_CMP_exec_certreq(3)>

=head1 HISTORY

The OpenSSL CMP support was added in OpenSSL 3.0.

=head1 COPYRIGHT

Copyright 2007-2021 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
