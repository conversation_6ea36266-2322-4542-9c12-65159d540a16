=pod

=head1 NAME

ossl_provider_find, ossl_provider_new, ossl_provider_up_ref,
ossl_provider_free,
ossl_provider_set_fallback, ossl_provider_set_module_path,
ossl_provider_add_parameter, ossl_provider_set_child, ossl_provider_get_parent,
ossl_provider_up_ref_parent, ossl_provider_free_parent,
ossl_provider_default_props_update, ossl_provider_get0_dispatch,
ossl_provider_init_as_child, ossl_provider_deinit_child,
ossl_provider_activate, ossl_provider_deactivate, ossl_provider_add_to_store,
ossl_provider_ctx,
ossl_provider_doall_activated,
ossl_provider_name, ossl_provider_dso,
ossl_provider_module_name, ossl_provider_module_path,
ossl_provider_libctx,
ossl_provider_teardown, ossl_provider_gettable_params,
ossl_provider_get_params,
ossl_provider_query_operation, ossl_provider_unquery_operation,
ossl_provider_set_operation_bit, ossl_provider_test_operation_bit,
ossl_provider_get_capabilities
- internal provider routines

=head1 SYNOPSIS

 #include "internal/provider.h"

 OSSL_PROVIDER *ossl_provider_find(OSSL_LIB_CTX *libctx, const char *name,
                                   int noconfig);
 OSSL_PROVIDER *ossl_provider_new(OSSL_LIB_CTX *libctx, const char *name,
                                  ossl_provider_init_fn *init_function
                                  int noconfig);
 int ossl_provider_up_ref(OSSL_PROVIDER *prov);
 void ossl_provider_free(OSSL_PROVIDER *prov);

 /* Setters */
 int ossl_provider_set_fallback(OSSL_PROVIDER *prov);
 int ossl_provider_set_module_path(OSSL_PROVIDER *prov, const char *path);
 int ossl_provider_add_parameter(OSSL_PROVIDER *prov, const char *name,
                                 const char *value);

 /* Child Providers */
 int ossl_provider_set_child(OSSL_PROVIDER *prov,
                             const OSSL_CORE_HANDLE *handle);
 const OSSL_CORE_HANDLE *ossl_provider_get_parent(OSSL_PROVIDER *prov);
 int ossl_provider_up_ref_parent(OSSL_PROVIDER *prov, int activate);
 int ossl_provider_free_parent(OSSL_PROVIDER *prov, int deactivate);
 int ossl_provider_default_props_update(OSSL_LIB_CTX *libctx,
                                        const char *props);

 /*
  * Activate the Provider
  * If the Provider is a module, the module will be loaded
  */
 int ossl_provider_activate(OSSL_PROVIDER *prov, int upcalls, int aschild);
 int ossl_provider_deactivate(OSSL_PROVIDER *prov, int removechildren);
 int ossl_provider_add_to_store(OSSL_PROVIDER *prov, OSSL_PROVIDER **actualprov,
                                int retain_fallbacks);

 /* Return pointer to the provider's context */
 void *ossl_provider_ctx(const OSSL_PROVIDER *prov);

 const OSSL_DISPATCH *ossl_provider_get0_dispatch(const OSSL_PROVIDER *prov);

 /* Iterate over all loaded providers */
 int ossl_provider_doall_activated(OSSL_LIB_CTX *,
                                   int (*cb)(OSSL_PROVIDER *provider,
                                             void *cbdata),
                                   void *cbdata);

 /* Getters for other library functions */
 const char *ossl_provider_name(OSSL_PROVIDER *prov);
 const DSO *ossl_provider_dso(OSSL_PROVIDER *prov);
 const char *ossl_provider_module_name(OSSL_PROVIDER *prov);
 const char *ossl_provider_module_path(OSSL_PROVIDER *prov);
 OSSL_LIB_CTX *ossl_provider_libctx(const OSSL_PROVIDER *prov);

 /* Thin wrappers around calls to the provider */
 void ossl_provider_teardown(const OSSL_PROVIDER *prov);
 const OSSL_PARAM *ossl_provider_gettable_params(const OSSL_PROVIDER *prov);
 int ossl_provider_get_params(const OSSL_PROVIDER *prov, OSSL_PARAM params[]);
 int ossl_provider_get_capabilities(const OSSL_PROVIDER *prov,
                                   const char *capability,
                                   OSSL_CALLBACK *cb,
                                   void *arg);
 const OSSL_ALGORITHM *ossl_provider_query_operation(const OSSL_PROVIDER *prov,
                                                     int operation_id,
                                                     int *no_cache);
 void ossl_provider_unquery_operation(const OSSL_PROVIDER *prov,
                                      int operation_id,
                                      const OSSL_ALGORITHM *algs);

 int ossl_provider_set_operation_bit(OSSL_PROVIDER *provider, size_t bitnum);
 int ossl_provider_test_operation_bit(OSSL_PROVIDER *provider, size_t bitnum,
                                      int *result);

 int ossl_provider_init_as_child(OSSL_LIB_CTX *ctx,
                                 const OSSL_CORE_HANDLE *handle,
                                 const OSSL_DISPATCH *in);
 void ossl_provider_deinit_child(OSSL_LIB_CTX *ctx);

=head1 DESCRIPTION

I<OSSL_PROVIDER> is a type that holds all the necessary information
to handle a provider, regardless of if it's built in to the
application or the OpenSSL libraries, or if it's a loadable provider
module.
Instances of this type are commonly referred to as "provider objects".

A provider object is always stored in a set of provider objects
in the library context.

Provider objects are reference counted.

Provider objects are initially inactive, i.e. they are only recorded
in the store, but are not used.
They are activated with the first call to ossl_provider_activate(),
and are deactivated with the last call to ossl_provider_deactivate().
Activation affects a separate counter.

=head2 Functions

ossl_provider_find() finds an existing provider object in the provider
object store by I<name>.
The config file will be automatically loaded unless I<noconfig> is set.
Typically I<noconfig> should be 0.
We set I<noconfig> to 1 only when calling these functions while processing a
config file in order to avoid recursively attempting to load the file.
The provider object it finds has its reference count incremented.

ossl_provider_new() creates a new provider object named I<name> and
stores it in the provider object store, unless there already is one
there with the same name.
If there already is one with the same name, it's returned with its
reference count incremented.
The config file will be automatically loaded unless I<noconfig> is set.
Typically I<noconfig> should be 0.
We set I<noconfig> to 1 only when calling these functions while processing a
config file in order to avoid recursively attempting to load the file.
The reference count of a newly created provider object will always
be 2; one for being added to the store, and one for the returned
reference.
If I<init_function> is NULL, the provider is assumed to be a
dynamically loadable module, with the symbol B<OSSL_provider_init> as
its initialisation function.
If I<init_function> isn't NULL, the provider is assumed to be built
in, with I<init_function> being the pointer to its initialisation
function.
For further description of the initialisation function, see the
description of ossl_provider_activate() below.

ossl_provider_up_ref() increments the provider object I<prov>'s
reference count.

ossl_provider_free() decrements the provider object I<prov>'s
reference count; when it drops to zero, the provider object is assumed
to have fallen out of use and will be deinitialized (its I<teardown>
function is called), and the associated module will be unloaded if one
was loaded, and I<prov> itself will be freed.

ossl_provider_set_fallback() marks an available provider I<prov> as
fallback.
Note that after this call, the provider object pointer that was
used can simply be dropped, but not freed.

ossl_provider_set_module_path() sets the module path to load the
provider module given the provider object I<prov>.
This will be used in preference to automatically trying to figure out
the path from the provider name and the default module directory (more
on this in L</NOTES>).

ossl_provider_libctx() returns the library context the given
provider I<prov> is registered in.

ossl_provider_add_parameter() adds a global parameter for the provider
to retrieve as it sees fit.
The parameters are a combination of I<name> and I<value>, and the
provider will use the name to find the value it wants.
Only text parameters can be given, and it's up to the provider to
interpret them.

ossl_provider_set_child() marks this provider as a child of a provider in the
parent library context. I<handle> is the B<OSSL_CORE_HANDLE> object passed to
the provider's B<OSSL_provider_init> function.

ossl_provider_get_parent() obtains the handle on the parent provider.

ossl_provider_up_ref_parent() increases the reference count on the parent
provider. If I<activate> is nonzero then the parent provider is also activated.

ossl_provider_free_parent() decreases the reference count on the parent
provider. If I<deactivate> is nonzero then the parent provider is also
deactivated.

ossl_provider_default_props_update() is responsible for informing any child
providers of an update to the default properties. The new properties are
supplied in the I<props> string.

ossl_provider_activate() "activates" the provider for the given
provider object I<prov> by incrementing its activation count, flagging
it as activated, and initializing it if it isn't already initialized.
Initializing means one of the following:

=over 4

=item *

If an initialization function was given with ossl_provider_new(), that
function will get called.

=item *

If no initialization function was given with ossl_provider_new(), a
loadable module with the I<name> that was given to ossl_provider_new()
will be located and loaded, then the symbol B<OSSL_provider_init> will
be located in that module, and called.

=back

If I<upcalls> is nonzero then, if this is a child provider, upcalls to the
parent libctx will be made to inform it of an up-ref. If I<aschild> is nonzero
then the provider will only be activated if it is a child provider. Otherwise
no action is taken and ossl_provider_activate() returns success.

ossl_provider_deactivate() "deactivates" the provider for the given
provider object I<prov> by decrementing its activation count.  When
that count reaches zero, the activation flag is cleared. If the
I<removechildren> parameter is 0 then no attempt is made to remove any
associated child providers.

ossl_provider_add_to_store() adds the provider I<prov> to the provider store and
makes it available to other threads. This will prevent future automatic loading
of fallback providers, unless I<retain_fallbacks> is true. If a provider of the
same name already exists in the store then it is not added but this function
still returns success. On success the I<actualprov> value is populated with a
pointer to the provider of the given name that is now in the store. The
reference passed in the I<prov> argument is consumed by this function. A
reference to the provider that should be used is passed back in the
I<actualprov> argument.

ossl_provider_ctx() returns a context created by the provider.
Outside of the provider, it's completely opaque, but it needs to be
passed back to some of the provider functions.

ossl_provider_get0_dispatch() returns the dispatch table that the provider
initially returned in the I<out> parameter of its B<OSSL_provider_init>
function.

ossl_provider_doall_activated() iterates over all the currently
"activated" providers, and calls I<cb> for each of them.
If no providers have been "activated" yet, it tries to activate all
available fallback providers before iterating over them.

ossl_provider_name() returns the name that was given with
ossl_provider_new().

ossl_provider_dso() returns a reference to the module, for providers
that come in the form of loadable modules.

ossl_provider_module_name() returns the filename of the module, for
providers that come in the form of loadable modules.

ossl_provider_module_path() returns the full path of the module file,
for providers that come in the form of loadable modules.

ossl_provider_teardown() calls the provider's I<teardown> function, if
the provider has one.

ossl_provider_gettable_params() calls the provider's I<gettable_params>
function, if the provider has one.
It should return an array of I<OSSL_PARAM> to describe all the
parameters that the provider has for the provider object.

ossl_provider_get_params() calls the provider's parameter request
responder.
It should treat the given I<OSSL_PARAM> array as described in
L<OSSL_PARAM(3)>.

ossl_provider_get_capabilities() calls the provider's I<get_capabilities> function,
if the provider has one. It provides the name of the I<capability> and a
callback I<cb> parameter to call for each capability that has a matching name in
the provider. The callback gets passed OSSL_PARAM details about the capability as
well as the caller supplied argument I<arg>.

ossl_provider_query_operation() calls the provider's
I<query_operation> function, if the provider has one.
It should return an array of I<OSSL_ALGORITHM> for the given
I<operation_id>.

ossl_provider_unquery_operation() informs the provider that the result of
ossl_provider_query_operation() is no longer going to be directly accessed and
that all relevant information has been copied.

ossl_provider_set_operation_bit() registers a 1 for operation I<bitnum>
in a bitstring that's internal to I<provider>.

ossl_provider_test_operation_bit() checks if the bit operation I<bitnum>
is set (1) or not (0) in the internal I<provider> bitstring, and sets
I<*result> to 1 or 0 accorddingly.

ossl_provider_init_as_child() stores in the library context I<ctx> references to
the necessary upcalls for managing child providers. The I<handle> and I<in>
parameters are the B<OSSL_CORE_HANDLE> and L<OSSL_DISPATCH(3)> pointers that were
passed to the provider's B<OSSL_provider_init> function.

ossl_provider_deinit_child() deregisters callbacks from the parent library
context about provider creation or removal events for the child library context
I<ctx>. Must only be called if I<ctx> is a child library context.

=head1 NOTES

Locating a provider module happens as follows:

=over 4

=item 1.

If a path was given with ossl_provider_set_module_path(), use that as
module path.
Otherwise, use the provider object's name as module path, with
platform specific standard extensions added.

=item 2.

If the environment variable B<OPENSSL_MODULES> is defined, assume its
value is a directory specification and merge it with the module path.
Otherwise, merge the value of the OpenSSL built in macro B<MODULESDIR>
with the module path.

=back

When this process is done, the result is used when trying to load the
provider module.

The command C<openssl version -m> can be used to find out the value
of the built in macro B<MODULESDIR>.

=head1 RETURN VALUES

ossl_provider_find() and ossl_provider_new() return a pointer to a
provider object (I<OSSL_PROVIDER>) on success, or NULL on error.

ossl_provider_up_ref() returns the value of the reference count after
it has been incremented.

ossl_provider_free() doesn't return any value.

ossl_provider_doall_activated() returns 1 if the callback was called for all
activated providers.  A return value of 0 means that the callback was not
called for any activated providers.

ossl_provider_set_module_path(), ossl_provider_set_fallback(),
ossl_provider_activate(), ossl_provider_activate_leave_fallbacks() and
ossl_provider_deactivate(), ossl_provider_add_to_store(),
ossl_provider_default_props_update() return 1 on success, or 0 on error.

ossl_provider_name(), ossl_provider_dso(),
ossl_provider_module_name(), and ossl_provider_module_path() return a
pointer to their respective data if it's available, otherwise NULL
is returned.

ossl_provider_libctx() return a pointer to the library context.
This may be NULL, and is perfectly valid, as it denotes the default
global library context.

ossl_provider_teardown() doesn't return any value.

ossl_provider_gettable_params() returns a pointer to a constant
I<OSSL_PARAM> array if this function is available in the provider,
otherwise NULL.

ossl_provider_get_params() returns 1 on success, or 0 on error.
If this function isn't available in the provider, 0 is returned.

ossl_provider_set_operation_bit() and ossl_provider_test_operation_bit()
return 1 on success, or 0 on error.

ossl_provider_get_capabilities() returns 1 on success, or 0 on error.
If this function isn't available in the provider or the provider does not
support the requested capability then 0 is returned.

=head1 SEE ALSO

L<OSSL_PROVIDER(3)>, L<provider(7)>, L<openssl(1)>

=head1 HISTORY

The functions described here were all added in OpenSSL 3.0.

=head1 COPYRIGHT

Copyright 2019-2022 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
