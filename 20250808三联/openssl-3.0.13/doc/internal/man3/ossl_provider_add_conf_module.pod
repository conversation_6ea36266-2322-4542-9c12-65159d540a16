=pod

=head1 NAME

ossl_provider_add_conf_module - internal standard configuration module

=head1 SYNOPSIS

 #include "internal/provider.h"

 /* Configuration */
 void ossl_provider_add_conf_module(void);

=head1 DESCRIPTION

ossl_provider_add_conf_module() adds the standard configuration module
for providers.
This allows providers to be configured with an OpenSSL L<config(5)> file.

=head1 RETURN VALUES

ossl_provider_add_conf_module() doesn't return any value.

=head1 SEE ALSO

L<OSSL_PROVIDER(3)>, L<ossl_provider_new(3)>

=head1 HISTORY

The functions described here were all added in OpenSSL 3.0.

=head1 COPYRIGHT

Copyright 2019 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
