=pod

=head1 NAME

OSSL_DEPRECATED, OSSL_DEPRECATED_FOR - General deprecation macros

=head1 SYNOPSIS

 #include <openssl/macros.h>

 #define OSSL_DEPRECATED(since)
 #define OSSL_DEPRECATED_FOR(since, msg)

=head1 DESCRIPTION

OSSL_DEPRECATED() implements the deprecated attribute if the compiler
supports it, otherwise it expands to nothing.  It takes one argument
I<since> that should be set to the OpenSSL version where the symbol was
deprecated, and will be displayed with the deprecation warning message,
for compilers that support user specified deprecation messages.

OSSL_DEPRECATED_FOR() does the same as OSSL_DEPRECATED(), but also takes a
second argument I<msg>, which is an additional text messages to be displayed
with the deprecation warning along with the OpenSSL version number, for
compilers that support user specified deprecation messages.

These macros are used to define the version specific deprecation macros
described in L<deprecation(7)>.

=begin comment

[RETURN VALUES isn't relevant for these macros, but find-doc-nits demands
the presence of this section]

=head1 RETURN VALUES

[podche<PERSON> doesn't like empty sections]

=end comment

=head1 SEE ALSO

L<deprecation(7)>

=head1 COPYRIGHT

Copyright 2020 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
