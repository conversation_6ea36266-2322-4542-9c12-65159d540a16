=pod

=head1 NAME

OPENSSL_NO_DEPRECATED_3_0, OSSL_DEPRECATEDIN_3_0,
OPENSSL_NO_DEPRECATED_1_1_1, OSSL_DEPRECATEDIN_1_1_1,
OPENSSL_NO_DEPRECATED_1_1_0, OSSL_DEPRECATEDIN_1_1_0,
OPENSSL_NO_DEPRECATED_1_0_2, OSSL_DEPRECATEDIN_1_0_2,
OPENSSL_NO_DEPRECATED_1_0_1, OSSL_DEPRECATEDIN_1_0_1,
OPENSSL_NO_DEPRECATED_1_0_0, OSSL_DEPRECATEDIN_1_0_0,
OPENSSL_NO_DEPRECATED_0_9_8, OSSL_DEPRECATEDIN_0_9_8,
deprecation - How to do deprecation

=head1 DESCRIPTION

Deprecation of a symbol is adding an attribute to the declaration of that
symbol (function, type, variable, but we currently only do that for
functions in our public header files, F<< <openssl/*.h> >>).

Removal of a symbol is not the same thing as deprecation, as it actually
explicitly removes the symbol from public view.

OpenSSL configuration supports deprecation as well as simulating removal of
symbols from public view (with the configuration option C<no-deprecated>, or
if the user chooses to do so, with L<OPENSSL_NO_DEPRECATED(7)>), and also
supports doing this in terms of a specified OpenSSL version (with the
configuration option C<--api>, or if the user chooses to do so, with
L<OPENSSL_API_COMPAT(7)>).

Deprecation is done using attribute macros named
B<OSSL_DEPRECATEDIN_I<version>>, used with any declaration it applies to.

Simulating removal is done with C<#ifndef> preprocessor guards using macros
named B<OPENSSL_NO_DEPRECATED_I<version>>.

B<OSSL_DEPRECATEDIN_I<version>> and B<OPENSSL_NO_DEPRECATED_I<version>> are
defined in F<< <openssl/macros.h> >>.

In those macro names, B<I<version>> corresponds to the OpenSSL release since
which the deprecation applies, with underscores instead of periods.  Because
of the change in version scheme with OpenSSL 3.0, the B<I<version>> for
versions before that are three numbers (such as C<1_1_0>), while they are
two numbers (such as C<3_0>) from 3.0 and on.

The implementation of a deprecated symbol is kept for one of two reasons:

=over 4

=item Planned to be removed

The symbol and its implementation are planned to be removed some time in the
future, but needs to remain available until that time.
Such an implementation needs to be guarded appropriately, as shown in
L</Implementations to be removed> below.

=item Planned to remain internally

The symbol is planned to be removed from public view, but will otherwise
remain for internal purposes.  In this case, the implementation doesn't need
to change or be guarded.

However, it's necessary to ensure that the declaration remains available for
the translation unit where the symbol is used or implemented, even when the
symbol is publicly unavailable through simulated removal.  That's done by
including an internal header file very early in the affected translation
units.  See L</Implementations to remain internally> below.

In the future, when the deprecated declaration is to actually be removed
from public view, it should be moved to an internal header file, with the
deprecation attribute removed, and the translation units that implement or
use that symbol should adjust their header inclusions accordingly.

=back

=head1 EXAMPLES

=head2 Header files

In public header files (F<< <openssl/*.h> >>), this is what a deprecation is
expected to look like, including the preprocessor wrapping for simulated
removal:

 # ifndef OPENSSL_NO_DEPRECATED_3_0
 /* ... */

 OSSL_DEPRECATEDIN_3_0 RSA *RSA_new_method(ENGINE *engine);

 /* ... */
 # endif

=head2 Implementations to be removed

For a deprecated function that we plan to remove in the future, for example
RSA_new_method(), the following should be found very early (before including
any OpenSSL header file) in the translation unit that implements it and in
any translation unit that uses it:

 /*
  * Suppress deprecation warnings for RSA low level implementations that are
  * kept until removal.
  */
 #define OPENSSL_SUPPRESS_DEPRECATED

The RSA_new_method() implementation itself must be guarded the same way as
its declaration in the public header file is:

 #ifndef OPENSSL_NO_DEPRECATED_3_0
 RSA *RSA_new_method(ENGINE *engine)
 {
     /* ... */
 }
 #endif

=head2 Implementations to remain internally

For a deprecated function that we plan to keep internally, for example
RSA_size(), the following should be found very early (before including any
other OpenSSL header file) in the translation unit that implements it and in
any translation unit that uses it:

 /*
  * RSA low level APIs are deprecated for public use, but are kept for
  * internal use.
  */
 #include "internal/deprecated.h"

=head1 SEE ALSO

L<openssl_user_macros(7)>

=head1 COPYRIGHT

Copyright 2020-2021 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
