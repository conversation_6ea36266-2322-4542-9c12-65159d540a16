=pod

=begin comment

WARNING: do not edit!
Generated by <PERSON><PERSON><PERSON> from doc/man1/openssl-ocsp.pod.in

=end comment

=head1 NAME

openssl-ocsp - Online Certificate Status Protocol command

=head1 SYNOPSIS

=head2 OCSP Client

B<openssl> B<ocsp>
[B<-help>]
[B<-out> I<file>]
[B<-issuer> I<file>]
[B<-cert> I<file>]
[B<-no_certs>]
[B<-serial> I<n>]
[B<-signer> I<file>]
[B<-signkey> I<file>]
[B<-sign_other> I<file>]
[B<-nonce>]
[B<-no_nonce>]
[B<-req_text>]
[B<-resp_text>]
[B<-text>]
[B<-reqout> I<file>]
[B<-respout> I<file>]
[B<-reqin> I<file>]
[B<-respin> I<file>]
[B<-url> I<URL>]
[B<-host> I<host>:I<port>]
[B<-path>]
[B<-proxy> I<[http[s]://][userinfo@]host[:port][/path]>]
[B<-no_proxy> I<addresses>]
[B<-header>]
[B<-timeout> I<seconds>]
[B<-VAfile> I<file>]
[B<-validity_period> I<n>]
[B<-status_age> I<n>]
[B<-noverify>]
[B<-verify_other> I<file>]
[B<-trust_other>]
[B<-no_intern>]
[B<-no_signature_verify>]
[B<-no_cert_verify>]
[B<-no_chain>]
[B<-no_cert_checks>]
[B<-no_explicit>]
[B<-port> I<num>]
[B<-ignore_err>]

=head2 OCSP Server

B<openssl> B<ocsp>
[B<-index> I<file>]
[B<-CA> I<file>]
[B<-rsigner> I<file>]
[B<-rkey> I<file>]
[B<-passin> I<arg>]
[B<-rother> I<file>]
[B<-rsigopt> I<nm>:I<v>]
[B<-rmd> I<digest>]
[B<-badsig>]
[B<-resp_no_certs>]
[B<-nmin> I<n>]
[B<-ndays> I<n>]
[B<-resp_key_id>]
[B<-nrequest> I<n>]
[B<-multi> I<process-count>]
[B<-rcid> I<digest>]
[B<-I<digest>>]
[B<-CAfile> I<file>]
[B<-no-CAfile>]
[B<-CApath> I<dir>]
[B<-no-CApath>]
[B<-CAstore> I<uri>]
[B<-no-CAstore>]
[B<-allow_proxy_certs>]
[B<-attime> I<timestamp>]
[B<-no_check_time>]
[B<-check_ss_sig>]
[B<-crl_check>]
[B<-crl_check_all>]
[B<-explicit_policy>]
[B<-extended_crl>]
[B<-ignore_critical>]
[B<-inhibit_any>]
[B<-inhibit_map>]
[B<-partial_chain>]
[B<-policy> I<arg>]
[B<-policy_check>]
[B<-policy_print>]
[B<-purpose> I<purpose>]
[B<-suiteB_128>]
[B<-suiteB_128_only>]
[B<-suiteB_192>]
[B<-trusted_first>]
[B<-no_alt_chains>]
[B<-use_deltas>]
[B<-auth_level> I<num>]
[B<-verify_depth> I<num>]
[B<-verify_email> I<email>]
[B<-verify_hostname> I<hostname>]
[B<-verify_ip> I<ip>]
[B<-verify_name> I<name>]
[B<-x509_strict>]
[B<-issuer_checks>]
[B<-provider> I<name>]
[B<-provider-path> I<path>]
[B<-propquery> I<propq>]

=head1 DESCRIPTION

The Online Certificate Status Protocol (OCSP) enables applications to
determine the (revocation) state of an identified certificate (RFC 2560).

This command performs many common OCSP tasks. It can be used
to print out requests and responses, create requests and send queries
to an OCSP responder and behave like a mini OCSP server itself.

=head1 OPTIONS

This command operates as either a client or a server.
The options are described below, divided into those two modes.

=head2 OCSP Client Options

=over 4

=item B<-help>

Print out a usage message.

=item B<-out> I<filename>

specify output filename, default is standard output.

=item B<-issuer> I<filename>

This specifies the current issuer certificate. This option can be used
multiple times.
This option B<MUST> come before any B<-cert> options.

=item B<-cert> I<filename>

Add the certificate I<filename> to the request. The issuer certificate
is taken from the previous B<-issuer> option, or an error occurs if no
issuer certificate is specified.

=item B<-no_certs>

Don't include any certificates in signed request.

=item B<-serial> I<num>

Same as the B<-cert> option except the certificate with serial number
B<num> is added to the request. The serial number is interpreted as a
decimal integer unless preceded by C<0x>. Negative integers can also
be specified by preceding the value by a C<-> sign.

=item B<-signer> I<filename>, B<-signkey> I<filename>

Sign the OCSP request using the certificate specified in the B<-signer>
option and the private key specified by the B<-signkey> option. If
the B<-signkey> option is not present then the private key is read
from the same file as the certificate. If neither option is specified then
the OCSP request is not signed.

=item B<-sign_other> I<filename>

Additional certificates to include in the signed request.
The input can be in PEM, DER, or PKCS#12 format.

=item B<-nonce>, B<-no_nonce>

Add an OCSP nonce extension to a request or disable OCSP nonce addition.
Normally if an OCSP request is input using the B<-reqin> option no
nonce is added: using the B<-nonce> option will force addition of a nonce.
If an OCSP request is being created (using B<-cert> and B<-serial> options)
a nonce is automatically added specifying B<-no_nonce> overrides this.

=item B<-req_text>, B<-resp_text>, B<-text>

Print out the text form of the OCSP request, response or both respectively.

=item B<-reqout> I<file>, B<-respout> I<file>

Write out the DER encoded certificate request or response to I<file>.

=item B<-reqin> I<file>, B<-respin> I<file>

Read OCSP request or response file from I<file>. These option are ignored
if OCSP request or response creation is implied by other options (for example
with B<-serial>, B<-cert> and B<-host> options).

=item B<-url> I<responder_url>

Specify the responder URL. Both HTTP and HTTPS (SSL/TLS) URLs can be specified.
The optional userinfo and fragment components are ignored.
Any given query component is handled as part of the path component.

=item B<-host> I<hostname>:I<port>, B<-path> I<pathname>

If the B<-host> option is present then the OCSP request is sent to the host
I<hostname> on port I<port>. The B<-path> option specifies the HTTP pathname
to use or "/" by default.  This is equivalent to specifying B<-url> with scheme
http:// and the given hostname, port, and pathname.

=item B<-proxy> I<[http[s]://][userinfo@]host[:port][/path]>

The HTTP(S) proxy server to use for reaching the OCSP server unless B<-no_proxy>
applies, see below.
The proxy port defaults to 80 or 443 if the scheme is C<https>; apart from that
the optional C<http://> or C<https://> prefix is ignored,
as well as any userinfo and path components.
Defaults to the environment variable C<http_proxy> if set, else C<HTTP_PROXY>
in case no TLS is used, otherwise C<https_proxy> if set, else C<HTTPS_PROXY>.

=item B<-no_proxy> I<addresses>

List of IP addresses and/or DNS names of servers
not to use an HTTP(S) proxy for, separated by commas and/or whitespace
(where in the latter case the whole argument must be enclosed in "...").
Default is from the environment variable C<no_proxy> if set, else C<NO_PROXY>.

=item B<-header> I<name>=I<value>

Adds the header I<name> with the specified I<value> to the OCSP request
that is sent to the responder.
This may be repeated.

=item B<-timeout> I<seconds>

Connection timeout to the OCSP responder in seconds.
On POSIX systems, when running as an OCSP responder, this option also limits
the time that the responder is willing to wait for the client request.
This time is measured from the time the responder accepts the connection until
the complete request is received.

=item B<-verify_other> I<file>

File or URI containing additional certificates to search
when attempting to locate
the OCSP response signing certificate. Some responders omit the actual signer's
certificate from the response: this option can be used to supply the necessary
certificate in such cases.
The input can be in PEM, DER, or PKCS#12 format.

=item B<-trust_other>

The certificates specified by the B<-verify_other> option should be explicitly
trusted and no additional checks will be performed on them. This is useful
when the complete responder certificate chain is not available or trusting a
root CA is not appropriate.

=item B<-VAfile> I<file>

File or URI containing explicitly trusted responder certificates.
Equivalent to the B<-verify_other> and B<-trust_other> options.
The input can be in PEM, DER, or PKCS#12 format.

=item B<-noverify>

Don't attempt to verify the OCSP response signature or the nonce
values. This option will normally only be used for debugging since it
disables all verification of the responders certificate.

=item B<-no_intern>

Ignore certificates contained in the OCSP response when searching for the
signers certificate. With this option the signers certificate must be specified
with either the B<-verify_other> or B<-VAfile> options.

=item B<-no_signature_verify>

Don't check the signature on the OCSP response. Since this option
tolerates invalid signatures on OCSP responses it will normally only be
used for testing purposes.

=item B<-no_cert_verify>

Don't verify the OCSP response signers certificate at all. Since this
option allows the OCSP response to be signed by any certificate it should
only be used for testing purposes.

=item B<-no_chain>

Do not use certificates in the response as additional untrusted CA
certificates.

=item B<-no_explicit>

Do not explicitly trust the root CA if it is set to be trusted for OCSP signing.

=item B<-no_cert_checks>

Don't perform any additional checks on the OCSP response signers certificate.
That is do not make any checks to see if the signers certificate is authorised
to provide the necessary status information: as a result this option should
only be used for testing purposes.

=item B<-validity_period> I<nsec>, B<-status_age> I<age>

These options specify the range of times, in seconds, which will be tolerated
in an OCSP response. Each certificate status response includes a B<notBefore>
time and an optional B<notAfter> time. The current time should fall between
these two values, but the interval between the two times may be only a few
seconds. In practice the OCSP responder and clients clocks may not be precisely
synchronised and so such a check may fail. To avoid this the
B<-validity_period> option can be used to specify an acceptable error range in
seconds, the default value is 5 minutes.

If the B<notAfter> time is omitted from a response then this means that new
status information is immediately available. In this case the age of the
B<notBefore> field is checked to see it is not older than I<age> seconds old.
By default this additional check is not performed.

=item B<-rcid> I<digest>

This option sets the digest algorithm to use for certificate identification
in the OCSP response. Any digest supported by the L<openssl-dgst(1)> command can
be used. The default is the same digest algorithm used in the request.

=item B<-I<digest>>

This option sets digest algorithm to use for certificate identification in the
OCSP request. Any digest supported by the OpenSSL B<dgst> command can be used.
The default is SHA-1. This option may be used multiple times to specify the
digest used by subsequent certificate identifiers.

=item B<-CAfile> I<file>, B<-no-CAfile>, B<-CApath> I<dir>, B<-no-CApath>,
B<-CAstore> I<uri>, B<-no-CAstore>

See L<openssl-verification-options(1)/Trusted Certificate Options> for details.

=item B<-allow_proxy_certs>, B<-attime>, B<-no_check_time>,
B<-check_ss_sig>, B<-crl_check>, B<-crl_check_all>,
B<-explicit_policy>, B<-extended_crl>, B<-ignore_critical>, B<-inhibit_any>,
B<-inhibit_map>, B<-no_alt_chains>, B<-partial_chain>, B<-policy>,
B<-policy_check>, B<-policy_print>, B<-purpose>, B<-suiteB_128>,
B<-suiteB_128_only>, B<-suiteB_192>, B<-trusted_first>, B<-use_deltas>,
B<-auth_level>, B<-verify_depth>, B<-verify_email>, B<-verify_hostname>,
B<-verify_ip>, B<-verify_name>, B<-x509_strict> B<-issuer_checks>

Set various options of certificate chain verification.
See L<openssl-verification-options(1)/Verification Options> for details.

=item B<-provider> I<name>

=item B<-provider-path> I<path>

=item B<-propquery> I<propq>

See L<openssl(1)/Provider Options>, L<provider(7)>, and L<property(7)>.

=back

=head2 OCSP Server Options

=over 4

=item B<-index> I<indexfile>

The I<indexfile> parameter is the name of a text index file in B<ca>
format containing certificate revocation information.

If the B<-index> option is specified then this command switches to
responder mode, otherwise it is in client mode. The request(s) the responder
processes can be either specified on the command line (using B<-issuer>
and B<-serial> options), supplied in a file (using the B<-reqin> option)
or via external OCSP clients (if B<-port> or B<-url> is specified).

If the B<-index> option is present then the B<-CA> and B<-rsigner> options
must also be present.

=item B<-CA> I<file>

CA certificate corresponding to the revocation information in the index
file given with B<-index>.
The input can be in PEM, DER, or PKCS#12 format.

=item B<-rsigner> I<file>

The certificate to sign OCSP responses with.

=item B<-rkey> I<file>

The private key to sign OCSP responses with: if not present the file
specified in the B<-rsigner> option is used.

=item B<-passin> I<arg>

The private key password source. For more information about the format of I<arg>
see L<openssl-passphrase-options(1)>.

=item B<-rother> I<file>

Additional certificates to include in the OCSP response.
The input can be in PEM, DER, or PKCS#12 format.

=item B<-rsigopt> I<nm>:I<v>

Pass options to the signature algorithm when signing OCSP responses.
Names and values of these options are algorithm-specific.

=item B<-rmd> I<digest>

The digest to use when signing the response.

=item B<-badsig>

Corrupt the response signature before writing it; this can be useful
for testing.

=item B<-resp_no_certs>

Don't include any certificates in the OCSP response.

=item B<-resp_key_id>

Identify the signer certificate using the key ID, default is to use the
subject name.

=item B<-port> I<portnum>

Port to listen for OCSP requests on. The port may also be specified
using the B<url> option.
A C<0> argument indicates that any available port shall be chosen automatically.

=item B<-ignore_err>

Ignore malformed requests or responses: When acting as an OCSP client, retry if
a malformed response is received. When acting as an OCSP responder, continue
running instead of terminating upon receiving a malformed request.

=item B<-nrequest> I<number>

The OCSP server will exit after receiving I<number> requests, default unlimited.

=item B<-multi> I<process-count>

Run the specified number of OCSP responder child processes, with the parent
process respawning child processes as needed.
Child processes will detect changes in the CA index file and automatically
reload it.
When running as a responder B<-timeout> option is recommended to limit the time
each child is willing to wait for the client's OCSP response.
This option is available on POSIX systems (that support the fork() and other
required unix system-calls).

=item B<-nmin> I<minutes>, B<-ndays> I<days>

Number of minutes or days when fresh revocation information is available:
used in the B<nextUpdate> field. If neither option is present then the
B<nextUpdate> field is omitted meaning fresh revocation information is
immediately available.

=back

=head1 OCSP RESPONSE VERIFICATION

OCSP Response follows the rules specified in RFC2560.

Initially the OCSP responder certificate is located and the signature on
the OCSP request checked using the responder certificate's public key.

Then a normal certificate verify is performed on the OCSP responder certificate
building up a certificate chain in the process. The locations of the trusted
certificates used to build the chain can be specified by the B<-CAfile>,
B<-CApath> or B<-CAstore> options or they will be looked for in the
standard OpenSSL certificates directory.

If the initial verify fails then the OCSP verify process halts with an
error.

Otherwise the issuing CA certificate in the request is compared to the OCSP
responder certificate: if there is a match then the OCSP verify succeeds.

Otherwise the OCSP responder certificate's CA is checked against the issuing
CA certificate in the request. If there is a match and the OCSPSigning
extended key usage is present in the OCSP responder certificate then the
OCSP verify succeeds.

Otherwise, if B<-no_explicit> is B<not> set the root CA of the OCSP responders
CA is checked to see if it is trusted for OCSP signing. If it is the OCSP
verify succeeds.

If none of these checks is successful then the OCSP verify fails.

What this effectively means if that if the OCSP responder certificate is
authorised directly by the CA it is issuing revocation information about
(and it is correctly configured) then verification will succeed.

If the OCSP responder is a "global responder" which can give details about
multiple CAs and has its own separate certificate chain then its root
CA can be trusted for OCSP signing. For example:

 openssl x509 -in ocspCA.pem -addtrust OCSPSigning -out trustedCA.pem

Alternatively the responder certificate itself can be explicitly trusted
with the B<-VAfile> option.

=head1 NOTES

As noted, most of the verify options are for testing or debugging purposes.
Normally only the B<-CApath>, B<-CAfile>, B<-CAstore> and (if the responder
is a 'global VA') B<-VAfile> options need to be used.

The OCSP server is only useful for test and demonstration purposes: it is
not really usable as a full OCSP responder. It contains only a very
simple HTTP request handling and can only handle the POST form of OCSP
queries. It also handles requests serially meaning it cannot respond to
new requests until it has processed the current one. The text index file
format of revocation is also inefficient for large quantities of revocation
data.

It is possible to run this command in responder mode via a CGI
script using the B<-reqin> and B<-respout> options.

=head1 EXAMPLES

Create an OCSP request and write it to a file:

 openssl ocsp -issuer issuer.pem -cert c1.pem -cert c2.pem -reqout req.der

Send a query to an OCSP responder with URL http://ocsp.myhost.com/ save the
response to a file, print it out in text form, and verify the response:

 openssl ocsp -issuer issuer.pem -cert c1.pem -cert c2.pem \
     -url http://ocsp.myhost.com/ -resp_text -respout resp.der

Read in an OCSP response and print out text form:

 openssl ocsp -respin resp.der -text -noverify

OCSP server on port 8888 using a standard B<ca> configuration, and a separate
responder certificate. All requests and responses are printed to a file.

 openssl ocsp -index demoCA/index.txt -port 8888 -rsigner rcert.pem -CA demoCA/cacert.pem
        -text -out log.txt

As above but exit after processing one request:

 openssl ocsp -index demoCA/index.txt -port 8888 -rsigner rcert.pem -CA demoCA/cacert.pem
     -nrequest 1

Query status information using an internally generated request:

 openssl ocsp -index demoCA/index.txt -rsigner rcert.pem -CA demoCA/cacert.pem
     -issuer demoCA/cacert.pem -serial 1

Query status information using request read from a file, and write the response
to a second file.

 openssl ocsp -index demoCA/index.txt -rsigner rcert.pem -CA demoCA/cacert.pem
     -reqin req.der -respout resp.der

=head1 HISTORY

The -no_alt_chains option was added in OpenSSL 1.1.0.

=head1 COPYRIGHT

Copyright 2001-2021 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
