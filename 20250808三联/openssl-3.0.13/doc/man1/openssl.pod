=pod

=head1 NAME

openssl - OpenSSL command line program

=head1 SYNOPSIS

B<openssl>
I<command>
[ I<options> ... ]
[ I<parameters> ... ]

B<openssl> B<no->I<XXX> [ I<options> ]

=head1 DESCRIPTION

OpenSSL is a cryptography toolkit implementing the Secure Sockets Layer (SSL
v2/v3) and Transport Layer Security (TLS v1) network protocols and related
cryptography standards required by them.

The B<openssl> program is a command line program for using the various
cryptography functions of OpenSSL's B<crypto> library from the shell.
It can be used for

 o  Creation and management of private keys, public keys and parameters
 o  Public key cryptographic operations
 o  Creation of X.509 certificates, CSRs and CRLs
 o  Calculation of Message Digests and Message Authentication Codes
 o  Encryption and Decryption with Ciphers
 o  SSL/TLS Client and Server Tests
 o  Handling of S/MIME signed or encrypted mail
 o  Timestamp requests, generation and verification

=head1 COMMAND SUMMARY

The B<openssl> program provides a rich variety of commands (I<command> in
the L</SYNOPSIS> above).
Each command can have many options and argument parameters, shown above as
I<options> and I<parameters>.

Detailed documentation and use cases for most standard subcommands are available
(e.g., L<openssl-x509(1)>). The subcommand L<openssl-list(1)> may be used to list
subcommands.

The command B<no->I<XXX> tests whether a command of the
specified name is available.  If no command named I<XXX> exists, it
returns 0 (success) and prints B<no->I<XXX>; otherwise it returns 1
and prints I<XXX>.  In both cases, the output goes to B<stdout> and
nothing is printed to B<stderr>.  Additional command line arguments
are always ignored.  Since for each cipher there is a command of the
same name, this provides an easy way for shell scripts to test for the
availability of ciphers in the B<openssl> program.  (B<no->I<XXX> is
not able to detect pseudo-commands such as B<quit>,
B<list>, or B<no->I<XXX> itself.)

=head2 Configuration Option

Many commands use an external configuration file for some or all of their
arguments and have a B<-config> option to specify that file.
The default name of the file is F<openssl.cnf> in the default certificate
storage area, which can be determined from the L<openssl-version(1)>
command using the B<-d> or B<-a> option.
The environment variable B<OPENSSL_CONF> can be used to specify a different
file location or to disable loading a configuration (using the empty string).

Among others, the configuration file can be used to load modules
and to specify parameters for generating certificates and random numbers.
See L<config(5)> for details.

=head2 Standard Commands

=over 4

=item B<asn1parse>

Parse an ASN.1 sequence.

=item B<ca>

Certificate Authority (CA) Management.

=item B<ciphers>

Cipher Suite Description Determination.

=item B<cms>

CMS (Cryptographic Message Syntax) command.

=item B<crl>

Certificate Revocation List (CRL) Management.

=item B<crl2pkcs7>

CRL to PKCS#7 Conversion.

=item B<dgst>

Message Digest calculation. MAC calculations are superseded by
L<openssl-mac(1)>.

=item B<dhparam>

Generation and Management of Diffie-Hellman Parameters. Superseded by
L<openssl-genpkey(1)> and L<openssl-pkeyparam(1)>.

=item B<dsa>

DSA Data Management.

=item B<dsaparam>

DSA Parameter Generation and Management. Superseded by
L<openssl-genpkey(1)> and L<openssl-pkeyparam(1)>.

=item B<ec>

EC (Elliptic curve) key processing.

=item B<ecparam>

EC parameter manipulation and generation.

=item B<enc>

Encryption, decryption, and encoding.

=item B<engine>

Engine (loadable module) information and manipulation.

=item B<errstr>

Error Number to Error String Conversion.

=item B<fipsinstall>

FIPS configuration installation.

=item B<gendsa>

Generation of DSA Private Key from Parameters. Superseded by
L<openssl-genpkey(1)> and L<openssl-pkey(1)>.

=item B<genpkey>

Generation of Private Key or Parameters.

=item B<genrsa>

Generation of RSA Private Key. Superseded by L<openssl-genpkey(1)>.

=item B<help>

Display information about a command's options.

=item B<info>

Display diverse information built into the OpenSSL libraries.

=item B<kdf>

Key Derivation Functions.

=item B<list>

List algorithms and features.

=item B<mac>

Message Authentication Code Calculation.

=item B<nseq>

Create or examine a Netscape certificate sequence.

=item B<ocsp>

Online Certificate Status Protocol command.

=item B<passwd>

Generation of hashed passwords.

=item B<pkcs12>

PKCS#12 Data Management.

=item B<pkcs7>

PKCS#7 Data Management.

=item B<pkcs8>

PKCS#8 format private key conversion command.

=item B<pkey>

Public and private key management.

=item B<pkeyparam>

Public key algorithm parameter management.

=item B<pkeyutl>

Public key algorithm cryptographic operation command.

=item B<prime>

Compute prime numbers.

=item B<rand>

Generate pseudo-random bytes.

=item B<rehash>

Create symbolic links to certificate and CRL files named by the hash values.

=item B<req>

PKCS#10 X.509 Certificate Signing Request (CSR) Management.

=item B<rsa>

RSA key management.

=item B<rsautl>

RSA command for signing, verification, encryption, and decryption. Superseded
by  L<openssl-pkeyutl(1)>.

=item B<s_client>

This implements a generic SSL/TLS client which can establish a transparent
connection to a remote server speaking SSL/TLS. It's intended for testing
purposes only and provides only rudimentary interface functionality but
internally uses mostly all functionality of the OpenSSL B<ssl> library.

=item B<s_server>

This implements a generic SSL/TLS server which accepts connections from remote
clients speaking SSL/TLS. It's intended for testing purposes only and provides
only rudimentary interface functionality but internally uses mostly all
functionality of the OpenSSL B<ssl> library.  It provides both an own command
line oriented protocol for testing SSL functions and a simple HTTP response
facility to emulate an SSL/TLS-aware webserver.

=item B<s_time>

SSL Connection Timer.

=item B<sess_id>

SSL Session Data Management.

=item B<smime>

S/MIME mail processing.

=item B<speed>

Algorithm Speed Measurement.

=item B<spkac>

SPKAC printing and generating command.

=item B<srp>

Maintain SRP password file. This command is deprecated.

=item B<storeutl>

Command to list and display certificates, keys, CRLs, etc.

=item B<ts>

Time Stamping Authority command.

=item B<verify>

X.509 Certificate Verification.
See also the L<openssl-verification-options(1)> manual page.

=item B<version>

OpenSSL Version Information.

=item B<x509>

X.509 Certificate Data Management.

=back

=head2 Message Digest Commands

=over 4

=item B<blake2b512>

BLAKE2b-512 Digest

=item B<blake2s256>

BLAKE2s-256 Digest

=item B<md2>

MD2 Digest

=item B<md4>

MD4 Digest

=item B<md5>

MD5 Digest

=item B<mdc2>

MDC2 Digest

=item B<rmd160>

RMD-160 Digest

=item B<sha1>

SHA-1 Digest

=item B<sha224>

SHA-2 224 Digest

=item B<sha256>

SHA-2 256 Digest

=item B<sha384>

SHA-2 384 Digest

=item B<sha512>

SHA-2 512 Digest

=item B<sha3-224>

SHA-3 224 Digest

=item B<sha3-256>

SHA-3 256 Digest

=item B<sha3-384>

SHA-3 384 Digest

=item B<sha3-512>

SHA-3 512 Digest

=item B<shake128>

SHA-3 SHAKE128 Digest

=item B<shake256>

SHA-3 SHAKE256 Digest

=item B<sm3>

SM3 Digest

=back

=head2 Encryption, Decryption, and Encoding Commands

The following aliases provide convenient access to the most used encodings
and ciphers.

Depending on how OpenSSL was configured and built, not all ciphers listed
here may be present. See L<openssl-enc(1)> for more information.

=over 4

=item B<aes128>, B<aes-128-cbc>, B<aes-128-cfb>, B<aes-128-ctr>, B<aes-128-ecb>, B<aes-128-ofb>

AES-128 Cipher

=item B<aes192>, B<aes-192-cbc>, B<aes-192-cfb>, B<aes-192-ctr>, B<aes-192-ecb>, B<aes-192-ofb>

AES-192 Cipher

=item B<aes256>, B<aes-256-cbc>, B<aes-256-cfb>, B<aes-256-ctr>, B<aes-256-ecb>, B<aes-256-ofb>

AES-256 Cipher

=item B<aria128>, B<aria-128-cbc>, B<aria-128-cfb>, B<aria-128-ctr>, B<aria-128-ecb>, B<aria-128-ofb>

Aria-128 Cipher

=item B<aria192>, B<aria-192-cbc>, B<aria-192-cfb>, B<aria-192-ctr>, B<aria-192-ecb>, B<aria-192-ofb>

Aria-192 Cipher

=item B<aria256>, B<aria-256-cbc>, B<aria-256-cfb>, B<aria-256-ctr>, B<aria-256-ecb>, B<aria-256-ofb>

Aria-256 Cipher

=item B<base64>

Base64 Encoding

=item B<bf>, B<bf-cbc>, B<bf-cfb>, B<bf-ecb>, B<bf-ofb>

Blowfish Cipher

=item B<camellia128>, B<camellia-128-cbc>, B<camellia-128-cfb>, B<camellia-128-ctr>, B<camellia-128-ecb>, B<camellia-128-ofb>

Camellia-128 Cipher

=item B<camellia192>, B<camellia-192-cbc>, B<camellia-192-cfb>, B<camellia-192-ctr>, B<camellia-192-ecb>, B<camellia-192-ofb>

Camellia-192 Cipher

=item B<camellia256>, B<camellia-256-cbc>, B<camellia-256-cfb>, B<camellia-256-ctr>, B<camellia-256-ecb>, B<camellia-256-ofb>

Camellia-256 Cipher

=item B<cast>, B<cast-cbc>

CAST Cipher

=item B<cast5-cbc>, B<cast5-cfb>, B<cast5-ecb>, B<cast5-ofb>

CAST5 Cipher

=item B<chacha20>

Chacha20 Cipher

=item B<des>, B<des-cbc>, B<des-cfb>, B<des-ecb>, B<des-ede>, B<des-ede-cbc>, B<des-ede-cfb>, B<des-ede-ofb>, B<des-ofb>

DES Cipher

=item B<des3>, B<desx>, B<des-ede3>, B<des-ede3-cbc>, B<des-ede3-cfb>, B<des-ede3-ofb>

Triple-DES Cipher

=item B<idea>, B<idea-cbc>, B<idea-cfb>, B<idea-ecb>, B<idea-ofb>

IDEA Cipher

=item B<rc2>, B<rc2-cbc>, B<rc2-cfb>, B<rc2-ecb>, B<rc2-ofb>

RC2 Cipher

=item B<rc4>

RC4 Cipher

=item B<rc5>, B<rc5-cbc>, B<rc5-cfb>, B<rc5-ecb>, B<rc5-ofb>

RC5 Cipher

=item B<seed>, B<seed-cbc>, B<seed-cfb>, B<seed-ecb>, B<seed-ofb>

SEED Cipher

=item B<sm4>, B<sm4-cbc>, B<sm4-cfb>, B<sm4-ctr>, B<sm4-ecb>, B<sm4-ofb>

SM4 Cipher

=back

=head1 OPTIONS

Details of which options are available depend on the specific command.
This section describes some common options with common behavior.

=head2 Common Options

=over 4

=item B<-help>

Provides a terse summary of all options.
If an option takes an argument, the "type" of argument is also given.

=item B<-->

This terminates the list of options. It is mostly useful if any filename
parameters start with a minus sign:

 openssl verify [flags...] -- -cert1.pem...

=back

=head2 Format Options

See L<openssl-format-options(1)> for manual page.

=head2 Pass Phrase Options

See the L<openssl-passphrase-options(1)> manual page.

=head2 Random State Options

Prior to OpenSSL 1.1.1, it was common for applications to store information
about the state of the random-number generator in a file that was loaded
at startup and rewritten upon exit. On modern operating systems, this is
generally no longer necessary as OpenSSL will seed itself from a trusted
entropy source provided by the operating system. These flags are still
supported for special platforms or circumstances that might require them.

It is generally an error to use the same seed file more than once and
every use of B<-rand> should be paired with B<-writerand>.

=over 4

=item B<-rand> I<files>

A file or files containing random data used to seed the random number
generator.
Multiple files can be specified separated by an OS-dependent character.
The separator is C<;> for MS-Windows, C<,> for OpenVMS, and C<:> for
all others. Another way to specify multiple files is to repeat this flag
with different filenames.

=item B<-writerand> I<file>

Writes the seed data to the specified I<file> upon exit.
This file can be used in a subsequent command invocation.

=back

=head2 Certificate Verification Options

See the L<openssl-verification-options(1)> manual page.

=head2 Name Format Options

See the L<openssl-namedisplay-options(1)> manual page.

=head2 TLS Version Options

Several commands use SSL, TLS, or DTLS. By default, the commands use TLS and
clients will offer the lowest and highest protocol version they support,
and servers will pick the highest version that the client offers that is also
supported by the server.

The options below can be used to limit which protocol versions are used,
and whether TCP (SSL and TLS) or UDP (DTLS) is used.
Note that not all protocols and flags may be available, depending on how
OpenSSL was built.

=over 4

=item B<-ssl3>, B<-tls1>, B<-tls1_1>, B<-tls1_2>, B<-tls1_3>, B<-no_ssl3>, B<-no_tls1>, B<-no_tls1_1>, B<-no_tls1_2>, B<-no_tls1_3>

These options require or disable the use of the specified SSL or TLS protocols.
When a specific TLS version is required, only that version will be offered or
accepted.
Only one specific protocol can be given and it cannot be combined with any of
the B<no_> options.
The B<no_*> options do not work with B<s_time> and B<ciphers> commands but work with
B<s_client> and B<s_server> commands.

=item B<-dtls>, B<-dtls1>, B<-dtls1_2>

These options specify to use DTLS instead of TLS.
With B<-dtls>, clients will negotiate any supported DTLS protocol version.
Use the B<-dtls1> or B<-dtls1_2> options to support only DTLS1.0 or DTLS1.2,
respectively.

=back

=head2 Engine Options

=over 4

=item B<-engine> I<id>

Load the engine identified by I<id> and use all the methods it implements
(algorithms, key storage, etc.), unless specified otherwise in the
command-specific documentation or it is configured to do so, as described in
L<config(5)/Engine Configuration>.

The engine will be used for key ids specified with B<-key> and similar
options when an option like B<-keyform engine> is given.

A special case is the C<loader_attic> engine, which
is meant just for internal OpenSSL testing purposes and
supports loading keys, parameters, certificates, and CRLs from files.
When this engine is used, files with such credentials are read via this engine.
Using the C<file:> schema is optional; a plain file (path) name will do.

=back

Options specifying keys, like B<-key> and similar, can use the generic
OpenSSL engine key loading URI scheme C<org.openssl.engine:> to retrieve
private keys and public keys.  The URI syntax is as follows, in simplified
form:

    org.openssl.engine:{engineid}:{keyid}

Where C<{engineid}> is the identity/name of the engine, and C<{keyid}> is a
key identifier that's acceptable by that engine.  For example, when using an
engine that interfaces against a PKCS#11 implementation, the generic key URI
would be something like this (this happens to be an example for the PKCS#11
engine that's part of OpenSC):

    -key org.openssl.engine:pkcs11:label_some-private-key

As a third possibility, for engines and providers that have implemented
their own L<OSSL_STORE_LOADER(3)>, C<org.openssl.engine:> should not be
necessary.  For a PKCS#11 implementation that has implemented such a loader,
the PKCS#11 URI as defined in RFC 7512 should be possible to use directly:

    -key pkcs11:object=some-private-key;pin-value=1234

=head2 Provider Options

=over 4

=item B<-provider> I<name>

Load and initialize the provider identified by I<name>. The I<name>
can be also a path to the provider module. In that case the provider name
will be the specified path and not just the provider module name.
Interpretation of relative paths is platform specific. The configured
"MODULESDIR" path, B<OPENSSL_MODULES> environment variable, or the path
specified by B<-provider-path> is prepended to relative paths.
See L<provider(7)> for a more detailed description.

=item B<-provider-path> I<path>

Specifies the search path that is to be used for looking for providers.
Equivalently, the B<OPENSSL_MODULES> environment variable may be set.

=item B<-propquery> I<propq>

Specifies the I<property query clause> to be used when fetching algorithms
from the loaded providers.
See L<property(7)> for a more detailed description.

=back

=head1 ENVIRONMENT

The OpenSSL library can be take some configuration parameters from the
environment.  Some of these variables are listed below.  For information
about specific commands, see L<openssl-engine(1)>,
L<openssl-rehash(1)>, and L<tsget(1)>.

For information about the use of environment variables in configuration,
see L<config(5)/ENVIRONMENT>.

For information about querying or specifying CPU architecture flags, see
L<OPENSSL_ia32cap(3)>, and L<OPENSSL_s390xcap(3)>.

For information about all environment variables used by the OpenSSL libraries,
see L<openssl-env(7)>.

=over 4

=item B<OPENSSL_TRACE=>I<name>[,...]

Enable tracing output of OpenSSL library, by name.
This output will only make sense if you know OpenSSL internals well.
Also, it might not give you any output at all, depending on how
OpenSSL was built.

The value is a comma separated list of names, with the following
available:

=over 4

=item B<TRACE>

Traces the OpenSSL trace API itself.

=item B<INIT>

Traces OpenSSL library initialization and cleanup.

=item B<TLS>

Traces the TLS/SSL protocol.

=item B<TLS_CIPHER>

Traces the ciphers used by the TLS/SSL protocol.

=item B<CONF>

Show details about provider and engine configuration.

=item B<ENGINE_TABLE>

The function that is used by RSA, DSA (etc) code to select registered
ENGINEs, cache defaults and functional references (etc), will generate
debugging summaries.

=item B<ENGINE_REF_COUNT>

Reference counts in the ENGINE structure will be monitored with a line
of generated for each change.

=item B<PKCS5V2>

Traces PKCS#5 v2 key generation.

=item B<PKCS12_KEYGEN>

Traces PKCS#12 key generation.

=item B<PKCS12_DECRYPT>

Traces PKCS#12 decryption.

=item B<X509V3_POLICY>

Generates the complete policy tree at various points during X.509 v3
policy evaluation.

=item B<BN_CTX>

Traces BIGNUM context operations.

=item B<CMP>

Traces CMP client and server activity.

=item B<STORE>

Traces STORE operations.

=item B<DECODER>

Traces decoder operations.

=item B<ENCODER>

Traces encoder operations.

=item B<REF_COUNT>

Traces decrementing certain ASN.1 structure references.

=back

=back

=head1 SEE ALSO

L<openssl-asn1parse(1)>,
L<openssl-ca(1)>,
L<openssl-ciphers(1)>,
L<openssl-cms(1)>,
L<openssl-crl(1)>,
L<openssl-crl2pkcs7(1)>,
L<openssl-dgst(1)>,
L<openssl-dhparam(1)>,
L<openssl-dsa(1)>,
L<openssl-dsaparam(1)>,
L<openssl-ec(1)>,
L<openssl-ecparam(1)>,
L<openssl-enc(1)>,
L<openssl-engine(1)>,
L<openssl-errstr(1)>,
L<openssl-gendsa(1)>,
L<openssl-genpkey(1)>,
L<openssl-genrsa(1)>,
L<openssl-kdf(1)>,
L<openssl-list(1)>,
L<openssl-mac(1)>,
L<openssl-nseq(1)>,
L<openssl-ocsp(1)>,
L<openssl-passwd(1)>,
L<openssl-pkcs12(1)>,
L<openssl-pkcs7(1)>,
L<openssl-pkcs8(1)>,
L<openssl-pkey(1)>,
L<openssl-pkeyparam(1)>,
L<openssl-pkeyutl(1)>,
L<openssl-prime(1)>,
L<openssl-rand(1)>,
L<openssl-rehash(1)>,
L<openssl-req(1)>,
L<openssl-rsa(1)>,
L<openssl-rsautl(1)>,
L<openssl-s_client(1)>,
L<openssl-s_server(1)>,
L<openssl-s_time(1)>,
L<openssl-sess_id(1)>,
L<openssl-smime(1)>,
L<openssl-speed(1)>,
L<openssl-spkac(1)>,
L<openssl-srp(1)>,
L<openssl-storeutl(1)>,
L<openssl-ts(1)>,
L<openssl-verify(1)>,
L<openssl-version(1)>,
L<openssl-x509(1)>,
L<config(5)>,
L<crypto(7)>,
L<openssl-env(7)>.
L<ssl(7)>,
L<x509v3_config(5)>


=head1 HISTORY

The B<list> -I<XXX>B<-algorithms> options were added in OpenSSL 1.0.0;
For notes on the availability of other commands, see their individual
manual pages.

The B<-issuer_checks> option is deprecated as of OpenSSL 1.1.0 and
is silently ignored.

The B<-xcertform> and B<-xkeyform> options
are obsolete since OpenSSL 3.0 and have no effect.

The interactive mode, which could be invoked by running C<openssl>
with no further arguments, was removed in OpenSSL 3.0, and running
that program with no arguments is now equivalent to C<openssl help>.

=head1 COPYRIGHT

Copyright 2000-2023 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
