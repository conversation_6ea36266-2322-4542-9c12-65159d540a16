=pod
{- OpenSSL::safe::output_do_not_edit_headers(); -}

=head1 NAME

openssl-smime - S/MIME command

=head1 SYNOPSIS

B<openssl> B<smime>
[B<-help>]
[B<-encrypt>]
[B<-decrypt>]
[B<-sign>]
[B<-resign>]
[B<-verify>]
[B<-pk7out>]
[B<-binary>]
[B<-crlfeol>]
[B<-I<cipher>>]
[B<-in> I<file>]
[B<-certfile> I<file>]
[B<-signer> I<file>]
[B<-nointern>]
[B<-noverify>]
[B<-nochain>]
[B<-nosigs>]
[B<-nocerts>]
[B<-noattr>]
[B<-nodetach>]
[B<-nosmimecap>]
[B<-recip> I< file>]
[B<-inform> B<DER>|B<PEM>|B<SMIME>]
[B<-outform> B<DER>|B<PEM>|B<SMIME>]
[B<-keyform> B<DER>|B<PEM>|B<P12>|B<ENGINE>]
[B<-passin> I<arg>]
[B<-inkey> I<filename>|I<uri>]
[B<-out> I<file>]
[B<-content> I<file>]
[B<-to> I<addr>]
[B<-from> I<ad>]
[B<-subject> I<s>]
[B<-text>]
[B<-indef>]
[B<-noindef>]
[B<-stream>]
[B<-md> I<digest>]
{- $OpenSSL::safe::opt_trust_synopsis -}
{- $OpenSSL::safe::opt_engine_synopsis -}{- $OpenSSL::safe::opt_r_synopsis -}
{- $OpenSSL::safe::opt_v_synopsis -}
{- $OpenSSL::safe::opt_provider_synopsis -}
{- $OpenSSL::safe::opt_config_synopsis -}
I<recipcert> ...

=head1 DESCRIPTION

This command handles S/MIME mail. It can encrypt, decrypt, sign
and verify S/MIME messages.

=head1 OPTIONS

There are six operation options that set the type of operation to be performed.
The meaning of the other options varies according to the operation type.

=over 4

=item B<-help>

Print out a usage message.

=item B<-encrypt>

Encrypt mail for the given recipient certificates. Input file is the message
to be encrypted. The output file is the encrypted mail in MIME format.

Note that no revocation check is done for the recipient cert, so if that
key has been compromised, others may be able to decrypt the text.

=item B<-decrypt>

Decrypt mail using the supplied certificate and private key. Expects an
encrypted mail message in MIME format for the input file. The decrypted mail
is written to the output file.

=item B<-sign>

Sign mail using the supplied certificate and private key. Input file is
the message to be signed. The signed message in MIME format is written
to the output file.

=item B<-verify>

Verify signed mail. Expects a signed mail message on input and outputs
the signed data. Both clear text and opaque signing is supported.

=item B<-pk7out>

Takes an input message and writes out a PEM encoded PKCS#7 structure.

=item B<-resign>

Resign a message: take an existing message and one or more new signers.

=item B<-in> I<filename>

The input message to be encrypted or signed or the MIME message to
be decrypted or verified.

=item B<-out> I<filename>

The message text that has been decrypted or verified or the output MIME
format message that has been signed or verified.

=item B<-inform> B<DER>|B<PEM>|B<SMIME>

The input format of the PKCS#7 (S/MIME) structure (if one is being read);
the default is B<SMIME>.
See L<openssl-format-options(1)> for details.

=item B<-outform> B<DER>|B<PEM>|B<SMIME>

The output format of the PKCS#7 (S/MIME) structure (if one is being written);
the default is B<SMIME>.
See L<openssl-format-options(1)> for details.

=item B<-keyform> B<DER>|B<PEM>|B<P12>|B<ENGINE>

The key format; unspecified by default.
See L<openssl-format-options(1)> for details.

=item B<-stream>, B<-indef>, B<-noindef>

The B<-stream> and B<-indef> options are equivalent and enable streaming I/O
for encoding operations. This permits single pass processing of data without
the need to hold the entire contents in memory, potentially supporting very
large files. Streaming is automatically set for S/MIME signing with detached
data if the output format is B<SMIME> it is currently off by default for all
other operations.

=item B<-noindef>

Disable streaming I/O where it would produce and indefinite length constructed
encoding. This option currently has no effect. In future streaming will be
enabled by default on all relevant operations and this option will disable it.

=item B<-content> I<filename>

This specifies a file containing the detached content, this is only
useful with the B<-verify> command. This is only usable if the PKCS#7
structure is using the detached signature form where the content is
not included. This option will override any content if the input format
is S/MIME and it uses the multipart/signed MIME content type.

=item B<-text>

This option adds plain text (text/plain) MIME headers to the supplied
message if encrypting or signing. If decrypting or verifying it strips
off text headers: if the decrypted or verified message is not of MIME
type text/plain then an error occurs.

=item B<-md> I<digest>

Digest algorithm to use when signing or resigning. If not present then the
default digest algorithm for the signing key will be used (usually SHA1).

=item B<-I<cipher>>

The encryption algorithm to use. For example DES  (56 bits) - B<-des>,
triple DES (168 bits) - B<-des3>,
EVP_get_cipherbyname() function) can also be used preceded by a dash, for
example B<-aes-128-cbc>. See L<openssl-enc(1)> for list of ciphers
supported by your version of OpenSSL.

If not specified triple DES is used. Only used with B<-encrypt>.

=item B<-nointern>

When verifying a message normally certificates (if any) included in
the message are searched for the signing certificate. With this option
only the certificates specified in the B<-certfile> option are used.
The supplied certificates can still be used as untrusted CAs however.

=item B<-noverify>

Do not verify the signers certificate of a signed message.

=item B<-nochain>

Do not do chain verification of signers certificates; that is, do not
use the certificates in the signed message as untrusted CAs.

=item B<-nosigs>

Don't try to verify the signatures on the message.

=item B<-nocerts>

When signing a message the signer's certificate is normally included
with this option it is excluded. This will reduce the size of the
signed message but the verifier must have a copy of the signers certificate
available locally (passed using the B<-certfile> option for example).

=item B<-noattr>

Normally when a message is signed a set of attributes are included which
include the signing time and supported symmetric algorithms. With this
option they are not included.

=item B<-nodetach>

When signing a message use opaque signing. This form is more resistant
to translation by mail relays but it cannot be read by mail agents that
do not support S/MIME.  Without this option cleartext signing with
the MIME type multipart/signed is used.

=item B<-nosmimecap>

When signing a message, do not include the B<SMIMECapabilities> attribute.

=item B<-binary>

Normally the input message is converted to "canonical" format which is
effectively using CR and LF as end of line: as required by the S/MIME
specification. When this option is present no translation occurs. This
is useful when handling binary data which may not be in MIME format.

=item B<-crlfeol>

Normally the output file uses a single B<LF> as end of line. When this
option is present B<CRLF> is used instead.

=item B<-certfile> I<file>

Allows additional certificates to be specified. When signing these will
be included with the message. When verifying these will be searched for
the signers certificates.
The input can be in PEM, DER, or PKCS#12 format.

=item B<-signer> I<file>

A signing certificate when signing or resigning a message, this option can be
used multiple times if more than one signer is required. If a message is being
verified then the signers certificates will be written to this file if the
verification was successful.

=item B<-nocerts>

Don't include signers certificate when signing.

=item B<-noattr>

Don't include any signed attributes when signing.

=item B<-recip> I<file>

The recipients certificate when decrypting a message. This certificate
must match one of the recipients of the message or an error occurs.

=item B<-inkey> I<filename>|I<uri>

The private key to use when signing or decrypting. This must match the
corresponding certificate. If this option is not specified then the
private key must be included in the certificate file specified with
the B<-recip> or B<-signer> file. When signing this option can be used
multiple times to specify successive keys.

=item B<-passin> I<arg>

The private key password source. For more information about the format of I<arg>
see L<openssl-passphrase-options(1)>.

=item B<-to>, B<-from>, B<-subject>

The relevant mail headers. These are included outside the signed
portion of a message so they may be included manually. If signing
then many S/MIME mail clients check the signers certificate's email
address matches that specified in the From: address.

{- $OpenSSL::safe::opt_v_item -}

Any verification errors cause the command to exit.

{- $OpenSSL::safe::opt_trust_item -}

{- $OpenSSL::safe::opt_engine_item -}

{- $OpenSSL::safe::opt_r_item -}

{- $OpenSSL::safe::opt_provider_item -}

{- $OpenSSL::safe::opt_config_item -}

=item I<recipcert> ...

One or more certificates of message recipients, used when encrypting
a message.

=back

=head1 NOTES

The MIME message must be sent without any blank lines between the
headers and the output. Some mail programs will automatically add
a blank line. Piping the mail directly to sendmail is one way to
achieve the correct format.

The supplied message to be signed or encrypted must include the
necessary MIME headers or many S/MIME clients won't display it
properly (if at all). You can use the B<-text> option to automatically
add plain text headers.

A "signed and encrypted" message is one where a signed message is
then encrypted. This can be produced by encrypting an already signed
message: see the examples section.

This version of the program only allows one signer per message but it
will verify multiple signers on received messages. Some S/MIME clients
choke if a message contains multiple signers. It is possible to sign
messages "in parallel" by signing an already signed message.

The options B<-encrypt> and B<-decrypt> reflect common usage in S/MIME
clients. Strictly speaking these process PKCS#7 enveloped data: PKCS#7
encrypted data is used for other purposes.

The B<-resign> option uses an existing message digest when adding a new
signer. This means that attributes must be present in at least one existing
signer using the same message digest or this operation will fail.

The B<-stream> and B<-indef> options enable streaming I/O support.
As a result the encoding is BER using indefinite length constructed encoding
and no longer DER. Streaming is supported for the B<-encrypt> operation and the
B<-sign> operation if the content is not detached.

Streaming is always used for the B<-sign> operation with detached data but
since the content is no longer part of the PKCS#7 structure the encoding
remains DER.

=head1 EXIT CODES

=over 4

=item Z<>0

The operation was completely successfully.

=item Z<>1

An error occurred parsing the command options.

=item Z<>2

One of the input files could not be read.

=item Z<>3

An error occurred creating the PKCS#7 file or when reading the MIME
message.

=item Z<>4

An error occurred decrypting or verifying the message.

=item Z<>5

The message was verified correctly but an error occurred writing out
the signers certificates.

=back

=head1 EXAMPLES

Create a cleartext signed message:

 openssl smime -sign -in message.txt -text -out mail.msg \
        -signer mycert.pem

Create an opaque signed message:

 openssl smime -sign -in message.txt -text -out mail.msg -nodetach \
        -signer mycert.pem

Create a signed message, include some additional certificates and
read the private key from another file:

 openssl smime -sign -in in.txt -text -out mail.msg \
        -signer mycert.pem -inkey mykey.pem -certfile mycerts.pem

Create a signed message with two signers:

 openssl smime -sign -in message.txt -text -out mail.msg \
        -signer mycert.pem -signer othercert.pem

Send a signed message under Unix directly to sendmail, including headers:

 openssl smime -sign -in in.txt -text -signer mycert.pem \
        -from <EMAIL> -to someone@somewhere \
        -subject "Signed message" | sendmail someone@somewhere

Verify a message and extract the signer's certificate if successful:

 openssl smime -verify -in mail.msg -signer user.pem -out signedtext.txt

Send encrypted mail using triple DES:

 openssl smime -encrypt -in in.txt -from <EMAIL> \
        -to someone@somewhere -subject "Encrypted message" \
        -des3 user.pem -out mail.msg

Sign and encrypt mail:

 openssl smime -sign -in ml.txt -signer my.pem -text \
        | openssl smime -encrypt -out mail.msg \
        -from <EMAIL> -to someone@somewhere \
        -subject "Signed and Encrypted message" -des3 user.pem

Note: the encryption command does not include the B<-text> option because the
message being encrypted already has MIME headers.

Decrypt mail:

 openssl smime -decrypt -in mail.msg -recip mycert.pem -inkey key.pem

The output from Netscape form signing is a PKCS#7 structure with the
detached signature format. You can use this program to verify the
signature by line wrapping the base64 encoded structure and surrounding
it with:

 -----BEGIN PKCS7-----
 -----END PKCS7-----

and using the command:

 openssl smime -verify -inform PEM -in signature.pem -content content.txt

Alternatively you can base64 decode the signature and use:

 openssl smime -verify -inform DER -in signature.der -content content.txt

Create an encrypted message using 128 bit Camellia:

 openssl smime -encrypt -in plain.txt -camellia128 -out mail.msg cert.pem

Add a signer to an existing message:

 openssl smime -resign -in mail.msg -signer newsign.pem -out mail2.msg

=head1 BUGS

The MIME parser isn't very clever: it seems to handle most messages that I've
thrown at it but it may choke on others.

The code currently will only write out the signer's certificate to a file: if
the signer has a separate encryption certificate this must be manually
extracted. There should be some heuristic that determines the correct
encryption certificate.

Ideally a database should be maintained of a certificates for each email
address.

The code doesn't currently take note of the permitted symmetric encryption
algorithms as supplied in the SMIMECapabilities signed attribute. This means the
user has to manually include the correct encryption algorithm. It should store
the list of permitted ciphers in a database and only use those.

No revocation checking is done on the signer's certificate.

The current code can only handle S/MIME v2 messages, the more complex S/MIME v3
structures may cause parsing errors.

=head1 SEE ALSO

L<ossl_store-file(7)>

=head1 HISTORY

The use of multiple B<-signer> options and the B<-resign> command were first
added in OpenSSL 1.0.0

The -no_alt_chains option was added in OpenSSL 1.1.0.

The B<-engine> option was deprecated in OpenSSL 3.0.

=head1 COPYRIGHT

Copyright 2000-2021 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
