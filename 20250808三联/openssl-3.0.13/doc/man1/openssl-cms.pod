=pod

=begin comment

WARNING: do not edit!
Generated by <PERSON><PERSON><PERSON> from doc/man1/openssl-cms.pod.in

=end comment

=head1 NAME

openssl-cms - CMS command

=head1 SYNOPSIS

B<openssl> B<cms>
[B<-help>]

General options:

[B<-in> I<filename>]
[B<-out> I<filename>]
[B<-config> I<configfile>]

Operation options:

[B<-encrypt>]
[B<-decrypt>]
[B<-sign>]
[B<-verify>]
[B<-resign>]
[B<-sign_receipt>]
[B<-verify_receipt> I<receipt>]
[B<-digest_create>]
[B<-digest_verify>]
[B<-compress>]
[B<-uncompress>]
[B<-EncryptedData_encrypt>]
[B<-EncryptedData_decrypt>]
[B<-data_create>]
[B<-data_out>]
[B<-cmsout>]

File format options:

[B<-inform> B<DER>|B<PEM>|B<SMIME>]
[B<-outform> B<DER>|B<PEM>|B<SMIME>]
[B<-rctform> B<DER>|B<PEM>|B<SMIME>]
[B<-stream>]
[B<-indef>]
[B<-noindef>]
[B<-binary>]
[B<-crlfeol>]
[B<-asciicrlf>]

Keys and password options:

[B<-pwri_password> I<password>]
[B<-secretkey> I<key>]
[B<-secretkeyid> I<id>]
[B<-inkey> I<filename>|I<uri>]
[B<-passin> I<arg>]
[B<-keyopt> I<name>:I<parameter>]
[B<-keyform> B<DER>|B<PEM>|B<P12>|B<ENGINE>]
[B<-engine> I<id>]
[B<-provider> I<name>]
[B<-provider-path> I<path>]
[B<-propquery> I<propq>]
[B<-rand> I<files>]
[B<-writerand> I<file>]

Encryption options:

[B<-originator> I<file>]
[B<-recip> I<file>]
[I<recipient-cert> ...]
[B<-I<cipher>>]
[B<-wrap> I<cipher>]
[B<-aes128-wrap>]
[B<-aes192-wrap>]
[B<-aes256-wrap>]
[B<-des3-wrap>]
[B<-debug_decrypt>]

Signing options:

[B<-md> I<digest>]
[B<-signer> I<file>]
[B<-certfile> I<file>]
[B<-cades>]
[B<-nodetach>]
[B<-nocerts>]
[B<-noattr>]
[B<-nosmimecap>]
[B<-receipt_request_all>]
[B<-receipt_request_first>]
[B<-receipt_request_from> I<emailaddress>]
[B<-receipt_request_to> I<emailaddress>]

Verification options:

[B<-signer> I<file>]
[B<-content> I<filename>]
[B<-no_content_verify>]
[B<-no_attr_verify>]
[B<-nosigs>]
[B<-noverify>]
[B<-nointern>]
[B<-cades>]
[B<-verify_retcode>]
[B<-CAfile> I<file>]
[B<-no-CAfile>]
[B<-CApath> I<dir>]
[B<-no-CApath>]
[B<-CAstore> I<uri>]
[B<-no-CAstore>]

Output options:

[B<-keyid>]
[B<-econtent_type> I<type>]
[B<-text>]
[B<-certsout> I<file>]
[B<-to> I<addr>]
[B<-from> I<addr>]
[B<-subject> I<subj>]

Printing options:

[B<-noout>]
[B<-print>]
[B<-nameopt> I<option>]
[B<-receipt_request_print>]

Validation options:

[B<-allow_proxy_certs>]
[B<-attime> I<timestamp>]
[B<-no_check_time>]
[B<-check_ss_sig>]
[B<-crl_check>]
[B<-crl_check_all>]
[B<-explicit_policy>]
[B<-extended_crl>]
[B<-ignore_critical>]
[B<-inhibit_any>]
[B<-inhibit_map>]
[B<-partial_chain>]
[B<-policy> I<arg>]
[B<-policy_check>]
[B<-policy_print>]
[B<-purpose> I<purpose>]
[B<-suiteB_128>]
[B<-suiteB_128_only>]
[B<-suiteB_192>]
[B<-trusted_first>]
[B<-no_alt_chains>]
[B<-use_deltas>]
[B<-auth_level> I<num>]
[B<-verify_depth> I<num>]
[B<-verify_email> I<email>]
[B<-verify_hostname> I<hostname>]
[B<-verify_ip> I<ip>]
[B<-verify_name> I<name>]
[B<-x509_strict>]
[B<-issuer_checks>]

=head1 DESCRIPTION

This command handles data in CMS format such as S/MIME v3.1 email messages.
It can encrypt, decrypt, sign, verify, compress, uncompress, and print messages.

=head1 OPTIONS

There are a number of operation options that set the type of operation to be
performed: encrypt, decrypt, sign, verify, resign, sign_receipt, verify_receipt,
digest_create, digest_verify, compress, uncompress,
EncryptedData_encrypt, EncryptedData_decrypt, data_create, data_out, or cmsout.
The relevance of the other options depends on the operation type
and their meaning may vary according to it.

=over 4

=item B<-help>

Print out a usage message.

=back

=head2 General options

=over 4

=item B<-in> I<filename>

The input message to be encrypted or signed or the message to be decrypted
or verified.

=item B<-out> I<filename>

The message text that has been decrypted or verified or the output MIME
format message that has been signed or verified.

=item B<-config> I<configfile>

See L<openssl(1)/Configuration Option>.

=back

=head2 Operation options

=over 4

=item B<-encrypt>

Encrypt data for the given recipient certificates. Input file is the message
to be encrypted. The output file is the encrypted data in MIME format. The
actual CMS type is B<EnvelopedData>.

Note that no revocation check is done for the recipient cert, so if that
key has been compromised, others may be able to decrypt the text.

=item B<-decrypt>

Decrypt data using the supplied certificate and private key. Expects
encrypted datain MIME format for the input file. The decrypted data
is written to the output file.

=item B<-sign>

Sign data using the supplied certificate and private key. Input file is
the message to be signed. The signed data in MIME format is written
to the output file.

=item B<-verify>

Verify signed data. Expects a signed data on input and outputs
the signed data. Both clear text and opaque signing is supported.

=item B<-resign>

Resign a message: take an existing message and one or more new signers.

=item B<-sign_receipt>

Generate and output a signed receipt for the supplied message. The input
message B<must> contain a signed receipt request. Functionality is otherwise
similar to the B<-sign> operation.

=item B<-verify_receipt> I<receipt>

Verify a signed receipt in filename B<receipt>. The input message B<must>
contain the original receipt request. Functionality is otherwise similar
to the B<-verify> operation.

=item B<-digest_create>

Create a CMS B<DigestedData> type.

=item B<-digest_verify>

Verify a CMS B<DigestedData> type and output the content.

=item B<-compress>

Create a CMS B<CompressedData> type. OpenSSL must be compiled with B<zlib>
support for this option to work, otherwise it will output an error.

=item B<-uncompress>

Uncompress a CMS B<CompressedData> type and output the content. OpenSSL must be
compiled with B<zlib> support for this option to work, otherwise it will
output an error.

=item B<-EncryptedData_encrypt>

Encrypt content using supplied symmetric key and algorithm using a CMS
B<EncryptedData> type and output the content.

=item B<-EncryptedData_decrypt>

Decrypt content using supplied symmetric key and algorithm using a CMS
B<EncryptedData> type and output the content.

=item B<-data_create>

Create a CMS B<Data> type.

=item B<-data_out>

B<Data> type and output the content.

=item B<-cmsout>

Takes an input message and writes out a PEM encoded CMS structure.

=back

=head2 File format options

=over 4

=item B<-inform> B<DER>|B<PEM>|B<SMIME>

The input format of the CMS structure (if one is being read);
the default is B<SMIME>.
See L<openssl-format-options(1)> for details.

=item B<-outform> B<DER>|B<PEM>|B<SMIME>

The output format of the CMS structure (if one is being written);
the default is B<SMIME>.
See L<openssl-format-options(1)> for details.

=item B<-rctform> B<DER>|B<PEM>|B<SMIME>

The signed receipt format for use with the B<-receipt_verify>; the default
is B<SMIME>.
See L<openssl-format-options(1)> for details.

=item B<-stream>, B<-indef>

The B<-stream> and B<-indef> options are equivalent and enable streaming I/O
for encoding operations. This permits single pass processing of data without
the need to hold the entire contents in memory, potentially supporting very
large files. Streaming is automatically set for S/MIME signing with detached
data if the output format is B<SMIME> it is currently off by default for all
other operations.

=item B<-noindef>

Disable streaming I/O where it would produce and indefinite length constructed
encoding. This option currently has no effect. In future streaming will be
enabled by default on all relevant operations and this option will disable it.

=item B<-binary>

Normally the input message is converted to "canonical" format which is
effectively using CR and LF as end of line: as required by the S/MIME
specification. When this option is present no translation occurs. This
is useful when handling binary data which may not be in MIME format.

=item B<-crlfeol>

Normally the output file uses a single B<LF> as end of line. When this
option is present B<CRLF> is used instead.

=item B<-asciicrlf>

When signing use ASCII CRLF format canonicalisation. This strips trailing
whitespace from all lines, deletes trailing blank lines at EOF and sets
the encapsulated content type. This option is normally used with detached
content and an output signature format of DER. This option is not normally
needed when verifying as it is enabled automatically if the encapsulated
content format is detected.

=back

=head2 Keys and password options

=over 4

=item B<-pwri_password> I<password>

Specify password for recipient.

=item B<-secretkey> I<key>

Specify symmetric key to use. The key must be supplied in hex format and be
consistent with the algorithm used. Supported by the B<-EncryptedData_encrypt>
B<-EncryptedData_decrypt>, B<-encrypt> and B<-decrypt> options. When used
with B<-encrypt> or B<-decrypt> the supplied key is used to wrap or unwrap the
content encryption key using an AES key in the B<KEKRecipientInfo> type.

=item B<-secretkeyid> I<id>

The key identifier for the supplied symmetric key for B<KEKRecipientInfo> type.
This option B<must> be present if the B<-secretkey> option is used with
B<-encrypt>. With B<-decrypt> operations the I<id> is used to locate the
relevant key if it is not supplied then an attempt is used to decrypt any
B<KEKRecipientInfo> structures.

=item B<-inkey> I<filename>|I<uri>

The private key to use when signing or decrypting. This must match the
corresponding certificate. If this option is not specified then the
private key must be included in the certificate file specified with
the B<-recip> or B<-signer> file. When signing this option can be used
multiple times to specify successive keys.

=item B<-passin> I<arg>

The private key password source. For more information about the format of B<arg>
see L<openssl-passphrase-options(1)>.

=item B<-keyopt> I<name>:I<parameter>

For signing and encryption this option can be used multiple times to
set customised parameters for the preceding key or certificate. It can
currently be used to set RSA-PSS for signing, RSA-OAEP for encryption
or to modify default parameters for ECDH.

=item B<-keyform> B<DER>|B<PEM>|B<P12>|B<ENGINE>

The format of the private key file; unspecified by default.
See L<openssl-format-options(1)> for details.

=item B<-engine> I<id>

See L<openssl(1)/Engine Options>.
This option is deprecated.

=item B<-provider> I<name>

=item B<-provider-path> I<path>

=item B<-propquery> I<propq>

See L<openssl(1)/Provider Options>, L<provider(7)>, and L<property(7)>.

=item B<-rand> I<files>, B<-writerand> I<file>

See L<openssl(1)/Random State Options> for details.

=back

=head2 Encryption and decryption options

=over 4

=item B<-originator> I<file>

A certificate of the originator of the encrypted message. Necessary for
decryption when Key Agreement is in use for a shared key.

=item B<-recip> I<file>

When decrypting a message this specifies the certificate of the recipient.
The certificate must match one of the recipients of the message.

When encrypting a message this option may be used multiple times to specify
each recipient. This form B<must> be used if customised parameters are
required (for example to specify RSA-OAEP).

Only certificates carrying RSA, Diffie-Hellman or EC keys are supported by this
option.

=item I<recipient-cert> ...

This is an alternative to using the B<-recip> option when encrypting a message.
One or more certificate filenames may be given.

=item B<-I<cipher>>

The encryption algorithm to use. For example triple DES (168 bits) - B<-des3>
or 256 bit AES - B<-aes256>. Any standard algorithm name (as used by the
EVP_get_cipherbyname() function) can also be used preceded by a dash, for
example B<-aes-128-cbc>. See L<openssl-enc(1)> for a list of ciphers
supported by your version of OpenSSL.

Currently the AES variants with GCM mode are the only supported AEAD
algorithms.

If not specified triple DES is used. Only used with B<-encrypt> and
B<-EncryptedData_create> commands.

=item B<-wrap> I<cipher>

Cipher algorithm to use for key wrap when encrypting the message using Key
Agreement for key transport. The algorithm specified should be suitable for key
wrap.

=item B<-aes128-wrap>, B<-aes192-wrap>, B<-aes256-wrap>, B<-des3-wrap>

Use AES128, AES192, AES256, or 3DES-EDE, respectively, to wrap key.
Depending on the OpenSSL build options used, B<-des3-wrap> may not be supported.

=item B<-debug_decrypt>

This option sets the B<CMS_DEBUG_DECRYPT> flag. This option should be used
with caution: see the notes section below.

=back

=head2 Signing options

=over 4

=item B<-md> I<digest>

Digest algorithm to use when signing or resigning. If not present then the
default digest algorithm for the signing key will be used (usually SHA1).

=item B<-signer> I<file>

A signing certificate.  When signing or resigning a message, this option can be
used multiple times if more than one signer is required.

=item B<-certfile> I<file>

Allows additional certificates to be specified. When signing these will
be included with the message. When verifying these will be searched for
the signers certificates.
The input can be in PEM, DER, or PKCS#12 format.

=item B<-cades>

When used with B<-sign>,
add an ESS signingCertificate or ESS signingCertificateV2 signed-attribute
to the SignerInfo, in order to make the signature comply with the requirements
for a CAdES Basic Electronic Signature (CAdES-BES).

=item B<-nodetach>

When signing a message use opaque signing: this form is more resistant
to translation by mail relays but it cannot be read by mail agents that
do not support S/MIME.  Without this option cleartext signing with
the MIME type multipart/signed is used.

=item B<-nocerts>

When signing a message the signer's certificate is normally included
with this option it is excluded. This will reduce the size of the
signed message but the verifier must have a copy of the signers certificate
available locally (passed using the B<-certfile> option for example).

=item B<-noattr>

Normally when a message is signed a set of attributes are included which
include the signing time and supported symmetric algorithms. With this
option they are not included.

=item B<-nosmimecap>

Exclude the list of supported algorithms from signed attributes, other options
such as signing time and content type are still included.

=item B<-receipt_request_all>, B<-receipt_request_first>

For B<-sign> option include a signed receipt request. Indicate requests should
be provided by all recipient or first tier recipients (those mailed directly
and not from a mailing list). Ignored it B<-receipt_request_from> is included.

=item B<-receipt_request_from> I<emailaddress>

For B<-sign> option include a signed receipt request. Add an explicit email
address where receipts should be supplied.

=item B<-receipt_request_to> I<emailaddress>

Add an explicit email address where signed receipts should be sent to. This
option B<must> but supplied if a signed receipt is requested.

=back

=head2 Verification options

=over 4

=item B<-signer> I<file>

If a message has been verified successfully then the signers certificate(s)
will be written to this file if the verification was successful.

=item B<-content> I<filename>

This specifies a file containing the detached content for operations taking
S/MIME input, such as the B<-verify> command. This is only usable if the CMS
structure is using the detached signature form where the content is
not included. This option will override any content if the input format
is S/MIME and it uses the multipart/signed MIME content type.

=item B<-no_content_verify>

Do not verify signed content signatures.

=item B<-no_attr_verify>

Do not verify signed attribute signatures.

=item B<-nosigs>

Don't verify message signature.

=item B<-noverify>

Do not verify the signers certificate of a signed message.

=item B<-nointern>

When verifying a message normally certificates (if any) included in
the message are searched for the signing certificate. With this option
only the certificates specified in the B<-certfile> option are used.
The supplied certificates can still be used as untrusted CAs however.

=item B<-cades>

When used with B<-verify>, require and check signer certificate digest.
See the NOTES section for more details.

=item B<-verify_retcode>

Exit nonzero on verification failure.

=item B<-CAfile> I<file>, B<-no-CAfile>, B<-CApath> I<dir>, B<-no-CApath>,
B<-CAstore> I<uri>, B<-no-CAstore>

See L<openssl-verification-options(1)/Trusted Certificate Options> for details.

=back

=head2 Output options

=over 4

=item B<-keyid>

Use subject key identifier to identify certificates instead of issuer name and
serial number. The supplied certificate B<must> include a subject key
identifier extension. Supported by B<-sign> and B<-encrypt> options.

=item B<-econtent_type> I<type>

Set the encapsulated content type to I<type> if not supplied the B<Data> type
is used. The I<type> argument can be any valid OID name in either text or
numerical format.

=item B<-text>

This option adds plain text (text/plain) MIME headers to the supplied
message if encrypting or signing. If decrypting or verifying it strips
off text headers: if the decrypted or verified message is not of MIME
type text/plain then an error occurs.

=item B<-certsout> I<file>

Any certificates contained in the input message are written to I<file>.

=item B<-to>, B<-from>, B<-subject>

The relevant email headers. These are included outside the signed
portion of a message so they may be included manually. If signing
then many S/MIME mail clients check the signers certificate's email
address matches that specified in the From: address.

=back

=head2 Printing options

=over 4

=item B<-noout>

For the B<-cmsout> operation do not output the parsed CMS structure.
This is useful if the syntax of the CMS structure is being checked.

=item B<-print>

For the B<-cmsout> operation print out all fields of the CMS structure.
This implies B<-noout>.
This is mainly useful for testing purposes.

=item B<-nameopt> I<option>

For the B<-cmsout> operation when B<-print> option is in use, specifies
printing options for string fields. For most cases B<utf8> is reasonable value.
See L<openssl-namedisplay-options(1)> for details.

=item B<-receipt_request_print>

For the B<-verify> operation print out the contents of any signed receipt
requests.

=back

=head2 Validation options

=over 4

=item B<-allow_proxy_certs>, B<-attime>, B<-no_check_time>,
B<-check_ss_sig>, B<-crl_check>, B<-crl_check_all>,
B<-explicit_policy>, B<-extended_crl>, B<-ignore_critical>, B<-inhibit_any>,
B<-inhibit_map>, B<-no_alt_chains>, B<-partial_chain>, B<-policy>,
B<-policy_check>, B<-policy_print>, B<-purpose>, B<-suiteB_128>,
B<-suiteB_128_only>, B<-suiteB_192>, B<-trusted_first>, B<-use_deltas>,
B<-auth_level>, B<-verify_depth>, B<-verify_email>, B<-verify_hostname>,
B<-verify_ip>, B<-verify_name>, B<-x509_strict> B<-issuer_checks>

Set various options of certificate chain verification.
See L<openssl-verification-options(1)/Verification Options> for details.

Any validation errors cause the command to exit.

=back

=head1 NOTES

The MIME message must be sent without any blank lines between the
headers and the output. Some mail programs will automatically add
a blank line. Piping the mail directly to sendmail is one way to
achieve the correct format.

The supplied message to be signed or encrypted must include the
necessary MIME headers or many S/MIME clients won't display it
properly (if at all). You can use the B<-text> option to automatically
add plain text headers.

A "signed and encrypted" message is one where a signed message is
then encrypted. This can be produced by encrypting an already signed
message: see the examples section.

This version of the program only allows one signer per message but it
will verify multiple signers on received messages. Some S/MIME clients
choke if a message contains multiple signers. It is possible to sign
messages "in parallel" by signing an already signed message.

The options B<-encrypt> and B<-decrypt> reflect common usage in S/MIME
clients. Strictly speaking these process CMS enveloped data: CMS
encrypted data is used for other purposes.

The B<-resign> option uses an existing message digest when adding a new
signer. This means that attributes must be present in at least one existing
signer using the same message digest or this operation will fail.

The B<-stream> and B<-indef> options enable streaming I/O support.
As a result the encoding is BER using indefinite length constructed encoding
and no longer DER. Streaming is supported for the B<-encrypt> operation and the
B<-sign> operation if the content is not detached.

Streaming is always used for the B<-sign> operation with detached data but
since the content is no longer part of the CMS structure the encoding
remains DER.

If the B<-decrypt> option is used without a recipient certificate then an
attempt is made to locate the recipient by trying each potential recipient
in turn using the supplied private key. To thwart the MMA attack
(Bleichenbacher's attack on PKCS #1 v1.5 RSA padding) all recipients are
tried whether they succeed or not and if no recipients match the message
is "decrypted" using a random key which will typically output garbage.
The B<-debug_decrypt> option can be used to disable the MMA attack protection
and return an error if no recipient can be found: this option should be used
with caution. For a fuller description see L<CMS_decrypt(3)>).

=head1 CADES BASIC ELECTRONIC SIGNATURE (CADES-BES)

A CAdES Basic Electronic Signature (CAdES-BES),
as defined in the European Standard ETSI EN 319 122-1 V1.1.1, contains:

=over 4

=item *

The signed user data as defined in CMS (RFC 3852);

=item *

Content-type of the EncapsulatedContentInfo value being signed;

=item *

Message-digest of the eContent OCTET STRING within encapContentInfo being signed;

=item *

An ESS signingCertificate or ESS signingCertificateV2 attribute,
as defined in Enhanced Security Services (ESS), RFC 2634 and RFC 5035.
An ESS signingCertificate attribute only allows for SHA-1 as digest algorithm.
An ESS signingCertificateV2 attribute allows for any digest algorithm.

=item *

The digital signature value computed on the user data and, when present, on the signed attributes.

NOTE that the B<-cades> option applies to the B<-sign> or B<-verify> operations.
With this option, the B<-verify> operation also requires that the
signingCertificate attribute is present and checks that the given identifiers
match the verification trust chain built during the verification process.

=back

=head1 EXIT CODES

=over 4

=item Z<>0

The operation was completely successfully.

=item Z<>1

An error occurred parsing the command options.

=item Z<>2

One of the input files could not be read.

=item Z<>3

An error occurred creating the CMS file or when reading the MIME
message.

=item Z<>4

An error occurred decrypting or verifying the message.

=item Z<>5

The message was verified correctly but an error occurred writing out
the signers certificates.

=back

=head1 COMPATIBILITY WITH PKCS#7 FORMAT

L<openssl-smime(1)> can only process the older B<PKCS#7> format.
B<openssl cms> supports Cryptographic Message Syntax format.
Use of some features will result in messages which cannot be processed by
applications which only support the older format. These are detailed below.

The use of the B<-keyid> option with B<-sign> or B<-encrypt>.

The B<-outform> I<PEM> option uses different headers.

The B<-compress> option.

The B<-secretkey> option when used with B<-encrypt>.

The use of PSS with B<-sign>.

The use of OAEP or non-RSA keys with B<-encrypt>.

Additionally the B<-EncryptedData_create> and B<-data_create> type cannot
be processed by the older L<openssl-smime(1)> command.

=head1 EXAMPLES

Create a cleartext signed message:

 openssl cms -sign -in message.txt -text -out mail.msg \
        -signer mycert.pem

Create an opaque signed message

 openssl cms -sign -in message.txt -text -out mail.msg -nodetach \
        -signer mycert.pem

Create a signed message, include some additional certificates and
read the private key from another file:

 openssl cms -sign -in in.txt -text -out mail.msg \
        -signer mycert.pem -inkey mykey.pem -certfile mycerts.pem

Create a signed message with two signers, use key identifier:

 openssl cms -sign -in message.txt -text -out mail.msg \
        -signer mycert.pem -signer othercert.pem -keyid

Send a signed message under Unix directly to sendmail, including headers:

 openssl cms -sign -in in.txt -text -signer mycert.pem \
        -from <EMAIL> -to someone@somewhere \
        -subject "Signed message" | sendmail someone@somewhere

Verify a message and extract the signer's certificate if successful:

 openssl cms -verify -in mail.msg -signer user.pem -out signedtext.txt

Send encrypted mail using triple DES:

 openssl cms -encrypt -in in.txt -from <EMAIL> \
        -to someone@somewhere -subject "Encrypted message" \
        -des3 user.pem -out mail.msg

Sign and encrypt mail:

 openssl cms -sign -in ml.txt -signer my.pem -text \
        | openssl cms -encrypt -out mail.msg \
        -from <EMAIL> -to someone@somewhere \
        -subject "Signed and Encrypted message" -des3 user.pem

Note: the encryption command does not include the B<-text> option because the
message being encrypted already has MIME headers.

Decrypt a message:

 openssl cms -decrypt -in mail.msg -recip mycert.pem -inkey key.pem

The output from Netscape form signing is a PKCS#7 structure with the
detached signature format. You can use this program to verify the
signature by line wrapping the base64 encoded structure and surrounding
it with:

 -----BEGIN PKCS7-----
 -----END PKCS7-----

and using the command,

 openssl cms -verify -inform PEM -in signature.pem -content content.txt

alternatively you can base64 decode the signature and use

 openssl cms -verify -inform DER -in signature.der -content content.txt

Create an encrypted message using 128 bit Camellia:

 openssl cms -encrypt -in plain.txt -camellia128 -out mail.msg cert.pem

Add a signer to an existing message:

 openssl cms -resign -in mail.msg -signer newsign.pem -out mail2.msg

Sign a message using RSA-PSS:

 openssl cms -sign -in message.txt -text -out mail.msg \
        -signer mycert.pem -keyopt rsa_padding_mode:pss

Create an encrypted message using RSA-OAEP:

 openssl cms -encrypt -in plain.txt -out mail.msg \
        -recip cert.pem -keyopt rsa_padding_mode:oaep

Use SHA256 KDF with an ECDH certificate:

 openssl cms -encrypt -in plain.txt -out mail.msg \
        -recip ecdhcert.pem -keyopt ecdh_kdf_md:sha256

Print CMS signed binary data in human-readable form:

openssl cms -in signed.cms -binary -inform DER -cmsout -print

=head1 BUGS

The MIME parser isn't very clever: it seems to handle most messages that I've
thrown at it but it may choke on others.

The code currently will only write out the signer's certificate to a file: if
the signer has a separate encryption certificate this must be manually
extracted. There should be some heuristic that determines the correct
encryption certificate.

Ideally a database should be maintained of a certificates for each email
address.

The code doesn't currently take note of the permitted symmetric encryption
algorithms as supplied in the SMIMECapabilities signed attribute. this means the
user has to manually include the correct encryption algorithm. It should store
the list of permitted ciphers in a database and only use those.

No revocation checking is done on the signer's certificate.

=head1 SEE ALSO

L<ossl_store-file(7)>

=head1 HISTORY

The use of multiple B<-signer> options and the B<-resign> command were first
added in OpenSSL 1.0.0.

The B<-keyopt> option was added in OpenSSL 1.0.2.

Support for RSA-OAEP and RSA-PSS was added in OpenSSL 1.0.2.

The use of non-RSA keys with B<-encrypt> and B<-decrypt>
was added in OpenSSL 1.0.2.

The -no_alt_chains option was added in OpenSSL 1.0.2b.

The B<-nameopt> option was added in OpenSSL 3.0.0.

The B<-engine> option was deprecated in OpenSSL 3.0.

=head1 COPYRIGHT

Copyright 2008-2023 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
