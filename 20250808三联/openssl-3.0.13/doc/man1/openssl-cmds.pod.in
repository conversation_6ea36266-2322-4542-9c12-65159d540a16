=pod
{- OpenSSL::safe::output_do_not_edit_headers(); -}

=head1 NAME

=for openssl names: openssl-cmds

asn1parse,
ca,
ciphers,
cmp,
cms,
crl,
crl2pkcs7,
dgst,
dhparam,
dsa,
dsaparam,
ec,
ecparam,
enc,
engine,
errstr,
gendsa,
genpkey,
genrsa,
info,
kdf,
mac,
nseq,
ocsp,
passwd,
pkcs12,
pkcs7,
pkcs8,
pkey,
pkeyparam,
pkeyutl,
prime,
rand,
rehash,
req,
rsa,
rsautl,
s_client,
s_server,
s_time,
sess_id,
smime,
speed,
spkac,
srp,
storeutl,
ts,
verify,
version,
x509
- OpenSSL application commands

=for openssl foreign manual apropos(1)

=head1 SYNOPSIS

=for openssl generic

B<openssl> I<cmd> B<-help> | [I<-option> | I<-option> I<arg>] ... [I<arg>] ...

=head1 DESCRIPTION

Every I<cmd> listed above is a (sub-)command of the L<openssl(1)> application.
It has its own detailed manual page at B<openssl-I<cmd>>(1). For example, to
view the manual page for the B<openssl dgst> command, type C<man openssl-dgst>.

=head1 OPTIONS

Among others, every subcommand has a help option.

=over 4

=item B<-help>

Print out a usage message for the subcommand.

=back

=head1 SEE ALSO

L<openssl(1)>,
L<openssl-asn1parse(1)>,
L<openssl-ca(1)>,
L<openssl-ciphers(1)>,
L<openssl-cmp(1)>,
L<openssl-cms(1)>,
L<openssl-crl(1)>,
L<openssl-crl2pkcs7(1)>,
L<openssl-dgst(1)>,
L<openssl-dhparam(1)>,
L<openssl-dsa(1)>,
L<openssl-dsaparam(1)>,
L<openssl-ec(1)>,
L<openssl-ecparam(1)>,
L<openssl-enc(1)>,
L<openssl-engine(1)>,
L<openssl-errstr(1)>,
L<openssl-gendsa(1)>,
L<openssl-genpkey(1)>,
L<openssl-genrsa(1)>,
L<openssl-info(1)>,
L<openssl-kdf(1)>,
L<openssl-mac(1)>,
L<openssl-nseq(1)>,
L<openssl-ocsp(1)>,
L<openssl-passwd(1)>,
L<openssl-pkcs12(1)>,
L<openssl-pkcs7(1)>,
L<openssl-pkcs8(1)>,
L<openssl-pkey(1)>,
L<openssl-pkeyparam(1)>,
L<openssl-pkeyutl(1)>,
L<openssl-prime(1)>,
L<openssl-rand(1)>,
L<openssl-rehash(1)>,
L<openssl-req(1)>,
L<openssl-rsa(1)>,
L<openssl-rsautl(1)>,
L<openssl-s_client(1)>,
L<openssl-s_server(1)>,
L<openssl-s_time(1)>,
L<openssl-sess_id(1)>,
L<openssl-smime(1)>,
L<openssl-speed(1)>,
L<openssl-spkac(1)>,
L<openssl-srp(1)>,
L<openssl-storeutl(1)>,
L<openssl-ts(1)>,
L<openssl-verify(1)>,
L<openssl-version(1)>,
L<openssl-x509(1)>,

=head1 HISTORY

=for openssl foreign manual apropos(1)

Initially, the manual page entry for the C<openssl I<cmd>> command used
to be available at I<cmd>(1). Later, the alias B<openssl-I<cmd>>(1) was
introduced, which made it easier to group the openssl commands using
the L<apropos(1)> command or the shell's tab completion.

In order to reduce cluttering of the global manual page namespace,
the manual page entries without the 'openssl-' prefix have been
deprecated in OpenSSL 3.0 and will be removed in OpenSSL 4.0.

=head1 COPYRIGHT

Copyright 2019-2022 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
