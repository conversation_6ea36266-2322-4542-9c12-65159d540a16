=pod

=begin comment
WARNING: do not edit!
Generated by <PERSON><PERSON><PERSON> from doc/man1/openssl-pkeyparam.pod.in

=end comment

=head1 NAME

openssl-pkeyparam - public key algorithm parameter processing command

=head1 SYNOPSIS

B<openssl> B<pkeyparam>
[B<-help>]
[B<-in> I<filename>]
[B<-out> I<filename>]
[B<-text>]
[B<-noout>]
[B<-check>]
[B<-engine> I<id>]
[B<-provider> I<name>]
[B<-provider-path> I<path>]
[B<-propquery> I<propq>]

=head1 DESCRIPTION

This command processes public key algorithm parameters.
They can be checked for correctness and their components printed out.

=head1 OPTIONS

=over 4

=item B<-help>

Print out a usage message.

=item B<-in> I<filename>

This specifies the input filename to read parameters from or standard input if
this option is not specified.

=item B<-out> I<filename>

This specifies the output filename to write parameters to or standard output if
this option is not specified.

=item B<-text>

Prints out the parameters in plain text in addition to the encoded version.

=item B<-noout>

Do not output the encoded version of the parameters.

=item B<-check>

This option checks the correctness of parameters.

=item B<-engine> I<id>

See L<openssl(1)/Engine Options>.
This option is deprecated.

=item B<-provider> I<name>

=item B<-provider-path> I<path>

=item B<-propquery> I<propq>

See L<openssl(1)/Provider Options>, L<provider(7)>, and L<property(7)>.

=back

=head1 EXAMPLES

Print out text version of parameters:

 openssl pkeyparam -in param.pem -text

=head1 NOTES

There are no B<-inform> or B<-outform> options for this command because only
PEM format is supported because the key type is determined by the PEM headers.

=head1 SEE ALSO

L<openssl(1)>,
L<openssl-genpkey(1)>,
L<openssl-rsa(1)>,
L<openssl-pkcs8(1)>,
L<openssl-dsa(1)>,
L<openssl-genrsa(1)>,
L<openssl-gendsa(1)>

=head1 HISTORY

The B<-engine> option was deprecated in OpenSSL 3.0.

=head1 COPYRIGHT

Copyright 2006-2021 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
