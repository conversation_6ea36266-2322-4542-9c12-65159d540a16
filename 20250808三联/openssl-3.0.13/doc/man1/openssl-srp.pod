=pod

=begin comment

WARNING: do not edit!
Generated by <PERSON><PERSON><PERSON> from doc/man1/openssl-srp.pod.in

=end comment

=head1 NAME

openssl-srp - maintain SRP password file

=head1 SYNOPSIS

B<openssl srp>
[B<-help>]
[B<-verbose>]
[B<-add>]
[B<-modify>]
[B<-delete>]
[B<-list>]
[B<-name> I<section>]
[B<-srpvfile> I<file>]
[B<-gn> I<identifier>]
[B<-userinfo> I<text>]
[B<-passin> I<arg>]
[B<-passout> I<arg>]
[B<-engine> I<id>]
[B<-rand> I<files>]
[B<-writerand> I<file>]
[B<-provider> I<name>]
[B<-provider-path> I<path>]
[B<-propquery> I<propq>]
[B<-config> I<configfile>]
[I<user> ...]

=head1 DESCRIPTION

This command is deprecated. It is used to maintain an SRP (secure remote
password) file. At most one of the B<-add>, B<-modify>, B<-delete>, and B<-list>
options can be specified.
These options take zero or more usernames as parameters and perform the
appropriate operation on the SRP file.
For B<-list>, if no I<user> is given then all users are displayed.

The configuration file to use, and the section within the file, can be
specified with the B<-config> and B<-name> flags, respectively.

=head1 OPTIONS

=over 4

=item B<-help>

Display an option summary.

=item B<-verbose>

Generate verbose output while processing.

=item B<-add>

Add a user and SRP verifier.

=item B<-modify>

Modify the SRP verifier of an existing user.

=item B<-delete>

Delete user from verifier file.

=item B<-list>

List users.

=item B<-name>

The particular SRP definition to use.

=item B<-srpvfile> I<file>

If the config file is not specified,
B<-srpvfile> can be used to specify the file to operate on.

=item B<-gn>

Specifies the B<g> and B<N> values, using one of
the strengths defined in IETF RFC 5054.

=item B<-userinfo>

specifies additional information to add when
adding or modifying a user.

=item B<-passin> I<arg>, B<-passout> I<arg>

The password source for the input and output file.
For more information about the format of B<arg>
see L<openssl-passphrase-options(1)>.

=item B<-engine> I<id>

See L<openssl(1)/Engine Options>.
This option is deprecated.

=item B<-rand> I<files>, B<-writerand> I<file>

See L<openssl(1)/Random State Options> for details.

=item B<-provider> I<name>

=item B<-provider-path> I<path>

=item B<-propquery> I<propq>

See L<openssl(1)/Provider Options>, L<provider(7)>, and L<property(7)>.

=item B<-config> I<configfile>

See L<openssl(1)/Configuration Option>.

[B<-rand> I<files>]
[B<-writerand> I<file>]

=back

=head1 HISTORY

The B<-engine> option was deprecated in OpenSSL 3.0.

=head1 COPYRIGHT

Copyright 2017-2021 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
