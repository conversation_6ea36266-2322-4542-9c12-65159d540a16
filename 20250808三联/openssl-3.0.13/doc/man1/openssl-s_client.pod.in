=pod
{- OpenSSL::safe::output_do_not_edit_headers(); -}

=head1 NAME

openssl-s_client - SSL/TLS client program

=head1 SYNOPSIS

B<openssl> B<s_client>
[B<-help>]
[B<-ssl_config> I<section>]
[B<-connect> I<host:port>]
[B<-host> I<hostname>]
[B<-port> I<port>]
[B<-bind> I<host:port>]
[B<-proxy> I<host:port>]
[B<-proxy_user> I<userid>]
[B<-proxy_pass> I<arg>]
[B<-unix> I<path>]
[B<-4>]
[B<-6>]
[B<-servername> I<name>]
[B<-noservername>]
[B<-verify> I<depth>]
[B<-verify_return_error>]
[B<-verify_quiet>]
[B<-verifyCAfile> I<filename>]
[B<-verifyCApath> I<dir>]
[B<-verifyCAstore> I<uri>]
[B<-cert> I<filename>]
[B<-certform> B<DER>|B<PEM>|B<P12>]
[B<-cert_chain> I<filename>]
[B<-build_chain>]
[B<-CRL> I<filename>]
[B<-CRLform> B<DER>|B<PEM>]
[B<-crl_download>]
[B<-key> I<filename>|I<uri>]
[B<-keyform> B<DER>|B<PEM>|B<P12>|B<ENGINE>]
[B<-pass> I<arg>]
[B<-chainCAfile> I<filename>]
[B<-chainCApath> I<directory>]
[B<-chainCAstore> I<uri>]
[B<-requestCAfile> I<filename>]
[B<-dane_tlsa_domain> I<domain>]
[B<-dane_tlsa_rrdata> I<rrdata>]
[B<-dane_ee_no_namechecks>]
[B<-reconnect>]
[B<-showcerts>]
[B<-prexit>]
[B<-debug>]
[B<-trace>]
[B<-nocommands>]
[B<-security_debug>]
[B<-security_debug_verbose>]
[B<-msg>]
[B<-timeout>]
[B<-mtu> I<size>]
[B<-no_etm>]
[B<-keymatexport> I<label>]
[B<-keymatexportlen> I<len>]
[B<-msgfile> I<filename>]
[B<-nbio_test>]
[B<-state>]
[B<-nbio>]
[B<-crlf>]
[B<-ign_eof>]
[B<-no_ign_eof>]
[B<-psk_identity> I<identity>]
[B<-psk> I<key>]
[B<-psk_session> I<file>]
[B<-quiet>]
[B<-sctp>]
[B<-sctp_label_bug>]
[B<-fallback_scsv>]
[B<-async>]
[B<-maxfraglen> I<len>]
[B<-max_send_frag>]
[B<-split_send_frag>]
[B<-max_pipelines>]
[B<-read_buf>]
[B<-ignore_unexpected_eof>]
[B<-bugs>]
[B<-comp>]
[B<-no_comp>]
[B<-brief>]
[B<-legacy_server_connect>]
[B<-no_legacy_server_connect>]
[B<-allow_no_dhe_kex>]
[B<-sigalgs> I<sigalglist>]
[B<-curves> I<curvelist>]
[B<-cipher> I<cipherlist>]
[B<-ciphersuites> I<val>]
[B<-serverpref>]
[B<-starttls> I<protocol>]
[B<-name> I<hostname>]
[B<-xmpphost> I<hostname>]
[B<-name> I<hostname>]
[B<-tlsextdebug>]
[B<-no_ticket>]
[B<-sess_out> I<filename>]
[B<-serverinfo> I<types>]
[B<-sess_in> I<filename>]
[B<-serverinfo> I<types>]
[B<-status>]
[B<-alpn> I<protocols>]
[B<-nextprotoneg> I<protocols>]
[B<-ct>]
[B<-noct>]
[B<-ctlogfile>]
[B<-keylogfile> I<file>]
[B<-early_data> I<file>]
[B<-enable_pha>]
[B<-use_srtp> I<value>]
[B<-srpuser> I<value>]
[B<-srppass> I<value>]
[B<-srp_lateuser>]
[B<-srp_moregroups>]
[B<-srp_strength> I<number>]
{- $OpenSSL::safe::opt_name_synopsis -}
{- $OpenSSL::safe::opt_version_synopsis -}
{- $OpenSSL::safe::opt_x_synopsis -}
{- $OpenSSL::safe::opt_trust_synopsis -}
{- $OpenSSL::safe::opt_s_synopsis -}
{- $OpenSSL::safe::opt_r_synopsis -}
{- $OpenSSL::safe::opt_provider_synopsis -}
{- $OpenSSL::safe::opt_engine_synopsis -}[B<-ssl_client_engine> I<id>]
{- $OpenSSL::safe::opt_v_synopsis -}
[I<host>:I<port>]

=head1 DESCRIPTION

This command implements a generic SSL/TLS client which
connects to a remote host using SSL/TLS. It is a I<very> useful diagnostic
tool for SSL servers.

=head1 OPTIONS

In addition to the options below, this command also supports the
common and client only options documented
in the "Supported Command Line Commands" section of the L<SSL_CONF_cmd(3)>
manual page.

=over 4

=item B<-help>

Print out a usage message.

=item B<-ssl_config> I<section>

Use the specified section of the configuration file to configure the B<SSL_CTX> object.

=item B<-connect> I<host>:I<port>

This specifies the host and optional port to connect to. It is possible to
select the host and port using the optional target positional argument instead.
If neither this nor the target positional argument are specified then an attempt
is made to connect to the local host on port 4433.

=item B<-host> I<hostname>

Host to connect to; use B<-connect> instead.

=item B<-port> I<port>

Connect to the specified port; use B<-connect> instead.

=item B<-bind> I<host:port>

This specifies the host address and or port to bind as the source for the
connection.  For Unix-domain sockets the port is ignored and the host is
used as the source socket address.

=item B<-proxy> I<host:port>

When used with the B<-connect> flag, the program uses the host and port
specified with this flag and issues an HTTP CONNECT command to connect
to the desired server.

=item B<-proxy_user> I<userid>

When used with the B<-proxy> flag, the program will attempt to authenticate
with the specified proxy using basic (base64) authentication.
NB: Basic authentication is insecure; the credentials are sent to the proxy
in easily reversible base64 encoding before any TLS/SSL session is established.
Therefore, these credentials are easily recovered by anyone able to sniff/trace
the network. Use with caution.

=item B<-proxy_pass> I<arg>

The proxy password source, used with the B<-proxy_user> flag.
For more information about the format of B<arg>
see L<openssl-passphrase-options(1)>.

=item B<-unix> I<path>

Connect over the specified Unix-domain socket.

=item B<-4>

Use IPv4 only.

=item B<-6>

Use IPv6 only.

=item B<-servername> I<name>

Set the TLS SNI (Server Name Indication) extension in the ClientHello message to
the given value.
If B<-servername> is not provided, the TLS SNI extension will be populated with
the name given to B<-connect> if it follows a DNS name format. If B<-connect> is
not provided either, the SNI is set to "localhost".
This is the default since OpenSSL 1.1.1.

Even though SNI should normally be a DNS name and not an IP address, if
B<-servername> is provided then that name will be sent, regardless of whether
it is a DNS name or not.

This option cannot be used in conjunction with B<-noservername>.

=item B<-noservername>

Suppresses sending of the SNI (Server Name Indication) extension in the
ClientHello message. Cannot be used in conjunction with the B<-servername> or
B<-dane_tlsa_domain> options.

=item B<-cert> I<filename>

The client certificate to use, if one is requested by the server.
The default is not to use a certificate.

The chain for the client certificate may be specified using B<-cert_chain>.

=item B<-certform> B<DER>|B<PEM>|B<P12>

The client certificate file format to use; unspecified by default.
See L<openssl-format-options(1)> for details.

=item B<-cert_chain>

A file or URI of untrusted certificates to use when attempting to build the
certificate chain related to the certificate specified via the B<-cert> option.
The input can be in PEM, DER, or PKCS#12 format.

=item B<-build_chain>

Specify whether the application should build the client certificate chain to be
provided to the server.

=item B<-CRL> I<filename>

CRL file to use to check the server's certificate.

=item B<-CRLform> B<DER>|B<PEM>

The CRL file format; unspecified by default.
See L<openssl-format-options(1)> for details.

=item B<-crl_download>

Download CRL from distribution points in the certificate.

=item B<-key> I<filename>|I<uri>

The client private key to use.
If not specified then the certificate file will be used to read also the key.

=item B<-keyform> B<DER>|B<PEM>|B<P12>|B<ENGINE>

The key format; unspecified by default.
See L<openssl-format-options(1)> for details.

=item B<-pass> I<arg>

the private key and certificate file password source.
For more information about the format of I<arg>
see L<openssl-passphrase-options(1)>.

=item B<-verify> I<depth>

The verify depth to use. This specifies the maximum length of the
server certificate chain and turns on server certificate verification.
Currently the verify operation continues after errors so all the problems
with a certificate chain can be seen. As a side effect the connection
will never fail due to a server certificate verify failure.

=item B<-verify_return_error>

Return verification errors instead of continuing. This will typically
abort the handshake with a fatal error.

=item B<-verify_quiet>

Limit verify output to only errors.

=item B<-verifyCAfile> I<filename>

A file in PEM format containing trusted certificates to use
for verifying the server's certificate.

=item B<-verifyCApath> I<dir>

A directory containing trusted certificates to use
for verifying the server's certificate.
This directory must be in "hash format",
see L<openssl-verify(1)> for more information.

=item B<-verifyCAstore> I<uri>

The URI of a store containing trusted certificates to use
for verifying the server's certificate.

=item B<-chainCAfile> I<file>

A file in PEM format containing trusted certificates to use
when attempting to build the client certificate chain.

=item B<-chainCApath> I<directory>

A directory containing trusted certificates to use
for building the client certificate chain provided to the server.
This directory must be in "hash format",
see L<openssl-verify(1)> for more information.

=item B<-chainCAstore> I<uri>

The URI of a store containing trusted certificates to use
when attempting to build the client certificate chain.
The URI may indicate a single certificate, as well as a collection of them.
With URIs in the C<file:> scheme, this acts as B<-chainCAfile> or
B<-chainCApath>, depending on if the URI indicates a directory or a
single file.
See L<ossl_store-file(7)> for more information on the C<file:> scheme.

=item B<-requestCAfile> I<file>

A file containing a list of certificates whose subject names will be sent
to the server in the B<certificate_authorities> extension. Only supported
for TLS 1.3

=item B<-dane_tlsa_domain> I<domain>

Enable RFC6698/RFC7671 DANE TLSA authentication and specify the
TLSA base domain which becomes the default SNI hint and the primary
reference identifier for hostname checks.  This must be used in
combination with at least one instance of the B<-dane_tlsa_rrdata>
option below.

When DANE authentication succeeds, the diagnostic output will include
the lowest (closest to 0) depth at which a TLSA record authenticated
a chain certificate.  When that TLSA record is a "2 1 0" trust
anchor public key that signed (rather than matched) the top-most
certificate of the chain, the result is reported as "TA public key
verified".  Otherwise, either the TLSA record "matched TA certificate"
at a positive depth or else "matched EE certificate" at depth 0.

=item B<-dane_tlsa_rrdata> I<rrdata>

Use one or more times to specify the RRDATA fields of the DANE TLSA
RRset associated with the target service.  The I<rrdata> value is
specified in "presentation form", that is four whitespace separated
fields that specify the usage, selector, matching type and associated
data, with the last of these encoded in hexadecimal.  Optional
whitespace is ignored in the associated data field.  For example:

  $ openssl s_client -brief -starttls smtp \
    -connect smtp.example.com:25 \
    -dane_tlsa_domain smtp.example.com \
    -dane_tlsa_rrdata "2 1 1
      B111DD8A1C2091A89BD4FD60C57F0716CCE50FEEFF8137CDBEE0326E 02CF362B" \
    -dane_tlsa_rrdata "2 1 1
      60B87575447DCBA2A36B7D11AC09FB24A9DB406FEE12D2CC90180517 616E8A18"
  ...
  Verification: OK
  Verified peername: smtp.example.com
  DANE TLSA 2 1 1 ...ee12d2cc90180517616e8a18 matched TA certificate at depth 1
  ...

=item B<-dane_ee_no_namechecks>

This disables server name checks when authenticating via DANE-EE(3) TLSA
records.
For some applications, primarily web browsers, it is not safe to disable name
checks due to "unknown key share" attacks, in which a malicious server can
convince a client that a connection to a victim server is instead a secure
connection to the malicious server.
The malicious server may then be able to violate cross-origin scripting
restrictions.
Thus, despite the text of RFC7671, name checks are by default enabled for
DANE-EE(3) TLSA records, and can be disabled in applications where it is safe
to do so.
In particular, SMTP and XMPP clients should set this option as SRV and MX
records already make it possible for a remote domain to redirect client
connections to any server of its choice, and in any case SMTP and XMPP clients
do not execute scripts downloaded from remote servers.

=item B<-reconnect>

Reconnects to the same server 5 times using the same session ID, this can
be used as a test that session caching is working.

=item B<-showcerts>

Displays the server certificate list as sent by the server: it only consists of
certificates the server has sent (in the order the server has sent them). It is
B<not> a verified chain.

=item B<-prexit>

Print session information when the program exits. This will always attempt
to print out information even if the connection fails. Normally information
will only be printed out once if the connection succeeds. This option is useful
because the cipher in use may be renegotiated or the connection may fail
because a client certificate is required or is requested only after an
attempt is made to access a certain URL. Note: the output produced by this
option is not always accurate because a connection might never have been
established.

=item B<-state>

Prints out the SSL session states.

=item B<-debug>

Print extensive debugging information including a hex dump of all traffic.

=item B<-nocommands>

Do not use interactive command letters.

=item B<-security_debug>

Enable security debug messages.

=item B<-security_debug_verbose>

Output more security debug output.

=item B<-msg>

Show protocol messages.

=item B<-timeout>

Enable send/receive timeout on DTLS connections.

=item B<-mtu> I<size>

Set MTU of the link layer to the specified size.

=item B<-no_etm>

Disable Encrypt-then-MAC negotiation.

=item B<-keymatexport> I<label>

Export keying material using the specified label.

=item B<-keymatexportlen> I<len>

Export the specified number of bytes of keying material; default is 20.

Show all protocol messages with hex dump.

=item B<-trace>

Show verbose trace output of protocol messages.

=item B<-msgfile> I<filename>

File to send output of B<-msg> or B<-trace> to, default standard output.

=item B<-nbio_test>

Tests nonblocking I/O

=item B<-nbio>

Turns on nonblocking I/O

=item B<-crlf>

This option translated a line feed from the terminal into CR+LF as required
by some servers.

=item B<-ign_eof>

Inhibit shutting down the connection when end of file is reached in the
input.

=item B<-quiet>

Inhibit printing of session and certificate information.  This implicitly
turns on B<-ign_eof> as well.

=item B<-no_ign_eof>

Shut down the connection when end of file is reached in the input.
Can be used to override the implicit B<-ign_eof> after B<-quiet>.

=item B<-psk_identity> I<identity>

Use the PSK identity I<identity> when using a PSK cipher suite.
The default value is "Client_identity" (without the quotes).

=item B<-psk> I<key>

Use the PSK key I<key> when using a PSK cipher suite. The key is
given as a hexadecimal number without leading 0x, for example -psk
1a2b3c4d.
This option must be provided in order to use a PSK cipher.

=item B<-psk_session> I<file>

Use the pem encoded SSL_SESSION data stored in I<file> as the basis of a PSK.
Note that this will only work if TLSv1.3 is negotiated.

=item B<-sctp>

Use SCTP for the transport protocol instead of UDP in DTLS. Must be used in
conjunction with B<-dtls>, B<-dtls1> or B<-dtls1_2>. This option is only
available where OpenSSL has support for SCTP enabled.

=item B<-sctp_label_bug>

Use the incorrect behaviour of older OpenSSL implementations when computing
endpoint-pair shared secrets for DTLS/SCTP. This allows communication with
older broken implementations but breaks interoperability with correct
implementations. Must be used in conjunction with B<-sctp>. This option is only
available where OpenSSL has support for SCTP enabled.

=item B<-fallback_scsv>

Send TLS_FALLBACK_SCSV in the ClientHello.

=item B<-async>

Switch on asynchronous mode. Cryptographic operations will be performed
asynchronously. This will only have an effect if an asynchronous capable engine
is also used via the B<-engine> option. For test purposes the dummy async engine
(dasync) can be used (if available).

=item B<-maxfraglen> I<len>

Enable Maximum Fragment Length Negotiation; allowed values are
C<512>, C<1024>, C<2048>, and C<4096>.

=item B<-max_send_frag> I<int>

The maximum size of data fragment to send.
See L<SSL_CTX_set_max_send_fragment(3)> for further information.

=item B<-split_send_frag> I<int>

The size used to split data for encrypt pipelines. If more data is written in
one go than this value then it will be split into multiple pipelines, up to the
maximum number of pipelines defined by max_pipelines. This only has an effect if
a suitable cipher suite has been negotiated, an engine that supports pipelining
has been loaded, and max_pipelines is greater than 1. See
L<SSL_CTX_set_split_send_fragment(3)> for further information.

=item B<-max_pipelines> I<int>

The maximum number of encrypt/decrypt pipelines to be used. This will only have
an effect if an engine has been loaded that supports pipelining (e.g. the dasync
engine) and a suitable cipher suite has been negotiated. The default value is 1.
See L<SSL_CTX_set_max_pipelines(3)> for further information.

=item B<-read_buf> I<int>

The default read buffer size to be used for connections. This will only have an
effect if the buffer size is larger than the size that would otherwise be used
and pipelining is in use (see L<SSL_CTX_set_default_read_buffer_len(3)> for
further information).

=item B<-ignore_unexpected_eof>

Some TLS implementations do not send the mandatory close_notify alert on
shutdown. If the application tries to wait for the close_notify alert but the
peer closes the connection without sending it, an error is generated. When this
option is enabled the peer does not need to send the close_notify alert and a
closed connection will be treated as if the close_notify alert was received.
For more information on shutting down a connection, see L<SSL_shutdown(3)>.

=item B<-bugs>

There are several known bugs in SSL and TLS implementations. Adding this
option enables various workarounds.

=item B<-comp>

Enables support for SSL/TLS compression.
This option was introduced in OpenSSL 1.1.0.
TLS compression is not recommended and is off by default as of
OpenSSL 1.1.0.

=item B<-no_comp>

Disables support for SSL/TLS compression.
TLS compression is not recommended and is off by default as of
OpenSSL 1.1.0.

=item B<-brief>

Only provide a brief summary of connection parameters instead of the
normal verbose output.

=item B<-sigalgs> I<sigalglist>

Specifies the list of signature algorithms that are sent by the client.
The server selects one entry in the list based on its preferences.
For example strings, see L<SSL_CTX_set1_sigalgs(3)>

=item B<-curves> I<curvelist>

Specifies the list of supported curves to be sent by the client. The curve is
ultimately selected by the server. For a list of all curves, use:

    $ openssl ecparam -list_curves

=item B<-cipher> I<cipherlist>

This allows the TLSv1.2 and below cipher list sent by the client to be modified.
This list will be combined with any TLSv1.3 ciphersuites that have been
configured. Although the server determines which ciphersuite is used it should
take the first supported cipher in the list sent by the client. See
L<openssl-ciphers(1)> for more information.

=item B<-ciphersuites> I<val>

This allows the TLSv1.3 ciphersuites sent by the client to be modified. This
list will be combined with any TLSv1.2 and below ciphersuites that have been
configured. Although the server determines which cipher suite is used it should
take the first supported cipher in the list sent by the client. See
L<openssl-ciphers(1)> for more information. The format for this list is a simple
colon (":") separated list of TLSv1.3 ciphersuite names.

=item B<-starttls> I<protocol>

Send the protocol-specific message(s) to switch to TLS for communication.
I<protocol> is a keyword for the intended protocol.  Currently, the only
supported keywords are "smtp", "pop3", "imap", "ftp", "xmpp", "xmpp-server",
"irc", "postgres", "mysql", "lmtp", "nntp", "sieve" and "ldap".

=item B<-xmpphost> I<hostname>

This option, when used with "-starttls xmpp" or "-starttls xmpp-server",
specifies the host for the "to" attribute of the stream element.
If this option is not specified, then the host specified with "-connect"
will be used.

This option is an alias of the B<-name> option for "xmpp" and "xmpp-server".

=item B<-name> I<hostname>

This option is used to specify hostname information for various protocols
used with B<-starttls> option. Currently only "xmpp", "xmpp-server",
"smtp" and "lmtp" can utilize this B<-name> option.

If this option is used with "-starttls xmpp" or "-starttls xmpp-server",
if specifies the host for the "to" attribute of the stream element. If this
option is not specified, then the host specified with "-connect" will be used.

If this option is used with "-starttls lmtp" or "-starttls smtp", it specifies
the name to use in the "LMTP LHLO" or "SMTP EHLO" message, respectively. If
this option is not specified, then "mail.example.com" will be used.

=item B<-tlsextdebug>

Print out a hex dump of any TLS extensions received from the server.

=item B<-no_ticket>

Disable RFC4507bis session ticket support.

=item B<-sess_out> I<filename>

Output SSL session to I<filename>.

=item B<-sess_in> I<filename>

Load SSL session from I<filename>. The client will attempt to resume a
connection from this session.

=item B<-serverinfo> I<types>

A list of comma-separated TLS Extension Types (numbers between 0 and
65535).  Each type will be sent as an empty ClientHello TLS Extension.
The server's response (if any) will be encoded and displayed as a PEM
file.

=item B<-status>

Sends a certificate status request to the server (OCSP stapling). The server
response (if any) is printed out.

=item B<-alpn> I<protocols>, B<-nextprotoneg> I<protocols>

These flags enable the Enable the Application-Layer Protocol Negotiation
or Next Protocol Negotiation (NPN) extension, respectively. ALPN is the
IETF standard and replaces NPN.
The I<protocols> list is a comma-separated list of protocol names that
the client should advertise support for. The list should contain the most
desirable protocols first.  Protocol names are printable ASCII strings,
for example "http/1.1" or "spdy/3".
An empty list of protocols is treated specially and will cause the
client to advertise support for the TLS extension but disconnect just
after receiving ServerHello with a list of server supported protocols.
The flag B<-nextprotoneg> cannot be specified if B<-tls1_3> is used.

=item B<-ct>, B<-noct>

Use one of these two options to control whether Certificate Transparency (CT)
is enabled (B<-ct>) or disabled (B<-noct>).
If CT is enabled, signed certificate timestamps (SCTs) will be requested from
the server and reported at handshake completion.

Enabling CT also enables OCSP stapling, as this is one possible delivery method
for SCTs.

=item B<-ctlogfile>

A file containing a list of known Certificate Transparency logs. See
L<SSL_CTX_set_ctlog_list_file(3)> for the expected file format.

=item B<-keylogfile> I<file>

Appends TLS secrets to the specified keylog file such that external programs
(like Wireshark) can decrypt TLS connections.

=item B<-early_data> I<file>

Reads the contents of the specified file and attempts to send it as early data
to the server. This will only work with resumed sessions that support early
data and when the server accepts the early data.

=item B<-enable_pha>

For TLSv1.3 only, send the Post-Handshake Authentication extension. This will
happen whether or not a certificate has been provided via B<-cert>.

=item B<-use_srtp> I<value>

Offer SRTP key management, where B<value> is a colon-separated profile list.

=item B<-srpuser> I<value>

Set the SRP username to the specified value. This option is deprecated.

=item B<-srppass> I<value>

Set the SRP password to the specified value. This option is deprecated.

=item B<-srp_lateuser>

SRP username for the second ClientHello message. This option is deprecated.

=item B<-srp_moregroups>  This option is deprecated.

Tolerate other than the known B<g> and B<N> values.

=item B<-srp_strength> I<number>

Set the minimal acceptable length, in bits, for B<N>.  This option is
deprecated.

{- $OpenSSL::safe::opt_version_item -}

{- $OpenSSL::safe::opt_name_item -}

{- $OpenSSL::safe::opt_x_item -}

{- $OpenSSL::safe::opt_trust_item -}

{- $OpenSSL::safe::opt_s_item -}

{- $OpenSSL::safe::opt_r_item -}

{- $OpenSSL::safe::opt_provider_item -}

{- $OpenSSL::safe::opt_engine_item -}

{- output_off() if $disabled{"deprecated-3.0"}; "" -}
=item B<-ssl_client_engine> I<id>

Specify engine to be used for client certificate operations.
{- output_on() if $disabled{"deprecated-3.0"}; "" -}

{- $OpenSSL::safe::opt_v_item -}

Verification errors are displayed, for debugging, but the command will
proceed unless the B<-verify_return_error> option is used.

=item I<host>:I<port>

Rather than providing B<-connect>, the target hostname and optional port may
be provided as a single positional argument after all options. If neither this
nor B<-connect> are provided, falls back to attempting to connect to
I<localhost> on port I<4433>.

=back

=head1 CONNECTED COMMANDS

If a connection is established with an SSL server then any data received
from the server is displayed and any key presses will be sent to the
server. If end of file is reached then the connection will be closed down. When
used interactively (which means neither B<-quiet> nor B<-ign_eof> have been
given), then certain commands are also recognized which perform special
operations. These commands are a letter which must appear at the start of a
line. They are listed below.

=over 4

=item B<Q>

End the current SSL connection and exit.

=item B<R>

Renegotiate the SSL session (TLSv1.2 and below only).

=item B<k>

Send a key update message to the server (TLSv1.3 only)

=item B<K>

Send a key update message to the server and request one back (TLSv1.3 only)

=back

=head1 NOTES

This command can be used to debug SSL servers. To connect to an SSL HTTP
server the command:

 openssl s_client -connect servername:443

would typically be used (https uses port 443). If the connection succeeds
then an HTTP command can be given such as "GET /" to retrieve a web page.

If the handshake fails then there are several possible causes, if it is
nothing obvious like no client certificate then the B<-bugs>,
B<-ssl3>, B<-tls1>, B<-no_ssl3>, B<-no_tls1> options can be tried
in case it is a buggy server. In particular you should play with these
options B<before> submitting a bug report to an OpenSSL mailing list.

A frequent problem when attempting to get client certificates working
is that a web client complains it has no certificates or gives an empty
list to choose from. This is normally because the server is not sending
the clients certificate authority in its "acceptable CA list" when it
requests a certificate. By using this command, the CA list can be viewed
and checked. However, some servers only request client authentication
after a specific URL is requested. To obtain the list in this case it
is necessary to use the B<-prexit> option and send an HTTP request
for an appropriate page.

If a certificate is specified on the command line using the B<-cert>
option it will not be used unless the server specifically requests
a client certificate. Therefore, merely including a client certificate
on the command line is no guarantee that the certificate works.

If there are problems verifying a server certificate then the
B<-showcerts> option can be used to show all the certificates sent by the
server.

This command is a test tool and is designed to continue the
handshake after any certificate verification errors. As a result it will
accept any certificate chain (trusted or not) sent by the peer. Non-test
applications should B<not> do this as it makes them vulnerable to a MITM
attack. This behaviour can be changed by with the B<-verify_return_error>
option: any verify errors are then returned aborting the handshake.

The B<-bind> option may be useful if the server or a firewall requires
connections to come from some particular address and or port.

=head1 BUGS

Because this program has a lot of options and also because some of the
techniques used are rather old, the C source for this command is rather
hard to read and not a model of how things should be done.
A typical SSL client program would be much simpler.

The B<-prexit> option is a bit of a hack. We should really report
information whenever a session is renegotiated.

=head1 SEE ALSO

L<openssl(1)>,
L<openssl-sess_id(1)>,
L<openssl-s_server(1)>,
L<openssl-ciphers(1)>,
L<SSL_CONF_cmd(3)>,
L<SSL_CTX_set_max_send_fragment(3)>,
L<SSL_CTX_set_split_send_fragment(3)>,
L<SSL_CTX_set_max_pipelines(3)>,
L<ossl_store-file(7)>

=head1 HISTORY

The B<-no_alt_chains> option was added in OpenSSL 1.1.0.
The B<-name> option was added in OpenSSL 1.1.1.

The B<-certform> option has become obsolete in OpenSSL 3.0.0 and has no effect.

The B<-engine> option was deprecated in OpenSSL 3.0.

=head1 COPYRIGHT

Copyright 2000-2023 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
