=pod

=begin comment

WARNING: do not edit!
Generated by <PERSON><PERSON><PERSON> from doc/man1/openssl-ecparam.pod.in

=end comment

=head1 NAME

openssl-ecparam - EC parameter manipulation and generation

=head1 SYNOPSIS

B<openssl ecparam>
[B<-help>]
[B<-inform> B<DER>|B<PEM>]
[B<-outform> B<DER>|B<PEM>]
[B<-in> I<filename>]
[B<-out> I<filename>]
[B<-noout>]
[B<-text>]
[B<-check>]
[B<-check_named>]
[B<-name> I<arg>]
[B<-list_curves>]
[B<-conv_form> I<arg>]
[B<-param_enc> I<arg>]
[B<-no_seed>]
[B<-genkey>]
[B<-engine> I<id>]
[B<-rand> I<files>]
[B<-writerand> I<file>]
[B<-provider> I<name>]
[B<-provider-path> I<path>]
[B<-propquery> I<propq>]

=head1 DESCRIPTION

This command is used to manipulate or generate EC parameter files.

OpenSSL is currently not able to generate new groups and therefore
this command can only create EC parameters from known (named) curves.

=head1 OPTIONS

=over 4

=item B<-help>

Print out a usage message.

=item B<-inform> B<DER>|B<PEM>

The EC parameters input format; unspecified by default.
See L<openssl-format-options(1)> for details.

=item B<-outform> B<DER>|B<PEM>

The EC parameters output format; the default is B<PEM>.
See L<openssl-format-options(1)> for details.

Parameters are encoded as B<EcpkParameters> as specified in IETF RFC 3279.

=item B<-in> I<filename>

This specifies the input filename to read parameters from or standard input if
this option is not specified.

=item B<-out> I<filename>

This specifies the output filename parameters to. Standard output is used
if this option is not present. The output filename should B<not> be the same
as the input filename.

=item B<-noout>

This option inhibits the output of the encoded version of the parameters.

=item B<-text>

This option prints out the EC parameters in human readable form.

=item B<-check>

Validate the elliptic curve parameters.

=item B<-check_named>

Validate the elliptic name curve parameters by checking if the curve parameters
match any built-in curves.

=item B<-name> I<arg>

Use the EC parameters with the specified 'short' name. Use B<-list_curves>
to get a list of all currently implemented EC parameters.

=item B<-list_curves>

Print out a list of all currently implemented EC parameters names and exit.

=item B<-conv_form> I<arg>

This specifies how the points on the elliptic curve are converted
into octet strings. Possible values are: B<compressed>, B<uncompressed> (the
default value) and B<hybrid>. For more information regarding
the point conversion forms please read the X9.62 standard.
B<Note> Due to patent issues the B<compressed> option is disabled
by default for binary curves and can be enabled by defining
the preprocessor macro B<OPENSSL_EC_BIN_PT_COMP> at compile time.

=item B<-param_enc> I<arg>

This specifies how the elliptic curve parameters are encoded.
Possible value are: B<named_curve>, i.e. the ec parameters are
specified by an OID, or B<explicit> where the ec parameters are
explicitly given (see RFC 3279 for the definition of the
EC parameters structures). The default value is B<named_curve>.
B<Note> the B<implicitlyCA> alternative, as specified in RFC 3279,
is currently not implemented in OpenSSL.

=item B<-no_seed>

This option inhibits that the 'seed' for the parameter generation
is included in the ECParameters structure (see RFC 3279).

=item B<-genkey>

This option will generate an EC private key using the specified parameters.

=item B<-engine> I<id>

See L<openssl(1)/Engine Options>.
This option is deprecated.

=item B<-rand> I<files>, B<-writerand> I<file>

See L<openssl(1)/Random State Options> for details.

=item B<-provider> I<name>

=item B<-provider-path> I<path>

=item B<-propquery> I<propq>

See L<openssl(1)/Provider Options>, L<provider(7)>, and L<property(7)>.

=back

The L<openssl-genpkey(1)> and L<openssl-pkeyparam(1)> commands are capable
of performing all the operations this command can, as well as supporting
other public key types.

=head1 EXAMPLES

The documentation for the L<openssl-genpkey(1)> and L<openssl-pkeyparam(1)>
commands contains examples equivalent to the ones listed here.

To create EC parameters with the group 'prime192v1':

  openssl ecparam -out ec_param.pem -name prime192v1

To create EC parameters with explicit parameters:

  openssl ecparam -out ec_param.pem -name prime192v1 -param_enc explicit

To validate given EC parameters:

  openssl ecparam -in ec_param.pem -check

To create EC parameters and a private key:

  openssl ecparam -out ec_key.pem -name prime192v1 -genkey

To change the point encoding to 'compressed':

  openssl ecparam -in ec_in.pem -out ec_out.pem -conv_form compressed

To print out the EC parameters to standard output:

  openssl ecparam -in ec_param.pem -noout -text

=head1 SEE ALSO

L<openssl(1)>,
L<openssl-pkeyparam(1)>,
L<openssl-genpkey(1)>,
L<openssl-ec(1)>,
L<openssl-dsaparam(1)>

=head1 HISTORY

The B<-engine> option was deprecated in OpenSSL 3.0.

The B<-C> option was removed in OpenSSL 3.0.

=head1 COPYRIGHT

Copyright 2003-2021 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
