=pod

=begin comment
{- join("\n", @autowarntext) -}

=end comment

=head1 NAME

openssl-pkcs7 - PKCS#7 command

=head1 SYNOPSIS

B<openssl> B<pkcs7>
[B<-help>]
[B<-inform> B<DER>|B<PEM>]
[B<-outform> B<DER>|B<PEM>]
[B<-in> I<filename>]
[B<-out> I<filename>]
[B<-print>]
[B<-print_certs>]
[B<-text>]
[B<-noout>]
{- $OpenSSL::safe::opt_engine_synopsis -}{- $OpenSSL::safe::opt_provider_synopsis -}

=head1 DESCRIPTION

This command processes PKCS#7 files.  Note that it only understands PKCS#7
v 1.5 as specified in IETF RFC 2315.  It cannot currently parse CMS as
described in IETF RFC 2630.

=head1 OPTIONS

=over 4

=item B<-help>

Print out a usage message.

=item B<-inform> B<DER>|B<PEM>, B<-outform> B<DER>|B<PEM>

The input and formats; the default is B<PEM>.
See L<openssl-format-options(1)> for details.

The data is a PKCS#7 Version 1.5 structure.

=item B<-in> I<filename>

This specifies the input filename to read from or standard input if this
option is not specified.

=item B<-out> I<filename>

Specifies the output filename to write to or standard output by
default.

=item B<-print>

Print out the full PKCS7 object.

=item B<-print_certs>

Prints out any certificates or CRLs contained in the file. They are
preceded by their subject and issuer names in one line format.

=item B<-text>

Prints out certificate details in full rather than just subject and
issuer names.

=item B<-noout>

Don't output the encoded version of the PKCS#7 structure (or certificates
if B<-print_certs> is set).

{- $OpenSSL::safe::opt_engine_item -}

{- $OpenSSL::safe::opt_provider_item -}

=back

=head1 EXAMPLES

Convert a PKCS#7 file from PEM to DER:

 openssl pkcs7 -in file.pem -outform DER -out file.der

Output all certificates in a file:

 openssl pkcs7 -in file.pem -print_certs -out certs.pem

=head1 SEE ALSO

L<openssl(1)>,
L<openssl-crl2pkcs7(1)>

=head1 HISTORY

The B<-engine> option was deprecated in OpenSSL 3.0.

=head1 COPYRIGHT

Copyright 2000-2021 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
