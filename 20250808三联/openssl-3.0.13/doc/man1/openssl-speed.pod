=pod

=begin comment

WARNING: do not edit!
Generated by <PERSON><PERSON><PERSON> from doc/man1/openssl-speed.pod.in

=end comment

=head1 NAME

openssl-speed - test library performance

=head1 SYNOPSIS

B<openssl speed>
[B<-help>]
[B<-elapsed>]
[B<-evp> I<algo>]
[B<-hmac> I<algo>]
[B<-cmac> I<algo>]
[B<-mb>]
[B<-aead>]
[B<-multi> I<num>]
[B<-async_jobs> I<num>]
[B<-misalign> I<num>]
[B<-decrypt>]
[B<-primes> I<num>]
[B<-seconds> I<num>]
[B<-bytes> I<num>]
[B<-mr>]
[B<-rand> I<files>]
[B<-writerand> I<file>]
[B<-engine> I<id>]
[B<-provider> I<name>]
[B<-provider-path> I<path>]
[B<-propquery> I<propq>]
[I<algorithm> ...]

=head1 DESCRIPTION

This command is used to test the performance of cryptographic algorithms.

=head1 OPTIONS

=over 4

=item B<-help>

Print out a usage message.

=item B<-elapsed>

When calculating operations- or bytes-per-second, use wall-clock time
instead of CPU user time as divisor. It can be useful when testing speed
of hardware engines.

=item B<-evp> I<algo>

Use the specified cipher or message digest algorithm via the EVP interface.
If I<algo> is an AEAD cipher, then you can pass B<-aead> to benchmark a
TLS-like sequence. And if I<algo> is a multi-buffer capable cipher, e.g.
aes-128-cbc-hmac-sha1, then B<-mb> will time multi-buffer operation.

To see the algorithms supported with this option, use
C<openssl list -digest-algorithms> or C<openssl list -cipher-algorithms>
command.

=item B<-multi> I<num>

Run multiple operations in parallel.

=item B<-async_jobs> I<num>

Enable async mode and start specified number of jobs.

=item B<-misalign> I<num>

Misalign the buffers by the specified number of bytes.

=item B<-hmac> I<digest>

Time the HMAC algorithm using the specified message digest.

=item B<-cmac> I<cipher>

Time the CMAC algorithm using the specified cipher e.g.
C<openssl speed -cmac aes128>.

=item B<-decrypt>

Time the decryption instead of encryption. Affects only the EVP testing.

=item B<-mb>

Enable multi-block mode on EVP-named cipher.

=item B<-aead>

Benchmark EVP-named AEAD cipher in TLS-like sequence.

=item B<-primes> I<num>

Generate a I<num>-prime RSA key and use it to run the benchmarks. This option
is only effective if RSA algorithm is specified to test.

=item B<-seconds> I<num>

Run benchmarks for I<num> seconds.

=item B<-bytes> I<num>

Run benchmarks on I<num>-byte buffers. Affects ciphers, digests and the CSPRNG.
The limit on the size of the buffer is INT_MAX - 64 bytes, which for a 32-bit 
int would be ********** bytes.

=item B<-mr>

Produce the summary in a mechanical, machine-readable, format.

=item B<-rand> I<files>, B<-writerand> I<file>

See L<openssl(1)/Random State Options> for details.

=item B<-engine> I<id>

See L<openssl(1)/Engine Options>.
This option is deprecated.

=item B<-provider> I<name>

=item B<-provider-path> I<path>

=item B<-propquery> I<propq>

See L<openssl(1)/Provider Options>, L<provider(7)>, and L<property(7)>.

=item I<algorithm> ...

If any I<algorithm> is given, then those algorithms are tested, otherwise a
pre-compiled grand selection is tested.

=back

=head1 BUGS

The I<algorithm> can be selected only from a pre-compiled subset of things
that the C<openssl speed> command knows about. To test any additional digest
or cipher algorithm supported by OpenSSL use the C<-evp> option.

There is no way to test the speed of any additional public key algorithms
supported by third party providers with the C<openssl speed> command.

=head1 HISTORY

The B<-engine> option was deprecated in OpenSSL 3.0.

=head1 COPYRIGHT

Copyright 2000-2022 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
