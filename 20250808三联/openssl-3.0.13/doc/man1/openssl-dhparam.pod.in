=pod
{- OpenSSL::safe::output_do_not_edit_headers(); -}

=head1 NAME

openssl-dhparam - DH parameter manipulation and generation

=head1 SYNOPSIS

B<openssl dhparam>
[B<-help>]
[B<-inform> B<DER>|B<PEM>]
[B<-outform> B<DER>|B<PEM>]
[B<-in> I<filename>]
[B<-out> I<filename>]
[B<-dsaparam>]
[B<-check>]
[B<-noout>]
[B<-text>]
[B<-2>]
[B<-3>]
[B<-5>]
{- $OpenSSL::safe::opt_engine_synopsis -}{- $OpenSSL::safe::opt_r_synopsis -}
{- $OpenSSL::safe::opt_provider_synopsis -}
[I<numbits>]

=head1 DESCRIPTION

This command is used to manipulate DH parameter files.

See L<openssl-genpkey(1)/EXAMPLES> for examples on how to generate
a key using a named safe prime group without generating intermediate
parameters.

=head1 OPTIONS

=over 4

=item B<-help>

Print out a usage message.

=item B<-inform> B<DER>|B<PEM>, B<-outform> B<DER>|B<PEM>

The input format and output format; the default is B<PEM>.
The object is compatible with the PKCS#3 B<DHparameter> structure.
See L<openssl-format-options(1)> for details.

=item B<-in> I<filename>

This specifies the input filename to read parameters from or standard input if
this option is not specified.

=item B<-out> I<filename>

This specifies the output filename parameters to. Standard output is used
if this option is not present. The output filename should B<not> be the same
as the input filename.

=item B<-dsaparam>

If this option is used, DSA rather than DH parameters are read or created;
they are converted to DH format.  Otherwise, "strong" primes (such
that (p-1)/2 is also prime) will be used for DH parameter generation.

DH parameter generation with the B<-dsaparam> option is much faster,
and the recommended exponent length is shorter, which makes DH key
exchange more efficient.  Beware that with such DSA-style DH
parameters, a fresh DH key should be created for each use to
avoid small-subgroup attacks that may be possible otherwise.

=item B<-check>

Performs numerous checks to see if the supplied parameters are valid and
displays a warning if not.

=item B<-2>, B<-3>, B<-5>

The generator to use, either 2, 3 or 5. If present then the
input file is ignored and parameters are generated instead. If not
present but I<numbits> is present, parameters are generated with the
default generator 2.

=item I<numbits>

This option specifies that a parameter set should be generated of size
I<numbits>. It must be the last option. If this option is present then
the input file is ignored and parameters are generated instead. If
this option is not present but a generator (B<-2>, B<-3> or B<-5>) is
present, parameters are generated with a default length of 2048 bits.
The minimum length is 512 bits. The maximum length is 10000 bits.

=item B<-noout>

This option inhibits the output of the encoded version of the parameters.

=item B<-text>

This option prints out the DH parameters in human readable form.

{- $OpenSSL::safe::opt_engine_item -}

{- $OpenSSL::safe::opt_r_item -}

{- $OpenSSL::safe::opt_provider_item -}

=back

=head1 NOTES

This command replaces the B<dh> and B<gendh> commands of previous
releases.

=head1 SEE ALSO

L<openssl(1)>,
L<openssl-pkeyparam(1)>,
L<openssl-dsaparam(1)>,
L<openssl-genpkey(1)>.

=head1 HISTORY

The B<-engine> option was deprecated in OpenSSL 3.0.

The B<-C> option was removed in OpenSSL 3.0.

=head1 COPYRIGHT

Copyright 2000-2023 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
