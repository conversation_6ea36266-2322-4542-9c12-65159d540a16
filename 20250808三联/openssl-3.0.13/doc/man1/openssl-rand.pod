=pod

=begin comment

WARNING: do not edit!
Generated by <PERSON><PERSON><PERSON> from doc/man1/openssl-rand.pod.in

=end comment

=head1 NAME

openssl-rand - generate pseudo-random bytes

=head1 SYNOPSIS

B<openssl rand>
[B<-help>]
[B<-out> I<file>]
[B<-base64>]
[B<-hex>]
[B<-engine> I<id>]
[B<-rand> I<files>]
[B<-writerand> I<file>]
[B<-provider> I<name>]
[B<-provider-path> I<path>]
[B<-propquery> I<propq>]
I<num>

=head1 DESCRIPTION

This command generates I<num> random bytes using a cryptographically
secure pseudo random number generator (CSPRNG).

The random bytes are generated using the L<RAND_bytes(3)> function,
which provides a security level of 256 bits, provided it managed to
seed itself successfully from a trusted operating system entropy source.
Otherwise, the command will fail with a nonzero error code.
For more details, see L<RAND_bytes(3)>, L<RAND(7)>, and L<EVP_RAND(7)>.

=head1 OPTIONS

=over 4

=item B<-help>

Print out a usage message.

=item B<-out> I<file>

Write to I<file> instead of standard output.

=item B<-base64>

Perform base64 encoding on the output.

=item B<-hex>

Show the output as a hex string.

=item B<-engine> I<id>

See L<openssl(1)/Engine Options>.
This option is deprecated.

=item B<-rand> I<files>, B<-writerand> I<file>

See L<openssl(1)/Random State Options> for details.

=item B<-provider> I<name>

=item B<-provider-path> I<path>

=item B<-propquery> I<propq>

See L<openssl(1)/Provider Options>, L<provider(7)>, and L<property(7)>.

=back

=head1 SEE ALSO

L<openssl(1)>,
L<RAND_bytes(3)>,
L<RAND(7)>,
L<EVP_RAND(7)>

=head1 HISTORY

The B<-engine> option was deprecated in OpenSSL 3.0.

=head1 COPYRIGHT

Copyright 2000-2021 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
