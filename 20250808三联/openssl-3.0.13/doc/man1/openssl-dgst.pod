=pod

=begin comment

WARNING: do not edit!
Generated by <PERSON><PERSON><PERSON> from doc/man1/openssl-dgst.pod.in

=end comment

=head1 NAME

openssl-dgst - perform digest operations

=head1 SYNOPSIS

B<openssl> B<dgst>|I<digest>
[B<-I<digest>>]
[B<-list>]
[B<-help>]
[B<-c>]
[B<-d>]
[B<-debug>]
[B<-hex>]
[B<-binary>]
[B<-xoflen> I<length>]
[B<-r>]
[B<-out> I<filename>]
[B<-sign> I<filename>|I<uri>]
[B<-keyform> B<DER>|B<PEM>|B<P12>|B<ENGINE>]
[B<-passin> I<arg>]
[B<-verify> I<filename>]
[B<-prverify> I<filename>]
[B<-signature> I<filename>]
[B<-sigopt> I<nm>:I<v>]
[B<-hmac> I<key>]
[B<-mac> I<alg>]
[B<-macopt> I<nm>:I<v>]
[B<-fips-fingerprint>]
[B<-engine> I<id>]
[B<-engine_impl> I<id>]
[B<-rand> I<files>]
[B<-writerand> I<file>]
[B<-provider> I<name>]
[B<-provider-path> I<path>]
[B<-propquery> I<propq>]
[I<file> ...]

=head1 DESCRIPTION

This command output the message digest of a supplied file or files
in hexadecimal, and also generates and verifies digital
signatures using message digests.

The generic name, B<openssl dgst>, may be used with an option specifying the
algorithm to be used.
The default digest is B<sha256>.
A supported I<digest> name may also be used as the sub-command name.
To see the list of supported algorithms, use C<openssl list -digest-algorithms>

=head1 OPTIONS

=over 4

=item B<-help>

Print out a usage message.

=item B<-I<digest>>

Specifies name of a supported digest to be used. See option B<-list> below :

=item B<-list>

Prints out a list of supported message digests.

=item B<-c>

Print out the digest in two digit groups separated by colons, only relevant if
the B<-hex> option is given as well.

=item B<-d>, B<-debug>

Print out BIO debugging information.

=item B<-hex>

Digest is to be output as a hex dump. This is the default case for a "normal"
digest as opposed to a digital signature.  See NOTES below for digital
signatures using B<-hex>.

=item B<-binary>

Output the digest or signature in binary form.

=item B<-xoflen> I<length>

Set the output length for XOF algorithms, such as B<shake128> and B<shake256>.
This option is not supported for signing operations.

For OpenSSL providers it is recommended to set this value for shake algorithms,
since the default values are set to only supply half of the maximum security
strength.

For backwards compatibility reasons the default xoflen length for B<shake128> is
16 (bytes) which results in a security strength of only 64 bits. To ensure the
maximum security strength of 128 bits, the xoflen should be set to at least 32.

For backwards compatibility reasons the default xoflen length for B<shake256> is
32 (bytes) which results in a security strength of only 128 bits. To ensure the
maximum security strength of 256 bits, the xoflen should be set to at least 64.

=item B<-r>

=for openssl foreign manual sha1sum(1)

Output the digest in the "coreutils" format, including newlines.
Used by programs like L<sha1sum(1)>.

=item B<-out> I<filename>

Filename to output to, or standard output by default.

=item B<-sign> I<filename>|I<uri>

Digitally sign the digest using the given private key. Note this option
does not support Ed25519 or Ed448 private keys. Use the L<openssl-pkeyutl(1)>
command instead for this.

=item B<-keyform> B<DER>|B<PEM>|B<P12>|B<ENGINE>

The format of the key to sign with; unspecified by default.
See L<openssl-format-options(1)> for details.

=item B<-sigopt> I<nm>:I<v>

Pass options to the signature algorithm during sign or verify operations.
Names and values of these options are algorithm-specific.

=item B<-passin> I<arg>

The private key password source. For more information about the format of I<arg>
see L<openssl-passphrase-options(1)>.

=item B<-verify> I<filename>

Verify the signature using the public key in "filename".
The output is either "Verified OK" or "Verification Failure".

=item B<-prverify> I<filename>

Verify the signature using the private key in "filename".

=item B<-signature> I<filename>

The actual signature to verify.

=item B<-hmac> I<key>

Create a hashed MAC using "key".

The L<openssl-mac(1)> command should be preferred to using this command line
option.

=item B<-mac> I<alg>

Create MAC (keyed Message Authentication Code). The most popular MAC
algorithm is HMAC (hash-based MAC), but there are other MAC algorithms
which are not based on hash, for instance B<gost-mac> algorithm,
supported by the B<gost> engine. MAC keys and other options should be set
via B<-macopt> parameter.

The L<openssl-mac(1)> command should be preferred to using this command line
option.

=item B<-macopt> I<nm>:I<v>

Passes options to MAC algorithm, specified by B<-mac> key.
Following options are supported by both by B<HMAC> and B<gost-mac>:

=over 4

=item B<key>:I<string>

Specifies MAC key as alphanumeric string (use if key contain printable
characters only). String length must conform to any restrictions of
the MAC algorithm for example exactly 32 chars for gost-mac.

=item B<hexkey>:I<string>

Specifies MAC key in hexadecimal form (two hex digits per byte).
Key length must conform to any restrictions of the MAC algorithm
for example exactly 32 chars for gost-mac.

=back

The L<openssl-mac(1)> command should be preferred to using this command line
option.

=item B<-fips-fingerprint>

Compute HMAC using a specific key for certain OpenSSL-FIPS operations.

=item B<-rand> I<files>, B<-writerand> I<file>

See L<openssl(1)/Random State Options> for details.

=item B<-engine> I<id>

See L<openssl(1)/Engine Options>.
This option is deprecated.

The engine is not used for digests unless the B<-engine_impl> option is
used or it is configured to do so, see L<config(5)/Engine Configuration Module>.

=item B<-engine_impl> I<id>

When used with the B<-engine> option, it specifies to also use
engine I<id> for digest operations.


=item B<-provider> I<name>

=item B<-provider-path> I<path>

=item B<-propquery> I<propq>

See L<openssl(1)/Provider Options>, L<provider(7)>, and L<property(7)>.

=item I<file> ...

File or files to digest. If no files are specified then standard input is
used.

=back


=head1 EXAMPLES

To create a hex-encoded message digest of a file:

 openssl dgst -md5 -hex file.txt
 or
 openssl md5 file.txt

To sign a file using SHA-256 with binary file output:

 openssl dgst -sha256 -sign privatekey.pem -out signature.sign file.txt
 or
 openssl sha256 -sign privatekey.pem -out signature.sign file.txt

To verify a signature:

 openssl dgst -sha256 -verify publickey.pem \
 -signature signature.sign \
 file.txt


=head1 NOTES

The digest mechanisms that are available will depend on the options
used when building OpenSSL.
The C<openssl list -digest-algorithms> command can be used to list them.

New or agile applications should use probably use SHA-256. Other digests,
particularly SHA-1 and MD5, are still widely used for interoperating
with existing formats and protocols.

When signing a file, this command will automatically determine the algorithm
(RSA, ECC, etc) to use for signing based on the private key's ASN.1 info.
When verifying signatures, it only handles the RSA, DSA, or ECDSA signature
itself, not the related data to identify the signer and algorithm used in
formats such as x.509, CMS, and S/MIME.

A source of random numbers is required for certain signing algorithms, in
particular ECDSA and DSA.

The signing and verify options should only be used if a single file is
being signed or verified.

Hex signatures cannot be verified using B<openssl>.  Instead, use "xxd -r"
or similar program to transform the hex signature into a binary signature
prior to verification.

The L<openssl-mac(1)> command is preferred over the B<-hmac>, B<-mac> and
B<-macopt> command line options.

=head1 SEE ALSO

L<openssl-mac(1)>

=head1 HISTORY

The default digest was changed from MD5 to SHA256 in OpenSSL 1.1.0.
The FIPS-related options were removed in OpenSSL 1.1.0.

The B<-engine> and B<-engine_impl> options were deprecated in OpenSSL 3.0.

=head1 COPYRIGHT

Copyright 2000-2022 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
