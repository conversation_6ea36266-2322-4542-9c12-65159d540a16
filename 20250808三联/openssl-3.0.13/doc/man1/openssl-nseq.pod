=pod

=begin comment

WARNING: do not edit!
Generated by <PERSON><PERSON><PERSON> from doc/man1/openssl-nseq.pod.in

=end comment

=head1 NAME

openssl-nseq - create or examine a Netscape certificate sequence

=head1 SYNOPSIS

B<openssl> B<nseq>
[B<-help>]
[B<-in> I<filename>]
[B<-out> I<filename>]
[B<-toseq>]
[B<-provider> I<name>]
[B<-provider-path> I<path>]
[B<-propquery> I<propq>]

=head1 DESCRIPTION

This command takes a file containing a Netscape certificate
sequence and prints out the certificates contained in it or takes a
file of certificates and converts it into a Netscape certificate
sequence.

A Netscape certificate sequence is an old Netscape-specific format that
can be sometimes be sent to browsers as an alternative to the standard PKCS#7
format when several certificates are sent to the browser, for example during
certificate enrollment.  It was also used by Netscape certificate server.

=head1 OPTIONS

=over 4

=item B<-help>

Print out a usage message.

=item B<-in> I<filename>

This specifies the input filename to read or standard input if this
option is not specified.

=item B<-out> I<filename>

Specifies the output filename or standard output by default.

=item B<-toseq>

Normally a Netscape certificate sequence will be input and the output
is the certificates contained in it. With the B<-toseq> option the
situation is reversed: a Netscape certificate sequence is created from
a file of certificates.

=item B<-provider> I<name>

=item B<-provider-path> I<path>

=item B<-propquery> I<propq>

See L<openssl(1)/Provider Options>, L<provider(7)>, and L<property(7)>.

=back

=head1 EXAMPLES

Output the certificates in a Netscape certificate sequence

 openssl nseq -in nseq.pem -out certs.pem

Create a Netscape certificate sequence

 openssl nseq -in certs.pem -toseq -out nseq.pem

=head1 COPYRIGHT

Copyright 2000-2020 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
