=pod

=begin comment

WARNING: do not edit!
Generated by <PERSON><PERSON><PERSON> from doc/man1/openssl-verify.pod.in

=end comment

=head1 NAME

openssl-verify - certificate verification command

=head1 SYNOPSIS

B<openssl> B<verify>
[B<-help>]
[B<-CRLfile> I<filename>|I<uri>]
[B<-crl_download>]
[B<-show_chain>]
[B<-verbose>]
[B<-trusted> I<filename>|I<uri>]
[B<-untrusted> I<filename>|I<uri>]
[B<-vfyopt> I<nm>:I<v>]
[B<-nameopt> I<option>]
[B<-CAfile> I<file>]
[B<-no-CAfile>]
[B<-CApath> I<dir>]
[B<-no-CApath>]
[B<-CAstore> I<uri>]
[B<-no-CAstore>]
[B<-engine> I<id>]
[B<-allow_proxy_certs>]
[B<-attime> I<timestamp>]
[B<-no_check_time>]
[B<-check_ss_sig>]
[B<-crl_check>]
[B<-crl_check_all>]
[B<-explicit_policy>]
[B<-extended_crl>]
[B<-ignore_critical>]
[B<-inhibit_any>]
[B<-inhibit_map>]
[B<-partial_chain>]
[B<-policy> I<arg>]
[B<-policy_check>]
[B<-policy_print>]
[B<-purpose> I<purpose>]
[B<-suiteB_128>]
[B<-suiteB_128_only>]
[B<-suiteB_192>]
[B<-trusted_first>]
[B<-no_alt_chains>]
[B<-use_deltas>]
[B<-auth_level> I<num>]
[B<-verify_depth> I<num>]
[B<-verify_email> I<email>]
[B<-verify_hostname> I<hostname>]
[B<-verify_ip> I<ip>]
[B<-verify_name> I<name>]
[B<-x509_strict>]
[B<-issuer_checks>]
[B<-provider> I<name>]
[B<-provider-path> I<path>]
[B<-propquery> I<propq>]
[B<-->]
[I<certificate> ...]

=head1 DESCRIPTION

This command verifies certificate chains. If a certificate chain has multiple
problems, this program attempts to display all of them.

=head1 OPTIONS

=over 4

=item B<-help>

Print out a usage message.

=item B<-CRLfile> I<filename>|I<uri>

The file or URI should contain one or more CRLs in PEM or DER format.
This option can be specified more than once to include CRLs from multiple
sources.

=item B<-crl_download>

Attempt to download CRL information for certificates via their CDP entries.

=item B<-show_chain>

Display information about the certificate chain that has been built (if
successful). Certificates in the chain that came from the untrusted list will be
flagged as "untrusted".

=item B<-verbose>

Print extra information about the operations being performed.

=item B<-trusted> I<filename>|I<uri>

A file or URI of (more or less) trusted certificates.
See L<openssl-verification-options(1)> for more information on trust settings.

This option can be specified more than once to load certificates from multiple
sources.

=item B<-untrusted> I<filename>|I<uri>

A file or URI of untrusted certificates to use for chain building.
This option can be specified more than once to load certificates from multiple
sources.

=item B<-vfyopt> I<nm>:I<v>

Pass options to the signature algorithm during verify operations.
Names and values of these options are algorithm-specific.

=item B<-nameopt> I<option>

This specifies how the subject or issuer names are displayed.
See L<openssl-namedisplay-options(1)> for details.

=item B<-engine> I<id>

See L<openssl(1)/Engine Options>.
This option is deprecated.

To load certificates or CRLs that require engine support, specify the
B<-engine> option before any of the
B<-trusted>, B<-untrusted> or B<-CRLfile> options.


=item B<-CAfile> I<file>, B<-no-CAfile>, B<-CApath> I<dir>, B<-no-CApath>,
B<-CAstore> I<uri>, B<-no-CAstore>

See L<openssl-verification-options(1)/Trusted Certificate Options> for details.

=item B<-allow_proxy_certs>, B<-attime>, B<-no_check_time>,
B<-check_ss_sig>, B<-crl_check>, B<-crl_check_all>,
B<-explicit_policy>, B<-extended_crl>, B<-ignore_critical>, B<-inhibit_any>,
B<-inhibit_map>, B<-no_alt_chains>, B<-partial_chain>, B<-policy>,
B<-policy_check>, B<-policy_print>, B<-purpose>, B<-suiteB_128>,
B<-suiteB_128_only>, B<-suiteB_192>, B<-trusted_first>, B<-use_deltas>,
B<-auth_level>, B<-verify_depth>, B<-verify_email>, B<-verify_hostname>,
B<-verify_ip>, B<-verify_name>, B<-x509_strict> B<-issuer_checks>

Set various options of certificate chain verification.
See L<openssl-verification-options(1)/Verification Options> for details.

=item B<-provider> I<name>

=item B<-provider-path> I<path>

=item B<-propquery> I<propq>

See L<openssl(1)/Provider Options>, L<provider(7)>, and L<property(7)>.

=item B<-->

Indicates the last option. All arguments following this are assumed to be
certificate files. This is useful if the first certificate filename begins
with a B<->.

=item I<certificate> ...

One or more target certificates to verify, one per file. If no certificates are
given, this command will attempt to read a single certificate from standard
input.

=back

=head1 DIAGNOSTICS

When a verify operation fails the output messages can be somewhat cryptic. The
general form of the error message is:

 server.pem: /C=AU/ST=Queensland/O=CryptSoft Pty Ltd/CN=Test CA (1024 bit)
 error 24 at 1 depth lookup:invalid CA certificate

The first line contains the name of the certificate being verified followed by
the subject name of the certificate. The second line contains the error number
and the depth. The depth is number of the certificate being verified when a
problem was detected starting with zero for the target ("leaf") certificate
itself then 1 for the CA that signed the target certificate and so on.
Finally a textual version of the error number is presented.

A list of the error codes and messages can be found in
L<X509_STORE_CTX_get_error(3)>; the full list is defined in the header file
F<< <openssl/x509_vfy.h> >>.

This command ignores many errors, in order to allow all the problems with a
certificate chain to be determined.

=head1 SEE ALSO

L<openssl-verification-options(1)>,
L<openssl-x509(1)>,
L<ossl_store-file(7)>

=head1 HISTORY

The B<-show_chain> option was added in OpenSSL 1.1.0.

The B<-engine option> was deprecated in OpenSSL 3.0.

=head1 COPYRIGHT

Copyright 2000-2021 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
