=pod

=head1 NAME

openssl-namedisplay-options - Distinguished name display options

=head1 SYNOPSIS

B<openssl>
I<command>
[ I<options> ... ]
[ I<parameters> ... ]

=head1 DESCRIPTION

OpenSSL provides fine-grain control over how the subject and issuer DN's are
displayed.
This is specified by using the B<-nameopt> option, which takes a
comma-separated list of options from the following set.
An option may be preceded by a minus sign, C<->, to turn it off.
The default value is C<oneline>.
The first four are the most commonly used.

=head1 OPTIONS

=head2 Name Format Option Arguments

The DN output format can be fine tuned with the following flags.

=over 4

=item B<compat>

Display the name using an old format from previous OpenSSL versions.

=item B<RFC2253>

Display the name using the format defined in RFC 2253.
It is equivalent to B<esc_2253>, B<esc_ctrl>, B<esc_msb>, B<utf8>,
B<dump_nostr>, B<dump_unknown>, B<dump_der>, B<sep_comma_plus>, B<dn_rev>
and B<sname>.

=item B<oneline>

Display the name in one line, using a format that is more readable
RFC 2253.
It is equivalent to B<esc_2253>, B<esc_ctrl>, B<esc_msb>, B<utf8>,
B<dump_nostr>, B<dump_der>, B<use_quote>, B<sep_comma_plus_space>,
B<space_eq> and B<sname> options.

=item B<multiline>

Display the name using multiple lines.
It is equivalent to B<esc_ctrl>, B<esc_msb>, B<sep_multiline>, B<space_eq>,
B<lname> and B<align>.

=item B<esc_2253>

Escape the "special" characters in a field, as required by RFC 2253.
That is, any of the characters C<,+"E<lt>E<gt>;>, C<#> at the beginning of
a string and leading or trailing spaces.

=item B<esc_2254>

Escape the "special" characters in a field as required by RFC 2254 in a field.
That is, the B<NUL> character and of C<()*>.

=item B<esc_ctrl>

Escape non-printable ASCII characters, codes less than 0x20 (space)
or greater than 0x7F (DELETE). They are displayed using RFC 2253 C<\XX>
notation where B<XX> are the two hex digits representing the character value.

=item B<esc_msb>

Escape any characters with the most significant bit set, that is with
values larger than 127, as described in B<esc_ctrl>.

=item B<use_quote>

Escapes some characters by surrounding the entire string with quotation
marks, C<">.
Without this option, individual special characters are preceded with
a backslash character, C<\>.

=item B<utf8>

Convert all strings to UTF-8 format first as required by RFC 2253.
If the output device is UTF-8 compatible, then using this option (and
not setting B<esc_msb>) may give the correct display of multibyte
characters.
If this option is not set, then multibyte characters larger than 0xFF
will be output as C<\UXXXX> for 16 bits or C<\WXXXXXXXX> for 32 bits.
In addition, any UTF8Strings will be converted to their character form first.

=item B<ignore_type>

This option does not attempt to interpret multibyte characters in any
way. That is, the content octets are merely dumped as though one octet
represents each character. This is useful for diagnostic purposes but
will result in rather odd looking output.

=item B<show_type>

Display the type of the ASN1 character string before the value,
such as C<BMPSTRING: Hello World>.

=item B<dump_der>

Any fields that would be output in hex format are displayed using
the DER encoding of the field.
If not set, just the content octets are displayed.
Either way, the B<#XXXX...> format of RFC 2253 is used.

=item B<dump_nostr>

Dump non-character strings, such as ASN.1 B<OCTET STRING>.
If this option is not set, then non character string types will be displayed
as though each content octet represents a single character.

=item B<dump_all>

Dump all fields. When this used with B<dump_der>, this allows the
DER encoding of the structure to be unambiguously determined.

=item B<dump_unknown>

Dump any field whose OID is not recognised by OpenSSL.

=item B<sep_comma_plus>, B<sep_comma_plus_space>, B<sep_semi_plus_space>,
B<sep_multiline>

Specify the field separators. The first word is used between the
Relative Distinguished Names (RDNs) and the second is between
multiple Attribute Value Assertions (AVAs). Multiple AVAs are
very rare and their use is discouraged.
The options ending in "space" additionally place a space after the separator to make it more readable.
The B<sep_multiline> starts each field on its own line, and uses "plus space"
for the AVA separator.
It also indents the fields by four characters.
The default value is B<sep_comma_plus_space>.

=item B<dn_rev>

Reverse the fields of the DN as required by RFC 2253.
This also reverses the order of multiple AVAs in a field, but this is
permissible as there is no ordering on values.

=item B<nofname>, B<sname>, B<lname>, B<oid>

Specify how the field name is displayed.
B<nofname> does not display the field at all.
B<sname> uses the "short name" form (CN for commonName for example).
B<lname> uses the long form.
B<oid> represents the OID in numerical form and is useful for
diagnostic purpose.

=item B<align>

Align field values for a more readable output. Only usable with
B<sep_multiline>.

=item B<space_eq>

Places spaces round the equal sign, C<=>, character which follows the field
name.

=back

=head1 COPYRIGHT

Copyright 2000-2020 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
