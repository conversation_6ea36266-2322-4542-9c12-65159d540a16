=pod
{- OpenSSL::safe::output_do_not_edit_headers(); -}

=head1 NAME

openssl-srp - maintain SRP password file

=head1 SYNOPSIS

B<openssl srp>
[B<-help>]
[B<-verbose>]
[B<-add>]
[B<-modify>]
[B<-delete>]
[B<-list>]
[B<-name> I<section>]
[B<-srpvfile> I<file>]
[B<-gn> I<identifier>]
[B<-userinfo> I<text>]
[B<-passin> I<arg>]
[B<-passout> I<arg>]
{- $OpenSSL::safe::opt_engine_synopsis -}{- $OpenSSL::safe::opt_r_synopsis -}
{- $OpenSSL::safe::opt_provider_synopsis -}
{- $OpenSSL::safe::opt_config_synopsis -}
[I<user> ...]

=head1 DESCRIPTION

This command is deprecated. It is used to maintain an SRP (secure remote
password) file. At most one of the B<-add>, B<-modify>, B<-delete>, and B<-list>
options can be specified.
These options take zero or more usernames as parameters and perform the
appropriate operation on the SRP file.
For B<-list>, if no I<user> is given then all users are displayed.

The configuration file to use, and the section within the file, can be
specified with the B<-config> and B<-name> flags, respectively.

=head1 OPTIONS

=over 4

=item B<-help>

Display an option summary.

=item B<-verbose>

Generate verbose output while processing.

=item B<-add>

Add a user and SRP verifier.

=item B<-modify>

Modify the SRP verifier of an existing user.

=item B<-delete>

Delete user from verifier file.

=item B<-list>

List users.

=item B<-name>

The particular SRP definition to use.

=item B<-srpvfile> I<file>

If the config file is not specified,
B<-srpvfile> can be used to specify the file to operate on.

=item B<-gn>

Specifies the B<g> and B<N> values, using one of
the strengths defined in IETF RFC 5054.

=item B<-userinfo>

specifies additional information to add when
adding or modifying a user.

=item B<-passin> I<arg>, B<-passout> I<arg>

The password source for the input and output file.
For more information about the format of B<arg>
see L<openssl-passphrase-options(1)>.

{- $OpenSSL::safe::opt_engine_item -}

{- $OpenSSL::safe::opt_r_item -}

{- $OpenSSL::safe::opt_provider_item -}

{- $OpenSSL::safe::opt_config_item -}

{- $OpenSSL::safe::opt_r_synopsis -}

=back

=head1 HISTORY

The B<-engine> option was deprecated in OpenSSL 3.0.

=head1 COPYRIGHT

Copyright 2017-2021 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
