=pod

=head1 NAME

PKCS12_gen_mac, PKCS12_setup_mac, PKCS12_set_mac,
PKCS12_verify_mac - Functions to create and manipulate a PKCS#12 structure

=head1 SYNOPSIS

 #include <openssl/pkcs12.h>

 int PKCS12_gen_mac(PKCS12 *p12, const char *pass, int passlen,
                    unsigned char *mac, unsigned int *maclen);
 int PKCS12_verify_mac(PKCS12 *p12, const char *pass, int passlen);
 int PKCS12_set_mac(PKCS12 *p12, const char *pass, int passlen,
                    unsigned char *salt, int saltlen, int iter,
                    const EVP_MD *md_type);
 int PKCS12_setup_mac(PKCS12 *p12, int iter, unsigned char *salt,
                      int saltlen, const EVP_MD *md_type);

=head1 DESCRIPTION

PKCS12_gen_mac() generates an HMAC over the entire PKCS#12 object using the
supplied password along with a set of already configured parameters.
The default key generation mechanism used is PKCS12KDF.

PKCS12_verify_mac() verifies the PKCS#12 object's HMAC using the supplied
password.

PKCS12_setup_mac() sets the MAC part of the PKCS#12 structure with the supplied
parameters.

PKCS12_set_mac() sets the MAC and MAC parameters into the PKCS#12 object.

I<pass> is the passphrase to use in the HMAC. I<salt> is the salt value to use,
I<iter> is the iteration count and I<md_type> is the message digest
function to use.

=head1 NOTES

If I<salt> is NULL then a suitable salt will be generated and used.

If I<iter> is 1 then an iteration count will be omitted from the PKCS#12
structure.

PKCS12_gen_mac(), PKCS12_verify_mac() and PKCS12_set_mac() make assumptions
regarding the encoding of the given passphrase. See L<passphrase-encoding(7)>
for more information.

=head1 RETURN VALUES

All functions return 1 on success and 0 if an error occurred.

=head1 CONFORMING TO

IETF RFC 7292 (L<https://tools.ietf.org/html/rfc7292>)

=head1 SEE ALSO

L<d2i_PKCS12(3)>,
L<EVP_KDF-PKCS12KDF(7)>,
L<PKCS12_create(3)>,
L<passphrase-encoding(7)>

=head1 COPYRIGHT

Copyright 2021-2023 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
