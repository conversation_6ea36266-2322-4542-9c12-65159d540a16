=pod

=head1 NAME

X509_check_issued - checks if certificate is apparently issued by another
certificate

=head1 SYNOPSIS

 #include <openssl/x509v3.h>

 int X509_check_issued(X509 *issuer, X509 *subject);


=head1 DESCRIPTION

X509_check_issued() checks if certificate I<subject> was apparently issued
using (CA) certificate I<issuer>. This function takes into account not only
matching of the issuer field of I<subject> with the subject field of I<issuer>,
but also compares all sub-fields of the B<authorityKeyIdentifier> extension of
I<subject>, as far as present, with the respective B<subjectKeyIdentifier>,
serial number, and issuer fields of I<issuer>, as far as present. It also checks
if the B<keyUsage> field (if present) of I<issuer> allows certificate signing.
It does not actually check the certificate signature. An error is returned
if the I<issuer> or the I<subject> are incomplete certificates.

=head1 RETURN VALUES

X509_check_issued() returns B<X509_V_OK> if all checks are successful
or some B<X509_V_ERR*> constant to indicate an error.

=head1 SEE ALSO

L<X509_verify_cert(3)>, L<X509_verify(3)>, L<X509_check_ca(3)>,
L<openssl-verify(1)>, L<X509_self_signed(3)>

=head1 COPYRIGHT

Copyright 2015-2021 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
