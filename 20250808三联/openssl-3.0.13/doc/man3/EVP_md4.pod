=pod

=head1 NAME

EVP_md4
- MD4 For EVP

=head1 SYNOPSIS

 #include <openssl/evp.h>

 const EVP_MD *EVP_md4(void);

=head1 DESCRIPTION

MD4 is a cryptographic hash function standardized in RFC 1320 and designed by
<PERSON>, first published in 1990. This implementation is only available
with the legacy provider.

=over 4

=item EVP_md4()

The MD4 algorithm which produces a 128-bit output from a given input.

=back

=head1 NOTES

Developers should be aware of the negative performance implications of
calling this function multiple times and should consider using
L<EVP_MD_fetch(3)> with L<EVP_MD-MD4(7)> instead.
See L<crypto(7)/Performance> for further information.

=head1 RETURN VALUES

These functions return a B<EVP_MD> structure that contains the
implementation of the message digest. See L<EVP_MD_meth_new(3)> for
details of the B<EVP_MD> structure.

=head1 CONFORMING TO

IETF RFC 1320.

=head1 SEE ALSO

L<evp(7)>,
L<provider(7)>,
L<EVP_DigestInit(3)>

=head1 COPYRIGHT

Copyright 2017-2023 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut

