=pod

=head1 NAME

SSL_set_bio, SSL_set0_rbio, SSL_set0_wbio - connect the SSL object with a BIO

=head1 SYNOPSIS

 #include <openssl/ssl.h>

 void SSL_set_bio(SSL *ssl, BIO *rbio, BIO *wbio);
 void SSL_set0_rbio(SSL *s, BIO *rbio);
 void SSL_set0_wbio(SSL *s, BIO *wbio);

=head1 DESCRIPTION

SSL_set0_rbio() connects the BIO B<rbio> for the read operations of the B<ssl>
object. The SSL engine inherits the behaviour of B<rbio>. If the BIO is
nonblocking then the B<ssl> object will also have nonblocking behaviour. This
function transfers ownership of B<rbio> to B<ssl>. It will be automatically
freed using L<BIO_free_all(3)> when the B<ssl> is freed. On calling this
function, any existing B<rbio> that was previously set will also be freed via a
call to L<BIO_free_all(3)> (this includes the case where the B<rbio> is set to
the same value as previously).

SSL_set0_wbio() works in the same as SSL_set0_rbio() except that it connects
the BIO B<wbio> for the write operations of the B<ssl> object. Note that if the
rbio and wbio are the same then SSL_set0_rbio() and SSL_set0_wbio() each take
ownership of one reference. Therefore, it may be necessary to increment the
number of references available using L<BIO_up_ref(3)> before calling the set0
functions.

SSL_set_bio() is similar to SSL_set0_rbio() and SSL_set0_wbio() except
that it connects both the B<rbio> and the B<wbio> at the same time, and
transfers the ownership of B<rbio> and B<wbio> to B<ssl> according to
the following set of rules:

=over 2

=item *

If neither the B<rbio> or B<wbio> have changed from their previous values
then nothing is done.

=item *

If the B<rbio> and B<wbio> parameters are different and both are different
to their
previously set values then one reference is consumed for the rbio and one
reference is consumed for the wbio.

=item *

If the B<rbio> and B<wbio> parameters are the same and the B<rbio> is not
the same as the previously set value then one reference is consumed.

=item *

If the B<rbio> and B<wbio> parameters are the same and the B<rbio> is the
same as the previously set value, then no additional references are consumed.

=item *

If the B<rbio> and B<wbio> parameters are different and the B<rbio> is the
same as the
previously set value then one reference is consumed for the B<wbio> and no
references are consumed for the B<rbio>.

=item *

If the B<rbio> and B<wbio> parameters are different and the B<wbio> is the
same as the previously set value and the old B<rbio> and B<wbio> values
were the same as each other then one reference is consumed for the B<rbio>
and no references are consumed for the B<wbio>.

=item *

If the B<rbio> and B<wbio> parameters are different and the B<wbio>
is the same as the
previously set value and the old B<rbio> and B<wbio> values were different
to each other, then one reference is consumed for the B<rbio> and one
reference is consumed for the B<wbio>.

=back

Because of this complexity, this function should be avoided;
use SSL_set0_rbio() and SSL_set0_wbio() instead.

=head1 RETURN VALUES

SSL_set_bio(), SSL_set0_rbio() and SSL_set0_wbio() cannot fail.

=head1 SEE ALSO

L<SSL_get_rbio(3)>,
L<SSL_connect(3)>, L<SSL_accept(3)>,
L<SSL_shutdown(3)>, L<ssl(7)>, L<bio(7)>

=head1 HISTORY

SSL_set0_rbio() and SSL_set0_wbio() were added in OpenSSL 1.1.0.

=head1 COPYRIGHT

Copyright 2000-2021 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
