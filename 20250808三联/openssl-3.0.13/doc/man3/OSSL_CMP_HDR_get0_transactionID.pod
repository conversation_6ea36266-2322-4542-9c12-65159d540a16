=pod

=head1 NAME

OSSL_CMP_HDR_get0_transactionID,
OSSL_CMP_HDR_get0_recipNonce
- functions manipulating CMP message headers

=head1 SYNOPSIS

  #include <openssl/cmp.h>

  ASN1_OCTET_STRING *OSSL_CMP_HDR_get0_transactionID(const
                                                     OSSL_CMP_PKIHEADER *hdr);
  ASN1_OCTET_STRING *OSSL_CMP_HDR_get0_recipNonce(const
                                                  OSSL_CMP_PKIHEADER *hdr);

=head1 DESCRIPTION

OSSL_CMP_HDR_get0_transactionID returns the transaction ID of the given
PKIHeader.

OSSL_CMP_HDR_get0_recipNonce returns the recipient nonce of the given PKIHeader.

=head1 NOTES

CMP is defined in RFC 4210.

=head1 RETURN VALUES

The functions return the intended pointer value as described above
or NULL if the respective entry does not exist and on error.

=head1 HISTORY

The OpenSSL CMP support was added in OpenSSL 3.0.

=head1 COPYRIGHT

Copyright 2007-2019 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
