=pod

=head1 NAME

TS_RESP_CTX_new_ex, TS_RESP_CTX_new,
TS_RESP_CTX_free - Timestamp response context object creation

=head1 SYNOPSIS

 #include <openssl/ts.h>

 TS_RESP_CTX *TS_RESP_CTX_new_ex(OSSL_LIB_CTX *libctx, const char *propq);
 TS_RESP_CTX *TS_RESP_CTX_new(void);
 void TS_RESP_CTX_free(TS_RESP_CTX *ctx);

=head1 DESCRIPTION

Creates a response context that can be used for generating responses.

TS_RESP_CTX_new_ex() allocates and initializes a TS_RESP_CTX structure with a
library context of I<libctx> and a property query of I<propq>.
The library context and property query can be used to select which providers
supply the fetched algorithms.

TS_RESP_CTX_new() is similar to TS_RESP_CTX_new_ex() but sets the library context
and property query to NULL. This results in the default (NULL) library context
being used for any operations requiring algorithm fetches.

TS_RESP_CTX_free() frees the B<TS_RESP_CTX> object I<ctx>.

=head1 RETURN VALUES

If the allocation fails, TS_RESP_CTX_new_ex() and TS_RESP_CTX_new() return NULL,
otherwise it returns a pointer to the newly allocated structure.

=head1 HISTORY

The function TS_RESP_CTX_new_ex() was added in OpenSSL 3.0.

=head1 COPYRIGHT

Copyright 2021 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
