=pod

=head1 NAME

EVP_rc2_cbc,
E<PERSON>_rc2_cfb,
<PERSON><PERSON>_rc2_cfb64,
<PERSON><PERSON>_rc2_ecb,
EVP_rc2_ofb,
EVP_rc2_40_cbc,
EVP_rc2_64_cbc
- EVP RC2 cipher

=head1 SYNOPSIS

 #include <openssl/evp.h>

 const EVP_CIPHER *EVP_rc2_cbc(void);
 const EVP_CIPHER *EVP_rc2_cfb(void);
 const EVP_CIPHER *EVP_rc2_cfb64(void);
 const EVP_CIPHER *EVP_rc2_ecb(void);
 const EVP_CIPHER *EVP_rc2_ofb(void);
 const EVP_CIPHER *EVP_rc2_40_cbc(void);
 const EVP_CIPHER *EVP_rc2_64_cbc(void);

=head1 DESCRIPTION

The RC2 encryption algorithm for EVP.

=over 4

=item EVP_rc2_cbc(),
EVP_rc2_cfb(),
EVP_rc2_cfb64(),
EVP_rc2_ecb(),
EVP_rc2_ofb()

RC2 encryption algorithm in CBC, CFB, ECB and OFB modes respectively. This is a
variable key length cipher with an additional parameter called "effective key
bits" or "effective key length". By default both are set to 128 bits.

=item EVP_rc2_40_cbc(),
EVP_rc2_64_cbc()

RC2 algorithm in CBC mode with a default key length and effective key length of
40 and 64 bits.

WARNING: these functions are obsolete. Their usage should be replaced with the
EVP_rc2_cbc(), EVP_CIPHER_CTX_set_key_length() and EVP_CIPHER_CTX_ctrl()
functions to set the key length and effective key length.

=back

=head1 NOTES

Developers should be aware of the negative performance implications of
calling these functions multiple times and should consider using
L<EVP_CIPHER_fetch(3)> with L<EVP_CIPHER-RC2(7)> instead.
See L<crypto(7)/Performance> for further information.

=head1 RETURN VALUES

These functions return an B<EVP_CIPHER> structure that contains the
implementation of the symmetric cipher. See L<EVP_CIPHER_meth_new(3)> for
details of the B<EVP_CIPHER> structure.

=head1 SEE ALSO

L<evp(7)>,
L<EVP_EncryptInit(3)>,
L<EVP_CIPHER_meth_new(3)>

=head1 COPYRIGHT

Copyright 2017-2023 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut

