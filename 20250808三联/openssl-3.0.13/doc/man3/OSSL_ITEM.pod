=pod

=head1 NAME

OSSL_ITEM - OpenSSL Core type for generic itemized data

=head1 SYNOPSIS

 #include <openssl/core.h>

 typedef struct ossl_item_st OSSL_ITEM;
 struct ossl_item_st {
     unsigned int id;
     void *ptr;
 };

=head1 DESCRIPTION

This type is a tuple of integer and pointer.
It's a generic type used as a generic descriptor, its exact meaning
being defined by how it's used.
Arrays of this type are passed between the OpenSSL libraries and the
providers, and must be terminated with a tuple where the integer is
zero and the pointer NULL.

This is currently mainly used for the return value of the provider's error
reason strings array, see L<provider-base(7)/Provider Functions>.

=begin comment RETURN VALUES doesn't make sense for a manual that only
describes a type, but document checkers still want that section, and
to have more than just the section title.

=head1 RETURN VALUES

txt

=end comment

=head1 SEE ALSO

L<crypto(7)>, L<provider-base(7)>, L<openssl-core.h(7)>

=head1 HISTORY

B<OSSL_ITEM> was added in OpenSSL 3.0

=head1 COPYRIGHT

Copyright 2022 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
