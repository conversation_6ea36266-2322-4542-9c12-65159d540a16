=pod

=head1 NAME

EVP_PKEY_get_attr,
EVP_PKEY_get_attr_count,
EVP_PKEY_get_attr_by_NID, EVP_PKEY_get_attr_by_OBJ,
E<PERSON>_PKEY_delete_attr,
EVP_PKEY_add1_attr,
EVP_PKEY_add1_attr_by_OBJ, EVP_PKEY_add1_attr_by_NID, EVP_PKEY_add1_attr_by_txt
- EVP_PKEY B<X509_ATTRIBUTE> functions

=head1 SYNOPSIS

 #include <openssl/x509.h>

 int EVP_PKEY_get_attr_count(const EVP_PKEY *key);
 int EVP_PKEY_get_attr_by_NID(const EVP_PKEY *key, int nid, int lastpos);
 int EVP_PKEY_get_attr_by_OBJ(const EVP_PKEY *key, const ASN1_OBJECT *obj,
                              int lastpos);
 X509_ATTRIBUTE *EVP_PKEY_get_attr(const EVP_PKEY *key, int loc);
 X509_ATTRIBUTE *EVP_PKEY_delete_attr(EVP_PKEY *key, int loc);
 int EVP_PKEY_add1_attr(EVP_PKEY *key, X509_ATTRIBUTE *attr);
 int EVP_PKEY_add1_attr_by_OBJ(EVP_PKEY *key,
                               const ASN1_OBJECT *obj, int type,
                               const unsigned char *bytes, int len);
 int EVP_PKEY_add1_attr_by_NID(EVP_PKEY *key,
                               int nid, int type,
                               const unsigned char *bytes, int len);
 int EVP_PKEY_add1_attr_by_txt(EVP_PKEY *key,
                               const char *attrname, int type,
                               const unsigned char *bytes, int len);

=head1 DESCRIPTION

These functions are used by B<PKCS12>.

EVP_PKEY_get_attr_by_OBJ() finds the location of the first matching object I<obj>
in the I<key> attribute list. The search starts at the position after I<lastpos>.
If the returned value is positive then it can be used on the next call to
EVP_PKEY_get_attr_by_OBJ() as the value of I<lastpos> in order to iterate through
the remaining attributes. I<lastpos> can be set to any negative value on the
first call, in order to start searching from the start of the attribute list.

EVP_PKEY_get_attr_by_NID() is similar to EVP_PKEY_get_attr_by_OBJ() except that
it passes the numerical identifier (NID) I<nid> associated with the object.
See <openssl/obj_mac.h> for a list of NID_*.

EVP_PKEY_get_attr() returns the B<X509_ATTRIBUTE> object at index I<loc> in the
I<key> attribute list. I<loc> should be in the range from 0 to
EVP_PKEY_get_attr_count() - 1.

EVP_PKEY_delete_attr() removes the B<X509_ATTRIBUTE> object at index I<loc> in
the I<key> attribute list.

EVP_PKEY_add1_attr() pushes a copy of the passed in B<X509_ATTRIBUTE> object
to the I<key> attribute list. A new I<key> attribute list is created if required.
An error occurs if either I<attr> is NULL, or the attribute already exists.

EVP_PKEY_add1_attr_by_OBJ() creates a new B<X509_ATTRIBUTE> using
X509_ATTRIBUTE_set1_object() and X509_ATTRIBUTE_set1_data() to assign a new
I<obj> with type I<type> and data I<bytes> of length I<len> and then pushes it
to the I<key> object's attribute list. If I<obj> already exists in the attribute
list then an error occurs.

EVP_PKEY_add1_attr_by_NID() is similar to EVP_PKEY_add1_attr_by_OBJ() except
that it passes the numerical identifier (NID) I<nid> associated with the object.
See <openssl/obj_mac.h> for a list of NID_*.

EVP_PKEY_add1_attr_by_txt() is similar to EVP_PKEY_add1_attr_by_OBJ() except
that it passes a name I<attrname> associated with the object.
See <openssl/obj_mac.h> for a list of SN_* names.

=head1 RETURN VALUES

EVP_PKEY_get_attr_count() returns the number of attributes in the I<key> object
attribute list or -1 if the attribute list is NULL.

EVP_PKEY_get_attr_by_OBJ() returns -1 if either the list is empty OR the object
is not found, otherwise it returns the location of the object in the list.

EVP_PKEY_get_attr_by_NID() is similar to EVP_PKEY_get_attr_by_OBJ(), except that
it returns -2 if the I<nid> is not known by OpenSSL.

EVP_PKEY_get_attr() returns either a B<X509_ATTRIBUTE> or NULL if there is a
error.

EVP_PKEY_delete_attr() returns either the removed B<X509_ATTRIBUTE> or NULL if
there is a error.

EVP_PKEY_add1_attr(), EVP_PKEY_add1_attr_by_OBJ(), EVP_PKEY_add1_attr_by_NID()
and EVP_PKEY_add1_attr_by_txt() return 1 on success or 0 otherwise.

=head1 NOTES

A B<EVP_PKEY> object's attribute list is initially NULL. All the above functions
listed will return an error unless EVP_PKEY_add1_attr() is called.
All functions listed assume that the I<key> is not NULL.

=head1 SEE ALSO

L<X509_ATTRIBUTE(3)>

=head1 COPYRIGHT

Copyright 2023-2024 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
