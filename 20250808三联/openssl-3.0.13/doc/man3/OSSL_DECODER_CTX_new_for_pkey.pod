=pod

=head1 NAME

OSSL_DECODER_CTX_new_for_pkey,
OSSL_DECODER_CTX_set_passphrase,
OSSL_DECODER_CTX_set_pem_password_cb,
OSSL_DECODER_CTX_set_passphrase_ui,
OSSL_DECODER_CTX_set_passphrase_cb
- Decoder routines to decode EVP_PKEYs

=head1 SYNOPSIS

 #include <openssl/decoder.h>

 OSSL_DECODER_CTX *
 OSSL_DECODER_CTX_new_for_pkey(EVP_PKEY **pkey,
                               const char *input_type,
                               const char *input_struct,
                               const char *keytype, int selection,
                               OSSL_LIB_CTX *libctx, const char *propquery);

 int OSSL_DECODER_CTX_set_passphrase(OSSL_DECODER_CTX *ctx,
                                     const unsigned char *kstr,
                                     size_t klen);
 int OSSL_DECODER_CTX_set_pem_password_cb(OSSL_DECODER_CTX *ctx,
                                          pem_password_cb *cb,
                                          void *cbarg);
 int OSSL_DECODER_CTX_set_passphrase_ui(OSSL_DECODER_CTX *ctx,
                                        const UI_METHOD *ui_method,
                                        void *ui_data);
 int OSSL_DECODER_CTX_set_passphrase_cb(OSSL_DECODER_CTX *ctx,
                                        OSSL_PASSPHRASE_CALLBACK *cb,
                                        void *cbarg);

=head1 DESCRIPTION

OSSL_DECODER_CTX_new_for_pkey() is a utility function that creates a
B<OSSL_DECODER_CTX>, finds all applicable decoder implementations and sets
them up, so all the caller has to do next is call functions like
L<OSSL_DECODER_from_bio(3)>.  The caller may use the optional I<input_type>,
I<input_struct>, I<keytype> and I<selection> to specify what the input is
expected to contain.  The I<pkey> must reference an B<EVP_PKEY *> variable
that will be set to the newly created B<EVP_PKEY> on successful decoding.
The referenced variable must be initialized to NULL before calling the
function.

Internally OSSL_DECODER_CTX_new_for_pkey() searches for all available
L<EVP_KEYMGMT(3)> implementations, and then builds a list of all potential
decoder implementations that may be able to process the encoded input into
data suitable for B<EVP_PKEY>s.  All these implementations are implicitly
fetched using I<libctx> and I<propquery>.

The search of decoder implementations can be limited with I<input_type> and
I<input_struct> which specifies a starting input type and input structure.
NULL is valid for both of them and signifies that the decoder implementations
will find out the input type on their own.
They are set with L<OSSL_DECODER_CTX_set_input_type(3)> and
L<OSSL_DECODER_CTX_set_input_structure(3)>.
See L</Input Types> and L</Input Structures> below for further information.

The search of decoder implementations can also be limited with I<keytype>
and I<selection>, which specifies the expected resulting keytype and contents.
NULL and zero are valid and signify that the decoder implementations will
find out the keytype and key contents on their own from the input they get.

If no suitable decoder implementation is found,
OSSL_DECODER_CTX_new_for_pkey() still creates a B<OSSL_DECODER_CTX>, but
with no associated decoder (L<OSSL_DECODER_CTX_get_num_decoders(3)> returns
zero).  This helps the caller to distinguish between an error when creating
the B<OSSL_ENCODER_CTX> and missing encoder implementation, and allows it to
act accordingly.

OSSL_DECODER_CTX_set_passphrase() gives the implementation a pass phrase to
use when decrypting the encoded private key. Alternatively, a pass phrase
callback may be specified with the following functions.

OSSL_DECODER_CTX_set_pem_password_cb(), OSSL_DECODER_CTX_set_passphrase_ui()
and OSSL_DECODER_CTX_set_passphrase_cb() set up a callback method that the
implementation can use to prompt for a pass phrase, giving the caller the
choice of preferred pass phrase callback form.  These are called indirectly,
through an internal L<OSSL_PASSPHRASE_CALLBACK(3)> function.

The internal L<OSSL_PASSPHRASE_CALLBACK(3)> function caches the pass phrase, to
be re-used in all decodings that are performed in the same decoding run (for
example, within one L<OSSL_DECODER_from_bio(3)> call).

=head2 Input Types

Available input types depend on the implementations that available providers
offer, and provider documentation should have the details.

Among the known input types that OpenSSL decoder implementations offer
for B<EVP_PKEY>s are C<DER>, C<PEM>, C<MSBLOB> and C<PVK>.
See L<openssl-glossary(7)> for further information on what these input
types mean.

=head2 Input Structures

Available input structures depend on the implementations that available
providers offer, and provider documentation should have the details.

Among the known input structures that OpenSSL decoder implementations
offer for B<EVP_PKEY>s are C<pkcs8> and C<SubjectPublicKeyInfo>.

OpenSSL decoder implementations also support the input structure
C<type-specific>.  This is the structure used for keys encoded
according to key type specific specifications.  For example, RSA keys
encoded according to PKCS#1.

=head2 Selections

I<selection> can be any one of the values described in
L<EVP_PKEY_fromdata(3)/Selections>.
Additionally I<selection> can also be set to B<0> to indicate that the code will
auto detect the selection.

=head1 RETURN VALUES

OSSL_DECODER_CTX_new_for_pkey() returns a pointer to a
B<OSSL_DECODER_CTX>, or NULL if it couldn't be created.

OSSL_DECODER_CTX_set_passphrase(), OSSL_DECODER_CTX_set_pem_password_cb(),
OSSL_DECODER_CTX_set_passphrase_ui() and
OSSL_DECODER_CTX_set_passphrase_cb() all return 1 on success, or 0 on
failure.

=head1 SEE ALSO

L<provider(7)>, L<OSSL_DECODER(3)>, L<OSSL_DECODER_CTX(3)>

=head1 HISTORY

The functions described here were added in OpenSSL 3.0.

=head1 COPYRIGHT

Copyright 2020-2023 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
