=pod

=head1 NAME

EVP_des_cbc,
<PERSON><PERSON>_des_cfb,
<PERSON><PERSON>_des_cfb1,
<PERSON><PERSON>_des_cfb8,
<PERSON><PERSON>_des_cfb64,
<PERSON><PERSON>_des_ecb,
<PERSON><PERSON>_des_ofb,
<PERSON><PERSON>_des_ede,
<PERSON><PERSON>_des_ede_cbc,
<PERSON><PERSON>_des_ede_cfb,
<PERSON><PERSON>_des_ede_cfb64,
E<PERSON>_des_ede_ecb,
<PERSON><PERSON>_des_ede_ofb,
EVP_des_ede3,
<PERSON><PERSON>_des_ede3_cbc,
<PERSON>VP_des_ede3_cfb,
<PERSON><PERSON>_des_ede3_cfb1,
EVP_des_ede3_cfb8,
<PERSON><PERSON>_des_ede3_cfb64,
<PERSON>VP_des_ede3_ecb,
<PERSON><PERSON>_des_ede3_ofb,
EVP_des_ede3_wrap
- EVP DES cipher

=head1 SYNOPSIS

=for openssl generic

 #include <openssl/evp.h>

 const EVP_CIPHER *EVP_ciphername(void)

I<EVP_ciphername> is used a placeholder for any of the described cipher
functions, such as I<EVP_des_cbc>.

=head1 DESCRIPTION

The DES encryption algorithm for EVP.

=over 4

=item EVP_des_cbc(),
EVP_des_ecb(),
EVP_des_cfb(),
EVP_des_cfb1(),
EVP_des_cfb8(),
EVP_des_cfb64(),
EVP_des_ofb()

DES in CBC, ECB, CFB with 64-bit shift, CFB with 1-bit shift, CFB with 8-bit
shift and OFB modes.

None of these algorithms are provided by the OpenSSL default provider.
To use them it is necessary to load either the OpenSSL legacy provider or another
implementation.

=item EVP_des_ede(),
EVP_des_ede_cbc(),
EVP_des_ede_cfb(),
EVP_des_ede_cfb64(),
EVP_des_ede_ecb(),
EVP_des_ede_ofb()

Two key triple DES in ECB, CBC, CFB with 64-bit shift and OFB modes.

=item EVP_des_ede3(),
EVP_des_ede3_cbc(),
EVP_des_ede3_cfb(),
EVP_des_ede3_cfb1(),
EVP_des_ede3_cfb8(),
EVP_des_ede3_cfb64(),
EVP_des_ede3_ecb(),
EVP_des_ede3_ofb()

Three-key triple DES in ECB, CBC, CFB with 64-bit shift, CFB with 1-bit shift,
CFB with 8-bit shift and OFB modes.

=item EVP_des_ede3_wrap()

Triple-DES key wrap according to RFC 3217 Section 3.

=back

=head1 NOTES

Developers should be aware of the negative performance implications of
calling these functions multiple times and should consider using
L<EVP_CIPHER_fetch(3)> with L<EVP_CIPHER-DES(7)> instead.
See L<crypto(7)/Performance> for further information.

=head1 RETURN VALUES

These functions return an B<EVP_CIPHER> structure that contains the
implementation of the symmetric cipher. See L<EVP_CIPHER_meth_new(3)> for
details of the B<EVP_CIPHER> structure.

=head1 SEE ALSO

L<evp(7)>,
L<EVP_EncryptInit(3)>,
L<EVP_CIPHER_meth_new(3)>

=head1 COPYRIGHT

Copyright 2017-2023 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut

