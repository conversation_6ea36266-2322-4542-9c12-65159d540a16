=pod

=head1 NAME

EVP_camellia_128_cbc,
<PERSON><PERSON>_camellia_192_cbc,
<PERSON><PERSON>_camellia_256_cbc,
<PERSON><PERSON>_camellia_128_cfb,
<PERSON><PERSON>_camellia_192_cfb,
<PERSON><PERSON>_camellia_256_cfb,
<PERSON><PERSON>_camellia_128_cfb1,
<PERSON><PERSON>_camellia_192_cfb1,
<PERSON><PERSON>_camellia_256_cfb1,
<PERSON><PERSON>_camellia_128_cfb8,
<PERSON><PERSON>_camellia_192_cfb8,
<PERSON><PERSON>_camellia_256_cfb8,
<PERSON><PERSON>_camellia_128_cfb128,
<PERSON><PERSON>_camellia_192_cfb128,
<PERSON><PERSON>_camellia_256_cfb128,
<PERSON><PERSON>_camellia_128_ctr,
<PERSON><PERSON>_camellia_192_ctr,
<PERSON><PERSON>_camellia_256_ctr,
<PERSON><PERSON>_camellia_128_ecb,
<PERSON><PERSON>_camellia_192_ecb,
EVP_camellia_256_ecb,
<PERSON><PERSON>_camellia_128_ofb,
<PERSON><PERSON>_camellia_192_ofb,
<PERSON>VP_camellia_256_ofb
- EVP Camellia cipher

=head1 SYNOPSIS

=for openssl generic

 #include <openssl/evp.h>

 const EVP_CIPHER *EVP_ciphername(void)

I<EVP_ciphername> is used a placeholder for any of the described cipher
functions, such as I<EVP_camellia_128_cbc>.

=head1 DESCRIPTION

The Camellia encryption algorithm for EVP.

=over 4

=item EVP_camellia_128_cbc(),
EVP_camellia_192_cbc(),
EVP_camellia_256_cbc(),
EVP_camellia_128_cfb(),
EVP_camellia_192_cfb(),
EVP_camellia_256_cfb(),
EVP_camellia_128_cfb1(),
EVP_camellia_192_cfb1(),
EVP_camellia_256_cfb1(),
EVP_camellia_128_cfb8(),
EVP_camellia_192_cfb8(),
EVP_camellia_256_cfb8(),
EVP_camellia_128_cfb128(),
EVP_camellia_192_cfb128(),
EVP_camellia_256_cfb128(),
EVP_camellia_128_ctr(),
EVP_camellia_192_ctr(),
EVP_camellia_256_ctr(),
EVP_camellia_128_ecb(),
EVP_camellia_192_ecb(),
EVP_camellia_256_ecb(),
EVP_camellia_128_ofb(),
EVP_camellia_192_ofb(),
EVP_camellia_256_ofb()

Camellia for 128, 192 and 256 bit keys in the following modes: CBC, CFB with
128-bit shift, CFB with 1-bit shift, CFB with 8-bit shift, CTR, ECB and OFB.

=back

=head1 NOTES

Developers should be aware of the negative performance implications of
calling these functions multiple times and should consider using
L<EVP_CIPHER_fetch(3)> with L<EVP_CIPHER-CAMELLIA(7)> instead.
See L<crypto(7)/Performance> for further information.

=head1 RETURN VALUES

These functions return an B<EVP_CIPHER> structure that contains the
implementation of the symmetric cipher. See L<EVP_CIPHER_meth_new(3)> for
details of the B<EVP_CIPHER> structure.

=head1 SEE ALSO

L<evp(7)>,
L<EVP_EncryptInit(3)>,
L<EVP_CIPHER_meth_new(3)>

=head1 COPYRIGHT

Copyright 2017-2023 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut

