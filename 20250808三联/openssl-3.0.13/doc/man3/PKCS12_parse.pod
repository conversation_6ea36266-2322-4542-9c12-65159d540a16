=pod

=head1 NAME

PKCS12_parse - parse a PKCS#12 structure

=head1 SYNOPSIS

 #include <openssl/pkcs12.h>

 int PKCS12_parse(PKCS12 *p12, const char *pass, EVP_PKEY **pkey, X509 **cert,
                  STACK_OF(X509) **ca);

=head1 DESCRIPTION

PKCS12_parse() parses a PKCS12 structure.

B<p12> is the B<PKCS12> structure to parse. B<pass> is the passphrase to use.
If successful the private key will be written to B<*pkey>, the corresponding
certificate to B<*cert> and any additional certificates to B<*ca>.

=head1 NOTES

Each of the parameters B<pkey>, B<cert>, and B<ca> can be NULL in which case
the private key, the corresponding certificate, or the additional certificates,
respectively, will be discarded.
If any of B<pkey> and B<cert> is non-NULL the variable it points to is
initialized.
If B<ca> is non-NULL and B<*ca> is NULL a new STACK will be allocated.
If B<ca> is non-NULL and B<*ca> is a valid STACK
then additional certificates are appended in the given order to B<*ca>.

The B<friendlyName> and B<localKeyID> attributes (if present) on each
certificate will be stored in the B<alias> and B<keyid> attributes of the
B<X509> structure.

The parameter B<pass> is interpreted as a string in the UTF-8 encoding. If it
is not valid UTF-8, then it is assumed to be ISO8859-1 instead.

In particular, this means that passwords in the locale character set
(or code page on Windows) must potentially be converted to UTF-8 before
use. This may include passwords from local text files, or input from
the terminal or command line. Refer to the documentation of
L<UI_OpenSSL(3)>, for example.

=head1 RETURN VALUES

PKCS12_parse() returns 1 for success and zero if an error occurred.

The error can be obtained from L<ERR_get_error(3)>

=head1 BUGS

Only a single private key and corresponding certificate is returned by this
function. More complex PKCS#12 files with multiple private keys will only
return the first match.

Only B<friendlyName> and B<localKeyID> attributes are currently stored in
certificates. Other attributes are discarded.

Attributes currently cannot be stored in the private key B<EVP_PKEY> structure.

=head1 SEE ALSO

L<d2i_PKCS12(3)>,
L<passphrase-encoding(7)>

=head1 COPYRIGHT

Copyright 2002-2020 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
