=pod

=head1 NAME

DH_new_by_nid, DH_get_nid - create or get DH named parameters

=head1 SYNOPSIS

 #include <openssl/dh.h>

The following functions have been deprecated since OpenSSL 3.0, and can be
hidden entirely by defining B<OPENSSL_API_COMPAT> with a suitable version value,
see L<openssl_user_macros(7)>:

 DH *DH_new_by_nid(int nid);

 int DH_get_nid(const DH *dh);

=head1 DESCRIPTION

DH_new_by_nid() creates and returns a DH structure containing named parameters
B<nid>. Currently B<nid> must be B<NID_ffdhe2048>, B<NID_ffdhe3072>,
B<NID_ffdhe4096>, B<NID_ffdhe6144>, B<NID_ffdhe8192>,
B<NID_modp_1536>, B<NID_modp_2048>, B<NID_modp_3072>,
B<NID_modp_4096>, B<NID_modp_6144> or B<NID_modp_8192>.

DH_get_nid() determines if the parameters contained in B<dh> match
any named safe prime group. It returns the NID corresponding to the matching
parameters or B<NID_undef> if there is no match.
This function is deprecated.

=head1 RETURN VALUES

DH_new_by_nid() returns a set of DH parameters or B<NULL> if an error occurred.

DH_get_nid() returns the NID of the matching set of parameters for p and g
and optionally q, otherwise it returns B<NID_undef> if there is no match.

=head1 HISTORY

All of these functions were deprecated in OpenSSL 3.0.

=head1 COPYRIGHT

Copyright 2017-2022 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
