=pod

=head1 NAME

SSL_set_fd, SSL_set_rfd, SSL_set_wfd - connect the SSL object with a file descriptor

=head1 SYNOPSIS

 #include <openssl/ssl.h>

 int SSL_set_fd(SSL *ssl, int fd);
 int SSL_set_rfd(SSL *ssl, int fd);
 int SSL_set_wfd(SSL *ssl, int fd);

=head1 DESCRIPTION

SSL_set_fd() sets the file descriptor B<fd> as the input/output facility
for the TLS/SSL (encrypted) side of B<ssl>. B<fd> will typically be the
socket file descriptor of a network connection.

When performing the operation, a B<socket BIO> is automatically created to
interface between the B<ssl> and B<fd>. The BIO and hence the SSL engine
inherit the behaviour of B<fd>. If B<fd> is nonblocking, the B<ssl> will
also have nonblocking behaviour.

If there was already a BIO connected to B<ssl>, BIO_free() will be called
(for both the reading and writing side, if different).

SSL_set_rfd() and SSL_set_wfd() perform the respective action, but only
for the read channel or the write channel, which can be set independently.

=head1 RETURN VALUES

The following return values can occur:

=over 4

=item Z<>0

The operation failed. Check the error stack to find out why.

=item Z<>1

The operation succeeded.

=back

=head1 NOTES

On Windows, a socket handle is a 64-bit data type (UINT_PTR), which leads to a
compiler warning (conversion from 'SOCKET' to 'int', possible loss of data) when
passing the socket handle to SSL_set_*fd(). For the time being, this warning can
safely be ignored, because although the Microsoft documentation claims that the
upper limit is INVALID_SOCKET-1 (2^64 - 2), in practice the current socket()
implementation returns an index into the kernel handle table, the size of which
is limited to 2^24.


=head1 SEE ALSO

L<SSL_get_fd(3)>, L<SSL_set_bio(3)>,
L<SSL_connect(3)>, L<SSL_accept(3)>,
L<SSL_shutdown(3)>, L<ssl(7)> , L<bio(7)>

=head1 COPYRIGHT

Copyright 2000-2021 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
