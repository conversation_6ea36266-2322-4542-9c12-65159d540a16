=pod

=head1 NAME

SSL_SESSION_get_protocol_version,
SSL_SESSION_set_protocol_version
- get and set the session protocol version

=head1 SYNOPSIS

 #include <openssl/ssl.h>

 int SSL_SESSION_get_protocol_version(const SSL_SESSION *s);
 int SSL_SESSION_set_protocol_version(SSL_SESSION *s, int version);

=head1 DESCRIPTION

SSL_SESSION_get_protocol_version() returns the protocol version number used
by session B<s>.

SSL_SESSION_set_protocol_version() sets the protocol version associated with the
SSL_SESSION object B<s> to the value B<version>. This value should be a version
constant such as B<TLS1_3_VERSION> etc. For example, this could be used to set
up a session based PSK (see L<SSL_CTX_set_psk_use_session_callback(3)>).

=head1 RETURN VALUES

SSL_SESSION_get_protocol_version() returns a number indicating the protocol
version used for the session; this number matches the constants I<e.g.>
B<TLS1_VERSION>, B<TLS1_2_VERSION> or B<TLS1_3_VERSION>.

Note that the SSL_SESSION_get_protocol_version() function
does B<not> perform a null check on the provided session B<s> pointer.

SSL_SESSION_set_protocol_version() returns 1 on success or 0 on failure.

=head1 SEE ALSO

L<ssl(7)>,
L<SSL_CTX_set_psk_use_session_callback(3)>

=head1 HISTORY

The SSL_SESSION_get_protocol_version() function was added in OpenSSL 1.1.0.
The SSL_SESSION_set_protocol_version() function was added in OpenSSL 1.1.1.

=head1 COPYRIGHT

Copyright 2001-2018 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
