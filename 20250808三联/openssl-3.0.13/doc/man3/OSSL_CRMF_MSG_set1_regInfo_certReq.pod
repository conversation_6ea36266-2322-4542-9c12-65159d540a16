=pod

=head1 NAME

OSSL_CRMF_MSG_get0_regInfo_utf8Pairs,
OSSL_CRMF_MSG_set1_regInfo_utf8Pairs,
OSSL_CRMF_MSG_get0_regInfo_certReq,
OSSL_CRMF_MSG_set1_regInfo_certReq
- functions getting or setting CRMF Registration Info

=head1 SYNOPSIS

 #include <openssl/crmf.h>

 ASN1_UTF8STRING
     *OSSL_CRMF_MSG_get0_regInfo_utf8Pairs(const OSSL_CRMF_MSG *msg);
 int OSSL_CRMF_MSG_set1_regInfo_utf8Pairs(OSSL_CRMF_MSG *msg,
                                          const ASN1_UTF8STRING *utf8pairs);
 OSSL_CRMF_CERTREQUEST
     *OSSL_CRMF_MSG_get0_regInfo_certReq(const OSSL_CRMF_MSG *msg);
 int OSSL_CRMF_MSG_set1_regInfo_certReq(OSSL_CRMF_MSG *msg,
                                        const OSSL_CRMF_CERTREQUEST *cr);

=head1 DESCRIPTION

OSSL_CRMF_MSG_get0_regInfo_utf8Pairs() returns the first utf8Pairs regInfo
in the given I<msg>, if present.

OSSL_CRMF_MSG_set1_regInfo_utf8Pairs() adds a copy of the given I<utf8pairs>
value as utf8Pairs regInfo to the given I<msg>. See RFC 4211 section 7.1.

OSSL_CRMF_MSG_get0_regInfo_certReq() returns the first certReq regInfo
in the given I<msg>, if present.

OSSL_CRMF_MSG_set1_regInfo_certReq() adds a copy of the given I<cr> value
as certReq regInfo to the given I<msg>. See RFC 4211 section 7.2.

=head1 RETURN VALUES

All get0_*() functions return the respective pointer value, NULL if not present.

All set1_*() functions return 1 on success, 0 on error.

=head1 NOTES

Calling the set1_*() functions multiple times
adds multiple instances of the respective
control to the regInfo structure of the given I<msg>. While RFC 4211 expects
multiple utf8Pairs in one regInfo structure, it does not allow multiple certReq.

=head1 SEE ALSO

RFC 4211

=head1 HISTORY

The OpenSSL CRMF support was added in OpenSSL 3.0.

=head1 COPYRIGHT

Copyright 2007-2021 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
