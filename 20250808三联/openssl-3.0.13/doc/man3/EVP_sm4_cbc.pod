=pod

=head1 NAME

EVP_sm4_cbc,
<PERSON><PERSON>_sm4_ecb,
<PERSON><PERSON>_sm4_cfb,
<PERSON>VP_sm4_cfb128,
EVP_sm4_ofb,
EVP_sm4_ctr
- EVP SM4 cipher

=head1 SYNOPSIS

 #include <openssl/evp.h>

 const EVP_CIPHER *EVP_sm4_cbc(void);
 const EVP_CIPHER *EVP_sm4_ecb(void);
 const EVP_CIPHER *EVP_sm4_cfb(void);
 const EVP_CIPHER *EVP_sm4_cfb128(void);
 const EVP_CIPHER *EVP_sm4_ofb(void);
 const EVP_CIPHER *EVP_sm4_ctr(void);

=head1 DESCRIPTION

The SM4 blockcipher (GB/T 32907-2016) for EVP.

All modes below use a key length of 128 bits and acts on blocks of 128 bits.

=over 4

=item EVP_sm4_cbc(),
EVP_sm4_ecb(),
<PERSON><PERSON>_sm4_cfb(),
E<PERSON>_sm4_cfb128(),
EVP_sm4_ofb(),
EVP_sm4_ctr()

The SM4 blockcipher with a 128-bit key in CBC, ECB, CFB, OFB and CTR modes
respectively.

=back

=head1 NOTES

Developers should be aware of the negative performance implications of
calling these functions multiple times and should consider using
L<EVP_CIPHER_fetch(3)> with L<EVP_CIPHER-SM4(7)> instead.
See L<crypto(7)/Performance> for further information.

=head1 RETURN VALUES

These functions return a B<EVP_CIPHER> structure that contains the
implementation of the symmetric cipher. See L<EVP_CIPHER_meth_new(3)> for
details of the B<EVP_CIPHER> structure.

=head1 SEE ALSO

L<evp(7)>,
L<EVP_EncryptInit(3)>,
L<EVP_CIPHER_meth_new(3)>

=head1 COPYRIGHT

Copyright 2017-2023 The OpenSSL Project Authors. All Rights Reserved.
Copyright 2017 Ribose Inc. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut

