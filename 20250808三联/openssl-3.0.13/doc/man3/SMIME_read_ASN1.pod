=pod

=head1 NAME

SMIME_read_ASN1_ex, SMIME_read_ASN1
- parse S/MIME message

=head1 SYNOPSIS

 #include <openssl/asn1.h>

 ASN1_VALUE *SMIME_read_ASN1_ex(BIO *in, int flags, BIO **bcont,
                                const ASN1_ITEM *it, ASN1_VALUE **x,
                                OSSL_LIB_CTX *libctx, const char *propq);
 ASN1_VALUE *SMIME_read_ASN1(BIO *in, BIO **bcont, const ASN1_ITEM *it);

=head1 DESCRIPTION

SMIME_read_ASN1_ex() parses a message in S/MIME format.

I<in> is a BIO to read the message from.
If the I<flags> argument contains B<CMS_BINARY> then the input is assumed to be
in binary format and is not translated to canonical form.
If in addition B<SMIME_ASCIICRLF> is set then the binary input is assumed
to be followed by B<CR> and B<LF> characters, else only by an B<LF> character.
I<x> can be used to optionally supply
a previously created I<it> ASN1_VALUE object (such as CMS_ContentInfo or PKCS7),
it can be set to NULL. Valid values that can be used by ASN.1 structure I<it>
are ASN1_ITEM_rptr(PKCS7) or ASN1_ITEM_rptr(CMS_ContentInfo). Any algorithm
fetches that occur during the operation will use the B<OSSL_LIB_CTX> supplied in
the I<libctx> parameter, and use the property query string I<propq> See
L<crypto(7)/ALGORITHM FETCHING> for further details about algorithm fetching.

If cleartext signing is used then the content is saved in a memory bio which is
written to I<*bcont>, otherwise I<*bcont> is set to NULL.

The parsed ASN1_VALUE structure is returned or NULL if an error occurred.

SMIME_read_ASN1() is similar to SMIME_read_ASN1_ex() but sets the value of I<x>
to NULL and the value of I<flags> to 0.

=head1 NOTES

The higher level functions L<SMIME_read_CMS_ex(3)> and
L<SMIME_read_PKCS7_ex(3)> should be used instead of SMIME_read_ASN1_ex().

To support future functionality if I<bcont> is not NULL I<*bcont> should be
initialized to NULL.

=head1 BUGS

The MIME parser used by SMIME_read_ASN1_ex() is somewhat primitive. While it will
handle most S/MIME messages more complex compound formats may not work.

The use of a memory BIO to hold the signed content limits the size of message
which can be processed due to memory restraints: a streaming single pass option
should be available.

=head1 RETURN VALUES

SMIME_read_ASN1_ex() and SMIME_read_ASN1() return a valid B<ASN1_VALUE>
structure or B<NULL> if an error occurred. The error can be obtained from
ERR_get_error(3).

=head1 SEE ALSO

L<ERR_get_error(3)>,
L<SMIME_read_CMS_ex(3)>,
L<SMIME_read_PKCS7_ex(3)>,
L<SMIME_write_ASN1(3)>,
L<SMIME_write_ASN1_ex(3)>

=head1 HISTORY

The function SMIME_read_ASN1_ex() was added in OpenSSL 3.0.

=head1 COPYRIGHT

Copyright 2020-2021 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
