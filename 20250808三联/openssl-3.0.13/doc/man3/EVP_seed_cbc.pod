=pod

=head1 NAME

EVP_seed_cbc,
<PERSON><PERSON>_seed_cfb,
<PERSON><PERSON>_seed_cfb128,
<PERSON><PERSON>_seed_ecb,
EVP_seed_ofb
- EVP SEED cipher

=head1 SYNOPSIS

 #include <openssl/evp.h>

 const EVP_CIPHER *EVP_seed_cbc(void);
 const EVP_CIPHER *EVP_seed_cfb(void);
 const EVP_CIPHER *EVP_seed_cfb128(void);
 const EVP_CIPHER *EVP_seed_ecb(void);
 const EVP_CIPHER *EVP_seed_ofb(void);

=head1 DESCRIPTION

The SEED encryption algorithm for EVP.

All modes below use a key length of 128 bits and acts on blocks of 128-bits.

=over 4

=item EVP_seed_cbc(),
EVP_seed_cfb(),
EVP_seed_cfb128(),
EVP_seed_ecb(),
EVP_seed_ofb()

The SEED encryption algorithm in CBC, CFB, ECB and OFB modes respectively.

=back

=head1 NOTES

Developers should be aware of the negative performance implications of
calling these functions multiple times and should consider using
L<EVP_CIPHER_fetch(3)> with L<EVP_CIPHER-SEED(7)> instead.
See L<crypto(7)/Performance> for further information.

=head1 RETURN VALUES

These functions return an B<EVP_CIPHER> structure that contains the
implementation of the symmetric cipher. See L<EVP_CIPHER_meth_new(3)> for
details of the B<EVP_CIPHER> structure.

=head1 SEE ALSO

L<evp(7)>,
L<EVP_EncryptInit(3)>,
L<EVP_CIPHER_meth_new(3)>

=head1 COPYRIGHT

Copyright 2017-2023 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
