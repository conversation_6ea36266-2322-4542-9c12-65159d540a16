=pod

=head1 NAME

EVP_PKEY_settable_params, EVP_PKEY_set_params,
EVP_PKEY_set_int_param, EVP_PKEY_set_size_t_param, EVP_PKEY_set_bn_param,
EVP_PKEY_set_utf8_string_param, EVP_PKEY_set_octet_string_param
- set key parameters into a key

=head1 SYNOPSIS

 #include <openssl/evp.h>

 const OSSL_PARAM *EVP_PKEY_settable_params(const EVP_PKEY *pkey);
 int EVP_PKEY_set_params(EVP_PKEY *pkey, OSSL_PARAM params[]);
 int EVP_PKEY_set_int_param(EVP_PKEY *pkey, const char *key_name, int in);
 int EVP_PKEY_set_size_t_param(EVP_PKEY *pkey, const char *key_name, size_t in);
 int EVP_PKEY_set_bn_param(EVP_PKEY *pkey, const char *key_name,
                           const BIGNUM *bn);
 int EVP_PKEY_set_utf8_string_param(EVP_PKEY *pkey, const char *key_name,
                                    const char *str);
 int EVP_PKEY_set_octet_string_param(EVP_PKEY *pkey, const char *key_name,
                                     const unsigned char *buf, size_t bsize);

=head1 DESCRIPTION

These functions can be used to set additional parameters into an existing
B<EVP_PKEY>.

EVP_PKEY_set_params() sets one or more I<params> into a I<pkey>.
See L<OSSL_PARAM(3)> for information about parameters.

EVP_PKEY_settable_params() returns a constant list of I<params> indicating
the names and types of key parameters that can be set.
See L<OSSL_PARAM(3)> for information about parameters.

EVP_PKEY_set_int_param() sets an integer value I<in> into a key I<pkey> for the
associated field I<key_name>.

EVP_PKEY_set_size_t_param() sets an size_t value I<in> into a key I<pkey> for
the associated field I<key_name>.

EVP_PKEY_set_bn_param() sets the BIGNUM value I<bn> into a key I<pkey> for the
associated field I<key_name>.

EVP_PKEY_set_utf8_string_param() sets the UTF8 string I<str> into a key I<pkey>
for the associated field I<key_name>.

EVP_PKEY_set_octet_string_param() sets the octet string value I<buf> with a
size I<bsize> into a key I<pkey> for the associated field I<key_name>.

=head1 NOTES

These functions only work for B<EVP_PKEY>s that contain a provider side key.

=head1 RETURN VALUES

EVP_PKEY_settable_params() returns NULL on error or if it is not supported,

All other methods return 1 if a value was successfully set, or 0 if
there was an error.

=head1 SEE ALSO

L<EVP_PKEY_gettable_params(3)>,
L<EVP_PKEY_CTX_new(3)>, L<provider-keymgmt(7)>, L<OSSL_PARAM(3)>,


=head1 HISTORY

These functions were added in OpenSSL 3.0.

=head1 COPYRIGHT

Copyright 2020-2021 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut

