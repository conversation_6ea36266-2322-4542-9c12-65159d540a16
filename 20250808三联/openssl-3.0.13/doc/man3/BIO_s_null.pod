=pod

=head1 NAME

BIO_s_null - null data sink

=head1 SYNOPSIS

 #include <openssl/bio.h>

 const BIO_METHOD *BIO_s_null(void);

=head1 DESCRIPTION

BIO_s_null() returns the null sink BIO method. Data written to
the null sink is discarded, reads return EOF.

=head1 NOTES

A null sink BIO behaves in a similar manner to the Unix /dev/null
device.

A null bio can be placed on the end of a chain to discard any data
passed through it.

A null sink is useful if, for example, an application wishes to digest some
data by writing through a digest bio but not send the digested data anywhere.
Since a BIO chain must normally include a source/sink BIO this can be achieved
by adding a null sink BIO to the end of the chain

=head1 RETURN VALUES

BIO_s_null() returns the null sink BIO method.

=head1 COPYRIGHT

Copyright 2000-2016 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
