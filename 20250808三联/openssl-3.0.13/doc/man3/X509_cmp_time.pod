=pod

=head1 NAME

X509_cmp_time, X509_cmp_current_time, X509_cmp_timeframe,
X509_time_adj, X509_time_adj_ex, X509_gmtime_adj
- X509 time functions

=head1 SYNOPSIS

 int X509_cmp_time(const ASN1_TIME *asn1_time, time_t *in_tm);
 int X509_cmp_current_time(const ASN1_TIME *asn1_time);
 int X509_cmp_timeframe(const X509_VERIFY_PARAM *vpm,
                        const ASN1_TIME *start, const ASN1_TIME *end);
 ASN1_TIME *X509_time_adj(ASN1_TIME *asn1_time, long offset_sec, time_t *in_tm);
 ASN1_TIME *X509_time_adj_ex(ASN1_TIME *asn1_time, int offset_day, long
                             offset_sec, time_t *in_tm);
 ASN1_TIME *X509_gmtime_adj(ASN1_TIME *asn1_time, long offset_sec);

=head1 DESCRIPTION

X509_cmp_time() compares the ASN1_TIME in I<asn1_time> with the time
in <in_tm>.

X509_cmp_current_time() compares the ASN1_TIME in
I<asn1_time> with the current time, expressed as time_t.

X509_cmp_timeframe() compares the given time period with the reference time
included in the verification parameters I<vpm> if they are not NULL and contain
B<X509_V_FLAG_USE_CHECK_TIME>; else the current time is used as reference time.

X509_time_adj_ex() sets the ASN1_TIME structure I<asn1_time> to the time
I<offset_day> and I<offset_sec> after I<in_tm>.

X509_time_adj() sets the ASN1_TIME structure I<asn1_time> to the time
I<offset_sec> after I<in_tm>. This method can only handle second
offsets up to the capacity of long, so the newer X509_time_adj_ex()
API should be preferred.

In both methods, if I<asn1_time> is NULL, a new ASN1_TIME structure
is allocated and returned.

In all methods, if I<in_tm> is NULL, the current time, expressed as
time_t, is used.

I<asn1_time> must satisfy the ASN1_TIME format mandated by RFC 5280,
i.e., its format must be either YYMMDDHHMMSSZ or YYYYMMDDHHMMSSZ.

X509_gmtime_adj() sets the ASN1_TIME structure I<asn1_time> to the time
I<offset_sec> after the current time. It is equivalent to calling
X509_time_adj() with the last parameter as NULL.

=head1 BUGS

Unlike many standard comparison functions, X509_cmp_time() and
X509_cmp_current_time() return 0 on error.

=head1 RETURN VALUES

X509_cmp_time() and X509_cmp_current_time() return -1 if I<asn1_time>
is earlier than, or equal to, I<in_tm> (resp. current time), and 1
otherwise. These methods return 0 on error.

X509_cmp_timeframe() returns 0 if I<vpm> is not NULL and the verification
parameters do not contain B<X509_V_FLAG_USE_CHECK_TIME>
but do contain B<X509_V_FLAG_NO_CHECK_TIME>. Otherwise it returns
1 if the end time is not NULL and the reference time (which has determined as
stated above) is past the end time, -1 if the start time is not NULL and the
reference time is before, else 0 to indicate that the reference time is in range
(implying that the end time is not before the start time if both are present).

X509_time_adj(), X509_time_adj_ex() and X509_gmtime_adj() return a pointer to
the updated ASN1_TIME structure, and NULL on error.

=head1 HISTORY

X509_cmp_timeframe() was added in OpenSSL 3.0.

=head1 COPYRIGHT

Copyright 2017-2022 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
