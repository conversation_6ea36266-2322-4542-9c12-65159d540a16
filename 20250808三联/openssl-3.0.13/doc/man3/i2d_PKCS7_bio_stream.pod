=pod

=head1 NAME

i2d_PKCS7_bio_stream - output PKCS7 structure in BER format

=head1 SYNOPSIS

 #include <openssl/pkcs7.h>

 int i2d_PKCS7_bio_stream(BIO *out, PKCS7 *p7, BIO *data, int flags);

=head1 DESCRIPTION

i2d_PKCS7_bio_stream() outputs a PKCS7 structure in BER format.

It is otherwise identical to the function SMIME_write_PKCS7().

=head1 NOTES

This function is effectively a version of the d2i_PKCS7_bio() supporting
streaming.

=head1 BUGS

The prefix "i2d" is arguably wrong because the function outputs BER format.

=head1 RETURN VALUES

i2d_PKCS7_bio_stream() returns 1 for success or 0 for failure.

=head1 SEE ALSO

L<ERR_get_error(3)>, L<PKCS7_sign(3)>,
L<PKCS7_verify(3)>, L<PKCS7_encrypt(3)>
L<PKCS7_decrypt(3)>,
L<SMIME_write_PKCS7(3)>,
L<PEM_write_bio_PKCS7_stream(3)>

=head1 HISTORY

The i2d_PKCS7_bio_stream() function was added in OpenSSL 1.0.0.

=head1 COPYRIGHT

Copyright 2008-2016 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
