=pod

=head1 NAME

EVP_PKEY_asn1_find,
EVP_PKEY_asn1_find_str,
EVP_PKEY_asn1_get_count,
EVP_PKEY_asn1_get0,
EVP_PKEY_asn1_get0_info
- enumerate public key ASN.1 methods

=head1 SYNOPSIS

 #include <openssl/evp.h>

 int EVP_PKEY_asn1_get_count(void);
 const EVP_PKEY_ASN1_METHOD *EVP_PKEY_asn1_get0(int idx);
 const EVP_PKEY_ASN1_METHOD *EVP_PKEY_asn1_find(ENGINE **pe, int type);
 const EVP_PKEY_ASN1_METHOD *EVP_PKEY_asn1_find_str(ENGINE **pe,
                                                    const char *str, int len);
 int EVP_PKEY_asn1_get0_info(int *ppkey_id, int *pkey_base_id,
                             int *ppkey_flags, const char **pinfo,
                             const char **ppem_str,
                             const EVP_PKEY_ASN1_METHOD *ameth);

=head1 DESCRIPTION

EVP_PKEY_asn1_count() returns a count of the number of public key
ASN.1 methods available: it includes standard methods and any methods
added by the application.

EVP_PKEY_asn1_get0() returns the public key ASN.1 method B<idx>.
The value of B<idx> must be between zero and EVP_PKEY_asn1_get_count()
- 1.

EVP_PKEY_asn1_find() looks up the B<EVP_PKEY_ASN1_METHOD> with NID
B<type>.
If B<pe> isn't B<NULL>, then it will look up an engine implementing a
B<EVP_PKEY_ASN1_METHOD> for the NID B<type> and return that instead,
and also set B<*pe> to point at the engine that implements it.

EVP_PKEY_asn1_find_str() looks up the B<EVP_PKEY_ASN1_METHOD> with PEM
type string B<str>.
Just like EVP_PKEY_asn1_find(), if B<pe> isn't B<NULL>, then it will
look up an engine implementing a B<EVP_PKEY_ASN1_METHOD> for the NID
B<type> and return that instead, and also set B<*pe> to point at the
engine that implements it.

EVP_PKEY_asn1_get0_info() returns the public key ID, base public key
ID (both NIDs), any flags, the method description and PEM type string
associated with the public key ASN.1 method B<*ameth>.

EVP_PKEY_asn1_count(), EVP_PKEY_asn1_get0(), EVP_PKEY_asn1_find() and
EVP_PKEY_asn1_find_str() are not thread safe, but as long as all
B<EVP_PKEY_ASN1_METHOD> objects are added before the application gets
threaded, using them is safe.  See L<EVP_PKEY_asn1_add0(3)>.

=head1 RETURN VALUES

EVP_PKEY_asn1_count() returns the number of available public key methods.

EVP_PKEY_asn1_get0() return a public key method or B<NULL> if B<idx> is
out of range.

EVP_PKEY_asn1_get0_info() returns 0 on failure, 1 on success.

=head1 SEE ALSO

L<EVP_PKEY_asn1_new(3)>, L<EVP_PKEY_asn1_add0(3)>

=head1 COPYRIGHT

Copyright 2017 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
