=pod

=head1 NAME

OSSL_trace_get_category_num, OSSL_trace_get_category_name
- OpenSSL tracing information functions

=head1 SYNOPSIS

 #include <openssl/trace.h>

 int OSSL_trace_get_category_num(const char *name);
 const char *OSSL_trace_get_category_name(int num);

=head1 DESCRIPTION

OSSL_trace_get_category_num() gives the category number corresponding
to the given C<name>.

OSSL_trace_get_category_name() gives the category name corresponding
to the given C<num>.

=head1 RETURN VALUES

OSSL_trace_get_category_num() returns the category number if the given
C<name> is a recognised category name, otherwise -1.

OSSL_trace_get_category_name() returns the category name if the given
C<num> is a recognised category number, otherwise NULL.

=head1 HISTORY

The OpenSSL Tracing API was added ino OpenSSL 3.0.

=head1 COPYRIGHT

Copyright 2019 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
