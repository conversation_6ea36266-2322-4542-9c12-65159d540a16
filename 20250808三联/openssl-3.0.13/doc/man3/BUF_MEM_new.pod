=pod

=head1 NAME

BUF_MEM_new, BUF_MEM_new_ex, BUF_MEM_free, BUF_MEM_grow,
BUF_MEM_grow_clean, BUF_reverse
- simple character array structure

=head1 SYNOPSIS

 #include <openssl/buffer.h>

 BUF_MEM *BUF_MEM_new(void);

 BUF_MEM *BUF_MEM_new_ex(unsigned long flags);

 void BUF_MEM_free(BUF_MEM *a);

 int BUF_MEM_grow(BUF_MEM *str, int len);
 size_t BUF_MEM_grow_clean(BUF_MEM *str, size_t len);

 void BUF_reverse(unsigned char *out, const unsigned char *in, size_t size);

=head1 DESCRIPTION

The buffer library handles simple character arrays. Buffers are used for
various purposes in the library, most notably memory BIOs.

BUF_MEM_new() allocates a new buffer of zero size.

BUF_MEM_new_ex() allocates a buffer with the specified flags.
The flag B<BUF_MEM_FLAG_SECURE> specifies that the B<data> pointer
should be allocated on the secure heap; see L<CRYPTO_secure_malloc(3)>.

BUF_MEM_free() frees up an already existing buffer. The data is zeroed
before freeing up in case the buffer contains sensitive data.

BUF_MEM_grow() changes the size of an already existing buffer to
B<len>. Any data already in the buffer is preserved if it increases in
size.

BUF_MEM_grow_clean() is similar to BUF_MEM_grow() but it sets any free'd
or additionally-allocated memory to zero.

BUF_reverse() reverses B<size> bytes at B<in> into B<out>.  If B<in>
is NULL, the array is reversed in-place.

=head1 RETURN VALUES

BUF_MEM_new() returns the buffer or NULL on error.

BUF_MEM_free() has no return value.

BUF_MEM_grow() and BUF_MEM_grow_clean() return
zero on error or the new size (i.e., B<len>).

=head1 SEE ALSO

L<bio(7)>,
L<CRYPTO_secure_malloc(3)>.

=head1 HISTORY

The BUF_MEM_new_ex() function was added in OpenSSL 1.1.0.

=head1 COPYRIGHT

Copyright 2000-2018 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
