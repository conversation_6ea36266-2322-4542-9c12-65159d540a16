=pod

=head1 NAME

SCT_print, SCT_LIST_print, SCT_validation_status_string -
Prints Signed Certificate Timestamps in a human-readable way

=head1 SYNOPSIS

 #include <openssl/ct.h>

 void SCT_print(const SCT *sct, BIO *out, int indent, const CTLOG_STORE *logs);
 void SCT_LIST_print(const STACK_OF(SCT) *sct_list, BIO *out, int indent,
                     const char *separator, const CTLOG_STORE *logs);
 const char *SCT_validation_status_string(const SCT *sct);

=head1 DESCRIPTION

SCT_print() prints a single Signed Certificate Timestamp (SCT) to a B<BIO> in
a human-readable format. SCT_LIST_print() prints an entire list of SCTs in a
similar way. A separator can be specified to delimit each SCT in the output.

The output can be indented by a specified number of spaces. If a B<CTLOG_STORE>
is provided, it will be used to print the description of the CT log that issued
each SCT (if that log is in the CTLOG_STORE). Alternatively, NULL can be passed
as the CTLOG_STORE parameter to disable this feature.

SCT_validation_status_string() will return the validation status of an SCT as
a human-readable string. Call SCT_validate() or SCT_LIST_validate()
beforehand in order to set the validation status of an SCT first.

=head1 RETURN VALUES

SCT_validation_status_string() returns a NUL-terminated string representing
the validation status of an B<SCT> object.

=head1 SEE ALSO

L<ct(7)>,
L<bio(7)>,
L<CTLOG_STORE_new(3)>,
L<SCT_validate(3)>

=head1 HISTORY

These functions were added in OpenSSL 1.1.0.

=head1 COPYRIGHT

Copyright 2016-2021 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
