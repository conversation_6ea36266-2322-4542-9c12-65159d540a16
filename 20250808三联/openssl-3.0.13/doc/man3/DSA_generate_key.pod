=pod

=head1 NAME

DSA_generate_key - generate DSA key pair

=head1 SYNOPSIS

 #include <openssl/dsa.h>

The following functions have been deprecated since OpenSSL 3.0, and can be
hidden entirely by defining B<OPENSSL_API_COMPAT> with a suitable version value,
see L<openssl_user_macros(7)>:

 int DSA_generate_key(DSA *a);

=head1 DESCRIPTION

All of the functions described on this page are deprecated.
Applications should instead use L<EVP_PKEY_keygen_init(3)> and
L<EVP_PKEY_keygen(3)> as described in L<EVP_PKEY-DSA(7)>.

DSA_generate_key() expects B<a> to contain DSA parameters. It generates
a new key pair and stores it in B<a-E<gt>pub_key> and B<a-E<gt>priv_key>.

The random generator must be seeded prior to calling DSA_generate_key().
If the automatic seeding or reseeding of the OpenSSL CSPRNG fails due to
external circumstances (see L<RAND(7)>), the operation will fail.

=head1 RETURN VALUES

DSA_generate_key() returns 1 on success, 0 otherwise.
The error codes can be obtained by L<ERR_get_error(3)>.

=head1 SEE ALSO

L<DSA_new(3)>, L<ERR_get_error(3)>, L<RAND_bytes(3)>,
L<DSA_generate_parameters_ex(3)>

=head1 HISTORY

This function was deprecated in OpenSSL 3.0.

=head1 COPYRIGHT

Copyright 2000-2021 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
