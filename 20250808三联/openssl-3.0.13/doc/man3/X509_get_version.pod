=pod

=head1 NAME

X509_get_version, X509_set_version, X509_REQ_get_version, X509_REQ_set_version,
X509_CRL_get_version, X509_CRL_set_version - get or set certificate,
certificate request or CRL version

=head1 SYNOPSIS

 #include <openssl/x509.h>

 long X509_get_version(const X509 *x);
 int X509_set_version(X509 *x, long version);

 long X509_REQ_get_version(const X509_REQ *req);
 int X509_REQ_set_version(X509_REQ *x, long version);

 long X509_CRL_get_version(const X509_CRL *crl);
 int X509_CRL_set_version(X509_CRL *x, long version);

=head1 DESCRIPTION

X509_get_version() returns the numerical value of the version field of
certificate B<x>. These correspond to the constants B<X509_VERSION_1>,
B<X509_VERSION_2>, and B<X509_VERSION_3>. Note: the values of these constants
are defined by standards (X.509 et al) to be one less than the certificate
version. So B<X509_VERSION_3> has value 2 and B<X509_VERSION_1> has value 0.

X509_set_version() sets the numerical value of the version field of certificate
B<x> to B<version>.

Similarly X509_REQ_get_version(), X509_REQ_set_version(),
X509_CRL_get_version() and X509_CRL_set_version() get and set the version
number of certificate requests and CRLs. They use constants
B<X509_REQ_VERSION_1>, B<X509_CRL_VERSION_1>, and B<X509_CRL_VERSION_2>.

=head1 NOTES

The version field of certificates, certificate requests and CRLs has a
DEFAULT value of B<v1(0)> meaning the field should be omitted for version
1. This is handled transparently by these functions.

=head1 RETURN VALUES

X509_get_version(), X509_REQ_get_version() and X509_CRL_get_version()
return the numerical value of the version field.

X509_set_version(), X509_REQ_set_version() and X509_CRL_set_version()
return 1 for success and 0 for failure.

=head1 SEE ALSO

L<d2i_X509(3)>,
L<ERR_get_error(3)>,
L<X509_CRL_get0_by_serial(3)>,
L<X509_get0_signature(3)>,
L<X509_get_ext_d2i(3)>,
L<X509_get_extension_flags(3)>,
L<X509_get_pubkey(3)>,
L<X509_get_subject_name(3)>,
L<X509_NAME_add_entry_by_txt(3)>,
L<X509_NAME_ENTRY_get_object(3)>,
L<X509_NAME_get_index_by_NID(3)>,
L<X509_NAME_print_ex(3)>,
L<X509_new(3)>,
L<X509_sign(3)>,
L<X509V3_get_d2i(3)>,
L<X509_verify_cert(3)>

=head1 HISTORY

X509_get_version(), X509_REQ_get_version() and X509_CRL_get_version() are
functions in OpenSSL 1.1.0, in previous versions they were macros.

=head1 COPYRIGHT

Copyright 2015-2021 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
