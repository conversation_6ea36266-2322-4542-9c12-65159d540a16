=pod

=head1 NAME

EVP_OpenInit, EVP_OpenUpdate, EVP_OpenFinal - EVP envelope decryption

=head1 SYNOPSIS

 #include <openssl/evp.h>

 int EVP_OpenInit(EVP_CIPHER_CTX *ctx, EVP_CIPHER *type, unsigned char *ek,
                  int ekl, unsigned char *iv, EVP_PKEY *priv);
 int EVP_OpenUpdate(EVP_CIPHER_CTX *ctx, unsigned char *out,
                    int *outl, unsigned char *in, int inl);
 int EVP_OpenFinal(EVP_CIPHER_CTX *ctx, unsigned char *out, int *outl);

=head1 DESCRIPTION

The EVP envelope routines are a high-level interface to envelope
decryption. They decrypt a public key encrypted symmetric key and
then decrypt data using it.

EVP_OpenInit() initializes a cipher context B<ctx> for decryption
with cipher B<type>. It decrypts the encrypted symmetric key of length
B<ekl> bytes passed in the B<ek> parameter using the private key B<priv>.
The IV is supplied in the B<iv> parameter.

EVP_OpenUpdate() and EVP_OpenFinal() have exactly the same properties
as the EVP_DecryptUpdate() and EVP_DecryptFinal() routines, as
documented on the L<EVP_EncryptInit(3)> manual
page.

=head1 NOTES

It is possible to call EVP_OpenInit() twice in the same way as
EVP_DecryptInit(). The first call should have B<priv> set to NULL
and (after setting any cipher parameters) it should be called again
with B<type> set to NULL.

If the cipher passed in the B<type> parameter is a variable length
cipher then the key length will be set to the value of the recovered
key length. If the cipher is a fixed length cipher then the recovered
key length must match the fixed cipher length.

=head1 RETURN VALUES

EVP_OpenInit() returns 0 on error or a non zero integer (actually the
recovered secret key size) if successful.

EVP_OpenUpdate() returns 1 for success or 0 for failure.

EVP_OpenFinal() returns 0 if the decrypt failed or 1 for success.

=head1 SEE ALSO

L<evp(7)>, L<RAND_bytes(3)>,
L<EVP_EncryptInit(3)>,
L<EVP_SealInit(3)>

=head1 COPYRIGHT

Copyright 2000-2020 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
