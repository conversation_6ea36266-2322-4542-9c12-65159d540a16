=pod

=head1 NAME

X509_load_http,
X509_http_nbio,
X509_CRL_load_http,
X509_CRL_http_nbio
- certificate and CRL loading functions

=head1 SYNOPSIS

 #include <openssl/x509.h>

 X509 *X509_load_http(const char *url, BIO *bio, BIO *rbio, int timeout);
 X509_CRL *X509_CRL_load_http(const char *url, BIO *bio, BIO *rbio, int timeout);

The following macros have been deprecated since OpenSSL 3.0, and can be
hidden entirely by defining B<OPENSSL_API_COMPAT> with a suitable version value,
see L<openssl_user_macros(7)>:

 #define X509_http_nbio(rctx, pcert)
 #define X509_CRL_http_nbio(rctx, pcrl)

=head1 DESCRIPTION

X509_load_http() and X509_CRL_load_http() loads a certificate or a CRL,
respectively, in ASN.1 format using HTTP from the given B<url>.

If B<bio> is given and B<rbio> is NULL then this BIO is used instead of an
internal one for connecting, writing the request, and reading the response.
If both B<bio> and B<rbio> are given (which may be memory BIOs, for instance)
then no explicit connection is attempted,
B<bio> is used for writing the request, and B<rbio> for reading the response.

If the B<timeout> parameter is > 0 this indicates the maximum number of seconds
to wait until the transfer is complete.
A value of 0 enables waiting indefinitely,
while a value < 0 immediately leads to a timeout condition.

X509_http_nbio() and X509_CRL_http_nbio() are macros for backward compatibility
that have the same effect as the functions above but with infinite timeout
and without the possibility to specify custom BIOs.

=head1 RETURN VALUES

On success the function yield the loaded value, else NULL.
Error conditions include connection/transfer timeout, parse errors, etc.

=head1 SEE ALSO

L<OSSL_HTTP_get(3)>

=head1 HISTORY

X509_load_http() and X509_CRL_load_http() were added in OpenSSL 3.0.
X509_http_nbio() and X509_CRL_http_nbio() were deprecated in OpenSSL 3.0.

=head1 COPYRIGHT

Copyright 2019-2021 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
