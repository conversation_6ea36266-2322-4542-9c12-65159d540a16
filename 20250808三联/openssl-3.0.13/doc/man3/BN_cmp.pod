=pod

=head1 NAME

BN_cmp, BN_ucmp, BN_is_zero, BN_is_one, BN_is_word, BN_abs_is_word, BN_is_odd - BIGNUM comparison and test functions

=head1 SYNOPSIS

 #include <openssl/bn.h>

 int BN_cmp(const BIGNUM *a, const BIGNUM *b);
 int BN_ucmp(const BIGNUM *a, const BIGNUM *b);

 int BN_is_zero(const BIGNUM *a);
 int BN_is_one(const BIGNUM *a);
 int BN_is_word(const BIGNUM *a, const BN_ULONG w);
 int BN_abs_is_word(const BIGNUM *a, const BN_ULONG w);
 int BN_is_odd(const BIGNUM *a);

=head1 DESCRIPTION

BN_cmp() compares the numbers I<a> and I<b>. BN_ucmp() compares their
absolute values.

BN_is_zero(), BN_is_one(), BN_is_word() and BN_abs_is_word() test if
I<a> equals 0, 1, I<w>, or E<verbar>I<w>E<verbar> respectively.
BN_is_odd() tests if I<a> is odd.

=head1 RETURN VALUES

BN_cmp() returns -1 if I<a> E<lt> I<b>, 0 if I<a> == I<b> and 1 if
I<a> E<gt> I<b>. BN_ucmp() is the same using the absolute values
of I<a> and I<b>.

BN_is_zero(), BN_is_one() BN_is_word(), BN_abs_is_word() and
BN_is_odd() return 1 if the condition is true, 0 otherwise.

=head1 HISTORY

Prior to OpenSSL 1.1.0, BN_is_zero(), BN_is_one(), BN_is_word(),
BN_abs_is_word() and BN_is_odd() were macros.

=head1 COPYRIGHT

Copyright 2000-2021 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
