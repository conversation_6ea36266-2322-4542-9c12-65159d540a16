=pod

=head1 NAME

ERR_new, ERR_set_debug, ERR_set_error, ERR_vset_error
- Error recording building blocks

=head1 SYNOPSIS

 #include <openssl/err.h>

 void ERR_new(void);
 void ERR_set_debug(const char *file, int line, const char *func);
 void ERR_set_error(int lib, int reason, const char *fmt, ...);
 void ERR_vset_error(int lib, int reason, const char *fmt, va_list args);

=head1 DESCRIPTION

The functions described here are generally not used directly, but
rather through macros such as L<ERR_raise(3)>.
They can still be useful for anyone that wants to make their own
macros.

ERR_new() allocates a new slot in the thread's error queue.

ERR_set_debug() sets the debug information related to the current
error in the thread's error queue.
The values that can be given are the filename I<file>, line in the
file I<line> and the name of the function I<func> where the error
occurred.
The names must be constant, this function will only save away the
pointers, not copy the strings.

ERR_set_error() sets the error information, which are the library
number I<lib> and the reason code I<reason>, and additional data as a
format string I<fmt> and an arbitrary number of arguments.
The additional data is processed with L<BIO_snprintf(3)> to form the
additional data string, which is allocated and store in the error
record.

ERR_vset_error() works like ERR_set_error(), but takes a B<va_list>
argument instead of a variable number of arguments.

=head1 RETURN VALUES

ERR_new, ERR_set_debug, ERR_set_error and ERR_vset_error
do not return any values.

=head1 NOTES

The library number is unique to each unit that records errors.
OpenSSL has a number of preallocated ones for its own uses, but
others may allocate their own library number dynamically with
L<ERR_get_next_error_library(3)>.

Reason codes are unique within each library, and may have an
associated set of strings as a short description of the reason.
For dynamically allocated library numbers, reason strings are recorded
with L<ERR_load_strings(3)>.

Provider authors are supplied with core versions of these functions,
see L<provider-base(7)>.

=head1 SEE ALSO

L<ERR_raise(3)>, L<ERR_get_next_error_library(3)>,
L<ERR_load_strings(3)>, L<BIO_snprintf(3)>, L<provider-base(7)>

=head1 COPYRIGHT

Copyright 2000-2020 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
