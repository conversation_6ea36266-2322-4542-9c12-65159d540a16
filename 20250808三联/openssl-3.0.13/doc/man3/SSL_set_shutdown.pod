=pod

=head1 NAME

SSL_set_shutdown, SSL_get_shutdown - manipulate shutdown state of an SSL connection

=head1 SYNOPSIS

 #include <openssl/ssl.h>

 void SSL_set_shutdown(SSL *ssl, int mode);

 int SSL_get_shutdown(const SSL *ssl);

=head1 DESCRIPTION

SSL_set_shutdown() sets the shutdown state of B<ssl> to B<mode>.

SSL_get_shutdown() returns the shutdown mode of B<ssl>.

=head1 NOTES

The shutdown state of an ssl connection is a bit-mask of:

=over 4

=item Z<>0

No shutdown setting, yet.

=item SSL_SENT_SHUTDOWN

A close_notify shutdown alert was sent to the peer, the connection is being
considered closed and the session is closed and correct.

=item SSL_RECEIVED_SHUTDOWN

A shutdown alert was received form the peer, either a normal close_notify
or a fatal error.

=back

SSL_SENT_SHUTDOWN and SSL_RECEIVED_SHUTDOWN can be set at the same time.

The shutdown state of the connection is used to determine the state of
the ssl session. If the session is still open, when
L<SSL_clear(3)> or L<SSL_free(3)> is called,
it is considered bad and removed according to RFC2246.
The actual condition for a correctly closed session is SSL_SENT_SHUTDOWN
(according to the TLS RFC, it is acceptable to only send the close_notify
alert but to not wait for the peer's answer, when the underlying connection
is closed).
SSL_set_shutdown() can be used to set this state without sending a
close alert to the peer (see L<SSL_shutdown(3)>).

If a close_notify was received, SSL_RECEIVED_SHUTDOWN will be set,
for setting SSL_SENT_SHUTDOWN the application must however still call
L<SSL_shutdown(3)> or SSL_set_shutdown() itself.

=head1 RETURN VALUES

SSL_set_shutdown() does not return diagnostic information.

SSL_get_shutdown() returns the current setting.

=head1 SEE ALSO

L<ssl(7)>, L<SSL_shutdown(3)>,
L<SSL_CTX_set_quiet_shutdown(3)>,
L<SSL_clear(3)>, L<SSL_free(3)>

=head1 COPYRIGHT

Copyright 2001-2018 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
