=pod

=head1 NAME

PKCS7_type_is_other - determine content type of PKCS#7 envelopedData structure

=head1 SYNOPSIS

 #include <openssl/pkcs7.h>

 int PKCS7_type_is_other(PKCS7 *p7);

=head1 DESCRIPTION

PKCS7_type_is_other() returns the whether the content type of a PKCS#7 envelopedData
structure is one of the following content types:

NID_pkcs7_data
NID_pkcs7_signed
NID_pkcs7_enveloped
NID_pkcs7_signedAndEnveloped
NID_pkcs7_digest
NID_pkcs7_encrypted

=head1 RETURN VALUES

PKCS7_type_is_other() returns either 0 if the content type is matched or 1 otherwise.

=head1 SEE ALSO

L<PKCS7_type_is_data(3)>, L<PKCS7_get_octet_string(3)>

=head1 COPYRIGHT

Copyright 2002-2020 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
