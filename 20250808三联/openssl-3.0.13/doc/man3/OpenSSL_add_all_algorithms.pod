=pod

=head1 NAME

OpenSSL_add_all_algorithms, OpenSSL_add_all_ciphers, OpenSSL_add_all_digests, EVP_cleanup -
add algorithms to internal table

=head1 SYNOPSIS

 #include <openssl/evp.h>

The following functions have been deprecated since OpenSSL 1.1.0, and can be
hidden entirely by defining B<OPENSSL_API_COMPAT> with a suitable version value,
see L<openssl_user_macros(7)>:

 void OpenSSL_add_all_algorithms(void);
 void OpenSSL_add_all_ciphers(void);
 void OpenSSL_add_all_digests(void);

 void EVP_cleanup(void);

=head1 DESCRIPTION

OpenSSL keeps an internal table of digest algorithms and ciphers. It uses
this table to lookup ciphers via functions such as EVP_get_cipher_byname().

OpenSSL_add_all_digests() adds all digest algorithms to the table.

OpenSSL_add_all_algorithms() adds all algorithms to the table (digests and
ciphers).

OpenSSL_add_all_ciphers() adds all encryption algorithms to the table including
password based encryption algorithms.

In versions prior to 1.1.0 EVP_cleanup() removed all ciphers and digests from
the table. It no longer has any effect in OpenSSL 1.1.0.

=head1 RETURN VALUES

None of the functions return a value.

=head1 SEE ALSO

L<evp(7)>, L<EVP_DigestInit(3)>,
L<EVP_EncryptInit(3)>

=head1 HISTORY

The OpenSSL_add_all_algorithms(), OpenSSL_add_all_ciphers(),
OpenSSL_add_all_digests(), and EVP_cleanup(), functions
were deprecated in OpenSSL 1.1.0 by OPENSSL_init_crypto() and should
not be used.

=head1 COPYRIGHT

Copyright 2000-2021 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
