=pod

=head1 NAME

BIO_find_type, BIO_next, BIO_method_type - BIO chain traversal

=head1 SYNOPSIS

 #include <openssl/bio.h>

 BIO *BIO_find_type(BIO *b, int bio_type);
 BIO *BIO_next(BIO *b);
 int BIO_method_type(const BIO *b);

=head1 DESCRIPTION

The BIO_find_type() searches for a BIO of a given type in a chain, starting
at BIO B<b>. If B<type> is a specific type (such as B<BIO_TYPE_MEM>) then a search
is made for a BIO of that type. If B<type> is a general type (such as
B<BIO_TYPE_SOURCE_SINK>) then the next matching BIO of the given general type is
searched for. BIO_find_type() returns the next matching BIO or NULL if none is
found.

The following general types are defined:
B<BIO_TYPE_DESCRIPTOR>, B<BIO_TYPE_FILTER>, and B<BIO_TYPE_SOURCE_SINK>.

For a list of the specific types, see the F<< <openssl/bio.h> >> header file.

BIO_next() returns the next BIO in a chain. It can be used to traverse all BIOs
in a chain or used in conjunction with BIO_find_type() to find all BIOs of a
certain type.

BIO_method_type() returns the type of a BIO.

=head1 RETURN VALUES

BIO_find_type() returns a matching BIO or NULL for no match.

BIO_next() returns the next BIO in a chain.

BIO_method_type() returns the type of the BIO B<b>.

=head1 EXAMPLES

Traverse a chain looking for digest BIOs:

 BIO *btmp;

 btmp = in_bio; /* in_bio is chain to search through */
 do {
     btmp = BIO_find_type(btmp, BIO_TYPE_MD);
     if (btmp == NULL)
         break; /* Not found */
     /* btmp is a digest BIO, do something with it ...*/
     ...

     btmp = BIO_next(btmp);
 } while (btmp);


=head1 COPYRIGHT

Copyright 2000-2021 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
