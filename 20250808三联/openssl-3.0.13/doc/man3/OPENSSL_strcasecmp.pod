=pod

=head1 NAME

OPENSSL_strcasecmp, OPENSSL_strncasecmp - compare two strings ignoring case

=head1 SYNOPSIS

 #include <openssl/crypto.h>

 int OPENSSL_strcasecmp(const char *s1, const char *s2);
 int OPENSSL_strncasecmp(const char *s1, const char *s2, size_t n);

=head1 DESCRIPTION

The OPENSSL_strcasecmp function performs a byte-by-byte comparison of the strings
B<s1> and B<s2>, ignoring the case of the characters.

The OPENSSL_strncasecmp function is similar, except that it compares no more than
B<n> bytes of B<s1> and B<s2>.

In POSIX-compatible system and on Windows these functions use "C" locale for
case insensitive. Otherwise the comparison is done in current locale.

=head1 RETURN VALUES

Both functions return an integer less than, equal to, or greater than zero if
s1 is found, respectively, to be less than, to match, or be greater than s2.

=head1 NOTES

OpenSSL extensively uses case insensitive comparison of ASCII strings. Though
OpenSSL itself is locale-agnostic, the applications using OpenSSL libraries may
unpredictably suffer when they use localization (e.g. Turkish locale is
well-known with a specific I/i cases). These functions use C locale for string
comparison.

=head1 COPYRIGHT

Copyright 2022 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
