=pod

=head1 NAME

RSA_print, RSA_print_fp,
DSAparams_print, DSAparams_print_fp, DSA_print, DSA_print_fp,
DHparams_print, DHparams_print_fp - print cryptographic parameters

=head1 SYNOPSIS

 #include <openssl/rsa.h>

The following functions have been deprecated since OpenSSL 3.0, and can be
hidden entirely by defining B<OPENSSL_API_COMPAT> with a suitable version value,
see L<openssl_user_macros(7)>:

 int RSA_print(BIO *bp, const RSA *x, int offset);
 int RSA_print_fp(FILE *fp, const RSA *x, int offset);

 #include <openssl/dsa.h>

The following functions have been deprecated since OpenSSL 3.0, and can be
hidden entirely by defining B<OPENSSL_API_COMPAT> with a suitable version value,
see L<openssl_user_macros(7)>:

 int DSAparams_print(BIO *bp, const DSA *x);
 int DSAparams_print_fp(FILE *fp, const DSA *x);
 int DSA_print(BIO *bp, const DSA *x, int offset);
 int DSA_print_fp(FILE *fp, const DSA *x, int offset);

 #include <openssl/dh.h>

The following functions have been deprecated since OpenSSL 3.0, and can be
hidden entirely by defining B<OPENSSL_API_COMPAT> with a suitable version value,
see L<openssl_user_macros(7)>:

 int DHparams_print(BIO *bp, DH *x);
 int DHparams_print_fp(FILE *fp, const DH *x);

=head1 DESCRIPTION

All of the functions described on this page are deprecated.
Applications should instead use L<EVP_PKEY_print_params(3)> and
L<EVP_PKEY_print_private(3)>.

A human-readable hexadecimal output of the components of the RSA
key, DSA parameters or key or DH parameters is printed to B<bp> or B<fp>.

The output lines are indented by B<offset> spaces.

=head1 RETURN VALUES

DSAparams_print(), DSAparams_print_fp(), DSA_print(), and DSA_print_fp()
return 1 for success and 0 or a negative value for failure.

DHparams_print() and DHparams_print_fp() return 1 on success, 0 on error.

=head1 SEE ALSO

 L<EVP_PKEY_print_params(3)>,
 L<EVP_PKEY_print_private(3)>,
 L<BN_bn2bin(3)>

=head1 HISTORY

All of these functions were deprecated in OpenSSL 3.0.

=head1 COPYRIGHT

Copyright 2000-2021 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
