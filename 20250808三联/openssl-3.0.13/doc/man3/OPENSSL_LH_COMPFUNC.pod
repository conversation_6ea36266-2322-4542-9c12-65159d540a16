=pod

=head1 NAME

LHASH, DECLARE_LHASH_OF,
OPENSSL_LH_COMPFUNC, OPENSSL_LH_HASHFUNC, OPENSSL_LH_DOALL_FUNC,
LHASH_DOALL_ARG_FN_TYPE,
IMPLEMENT_LHASH_HASH_FN, IMPLEMENT_LHASH_COMP_FN,
lh_TYPE_new, lh_TYPE_free, lh_TYPE_flush,
lh_TYPE_insert, lh_TYPE_delete, lh_TYPE_retrieve,
lh_TYPE_doall, lh_TYPE_doall_arg, lh_TYPE_num_items, lh_TYPE_get_down_load,
lh_TYPE_set_down_load, lh_TYPE_error,
OPENSSL_LH_new, OPENSSL_LH_free,  OPENSSL_LH_flush,
OPENSSL_LH_insert, OPENSSL_LH_delete, OPENSSL_LH_retrieve,
OPENSSL_LH_doall, OPENSSL_LH_doall_arg, OPENSSL_LH_num_items,
OPENSSL_LH_get_down_load, OPENSSL_LH_set_down_load, OPENSSL_LH_error
- dynamic hash table

=head1 SYNOPSIS

=for openssl generic

 #include <openssl/lhash.h>

 DECLARE_LHASH_OF(TYPE);

 LHASH_OF(TYPE) *lh_TYPE_new(OPENSSL_LH_HASHFUNC hash, OPENSSL_LH_COMPFUNC compare);
 void lh_TYPE_free(LHASH_OF(TYPE) *table);
 void lh_TYPE_flush(LHASH_OF(TYPE) *table);

 TYPE *lh_TYPE_insert(LHASH_OF(TYPE) *table, TYPE *data);
 TYPE *lh_TYPE_delete(LHASH_OF(TYPE) *table, TYPE *data);
 TYPE *lh_TYPE_retrieve(LHASH_OF(TYPE) *table, TYPE *data);

 void lh_TYPE_doall(LHASH_OF(TYPE) *table, OPENSSL_LH_DOALL_FUNC func);
 void lh_TYPE_doall_arg(LHASH_OF(TYPE) *table, OPENSSL_LH_DOALL_FUNCARG func,
                        TYPE *arg);

 unsigned long lh_TYPE_num_items(OPENSSL_LHASH *lh);
 unsigned long lh_TYPE_get_down_load(OPENSSL_LHASH *lh);
 void lh_TYPE_set_down_load(OPENSSL_LHASH *lh, unsigned long dl);

 int lh_TYPE_error(LHASH_OF(TYPE) *table);

 typedef int (*OPENSSL_LH_COMPFUNC)(const void *, const void *);
 typedef unsigned long (*OPENSSL_LH_HASHFUNC)(const void *);
 typedef void (*OPENSSL_LH_DOALL_FUNC)(const void *);
 typedef void (*LHASH_DOALL_ARG_FN_TYPE)(const void *, const void *);

 OPENSSL_LHASH *OPENSSL_LH_new(OPENSSL_LH_HASHFUNC h, OPENSSL_LH_COMPFUNC c);
 void OPENSSL_LH_free(OPENSSL_LHASH *lh);
 void OPENSSL_LH_flush(OPENSSL_LHASH *lh);

 void *OPENSSL_LH_insert(OPENSSL_LHASH *lh, void *data);
 void *OPENSSL_LH_delete(OPENSSL_LHASH *lh, const void *data);
 void *OPENSSL_LH_retrieve(OPENSSL_LHASH *lh, const void *data);

 void OPENSSL_LH_doall(OPENSSL_LHASH *lh, OPENSSL_LH_DOALL_FUNC func);
 void OPENSSL_LH_doall_arg(OPENSSL_LHASH *lh, OPENSSL_LH_DOALL_FUNCARG func, void *arg);

 unsigned long OPENSSL_LH_num_items(OPENSSL_LHASH *lh);
 unsigned long OPENSSL_LH_get_down_load(OPENSSL_LHASH *lh);
 void OPENSSL_LH_set_down_load(OPENSSL_LHASH *lh, unsigned long dl);

 int OPENSSL_LH_error(OPENSSL_LHASH *lh);

 #define LH_LOAD_MULT   /* integer constant */

=head1 DESCRIPTION

This library implements type-checked dynamic hash tables. The hash
table entries can be arbitrary structures. Usually they consist of key
and value fields.  In the description here, B<I<TYPE>> is used a placeholder
for any of the OpenSSL datatypes, such as I<SSL_SESSION>.

B<lh_I<TYPE>_new>() creates a new B<LHASH_OF>(B<I<TYPE>>) structure to store
arbitrary data entries, and specifies the 'hash' and 'compare'
callbacks to be used in organising the table's entries.  The I<hash>
callback takes a pointer to a table entry as its argument and returns
an unsigned long hash value for its key field.  The hash value is
normally truncated to a power of 2, so make sure that your hash
function returns well mixed low order bits.  The I<compare> callback
takes two arguments (pointers to two hash table entries), and returns
0 if their keys are equal, nonzero otherwise.

If your hash table
will contain items of some particular type and the I<hash> and
I<compare> callbacks hash/compare these types, then the
B<IMPLEMENT_LHASH_HASH_FN> and B<IMPLEMENT_LHASH_COMP_FN> macros can be
used to create callback wrappers of the prototypes required by
B<lh_I<TYPE>_new>() as shown in this example:

 /*
  * Implement the hash and compare functions; "stuff" can be any word.
  */
 static unsigned long stuff_hash(const TYPE *a)
 {
     ...
 }
 static int stuff_cmp(const TYPE *a, const TYPE *b)
 {
     ...
 }

 /*
  * Implement the wrapper functions.
  */
 static IMPLEMENT_LHASH_HASH_FN(stuff, TYPE)
 static IMPLEMENT_LHASH_COMP_FN(stuff, TYPE)

If the type is going to be used in several places, the following macros
can be used in a common header file to declare the function wrappers:

 DECLARE_LHASH_HASH_FN(stuff, TYPE)
 DECLARE_LHASH_COMP_FN(stuff, TYPE)

Then a hash table of B<I<TYPE>> objects can be created using this:

 LHASH_OF(TYPE) *htable;

 htable = B<lh_I<TYPE>_new>(LHASH_HASH_FN(stuff), LHASH_COMP_FN(stuff));

B<lh_I<TYPE>_free>() frees the B<LHASH_OF>(B<I<TYPE>>) structure
I<table>. Allocated hash table entries will not be freed; consider
using B<lh_I<TYPE>_doall>() to deallocate any remaining entries in the
hash table (see below).

B<lh_I<TYPE>_flush>() empties the B<LHASH_OF>(B<I<TYPE>>) structure I<table>. New
entries can be added to the flushed table.  Allocated hash table entries
will not be freed; consider using B<lh_I<TYPE>_doall>() to deallocate any
remaining entries in the hash table (see below).

B<lh_I<TYPE>_insert>() inserts the structure pointed to by I<data> into
I<table>.  If there already is an entry with the same key, the old
value is replaced. Note that B<lh_I<TYPE>_insert>() stores pointers, the
data are not copied.

B<lh_I<TYPE>_delete>() deletes an entry from I<table>.

B<lh_I<TYPE>_retrieve>() looks up an entry in I<table>. Normally, I<data>
is a structure with the key field(s) set; the function will return a
pointer to a fully populated structure.

B<lh_I<TYPE>_doall>() will, for every entry in the hash table, call
I<func> with the data item as its parameter.
For example:

 /* Cleans up resources belonging to 'a' (this is implemented elsewhere) */
 void TYPE_cleanup_doall(TYPE *a);

 /* Implement a prototype-compatible wrapper for "TYPE_cleanup" */
 IMPLEMENT_LHASH_DOALL_FN(TYPE_cleanup, TYPE)

 /* Call "TYPE_cleanup" against all items in a hash table. */
 lh_TYPE_doall(hashtable, LHASH_DOALL_FN(TYPE_cleanup));

 /* Then the hash table itself can be deallocated */
 lh_TYPE_free(hashtable);

B<lh_I<TYPE>_doall_arg>() is the same as B<lh_I<TYPE>_doall>() except that
I<func> will be called with I<arg> as the second argument and I<func>
should be of type B<LHASH_DOALL_ARG_FN>(B<I<TYPE>>) (a callback prototype
that is passed both the table entry and an extra argument).  As with
lh_doall(), you can instead choose to declare your callback with a
prototype matching the types you are dealing with and use the
declare/implement macros to create compatible wrappers that cast
variables before calling your type-specific callbacks.  An example of
this is demonstrated here (printing all hash table entries to a BIO
that is provided by the caller):

 /* Prints item 'a' to 'output_bio' (this is implemented elsewhere) */
 void TYPE_print_doall_arg(const TYPE *a, BIO *output_bio);

 /* Implement a prototype-compatible wrapper for "TYPE_print" */
 static IMPLEMENT_LHASH_DOALL_ARG_FN(TYPE, const TYPE, BIO)

 /* Print out the entire hashtable to a particular BIO */
 lh_TYPE_doall_arg(hashtable, LHASH_DOALL_ARG_FN(TYPE_print), BIO,
                   logging_bio);

Note that it is by default B<not> safe to use B<lh_I<TYPE>_delete>() inside a
callback passed to B<lh_I<TYPE>_doall>() or B<lh_I<TYPE>_doall_arg>(). The
reason for this is that deleting an item from the hash table may result in the
hash table being contracted to a smaller size and rehashed.
B<lh_I<TYPE>_doall>() and B<lh_I<TYPE>_doall_arg>() are unsafe and will exhibit
undefined behaviour under these conditions, as these functions assume the hash
table size and bucket pointers do not change during the call.

If it is desired to use B<lh_I<TYPE>_doall>() or B<lh_I<TYPE>_doall_arg>() with
B<lh_I<TYPE>_delete>(), it is essential that you call
B<lh_I<TYPE>_set_down_load>() with a I<down_load> argument of 0 first. This
disables hash table contraction and guarantees that it will be safe to delete
items from a hash table during a call to B<lh_I<TYPE>_doall>() or
B<lh_I<TYPE>_doall_arg>().

It is never safe to call B<lh_I<TYPE>_insert>() during a call to
B<lh_I<TYPE>_doall>() or B<lh_I<TYPE>_doall_arg>().

B<lh_I<TYPE>_error>() can be used to determine if an error occurred in the last
operation.

B<lh_I<TYPE>_num_items>() returns the number of items in the hash table.

B<lh_I<TYPE>_get_down_load>() and B<lh_I<TYPE>_set_down_load>() get and set the
factor used to determine when the hash table is contracted. The factor is the
load factor at or below which hash table contraction will occur, multiplied by
B<LH_LOAD_MULT>, where the load factor is the number of items divided by the
number of nodes. Setting this value to 0 disables hash table contraction.

OPENSSL_LH_new() is the same as the B<lh_I<TYPE>_new>() except that it is not
type specific. So instead of returning an B<LHASH_OF(I<TYPE>)> value it returns
a B<void *>. In the same way the functions OPENSSL_LH_free(),
OPENSSL_LH_flush(), OPENSSL_LH_insert(), OPENSSL_LH_delete(),
OPENSSL_LH_retrieve(), OPENSSL_LH_doall(), OPENSSL_LH_doall_arg(),
OPENSSL_LH_num_items(), OPENSSL_LH_get_down_load(), OPENSSL_LH_set_down_load()
and OPENSSL_LH_error() are equivalent to the similarly named B<lh_I<TYPE>>
functions except that they return or use a B<void *> where the equivalent
B<lh_I<TYPE>> function returns or uses a B<I<TYPE> *> or B<LHASH_OF(I<TYPE>) *>.
B<lh_I<TYPE>> functions are implemented as type checked wrappers around the
B<OPENSSL_LH> functions. Most applications should not call the B<OPENSSL_LH>
functions directly.

=head1 RETURN VALUES

B<lh_I<TYPE>_new>() and OPENSSL_LH_new() return NULL on error, otherwise a
pointer to the new B<LHASH> structure.

When a hash table entry is replaced, B<lh_I<TYPE>_insert>() or
OPENSSL_LH_insert() return the value being replaced. NULL is returned on normal
operation and on error.

B<lh_I<TYPE>_delete>() and OPENSSL_LH_delete() return the entry being deleted.
NULL is returned if there is no such value in the hash table.

B<lh_I<TYPE>_retrieve>() and OPENSSL_LH_retrieve() return the hash table entry
if it has been found, NULL otherwise.

B<lh_I<TYPE>_error>() and OPENSSL_LH_error() return 1 if an error occurred in
the last operation, 0 otherwise. It's meaningful only after non-retrieve
operations.

B<lh_I<TYPE>_free>(), OPENSSL_LH_free(), B<lh_I<TYPE>_flush>(),
OPENSSL_LH_flush(), B<lh_I<TYPE>_doall>() OPENSSL_LH_doall(),
B<lh_I<TYPE>_doall_arg>() and OPENSSL_LH_doall_arg() return no values.

=head1 NOTE

The LHASH code is not thread safe. All updating operations, as well as
B<lh_I<TYPE>_error>() or OPENSSL_LH_error() calls must be performed under
a write lock. All retrieve operations should be performed under a read lock,
I<unless> accurate usage statistics are desired. In which case, a write lock
should be used for retrieve operations as well. For output of the usage
statistics, using the functions from L<OPENSSL_LH_stats(3)>, a read lock
suffices.

The LHASH code regards table entries as constant data.  As such, it
internally represents lh_insert()'d items with a "const void *"
pointer type.  This is why callbacks such as those used by lh_doall()
and lh_doall_arg() declare their prototypes with "const", even for the
parameters that pass back the table items' data pointers - for
consistency, user-provided data is "const" at all times as far as the
LHASH code is concerned.  However, as callers are themselves providing
these pointers, they can choose whether they too should be treating
all such parameters as constant.

As an example, a hash table may be maintained by code that, for
reasons of encapsulation, has only "const" access to the data being
indexed in the hash table (i.e. it is returned as "const" from
elsewhere in their code) - in this case the LHASH prototypes are
appropriate as-is.  Conversely, if the caller is responsible for the
life-time of the data in question, then they may well wish to make
modifications to table item passed back in the lh_doall() or
lh_doall_arg() callbacks (see the "TYPE_cleanup" example above).  If
so, the caller can either cast the "const" away (if they're providing
the raw callbacks themselves) or use the macros to declare/implement
the wrapper functions without "const" types.

Callers that only have "const" access to data they're indexing in a
table, yet declare callbacks without constant types (or cast the
"const" away themselves), are therefore creating their own risks/bugs
without being encouraged to do so by the API.  On a related note,
those auditing code should pay special attention to any instances of
DECLARE/IMPLEMENT_LHASH_DOALL_[ARG_]_FN macros that provide types
without any "const" qualifiers.

=head1 BUGS

B<lh_I<TYPE>_insert>() and OPENSSL_LH_insert() return NULL both for success
and error.

=head1 SEE ALSO

L<OPENSSL_LH_stats(3)>

=head1 HISTORY

In OpenSSL 1.0.0, the lhash interface was revamped for better
type checking.

=head1 COPYRIGHT

Copyright 2000-2022 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
