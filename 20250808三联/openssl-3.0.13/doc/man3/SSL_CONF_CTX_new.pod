=pod

=head1 NAME

SSL_CONF_CTX_new, SSL_CONF_CTX_free - SSL configuration allocation functions

=head1 SYNOPSIS

 #include <openssl/ssl.h>

 SSL_CONF_CTX *SSL_CONF_CTX_new(void);
 void SSL_CONF_CTX_free(SSL_CONF_CTX *cctx);

=head1 DESCRIPTION

The function SSL_CONF_CTX_new() allocates and initialises an B<SSL_CONF_CTX>
structure for use with the SSL_CONF functions.

The function SSL_CONF_CTX_free() frees up the context B<cctx>.
If B<cctx> is NULL nothing is done.

=head1 RETURN VALUES

SSL_CONF_CTX_new() returns either the newly allocated B<SSL_CONF_CTX> structure
or B<NULL> if an error occurs.

SSL_CONF_CTX_free() does not return a value.

=head1 SEE ALSO

L<ssl(7)>,
L<SSL_CONF_CTX_set_flags(3)>,
L<SSL_CONF_CTX_set_ssl_ctx(3)>,
L<SSL_CONF_CTX_set1_prefix(3)>,
L<SSL_CONF_cmd(3)>,
L<SSL_CONF_cmd_argv(3)>

=head1 HISTORY

These functions were added in OpenSSL 1.0.2.

=head1 COPYRIGHT

Copyright 2012-2016 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
