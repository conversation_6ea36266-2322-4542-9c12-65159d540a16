=pod

=head1 NAME

EVP_PKEY_missing_parameters, EVP_PKEY_copy_parameters, EVP_PKEY_parameters_eq,
EVP_PKEY_cmp_parameters, EVP_PKEY_eq,
EVP_PKEY_cmp - public key parameter and comparison functions

=head1 SYNOPSIS

 #include <openssl/evp.h>

 int EVP_PKEY_missing_parameters(const EVP_PKEY *pkey);
 int EVP_PKEY_copy_parameters(EVP_PKEY *to, const EVP_PKEY *from);

 int EVP_PKEY_parameters_eq(const EVP_PKEY *a, const EVP_PKEY *b);
 int EVP_PKEY_eq(const EVP_PKEY *a, const EVP_PKEY *b);

The following functions have been deprecated since OpenSSL 3.0, and can be
hidden entirely by defining B<OPENSSL_API_COMPAT> with a suitable version value,
see L<openssl_user_macros(7)>:

 int EVP_PKEY_cmp_parameters(const EVP_PKEY *a, const EVP_PKEY *b);
 int EVP_PKEY_cmp(const EVP_PKEY *a, const EVP_PKEY *b);

=head1 DESCRIPTION

The function EVP_PKEY_missing_parameters() returns 1 if the public key
parameters of B<pkey> are missing and 0 if they are present or the algorithm
doesn't use parameters.

The function EVP_PKEY_copy_parameters() copies the parameters from key
B<from> to key B<to>. An error is returned if the parameters are missing in
B<from> or present in both B<from> and B<to> and mismatch. If the parameters
in B<from> and B<to> are both present and match this function has no effect.

The function EVP_PKEY_parameters_eq() checks the parameters of keys
B<a> and B<b> for equality.

The function EVP_PKEY_eq() checks the keys B<a> and B<b> for equality,
including their parameters if they are available.

=head1 NOTES

The main purpose of the functions EVP_PKEY_missing_parameters() and
EVP_PKEY_copy_parameters() is to handle public keys in certificates where the
parameters are sometimes omitted from a public key if they are inherited from
the CA that signed it.

The deprecated functions EVP_PKEY_cmp() and EVP_PKEY_cmp_parameters() differ in
their return values compared to other _cmp() functions. They are aliases for
EVP_PKEY_eq() and EVP_PKEY_parameters_eq().

The function EVP_PKEY_cmp() previously only checked the key parameters
(if there are any) and the public key, assuming that there always was
a public key and that private key equality could be derived from that.
Because it's no longer assumed that the private key in an L<EVP_PKEY(3)> is
always accompanied by a public key, the comparison can not rely on public
key comparison alone.

Instead, EVP_PKEY_eq() (and therefore also EVP_PKEY_cmp()) now compares:

=over 4

=item 1.

the key parameters (if there are any)

=item 2.

the public keys or the private keys of the two B<EVP_PKEY>s, depending on
what they both contain.

=back

=begin comment

Exactly what is compared is ultimately at the discretion of the provider
that holds the key, as they will compare what makes sense to them that fits
the selector bits they are passed.

=end comment

=head1 RETURN VALUES

The function EVP_PKEY_missing_parameters() returns 1 if the public key
parameters of B<pkey> are missing and 0 if they are present or the algorithm
doesn't use parameters.

These functions EVP_PKEY_copy_parameters() returns 1 for success and 0 for
failure.

The functions EVP_PKEY_cmp_parameters(), EVP_PKEY_parameters_eq(),
EVP_PKEY_cmp() and EVP_PKEY_eq() return 1 if their
inputs match, 0 if they don't match, -1 if the key types are different and
-2 if the operation is not supported.

=head1 SEE ALSO

L<EVP_PKEY_CTX_new(3)>,
L<EVP_PKEY_keygen(3)>

=head1 HISTORY

The EVP_PKEY_cmp() and EVP_PKEY_cmp_parameters() functions were deprecated in
OpenSSL 3.0.

The EVP_PKEY_eq() and EVP_PKEY_parameters_eq() were added in OpenSSL 3.0 to
replace EVP_PKEY_cmp() and EVP_PKEY_cmp_parameters().

=head1 COPYRIGHT

Copyright 2006-2021 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
