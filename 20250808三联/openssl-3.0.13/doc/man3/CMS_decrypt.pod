=pod

=head1 NAME

CMS_decrypt, CMS_decrypt_set1_pkey_and_peer,
CMS_decrypt_set1_pkey, CMS_decrypt_set1_password
- decrypt content from a CMS envelopedData structure

=head1 SYNOPSIS

 #include <openssl/cms.h>

 int CMS_decrypt(CMS_ContentInfo *cms, EVP_PKEY *pkey, X509 *cert,
                 BIO *dcont, BIO *out, unsigned int flags);
 int CMS_decrypt_set1_pkey_and_peer(CMS_ContentInfo *cms,
                 EVP_PKEY *pk, X509 *cert, X509 *peer);
 int CMS_decrypt_set1_pkey(CMS_ContentInfo *cms, EVP_PKEY *pk, X509 *cert);
 int CMS_decrypt_set1_password(CMS_ContentInfo *cms,
                               unsigned char *pass, ossl_ssize_t passlen);

=head1 DESCRIPTION

CMS_decrypt() extracts the decrypted content from a CMS EnvelopedData
or AuthEnvelopedData structure.
It uses CMS_decrypt_set1_pkey() to decrypt the content
with the recipient private key I<pkey> if I<pkey> is not NULL.
In this case, it is recommended to provide the associated certificate
in I<cert> - see the NOTES below.
I<out> is a BIO to write the content to and
I<flags> is an optional set of flags.
If I<pkey> is NULL the function assumes that decryption was already done
(e.g., using CMS_decrypt_set1_pkey() or CMS_decrypt_set1_password()) and just
provides the content unless I<cert>, I<dcont>, and I<out> are NULL as well.
The I<dcont> parameter is used in the rare case where the encrypted content
is detached. It will normally be set to NULL.

CMS_decrypt_set1_pkey_and_peer() decrypts the CMS_ContentInfo structure I<cms>
using the private key I<pkey>, the corresponding certificate I<cert>, which is
recommended to be supplied but may be NULL,
and the (optional) originator certificate I<peer>.
On success, it also records in I<cms> the decryption key I<pkey>, and this
should be followed by C<CMS_decrypt(cms, NULL, NULL, dcont, out, flags)>.
This call deallocates any decryption key stored in I<cms>.

CMS_decrypt_set1_pkey() is the same as
CMS_decrypt_set1_pkey_and_peer() with I<peer> being NULL.

CMS_decrypt_set1_password() decrypts the CMS_ContentInfo structure I<cms>
using the secret I<pass> of length I<passlen>.
On success, it also records in I<cms> the decryption key used, and this
should be followed by C<CMS_decrypt(cms, NULL, NULL, dcont, out, flags)>.
This call deallocates any decryption key stored in I<cms>.

=head1 NOTES

Although the recipients certificate is not needed to decrypt the data it is
needed to locate the appropriate (of possible several) recipients in the CMS
structure.

If I<cert> is set to NULL all possible recipients are tried. This case however
is problematic. To thwart the MMA attack (Bleichenbacher's attack on
PKCS #1 v1.5 RSA padding) all recipients are tried whether they succeed or
not. If no recipient succeeds then a random symmetric key is used to decrypt
the content: this will typically output garbage and may (but is not guaranteed
to) ultimately return a padding error only. If CMS_decrypt() just returned an
error when all recipient encrypted keys failed to decrypt an attacker could
use this in a timing attack. If the special flag B<CMS_DEBUG_DECRYPT> is set
then the above behaviour is modified and an error B<is> returned if no
recipient encrypted key can be decrypted B<without> generating a random
content encryption key. Applications should use this flag with
B<extreme caution> especially in automated gateways as it can leave them
open to attack.

It is possible to determine the correct recipient key by other means (for
example looking them up in a database) and setting them in the CMS structure
in advance using the CMS utility functions such as CMS_set1_pkey(),
or use CMS_decrypt_set1_password() if the recipient has a symmetric key.
In these cases both I<cert> and I<pkey> should be set to NULL.

To process KEKRecipientInfo types CMS_set1_key() or CMS_RecipientInfo_set0_key()
and CMS_RecipientInfo_decrypt() should be called before CMS_decrypt() and
I<cert> and I<pkey> set to NULL.

The following flags can be passed in the I<flags> parameter.

If the B<CMS_TEXT> flag is set MIME headers for type C<text/plain> are deleted
from the content. If the content is not of type C<text/plain> then an error is
returned.

=head1 RETURN VALUES

CMS_decrypt(), CMS_decrypt_set1_pkey_and_peer(),
CMS_decrypt_set1_pkey(), and CMS_decrypt_set1_password()
return either 1 for success or 0 for failure.
The error can be obtained from ERR_get_error(3).

=head1 BUGS

The B<set1_> part of these function names is misleading
and should better read: B<with_>.

The lack of single pass processing and the need to hold all data in memory as
mentioned in CMS_verify() also applies to CMS_decrypt().

=head1 SEE ALSO

L<ERR_get_error(3)>, L<CMS_encrypt(3)>

=head1 HISTORY

CMS_decrypt_set1_pkey_and_peer() and CMS_decrypt_set1_password()
were added in OpenSSL 3.0.

=head1 COPYRIGHT

Copyright 2008-2023 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
