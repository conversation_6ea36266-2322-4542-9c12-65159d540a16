=pod

=head1 NAME

DSA_size, DSA_bits, DSA_security_bits - get DSA signature size, key bits or security bits

=head1 SYNOPSIS

 #include <openssl/dsa.h>

The following functions have been deprecated since OpenSSL 3.0, and can be
hidden entirely by defining B<OPENSSL_API_COMPAT> with a suitable version value,
see L<openssl_user_macros(7)>:

 int DSA_bits(const DSA *dsa);

 int DSA_size(const DSA *dsa);

 int DSA_security_bits(const DSA *dsa);

=head1 DESCRIPTION

All of the functions described on this page are deprecated.
Applications should instead use L<EVP_PKEY_get_bits(3)>,
L<EVP_PKEY_get_security_bits(3)> and L<EVP_PKEY_get_size(3)>.

DSA_bits() returns the number of bits in key I<dsa>: this is the number
of bits in the I<p> parameter.

DSA_size() returns the maximum size of an ASN.1 encoded DSA signature
for key I<dsa> in bytes. It can be used to determine how much memory must
be allocated for a DSA signature.

DSA_security_bits() returns the number of security bits of the given I<dsa>
key. See L<BN_security_bits(3)>.

=head1 RETURN VALUES

DSA_security_bits() returns the number of security bits in the key, or -1 if
I<dsa> doesn't hold any key parameters.

DSA_bits() returns the number of bits in the key, or -1 if I<dsa> doesn't
hold any key parameters.

DSA_size() returns the signature size in bytes, or -1 if I<dsa> doesn't
hold any key parameters.

=head1 SEE ALSO

L<EVP_PKEY_get_bits(3)>,
L<EVP_PKEY_get_security_bits(3)>,
L<EVP_PKEY_get_size(3)>,
L<DSA_new(3)>, L<DSA_sign(3)>

=head1 HISTORY

All of these functions were deprecated in OpenSSL 3.0.

=head1 COPYRIGHT

Copyright 2000-2021 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
