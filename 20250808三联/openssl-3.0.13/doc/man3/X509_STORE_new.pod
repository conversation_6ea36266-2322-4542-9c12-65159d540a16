=pod

=head1 NAME

X509_STORE_new, X509_STORE_up_ref, X509_STORE_free,
X509_STORE_lock,X509_STORE_unlock
- X509_STORE allocation, freeing and locking functions

=head1 SYNOPSIS

 #include <openssl/x509_vfy.h>

 X509_STORE *X509_STORE_new(void);
 void X509_STORE_free(X509_STORE *v);
 int X509_STORE_lock(X509_STORE *v);
 int X509_STORE_unlock(X509_STORE *v);
 int X509_STORE_up_ref(X509_STORE *v);

=head1 DESCRIPTION

The X509_STORE_new() function returns a new X509_STORE.

X509_STORE_up_ref() increments the reference count associated with the
X509_STORE object.

X509_STORE_lock() locks the store from modification by other threads,
X509_STORE_unlock() unlocks it.

X509_STORE_free() frees up a single X509_STORE object.

=head1 RETURN VALUES

X509_STORE_new() returns a newly created X509_STORE or NULL if the call fails.

X509_STORE_up_ref(), X509_STORE_lock() and X509_STORE_unlock() return
1 for success and 0 for failure.

X509_STORE_free() does not return values.

=head1 SEE ALSO

L<X509_STORE_set_verify_cb_func(3)>
L<X509_STORE_get0_param(3)>

=head1 HISTORY

The X509_STORE_up_ref(), X509_STORE_lock() and X509_STORE_unlock()
functions were added in OpenSSL 1.1.0.

=head1 COPYRIGHT

Copyright 2016-2020 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
