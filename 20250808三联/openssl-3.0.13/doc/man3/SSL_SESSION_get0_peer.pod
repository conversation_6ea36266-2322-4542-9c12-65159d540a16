=pod

=head1 NAME

SSL_SESSION_get0_peer
- get details about peer's certificate for a session

=head1 SYNOPSIS

 #include <openssl/ssl.h>

 X509 *SSL_SESSION_get0_peer(SSL_SESSION *s);

=head1 DESCRIPTION

SSL_SESSION_get0_peer() returns the peer certificate associated with the session
B<s> or NULL if no peer certificate is available. The caller should not free the
returned value (unless L<X509_up_ref(3)> has also been called).

=head1 RETURN VALUES

SSL_SESSION_get0_peer() returns a pointer to the peer certificate or NULL if
no peer certificate is available.

=head1 SEE ALSO

L<ssl(7)>

=head1 COPYRIGHT

Copyright 2017 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
