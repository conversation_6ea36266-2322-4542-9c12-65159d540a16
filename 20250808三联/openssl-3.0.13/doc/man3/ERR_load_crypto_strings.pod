=pod

=head1 NAME

ERR_load_crypto_strings, SSL_load_error_strings, ERR_free_strings -
load and free error strings

=head1 SYNOPSIS

The following functions have been deprecated since OpenSSL 1.1.0, and can be
hidden entirely by defining B<OPENSSL_API_COMPAT> with a suitable version value,
see L<openssl_user_macros(7)>:

 #include <openssl/err.h>

 void ERR_load_crypto_strings(void);
 void ERR_free_strings(void);

 #include <openssl/ssl.h>

 void SSL_load_error_strings(void);

=head1 DESCRIPTION

ERR_load_crypto_strings() registers the error strings for all
B<libcrypto> functions. SSL_load_error_strings() does the same,
but also registers the B<libssl> error strings.

In versions prior to OpenSSL 1.1.0,
ERR_free_strings() releases any resources created by the above functions.

=head1 RETURN VALUES

ERR_load_crypto_strings(), SSL_load_error_strings() and
ERR_free_strings() return no values.

=head1 SEE ALSO

L<ERR_error_string(3)>

=head1 HISTORY

The ERR_load_crypto_strings(), SSL_load_error_strings(), and
ERR_free_strings() functions were deprecated in OpenSSL 1.1.0 by
OPENSSL_init_crypto() and OPENSSL_init_ssl() and should not be used.

=head1 COPYRIGHT

Copyright 2000-2021 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
