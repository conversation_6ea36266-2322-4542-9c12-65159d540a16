=pod

=head1 NAME

DH_generate_key, DH_compute_key, DH_compute_key_padded - perform
Diffie-<PERSON><PERSON> key exchange

=head1 SYNOPSIS

 #include <openssl/dh.h>

The following functions have been deprecated since OpenSSL 3.0, and can be
hidden entirely by defining B<OPENSSL_API_COMPAT> with a suitable version value,
see L<openssl_user_macros(7)>:

 int DH_generate_key(DH *dh);

 int DH_compute_key(unsigned char *key, const BIGNUM *pub_key, DH *dh);

 int DH_compute_key_padded(unsigned char *key, const BIGNUM *pub_key, DH *dh);

=head1 DESCRIPTION

All of the functions described on this page are deprecated.
Applications should instead use L<EVP_PKEY_derive_init(3)>
and L<EVP_PKEY_derive(3)>.

DH_generate_key() performs the first step of a Diffie-<PERSON><PERSON> key
exchange by generating private and public DH values. By calling
DH_compute_key() or DH_compute_key_padded(), these are combined with
the other party's public value to compute the shared key.

DH_generate_key() expects B<dh> to contain the shared parameters
B<dh-E<gt>p> and B<dh-E<gt>g>. It generates a random private DH value
unless B<dh-E<gt>priv_key> is already set, and computes the
corresponding public value B<dh-E<gt>pub_key>, which can then be
published.

DH_compute_key() computes the shared secret from the private DH value
in B<dh> and the other party's public value in B<pub_key> and stores
it in B<key>. B<key> must point to B<DH_size(dh)> bytes of memory.
The padding style is RFC 5246 (8.1.2) that strips leading zero bytes.
It is not constant time due to the leading zero bytes being stripped.
The return value should be considered public.

DH_compute_key_padded() is similar but stores a fixed number of bytes.
The padding style is NIST SP 800-56A (C.1) that retains leading zero bytes.
It is constant time due to the leading zero bytes being retained.
The return value should be considered public.

=head1 RETURN VALUES

DH_generate_key() returns 1 on success, 0 otherwise.

DH_compute_key() returns the size of the shared secret on success, -1
on error.

DH_compute_key_padded() returns B<DH_size(dh)> on success, -1 on error.

The error codes can be obtained by L<ERR_get_error(3)>.

=head1 SEE ALSO

L<EVP_PKEY_derive(3)>,
L<DH_new(3)>, L<ERR_get_error(3)>, L<RAND_bytes(3)>, L<DH_size(3)>

=head1 HISTORY

DH_compute_key_padded() was added in OpenSSL 1.0.2.

All of these functions were deprecated in OpenSSL 3.0.

=head1 COPYRIGHT

Copyright 2000-2021 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
