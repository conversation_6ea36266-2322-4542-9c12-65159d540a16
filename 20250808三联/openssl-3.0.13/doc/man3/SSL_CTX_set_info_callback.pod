=pod

=head1 NAME

SSL_CTX_set_info_callback,
SSL_CTX_get_info_callback,
SSL_set_info_callback,
SSL_get_info_callback
- handle information callback for SSL connections

=head1 SYNOPSIS

 #include <openssl/ssl.h>

 void SSL_CTX_set_info_callback(SSL_CTX *ctx,
                                void (*callback) (const SSL *ssl, int type, int val));

 void (*SSL_CTX_get_info_callback(SSL_CTX *ctx)) (const SSL *ssl, int type, int val);

 void SSL_set_info_callback(SSL *ssl,
                            void (*callback) (const SSL *ssl, int type, int val));

 void (*SSL_get_info_callback(const SSL *ssl)) (const SSL *ssl, int type, int val);

=head1 DESCRIPTION

SSL_CTX_set_info_callback() sets the B<callback> function, that can be used to
obtain state information for SSL objects created from B<ctx> during connection
setup and use. The setting for B<ctx> is overridden from the setting for
a specific SSL object, if specified.
When B<callback> is NULL, no callback function is used.

SSL_set_info_callback() sets the B<callback> function, that can be used to
obtain state information for B<ssl> during connection setup and use.
When B<callback> is NULL, the callback setting currently valid for
B<ctx> is used.

SSL_CTX_get_info_callback() returns a pointer to the currently set information
callback function for B<ctx>.

SSL_get_info_callback() returns a pointer to the currently set information
callback function for B<ssl>.

=head1 NOTES

When setting up a connection and during use, it is possible to obtain state
information from the SSL/TLS engine. When set, an information callback function
is called whenever a significant event occurs such as: the state changes,
an alert appears, or an error occurs.

The callback function is called as B<callback(SSL *ssl, int where, int ret)>.
The B<where> argument specifies information about where (in which context)
the callback function was called. If B<ret> is 0, an error condition occurred.
If an alert is handled, SSL_CB_ALERT is set and B<ret> specifies the alert
information.

B<where> is a bit-mask made up of the following bits:

=over 4

=item SSL_CB_LOOP

Callback has been called to indicate state change or some other significant
state machine event. This may mean that the callback gets invoked more than once
per state in some situations.

=item SSL_CB_EXIT

Callback has been called to indicate exit of a handshake function. This will
happen after the end of a handshake, but may happen at other times too such as
on error or when IO might otherwise block and nonblocking is being used.

=item SSL_CB_READ

Callback has been called during read operation.

=item SSL_CB_WRITE

Callback has been called during write operation.

=item SSL_CB_ALERT

Callback has been called due to an alert being sent or received.

=item SSL_CB_READ_ALERT               (SSL_CB_ALERT|SSL_CB_READ)

=item SSL_CB_WRITE_ALERT              (SSL_CB_ALERT|SSL_CB_WRITE)

=item SSL_CB_ACCEPT_LOOP              (SSL_ST_ACCEPT|SSL_CB_LOOP)

=item SSL_CB_ACCEPT_EXIT              (SSL_ST_ACCEPT|SSL_CB_EXIT)

=item SSL_CB_CONNECT_LOOP             (SSL_ST_CONNECT|SSL_CB_LOOP)

=item SSL_CB_CONNECT_EXIT             (SSL_ST_CONNECT|SSL_CB_EXIT)

=item SSL_CB_HANDSHAKE_START

Callback has been called because a new handshake is started. It also occurs when
resuming a handshake following a pause to handle early data.

=item SSL_CB_HANDSHAKE_DONE

Callback has been called because a handshake is finished.  It also occurs if the
handshake is paused to allow the exchange of early data.

=back

The current state information can be obtained using the
L<SSL_state_string(3)> family of functions.

The B<ret> information can be evaluated using the
L<SSL_alert_type_string(3)> family of functions.

=head1 RETURN VALUES

SSL_set_info_callback() does not provide diagnostic information.

SSL_get_info_callback() returns the current setting.

=head1 EXAMPLES

The following example callback function prints state strings, information
about alerts being handled and error messages to the B<bio_err> BIO.

 void apps_ssl_info_callback(const SSL *s, int where, int ret)
 {
     const char *str;
     int w = where & ~SSL_ST_MASK;

     if (w & SSL_ST_CONNECT)
         str = "SSL_connect";
     else if (w & SSL_ST_ACCEPT)
         str = "SSL_accept";
     else
         str = "undefined";

     if (where & SSL_CB_LOOP) {
         BIO_printf(bio_err, "%s:%s\n", str, SSL_state_string_long(s));
     } else if (where & SSL_CB_ALERT) {
         str = (where & SSL_CB_READ) ? "read" : "write";
         BIO_printf(bio_err, "SSL3 alert %s:%s:%s\n", str,
                    SSL_alert_type_string_long(ret),
                    SSL_alert_desc_string_long(ret));
     } else if (where & SSL_CB_EXIT) {
         if (ret == 0) {
             BIO_printf(bio_err, "%s:failed in %s\n",
                        str, SSL_state_string_long(s));
         } else if (ret < 0) {
             BIO_printf(bio_err, "%s:error in %s\n",
                        str, SSL_state_string_long(s));
         }
     }
 }

=head1 SEE ALSO

L<ssl(7)>, L<SSL_state_string(3)>,
L<SSL_alert_type_string(3)>

=head1 COPYRIGHT

Copyright 2001-2023 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
