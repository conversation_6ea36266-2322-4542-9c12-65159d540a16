=pod

=head1 NAME

ASN1_INTEGER_get_uint64, ASN1_INTEGER_set_uint64,
ASN1_INTEGER_get_int64, ASN1_INTEGER_get, ASN1_INTEGER_set_int64, ASN1_INTEGER_set, BN_to_ASN1_INTEGER, ASN1_INTEGER_to_BN, ASN1_ENUMERATED_get_int64, ASN1_ENUMERATED_get, ASN1_ENUMERATED_set_int64, ASN1_ENUMERATED_set, BN_to_ASN1_ENUMERATED, ASN1_ENUMERATED_to_BN
- ASN.1 INTEGER and ENUMERATED utilities

=head1 SYNOPSIS

 #include <openssl/asn1.h>

 int ASN1_INTEGER_get_int64(int64_t *pr, const ASN1_INTEGER *a);
 long ASN1_INTEGER_get(const ASN1_INTEGER *a);

 int ASN1_INTEGER_set_int64(ASN1_INTEGER *a, int64_t r);
 int ASN1_INTEGER_set(ASN1_INTEGER *a, long v);

 int ASN1_INTEGER_get_uint64(uint64_t *pr, const ASN1_INTEGER *a);
 int ASN1_INTEGER_set_uint64(ASN1_INTEGER *a, uint64_t r);

 ASN1_INTEGER *BN_to_ASN1_INTEGER(const BIGNUM *bn, ASN1_INTEGER *ai);
 BIGNUM *ASN1_INTEGER_to_BN(const ASN1_INTEGER *ai, BIGNUM *bn);

 int ASN1_ENUMERATED_get_int64(int64_t *pr, const ASN1_ENUMERATED *a);
 long ASN1_ENUMERATED_get(const ASN1_ENUMERATED *a);

 int ASN1_ENUMERATED_set_int64(ASN1_ENUMERATED *a, int64_t r);
 int ASN1_ENUMERATED_set(ASN1_ENUMERATED *a, long v);

 ASN1_ENUMERATED *BN_to_ASN1_ENUMERATED(const BIGNUM *bn, ASN1_ENUMERATED *ai);
 BIGNUM *ASN1_ENUMERATED_to_BN(const ASN1_ENUMERATED *ai, BIGNUM *bn);

=head1 DESCRIPTION

These functions convert to and from B<ASN1_INTEGER> and B<ASN1_ENUMERATED>
structures.

ASN1_INTEGER_get_int64() converts an B<ASN1_INTEGER> into an B<int64_t> type
If successful it returns 1 and sets I<*pr> to the value of I<a>. If it fails
(due to invalid type or the value being too big to fit into an B<int64_t> type)
it returns 0.

ASN1_INTEGER_get_uint64() is similar to ASN1_INTEGER_get_int64_t() except it
converts to a B<uint64_t> type and an error is returned if the passed integer
is negative.

ASN1_INTEGER_get() also returns the value of I<a> but it returns 0 if I<a> is
NULL and -1 on error (which is ambiguous because -1 is a legitimate value for
an B<ASN1_INTEGER>). New applications should use ASN1_INTEGER_get_int64()
instead.

ASN1_INTEGER_set_int64() sets the value of B<ASN1_INTEGER> I<a> to the
B<int64_t> value I<r>.

ASN1_INTEGER_set_uint64() sets the value of B<ASN1_INTEGER> I<a> to the
B<uint64_t> value I<r>.

ASN1_INTEGER_set() sets the value of B<ASN1_INTEGER> I<a> to the I<long> value
I<v>.

BN_to_ASN1_INTEGER() converts B<BIGNUM> I<bn> to an B<ASN1_INTEGER>. If I<ai>
is NULL a new B<ASN1_INTEGER> structure is returned. If I<ai> is not NULL then
the existing structure will be used instead.

ASN1_INTEGER_to_BN() converts ASN1_INTEGER I<ai> into a B<BIGNUM>. If I<bn> is
NULL a new B<BIGNUM> structure is returned. If I<bn> is not NULL then the
existing structure will be used instead.

ASN1_ENUMERATED_get_int64(), ASN1_ENUMERATED_set_int64(),
ASN1_ENUMERATED_set(), BN_to_ASN1_ENUMERATED() and ASN1_ENUMERATED_to_BN()
behave in an identical way to their ASN1_INTEGER counterparts except they
operate on an B<ASN1_ENUMERATED> value.

ASN1_ENUMERATED_get() returns the value of I<a> in a similar way to
ASN1_INTEGER_get() but it returns B<0xffffffffL> if the value of I<a> will not
fit in a long type. New applications should use ASN1_ENUMERATED_get_int64()
instead.

=head1 NOTES

In general an B<ASN1_INTEGER> or B<ASN1_ENUMERATED> type can contain an
integer of almost arbitrary size and so cannot always be represented by a C
B<int64_t> type. However, in many cases (for example version numbers) they
represent small integers which can be more easily manipulated if converted to
an appropriate C integer type.

=head1 BUGS

The ambiguous return values of ASN1_INTEGER_get() and ASN1_ENUMERATED_get()
mean these functions should be avoided if possible. They are retained for
compatibility. Normally the ambiguous return values are not legitimate
values for the fields they represent.

=head1 RETURN VALUES

ASN1_INTEGER_set_int64(), ASN1_INTEGER_set(), ASN1_ENUMERATED_set_int64() and
ASN1_ENUMERATED_set() return 1 for success and 0 for failure. They will only
fail if a memory allocation error occurs.

ASN1_INTEGER_get_int64() and ASN1_ENUMERATED_get_int64() return 1 for success
and 0 for failure. They will fail if the passed type is incorrect (this will
only happen if there is a programming error) or if the value exceeds the range
of an B<int64_t> type.

BN_to_ASN1_INTEGER() and BN_to_ASN1_ENUMERATED() return an B<ASN1_INTEGER> or
B<ASN1_ENUMERATED> structure respectively or NULL if an error occurs. They will
only fail due to a memory allocation error.

ASN1_INTEGER_to_BN() and ASN1_ENUMERATED_to_BN() return a B<BIGNUM> structure
of NULL if an error occurs. They can fail if the passed type is incorrect
(due to programming error) or due to a memory allocation failure.

=head1 SEE ALSO

L<ERR_get_error(3)>

=head1 HISTORY

ASN1_INTEGER_set_int64(), ASN1_INTEGER_get_int64(),
ASN1_ENUMERATED_set_int64() and ASN1_ENUMERATED_get_int64()
were added in OpenSSL 1.1.0.

=head1 COPYRIGHT

Copyright 2015-2021 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
