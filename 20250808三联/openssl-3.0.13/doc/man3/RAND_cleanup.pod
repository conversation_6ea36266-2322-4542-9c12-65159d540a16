=pod

=head1 NAME

RAND_cleanup - erase the PRNG state

=head1 SYNOPSIS

 #include <openssl/rand.h>

The following function has been deprecated since OpenSSL 1.1.0, and can be
hidden entirely by defining B<OPENSSL_API_COMPAT> with a suitable version value,
see L<openssl_user_macros(7)>:

 void RAND_cleanup(void);

=head1 DESCRIPTION

Prior to OpenSSL 1.1.0, RAND_cleanup() released all resources used by
the PRNG.  As of version 1.1.0, it does nothing and should not be called,
since no explicit initialisation or de-initialisation is necessary. See
L<OPENSSL_init_crypto(3)>.

=head1 RETURN VALUES

RAND_cleanup() returns no value.

=head1 SEE ALSO

L<RAND(7)>

=head1 HISTORY

RAND_cleanup() was deprecated in OpenSSL 1.1.0; do not use it.
See L<OPENSSL_init_crypto(3)>

=head1 COPYRIGHT

Copyright 2000-2021 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
