=pod

=head1 NAME

SSL_SESSION_is_resumable
- determine whether an SSL_SESSION object can be used for resumption

=head1 SYNOPSIS

 #include <openssl/ssl.h>

 int SSL_SESSION_is_resumable(const SSL_SESSION *s);

=head1 DESCRIPTION

SSL_SESSION_is_resumable() determines whether an SSL_SESSION object can be used
to resume a session or not. Returns 1 if it can or 0 if not. Note that
attempting to resume with a non-resumable session will result in a full
handshake.

=head1 RETURN VALUES

SSL_SESSION_is_resumable() returns 1 if the session is resumable or 0 otherwise.

=head1 SEE ALSO

L<ssl(7)>,
L<SSL_get_session(3)>,
L<SSL_CTX_sess_set_new_cb(3)>

=head1 HISTORY

The SSL_SESSION_is_resumable() function was added in OpenSSL 1.1.1.

=head1 COPYRIGHT

Copyright 2017-2018 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
