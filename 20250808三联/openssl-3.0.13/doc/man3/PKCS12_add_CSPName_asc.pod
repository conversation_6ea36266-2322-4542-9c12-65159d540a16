=pod

=head1 NAME

PKCS12_add_CSPName_asc - Add a Microsoft CSP Name attribute to a PKCS#12 safeBag

=head1 SYNOPSIS

 #include <openssl/pkcs12.h>

 int PKCS12_add_CSPName_asc(PKCS12_SAFEBAG *bag, const char *name, int namelen);

=head1 DESCRIPTION

PKCS12_add_CSPName_asc() adds an ASCII string representation of the Microsoft CSP Name attribute to a PKCS#12 safeBag.

I<bag> is the B<PKCS12_SAFEBAG> to add the attribute to.

=head1 RETURN VALUES

Returns 1 for success or 0 for failure.

=head1 SEE ALSO

L<PKCS12_add_friendlyname_asc(3)>

=head1 COPYRIGHT

Copyright 2019 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
