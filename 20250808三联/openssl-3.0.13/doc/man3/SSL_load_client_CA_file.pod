=pod

=head1 NAME

SSL_load_client_CA_file_ex, SSL_load_client_CA_file,
SSL_add_file_cert_subjects_to_stack,
SSL_add_dir_cert_subjects_to_stack,
SSL_add_store_cert_subjects_to_stack
- load certificate names

=head1 SYNOPSIS

 #include <openssl/ssl.h>

 STACK_OF(X509_NAME) *SSL_load_client_CA_file_ex(const char *file,
                                                 OSSL_LIB_CTX *libctx,
                                                 const char *propq);
 STACK_OF(X509_NAME) *SSL_load_client_CA_file(const char *file);

 int SSL_add_file_cert_subjects_to_stack(STACK_OF(X509_NAME) *stack,
                                         const char *file);
 int SSL_add_dir_cert_subjects_to_stack(STACK_OF(X509_NAME) *stack,
                                        const char *dir);
 int SSL_add_store_cert_subjects_to_stack(STACK_OF(X509_NAME) *stack,
                                          const char *store);

=head1 DESCRIPTION

SSL_load_client_CA_file_ex() reads certificates from I<file> and returns
a STACK_OF(X509_NAME) with the subject names found. The library context I<libctx>
and property query I<propq> are used when fetching algorithms from providers.

SSL_load_client_CA_file() is similar to SSL_load_client_CA_file_ex()
but uses NULL for the library context I<libctx> and property query I<propq>.

SSL_add_file_cert_subjects_to_stack() reads certificates from I<file>,
and adds their subject name to the already existing I<stack>.

SSL_add_dir_cert_subjects_to_stack() reads certificates from every
file in the directory I<dir>, and adds their subject name to the
already existing I<stack>.

SSL_add_store_cert_subjects_to_stack() loads certificates from the
I<store> URI, and adds their subject name to the already existing
I<stack>.

=head1 NOTES

SSL_load_client_CA_file() reads a file of PEM formatted certificates and
extracts the X509_NAMES of the certificates found. While the name suggests
the specific usage as support function for
L<SSL_CTX_set_client_CA_list(3)>,
it is not limited to CA certificates.

=head1 RETURN VALUES

The following return values can occur:

=over 4

=item NULL

The operation failed, check out the error stack for the reason.

=item Pointer to STACK_OF(X509_NAME)

Pointer to the subject names of the successfully read certificates.

=back

=head1 EXAMPLES

Load names of CAs from file and use it as a client CA list:

 SSL_CTX *ctx;
 STACK_OF(X509_NAME) *cert_names;

 ...
 cert_names = SSL_load_client_CA_file("/path/to/CAfile.pem");
 if (cert_names != NULL)
     SSL_CTX_set_client_CA_list(ctx, cert_names);
 else
     /* error */
 ...

=head1 SEE ALSO

L<ssl(7)>,
L<ossl_store(7)>,
L<SSL_CTX_set_client_CA_list(3)>

=head1 HISTORY

SSL_load_client_CA_file_ex() and SSL_add_store_cert_subjects_to_stack()
were added in OpenSSL 3.0.

=head1 COPYRIGHT

Copyright 2000-2021 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
