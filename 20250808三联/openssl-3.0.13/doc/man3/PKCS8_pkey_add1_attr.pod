=pod

=head1 NAME

PKCS8_pkey_get0_attrs, PKCS8_pkey_add1_attr, PKCS8_pkey_add1_attr_by_NID, PKCS8_pkey_add1_attr_by_OBJ - PKCS8 attribute functions

=head1 SYNOPSIS

 #include <openssl/x509.h>

 const STACK_OF(X509_ATTRIBUTE) *
 PKCS8_pkey_get0_attrs(const PKCS8_PRIV_KEY_INFO *p8);
 int PKCS8_pkey_add1_attr(PKCS8_PRIV_KEY_INFO *p8, X509_ATTRIBUTE *attr);
 int PKCS8_pkey_add1_attr_by_NID(PKCS8_PRIV_KEY_INFO *p8, int nid, int type,
                                 const unsigned char *bytes, int len);
 int PKCS8_pkey_add1_attr_by_OBJ(PKCS8_PRIV_KEY_INFO *p8, const ASN1_OBJECT *obj,
                                int type, const unsigned char *bytes, int len);

=head1 DESCRIPTION

PKCS8_pkey_get0_attrs() returns a const STACK of X509_ATTRIBUTE present in
the passed const PKCS8_PRIV_KEY_INFO structure B<p8>.

PKCS8_pkey_add1_attr() adds a constructed X509_ATTRIBUTE B<attr> to the
existing PKCS8_PRIV_KEY_INFO structure B<p8>.

PKCS8_pkey_add1_attr_by_NID() and PKCS8_pkey_add1_attr_by_OBJ() construct a new
X509_ATTRIBUTE from the passed arguments and add it to the existing
PKCS8_PRIV_KEY_INFO structure B<p8>.

=head1 RETURN VALUES

PKCS8_pkey_add1_attr(), PKCS8_pkey_add1_attr_by_NID(), and
PKCS8_pkey_add1_attr_by_OBJ() return 1 for success and 0 for failure.

=head1 NOTES

STACK of X509_ATTRIBUTE is present in many X509-related structures and some of
them have the corresponding set of similar functions.

=head1 SEE ALSO

L<crypto(7)>

=head1 COPYRIGHT

Copyright 2020 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
