=pod

=head1 NAME

DH_generate_parameters_ex, DH_generate_parameters,
DH_check, DH_check_params,
DH_check_ex, DH_check_params_ex, DH_check_pub_key_ex
- generate and check Di<PERSON>ie-<PERSON><PERSON>
parameters

=head1 SYNOPSIS

 #include <openssl/dh.h>

The following functions have been deprecated since OpenSSL 3.0, and can be
hidden entirely by defining B<OPENSSL_API_COMPAT> with a suitable version value,
see L<openssl_user_macros(7)>:

 int DH_generate_parameters_ex(DH *dh, int prime_len, int generator, BN_GENCB *cb);

 int DH_check(DH *dh, int *codes);
 int DH_check_params(DH *dh, int *codes);

 int DH_check_ex(const DH *dh);
 int DH_check_params_ex(const DH *dh);
 int DH_check_pub_key_ex(const DH *dh, const BIGNUM *pub_key);

The following functions have been deprecated since OpenSSL 0.9.8, and can be
hidden entirely by defining B<OPENSSL_API_COMPAT> with a suitable version value,
see L<openssl_user_macros(7)>:

 DH *DH_generate_parameters(int prime_len, int generator,
                            void (*callback)(int, int, void *), void *cb_arg);

=head1 DESCRIPTION

All of the functions described on this page are deprecated.
Applications should instead use L<EVP_PKEY_check(3)>,
L<EVP_PKEY_public_check(3)>, L<EVP_PKEY_private_check(3)> and
L<EVP_PKEY_param_check(3)>.

DH_generate_parameters_ex() generates Diffie-Hellman parameters that can
be shared among a group of users, and stores them in the provided B<DH>
structure. The pseudo-random number generator must be
seeded before calling it.
The parameters generated by DH_generate_parameters_ex() should not be used in
signature schemes.

B<prime_len> is the length in bits of the safe prime to be generated.
B<generator> is a small number E<gt> 1, typically 2 or 5.

A callback function may be used to provide feedback about the progress
of the key generation. If B<cb> is not B<NULL>, it will be
called as described in L<BN_generate_prime(3)> while a random prime
number is generated, and when a prime has been found, B<BN_GENCB_call(cb, 3, 0)>
is called. See L<BN_generate_prime_ex(3)> for information on
the BN_GENCB_call() function.

DH_generate_parameters() is similar to DH_generate_prime_ex() but
expects an old-style callback function; see
L<BN_generate_prime(3)> for information on the old-style callback.

DH_check_params() confirms that the B<p> and B<g> are likely enough to
be valid.
This is a lightweight check, if a more thorough check is needed, use
DH_check().
The value of B<*codes> is updated with any problems found.
If B<*codes> is zero then no problems were found, otherwise the
following bits may be set:

=over 4

=item DH_CHECK_P_NOT_PRIME

The parameter B<p> has been determined to not being an odd prime.
Note that the lack of this bit doesn't guarantee that B<p> is a
prime.

=item DH_NOT_SUITABLE_GENERATOR

The generator B<g> is not suitable.
Note that the lack of this bit doesn't guarantee that B<g> is
suitable, unless B<p> is known to be a strong prime.

=item DH_MODULUS_TOO_SMALL

The modulus is too small.

=item DH_MODULUS_TOO_LARGE

The modulus is too large.

=back

DH_check() confirms that the Diffie-Hellman parameters B<dh> are valid. The
value of B<*codes> is updated with any problems found. If B<*codes> is zero then
no problems were found, otherwise the following bits may be set:

=over 4

=item DH_CHECK_P_NOT_PRIME

The parameter B<p> is not prime.

=item DH_CHECK_P_NOT_SAFE_PRIME

The parameter B<p> is not a safe prime and no B<q> value is present.

=item DH_UNABLE_TO_CHECK_GENERATOR

The generator B<g> cannot be checked for suitability.

=item DH_NOT_SUITABLE_GENERATOR

The generator B<g> is not suitable.

=item DH_CHECK_Q_NOT_PRIME

The parameter B<q> is not prime.

=item DH_CHECK_INVALID_Q_VALUE

The parameter B<q> is invalid.

=item DH_CHECK_INVALID_J_VALUE

The parameter B<j> is invalid.

=back

If 0 is returned or B<*codes> is set to a nonzero value the supplied
parameters should not be used for Diffie-Hellman operations otherwise
the security properties of the key exchange are not guaranteed.

DH_check_ex(), DH_check_params() and DH_check_pub_key_ex() are similar to
DH_check() and DH_check_params() respectively, but the error reasons are added
to the thread's error queue instead of provided as return values from the
function.

=head1 RETURN VALUES

DH_generate_parameters_ex(), DH_check() and DH_check_params() return 1
if the check could be performed, 0 otherwise.

DH_generate_parameters() returns a pointer to the DH structure or NULL if
the parameter generation fails.

DH_check_ex(), DH_check_params() and DH_check_pub_key_ex() return 1 if the
check is successful, 0 for failed.

The error codes can be obtained by L<ERR_get_error(3)>.

=head1 SEE ALSO

L<DH_new(3)>, L<ERR_get_error(3)>, L<RAND_bytes(3)>,
L<DH_free(3)>

=head1 HISTORY

All of these functions were deprecated in OpenSSL 3.0.

DH_generate_parameters() was deprecated in OpenSSL 0.9.8; use
DH_generate_parameters_ex() instead.

=head1 COPYRIGHT

Copyright 2000-2023 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
