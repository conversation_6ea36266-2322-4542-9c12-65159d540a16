=pod

=head1 NAME

X509_CRL_get0_by_serial, X509_CRL_get0_by_cert, X509_CRL_get_REVOKED,
X509_REVOKED_get0_serialNumber, X509_REVOKED_get0_revocationDate,
X509_REVOKED_set_serialNumber, X509_REVOKED_set_revocationDate,
X509_CRL_add0_revoked, X509_CRL_sort - CRL revoked entry utility
functions

=head1 SYNOPSIS

 #include <openssl/x509.h>

 int X509_CRL_get0_by_serial(X509_CRL *crl,
                             X509_REVOKED **ret, const ASN1_INTEGER *serial);
 int X509_CRL_get0_by_cert(X509_CRL *crl, X509_REVOKED **ret, X509 *x);

 STACK_OF(X509_REVOKED) *X509_CRL_get_REVOKED(X509_CRL *crl);

 const ASN1_INTEGER *X509_REVOKED_get0_serialNumber(const X509_REVOKED *r);
 const ASN1_TIME *X509_REVOKED_get0_revocationDate(const X509_REVOKED *r);

 int X509_REVOKED_set_serialNumber(X509_REVOKED *r, ASN1_INTEGER *serial);
 int X509_REVOKED_set_revocationDate(X509_REVOKED *r, ASN1_TIME *tm);

 int X509_CRL_add0_revoked(X509_CRL *crl, X509_REVOKED *rev);

 int X509_CRL_sort(X509_CRL *crl);

=head1 DESCRIPTION

X509_CRL_get0_by_serial() attempts to find a revoked entry in I<crl> for
serial number I<serial>. If it is successful, it sets I<*ret> to the internal
pointer of the matching entry. As a result, I<*ret> B<MUST NOT> be freed
after the call.

X509_CRL_get0_by_cert() is similar to X509_get0_by_serial() except it
looks for a revoked entry using the serial number of certificate I<x>.

X509_CRL_get_REVOKED() returns an internal pointer to a STACK of all
revoked entries for I<crl>.

X509_REVOKED_get0_serialNumber() returns an internal pointer to the
serial number of I<r>.

X509_REVOKED_get0_revocationDate() returns an internal pointer to the
revocation date of I<r>.

X509_REVOKED_set_serialNumber() sets the serial number of I<r> to I<serial>.
The supplied I<serial> pointer is not used internally so it should be
freed after use.

X509_REVOKED_set_revocationDate() sets the revocation date of I<r> to
I<tm>. The supplied I<tm> pointer is not used internally so it should be
freed after use.

X509_CRL_add0_revoked() appends revoked entry I<rev> to CRL I<crl>. The
pointer I<rev> is used internally so it B<MUST NOT> be freed after the call:
it is freed when the parent CRL is freed.

X509_CRL_sort() sorts the revoked entries of I<crl> into ascending serial
number order.

=head1 NOTES

Applications can determine the number of revoked entries returned by
X509_CRL_get_REVOKED() using sk_X509_REVOKED_num() and examine each one
in turn using sk_X509_REVOKED_value().

=head1 RETURN VALUES

X509_CRL_get0_by_serial() and X509_CRL_get0_by_cert() return 0 for failure,
1 on success except if the revoked entry has the reason C<removeFromCRL> (8),
in which case 2 is returned.

X509_CRL_get_REVOKED() returns a STACK of revoked entries.

X509_REVOKED_get0_serialNumber() returns an B<ASN1_INTEGER> structure.

X509_REVOKED_get0_revocationDate() returns an B<ASN1_TIME> structure.

X509_REVOKED_set_serialNumber(), X509_REVOKED_set_revocationDate(),
X509_CRL_add0_revoked() and X509_CRL_sort() return 1 for success and 0 for
failure.

=head1 SEE ALSO

L<d2i_X509(3)>,
L<ERR_get_error(3)>,
L<X509_get0_signature(3)>,
L<X509_get_ext_d2i(3)>,
L<X509_get_extension_flags(3)>,
L<X509_get_pubkey(3)>,
L<X509_get_subject_name(3)>,
L<X509_get_version(3)>,
L<X509_NAME_add_entry_by_txt(3)>,
L<X509_NAME_ENTRY_get_object(3)>,
L<X509_NAME_get_index_by_NID(3)>,
L<X509_NAME_print_ex(3)>,
L<X509_new(3)>,
L<X509_sign(3)>,
L<X509V3_get_d2i(3)>,
L<X509_verify_cert(3)>

=head1 COPYRIGHT

Copyright 2015-2022 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
