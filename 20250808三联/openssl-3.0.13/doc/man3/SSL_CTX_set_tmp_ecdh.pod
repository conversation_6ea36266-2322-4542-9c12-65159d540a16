=pod

=head1 NAME

SSL_CTX_set_tmp_ecdh, SSL_set_tmp_ecdh, SSL_CTX_set_ecdh_auto, SSL_set_ecdh_auto
- handle ECDH keys for ephemeral key exchange

=head1 SYNOPSIS

 #include <openssl/ssl.h>

 long SSL_CTX_set_tmp_ecdh(SSL_CTX *ctx, const EC_KEY *ecdh);
 long SSL_set_tmp_ecdh(SSL *ssl, const EC_KEY *ecdh);

 long SSL_CTX_set_ecdh_auto(SSL_CTX *ctx, int state);
 long SSL_set_ecdh_auto(SSL *ssl, int state);

=head1 DESCRIPTION

SSL_CTX_set_tmp_ecdh() sets ECDH parameters to be used to be B<ecdh>.
The key is inherited by all B<ssl> objects created from B<ctx>.
This macro is deprecated in favor of L<SSL_CTX_set1_groups(3)>.

SSL_set_tmp_ecdh() sets the parameters only for B<ssl>.
This macro is deprecated in favor of L<SSL_set1_groups(3)>.

SSL_CTX_set_ecdh_auto() and SSL_set_ecdh_auto() are deprecated and
have no effect.

=head1 RETURN VALUES

SSL_CTX_set_tmp_ecdh() and SSL_set_tmp_ecdh() return 1 on success and 0
on failure.

=head1 SEE ALSO

L<ssl(7)>, L<SSL_CTX_set1_curves(3)>, L<SSL_CTX_set_cipher_list(3)>,
L<SSL_CTX_set_options(3)>, L<SSL_CTX_set_tmp_dh_callback(3)>,
L<openssl-ciphers(1)>, L<openssl-ecparam(1)>

=head1 COPYRIGHT

Copyright 2018 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
