=pod

=head1 NAME

EVP_PKEY_todata, EVP_PKEY_export
- functions to return keys as an array of key parameters

=head1 SYNOPSIS

 #include <openssl/evp.h>

 int EVP_PKEY_todata(const EVP_PKEY *pkey, int selection, OSSL_PARAM **params);
 int EVP_PKEY_export(const EVP_PKEY *pkey, int selection,
                     OSSL_CALLBACK *export_cb, void *export_cbarg);

=head1 DESCRIPTION

The functions described here are used to extract B<EVP_PKEY> key values as an
array of L<OSSL_PARAM(3)>.

EVP_PKEY_todata() extracts values from a key I<pkey> using the I<selection>.
I<selection> is described in L<EVP_PKEY_fromdata(3)/Selections>.
L<OSSL_PARAM_free(3)> should be used to free the returned parameters in
I<*params>.

EVP_PKEY_export() is similar to EVP_PKEY_todata() but uses a callback
I<export_cb> that gets passed the value of I<export_cbarg>.
See L<openssl-core.h(7)> for more information about the callback. Note that the
L<OSSL_PARAM(3)> array that is passed to the callback is not persistent after the
callback returns. The user must preserve the items of interest, or use
EVP_PKEY_todata() if persistence is required.

=head1 NOTES

These functions only work with key management methods coming from a provider.
This is the mirror function to L<EVP_PKEY_fromdata(3)>.

=head1 RETURN VALUES

EVP_PKEY_todata() and EVP_PKEY_export() return 1 for success and 0 for failure.

=head1 SEE ALSO

L<OSSL_PARAM(3)>, L<openssl-core.h(7)>,
L<EVP_PKEY_fromdata(3)>,
L<EVP_PKEY-RSA(7)>, L<EVP_PKEY-DSA(7)>, L<EVP_PKEY-DH(7)>, L<EVP_PKEY-EC(7)>,
L<EVP_PKEY-ED448(7)>, L<EVP_PKEY-X25519(7)>, L<EVP_PKEY-X448(7)>,
L<EVP_PKEY-ED25519(7)>

=head1 HISTORY

These functions were added in OpenSSL 3.0.

=head1 COPYRIGHT

Copyright 2021-2023 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut

