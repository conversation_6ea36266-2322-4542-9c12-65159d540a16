=pod

=head1 NAME

X509_get0_distinguishing_id, X509_set0_distinguishing_id,
X509_REQ_get0_distinguishing_id, X509_REQ_set0_distinguishing_id
- get or set the Distinguishing ID for certificate operations

=head1 SYNOPSIS

 #include <openssl/x509.h>

 ASN1_OCTET_STRING *X509_get0_distinguishing_id(X509 *x);
 void X509_set0_distinguishing_id(X509 *x, ASN1_OCTET_STRING *distid);
 ASN1_OCTET_STRING *X509_REQ_get0_distinguishing_id(X509_REQ *x);
 void X509_REQ_set0_distinguishing_id(X509_REQ *x, ASN1_OCTET_STRING *distid);

=head1 DESCRIPTION

The Distinguishing ID is defined in FIPS 196 as follows:

=over 4

=item I<Distinguishing  identifier>

Information which unambiguously distinguishes
an entity in the authentication process.

=back

The SM2 signature algorithm requires a Distinguishing ID value when generating
and verifying a signature, but the Ddistinguishing ID may also find other uses.
In the context of SM2, the Distinguishing ID is often referred to as the "SM2
ID".

For the purpose off verifying a certificate or a certification request, a
Distinguishing ID may be attached to it, so functions like L<X509_verify(3)>
or L<X509_REQ_verify(3)> have easy access to that identity for signature
verification.

X509_get0_distinguishing_id() gets the Distinguishing ID value of a certificate
B<x> by returning an B<ASN1_OCTET_STRING> object which should not be freed by
the caller.

X509_set0_distinguishing_id() assigns B<distid> to the certificate B<x>.
Calling this function transfers the memory management of the value to the X509
object, and therefore the value that has been passed in should not be freed by
the caller after this function has been called.

X509_REQ_get0_distinguishing_id() and X509_REQ_set0_distinguishing_id()
have the same functionality as X509_get0_distinguishing_id() and
X509_set0_distinguishing_id() except that they deal with  B<X509_REQ>
objects instead of B<X509>.

=head1 RETURN VALUES

X509_set0_distinguishing_id() and X509_REQ_set0_distinguishing_id() do not
return a value.

=head1 SEE ALSO

L<X509_verify(3)>, L<SM2(7)>

=head1 COPYRIGHT

Copyright 2019-2020 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
