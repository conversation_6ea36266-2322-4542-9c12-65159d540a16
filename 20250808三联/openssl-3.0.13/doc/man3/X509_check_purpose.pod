=pod

=head1 NAME

X509_check_purpose - Check the purpose of a certificate

=head1 SYNOPSIS

 #include <openssl/x509v3.h>

 int X509_check_purpose(X509 *x, int id, int ca);

=head1 DESCRIPTION

This function checks if certificate I<x> was created with the purpose
represented by I<id>. If I<ca> is nonzero, then certificate I<x> is
checked to determine if it's a possible CA with various levels of certainty
possibly returned. The certificate I<x> must be a complete certificate
otherwise the function returns an error.

Below are the potential ID's that can be checked:

 # define X509_PURPOSE_SSL_CLIENT        1
 # define X509_PURPOSE_SSL_SERVER        2
 # define X509_PURPOSE_NS_SSL_SERVER     3
 # define X509_PURPOSE_SMIME_SIGN        4
 # define X509_PURPOSE_SMIME_ENCRYPT     5
 # define X509_PURPOSE_CRL_SIGN          6
 # define X509_PURPOSE_ANY               7
 # define X509_PURPOSE_OCSP_HELPER       8
 # define X509_PURPOSE_TIMESTAMP_SIGN    9

The checks performed take into account the X.509 extensions
keyUsage, extendedKeyUsage, and basicConstraints.

=head1 RETURN VALUES

For non-CA checks

=over 4

=item -1 an error condition has occurred

=item E<32>1 if the certificate was created to perform the purpose represented by I<id>

=item E<32>0 if the certificate was not created to perform the purpose represented by I<id>

=back

For CA checks the below integers could be returned with the following meanings:

=over 4

=item -1 an error condition has occurred

=item E<32>0 not a CA or does not have the purpose represented by I<id>

=item E<32>1 is a CA.

=item E<32>2 Only possible in old versions of openSSL when basicConstraints are absent.
         New versions will not return this value. May be a CA

=item E<32>3 basicConstraints absent but self signed V1.

=item E<32>4 basicConstraints absent but keyUsage present and keyCertSign asserted.

=item E<32>5 legacy Netscape specific CA Flags present

=back

=head1 COPYRIGHT

Copyright 2019-2021 The OpenSSL Project Authors. All Rights Reserved.
Licensed under the Apache License 2.0 (the "License"). You may not use this
file except in compliance with the License. You can obtain a copy in the file
LICENSE in the source distribution or at L<https://www.openssl.org/source/license.html>.

=cut
