=pod

=head1 NAME

ASN1_AUX, ASN1_PRINT_ARG, ASN1_STREAM_ARG, ASN1_aux_cb, ASN1_aux_const_cb
- ASN.1 auxiliary data

=head1 SYNOPSIS

 #include <openssl/asn1t.h>

 struct ASN1_AUX_st {
     void *app_data;
     int flags;
     int ref_offset;             /* Offset of reference value */
     int ref_lock;               /* Offset to an CRYPTO_RWLOCK */
     ASN1_aux_cb *asn1_cb;
     int enc_offset;             /* Offset of ASN1_ENCODING structure */
     ASN1_aux_const_cb *asn1_const_cb; /* for ASN1_OP_I2D_ and ASN1_OP_PRINT_ */
 };
 typedef struct ASN1_AUX_st ASN1_AUX;

 struct ASN1_PRINT_ARG_st {
     BIO *out;
     int indent;
     const ASN1_PCTX *pctx;
 };
 typedef struct ASN1_PRINT_ARG_st ASN1_PRINT_ARG;

 struct ASN1_STREAM_ARG_st {
     BIO *out;
     BIO *ndef_bio;
     unsigned char **boundary;
 };
 typedef struct ASN1_STREAM_ARG_st ASN1_STREAM_ARG;

 typedef int ASN1_aux_cb(int operation, ASN1_VALUE **in, const ASN1_ITEM *it,
                         void *exarg);
 typedef int ASN1_aux_const_cb(int operation, const ASN1_VALUE **in,
                               const ASN1_ITEM *it, void *exarg);

=head1 DESCRIPTION

ASN.1 data structures can be associated with an B<ASN1_AUX> object to supply
additional information about the ASN.1 structure. An B<ASN1_AUX> structure is
associated with the structure during the definition of the ASN.1 template. For
example an B<ASN1_AUX> structure will be associated by using one of the various
ASN.1 template definition macros that supply auxiliary information such as
ASN1_SEQUENCE_enc(), ASN1_SEQUENCE_ref(), ASN1_SEQUENCE_cb_const_cb(),
ASN1_SEQUENCE_const_cb(), ASN1_SEQUENCE_cb() or ASN1_NDEF_SEQUENCE_cb().

An B<ASN1_AUX> structure contains the following information.

=over 4

=item I<app_data>

Arbitrary application data

=item I<flags>

Flags which indicate the auxiliarly functionality supported.

The B<ASN1_AFLG_REFCOUNT> flag indicates that objects support reference counting.

The B<ASN1_AFLG_ENCODING> flag indicates that the original encoding of the
object will be saved.

The B<ASN1_AFLG_BROKEN> flag is a work around for broken encoders where the
sequence length value may not be correct. This should generally not be used.

The B<ASN1_AFLG_CONST_CB> flag indicates that the "const" form of the
B<ASN1_AUX> callback should be used in preference to the non-const form.

=item I<ref_offset>

If the B<ASN1_AFLG_REFCOUNT> flag is set then this value is assumed to be an
offset into the B<ASN1_VALUE> structure where a B<CRYPTO_REF_COUNT> may be
found for the purposes of reference counting.

=item I<ref_lock>

If the B<ASN1_AFLG_REFCOUNT> flag is set then this value is assumed to be an
offset into the B<ASN1_VALUE> structure where a B<CRYPTO_RWLOCK> may be
found for the purposes of reference counting.

=item I<asn1_cb>

A callback that will be invoked at various points during the processing of
the the B<ASN1_VALLUE>. See below for further details.

=item I<enc_offset>

Offset into the B<ASN1_VALUE> object where the original encoding of the object
will be saved if the B<ASN1_AFLG_ENCODING> flag has been set.

=item I<asn1_const_cb>

A callback that will be invoked at various points during the processing of
the the B<ASN1_VALLUE>. This is used in preference to the I<asn1_cb> callback if
the B<ASN1_AFLG_CONST_CB> flag is set. See below for further details.

=back

During the processing of an B<ASN1_VALUE> object the callbacks set via
I<asn1_cb> or I<asn1_const_cb> will be invoked as a result of various events
indicated via the I<operation> parameter. The value of I<*in> will be the
B<ASN1_VALUE> object being processed based on the template in I<it>. An
additional operation specific parameter may be passed in I<exarg>. The currently
supported operations are as follows. The callbacks should return a positive
value on success or zero on error, unless otherwise noted below.

=over 4

=item B<ASN1_OP_NEW_PRE>

Invoked when processing a B<CHOICE>, B<SEQUENCE> or B<NDEF_SEQUENCE> structure
prior to an B<ASN1_VALUE> object being allocated. The callback may allocate the
B<ASN1_VALUE> itself and store it in I<*pval>. If it does so it should return 2
from the callback. On error it should return 0.

=item B<ASN1_OP_NEW_POST>

Invoked when processing a B<CHOICE>, B<SEQUENCE> or B<NDEF_SEQUENCE> structure
after an B<ASN1_VALUE> object has been allocated. The allocated object is in
I<*pval>.

=item B<ASN1_OP_FREE_PRE>

Invoked when processing a B<CHOICE>, B<SEQUENCE> or B<NDEF_SEQUENCE> structure
immediately before an B<ASN1_VALUE> is freed. If the callback originally
constructed the B<ASN1_VALUE> via B<ASN1_OP_NEW_PRE> then it should free it at
this point and return 2 from the callback. Otherwise it should return 1 for
success or 0 on error.

=item B<ASN1_OP_FREE_POST>

Invoked when processing a B<CHOICE>, B<SEQUENCE> or B<NDEF_SEQUENCE> structure
immediately after B<ASN1_VALUE> sub-structures are freed.

=item B<ASN1_OP_D2I_PRE>

Invoked when processing a B<CHOICE>, B<SEQUENCE> or B<NDEF_SEQUENCE> structure
immediately before a "d2i" operation for the B<ASN1_VALUE>.

=item B<ASN1_OP_D2I_POST>

Invoked when processing a B<CHOICE>, B<SEQUENCE> or B<NDEF_SEQUENCE> structure
immediately after a "d2i" operation for the B<ASN1_VALUE>.

=item B<ASN1_OP_I2D_PRE>

Invoked when processing a B<CHOICE>, B<SEQUENCE> or B<NDEF_SEQUENCE> structure
immediately before a "i2d" operation for the B<ASN1_VALUE>.

=item B<ASN1_OP_I2D_POST>

Invoked when processing a B<CHOICE>, B<SEQUENCE> or B<NDEF_SEQUENCE> structure
immediately after a "i2d" operation for the B<ASN1_VALUE>.

=item B<ASN1_OP_PRINT_PRE>

Invoked when processing a B<SEQUENCE> or B<NDEF_SEQUENCE> structure immediately
before printing the B<ASN1_VALUE>. The I<exarg> argument will be a pointer to an
B<ASN1_PRINT_ARG> structure (see below).

=item B<ASN1_OP_PRINT_POST>

Invoked when processing a B<SEQUENCE> or B<NDEF_SEQUENCE> structure immediately
after printing the B<ASN1_VALUE>. The I<exarg> argument will be a pointer to an
B<ASN1_PRINT_ARG> structure (see below).

=item B<ASN1_OP_STREAM_PRE>

Invoked immediately prior to streaming the B<ASN1_VALUE> data using indefinite
length encoding. The I<exarg> argument will be a pointer to a B<ASN1_STREAM_ARG>
structure (see below).

=item B<ASN1_OP_STREAM_POST>

Invoked immediately after streaming the B<ASN1_VALUE> data using indefinite
length encoding. The I<exarg> argument will be a pointer to a B<ASN1_STREAM_ARG>
structure (see below).

=item B<ASN1_OP_DETACHED_PRE>

Invoked immediately prior to processing the B<ASN1_VALUE> data as a "detached"
value (as used in CMS and PKCS7). The I<exarg> argument will be a pointer to a
B<ASN1_STREAM_ARG> structure (see below).

=item B<ASN1_OP_DETACHED_POST>

Invoked immediately after processing the B<ASN1_VALUE> data as a "detached"
value (as used in CMS and PKCS7). The I<exarg> argument will be a pointer to a
B<ASN1_STREAM_ARG> structure (see below).

=item B<ASN1_OP_DUP_PRE>

Invoked immediate prior to an ASN1_VALUE being duplicated via a call to
ASN1_item_dup().

=item B<ASN1_OP_DUP_POST>

Invoked immediate after to an ASN1_VALUE has been duplicated via a call to
ASN1_item_dup().

=item B<ASN1_OP_GET0_LIBCTX>

Invoked in order to obtain the B<OSSL_LIB_CTX> associated with an B<ASN1_VALUE>
if any. A pointer to an B<OSSL_LIB_CTX> should be stored in I<*exarg> if such
a value exists.

=item B<ASN1_OP_GET0_PROPQ>

Invoked in order to obtain the property query string associated with an
B<ASN1_VALUE> if any. A pointer to the property query string should be stored in
I<*exarg> if such a value exists.

=back

An B<ASN1_PRINT_ARG> object is used during processing of B<ASN1_OP_PRINT_PRE>
and B<ASN1_OP_PRINT_POST> callback operations. It contains the following
information.

=over 4

=item I<out>

The B<BIO> being used to print the data out.

=item I<ndef_bio>

The current number of indent spaces that should be used for printing this data.

=item I<pctx>

The context for the B<ASN1_PCTX> operation.

=back

An B<ASN1_STREAM_ARG> object is used during processing of B<ASN1_OP_STREAM_PRE>,
B<ASN1_OP_STREAM_POST>, B<ASN1_OP_DETACHED_PRE> and B<ASN1_OP_DETACHED_POST>
callback operations. It contains the following information.

=over 4

=item I<out>

The B<BIO> to stream through

=item I<ndef_bio>

The B<BIO> with filters appended

=item I<boundary>

The streaming I/O boundary.

=back

=head1 RETURN VALUES

The callbacks return 0 on error and a positive value on success. Some operations
require specific positive success values as noted above.

=head1 SEE ALSO

L<ASN1_item_new_ex(3)>

=head1 HISTORY

The ASN1_aux_const_cb() callback and the B<ASN1_OP_GET0_LIBCTX> and
B<ASN1_OP_GET0_PROPQ> operation types were added in OpenSSL 3.0.

=head1 COPYRIGHT

Copyright 2021-2023 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
