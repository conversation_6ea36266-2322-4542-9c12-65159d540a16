=pod

=head1 NAME

PKCS12_key_gen_asc, PKCS12_key_gen_asc_ex,
PKCS12_key_gen_uni, PKCS12_key_gen_uni_ex,
PKCS12_key_gen_utf8, PKCS12_key_gen_utf8_ex - PKCS#12 Password based key derivation

=head1 SYNOPSIS

 #include <openssl/pkcs12.h>

 int PKCS12_key_gen_asc(const char *pass, int passlen, unsigned char *salt,
                        int saltlen, int id, int iter, int n,
                        unsigned char *out, const EVP_MD *md_type);
 int PKCS12_key_gen_asc_ex(const char *pass, int passlen, unsigned char *salt,
                           int saltlen, int id, int iter, int n,
                           unsigned char *out, const EVP_MD *md_type,
                           OSSL_LIB_CTX *ctx, const char *propq);
 int PKCS12_key_gen_uni(unsigned char *pass, int passlen, unsigned char *salt,
                        int saltlen, int id, int iter, int n,
                        unsigned char *out, const EVP_MD *md_type);
 int PKCS12_key_gen_uni_ex(unsigned char *pass, int passlen, unsigned char *salt,
                           int saltlen, int id, int iter, int n,
                           unsigned char *out, const EVP_MD *md_type,
                           OSSL_LIB_CTX *ctx, const char *propq);
 int PKCS12_key_gen_utf8(const char *pass, int passlen, unsigned char *salt,
                         int saltlen, int id, int iter, int n,
                         unsigned char *out, const EVP_MD *md_type);
 int PKCS12_key_gen_utf8_ex(const char *pass, int passlen, unsigned char *salt,
                            int saltlen, int id, int iter, int n,
                            unsigned char *out, const EVP_MD *md_type,
                            OSSL_LIB_CTX *ctx, const char *propq);

=head1 DESCRIPTION

These methods perform a key derivation according to PKCS#12 (RFC7292)
with an input password I<pass> of length I<passlen>, a salt I<salt> of length
I<saltlen>, an iteration count I<iter> and a digest algorithm I<md_type>.
The ID byte I<id> determines how the resulting key is intended to be used:

=over 4

=item *

If ID=1, then the pseudorandom bits being produced are to be used
as key material for performing encryption or decryption.

=item *

If ID=2, then the pseudorandom bits being produced are to be used
as an IV (Initial Value) for encryption or decryption.

=item *

If ID=3, then the pseudorandom bits being produced are to be used
as an integrity key for MACing.

=back

The intended format of the supplied password is determined by the method chosen:

=over 4

=item *

PKCS12_key_gen_asc() and PKCS12_key_gen_asc_ex() expect an ASCII-formatted password.

=item *

PKCS12_key_gen_uni() and PKCS12_key_gen_uni_ex() expect a Unicode-formatted password.

=item *

PKCS12_key_gen_utf8() and PKCS12_key_gen_utf8_ex() expect a UTF-8 encoded password.

=back

I<pass> is the password used in the derivation of length I<passlen>. I<pass>
is an optional parameter and can be NULL. If I<passlen> is -1, then the
function will calculate the length of I<pass> using strlen().

I<salt> is the salt used in the derivation of length I<saltlen>. If the
I<salt> is NULL, then I<saltlen> must be 0. The function will not
attempt to calculate the length of the I<salt> because it is not assumed to
be NULL terminated.

I<iter> is the iteration count and its value should be greater than or
equal to 1. RFC 2898 suggests an iteration count of at least 1000. Any
I<iter> less than 1 is treated as a single iteration.

I<digest> is the message digest function used in the derivation.

The derived key will be written to I<out>. The size of the I<out> buffer
is specified via I<n>.

Functions ending in _ex() allow for a library context I<ctx> and property query
I<propq> to be used to select algorithm implementations.

=head1 NOTES

A typical application of this function is to derive keying material for an
encryption algorithm from a password in the I<pass>, a salt in I<salt>,
and an iteration count.

Increasing the I<iter> parameter slows down the algorithm which makes it
harder for an attacker to perform a brute force attack using a large number
of candidate passwords.

=head1 RETURN VALUES

Returns 1 on success or 0 on error.

=head1 CONFORMING TO

IETF RFC 7292 (L<https://tools.ietf.org/html/rfc7292>)

=head1 SEE ALSO

L<PKCS12_create_ex(3)>,
L<PKCS12_pbe_crypt_ex(3)>,
L<passphrase-encoding(7)>

=head1 HISTORY

PKCS12_key_gen_asc_ex(), PKCS12_key_gen_uni_ex() and PKCS12_key_gen_utf8_ex()
were added in OpenSSL 3.0.

=head1 COPYRIGHT

Copyright 2021 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
