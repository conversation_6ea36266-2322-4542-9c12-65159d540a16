=pod

=head1 NAME

X509_cmp, X509_NAME_cmp,
X509_issuer_and_serial_cmp, X509_issuer_name_cmp, X509_subject_name_cmp,
X509_CRL_cmp, X509_CRL_match
- compare X509 certificates and related values

=head1 SYNOPSIS

 #include <openssl/x509.h>

 int X509_cmp(const X509 *a, const X509 *b);
 int X509_NAME_cmp(const X509_NAME *a, const X509_NAME *b);
 int X509_issuer_and_serial_cmp(const X509 *a, const X509 *b);
 int X509_issuer_name_cmp(const X509 *a, const X509 *b);
 int X509_subject_name_cmp(const X509 *a, const X509 *b);
 int X509_CRL_cmp(const X509_CRL *a, const X509_CRL *b);
 int X509_CRL_match(const X509_CRL *a, const X509_CRL *b);

=head1 DESCRIPTION

This set of functions are used to compare X509 objects, including X509
certificates, X509 CRL objects and various values in an X509 certificate.

The X509_cmp() function compares two B<X509> objects indicated by parameters
I<a> and I<b>. The comparison is based on the B<memcmp> result of the hash
values of two B<X509> objects and the canonical (DER) encoding values.

The X509_NAME_cmp() function compares two B<X509_NAME> objects indicated by
parameters I<a> and I<b>. The comparison is based on the B<memcmp> result of the
canonical (DER) encoding values of the two objects using L<i2d_X509_NAME(3)>.
This procedure adheres to the matching rules for Distinguished Names (DN)
given in RFC 4517 section 4.2.15 and RFC 5280 section 7.1.
In particular, the order of Relative Distinguished Names (RDNs) is relevant.
On the other hand, if an RDN is multi-valued, i.e., it contains a set of
AttributeValueAssertions (AVAs), its members are effectively not ordered.

The X509_issuer_and_serial_cmp() function compares the serial number and issuer
values in the given B<X509> objects I<a> and I<b>.

The X509_issuer_name_cmp(), X509_subject_name_cmp() and X509_CRL_cmp() functions
are effectively wrappers of the X509_NAME_cmp() function. These functions compare
issuer names and subject names of the X<509> objects, or issuers of B<X509_CRL>
objects, respectively.

The X509_CRL_match() function compares two B<X509_CRL> objects. Unlike the
X509_CRL_cmp() function, this function compares the whole CRL content instead
of just the issuer name.

=head1 RETURN VALUES

The B<X509> comparison functions return B<-1>, B<0>, or B<1> if object I<a> is
found to be less than, to match, or be greater than object I<b>, respectively.

X509_NAME_cmp(), X509_issuer_and_serial_cmp(), X509_issuer_name_cmp(),
X509_subject_name_cmp(), X509_CRL_cmp(), and X509_CRL_match()
may return B<-2> to indicate an error.

=head1 NOTES

These functions in fact utilize the underlying B<memcmp> of the C library to do
the comparison job. Data to be compared varies from DER encoding data, hash
value or B<ASN1_STRING>. The sign of the comparison can be used to order the
objects but it does not have a special meaning in some cases.

X509_NAME_cmp() and wrappers utilize the value B<-2> to indicate errors in some
circumstances, which could cause confusion for the applications.

=head1 SEE ALSO

L<i2d_X509_NAME(3)>, L<i2d_X509(3)>

=head1 COPYRIGHT

Copyright 2019-2021 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
