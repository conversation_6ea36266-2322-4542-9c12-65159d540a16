=pod

=head1 NAME

DSA_new, DSA_free - allocate and free DSA objects

=head1 SYNOPSIS

 #include <openssl/dsa.h>

The following functions have been deprecated since OpenSSL 3.0, and can be
hidden entirely by defining B<OPENSSL_API_COMPAT> with a suitable version value,
see L<openssl_user_macros(7)>:

 DSA* DSA_new(void);

 void DSA_free(DSA *dsa);

=head1 DESCRIPTION

All of the functions described on this page are deprecated.
Applications should instead use L<EVP_PKEY_new(3)> and L<EVP_PKEY_free(3)>.

DSA_new() allocates and initializes a B<DSA> structure. It is equivalent to
calling DSA_new_method(NULL).

DSA_free() frees the B<DSA> structure and its components. The values are
erased before the memory is returned to the system.
If B<dsa> is NULL nothing is done.

=head1 RETURN VALUES

If the allocation fails, DSA_new() returns B<NULL> and sets an error
code that can be obtained by
L<ERR_get_error(3)>. Otherwise it returns a pointer
to the newly allocated structure.

DSA_free() returns no value.

=head1 SEE ALSO

L<EVP_PKEY_new(3)>, L<EVP_PKEY_free(3)>,
L<DSA_new(3)>, L<ERR_get_error(3)>,
L<DSA_generate_parameters(3)>,
L<DSA_generate_key(3)>

=head1 HISTORY

All of these functions were deprecated in OpenSSL 3.0.

=head1 COPYRIGHT

Copyright 2000-2021 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
