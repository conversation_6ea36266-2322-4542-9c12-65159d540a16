=pod

=head1 NAME

EVP_chacha20,
<PERSON><PERSON>_chacha20_poly1305
- <PERSON><PERSON>Cha20 stream cipher

=head1 SYNOPSIS

 #include <openssl/evp.h>

 const EVP_CIPHER *EVP_chacha20(void);
 const EVP_CIPHER *EVP_chacha20_poly1305(void);

=head1 DESCRIPTION

The ChaCha20 stream cipher for EVP.

=over 4

=item EVP_chacha20()

The ChaCha20 stream cipher. The key length is 256 bits, the IV is 128 bits long.
The first 64 bits consists of a counter in little-endian order followed by a 64
bit nonce. For example a nonce of:

0000000000000002

With an initial counter of 42 (2a in hex) would be expressed as:

2a000000000000000000000000000002

=item EVP_chacha20_poly1305()

Authenticated encryption with ChaCha20-Poly1305. Like EVP_chacha20(), the key
is 256 bits and the IV is 96 bits. This supports additional authenticated data
(AAD) and produces a 128-bit authentication tag. See the
L<EVP_EncryptInit(3)/AEAD Interface> section for more information.

=back

=head1 NOTES

Developers should be aware of the negative performance implications of
calling these functions multiple times and should consider using
L<EVP_CIPHER_fetch(3)> with L<EVP_CIPHER-CHACHA(7)> instead.
See L<crypto(7)/Performance> for further information.

L<RFC 7539|https://www.rfc-editor.org/rfc/rfc7539.html#section-2.4>
uses a 32 bit counter and a 96 bit nonce for the IV.

=head1 RETURN VALUES

These functions return an B<EVP_CIPHER> structure that contains the
implementation of the symmetric cipher. See L<EVP_CIPHER_meth_new(3)> for
details of the B<EVP_CIPHER> structure.

=head1 SEE ALSO

L<evp(7)>,
L<EVP_EncryptInit(3)>,
L<EVP_CIPHER_meth_new(3)>

=head1 COPYRIGHT

Copyright 2017-2023 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut

