=pod

=head1 NAME

EVP_SignInit, EVP_SignInit_ex, EVP_SignUpdate,
EVP_SignFinal_ex, EVP_SignFinal
- EVP signing functions

=head1 SYNOPSIS

 #include <openssl/evp.h>

 int EVP_SignInit_ex(EVP_MD_CTX *ctx, const EVP_MD *type, ENGINE *impl);
 int EVP_SignUpdate(EVP_MD_CTX *ctx, const void *d, unsigned int cnt);
 int EVP_SignFinal_ex(EVP_MD_CTX *ctx, unsigned char *md, unsigned int *s,
                      EVP_PKEY *pkey, OSSL_LIB_CTX *libctx, const char *propq);
 int EVP_SignFinal(EVP_MD_CTX *ctx, unsigned char *sig, unsigned int *s,
                   EVP_PKEY *pkey);

 void EVP_SignInit(EVP_MD_CTX *ctx, const EVP_MD *type);

=head1 DESCRIPTION

The EVP signature routines are a high-level interface to digital
signatures.

EVP_SignInit_ex() sets up signing context I<ctx> to use digest
I<type> from B<ENGINE> I<impl>. I<ctx> must be created with
EVP_MD_CTX_new() before calling this function.

EVP_SignUpdate() hashes I<cnt> bytes of data at I<d> into the
signature context I<ctx>. This function can be called several times on the
same I<ctx> to include additional data.

EVP_SignFinal_ex() signs the data in I<ctx> using the private key
I<pkey> and places the signature in I<sig>. The library context I<libctx> and
property query I<propq> are used when creating a context to use with the key
I<pkey>. I<sig> must be at least C<EVP_PKEY_get_size(pkey)> bytes in size.
I<s> is an OUT parameter, and not used as an IN parameter.
The number of bytes of data written (i.e. the length of the signature)
will be written to the integer at I<s>, at most C<EVP_PKEY_get_size(pkey)>
bytes will be written.

EVP_SignFinal() is similar to EVP_SignFinal_ex() but uses default
values of NULL for the library context I<libctx> and the property query I<propq>.

EVP_SignInit() initializes a signing context I<ctx> to use the default
implementation of digest I<type>.

=head1 RETURN VALUES

EVP_SignInit_ex(), EVP_SignUpdate(), EVP_SignFinal_ex() and
EVP_SignFinal() return 1 for success and 0 for failure.

The error codes can be obtained by L<ERR_get_error(3)>.

=head1 NOTES

The B<EVP> interface to digital signatures should almost always be used in
preference to the low-level interfaces. This is because the code then becomes
transparent to the algorithm used and much more flexible.

When signing with some private key types the random number generator must
be seeded. If the automatic seeding or reseeding of the OpenSSL CSPRNG fails
due to external circumstances (see L<RAND(7)>), the operation will fail.

The call to EVP_SignFinal() internally finalizes a copy of the digest context.
This means that calls to EVP_SignUpdate() and EVP_SignFinal() can be called
later to digest and sign additional data.

Since only a copy of the digest context is ever finalized the context must
be cleaned up after use by calling EVP_MD_CTX_free() or a memory leak
will occur.

=head1 BUGS

Older versions of this documentation wrongly stated that calls to
EVP_SignUpdate() could not be made after calling EVP_SignFinal().

Since the private key is passed in the call to EVP_SignFinal() any error
relating to the private key (for example an unsuitable key and digest
combination) will not be indicated until after potentially large amounts of
data have been passed through EVP_SignUpdate().

It is not possible to change the signing parameters using these function.

The previous two bugs are fixed in the newer EVP_DigestSign*() functions.

=head1 SEE ALSO

L<EVP_PKEY_get_size(3)>, L<EVP_PKEY_get_bits(3)>,
L<EVP_PKEY_get_security_bits(3)>,
L<EVP_VerifyInit(3)>,
L<EVP_DigestInit(3)>,
L<evp(7)>, L<HMAC(3)>, L<MD2(3)>,
L<MD5(3)>, L<MDC2(3)>, L<RIPEMD160(3)>,
L<SHA1(3)>, L<openssl-dgst(1)>

=head1 HISTORY

The function EVP_SignFinal_ex() was added in OpenSSL 3.0.

=head1 COPYRIGHT

Copyright 2000-2021 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
