=pod

=head1 NAME

SSL_SESSION_get0_hostname,
SSL_SESSION_set1_hostname,
SSL_SESSION_get0_alpn_selected,
SSL_SESSION_set1_alpn_selected
- get and set SNI and ALPN data associated with a session

=head1 SYNOPSIS

 #include <openssl/ssl.h>

 const char *SSL_SESSION_get0_hostname(const SSL_SESSION *s);
 int SSL_SESSION_set1_hostname(SSL_SESSION *s, const char *hostname);

 void SSL_SESSION_get0_alpn_selected(const SSL_SESSION *s,
                                     const unsigned char **alpn,
                                     size_t *len);
 int SSL_SESSION_set1_alpn_selected(SSL_SESSION *s, const unsigned char *alpn,
                                    size_t len);

=head1 DESCRIPTION

SSL_SESSION_get0_hostname() retrieves the SNI value that was sent by the
client when the session was created if it was accepted by the server and TLSv1.2
or below was negotiated. Otherwise NULL is returned. Note that in TLSv1.3 the
SNI hostname is negotiated with each handshake including resumption handshakes
and is therefore never associated with the session.

The value returned is a pointer to memory maintained within B<s> and
should not be free'd.

SSL_SESSION_set1_hostname() sets the SNI value for the hostname to a copy of
the string provided in hostname.

SSL_SESSION_get0_alpn_selected() retrieves the selected ALPN protocol for this
session and its associated length in bytes. The returned value of B<*alpn> is a
pointer to memory maintained within B<s> and should not be free'd.

SSL_SESSION_set1_alpn_selected() sets the ALPN protocol for this session to the
value in B<alpn> which should be of length B<len> bytes. A copy of the input
value is made, and the caller retains ownership of the memory pointed to by
B<alpn>.

=head1 RETURN VALUES

SSL_SESSION_get0_hostname() returns either a string or NULL based on if there
is the SNI value sent by client.

SSL_SESSION_set1_hostname() returns 1 on success or 0 on error.

SSL_SESSION_set1_alpn_selected() returns 1 on success or 0 on error.

=head1 SEE ALSO

L<ssl(7)>,
L<d2i_SSL_SESSION(3)>,
L<SSL_SESSION_get_time(3)>,
L<SSL_SESSION_free(3)>

=head1 HISTORY

The SSL_SESSION_set1_hostname(), SSL_SESSION_get0_alpn_selected() and
SSL_SESSION_set1_alpn_selected() functions were added in OpenSSL 1.1.1.

=head1 COPYRIGHT

Copyright 2016-2020 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
