=pod

=head1 NAME

EVP_PBE_CipherInit, EVP_PBE_CipherInit_ex,
EVP_PBE_find, EVP_PBE_find_ex,
EVP_PBE_alg_add_type, EVP_PBE_alg_add - Password based encryption routines

=head1 SYNOPSIS

 #include <openssl/evp.h>

 int EVP_PBE_CipherInit(ASN1_OBJECT *pbe_obj, const char *pass, int passlen,
                        ASN1_TYPE *param, EVP_CIPHER_CTX *ctx, int en_de);
 int EVP_PBE_CipherInit_ex(ASN1_OBJECT *pbe_obj, const char *pass, int passlen,
                           ASN1_TYPE *param, EVP_CIPHER_CTX *ctx, int en_de,
                           OSSL_LIB_CTX *libctx, const char *propq);

 int EVP_PBE_find(int type, int pbe_nid, int *pcnid, int *pmnid,
                  EVP_PBE_KEYGEN **pkeygen);
 int EVP_PBE_find_ex(int type, int pbe_nid, int *pcnid, int *pmnid,
                     EVP_PBE_KEYGEN **pkeygen, EVP_PBE_KEYGEN_EX **keygen_ex);

 int EVP_PBE_alg_add_type(int pbe_type, int pbe_nid, int cipher_nid,
                          int md_nid, EVP_PBE_KEYGEN *keygen);
 int EVP_PBE_alg_add(int nid, const EVP_CIPHER *cipher, const EVP_MD *md,
                     EVP_PBE_KEYGEN *keygen);

=head1 DESCRIPTION

=head2 PBE operations

EVP_PBE_CipherInit() and EVP_PBE_CipherInit_ex() initialise an B<EVP_CIPHER_CTX>
I<ctx> for encryption (I<en_de>=1) or decryption (I<en_de>=0) using the password
I<pass> of length I<passlen>. The PBE algorithm type and parameters are extracted
from an OID I<pbe_obj> and parameters I<param>.

EVP_PBE_CipherInit_ex() also allows the application to specify a library context
I<libctx> and property query I<propq> to select appropriate algorithm
implementations.

=head2 PBE algorithm search

EVP_PBE_find() and EVP_PBE_find_ex() search for a matching algorithm using two parameters:

1. An algorithm type I<type> which can be:

=over 4

=item *

EVP_PBE_TYPE_OUTER - A PBE algorithm

=item *

EVP_PBE_TYPE_PRF - A pseudo-random function

=item *

EVP_PBE_TYPE_KDF - A key derivation function

=back

2. A I<pbe_nid> which can represent the algorithm identifier with parameters e.g.
B<NID_pbeWithSHA1AndRC2_CBC> or an algorithm class e.g. B<NID_pbes2>.

They return the algorithm's cipher ID I<pcnid>, digest ID I<pmnid> and a key
generation function for the algorithm I<pkeygen>. EVP_PBE_CipherInit_ex() also
returns an extended key generation function I<keygen_ex> which takes a library
context and property query.

If a NULL is supplied for any of I<pcnid>, I<pmnid>, I<pkeygen> or I<pkeygen_ex>
then this parameter is not returned.

=head2 PBE algorithm add

EVP_PBE_alg_add_type() and EVP_PBE_alg_add() add an algorithm to the list
of known algorithms. Their parameters have the same meaning as for
EVP_PBE_find() and EVP_PBE_find_ex() functions.

=head1 NOTES

The arguments I<pbe_obj> and I<param> to EVP_PBE_CipherInit() and EVP_PBE_CipherInit_ex()
together form an B<X509_ALGOR> and can often be extracted directly from this structure.

=head1 RETURN VALUES

Return value is 1 for success and 0 if an error occurred.

=head1 SEE ALSO

L<PKCS5_PBE_keyivgen(3)>,
L<PKCS12_PBE_keyivgen_ex(3)>,
L<PKCS5_v2_PBE_keyivgen_ex(3)>,
L<PKCS12_pbe_crypt_ex(3)>,
L<PKCS12_create_ex(3)>

=head1 HISTORY

EVP_PBE_CipherInit_ex() and EVP_PBE_find_ex() were added in OpenSSL 3.0.

=head1 COPYRIGHT

Copyright 2021-2022 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
