=pod

=head1 NAME

OPENSSL_FILE, OPENSSL_LINE, OPENSSL_FUNC,
OPENSSL_MSTR, OPENSSL_MSTR_HELPER
- generic C programming utility macros

=head1 SYNOPSIS

 #include <openssl/macros.h>

 #define OPENSSL_FILE /* typically: __FILE__ */
 #define OPENSSL_LINE /* typically: __LINE__ */
 #define OPENSSL_FUNC /* typically: __func__ */

 #define OPENSSL_MSTR_HELPER(x) #x
 #define OPENSSL_MSTR(x) OPENSSL_MSTR_HELPER(x)

=head1 DESCRIPTION

The macros B<OPENSSL_FILE> and B<OPENSSL_LINE>
typically yield the current filename and line number during C compilation.
When B<OPENSSL_NO_FILENAMES> is defined they yield B<""> and B<0>, respectively.

The macro B<OPENSSL_FUNC> attempts to yield the name of the C function
currently being compiled, as far as language and compiler versions allow.
Otherwise, it yields "(unknown function)".

The macro B<OPENSSL_MSTR> yields the expansion of the macro given as argument,
which is useful for concatenation with string constants.
The macro B<OPENSSL_MSTR_HELPER> is an auxiliary macro for this purpose.

=head1 RETURN VALUES

see above

=head1 SEE ALSO

L<crypto(7)>

=head1 HISTORY

B<OPENSSL_FUNC>, B<OPENSSL_MSTR>, and B<OPENSSL_MSTR_HELPER>
were added in OpenSSL 3.0.

=head1 COPYRIGHT

Copyright 2018-2019 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
