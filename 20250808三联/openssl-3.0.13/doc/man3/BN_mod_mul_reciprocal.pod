=pod

=head1 NAME

BN_mod_mul_reciprocal, BN_div_recp, BN_RECP_CTX_new,
BN_RECP_CTX_free, BN_RECP_CTX_set - modular multiplication using
reciprocal

=head1 SYNOPSIS

 #include <openssl/bn.h>

 BN_RECP_CTX *BN_RECP_CTX_new(void);
 void BN_RECP_CTX_free(BN_RECP_CTX *recp);

 int BN_RECP_CTX_set(BN_RECP_CTX *recp, const BIGNUM *m, BN_CTX *ctx);

 int BN_div_recp(BIGNUM *dv, BIGNUM *rem, const BIGNUM *a, BN_RECP_CTX *recp,
                 BN_CTX *ctx);

 int BN_mod_mul_reciprocal(BIGNUM *r, const BIGNUM *a, const BIGNUM *b,
                           BN_RECP_CTX *recp, BN_CTX *ctx);

=head1 DESCRIPTION

BN_mod_mul_reciprocal() can be used to perform an efficient
L<BN_mod_mul(3)> operation when the operation will be performed
repeatedly with the same modulus. It computes B<r>=(B<a>*B<b>)%B<m>
using B<recp>=1/B<m>, which is set as described below.  B<ctx> is a
previously allocated B<BN_CTX> used for temporary variables.

BN_RECP_CTX_new() allocates and initializes a B<BN_RECP> structure.

BN_RECP_CTX_free() frees the components of the B<BN_RECP>, and, if it
was created by BN_RECP_CTX_new(), also the structure itself.
If B<recp> is NULL, nothing is done.

BN_RECP_CTX_set() stores B<m> in B<recp> and sets it up for computing
1/B<m> and shifting it left by BN_num_bits(B<m>)+1 to make it an
integer. The result and the number of bits it was shifted left will
later be stored in B<recp>.

BN_div_recp() divides B<a> by B<m> using B<recp>. It places the quotient
in B<dv> and the remainder in B<rem>.

The B<BN_RECP_CTX> structure cannot be shared between threads.

=head1 RETURN VALUES

BN_RECP_CTX_new() returns the newly allocated B<BN_RECP_CTX>, and NULL
on error.

BN_RECP_CTX_free() has no return value.

For the other functions, 1 is returned for success, 0 on error.
The error codes can be obtained by L<ERR_get_error(3)>.

=head1 SEE ALSO

L<ERR_get_error(3)>, L<BN_add(3)>,
L<BN_CTX_new(3)>

=head1 HISTORY

BN_RECP_CTX_init() was removed in OpenSSL 1.1.0

=head1 COPYRIGHT

Copyright 2000-2021 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
