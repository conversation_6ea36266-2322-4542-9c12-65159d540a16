=pod

=head1 NAME

SSL_free - free an allocated SSL structure

=head1 SYNOPSIS

 #include <openssl/ssl.h>

 void SSL_free(SSL *ssl);

=head1 DESCRIPTION

SSL_free() decrements the reference count of B<ssl>, and removes the SSL
structure pointed to by B<ssl> and frees up the allocated memory if the
reference count has reached 0.
If B<ssl> is NULL nothing is done.

=head1 NOTES

SSL_free() also calls the free()ing procedures for indirectly affected items, if
applicable: the buffering BIO, the read and write BIOs,
cipher lists specially created for this B<ssl>, the B<SSL_SESSION>.
Do not explicitly free these indirectly freed up items before or after
calling SSL_free(), as trying to free things twice may lead to program
failure.

The ssl session has reference counts from two users: the SSL object, for
which the reference count is removed by SSL_free() and the internal
session cache. If the session is considered bad, because
L<SSL_shutdown(3)> was not called for the connection
and L<SSL_set_shutdown(3)> was not used to set the
SSL_SENT_SHUTDOWN state, the session will also be removed
from the session cache as required by RFC2246.

=head1 RETURN VALUES

SSL_free() does not provide diagnostic information.

L<SSL_new(3)>, L<SSL_clear(3)>,
L<SSL_shutdown(3)>, L<SSL_set_shutdown(3)>,
L<ssl(7)>

=head1 COPYRIGHT

Copyright 2000-2016 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
