=pod

=head1 NAME

PKCS12_create, PKCS12_create_ex - create a PKCS#12 structure

=head1 SYNOPSIS

 #include <openssl/pkcs12.h>

 PKCS12 *PKCS12_create(const char *pass, const char *name, EVP_PKEY *pkey,
                       X509 *cert, STACK_OF(X509) *ca,
                       int nid_key, int nid_cert, int iter, int mac_iter, int keytype);
 PKCS12 *PKCS12_create_ex(const char *pass, const char *name, EVP_PKEY *pkey,
                          X509 *cert, STACK_OF(X509) *ca, int nid_key, int nid_cert,
                          int iter, int mac_iter, int keytype,
                          OSSL_LIB_CTX *ctx, const char *propq);

=head1 DESCRIPTION

PKCS12_create() creates a PKCS#12 structure.

I<pass> is the passphrase to use. I<name> is the B<friendlyName> to use for
the supplied certificate and key. I<pkey> is the private key to include in
the structure and I<cert> its corresponding certificates. I<ca>, if not B<NULL>
is an optional set of certificates to also include in the structure.

I<nid_key> and I<nid_cert> are the encryption algorithms that should be used
for the key and certificate respectively. The modes
GCM, CCM, XTS, and OCB are unsupported. I<iter> is the encryption algorithm
iteration count to use and I<mac_iter> is the MAC iteration count to use.
I<keytype> is the type of key.

PKCS12_create_ex() is identical to PKCS12_create() but allows for a library context
I<ctx> and property query I<propq> to be used to select algorithm implementations.

=head1 NOTES

The parameters I<nid_key>, I<nid_cert>, I<iter>, I<mac_iter> and I<keytype>
can all be set to zero and sensible defaults will be used.

These defaults are: AES password based encryption (PBES2 with PBKDF2 and
AES-256-CBC) for private keys and certificates, the PBKDF2 and MAC key
derivation iteration count of B<PKCS12_DEFAULT_ITER> (currently 2048), and
MAC algorithm HMAC with SHA2-256. The MAC key derivation algorithm used
for the outer PKCS#12 structure is PKCS12KDF.

The default MAC iteration count is 1 in order to retain compatibility with
old software which did not interpret MAC iteration counts. If such compatibility
is not required then I<mac_iter> should be set to PKCS12_DEFAULT_ITER.

I<keytype> adds a flag to the store private key. This is a non standard extension
that is only currently interpreted by MSIE. If set to zero the flag is omitted,
if set to B<KEY_SIG> the key can be used for signing only, if set to B<KEY_EX>
it can be used for signing and encryption. This option was useful for old
export grade software which could use signing only keys of arbitrary size but
had restrictions on the permissible sizes of keys which could be used for
encryption.

If I<name> is B<NULL> and I<cert> contains an I<alias> then this will be
used for the corresponding B<friendlyName> in the PKCS12 structure instead.
Similarly, if I<pkey> is NULL and I<cert> contains a I<keyid> then this will be
used for the corresponding B<localKeyID> in the PKCS12 structure instead of the
id calculated from the I<pkey>.

For all certificates in I<ca> then if a certificate contains an I<alias> or
I<keyid> then this will be used for the corresponding B<friendlyName> or
B<localKeyID> in the PKCS12 structure.

Either I<pkey>, I<cert> or both can be B<NULL> to indicate that no key or
certificate is required. In previous versions both had to be present or
a fatal error is returned.

I<nid_key> or I<nid_cert> can be set to -1 indicating that no encryption
should be used.

I<mac_iter> can be set to -1 and the MAC will then be omitted entirely.
This can be useful when running with the FIPS provider as the PKCS12KDF
is not a FIPS approvable algorithm.

PKCS12_create() makes assumptions regarding the encoding of the given pass
phrase.
See L<passphrase-encoding(7)> for more information.

=head1 RETURN VALUES

PKCS12_create() returns a valid B<PKCS12> structure or NULL if an error occurred.

=head1 CONFORMING TO

IETF RFC 7292 (L<https://tools.ietf.org/html/rfc7292>)

=head1 SEE ALSO

L<EVP_KDF-PKCS12KDF(7)>,
L<d2i_PKCS12(3)>,
L<OSSL_PROVIDER-FIPS(7)>,
L<passphrase-encoding(7)>

=head1 HISTORY

PKCS12_create_ex() was added in OpenSSL 3.0.

The defaults for encryption algorithms, MAC algorithm, and the MAC key
derivation iteration count were changed in OpenSSL 3.0 to more modern
standards.

=head1 COPYRIGHT

Copyright 2002-2024 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
