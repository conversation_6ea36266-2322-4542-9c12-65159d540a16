=pod

=head1 NAME

CMS_sign_receipt - create a CMS signed receipt

=head1 SYNOPSIS

 #include <openssl/cms.h>

 CMS_ContentInfo *CMS_sign_receipt(CMS_SignerInfo *si, X509 *signcert,
                                   EVP_PKEY *pkey, STACK_OF(X509) *certs,
                                   unsigned int flags);

=head1 DESCRIPTION

CMS_sign_receipt() creates and returns a CMS signed receipt structure. B<si> is
the B<CMS_SignerInfo> structure containing the signed receipt request.
B<signcert> is the certificate to sign with, B<pkey> is the corresponding
private key.  B<certs> is an optional additional set of certificates to include
in the CMS structure (for example any intermediate CAs in the chain).

B<flags> is an optional set of flags.

=head1 NOTES

This functions behaves in a similar way to CMS_sign() except the flag values
B<CMS_DETACHED>, B<CMS_BINARY>, B<CMS_NOATTR>, B<CMS_TEXT> and B<CMS_STREAM>
are not supported since they do not make sense in the context of signed
receipts.

=head1 RETURN VALUES

CMS_sign_receipt() returns either a valid CMS_ContentInfo structure or NULL if
an error occurred.  The error can be obtained from ERR_get_error(3).

=head1 SEE ALSO

L<ERR_get_error(3)>,
L<CMS_verify_receipt(3)>,
L<CMS_sign(3)>

=head1 COPYRIGHT

Copyright 2008-2016 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
