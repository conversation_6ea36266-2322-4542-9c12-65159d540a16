=pod

=head1 NAME

OSSL_ENCODER_CTX_new_for_pkey,
OSSL_ENCODER_CTX_set_cipher,
OSSL_ENCODER_CTX_set_passphrase,
OSSL_ENCODER_CTX_set_pem_password_cb,
OSSL_ENCODER_CTX_set_passphrase_cb,
OSSL_ENCODER_CTX_set_passphrase_ui
- Encoder routines to encode EVP_PKEYs

=head1 SYNOPSIS

 #include <openssl/encoder.h>

 OSSL_ENCODER_CTX *
 OSSL_ENCODER_CTX_new_for_pkey(const EVP_PKEY *pkey, int selection,
                               const char *output_type,
                               const char *output_structure,
                               const char *propquery);

 int OSSL_ENCODER_CTX_set_cipher(OSSL_ENCODER_CTX *ctx,
                                 const char *cipher_name,
                                 const char *propquery);
 int OSSL_ENCODER_CTX_set_passphrase(OSSL_ENCODER_CTX *ctx,
                                     const unsigned char *kstr,
                                     size_t klen);
 int OSSL_ENCODER_CTX_set_pem_password_cb(OSSL_ENCODER_CTX *ctx,
                                          pem_password_cb *cb, void *cbarg);
 int OSSL_ENCODER_CTX_set_passphrase_ui(OSSL_ENCODER_CTX *ctx,
                                        const UI_METHOD *ui_method,
                                        void *ui_data);
 int OSSL_ENCODER_CTX_set_passphrase_cb(OSSL_ENCODER_CTX *ctx,
                                        OSSL_PASSPHRASE_CALLBACK *cb,
                                        void *cbarg);

=head1 DESCRIPTION

OSSL_ENCODER_CTX_new_for_pkey() is a utility function that creates a
B<OSSL_ENCODER_CTX>, finds all applicable encoder implementations and sets
them up, so almost all the caller has to do next is call functions like
L<OSSL_ENCODER_to_bio(3)>.  I<output_type> determines the final output
encoding, and I<selection> can be used to select what parts of the I<pkey>
should be included in the output.  I<output_type> is further discussed in
L</Output types> below, and I<selection> is further described in
L</Selections>.

Internally, OSSL_ENCODER_CTX_new_for_pkey() uses the names from the
L<EVP_KEYMGMT(3)> implementation associated with I<pkey> to build a list of
applicable encoder implementations that are used to process the I<pkey> into
the encoding named by I<output_type>, with the outermost structure named by
I<output_structure> if that's relevant.  All these implementations are
implicitly fetched, with I<propquery> for finer selection.

If no suitable encoder implementation is found,
OSSL_ENCODER_CTX_new_for_pkey() still creates a B<OSSL_ENCODER_CTX>, but
with no associated encoder (L<OSSL_ENCODER_CTX_get_num_encoders(3)> returns
zero).  This helps the caller to distinguish between an error when creating
the B<OSSL_ENCODER_CTX> and missing encoder implementation, and allows it to
act accordingly.

OSSL_ENCODER_CTX_set_cipher() tells the implementation what cipher
should be used to encrypt encoded keys.  The cipher is given by
name I<cipher_name>.  The interpretation of that I<cipher_name> is
implementation dependent.  The implementation may implement the cipher
directly itself or by other implementations, or it may choose to fetch
it.  If the implementation supports fetching the cipher, then it may
use I<propquery> as properties to be queried for when fetching.
I<cipher_name> may also be NULL, which will result in unencrypted
encoding.

OSSL_ENCODER_CTX_set_passphrase() gives the implementation a
pass phrase to use when encrypting the encoded private key.
Alternatively, a pass phrase callback may be specified with the
following functions.

OSSL_ENCODER_CTX_set_pem_password_cb(), OSSL_ENCODER_CTX_set_passphrase_ui()
and OSSL_ENCODER_CTX_set_passphrase_cb() sets up a callback method that the
implementation can use to prompt for a pass phrase, giving the caller the
choice of preferred pass phrase callback form.  These are called indirectly,
through an internal L<OSSL_PASSPHRASE_CALLBACK(3)> function.

=head2 Output types

The possible B<EVP_PKEY> output types depends on the available
implementations.

OpenSSL has built in implementations for the following output types:

=over 4

=item C<TEXT>

The output is a human readable description of the key.
L<EVP_PKEY_print_private(3)>, L<EVP_PKEY_print_public(3)> and
L<EVP_PKEY_print_params(3)> use this for their output.

=item C<DER>

The output is the DER encoding of the I<selection> of the I<pkey>.

=item C<PEM>

The output is the I<selection> of the I<pkey> in PEM format.

=back

=head2 Selections

I<selection> can be any one of the values described in
L<EVP_PKEY_fromdata(3)/Selections>.

These are only 'hints' since the encoder implementations are free to
determine what makes sense to include in the output, and this may depend on
the desired output.  For example, an EC key in a PKCS#8 structure doesn't
usually include the public key.

=head1 RETURN VALUES

OSSL_ENCODER_CTX_new_for_pkey() returns a pointer to an B<OSSL_ENCODER_CTX>,
or NULL if it couldn't be created.

OSSL_ENCODER_CTX_set_cipher(), OSSL_ENCODER_CTX_set_passphrase(),
OSSL_ENCODER_CTX_set_pem_password_cb(), OSSL_ENCODER_CTX_set_passphrase_ui()
and OSSL_ENCODER_CTX_set_passphrase_cb() all return 1 on success, or 0 on
failure.

=head1 SEE ALSO

L<provider(7)>, L<OSSL_ENCODER(3)>, L<OSSL_ENCODER_CTX(3)>

=head1 HISTORY

The functions described here were added in OpenSSL 3.0.

=head1 COPYRIGHT

Copyright 2019-2021 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
