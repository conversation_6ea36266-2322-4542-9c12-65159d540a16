=pod

=head1 NAME

PKCS12_get_friendlyname - Retrieve the friendlyname attribute from a PKCS#12 safeBag

=head1 SYNOPSIS

 #include <openssl/pkcs12.h>

 char *PKCS12_get_friendlyname(PKCS12_SAFEBAG *bag);

=head1 DESCRIPTION

PKCS12_get_friendlyname() retrieves a UTF-8 string representation of the PKCS#9
friendlyName attribute for a PKCS#12 safeBag item.

I<bag> is the B<PKCS12_SAFEBAG> to retrieve the attribute from.

=head1 RETURN VALUES

A UTF-8 string, or NULL if the attribute was either not present or an error occurred.

The returned string is allocated by OpenSSL and should be freed by the user.

=head1 SEE ALSO

L<PKCS12_add_friendlyname_asc(3)>

=head1 COPYRIGHT

Copyright 2019 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
