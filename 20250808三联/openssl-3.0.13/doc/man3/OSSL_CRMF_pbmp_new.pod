=pod

=head1 NAME

OSSL_CRMF_pbm_new,
OSSL_CRMF_pbmp_new
- functions for producing Password-Based MAC (PBM)

=head1 SYNOPSIS

 #include <openssl/crmf.h>

 int OSSL_CRMF_pbm_new(OSSL_LIB_CTX *libctx, const char *propq,
                       const OSSL_CRMF_PBMPARAMETER *pbmp,
                       const unsigned char *msg, size_t msglen,
                       const unsigned char *sec, size_t seclen,
                       unsigned char **mac, size_t *maclen);

 OSSL_CRMF_PBMPARAMETER *OSSL_CRMF_pbmp_new(OSSL_LIB_CTX *libctx, size_t saltlen,
                                            int owfnid, size_t itercnt,
                                            int macnid);

=head1 DESCRIPTION

OSSL_CRMF_pbm_new() generates a PBM (Password-Based MAC) based on given PBM
parameters I<pbmp>, message I<msg>, and secret I<sec>, along with the respective
lengths I<msglen> and I<seclen>.
The optional library context I<libctx> and I<propq> parameters may be used
to influence the selection of the MAC algorithm referenced in the I<pbmp>;
see L<crypto(7)/ALGORITHM FETCHING> for further information.
On success writes the address of the newly
allocated MAC via the I<mac> reference parameter and writes the length via the
I<maclen> reference parameter unless it its NULL.

OSSL_CRMF_pbmp_new() initializes and returns a new B<PBMParameter> structure
with a new random salt of given length I<saltlen>,
OWF (one-way function) NID I<owfnid>, OWF iteration count I<itercnt>,
and MAC NID I<macnid>.
The library context I<libctx> parameter may be used to select the provider
for the random number generation (DRBG) and may be NULL for the default.

=head1 NOTES

The algorithms for the OWF (one-way function) and for the MAC (message
authentication code) may be any with a NID defined in F<< <openssl/objects.h> >>.
As specified by RFC 4210, these should include NID_hmac_sha1.

RFC 4210 recommends that the salt SHOULD be at least 8 bytes (64 bits) long,
where 16 bytes is common.

The iteration count must be at least 100, as stipulated by RFC 4211, and is
limited to at most 100000 to avoid DoS through manipulated or otherwise
malformed input.

=head1 RETURN VALUES

OSSL_CRMF_pbm_new() returns 1 on success, 0 on error.

OSSL_CRMF_pbmp_new() returns a new and initialized OSSL_CRMF_PBMPARAMETER
structure, or NULL on error.

=head1 EXAMPLES

 OSSL_CRMF_PBMPARAMETER *pbm = NULL;
 unsigned char *msg = "Hello";
 unsigned char *sec = "SeCrEt";
 unsigned char *mac = NULL;
 size_t maclen;

 if ((pbm = OSSL_CRMF_pbmp_new(16, NID_sha256, 500, NID_hmac_sha1) == NULL))
     goto err;
 if (!OSSL_CRMF_pbm_new(pbm, msg, 5, sec, 6, &mac, &maclen))
     goto err;

=head1 SEE ALSO

RFC 4211 section 4.4

=head1 HISTORY

The OpenSSL CRMF support was added in OpenSSL 3.0.

=head1 COPYRIGHT

Copyright 2007-2021 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
