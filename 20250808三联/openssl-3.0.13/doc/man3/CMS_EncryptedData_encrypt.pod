=pod

=head1 NAME

CMS_EncryptedData_encrypt_ex, CMS_EncryptedData_encrypt
- Create CMS EncryptedData

=head1 SYNOPSIS

 #include <openssl/cms.h>

 CMS_ContentInfo *CMS_EncryptedData_encrypt_ex(BIO *in,
                                               const EVP_CIPHER *cipher,
                                               const unsigned char *key,
                                               size_t keylen,
                                               unsigned int flags,
                                               OSSL_LIB_CTX *ctx,
                                               const char *propq);

 CMS_ContentInfo *CMS_EncryptedData_encrypt(BIO *in,
     const EVP_CIPHER *cipher, const unsigned char *key, size_t keylen,
     unsigned int flags);

=head1 DESCRIPTION

CMS_EncryptedData_encrypt_ex() creates a B<CMS_ContentInfo> structure
with a type B<NID_pkcs7_encrypted>. I<in> is a BIO containing the data to
encrypt using I<cipher> and the encryption key I<key> of size I<keylen> bytes.
The library context I<libctx> and the property query I<propq> are used when
retrieving algorithms from providers. I<flags> is a set of optional flags.

The I<flags> field supports the options B<CMS_DETACHED>, B<CMS_STREAM> and
B<CMS_PARTIAL>. Internally CMS_final() is called unless B<CMS_STREAM> and/or
B<CMS_PARTIAL> is specified.

The algorithm passed in the I<cipher> parameter must support ASN1 encoding of
its parameters.

The B<CMS_ContentInfo> structure can be freed using L<CMS_ContentInfo_free(3)>.

CMS_EncryptedData_encrypt() is similar to CMS_EncryptedData_encrypt_ex()
but uses default values of NULL for the library context I<libctx> and the
property query I<propq>.

=head1 RETURN VALUES

If the allocation fails, CMS_EncryptedData_encrypt_ex() and
CMS_EncryptedData_encrypt() return NULL and set an error code that can be
obtained by L<ERR_get_error(3)>. Otherwise they return a pointer to the newly
allocated structure.

=head1 SEE ALSO

L<ERR_get_error(3)>, L<CMS_final(3)>, L<CMS_EncryptedData_decrypt(3)>

=head1 HISTORY

The CMS_EncryptedData_encrypt_ex() method was added in OpenSSL 3.0.

=head1 COPYRIGHT

Copyright 2020-2021 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
