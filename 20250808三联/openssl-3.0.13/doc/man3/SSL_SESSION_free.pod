=pod

=head1 NAME

SSL_SESSION_new,
SSL_SESSION_dup,
SSL_SESSION_up_ref,
SSL_SESSION_free - create, free and manage SSL_SESSION structures

=head1 SYNOPSIS

 #include <openssl/ssl.h>

 SSL_SESSION *SSL_SESSION_new(void);
 SSL_SESSION *SSL_SESSION_dup(const SSL_SESSION *src);
 int SSL_SESSION_up_ref(SSL_SESSION *ses);
 void SSL_SESSION_free(SSL_SESSION *session);

=head1 DESCRIPTION

SSL_SESSION_new() creates a new SSL_SESSION structure and returns a pointer to
it.

SSL_SESSION_dup() creates a new SSL_SESSION structure that is a copy of B<src>.
The copy is not owned by any cache that B<src> may have been in.

SSL_SESSION_up_ref() increments the reference count on the given SSL_SESSION
structure.

SSL_SESSION_free() decrements the reference count of B<session> and removes
the B<SSL_SESSION> structure pointed to by B<session> and frees up the allocated
memory, if the reference count has reached 0.
If B<session> is NULL nothing is done.

=head1 NOTES

SSL_SESSION objects are allocated, when a TLS/SSL handshake operation
is successfully completed. Depending on the settings, see
L<SSL_CTX_set_session_cache_mode(3)>,
the SSL_SESSION objects are internally referenced by the SSL_CTX and
linked into its session cache. SSL objects may be using the SSL_SESSION object;
as a session may be reused, several SSL objects may be using one SSL_SESSION
object at the same time. It is therefore crucial to keep the reference
count (usage information) correct and not delete a SSL_SESSION object
that is still used, as this may lead to program failures due to
dangling pointers. These failures may also appear delayed, e.g.
when an SSL_SESSION object was completely freed as the reference count
incorrectly became 0, but it is still referenced in the internal
session cache and the cache list is processed during a
L<SSL_CTX_flush_sessions(3)> operation.

SSL_SESSION_free() must only be called for SSL_SESSION objects, for
which the reference count was explicitly incremented (e.g.
by calling SSL_get1_session(), see L<SSL_get_session(3)>)
or when the SSL_SESSION object was generated outside a TLS handshake
operation, e.g. by using L<d2i_SSL_SESSION(3)>.
It must not be called on other SSL_SESSION objects, as this would cause
incorrect reference counts and therefore program failures.

=head1 RETURN VALUES

SSL_SESSION_new returns a pointer to the newly allocated SSL_SESSION structure
or NULL on error.

SSL_SESSION_dup returns a pointer to the new copy or NULL on error.

SSL_SESSION_up_ref returns 1 on success or 0 on error.

=head1 SEE ALSO

L<ssl(7)>, L<SSL_get_session(3)>,
L<SSL_CTX_set_session_cache_mode(3)>,
L<SSL_CTX_flush_sessions(3)>,
L<d2i_SSL_SESSION(3)>

=head1 HISTORY

The SSL_SESSION_dup() function was added in OpenSSL 1.1.1.

=head1 COPYRIGHT

Copyright 2000-2023 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
