=pod

=head1 NAME

EVP_BytesToKey - password based encryption routine

=head1 SYNOPSIS

 #include <openssl/evp.h>

 int EVP_BytesToKey(const EVP_CIPHER *type, const EVP_MD *md,
                    const unsigned char *salt,
                    const unsigned char *data, int datal, int count,
                    unsigned char *key, unsigned char *iv);

=head1 DESCRIPTION

EVP_BytesToKey() derives a key and IV from various parameters. B<type> is
the cipher to derive the key and IV for. B<md> is the message digest to use.
The B<salt> parameter is used as a salt in the derivation: it should point to
an 8 byte buffer or NULL if no salt is used. B<data> is a buffer containing
B<datal> bytes which is used to derive the keying data. B<count> is the
iteration count to use. The derived key and IV will be written to B<key>
and B<iv> respectively.

=head1 NOTES

A typical application of this function is to derive keying material for an
encryption algorithm from a password in the B<data> parameter.

Increasing the B<count> parameter slows down the algorithm which makes it
harder for an attacker to perform a brute force attack using a large number
of candidate passwords.

If the total key and IV length is less than the digest length and
B<MD5> is used then the derivation algorithm is compatible with PKCS#5 v1.5
otherwise a non standard extension is used to derive the extra data.

Newer applications should use a more modern algorithm such as PBKDF2 as
defined in PKCS#5v2.1 and provided by PKCS5_PBKDF2_HMAC.

=head1 KEY DERIVATION ALGORITHM

The key and IV is derived by concatenating D_1, D_2, etc until
enough data is available for the key and IV. D_i is defined as:

        D_i = HASH^count(D_(i-1) || data || salt)

where || denotes concatenation, D_0 is empty, HASH is the digest
algorithm in use, HASH^1(data) is simply HASH(data), HASH^2(data)
is HASH(HASH(data)) and so on.

The initial bytes are used for the key and the subsequent bytes for
the IV.

=head1 RETURN VALUES

If B<data> is NULL, then EVP_BytesToKey() returns the number of bytes
needed to store the derived key.
Otherwise, EVP_BytesToKey() returns the size of the derived key in bytes,
or 0 on error.

=head1 SEE ALSO

L<evp(7)>, L<RAND_bytes(3)>,
L<PKCS5_PBKDF2_HMAC(3)>,
L<EVP_EncryptInit(3)>

=head1 COPYRIGHT

Copyright 2001-2016 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
