=pod

=head1 NAME

SSL_CTX_set_dh_auto, SSL_set_dh_auto, SSL_CTX_set0_tmp_dh_pkey,
SSL_set0_tmp_dh_pkey, SSL_CTX_set_tmp_dh_callback, SSL_CTX_set_tmp_dh,
SSL_set_tmp_dh_callback, SSL_set_tmp_dh
- handle DH keys for ephemeral key exchange

=head1 SYNOPSIS

 #include <openssl/ssl.h>

 long SSL_CTX_set_dh_auto(SSL_CTX *ctx, int onoff);
 long SSL_set_dh_auto(SSL *s, int onoff);
 int SSL_CTX_set0_tmp_dh_pkey(SSL_CTX *ctx, EVP_PKEY *dhpkey);
 int SSL_set0_tmp_dh_pkey(SSL *s, EVP_PKEY *dhpkey);

The following functions have been deprecated since OpenSSL 3.0, and can be
hidden entirely by defining B<OPENSSL_API_COMPAT> with a suitable version value,
see L<openssl_user_macros(7)>:

 void SSL_CTX_set_tmp_dh_callback(SSL_CTX *ctx,
                                  DH *(*tmp_dh_callback)(SSL *ssl, int is_export,
                                                         int keylength));
 long SSL_CTX_set_tmp_dh(SSL_CTX *ctx, DH *dh);

 void SSL_set_tmp_dh_callback(SSL *ctx,
                              DH *(*tmp_dh_callback)(SSL *ssl, int is_export,
                                                     int keylength));
 long SSL_set_tmp_dh(SSL *ssl, DH *dh);

=head1 DESCRIPTION

The functions described on this page are relevant for servers only.

Some ciphersuites may use ephemeral Diffie-Hellman (DH) key exchange. In these
cases, the session data is negotiated using the ephemeral/temporary DH key and
the key supplied and certified by the certificate chain is only used for
signing. Anonymous ciphers (without a permanent server key) also use ephemeral
DH keys.

Using ephemeral DH key exchange yields forward secrecy as the connection
can only be decrypted when the DH key is known. By generating a temporary
DH key inside the server application that is lost when the application
is left, it becomes impossible for an attacker to decrypt past sessions,
even if they get hold of the normal (certified) key, as this key was
only used for signing.

In order to perform a DH key exchange the server must use a DH group
(DH parameters) and generate a DH key. The server will always generate
a new DH key during the negotiation.

As generating DH parameters is extremely time consuming, an application
should not generate the parameters on the fly. DH parameters can be reused, as
the actual key is newly generated during the negotiation.

Typically applications should use well known DH parameters that have built-in
support in OpenSSL. The macros SSL_CTX_set_dh_auto() and SSL_set_dh_auto()
configure OpenSSL to use the default built-in DH parameters for the B<SSL_CTX>
and B<SSL> objects respectively. Passing a value of 1 in the I<onoff> parameter
switches the feature on, and passing a value of 0 switches it off. The default
setting is off.

If "auto" DH parameters are switched on then the parameters will be selected to
be consistent with the size of the key associated with the server's certificate.
If there is no certificate (e.g. for PSK ciphersuites), then it it will be
consistent with the size of the negotiated symmetric cipher key.

Applications may supply their own DH parameters instead of using the built-in
values. This approach is discouraged and applications should in preference use
the built-in parameter support described above. Applications wishing to supply
their own DH parameters should call SSL_CTX_set0_tmp_dh_pkey() or
SSL_set0_tmp_dh_pkey() to supply the parameters for the B<SSL_CTX> or B<SSL>
respectively. The parameters should be supplied in the I<dhpkey> argument as
an B<EVP_PKEY> containing DH parameters. Ownership of the I<dhpkey> value is
passed to the B<SSL_CTX> or B<SSL> object as a result of this call, and so the
caller should not free it if the function call is successful.

The deprecated macros SSL_CTX_set_tmp_dh() and SSL_set_tmp_dh() do the same
thing as SSL_CTX_set0_tmp_dh_pkey() and SSL_set0_tmp_dh_pkey() except that the
DH parameters are supplied in a B<DH> object instead in the I<dh> argument, and
ownership of the B<DH> object is retained by the application. Applications
should use "auto" parameters instead, or call SSL_CTX_set0_tmp_dh_pkey() or
SSL_set0_tmp_dh_pkey() as appropriate.

An application may instead specify the DH parameters via a callback function
using the functions SSL_CTX_set_tmp_dh_callback() or SSL_set_tmp_dh_callback()
to set the callback for the B<SSL_CTX> or B<SSL> object respectively. These
functions are deprecated. Applications should instead use "auto" parameters, or
specify the parameters via SSL_CTX_set0_tmp_dh_pkey() or SSL_set0_tmp_dh_pkey()
as appropriate.

The callback will be invoked during a connection when DH parameters are
required. The B<SSL> object for the current connection is supplied as an
argument. Previous versions of OpenSSL used the B<is_export> and B<keylength>
arguments to control parameter generation for export and non-export
cipher suites. Modern OpenSSL does not support export ciphersuites and so these
arguments are unused and can be ignored by the callback. The callback should
return the parameters to be used in a DH object. Ownership of the DH object is
retained by the application and should later be freed.

=head1 RETURN VALUES

All of these functions/macros return 1 for success or 0 on error.

=head1 SEE ALSO

L<ssl(7)>, L<SSL_CTX_set_cipher_list(3)>,
L<SSL_CTX_set_options(3)>,
L<openssl-ciphers(1)>, L<openssl-dhparam(1)>

=head1 COPYRIGHT

Copyright 2001-2023 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
