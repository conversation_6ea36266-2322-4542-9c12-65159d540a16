=pod

=head1 NAME

OSSL_CMP_MSG_http_perform
- client-side HTTP(S) transfer of a CMP request-response pair

=head1 SYNOPSIS

 #include <openssl/cmp.h>

 OSSL_CMP_MSG *OSSL_CMP_MSG_http_perform(OSSL_CMP_CTX *ctx,
                                         const OSSL_CMP_MSG *req);

=head1 DESCRIPTION

OSSL_CMP_MSG_http_perform() sends the given PKIMessage I<req>
to the CMP server specified in I<ctx> via L<OSSL_CMP_CTX_set1_server(3)>
and optionally L<OSSL_CMP_CTX_set_serverPort(3)>, using
any "CMP alias" optionally specified via L<OSSL_CMP_CTX_set1_serverPath(3)>.
The default port is 80 for HTTP and 443 for HTTPS; the default path is "/".
On success the function returns the server's response PKIMessage.

The function makes use of any HTTP callback function
set via L<OSSL_CMP_CTX_set_http_cb(3)>.
It respects any timeout value set via L<OSSL_CMP_CTX_set_option(3)>
with an B<OSSL_CMP_OPT_MSG_TIMEOUT> argument.
It also respects any HTTP(S) proxy options set via L<OSSL_CMP_CTX_set1_proxy(3)>
and L<OSSL_CMP_CTX_set1_no_proxy(3)> and the respective environment variables.
Proxying plain HTTP is supported directly,
while using a proxy for HTTPS connections requires a suitable callback function
such as L<OSSL_HTTP_proxy_connect(3)>.

=head1 NOTES

CMP is defined in RFC 4210.
HTTP transfer for CMP is defined in RFC 6712.

=head1 RETURN VALUES

OSSL_CMP_MSG_http_perform() returns a CMP message on success, else NULL.

=head1 SEE ALSO

L<OSSL_CMP_CTX_new(3)>, L<OSSL_HTTP_proxy_connect(3)>.

=head1 HISTORY

The OpenSSL CMP support was added in OpenSSL 3.0.

=head1 COPYRIGHT

Copyright 2007-2021 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
