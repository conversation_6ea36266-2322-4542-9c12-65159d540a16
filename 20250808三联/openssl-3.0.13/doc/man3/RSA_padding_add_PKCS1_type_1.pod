=pod

=head1 NAME

RSA_padding_add_PKCS1_type_1, RSA_padding_check_PKCS1_type_1,
RSA_padding_add_PKCS1_type_2, RSA_padding_check_PKCS1_type_2,
RSA_padding_add_PKCS1_OAEP, RSA_padding_check_PKCS1_OAEP,
RSA_padding_add_PKCS1_OAEP_mgf1, RSA_padding_check_PKCS1_OAEP_mgf1,
RSA_padding_add_none, RSA_padding_check_none - asymmetric encryption
padding

=head1 SYNOPSIS

 #include <openssl/rsa.h>

The following functions have been deprecated since OpenSSL 3.0, and can be
hidden entirely by defining B<OPENSSL_API_COMPAT> with a suitable version value,
see L<openssl_user_macros(7)>:

 int RSA_padding_add_PKCS1_type_1(unsigned char *to, int tlen,
                                  const unsigned char *f, int fl);

 int RSA_padding_check_PKCS1_type_1(unsigned char *to, int tlen,
                                    const unsigned char *f, int fl, int rsa_len);

 int RSA_padding_add_PKCS1_type_2(unsigned char *to, int tlen,
                                  const unsigned char *f, int fl);

 int RSA_padding_check_PKCS1_type_2(unsigned char *to, int tlen,
                                    const unsigned char *f, int fl, int rsa_len);

 int RSA_padding_add_PKCS1_OAEP(unsigned char *to, int tlen,
                                const unsigned char *f, int fl,
                                const unsigned char *p, int pl);

 int RSA_padding_check_PKCS1_OAEP(unsigned char *to, int tlen,
                                  const unsigned char *f, int fl, int rsa_len,
                                  const unsigned char *p, int pl);

 int RSA_padding_add_PKCS1_OAEP_mgf1(unsigned char *to, int tlen,
                                     const unsigned char *f, int fl,
                                     const unsigned char *p, int pl,
                                     const EVP_MD *md, const EVP_MD *mgf1md);

 int RSA_padding_check_PKCS1_OAEP_mgf1(unsigned char *to, int tlen,
                                       const unsigned char *f, int fl, int rsa_len,
                                       const unsigned char *p, int pl,
                                       const EVP_MD *md, const EVP_MD *mgf1md);

 int RSA_padding_add_none(unsigned char *to, int tlen,
                          const unsigned char *f, int fl);

 int RSA_padding_check_none(unsigned char *to, int tlen,
                            const unsigned char *f, int fl, int rsa_len);

=head1 DESCRIPTION

All of the functions described on this page are deprecated.
Applications should instead use the EVP PKEY APIs.

The RSA_padding_xxx_xxx() functions are called from the RSA encrypt,
decrypt, sign and verify functions. Normally they should not be called
from application programs.

However, they can also be called directly to implement padding for other
asymmetric ciphers. RSA_padding_add_PKCS1_OAEP() and
RSA_padding_check_PKCS1_OAEP() may be used in an application combined
with B<RSA_NO_PADDING> in order to implement OAEP with an encoding
parameter.

RSA_padding_add_xxx() encodes B<fl> bytes from B<f> so as to fit into
B<tlen> bytes and stores the result at B<to>. An error occurs if B<fl>
does not meet the size requirements of the encoding method.

The following encoding methods are implemented:

=over 4

=item PKCS1_type_1

PKCS #1 v2.0 EMSA-PKCS1-v1_5 (PKCS #1 v1.5 block type 1); used for signatures

=item PKCS1_type_2

PKCS #1 v2.0 EME-PKCS1-v1_5 (PKCS #1 v1.5 block type 2)

=item PKCS1_OAEP

PKCS #1 v2.0 EME-OAEP

=item none

simply copy the data

=back

The random number generator must be seeded prior to calling
RSA_padding_add_xxx().
If the automatic seeding or reseeding of the OpenSSL CSPRNG fails due to
external circumstances (see L<RAND(7)>), the operation will fail.

RSA_padding_check_xxx() verifies that the B<fl> bytes at B<f> contain
a valid encoding for a B<rsa_len> byte RSA key in the respective
encoding method and stores the recovered data of at most B<tlen> bytes
(for B<RSA_NO_PADDING>: of size B<tlen>)
at B<to>.

For RSA_padding_xxx_OAEP(), B<p> points to the encoding parameter
of length B<pl>. B<p> may be B<NULL> if B<pl> is 0.

For RSA_padding_xxx_OAEP_mgf1(), B<md> points to the md hash,
if B<md> is B<NULL> that means md=sha1, and B<mgf1md> points to
the mgf1 hash, if B<mgf1md> is B<NULL> that means mgf1md=md.

=head1 RETURN VALUES

The RSA_padding_add_xxx() functions return 1 on success, 0 on error.
The RSA_padding_check_xxx() functions return the length of the
recovered data, -1 on error. Error codes can be obtained by calling
L<ERR_get_error(3)>.

=head1 WARNINGS

The result of RSA_padding_check_PKCS1_type_2() is a very sensitive
information which can potentially be used to mount a Bleichenbacher
padding oracle attack. This is an inherent weakness in the PKCS #1
v1.5 padding design. Prefer PKCS1_OAEP padding. If that is not
possible, the result of RSA_padding_check_PKCS1_type_2() should be
checked in constant time if it matches the expected length of the
plaintext and additionally some application specific consistency
checks on the plaintext need to be performed in constant time.
If the plaintext is rejected it must be kept secret which of the
checks caused the application to reject the message.
Do not remove the zero-padding from the decrypted raw RSA data
which was computed by RSA_private_decrypt() with B<RSA_NO_PADDING>,
as this would create a small timing side channel which could be
used to mount a Bleichenbacher attack against any padding mode
including PKCS1_OAEP.

=head1 SEE ALSO

L<RSA_public_encrypt(3)>,
L<RSA_private_decrypt(3)>,
L<RSA_sign(3)>, L<RSA_verify(3)>,
L<RAND(7)>

=head1 HISTORY

All of these functions were deprecated in OpenSSL 3.0.

=head1 COPYRIGHT

Copyright 2000-2021 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
