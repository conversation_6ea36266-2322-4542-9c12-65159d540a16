=pod

=head1 NAME

RSA_sign, RSA_verify - RSA signatures

=head1 SYNOPSIS

 #include <openssl/rsa.h>

The following functions have been deprecated since OpenSSL 3.0, and can be
hidden entirely by defining B<OPENSSL_API_COMPAT> with a suitable version value,
see L<openssl_user_macros(7)>:

 int RSA_sign(int type, const unsigned char *m, unsigned int m_len,
              unsigned char *sigret, unsigned int *siglen, RSA *rsa);

 int RSA_verify(int type, const unsigned char *m, unsigned int m_len,
                unsigned char *sigbuf, unsigned int siglen, RSA *rsa);

=head1 DESCRIPTION

All of the functions described on this page are deprecated.
Applications should instead use L<EVP_PKEY_sign_init(3)>, L<EVP_PKEY_sign(3)>,
L<EVP_PKEY_verify_init(3)> and L<EVP_PKEY_verify(3)>.

RSA_sign() signs the message digest B<m> of size B<m_len> using the
private key B<rsa> using RSASSA-PKCS1-v1_5 as specified in RFC 3447. It
stores the signature in B<sigret> and the signature size in B<siglen>.
B<sigret> must point to RSA_size(B<rsa>) bytes of memory.
Note that PKCS #1 adds meta-data, placing limits on the size of the
key that can be used.
See L<RSA_private_encrypt(3)> for lower-level
operations.

B<type> denotes the message digest algorithm that was used to generate
B<m>.
If B<type> is B<NID_md5_sha1>,
an SSL signature (MD5 and SHA1 message digests with PKCS #1 padding
and no algorithm identifier) is created.

RSA_verify() verifies that the signature B<sigbuf> of size B<siglen>
matches a given message digest B<m> of size B<m_len>. B<type> denotes
the message digest algorithm that was used to generate the signature.
B<rsa> is the signer's public key.

=head1 RETURN VALUES

RSA_sign() returns 1 on success and 0 for failure.
RSA_verify() returns 1 on successful verification and 0 for failure.

The error codes can be obtained by L<ERR_get_error(3)>.

=head1 CONFORMING TO

SSL, PKCS #1 v2.0

=head1 SEE ALSO

L<ERR_get_error(3)>,
L<RSA_private_encrypt(3)>,
L<RSA_public_decrypt(3)>

=head1 HISTORY

All of these functions were deprecated in OpenSSL 3.0.

=head1 COPYRIGHT

Copyright 2000-2021 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
