=pod

=head1 NAME

BN_mod_inverse - compute inverse modulo n

=head1 SYNOPSIS

 #include <openssl/bn.h>

 BIGNUM *BN_mod_inverse(BIGNUM *r, BIGNUM *a, const BIGNUM *n,
                        BN_CTX *ctx);

=head1 DESCRIPTION

BN_mod_inverse() computes the inverse of B<a> modulo B<n>
places the result in B<r> (C<(a*r)%n==1>). If B<r> is NULL,
a new B<BIGNUM> is created.

B<ctx> is a previously allocated B<BN_CTX> used for temporary
variables. B<r> may be the same B<BIGNUM> as B<a>.

=head1 NOTES

It is an error to use the same B<BIGNUM> as B<n>.

=head1 RETURN VALUES

BN_mod_inverse() returns the B<BIGNUM> containing the inverse, and
NULL on error. The error codes can be obtained by L<ERR_get_error(3)>.

=head1 SEE ALSO

L<ERR_get_error(3)>, L<BN_add(3)>

=head1 COPYRIGHT

Copyright 2000-2017 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
