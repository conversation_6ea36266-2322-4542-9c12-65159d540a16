=pod

=head1 NAME

OSSL_HTTP_adapt_proxy,
OSSL_parse_url,
OSSL_HTTP_parse_url,
OCSP_parse_url
- http utility functions

=head1 SYNOPSIS

 #include <openssl/http.h>

 const char *OSSL_HTTP_adapt_proxy(const char *proxy, const char *no_proxy,
                                   const char *server, int use_ssl);

 int OSSL_parse_url(const char *url, char **pscheme, char **puser, char **phost,
                    char **pport, int *pport_num,
                    char **ppath, char **pquery, char **pfrag);
 int OSSL_HTTP_parse_url(const char *url,
                         int *pssl, char **puser, char **phost,
                         char **pport, int *pport_num,
                         char **ppath, char **pquery, char **pfrag);

The following functions have been deprecated since OpenSSL 3.0, and can be
hidden entirely by defining B<OPENSSL_API_COMPAT> with a suitable version value,
see L<openssl_user_macros(7)>:

 int OCSP_parse_url(const char *url, char **phost, char **pport, char **ppath,
                    int *pssl);

=head1 DESCRIPTION

OSSL_HTTP_adapt_proxy() takes an optional proxy hostname I<proxy>
and returns it transformed according to the optional I<no_proxy> parameter,
I<server>, I<use_ssl>, and the applicable environment variable, as follows.
If I<proxy> is NULL, take any default value from the C<http_proxy>
environment variable, or from C<https_proxy> if I<use_ssl> is nonzero.
If this still does not yield a proxy hostname,
take any further default value from the C<HTTP_PROXY>
environment variable, or from C<HTTPS_PROXY> if I<use_ssl> is nonzero.
If I<no_proxy> is NULL, take any default exclusion value from the C<no_proxy>
environment variable, or else from C<NO_PROXY>.
Return the determined proxy hostname unless the exclusion contains I<server>.
Otherwise return NULL.

OSSL_parse_url() parses its input string I<url> as a URL of the form
C<[scheme://][userinfo@]host[:port][/path][?query][#fragment]> and splits it up
into scheme, userinfo, host, port, path, query, and fragment components.
The host (or server) component may be a DNS name or an IP address
where IPv6 addresses should be enclosed in square brackets C<[> and C<]>.
The port component is optional and defaults to C<0>.
If given, it must be in decimal form.  If the I<pport_num> argument is not NULL
the integer value of the port number is assigned to I<*pport_num> on success.
The path component is also optional and defaults to C</>.
Each non-NULL result pointer argument I<pscheme>, I<puser>, I<phost>, I<pport>,
I<ppath>, I<pquery>, and I<pfrag>, is assigned the respective url component.
On success, they are guaranteed to contain non-NULL string pointers, else NULL.
It is the responsibility of the caller to free them using L<OPENSSL_free(3)>.
If I<pquery> is NULL, any given query component is handled as part of the path.
A string returned via I<*ppath> is guaranteed to begin with a C</> character.
For absent scheme, userinfo, port, query, and fragment components
an empty string is provided.

OSSL_HTTP_parse_url() is a special form of OSSL_parse_url()
where the scheme, if given, must be C<http> or C<https>.
If I<pssl> is not NULL, I<*pssl> is assigned 1 in case parsing was successful
and the scheme is C<https>, else 0.
The port component is optional and defaults to C<443> if the scheme is C<https>,
else C<80>.
Note that relative paths must be given with a leading C</>,
otherwise the first path element is interpreted as the hostname.

Calling the deprecated function OCSP_parse_url(url, host, port, path, ssl)
is equivalent to
OSSL_HTTP_parse_url(url, ssl, NULL, host, port, NULL, path, NULL, NULL).

=head1 RETURN VALUES

OSSL_HTTP_adapt_proxy() returns NULL if no proxy is to be used,
otherwise a constant proxy hostname string,
which is either the proxy name handed in or an environment variable value.

OSSL_parse_url(), OSSL_HTTP_parse_url(), and OCSP_parse_url()
return 1 on success, 0 on error.

=head1 SEE ALSO

L<OSSL_HTTP_transfer(3)>

=head1 HISTORY

OSSL_HTTP_adapt_proxy(),
OSSL_parse_url() and OSSL_HTTP_parse_url() were added in OpenSSL 3.0.
OCSP_parse_url() was deprecated in OpenSSL 3.0.

=head1 COPYRIGHT

Copyright 2019-2023 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
