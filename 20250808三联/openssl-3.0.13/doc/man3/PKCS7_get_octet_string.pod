=pod

=head1 NAME

PKCS7_get_octet_string - return octet string from a PKCS#7 envelopedData structure

=head1 SYNOPSIS

 #include <openssl/pkcs7.h>

 ASN1_OCTET_STRING *PKCS7_get_octet_string(PKCS7 *p7);

=head1 DESCRIPTION

PKCS7_get_octet_string() returns a pointer to an ASN1 octet string from a
PKCS#7 envelopedData structure or B<NULL> if the structure cannot be parsed.

=head1 NOTES

As the B<0> implies, PKCS7_get_octet_string() returns internal pointers which
should not be freed by the caller.

=head1 RETURN VALUES

PKCS7_get_octet_string() returns an ASN1_OCTET_STRING pointer.

=head1 SEE ALSO

L<PKCS7_type_is_data(3)>

=head1 COPYRIGHT

Copyright 2002-2020 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
