=pod

=head1 NAME

OPENSSL_Applink - glue between OpenSSL BIO and Win32 compiler run-time

=head1 SYNOPSIS

 __declspec(dllexport) void **OPENSSL_Applink();

=head1 DESCRIPTION

OPENSSL_Applink is application-side interface which provides a glue
between OpenSSL BIO layer and Win32 compiler run-time environment.
Even though it appears at application side, it's essentially OpenSSL
private interface. For this reason application developers are not
expected to implement it, but to compile provided module with
compiler of their choice and link it into the target application.
The referred module is available as F<applink.c>, located alongside
the public header files (only on the platforms where applicable).

=head1 RETURN VALUES

Not available.

=head1 COPYRIGHT

Copyright 2004-2018 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file L<PERSON><PERSON><PERSON> in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
