=pod

=head1 NAME

EVP_KEYEXCH_fetch, <PERSON><PERSON>_KEY<PERSON>CH_free, <PERSON><PERSON>_<PERSON>E<PERSON><PERSON>CH_up_ref,
<PERSON><PERSON>_KEYEXCH_get0_provider, E<PERSON>_KEYEXCH_is_a, <PERSON><PERSON>_KEYEXCH_do_all_provided,
<PERSON><PERSON>_KEYEXCH_names_do_all, <PERSON><PERSON>_KEYEXCH_get0_name, <PERSON><PERSON>_KEYEXCH_get0_description,
E<PERSON>_KEYEXCH_gettable_ctx_params, EVP_KEYEXCH_settable_ctx_params
- Functions to manage EVP_KEYEXCH algorithm objects

=head1 SYNOPSIS

 #include <openssl/evp.h>

 EVP_KEYEXCH *EVP_KEYEXCH_fetch(OSSL_LIB_CTX *ctx, const char *algorithm,
                                const char *properties);
 void EVP_KEYEXCH_free(EVP_KEYEXCH *exchange);
 int EVP_KEYEXCH_up_ref(EVP_KEYEXCH *exchange);
 OSSL_PROVIDER *EVP_KEYEXCH_get0_provider(const E<PERSON>_KEYEXCH *exchange);
 int EVP_KEYEXCH_is_a(const EVP_KEYEXCH *exchange, const char *name);
 const char *EVP_KEYEXCH_get0_name(const EVP_KEYEXCH *exchange);
 void EVP_KEYEXCH_do_all_provided(OSSL_LIB_CTX *libctx,
                                  void (*fn)(EVP_KEYEXCH *exchange, void *arg),
                                  void *arg);
 int EVP_KEYEXCH_names_do_all(const EVP_KEYEXCH *exchange,
                              void (*fn)(const char *name, void *data),
                              void *data);
 const char *EVP_KEYEXCH_get0_description(const EVP_KEYEXCH *keyexch);
 const OSSL_PARAM *EVP_KEYEXCH_gettable_ctx_params(const EVP_KEYEXCH *keyexch);
 const OSSL_PARAM *EVP_KEYEXCH_settable_ctx_params(const EVP_KEYEXCH *keyexch);

=head1 DESCRIPTION

EVP_KEYEXCH_fetch() fetches the key exchange implementation for the given
I<algorithm> from any provider offering it, within the criteria given
by the I<properties>.
See L<crypto(7)/ALGORITHM FETCHING> for further information.

The returned value must eventually be freed with EVP_KEYEXCH_free().

EVP_KEYEXCH_free() decrements the reference count for the B<EVP_KEYEXCH>
structure. Typically this structure will have been obtained from an earlier call
to EVP_KEYEXCH_fetch(). If the reference count drops to 0 then the
structure is freed.

EVP_KEYEXCH_up_ref() increments the reference count for an B<EVP_KEYEXCH>
structure.

EVP_KEYEXCH_get0_provider() returns the provider that I<exchange> was
fetched from.

EVP_KEYEXCH_is_a() checks if I<exchange> is an implementation of an
algorithm that's identifiable with I<name>.

EVP_KEYEXCH_get0_name() returns the algorithm name from the provided
implementation for the given I<exchange>. Note that the I<exchange> may have
multiple synonyms associated with it. In this case the first name from the
algorithm definition is returned. Ownership of the returned string is retained
by the I<exchange> object and should not be freed by the caller.

EVP_KEYEXCH_names_do_all() traverses all names for the I<exchange>, and
calls I<fn> with each name and I<data>.

EVP_KEYEXCH_get0_description() returns a description of the I<keyexch>, meant
for display and human consumption.  The description is at the discretion of
the I<keyexch> implementation.

EVP_KEYEXCH_do_all_provided() traverses all key exchange implementations by
all activated providers in the library context I<libctx>, and for each
of the implementations, calls I<fn> with the implementation method and
I<data> as arguments.

EVP_KEYEXCH_gettable_ctx_params() and EVP_KEYEXCH_settable_ctx_params() return
a constant L<OSSL_PARAM(3)> array that describes the names and types of key
parameters that can be retrieved or set by a key exchange algorithm using
L<EVP_PKEY_CTX_get_params(3)> and L<EVP_PKEY_CTX_set_params(3)>.

=head1 RETURN VALUES

EVP_KEYEXCH_fetch() returns a pointer to a B<EVP_KEYEXCH> for success
or NULL for failure.

EVP_KEYEXCH_up_ref() returns 1 for success or 0 otherwise.

EVP_KEYEXCH_names_do_all() returns 1 if the callback was called for all
names. A return value of 0 means that the callback was not called for any names.

EVP_KEYEXCH_is_a() returns 1 of I<exchange> was identifiable,
otherwise 0.

EVP_KEYEXCH_gettable_ctx_params() and EVP_KEYEXCH_settable_ctx_params() return
a constant L<OSSL_PARAM(3)> array or NULL on error.

=head1 SEE ALSO

L<crypto(7)/ALGORITHM FETCHING>, L<OSSL_PROVIDER(3)>

=head1 HISTORY

The functions described here were added in OpenSSL 3.0.

=head1 COPYRIGHT

Copyright 2019-2021 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
