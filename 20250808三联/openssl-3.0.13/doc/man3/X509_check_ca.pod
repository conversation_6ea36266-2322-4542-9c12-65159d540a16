=pod

=head1 NAME

X509_check_ca - check if given certificate is CA certificate

=head1 SYNOPSIS

 #include <openssl/x509v3.h>

 int X509_check_ca(X509 *cert);

=head1 DESCRIPTION

This function checks if given certificate is CA certificate (can be used
to sign other certificates). The certificate must be a complete certificate
otherwise an error is returned.

=head1 RETURN VALUES

Function return 0, if it is not CA certificate, 1 if it is proper X509v3
CA certificate with B<basicConstraints> extension CA:TRUE,
3, if it is self-signed X509 v1 certificate, 4, if it is certificate with
B<keyUsage> extension with bit B<keyCertSign> set, but without
B<basicConstraints>, and 5 if it has outdated Netscape Certificate Type
extension telling that it is CA certificate.

This function will also return 0 on error.

Actually, any nonzero value means that this certificate could have been
used to sign other certificates.

=head1 SEE ALSO

L<X509_verify_cert(3)>,
L<X509_check_issued(3)>,
L<X509_check_purpose(3)>

=head1 COPYRIGHT

Copyright 2015-2021 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
