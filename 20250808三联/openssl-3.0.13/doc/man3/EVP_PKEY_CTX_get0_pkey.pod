=pod

=head1 NAME

EVP_PKEY_CTX_get0_pkey,
EVP_PKEY_CTX_get0_peerkey
- functions for accessing the EVP_PKEY associated with an EVP_PKEY_CTX

=head1 SYNOPSIS

 #include <openssl/evp.h>

 EVP_PKEY *EVP_PKEY_CTX_get0_pkey(EVP_PKEY_CTX *ctx);
 EVP_PKEY *EVP_PKEY_CTX_get0_peerkey(EVP_PKEY_CTX *ctx);

=head1 DESCRIPTION

EVP_PKEY_CTX_get0_pkey() is used to access the B<EVP_PKEY>
associated with the given B<EVP_PKEY_CTX> I<ctx>.
The B<EVP_PKEY> obtained is the one used for creating the B<EVP_PKEY_CTX>
using either L<EVP_PKEY_CTX_new(3)> or L<EVP_PKEY_CTX_new_from_pkey(3)>.

EVP_PKEY_CTX_get0_peerkey() is used to access the peer B<EVP_PKEY>
associated with the given B<EVP_PKEY_CTX> I<ctx>.
The peer B<EVP_PKEY> obtained is the one set using
either L<EVP_PKEY_derive_set_peer(3)> or L<EVP_PKEY_derive_set_peer_ex(3)>.

=head1 RETURN VALUES

EVP_PKEY_CTX_get0_pkey() returns the B<EVP_PKEY> associated with the
EVP_PKEY_CTX or NULL if it is not set.

EVP_PKEY_CTX_get0_peerkey() returns the peer B<EVP_PKEY> associated with the
EVP_PKEY_CTX or NULL if it is not set.

The returned EVP_PKEY objects are owned by the EVP_PKEY_CTX,
and therefore should not explicitly be freed by the caller.

These functions do not affect the EVP_PKEY reference count.
They merely act as getter functions, and should be treated as such.

=head1 SEE ALSO

L<EVP_PKEY_CTX_new(3)>, L<EVP_PKEY_CTX_new_from_pkey(3)>,
L<EVP_PKEY_derive_set_peer(3)>, L<EVP_PKEY_derive_set_peer_ex(3)>

=head1 COPYRIGHT

Copyright 2022-2023 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").
You may not use this file except in compliance with the License.
You can obtain a copy in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
