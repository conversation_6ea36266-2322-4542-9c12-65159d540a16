=pod

=head1 NAME

BN_security_bits - returns bits of security based on given numbers

=head1 SYNOPSIS

 #include <openssl/bn.h>

 int BN_security_bits(int L, int N);

=head1 DESCRIPTION

BN_security_bits() returns the number of bits of security provided by a
specific algorithm and a particular key size. The bits of security is
defined in NIST SP800-57. Currently, BN_security_bits() support two types
of asymmetric algorithms: the FFC (Finite Field Cryptography) and IFC
(Integer Factorization Cryptography). For FFC, e.g., DSA and DH, both
parameters B<L> and B<N> are used to decide the bits of security, where
B<L> is the size of the public key and B<N> is the size of the private
key. For IFC, e.g., RSA, only B<L> is used and it's commonly considered
to be the key size (modulus).

=head1 RETURN VALUES

Number of security bits.

=head1 NOTES

ECC (Elliptic Curve Cryptography) is not covered by the BN_security_bits()
function. The symmetric algorithms are not covered neither.

=head1 SEE ALSO

L<DH_security_bits(3)>, L<DSA_security_bits(3)>, L<RSA_security_bits(3)>

=head1 HISTORY

The BN_security_bits() function was added in OpenSSL 1.1.0.

=head1 COPYRIGHT

Copyright 2017-2019 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
