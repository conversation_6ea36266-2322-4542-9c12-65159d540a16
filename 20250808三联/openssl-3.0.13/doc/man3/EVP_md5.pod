=pod

=head1 NAME

EVP_md5,
EVP_md5_sha1
- MD5 For EVP

=head1 SYNOPSIS

 #include <openssl/evp.h>

 const EVP_MD *EVP_md5(void);
 const EVP_MD *EVP_md5_sha1(void);

=head1 DESCRIPTION

MD5 is a cryptographic hash function standardized in RFC 1321 and designed by
<PERSON>.

The CMU Software Engineering Institute considers MD5 unsuitable for further
use since its security has been severely compromised.

=over 4

=item EVP_md5()

The MD5 algorithm which produces a 128-bit output from a given input.

=item EVP_md5_sha1()

A hash algorithm of SSL v3 that combines MD5 with SHA-1 as described in RFC
6101.

WARNING: this algorithm is not intended for non-SSL usage.

=back

=head1 NOTES

Developers should be aware of the negative performance implications of
calling these functions multiple times and should consider using
L<EVP_MD_fetch(3)> with L<EVP_MD-MD5(7)> or L<EVP_MD-MD5-SHA1(7)> instead.
See L<crypto(7)/Performance> for further information.

=head1 RETURN VALUES

These functions return a B<EVP_MD> structure that contains the
implementation of the message digest. See L<EVP_MD_meth_new(3)> for
details of the B<EVP_MD> structure.

=head1 CONFORMING TO

IETF RFC 1321.

=head1 SEE ALSO

L<evp(7)>,
L<EVP_DigestInit(3)>

=head1 COPYRIGHT

Copyright 2017-2023 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut

