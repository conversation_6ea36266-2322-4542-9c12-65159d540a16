.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "EVP_KDF-PBKDF2 7ossl"
.TH EVP_KDF-PBKDF2 7ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
EVP_KDF\-PBKDF2 \- The PBKDF2 EVP_KDF implementation
.SH DESCRIPTION
.IX Header "DESCRIPTION"
Support for computing the \fBPBKDF2\fR password-based KDF through the \fBEVP_KDF\fR
API.
.PP
The EVP_KDF\-PBKDF2 algorithm implements the PBKDF2 password-based key
derivation function, as described in SP800\-132; it derives a key from a password
using a salt and iteration count.
.SS Identity
.IX Subsection "Identity"
"PBKDF2" is the name for this implementation; it
can be used with the \fBEVP_KDF_fetch()\fR function.
.SS "Supported parameters"
.IX Subsection "Supported parameters"
The supported parameters are:
.IP """pass"" (\fBOSSL_KDF_PARAM_PASSWORD\fR) <octet string>" 4
.IX Item """pass"" (OSSL_KDF_PARAM_PASSWORD) <octet string>"
.PD 0
.IP """salt"" (\fBOSSL_KDF_PARAM_SALT\fR) <octet string>" 4
.IX Item """salt"" (OSSL_KDF_PARAM_SALT) <octet string>"
.IP """iter"" (\fBOSSL_KDF_PARAM_ITER\fR) <unsigned integer>" 4
.IX Item """iter"" (OSSL_KDF_PARAM_ITER) <unsigned integer>"
.PD
This parameter has a default value of 2048.
.IP """properties"" (\fBOSSL_KDF_PARAM_PROPERTIES\fR) <UTF8 string>" 4
.IX Item """properties"" (OSSL_KDF_PARAM_PROPERTIES) <UTF8 string>"
.PD 0
.IP """digest"" (\fBOSSL_KDF_PARAM_DIGEST\fR) <UTF8 string>" 4
.IX Item """digest"" (OSSL_KDF_PARAM_DIGEST) <UTF8 string>"
.PD
These parameters work as described in "PARAMETERS" in \fBEVP_KDF\fR\|(3).
.IP """pkcs5"" (\fBOSSL_KDF_PARAM_PKCS5\fR) <integer>" 4
.IX Item """pkcs5"" (OSSL_KDF_PARAM_PKCS5) <integer>"
This parameter can be used to enable or disable SP800\-132 compliance checks.
Setting the mode to 0 enables the compliance checks.
.Sp
The checks performed are:
.RS 4
.IP "\- the iteration count is at least 1000." 4
.IX Item "- the iteration count is at least 1000."
.PD 0
.IP "\- the salt length is at least 128 bits." 4
.IX Item "- the salt length is at least 128 bits."
.IP "\- the derived key length is at least 112 bits." 4
.IX Item "- the derived key length is at least 112 bits."
.RE
.RS 4
.PD
.Sp
The default provider uses a default mode of 1 for backwards compatibility,
and the FIPS provider uses a default mode of 0.
.Sp
The value string is expected to be a decimal number 0 or 1.
.RE
.SH NOTES
.IX Header "NOTES"
A typical application of this algorithm is to derive keying material for an
encryption algorithm from a password in the "pass", a salt in "salt",
and an iteration count.
.PP
Increasing the "iter" parameter slows down the algorithm which makes it
harder for an attacker to perform a brute force attack using a large number
of candidate passwords.
.PP
No assumption is made regarding the given password; it is simply treated as a
byte sequence.
.SH "CONFORMING TO"
.IX Header "CONFORMING TO"
SP800\-132
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBEVP_KDF\fR\|(3),
\&\fBEVP_KDF_CTX_new\fR\|(3),
\&\fBEVP_KDF_CTX_free\fR\|(3),
\&\fBEVP_KDF_CTX_set_params\fR\|(3),
\&\fBEVP_KDF_derive\fR\|(3),
"PARAMETERS" in \fBEVP_KDF\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
This functionality was added in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2018\-2022 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
