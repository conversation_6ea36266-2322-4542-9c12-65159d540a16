.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "EVP_KEYEXCH-X25519 7ossl"
.TH EVP_KEYEXCH-X25519 7ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
EVP_KEYEXCH\-X25519,
EVP_KEYEXCH\-X448
\&\- X25519 and X448 Key Exchange algorithm support
.SH DESCRIPTION
.IX Header "DESCRIPTION"
Key exchange support for the \fBX25519\fR and \fBX448\fR key types.
.SS "Key exchange parameters"
.IX Subsection "Key exchange parameters"
.IP """pad"" (\fBOSSL_EXCHANGE_PARAM_PAD\fR) <unsigned integer>" 4
.IX Item """pad"" (OSSL_EXCHANGE_PARAM_PAD) <unsigned integer>"
See "Common Key Exchange parameters" in \fBprovider\-keyexch\fR\|(7).
.SH EXAMPLES
.IX Header "EXAMPLES"
Keys for the host and peer can be generated as shown in
"Examples" in \fBEVP_PKEY\-X25519\fR\|(7).
.PP
The code to generate a shared secret is identical to
"Examples" in \fBEVP_KEYEXCH\-DH\fR\|(7).
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBEVP_PKEY\-FFC\fR\|(7),
\&\fBEVP_PKEY\-DH\fR\|(7)
\&\fBEVP_PKEY\fR\|(3),
\&\fBprovider\-keyexch\fR\|(7),
\&\fBprovider\-keymgmt\fR\|(7),
\&\fBOSSL_PROVIDER\-default\fR\|(7),
\&\fBOSSL_PROVIDER\-FIPS\fR\|(7),
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2020 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
