.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "EVP_KDF-KB 7ossl"
.TH EVP_KDF-KB 7ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
EVP_KDF\-KB \- The Key\-Based EVP_KDF implementation
.SH DESCRIPTION
.IX Header "DESCRIPTION"
The EVP_KDF\-KB algorithm implements the Key-Based key derivation function
(KBKDF).  KBKDF derives a key from repeated application of a keyed MAC to an
input secret (and other optional values).
.SS Identity
.IX Subsection "Identity"
"KBKDF" is the name for this implementation; it can be used with the
\&\fBEVP_KDF_fetch()\fR function.
.SS "Supported parameters"
.IX Subsection "Supported parameters"
The supported parameters are:
.IP """mode"" (\fBOSSL_KDF_PARAM_MODE\fR) <UTF8 string>" 4
.IX Item """mode"" (OSSL_KDF_PARAM_MODE) <UTF8 string>"
The mode parameter determines which flavor of KBKDF to use \- currently the
choices are "counter" and "feedback". "counter" is the default, and will be
used if unspecified.
.IP """mac"" (\fBOSSL_KDF_PARAM_MAC\fR) <UTF8 string>" 4
.IX Item """mac"" (OSSL_KDF_PARAM_MAC) <UTF8 string>"
The value is either CMAC or HMAC.
.IP """digest"" (\fBOSSL_KDF_PARAM_DIGEST\fR) <UTF8 string>" 4
.IX Item """digest"" (OSSL_KDF_PARAM_DIGEST) <UTF8 string>"
.PD 0
.IP """cipher"" (\fBOSSL_KDF_PARAM_CIPHER\fR) <UTF8 string>" 4
.IX Item """cipher"" (OSSL_KDF_PARAM_CIPHER) <UTF8 string>"
.IP """properties"" (\fBOSSL_KDF_PARAM_PROPERTIES\fR) <UTF8 string>" 4
.IX Item """properties"" (OSSL_KDF_PARAM_PROPERTIES) <UTF8 string>"
.IP """key"" (\fBOSSL_KDF_PARAM_KEY\fR) <octet string>" 4
.IX Item """key"" (OSSL_KDF_PARAM_KEY) <octet string>"
.IP """salt"" (\fBOSSL_KDF_PARAM_SALT\fR) <octet string>" 4
.IX Item """salt"" (OSSL_KDF_PARAM_SALT) <octet string>"
.IP """info (\fBOSSL_KDF_PARAM_INFO\fR) <octet string>" 4
.IX Item """info (OSSL_KDF_PARAM_INFO) <octet string>"
.IP """seed"" (\fBOSSL_KDF_PARAM_SEED\fR) <octet string>" 4
.IX Item """seed"" (OSSL_KDF_PARAM_SEED) <octet string>"
.PD
The seed parameter is unused in counter mode.
.IP """use-l"" (\fBOSSL_KDF_PARAM_KBKDF_USE_L\fR) <integer>" 4
.IX Item """use-l"" (OSSL_KDF_PARAM_KBKDF_USE_L) <integer>"
Set to \fB0\fR to disable use of the optional Fixed Input data 'L' (see SP800\-108).
The default value of \fB1\fR will be used if unspecified.
.IP """use-separator"" (\fBOSSL_KDF_PARAM_KBKDF_USE_SEPARATOR\fR) <integer>" 4
.IX Item """use-separator"" (OSSL_KDF_PARAM_KBKDF_USE_SEPARATOR) <integer>"
Set to \fB0\fR to disable use of the optional Fixed Input data 'zero separator'
(see SP800\-108) that is placed between the Label and Context.
The default value of \fB1\fR will be used if unspecified.
.PP
Depending on whether mac is CMAC or HMAC, either digest or cipher is required
(respectively) and the other is unused.
.PP
The parameters key, salt, info, and seed correspond to KI, Label, Context, and
IV (respectively) in SP800\-108.  As in that document, salt, info, and seed are
optional and may be omitted.
.PP
"mac", "digest", cipher" and "properties" are described in
"PARAMETERS" in \fBEVP_KDF\fR\|(3).
.SH NOTES
.IX Header "NOTES"
A context for KBKDF can be obtained by calling:
.PP
.Vb 2
\& EVP_KDF *kdf = EVP_KDF_fetch(NULL, "KBKDF", NULL);
\& EVP_KDF_CTX *kctx = EVP_KDF_CTX_new(kdf);
.Ve
.PP
The output length of an KBKDF is specified via the \f(CW\*(C`keylen\*(C'\fR
parameter to the \fBEVP_KDF_derive\fR\|(3) function.
.PP
Note that currently OpenSSL only implements counter and feedback modes.  Other
variants may be supported in the future.
.SH EXAMPLES
.IX Header "EXAMPLES"
This example derives 10 bytes using COUNTER\-HMAC\-SHA256, with KI "secret",
Label "label", and Context "context".
.PP
.Vb 4
\& EVP_KDF *kdf;
\& EVP_KDF_CTX *kctx;
\& unsigned char out[10];
\& OSSL_PARAM params[6], *p = params;
\&
\& kdf = EVP_KDF_fetch(NULL, "KBKDF", NULL);
\& kctx = EVP_KDF_CTX_new(kdf);
\& EVP_KDF_free(kdf);
\&
\& *p++ = OSSL_PARAM_construct_utf8_string(OSSL_KDF_PARAM_DIGEST,
\&                                         "SHA2\-256", 0);
\& *p++ = OSSL_PARAM_construct_utf8_string(OSSL_KDF_PARAM_MAC,
\&                                         "HMAC", 0);
\& *p++ = OSSL_PARAM_construct_octet_string(OSSL_KDF_PARAM_KEY,
\&                                          "secret", strlen("secret"));
\& *p++ = OSSL_PARAM_construct_octet_string(OSSL_KDF_PARAM_SALT,
\&                                          "label", strlen("label"));
\& *p++ = OSSL_PARAM_construct_octet_string(OSSL_KDF_PARAM_INFO,
\&                                          "context", strlen("context"));
\& *p = OSSL_PARAM_construct_end();
\& if (EVP_KDF_derive(kctx, out, sizeof(out), params) <= 0)
\&     error("EVP_KDF_derive");
\&
\& EVP_KDF_CTX_free(kctx);
.Ve
.PP
This example derives 10 bytes using FEEDBACK\-CMAC\-AES256, with KI "secret",
Label "label", and IV "sixteen bytes iv".
.PP
.Vb 5
\& EVP_KDF *kdf;
\& EVP_KDF_CTX *kctx;
\& unsigned char out[10];
\& OSSL_PARAM params[8], *p = params;
\& unsigned char *iv = "sixteen bytes iv";
\&
\& kdf = EVP_KDF_fetch(NULL, "KBKDF", NULL);
\& kctx = EVP_KDF_CTX_new(kdf);
\& EVP_KDF_free(kdf);
\&
\& *p++ = OSSL_PARAM_construct_utf8_string(OSSL_KDF_PARAM_CIPHER, "AES256", 0);
\& *p++ = OSSL_PARAM_construct_utf8_string(OSSL_KDF_PARAM_MAC, "CMAC", 0);
\& *p++ = OSSL_PARAM_construct_utf8_string(OSSL_KDF_PARAM_MODE, "FEEDBACK", 0);
\& *p++ = OSSL_PARAM_construct_octet_string(OSSL_KDF_PARAM_KEY,
\&                                          "secret", strlen("secret"));
\& *p++ = OSSL_PARAM_construct_octet_string(OSSL_KDF_PARAM_SALT,
\&                                          "label", strlen("label"));
\& *p++ = OSSL_PARAM_construct_octet_string(OSSL_KDF_PARAM_INFO,
\&                                          "context", strlen("context"));
\& *p++ = OSSL_PARAM_construct_octet_string(OSSL_KDF_PARAM_SEED,
\&                                          iv, strlen(iv));
\& *p = OSSL_PARAM_construct_end();
\& if (EVP_KDF_derive(kctx, out, sizeof(out), params) <= 0)
\&     error("EVP_KDF_derive");
\&
\& EVP_KDF_CTX_free(kctx);
.Ve
.SH "CONFORMING TO"
.IX Header "CONFORMING TO"
NIST SP800\-108, IETF RFC 6803, IETF RFC 8009.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBEVP_KDF\fR\|(3),
\&\fBEVP_KDF_CTX_free\fR\|(3),
\&\fBEVP_KDF_CTX_get_kdf_size\fR\|(3),
\&\fBEVP_KDF_derive\fR\|(3),
"PARAMETERS" in \fBEVP_KDF\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
This functionality was added in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2019\-2021 The OpenSSL Project Authors. All Rights Reserved.
Copyright 2019 Red Hat, Inc.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
