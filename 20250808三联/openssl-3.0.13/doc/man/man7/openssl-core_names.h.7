.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "OPENSSL-CORE_NAMES.H 7ossl"
.TH OPENSSL-CORE_NAMES.H 7ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
openssl/core_names.h \- OpenSSL provider parameter names
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/core_names.h>
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
The \fI<openssl/core_names.h>\fR header defines a multitude of macros
for \fBOSSL_PARAM\fR\|(3) names, algorithm names and other known names used
with OpenSSL's providers, made available for practical purposes only.
.PP
Existing names are further described in the manuals for OpenSSL's
providers (see "SEE ALSO") and the manuals for each algorithm they
provide (listed in those provider manuals).
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBOSSL_PROVIDER\-default\fR\|(7), \fBOSSL_PROVIDER\-FIPS\fR\|(7),
\&\fBOSSL_PROVIDER\-legacy\fR\|(7)
.SH HISTORY
.IX Header "HISTORY"
The macros described here were added in OpenSSL 3.0.
.SH CAVEATS
.IX Header "CAVEATS"
\&\fIThis header file does not constitute a general registry of names\fR.
Providers that implement new algorithms are to be responsible for
their own parameter names.
.PP
However, authors of provider that implement their own variants of
algorithms that OpenSSL providers support will want to pay attention
to the names provided in this header to work in a compatible manner.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2020 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
