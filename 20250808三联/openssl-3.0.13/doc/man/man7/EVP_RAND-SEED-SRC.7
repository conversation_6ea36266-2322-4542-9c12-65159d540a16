.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "EVP_RAND-SEED-SRC 7ossl"
.TH EVP_RAND-SEED-SRC 7ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
EVP_RAND\-SEED\-SRC \- The randomness seed source EVP_RAND implementation
.SH DESCRIPTION
.IX Header "DESCRIPTION"
Support for deterministic random number generator seeding through the
\&\fBEVP_RAND\fR API.
.PP
The seed sources used are specified at the time OpenSSL is configured for
building using the \fB\-\-with\-rand\-seed=\fR option.  By default, operating system
randomness sources are used.
.SS Identity
.IX Subsection "Identity"
"SEED-SRC" is the name for this implementation; it can be used with the
\&\fBEVP_RAND_fetch()\fR function.
.SS "Supported parameters"
.IX Subsection "Supported parameters"
The supported parameters are:
.IP """state"" (\fBOSSL_RAND_PARAM_STATE\fR) <integer>" 4
.IX Item """state"" (OSSL_RAND_PARAM_STATE) <integer>"
.PD 0
.IP """strength"" (\fBOSSL_RAND_PARAM_STRENGTH\fR) <unsigned integer>" 4
.IX Item """strength"" (OSSL_RAND_PARAM_STRENGTH) <unsigned integer>"
.IP """max_request"" (\fBOSSL_RAND_PARAM_MAX_REQUEST\fR) <unsigned integer>" 4
.IX Item """max_request"" (OSSL_RAND_PARAM_MAX_REQUEST) <unsigned integer>"
.PD
These parameters work as described in "PARAMETERS" in \fBEVP_RAND\fR\|(3).
.SH NOTES
.IX Header "NOTES"
A context for the seed source can be obtained by calling:
.PP
.Vb 2
\& EVP_RAND *rand = EVP_RAND_fetch(NULL, "SEED\-SRC", NULL);
\& EVP_RAND_CTX *rctx = EVP_RAND_CTX_new(rand);
.Ve
.SH EXAMPLES
.IX Header "EXAMPLES"
.Vb 5
\& EVP_RAND *rand;
\& EVP_RAND_CTX *seed, *rctx;
\& unsigned char bytes[100];
\& OSSL_PARAM params[2], *p = params;
\& unsigned int strength = 128;
\&
\& /* Create and instantiate a seed source */
\& rand = EVP_RAND_fetch(NULL, "SEED\-SRC", NULL);
\& seed = EVP_RAND_CTX_new(rand, NULL);
\& EVP_RAND_instantiate(seed, strength, 0, NULL, 0, NULL);
\& EVP_RAND_free(rand);
\&
\& /* Feed this into a DRBG */
\& rand = EVP_RAND_fetch(NULL, "CTR\-DRBG", NULL);
\& rctx = EVP_RAND_CTX_new(rand, seed);
\& EVP_RAND_free(rand);
\&
\& /* Configure the DRBG */
\& *p++ = OSSL_PARAM_construct_utf8_string(OSSL_DRBG_PARAM_CIPHER,
\&                                         SN_aes_256_ctr, 0);
\& *p = OSSL_PARAM_construct_end();
\& EVP_RAND_instantiate(rctx, strength, 0, NULL, 0, params);
\&
\& EVP_RAND_generate(rctx, bytes, sizeof(bytes), strength, 0, NULL, 0);
\&
\& EVP_RAND_CTX_free(rctx);
\& EVP_RAND_CTX_free(seed);
.Ve
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBEVP_RAND\fR\|(3),
"PARAMETERS" in \fBEVP_RAND\fR\|(3)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2020\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
