.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "EVP_KEM-RSA 7ossl"
.TH EVP_KEM-RSA 7ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
EVP_KEM\-RSA
\&\- EVP_KEM RSA keytype and algorithm support
.SH DESCRIPTION
.IX Header "DESCRIPTION"
The \fBRSA\fR keytype and its parameters are described in \fBEVP_PKEY\-RSA\fR\|(7).
See \fBEVP_PKEY_encapsulate\fR\|(3) and \fBEVP_PKEY_decapsulate\fR\|(3) for more info.
.SS "RSA KEM parameters"
.IX Subsection "RSA KEM parameters"
.IP """operation"" (\fBOSSL_KEM_PARAM_OPERATION\fR) <UTF8 string>" 4
.IX Item """operation"" (OSSL_KEM_PARAM_OPERATION) <UTF8 string>"
The OpenSSL RSA Key Encapsulation Mechanism only currently supports the
following operation
.RS 4
.IP """RSASVE""" 4
.IX Item """RSASVE"""
The encapsulate function simply generates a secret using random bytes and then
encrypts the secret using the RSA public key (with no padding).
The decapsulate function recovers the secret using the RSA private key.
.RE
.RS 4
.Sp
This can be set using \fBEVP_PKEY_CTX_set_kem_op()\fR.
.RE
.SH "CONFORMING TO"
.IX Header "CONFORMING TO"
.IP SP800\-56Br2 4
.IX Item "SP800-56Br2"
Section ******* RSASVE Generate Operation (RSASVE.GENERATE).
Section ******* RSASVE Recovery Operation (RSASVE.RECOVER).
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBEVP_PKEY_CTX_set_kem_op\fR\|(3),
\&\fBEVP_PKEY_encapsulate\fR\|(3),
\&\fBEVP_PKEY_decapsulate\fR\|(3)
\&\fBEVP_KEYMGMT\fR\|(3),
\&\fBEVP_PKEY\fR\|(3),
\&\fBprovider\-keymgmt\fR\|(7)
.SH HISTORY
.IX Header "HISTORY"
This functionality was added in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2020 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
