.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "X25519 7ossl"
.TH X25519 7ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
X25519,
X448
\&\- EVP_PKEY X25519 and X448 support
.SH DESCRIPTION
.IX Header "DESCRIPTION"
The \fBX25519\fR and \fBX448\fR EVP_PKEY implementation supports key generation and
key derivation using \fBX25519\fR and \fBX448\fR. It has associated private and public
key formats compatible with RFC 8410.
.PP
No additional parameters can be set during key generation.
.PP
The peer public key must be set using \fBEVP_PKEY_derive_set_peer()\fR when
performing key derivation.
.SH NOTES
.IX Header "NOTES"
A context for the \fBX25519\fR algorithm can be obtained by calling:
.PP
.Vb 1
\& EVP_PKEY_CTX *pctx = EVP_PKEY_CTX_new_id(EVP_PKEY_X25519, NULL);
.Ve
.PP
For the \fBX448\fR algorithm a context can be obtained by calling:
.PP
.Vb 1
\& EVP_PKEY_CTX *pctx = EVP_PKEY_CTX_new_id(EVP_PKEY_X448, NULL);
.Ve
.PP
X25519 or X448 private keys can be set directly using
\&\fBEVP_PKEY_new_raw_private_key\fR\|(3) or loaded from a PKCS#8 private key file
using \fBPEM_read_bio_PrivateKey\fR\|(3) (or similar function). Completely new keys
can also be generated (see the example below). Setting a private key also sets
the associated public key.
.PP
X25519 or X448 public keys can be set directly using
\&\fBEVP_PKEY_new_raw_public_key\fR\|(3) or loaded from a SubjectPublicKeyInfo
structure in a PEM file using \fBPEM_read_bio_PUBKEY\fR\|(3) (or similar function).
.SH EXAMPLES
.IX Header "EXAMPLES"
This example generates an \fBX25519\fR private key and writes it to standard
output in PEM format:
.PP
.Vb 9
\& #include <openssl/evp.h>
\& #include <openssl/pem.h>
\& ...
\& EVP_PKEY *pkey = NULL;
\& EVP_PKEY_CTX *pctx = EVP_PKEY_CTX_new_id(EVP_PKEY_X25519, NULL);
\& EVP_PKEY_keygen_init(pctx);
\& EVP_PKEY_keygen(pctx, &pkey);
\& EVP_PKEY_CTX_free(pctx);
\& PEM_write_PrivateKey(stdout, pkey, NULL, NULL, 0, NULL, NULL);
.Ve
.PP
The key derivation example in \fBEVP_PKEY_derive\fR\|(3) can be used with
\&\fBX25519\fR and \fBX448\fR.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBEVP_PKEY_CTX_new\fR\|(3),
\&\fBEVP_PKEY_keygen\fR\|(3),
\&\fBEVP_PKEY_derive\fR\|(3),
\&\fBEVP_PKEY_derive_set_peer\fR\|(3)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2017\-2020 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
