.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "PROXY-CERTIFICATES 7ossl"
.TH PROXY-CERTIFICATES 7ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
proxy\-certificates \- Proxy certificates in OpenSSL
.SH DESCRIPTION
.IX Header "DESCRIPTION"
Proxy certificates are defined in RFC 3820.  They are used to
extend rights to some other entity (a computer process, typically, or
sometimes to the user itself).  This allows the entity to perform
operations on behalf of the owner of the EE (End Entity) certificate.
.PP
The requirements for a valid proxy certificate are:
.IP \(bu 4
They are issued by an End Entity, either a normal EE certificate, or
another proxy certificate.
.IP \(bu 4
They must not have the \fBsubjectAltName\fR or \fBissuerAltName\fR
extensions.
.IP \(bu 4
They must have the \fBproxyCertInfo\fR extension.
.IP \(bu 4
They must have the subject of their issuer, with one \fBcommonName\fR
added.
.SS "Enabling proxy certificate verification"
.IX Subsection "Enabling proxy certificate verification"
OpenSSL expects applications that want to use proxy certificates to be
specially aware of them, and make that explicit.  This is done by
setting an X509 verification flag:
.PP
.Vb 1
\&    X509_STORE_CTX_set_flags(ctx, X509_V_FLAG_ALLOW_PROXY_CERTS);
.Ve
.PP
or
.PP
.Vb 1
\&    X509_VERIFY_PARAM_set_flags(param, X509_V_FLAG_ALLOW_PROXY_CERTS);
.Ve
.PP
See "NOTES" for a discussion on this requirement.
.SS "Creating proxy certificates"
.IX Subsection "Creating proxy certificates"
Creating proxy certificates can be done using the \fBopenssl\-x509\fR\|(1)
command, with some extra extensions:
.PP
.Vb 7
\&    [ proxy ]
\&    # A proxy certificate MUST NEVER be a CA certificate.
\&    basicConstraints = CA:FALSE
\&    # Usual authority key ID
\&    authorityKeyIdentifier = keyid,issuer:always
\&    # The extension which marks this certificate as a proxy
\&    proxyCertInfo = critical,language:id\-ppl\-anyLanguage,pathlen:1,policy:text:AB
.Ve
.PP
It's also possible to specify the proxy extension in a separate section:
.PP
.Vb 1
\&    proxyCertInfo = critical,@proxy_ext
\&
\&    [ proxy_ext ]
\&    language = id\-ppl\-anyLanguage
\&    pathlen = 0
\&    policy = text:BC
.Ve
.PP
The policy value has a specific syntax, \fIsyntag\fR:\fIstring\fR, where the
\&\fIsyntag\fR determines what will be done with the string.  The following
\&\fIsyntag\fRs are recognised:
.IP \fBtext\fR 4
.IX Item "text"
indicates that the string is a byte sequence, without any encoding:
.Sp
.Vb 1
\&    policy=text:räksmörgås
.Ve
.IP \fBhex\fR 4
.IX Item "hex"
indicates the string is encoded hexadecimal encoded binary data, with
colons between each byte (every second hex digit):
.Sp
.Vb 1
\&    policy=hex:72:E4:6B:73:6D:F6:72:67:E5:73
.Ve
.IP \fBfile\fR 4
.IX Item "file"
indicates that the text of the policy should be taken from a file.
The string is then a filename.  This is useful for policies that are
more than a few lines, such as XML or other markup.
.PP
Note that the proxy policy value is what determines the rights granted
to the process during the proxy certificate, and it is up to the
application to interpret and combine these policies.>
.PP
With a proxy extension, creating a proxy certificate is a matter of
two commands:
.PP
.Vb 3
\&    openssl req \-new \-config proxy.cnf \e
\&        \-out proxy.req \-keyout proxy.key \e
\&        \-subj "/DC=org/DC=openssl/DC=users/CN=proxy"
\&
\&    openssl x509 \-req \-CAcreateserial \-in proxy.req \-out proxy.crt \e
\&        \-CA user.crt \-CAkey user.key \-days 7 \e
\&        \-extfile proxy.cnf \-extensions proxy
.Ve
.PP
You can also create a proxy certificate using another proxy
certificate as issuer. Note that this example uses a different
configuration section for the proxy extensions:
.PP
.Vb 3
\&    openssl req \-new \-config proxy.cnf \e
\&        \-out proxy2.req \-keyout proxy2.key \e
\&        \-subj "/DC=org/DC=openssl/DC=users/CN=proxy/CN=proxy 2"
\&
\&    openssl x509 \-req \-CAcreateserial \-in proxy2.req \-out proxy2.crt \e
\&        \-CA proxy.crt \-CAkey proxy.key \-days 7 \e
\&        \-extfile proxy.cnf \-extensions proxy_2
.Ve
.SS "Using proxy certs in applications"
.IX Subsection "Using proxy certs in applications"
To interpret proxy policies, the application would normally start with
some default rights (perhaps none at all), then compute the resulting
rights by checking the rights against the chain of proxy certificates,
user certificate and CA certificates.
.PP
The complicated part is figuring out how to pass data between your
application and the certificate validation procedure.
.PP
The following ingredients are needed for such processing:
.IP \(bu 4
a callback function that will be called for every certificate being
validated.  The callback is called several times for each certificate,
so you must be careful to do the proxy policy interpretation at the
right time.  You also need to fill in the defaults when the EE
certificate is checked.
.IP \(bu 4
a data structure that is shared between your application code and the
callback.
.IP \(bu 4
a wrapper function that sets it all up.
.IP \(bu 4
an ex_data index function that creates an index into the generic
ex_data store that is attached to an X509 validation context.
.PP
The following skeleton code can be used as a starting point:
.PP
.Vb 4
\&    #include <string.h>
\&    #include <netdb.h>
\&    #include <openssl/x509.h>
\&    #include <openssl/x509v3.h>
\&
\&    #define total_rights 25
\&
\&    /*
\&     * In this example, I will use a view of granted rights as a bit
\&     * array, one bit for each possible right.
\&     */
\&    typedef struct your_rights {
\&        unsigned char rights[(total_rights + 7) / 8];
\&    } YOUR_RIGHTS;
\&
\&    /*
\&     * The following procedure will create an index for the ex_data
\&     * store in the X509 validation context the first time it\*(Aqs
\&     * called.  Subsequent calls will return the same index.
\&     */
\&    static int get_proxy_auth_ex_data_idx(X509_STORE_CTX *ctx)
\&    {
\&        static volatile int idx = \-1;
\&
\&        if (idx < 0) {
\&            X509_STORE_lock(X509_STORE_CTX_get0_store(ctx));
\&            if (idx < 0) {
\&                idx = X509_STORE_CTX_get_ex_new_index(0,
\&                                                      "for verify callback",
\&                                                      NULL,NULL,NULL);
\&            }
\&            X509_STORE_unlock(X509_STORE_CTX_get0_store(ctx));
\&        }
\&        return idx;
\&    }
\&
\&    /* Callback to be given to the X509 validation procedure.  */
\&    static int verify_callback(int ok, X509_STORE_CTX *ctx)
\&    {
\&        if (ok == 1) {
\&            /*
\&             * It\*(Aqs REALLY important you keep the proxy policy check
\&             * within this section.  It\*(Aqs important to know that when
\&             * ok is 1, the certificates are checked from top to
\&             * bottom.  You get the CA root first, followed by the
\&             * possible chain of intermediate CAs, followed by the EE
\&             * certificate, followed by the possible proxy
\&             * certificates.
\&             */
\&            X509 *xs = X509_STORE_CTX_get_current_cert(ctx);
\&
\&            if (X509_get_extension_flags(xs) & EXFLAG_PROXY) {
\&                YOUR_RIGHTS *rights =
\&                    (YOUR_RIGHTS *)X509_STORE_CTX_get_ex_data(ctx,
\&                        get_proxy_auth_ex_data_idx(ctx));
\&                PROXY_CERT_INFO_EXTENSION *pci =
\&                    X509_get_ext_d2i(xs, NID_proxyCertInfo, NULL, NULL);
\&
\&                switch (OBJ_obj2nid(pci\->proxyPolicy\->policyLanguage)) {
\&                case NID_Independent:
\&                    /*
\&                     * Do whatever you need to grant explicit rights
\&                     * to this particular proxy certificate, usually
\&                     * by pulling them from some database.  If there
\&                     * are none to be found, clear all rights (making
\&                     * this and any subsequent proxy certificate void
\&                     * of any rights).
\&                     */
\&                    memset(rights\->rights, 0, sizeof(rights\->rights));
\&                    break;
\&                case NID_id_ppl_inheritAll:
\&                    /*
\&                     * This is basically a NOP, we simply let the
\&                     * current rights stand as they are.
\&                     */
\&                    break;
\&                default:
\&                    /*
\&                     * This is usually the most complex section of
\&                     * code.  You really do whatever you want as long
\&                     * as you follow RFC 3820.  In the example we use
\&                     * here, the simplest thing to do is to build
\&                     * another, temporary bit array and fill it with
\&                     * the rights granted by the current proxy
\&                     * certificate, then use it as a mask on the
\&                     * accumulated rights bit array, and voilà, you
\&                     * now have a new accumulated rights bit array.
\&                     */
\&                    {
\&                        int i;
\&                        YOUR_RIGHTS tmp_rights;
\&                        memset(tmp_rights.rights, 0,
\&                               sizeof(tmp_rights.rights));
\&
\&                        /*
\&                         * process_rights() is supposed to be a
\&                         * procedure that takes a string and its
\&                         * length, interprets it and sets the bits
\&                         * in the YOUR_RIGHTS pointed at by the
\&                         * third argument.
\&                         */
\&                        process_rights((char *) pci\->proxyPolicy\->policy\->data,
\&                                       pci\->proxyPolicy\->policy\->length,
\&                                       &tmp_rights);
\&
\&                        for(i = 0; i < total_rights / 8; i++)
\&                            rights\->rights[i] &= tmp_rights.rights[i];
\&                    }
\&                    break;
\&                }
\&                PROXY_CERT_INFO_EXTENSION_free(pci);
\&            } else if (!(X509_get_extension_flags(xs) & EXFLAG_CA)) {
\&                /* We have an EE certificate, let\*(Aqs use it to set default! */
\&                YOUR_RIGHTS *rights =
\&                    (YOUR_RIGHTS *)X509_STORE_CTX_get_ex_data(ctx,
\&                        get_proxy_auth_ex_data_idx(ctx));
\&
\&                /*
\&                 * The following procedure finds out what rights the
\&                 * owner of the current certificate has, and sets them
\&                 * in the YOUR_RIGHTS structure pointed at by the
\&                 * second argument.
\&                 */
\&                set_default_rights(xs, rights);
\&            }
\&        }
\&        return ok;
\&    }
\&
\&    static int my_X509_verify_cert(X509_STORE_CTX *ctx,
\&                                   YOUR_RIGHTS *needed_rights)
\&    {
\&        int ok;
\&        int (*save_verify_cb)(int ok,X509_STORE_CTX *ctx) =
\&            X509_STORE_CTX_get_verify_cb(ctx);
\&        YOUR_RIGHTS rights;
\&
\&        X509_STORE_CTX_set_verify_cb(ctx, verify_callback);
\&        X509_STORE_CTX_set_ex_data(ctx, get_proxy_auth_ex_data_idx(ctx),
\&                                   &rights);
\&        X509_STORE_CTX_set_flags(ctx, X509_V_FLAG_ALLOW_PROXY_CERTS);
\&        ok = X509_verify_cert(ctx);
\&
\&        if (ok == 1) {
\&            ok = check_needed_rights(rights, needed_rights);
\&        }
\&
\&        X509_STORE_CTX_set_verify_cb(ctx, save_verify_cb);
\&
\&        return ok;
\&    }
.Ve
.PP
If you use SSL or TLS, you can easily set up a callback to have the
certificates checked properly, using the code above:
.PP
.Vb 2
\&    SSL_CTX_set_cert_verify_callback(s_ctx, my_X509_verify_cert,
\&                                     &needed_rights);
.Ve
.SH NOTES
.IX Header "NOTES"
To this date, it seems that proxy certificates have only been used in
environments that are aware of them, and no one seems to have
investigated how they can be used or misused outside of such an
environment.
.PP
For that reason, OpenSSL requires that applications aware of proxy
certificates must also make that explicit.
.PP
\&\fBsubjectAltName\fR and \fBissuerAltName\fR are forbidden in proxy
certificates, and this is enforced in OpenSSL.  The subject must be
the same as the issuer, with one commonName added on.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBX509_STORE_CTX_set_flags\fR\|(3),
\&\fBX509_STORE_CTX_set_verify_cb\fR\|(3),
\&\fBX509_VERIFY_PARAM_set_flags\fR\|(3),
\&\fBSSL_CTX_set_cert_verify_callback\fR\|(3),
\&\fBopenssl\-req\fR\|(1), \fBopenssl\-x509\fR\|(1),
RFC 3820 <https://tools.ietf.org/html/rfc3820>
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2019\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
