.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "EVP_KDF-TLS13_KDF 7ossl"
.TH EVP_KDF-TLS13_KDF 7ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
EVP_KDF\-TLS13_KDF \- The TLS 1.3 EVP_KDF implementation
.SH DESCRIPTION
.IX Header "DESCRIPTION"
Support for computing the TLS 1.3 version of the \fBHKDF\fR KDF through
the \fBEVP_KDF\fR API.
.PP
The EVP_KDF\-TLS13_KDF algorithm implements the HKDF key derivation function
as used by TLS 1.3.
.SS Identity
.IX Subsection "Identity"
"TLS13\-KDF" is the name for this implementation; it
can be used with the \fBEVP_KDF_fetch()\fR function.
.SS "Supported parameters"
.IX Subsection "Supported parameters"
The supported parameters are:
.IP """properties"" (\fBOSSL_KDF_PARAM_PROPERTIES\fR) <UTF8 string>" 4
.IX Item """properties"" (OSSL_KDF_PARAM_PROPERTIES) <UTF8 string>"
.PD 0
.IP """digest"" (\fBOSSL_KDF_PARAM_DIGEST\fR) <UTF8 string>" 4
.IX Item """digest"" (OSSL_KDF_PARAM_DIGEST) <UTF8 string>"
.IP """key"" (\fBOSSL_KDF_PARAM_KEY\fR) <octet string>" 4
.IX Item """key"" (OSSL_KDF_PARAM_KEY) <octet string>"
.IP """salt"" (\fBOSSL_KDF_PARAM_SALT\fR) <octet string>" 4
.IX Item """salt"" (OSSL_KDF_PARAM_SALT) <octet string>"
.PD
These parameters work as described in "PARAMETERS" in \fBEVP_KDF\fR\|(3).
.IP """prefix"" (\fBOSSL_KDF_PARAM_PREFIX\fR) <octet string>" 4
.IX Item """prefix"" (OSSL_KDF_PARAM_PREFIX) <octet string>"
This parameter sets the label prefix on the specified TLS 1.3 KDF context.
For TLS 1.3 this should be set to the ASCII string "tls13 " without a
trailing zero byte.  Refer to RFC 8446 section 7.1 "Key Schedule" for details.
.IP """label"" (\fBOSSL_KDF_PARAM_LABEL\fR) <octet string>" 4
.IX Item """label"" (OSSL_KDF_PARAM_LABEL) <octet string>"
This parameter sets the label on the specified TLS 1.3 KDF context.
Refer to RFC 8446 section 7.1 "Key Schedule" for details.
.IP """data"" (\fBOSSL_KDF_PARAM_DATA\fR) <octet string>" 4
.IX Item """data"" (OSSL_KDF_PARAM_DATA) <octet string>"
This parameter sets the context data on the specified TLS 1.3 KDF context.
Refer to RFC 8446 section 7.1 "Key Schedule" for details.
.IP """mode"" (\fBOSSL_KDF_PARAM_MODE\fR) <UTF8 string> or <integer>" 4
.IX Item """mode"" (OSSL_KDF_PARAM_MODE) <UTF8 string> or <integer>"
This parameter sets the mode for the TLS 1.3 KDF operation.
There are two modes that are currently defined:
.RS 4
.IP """EXTRACT_ONLY"" or \fBEVP_KDF_HKDF_MODE_EXTRACT_ONLY\fR" 4
.IX Item """EXTRACT_ONLY"" or EVP_KDF_HKDF_MODE_EXTRACT_ONLY"
In this mode calling \fBEVP_KDF_derive\fR\|(3) will just perform the extract
operation. The value returned will be the intermediate fixed-length pseudorandom
key K.  The \fIkeylen\fR parameter must match the size of K, which can be looked
up by calling \fBEVP_KDF_CTX_get_kdf_size()\fR after setting the mode and digest.
.Sp
The digest, key and salt values must be set before a key is derived otherwise
an error will occur.
.IP """EXPAND_ONLY"" or \fBEVP_KDF_HKDF_MODE_EXPAND_ONLY\fR" 4
.IX Item """EXPAND_ONLY"" or EVP_KDF_HKDF_MODE_EXPAND_ONLY"
In this mode calling \fBEVP_KDF_derive\fR\|(3) will just perform the expand
operation. The input key should be set to the intermediate fixed-length
pseudorandom key K returned from a previous extract operation.
.Sp
The digest, key and info values must be set before a key is derived otherwise
an error will occur.
.RE
.RS 4
.RE
.SH NOTES
.IX Header "NOTES"
This KDF is intended for use by the TLS 1.3 implementation in libssl.
It does not support all the options and capabilities that HKDF does.
.PP
The \fIOSSL_PARAM\fR array passed to \fBEVP_KDF_derive\fR\|(3) or
\&\fBEVP_KDF_CTX_set_params\fR\|(3) must specify all of the parameters required.
This KDF does not support a piecemeal approach to providing these.
.PP
A context for a TLS 1.3 KDF can be obtained by calling:
.PP
.Vb 2
\& EVP_KDF *kdf = EVP_KDF_fetch(NULL, "TLS13\-KDF", NULL);
\& EVP_KDF_CTX *kctx = EVP_KDF_CTX_new(kdf);
.Ve
.PP
The output length of a TLS 1.3 KDF expand operation is specified via the
\&\fIkeylen\fR parameter to the \fBEVP_KDF_derive\fR\|(3) function.  When using
EVP_KDF_HKDF_MODE_EXTRACT_ONLY the \fIkeylen\fR parameter must equal the size of
the intermediate fixed-length pseudorandom key otherwise an error will occur.
For that mode, the fixed output size can be looked up by calling
\&\fBEVP_KDF_CTX_get_kdf_size()\fR after setting the mode and digest on the
\&\fBEVP_KDF_CTX\fR.
.SH "CONFORMING TO"
.IX Header "CONFORMING TO"
RFC 8446
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBEVP_KDF\fR\|(3),
\&\fBEVP_KDF_CTX_new\fR\|(3),
\&\fBEVP_KDF_CTX_free\fR\|(3),
\&\fBEVP_KDF_CTX_get_kdf_size\fR\|(3),
\&\fBEVP_KDF_CTX_set_params\fR\|(3),
\&\fBEVP_KDF_derive\fR\|(3),
"PARAMETERS" in \fBEVP_KDF\fR\|(3),
\&\fBEVP_KDF\-HKDF\fR\|(7)
.SH HISTORY
.IX Header "HISTORY"
This functionality was added in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
