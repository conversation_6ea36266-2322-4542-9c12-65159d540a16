.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "EVP_ASYM_CIPHER-RSA 7ossl"
.TH EVP_ASYM_CIPHER-RSA 7ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
EVP_ASYM_CIPHER\-RSA
\&\- RSA Asymmetric Cipher algorithm support
.SH DESCRIPTION
.IX Header "DESCRIPTION"
Asymmetric Cipher support for the \fBRSA\fR key type.
.SS "RSA Asymmetric Cipher parameters"
.IX Subsection "RSA Asymmetric Cipher parameters"
.IP """pad-mode"" (\fBOSSL_ASYM_CIPHER_PARAM_PAD_MODE\fR) <UTF8 string>" 4
.IX Item """pad-mode"" (OSSL_ASYM_CIPHER_PARAM_PAD_MODE) <UTF8 string>"
The default provider understands these RSA padding modes in string form:
.RS 4
.IP """none"" (\fBOSSL_PKEY_RSA_PAD_MODE_NONE\fR)" 4
.IX Item """none"" (OSSL_PKEY_RSA_PAD_MODE_NONE)"
.PD 0
.IP """oaep"" (\fBOSSL_PKEY_RSA_PAD_MODE_OAEP\fR)" 4
.IX Item """oaep"" (OSSL_PKEY_RSA_PAD_MODE_OAEP)"
.IP """pkcs1"" (\fBOSSL_PKEY_RSA_PAD_MODE_PKCSV15\fR)" 4
.IX Item """pkcs1"" (OSSL_PKEY_RSA_PAD_MODE_PKCSV15)"
.IP """x931"" (\fBOSSL_PKEY_RSA_PAD_MODE_X931\fR)" 4
.IX Item """x931"" (OSSL_PKEY_RSA_PAD_MODE_X931)"
.RE
.RS 4
.RE
.IP """pad-mode"" (\fBOSSL_ASYM_CIPHER_PARAM_PAD_MODE\fR) <integer>" 4
.IX Item """pad-mode"" (OSSL_ASYM_CIPHER_PARAM_PAD_MODE) <integer>"
.PD
The default provider understands these RSA padding modes in integer form:
.RS 4
.IP "1 (\fBRSA_PKCS1_PADDING\fR)" 4
.IX Item "1 (RSA_PKCS1_PADDING)"
.PD 0
.IP "3 (\fBRSA_NO_PADDING\fR)" 4
.IX Item "3 (RSA_NO_PADDING)"
.IP "4 (\fBRSA_PKCS1_OAEP_PADDING\fR)" 4
.IX Item "4 (RSA_PKCS1_OAEP_PADDING)"
.IP "5 (\fBRSA_X931_PADDING\fR)" 4
.IX Item "5 (RSA_X931_PADDING)"
.RE
.RS 4
.PD
.Sp
See \fBEVP_PKEY_CTX_set_rsa_padding\fR\|(3) for further details.
.RE
.IP """digest"" (\fBOSSL_ASYM_CIPHER_PARAM_OAEP_DIGEST\fR) <UTF8 string>" 4
.IX Item """digest"" (OSSL_ASYM_CIPHER_PARAM_OAEP_DIGEST) <UTF8 string>"
.PD 0
.IP """digest-props"" (\fBOSSL_ASYM_CIPHER_PARAM_OAEP_DIGEST_PROPS\fR) <UTF8 string>" 4
.IX Item """digest-props"" (OSSL_ASYM_CIPHER_PARAM_OAEP_DIGEST_PROPS) <UTF8 string>"
.IP """mgf1\-digest"" (\fBOSSL_ASYM_CIPHER_PARAM_MGF1_DIGEST\fR) <UTF8 string>" 4
.IX Item """mgf1-digest"" (OSSL_ASYM_CIPHER_PARAM_MGF1_DIGEST) <UTF8 string>"
.IP """mgf1\-digest\-props"" (\fBOSSL_ASYM_CIPHER_PARAM_MGF1_DIGEST_PROPS\fR) <UTF8 string>" 4
.IX Item """mgf1-digest-props"" (OSSL_ASYM_CIPHER_PARAM_MGF1_DIGEST_PROPS) <UTF8 string>"
.IP """oaep-label"" (\fBOSSL_ASYM_CIPHER_PARAM_OAEP_LABEL\fR) <octet string>" 4
.IX Item """oaep-label"" (OSSL_ASYM_CIPHER_PARAM_OAEP_LABEL) <octet string>"
.IP """tls-client-version"" (\fBOSSL_ASYM_CIPHER_PARAM_TLS_CLIENT_VERSION\fR) <unsigned integer>" 4
.IX Item """tls-client-version"" (OSSL_ASYM_CIPHER_PARAM_TLS_CLIENT_VERSION) <unsigned integer>"
.PD
See \fBRSA_PKCS1_WITH_TLS_PADDING\fR on the page \fBEVP_PKEY_CTX_set_rsa_padding\fR\|(3).
.IP """tls-negotiated-version"" (\fBOSSL_ASYM_CIPHER_PARAM_TLS_CLIENT_VERSION\fR) <unsigned integer>" 4
.IX Item """tls-negotiated-version"" (OSSL_ASYM_CIPHER_PARAM_TLS_CLIENT_VERSION) <unsigned integer>"
See \fBRSA_PKCS1_WITH_TLS_PADDING\fR on the page \fBEVP_PKEY_CTX_set_rsa_padding\fR\|(3).
.Sp
See "Asymmetric Cipher Parameters" in \fBprovider\-asym_cipher\fR\|(7) for more information.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBEVP_PKEY\-RSA\fR\|(7),
\&\fBEVP_PKEY\fR\|(3),
\&\fBprovider\-asym_cipher\fR\|(7),
\&\fBprovider\-keymgmt\fR\|(7),
\&\fBOSSL_PROVIDER\-default\fR\|(7)
\&\fBOSSL_PROVIDER\-FIPS\fR\|(7)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2022 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
