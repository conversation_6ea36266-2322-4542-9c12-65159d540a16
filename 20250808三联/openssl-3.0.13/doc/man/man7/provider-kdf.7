.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "PROVIDER-KDF 7ossl"
.TH PROVIDER-KDF 7ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
provider\-kdf \- The KDF library <\-> provider functions
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 2
\& #include <openssl/core_dispatch.h>
\& #include <openssl/core_names.h>
\&
\& /*
\&  * None of these are actual functions, but are displayed like this for
\&  * the function signatures for functions that are offered as function
\&  * pointers in OSSL_DISPATCH arrays.
\&  */
\&
\& /* Context management */
\& void *OSSL_FUNC_kdf_newctx(void *provctx);
\& void OSSL_FUNC_kdf_freectx(void *kctx);
\& void *OSSL_FUNC_kdf_dupctx(void *src);
\&
\& /* Encryption/decryption */
\& int OSSL_FUNC_kdf_reset(void *kctx);
\& int OSSL_FUNC_kdf_derive(void *kctx, unsigned char *key, size_t keylen,
\&                          const OSSL_PARAM params[]);
\&
\& /* KDF parameter descriptors */
\& const OSSL_PARAM *OSSL_FUNC_kdf_gettable_params(void *provctx);
\& const OSSL_PARAM *OSSL_FUNC_kdf_gettable_ctx_params(void *kcxt, void *provctx);
\& const OSSL_PARAM *OSSL_FUNC_kdf_settable_ctx_params(void *kcxt, void *provctx);
\&
\& /* KDF parameters */
\& int OSSL_FUNC_kdf_get_params(OSSL_PARAM params[]);
\& int OSSL_FUNC_kdf_get_ctx_params(void *kctx, OSSL_PARAM params[]);
\& int OSSL_FUNC_kdf_set_ctx_params(void *kctx, const OSSL_PARAM params[]);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
This documentation is primarily aimed at provider authors. See \fBprovider\fR\|(7)
for further information.
.PP
The KDF operation enables providers to implement KDF algorithms and make
them available to applications via the API functions \fBEVP_KDF_CTX_reset\fR\|(3),
and \fBEVP_KDF_derive\fR\|(3).
.PP
All "functions" mentioned here are passed as function pointers between
\&\fIlibcrypto\fR and the provider in \fBOSSL_DISPATCH\fR\|(3) arrays via
\&\fBOSSL_ALGORITHM\fR\|(3) arrays that are returned by the provider's
\&\fBprovider_query_operation()\fR function
(see "Provider Functions" in \fBprovider\-base\fR\|(7)).
.PP
All these "functions" have a corresponding function type definition
named \fBOSSL_FUNC_{name}_fn\fR, and a helper function to retrieve the
function pointer from an \fBOSSL_DISPATCH\fR\|(3) element named
\&\fBOSSL_FUNC_{name}\fR.
For example, the "function" \fBOSSL_FUNC_kdf_newctx()\fR has these:
.PP
.Vb 3
\& typedef void *(OSSL_FUNC_kdf_newctx_fn)(void *provctx);
\& static ossl_inline OSSL_FUNC_kdf_newctx_fn
\&     OSSL_FUNC_kdf_newctx(const OSSL_DISPATCH *opf);
.Ve
.PP
\&\fBOSSL_DISPATCH\fR\|(3) array entries are identified by numbers that are provided as
macros in \fBopenssl\-core_dispatch.h\fR\|(7), as follows:
.PP
.Vb 3
\& OSSL_FUNC_kdf_newctx               OSSL_FUNC_KDF_NEWCTX
\& OSSL_FUNC_kdf_freectx              OSSL_FUNC_KDF_FREECTX
\& OSSL_FUNC_kdf_dupctx               OSSL_FUNC_KDF_DUPCTX
\&
\& OSSL_FUNC_kdf_reset                OSSL_FUNC_KDF_RESET
\& OSSL_FUNC_kdf_derive               OSSL_FUNC_KDF_DERIVE
\&
\& OSSL_FUNC_kdf_get_params           OSSL_FUNC_KDF_GET_PARAMS
\& OSSL_FUNC_kdf_get_ctx_params       OSSL_FUNC_KDF_GET_CTX_PARAMS
\& OSSL_FUNC_kdf_set_ctx_params       OSSL_FUNC_KDF_SET_CTX_PARAMS
\&
\& OSSL_FUNC_kdf_gettable_params      OSSL_FUNC_KDF_GETTABLE_PARAMS
\& OSSL_FUNC_kdf_gettable_ctx_params  OSSL_FUNC_KDF_GETTABLE_CTX_PARAMS
\& OSSL_FUNC_kdf_settable_ctx_params  OSSL_FUNC_KDF_SETTABLE_CTX_PARAMS
.Ve
.PP
A KDF algorithm implementation may not implement all of these functions.
In order to be a consistent set of functions, at least the following functions
must be implemented: \fBOSSL_FUNC_kdf_newctx()\fR, \fBOSSL_FUNC_kdf_freectx()\fR,
\&\fBOSSL_FUNC_kdf_set_ctx_params()\fR, \fBOSSL_FUNC_kdf_derive()\fR.
All other functions are optional.
.SS "Context Management Functions"
.IX Subsection "Context Management Functions"
\&\fBOSSL_FUNC_kdf_newctx()\fR should create and return a pointer to a provider side
structure for holding context information during a KDF operation.
A pointer to this context will be passed back in a number of the other KDF
operation function calls.
The parameter \fIprovctx\fR is the provider context generated during provider
initialisation (see \fBprovider\fR\|(7)).
.PP
\&\fBOSSL_FUNC_kdf_freectx()\fR is passed a pointer to the provider side KDF context in
the \fIkctx\fR parameter.
If it receives NULL as \fIkctx\fR value, it should not do anything other than
return.
This function should free any resources associated with that context.
.PP
\&\fBOSSL_FUNC_kdf_dupctx()\fR should duplicate the provider side KDF context in the
\&\fIkctx\fR parameter and return the duplicate copy.
.SS "Encryption/Decryption Functions"
.IX Subsection "Encryption/Decryption Functions"
\&\fBOSSL_FUNC_kdf_reset()\fR initialises a KDF operation given a provider
side KDF context in the \fIkctx\fR parameter.
.PP
\&\fBOSSL_FUNC_kdf_derive()\fR performs the KDF operation after processing the
\&\fIparams\fR as per \fBOSSL_FUNC_kdf_set_ctx_params()\fR.
The \fIkctx\fR parameter contains a pointer to the provider side context.
The resulting key of the desired \fIkeylen\fR should be written to \fIkey\fR.
If the algorithm does not support the requested \fIkeylen\fR the function must
return error.
.SS "KDF Parameters"
.IX Subsection "KDF Parameters"
See \fBOSSL_PARAM\fR\|(3) for further details on the parameters structure used by
these functions.
.PP
\&\fBOSSL_FUNC_kdf_get_params()\fR gets details of parameter values associated with the
provider algorithm and stores them in \fIparams\fR.
.PP
\&\fBOSSL_FUNC_kdf_set_ctx_params()\fR sets KDF parameters associated with the given
provider side KDF context \fIkctx\fR to \fIparams\fR.
Any parameter settings are additional to any that were previously set.
Passing NULL for \fIparams\fR should return true.
.PP
\&\fBOSSL_FUNC_kdf_get_ctx_params()\fR retrieves gettable parameter values associated
with the given provider side KDF context \fIkctx\fR and stores them in \fIparams\fR.
Passing NULL for \fIparams\fR should return true.
.PP
\&\fBOSSL_FUNC_kdf_gettable_params()\fR, \fBOSSL_FUNC_kdf_gettable_ctx_params()\fR,
and \fBOSSL_FUNC_kdf_settable_ctx_params()\fR all return constant \fBOSSL_PARAM\fR\|(3)
arrays as descriptors of the parameters that \fBOSSL_FUNC_kdf_get_params()\fR,
\&\fBOSSL_FUNC_kdf_get_ctx_params()\fR, and \fBOSSL_FUNC_kdf_set_ctx_params()\fR
can handle, respectively.  \fBOSSL_FUNC_kdf_gettable_ctx_params()\fR and
\&\fBOSSL_FUNC_kdf_settable_ctx_params()\fR will return the parameters associated
with the provider side context \fIkctx\fR in its current state if it is
not NULL.  Otherwise, they return the parameters associated with the
provider side algorithm \fIprovctx\fR.
.PP
Parameters currently recognised by built-in KDFs are as follows. Not all
parameters are relevant to, or are understood by all KDFs:
.IP """size"" (\fBOSSL_KDF_PARAM_SIZE\fR) <unsigned integer>" 4
.IX Item """size"" (OSSL_KDF_PARAM_SIZE) <unsigned integer>"
Gets the output size from the associated KDF ctx.
If the algorithm produces a variable amount of output, SIZE_MAX should be
returned.
If the input parameters required to calculate the fixed output size have not yet
been supplied, 0 should be returned indicating an error.
.IP """key"" (\fBOSSL_KDF_PARAM_KEY\fR) <octet string>" 4
.IX Item """key"" (OSSL_KDF_PARAM_KEY) <octet string>"
Sets the key in the associated KDF ctx.
.IP """secret"" (\fBOSSL_KDF_PARAM_SECRET\fR) <octet string>" 4
.IX Item """secret"" (OSSL_KDF_PARAM_SECRET) <octet string>"
Sets the secret in the associated KDF ctx.
.IP """pass"" (\fBOSSL_KDF_PARAM_PASSWORD\fR) <octet string>" 4
.IX Item """pass"" (OSSL_KDF_PARAM_PASSWORD) <octet string>"
Sets the password in the associated KDF ctx.
.IP """cipher"" (\fBOSSL_KDF_PARAM_CIPHER\fR) <UTF8 string>" 4
.IX Item """cipher"" (OSSL_KDF_PARAM_CIPHER) <UTF8 string>"
.PD 0
.IP """digest"" (\fBOSSL_KDF_PARAM_DIGEST\fR) <UTF8 string>" 4
.IX Item """digest"" (OSSL_KDF_PARAM_DIGEST) <UTF8 string>"
.IP """mac"" (\fBOSSL_KDF_PARAM_MAC\fR) <UTF8 string>" 4
.IX Item """mac"" (OSSL_KDF_PARAM_MAC) <UTF8 string>"
.PD
Sets the name of the underlying cipher, digest or MAC to be used.
It must name a suitable algorithm for the KDF that's being used.
.IP """maclen"" (\fBOSSL_KDF_PARAM_MAC_SIZE\fR) <octet string>" 4
.IX Item """maclen"" (OSSL_KDF_PARAM_MAC_SIZE) <octet string>"
Sets the length of the MAC in the associated KDF ctx.
.IP """properties"" (\fBOSSL_KDF_PARAM_PROPERTIES\fR) <UTF8 string>" 4
.IX Item """properties"" (OSSL_KDF_PARAM_PROPERTIES) <UTF8 string>"
Sets the properties to be queried when trying to fetch the underlying algorithm.
This must be given together with the algorithm naming parameter to be
considered valid.
.IP """iter"" (\fBOSSL_KDF_PARAM_ITER\fR) <unsigned integer>" 4
.IX Item """iter"" (OSSL_KDF_PARAM_ITER) <unsigned integer>"
Sets the number of iterations in the associated KDF ctx.
.IP """mode"" (\fBOSSL_KDF_PARAM_MODE\fR) <UTF8 string>" 4
.IX Item """mode"" (OSSL_KDF_PARAM_MODE) <UTF8 string>"
Sets the mode in the associated KDF ctx.
.IP """pkcs5"" (\fBOSSL_KDF_PARAM_PKCS5\fR) <integer>" 4
.IX Item """pkcs5"" (OSSL_KDF_PARAM_PKCS5) <integer>"
Enables or disables the SP800\-132 compliance checks.
A mode of 0 enables the compliance checks.
.Sp
The checks performed are:
.RS 4
.IP "\- the iteration count is at least 1000." 4
.IX Item "- the iteration count is at least 1000."
.PD 0
.IP "\- the salt length is at least 128 bits." 4
.IX Item "- the salt length is at least 128 bits."
.IP "\- the derived key length is at least 112 bits." 4
.IX Item "- the derived key length is at least 112 bits."
.RE
.RS 4
.RE
.IP """ukm"" (\fBOSSL_KDF_PARAM_UKM\fR) <octet string>" 4
.IX Item """ukm"" (OSSL_KDF_PARAM_UKM) <octet string>"
.PD
Sets an optional random string that is provided by the sender called
"partyAInfo".  In CMS this is the user keying material.
.IP """cekalg"" (\fBOSSL_KDF_PARAM_CEK_ALG\fR) <UTF8 string>" 4
.IX Item """cekalg"" (OSSL_KDF_PARAM_CEK_ALG) <UTF8 string>"
Sets the CEK wrapping algorithm name in the associated KDF ctx.
.IP """n"" (\fBOSSL_KDF_PARAM_SCRYPT_N\fR) <unsigned integer>" 4
.IX Item """n"" (OSSL_KDF_PARAM_SCRYPT_N) <unsigned integer>"
Sets the scrypt work factor parameter N in the associated KDF ctx.
.IP """r"" (\fBOSSL_KDF_PARAM_SCRYPT_R\fR) <unsigned integer>" 4
.IX Item """r"" (OSSL_KDF_PARAM_SCRYPT_R) <unsigned integer>"
Sets the scrypt work factor parameter r in the associated KDF ctx.
.IP """p"" (\fBOSSL_KDF_PARAM_SCRYPT_P\fR) <unsigned integer>" 4
.IX Item """p"" (OSSL_KDF_PARAM_SCRYPT_P) <unsigned integer>"
Sets the scrypt work factor parameter p in the associated KDF ctx.
.IP """maxmem_bytes"" (\fBOSSL_KDF_PARAM_SCRYPT_MAXMEM\fR) <unsigned integer>" 4
.IX Item """maxmem_bytes"" (OSSL_KDF_PARAM_SCRYPT_MAXMEM) <unsigned integer>"
Sets the scrypt work factor parameter maxmem in the associated KDF ctx.
.IP """prefix"" (\fBOSSL_KDF_PARAM_PREFIX\fR) <octet string>" 4
.IX Item """prefix"" (OSSL_KDF_PARAM_PREFIX) <octet string>"
Sets the prefix string using by the TLS 1.3 version of HKDF in the
associated KDF ctx.
.IP """label"" (\fBOSSL_KDF_PARAM_LABEL\fR) <octet string>" 4
.IX Item """label"" (OSSL_KDF_PARAM_LABEL) <octet string>"
Sets the label string using by the TLS 1.3 version of HKDF in the
associated KDF ctx.
.IP """data"" (\fBOSSL_KDF_PARAM_DATA\fR) <octet string>" 4
.IX Item """data"" (OSSL_KDF_PARAM_DATA) <octet string>"
Sets the context string using by the TLS 1.3 version of HKDF in the
associated KDF ctx.
.IP """info"" (\fBOSSL_KDF_PARAM_INFO\fR) <octet string>" 4
.IX Item """info"" (OSSL_KDF_PARAM_INFO) <octet string>"
Sets the optional shared info in the associated KDF ctx.
.IP """seed"" (\fBOSSL_KDF_PARAM_SEED\fR) <octet string>" 4
.IX Item """seed"" (OSSL_KDF_PARAM_SEED) <octet string>"
Sets the IV in the associated KDF ctx.
.IP """xcghash"" (\fBOSSL_KDF_PARAM_SSHKDF_XCGHASH\fR) <octet string>" 4
.IX Item """xcghash"" (OSSL_KDF_PARAM_SSHKDF_XCGHASH) <octet string>"
Sets the xcghash in the associated KDF ctx.
.IP """session_id"" (\fBOSSL_KDF_PARAM_SSHKDF_SESSION_ID\fR) <octet string>" 4
.IX Item """session_id"" (OSSL_KDF_PARAM_SSHKDF_SESSION_ID) <octet string>"
Sets the session ID in the associated KDF ctx.
.IP """type"" (\fBOSSL_KDF_PARAM_SSHKDF_TYPE\fR) <UTF8 string>" 4
.IX Item """type"" (OSSL_KDF_PARAM_SSHKDF_TYPE) <UTF8 string>"
Sets the SSH KDF type parameter in the associated KDF ctx.
There are six supported types:
.RS 4
.IP EVP_KDF_SSHKDF_TYPE_INITIAL_IV_CLI_TO_SRV 4
.IX Item "EVP_KDF_SSHKDF_TYPE_INITIAL_IV_CLI_TO_SRV"
The Initial IV from client to server.
A single char of value 65 (ASCII char 'A').
.IP EVP_KDF_SSHKDF_TYPE_INITIAL_IV_SRV_TO_CLI 4
.IX Item "EVP_KDF_SSHKDF_TYPE_INITIAL_IV_SRV_TO_CLI"
The Initial IV from server to client
A single char of value 66 (ASCII char 'B').
.IP EVP_KDF_SSHKDF_TYPE_ENCRYPTION_KEY_CLI_TO_SRV 4
.IX Item "EVP_KDF_SSHKDF_TYPE_ENCRYPTION_KEY_CLI_TO_SRV"
The Encryption Key from client to server
A single char of value 67 (ASCII char 'C').
.IP EVP_KDF_SSHKDF_TYPE_ENCRYPTION_KEY_SRV_TO_CLI 4
.IX Item "EVP_KDF_SSHKDF_TYPE_ENCRYPTION_KEY_SRV_TO_CLI"
The Encryption Key from server to client
A single char of value 68 (ASCII char 'D').
.IP EVP_KDF_SSHKDF_TYPE_INTEGRITY_KEY_CLI_TO_SRV 4
.IX Item "EVP_KDF_SSHKDF_TYPE_INTEGRITY_KEY_CLI_TO_SRV"
The Integrity Key from client to server
A single char of value 69 (ASCII char 'E').
.IP EVP_KDF_SSHKDF_TYPE_INTEGRITY_KEY_SRV_TO_CLI 4
.IX Item "EVP_KDF_SSHKDF_TYPE_INTEGRITY_KEY_SRV_TO_CLI"
The Integrity Key from client to server
A single char of value 70 (ASCII char 'F').
.RE
.RS 4
.RE
.IP """constant"" (\fBOSSL_KDF_PARAM_CONSTANT\fR) <octet string>" 4
.IX Item """constant"" (OSSL_KDF_PARAM_CONSTANT) <octet string>"
Sets the constant value in the associated KDF ctx.
.IP """id"" (\fBOSSL_KDF_PARAM_PKCS12_ID\fR) <integer>" 4
.IX Item """id"" (OSSL_KDF_PARAM_PKCS12_ID) <integer>"
Sets the intended usage of the output bits in the associated KDF ctx.
It is defined as per RFC 7292 section B.3.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBOSSL_FUNC_kdf_newctx()\fR and \fBOSSL_FUNC_kdf_dupctx()\fR should return the newly created
provider side KDF context, or NULL on failure.
.PP
\&\fBOSSL_FUNC_kdf_derive()\fR, \fBOSSL_FUNC_kdf_get_params()\fR,
\&\fBOSSL_FUNC_kdf_get_ctx_params()\fR and \fBOSSL_FUNC_kdf_set_ctx_params()\fR should return 1 for
success or 0 on error.
.PP
\&\fBOSSL_FUNC_kdf_gettable_params()\fR, \fBOSSL_FUNC_kdf_gettable_ctx_params()\fR and
\&\fBOSSL_FUNC_kdf_settable_ctx_params()\fR should return a constant \fBOSSL_PARAM\fR\|(3)
array, or NULL if none is offered.
.SH NOTES
.IX Header "NOTES"
The KDF life-cycle is described in \fBlife_cycle\-kdf\fR\|(7).  Providers should
ensure that the various transitions listed there are supported.  At some point
the EVP layer will begin enforcing the listed transitions.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBprovider\fR\|(7), \fBlife_cycle\-kdf\fR\|(7), \fBEVP_KDF\fR\|(3).
.SH HISTORY
.IX Header "HISTORY"
The provider KDF interface was introduced in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2020\-2023 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
