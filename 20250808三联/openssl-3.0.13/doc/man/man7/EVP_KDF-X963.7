.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "EVP_KDF-X963 7ossl"
.TH EVP_KDF-X963 7ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
EVP_KDF\-X963 \- The X9.63\-2001 EVP_KDF implementation
.SH DESCRIPTION
.IX Header "DESCRIPTION"
The EVP_KDF\-X963 algorithm implements the key derivation function (X963KDF).
X963KDF is used by Cryptographic Message Syntax (CMS) for EC KeyAgreement, to
derive a key using input such as a shared secret key and shared info.
.SS Identity
.IX Subsection "Identity"
"X963KDF" is the name for this implementation; it
can be used with the \fBEVP_KDF_fetch()\fR function.
.SS "Supported parameters"
.IX Subsection "Supported parameters"
The supported parameters are:
.IP """properties"" (\fBOSSL_KDF_PARAM_PROPERTIES\fR) <UTF8 string>" 4
.IX Item """properties"" (OSSL_KDF_PARAM_PROPERTIES) <UTF8 string>"
.PD 0
.IP """digest"" (\fBOSSL_KDF_PARAM_DIGEST\fR) <UTF8 string>" 4
.IX Item """digest"" (OSSL_KDF_PARAM_DIGEST) <UTF8 string>"
.PD
These parameters work as described in "PARAMETERS" in \fBEVP_KDF\fR\|(3).
.IP """key"" (\fBOSSL_KDF_PARAM_KEY\fR) <octet string>" 4
.IX Item """key"" (OSSL_KDF_PARAM_KEY) <octet string>"
The shared secret used for key derivation.
This parameter sets the secret.
.IP """info"" (\fBOSSL_KDF_PARAM_INFO\fR) <octet string>" 4
.IX Item """info"" (OSSL_KDF_PARAM_INFO) <octet string>"
This parameter specifies an optional value for shared info.
.SH NOTES
.IX Header "NOTES"
X963KDF is very similar to the SSKDF that uses a digest as the auxiliary function,
X963KDF appends the counter to the secret, whereas SSKDF prepends the counter.
.PP
A context for X963KDF can be obtained by calling:
.PP
.Vb 2
\& EVP_KDF *kdf = EVP_KDF_fetch(NULL, "X963KDF", NULL);
\& EVP_KDF_CTX *kctx = EVP_KDF_CTX_new(kdf);
.Ve
.PP
The output length of an X963KDF is specified via the \fIkeylen\fR
parameter to the \fBEVP_KDF_derive\fR\|(3) function.
.SH EXAMPLES
.IX Header "EXAMPLES"
This example derives 10 bytes, with the secret key "secret" and sharedinfo
value "label":
.PP
.Vb 4
\& EVP_KDF *kdf;
\& EVP_KDF_CTX *kctx;
\& unsigned char out[10];
\& OSSL_PARAM params[4], *p = params;
\&
\& kdf = EVP_KDF_fetch(NULL, "X963KDF", NULL);
\& kctx = EVP_KDF_CTX_new(kdf);
\& EVP_KDF_free(kdf);
\&
\& *p++ = OSSL_PARAM_construct_utf8_string(OSSL_KDF_PARAM_DIGEST,
\&                                         SN_sha256, strlen(SN_sha256));
\& *p++ = OSSL_PARAM_construct_octet_string(OSSL_KDF_PARAM_SECRET,
\&                                          "secret", (size_t)6);
\& *p++ = OSSL_PARAM_construct_octet_string(OSSL_KDF_PARAM_INFO,
\&                                          "label", (size_t)5);
\& *p = OSSL_PARAM_construct_end();
\& if (EVP_KDF_derive(kctx, out, sizeof(out), params) <= 0) {
\&     error("EVP_KDF_derive");
\& }
\&
\& EVP_KDF_CTX_free(kctx);
.Ve
.SH "CONFORMING TO"
.IX Header "CONFORMING TO"
"SEC 1: Elliptic Curve Cryptography"
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBEVP_KDF\fR\|(3),
\&\fBEVP_KDF_CTX_new\fR\|(3),
\&\fBEVP_KDF_CTX_free\fR\|(3),
\&\fBEVP_KDF_CTX_set_params\fR\|(3),
\&\fBEVP_KDF_CTX_get_kdf_size\fR\|(3),
\&\fBEVP_KDF_derive\fR\|(3),
"PARAMETERS" in \fBEVP_KDF\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
This functionality was added in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2019\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
