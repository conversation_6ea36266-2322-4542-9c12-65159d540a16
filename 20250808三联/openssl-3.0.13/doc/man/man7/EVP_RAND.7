.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "EVP_RAND 7ossl"
.TH EVP_RAND 7ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
EVP_RAND \- the random bit generator
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 2
\& #include <openssl/evp.h>
\& #include <rand.h>
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
The default OpenSSL RAND method is based on the EVP_RAND classes to provide
non-deterministic inputs to other cryptographic algorithms.
.PP
While the RAND API is the 'frontend' which is intended to be used by
application developers for obtaining random bytes, the EVP_RAND API
serves as the 'backend', connecting the former with the operating
systems's entropy sources and providing access to deterministic random
bit generators (DRBG) and their configuration parameters.
A DRBG is a certain type of cryptographically-secure pseudo-random
number generator (CSPRNG), which is described in
[NIST SP 800\-90A Rev. 1].
.SS Disclaimer
.IX Subsection "Disclaimer"
Unless you have very specific requirements for your random generator,
it is in general not necessary to utilize the EVP_RAND API directly.
The usual way to obtain random bytes is to use \fBRAND_bytes\fR\|(3) or
\&\fBRAND_priv_bytes\fR\|(3), see also \fBRAND\fR\|(7).
.SS "Typical Use Cases"
.IX Subsection "Typical Use Cases"
Typical examples for such special use cases are the following:
.IP \(bu 2
You want to use your own private DRBG instances.
Multiple DRBG instances which are accessed only by a single thread provide
additional security (because their internal states are independent) and
better scalability in multithreaded applications (because they don't need
to be locked).
.IP \(bu 2
You need to integrate a previously unsupported entropy source.
Refer to \fBprovider\-rand\fR\|(7) for the implementation details to support adding
randomness sources to EVP_RAND.
.IP \(bu 2
You need to change the default settings of the standard OpenSSL RAND
implementation to meet specific requirements.
.SH "EVP_RAND CHAINING"
.IX Header "EVP_RAND CHAINING"
An EVP_RAND instance can be used as the entropy source of another
EVP_RAND instance, provided it has itself access to a valid entropy source.
The EVP_RAND instance which acts as entropy source is called the \fIparent\fR,
the other instance the \fIchild\fR.  Typically, the child will be a DRBG because
it does not make sense for the child to be an entropy source.
.PP
This is called chaining. A chained EVP_RAND instance is created by passing
a pointer to the parent EVP_RAND_CTX as argument to the \fBEVP_RAND_CTX_new()\fR call.
It is possible to create chains of more than two DRBG in a row.
It is also possible to use any EVP_RAND_CTX class as the parent, however, only
a live entropy source may ignore and not use its parent.
.SH "THE THREE SHARED DRBG INSTANCES"
.IX Header "THE THREE SHARED DRBG INSTANCES"
Currently, there are three shared DRBG instances,
the <primary>, <public>, and <private> DRBG.
While the <primary> DRBG is a single global instance, the <public> and <private>
DRBG are created per thread and accessed through thread-local storage.
.PP
By default, the functions \fBRAND_bytes\fR\|(3) and \fBRAND_priv_bytes\fR\|(3) use
the thread-local <public> and <private> DRBG instance, respectively.
.SS "The <primary> DRBG instance"
.IX Subsection "The <primary> DRBG instance"
The <primary> DRBG is not used directly by the application, only for reseeding
the two other two DRBG instances. It reseeds itself by obtaining randomness
either from os entropy sources or by consuming randomness which was added
previously by \fBRAND_add\fR\|(3).
.SS "The <public> DRBG instance"
.IX Subsection "The <public> DRBG instance"
This instance is used per default by \fBRAND_bytes\fR\|(3).
.SS "The <private> DRBG instance"
.IX Subsection "The <private> DRBG instance"
This instance is used per default by \fBRAND_priv_bytes\fR\|(3)
.SH LOCKING
.IX Header "LOCKING"
The <primary> DRBG is intended to be accessed concurrently for reseeding
by its child DRBG instances. The necessary locking is done internally.
It is \fInot\fR thread-safe to access the <primary> DRBG directly via the
EVP_RAND interface.
The <public> and <private> DRBG are thread-local, i.e. there is an
instance of each per thread. So they can safely be accessed without
locking via the EVP_RAND interface.
.PP
Pointers to these DRBG instances can be obtained using
\&\fBRAND_get0_primary()\fR, \fBRAND_get0_public()\fR and \fBRAND_get0_private()\fR, respectively.
Note that it is not allowed to store a pointer to one of the thread-local
DRBG instances in a variable or other memory location where it will be
accessed and used by multiple threads.
.PP
All other DRBG instances created by an application don't support locking,
because they are intended to be used by a single thread.
Instead of accessing a single DRBG instance concurrently from different
threads, it is recommended to instantiate a separate DRBG instance per
thread. Using the <primary> DRBG as entropy source for multiple DRBG
instances on different threads is thread-safe, because the DRBG instance
will lock the <primary> DRBG automatically for obtaining random input.
.SH "THE OVERALL PICTURE"
.IX Header "THE OVERALL PICTURE"
The following picture gives an overview over how the DRBG instances work
together and are being used.
.PP
.Vb 10
\&               +\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-+
\&               | os entropy sources |
\&               +\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-+
\&                        |
\&                        v           +\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-+
\&     RAND_add() ==> <primary>     <\-| shared DRBG (with locking)  |
\&                      /   \e         +\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-+
\&                     /     \e              +\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-+
\&              <public>     <private>   <\- | per\-thread DRBG instances |
\&                 |             |          +\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-+
\&                 v             v
\&               RAND_bytes()   RAND_priv_bytes()
\&                    |               ^
\&                    |               |
\&    +\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-+      +\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-+
\&    | general purpose  |      | used for secrets like session keys |
\&    | random generator |      | and private keys for certificates  |
\&    +\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-+      +\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-+
.Ve
.PP
The usual way to obtain random bytes is to call RAND_bytes(...) or
RAND_priv_bytes(...). These calls are roughly equivalent to calling
EVP_RAND_generate(<public>, ...) and
EVP_RAND_generate(<private>, ...),
respectively.
.SH RESEEDING
.IX Header "RESEEDING"
A DRBG instance seeds itself automatically, pulling random input from
its entropy source. The entropy source can be either a trusted operating
system entropy source, or another DRBG with access to such a source.
.PP
Automatic reseeding occurs after a predefined number of generate requests.
The selection of the trusted entropy sources is configured at build
time using the \-\-with\-rand\-seed option. The following sections explain
the reseeding process in more detail.
.SS "Automatic Reseeding"
.IX Subsection "Automatic Reseeding"
Before satisfying a generate request (\fBEVP_RAND_generate\fR\|(3)), the DRBG
reseeds itself automatically, if one of the following conditions holds:
.PP
\&\- the DRBG was not instantiated (=seeded) yet or has been uninstantiated.
.PP
\&\- the number of generate requests since the last reseeding exceeds a
certain threshold, the so called \fIreseed_interval\fR.
This behaviour can be disabled by setting the \fIreseed_interval\fR to 0.
.PP
\&\- the time elapsed since the last reseeding exceeds a certain time
interval, the so called \fIreseed_time_interval\fR.
This can be disabled by setting the \fIreseed_time_interval\fR to 0.
.PP
\&\- the DRBG is in an error state.
.PP
\&\fBNote\fR: An error state is entered if the entropy source fails while
the DRBG is seeding or reseeding.
The last case ensures that the DRBG automatically recovers
from the error as soon as the entropy source is available again.
.SS "Manual Reseeding"
.IX Subsection "Manual Reseeding"
In addition to automatic reseeding, the caller can request an immediate
reseeding of the DRBG with fresh entropy by setting the
\&\fIprediction resistance\fR parameter to 1 when calling
\&\fBEVP_RAND_generate\fR\|(3).
.PP
The document [NIST SP 800\-90C] describes prediction resistance requests
in detail and imposes strict conditions on the entropy sources that are
approved for providing prediction resistance.
A request for prediction resistance can only be satisfied by pulling fresh
entropy from a live entropy source (section 5.5.2 of [NIST SP 800\-90C]).
It is up to the user to ensure that a live entropy source is configured
and is being used.
.PP
For the three shared DRBGs (and only for these) there is another way to
reseed them manually:
If \fBRAND_add\fR\|(3) is called with a positive \fIrandomness\fR argument
(or \fBRAND_seed\fR\|(3)), then this will immediately reseed the <primary> DRBG.
The <public> and <private> DRBG will detect this on their next generate
call and reseed, pulling randomness from <primary>.
.PP
The last feature has been added to support the common practice used with
previous OpenSSL versions to call \fBRAND_add()\fR before calling \fBRAND_bytes()\fR.
.SS "Entropy Input and Additional Data"
.IX Subsection "Entropy Input and Additional Data"
The DRBG distinguishes two different types of random input: \fIentropy\fR,
which comes from a trusted source, and \fIadditional input\fR',
which can optionally be added by the user and is considered untrusted.
It is possible to add \fIadditional input\fR not only during reseeding,
but also for every generate request.
.SS "Configuring the Random Seed Source"
.IX Subsection "Configuring the Random Seed Source"
In most cases OpenSSL will automatically choose a suitable seed source
for automatically seeding and reseeding its <primary> DRBG. In some cases
however, it will be necessary to explicitly specify a seed source during
configuration, using the \-\-with\-rand\-seed option. For more information,
see the INSTALL instructions. There are also operating systems where no
seed source is available and automatic reseeding is disabled by default.
.PP
The following two sections describe the reseeding process of the primary
DRBG, depending on whether automatic reseeding is available or not.
.SS "Reseeding the primary DRBG with automatic seeding enabled"
.IX Subsection "Reseeding the primary DRBG with automatic seeding enabled"
Calling \fBRAND_poll()\fR or \fBRAND_add()\fR is not necessary, because the DRBG
pulls the necessary entropy from its source automatically.
However, both calls are permitted, and do reseed the RNG.
.PP
\&\fBRAND_add()\fR can be used to add both kinds of random input, depending on the
value of the \fIrandomness\fR argument:
.IP "randomness == 0:" 4
.IX Item "randomness == 0:"
The random bytes are mixed as additional input into the current state of
the DRBG.
Mixing in additional input is not considered a full reseeding, hence the
reseed counter is not reset.
.IP "randomness > 0:" 4
.IX Item "randomness > 0:"
The random bytes are used as entropy input for a full reseeding
(resp. reinstantiation) if the DRBG is instantiated
(resp. uninstantiated or in an error state).
The number of random bits required for reseeding is determined by the
security strength of the DRBG. Currently it defaults to 256 bits (32 bytes).
It is possible to provide less randomness than required.
In this case the missing randomness will be obtained by pulling random input
from the trusted entropy sources.
.PP
NOTE: Manual reseeding is *not allowed* in FIPS mode, because
[NIST SP\-800\-90Ar1] mandates that entropy *shall not* be provided by
the consuming application for instantiation (Section 9.1) or
reseeding (Section 9.2). For that reason, the \fIrandomness\fR
argument is ignored and the random bytes provided by the \fBRAND_add\fR\|(3) and
\&\fBRAND_seed\fR\|(3) calls are treated as additional data.
.SS "Reseeding the primary DRBG with automatic seeding disabled"
.IX Subsection "Reseeding the primary DRBG with automatic seeding disabled"
Calling \fBRAND_poll()\fR will always fail.
.PP
\&\fBRAND_add()\fR needs to be called for initial seeding and periodic reseeding.
At least 48 bytes (384 bits) of randomness have to be provided, otherwise
the (re\-)seeding of the DRBG will fail. This corresponds to one and a half
times the security strength of the DRBG. The extra half is used for the
nonce during instantiation.
.PP
More precisely, the number of bytes needed for seeding depend on the
\&\fIsecurity strength\fR of the DRBG, which is set to 256 by default.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBRAND\fR\|(7), \fBEVP_RAND\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
This functionality was added in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2017\-2020 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
