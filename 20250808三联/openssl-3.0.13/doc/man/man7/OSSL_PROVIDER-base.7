.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "OSSL_PROVIDER-BASE 7ossl"
.TH OSSL_PROVIDER-BASE 7ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
OSSL_PROVIDER\-base \- OpenSSL base provider
.SH DESCRIPTION
.IX Header "DESCRIPTION"
The OpenSSL base provider supplies the encoding for OpenSSL's
asymmetric cryptography.
.SS Properties
.IX Subsection "Properties"
The implementations in this provider specifically have this property
defined:
.IP """provider=base""" 4
.IX Item """provider=base"""
.PP
It may be used in a property query string with fetching functions.
.PP
It isn't mandatory to query for this property, except to make sure to get
implementations of this provider and none other.
.IP """type=parameters""" 4
.IX Item """type=parameters"""
.PD 0
.IP """type=private""" 4
.IX Item """type=private"""
.IP """type=public""" 4
.IX Item """type=public"""
.PD
.PP
These may be used in a property query string with fetching functions to select
which data are to be encoded.  Either the private key material, the public
key material or the domain parameters can be selected.
.IP """format=der""" 4
.IX Item """format=der"""
.PD 0
.IP """format=pem""" 4
.IX Item """format=pem"""
.IP """format=text""" 4
.IX Item """format=text"""
.PD
.PP
These may be used in a property query string with fetching functions to select
the encoding output format.  Either the DER, PEM and plaintext are
currently permitted.
.SH "OPERATIONS AND ALGORITHMS"
.IX Header "OPERATIONS AND ALGORITHMS"
The OpenSSL base provider supports these operations and algorithms:
.SS "Asymmetric Key Encoder"
.IX Subsection "Asymmetric Key Encoder"
In addition to "provider=base", some of these encoders define the
property "fips=yes", to allow them to be used together with the FIPS
provider.
.IP "RSA, see \fBOSSL_ENCODER\-RSA\fR\|(7)" 4
.IX Item "RSA, see OSSL_ENCODER-RSA"
.PD 0
.IP "DH, see \fBOSSL_ENCODER\-DH\fR\|(7)" 4
.IX Item "DH, see OSSL_ENCODER-DH"
.IP "DSA, see \fBOSSL_ENCODER\-DSA\fR\|(7)" 4
.IX Item "DSA, see OSSL_ENCODER-DSA"
.IP "EC, see \fBOSSL_ENCODER\-EC\fR\|(7)" 4
.IX Item "EC, see OSSL_ENCODER-EC"
.IP "X25519, see \fBOSSL_ENCODER\-X25519\fR\|(7)" 4
.IX Item "X25519, see OSSL_ENCODER-X25519"
.IP "X448, see \fBOSSL_ENCODER\-X448\fR\|(7)" 4
.IX Item "X448, see OSSL_ENCODER-X448"
.PD
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBOSSL_PROVIDER\-default\fR\|(7), \fBopenssl\-core.h\fR\|(7),
\&\fBopenssl\-core_dispatch.h\fR\|(7), \fBprovider\fR\|(7)
.SH HISTORY
.IX Header "HISTORY"
This functionality was added in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2020 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
