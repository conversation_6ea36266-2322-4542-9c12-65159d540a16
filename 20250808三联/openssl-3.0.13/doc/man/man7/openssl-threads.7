.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "OPENSSL-THREADS 7ossl"
.TH OPENSSL-THREADS 7ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
openssl\-threads \- Overview of thread safety in OpenSSL
.SH DESCRIPTION
.IX Header "DESCRIPTION"
In this man page, we use the term \fBthread-safe\fR to indicate that an
object or function can be used by multiple threads at the same time.
.PP
OpenSSL can be built with or without threads support. The most important
use of this support is so that OpenSSL itself can use a single consistent
API, as shown in "EXAMPLES" in \fBCRYPTO_THREAD_run_once\fR\|(3).
Multi-platform applications can also use this API.
.PP
In particular, being configured for threads support does not imply that
all OpenSSL objects are thread-safe.
To emphasize: \fImost objects are not safe for simultaneous use\fR.
Exceptions to this should be documented on the specific manual pages, and
some general high-level guidance is given here.
.PP
One major use of the OpenSSL thread API is to implement reference counting.
Many objects within OpenSSL are reference-counted, so resources are not
released, until the last reference is removed.
References are often increased automatically (such as when an \fBX509\fR
certificate object is added into an \fBX509_STORE\fR trust store).
There is often an \fR\f(BIobject\fR\fB_up_ref\fR() function that can be used to increase
the reference count.
Failure to match \fB\fR\f(BIobject\fR\fB_up_ref\fR() calls with the right number of
\&\fB\fR\f(BIobject\fR\fB_free\fR() calls is a common source of memory leaks when a program
exits.
.PP
Many objects have set and get API's to set attributes in the object.
A \f(CW\*(C`set0\*(C'\fR passes ownership from the caller to the object and a
\&\f(CW\*(C`get0\*(C'\fR returns a pointer but the attribute ownership
remains with the object and a reference to it is returned.
A \f(CW\*(C`set1\*(C'\fR or \f(CW\*(C`get1\*(C'\fR function does not change the ownership, but instead
updates the attribute's reference count so that the object is shared
between the caller and the object; the caller must free the returned
attribute when finished.
Functions that involve attributes that have reference counts themselves,
but are named with just \f(CW\*(C`set\*(C'\fR or \f(CW\*(C`get\*(C'\fR are historical; and the documentation
must state how the references are handled.
Get methods are often thread-safe as long as the ownership requirements are
met and shared objects are not modified.
Set methods, or modifying shared objects, are generally not thread-safe
as discussed below.
.PP
Objects are thread-safe
as long as the API's being invoked don't modify the object; in this
case the parameter is usually marked in the API as \f(CW\*(C`const\*(C'\fR.
Not all parameters are marked this way.
Note that a \f(CW\*(C`const\*(C'\fR declaration does not mean immutable; for example
\&\fBX509_cmp\fR\|(3) takes pointers to \f(CW\*(C`const\*(C'\fR objects, but the implementation
uses a C cast to remove that so it can lock objects, generate and cache
a DER encoding, and so on.
.PP
Another instance of thread-safety is when updates to an object's
internal state, such as cached values, are done with locks.
One example of this is the reference counting API's described above.
.PP
In all cases, however, it is generally not safe for one thread to
mutate an object, such as setting elements of a private or public key,
while another thread is using that object, such as verifying a signature.
.PP
The same API's can usually be used simultaneously on different objects
without interference.
For example, two threads can calculate a signature using two different
\&\fBEVP_PKEY_CTX\fR objects.
.PP
For implicit global state or singletons, thread-safety depends on the facility.
The \fBCRYPTO_secure_malloc\fR\|(3) and related API's have their own lock,
while \fBCRYPTO_malloc\fR\|(3) assumes the underlying platform allocation
will do any necessary locking.
Some API's, such as \fBNCONF_load\fR\|(3) and related, or \fBOBJ_create\fR\|(3)
do no locking at all; this can be considered a bug.
.PP
A separate, although related, issue is modifying "factory" objects
when other objects have been created from that.
For example, an \fBSSL_CTX\fR object created by \fBSSL_CTX_new\fR\|(3) is used
to create per-connection \fBSSL\fR objects by calling \fBSSL_new\fR\|(3).
In this specific case, and probably for factory methods in general, it is
not safe to modify the factory object after it has been used to create
other objects.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBCRYPTO_THREAD_run_once\fR\|(3),
local system threads documentation.
.SH BUGS
.IX Header "BUGS"
This page is admittedly very incomplete.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
