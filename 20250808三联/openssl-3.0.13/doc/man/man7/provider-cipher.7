.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "PROVIDER-CIPHER 7ossl"
.TH PROVIDER-CIPHER 7ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
provider\-cipher \- The cipher library <\-> provider functions
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 2
\& #include <openssl/core_dispatch.h>
\& #include <openssl/core_names.h>
\&
\& /*
\&  * None of these are actual functions, but are displayed like this for
\&  * the function signatures for functions that are offered as function
\&  * pointers in OSSL_DISPATCH arrays.
\&  */
\&
\& /* Context management */
\& void *OSSL_FUNC_cipher_newctx(void *provctx);
\& void OSSL_FUNC_cipher_freectx(void *cctx);
\& void *OSSL_FUNC_cipher_dupctx(void *cctx);
\&
\& /* Encryption/decryption */
\& int OSSL_FUNC_cipher_encrypt_init(void *cctx, const unsigned char *key,
\&                                   size_t keylen, const unsigned char *iv,
\&                                   size_t ivlen, const OSSL_PARAM params[]);
\& int OSSL_FUNC_cipher_decrypt_init(void *cctx, const unsigned char *key,
\&                                   size_t keylen, const unsigned char *iv,
\&                                   size_t ivlen, const OSSL_PARAM params[]);
\& int OSSL_FUNC_cipher_update(void *cctx, unsigned char *out, size_t *outl,
\&                             size_t outsize, const unsigned char *in, size_t inl);
\& int OSSL_FUNC_cipher_final(void *cctx, unsigned char *out, size_t *outl,
\&                            size_t outsize);
\& int OSSL_FUNC_cipher_cipher(void *cctx, unsigned char *out, size_t *outl,
\&                             size_t outsize, const unsigned char *in, size_t inl);
\&
\& /* Cipher parameter descriptors */
\& const OSSL_PARAM *OSSL_FUNC_cipher_gettable_params(void *provctx);
\&
\& /* Cipher operation parameter descriptors */
\& const OSSL_PARAM *OSSL_FUNC_cipher_gettable_ctx_params(void *cctx,
\&                                                        void *provctx);
\& const OSSL_PARAM *OSSL_FUNC_cipher_settable_ctx_params(void *cctx,
\&                                                        void *provctx);
\&
\& /* Cipher parameters */
\& int OSSL_FUNC_cipher_get_params(OSSL_PARAM params[]);
\&
\& /* Cipher operation parameters */
\& int OSSL_FUNC_cipher_get_ctx_params(void *cctx, OSSL_PARAM params[]);
\& int OSSL_FUNC_cipher_set_ctx_params(void *cctx, const OSSL_PARAM params[]);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
This documentation is primarily aimed at provider authors. See \fBprovider\fR\|(7)
for further information.
.PP
The CIPHER operation enables providers to implement cipher algorithms and make
them available to applications via the API functions \fBEVP_EncryptInit_ex\fR\|(3),
\&\fBEVP_EncryptUpdate\fR\|(3) and \fBEVP_EncryptFinal\fR\|(3) (as well as the decrypt
equivalents and other related functions).
.PP
All "functions" mentioned here are passed as function pointers between
\&\fIlibcrypto\fR and the provider in \fBOSSL_DISPATCH\fR\|(3) arrays via
\&\fBOSSL_ALGORITHM\fR\|(3) arrays that are returned by the provider's
\&\fBprovider_query_operation()\fR function
(see "Provider Functions" in \fBprovider\-base\fR\|(7)).
.PP
All these "functions" have a corresponding function type definition
named \fBOSSL_FUNC_{name}_fn\fR, and a helper function to retrieve the
function pointer from an \fBOSSL_DISPATCH\fR\|(3) element named
\&\fBOSSL_FUNC_{name}\fR.
For example, the "function" \fBOSSL_FUNC_cipher_newctx()\fR has these:
.PP
.Vb 3
\& typedef void *(OSSL_FUNC_cipher_newctx_fn)(void *provctx);
\& static ossl_inline OSSL_FUNC_cipher_newctx_fn
\&     OSSL_FUNC_cipher_newctx(const OSSL_DISPATCH *opf);
.Ve
.PP
\&\fBOSSL_DISPATCH\fR\|(3) arrays are indexed by numbers that are provided as
macros in \fBopenssl\-core_dispatch.h\fR\|(7), as follows:
.PP
.Vb 3
\& OSSL_FUNC_cipher_newctx               OSSL_FUNC_CIPHER_NEWCTX
\& OSSL_FUNC_cipher_freectx              OSSL_FUNC_CIPHER_FREECTX
\& OSSL_FUNC_cipher_dupctx               OSSL_FUNC_CIPHER_DUPCTX
\&
\& OSSL_FUNC_cipher_encrypt_init         OSSL_FUNC_CIPHER_ENCRYPT_INIT
\& OSSL_FUNC_cipher_decrypt_init         OSSL_FUNC_CIPHER_DECRYPT_INIT
\& OSSL_FUNC_cipher_update               OSSL_FUNC_CIPHER_UPDATE
\& OSSL_FUNC_cipher_final                OSSL_FUNC_CIPHER_FINAL
\& OSSL_FUNC_cipher_cipher               OSSL_FUNC_CIPHER_CIPHER
\&
\& OSSL_FUNC_cipher_get_params           OSSL_FUNC_CIPHER_GET_PARAMS
\& OSSL_FUNC_cipher_get_ctx_params       OSSL_FUNC_CIPHER_GET_CTX_PARAMS
\& OSSL_FUNC_cipher_set_ctx_params       OSSL_FUNC_CIPHER_SET_CTX_PARAMS
\&
\& OSSL_FUNC_cipher_gettable_params      OSSL_FUNC_CIPHER_GETTABLE_PARAMS
\& OSSL_FUNC_cipher_gettable_ctx_params  OSSL_FUNC_CIPHER_GETTABLE_CTX_PARAMS
\& OSSL_FUNC_cipher_settable_ctx_params  OSSL_FUNC_CIPHER_SETTABLE_CTX_PARAMS
.Ve
.PP
A cipher algorithm implementation may not implement all of these functions.
In order to be a consistent set of functions there must at least be a complete
set of "encrypt" functions, or a complete set of "decrypt" functions, or a
single "cipher" function.
In all cases both the OSSL_FUNC_cipher_newctx and OSSL_FUNC_cipher_freectx functions must be
present.
All other functions are optional.
.SS "Context Management Functions"
.IX Subsection "Context Management Functions"
\&\fBOSSL_FUNC_cipher_newctx()\fR should create and return a pointer to a provider side
structure for holding context information during a cipher operation.
A pointer to this context will be passed back in a number of the other cipher
operation function calls.
The parameter \fIprovctx\fR is the provider context generated during provider
initialisation (see \fBprovider\fR\|(7)).
.PP
\&\fBOSSL_FUNC_cipher_freectx()\fR is passed a pointer to the provider side cipher context in
the \fIcctx\fR parameter.
This function should free any resources associated with that context.
.PP
\&\fBOSSL_FUNC_cipher_dupctx()\fR should duplicate the provider side cipher context in the
\&\fIcctx\fR parameter and return the duplicate copy.
.SS "Encryption/Decryption Functions"
.IX Subsection "Encryption/Decryption Functions"
\&\fBOSSL_FUNC_cipher_encrypt_init()\fR initialises a cipher operation for encryption given a
newly created provider side cipher context in the \fIcctx\fR parameter.
The key to be used is given in \fIkey\fR which is \fIkeylen\fR bytes long.
The IV to be used is given in \fIiv\fR which is \fIivlen\fR bytes long.
The \fIparams\fR, if not NULL, should be set on the context in a manner similar to
using \fBOSSL_FUNC_cipher_set_ctx_params()\fR.
.PP
\&\fBOSSL_FUNC_cipher_decrypt_init()\fR is the same as \fBOSSL_FUNC_cipher_encrypt_init()\fR except that it
initialises the context for a decryption operation.
.PP
\&\fBOSSL_FUNC_cipher_update()\fR is called to supply data to be encrypted/decrypted as part of
a previously initialised cipher operation.
The \fIcctx\fR parameter contains a pointer to a previously initialised provider
side context.
\&\fBOSSL_FUNC_cipher_update()\fR should encrypt/decrypt \fIinl\fR bytes of data at the location
pointed to by \fIin\fR.
The encrypted data should be stored in \fIout\fR and the amount of data written to
\&\fI*outl\fR which should not exceed \fIoutsize\fR bytes.
\&\fBOSSL_FUNC_cipher_update()\fR may be called multiple times for a single cipher operation.
It is the responsibility of the cipher implementation to handle input lengths
that are not multiples of the block length.
In such cases a cipher implementation will typically cache partial blocks of
input data until a complete block is obtained.
The pointers \fIout\fR and \fIin\fR may point to the same location, in which
case the encryption must be done in-place. If \fIout\fR and \fIin\fR point to different
locations, the requirements of \fBEVP_EncryptUpdate\fR\|(3) and \fBEVP_DecryptUpdate\fR\|(3)
guarantee that the two buffers are disjoint.
Similarly, the requirements of \fBEVP_EncryptUpdate\fR\|(3) and \fBEVP_DecryptUpdate\fR\|(3)
ensure that the buffer pointed to by \fIout\fR contains sufficient room for the
operation being performed.
.PP
\&\fBOSSL_FUNC_cipher_final()\fR completes an encryption or decryption started through previous
\&\fBOSSL_FUNC_cipher_encrypt_init()\fR or \fBOSSL_FUNC_cipher_decrypt_init()\fR, and \fBOSSL_FUNC_cipher_update()\fR
calls.
The \fIcctx\fR parameter contains a pointer to the provider side context.
Any final encryption/decryption output should be written to \fIout\fR and the
amount of data written to \fI*outl\fR which should not exceed \fIoutsize\fR bytes.
The same expectations apply to \fIoutsize\fR as documented for
\&\fBEVP_EncryptFinal\fR\|(3) and \fBEVP_DecryptFinal\fR\|(3).
.PP
\&\fBOSSL_FUNC_cipher_cipher()\fR performs encryption/decryption using the provider side cipher
context in the \fIcctx\fR parameter that should have been previously initialised via
a call to \fBOSSL_FUNC_cipher_encrypt_init()\fR or \fBOSSL_FUNC_cipher_decrypt_init()\fR.
This should call the raw underlying cipher function without any padding.
This will be invoked in the provider as a result of the application calling
\&\fBEVP_Cipher\fR\|(3).
The application is responsible for ensuring that the input is a multiple of the
block length.
The data to be encrypted/decrypted will be in \fIin\fR, and it will be \fIinl\fR bytes
in length.
The output from the encryption/decryption should be stored in \fIout\fR and the
amount of data stored should be put in \fI*outl\fR which should be no more than
\&\fIoutsize\fR bytes.
.SS "Cipher Parameters"
.IX Subsection "Cipher Parameters"
See \fBOSSL_PARAM\fR\|(3) for further details on the parameters structure used by
these functions.
.PP
\&\fBOSSL_FUNC_cipher_get_params()\fR gets details of the algorithm implementation
and stores them in \fIparams\fR.
.PP
\&\fBOSSL_FUNC_cipher_set_ctx_params()\fR sets cipher operation parameters for the
provider side cipher context \fIcctx\fR to \fIparams\fR.
Any parameter settings are additional to any that were previously set.
Passing NULL for \fIparams\fR should return true.
.PP
\&\fBOSSL_FUNC_cipher_get_ctx_params()\fR gets cipher operation details details from
the given provider side cipher context \fIcctx\fR and stores them in \fIparams\fR.
Passing NULL for \fIparams\fR should return true.
.PP
\&\fBOSSL_FUNC_cipher_gettable_params()\fR, \fBOSSL_FUNC_cipher_gettable_ctx_params()\fR,
and \fBOSSL_FUNC_cipher_settable_ctx_params()\fR all return constant \fBOSSL_PARAM\fR\|(3)
arrays as descriptors of the parameters that \fBOSSL_FUNC_cipher_get_params()\fR,
\&\fBOSSL_FUNC_cipher_get_ctx_params()\fR, and \fBOSSL_FUNC_cipher_set_ctx_params()\fR
can handle, respectively.  \fBOSSL_FUNC_cipher_gettable_ctx_params()\fR and
\&\fBOSSL_FUNC_cipher_settable_ctx_params()\fR will return the parameters associated
with the provider side context \fIcctx\fR in its current state if it is
not NULL.  Otherwise, they return the parameters associated with the
provider side algorithm \fIprovctx\fR.
.PP
Parameters currently recognised by built-in ciphers are listed in
"PARAMETERS" in \fBEVP_EncryptInit\fR\|(3).
Not all parameters are relevant to, or are understood by all ciphers.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBOSSL_FUNC_cipher_newctx()\fR and \fBOSSL_FUNC_cipher_dupctx()\fR should return the newly created
provider side cipher context, or NULL on failure.
.PP
\&\fBOSSL_FUNC_cipher_encrypt_init()\fR, \fBOSSL_FUNC_cipher_decrypt_init()\fR, \fBOSSL_FUNC_cipher_update()\fR,
\&\fBOSSL_FUNC_cipher_final()\fR, \fBOSSL_FUNC_cipher_cipher()\fR, \fBOSSL_FUNC_cipher_get_params()\fR,
\&\fBOSSL_FUNC_cipher_get_ctx_params()\fR and \fBOSSL_FUNC_cipher_set_ctx_params()\fR should return 1 for
success or 0 on error.
.PP
\&\fBOSSL_FUNC_cipher_gettable_params()\fR, \fBOSSL_FUNC_cipher_gettable_ctx_params()\fR and
\&\fBOSSL_FUNC_cipher_settable_ctx_params()\fR should return a constant \fBOSSL_PARAM\fR\|(3)
array, or NULL if none is offered.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBprovider\fR\|(7), \fBOSSL_PROVIDER\-FIPS\fR\|(7), \fBOSSL_PROVIDER\-default\fR\|(7),
\&\fBOSSL_PROVIDER\-legacy\fR\|(7),
\&\fBEVP_CIPHER\-AES\fR\|(7), \fBEVP_CIPHER\-ARIA\fR\|(7), \fBEVP_CIPHER\-BLOWFISH\fR\|(7),
\&\fBEVP_CIPHER\-CAMELLIA\fR\|(7), \fBEVP_CIPHER\-CAST\fR\|(7), \fBEVP_CIPHER\-CHACHA\fR\|(7),
\&\fBEVP_CIPHER\-DES\fR\|(7), \fBEVP_CIPHER\-IDEA\fR\|(7), \fBEVP_CIPHER\-RC2\fR\|(7),
\&\fBEVP_CIPHER\-RC4\fR\|(7), \fBEVP_CIPHER\-RC5\fR\|(7), \fBEVP_CIPHER\-SEED\fR\|(7),
\&\fBEVP_CIPHER\-SM4\fR\|(7), \fBEVP_CIPHER\-NULL\fR\|(7),
\&\fBlife_cycle\-cipher\fR\|(7), \fBEVP_EncryptInit\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
The provider CIPHER interface was introduced in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2019\-2023 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
