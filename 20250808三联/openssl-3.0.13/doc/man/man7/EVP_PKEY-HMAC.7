.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "EVP_PKEY-HMAC 7ossl"
.TH EVP_PKEY-HMAC 7ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
EVP_PKEY\-HMAC, EVP_KEYMGMT\-HMAC, EVP_PKEY\-Siphash, EVP_KEYMGMT\-Siphash,
EVP_PKEY\-Poly1305, EVP_KEYMGMT\-Poly1305, EVP_PKEY\-CMAC, EVP_KEYMGMT\-CMAC
\&\- EVP_PKEY legacy MAC keytypes and algorithm support
.SH DESCRIPTION
.IX Header "DESCRIPTION"
The \fBHMAC\fR and \fBCMAC\fR key types are implemented in OpenSSL's default and FIPS
providers. Additionally the \fBSiphash\fR and \fBPoly1305\fR key types are implemented
in the default provider. Performing MAC operations via an EVP_PKEY
is considered legacy and are only available for backwards compatibility purposes
and for a restricted set of algorithms. The preferred way of performing MAC
operations is via the EVP_MAC APIs. See \fBEVP_MAC_init\fR\|(3).
.PP
For further details on using EVP_PKEY based MAC keys see
\&\fBEVP_SIGNATURE\-HMAC\fR\|(7), \fBEVP_SIGNATURE\-Siphash\fR\|(7),
\&\fBEVP_SIGNATURE\-Poly1305\fR\|(7) or \fBEVP_SIGNATURE\-CMAC\fR\|(7).
.SS "Common MAC parameters"
.IX Subsection "Common MAC parameters"
All the \fBMAC\fR keytypes support the following parameters.
.IP """priv"" (\fBOSSL_PKEY_PARAM_PRIV_KEY\fR) <octet string>" 4
.IX Item """priv"" (OSSL_PKEY_PARAM_PRIV_KEY) <octet string>"
The MAC key value.
.IP """properties"" (\fBOSSL_PKEY_PARAM_PROPERTIES\fR) <UTF8 string>" 4
.IX Item """properties"" (OSSL_PKEY_PARAM_PROPERTIES) <UTF8 string>"
A property query string to be used when any algorithms are fetched.
.SS "CMAC parameters"
.IX Subsection "CMAC parameters"
As well as the parameters described above, the \fBCMAC\fR keytype additionally
supports the following parameters.
.IP """cipher"" (\fBOSSL_PKEY_PARAM_CIPHER\fR) <UTF8 string>" 4
.IX Item """cipher"" (OSSL_PKEY_PARAM_CIPHER) <UTF8 string>"
The name of a cipher to be used when generating the MAC.
.IP """engine"" (\fBOSSL_PKEY_PARAM_ENGINE\fR) <UTF8 string>" 4
.IX Item """engine"" (OSSL_PKEY_PARAM_ENGINE) <UTF8 string>"
The name of an engine to be used for the specified cipher (if any).
.SS "Common MAC key generation parameters"
.IX Subsection "Common MAC key generation parameters"
MAC key generation is unusual in that no new key is actually generated. Instead
a new provider side key object is created with the supplied raw key value. This
is done for backwards compatibility with previous versions of OpenSSL.
.IP """priv"" (\fBOSSL_PKEY_PARAM_PRIV_KEY\fR) <octet string>" 4
.IX Item """priv"" (OSSL_PKEY_PARAM_PRIV_KEY) <octet string>"
The MAC key value.
.SS "CMAC key generation parameters"
.IX Subsection "CMAC key generation parameters"
In addition to the common MAC key generation parameters, the CMAC key generation
additionally recognises the following.
.IP """cipher"" (\fBOSSL_PKEY_PARAM_CIPHER\fR) <UTF8 string>" 4
.IX Item """cipher"" (OSSL_PKEY_PARAM_CIPHER) <UTF8 string>"
The name of a cipher to be used when generating the MAC.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBEVP_KEYMGMT\fR\|(3), \fBEVP_PKEY\fR\|(3), \fBprovider\-keymgmt\fR\|(7)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2020 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
