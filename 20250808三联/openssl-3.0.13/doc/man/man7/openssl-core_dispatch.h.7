.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "OPENSSL-CORE_DISPATCH.H 7ossl"
.TH OPENSSL-CORE_DISPATCH.H 7ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
openssl/core_dispatch.h
\&\- OpenSSL provider dispatch numbers and function types
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/core_dispatch.h>
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
The \fI<openssl/core_dispatch.h>\fR header defines all the operation
numbers, dispatch numbers and provider interface function types
currently available.
.PP
The operation and dispatch numbers are represented with macros, which
are named as follows:
.IP "operation numbers" 4
.IX Item "operation numbers"
These macros have the form \f(CW\*(C`OSSL_OP_\fR\f(CIopname\fR\f(CW\*(C'\fR.
.IP "dipatch numbers" 4
.IX Item "dipatch numbers"
These macros have the form \f(CW\*(C`OSSL_FUNC_\fR\f(CIopname\fR\f(CW_\fR\f(CIfuncname\fR\f(CW\*(C'\fR, where
\&\f(CW\*(C`\fR\f(CIopname\fR\f(CW\*(C'\fR is the same as in the macro for the operation this
function belongs to.
.PP
With every dispatch number, there is an associated function type.
.PP
For further information, please see the \fBprovider\fR\|(7)
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBprovider\fR\|(7)
.SH HISTORY
.IX Header "HISTORY"
The types and macros described here were added in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2020 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
