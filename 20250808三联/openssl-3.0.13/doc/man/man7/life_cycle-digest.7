.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "LIFE_CYCLE-DIGEST 7ossl"
.TH LIFE_CYCLE-DIGEST 7ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
life_cycle\-digest \- The digest algorithm life\-cycle
.SH DESCRIPTION
.IX Header "DESCRIPTION"
All message digests (MDs) go through a number of stages in their life-cycle:
.IP start 4
.IX Item "start"
This state represents the MD before it has been allocated.  It is the
starting state for any life-cycle transitions.
.IP newed 4
.IX Item "newed"
This state represents the MD after it has been allocated.
.IP initialised 4
.IX Item "initialised"
This state represents the MD when it is set up and capable of processing
input.
.IP updated 4
.IX Item "updated"
This state represents the MD when it is set up and capable of processing
additional input or generating output.
.IP finaled 4
.IX Item "finaled"
This state represents the MD when it has generated output.
.IP freed 4
.IX Item "freed"
This state is entered when the MD is freed.  It is the terminal state
for all life-cycle transitions.
.SS "State Transition Diagram"
.IX Subsection "State Transition Diagram"
The usual life-cycle of a MD is illustrated:
                     +-------------------+
                     |       start       |
                     +-------------------+
                       |
                       | EVP_MD_CTX_new
                       v
                     +-------------------+         EVP_MD_CTX_reset
                     |       newed       | <------------------------------+
                     +-------------------+                                |
                       |                                                  |
                       | EVP_DigestInit                                   |
                       v                                                  |
                     +-------------------+                                |
                +--> |    initialised    | <+ EVP_DigestInit              |
                |    +-------------------+  |                             |
                |      |                    |      EVP_DigestUpdate       |
                |      | EVP_DigestUpdate   |    +------------------+     |
                |      v                    |    v                  |     |
                |    +------------------------------------------------+   |
 EVP_DigestInit |    |                    updated                     | --+
                |    +------------------------------------------------+   |
                |      |                    |                             |
                |      | EVP_DigestFinal    | EVP_DigestFinalXOF          |
                |      v                    v                             |
                |    +------------------------------------------------+   |
                +--- |                    finaled                     | --+
                     +------------------------------------------------+
                       |
                       | EVP_MD_CTX_free
                       v
                     +-------------------+
                     |       freed       |
                     +-------------------+
.SS "Formal State Transitions"
.IX Subsection "Formal State Transitions"
This section defines all of the legal state transitions.
This is the canonical list.
 Function Call                --------------------- Current State ----------------------
                              start   newed    initialised   updated     finaled   freed
 EVP_MD_CTX_new               newed
 EVP_DigestInit                    initialised initialised initialised initialised
 EVP_DigestUpdate                                updated     updated
 EVP_DigestFinal                                             finaled
 EVP_DigestFinalXOF                                          finaled
 EVP_MD_CTX_free              freed   freed       freed       freed       freed
 EVP_MD_CTX_reset                     newed       newed       newed       newed
 EVP_MD_CTX_get_params                newed    initialised   updated
 EVP_MD_CTX_set_params                newed    initialised   updated
 EVP_MD_CTX_gettable_params           newed    initialised   updated
 EVP_MD_CTX_settable_params           newed    initialised   updated
.SH NOTES
.IX Header "NOTES"
At some point the EVP layer will begin enforcing the transitions described
herein.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBprovider\-digest\fR\|(7), \fBEVP_DigestInit\fR\|(3)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
