.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "OSSL_PROVIDER-LEGACY 7ossl"
.TH OSSL_PROVIDER-LEGACY 7ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
OSSL_PROVIDER\-legacy \- OpenSSL legacy provider
.SH DESCRIPTION
.IX Header "DESCRIPTION"
The OpenSSL legacy provider supplies OpenSSL implementations of algorithms
that have been deemed legacy.  Such algorithms have commonly fallen out of
use, have been deemed insecure by the cryptography community, or something
similar.
.PP
We can consider this the retirement home of cryptographic algorithms.
.SS Properties
.IX Subsection "Properties"
The implementations in this provider specifically has this property
defined:
.IP """provider=legacy""" 4
.IX Item """provider=legacy"""
.PP
It may be used in a property query string with fetching functions such as
\&\fBEVP_MD_fetch\fR\|(3) or \fBEVP_CIPHER_fetch\fR\|(3), as well as with other
functions that take a property query string, such as
\&\fBEVP_PKEY_CTX_new_from_name\fR\|(3).
.PP
It isn't mandatory to query for any of these properties, except to
make sure to get implementations of this provider and none other.
.SH "OPERATIONS AND ALGORITHMS"
.IX Header "OPERATIONS AND ALGORITHMS"
The OpenSSL legacy provider supports these operations and algorithms:
.SS "Hashing Algorithms / Message Digests"
.IX Subsection "Hashing Algorithms / Message Digests"
.IP "MD2, see \fBEVP_MD\-MD2\fR\|(7)" 4
.IX Item "MD2, see EVP_MD-MD2"
.PD 0
.IP "MD4, see \fBEVP_MD\-MD4\fR\|(7)" 4
.IX Item "MD4, see EVP_MD-MD4"
.IP "MDC2, see \fBEVP_MD\-MDC2\fR\|(7)" 4
.IX Item "MDC2, see EVP_MD-MDC2"
.IP "WHIRLPOOL, see \fBEVP_MD\-WHIRLPOOL\fR\|(7)" 4
.IX Item "WHIRLPOOL, see EVP_MD-WHIRLPOOL"
.IP "RIPEMD160, see \fBEVP_MD\-RIPEMD160\fR\|(7)" 4
.IX Item "RIPEMD160, see EVP_MD-RIPEMD160"
.PD
.SS "Symmetric Ciphers"
.IX Subsection "Symmetric Ciphers"
Not all of these symmetric cipher algorithms are enabled by default.
.IP "Blowfish, see \fBEVP_CIPHER\-BLOWFISH\fR\|(7)" 4
.IX Item "Blowfish, see EVP_CIPHER-BLOWFISH"
.PD 0
.IP "CAST, see \fBEVP_CIPHER\-CAST\fR\|(7)" 4
.IX Item "CAST, see EVP_CIPHER-CAST"
.IP "DES, see \fBEVP_CIPHER\-DES\fR\|(7)" 4
.IX Item "DES, see EVP_CIPHER-DES"
.PD
The algorithm names are: DES_ECB, DES_CBC, DES_OFB, DES_CFB, DES_CFB1, DES_CFB8
and DESX_CBC.
.IP "IDEA, see \fBEVP_CIPHER\-IDEA\fR\|(7)" 4
.IX Item "IDEA, see EVP_CIPHER-IDEA"
.PD 0
.IP "RC2, see \fBEVP_CIPHER\-RC2\fR\|(7)" 4
.IX Item "RC2, see EVP_CIPHER-RC2"
.IP "RC4, see \fBEVP_CIPHER\-RC4\fR\|(7)" 4
.IX Item "RC4, see EVP_CIPHER-RC4"
.IP "RC5, see \fBEVP_CIPHER\-RC5\fR\|(7)" 4
.IX Item "RC5, see EVP_CIPHER-RC5"
.PD
Disabled by default. Use \fIenable\-rc5\fR config option to enable.
.IP "SEED, see \fBEVP_CIPHER\-SEED\fR\|(7)" 4
.IX Item "SEED, see EVP_CIPHER-SEED"
.SS "Key Derivation Function (KDF)"
.IX Subsection "Key Derivation Function (KDF)"
.PD 0
.IP PBKDF1 4
.IX Item "PBKDF1"
.PD
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBOSSL_PARAM\fR\|(3),
\&\fBopenssl\-core.h\fR\|(7),
\&\fBopenssl\-core_dispatch.h\fR\|(7),
\&\fBprovider\fR\|(7)
.SH HISTORY
.IX Header "HISTORY"
This functionality was added in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2020\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
