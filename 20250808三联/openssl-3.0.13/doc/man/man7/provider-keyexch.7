.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "PROVIDER-KEYEXCH 7ossl"
.TH PROVIDER-KEYEXCH 7ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
provider\-keyexch \- The keyexch library <\-> provider functions
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 2
\& #include <openssl/core_dispatch.h>
\& #include <openssl/core_names.h>
\&
\& /*
\&  * None of these are actual functions, but are displayed like this for
\&  * the function signatures for functions that are offered as function
\&  * pointers in OSSL_DISPATCH arrays.
\&  */
\&
\& /* Context management */
\& void *OSSL_FUNC_keyexch_newctx(void *provctx);
\& void OSSL_FUNC_keyexch_freectx(void *ctx);
\& void *OSSL_FUNC_keyexch_dupctx(void *ctx);
\&
\& /* Shared secret derivation */
\& int OSSL_FUNC_keyexch_init(void *ctx, void *provkey,
\&                            const OSSL_PARAM params[]);
\& int OSSL_FUNC_keyexch_set_peer(void *ctx, void *provkey);
\& int OSSL_FUNC_keyexch_derive(void *ctx, unsigned char *secret, size_t *secretlen,
\&                              size_t outlen);
\&
\& /* Key Exchange parameters */
\& int OSSL_FUNC_keyexch_set_ctx_params(void *ctx, const OSSL_PARAM params[]);
\& const OSSL_PARAM *OSSL_FUNC_keyexch_settable_ctx_params(void *ctx,
\&                                                         void *provctx);
\& int OSSL_FUNC_keyexch_get_ctx_params(void *ctx, OSSL_PARAM params[]);
\& const OSSL_PARAM *OSSL_FUNC_keyexch_gettable_ctx_params(void *ctx,
\&                                                         void *provctx);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
This documentation is primarily aimed at provider authors. See \fBprovider\fR\|(7)
for further information.
.PP
The key exchange (OSSL_OP_KEYEXCH) operation enables providers to implement key
exchange algorithms and make them available to applications via
\&\fBEVP_PKEY_derive\fR\|(3) and
other related functions).
.PP
All "functions" mentioned here are passed as function pointers between
\&\fIlibcrypto\fR and the provider in \fBOSSL_DISPATCH\fR\|(3) arrays via
\&\fBOSSL_ALGORITHM\fR\|(3) arrays that are returned by the provider's
\&\fBprovider_query_operation()\fR function
(see "Provider Functions" in \fBprovider\-base\fR\|(7)).
.PP
All these "functions" have a corresponding function type definition
named \fBOSSL_FUNC_{name}_fn\fR, and a helper function to retrieve the
function pointer from an \fBOSSL_DISPATCH\fR\|(3) element named
\&\fBOSSL_FUNC_{name}\fR.
For example, the "function" \fBOSSL_FUNC_keyexch_newctx()\fR has these:
.PP
.Vb 3
\& typedef void *(OSSL_FUNC_keyexch_newctx_fn)(void *provctx);
\& static ossl_inline OSSL_FUNC_keyexch_newctx_fn
\&     OSSL_FUNC_keyexch_newctx(const OSSL_DISPATCH *opf);
.Ve
.PP
\&\fBOSSL_DISPATCH\fR\|(3) arrays are indexed by numbers that are provided as
macros in \fBopenssl\-core_dispatch.h\fR\|(7), as follows:
.PP
.Vb 3
\& OSSL_FUNC_keyexch_newctx                OSSL_FUNC_KEYEXCH_NEWCTX
\& OSSL_FUNC_keyexch_freectx               OSSL_FUNC_KEYEXCH_FREECTX
\& OSSL_FUNC_keyexch_dupctx                OSSL_FUNC_KEYEXCH_DUPCTX
\&
\& OSSL_FUNC_keyexch_init                  OSSL_FUNC_KEYEXCH_INIT
\& OSSL_FUNC_keyexch_set_peer              OSSL_FUNC_KEYEXCH_SET_PEER
\& OSSL_FUNC_keyexch_derive                OSSL_FUNC_KEYEXCH_DERIVE
\&
\& OSSL_FUNC_keyexch_set_ctx_params        OSSL_FUNC_KEYEXCH_SET_CTX_PARAMS
\& OSSL_FUNC_keyexch_settable_ctx_params   OSSL_FUNC_KEYEXCH_SETTABLE_CTX_PARAMS
\& OSSL_FUNC_keyexch_get_ctx_params        OSSL_FUNC_KEYEXCH_GET_CTX_PARAMS
\& OSSL_FUNC_keyexch_gettable_ctx_params   OSSL_FUNC_KEYEXCH_GETTABLE_CTX_PARAMS
.Ve
.PP
A key exchange algorithm implementation may not implement all of these functions.
In order to be a consistent set of functions a provider must implement
OSSL_FUNC_keyexch_newctx, OSSL_FUNC_keyexch_freectx, OSSL_FUNC_keyexch_init and OSSL_FUNC_keyexch_derive.
All other functions are optional.
.PP
A key exchange algorithm must also implement some mechanism for generating,
loading or importing keys via the key management (OSSL_OP_KEYMGMT) operation.
See \fBprovider\-keymgmt\fR\|(7) for further details.
.SS "Context Management Functions"
.IX Subsection "Context Management Functions"
\&\fBOSSL_FUNC_keyexch_newctx()\fR should create and return a pointer to a provider side
structure for holding context information during a key exchange operation.
A pointer to this context will be passed back in a number of the other key
exchange operation function calls.
The parameter \fIprovctx\fR is the provider context generated during provider
initialisation (see \fBprovider\fR\|(7)).
.PP
\&\fBOSSL_FUNC_keyexch_freectx()\fR is passed a pointer to the provider side key exchange
context in the \fIctx\fR parameter.
This function should free any resources associated with that context.
.PP
\&\fBOSSL_FUNC_keyexch_dupctx()\fR should duplicate the provider side key exchange context in
the \fIctx\fR parameter and return the duplicate copy.
.SS "Shared Secret Derivation Functions"
.IX Subsection "Shared Secret Derivation Functions"
\&\fBOSSL_FUNC_keyexch_init()\fR initialises a key exchange operation given a provider side key
exchange context in the \fIctx\fR parameter, and a pointer to a provider key object
in the \fIprovkey\fR parameter.
The \fIparams\fR, if not NULL, should be set on the context in a manner similar to
using \fBOSSL_FUNC_keyexch_set_params()\fR.
The key object should have been previously
generated, loaded or imported into the provider using the key management
(OSSL_OP_KEYMGMT) operation (see \fBprovider\-keymgmt\fR\|(7)>.
.PP
\&\fBOSSL_FUNC_keyexch_set_peer()\fR is called to supply the peer's public key (in the
\&\fIprovkey\fR parameter) to be used when deriving the shared secret.
It is also passed a previously initialised key exchange context in the \fIctx\fR
parameter.
The key object should have been previously generated, loaded or imported into
the provider using the key management (OSSL_OP_KEYMGMT) operation (see
\&\fBprovider\-keymgmt\fR\|(7)>.
.PP
\&\fBOSSL_FUNC_keyexch_derive()\fR performs the actual key exchange itself by deriving a shared
secret.
A previously initialised key exchange context is passed in the \fIctx\fR
parameter.
The derived secret should be written to the location \fIsecret\fR which should not
exceed \fIoutlen\fR bytes.
The length of the shared secret should be written to \fI*secretlen\fR.
If \fIsecret\fR is NULL then the maximum length of the shared secret should be
written to \fI*secretlen\fR.
.SS "Key Exchange Parameters Functions"
.IX Subsection "Key Exchange Parameters Functions"
\&\fBOSSL_FUNC_keyexch_set_ctx_params()\fR sets key exchange parameters associated with the
given provider side key exchange context \fIctx\fR to \fIparams\fR,
see "Common Key Exchange parameters".
Any parameter settings are additional to any that were previously set.
Passing NULL for \fIparams\fR should return true.
.PP
\&\fBOSSL_FUNC_keyexch_get_ctx_params()\fR gets key exchange parameters associated with the
given provider side key exchange context \fIctx\fR into \fIparams\fR,
see "Common Key Exchange parameters".
Passing NULL for \fIparams\fR should return true.
.PP
\&\fBOSSL_FUNC_keyexch_settable_ctx_params()\fR yields a constant \fBOSSL_PARAM\fR\|(3) array that
describes the settable parameters, i.e. parameters that can be used with
\&\fBOP_signature_set_ctx_params()\fR.
If \fBOSSL_FUNC_keyexch_settable_ctx_params()\fR is present, \fBOSSL_FUNC_keyexch_set_ctx_params()\fR must
also be present, and vice versa.
Similarly, \fBOSSL_FUNC_keyexch_gettable_ctx_params()\fR yields a constant \fBOSSL_PARAM\fR\|(3)
array that describes the gettable parameters, i.e. parameters that can be
handled by \fBOP_signature_get_ctx_params()\fR.
If \fBOSSL_FUNC_keyexch_gettable_ctx_params()\fR is present, \fBOSSL_FUNC_keyexch_get_ctx_params()\fR must
also be present, and vice versa.
.PP
Notice that not all settable parameters are also gettable, and vice versa.
.SS "Common Key Exchange parameters"
.IX Subsection "Common Key Exchange parameters"
See \fBOSSL_PARAM\fR\|(3) for further details on the parameters structure used by
the \fBOSSL_FUNC_keyexch_set_ctx_params()\fR and \fBOSSL_FUNC_keyexch_get_ctx_params()\fR functions.
.PP
Common parameters currently recognised by built-in key exchange algorithms are
as follows.
.IP """kdf-type"" (\fBOSSL_EXCHANGE_PARAM_KDF_TYPE\fR) <UTF8 string>" 4
.IX Item """kdf-type"" (OSSL_EXCHANGE_PARAM_KDF_TYPE) <UTF8 string>"
Sets or gets the Key Derivation Function type to apply within the associated key
exchange ctx.
.IP """kdf-digest"" (\fBOSSL_EXCHANGE_PARAM_KDF_DIGEST\fR) <UTF8 string>" 4
.IX Item """kdf-digest"" (OSSL_EXCHANGE_PARAM_KDF_DIGEST) <UTF8 string>"
Sets or gets the Digest algorithm to be used as part of the Key Derivation Function
associated with the given key exchange ctx.
.IP """kdf-digest-props"" (\fBOSSL_EXCHANGE_PARAM_KDF_DIGEST_PROPS\fR) <UTF8 string>" 4
.IX Item """kdf-digest-props"" (OSSL_EXCHANGE_PARAM_KDF_DIGEST_PROPS) <UTF8 string>"
Sets properties to be used upon look up of the implementation for the selected
Digest algorithm for the Key Derivation Function associated with the given key
exchange ctx.
.IP """kdf-outlen"" (\fBOSSL_EXCHANGE_PARAM_KDF_OUTLEN\fR) <unsigned integer>" 4
.IX Item """kdf-outlen"" (OSSL_EXCHANGE_PARAM_KDF_OUTLEN) <unsigned integer>"
Sets or gets the desired size for the output of the chosen Key Derivation Function
associated with the given key exchange ctx.
The length of the "kdf-outlen" parameter should not exceed that of a \fBsize_t\fR.
.IP """kdf-ukm"" (\fBOSSL_EXCHANGE_PARAM_KDF_UKM\fR) <octet string>" 4
.IX Item """kdf-ukm"" (OSSL_EXCHANGE_PARAM_KDF_UKM) <octet string>"
Sets the User Key Material to be used as part of the selected Key Derivation
Function associated with the given key exchange ctx.
.IP """kdf-ukm"" (\fBOSSL_EXCHANGE_PARAM_KDF_UKM\fR) <octet string ptr>" 4
.IX Item """kdf-ukm"" (OSSL_EXCHANGE_PARAM_KDF_UKM) <octet string ptr>"
Gets a pointer to the User Key Material to be used as part of the selected
Key Derivation Function associated with the given key exchange ctx. Providers
usually do not need to support this gettable parameter as its sole purpose
is to support functionality of the deprecated \fBEVP_PKEY_CTX_get0_ecdh_kdf_ukm()\fR
and \fBEVP_PKEY_CTX_get0_dh_kdf_ukm()\fR functions.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBOSSL_FUNC_keyexch_newctx()\fR and \fBOSSL_FUNC_keyexch_dupctx()\fR should return the newly created
provider side key exchange context, or NULL on failure.
.PP
\&\fBOSSL_FUNC_keyexch_init()\fR, \fBOSSL_FUNC_keyexch_set_peer()\fR, \fBOSSL_FUNC_keyexch_derive()\fR,
\&\fBOSSL_FUNC_keyexch_set_params()\fR, and \fBOSSL_FUNC_keyexch_get_params()\fR should return 1 for success
or 0 on error.
.PP
\&\fBOSSL_FUNC_keyexch_settable_ctx_params()\fR and \fBOSSL_FUNC_keyexch_gettable_ctx_params()\fR should
always return a constant \fBOSSL_PARAM\fR\|(3) array.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBprovider\fR\|(7)
.SH HISTORY
.IX Header "HISTORY"
The provider KEYEXCH interface was introduced in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2019\-2022 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
