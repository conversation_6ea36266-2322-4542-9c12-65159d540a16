.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "BIO 7ossl"
.TH BIO 7ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
bio \- Basic I/O abstraction
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/bio.h>
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
A BIO is an I/O abstraction, it hides many of the underlying I/O
details from an application. If an application uses a BIO for its
I/O it can transparently handle SSL connections, unencrypted network
connections and file I/O.
.PP
There are two types of BIO, a source/sink BIO and a filter BIO.
.PP
As its name implies a source/sink BIO is a source and/or sink of data,
examples include a socket BIO and a file BIO.
.PP
A filter BIO takes data from one BIO and passes it through to
another, or the application. The data may be left unmodified (for
example a message digest BIO) or translated (for example an
encryption BIO). The effect of a filter BIO may change according
to the I/O operation it is performing: for example an encryption
BIO will encrypt data if it is being written to and decrypt data
if it is being read from.
.PP
BIOs can be joined together to form a chain (a single BIO is a chain
with one component). A chain normally consists of one source/sink
BIO and one or more filter BIOs. Data read from or written to the
first BIO then traverses the chain to the end (normally a source/sink
BIO).
.PP
Some BIOs (such as memory BIOs) can be used immediately after calling
\&\fBBIO_new()\fR. Others (such as file BIOs) need some additional initialization,
and frequently a utility function exists to create and initialize such BIOs.
.PP
If \fBBIO_free()\fR is called on a BIO chain it will only free one BIO resulting
in a memory leak.
.PP
Calling \fBBIO_free_all()\fR on a single BIO has the same effect as calling
\&\fBBIO_free()\fR on it other than the discarded return value.
.PP
Normally the \fItype\fR argument is supplied by a function which returns a
pointer to a BIO_METHOD. There is a naming convention for such functions:
a source/sink BIO typically starts with \fIBIO_s_\fR and
a filter BIO with \fIBIO_f_\fR.
.SH EXAMPLES
.IX Header "EXAMPLES"
Create a memory BIO:
.PP
.Vb 1
\& BIO *mem = BIO_new(BIO_s_mem());
.Ve
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBBIO_ctrl\fR\|(3),
\&\fBBIO_f_base64\fR\|(3), \fBBIO_f_buffer\fR\|(3),
\&\fBBIO_f_cipher\fR\|(3), \fBBIO_f_md\fR\|(3),
\&\fBBIO_f_null\fR\|(3), \fBBIO_f_ssl\fR\|(3),
\&\fBBIO_f_readbuffer\fR\|(3),
\&\fBBIO_find_type\fR\|(3), \fBBIO_new\fR\|(3),
\&\fBBIO_new_bio_pair\fR\|(3),
\&\fBBIO_push\fR\|(3), \fBBIO_read_ex\fR\|(3),
\&\fBBIO_s_accept\fR\|(3), \fBBIO_s_bio\fR\|(3),
\&\fBBIO_s_connect\fR\|(3), \fBBIO_s_fd\fR\|(3),
\&\fBBIO_s_file\fR\|(3), \fBBIO_s_mem\fR\|(3),
\&\fBBIO_s_null\fR\|(3), \fBBIO_s_socket\fR\|(3),
\&\fBBIO_set_callback\fR\|(3),
\&\fBBIO_should_retry\fR\|(3)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
