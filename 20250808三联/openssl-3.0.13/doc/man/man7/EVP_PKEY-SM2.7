.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "EVP_PKEY-SM2 7ossl"
.TH EVP_PKEY-SM2 7ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
EVP_PKEY\-SM2, EVP_KEYMGMT\-SM2, SM2
\&\- EVP_PKEY keytype support for the Chinese SM2 signature and encryption algorithms
.SH DESCRIPTION
.IX Header "DESCRIPTION"
The \fBSM2\fR algorithm was first defined by the Chinese national standard GM/T
0003\-2012 and was later standardized by ISO as ISO/IEC 14888. \fBSM2\fR is actually
an elliptic curve based algorithm. The current implementation in OpenSSL supports
both signature and encryption schemes via the EVP interface.
.PP
When doing the \fBSM2\fR signature algorithm, it requires a distinguishing identifier
to form the message prefix which is hashed before the real message is hashed.
.SS "Common SM2 parameters"
.IX Subsection "Common SM2 parameters"
SM2 uses the parameters defined in "Common EC parameters" in \fBEVP_PKEY\-EC\fR\|(7).
The following parameters are different:
.IP """cofactor"" (\fBOSSL_PKEY_PARAM_EC_COFACTOR\fR) <unsigned integer>" 4
.IX Item """cofactor"" (OSSL_PKEY_PARAM_EC_COFACTOR) <unsigned integer>"
This parameter is ignored for \fBSM2\fR.
.IP "(\fBOSSL_PKEY_PARAM_DEFAULT_DIGEST\fR) <UTF8 string>" 4
.IX Item "(OSSL_PKEY_PARAM_DEFAULT_DIGEST) <UTF8 string>"
Getter that returns the default digest name.
(Currently returns "SM3" as of OpenSSL 3.0).
.SH NOTES
.IX Header "NOTES"
\&\fBSM2\fR signatures can be generated by using the 'DigestSign' series of APIs, for
instance, \fBEVP_DigestSignInit()\fR, \fBEVP_DigestSignUpdate()\fR and \fBEVP_DigestSignFinal()\fR.
Ditto for the verification process by calling the 'DigestVerify' series of APIs.
.PP
Before computing an \fBSM2\fR signature, an \fBEVP_PKEY_CTX\fR needs to be created,
and an \fBSM2\fR ID must be set for it, like this:
.PP
.Vb 1
\& EVP_PKEY_CTX_set1_id(pctx, id, id_len);
.Ve
.PP
Before calling the \fBEVP_DigestSignInit()\fR or \fBEVP_DigestVerifyInit()\fR functions,
that \fBEVP_PKEY_CTX\fR should be assigned to the \fBEVP_MD_CTX\fR, like this:
.PP
.Vb 1
\& EVP_MD_CTX_set_pkey_ctx(mctx, pctx);
.Ve
.PP
There is normally no need to pass a \fBpctx\fR parameter to \fBEVP_DigestSignInit()\fR
or \fBEVP_DigestVerifyInit()\fR in such a scenario.
.PP
SM2 can be tested with the \fBopenssl\-speed\fR\|(1) application since version 3.0.
Currently, the only valid algorithm name is \fBsm2\fR.
.PP
Since version 3.0, SM2 keys can be generated and loaded only when the domain
parameters specify the SM2 elliptic curve.
.SH EXAMPLES
.IX Header "EXAMPLES"
This example demonstrates the calling sequence for using an \fBEVP_PKEY\fR to verify
a message with the SM2 signature algorithm and the SM3 hash algorithm:
.PP
.Vb 1
\& #include <openssl/evp.h>
\&
\& /* obtain an EVP_PKEY using whatever methods... */
\& mctx = EVP_MD_CTX_new();
\& pctx = EVP_PKEY_CTX_new(pkey, NULL);
\& EVP_PKEY_CTX_set1_id(pctx, id, id_len);
\& EVP_MD_CTX_set_pkey_ctx(mctx, pctx);
\& EVP_DigestVerifyInit(mctx, NULL, EVP_sm3(), NULL, pkey);
\& EVP_DigestVerifyUpdate(mctx, msg, msg_len);
\& EVP_DigestVerifyFinal(mctx, sig, sig_len)
.Ve
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBEVP_PKEY_CTX_new\fR\|(3),
\&\fBEVP_DigestSignInit\fR\|(3),
\&\fBEVP_DigestVerifyInit\fR\|(3),
\&\fBEVP_PKEY_CTX_set1_id\fR\|(3),
\&\fBEVP_MD_CTX_set_pkey_ctx\fR\|(3)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2018\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
