.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "EVP_MD-SHA1 7ossl"
.TH EVP_MD-SHA1 7ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
EVP_MD\-SHA1 \- The SHA1 EVP_MD implementation
.SH DESCRIPTION
.IX Header "DESCRIPTION"
Support for computing SHA1 digests through the \fBEVP_MD\fR API.
.SS Identities
.IX Subsection "Identities"
This implementation is available with the FIPS provider as well as the
default provider, and is identified with the names "SHA1" and "SHA\-1".
.SS "Gettable Parameters"
.IX Subsection "Gettable Parameters"
This implementation supports the common gettable parameters described
in \fBEVP_MD\-common\fR\|(7).
.SS "Settable Context Parameters"
.IX Subsection "Settable Context Parameters"
This implementation supports the following \fBOSSL_PARAM\fR\|(3) entries,
settable for an \fBEVP_MD_CTX\fR with \fBEVP_MD_CTX_set_params\fR\|(3):
.IP """ssl3\-ms"" (\fBOSSL_DIGEST_PARAM_SSL3_MS\fR) <octet string>" 4
.IX Item """ssl3-ms"" (OSSL_DIGEST_PARAM_SSL3_MS) <octet string>"
This parameter is set by libssl in order to calculate a signature hash for an
SSLv3 CertificateVerify message as per RFC6101.
It is only set after all handshake messages have already been digested via
\&\fBOP_digest_update()\fR calls.
The parameter provides the master secret value to be added to the digest.
The digest implementation should calculate the complete digest as per RFC6101
section 5.6.8.
The next call after setting this parameter should be \fBOP_digest_final()\fR.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBEVP_MD_CTX_set_params\fR\|(3), \fBprovider\-digest\fR\|(7),
\&\fBOSSL_PROVIDER\-FIPS\fR\|(7), \fBOSSL_PROVIDER\-default\fR\|(7)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2020 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
