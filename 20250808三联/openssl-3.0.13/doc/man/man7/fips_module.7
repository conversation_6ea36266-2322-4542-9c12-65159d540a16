.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "FIPS_MODULE 7ossl"
.TH FIPS_MODULE 7ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
fips_module \- OpenSSL fips module guide
.SH SYNOPSIS
.IX Header "SYNOPSIS"
See the individual manual pages for details.
.SH DESCRIPTION
.IX Header "DESCRIPTION"
This guide details different ways that OpenSSL can be used in conjunction
with the FIPS module. Which is the correct approach to use will depend on your
own specific circumstances and what you are attempting to achieve.
.PP
For information related to installing the FIPS module see
<https://github.com/openssl/openssl/blob/master/README\-FIPS.md>.
.PP
Note that the old functions \fBFIPS_mode()\fR and \fBFIPS_mode_set()\fR are no longer
present so you must remove them from your application if you use them.
.PP
Applications written to use the OpenSSL 3.0 FIPS module should not use any
legacy APIs or features that avoid the FIPS module. Specifically this includes:
.IP \(bu 4
Low level cryptographic APIs (use the high level APIs, such as EVP, instead)
.IP \(bu 4
Engines
.IP \(bu 4
Any functions that create or modify custom "METHODS" (for example
\&\fBEVP_MD_meth_new()\fR, \fBEVP_CIPHER_meth_new()\fR, \fBEVP_PKEY_meth_new()\fR, \fBRSA_meth_new()\fR,
\&\fBEC_KEY_METHOD_new()\fR, etc.)
.PP
All of the above APIs are deprecated in OpenSSL 3.0 \- so a simple rule is to
avoid using all deprecated functions. See \fBmigration_guide\fR\|(7) for a list of
deprecated functions.
.SS "Making all applications use the FIPS module by default"
.IX Subsection "Making all applications use the FIPS module by default"
One simple approach is to cause all applications that are using OpenSSL to only
use the FIPS module for cryptographic algorithms by default.
.PP
This approach can be done purely via configuration. As long as applications are
built and linked against OpenSSL 3.0 and do not override the loading of the
default config file or its settings then they can automatically start using the
FIPS module without the need for any further code changes.
.PP
To do this the default OpenSSL config file will have to be modified. The
location of this config file will depend on the platform, and any options that
were given during the build process. You can check the location of the config
file by running this command:
.PP
.Vb 2
\&    $ openssl version \-d
\&    OPENSSLDIR: "/usr/local/ssl"
.Ve
.PP
Caution: Many Operating Systems install OpenSSL by default. It is a common error
to not have the correct version of OpenSSL in your \f(CW$PATH\fR. Check that you are
running an OpenSSL 3.0 version like this:
.PP
.Vb 2
\&    $ openssl version \-v
\&    OpenSSL 3.0.0\-dev xx XXX xxxx (Library: OpenSSL 3.0.0\-dev xx XXX xxxx)
.Ve
.PP
The \fBOPENSSLDIR\fR value above gives the directory name for where the default
config file is stored. So in this case the default config file will be called
\&\fI/usr/local/ssl/openssl.cnf\fR.
.PP
Edit the config file to add the following lines near the beginning:
.PP
.Vb 2
\&    config_diagnostics = 1
\&    openssl_conf = openssl_init
\&
\&    .include /usr/local/ssl/fipsmodule.cnf
\&
\&    [openssl_init]
\&    providers = provider_sect
\&
\&    [provider_sect]
\&    fips = fips_sect
\&    base = base_sect
\&
\&    [base_sect]
\&    activate = 1
.Ve
.PP
Obviously the include file location above should match the path and name of the
FIPS module config file that you installed earlier.
See <https://github.com/openssl/openssl/blob/master/README\-FIPS.md>.
.PP
For FIPS usage, it is recommended that the \fBconfig_diagnostics\fR option is
enabled to prevent accidental use of non-FIPS validated algorithms via broken
or mistaken configuration.  See \fBconfig\fR\|(5).
.PP
Any applications that use OpenSSL 3.0 and are started after these changes are
made will start using only the FIPS module unless those applications take
explicit steps to avoid this default behaviour. Note that this configuration
also activates the "base" provider. The base provider does not include any
cryptographic algorithms (and therefore does not impact the validation status of
any cryptographic operations), but does include other supporting algorithms that
may be required. It is designed to be used in conjunction with the FIPS module.
.PP
This approach has the primary advantage that it is simple, and no code changes
are required in applications in order to benefit from the FIPS module. There are
some disadvantages to this approach:
.IP \(bu 4
You may not want all applications to use the FIPS module.
.Sp
It may be the case that some applications should and some should not use the
FIPS module.
.IP \(bu 4
If applications take explicit steps to not load the default config file or
set different settings.
.Sp
This method will not work for these cases.
.IP \(bu 4
The algorithms available in the FIPS module are a subset of the algorithms
that are available in the default OpenSSL Provider.
.Sp
If any applications attempt to use any algorithms that are not present,
then they will fail.
.IP \(bu 4
Usage of certain deprecated APIs avoids the use of the FIPS module.
.Sp
If any applications use those APIs then the FIPS module will not be used.
.SS "Selectively making applications use the FIPS module by default"
.IX Subsection "Selectively making applications use the FIPS module by default"
A variation on the above approach is to do the same thing on an individual
application basis. The default OpenSSL config file depends on the compiled in
value for \fBOPENSSLDIR\fR as described in the section above. However it is also
possible to override the config file to be used via the \fBOPENSSL_CONF\fR
environment variable. For example the following, on Unix, will cause the
application to be executed with a non-standard config file location:
.PP
.Vb 1
\&    $ OPENSSL_CONF=/my/nondefault/openssl.cnf myapplication
.Ve
.PP
Using this mechanism you can control which config file is loaded (and hence
whether the FIPS module is loaded) on an application by application basis.
.PP
This removes the disadvantage listed above that you may not want all
applications to use the FIPS module. All the other advantages and disadvantages
still apply.
.SS "Programmatically loading the FIPS module (default library context)"
.IX Subsection "Programmatically loading the FIPS module (default library context)"
Applications may choose to load the FIPS provider explicitly rather than relying
on config to do this. The config file is still necessary in order to hold the
FIPS module config data (such as its self test status and integrity data). But
in this case we do not automatically activate the FIPS provider via that config
file.
.PP
To do things this way configure as per
"Making all applications use the FIPS module by default" above, but edit the
\&\fIfipsmodule.cnf\fR file to remove or comment out the line which says
\&\f(CW\*(C`activate = 1\*(C'\fR (note that setting this value to 0 is \fInot\fR sufficient).
This means all the required config information will be available to load the
FIPS module, but it is not automatically loaded when the application starts. The
FIPS provider can then be loaded programmatically like this:
.PP
.Vb 1
\&    #include <openssl/provider.h>
\&
\&    int main(void)
\&    {
\&        OSSL_PROVIDER *fips;
\&        OSSL_PROVIDER *base;
\&
\&        fips = OSSL_PROVIDER_load(NULL, "fips");
\&        if (fips == NULL) {
\&            printf("Failed to load FIPS provider\en");
\&            exit(EXIT_FAILURE);
\&        }
\&        base = OSSL_PROVIDER_load(NULL, "base");
\&        if (base == NULL) {
\&            OSSL_PROVIDER_unload(fips);
\&            printf("Failed to load base provider\en");
\&            exit(EXIT_FAILURE);
\&        }
\&
\&        /* Rest of application */
\&
\&        OSSL_PROVIDER_unload(base);
\&        OSSL_PROVIDER_unload(fips);
\&        exit(EXIT_SUCCESS);
\&    }
.Ve
.PP
Note that this should be one of the first things that you do in your
application. If any OpenSSL functions get called that require the use of
cryptographic functions before this occurs then, if no provider has yet been
loaded, then the default provider will be automatically loaded. If you then
later explicitly load the FIPS provider then you will have both the FIPS and the
default provider loaded at the same time. It is undefined which implementation
of an algorithm will be used if multiple implementations are available and you
have not explicitly specified via a property query (see below) which one should
be used.
.PP
Also note that in this example we have additionally loaded the "base" provider.
This loads a sub-set of algorithms that are also available in the default
provider \- specifically non cryptographic ones which may be used in conjunction
with the FIPS provider. For example this contains algorithms for encoding and
decoding keys. If you decide not to load the default provider then you
will usually want to load the base provider instead.
.PP
In this example we are using the "default" library context. OpenSSL functions
operate within the scope of a library context. If no library context is
explicitly specified then the default library context is used. For further
details about library contexts see the \fBOSSL_LIB_CTX\fR\|(3) man page.
.SS "Loading the FIPS module at the same time as other providers"
.IX Subsection "Loading the FIPS module at the same time as other providers"
It is possible to have the FIPS provider and other providers (such as the
default provider) all loaded at the same time into the same library context. You
can use a property query string during algorithm fetches to specify which
implementation you would like to use.
.PP
For example to fetch an implementation of SHA256 which conforms to FIPS
standards you can specify the property query \f(CW\*(C`fips=yes\*(C'\fR like this:
.PP
.Vb 1
\&    EVP_MD *sha256;
\&
\&    sha256 = EVP_MD_fetch(NULL, "SHA2\-256", "fips=yes");
.Ve
.PP
If no property query is specified, or more than one implementation matches the
property query then it is undefined which implementation of a particular
algorithm will be returned.
.PP
This example shows an explicit request for an implementation of SHA256 from the
default provider:
.PP
.Vb 1
\&    EVP_MD *sha256;
\&
\&    sha256 = EVP_MD_fetch(NULL, "SHA2\-256", "provider=default");
.Ve
.PP
It is also possible to set a default property query string. The following
example sets the default property query of \f(CW\*(C`fips=yes\*(C'\fR for all fetches within
the default library context:
.PP
.Vb 1
\&    EVP_set_default_properties(NULL, "fips=yes");
.Ve
.PP
If a fetch function has both an explicit property query specified, and a
default property query is defined then the two queries are merged together and
both apply. The local property query overrides the default properties if the
same property name is specified in both.
.PP
There are two important built-in properties that you should be aware of:
.PP
The "provider" property enables you to specify which provider you want an
implementation to be fetched from, e.g. \f(CW\*(C`provider=default\*(C'\fR or \f(CW\*(C`provider=fips\*(C'\fR.
All algorithms implemented in a provider have this property set on them.
.PP
There is also the \f(CW\*(C`fips\*(C'\fR property. All FIPS algorithms match against the
property query \f(CW\*(C`fips=yes\*(C'\fR. There are also some non-cryptographic algorithms
available in the default and base providers that also have the \f(CW\*(C`fips=yes\*(C'\fR
property defined for them. These are the encoder and decoder algorithms that
can (for example) be used to write out a key generated in the FIPS provider to a
file. The encoder and decoder algorithms are not in the FIPS module itself but
are allowed to be used in conjunction with the FIPS algorithms.
.PP
It is possible to specify default properties within a config file. For example
the following config file automatically loads the default and FIPS providers and
sets the default property value to be \f(CW\*(C`fips=yes\*(C'\fR. Note that this config file
does not load the "base" provider. All supporting algorithms that are in "base"
are also in "default", so it is unnecessary in this case:
.PP
.Vb 2
\&    config_diagnostics = 1
\&    openssl_conf = openssl_init
\&
\&    .include /usr/local/ssl/fipsmodule.cnf
\&
\&    [openssl_init]
\&    providers = provider_sect
\&    alg_section = algorithm_sect
\&
\&    [provider_sect]
\&    fips = fips_sect
\&    default = default_sect
\&
\&    [default_sect]
\&    activate = 1
\&
\&    [algorithm_sect]
\&    default_properties = fips=yes
.Ve
.SS "Programmatically loading the FIPS module (nondefault library context)"
.IX Subsection "Programmatically loading the FIPS module (nondefault library context)"
In addition to using properties to separate usage of the FIPS module from other
usages this can also be achieved using library contexts. In this example we
create two library contexts. In one we assume the existence of a config file
called \fIopenssl\-fips.cnf\fR that automatically loads and configures the FIPS and
base providers. The other library context will just use the default provider.
.PP
.Vb 4
\&    OSSL_LIB_CTX *fips_libctx, *nonfips_libctx;
\&    OSSL_PROVIDER *defctxnull = NULL;
\&    EVP_MD *fipssha256 = NULL, *nonfipssha256 = NULL;
\&    int ret = 1;
\&
\&    /*
\&     * Create two nondefault library contexts. One for fips usage and
\&     * one for non\-fips usage
\&     */
\&    fips_libctx = OSSL_LIB_CTX_new();
\&    nonfips_libctx = OSSL_LIB_CTX_new();
\&    if (fips_libctx == NULL || nonfips_libctx == NULL)
\&        goto err;
\&
\&    /* Prevent anything from using the default library context */
\&    defctxnull = OSSL_PROVIDER_load(NULL, "null");
\&
\&    /*
\&     * Load config file for the FIPS library context. We assume that
\&     * this config file will automatically activate the FIPS and base
\&     * providers so we don\*(Aqt need to explicitly load them here.
\&     */
\&    if (!OSSL_LIB_CTX_load_config(fips_libctx, "openssl\-fips.cnf"))
\&        goto err;
\&
\&    /*
\&     * We don\*(Aqt need to do anything special to load the default
\&     * provider into nonfips_libctx. This happens automatically if no
\&     * other providers are loaded.
\&     * Because we don\*(Aqt call OSSL_LIB_CTX_load_config() explicitly for
\&     * nonfips_libctx it will just use the default config file.
\&     */
\&
\&    /* As an example get some digests */
\&
\&    /* Get a FIPS validated digest */
\&    fipssha256 = EVP_MD_fetch(fips_libctx, "SHA2\-256", NULL);
\&    if (fipssha256 == NULL)
\&        goto err;
\&
\&    /* Get a non\-FIPS validated digest */
\&    nonfipssha256 = EVP_MD_fetch(nonfips_libctx, "SHA2\-256", NULL);
\&    if (nonfipssha256 == NULL)
\&        goto err;
\&
\&    /* Use the digests */
\&
\&    printf("Success\en");
\&    ret = 0;
\&
\&    err:
\&    EVP_MD_free(fipssha256);
\&    EVP_MD_free(nonfipssha256);
\&    OSSL_LIB_CTX_free(fips_libctx);
\&    OSSL_LIB_CTX_free(nonfips_libctx);
\&    OSSL_PROVIDER_unload(defctxnull);
\&
\&    return ret;
.Ve
.PP
Note that we have made use of the special "null" provider here which we load
into the default library context. We could have chosen to use the default
library context for FIPS usage, and just create one additional library context
for other usages \- or vice versa. However if code has not been converted to use
library contexts then the default library context will be automatically used.
This could be the case for your own existing applications as well as certain
parts of OpenSSL itself. Not all parts of OpenSSL are library context aware. If
this happens then you could "accidentally" use the wrong library context for a
particular operation. To be sure this doesn't happen you can load the "null"
provider into the default library context. Because a provider has been
explicitly loaded, the default provider will not automatically load. This means
code using the default context by accident will fail because no algorithms will
be available.
.PP
See "Library Context" in \fBmigration_guide\fR\|(7) for additional information about the
Library Context.
.SS "Using Encoders and Decoders with the FIPS module"
.IX Subsection "Using Encoders and Decoders with the FIPS module"
Encoders and decoders are used to read and write keys or parameters from or to
some external format (for example a PEM file). If your application generates
keys or parameters that then need to be written into PEM or DER format
then it is likely that you will need to use an encoder to do this. Similarly
you need a decoder to read previously saved keys and parameters. In most cases
this will be invisible to you if you are using APIs that existed in
OpenSSL 1.1.1 or earlier such as \fBi2d_PrivateKey\fR\|(3). However the appropriate
encoder/decoder will need to be available in the library context associated with
the key or parameter object. The built-in OpenSSL encoders and decoders are
implemented in both the default and base providers and are not in the FIPS
module boundary. However since they are not cryptographic algorithms themselves
it is still possible to use them in conjunction with the FIPS module, and
therefore these encoders/decoders have the \f(CW\*(C`fips=yes\*(C'\fR property against them.
You should ensure that either the default or base provider is loaded into the
library context in this case.
.SS "Using the FIPS module in SSL/TLS"
.IX Subsection "Using the FIPS module in SSL/TLS"
Writing an application that uses libssl in conjunction with the FIPS module is
much the same as writing a normal libssl application. If you are using global
properties and the default library context to specify usage of FIPS validated
algorithms then this will happen automatically for all cryptographic algorithms
in libssl. If you are using a nondefault library context to load the FIPS
provider then you can supply this to libssl using the function
\&\fBSSL_CTX_new_ex\fR\|(3). This works as a drop in replacement for the function
\&\fBSSL_CTX_new\fR\|(3) except it provides you with the capability to specify the
library context to be used. You can also use the same function to specify
libssl specific properties to use.
.PP
In this first example we create two SSL_CTX objects using two different library
contexts.
.PP
.Vb 11
\&    /*
\&     * We assume that a nondefault library context with the FIPS
\&     * provider loaded has been created called fips_libctx.
\&     */
\&    SSL_CTX *fips_ssl_ctx = SSL_CTX_new_ex(fips_libctx, NULL, TLS_method());
\&    /*
\&     * We assume that a nondefault library context with the default
\&     * provider loaded has been created called non_fips_libctx.
\&     */
\&    SSL_CTX *non_fips_ssl_ctx = SSL_CTX_new_ex(non_fips_libctx, NULL,
\&                                               TLS_method());
.Ve
.PP
In this second example we create two SSL_CTX objects using different properties
to specify FIPS usage:
.PP
.Vb 10
\&    /*
\&     * The "fips=yes" property includes all FIPS approved algorithms
\&     * as well as encoders from the default provider that are allowed
\&     * to be used. The NULL below indicates that we are using the
\&     * default library context.
\&     */
\&    SSL_CTX *fips_ssl_ctx = SSL_CTX_new_ex(NULL, "fips=yes", TLS_method());
\&    /*
\&     * The "provider!=fips" property allows algorithms from any
\&     * provider except the FIPS provider
\&     */
\&    SSL_CTX *non_fips_ssl_ctx = SSL_CTX_new_ex(NULL, "provider!=fips",
\&                                               TLS_method());
.Ve
.SS "Confirming that an algorithm is being provided by the FIPS module"
.IX Subsection "Confirming that an algorithm is being provided by the FIPS module"
A chain of links needs to be followed to go from an algorithm instance to the
provider that implements it. The process is similar for all algorithms. Here the
example of a digest is used.
.PP
To go from an \fBEVP_MD_CTX\fR to an \fBEVP_MD\fR, use \fBEVP_MD_CTX_md\fR\|(3) .
To go from the \fBEVP_MD\fR to its \fBOSSL_PROVIDER\fR,
use \fBEVP_MD_get0_provider\fR\|(3).
To extract the name from the \fBOSSL_PROVIDER\fR, use
\&\fBOSSL_PROVIDER_get0_name\fR\|(3).
.SH NOTES
.IX Header "NOTES"
Some released versions of OpenSSL do not include a validated
FIPS provider.  To determine which versions have undergone
the validation process, please refer to the
OpenSSL Downloads page <https://www.openssl.org/source/>.  If you
require FIPS-approved functionality, it is essential to build your FIPS
provider using one of the validated versions listed there.  Normally,
it is possible to utilize a FIPS provider constructed from one of the
validated versions alongside \fIlibcrypto\fR and \fIlibssl\fR compiled from any
release within the same major release series.  This flexibility enables
you to address bug fixes and CVEs that fall outside the FIPS boundary.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBmigration_guide\fR\|(7), \fBcrypto\fR\|(7), \fBfips_config\fR\|(5),
<https://www.openssl.org/source/>
.SH HISTORY
.IX Header "HISTORY"
The FIPS module guide was created for use with the new FIPS provider
in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2021\-2023 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
