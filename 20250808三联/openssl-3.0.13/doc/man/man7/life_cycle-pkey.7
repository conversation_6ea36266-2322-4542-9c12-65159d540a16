.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "LIFE_CYCLE-PKEY 7ossl"
.TH LIFE_CYCLE-PKEY 7ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
life_cycle\-pkey \- The PKEY algorithm life\-cycle
.SH DESCRIPTION
.IX Header "DESCRIPTION"
All public keys (PKEYs) go through a number of stages in their life-cycle:
.IP start 4
.IX Item "start"
This state represents the PKEY before it has been allocated.  It is the
starting state for any life-cycle transitions.
.IP newed 4
.IX Item "newed"
This state represents the PKEY after it has been allocated.
.IP decapsulate 4
.IX Item "decapsulate"
This state represents the PKEY when it is ready to perform a private key decapsulation
operation.
.IP decrypt 4
.IX Item "decrypt"
This state represents the PKEY when it is ready to decrypt some ciphertext.
.IP derive 4
.IX Item "derive"
This state represents the PKEY when it is ready to derive a shared secret.
.IP "digest sign" 4
.IX Item "digest sign"
This state represents the PKEY when it is ready to perform a private key signature
operation.
.IP encapsulate 4
.IX Item "encapsulate"
This state represents the PKEY when it is ready to perform a public key encapsulation
operation.
.IP encrypt 4
.IX Item "encrypt"
This state represents the PKEY when it is ready to encrypt some plaintext.
.IP "key generation" 4
.IX Item "key generation"
This state represents the PKEY when it is ready to generate a new public/private key.
.IP "parameter generation" 4
.IX Item "parameter generation"
This state represents the PKEY when it is ready to generate key parameters.
.IP verify 4
.IX Item "verify"
This state represents the PKEY when it is ready to verify a public key signature.
.IP "verify recover" 4
.IX Item "verify recover"
This state represents the PKEY when it is ready to recover a public key signature data.
.IP freed 4
.IX Item "freed"
This state is entered when the PKEY is freed.  It is the terminal state
for all life-cycle transitions.
.SS "State Transition Diagram"
.IX Subsection "State Transition Diagram"
The usual life-cycle of a PKEY object is illustrated:
                                                   +-------------+
                                                   |             |
                                                   |    start    |
                                                   |             |
                   EVP_PKEY_derive                 +-------------+
 +-------------+   EVP_PKEY_derive_set_peer               |                                          +-------------+
 |             |----------------------------+             |             +----------------------------|             |
 |   derive    |                            |             |             |  EVP_PKEY_verify           |   verify    |
 |             |<---------------------------+             |             +--------------------------->|             |
 +-------------+                                          |                                          +-------------+
             ^                                            |                                            ^
             |   EVP_PKEY_derive_init                     |             EVP_PKEY_verify_init           |
             +---------------------------------------+    |    +---------------------------------------+
                                                     |    |    |
 +-------------+                                     |    |    |                                     +-------------+
 |             |----------------------------+        |    |    |        +----------------------------|             |
 | digest sign |   EVP_PKEY_sign            |        |    |    |        |  EVP_PKEY_verify_recover   |   verify    |
 |             |<---------------------------+        |    |    |        +--------------------------->|   recover   |
 +-------------+                                     |    |    |                                     +-------------+
             ^                                       |    |    |                                       ^
             |     EVP_PKEY_sign_init                |    |    |        EVP_PKEY_verify_recover_init   |
             +---------------------------------+     |    |    |     +---------------------------------+
                                               |     |    |    |     |
 +-------------+                               |     |    |    |     |                               +-------------+
 |             |----------------------------+  |     |    |    |     |  +----------------------------|             |
 | decapsulate |   EVP_PKEY_decapsulate     |  |     |    |    |     |  |  EVP_PKEY_decrypt          |   decrypt   |
 |             |<---------------------------+  |     |    v    |     |  +--------------------------->|             |
 +-------------+                               |   +-------------+   |                               +-------------+
             ^                                 +---|             |---+                                 ^
             |     EVP_PKEY_decapsulate_init       |             |      EVP_PKEY_decrypt_init          |
             +-------------------------------------|    newed    |-------------------------------------+
                                                   |             |
                                               +---|             |---+
 +-------------+                               |   +-------------+   |                               +-------------+
 |             |----------------------------+  |     |         |     |  +----------------------------|             |
 | encapsulate |   EVP_PKEY_encapsulate     |  |     |         |     |  |  EVP_PKEY_encrypt          |   encrypt   |
 |             |<---------------------------+  |     |         |     |  +--------------------------->|             |
 +-------------+                               |     |         |     |                               +-------------+
             ^                                 |     |         |     |                                 ^
             |     EVP_PKEY_encapsulate_init   |     |         |     |  EVP_PKEY_encrypt_init          |
             +---------------------------------+     |         |     +---------------------------------+
                                                     |         |
             +---------------------------------------+         +---------------------------------------+
             |     EVP_PKEY_paramgen_init                               EVP_PKEY_keygen_init           |
             v                                                                                         v
 +-------------+                                                                                     +-------------+
 |             |----------------------------+                           +----------------------------|             |
 |  parameter  |                            |                           |                            |     key     |
 |  generation |<---------------------------+                           +--------------------------->|  generation |
 +-------------+   EVP_PKEY_paramgen                                       EVP_PKEY_keygen           +-------------+
                   EVP_PKEY_gen                                            EVP_PKEY_gen


                                    + - - - - - +                    +-----------+
                                    '           ' EVP_PKEY_CTX_free  |           |
                                    ' any state '------------------->|   freed   |
                                    '           '                    |           |
                                    + - - - - - +                    +-----------+
.SS "Formal State Transitions"
.IX Subsection "Formal State Transitions"
This section defines all of the legal state transitions.
This is the canonical list.
 Function Call                 ---------------------------------------------------------------------- Current State ----------------------------------------------------------------------
                               start    newed       digest       verify       verify       encrypt      decrypt      derive      encapsulate  decapsulate  parameter       key       freed
                                                     sign                     recover                                                                      generation   generation
 EVP_PKEY_CTX_new              newed
 EVP_PKEY_CTX_new_id           newed
 EVP_PKEY_CTX_new_from_name    newed
 EVP_PKEY_CTX_new_from_pkey    newed
 EVP_PKEY_sign_init                    digest       digest       digest       digest       digest       digest       digest       digest       digest       digest       digest
                                        sign         sign         sign         sign         sign         sign         sign         sign         sign         sign         sign
 EVP_PKEY_sign                                      digest
                                                     sign
 EVP_PKEY_verify_init                  verify       verify       verify       verify       verify       verify       verify       verify       verify       verify       verify
 EVP_PKEY_verify                                                 verify
 EVP_PKEY_verify_recover_init          verify       verify       verify       verify       verify       verify       verify       verify       verify       verify       verify
                                       recover      recover      recover      recover      recover      recover      recover      recover      recover      recover      recover
 EVP_PKEY_verify_recover                                                      verify
                                                                              recover
 EVP_PKEY_encrypt_init                 encrypt      encrypt      encrypt      encrypt      encrypt      encrypt      encrypt      encrypt      encrypt      encrypt      encrypt
 EVP_PKEY_encrypt                                                                          encrypt
 EVP_PKEY_decrypt_init                 decrypt      decrypt      decrypt      decrypt      decrypt      decrypt      decrypt      decrypt      decrypt      decrypt      decrypt
 EVP_PKEY_decrypt                                                                                       decrypt
 EVP_PKEY_derive_init                  derive       derive       derive       derive       derive       derive       derive       derive       derive       derive       derive
 EVP_PKEY_derive_set_peer                                                                                            derive
 EVP_PKEY_derive                                                                                                     derive
 EVP_PKEY_encapsulate_init            encapsulate  encapsulate  encapsulate  encapsulate  encapsulate  encapsulate  encapsulate  encapsulate  encapsulate  encapsulate  encapsulate
 EVP_PKEY_encapsulate                                                                                                            encapsulate
 EVP_PKEY_decapsulate_init            decapsulate  decapsulate  decapsulate  decapsulate  decapsulate  decapsulate  decapsulate  decapsulate  decapsulate  decapsulate  decapsulate
 EVP_PKEY_decapsulate                                                                                                                         decapsulate
 EVP_PKEY_paramgen_init               parameter    parameter    parameter    parameter    parameter    parameter    parameter    parameter    parameter    parameter    parameter
                                      generation   generation   generation   generation   generation   generation   generation   generation   generation   generation   generation
 EVP_PKEY_paramgen                                                                                                                                         parameter
                                                                                                                                                           generation
 EVP_PKEY_keygen_init                    key          key          key          key          key          key          key          key          key          key          key
                                      generation   generation   generation   generation   generation   generation   generation   generation   generation   generation   generation
 EVP_PKEY_keygen                                                                                                                                                           key
                                                                                                                                                                        generation
 EVP_PKEY_gen                                                                                                                                              parameter       key
                                                                                                                                                           generation   generation
 EVP_PKEY_CTX_get_params                newed       digest       verify       verify       encrypt      decrypt      derive      encapsulate  decapsulate  parameter       key
                                                     sign                     recover                                                                      generation   generation
 EVP_PKEY_CTX_set_params                newed       digest       verify       verify       encrypt      decrypt      derive      encapsulate  decapsulate  parameter       key
                                                     sign                     recover                                                                      generation   generation
 EVP_PKEY_CTX_gettable_params           newed       digest       verify       verify       encrypt      decrypt      derive      encapsulate  decapsulate  parameter       key
                                                     sign                     recover                                                                      generation   generation
 EVP_PKEY_CTX_settable_params           newed       digest       verify       verify       encrypt      decrypt      derive      encapsulate  decapsulate  parameter       key
                                                     sign                     recover                                                                      generation   generation
 EVP_PKEY_CTX_free             freed    freed        freed        freed        freed        freed        freed        freed        freed        freed        freed        freed
.SH NOTES
.IX Header "NOTES"
At some point the EVP layer will begin enforcing the transitions described
herein.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBEVP_PKEY_new\fR\|(3),
\&\fBEVP_PKEY_decapsulate\fR\|(3), \fBEVP_PKEY_decrypt\fR\|(3), \fBEVP_PKEY_encapsulate\fR\|(3),
\&\fBEVP_PKEY_encrypt\fR\|(3), \fBEVP_PKEY_derive\fR\|(3), \fBEVP_PKEY_keygen\fR\|(3),
\&\fBEVP_PKEY_sign\fR\|(3), \fBEVP_PKEY_verify\fR\|(3), \fBEVP_PKEY_verify_recover\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
The provider PKEY interface was introduced in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2021\-2023 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
