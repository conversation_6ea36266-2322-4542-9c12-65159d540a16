.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "EVP_MD-SHA2 7ossl"
.TH EVP_MD-SHA2 7ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
EVP_MD\-SHA2 \- The SHA2 EVP_MD implementation
.SH DESCRIPTION
.IX Header "DESCRIPTION"
Support for computing SHA2 digests through the \fBEVP_MD\fR API.
.SS Identities
.IX Subsection "Identities"
This implementation includes the following varieties:
.IP \(bu 4
Available with the FIPS provider as well as the default provider:
.RS 4
.IP SHA2\-224 4
.IX Item "SHA2-224"
Known names are "SHA2\-224", "SHA\-224" and "SHA224".
.IP SHA2\-256 4
.IX Item "SHA2-256"
Known names are "SHA2\-256", "SHA\-256" and "SHA256".
.IP SHA2\-384 4
.IX Item "SHA2-384"
Known names are "SHA2\-384", "SHA\-384" and "SHA384".
.IP SHA2\-512 4
.IX Item "SHA2-512"
Known names are "SHA2\-512", "SHA\-512" and "SHA512".
.RE
.RS 4
.RE
.IP \(bu 4
Available with the default provider:
.RS 4
.IP SHA2\-512/224 4
.IX Item "SHA2-512/224"
Known names are "SHA2\-512/224", "SHA\-512/224" and "SHA512\-224".
.IP SHA2\-512/256 4
.IX Item "SHA2-512/256"
Known names are "SHA2\-512/256", "SHA\-512/256" and "SHA512\-256".
.RE
.RS 4
.RE
.SS "Gettable Parameters"
.IX Subsection "Gettable Parameters"
This implementation supports the common gettable parameters described
in \fBEVP_MD\-common\fR\|(7).
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBprovider\-digest\fR\|(7), \fBOSSL_PROVIDER\-FIPS\fR\|(7), \fBOSSL_PROVIDER\-default\fR\|(7)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2020 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
