.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "EVP_MAC-KMAC 7ossl"
.TH EVP_MAC-KMAC 7ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
EVP_MAC\-KMAC, EVP_MAC\-KMAC128, EVP_MAC\-KMAC256
\&\- The KMAC EVP_MAC implementations
.SH DESCRIPTION
.IX Header "DESCRIPTION"
Support for computing KMAC MACs through the \fBEVP_MAC\fR API.
.SS Identity
.IX Subsection "Identity"
These implementations are identified with one of these names and
properties, to be used with \fBEVP_MAC_fetch()\fR:
.IP """KMAC\-128"", ""provider=default"" or ""provider=fips""" 4
.IX Item """KMAC-128"", ""provider=default"" or ""provider=fips"""
.PD 0
.IP """KMAC\-256"", ""provider=default"" or ""provider=fips""" 4
.IX Item """KMAC-256"", ""provider=default"" or ""provider=fips"""
.PD
.SS "Supported parameters"
.IX Subsection "Supported parameters"
The general description of these parameters can be found in
"PARAMETERS" in \fBEVP_MAC\fR\|(3).
.PP
All these parameters (except for "block-size") can be set with
\&\fBEVP_MAC_CTX_set_params()\fR.
Furthermore, the "size" parameter can be retrieved with
\&\fBEVP_MAC_CTX_get_params()\fR, or with \fBEVP_MAC_CTX_get_mac_size()\fR.
The length of the "size" parameter should not exceed that of a \fBsize_t\fR.
Likewise, the "block-size" parameter can be retrieved with
\&\fBEVP_MAC_CTX_get_params()\fR, or with \fBEVP_MAC_CTX_get_block_size()\fR.
.IP """key"" (\fBOSSL_MAC_PARAM_KEY\fR) <octet string>" 4
.IX Item """key"" (OSSL_MAC_PARAM_KEY) <octet string>"
Sets the MAC key.
Setting this parameter is identical to passing a \fIkey\fR to \fBEVP_MAC_init\fR\|(3).
The length of the key (in bytes) must be in the range 4...512.
.IP """custom"" (\fBOSSL_MAC_PARAM_CUSTOM\fR) <octet string>" 4
.IX Item """custom"" (OSSL_MAC_PARAM_CUSTOM) <octet string>"
Sets the customization string.
It is an optional value with a length of at most 512 bytes, and is
empty by default.
.IP """size"" (\fBOSSL_MAC_PARAM_SIZE\fR) <unsigned integer>" 4
.IX Item """size"" (OSSL_MAC_PARAM_SIZE) <unsigned integer>"
Sets the MAC size.
By default, it is 32 for \f(CW\*(C`KMAC\-128\*(C'\fR and 64 for \f(CW\*(C`KMAC\-256\*(C'\fR.
.IP """block-size"" (\fBOSSL_MAC_PARAM_BLOCK_SIZE\fR) <unsigned integer>" 4
.IX Item """block-size"" (OSSL_MAC_PARAM_BLOCK_SIZE) <unsigned integer>"
Gets the MAC block size.
It is 168 for \f(CW\*(C`KMAC\-128\*(C'\fR and 136 for \f(CW\*(C`KMAC\-256\*(C'\fR.
.IP """xof"" (\fBOSSL_MAC_PARAM_XOF\fR) <integer>" 4
.IX Item """xof"" (OSSL_MAC_PARAM_XOF) <integer>"
The "xof" parameter value is expected to be 1 or 0. Use 1 to enable XOF mode.
The default value is 0.
.PP
The "custom" parameter must be set as part of or before the \fBEVP_MAC_init()\fR call.
The "xof" and "size" parameters can be set at any time before \fBEVP_MAC_final()\fR.
The "key" parameter is set as part of the \fBEVP_MAC_init()\fR call, but can be
set before it instead.
.SH EXAMPLES
.IX Header "EXAMPLES"
.Vb 2
\&  #include <openssl/evp.h>
\&  #include <openssl/params.h>
\&
\&  static int do_kmac(const unsigned char *in, size_t in_len,
\&                     const unsigned char *key, size_t key_len,
\&                     const unsigned char *custom, size_t custom_len,
\&                     int xof_enabled, unsigned char *out, int out_len)
\&  {
\&      EVP_MAC_CTX *ctx = NULL;
\&      EVP_MAC *mac = NULL;
\&      OSSL_PARAM params[4], *p;
\&      int ret = 0;
\&      size_t l = 0;
\&
\&      mac = EVP_MAC_fetch(NULL, "KMAC\-128", NULL);
\&      if (mac == NULL)
\&          goto err;
\&      ctx = EVP_MAC_CTX_new(mac);
\&      /* The mac can be freed after it is used by EVP_MAC_CTX_new */
\&      EVP_MAC_free(mac);
\&      if (ctx == NULL)
\&          goto err;
\&
\&      /*
\&       * Setup parameters required before calling EVP_MAC_init()
\&       * The parameters OSSL_MAC_PARAM_XOF and OSSL_MAC_PARAM_SIZE may also be
\&       * used at this point.
\&       */
\&      p = params;
\&      *p++ = OSSL_PARAM_construct_octet_string(OSSL_MAC_PARAM_KEY,
\&                                               (void *)key, key_len);
\&      if (custom != NULL && custom_len != 0)
\&        *p++ = OSSL_PARAM_construct_octet_string(OSSL_MAC_PARAM_CUSTOM,
\&                                                 (void *)custom, custom_len);
\&      *p = OSSL_PARAM_construct_end();
\&      if (!EVP_MAC_CTX_set_params(ctx, params))
\&          goto err;
\&
\&      if (!EVP_MAC_init(ctx))
\&          goto err;
\&
\&      /*
\&       * Note: the following optional parameters can be set any time
\&       * before EVP_MAC_final().
\&       */
\&      p = params;
\&      *p++ = OSSL_PARAM_construct_int(OSSL_MAC_PARAM_XOF, &xof_enabled);
\&      *p++ = OSSL_PARAM_construct_int(OSSL_MAC_PARAM_SIZE, &out_len);
\&      *p = OSSL_PARAM_construct_end();
\&      if (!EVP_MAC_CTX_set_params(ctx, params))
\&          goto err;
\&
\&      /* The update may be called multiple times here for streamed input */
\&      if (!EVP_MAC_update(ctx, in, in_len))
\&          goto err;
\&      if (!EVP_MAC_final(ctx, out, &l, out_len))
\&          goto err;
\&      ret = 1;
\&  err:
\&      EVP_MAC_CTX_free(ctx);
\&      return ret;
\&  }
.Ve
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBEVP_MAC_CTX_get_params\fR\|(3), \fBEVP_MAC_CTX_set_params\fR\|(3),
"PARAMETERS" in \fBEVP_MAC\fR\|(3), \fBOSSL_PARAM\fR\|(3)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2018\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
