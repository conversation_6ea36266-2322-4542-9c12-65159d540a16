.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "MIGRATION_GUIDE 7ossl"
.TH MIGRATION_GUIDE 7ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
migration_guide \- OpenSSL migration guide
.SH SYNOPSIS
.IX Header "SYNOPSIS"
See the individual manual pages for details.
.SH DESCRIPTION
.IX Header "DESCRIPTION"
This guide details the changes required to migrate to new versions of OpenSSL.
Currently this covers OpenSSL 3.0. For earlier versions refer to
<https://github.com/openssl/openssl/blob/master/CHANGES.md>.
For an overview of some of the key concepts introduced in OpenSSL 3.0 see
\&\fBcrypto\fR\|(7).
.SH "OPENSSL 3.0"
.IX Header "OPENSSL 3.0"
.SS "Main Changes from OpenSSL 1.1.1"
.IX Subsection "Main Changes from OpenSSL 1.1.1"
\fIMajor Release\fR
.IX Subsection "Major Release"
.PP
OpenSSL 3.0 is a major release and consequently any application that currently
uses an older version of OpenSSL will at the very least need to be recompiled in
order to work with the new version. It is the intention that the large majority
of applications will work unchanged with OpenSSL 3.0 if those applications
previously worked with OpenSSL 1.1.1. However this is not guaranteed and some
changes may be required in some cases. Changes may also be required if
applications need to take advantage of some of the new features available in
OpenSSL 3.0 such as the availability of the FIPS module.
.PP
\fILicense Change\fR
.IX Subsection "License Change"
.PP
In previous versions, OpenSSL was licensed under the dual OpenSSL and SSLeay
licenses <https://www.openssl.org/source/license-openssl-ssleay.txt>
(both licenses apply). From OpenSSL 3.0 this is replaced by the
Apache License v2 <https://www.openssl.org/source/apache-license-2.0.txt>.
.PP
\fIProviders and FIPS support\fR
.IX Subsection "Providers and FIPS support"
.PP
One of the key changes from OpenSSL 1.1.1 is the introduction of the Provider
concept. Providers collect together and make available algorithm implementations.
With OpenSSL 3.0 it is possible to specify, either programmatically or via a
config file, which providers you want to use for any given application.
OpenSSL 3.0 comes with 5 different providers as standard. Over time third
parties may distribute additional providers that can be plugged into OpenSSL.
All algorithm implementations available via providers are accessed through the
"high level" APIs (for example those functions prefixed with \f(CW\*(C`EVP\*(C'\fR). They cannot
be accessed using the "Low Level APIs".
.PP
One of the standard providers available is the FIPS provider. This makes
available FIPS validated cryptographic algorithms.
The FIPS provider is disabled by default and needs to be enabled explicitly
at configuration time using the \f(CW\*(C`enable\-fips\*(C'\fR option. If it is enabled,
the FIPS provider gets built and installed in addition to the other standard
providers. No separate installation procedure is necessary.
There is however a dedicated \f(CW\*(C`install_fips\*(C'\fR make target, which serves the
special purpose of installing only the FIPS provider into an existing
OpenSSL installation.
.PP
Not all algorithms may be available for the application at a particular moment.
If the application code uses any digest or cipher algorithm via the EVP interface,
the application should verify the result of the \fBEVP_EncryptInit\fR\|(3),
\&\fBEVP_EncryptInit_ex\fR\|(3), and \fBEVP_DigestInit\fR\|(3) functions. In case when
the requested algorithm is not available, these functions will fail.
.PP
See also "Legacy Algorithms" for information on the legacy provider.
.PP
See also "Completing the installation of the FIPS Module" and
"Using the FIPS Module in applications".
.PP
\fILow Level APIs\fR
.IX Subsection "Low Level APIs"
.PP
OpenSSL has historically provided two sets of APIs for invoking cryptographic
algorithms: the "high level" APIs (such as the \f(CW\*(C`EVP\*(C'\fR APIs) and the "low level"
APIs. The high level APIs are typically designed to work across all algorithm
types. The "low level" APIs are targeted at a specific algorithm implementation.
For example, the EVP APIs provide the functions \fBEVP_EncryptInit_ex\fR\|(3),
\&\fBEVP_EncryptUpdate\fR\|(3) and \fBEVP_EncryptFinal\fR\|(3) to perform symmetric
encryption. Those functions can be used with the algorithms AES, CHACHA, 3DES etc.
On the other hand, to do AES encryption using the low level APIs you would have
to call AES specific functions such as \fBAES_set_encrypt_key\fR\|(3),
\&\fBAES_encrypt\fR\|(3), and so on. The functions for 3DES are different.
Use of the low level APIs has been informally discouraged by the OpenSSL
development team for a long time. However in OpenSSL 3.0 this is made more
formal. All such low level APIs have been deprecated. You may still use them in
your applications, but you may start to see deprecation warnings during
compilation (dependent on compiler support for this). Deprecated APIs may be
removed from future versions of OpenSSL so you are strongly encouraged to update
your code to use the high level APIs instead.
.PP
This is described in more detail in "Deprecation of Low Level Functions"
.PP
\fILegacy Algorithms\fR
.IX Subsection "Legacy Algorithms"
.PP
Some cryptographic algorithms such as \fBMD2\fR and \fBDES\fR that were available via
the EVP APIs are now considered legacy and their use is strongly discouraged.
These legacy EVP algorithms are still available in OpenSSL 3.0 but not by
default. If you want to use them then you must load the legacy provider.
This can be as simple as a config file change, or can be done programmatically.
See \fBOSSL_PROVIDER\-legacy\fR\|(7) for a complete list of algorithms.
Applications using the EVP APIs to access these algorithms should instead use
more modern algorithms. If that is not possible then these applications
should ensure that the legacy provider has been loaded. This can be achieved
either programmatically or via configuration. See \fBcrypto\fR\|(7) man page for
more information about providers.
.PP
\fIEngines and "METHOD" APIs\fR
.IX Subsection "Engines and ""METHOD"" APIs"
.PP
The refactoring to support Providers conflicts internally with the APIs used to
support engines, including the ENGINE API and any function that creates or
modifies custom "METHODS" (for example \fBEVP_MD_meth_new\fR\|(3),
\&\fBEVP_CIPHER_meth_new\fR\|(3), \fBEVP_PKEY_meth_new\fR\|(3), \fBRSA_meth_new\fR\|(3),
\&\fBEC_KEY_METHOD_new\fR\|(3), etc.). These functions are being deprecated in
OpenSSL 3.0, and users of these APIs should know that their use can likely
bypass provider selection and configuration, with unintended consequences.
This is particularly relevant for applications written to use the OpenSSL 3.0
FIPS module, as detailed below. Authors and maintainers of external engines are
strongly encouraged to refactor their code transforming engines into providers
using the new Provider API and avoiding deprecated methods.
.PP
\fISupport of legacy engines\fR
.IX Subsection "Support of legacy engines"
.PP
If openssl is not built without engine support or deprecated API support, engines
will still work. However, their applicability will be limited.
.PP
New algorithms provided via engines will still work.
.PP
Engine-backed keys can be loaded via custom \fBOSSL_STORE\fR implementation.
In this case the \fBEVP_PKEY\fR objects created via \fBENGINE_load_private_key\fR\|(3)
will be considered legacy and will continue to work.
.PP
To ensure the future compatibility, the engines should be turned to providers.
To prefer the provider-based hardware offload, you can specify the default
properties to prefer your provider.
.PP
\fIVersioning Scheme\fR
.IX Subsection "Versioning Scheme"
.PP
The OpenSSL versioning scheme has changed with the OpenSSL 3.0 release. The new
versioning scheme has this format:
.PP
MAJOR.MINOR.PATCH
.PP
For OpenSSL 1.1.1 and below, different patch levels were indicated by a letter
at the end of the release version number. This will no longer be used and
instead the patch level is indicated by the final number in the version. A
change in the second (MINOR) number indicates that new features may have been
added. OpenSSL versions with the same major number are API and ABI compatible.
If the major number changes then API and ABI compatibility is not guaranteed.
.PP
For more information, see \fBOpenSSL_version\fR\|(3).
.PP
\fIOther major new features\fR
.IX Subsection "Other major new features"
.PP
Certificate Management Protocol (CMP, RFC 4210)
.IX Subsection "Certificate Management Protocol (CMP, RFC 4210)"
.PP
This also covers CRMF (RFC 4211) and HTTP transfer (RFC 6712)
See \fBopenssl\-cmp\fR\|(1) and \fBOSSL_CMP_exec_certreq\fR\|(3) as starting points.
.PP
HTTP(S) client
.IX Subsection "HTTP(S) client"
.PP
A proper HTTP(S) client that supports GET and POST, redirection, plain and
ASN.1\-encoded contents, proxies, and timeouts.
.PP
Key Derivation Function API (EVP_KDF)
.IX Subsection "Key Derivation Function API (EVP_KDF)"
.PP
This simplifies the process of adding new KDF and PRF implementations.
.PP
Previously KDF algorithms had been shoe-horned into using the EVP_PKEY object
which was not a logical mapping.
Existing applications that use KDF algorithms using EVP_PKEY
(scrypt, TLS1 PRF and HKDF) may be slower as they use an EVP_KDF bridge
internally.
All new applications should use the new \fBEVP_KDF\fR\|(3) interface.
See also "Key Derivation Function (KDF)" in \fBOSSL_PROVIDER\-default\fR\|(7) and
"Key Derivation Function (KDF)" in \fBOSSL_PROVIDER\-FIPS\fR\|(7).
.PP
Message Authentication Code API (EVP_MAC)
.IX Subsection "Message Authentication Code API (EVP_MAC)"
.PP
This simplifies the process of adding MAC implementations.
.PP
This includes a generic EVP_PKEY to EVP_MAC bridge, to facilitate the continued
use of MACs through raw private keys in functionality such as
\&\fBEVP_DigestSign\fR\|(3) and \fBEVP_DigestVerify\fR\|(3).
.PP
All new applications should use the new \fBEVP_MAC\fR\|(3) interface.
See also "Message Authentication Code (MAC)" in \fBOSSL_PROVIDER\-default\fR\|(7)
and "Message Authentication Code (MAC)" in \fBOSSL_PROVIDER\-FIPS\fR\|(7).
.PP
Algorithm Fetching
.IX Subsection "Algorithm Fetching"
.PP
Using calls to convenience functions such as \fBEVP_sha256()\fR and \fBEVP_aes_256_gcm()\fR may
incur a performance penalty when using providers.
Retrieving algorithms from providers involves searching for an algorithm by name.
This is much slower than directly accessing a method table.
It is recommended to prefetch algorithms if an algorithm is used many times.
See "Performance" in \fBcrypto\fR\|(7), "Explicit fetching" in \fBcrypto\fR\|(7) and "Implicit fetching" in \fBcrypto\fR\|(7).
.PP
Support for Linux Kernel TLS
.IX Subsection "Support for Linux Kernel TLS"
.PP
In order to use KTLS, support for it must be compiled in using the
\&\f(CW\*(C`enable\-ktls\*(C'\fR configuration option. It must also be enabled at run time using
the \fBSSL_OP_ENABLE_KTLS\fR option.
.PP
New Algorithms
.IX Subsection "New Algorithms"
.IP \(bu 4
KDF algorithms "SINGLE STEP" and "SSH"
.Sp
See \fBEVP_KDF\-SS\fR\|(7) and \fBEVP_KDF\-SSHKDF\fR\|(7)
.IP \(bu 4
MAC Algorithms "GMAC" and "KMAC"
.Sp
See \fBEVP_MAC\-GMAC\fR\|(7) and \fBEVP_MAC\-KMAC\fR\|(7).
.IP \(bu 4
KEM Algorithm "RSASVE"
.Sp
See \fBEVP_KEM\-RSA\fR\|(7).
.IP \(bu 4
Cipher Algorithm "AES-SIV"
.Sp
See "SIV Mode" in \fBEVP_EncryptInit\fR\|(3).
.IP \(bu 4
AES Key Wrap inverse ciphers supported by EVP layer.
.Sp
The inverse ciphers use AES decryption for wrapping, and AES encryption for
unwrapping. The algorithms are: "AES\-128\-WRAP\-INV", "AES\-192\-WRAP\-INV",
"AES\-256\-WRAP\-INV", "AES\-128\-WRAP\-PAD\-INV", "AES\-192\-WRAP\-PAD\-INV" and
"AES\-256\-WRAP\-PAD\-INV".
.IP \(bu 4
CTS ciphers added to EVP layer.
.Sp
The algorithms are "AES\-128\-CBC\-CTS", "AES\-192\-CBC\-CTS", "AES\-256\-CBC\-CTS",
"CAMELLIA\-128\-CBC\-CTS", "CAMELLIA\-192\-CBC\-CTS" and "CAMELLIA\-256\-CBC\-CTS".
CS1, CS2 and CS3 variants are supported.
.PP
CMS and PKCS#7 updates
.IX Subsection "CMS and PKCS#7 updates"
.IP \(bu 4
Added CAdES-BES signature verification support.
.IP \(bu 4
Added CAdES-BES signature scheme and attributes support (RFC 5126) to CMS API.
.IP \(bu 4
Added AuthEnvelopedData content type structure (RFC 5083) using AES_GCM
.Sp
This uses the AES-GCM parameter (RFC 5084) for the Cryptographic Message Syntax.
Its purpose is to support encryption and decryption of a digital envelope that
is both authenticated and encrypted using AES GCM mode.
.IP \(bu 4
\&\fBPKCS7_get_octet_string\fR\|(3) and \fBPKCS7_type_is_other\fR\|(3) were made public.
.PP
PKCS#12 API updates
.IX Subsection "PKCS#12 API updates"
.PP
The default algorithms for pkcs12 creation with the \fBPKCS12_create()\fR function
were changed to more modern PBKDF2 and AES based algorithms. The default
MAC iteration count was changed to PKCS12_DEFAULT_ITER to make it equal
with the password-based encryption iteration count. The default digest
algorithm for the MAC computation was changed to SHA\-256. The pkcs12
application now supports \-legacy option that restores the previous
default algorithms to support interoperability with legacy systems.
.PP
Added enhanced PKCS#12 APIs which accept a library context \fBOSSL_LIB_CTX\fR
and (where relevant) a property query. Other APIs which handle PKCS#7 and
PKCS#8 objects have also been enhanced where required. This includes:
.PP
\&\fBPKCS12_add_key_ex\fR\|(3), \fBPKCS12_add_safe_ex\fR\|(3), \fBPKCS12_add_safes_ex\fR\|(3),
\&\fBPKCS12_create_ex\fR\|(3), \fBPKCS12_decrypt_skey_ex\fR\|(3), \fBPKCS12_init_ex\fR\|(3),
\&\fBPKCS12_item_decrypt_d2i_ex\fR\|(3), \fBPKCS12_item_i2d_encrypt_ex\fR\|(3),
\&\fBPKCS12_key_gen_asc_ex\fR\|(3), \fBPKCS12_key_gen_uni_ex\fR\|(3), \fBPKCS12_key_gen_utf8_ex\fR\|(3),
\&\fBPKCS12_pack_p7encdata_ex\fR\|(3), \fBPKCS12_pbe_crypt_ex\fR\|(3), \fBPKCS12_PBE_keyivgen_ex\fR\|(3),
\&\fBPKCS12_SAFEBAG_create_pkcs8_encrypt_ex\fR\|(3), \fBPKCS5_pbe2_set_iv_ex\fR\|(3),
\&\fBPKCS5_pbe_set0_algor_ex\fR\|(3), \fBPKCS5_pbe_set_ex\fR\|(3), \fBPKCS5_pbkdf2_set_ex\fR\|(3),
\&\fBPKCS5_v2_PBE_keyivgen_ex\fR\|(3), \fBPKCS5_v2_scrypt_keyivgen_ex\fR\|(3),
\&\fBPKCS8_decrypt_ex\fR\|(3), \fBPKCS8_encrypt_ex\fR\|(3), \fBPKCS8_set0_pbe_ex\fR\|(3).
.PP
As part of this change the EVP_PBE_xxx APIs can also accept a library
context and property query and will call an extended version of the key/IV
derivation function which supports these parameters. This includes
\&\fBEVP_PBE_CipherInit_ex\fR\|(3), \fBEVP_PBE_find_ex\fR\|(3) and \fBEVP_PBE_scrypt_ex\fR\|(3).
.PP
PKCS#12 KDF versus FIPS
.IX Subsection "PKCS#12 KDF versus FIPS"
.PP
Unlike in 1.x.y, the PKCS12KDF algorithm used when a PKCS#12 structure
is created with a MAC that does not work with the FIPS provider as the PKCS12KDF
is not a FIPS approvable mechanism.
.PP
See \fBEVP_KDF\-PKCS12KDF\fR\|(7), \fBPKCS12_create\fR\|(3), \fBopenssl\-pkcs12\fR\|(1),
\&\fBOSSL_PROVIDER\-FIPS\fR\|(7).
.PP
Windows thread synchronization changes
.IX Subsection "Windows thread synchronization changes"
.PP
Windows thread synchronization uses read/write primitives (SRWLock) when
supported by the OS, otherwise CriticalSection continues to be used.
.PP
Trace API
.IX Subsection "Trace API"
.PP
A new generic trace API has been added which provides support for enabling
instrumentation through trace output. This feature is mainly intended as an aid
for developers and is disabled by default. To utilize it, OpenSSL needs to be
configured with the \f(CW\*(C`enable\-trace\*(C'\fR option.
.PP
If the tracing API is enabled, the application can activate trace output by
registering BIOs as trace channels for a number of tracing and debugging
categories. See \fBOSSL_trace_enabled\fR\|(3).
.PP
Key validation updates
.IX Subsection "Key validation updates"
.PP
\&\fBEVP_PKEY_public_check\fR\|(3) and \fBEVP_PKEY_param_check\fR\|(3) now work for
more key types. This includes RSA, DSA, ED25519, X25519, ED448 and X448.
Previously (in 1.1.1) they would return \-2. For key types that do not have
parameters then \fBEVP_PKEY_param_check\fR\|(3) will always return 1.
.PP
\fIOther notable deprecations and changes\fR
.IX Subsection "Other notable deprecations and changes"
.PP
The function code part of an OpenSSL error code is no longer relevant
.IX Subsection "The function code part of an OpenSSL error code is no longer relevant"
.PP
This code is now always set to zero. Related functions are deprecated.
.PP
STACK and HASH macros have been cleaned up
.IX Subsection "STACK and HASH macros have been cleaned up"
.PP
The type-safe wrappers are declared everywhere and implemented once.
See \fBDEFINE_STACK_OF\fR\|(3) and \fBDECLARE_LHASH_OF\fR\|(3).
.PP
The RAND_DRBG subsystem has been removed
.IX Subsection "The RAND_DRBG subsystem has been removed"
.PP
The new \fBEVP_RAND\fR\|(3) is a partial replacement: the DRBG callback framework is
absent. The RAND_DRBG API did not fit well into the new provider concept as
implemented by EVP_RAND and EVP_RAND_CTX.
.PP
Removed \fBFIPS_mode()\fR and \fBFIPS_mode_set()\fR
.IX Subsection "Removed FIPS_mode() and FIPS_mode_set()"
.PP
These functions are legacy APIs that are not applicable to the new provider
model. Applications should instead use
\&\fBEVP_default_properties_is_fips_enabled\fR\|(3) and
\&\fBEVP_default_properties_enable_fips\fR\|(3).
.PP
Key generation is slower
.IX Subsection "Key generation is slower"
.PP
The Miller-Rabin test now uses 64 rounds, which is used for all prime generation,
including RSA key generation. This affects the time for larger keys sizes.
.PP
The default key generation method for the regular 2\-prime RSA keys was changed
to the FIPS186\-4 B.3.6 method (Generation of Probable Primes with Conditions
Based on Auxiliary Probable Primes). This method is slower than the original
method.
.PP
Change PBKDF2 to conform to SP800\-132 instead of the older PKCS5 RFC2898
.IX Subsection "Change PBKDF2 to conform to SP800-132 instead of the older PKCS5 RFC2898"
.PP
This checks that the salt length is at least 128 bits, the derived key length is
at least 112 bits, and that the iteration count is at least 1000.
For backwards compatibility these checks are disabled by default in the
default provider, but are enabled by default in the FIPS provider.
.PP
To enable or disable the checks see \fBOSSL_KDF_PARAM_PKCS5\fR in
\&\fBEVP_KDF\-PBKDF2\fR\|(7). The parameter can be set using \fBEVP_KDF_derive\fR\|(3).
.PP
Enforce a minimum DH modulus size of 512 bits
.IX Subsection "Enforce a minimum DH modulus size of 512 bits"
.PP
Smaller sizes now result in an error.
.PP
SM2 key changes
.IX Subsection "SM2 key changes"
.PP
EC EVP_PKEYs with the SM2 curve have been reworked to automatically become
EVP_PKEY_SM2 rather than EVP_PKEY_EC.
.PP
Unlike in previous OpenSSL versions, this means that applications cannot
call \f(CW\*(C`EVP_PKEY_set_alias_type(pkey, EVP_PKEY_SM2)\*(C'\fR to get SM2 computations.
.PP
Parameter and key generation is also reworked to make it possible
to generate EVP_PKEY_SM2 parameters and keys. Applications must now generate
SM2 keys directly and must not create an EVP_PKEY_EC key first. It is no longer
possible to import an SM2 key with domain parameters other than the SM2 elliptic
curve ones.
.PP
Validation of SM2 keys has been separated from the validation of regular EC
keys, allowing to improve the SM2 validation process to reject loaded private
keys that are not conforming to the SM2 ISO standard.
In particular, a private scalar \fIk\fR outside the range \fI1 <= k < n\-1\fR is
now correctly rejected.
.PP
\fBEVP_PKEY_set_alias_type()\fR method has been removed
.IX Subsection "EVP_PKEY_set_alias_type() method has been removed"
.PP
This function made a \fBEVP_PKEY\fR object mutable after it had been set up. In
OpenSSL 3.0 it was decided that a provided key should not be able to change its
type, so this function has been removed.
.PP
Functions that return an internal key should be treated as read only
.IX Subsection "Functions that return an internal key should be treated as read only"
.PP
Functions such as \fBEVP_PKEY_get0_RSA\fR\|(3) behave slightly differently in
OpenSSL 3.0. Previously they returned a pointer to the low-level key used
internally by libcrypto. From OpenSSL 3.0 this key may now be held in a
provider. Calling these functions will only return a handle on the internal key
where the EVP_PKEY was constructed using this key in the first place, for
example using a function or macro such as \fBEVP_PKEY_assign_RSA\fR\|(3),
\&\fBEVP_PKEY_set1_RSA\fR\|(3), etc.
Where the EVP_PKEY holds a provider managed key, then these functions now return
a cached copy of the key. Changes to the internal provider key that take place
after the first time the cached key is accessed will not be reflected back in
the cached copy. Similarly any changes made to the cached copy by application
code will not be reflected back in the internal provider key.
.PP
For the above reasons the keys returned from these functions should typically be
treated as read-only. To emphasise this the value returned from
\&\fBEVP_PKEY_get0_RSA\fR\|(3), \fBEVP_PKEY_get0_DSA\fR\|(3), \fBEVP_PKEY_get0_EC_KEY\fR\|(3) and
\&\fBEVP_PKEY_get0_DH\fR\|(3) have been made const. This may break some existing code.
Applications broken by this change should be modified. The preferred solution is
to refactor the code to avoid the use of these deprecated functions. Failing
this the code should be modified to use a const pointer instead.
The \fBEVP_PKEY_get1_RSA\fR\|(3), \fBEVP_PKEY_get1_DSA\fR\|(3), \fBEVP_PKEY_get1_EC_KEY\fR\|(3)
and \fBEVP_PKEY_get1_DH\fR\|(3) functions continue to return a non-const pointer to
enable them to be "freed". However they should also be treated as read-only.
.PP
The public key check has moved from \fBEVP_PKEY_derive()\fR to \fBEVP_PKEY_derive_set_peer()\fR
.IX Subsection "The public key check has moved from EVP_PKEY_derive() to EVP_PKEY_derive_set_peer()"
.PP
This may mean result in an error in \fBEVP_PKEY_derive_set_peer\fR\|(3) rather than
during \fBEVP_PKEY_derive\fR\|(3).
To disable this check use EVP_PKEY_derive_set_peer_ex(dh, peer, 0).
.PP
The print format has cosmetic changes for some functions
.IX Subsection "The print format has cosmetic changes for some functions"
.PP
The output from numerous "printing" functions such as \fBX509_signature_print\fR\|(3),
\&\fBX509_print_ex\fR\|(3), \fBX509_CRL_print_ex\fR\|(3), and other similar functions has been
amended such that there may be cosmetic differences between the output
observed in 1.1.1 and 3.0. This also applies to the \fB\-text\fR output from the
\&\fBopenssl x509\fR and \fBopenssl crl\fR applications.
.PP
Interactive mode from the \fBopenssl\fR program has been removed
.IX Subsection "Interactive mode from the openssl program has been removed"
.PP
From now on, running it without arguments is equivalent to \fBopenssl help\fR.
.PP
The error return values from some control calls (ctrl) have changed
.IX Subsection "The error return values from some control calls (ctrl) have changed"
.PP
One significant change is that controls which used to return \-2 for
invalid inputs, now return \-1 indicating a generic error condition instead.
.PP
DH and DHX key types have different settable parameters
.IX Subsection "DH and DHX key types have different settable parameters"
.PP
Previously (in 1.1.1) these conflicting parameters were allowed, but will now
result in errors. See \fBEVP_PKEY\-DH\fR\|(7) for further details. This affects the
behaviour of \fBopenssl\-genpkey\fR\|(1) for DH parameter generation.
.PP
\fBEVP_CIPHER_CTX_set_flags()\fR ordering change
.IX Subsection "EVP_CIPHER_CTX_set_flags() ordering change"
.PP
If using a cipher from a provider the \fBEVP_CIPH_FLAG_LENGTH_BITS\fR flag can only
be set \fBafter\fR the cipher has been assigned to the cipher context.
See "FLAGS" in \fBEVP_EncryptInit\fR\|(3) for more information.
.PP
Validation of operation context parameters
.IX Subsection "Validation of operation context parameters"
.PP
Due to move of the implementation of cryptographic operations to the
providers, validation of various operation parameters can be postponed until
the actual operation is executed where previously it happened immediately
when an operation parameter was set.
.PP
For example when setting an unsupported curve with
\&\fBEVP_PKEY_CTX_set_ec_paramgen_curve_nid()\fR this function call will not fail
but later keygen operations with the EVP_PKEY_CTX will fail.
.PP
Removal of function code from the error codes
.IX Subsection "Removal of function code from the error codes"
.PP
The function code part of the error code is now always set to 0. For that
reason the \fBERR_GET_FUNC()\fR macro was removed. Applications must resolve
the error codes only using the library number and the reason code.
.PP
ChaCha20\-Poly1305 cipher does not allow a truncated IV length to be used
.IX Subsection "ChaCha20-Poly1305 cipher does not allow a truncated IV length to be used"
.PP
In OpenSSL 3.0 setting the IV length to any value other than 12 will result in an
error.
Prior to OpenSSL 3.0 the ivlen could be smaller that the required 12 byte length,
using EVP_CIPHER_CTX_ctrl(ctx, EVP_CRTL_AEAD_SET_IVLEN, ivlen, NULL). This resulted
in an IV that had leading zero padding.
.SS "Installation and Compilation"
.IX Subsection "Installation and Compilation"
Please refer to the INSTALL.md file in the top of the distribution for
instructions on how to build and install OpenSSL 3.0. Please also refer to the
various platform specific NOTES files for your specific platform.
.SS "Upgrading from OpenSSL 1.1.1"
.IX Subsection "Upgrading from OpenSSL 1.1.1"
Upgrading to OpenSSL 3.0 from OpenSSL 1.1.1 should be relatively straight
forward in most cases. The most likely area where you will encounter problems
is if you have used low level APIs in your code (as discussed above). In that
case you are likely to start seeing deprecation warnings when compiling your
application. If this happens you have 3 options:
.IP 1. 4
Ignore the warnings. They are just warnings. The deprecated functions are still present and you may still use them. However be aware that they may be removed from a future version of OpenSSL.
.IP 2. 4
Suppress the warnings. Refer to your compiler documentation on how to do this.
.IP 3. 4
Remove your usage of the low level APIs. In this case you will need to rewrite your code to use the high level APIs instead
.PP
\fIError code changes\fR
.IX Subsection "Error code changes"
.PP
As OpenSSL 3.0 provides a brand new Encoder/Decoder mechanism for working with
widely used file formats, application code that checks for particular error
reason codes on key loading failures might need an update.
.PP
Password-protected keys may deserve special attention. If only some errors
are treated as an indicator that the user should be asked about the password again,
it's worth testing these scenarios and processing the newly relevant codes.
.PP
There may be more cases to treat specially, depending on the calling application code.
.SS "Upgrading from OpenSSL 1.0.2"
.IX Subsection "Upgrading from OpenSSL 1.0.2"
Upgrading to OpenSSL 3.0 from OpenSSL 1.0.2 is likely to be significantly more
difficult. In addition to the issues discussed above in the section about
"Upgrading from OpenSSL 1.1.1", the main things to be aware of are:
.IP 1. 4
The build and installation procedure has changed significantly.
.Sp
Check the file INSTALL.md in the top of the installation for instructions on how
to build and install OpenSSL for your platform. Also read the various NOTES
files in the same directory, as applicable for your platform.
.IP 2. 4
Many structures have been made opaque in OpenSSL 3.0.
.Sp
The structure definitions have been removed from the public header files and
moved to internal header files. In practice this means that you can no longer
stack allocate some structures. Instead they must be heap allocated through some
function call (typically those function names have a \f(CW\*(C`_new\*(C'\fR suffix to them).
Additionally you must use "setter" or "getter" functions to access the fields
within those structures.
.Sp
For example code that previously looked like this:
.Sp
.Vb 1
\& EVP_MD_CTX md_ctx;
\&
\& /* This line will now generate compiler errors */
\& EVP_MD_CTX_init(&md_ctx);
.Ve
.Sp
The code needs to be amended to look like this:
.Sp
.Vb 1
\& EVP_MD_CTX *md_ctx;
\&
\& md_ctx = EVP_MD_CTX_new();
\& ...
\& ...
\& EVP_MD_CTX_free(md_ctx);
.Ve
.IP 3. 4
Support for TLSv1.3 has been added.
.Sp
This has a number of implications for SSL/TLS applications. See the
TLS1.3 page <https://wiki.openssl.org/index.php/TLS1.3> for further details.
.PP
More details about the breaking changes between OpenSSL versions 1.0.2 and 1.1.0
can be found on the
OpenSSL 1.1.0 Changes page <https://wiki.openssl.org/index.php/OpenSSL_1.1.0_Changes>.
.PP
\fIUpgrading from the OpenSSL 2.0 FIPS Object Module\fR
.IX Subsection "Upgrading from the OpenSSL 2.0 FIPS Object Module"
.PP
The OpenSSL 2.0 FIPS Object Module was a separate download that had to be built
separately and then integrated into your main OpenSSL 1.0.2 build.
In OpenSSL 3.0 the FIPS support is fully integrated into the mainline version of
OpenSSL and is no longer a separate download. For further information see
"Completing the installation of the FIPS Module".
.PP
The function calls \fBFIPS_mode()\fR and \fBFIPS_mode_set()\fR have been removed
from OpenSSL 3.0. You should rewrite your application to not use them.
See \fBfips_module\fR\|(7) and \fBOSSL_PROVIDER\-FIPS\fR\|(7) for details.
.SS "Completing the installation of the FIPS Module"
.IX Subsection "Completing the installation of the FIPS Module"
The FIPS Module will be built and installed automatically if FIPS support has
been configured. The current documentation can be found in the
README-FIPS <https://github.com/openssl/openssl/blob/master/README-FIPS.md> file.
.SS Programming
.IX Subsection "Programming"
Applications written to work with OpenSSL 1.1.1 will mostly just work with
OpenSSL 3.0. However changes will be required if you want to take advantage of
some of the new features that OpenSSL 3.0 makes available. In order to do that
you need to understand some new concepts introduced in OpenSSL 3.0.
Read "Library contexts" in \fBcrypto\fR\|(7) for further information.
.PP
\fILibrary Context\fR
.IX Subsection "Library Context"
.PP
A library context allows different components of a complex application to each
use a different library context and have different providers loaded with
different configuration settings.
See "Library contexts" in \fBcrypto\fR\|(7) for further info.
.PP
If the user creates an \fBOSSL_LIB_CTX\fR via \fBOSSL_LIB_CTX_new\fR\|(3) then many
functions may need to be changed to pass additional parameters to handle the
library context.
.PP
Using a Library Context \- Old functions that should be changed
.IX Subsection "Using a Library Context - Old functions that should be changed"
.PP
If a library context is needed then all EVP_* digest functions that return a
\&\fBconst EVP_MD *\fR such as \fBEVP_sha256()\fR should be replaced with a call to
\&\fBEVP_MD_fetch\fR\|(3). See "ALGORITHM FETCHING" in \fBcrypto\fR\|(7).
.PP
If a library context is needed then all EVP_* cipher functions that return a
\&\fBconst EVP_CIPHER *\fR such as \fBEVP_aes_128_cbc()\fR should be replaced vith a call to
\&\fBEVP_CIPHER_fetch\fR\|(3). See "ALGORITHM FETCHING" in \fBcrypto\fR\|(7).
.PP
Some functions can be passed an object that has already been set up with a library
context such as \fBd2i_X509\fR\|(3), \fBd2i_X509_CRL\fR\|(3), \fBd2i_X509_REQ\fR\|(3) and
\&\fBd2i_X509_PUBKEY\fR\|(3). If NULL is passed instead then the created object will be
set up with the default library context. Use \fBX509_new_ex\fR\|(3),
\&\fBX509_CRL_new_ex\fR\|(3), \fBX509_REQ_new_ex\fR\|(3) and \fBX509_PUBKEY_new_ex\fR\|(3) if a
library context is required.
.PP
All functions listed below with a \fINAME\fR have a replacement function \fINAME_ex\fR
that takes \fBOSSL_LIB_CTX\fR as an additional argument. Functions that have other
mappings are listed along with the respective name.
.IP \(bu 4
\&\fBASN1_item_new\fR\|(3), \fBASN1_item_d2i\fR\|(3), \fBASN1_item_d2i_fp\fR\|(3),
\&\fBASN1_item_d2i_bio\fR\|(3), \fBASN1_item_sign\fR\|(3) and \fBASN1_item_verify\fR\|(3)
.IP \(bu 4
\&\fBBIO_new\fR\|(3)
.IP \(bu 4
\&\fBb2i_RSA_PVK_bio()\fR and \fBi2b_PVK_bio()\fR
.IP \(bu 4
\&\fBBN_CTX_new\fR\|(3) and \fBBN_CTX_secure_new\fR\|(3)
.IP \(bu 4
\&\fBCMS_AuthEnvelopedData_create\fR\|(3), \fBCMS_ContentInfo_new\fR\|(3), \fBCMS_data_create\fR\|(3),
\&\fBCMS_digest_create\fR\|(3), \fBCMS_EncryptedData_encrypt\fR\|(3), \fBCMS_encrypt\fR\|(3),
\&\fBCMS_EnvelopedData_create\fR\|(3), \fBCMS_ReceiptRequest_create0\fR\|(3) and \fBCMS_sign\fR\|(3)
.IP \(bu 4
\&\fBCONF_modules_load_file\fR\|(3)
.IP \(bu 4
\&\fBCTLOG_new\fR\|(3), \fBCTLOG_new_from_base64\fR\|(3) and \fBCTLOG_STORE_new\fR\|(3)
.IP \(bu 4
\&\fBCT_POLICY_EVAL_CTX_new\fR\|(3)
.IP \(bu 4
\&\fBd2i_AutoPrivateKey\fR\|(3), \fBd2i_PrivateKey\fR\|(3) and \fBd2i_PUBKEY\fR\|(3)
.IP \(bu 4
\&\fBd2i_PrivateKey_bio\fR\|(3) and \fBd2i_PrivateKey_fp\fR\|(3)
.Sp
Use \fBd2i_PrivateKey_ex_bio\fR\|(3) and \fBd2i_PrivateKey_ex_fp\fR\|(3)
.IP \(bu 4
\&\fBEC_GROUP_new\fR\|(3)
.Sp
Use \fBEC_GROUP_new_by_curve_name_ex\fR\|(3) or \fBEC_GROUP_new_from_params\fR\|(3).
.IP \(bu 4
\&\fBEVP_DigestSignInit\fR\|(3) and \fBEVP_DigestVerifyInit\fR\|(3)
.IP \(bu 4
\&\fBEVP_PBE_CipherInit\fR\|(3), \fBEVP_PBE_find\fR\|(3) and \fBEVP_PBE_scrypt\fR\|(3)
.IP \(bu 4
\&\fBPKCS5_PBE_keyivgen\fR\|(3)
.IP \(bu 4
\&\fBEVP_PKCS82PKEY\fR\|(3)
.IP \(bu 4
\&\fBEVP_PKEY_CTX_new_id\fR\|(3)
.Sp
Use \fBEVP_PKEY_CTX_new_from_name\fR\|(3)
.IP \(bu 4
\&\fBEVP_PKEY_derive_set_peer\fR\|(3), \fBEVP_PKEY_new_raw_private_key\fR\|(3)
and \fBEVP_PKEY_new_raw_public_key\fR\|(3)
.IP \(bu 4
\&\fBEVP_SignFinal\fR\|(3) and \fBEVP_VerifyFinal\fR\|(3)
.IP \(bu 4
\&\fBNCONF_new\fR\|(3)
.IP \(bu 4
\&\fBOCSP_RESPID_match\fR\|(3) and \fBOCSP_RESPID_set_by_key\fR\|(3)
.IP \(bu 4
\&\fBOPENSSL_thread_stop\fR\|(3)
.IP \(bu 4
\&\fBOSSL_STORE_open\fR\|(3)
.IP \(bu 4
\&\fBPEM_read_bio_Parameters\fR\|(3), \fBPEM_read_bio_PrivateKey\fR\|(3), \fBPEM_read_bio_PUBKEY\fR\|(3),
\&\fBPEM_read_PrivateKey\fR\|(3) and \fBPEM_read_PUBKEY\fR\|(3)
.IP \(bu 4
\&\fBPEM_write_bio_PrivateKey\fR\|(3), \fBPEM_write_bio_PUBKEY\fR\|(3), \fBPEM_write_PrivateKey\fR\|(3)
and \fBPEM_write_PUBKEY\fR\|(3)
.IP \(bu 4
\&\fBPEM_X509_INFO_read_bio\fR\|(3) and \fBPEM_X509_INFO_read\fR\|(3)
.IP \(bu 4
\&\fBPKCS12_add_key\fR\|(3), \fBPKCS12_add_safe\fR\|(3), \fBPKCS12_add_safes\fR\|(3),
\&\fBPKCS12_create\fR\|(3), \fBPKCS12_decrypt_skey\fR\|(3), \fBPKCS12_init\fR\|(3), \fBPKCS12_item_decrypt_d2i\fR\|(3),
\&\fBPKCS12_item_i2d_encrypt\fR\|(3), \fBPKCS12_key_gen_asc\fR\|(3), \fBPKCS12_key_gen_uni\fR\|(3),
\&\fBPKCS12_key_gen_utf8\fR\|(3), \fBPKCS12_pack_p7encdata\fR\|(3), \fBPKCS12_pbe_crypt\fR\|(3),
\&\fBPKCS12_PBE_keyivgen\fR\|(3), \fBPKCS12_SAFEBAG_create_pkcs8_encrypt\fR\|(3)
.IP \(bu 4
\&\fBPKCS5_pbe_set0_algor\fR\|(3), \fBPKCS5_pbe_set\fR\|(3), \fBPKCS5_pbe2_set_iv\fR\|(3),
\&\fBPKCS5_pbkdf2_set\fR\|(3) and \fBPKCS5_v2_scrypt_keyivgen\fR\|(3)
.IP \(bu 4
\&\fBPKCS7_encrypt\fR\|(3), \fBPKCS7_new\fR\|(3) and \fBPKCS7_sign\fR\|(3)
.IP \(bu 4
\&\fBPKCS8_decrypt\fR\|(3), \fBPKCS8_encrypt\fR\|(3) and \fBPKCS8_set0_pbe\fR\|(3)
.IP \(bu 4
\&\fBRAND_bytes\fR\|(3) and \fBRAND_priv_bytes\fR\|(3)
.IP \(bu 4
\&\fBSMIME_write_ASN1\fR\|(3)
.IP \(bu 4
\&\fBSSL_load_client_CA_file\fR\|(3)
.IP \(bu 4
\&\fBSSL_CTX_new\fR\|(3)
.IP \(bu 4
\&\fBTS_RESP_CTX_new\fR\|(3)
.IP \(bu 4
\&\fBX509_CRL_new\fR\|(3)
.IP \(bu 4
\&\fBX509_load_cert_crl_file\fR\|(3) and \fBX509_load_cert_file\fR\|(3)
.IP \(bu 4
\&\fBX509_LOOKUP_by_subject\fR\|(3) and \fBX509_LOOKUP_ctrl\fR\|(3)
.IP \(bu 4
\&\fBX509_NAME_hash\fR\|(3)
.IP \(bu 4
\&\fBX509_new\fR\|(3)
.IP \(bu 4
\&\fBX509_REQ_new\fR\|(3) and \fBX509_REQ_verify\fR\|(3)
.IP \(bu 4
\&\fBX509_STORE_CTX_new\fR\|(3), \fBX509_STORE_set_default_paths\fR\|(3), \fBX509_STORE_load_file\fR\|(3),
\&\fBX509_STORE_load_locations\fR\|(3) and \fBX509_STORE_load_store\fR\|(3)
.PP
New functions that use a Library context
.IX Subsection "New functions that use a Library context"
.PP
The following functions can be passed a library context if required.
Passing NULL will use the default library context.
.IP \(bu 4
\&\fBBIO_new_from_core_bio\fR\|(3)
.IP \(bu 4
\&\fBEVP_ASYM_CIPHER_fetch\fR\|(3) and \fBEVP_ASYM_CIPHER_do_all_provided\fR\|(3)
.IP \(bu 4
\&\fBEVP_CIPHER_fetch\fR\|(3) and \fBEVP_CIPHER_do_all_provided\fR\|(3)
.IP \(bu 4
\&\fBEVP_default_properties_enable_fips\fR\|(3) and
\&\fBEVP_default_properties_is_fips_enabled\fR\|(3)
.IP \(bu 4
\&\fBEVP_KDF_fetch\fR\|(3) and \fBEVP_KDF_do_all_provided\fR\|(3)
.IP \(bu 4
\&\fBEVP_KEM_fetch\fR\|(3) and \fBEVP_KEM_do_all_provided\fR\|(3)
.IP \(bu 4
\&\fBEVP_KEYEXCH_fetch\fR\|(3) and \fBEVP_KEYEXCH_do_all_provided\fR\|(3)
.IP \(bu 4
\&\fBEVP_KEYMGMT_fetch\fR\|(3) and \fBEVP_KEYMGMT_do_all_provided\fR\|(3)
.IP \(bu 4
\&\fBEVP_MAC_fetch\fR\|(3) and \fBEVP_MAC_do_all_provided\fR\|(3)
.IP \(bu 4
\&\fBEVP_MD_fetch\fR\|(3) and \fBEVP_MD_do_all_provided\fR\|(3)
.IP \(bu 4
\&\fBEVP_PKEY_CTX_new_from_pkey\fR\|(3)
.IP \(bu 4
\&\fBEVP_PKEY_Q_keygen\fR\|(3)
.IP \(bu 4
\&\fBEVP_Q_mac\fR\|(3) and \fBEVP_Q_digest\fR\|(3)
.IP \(bu 4
\&\fBEVP_RAND\fR\|(3) and \fBEVP_RAND_do_all_provided\fR\|(3)
.IP \(bu 4
\&\fBEVP_set_default_properties\fR\|(3)
.IP \(bu 4
\&\fBEVP_SIGNATURE_fetch\fR\|(3) and \fBEVP_SIGNATURE_do_all_provided\fR\|(3)
.IP \(bu 4
\&\fBOSSL_CMP_CTX_new\fR\|(3) and \fBOSSL_CMP_SRV_CTX_new\fR\|(3)
.IP \(bu 4
\&\fBOSSL_CRMF_ENCRYPTEDVALUE_get1_encCert\fR\|(3)
.IP \(bu 4
\&\fBOSSL_CRMF_MSG_create_popo\fR\|(3) and \fBOSSL_CRMF_MSGS_verify_popo\fR\|(3)
.IP \(bu 4
\&\fBOSSL_CRMF_pbm_new\fR\|(3) and \fBOSSL_CRMF_pbmp_new\fR\|(3)
.IP \(bu 4
\&\fBOSSL_DECODER_CTX_add_extra\fR\|(3) and \fBOSSL_DECODER_CTX_new_for_pkey\fR\|(3)
.IP \(bu 4
\&\fBOSSL_DECODER_fetch\fR\|(3) and \fBOSSL_DECODER_do_all_provided\fR\|(3)
.IP \(bu 4
\&\fBOSSL_ENCODER_CTX_add_extra\fR\|(3)
.IP \(bu 4
\&\fBOSSL_ENCODER_fetch\fR\|(3) and \fBOSSL_ENCODER_do_all_provided\fR\|(3)
.IP \(bu 4
\&\fBOSSL_LIB_CTX_free\fR\|(3), \fBOSSL_LIB_CTX_load_config\fR\|(3) and \fBOSSL_LIB_CTX_set0_default\fR\|(3)
.IP \(bu 4
\&\fBOSSL_PROVIDER_add_builtin\fR\|(3), \fBOSSL_PROVIDER_available\fR\|(3),
\&\fBOSSL_PROVIDER_do_all\fR\|(3), \fBOSSL_PROVIDER_load\fR\|(3),
\&\fBOSSL_PROVIDER_set_default_search_path\fR\|(3) and \fBOSSL_PROVIDER_try_load\fR\|(3)
.IP \(bu 4
\&\fBOSSL_SELF_TEST_get_callback\fR\|(3) and \fBOSSL_SELF_TEST_set_callback\fR\|(3)
.IP \(bu 4
\&\fBOSSL_STORE_attach\fR\|(3)
.IP \(bu 4
\&\fBOSSL_STORE_LOADER_fetch\fR\|(3) and \fBOSSL_STORE_LOADER_do_all_provided\fR\|(3)
.IP \(bu 4
\&\fBRAND_get0_primary\fR\|(3), \fBRAND_get0_private\fR\|(3), \fBRAND_get0_public\fR\|(3),
\&\fBRAND_set_DRBG_type\fR\|(3) and \fBRAND_set_seed_source_type\fR\|(3)
.PP
\fIProviders\fR
.IX Subsection "Providers"
.PP
Providers are described in detail here "Providers" in \fBcrypto\fR\|(7).
See also "OPENSSL PROVIDERS" in \fBcrypto\fR\|(7).
.PP
\fIFetching algorithms and property queries\fR
.IX Subsection "Fetching algorithms and property queries"
.PP
Implicit and Explicit Fetching is described in detail here
"ALGORITHM FETCHING" in \fBcrypto\fR\|(7).
.PP
\fIMapping EVP controls and flags to provider \fR\f(BIOSSL_PARAM\fR\fI\|(3) parameters\fR
.IX Subsection "Mapping EVP controls and flags to provider OSSL_PARAM parameters"
.PP
The existing functions for controls (such as \fBEVP_CIPHER_CTX_ctrl\fR\|(3)) and
manipulating flags (such as \fBEVP_MD_CTX_set_flags\fR\|(3))internally use
\&\fBOSSL_PARAMS\fR to pass information to/from provider objects.
See \fBOSSL_PARAM\fR\|(3) for additional information related to parameters.
.PP
For ciphers see "CONTROLS" in \fBEVP_EncryptInit\fR\|(3), "FLAGS" in \fBEVP_EncryptInit\fR\|(3) and
"PARAMETERS" in \fBEVP_EncryptInit\fR\|(3).
.PP
For digests see "CONTROLS" in \fBEVP_DigestInit\fR\|(3), "FLAGS" in \fBEVP_DigestInit\fR\|(3) and
"PARAMETERS" in \fBEVP_DigestInit\fR\|(3).
.PP
\fIDeprecation of Low Level Functions\fR
.IX Subsection "Deprecation of Low Level Functions"
.PP
A significant number of APIs have been deprecated in OpenSSL 3.0.
This section describes some common categories of deprecations.
See "Deprecated function mappings" for the list of deprecated functions
that refer to these categories.
.PP
Providers are a replacement for engines and low-level method overrides
.IX Subsection "Providers are a replacement for engines and low-level method overrides"
.PP
Any accessor that uses an ENGINE is deprecated (such as \fBEVP_PKEY_set1_engine()\fR).
Applications using engines should instead use providers.
.PP
Before providers were added algorithms were overridden by changing the methods
used by algorithms. All these methods such as \fBRSA_new_method()\fR and \fBRSA_meth_new()\fR
are now deprecated and can be replaced by using providers instead.
.PP
Deprecated i2d and d2i functions for low-level key types
.IX Subsection "Deprecated i2d and d2i functions for low-level key types"
.PP
Any i2d and d2i functions such as \fBd2i_DHparams()\fR that take a low-level key type
have been deprecated. Applications should instead use the \fBOSSL_DECODER\fR\|(3) and
\&\fBOSSL_ENCODER\fR\|(3) APIs to read and write files.
See "Migration" in \fBd2i_RSAPrivateKey\fR\|(3) for further details.
.PP
Deprecated low-level key object getters and setters
.IX Subsection "Deprecated low-level key object getters and setters"
.PP
Applications that set or get low-level key objects (such as \fBEVP_PKEY_set1_DH()\fR
or \fBEVP_PKEY_get0()\fR) should instead use the OSSL_ENCODER
(See \fBOSSL_ENCODER_to_bio\fR\|(3)) or OSSL_DECODER (See \fBOSSL_DECODER_from_bio\fR\|(3))
APIs, or alternatively use \fBEVP_PKEY_fromdata\fR\|(3) or \fBEVP_PKEY_todata\fR\|(3).
.PP
Deprecated low-level key parameter getters
.IX Subsection "Deprecated low-level key parameter getters"
.PP
Functions that access low-level objects directly such as \fBRSA_get0_n\fR\|(3) are now
deprecated. Applications should use one of \fBEVP_PKEY_get_bn_param\fR\|(3),
\&\fBEVP_PKEY_get_int_param\fR\|(3), l<\fBEVP_PKEY_get_size_t_param\fR\|(3)>,
\&\fBEVP_PKEY_get_utf8_string_param\fR\|(3), \fBEVP_PKEY_get_octet_string_param\fR\|(3) or
\&\fBEVP_PKEY_get_params\fR\|(3) to access fields from an EVP_PKEY.
Gettable parameters are listed in "Common RSA parameters" in \fBEVP_PKEY\-RSA\fR\|(7),
"DH parameters" in \fBEVP_PKEY\-DH\fR\|(7), "DSA parameters" in \fBEVP_PKEY\-DSA\fR\|(7),
"FFC parameters" in \fBEVP_PKEY\-FFC\fR\|(7), "Common EC parameters" in \fBEVP_PKEY\-EC\fR\|(7) and
"Common X25519, X448, ED25519 and ED448 parameters" in \fBEVP_PKEY\-X25519\fR\|(7).
Applications may also use \fBEVP_PKEY_todata\fR\|(3) to return all fields.
.PP
Deprecated low-level key parameter setters
.IX Subsection "Deprecated low-level key parameter setters"
.PP
Functions that access low-level objects directly such as \fBRSA_set0_crt_params\fR\|(3)
are now deprecated. Applications should use \fBEVP_PKEY_fromdata\fR\|(3) to create
new keys from user provided key data. Keys should be immutable once they are
created, so if required the user may use \fBEVP_PKEY_todata\fR\|(3), \fBOSSL_PARAM_merge\fR\|(3),
and \fBEVP_PKEY_fromdata\fR\|(3) to create a modified key.
See "Examples" in \fBEVP_PKEY\-DH\fR\|(7) for more information.
See "Deprecated low-level key generation functions" for information on
generating a key using parameters.
.PP
Deprecated low-level object creation
.IX Subsection "Deprecated low-level object creation"
.PP
Low-level objects were created using methods such as \fBRSA_new\fR\|(3),
\&\fBRSA_up_ref\fR\|(3) and \fBRSA_free\fR\|(3). Applications should instead use the
high-level EVP_PKEY APIs, e.g. \fBEVP_PKEY_new\fR\|(3), \fBEVP_PKEY_up_ref\fR\|(3) and
\&\fBEVP_PKEY_free\fR\|(3).
See also \fBEVP_PKEY_CTX_new_from_name\fR\|(3) and \fBEVP_PKEY_CTX_new_from_pkey\fR\|(3).
.PP
EVP_PKEYs may be created in a variety of ways:
See also "Deprecated low-level key generation functions",
"Deprecated low-level key reading and writing functions" and
"Deprecated low-level key parameter setters".
.PP
Deprecated low-level encryption functions
.IX Subsection "Deprecated low-level encryption functions"
.PP
Low-level encryption functions such as \fBAES_encrypt\fR\|(3) and \fBAES_decrypt\fR\|(3)
have been informally discouraged from use for a long time. Applications should
instead use the high level EVP APIs \fBEVP_EncryptInit_ex\fR\|(3),
\&\fBEVP_EncryptUpdate\fR\|(3), and \fBEVP_EncryptFinal_ex\fR\|(3) or
\&\fBEVP_DecryptInit_ex\fR\|(3), \fBEVP_DecryptUpdate\fR\|(3) and \fBEVP_DecryptFinal_ex\fR\|(3).
.PP
Deprecated low-level digest functions
.IX Subsection "Deprecated low-level digest functions"
.PP
Use of low-level digest functions such as \fBSHA1_Init\fR\|(3) have been
informally discouraged from use for a long time.  Applications should instead
use the the high level EVP APIs \fBEVP_DigestInit_ex\fR\|(3), \fBEVP_DigestUpdate\fR\|(3)
and \fBEVP_DigestFinal_ex\fR\|(3), or the quick one-shot \fBEVP_Q_digest\fR\|(3).
.PP
Note that the functions \fBSHA1\fR\|(3), \fBSHA224\fR\|(3), \fBSHA256\fR\|(3), \fBSHA384\fR\|(3)
and \fBSHA512\fR\|(3) have changed to macros that use \fBEVP_Q_digest\fR\|(3).
.PP
Deprecated low-level signing functions
.IX Subsection "Deprecated low-level signing functions"
.PP
Use of low-level signing functions such as \fBDSA_sign\fR\|(3) have been
informally discouraged for a long time. Instead applications should use
\&\fBEVP_DigestSign\fR\|(3) and \fBEVP_DigestVerify\fR\|(3).
See also \fBEVP_SIGNATURE\-RSA\fR\|(7), \fBEVP_SIGNATURE\-DSA\fR\|(7),
\&\fBEVP_SIGNATURE\-ECDSA\fR\|(7) and \fBEVP_SIGNATURE\-ED25519\fR\|(7).
.PP
Deprecated low-level MAC functions
.IX Subsection "Deprecated low-level MAC functions"
.PP
Low-level mac functions such as \fBCMAC_Init\fR\|(3) are deprecated.
Applications should instead use the new \fBEVP_MAC\fR\|(3) interface, using
\&\fBEVP_MAC_CTX_new\fR\|(3), \fBEVP_MAC_CTX_free\fR\|(3), \fBEVP_MAC_init\fR\|(3),
\&\fBEVP_MAC_update\fR\|(3) and \fBEVP_MAC_final\fR\|(3) or the single-shot MAC function
\&\fBEVP_Q_mac\fR\|(3).
See \fBEVP_MAC\fR\|(3), \fBEVP_MAC\-HMAC\fR\|(7), \fBEVP_MAC\-CMAC\fR\|(7), \fBEVP_MAC\-GMAC\fR\|(7),
\&\fBEVP_MAC\-KMAC\fR\|(7), \fBEVP_MAC\-BLAKE2\fR\|(7), \fBEVP_MAC\-Poly1305\fR\|(7) and
\&\fBEVP_MAC\-Siphash\fR\|(7) for additional information.
.PP
Note that the one-shot method \fBHMAC()\fR is still available for compatibility purposes,
but this can also be replaced by using EVP_Q_MAC if a library context is required.
.PP
Deprecated low-level validation functions
.IX Subsection "Deprecated low-level validation functions"
.PP
Low-level validation functions such as \fBDH_check\fR\|(3) have been informally
discouraged from use for a long time. Applications should instead use the high-level
EVP_PKEY APIs such as \fBEVP_PKEY_check\fR\|(3), \fBEVP_PKEY_param_check\fR\|(3),
\&\fBEVP_PKEY_param_check_quick\fR\|(3), \fBEVP_PKEY_public_check\fR\|(3),
\&\fBEVP_PKEY_public_check_quick\fR\|(3), \fBEVP_PKEY_private_check\fR\|(3),
and \fBEVP_PKEY_pairwise_check\fR\|(3).
.PP
Deprecated low-level key exchange functions
.IX Subsection "Deprecated low-level key exchange functions"
.PP
Many low-level functions have been informally discouraged from use for a long
time. Applications should instead use \fBEVP_PKEY_derive\fR\|(3).
See \fBEVP_KEYEXCH\-DH\fR\|(7), \fBEVP_KEYEXCH\-ECDH\fR\|(7) and \fBEVP_KEYEXCH\-X25519\fR\|(7).
.PP
Deprecated low-level key generation functions
.IX Subsection "Deprecated low-level key generation functions"
.PP
Many low-level functions have been informally discouraged from use for a long
time. Applications should instead use \fBEVP_PKEY_keygen_init\fR\|(3) and
\&\fBEVP_PKEY_generate\fR\|(3) as described in \fBEVP_PKEY\-DSA\fR\|(7), \fBEVP_PKEY\-DH\fR\|(7),
\&\fBEVP_PKEY\-RSA\fR\|(7), \fBEVP_PKEY\-EC\fR\|(7) and \fBEVP_PKEY\-X25519\fR\|(7).
The 'quick' one-shot function \fBEVP_PKEY_Q_keygen\fR\|(3) and macros for the most
common cases: <\fBEVP_RSA_gen\fR\|(3)> and \fBEVP_EC_gen\fR\|(3) may also be used.
.PP
Deprecated low-level key reading and writing functions
.IX Subsection "Deprecated low-level key reading and writing functions"
.PP
Use of low-level objects (such as DSA) has been informally discouraged from use
for a long time. Functions to read and write these low-level objects (such as
\&\fBPEM_read_DSA_PUBKEY()\fR) should be replaced. Applications should instead use
\&\fBOSSL_ENCODER_to_bio\fR\|(3) and \fBOSSL_DECODER_from_bio\fR\|(3).
.PP
Deprecated low-level key printing functions
.IX Subsection "Deprecated low-level key printing functions"
.PP
Use of low-level objects (such as DSA) has been informally discouraged from use
for a long time. Functions to print these low-level objects such as
\&\fBDSA_print()\fR should be replaced with the equivalent EVP_PKEY functions.
Application should use one of \fBEVP_PKEY_print_public\fR\|(3),
\&\fBEVP_PKEY_print_private\fR\|(3), \fBEVP_PKEY_print_params\fR\|(3),
\&\fBEVP_PKEY_print_public_fp\fR\|(3), \fBEVP_PKEY_print_private_fp\fR\|(3) or
\&\fBEVP_PKEY_print_params_fp\fR\|(3). Note that internally these use
\&\fBOSSL_ENCODER_to_bio\fR\|(3) and \fBOSSL_DECODER_from_bio\fR\|(3).
.PP
\fIDeprecated function mappings\fR
.IX Subsection "Deprecated function mappings"
.PP
The following functions have been deprecated in 3.0.
.IP \(bu 4
\&\fBAES_bi_ige_encrypt()\fR and \fBAES_ige_encrypt()\fR
.Sp
There is no replacement for the IGE functions. New code should not use these modes.
These undocumented functions were never integrated into the EVP layer.
They implemented the AES Infinite Garble Extension (IGE) mode and AES
Bi-directional IGE mode. These modes were never formally standardised and
usage of these functions is believed to be very small. In particular
\&\fBAES_bi_ige_encrypt()\fR has a known bug. It accepts 2 AES keys, but only one
is ever used. The security implications are believed to be minimal, but
this issue was never fixed for backwards compatibility reasons.
.IP \(bu 4
\&\fBAES_encrypt()\fR, \fBAES_decrypt()\fR, \fBAES_set_encrypt_key()\fR, \fBAES_set_decrypt_key()\fR,
\&\fBAES_cbc_encrypt()\fR, \fBAES_cfb128_encrypt()\fR, \fBAES_cfb1_encrypt()\fR, \fBAES_cfb8_encrypt()\fR,
\&\fBAES_ecb_encrypt()\fR, \fBAES_ofb128_encrypt()\fR
.IP \(bu 4
\&\fBAES_unwrap_key()\fR, \fBAES_wrap_key()\fR
.Sp
See "Deprecated low-level encryption functions"
.IP \(bu 4
\&\fBAES_options()\fR
.Sp
There is no replacement. It returned a string indicating if the AES code was unrolled.
.IP \(bu 4
\&\fBASN1_digest()\fR, \fBASN1_sign()\fR, \fBASN1_verify()\fR
.Sp
There are no replacements. These old functions are not used, and could be
disabled with the macro NO_ASN1_OLD since OpenSSL 0.9.7.
.IP \(bu 4
\&\fBASN1_STRING_length_set()\fR
.Sp
Use \fBASN1_STRING_set\fR\|(3) or \fBASN1_STRING_set0\fR\|(3) instead.
This was a potentially unsafe function that could change the bounds of a
previously passed in pointer.
.IP \(bu 4
\&\fBBF_encrypt()\fR, \fBBF_decrypt()\fR, \fBBF_set_key()\fR, \fBBF_cbc_encrypt()\fR, \fBBF_cfb64_encrypt()\fR,
\&\fBBF_ecb_encrypt()\fR, \fBBF_ofb64_encrypt()\fR
.Sp
See "Deprecated low-level encryption functions".
The Blowfish algorithm has been moved to the Legacy Provider.
.IP \(bu 4
\&\fBBF_options()\fR
.Sp
There is no replacement. This option returned a constant string.
.IP \(bu 4
\&\fBBIO_get_callback()\fR, \fBBIO_set_callback()\fR, \fBBIO_debug_callback()\fR
.Sp
Use the respective non-deprecated \fB_ex()\fR functions.
.IP \(bu 4
\&\fBBN_is_prime_ex()\fR, \fBBN_is_prime_fasttest_ex()\fR
.Sp
Use \fBBN_check_prime\fR\|(3) which avoids possible misuse and always uses at least
64 rounds of the Miller-Rabin primality test.
.IP \(bu 4
\&\fBBN_pseudo_rand()\fR, \fBBN_pseudo_rand_range()\fR
.Sp
Use \fBBN_rand\fR\|(3) and \fBBN_rand_range\fR\|(3).
.IP \(bu 4
\&\fBBN_X931_derive_prime_ex()\fR, \fBBN_X931_generate_prime_ex()\fR, \fBBN_X931_generate_Xpq()\fR
.Sp
There are no replacements for these low-level functions. They were used internally
by \fBRSA_X931_derive_ex()\fR and \fBRSA_X931_generate_key_ex()\fR which are also deprecated.
Use \fBEVP_PKEY_keygen\fR\|(3) instead.
.IP \(bu 4
\&\fBCamellia_encrypt()\fR, \fBCamellia_decrypt()\fR, \fBCamellia_set_key()\fR,
\&\fBCamellia_cbc_encrypt()\fR, \fBCamellia_cfb128_encrypt()\fR, \fBCamellia_cfb1_encrypt()\fR,
\&\fBCamellia_cfb8_encrypt()\fR, \fBCamellia_ctr128_encrypt()\fR, \fBCamellia_ecb_encrypt()\fR,
\&\fBCamellia_ofb128_encrypt()\fR
.Sp
See "Deprecated low-level encryption functions".
.IP \(bu 4
\&\fBCAST_encrypt()\fR, \fBCAST_decrypt()\fR, \fBCAST_set_key()\fR, \fBCAST_cbc_encrypt()\fR,
\&\fBCAST_cfb64_encrypt()\fR, \fBCAST_ecb_encrypt()\fR, \fBCAST_ofb64_encrypt()\fR
.Sp
See "Deprecated low-level encryption functions".
The CAST algorithm has been moved to the Legacy Provider.
.IP \(bu 4
\&\fBCMAC_CTX_new()\fR, \fBCMAC_CTX_cleanup()\fR, \fBCMAC_CTX_copy()\fR, \fBCMAC_CTX_free()\fR,
\&\fBCMAC_CTX_get0_cipher_ctx()\fR
.Sp
See "Deprecated low-level MAC functions".
.IP \(bu 4
\&\fBCMAC_Init()\fR, \fBCMAC_Update()\fR, \fBCMAC_Final()\fR, \fBCMAC_resume()\fR
.Sp
See "Deprecated low-level MAC functions".
.IP \(bu 4
\&\fBCRYPTO_mem_ctrl()\fR, \fBCRYPTO_mem_debug_free()\fR, \fBCRYPTO_mem_debug_malloc()\fR,
\&\fBCRYPTO_mem_debug_pop()\fR, \fBCRYPTO_mem_debug_push()\fR, \fBCRYPTO_mem_debug_realloc()\fR,
\&\fBCRYPTO_mem_leaks()\fR, \fBCRYPTO_mem_leaks_cb()\fR, \fBCRYPTO_mem_leaks_fp()\fR,
\&\fBCRYPTO_set_mem_debug()\fR
.Sp
Memory-leak checking has been deprecated in favor of more modern development
tools, such as compiler memory and leak sanitizers or Valgrind.
.IP \(bu 4
\&\fBCRYPTO_cts128_encrypt_block()\fR, \fBCRYPTO_cts128_encrypt()\fR,
\&\fBCRYPTO_cts128_decrypt_block()\fR, \fBCRYPTO_cts128_decrypt()\fR,
\&\fBCRYPTO_nistcts128_encrypt_block()\fR, \fBCRYPTO_nistcts128_encrypt()\fR,
\&\fBCRYPTO_nistcts128_decrypt_block()\fR, \fBCRYPTO_nistcts128_decrypt()\fR
.Sp
Use the higher level functions \fBEVP_CipherInit_ex2()\fR, \fBEVP_CipherUpdate()\fR and
\&\fBEVP_CipherFinal_ex()\fR instead.
See the "cts_mode" parameter in
"Gettable and Settable EVP_CIPHER_CTX parameters" in \fBEVP_EncryptInit\fR\|(3).
See "EXAMPLES" in \fBEVP_EncryptInit\fR\|(3) for a AES\-256\-CBC\-CTS example.
.IP \(bu 4
\&\fBd2i_DHparams()\fR, \fBd2i_DHxparams()\fR, \fBd2i_DSAparams()\fR, \fBd2i_DSAPrivateKey()\fR,
\&\fBd2i_DSAPrivateKey_bio()\fR, \fBd2i_DSAPrivateKey_fp()\fR, \fBd2i_DSA_PUBKEY()\fR,
\&\fBd2i_DSA_PUBKEY_bio()\fR, \fBd2i_DSA_PUBKEY_fp()\fR, \fBd2i_DSAPublicKey()\fR,
\&\fBd2i_ECParameters()\fR, \fBd2i_ECPrivateKey()\fR, \fBd2i_ECPrivateKey_bio()\fR,
\&\fBd2i_ECPrivateKey_fp()\fR, \fBd2i_EC_PUBKEY()\fR, \fBd2i_EC_PUBKEY_bio()\fR,
\&\fBd2i_EC_PUBKEY_fp()\fR, \fBo2i_ECPublicKey()\fR, \fBd2i_RSAPrivateKey()\fR,
\&\fBd2i_RSAPrivateKey_bio()\fR, \fBd2i_RSAPrivateKey_fp()\fR, \fBd2i_RSA_PUBKEY()\fR,
\&\fBd2i_RSA_PUBKEY_bio()\fR, \fBd2i_RSA_PUBKEY_fp()\fR, \fBd2i_RSAPublicKey()\fR,
\&\fBd2i_RSAPublicKey_bio()\fR, \fBd2i_RSAPublicKey_fp()\fR
.Sp
See "Deprecated i2d and d2i functions for low-level key types"
.IP \(bu 4
\&\fBDES_crypt()\fR, \fBDES_fcrypt()\fR, \fBDES_encrypt1()\fR, \fBDES_encrypt2()\fR, \fBDES_encrypt3()\fR,
\&\fBDES_decrypt3()\fR, \fBDES_ede3_cbc_encrypt()\fR, \fBDES_ede3_cfb64_encrypt()\fR,
\&\fBDES_ede3_cfb_encrypt()\fR,\fBDES_ede3_ofb64_encrypt()\fR,
\&\fBDES_ecb_encrypt()\fR, \fBDES_ecb3_encrypt()\fR, \fBDES_ofb64_encrypt()\fR, \fBDES_ofb_encrypt()\fR,
DES_cfb64_encrypt \fBDES_cfb_encrypt()\fR, \fBDES_cbc_encrypt()\fR, \fBDES_ncbc_encrypt()\fR,
\&\fBDES_pcbc_encrypt()\fR, \fBDES_xcbc_encrypt()\fR, \fBDES_cbc_cksum()\fR, \fBDES_quad_cksum()\fR,
\&\fBDES_check_key_parity()\fR, \fBDES_is_weak_key()\fR, \fBDES_key_sched()\fR, \fBDES_options()\fR,
\&\fBDES_random_key()\fR, \fBDES_set_key()\fR, \fBDES_set_key_checked()\fR, \fBDES_set_key_unchecked()\fR,
\&\fBDES_set_odd_parity()\fR, \fBDES_string_to_2keys()\fR, \fBDES_string_to_key()\fR
.Sp
See "Deprecated low-level encryption functions".
Algorithms for "DESX-CBC", "DES-ECB", "DES-CBC", "DES-OFB", "DES-CFB",
"DES\-CFB1" and "DES\-CFB8" have been moved to the Legacy Provider.
.IP \(bu 4
\&\fBDH_bits()\fR, \fBDH_security_bits()\fR, \fBDH_size()\fR
.Sp
Use \fBEVP_PKEY_get_bits\fR\|(3), \fBEVP_PKEY_get_security_bits\fR\|(3) and
\&\fBEVP_PKEY_get_size\fR\|(3).
.IP \(bu 4
\&\fBDH_check()\fR, \fBDH_check_ex()\fR, \fBDH_check_params()\fR, \fBDH_check_params_ex()\fR,
\&\fBDH_check_pub_key()\fR, \fBDH_check_pub_key_ex()\fR
.Sp
See "Deprecated low-level validation functions"
.IP \(bu 4
\&\fBDH_clear_flags()\fR, \fBDH_test_flags()\fR, \fBDH_set_flags()\fR
.Sp
The \fBDH_FLAG_CACHE_MONT_P\fR flag has been deprecated without replacement.
The \fBDH_FLAG_TYPE_DH\fR and \fBDH_FLAG_TYPE_DHX\fR have been deprecated.
Use \fBEVP_PKEY_is_a()\fR to determine the type of a key.
There is no replacement for setting these flags.
.IP \(bu 4
\&\fBDH_compute_key()\fR \fBDH_compute_key_padded()\fR
.Sp
See "Deprecated low-level key exchange functions".
.IP \(bu 4
\&\fBDH_new()\fR, \fBDH_new_by_nid()\fR, \fBDH_free()\fR, \fBDH_up_ref()\fR
.Sp
See "Deprecated low-level object creation"
.IP \(bu 4
\&\fBDH_generate_key()\fR, \fBDH_generate_parameters_ex()\fR
.Sp
See "Deprecated low-level key generation functions".
.IP \(bu 4
\&\fBDH_get0_pqg()\fR, \fBDH_get0_p()\fR, \fBDH_get0_q()\fR, \fBDH_get0_g()\fR, \fBDH_get0_key()\fR,
\&\fBDH_get0_priv_key()\fR, \fBDH_get0_pub_key()\fR, \fBDH_get_length()\fR, \fBDH_get_nid()\fR
.Sp
See "Deprecated low-level key parameter getters"
.IP \(bu 4
\&\fBDH_get_1024_160()\fR, \fBDH_get_2048_224()\fR, \fBDH_get_2048_256()\fR
.Sp
Applications should instead set the \fBOSSL_PKEY_PARAM_GROUP_NAME\fR as specified in
"DH parameters" in \fBEVP_PKEY\-DH\fR\|(7)) to one of "dh_1024_160", "dh_2048_224" or
"dh_2048_256" when generating a DH key.
.IP \(bu 4
\&\fBDH_KDF_X9_42()\fR
.Sp
Applications should use \fBEVP_PKEY_CTX_set_dh_kdf_type\fR\|(3) instead.
.IP \(bu 4
\&\fBDH_get_default_method()\fR, \fBDH_get0_engine()\fR, DH_meth_*(), \fBDH_new_method()\fR,
\&\fBDH_OpenSSL()\fR, \fBDH_get_ex_data()\fR, \fBDH_set_default_method()\fR, \fBDH_set_method()\fR,
\&\fBDH_set_ex_data()\fR
.Sp
See "Providers are a replacement for engines and low-level method overrides"
.IP \(bu 4
\&\fBDHparams_print()\fR, \fBDHparams_print_fp()\fR
.Sp
See "Deprecated low-level key printing functions"
.IP \(bu 4
\&\fBDH_set0_key()\fR, \fBDH_set0_pqg()\fR, \fBDH_set_length()\fR
.Sp
See "Deprecated low-level key parameter setters"
.IP \(bu 4
\&\fBDSA_bits()\fR, \fBDSA_security_bits()\fR, \fBDSA_size()\fR
.Sp
Use \fBEVP_PKEY_get_bits\fR\|(3), \fBEVP_PKEY_get_security_bits\fR\|(3) and
\&\fBEVP_PKEY_get_size\fR\|(3).
.IP \(bu 4
\&\fBDHparams_dup()\fR, \fBDSA_dup_DH()\fR
.Sp
There is no direct replacement. Applications may use \fBEVP_PKEY_copy_parameters\fR\|(3)
and \fBEVP_PKEY_dup\fR\|(3) instead.
.IP \(bu 4
\&\fBDSA_generate_key()\fR, \fBDSA_generate_parameters_ex()\fR
.Sp
See "Deprecated low-level key generation functions".
.IP \(bu 4
\&\fBDSA_get0_engine()\fR, \fBDSA_get_default_method()\fR, \fBDSA_get_ex_data()\fR,
\&\fBDSA_get_method()\fR, DSA_meth_*(), \fBDSA_new_method()\fR, \fBDSA_OpenSSL()\fR,
\&\fBDSA_set_default_method()\fR, \fBDSA_set_ex_data()\fR, \fBDSA_set_method()\fR
.Sp
See "Providers are a replacement for engines and low-level method overrides".
.IP \(bu 4
\&\fBDSA_get0_p()\fR, \fBDSA_get0_q()\fR, \fBDSA_get0_g()\fR, \fBDSA_get0_pqg()\fR, \fBDSA_get0_key()\fR,
\&\fBDSA_get0_priv_key()\fR, \fBDSA_get0_pub_key()\fR
.Sp
See "Deprecated low-level key parameter getters".
.IP \(bu 4
\&\fBDSA_new()\fR, \fBDSA_free()\fR, \fBDSA_up_ref()\fR
.Sp
See "Deprecated low-level object creation"
.IP \(bu 4
\&\fBDSAparams_dup()\fR
.Sp
There is no direct replacement. Applications may use \fBEVP_PKEY_copy_parameters\fR\|(3)
and \fBEVP_PKEY_dup\fR\|(3) instead.
.IP \(bu 4
\&\fBDSAparams_print()\fR, \fBDSAparams_print_fp()\fR, \fBDSA_print()\fR, \fBDSA_print_fp()\fR
.Sp
See "Deprecated low-level key printing functions"
.IP \(bu 4
\&\fBDSA_set0_key()\fR, \fBDSA_set0_pqg()\fR
.Sp
See "Deprecated low-level key parameter setters"
.IP \(bu 4
\&\fBDSA_set_flags()\fR, \fBDSA_clear_flags()\fR, \fBDSA_test_flags()\fR
.Sp
The \fBDSA_FLAG_CACHE_MONT_P\fR flag has been deprecated without replacement.
.IP \(bu 4
\&\fBDSA_sign()\fR, \fBDSA_do_sign()\fR, \fBDSA_sign_setup()\fR, \fBDSA_verify()\fR, \fBDSA_do_verify()\fR
.Sp
See "Deprecated low-level signing functions".
.IP \(bu 4
\&\fBECDH_compute_key()\fR
.Sp
See "Deprecated low-level key exchange functions".
.IP \(bu 4
\&\fBECDH_KDF_X9_62()\fR
.Sp
Applications may either set this using the helper function
\&\fBEVP_PKEY_CTX_set_ecdh_kdf_type\fR\|(3) or by setting an \fBOSSL_PARAM\fR\|(3) using the
"kdf-type" as shown in "EXAMPLES" in \fBEVP_KEYEXCH\-ECDH\fR\|(7)
.IP \(bu 4
\&\fBECDSA_sign()\fR, \fBECDSA_sign_ex()\fR, \fBECDSA_sign_setup()\fR, \fBECDSA_do_sign()\fR,
\&\fBECDSA_do_sign_ex()\fR, \fBECDSA_verify()\fR, \fBECDSA_do_verify()\fR
.Sp
See "Deprecated low-level signing functions".
.IP \(bu 4
\&\fBECDSA_size()\fR
.Sp
Applications should use \fBEVP_PKEY_get_size\fR\|(3).
.IP \(bu 4
\&\fBEC_GF2m_simple_method()\fR, \fBEC_GFp_mont_method()\fR, \fBEC_GFp_nist_method()\fR,
\&\fBEC_GFp_nistp224_method()\fR, \fBEC_GFp_nistp256_method()\fR, \fBEC_GFp_nistp521_method()\fR,
\&\fBEC_GFp_simple_method()\fR
.Sp
There are no replacements for these functions. Applications should rely on the
library automatically assigning a suitable method internally when an EC_GROUP
is constructed.
.IP \(bu 4
\&\fBEC_GROUP_clear_free()\fR
.Sp
Use \fBEC_GROUP_free\fR\|(3) instead.
.IP \(bu 4
\&\fBEC_GROUP_get_curve_GF2m()\fR, \fBEC_GROUP_get_curve_GFp()\fR, \fBEC_GROUP_set_curve_GF2m()\fR,
\&\fBEC_GROUP_set_curve_GFp()\fR
.Sp
Applications should use \fBEC_GROUP_get_curve\fR\|(3) and \fBEC_GROUP_set_curve\fR\|(3).
.IP \(bu 4
\&\fBEC_GROUP_have_precompute_mult()\fR, \fBEC_GROUP_precompute_mult()\fR,
\&\fBEC_KEY_precompute_mult()\fR
.Sp
These functions are not widely used. Applications should instead switch to
named curves which OpenSSL has hardcoded lookup tables for.
.IP \(bu 4
\&\fBEC_GROUP_new()\fR, \fBEC_GROUP_method_of()\fR, \fBEC_POINT_method_of()\fR
.Sp
EC_METHOD is now an internal-only concept and a suitable EC_METHOD is assigned
internally without application intervention.
Users of \fBEC_GROUP_new()\fR should switch to a different suitable constructor.
.IP \(bu 4
\&\fBEC_KEY_can_sign()\fR
.Sp
Applications should use \fBEVP_PKEY_can_sign\fR\|(3) instead.
.IP \(bu 4
\&\fBEC_KEY_check_key()\fR
.Sp
See "Deprecated low-level validation functions"
.IP \(bu 4
\&\fBEC_KEY_set_flags()\fR, \fBEC_KEY_get_flags()\fR, \fBEC_KEY_clear_flags()\fR
.Sp
See "Common EC parameters" in \fBEVP_PKEY\-EC\fR\|(7) which handles flags as separate
parameters for \fBOSSL_PKEY_PARAM_EC_POINT_CONVERSION_FORMAT\fR,
\&\fBOSSL_PKEY_PARAM_EC_GROUP_CHECK_TYPE\fR, \fBOSSL_PKEY_PARAM_EC_ENCODING\fR,
\&\fBOSSL_PKEY_PARAM_USE_COFACTOR_ECDH\fR and
\&\fBOSSL_PKEY_PARAM_EC_INCLUDE_PUBLIC\fR.
See also "EXAMPLES" in \fBEVP_PKEY\-EC\fR\|(7)
.IP \(bu 4
\&\fBEC_KEY_dup()\fR, \fBEC_KEY_copy()\fR
.Sp
There is no direct replacement. Applications may use \fBEVP_PKEY_copy_parameters\fR\|(3)
and \fBEVP_PKEY_dup\fR\|(3) instead.
.IP \(bu 4
\&\fBEC_KEY_decoded_from_explicit_params()\fR
.Sp
There is no replacement.
.IP \(bu 4
\&\fBEC_KEY_generate_key()\fR
.Sp
See "Deprecated low-level key generation functions".
.IP \(bu 4
\&\fBEC_KEY_get0_group()\fR, \fBEC_KEY_get0_private_key()\fR, \fBEC_KEY_get0_public_key()\fR,
\&\fBEC_KEY_get_conv_form()\fR, \fBEC_KEY_get_enc_flags()\fR
.Sp
See "Deprecated low-level key parameter getters".
.IP \(bu 4
\&\fBEC_KEY_get0_engine()\fR, \fBEC_KEY_get_default_method()\fR, \fBEC_KEY_get_method()\fR,
\&\fBEC_KEY_new_method()\fR, \fBEC_KEY_get_ex_data()\fR, \fBEC_KEY_OpenSSL()\fR,
\&\fBEC_KEY_set_ex_data()\fR, \fBEC_KEY_set_default_method()\fR, EC_KEY_METHOD_*(),
\&\fBEC_KEY_set_method()\fR
.Sp
See "Providers are a replacement for engines and low-level method overrides"
.IP \(bu 4
\&\fBEC_METHOD_get_field_type()\fR
.Sp
Use \fBEC_GROUP_get_field_type\fR\|(3) instead.
See "Providers are a replacement for engines and low-level method overrides"
.IP \(bu 4
\&\fBEC_KEY_key2buf()\fR, \fBEC_KEY_oct2key()\fR, \fBEC_KEY_oct2priv()\fR, \fBEC_KEY_priv2buf()\fR,
\&\fBEC_KEY_priv2oct()\fR
.Sp
There are no replacements for these.
.IP \(bu 4
\&\fBEC_KEY_new()\fR, \fBEC_KEY_new_by_curve_name()\fR, \fBEC_KEY_free()\fR, \fBEC_KEY_up_ref()\fR
.Sp
See "Deprecated low-level object creation"
.IP \(bu 4
\&\fBEC_KEY_print()\fR, \fBEC_KEY_print_fp()\fR
.Sp
See "Deprecated low-level key printing functions"
.IP \(bu 4
\&\fBEC_KEY_set_asn1_flag()\fR, \fBEC_KEY_set_conv_form()\fR, \fBEC_KEY_set_enc_flags()\fR
.Sp
See "Deprecated low-level key parameter setters".
.IP \(bu 4
\&\fBEC_KEY_set_group()\fR, \fBEC_KEY_set_private_key()\fR, \fBEC_KEY_set_public_key()\fR,
\&\fBEC_KEY_set_public_key_affine_coordinates()\fR
.Sp
See "Deprecated low-level key parameter setters".
.IP \(bu 4
\&\fBECParameters_print()\fR, \fBECParameters_print_fp()\fR, \fBECPKParameters_print()\fR,
\&\fBECPKParameters_print_fp()\fR
.Sp
See "Deprecated low-level key printing functions"
.IP \(bu 4
\&\fBEC_POINT_bn2point()\fR, \fBEC_POINT_point2bn()\fR
.Sp
These functions were not particularly useful, since EC point serialization
formats are not individual big-endian integers.
.IP \(bu 4
\&\fBEC_POINT_get_affine_coordinates_GF2m()\fR, \fBEC_POINT_get_affine_coordinates_GFp()\fR,
\&\fBEC_POINT_set_affine_coordinates_GF2m()\fR, \fBEC_POINT_set_affine_coordinates_GFp()\fR
.Sp
Applications should use \fBEC_POINT_get_affine_coordinates\fR\|(3) and
\&\fBEC_POINT_set_affine_coordinates\fR\|(3) instead.
.IP \(bu 4
\&\fBEC_POINT_get_Jprojective_coordinates_GFp()\fR, \fBEC_POINT_set_Jprojective_coordinates_GFp()\fR
.Sp
These functions are not widely used. Applications should instead use the
\&\fBEC_POINT_set_affine_coordinates\fR\|(3) and \fBEC_POINT_get_affine_coordinates\fR\|(3)
functions.
.IP \(bu 4
\&\fBEC_POINT_make_affine()\fR, \fBEC_POINTs_make_affine()\fR
.Sp
There is no replacement. These functions were not widely used, and OpenSSL
automatically performs this conversion when needed.
.IP \(bu 4
\&\fBEC_POINT_set_compressed_coordinates_GF2m()\fR, \fBEC_POINT_set_compressed_coordinates_GFp()\fR
.Sp
Applications should use \fBEC_POINT_set_compressed_coordinates\fR\|(3) instead.
.IP \(bu 4
\&\fBEC_POINTs_mul()\fR
.Sp
This function is not widely used. Applications should instead use the
\&\fBEC_POINT_mul\fR\|(3) function.
.IP \(bu 4
\&\fBENGINE_*()\fR
.Sp
All engine functions are deprecated. An engine should be rewritten as a provider.
See "Providers are a replacement for engines and low-level method overrides".
.IP \(bu 4
\&\fBERR_load_*()\fR, \fBERR_func_error_string()\fR, \fBERR_get_error_line()\fR,
\&\fBERR_get_error_line_data()\fR, \fBERR_get_state()\fR
.Sp
OpenSSL now loads error strings automatically so these functions are not needed.
.IP \(bu 4
\&\fBERR_peek_error_line_data()\fR, \fBERR_peek_last_error_line_data()\fR
.Sp
The new functions are \fBERR_peek_error_func\fR\|(3), \fBERR_peek_last_error_func\fR\|(3),
\&\fBERR_peek_error_data\fR\|(3), \fBERR_peek_last_error_data\fR\|(3), \fBERR_get_error_all\fR\|(3),
\&\fBERR_peek_error_all\fR\|(3) and \fBERR_peek_last_error_all\fR\|(3).
Applications should use \fBERR_get_error_all\fR\|(3), or pick information
with ERR_peek functions and finish off with getting the error code by using
\&\fBERR_get_error\fR\|(3).
.IP \(bu 4
\&\fBEVP_CIPHER_CTX_iv()\fR, \fBEVP_CIPHER_CTX_iv_noconst()\fR, \fBEVP_CIPHER_CTX_original_iv()\fR
.Sp
Applications should instead use \fBEVP_CIPHER_CTX_get_updated_iv\fR\|(3),
\&\fBEVP_CIPHER_CTX_get_updated_iv\fR\|(3) and \fBEVP_CIPHER_CTX_get_original_iv\fR\|(3)
respectively.
See \fBEVP_CIPHER_CTX_get_original_iv\fR\|(3) for further information.
.IP \(bu 4
\&\fBEVP_CIPHER_meth_*()\fR, \fBEVP_MD_CTX_set_update_fn()\fR, \fBEVP_MD_CTX_update_fn()\fR,
\&\fBEVP_MD_meth_*()\fR
.Sp
See "Providers are a replacement for engines and low-level method overrides".
.IP \(bu 4
\&\fBEVP_PKEY_CTRL_PKCS7_ENCRYPT()\fR, \fBEVP_PKEY_CTRL_PKCS7_DECRYPT()\fR,
\&\fBEVP_PKEY_CTRL_PKCS7_SIGN()\fR, \fBEVP_PKEY_CTRL_CMS_ENCRYPT()\fR,
\&\fBEVP_PKEY_CTRL_CMS_DECRYPT()\fR, and \fBEVP_PKEY_CTRL_CMS_SIGN()\fR
.Sp
These control operations are not invoked by the OpenSSL library anymore and
are replaced by direct checks of the key operation against the key type
when the operation is initialized.
.IP \(bu 4
\&\fBEVP_PKEY_CTX_get0_dh_kdf_ukm()\fR, \fBEVP_PKEY_CTX_get0_ecdh_kdf_ukm()\fR
.Sp
See the "kdf-ukm" item in "DH key exchange parameters" in \fBEVP_KEYEXCH\-DH\fR\|(7) and
"ECDH Key Exchange parameters" in \fBEVP_KEYEXCH\-ECDH\fR\|(7).
These functions are obsolete and should not be required.
.IP \(bu 4
\&\fBEVP_PKEY_CTX_set_rsa_keygen_pubexp()\fR
.Sp
Applications should use \fBEVP_PKEY_CTX_set1_rsa_keygen_pubexp\fR\|(3) instead.
.IP \(bu 4
\&\fBEVP_PKEY_cmp()\fR, \fBEVP_PKEY_cmp_parameters()\fR
.Sp
Applications should use \fBEVP_PKEY_eq\fR\|(3) and \fBEVP_PKEY_parameters_eq\fR\|(3) instead.
See \fBEVP_PKEY_copy_parameters\fR\|(3) for further details.
.IP \(bu 4
\&\fBEVP_PKEY_encrypt_old()\fR, \fBEVP_PKEY_decrypt_old()\fR,
.Sp
Applications should use \fBEVP_PKEY_encrypt_init\fR\|(3) and \fBEVP_PKEY_encrypt\fR\|(3) or
\&\fBEVP_PKEY_decrypt_init\fR\|(3) and \fBEVP_PKEY_decrypt\fR\|(3) instead.
.IP \(bu 4
\&\fBEVP_PKEY_get0()\fR
.Sp
This function returns NULL if the key comes from a provider.
.IP \(bu 4
\&\fBEVP_PKEY_get0_DH()\fR, \fBEVP_PKEY_get0_DSA()\fR, \fBEVP_PKEY_get0_EC_KEY()\fR, \fBEVP_PKEY_get0_RSA()\fR,
\&\fBEVP_PKEY_get1_DH()\fR, \fBEVP_PKEY_get1_DSA()\fR, EVP_PKEY_get1_EC_KEY and \fBEVP_PKEY_get1_RSA()\fR,
\&\fBEVP_PKEY_get0_hmac()\fR, \fBEVP_PKEY_get0_poly1305()\fR, \fBEVP_PKEY_get0_siphash()\fR
.Sp
See "Functions that return an internal key should be treated as read only".
.IP \(bu 4
\&\fBEVP_PKEY_meth_*()\fR
.Sp
See "Providers are a replacement for engines and low-level method overrides".
.IP \(bu 4
\&\fBEVP_PKEY_new_CMAC_key()\fR
.Sp
See "Deprecated low-level MAC functions".
.IP \(bu 4
\&\fBEVP_PKEY_assign()\fR, \fBEVP_PKEY_set1_DH()\fR, \fBEVP_PKEY_set1_DSA()\fR,
\&\fBEVP_PKEY_set1_EC_KEY()\fR, \fBEVP_PKEY_set1_RSA()\fR
.Sp
See "Deprecated low-level key object getters and setters"
.IP \(bu 4
\&\fBEVP_PKEY_set1_tls_encodedpoint()\fR \fBEVP_PKEY_get1_tls_encodedpoint()\fR
.Sp
These functions were previously used by libssl to set or get an encoded public
key into/from an EVP_PKEY object. With OpenSSL 3.0 these are replaced by the more
generic functions \fBEVP_PKEY_set1_encoded_public_key\fR\|(3) and
\&\fBEVP_PKEY_get1_encoded_public_key\fR\|(3).
The old versions have been converted to deprecated macros that just call the
new functions.
.IP \(bu 4
\&\fBEVP_PKEY_set1_engine()\fR, \fBEVP_PKEY_get0_engine()\fR
.Sp
See "Providers are a replacement for engines and low-level method overrides".
.IP \(bu 4
\&\fBEVP_PKEY_set_alias_type()\fR
.Sp
This function has been removed. There is no replacement.
See "\fBEVP_PKEY_set_alias_type()\fR method has been removed"
.IP \(bu 4
\&\fBHMAC_Init_ex()\fR, \fBHMAC_Update()\fR, \fBHMAC_Final()\fR, \fBHMAC_size()\fR
.Sp
See "Deprecated low-level MAC functions".
.IP \(bu 4
\&\fBHMAC_CTX_new()\fR, \fBHMAC_CTX_free()\fR, \fBHMAC_CTX_copy()\fR, \fBHMAC_CTX_reset()\fR,
\&\fBHMAC_CTX_set_flags()\fR, \fBHMAC_CTX_get_md()\fR
.Sp
See "Deprecated low-level MAC functions".
.IP \(bu 4
\&\fBi2d_DHparams()\fR, \fBi2d_DHxparams()\fR
.Sp
See "Deprecated low-level key reading and writing functions"
and "Migration" in \fBd2i_RSAPrivateKey\fR\|(3)
.IP \(bu 4
\&\fBi2d_DSAparams()\fR, \fBi2d_DSAPrivateKey()\fR, \fBi2d_DSAPrivateKey_bio()\fR,
\&\fBi2d_DSAPrivateKey_fp()\fR, \fBi2d_DSA_PUBKEY()\fR, \fBi2d_DSA_PUBKEY_bio()\fR,
\&\fBi2d_DSA_PUBKEY_fp()\fR, \fBi2d_DSAPublicKey()\fR
.Sp
See "Deprecated low-level key reading and writing functions"
and "Migration" in \fBd2i_RSAPrivateKey\fR\|(3)
.IP \(bu 4
\&\fBi2d_ECParameters()\fR, \fBi2d_ECPrivateKey()\fR, \fBi2d_ECPrivateKey_bio()\fR,
\&\fBi2d_ECPrivateKey_fp()\fR, \fBi2d_EC_PUBKEY()\fR, \fBi2d_EC_PUBKEY_bio()\fR,
\&\fBi2d_EC_PUBKEY_fp()\fR, \fBi2o_ECPublicKey()\fR
.Sp
See "Deprecated low-level key reading and writing functions"
and "Migration" in \fBd2i_RSAPrivateKey\fR\|(3)
.IP \(bu 4
\&\fBi2d_RSAPrivateKey()\fR, \fBi2d_RSAPrivateKey_bio()\fR, \fBi2d_RSAPrivateKey_fp()\fR,
\&\fBi2d_RSA_PUBKEY()\fR, \fBi2d_RSA_PUBKEY_bio()\fR, \fBi2d_RSA_PUBKEY_fp()\fR,
\&\fBi2d_RSAPublicKey()\fR, \fBi2d_RSAPublicKey_bio()\fR, \fBi2d_RSAPublicKey_fp()\fR
.Sp
See "Deprecated low-level key reading and writing functions"
and "Migration" in \fBd2i_RSAPrivateKey\fR\|(3)
.IP \(bu 4
\&\fBIDEA_encrypt()\fR, \fBIDEA_set_decrypt_key()\fR, \fBIDEA_set_encrypt_key()\fR,
\&\fBIDEA_cbc_encrypt()\fR, \fBIDEA_cfb64_encrypt()\fR, \fBIDEA_ecb_encrypt()\fR,
\&\fBIDEA_ofb64_encrypt()\fR
.Sp
See "Deprecated low-level encryption functions".
IDEA has been moved to the Legacy Provider.
.IP \(bu 4
\&\fBIDEA_options()\fR
.Sp
There is no replacement. This function returned a constant string.
.IP \(bu 4
\&\fBMD2()\fR, \fBMD2_Init()\fR, \fBMD2_Update()\fR, \fBMD2_Final()\fR
.Sp
See "Deprecated low-level encryption functions".
MD2 has been moved to the Legacy Provider.
.IP \(bu 4
\&\fBMD2_options()\fR
.Sp
There is no replacement. This function returned a constant string.
.IP \(bu 4
\&\fBMD4()\fR, \fBMD4_Init()\fR, \fBMD4_Update()\fR, \fBMD4_Final()\fR, \fBMD4_Transform()\fR
.Sp
See "Deprecated low-level encryption functions".
MD4 has been moved to the Legacy Provider.
.IP \(bu 4
\&\fBMDC2()\fR, \fBMDC2_Init()\fR, \fBMDC2_Update()\fR, \fBMDC2_Final()\fR
.Sp
See "Deprecated low-level encryption functions".
MDC2 has been moved to the Legacy Provider.
.IP \(bu 4
\&\fBMD5()\fR, \fBMD5_Init()\fR, \fBMD5_Update()\fR, \fBMD5_Final()\fR, \fBMD5_Transform()\fR
.Sp
See "Deprecated low-level encryption functions".
.IP \(bu 4
\&\fBNCONF_WIN32()\fR
.Sp
This undocumented function has no replacement.
See "HISTORY" in \fBconfig\fR\|(5) for more details.
.IP \(bu 4
\&\fBOCSP_parse_url()\fR
.Sp
Use \fBOSSL_HTTP_parse_url\fR\|(3) instead.
.IP \(bu 4
\&\fBOCSP_REQ_CTX\fR type and \fBOCSP_REQ_CTX_*()\fR functions
.Sp
These methods were used to collect all necessary data to form a HTTP request,
and to perform the HTTP transfer with that request.  With OpenSSL 3.0, the
type is \fBOSSL_HTTP_REQ_CTX\fR, and the deprecated functions are replaced
with \fBOSSL_HTTP_REQ_CTX_*()\fR. See \fBOSSL_HTTP_REQ_CTX\fR\|(3) for additional
details.
.IP \(bu 4
\&\fBOPENSSL_fork_child()\fR, \fBOPENSSL_fork_parent()\fR, \fBOPENSSL_fork_prepare()\fR
.Sp
There is no replacement for these functions. These pthread fork support methods
were unused by OpenSSL.
.IP \(bu 4
\&\fBOSSL_STORE_ctrl()\fR, \fBOSSL_STORE_do_all_loaders()\fR, \fBOSSL_STORE_LOADER_get0_engine()\fR,
\&\fBOSSL_STORE_LOADER_get0_scheme()\fR, \fBOSSL_STORE_LOADER_new()\fR,
\&\fBOSSL_STORE_LOADER_set_attach()\fR, \fBOSSL_STORE_LOADER_set_close()\fR,
\&\fBOSSL_STORE_LOADER_set_ctrl()\fR, \fBOSSL_STORE_LOADER_set_eof()\fR,
\&\fBOSSL_STORE_LOADER_set_error()\fR, \fBOSSL_STORE_LOADER_set_expect()\fR,
\&\fBOSSL_STORE_LOADER_set_find()\fR, \fBOSSL_STORE_LOADER_set_load()\fR,
\&\fBOSSL_STORE_LOADER_set_open()\fR, \fBOSSL_STORE_LOADER_set_open_ex()\fR,
\&\fBOSSL_STORE_register_loader()\fR, \fBOSSL_STORE_unregister_loader()\fR,
\&\fBOSSL_STORE_vctrl()\fR
.Sp
These functions helped applications and engines create loaders for
schemes they supported.  These are all deprecated and discouraged in favour of
provider implementations, see \fBprovider\-storemgmt\fR\|(7).
.IP \(bu 4
\&\fBPEM_read_DHparams()\fR, \fBPEM_read_bio_DHparams()\fR,
\&\fBPEM_read_DSAparams()\fR, \fBPEM_read_bio_DSAparams()\fR,
\&\fBPEM_read_DSAPrivateKey()\fR, \fBPEM_read_DSA_PUBKEY()\fR,
PEM_read_bio_DSAPrivateKey and \fBPEM_read_bio_DSA_PUBKEY()\fR,
\&\fBPEM_read_ECPKParameters()\fR, \fBPEM_read_ECPrivateKey()\fR, \fBPEM_read_EC_PUBKEY()\fR,
\&\fBPEM_read_bio_ECPKParameters()\fR, \fBPEM_read_bio_ECPrivateKey()\fR, \fBPEM_read_bio_EC_PUBKEY()\fR,
\&\fBPEM_read_RSAPrivateKey()\fR, \fBPEM_read_RSA_PUBKEY()\fR, \fBPEM_read_RSAPublicKey()\fR,
\&\fBPEM_read_bio_RSAPrivateKey()\fR, \fBPEM_read_bio_RSA_PUBKEY()\fR, \fBPEM_read_bio_RSAPublicKey()\fR,
\&\fBPEM_write_bio_DHparams()\fR, \fBPEM_write_bio_DHxparams()\fR, \fBPEM_write_DHparams()\fR, \fBPEM_write_DHxparams()\fR,
\&\fBPEM_write_DSAparams()\fR, \fBPEM_write_DSAPrivateKey()\fR, \fBPEM_write_DSA_PUBKEY()\fR,
\&\fBPEM_write_bio_DSAparams()\fR, \fBPEM_write_bio_DSAPrivateKey()\fR, \fBPEM_write_bio_DSA_PUBKEY()\fR,
\&\fBPEM_write_ECPKParameters()\fR, \fBPEM_write_ECPrivateKey()\fR, \fBPEM_write_EC_PUBKEY()\fR,
\&\fBPEM_write_bio_ECPKParameters()\fR, \fBPEM_write_bio_ECPrivateKey()\fR, \fBPEM_write_bio_EC_PUBKEY()\fR,
\&\fBPEM_write_RSAPrivateKey()\fR, \fBPEM_write_RSA_PUBKEY()\fR, \fBPEM_write_RSAPublicKey()\fR,
\&\fBPEM_write_bio_RSAPrivateKey()\fR, \fBPEM_write_bio_RSA_PUBKEY()\fR,
\&\fBPEM_write_bio_RSAPublicKey()\fR,
.Sp
See "Deprecated low-level key reading and writing functions"
.IP \(bu 4
\&\fBPKCS1_MGF1()\fR
.Sp
See "Deprecated low-level encryption functions".
.IP \(bu 4
\&\fBRAND_get_rand_method()\fR, \fBRAND_set_rand_method()\fR, \fBRAND_OpenSSL()\fR,
\&\fBRAND_set_rand_engine()\fR
.Sp
Applications should instead use \fBRAND_set_DRBG_type\fR\|(3),
\&\fBEVP_RAND\fR\|(3) and \fBEVP_RAND\fR\|(7).
See \fBRAND_set_rand_method\fR\|(3) for more details.
.IP \(bu 4
\&\fBRC2_encrypt()\fR, \fBRC2_decrypt()\fR, \fBRC2_set_key()\fR, \fBRC2_cbc_encrypt()\fR, \fBRC2_cfb64_encrypt()\fR,
\&\fBRC2_ecb_encrypt()\fR, \fBRC2_ofb64_encrypt()\fR,
\&\fBRC4()\fR, \fBRC4_set_key()\fR, \fBRC4_options()\fR,
\&\fBRC5_32_encrypt()\fR, \fBRC5_32_set_key()\fR, \fBRC5_32_decrypt()\fR, \fBRC5_32_cbc_encrypt()\fR,
\&\fBRC5_32_cfb64_encrypt()\fR, \fBRC5_32_ecb_encrypt()\fR, \fBRC5_32_ofb64_encrypt()\fR
.Sp
See "Deprecated low-level encryption functions".
The Algorithms "RC2", "RC4" and "RC5" have been moved to the Legacy Provider.
.IP \(bu 4
\&\fBRIPEMD160()\fR, \fBRIPEMD160_Init()\fR, \fBRIPEMD160_Update()\fR, \fBRIPEMD160_Final()\fR,
\&\fBRIPEMD160_Transform()\fR
.Sp
See "Deprecated low-level digest functions".
The RIPE algorithm has been moved to the Legacy Provider.
.IP \(bu 4
\&\fBRSA_bits()\fR, \fBRSA_security_bits()\fR, \fBRSA_size()\fR
.Sp
Use \fBEVP_PKEY_get_bits\fR\|(3), \fBEVP_PKEY_get_security_bits\fR\|(3) and
\&\fBEVP_PKEY_get_size\fR\|(3).
.IP \(bu 4
\&\fBRSA_check_key()\fR, \fBRSA_check_key_ex()\fR
.Sp
See "Deprecated low-level validation functions"
.IP \(bu 4
\&\fBRSA_clear_flags()\fR, \fBRSA_flags()\fR, \fBRSA_set_flags()\fR, \fBRSA_test_flags()\fR,
\&\fBRSA_setup_blinding()\fR, \fBRSA_blinding_off()\fR, \fBRSA_blinding_on()\fR
.Sp
All of these RSA flags have been deprecated without replacement:
.Sp
\&\fBRSA_FLAG_BLINDING\fR, \fBRSA_FLAG_CACHE_PRIVATE\fR, \fBRSA_FLAG_CACHE_PUBLIC\fR,
\&\fBRSA_FLAG_EXT_PKEY\fR, \fBRSA_FLAG_NO_BLINDING\fR, \fBRSA_FLAG_THREAD_SAFE\fR
\&\fBRSA_METHOD_FLAG_NO_CHECK\fR
.IP \(bu 4
\&\fBRSA_generate_key_ex()\fR, \fBRSA_generate_multi_prime_key()\fR
.Sp
See "Deprecated low-level key generation functions".
.IP \(bu 4
\&\fBRSA_get0_engine()\fR
.Sp
See "Providers are a replacement for engines and low-level method overrides"
.IP \(bu 4
\&\fBRSA_get0_crt_params()\fR, \fBRSA_get0_d()\fR, \fBRSA_get0_dmp1()\fR, \fBRSA_get0_dmq1()\fR,
\&\fBRSA_get0_e()\fR, \fBRSA_get0_factors()\fR, \fBRSA_get0_iqmp()\fR, \fBRSA_get0_key()\fR,
\&\fBRSA_get0_multi_prime_crt_params()\fR, \fBRSA_get0_multi_prime_factors()\fR, \fBRSA_get0_n()\fR,
\&\fBRSA_get0_p()\fR, \fBRSA_get0_pss_params()\fR, \fBRSA_get0_q()\fR,
\&\fBRSA_get_multi_prime_extra_count()\fR
.Sp
See "Deprecated low-level key parameter getters"
.IP \(bu 4
\&\fBRSA_new()\fR, \fBRSA_free()\fR, \fBRSA_up_ref()\fR
.Sp
See "Deprecated low-level object creation".
.IP \(bu 4
\&\fBRSA_get_default_method()\fR, RSA_get_ex_data and \fBRSA_get_method()\fR
.Sp
See "Providers are a replacement for engines and low-level method overrides".
.IP \(bu 4
\&\fBRSA_get_version()\fR
.Sp
There is no replacement.
.IP \(bu 4
\&\fBRSA_meth_*()\fR, \fBRSA_new_method()\fR, RSA_null_method and \fBRSA_PKCS1_OpenSSL()\fR
.Sp
See "Providers are a replacement for engines and low-level method overrides".
.IP \(bu 4
\&\fBRSA_padding_add_*()\fR, \fBRSA_padding_check_*()\fR
.Sp
See "Deprecated low-level signing functions" and
"Deprecated low-level encryption functions".
.IP \(bu 4
\&\fBRSA_print()\fR, \fBRSA_print_fp()\fR
.Sp
See "Deprecated low-level key printing functions"
.IP \(bu 4
\&\fBRSA_public_encrypt()\fR, \fBRSA_private_decrypt()\fR
.Sp
See "Deprecated low-level encryption functions"
.IP \(bu 4
\&\fBRSA_private_encrypt()\fR, \fBRSA_public_decrypt()\fR
.Sp
This is equivalent to doing sign and verify recover operations (with a padding
mode of none). See "Deprecated low-level signing functions".
.IP \(bu 4
\&\fBRSAPrivateKey_dup()\fR, \fBRSAPublicKey_dup()\fR
.Sp
There is no direct replacement. Applications may use \fBEVP_PKEY_dup\fR\|(3).
.IP \(bu 4
\&\fBRSAPublicKey_it()\fR, \fBRSAPrivateKey_it()\fR
.Sp
See "Deprecated low-level key reading and writing functions"
.IP \(bu 4
\&\fBRSA_set0_crt_params()\fR, \fBRSA_set0_factors()\fR, \fBRSA_set0_key()\fR,
\&\fBRSA_set0_multi_prime_params()\fR
.Sp
See "Deprecated low-level key parameter setters".
.IP \(bu 4
\&\fBRSA_set_default_method()\fR, \fBRSA_set_method()\fR, \fBRSA_set_ex_data()\fR
.Sp
See "Providers are a replacement for engines and low-level method overrides"
.IP \(bu 4
\&\fBRSA_sign()\fR, \fBRSA_sign_ASN1_OCTET_STRING()\fR, \fBRSA_verify()\fR,
\&\fBRSA_verify_ASN1_OCTET_STRING()\fR, \fBRSA_verify_PKCS1_PSS()\fR,
\&\fBRSA_verify_PKCS1_PSS_mgf1()\fR
.Sp
See "Deprecated low-level signing functions".
.IP \(bu 4
\&\fBRSA_X931_derive_ex()\fR, \fBRSA_X931_generate_key_ex()\fR, \fBRSA_X931_hash_id()\fR
.Sp
There are no replacements for these functions.
X931 padding can be set using "Signature Parameters" in \fBEVP_SIGNATURE\-RSA\fR\|(7).
See \fBOSSL_SIGNATURE_PARAM_PAD_MODE\fR.
.IP \(bu 4
\&\fBSEED_encrypt()\fR, \fBSEED_decrypt()\fR, \fBSEED_set_key()\fR, \fBSEED_cbc_encrypt()\fR,
\&\fBSEED_cfb128_encrypt()\fR, \fBSEED_ecb_encrypt()\fR, \fBSEED_ofb128_encrypt()\fR
.Sp
See "Deprecated low-level encryption functions".
The SEED algorithm has been moved to the Legacy Provider.
.IP \(bu 4
\&\fBSHA1_Init()\fR, \fBSHA1_Update()\fR, \fBSHA1_Final()\fR, \fBSHA1_Transform()\fR,
\&\fBSHA224_Init()\fR, \fBSHA224_Update()\fR, \fBSHA224_Final()\fR,
\&\fBSHA256_Init()\fR, \fBSHA256_Update()\fR, \fBSHA256_Final()\fR, \fBSHA256_Transform()\fR,
\&\fBSHA384_Init()\fR, \fBSHA384_Update()\fR, \fBSHA384_Final()\fR,
\&\fBSHA512_Init()\fR, \fBSHA512_Update()\fR, \fBSHA512_Final()\fR, \fBSHA512_Transform()\fR
.Sp
See "Deprecated low-level digest functions".
.IP \(bu 4
\&\fBSRP_Calc_A()\fR, \fBSRP_Calc_B()\fR, \fBSRP_Calc_client_key()\fR, \fBSRP_Calc_server_key()\fR,
\&\fBSRP_Calc_u()\fR, \fBSRP_Calc_x()\fR, \fBSRP_check_known_gN_param()\fR, \fBSRP_create_verifier()\fR,
\&\fBSRP_create_verifier_BN()\fR, \fBSRP_get_default_gN()\fR, \fBSRP_user_pwd_free()\fR, \fBSRP_user_pwd_new()\fR,
\&\fBSRP_user_pwd_set0_sv()\fR, \fBSRP_user_pwd_set1_ids()\fR, \fBSRP_user_pwd_set_gN()\fR,
\&\fBSRP_VBASE_add0_user()\fR, \fBSRP_VBASE_free()\fR, \fBSRP_VBASE_get1_by_user()\fR, \fBSRP_VBASE_init()\fR,
\&\fBSRP_VBASE_new()\fR, \fBSRP_Verify_A_mod_N()\fR, \fBSRP_Verify_B_mod_N()\fR
.Sp
There are no replacements for the SRP functions.
.IP \(bu 4
\&\fBSSL_CTX_set_tmp_dh_callback()\fR, \fBSSL_set_tmp_dh_callback()\fR,
\&\fBSSL_CTX_set_tmp_dh()\fR, \fBSSL_set_tmp_dh()\fR
.Sp
These are used to set the Diffie-Hellman (DH) parameters that are to be used by
servers requiring ephemeral DH keys. Instead applications should consider using
the built-in DH parameters that are available by calling \fBSSL_CTX_set_dh_auto\fR\|(3)
or \fBSSL_set_dh_auto\fR\|(3). If custom parameters are necessary then applications can
use the alternative functions \fBSSL_CTX_set0_tmp_dh_pkey\fR\|(3) and
\&\fBSSL_set0_tmp_dh_pkey\fR\|(3). There is no direct replacement for the "callback"
functions. The callback was originally useful in order to have different
parameters for export and non-export ciphersuites. Export ciphersuites are no
longer supported by OpenSSL. Use of the callback functions should be replaced
by one of the other methods described above.
.IP \(bu 4
\&\fBSSL_CTX_set_tlsext_ticket_key_cb()\fR
.Sp
Use the new \fBSSL_CTX_set_tlsext_ticket_key_evp_cb\fR\|(3) function instead.
.IP \(bu 4
\&\fBWHIRLPOOL()\fR, \fBWHIRLPOOL_Init()\fR, \fBWHIRLPOOL_Update()\fR, \fBWHIRLPOOL_Final()\fR,
\&\fBWHIRLPOOL_BitUpdate()\fR
.Sp
See "Deprecated low-level digest functions".
The Whirlpool algorithm has been moved to the Legacy Provider.
.IP \(bu 4
\&\fBX509_certificate_type()\fR
.Sp
This was an undocumented function. Applications can use \fBX509_get0_pubkey\fR\|(3)
and \fBX509_get0_signature\fR\|(3) instead.
.IP \(bu 4
\&\fBX509_http_nbio()\fR, \fBX509_CRL_http_nbio()\fR
.Sp
Use \fBX509_load_http\fR\|(3) and \fBX509_CRL_load_http\fR\|(3) instead.
.PP
\fINID handling for provided keys and algorithms\fR
.IX Subsection "NID handling for provided keys and algorithms"
.PP
The following functions for NID (numeric id) handling have changed semantics.
.IP \(bu 4
\&\fBEVP_PKEY_id()\fR, \fBEVP_PKEY_get_id()\fR
.Sp
This function was previously used to reliably return the NID of
an EVP_PKEY object, e.g., to look up the name of the algorithm of
such EVP_PKEY by calling \fBOBJ_nid2sn\fR\|(3). With the introduction
of \fBprovider\fR\|(7)s \fBEVP_PKEY_id()\fR or its new equivalent
\&\fBEVP_PKEY_get_id\fR\|(3) might now also return the value \-1
(\fBEVP_PKEY_KEYMGMT\fR) indicating the use of a provider to
implement the EVP_PKEY object. Therefore, the use of
\&\fBEVP_PKEY_get0_type_name\fR\|(3) is recommended for retrieving
the name of the EVP_PKEY algorithm.
.SS "Using the FIPS Module in applications"
.IX Subsection "Using the FIPS Module in applications"
See \fBfips_module\fR\|(7) and \fBOSSL_PROVIDER\-FIPS\fR\|(7) for details.
.SS "OpenSSL command line application changes"
.IX Subsection "OpenSSL command line application changes"
\fINew applications\fR
.IX Subsection "New applications"
.PP
\&\fBopenssl kdf\fR uses the new \fBEVP_KDF\fR\|(3) API.
\&\fBopenssl kdf\fR uses the new \fBEVP_MAC\fR\|(3) API.
.PP
\fIAdded options\fR
.IX Subsection "Added options"
.PP
\&\fB\-provider_path\fR and \fB\-provider\fR are available to all apps and can be used
multiple times to load any providers, such as the 'legacy' provider or third
party providers. If used then the 'default' provider would also need to be
specified if required. The \fB\-provider_path\fR must be specified before the
\&\fB\-provider\fR option.
.PP
The \fBlist\fR app has many new options. See \fBopenssl\-list\fR\|(1) for more
information.
.PP
\&\fB\-crl_lastupdate\fR and \fB\-crl_nextupdate\fR used by \fBopenssl ca\fR allows
explicit setting of fields in the generated CRL.
.PP
\fIRemoved options\fR
.IX Subsection "Removed options"
.PP
Interactive mode is not longer available.
.PP
The \fB\-crypt\fR option used by \fBopenssl passwd\fR.
The \fB\-c\fR option used by \fBopenssl x509\fR, \fBopenssl dhparam\fR,
\&\fBopenssl dsaparam\fR, and \fBopenssl ecparam\fR.
.PP
\fIOther Changes\fR
.IX Subsection "Other Changes"
.PP
The output of Command line applications may have minor changes.
These are primarily changes in capitalisation and white space.  However, in some
cases, there are additional differences.
For example, the DH parameters output from \fBopenssl dhparam\fR now lists 'P',
\&'Q', 'G' and 'pcounter' instead of 'prime', 'generator', 'subgroup order' and
\&'counter' respectively.
.PP
The \fBopenssl\fR commands that read keys, certificates, and CRLs now
automatically detect the PEM or DER format of the input files so it is not
necessary to explicitly specify the input format anymore. However if the
input format option is used the specified format will be required.
.PP
\&\fBopenssl speed\fR no longer uses low-level API calls.
This implies some of the performance numbers might not be comparable with the
previous releases due to higher overhead. This applies particularly to
measuring performance on smaller data chunks.
.PP
b<openssl dhparam>, \fBopenssl dsa\fR, \fBopenssl gendsa\fR, \fBopenssl dsaparam\fR,
\&\fBopenssl genrsa\fR and \fBopenssl rsa\fR have been modified to use PKEY APIs.
\&\fBopenssl genrsa\fR and \fBopenssl rsa\fR now write PKCS #8 keys by default.
.PP
\fIDefault settings\fR
.IX Subsection "Default settings"
.PP
"SHA256" is now the default digest for TS query used by \fBopenssl ts\fR.
.PP
\fIDeprecated apps\fR
.IX Subsection "Deprecated apps"
.PP
\&\fBopenssl rsautl\fR is deprecated, use \fBopenssl pkeyutl\fR instead.
\&\fBopenssl dhparam\fR, \fBopenssl dsa\fR, \fBopenssl gendsa\fR, \fBopenssl dsaparam\fR,
\&\fBopenssl genrsa\fR, \fBopenssl rsa\fR, \fBopenssl genrsa\fR and \fBopenssl rsa\fR are
now in maintenance mode and no new features will be added to them.
.SS "TLS Changes"
.IX Subsection "TLS Changes"
.IP \(bu 4
TLS 1.3 FFDHE key exchange support added
.Sp
This uses DH safe prime named groups.
.IP \(bu 4
Support for fully "pluggable" TLSv1.3 groups.
.Sp
This means that providers may supply their own group implementations (using
either the "key exchange" or the "key encapsulation" methods) which will
automatically be detected and used by libssl.
.IP \(bu 4
SSL and SSL_CTX options are now 64 bit instead of 32 bit.
.Sp
The signatures of the functions to get and set options on SSL and
SSL_CTX objects changed from "unsigned long" to "uint64_t" type.
.Sp
This may require source code changes. For example it is no longer possible
to use the \fBSSL_OP_\fR macro values in preprocessor \f(CW\*(C`#if\*(C'\fR conditions.
However it is still possible to test whether these macros are defined or not.
.Sp
See \fBSSL_CTX_get_options\fR\|(3), \fBSSL_CTX_set_options\fR\|(3),
\&\fBSSL_get_options\fR\|(3) and \fBSSL_set_options\fR\|(3).
.IP \(bu 4
\&\fBSSL_set1_host()\fR and \fBSSL_add1_host()\fR Changes
.Sp
These functions now take IP literal addresses as well as actual hostnames.
.IP \(bu 4
Added SSL option SSL_OP_CLEANSE_PLAINTEXT
.Sp
If the option is set, openssl cleanses (zeroizes) plaintext bytes from
internal buffers after delivering them to the application. Note,
the application is still responsible for cleansing other copies
(e.g.: data received by \fBSSL_read\fR\|(3)).
.IP \(bu 4
Client-initiated renegotiation is disabled by default.
.Sp
To allow it, use the \fB\-client_renegotiation\fR option,
the \fBSSL_OP_ALLOW_CLIENT_RENEGOTIATION\fR flag, or the \f(CW\*(C`ClientRenegotiation\*(C'\fR
config parameter as appropriate.
.IP \(bu 4
Secure renegotiation is now required by default for TLS connections
.Sp
Support for RFC 5746 secure renegotiation is now required by default for
SSL or TLS connections to succeed.  Applications that require the ability
to connect to legacy peers will need to explicitly set
SSL_OP_LEGACY_SERVER_CONNECT.  Accordingly, SSL_OP_LEGACY_SERVER_CONNECT
is no longer set as part of SSL_OP_ALL.
.IP \(bu 4
Combining the Configure options no-ec and no-dh no longer disables TLSv1.3
.Sp
Typically if OpenSSL has no EC or DH algorithms then it cannot support
connections with TLSv1.3. However OpenSSL now supports "pluggable" groups
through providers. Therefore third party providers may supply group
implementations even where there are no built-in ones. Attempting to create
TLS connections in such a build without also disabling TLSv1.3 at run time or
using third party provider groups may result in handshake failures. TLSv1.3
can be disabled at compile time using the "no\-tls1_3" Configure option.
.IP \(bu 4
\&\fBSSL_CTX_set_ciphersuites()\fR and \fBSSL_set_ciphersuites()\fR changes.
.Sp
The methods now ignore unknown ciphers.
.IP \(bu 4
Security callback change.
.Sp
The security callback, which can be customised by application code, supports
the security operation SSL_SECOP_TMP_DH. This is defined to take an EVP_PKEY
in the "other" parameter. In most places this is what is passed. All these
places occur server side. However there was one client side call of this
security operation and it passed a DH object instead. This is incorrect
according to the definition of SSL_SECOP_TMP_DH, and is inconsistent with all
of the other locations. Therefore this client side call has been changed to
pass an EVP_PKEY instead.
.IP \(bu 4
New SSL option SSL_OP_IGNORE_UNEXPECTED_EOF
.Sp
The SSL option SSL_OP_IGNORE_UNEXPECTED_EOF is introduced. If that option
is set, an unexpected EOF is ignored, it pretends a close notify was received
instead and so the returned error becomes SSL_ERROR_ZERO_RETURN.
.IP \(bu 4
The security strength of SHA1 and MD5 based signatures in TLS has been reduced.
.Sp
This results in SSL 3, TLS 1.0, TLS 1.1 and DTLS 1.0 no longer
working at the default security level of 1 and instead requires security
level 0. The security level can be changed either using the cipher string
with \f(CW@SECLEVEL\fR, or calling \fBSSL_CTX_set_security_level\fR\|(3). This also means
that where the signature algorithms extension is missing from a ClientHello
then the handshake will fail in TLS 1.2 at security level 1. This is because,
although this extension is optional, failing to provide one means that
OpenSSL will fallback to a default set of signature algorithms. This default
set requires the availability of SHA1.
.IP \(bu 4
X509 certificates signed using SHA1 are no longer allowed at security level 1 and above.
.Sp
In TLS/SSL the default security level is 1. It can be set either using the cipher
string with \f(CW@SECLEVEL\fR, or calling \fBSSL_CTX_set_security_level\fR\|(3). If the
leaf certificate is signed with SHA\-1, a call to \fBSSL_CTX_use_certificate\fR\|(3)
will fail if the security level is not lowered first.
Outside TLS/SSL, the default security level is \-1 (effectively 0). It can
be set using \fBX509_VERIFY_PARAM_set_auth_level\fR\|(3) or using the \fB\-auth_level\fR
options of the commands.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBfips_module\fR\|(7)
.SH HISTORY
.IX Header "HISTORY"
The migration guide was created for OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2021\-2023 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
