.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "EVP_KDF-SSHKDF 7ossl"
.TH EVP_KDF-SSHKDF 7ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
EVP_KDF\-SSHKDF \- The SSHKDF EVP_KDF implementation
.SH DESCRIPTION
.IX Header "DESCRIPTION"
Support for computing the \fBSSHKDF\fR KDF through the \fBEVP_KDF\fR API.
.PP
The EVP_KDF\-SSHKDF algorithm implements the SSHKDF key derivation function.
It is defined in RFC 4253, section 7.2 and is used by SSH to derive IVs,
encryption keys and integrity keys.
Five inputs are required to perform key derivation: The hashing function
(for example SHA256), the Initial Key, the Exchange Hash, the Session ID,
and the derivation key type.
.SS Identity
.IX Subsection "Identity"
"SSHKDF" is the name for this implementation; it
can be used with the \fBEVP_KDF_fetch()\fR function.
.SS "Supported parameters"
.IX Subsection "Supported parameters"
The supported parameters are:
.IP """properties"" (\fBOSSL_KDF_PARAM_PROPERTIES\fR) <UTF8 string>" 4
.IX Item """properties"" (OSSL_KDF_PARAM_PROPERTIES) <UTF8 string>"
.PD 0
.IP """digest"" (\fBOSSL_KDF_PARAM_DIGEST\fR) <UTF8 string>" 4
.IX Item """digest"" (OSSL_KDF_PARAM_DIGEST) <UTF8 string>"
.IP """key"" (\fBOSSL_KDF_PARAM_KEY\fR) <octet string>" 4
.IX Item """key"" (OSSL_KDF_PARAM_KEY) <octet string>"
.PD
These parameters work as described in "PARAMETERS" in \fBEVP_KDF\fR\|(3).
.IP """xcghash"" (\fBOSSL_KDF_PARAM_SSHKDF_XCGHASH\fR) <octet string>" 4
.IX Item """xcghash"" (OSSL_KDF_PARAM_SSHKDF_XCGHASH) <octet string>"
.PD 0
.IP """session_id"" (\fBOSSL_KDF_PARAM_SSHKDF_SESSION_ID\fR) <octet string>" 4
.IX Item """session_id"" (OSSL_KDF_PARAM_SSHKDF_SESSION_ID) <octet string>"
.PD
These parameters set the respective values for the KDF.
If a value is already set, the contents are replaced.
.IP """type"" (\fBOSSL_KDF_PARAM_SSHKDF_TYPE\fR) <UTF8 string>" 4
.IX Item """type"" (OSSL_KDF_PARAM_SSHKDF_TYPE) <UTF8 string>"
This parameter sets the type for the SSHKDF operation.
There are six supported types:
.RS 4
.IP EVP_KDF_SSHKDF_TYPE_INITIAL_IV_CLI_TO_SRV 4
.IX Item "EVP_KDF_SSHKDF_TYPE_INITIAL_IV_CLI_TO_SRV"
The Initial IV from client to server.
A single char of value 65 (ASCII char 'A').
.IP EVP_KDF_SSHKDF_TYPE_INITIAL_IV_SRV_TO_CLI 4
.IX Item "EVP_KDF_SSHKDF_TYPE_INITIAL_IV_SRV_TO_CLI"
The Initial IV from server to client
A single char of value 66 (ASCII char 'B').
.IP EVP_KDF_SSHKDF_TYPE_ENCRYPTION_KEY_CLI_TO_SRV 4
.IX Item "EVP_KDF_SSHKDF_TYPE_ENCRYPTION_KEY_CLI_TO_SRV"
The Encryption Key from client to server
A single char of value 67 (ASCII char 'C').
.IP EVP_KDF_SSHKDF_TYPE_ENCRYPTION_KEY_SRV_TO_CLI 4
.IX Item "EVP_KDF_SSHKDF_TYPE_ENCRYPTION_KEY_SRV_TO_CLI"
The Encryption Key from server to client
A single char of value 68 (ASCII char 'D').
.IP EVP_KDF_SSHKDF_TYPE_INTEGRITY_KEY_CLI_TO_SRV 4
.IX Item "EVP_KDF_SSHKDF_TYPE_INTEGRITY_KEY_CLI_TO_SRV"
The Integrity Key from client to server
A single char of value 69 (ASCII char 'E').
.IP EVP_KDF_SSHKDF_TYPE_INTEGRITY_KEY_SRV_TO_CLI 4
.IX Item "EVP_KDF_SSHKDF_TYPE_INTEGRITY_KEY_SRV_TO_CLI"
The Integrity Key from client to server
A single char of value 70 (ASCII char 'F').
.RE
.RS 4
.RE
.SH NOTES
.IX Header "NOTES"
A context for SSHKDF can be obtained by calling:
.PP
.Vb 2
\& EVP_KDF *kdf = EVP_KDF_fetch(NULL, "SSHKDF", NULL);
\& EVP_KDF_CTX *kctx = EVP_KDF_CTX_new(kdf);
.Ve
.PP
The output length of the SSHKDF derivation is specified via the \fIkeylen\fR
parameter to the \fBEVP_KDF_derive\fR\|(3) function.
Since the SSHKDF output length is variable, calling \fBEVP_KDF_CTX_get_kdf_size\fR\|(3)
to obtain the requisite length is not meaningful. The caller must
allocate a buffer of the desired length, and pass that buffer to the
\&\fBEVP_KDF_derive\fR\|(3) function along with the desired length.
.SH EXAMPLES
.IX Header "EXAMPLES"
This example derives an 8 byte IV using SHA\-256 with a 1K "key" and appropriate
"xcghash" and "session_id" values:
.PP
.Vb 9
\& EVP_KDF *kdf;
\& EVP_KDF_CTX *kctx;
\& char type = EVP_KDF_SSHKDF_TYPE_INITIAL_IV_CLI_TO_SRV;
\& unsigned char key[1024] = "01234...";
\& unsigned char xcghash[32] = "012345...";
\& unsigned char session_id[32] = "012345...";
\& unsigned char out[8];
\& size_t outlen = sizeof(out);
\& OSSL_PARAM params[6], *p = params;
\&
\& kdf = EVP_KDF_fetch(NULL, "SSHKDF", NULL);
\& kctx = EVP_KDF_CTX_new(kdf);
\& EVP_KDF_free(kdf);
\&
\& *p++ = OSSL_PARAM_construct_utf8_string(OSSL_KDF_PARAM_DIGEST,
\&                                         SN_sha256, strlen(SN_sha256));
\& *p++ = OSSL_PARAM_construct_octet_string(OSSL_KDF_PARAM_KEY,
\&                                          key, (size_t)1024);
\& *p++ = OSSL_PARAM_construct_octet_string(OSSL_KDF_PARAM_SSHKDF_XCGHASH,
\&                                          xcghash, (size_t)32);
\& *p++ = OSSL_PARAM_construct_octet_string(OSSL_KDF_PARAM_SSHKDF_SESSION_ID,
\&                                          session_id, (size_t)32);
\& *p++ = OSSL_PARAM_construct_utf8_string(OSSL_KDF_PARAM_SSHKDF_TYPE,
\&                                         &type, sizeof(type));
\& *p = OSSL_PARAM_construct_end();
\& if (EVP_KDF_derive(kctx, out, outlen, params) <= 0)
\&     /* Error */
.Ve
.SH "CONFORMING TO"
.IX Header "CONFORMING TO"
RFC 4253
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBEVP_KDF\fR\|(3),
\&\fBEVP_KDF_CTX_new\fR\|(3),
\&\fBEVP_KDF_CTX_free\fR\|(3),
\&\fBEVP_KDF_CTX_set_params\fR\|(3),
\&\fBEVP_KDF_CTX_get_kdf_size\fR\|(3),
\&\fBEVP_KDF_derive\fR\|(3),
"PARAMETERS" in \fBEVP_KDF\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
This functionality was added in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2016\-2022 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
