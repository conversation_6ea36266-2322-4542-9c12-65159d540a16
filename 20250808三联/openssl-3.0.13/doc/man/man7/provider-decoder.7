.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "PROVIDER-DECODER 7ossl"
.TH PROVIDER-DECODER 7ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
provider\-decoder \- The OSSL_DECODER library <\-> provider functions
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/core_dispatch.h>
\&
\& /*
\&  * None of these are actual functions, but are displayed like this for
\&  * the function signatures for functions that are offered as function
\&  * pointers in OSSL_DISPATCH arrays.
\&  */
\&
\& /* Decoder parameter accessor and descriptor */
\& const OSSL_PARAM *OSSL_FUNC_decoder_gettable_params(void *provctx);
\& int OSSL_FUNC_decoder_get_params(OSSL_PARAM params[]);
\&
\& /* Functions to construct / destruct / manipulate the decoder context */
\& void *OSSL_FUNC_decoder_newctx(void *provctx);
\& void OSSL_FUNC_decoder_freectx(void *ctx);
\& const OSSL_PARAM *OSSL_FUNC_decoder_settable_ctx_params(void *provctx);
\& int OSSL_FUNC_decoder_set_ctx_params(void *ctx, const OSSL_PARAM params[]);
\&
\& /* Functions to check selection support */
\& int OSSL_FUNC_decoder_does_selection(void *provctx, int selection);
\&
\& /* Functions to decode object data */
\& int OSSL_FUNC_decoder_decode(void *ctx, OSSL_CORE_BIO *in,
\&                              int selection,
\&                              OSSL_CALLBACK *data_cb, void *data_cbarg,
\&                              OSSL_PASSPHRASE_CALLBACK *cb, void *cbarg);
\&
\& /* Functions to export a decoded object */
\& int OSSL_FUNC_decoder_export_object(void *ctx,
\&                                       const void *objref, size_t objref_sz,
\&                                       OSSL_CALLBACK *export_cb,
\&                                       void *export_cbarg);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fIThe term "decode" is used throughout this manual.  This includes but is
not limited to deserialization as individual decoders can also do
decoding into intermediate data formats.\fR
.PP
The DECODER operation is a generic method to create a provider-native
object reference or intermediate decoded data from an encoded form
read from the given \fBOSSL_CORE_BIO\fR. If the caller wants to decode
data from memory, it should provide a \fBBIO_s_mem\fR\|(3) \fBBIO\fR. The decoded
data or object reference is passed along with eventual metadata
to the \fImetadata_cb\fR as \fBOSSL_PARAM\fR\|(3) parameters.
.PP
The decoder doesn't need to know more about the \fBOSSL_CORE_BIO\fR
pointer than being able to pass it to the appropriate BIO upcalls (see
"Core functions" in \fBprovider\-base\fR\|(7)).
.PP
The DECODER implementation may be part of a chain, where data is
passed from one to the next.  For example, there may be an
implementation to decode an object from PEM to DER, and another one
that decodes DER to a provider-native object.
.PP
The last decoding step in the decoding chain is usually supposed to create
a provider-native object referenced by an object reference. To import
that object into a different provider the \fBOSSL_FUNC_decoder_export_object()\fR
can be called as the final step of the decoding process.
.PP
All "functions" mentioned here are passed as function pointers between
\&\fIlibcrypto\fR and the provider in \fBOSSL_DISPATCH\fR\|(3) arrays via
\&\fBOSSL_ALGORITHM\fR\|(3) arrays that are returned by the provider's
\&\fBprovider_query_operation()\fR function
(see "Provider Functions" in \fBprovider\-base\fR\|(7)).
.PP
All these "functions" have a corresponding function type definition
named \fBOSSL_FUNC_{name}_fn\fR, and a helper function to retrieve the
function pointer from an \fBOSSL_DISPATCH\fR\|(3) element named
\&\fBOSSL_FUNC_{name}\fR.
For example, the "function" \fBOSSL_FUNC_decoder_decode()\fR has these:
.PP
.Vb 7
\& typedef int
\&     (OSSL_FUNC_decoder_decode_fn)(void *ctx, OSSL_CORE_BIO *in,
\&                                   int selection,
\&                                   OSSL_CALLBACK *data_cb, void *data_cbarg,
\&                                   OSSL_PASSPHRASE_CALLBACK *cb, void *cbarg);
\& static ossl_inline OSSL_FUNC_decoder_decode_fn*
\&     OSSL_FUNC_decoder_decode(const OSSL_DISPATCH *opf);
.Ve
.PP
\&\fBOSSL_DISPATCH\fR\|(3) arrays are indexed by numbers that are provided as
macros in \fBopenssl\-core_dispatch.h\fR\|(7), as follows:
.PP
.Vb 2
\& OSSL_FUNC_decoder_get_params          OSSL_FUNC_DECODER_GET_PARAMS
\& OSSL_FUNC_decoder_gettable_params     OSSL_FUNC_DECODER_GETTABLE_PARAMS
\&
\& OSSL_FUNC_decoder_newctx              OSSL_FUNC_DECODER_NEWCTX
\& OSSL_FUNC_decoder_freectx             OSSL_FUNC_DECODER_FREECTX
\& OSSL_FUNC_decoder_set_ctx_params      OSSL_FUNC_DECODER_SET_CTX_PARAMS
\& OSSL_FUNC_decoder_settable_ctx_params OSSL_FUNC_DECODER_SETTABLE_CTX_PARAMS
\&
\& OSSL_FUNC_decoder_does_selection      OSSL_FUNC_DECODER_DOES_SELECTION
\&
\& OSSL_FUNC_decoder_decode              OSSL_FUNC_DECODER_DECODE
\&
\& OSSL_FUNC_decoder_export_object       OSSL_FUNC_DECODER_EXPORT_OBJECT
.Ve
.SS "Names and properties"
.IX Subsection "Names and properties"
The name of an implementation should match the target type of object
it decodes. For example, an implementation that decodes an RSA key
should be named "RSA". Likewise, an implementation that decodes DER data
from PEM input should be named "DER".
.PP
Properties can be used to further specify details about an implementation:
.IP input 4
.IX Item "input"
This property is used to specify what format of input the implementation
can decode.
.Sp
This property is \fImandatory\fR.
.Sp
OpenSSL providers recognize the following input types:
.RS 4
.IP pem 4
.IX Item "pem"
An implementation with that input type decodes PEM formatted data.
.IP der 4
.IX Item "der"
An implementation with that input type decodes DER formatted data.
.IP msblob 4
.IX Item "msblob"
An implementation with that input type decodes MSBLOB formatted data.
.IP pvk 4
.IX Item "pvk"
An implementation with that input type decodes PVK formatted data.
.RE
.RS 4
.RE
.IP structure 4
.IX Item "structure"
This property is used to specify the structure that the decoded data is
expected to have.
.Sp
This property is \fIoptional\fR.
.Sp
Structures currently recognised by built-in decoders:
.RS 4
.IP """type-specific""" 4
.IX Item """type-specific"""
Type specific structure.
.IP """pkcs8""" 4
.IX Item """pkcs8"""
Structure according to the PKCS#8 specification.
.IP """SubjectPublicKeyInfo""" 4
.IX Item """SubjectPublicKeyInfo"""
Encoding of public keys according to the Subject Public Key Info of RFC 5280.
.RE
.RS 4
.RE
.PP
The possible values of both these properties is open ended.  A provider may
very well specify input types and structures that libcrypto doesn't know
anything about.
.SS "Subset selections"
.IX Subsection "Subset selections"
Sometimes, an object has more than one subset of data that is interesting to
treat separately or together.  It's possible to specify what subsets are to
be decoded, with a set of bits \fIselection\fR that are passed in an \fBint\fR.
.PP
This set of bits depend entirely on what kind of provider-side object is
to be decoded.  For example, those bits are assumed to be the same as those
used with \fBprovider\-keymgmt\fR\|(7) (see "Key Objects" in \fBprovider\-keymgmt\fR\|(7)) when
the object is an asymmetric keypair \- e.g., \fBOSSL_KEYMGMT_SELECT_PRIVATE_KEY\fR
if the object to be decoded is supposed to contain private key components.
.PP
\&\fBOSSL_FUNC_decoder_does_selection()\fR should tell if a particular implementation
supports any of the combinations given by \fIselection\fR.
.SS "Context functions"
.IX Subsection "Context functions"
\&\fBOSSL_FUNC_decoder_newctx()\fR returns a context to be used with the rest of
the functions.
.PP
\&\fBOSSL_FUNC_decoder_freectx()\fR frees the given \fIctx\fR as created by
\&\fBOSSL_FUNC_decoder_newctx()\fR.
.PP
\&\fBOSSL_FUNC_decoder_set_ctx_params()\fR sets context data according to parameters
from \fIparams\fR that it recognises.  Unrecognised parameters should be
ignored.
Passing NULL for \fIparams\fR should return true.
.PP
\&\fBOSSL_FUNC_decoder_settable_ctx_params()\fR returns a constant \fBOSSL_PARAM\fR\|(3)
array describing the parameters that \fBOSSL_FUNC_decoder_set_ctx_params()\fR
can handle.
.PP
See \fBOSSL_PARAM\fR\|(3) for further details on the parameters structure used by
\&\fBOSSL_FUNC_decoder_set_ctx_params()\fR and \fBOSSL_FUNC_decoder_settable_ctx_params()\fR.
.SS "Export function"
.IX Subsection "Export function"
When a provider-native object is created by a decoder it would be unsuitable
for direct use with a foreign provider. The export function allows for
exporting the object into that foreign provider if the foreign provider
supports the type of the object and provides an import function.
.PP
\&\fBOSSL_FUNC_decoder_export_object()\fR should export the object of size \fIobjref_sz\fR
referenced by \fIobjref\fR as an \fBOSSL_PARAM\fR\|(3) array and pass that into the
\&\fIexport_cb\fR as well as the given \fIexport_cbarg\fR.
.SS "Decoding functions"
.IX Subsection "Decoding functions"
\&\fBOSSL_FUNC_decoder_decode()\fR should decode the data as read from
the \fBOSSL_CORE_BIO\fR \fIin\fR to produce decoded data or an object to be
passed as reference in an \fBOSSL_PARAM\fR\|(3) array along with possible other
metadata that was decoded from the input. This \fBOSSL_PARAM\fR\|(3) array is
then passed to the \fIdata_cb\fR callback.  The \fIselection\fR bits,
if relevant, should determine what the input data should contain.
The decoding functions also take an \fBOSSL_PASSPHRASE_CALLBACK\fR\|(3) function
pointer along with a pointer to application data \fIcbarg\fR, which should be
used when a pass phrase prompt is needed.
.PP
It's important to understand that the return value from this function is
interpreted as follows:
.IP "True (1)" 4
.IX Item "True (1)"
This means "carry on the decoding process", and is meaningful even though
this function couldn't decode the input into anything, because there may be
another decoder implementation that can decode it into something.
.Sp
The \fIdata_cb\fR callback should never be called when this function can't
decode the input into anything.
.IP "False (0)" 4
.IX Item "False (0)"
This means "stop the decoding process", and is meaningful when the input
could be decoded into some sort of object that this function understands,
but further treatment of that object results into errors that won't be
possible for some other decoder implementation to get a different result.
.PP
The conditions to stop the decoding process are at the discretion of the
implementation.
.SS "Decoder operation parameters"
.IX Subsection "Decoder operation parameters"
There are currently no operation parameters currently recognised by the
built-in decoders.
.PP
Parameters currently recognised by the built-in pass phrase callback:
.IP """info"" (\fBOSSL_PASSPHRASE_PARAM_INFO\fR) <UTF8 string>" 4
.IX Item """info"" (OSSL_PASSPHRASE_PARAM_INFO) <UTF8 string>"
A string of information that will become part of the pass phrase
prompt.  This could be used to give the user information on what kind
of object it's being prompted for.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBOSSL_FUNC_decoder_newctx()\fR returns a pointer to a context, or NULL on
failure.
.PP
\&\fBOSSL_FUNC_decoder_set_ctx_params()\fR returns 1, unless a recognised
parameter was invalid or caused an error, for which 0 is returned.
.PP
\&\fBOSSL_FUNC_decoder_settable_ctx_params()\fR returns a pointer to an array of
constant \fBOSSL_PARAM\fR\|(3) elements.
.PP
\&\fBOSSL_FUNC_decoder_does_selection()\fR returns 1 if the decoder implementation
supports any of the \fIselection\fR bits, otherwise 0.
.PP
\&\fBOSSL_FUNC_decoder_decode()\fR returns 1 to signal that the decoding process
should continue, or 0 to signal that it should stop.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBprovider\fR\|(7)
.SH HISTORY
.IX Header "HISTORY"
The DECODER interface was introduced in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2019\-2023 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
