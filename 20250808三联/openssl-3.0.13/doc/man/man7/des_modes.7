.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "DES_MODES 7ossl"
.TH DES_MODES 7ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
des_modes \- the variants of DES and other crypto algorithms of OpenSSL
.SH DESCRIPTION
.IX Header "DESCRIPTION"
Several crypto algorithms for OpenSSL can be used in a number of modes.  Those
are used for using block ciphers in a way similar to stream ciphers, among
other things.
.SH OVERVIEW
.IX Header "OVERVIEW"
.SS "Electronic Codebook Mode (ECB)"
.IX Subsection "Electronic Codebook Mode (ECB)"
Normally, this is found as the function \fIalgorithm\fR\fB_ecb_encrypt()\fR.
.IP \(bu 2
64 bits are enciphered at a time.
.IP \(bu 2
The order of the blocks can be rearranged without detection.
.IP \(bu 2
The same plaintext block always produces the same ciphertext block
(for the same key) making it vulnerable to a 'dictionary attack'.
.IP \(bu 2
An error will only affect one ciphertext block.
.SS "Cipher Block Chaining Mode (CBC)"
.IX Subsection "Cipher Block Chaining Mode (CBC)"
Normally, this is found as the function \fIalgorithm\fR\fB_cbc_encrypt()\fR.
Be aware that \fBdes_cbc_encrypt()\fR is not really DES CBC (it does
not update the IV); use \fBdes_ncbc_encrypt()\fR instead.
.IP \(bu 2
a multiple of 64 bits are enciphered at a time.
.IP \(bu 2
The CBC mode produces the same ciphertext whenever the same
plaintext is encrypted using the same key and starting variable.
.IP \(bu 2
The chaining operation makes the ciphertext blocks dependent on the
current and all preceding plaintext blocks and therefore blocks can not
be rearranged.
.IP \(bu 2
The use of different starting variables prevents the same plaintext
enciphering to the same ciphertext.
.IP \(bu 2
An error will affect the current and the following ciphertext blocks.
.SS "Cipher Feedback Mode (CFB)"
.IX Subsection "Cipher Feedback Mode (CFB)"
Normally, this is found as the function \fIalgorithm\fR\fB_cfb_encrypt()\fR.
.IP \(bu 2
a number of bits (j) <= 64 are enciphered at a time.
.IP \(bu 2
The CFB mode produces the same ciphertext whenever the same
plaintext is encrypted using the same key and starting variable.
.IP \(bu 2
The chaining operation makes the ciphertext variables dependent on the
current and all preceding variables and therefore j\-bit variables are
chained together and can not be rearranged.
.IP \(bu 2
The use of different starting variables prevents the same plaintext
enciphering to the same ciphertext.
.IP \(bu 2
The strength of the CFB mode depends on the size of k (maximal if
j == k).  In my implementation this is always the case.
.IP \(bu 2
Selection of a small value for j will require more cycles through
the encipherment algorithm per unit of plaintext and thus cause
greater processing overheads.
.IP \(bu 2
Only multiples of j bits can be enciphered.
.IP \(bu 2
An error will affect the current and the following ciphertext variables.
.SS "Output Feedback Mode (OFB)"
.IX Subsection "Output Feedback Mode (OFB)"
Normally, this is found as the function \fIalgorithm\fR\fB_ofb_encrypt()\fR.
.IP \(bu 2
a number of bits (j) <= 64 are enciphered at a time.
.IP \(bu 2
The OFB mode produces the same ciphertext whenever the same
plaintext enciphered using the same key and starting variable.  More
over, in the OFB mode the same key stream is produced when the same
key and start variable are used.  Consequently, for security reasons
a specific start variable should be used only once for a given key.
.IP \(bu 2
The absence of chaining makes the OFB more vulnerable to specific attacks.
.IP \(bu 2
The use of different start variables values prevents the same
plaintext enciphering to the same ciphertext, by producing different
key streams.
.IP \(bu 2
Selection of a small value for j will require more cycles through
the encipherment algorithm per unit of plaintext and thus cause
greater processing overheads.
.IP \(bu 2
Only multiples of j bits can be enciphered.
.IP \(bu 2
OFB mode of operation does not extend ciphertext errors in the
resultant plaintext output.  Every bit error in the ciphertext causes
only one bit to be in error in the deciphered plaintext.
.IP \(bu 2
OFB mode is not self-synchronizing.  If the two operation of
encipherment and decipherment get out of synchronism, the system needs
to be re-initialized.
.IP \(bu 2
Each re-initialization should use a value of the start variable
different from the start variable values used before with the same
key.  The reason for this is that an identical bit stream would be
produced each time from the same parameters.  This would be
susceptible to a 'known plaintext' attack.
.SS "Triple ECB Mode"
.IX Subsection "Triple ECB Mode"
Normally, this is found as the function \fIalgorithm\fR\fB_ecb3_encrypt()\fR.
.IP \(bu 2
Encrypt with key1, decrypt with key2 and encrypt with key3 again.
.IP \(bu 2
As for ECB encryption but increases the key length to 168 bits.
There are theoretic attacks that can be used that make the effective
key length 112 bits, but this attack also requires 2^56 blocks of
memory, not very likely, even for the NSA.
.IP \(bu 2
If both keys are the same it is equivalent to encrypting once with
just one key.
.IP \(bu 2
If the first and last key are the same, the key length is 112 bits.
There are attacks that could reduce the effective key strength
to only slightly more than 56 bits, but these require a lot of memory.
.IP \(bu 2
If all 3 keys are the same, this is effectively the same as normal
ecb mode.
.SS "Triple CBC Mode"
.IX Subsection "Triple CBC Mode"
Normally, this is found as the function \fIalgorithm\fR\fB_ede3_cbc_encrypt()\fR.
.IP \(bu 2
Encrypt with key1, decrypt with key2 and then encrypt with key3.
.IP \(bu 2
As for CBC encryption but increases the key length to 168 bits with
the same restrictions as for triple ecb mode.
.SH NOTES
.IX Header "NOTES"
This text was been written in large parts by Eric Young in his original
documentation for SSLeay, the predecessor of OpenSSL.  In turn, he attributed
it to:
.PP
.Vb 5
\&        AS 2805.5.2
\&        Australian Standard
\&        Electronic funds transfer \- Requirements for interfaces,
\&        Part 5.2: Modes of operation for an n\-bit block cipher algorithm
\&        Appendix A
.Ve
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBBF_encrypt\fR\|(3), \fBDES_crypt\fR\|(3)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2017 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
