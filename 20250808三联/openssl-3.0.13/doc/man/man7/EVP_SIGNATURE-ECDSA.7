.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "EVP_SIGNATURE-ECDSA 7ossl"
.TH EVP_SIGNATURE-ECDSA 7ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
EVP_SIGNATURE\-ECDSA \- The EVP_PKEY ECDSA signature implementation.
.SH DESCRIPTION
.IX Header "DESCRIPTION"
Support for computing ECDSA signatures.
See \fBEVP_PKEY\-EC\fR\|(7) for information related to EC keys.
.SS "ECDSA Signature Parameters"
.IX Subsection "ECDSA Signature Parameters"
The following signature parameters can be set using \fBEVP_PKEY_CTX_set_params()\fR.
This may be called after \fBEVP_PKEY_sign_init()\fR or \fBEVP_PKEY_verify_init()\fR,
and before calling \fBEVP_PKEY_sign()\fR or \fBEVP_PKEY_verify()\fR.
.IP """digest"" (\fBOSSL_SIGNATURE_PARAM_DIGEST\fR) <UTF8 string>" 4
.IX Item """digest"" (OSSL_SIGNATURE_PARAM_DIGEST) <UTF8 string>"
.PD 0
.IP """properties"" (\fBOSSL_SIGNATURE_PARAM_PROPERTIES\fR) <UTF8 string>" 4
.IX Item """properties"" (OSSL_SIGNATURE_PARAM_PROPERTIES) <UTF8 string>"
.PD
These parameters are described in \fBprovider\-signature\fR\|(7).
.PP
The following signature parameters can be retrieved using
\&\fBEVP_PKEY_CTX_get_params()\fR.
.IP """algorithm-id"" (\fBOSSL_SIGNATURE_PARAM_ALGORITHM_ID\fR) <octet string>" 4
.IX Item """algorithm-id"" (OSSL_SIGNATURE_PARAM_ALGORITHM_ID) <octet string>"
.PD 0
.IP """digest"" (\fBOSSL_SIGNATURE_PARAM_DIGEST\fR) <UTF8 string>" 4
.IX Item """digest"" (OSSL_SIGNATURE_PARAM_DIGEST) <UTF8 string>"
.PD
The parameters are described in \fBprovider\-signature\fR\|(7).
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBEVP_PKEY_CTX_set_params\fR\|(3),
\&\fBEVP_PKEY_sign\fR\|(3),
\&\fBEVP_PKEY_verify\fR\|(3),
\&\fBprovider\-signature\fR\|(7),
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2020\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
