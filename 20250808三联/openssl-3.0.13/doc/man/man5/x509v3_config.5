.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "X509V3_CONFIG 5ossl"
.TH X509V3_CONFIG 5ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
x509v3_config \- X509 V3 certificate extension configuration format
.SH DESCRIPTION
.IX Header "DESCRIPTION"
Several OpenSSL commands can add extensions to a certificate or
certificate request based on the contents of a configuration file
and CLI options such as \fB\-addext\fR.
The syntax of configuration files is described in \fBconfig\fR\|(5).
The commands typically have an option to specify the name of the configuration
file, and a section within that file; see the documentation of the
individual command for details.
.PP
This page uses \fBextensions\fR as the name of the section, when needed
in examples.
.PP
Each entry in the extension section takes the form:
.PP
.Vb 1
\& name = [critical, ]value(s)
.Ve
.PP
If \fBcritical\fR is present then the extension will be marked as critical.
.PP
If multiple entries are processed for the same extension name,
later entries override earlier ones with the same name.
.PP
The format of \fBvalues\fR depends on the value of \fBname\fR, many have a
type-value pairing where the type and value are separated by a colon.
There are four main types of extension:
.PP
.Vb 4
\& string
\& multi\-valued
\& raw
\& arbitrary
.Ve
.PP
Each is described in the following paragraphs.
.PP
String extensions simply have a string which contains either the value itself
or how it is obtained.
.PP
Multi-valued extensions have a short form and a long form. The short form
is a comma-separated list of names and values:
.PP
.Vb 1
\& basicConstraints = critical, CA:true, pathlen:1
.Ve
.PP
The long form allows the values to be placed in a separate section:
.PP
.Vb 2
\& [extensions]
\& basicConstraints = critical, @basic_constraints
\&
\& [basic_constraints]
\& CA = true
\& pathlen = 1
.Ve
.PP
Both forms are equivalent.
.PP
If an extension is multi-value and a field value must contain a comma the long
form must be used otherwise the comma would be misinterpreted as a field
separator. For example:
.PP
.Vb 1
\& subjectAltName = URI:ldap://somehost.com/CN=foo,OU=bar
.Ve
.PP
will produce an error but the equivalent form:
.PP
.Vb 2
\& [extensions]
\& subjectAltName = @subject_alt_section
\&
\& [subject_alt_section]
\& subjectAltName = URI:ldap://somehost.com/CN=foo,OU=bar
.Ve
.PP
is valid.
.PP
OpenSSL does not support multiple occurrences of the same field within a
section. In this example:
.PP
.Vb 2
\& [extensions]
\& subjectAltName = @alt_section
\&
\& [alt_section]
\& email = <EMAIL>
\& email = <EMAIL>
.Ve
.PP
will only recognize the last value.  To specify multiple values append a
numeric identifier, as shown here:
.PP
.Vb 2
\& [extensions]
\& subjectAltName = @alt_section
\&
\& [alt_section]
\& email.1 = <EMAIL>
\& email.2 = <EMAIL>
.Ve
.PP
The syntax of raw extensions is defined by the source code that parses
the extension but should be documented.
See "Certificate Policies" for an example of a raw extension.
.PP
If an extension type is unsupported, then the \fIarbitrary\fR extension syntax
must be used, see the "ARBITRARY EXTENSIONS" section for more details.
.SH "STANDARD EXTENSIONS"
.IX Header "STANDARD EXTENSIONS"
The following sections describe the syntax of each supported extension.
They do not define the semantics of the extension.
.SS "Basic Constraints"
.IX Subsection "Basic Constraints"
This is a multi-valued extension which indicates whether a certificate is
a CA certificate. The first value is \fBCA\fR followed by \fBTRUE\fR or
\&\fBFALSE\fR. If \fBCA\fR is \fBTRUE\fR then an optional \fBpathlen\fR name followed by a
nonnegative value can be included.
.PP
For example:
.PP
.Vb 1
\& basicConstraints = CA:TRUE
\&
\& basicConstraints = CA:FALSE
\&
\& basicConstraints = critical, CA:TRUE, pathlen:1
.Ve
.PP
A CA certificate \fImust\fR include the \fBbasicConstraints\fR name with the \fBCA\fR
parameter set to \fBTRUE\fR. An end-user certificate must either have \fBCA:FALSE\fR
or omit the extension entirely.
The \fBpathlen\fR parameter specifies the maximum number of CAs that can appear
below this one in a chain. A \fBpathlen\fR of zero means the CA cannot sign
any sub-CA's, and can only sign end-entity certificates.
.SS "Key Usage"
.IX Subsection "Key Usage"
Key usage is a multi-valued extension consisting of a list of names of
the permitted key usages.  The defined values are: \f(CW\*(C`digitalSignature\*(C'\fR,
\&\f(CW\*(C`nonRepudiation\*(C'\fR, \f(CW\*(C`keyEncipherment\*(C'\fR, \f(CW\*(C`dataEncipherment\*(C'\fR, \f(CW\*(C`keyAgreement\*(C'\fR,
\&\f(CW\*(C`keyCertSign\*(C'\fR, \f(CW\*(C`cRLSign\*(C'\fR, \f(CW\*(C`encipherOnly\*(C'\fR, and \f(CW\*(C`decipherOnly\*(C'\fR.
.PP
Examples:
.PP
.Vb 1
\& keyUsage = digitalSignature, nonRepudiation
\&
\& keyUsage = critical, keyCertSign
.Ve
.SS "Extended Key Usage"
.IX Subsection "Extended Key Usage"
This extension consists of a list of values indicating purposes for which
the certificate public key can be used.
Each value can be either a short text name or an OID.
The following text names, and their intended meaning, are known:
.PP
.Vb 10
\& Value                  Meaning according to RFC 5280 etc.
\& \-\-\-\-\-                  \-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-
\& serverAuth             SSL/TLS WWW Server Authentication
\& clientAuth             SSL/TLS WWW Client Authentication
\& codeSigning            Code Signing
\& emailProtection        E\-mail Protection (S/MIME)
\& timeStamping           Trusted Timestamping
\& OCSPSigning            OCSP Signing
\& ipsecIKE               ipsec Internet Key Exchange
\& msCodeInd              Microsoft Individual Code Signing (authenticode)
\& msCodeCom              Microsoft Commercial Code Signing (authenticode)
\& msCTLSign              Microsoft Trust List Signing
\& msEFS                  Microsoft Encrypted File System
.Ve
.PP
While IETF RFC 5280 says that \fBid-kp-serverAuth\fR and \fBid-kp-clientAuth\fR
are only for WWW use, in practice they are used for all kinds of TLS clients
and servers, and this is what OpenSSL assumes as well.
.PP
Examples:
.PP
.Vb 1
\& extendedKeyUsage = critical, codeSigning, *******
\&
\& extendedKeyUsage = serverAuth, clientAuth
.Ve
.SS "Subject Key Identifier"
.IX Subsection "Subject Key Identifier"
The SKID extension specification has a value with three choices.
If the value is the word \fBnone\fR then no SKID extension will be included.
If the value is the word \fBhash\fR, or by default for the \fBx509\fR, \fBreq\fR, and
\&\fBca\fR apps, the process specified in RFC 5280 section *******. (1) is followed:
The keyIdentifier is composed of the 160\-bit SHA\-1 hash of the value of the BIT
STRING subjectPublicKey (excluding the tag, length, and number of unused bits).
.PP
Otherwise, the value must be a hex string (possibly with \f(CW\*(C`:\*(C'\fR separating bytes)
to output directly, however, this is strongly discouraged.
.PP
Example:
.PP
.Vb 1
\& subjectKeyIdentifier = hash
.Ve
.SS "Authority Key Identifier"
.IX Subsection "Authority Key Identifier"
The AKID extension specification may have the value \fBnone\fR
indicating that no AKID shall be included.
Otherwise it may have the value \fBkeyid\fR or \fBissuer\fR
or both of them, separated by \f(CW\*(C`,\*(C'\fR.
Either or both can have the option \fBalways\fR,
indicated by putting a colon \f(CW\*(C`:\*(C'\fR between the value and this option.
For self-signed certificates the AKID is suppressed unless \fBalways\fR is present.
By default the \fBx509\fR, \fBreq\fR, and \fBca\fR apps behave as if
"none" was given for self-signed certificates and "keyid, issuer" otherwise.
.PP
If \fBkeyid\fR is present, an attempt is made to
copy the subject key identifier (SKID) from the issuer certificate except if
the issuer certificate is the same as the current one and it is not self-signed.
The hash of the public key related to the signing key is taken as fallback
if the issuer certificate is the same as the current certificate.
If \fBalways\fR is present but no value can be obtained, an error is returned.
.PP
If \fBissuer\fR is present, and in addition it has the option \fBalways\fR specified
or \fBkeyid\fR is not present,
then the issuer DN and serial number are copied from the issuer certificate.
.PP
Examples:
.PP
.Vb 1
\& authorityKeyIdentifier = keyid, issuer
\&
\& authorityKeyIdentifier = keyid, issuer:always
.Ve
.SS "Subject Alternative Name"
.IX Subsection "Subject Alternative Name"
This is a multi-valued extension that supports several types of name
identifier, including
\&\fBemail\fR (an email address),
\&\fBURI\fR (a uniform resource indicator),
\&\fBDNS\fR (a DNS domain name),
\&\fBRID\fR (a registered ID: OBJECT IDENTIFIER),
\&\fBIP\fR (an IP address),
\&\fBdirName\fR (a distinguished name),
and \fBotherName\fR.
The syntax of each is described in the following paragraphs.
.PP
The \fBemail\fR option has two special values.
\&\f(CW\*(C`copy\*(C'\fR will automatically include any email addresses
contained in the certificate subject name in the extension.
\&\f(CW\*(C`move\*(C'\fR will automatically move any email addresses
from the certificate subject name to the extension.
.PP
The IP address used in the \fBIP\fR option can be in either IPv4 or IPv6 format.
.PP
The value of \fBdirName\fR is specifies the configuration section containing
the distinguished name to use, as a set of name-value pairs.
Multi-valued AVAs can be formed by prefacing the name with a \fB+\fR character.
.PP
The value of \fBotherName\fR can include arbitrary data associated with an OID;
the value should be the OID followed by a semicolon and the content in specified
using the syntax in \fBASN1_generate_nconf\fR\|(3).
.PP
Examples:
.PP
.Vb 1
\& subjectAltName = email:copy, email:<EMAIL>, URI:http://my.example.com/
\&
\& subjectAltName = IP:***********
\&
\& subjectAltName = IP:13::17
\&
\& subjectAltName = email:<EMAIL>, RID:*******
\&
\& subjectAltName = otherName:*******;UTF8:some other identifier
\&
\& [extensions]
\& subjectAltName = dirName:dir_sect
\&
\& [dir_sect]
\& C = UK
\& O = My Organization
\& OU = My Unit
\& CN = My Name
.Ve
.PP
Non-ASCII Email Address conforming the syntax defined in Section 3.3 of RFC 6531
are provided as otherName.SmtpUTF8Mailbox. According to RFC 8398, the email
address should be provided as UTF8String. To enforce the valid representation in
the certificate, the SmtpUTF8Mailbox should be provided as follows
.PP
.Vb 3
\& subjectAltName=@alts
\& [alts]
\& otherName = *******.*******.9;FORMAT:UTF8,UTF8String:nonasciiname.example.com
.Ve
.SS "Issuer Alternative Name"
.IX Subsection "Issuer Alternative Name"
This extension supports most of the options of subject alternative name;
it does not support \fBemail:copy\fR.
It also adds \fBissuer:copy\fR as an allowed value, which copies any subject
alternative names from the issuer certificate, if possible.
.PP
Example:
.PP
.Vb 1
\& issuerAltName = issuer:copy
.Ve
.SS "Authority Info Access"
.IX Subsection "Authority Info Access"
This extension gives details about how to retrieve information that
related to the certificate that the CA makes available. The syntax is
\&\fBaccess_id;location\fR, where \fBaccess_id\fR is an object identifier
(although only a few values are well-known) and \fBlocation\fR has the same
syntax as subject alternative name (except that \fBemail:copy\fR is not supported).
.PP
Possible values for access_id include \fBOCSP\fR (OCSP responder),
\&\fBcaIssuers\fR (CA Issuers),
\&\fBad_timestamping\fR (AD Time Stamping),
\&\fBAD_DVCS\fR (ad dvcs),
\&\fBcaRepository\fR (CA Repository).
.PP
Examples:
.PP
.Vb 1
\& authorityInfoAccess = OCSP;URI:http://ocsp.example.com/,caIssuers;URI:http://myca.example.com/ca.cer
\&
\& authorityInfoAccess = OCSP;URI:http://ocsp.example.com/
.Ve
.SS "CRL distribution points"
.IX Subsection "CRL distribution points"
This is a multi-valued extension whose values can be either a name-value
pair using the same form as subject alternative name or a single value
specifying the section name containing all the distribution point values.
.PP
When a name-value pair is used, a DistributionPoint extension will
be set with the given value as the fullName field as the distributionPoint
value, and the reasons and cRLIssuer fields will be omitted.
.PP
When a single option is used, the value specifies the section, and that
section can have the following items:
.IP fullname 4
.IX Item "fullname"
The full name of the distribution point, in the same format as the subject
alternative name.
.IP relativename 4
.IX Item "relativename"
The value is taken as a distinguished name fragment that is set as the
value of the nameRelativeToCRLIssuer field.
.IP CRLIssuer 4
.IX Item "CRLIssuer"
The value must in the same format as the subject alternative name.
.IP reasons 4
.IX Item "reasons"
A multi-value field that contains the reasons for revocation. The recognized
values are: \f(CW\*(C`keyCompromise\*(C'\fR, \f(CW\*(C`CACompromise\*(C'\fR, \f(CW\*(C`affiliationChanged\*(C'\fR,
\&\f(CW\*(C`superseded\*(C'\fR, \f(CW\*(C`cessationOfOperation\*(C'\fR, \f(CW\*(C`certificateHold\*(C'\fR,
\&\f(CW\*(C`privilegeWithdrawn\*(C'\fR, and \f(CW\*(C`AACompromise\*(C'\fR.
.PP
Only one of \fBfullname\fR or \fBrelativename\fR should be specified.
.PP
Simple examples:
.PP
.Vb 1
\& crlDistributionPoints = URI:http://example.com/myca.crl
\&
\& crlDistributionPoints = URI:http://example.com/myca.crl, URI:http://example.org/my.crl
.Ve
.PP
Full distribution point example:
.PP
.Vb 2
\& [extensions]
\& crlDistributionPoints = crldp1_section
\&
\& [crldp1_section]
\& fullname = URI:http://example.com/myca.crl
\& CRLissuer = dirName:issuer_sect
\& reasons = keyCompromise, CACompromise
\&
\& [issuer_sect]
\& C = UK
\& O = Organisation
\& CN = Some Name
.Ve
.SS "Issuing Distribution Point"
.IX Subsection "Issuing Distribution Point"
This extension should only appear in CRLs. It is a multi-valued extension
whose syntax is similar to the "section" pointed to by the CRL distribution
points extension. The following names have meaning:
.IP fullname 4
.IX Item "fullname"
The full name of the distribution point, in the same format as the subject
alternative name.
.IP relativename 4
.IX Item "relativename"
The value is taken as a distinguished name fragment that is set as the
value of the nameRelativeToCRLIssuer field.
.IP onlysomereasons 4
.IX Item "onlysomereasons"
A multi-value field that contains the reasons for revocation. The recognized
values are: \f(CW\*(C`keyCompromise\*(C'\fR, \f(CW\*(C`CACompromise\*(C'\fR, \f(CW\*(C`affiliationChanged\*(C'\fR,
\&\f(CW\*(C`superseded\*(C'\fR, \f(CW\*(C`cessationOfOperation\*(C'\fR, \f(CW\*(C`certificateHold\*(C'\fR,
\&\f(CW\*(C`privilegeWithdrawn\*(C'\fR, and \f(CW\*(C`AACompromise\*(C'\fR.
.IP "onlyuser, onlyCA, onlyAA, indirectCRL" 4
.IX Item "onlyuser, onlyCA, onlyAA, indirectCRL"
The value for each of these names is a boolean.
.PP
Example:
.PP
.Vb 2
\& [extensions]
\& issuingDistributionPoint = critical, @idp_section
\&
\& [idp_section]
\& fullname = URI:http://example.com/myca.crl
\& indirectCRL = TRUE
\& onlysomereasons = keyCompromise, CACompromise
.Ve
.SS "Certificate Policies"
.IX Subsection "Certificate Policies"
This is a \fIraw\fR extension that supports all of the defined fields of the
certificate extension.
.PP
Policies without qualifiers are specified by giving the OID.
Multiple policies are comma-separated. For example:
.PP
.Vb 1
\& certificatePolicies = *******, *******
.Ve
.PP
To include policy qualifiers, use the "@section" syntax to point to a
section that specifies all the information.
.PP
The section referred to must include the policy OID using the name
\&\fBpolicyIdentifier\fR. cPSuri qualifiers can be included using the syntax:
.PP
.Vb 1
\& CPS.nnn = value
.Ve
.PP
where \f(CW\*(C`nnn\*(C'\fR is a number.
.PP
userNotice qualifiers can be set using the syntax:
.PP
.Vb 1
\& userNotice.nnn = @notice
.Ve
.PP
The value of the userNotice qualifier is specified in the relevant section.
This section can include \fBexplicitText\fR, \fBorganization\fR, and \fBnoticeNumbers\fR
options. explicitText and organization are text strings, noticeNumbers is a
comma separated list of numbers. The organization and noticeNumbers options
(if included) must BOTH be present. Some software might require
the \fBia5org\fR option at the top level; this changes the encoding from
Displaytext to IA5String.
.PP
Example:
.PP
.Vb 2
\& [extensions]
\& certificatePolicies = ia5org, *******, *******.8, @polsect
\&
\& [polsect]
\& policyIdentifier = *******
\& CPS.1 = "http://my.host.example.com/"
\& CPS.2 = "http://my.your.example.com/"
\& userNotice.1 = @notice
\&
\& [notice]
\& explicitText = "Explicit Text Here"
\& organization = "Organisation Name"
\& noticeNumbers = 1, 2, 3, 4
.Ve
.PP
The character encoding of explicitText can be specified by prefixing the
value with \fBUTF8\fR, \fBBMP\fR, or \fBVISIBLE\fR followed by colon. For example:
.PP
.Vb 2
\& [notice]
\& explicitText = "UTF8:Explicit Text Here"
.Ve
.SS "Policy Constraints"
.IX Subsection "Policy Constraints"
This is a multi-valued extension which consisting of the names
\&\fBrequireExplicitPolicy\fR or \fBinhibitPolicyMapping\fR and a non negative integer
value. At least one component must be present.
.PP
Example:
.PP
.Vb 1
\& policyConstraints = requireExplicitPolicy:3
.Ve
.SS "Inhibit Any Policy"
.IX Subsection "Inhibit Any Policy"
This is a string extension whose value must be a non negative integer.
.PP
Example:
.PP
.Vb 1
\& inhibitAnyPolicy = 2
.Ve
.SS "Name Constraints"
.IX Subsection "Name Constraints"
This is a multi-valued extension. The name should
begin with the word \fBpermitted\fR or \fBexcluded\fR followed by a \fB;\fR. The rest of
the name and the value follows the syntax of subjectAltName except
\&\fBemail:copy\fR
is not supported and the \fBIP\fR form should consist of an IP addresses and
subnet mask separated by a \fB/\fR.
.PP
Examples:
.PP
.Vb 1
\& nameConstraints = permitted;IP:***********/***********
\&
\& nameConstraints = permitted;email:.example.com
\&
\& nameConstraints = excluded;email:.com
.Ve
.SS "OCSP No Check"
.IX Subsection "OCSP No Check"
This is a string extension. It is parsed, but ignored.
.PP
Example:
.PP
.Vb 1
\& noCheck = ignored
.Ve
.SS "TLS Feature (aka Must Staple)"
.IX Subsection "TLS Feature (aka Must Staple)"
This is a multi-valued extension consisting of a list of TLS extension
identifiers. Each identifier may be a number (0..65535) or a supported name.
When a TLS client sends a listed extension, the TLS server is expected to
include that extension in its reply.
.PP
The supported names are: \fBstatus_request\fR and \fBstatus_request_v2\fR.
.PP
Example:
.PP
.Vb 1
\& tlsfeature = status_request
.Ve
.SH "DEPRECATED EXTENSIONS"
.IX Header "DEPRECATED EXTENSIONS"
The following extensions are non standard, Netscape specific and largely
obsolete. Their use in new applications is discouraged.
.SS "Netscape String extensions"
.IX Subsection "Netscape String extensions"
Netscape Comment (\fBnsComment\fR) is a string extension containing a comment
which will be displayed when the certificate is viewed in some browsers.
Other extensions of this type are: \fBnsBaseUrl\fR,
\&\fBnsRevocationUrl\fR, \fBnsCaRevocationUrl\fR, \fBnsRenewalUrl\fR, \fBnsCaPolicyUrl\fR
and \fBnsSslServerName\fR.
.SS "Netscape Certificate Type"
.IX Subsection "Netscape Certificate Type"
This is a multi-valued extensions which consists of a list of flags to be
included. It was used to indicate the purposes for which a certificate could
be used. The basicConstraints, keyUsage and extended key usage extensions are
now used instead.
.PP
Acceptable values for nsCertType are: \fBclient\fR, \fBserver\fR, \fBemail\fR,
\&\fBobjsign\fR, \fBreserved\fR, \fBsslCA\fR, \fBemailCA\fR, \fBobjCA\fR.
.SH "ARBITRARY EXTENSIONS"
.IX Header "ARBITRARY EXTENSIONS"
If an extension is not supported by the OpenSSL code then it must be encoded
using the arbitrary extension format. It is also possible to use the arbitrary
format for supported extensions. Extreme care should be taken to ensure that
the data is formatted correctly for the given extension type.
.PP
There are two ways to encode arbitrary extensions.
.PP
The first way is to use the word ASN1 followed by the extension content
using the same syntax as \fBASN1_generate_nconf\fR\|(3).
For example:
.PP
.Vb 3
\& [extensions]
\& ******* = critical, ASN1:UTF8String:Some random data
\& *******.1 = ASN1:SEQUENCE:seq_sect
\&
\& [seq_sect]
\& field1 = UTF8:field1
\& field2 = UTF8:field2
.Ve
.PP
It is also possible to use the word DER to include the raw encoded data in any
extension.
.PP
.Vb 2
\& ******* = critical, DER:01:02:03:04
\& *******.1 = DER:01020304
.Ve
.PP
The value following DER is a hex dump of the DER encoding of the extension
Any extension can be placed in this form to override the default behaviour.
For example:
.PP
.Vb 1
\& basicConstraints = critical, DER:00:01:02:03
.Ve
.SH WARNINGS
.IX Header "WARNINGS"
There is no guarantee that a specific implementation will process a given
extension. It may therefore be sometimes possible to use certificates for
purposes prohibited by their extensions because a specific application does
not recognize or honour the values of the relevant extensions.
.PP
The DER and ASN1 options should be used with caution. It is possible to create
invalid extensions if they are not used carefully.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBopenssl\-req\fR\|(1), \fBopenssl\-ca\fR\|(1), \fBopenssl\-x509\fR\|(1),
\&\fBASN1_generate_nconf\fR\|(3)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2004\-2023 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
