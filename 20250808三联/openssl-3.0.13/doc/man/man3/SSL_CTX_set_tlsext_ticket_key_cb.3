.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "SSL_CTX_SET_TLSEXT_TICKET_KEY_CB 3ossl"
.TH SSL_CTX_SET_TLSEXT_TICKET_KEY_CB 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
SSL_CTX_set_tlsext_ticket_key_evp_cb,
SSL_CTX_set_tlsext_ticket_key_cb
\&\- set a callback for session ticket processing
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/tls1.h>
\&
\& int SSL_CTX_set_tlsext_ticket_key_evp_cb(SSL_CTX sslctx,
\&     int (*cb)(SSL *s, unsigned char key_name[16],
\&               unsigned char iv[EVP_MAX_IV_LENGTH],
\&               EVP_CIPHER_CTX *ctx, EVP_MAC_CTX *hctx, int enc));
.Ve
.PP
The following function has been deprecated since OpenSSL 3.0, and can be
hidden entirely by defining \fBOPENSSL_API_COMPAT\fR with a suitable version value,
see \fBopenssl_user_macros\fR\|(7):
.PP
.Vb 4
\& int SSL_CTX_set_tlsext_ticket_key_cb(SSL_CTX sslctx,
\&     int (*cb)(SSL *s, unsigned char key_name[16],
\&               unsigned char iv[EVP_MAX_IV_LENGTH],
\&               EVP_CIPHER_CTX *ctx, HMAC_CTX *hctx, int enc));
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBSSL_CTX_set_tlsext_ticket_key_evp_cb()\fR sets a callback function \fIcb\fR for handling
session tickets for the ssl context \fIsslctx\fR. Session tickets, defined in
RFC5077 provide an enhanced session resumption capability where the server
implementation is not required to maintain per session state. It only applies
to TLS and there is no SSLv3 implementation.
.PP
The callback function \fIcb\fR will be called for every client instigated TLS
session when session ticket extension is presented in the TLS hello
message. It is the responsibility of this function to create or retrieve the
cryptographic parameters and to maintain their state.
.PP
The OpenSSL library uses your callback function to help implement a common TLS
ticket construction state according to RFC5077 Section 4 such that per session
state is unnecessary and a small set of cryptographic variables needs to be
maintained by the callback function implementation.
.PP
In order to reuse a session, a TLS client must send the session ticket
extension to the server. The client must send exactly one session ticket.
The server, through the callback function, either agrees to reuse the session
ticket information or it starts a full TLS handshake to create a new session
ticket.
.PP
Before the callback function is started \fIctx\fR and \fIhctx\fR have been
initialised with \fBEVP_CIPHER_CTX_reset\fR\|(3) and \fBEVP_MAC_CTX_new\fR\|(3)
respectively.
.PP
For new sessions tickets, when the client doesn't present a session ticket, or
an attempted retrieval of the ticket failed, or a renew option was indicated,
the callback function will be called with \fIenc\fR equal to 1. The OpenSSL
library expects that the function will set an arbitrary \fIname\fR, initialize
\&\fIiv\fR, and set the cipher context \fIctx\fR and the hash context \fIhctx\fR.
.PP
The \fIname\fR is 16 characters long and is used as a key identifier.
.PP
The \fIiv\fR length is the length of the IV of the corresponding cipher. The
maximum IV length is \fBEVP_MAX_IV_LENGTH\fR bytes defined in \fI<openssl/evp.h>\fR.
.PP
The initialization vector \fIiv\fR should be a random value. The cipher context
\&\fIctx\fR should use the initialisation vector \fIiv\fR. The cipher context can be
set using \fBEVP_EncryptInit_ex\fR\|(3). The hmac context and digest can be set using
\&\fBEVP_MAC_CTX_set_params\fR\|(3) with the \fBOSSL_MAC_PARAM_KEY\fR and
\&\fBOSSL_MAC_PARAM_DIGEST\fR parameters respectively.
.PP
When the client presents a session ticket, the callback function with be called
with \fIenc\fR set to 0 indicating that the \fIcb\fR function should retrieve a set
of parameters. In this case \fIname\fR and \fIiv\fR have already been parsed out of
the session ticket. The OpenSSL library expects that the \fIname\fR will be used
to retrieve a cryptographic parameters and that the cryptographic context
\&\fIctx\fR will be set with the retrieved parameters and the initialization vector
\&\fIiv\fR. using a function like \fBEVP_DecryptInit_ex\fR\|(3). The key material and
digest for \fIhctx\fR need to be set using \fBEVP_MAC_CTX_set_params\fR\|(3) with the
\&\fBOSSL_MAC_PARAM_KEY\fR and \fBOSSL_MAC_PARAM_DIGEST\fR parameters respectively.
.PP
If the \fIname\fR is still valid but a renewal of the ticket is required the
callback function should return 2. The library will call the callback again
with an argument of enc equal to 1 to set the new ticket.
.PP
The return value of the \fIcb\fR function is used by OpenSSL to determine what
further processing will occur. The following return values have meaning:
.IP 2 4
.IX Item "2"
This indicates that the \fIctx\fR and \fIhctx\fR have been set and the session can
continue on those parameters. Additionally it indicates that the session
ticket is in a renewal period and should be replaced. The OpenSSL library will
call \fIcb\fR again with an enc argument of 1 to set the new ticket (see RFC5077
3.3 paragraph 2).
.IP 1 4
.IX Item "1"
This indicates that the \fIctx\fR and \fIhctx\fR have been set and the session can
continue on those parameters.
.IP 0 4
This indicates that it was not possible to set/retrieve a session ticket and
the SSL/TLS session will continue by negotiating a set of cryptographic
parameters or using the alternate SSL/TLS resumption mechanism, session ids.
.Sp
If called with enc equal to 0 the library will call the \fIcb\fR again to get
a new set of parameters.
.IP "less than 0" 4
.IX Item "less than 0"
This indicates an error.
.PP
The \fBSSL_CTX_set_tlsext_ticket_key_cb()\fR function is identical to
\&\fBSSL_CTX_set_tlsext_ticket_key_evp_cb()\fR except that it takes a deprecated
HMAC_CTX pointer instead of an EVP_MAC_CTX one.
Before this callback function is started \fIhctx\fR will have been
initialised with \fBEVP_MAC_CTX_new\fR\|(3) and the digest set with
\&\fBEVP_MAC_CTX_set_params\fR\|(3).
The \fIhctx\fR key material can be set using \fBHMAC_Init_ex\fR\|(3).
.SH NOTES
.IX Header "NOTES"
Session resumption shortcuts the TLS so that the client certificate
negotiation don't occur. It makes up for this by storing client certificate
an all other negotiated state information encrypted within the ticket. In a
resumed session the applications will have all this state information available
exactly as if a full negotiation had occurred.
.PP
If an attacker can obtain the key used to encrypt a session ticket, they can
obtain the master secret for any ticket using that key and decrypt any traffic
using that session: even if the cipher suite supports forward secrecy. As
a result applications may wish to use multiple keys and avoid using long term
keys stored in files.
.PP
Applications can use longer keys to maintain a consistent level of security.
For example if a cipher suite uses 256 bit ciphers but only a 128 bit ticket key
the overall security is only 128 bits because breaking the ticket key will
enable an attacker to obtain the session keys.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
Returns 1 to indicate the callback function was set and 0 otherwise.
.SH EXAMPLES
.IX Header "EXAMPLES"
Reference Implementation:
.PP
.Vb 2
\& SSL_CTX_set_tlsext_ticket_key_evp_cb(SSL, ssl_tlsext_ticket_key_cb);
\& ...
\&
\& static int ssl_tlsext_ticket_key_cb(SSL *s, unsigned char key_name[16],
\&                                     unsigned char *iv, EVP_CIPHER_CTX *ctx,
\&                                     EVP_MAC_CTX *hctx, int enc)
\& {
\&     OSSL_PARAM params[3];
\&     your_type_t *key; /* something that you need to implement */
\&
\&     if (enc) { /* create new session */
\&         if (RAND_bytes(iv, EVP_MAX_IV_LENGTH) <= 0)
\&             return \-1; /* insufficient random */
\&
\&         key = currentkey(); /* something that you need to implement */
\&         if (key == NULL) {
\&             /* current key doesn\*(Aqt exist or isn\*(Aqt valid */
\&             key = createkey(); /*
\&                                 * Something that you need to implement.
\&                                 * createkey needs to initialise a name,
\&                                 * an aes_key, a hmac_key and optionally
\&                                 * an expire time.
\&                                 */
\&             if (key == NULL) /* key couldn\*(Aqt be created */
\&                 return 0;
\&         }
\&         memcpy(key_name, key\->name, 16);
\&
\&         if (EVP_EncryptInit_ex(&ctx, EVP_aes_256_cbc(), NULL, key\->aes_key,
\&                                iv) == 0)
\&            return \-1; /* error in cipher initialisation */
\&
\&         params[0] = OSSL_PARAM_construct_octet_string(OSSL_MAC_PARAM_KEY,
\&                                                       key\->hmac_key, 32);
\&         params[1] = OSSL_PARAM_construct_utf8_string(OSSL_MAC_PARAM_DIGEST,
\&                                                      "sha256", 0);
\&         params[2] = OSSL_PARAM_construct_end();
\&         if (EVP_MAC_CTX_set_params(hctx, params) == 0)
\&            return \-1; /* error in mac initialisation */
\&
\&         return 1;
\&
\&     } else { /* retrieve session */
\&         time_t t = time(NULL);
\&         key = findkey(key_name); /* something that you need to implement */
\&
\&         if (key == NULL || key\->expire < t)
\&             return 0;
\&
\&         params[0] = OSSL_PARAM_construct_octet_string(OSSL_KDF_PARAM_KEY,
\&                                                       key\->hmac_key, 32);
\&         params[1] = OSSL_PARAM_construct_utf8_string(OSSL_MAC_PARAM_DIGEST,
\&                                                      "sha256", 0);
\&         params[2] = OSSL_PARAM_construct_end();
\&         if (EVP_MAC_CTX_set_params(hctx, params) == 0)
\&            return \-1; /* error in mac initialisation */
\&
\&         if (EVP_DecryptInit_ex(&ctx, EVP_aes_256_cbc(), NULL, key\->aes_key,
\&                                iv) == 0)
\&            return \-1; /* error in cipher initialisation */
\&
\&         if (key\->expire < t \- RENEW_TIME) { /* RENEW_TIME: implement */
\&             /*
\&              * return 2 \- This session will get a new ticket even though the
\&              * current one is still valid.
\&              */
\&             return 2;
\&         }
\&         return 1;
\&     }
\& }
.Ve
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBssl\fR\|(7), \fBSSL_set_session\fR\|(3),
\&\fBSSL_session_reused\fR\|(3),
\&\fBSSL_CTX_add_session\fR\|(3),
\&\fBSSL_CTX_sess_number\fR\|(3),
\&\fBSSL_CTX_sess_set_get_cb\fR\|(3),
\&\fBSSL_CTX_set_session_id_context\fR\|(3),
.SH HISTORY
.IX Header "HISTORY"
The \fBSSL_CTX_set_tlsext_ticket_key_cb()\fR function was deprecated in OpenSSL 3.0.
.PP
The \fBSSL_CTX_set_tlsext_ticket_key_evp_cb()\fR function was introduced in
OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2014\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
