.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "EVP_PKEY_DECRYPT 3ossl"
.TH EVP_PKEY_DECRYPT 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
EVP_PKEY_decrypt_init, EVP_PKEY_decrypt_init_ex,
EVP_PKEY_decrypt \- decrypt using a public key algorithm
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/evp.h>
\&
\& int EVP_PKEY_decrypt_init(EVP_PKEY_CTX *ctx);
\& int EVP_PKEY_decrypt_init_ex(EVP_PKEY_CTX *ctx, const OSSL_PARAM params[]);
\& int EVP_PKEY_decrypt(EVP_PKEY_CTX *ctx,
\&                      unsigned char *out, size_t *outlen,
\&                      const unsigned char *in, size_t inlen);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
The \fBEVP_PKEY_decrypt_init()\fR function initializes a public key algorithm
context using key \fIpkey\fR for a decryption operation.
.PP
The \fBEVP_PKEY_decrypt_init_ex()\fR function initializes a public key algorithm
context using key \fIpkey\fR for a decryption operation and sets the
algorithm specific \fIparams\fR.
.PP
The \fBEVP_PKEY_decrypt()\fR function performs a public key decryption operation
using \fIctx\fR. The data to be decrypted is specified using the \fIin\fR and
\&\fIinlen\fR parameters. If \fIout\fR is NULL then the minimum required size of
the output buffer is written to the \fI*outlen\fR parameter.
.PP
If \fIout\fR is not NULL then before the call the \fI*outlen\fR parameter must
contain the length of the \fIout\fR buffer. If the call is successful the
decrypted data is written to \fIout\fR and the amount of the decrypted data
written to \fI*outlen\fR, otherwise an error is returned.
.SH NOTES
.IX Header "NOTES"
After the call to \fBEVP_PKEY_decrypt_init()\fR algorithm specific control
operations can be performed to set any appropriate parameters for the
operation.  These operations can be included in the \fBEVP_PKEY_decrypt_init_ex()\fR
call.
.PP
The function \fBEVP_PKEY_decrypt()\fR can be called more than once on the same
context if several operations are performed using the same parameters.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBEVP_PKEY_decrypt_init()\fR, \fBEVP_PKEY_decrypt_init_ex()\fR and \fBEVP_PKEY_decrypt()\fR
return 1 for success and 0 or a negative value for failure. In particular a
return value of \-2 indicates the operation is not supported by the public key
algorithm.
.SH EXAMPLES
.IX Header "EXAMPLES"
Decrypt data using OAEP (for RSA keys):
.PP
.Vb 2
\& #include <openssl/evp.h>
\& #include <openssl/rsa.h>
\&
\& EVP_PKEY_CTX *ctx;
\& ENGINE *eng;
\& unsigned char *out, *in;
\& size_t outlen, inlen;
\& EVP_PKEY *key;
\&
\& /*
\&  * NB: assumes key, eng, in, inlen are already set up
\&  * and that key is an RSA private key
\&  */
\& ctx = EVP_PKEY_CTX_new(key, eng);
\& if (!ctx)
\&     /* Error occurred */
\& if (EVP_PKEY_decrypt_init(ctx) <= 0)
\&     /* Error */
\& if (EVP_PKEY_CTX_set_rsa_padding(ctx, RSA_PKCS1_OAEP_PADDING) <= 0)
\&     /* Error */
\&
\& /* Determine buffer length */
\& if (EVP_PKEY_decrypt(ctx, NULL, &outlen, in, inlen) <= 0)
\&     /* Error */
\&
\& out = OPENSSL_malloc(outlen);
\&
\& if (!out)
\&     /* malloc failure */
\&
\& if (EVP_PKEY_decrypt(ctx, out, &outlen, in, inlen) <= 0)
\&     /* Error */
\&
\& /* Decrypted data is outlen bytes written to buffer out */
.Ve
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBEVP_PKEY_CTX_new\fR\|(3),
\&\fBEVP_PKEY_encrypt\fR\|(3),
\&\fBEVP_PKEY_sign\fR\|(3),
\&\fBEVP_PKEY_verify\fR\|(3),
\&\fBEVP_PKEY_verify_recover\fR\|(3),
\&\fBEVP_PKEY_derive\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
These functions were added in OpenSSL 1.0.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2006\-2022 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
