.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "DH_GENERATE_KEY 3ossl"
.TH DH_GENERATE_KEY 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
DH_generate_key, DH_compute_key, DH_compute_key_padded \- perform
Diffie\-Hellman key exchange
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/dh.h>
.Ve
.PP
The following functions have been deprecated since OpenSSL 3.0, and can be
hidden entirely by defining \fBOPENSSL_API_COMPAT\fR with a suitable version value,
see \fBopenssl_user_macros\fR\|(7):
.PP
.Vb 1
\& int DH_generate_key(DH *dh);
\&
\& int DH_compute_key(unsigned char *key, const BIGNUM *pub_key, DH *dh);
\&
\& int DH_compute_key_padded(unsigned char *key, const BIGNUM *pub_key, DH *dh);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
All of the functions described on this page are deprecated.
Applications should instead use \fBEVP_PKEY_derive_init\fR\|(3)
and \fBEVP_PKEY_derive\fR\|(3).
.PP
\&\fBDH_generate_key()\fR performs the first step of a Diffie-Hellman key
exchange by generating private and public DH values. By calling
\&\fBDH_compute_key()\fR or \fBDH_compute_key_padded()\fR, these are combined with
the other party's public value to compute the shared key.
.PP
\&\fBDH_generate_key()\fR expects \fBdh\fR to contain the shared parameters
\&\fBdh\->p\fR and \fBdh\->g\fR. It generates a random private DH value
unless \fBdh\->priv_key\fR is already set, and computes the
corresponding public value \fBdh\->pub_key\fR, which can then be
published.
.PP
\&\fBDH_compute_key()\fR computes the shared secret from the private DH value
in \fBdh\fR and the other party's public value in \fBpub_key\fR and stores
it in \fBkey\fR. \fBkey\fR must point to \fBDH_size(dh)\fR bytes of memory.
The padding style is RFC 5246 (8.1.2) that strips leading zero bytes.
It is not constant time due to the leading zero bytes being stripped.
The return value should be considered public.
.PP
\&\fBDH_compute_key_padded()\fR is similar but stores a fixed number of bytes.
The padding style is NIST SP 800\-56A (C.1) that retains leading zero bytes.
It is constant time due to the leading zero bytes being retained.
The return value should be considered public.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBDH_generate_key()\fR returns 1 on success, 0 otherwise.
.PP
\&\fBDH_compute_key()\fR returns the size of the shared secret on success, \-1
on error.
.PP
\&\fBDH_compute_key_padded()\fR returns \fBDH_size(dh)\fR on success, \-1 on error.
.PP
The error codes can be obtained by \fBERR_get_error\fR\|(3).
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBEVP_PKEY_derive\fR\|(3),
\&\fBDH_new\fR\|(3), \fBERR_get_error\fR\|(3), \fBRAND_bytes\fR\|(3), \fBDH_size\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
\&\fBDH_compute_key_padded()\fR was added in OpenSSL 1.0.2.
.PP
All of these functions were deprecated in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
