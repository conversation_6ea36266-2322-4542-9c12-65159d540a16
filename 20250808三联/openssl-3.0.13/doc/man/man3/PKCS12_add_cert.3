.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "PKCS12_ADD_CERT 3ossl"
.TH PKCS12_ADD_CERT 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
PKCS12_add_cert, PKCS12_add_key, PKCS12_add_key_ex,
PKCS12_add_secret \- Add an object to a set of PKCS#12 safeBags
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/pkcs12.h>
\&
\& PKCS12_SAFEBAG *PKCS12_add_cert(STACK_OF(PKCS12_SAFEBAG) **pbags, X509 *cert);
\& PKCS12_SAFEBAG *PKCS12_add_key(STACK_OF(PKCS12_SAFEBAG) **pbags,
\&                               EVP_PKEY *key, int key_usage, int iter,
\&                               int key_nid, const char *pass);
\& PKCS12_SAFEBAG *PKCS12_add_key_ex(STACK_OF(PKCS12_SAFEBAG) **pbags,
\&                                   EVP_PKEY *key, int key_usage, int iter,
\&                                   int key_nid, const char *pass,
\&                                   OSSL_LIB_CTX *ctx, const char *propq);
\&
\& PKCS12_SAFEBAG *PKCS12_add_secret(STACK_OF(PKCS12_SAFEBAG) **pbags,
\&                                  int nid_type, const unsigned char *value, int len);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
These functions create a new \fBPKCS12_SAFEBAG\fR and add it to the set of safeBags
in \fIpbags\fR.
.PP
\&\fBPKCS12_add_cert()\fR creates a PKCS#12 certBag containing the supplied
certificate and adds this to the set of PKCS#12 safeBags.
.PP
\&\fBPKCS12_add_key()\fR creates a PKCS#12 keyBag (unencrypted) or a pkcs8shroudedKeyBag
(encrypted) containing the supplied \fBEVP_PKEY\fR and adds this to the set of PKCS#12
safeBags. If \fIkey_nid\fR is not \-1 then the key is encrypted with the supplied
algorithm, using \fIpass\fR as the passphrase and \fIiter\fR as the iteration count. If
\&\fIiter\fR is zero then a default value for iteration count of 2048 is used.
.PP
\&\fBPKCS12_add_key_ex()\fR is identical to \fBPKCS12_add_key()\fR but allows for a library
context \fIctx\fR and property query \fIpropq\fR to be used to select algorithm
implementations.
.PP
\&\fBPKCS12_add_secret()\fR creates a PKCS#12 secretBag with an OID corresponding to
the supplied \fInid_type\fR containing the supplied value as an ASN1 octet string.
This is then added to the set of PKCS#12 safeBags.
.SH NOTES
.IX Header "NOTES"
If a certificate contains an \fIalias\fR or a \fIkeyid\fR then this will be
used for the corresponding \fBfriendlyName\fR or \fBlocalKeyID\fR in the
PKCS12 structure.
.PP
\&\fBPKCS12_add_key()\fR makes assumptions regarding the encoding of the given pass
phrase.
See \fBpassphrase\-encoding\fR\|(7) for more information.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
A valid \fBPKCS12_SAFEBAG\fR structure or NULL if an error occurred.
.SH "CONFORMING TO"
.IX Header "CONFORMING TO"
IETF RFC 7292 (<https://tools.ietf.org/html/rfc7292>)
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBPKCS12_create\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
\&\fBPKCS12_add_secret()\fR and \fBPKCS12_add_key_ex()\fR were added in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2020\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
