.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "X509_GET0_DISTINGUISHING_ID 3ossl"
.TH X509_GET0_DISTINGUISHING_ID 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
X509_get0_distinguishing_id, X509_set0_distinguishing_id,
X509_REQ_get0_distinguishing_id, X509_REQ_set0_distinguishing_id
\&\- get or set the Distinguishing ID for certificate operations
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/x509.h>
\&
\& ASN1_OCTET_STRING *X509_get0_distinguishing_id(X509 *x);
\& void X509_set0_distinguishing_id(X509 *x, ASN1_OCTET_STRING *distid);
\& ASN1_OCTET_STRING *X509_REQ_get0_distinguishing_id(X509_REQ *x);
\& void X509_REQ_set0_distinguishing_id(X509_REQ *x, ASN1_OCTET_STRING *distid);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
The Distinguishing ID is defined in FIPS 196 as follows:
.IP "\fIDistinguishing  identifier\fR" 4
.IX Item "Distinguishing identifier"
Information which unambiguously distinguishes
an entity in the authentication process.
.PP
The SM2 signature algorithm requires a Distinguishing ID value when generating
and verifying a signature, but the Ddistinguishing ID may also find other uses.
In the context of SM2, the Distinguishing ID is often referred to as the "SM2
ID".
.PP
For the purpose off verifying a certificate or a certification request, a
Distinguishing ID may be attached to it, so functions like \fBX509_verify\fR\|(3)
or \fBX509_REQ_verify\fR\|(3) have easy access to that identity for signature
verification.
.PP
\&\fBX509_get0_distinguishing_id()\fR gets the Distinguishing ID value of a certificate
\&\fBx\fR by returning an \fBASN1_OCTET_STRING\fR object which should not be freed by
the caller.
.PP
\&\fBX509_set0_distinguishing_id()\fR assigns \fBdistid\fR to the certificate \fBx\fR.
Calling this function transfers the memory management of the value to the X509
object, and therefore the value that has been passed in should not be freed by
the caller after this function has been called.
.PP
\&\fBX509_REQ_get0_distinguishing_id()\fR and \fBX509_REQ_set0_distinguishing_id()\fR
have the same functionality as \fBX509_get0_distinguishing_id()\fR and
\&\fBX509_set0_distinguishing_id()\fR except that they deal with  \fBX509_REQ\fR
objects instead of \fBX509\fR.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBX509_set0_distinguishing_id()\fR and \fBX509_REQ_set0_distinguishing_id()\fR do not
return a value.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBX509_verify\fR\|(3), \fBSM2\fR\|(7)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2019\-2020 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
