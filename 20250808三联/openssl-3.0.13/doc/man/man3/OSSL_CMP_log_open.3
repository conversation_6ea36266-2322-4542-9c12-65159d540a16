.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "OSSL_CMP_LOG_OPEN 3ossl"
.TH OSSL_CMP_LOG_OPEN 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
OSSL_CMP_log_open,
OSSL_CMP_log_close,
OSSL_CMP_severity,
OSSL_CMP_LOG_EMERG,
OSSL_CMP_LOG_ALERT,
OSSL_CMP_LOG_CRIT,
OSSL_CMP_LOG_ERR,
OSSL_CMP_LOG_WARNING,
OSSL_CMP_LOG_NOTICE,
OSSL_CMP_LOG_INFO,
OSSL_CMP_LOG_DEBUG,
OSSL_CMP_LOG_TRACE,
.PP
OSSL_CMP_log_cb_t,
OSSL_CMP_print_to_bio,
OSSL_CMP_print_errors_cb
\&\- functions for logging and error reporting
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/cmp_util.h>
\&
\& int  OSSL_CMP_log_open(void);
\& void OSSL_CMP_log_close(void);
\&
\& /* severity level declarations resemble those from syslog.h */
\& typedef int OSSL_CMP_severity;
\& #define OSSL_CMP_LOG_EMERG   0
\& #define OSSL_CMP_LOG_ALERT   1
\& #define OSSL_CMP_LOG_CRIT    2
\& #define OSSL_CMP_LOG_ERR     3
\& #define OSSL_CMP_LOG_WARNING 4
\& #define OSSL_CMP_LOG_NOTICE  5
\& #define OSSL_CMP_LOG_INFO    6
\& #define OSSL_CMP_LOG_DEBUG   7
\& #define OSSL_CMP_LOG_TRACE   8
\&
\& typedef int (*OSSL_CMP_log_cb_t)(const char *component,
\&                                  const char *file, int line,
\&                                  OSSL_CMP_severity level, const char *msg);
\& int OSSL_CMP_print_to_bio(BIO *bio, const char *component, const char *file,
\&                           int line, OSSL_CMP_severity level, const char *msg);
\& void OSSL_CMP_print_errors_cb(OSSL_CMP_log_cb_t log_fn);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
The logging and error reporting facility described here contains
convenience functions for CMP-specific logging,
including a string prefix mirroring the severity levels of syslog.h,
and enhancements of the error queue mechanism needed for large diagnostic
messages produced by the CMP library in case of certificate validation failures.
.PP
When an interesting activity is performed or an error occurs, some detail
should be provided for user information, debugging, and auditing purposes.
A CMP application can obtain this information by providing a callback function
with the following type:
.PP
.Vb 3
\& typedef int (*OSSL_CMP_log_cb_t)(const char *component,
\&                                  const char *file, int line,
\&                                  OSSL_CMP_severity level, const char *msg);
.Ve
.PP
The parameters may provide
some component info (which may be a module name and/or function name) or NULL,
a file pathname or NULL,
a line number or 0 indicating the source code location,
a severity level, and
a message string describing the nature of the event, terminated by '\en'.
.PP
Even when an activity is successful some warnings may be useful and some degree
of auditing may be required. Therefore, the logging facility supports a severity
level and the callback function has a \fIlevel\fR parameter indicating such a
level, such that error, warning, info, debug, etc. can be treated differently.
The callback is activated only when the severity level is sufficient according
to the current level of verbosity, which by default is \fBOSSL_CMP_LOG_INFO\fR.
.PP
The callback function may itself do non-trivial tasks like writing to
a log file or remote stream, which in turn may fail.
Therefore, the function should return 1 on success and 0 on failure.
.PP
\&\fBOSSL_CMP_log_open()\fR initializes the CMP-specific logging facility to output
everything to STDOUT. It fails if the integrated tracing is disabled or STDIO
is not available. It may be called during application startup.
Alternatively, \fBOSSL_CMP_CTX_set_log_cb\fR\|(3) can be used for more flexibility.
As long as neither if the two is used any logging output is ignored.
.PP
\&\fBOSSL_CMP_log_close()\fR may be called when all activities are finished to flush
any pending CMP-specific log output and deallocate related resources.
It may be called multiple times. It does get called at OpenSSL shutdown.
.PP
\&\fBOSSL_CMP_print_to_bio()\fR prints the given component info, filename, line number,
severity level, and log message or error queue message to the given \fIbio\fR.
\&\fIcomponent\fR usually is a function or module name.
If it is NULL, empty, or "(unknown function)" then "CMP" is used as fallback.
.PP
\&\fBOSSL_CMP_print_errors_cb()\fR outputs any entries in the OpenSSL error queue.
It is similar to \fBERR_print_errors_cb\fR\|(3) but uses the CMP log callback
function \fIlog_fn\fR for uniformity with CMP logging if not NULL. Otherwise it
prints to STDERR using \fBOSSL_CMP_print_to_bio\fR\|(3) (unless \fBOPENSSL_NO_STDIO\fR
is defined).
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBOSSL_CMP_log_close()\fR and \fBOSSL_CMP_print_errors_cb()\fR do not return anything.
.PP
All other functions return 1 on success, 0 on error.
.SH HISTORY
.IX Header "HISTORY"
The OpenSSL CMP support was added in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2007\-2023 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
