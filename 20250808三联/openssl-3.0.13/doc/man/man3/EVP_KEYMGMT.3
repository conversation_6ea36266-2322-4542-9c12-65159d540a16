.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "EVP_KEYMGMT 3ossl"
.TH EVP_KEYMGMT 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
EVP_KEYMGMT,
EVP_KEYMGMT_fetch,
EVP_KEYMGMT_up_ref,
EVP_KEYMGMT_free,
EVP_KEYMGMT_get0_provider,
EVP_KEYMGMT_is_a,
EVP_KEYMGMT_get0_description,
EVP_KEYMGMT_get0_name,
EVP_KEYMGMT_do_all_provided,
EVP_KEYMGMT_names_do_all,
EVP_KEYMGMT_gettable_params,
EVP_KEYMGMT_settable_params,
EVP_KEYMGMT_gen_settable_params
\&\- EVP key management routines
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/evp.h>
\&
\& typedef struct evp_keymgmt_st EVP_KEYMGMT;
\&
\& EVP_KEYMGMT *EVP_KEYMGMT_fetch(OSSL_LIB_CTX *ctx, const char *algorithm,
\&                                const char *properties);
\& int EVP_KEYMGMT_up_ref(EVP_KEYMGMT *keymgmt);
\& void EVP_KEYMGMT_free(EVP_KEYMGMT *keymgmt);
\& const OSSL_PROVIDER *EVP_KEYMGMT_get0_provider(const EVP_KEYMGMT *keymgmt);
\& int EVP_KEYMGMT_is_a(const EVP_KEYMGMT *keymgmt, const char *name);
\& const char *EVP_KEYMGMT_get0_name(const EVP_KEYMGMT *keymgmt);
\& const char *EVP_KEYMGMT_get0_description(const EVP_KEYMGMT *keymgmt);
\&
\& void EVP_KEYMGMT_do_all_provided(OSSL_LIB_CTX *libctx,
\&                                  void (*fn)(EVP_KEYMGMT *keymgmt, void *arg),
\&                                  void *arg);
\& int EVP_KEYMGMT_names_do_all(const EVP_KEYMGMT *keymgmt,
\&                              void (*fn)(const char *name, void *data),
\&                              void *data);
\& const OSSL_PARAM *EVP_KEYMGMT_gettable_params(const EVP_KEYMGMT *keymgmt);
\& const OSSL_PARAM *EVP_KEYMGMT_settable_params(const EVP_KEYMGMT *keymgmt);
\& const OSSL_PARAM *EVP_KEYMGMT_gen_settable_params(const EVP_KEYMGMT *keymgmt);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBEVP_KEYMGMT\fR is a method object that represents key management
implementations for different cryptographic algorithms.
This method object provides functionality to have providers import key
material from the outside, as well as export key material to the
outside.
Most of the functionality can only be used internally and has no
public interface, this object is simply passed into other functions
when needed.
.PP
\&\fBEVP_KEYMGMT_fetch()\fR looks for an algorithm within the provider that
has been loaded into the \fBOSSL_LIB_CTX\fR given by \fIctx\fR, having the
name given by \fIalgorithm\fR and the properties given by \fIproperties\fR.
.PP
\&\fBEVP_KEYMGMT_up_ref()\fR increments the reference count for the given
\&\fBEVP_KEYMGMT\fR \fIkeymgmt\fR.
.PP
\&\fBEVP_KEYMGMT_free()\fR decrements the reference count for the given
\&\fBEVP_KEYMGMT\fR \fIkeymgmt\fR, and when the count reaches zero, frees it.
.PP
\&\fBEVP_KEYMGMT_get0_provider()\fR returns the provider that has this particular
implementation.
.PP
\&\fBEVP_KEYMGMT_is_a()\fR checks if \fIkeymgmt\fR is an implementation of an
algorithm that's identifiable with \fIname\fR.
.PP
\&\fBEVP_KEYMGMT_get0_name()\fR returns the algorithm name from the provided
implementation for the given \fIkeymgmt\fR. Note that the \fIkeymgmt\fR may have
multiple synonyms associated with it. In this case the first name from the
algorithm definition is returned. Ownership of the returned string is
retained by the \fIkeymgmt\fR object and should not be freed by the caller.
.PP
\&\fBEVP_KEYMGMT_names_do_all()\fR traverses all names for the \fIkeymgmt\fR, and
calls \fIfn\fR with each name and \fIdata\fR.
.PP
\&\fBEVP_KEYMGMT_get0_description()\fR returns a description of the \fIkeymgmt\fR, meant
for display and human consumption.  The description is at the discretion
of the \fIkeymgmt\fR implementation.
.PP
\&\fBEVP_KEYMGMT_do_all_provided()\fR traverses all key keymgmt implementations by
all activated providers in the library context \fIlibctx\fR, and for each
of the implementations, calls \fIfn\fR with the implementation method and
\&\fIdata\fR as arguments.
.PP
\&\fBEVP_KEYMGMT_gettable_params()\fR and \fBEVP_KEYMGMT_settable_params()\fR return a
constant \fBOSSL_PARAM\fR\|(3) array that describes the names and types of key
parameters that can be retrieved or set.
\&\fBEVP_KEYMGMT_gettable_params()\fR is used by \fBEVP_PKEY_gettable_params\fR\|(3).
.PP
\&\fBEVP_KEYMGMT_gen_settable_params()\fR returns a constant \fBOSSL_PARAM\fR\|(3) array that
describes the names and types of key generation parameters that can be set via
\&\fBEVP_PKEY_CTX_set_params\fR\|(3).
.SH NOTES
.IX Header "NOTES"
\&\fBEVP_KEYMGMT_fetch()\fR may be called implicitly by other fetching
functions, using the same library context and properties.
Any other API that uses keys will typically do this.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBEVP_KEYMGMT_fetch()\fR returns a pointer to the key management
implementation represented by an EVP_KEYMGMT object, or NULL on
error.
.PP
\&\fBEVP_KEYMGMT_up_ref()\fR returns 1 on success, or 0 on error.
.PP
\&\fBEVP_KEYMGMT_names_do_all()\fR returns 1 if the callback was called for all
names. A return value of 0 means that the callback was not called for any names.
.PP
\&\fBEVP_KEYMGMT_free()\fR doesn't return any value.
.PP
\&\fBEVP_KEYMGMT_get0_provider()\fR returns a pointer to a provider object, or NULL
on error.
.PP
\&\fBEVP_KEYMGMT_is_a()\fR returns 1 of \fIkeymgmt\fR was identifiable,
otherwise 0.
.PP
\&\fBEVP_KEYMGMT_get0_name()\fR returns the algorithm name, or NULL on error.
.PP
\&\fBEVP_KEYMGMT_get0_description()\fR returns a pointer to a description, or NULL if
there isn't one.
.PP
\&\fBEVP_KEYMGMT_gettable_params()\fR, \fBEVP_KEYMGMT_settable_params()\fR and
\&\fBEVP_KEYMGMT_gen_settable_params()\fR return a constant \fBOSSL_PARAM\fR\|(3) array or
NULL on error.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBEVP_MD_fetch\fR\|(3), \fBOSSL_LIB_CTX\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
The functions described here were added in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2019\-2023 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
