.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "SMIME_WRITE_PKCS7 3ossl"
.TH SMIME_WRITE_PKCS7 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
SMIME_write_PKCS7 \- convert PKCS#7 structure to S/MIME format
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/pkcs7.h>
\&
\& int SMIME_write_PKCS7(BIO *out, PKCS7 *p7, BIO *data, int flags);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBSMIME_write_PKCS7()\fR adds the appropriate MIME headers to a PKCS#7
structure to produce an S/MIME message.
.PP
\&\fBout\fR is the BIO to write the data to. \fBp7\fR is the appropriate \fBPKCS7\fR
structure. If streaming is enabled then the content must be supplied in the
\&\fBdata\fR argument. \fBflags\fR is an optional set of flags.
.SH NOTES
.IX Header "NOTES"
The following flags can be passed in the \fBflags\fR parameter.
.PP
If \fBPKCS7_DETACHED\fR is set then cleartext signing will be used,
this option only makes sense for signedData where \fBPKCS7_DETACHED\fR
is also set when \fBPKCS7_sign()\fR is also called.
.PP
If the \fBPKCS7_TEXT\fR flag is set MIME headers for type \fBtext/plain\fR
are added to the content, this only makes sense if \fBPKCS7_DETACHED\fR
is also set.
.PP
If the \fBPKCS7_STREAM\fR flag is set streaming is performed. This flag should
only be set if \fBPKCS7_STREAM\fR was also set in the previous call to
\&\fBPKCS7_sign()\fR or \fBPKCS7_encrypt()\fR.
.PP
If cleartext signing is being used and \fBPKCS7_STREAM\fR not set then
the data must be read twice: once to compute the signature in \fBPKCS7_sign()\fR
and once to output the S/MIME message.
.PP
If streaming is performed the content is output in BER format using indefinite
length constructed encoding except in the case of signed data with detached
content where the content is absent and DER format is used.
.SH BUGS
.IX Header "BUGS"
\&\fBSMIME_write_PKCS7()\fR always base64 encodes PKCS#7 structures, there
should be an option to disable this.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBSMIME_write_PKCS7()\fR returns 1 for success or 0 for failure.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBERR_get_error\fR\|(3), \fBPKCS7_sign\fR\|(3),
\&\fBPKCS7_verify\fR\|(3), \fBPKCS7_encrypt\fR\|(3)
\&\fBPKCS7_decrypt\fR\|(3)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2002\-2016 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
