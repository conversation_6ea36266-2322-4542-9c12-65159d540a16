.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "CMS_GET1_RECEIPTREQUEST 3ossl"
.TH CMS_GET1_RECEIPTREQUEST 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
CMS_ReceiptRequest_create0_ex, CMS_ReceiptRequest_create0,
CMS_add1_ReceiptRequest, CMS_get1_ReceiptRequest, CMS_ReceiptRequest_get0_values
\&\- CMS signed receipt request functions
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/cms.h>
\&
\& CMS_ReceiptRequest *CMS_ReceiptRequest_create0_ex(
\&     unsigned char *id, int idlen, int allorfirst,
\&     STACK_OF(GENERAL_NAMES) *receiptList, STACK_OF(GENERAL_NAMES) *receiptsTo,
\&     OSSL_LIB_CTX *libctx);
\& CMS_ReceiptRequest *CMS_ReceiptRequest_create0(
\&     unsigned char *id, int idlen, int allorfirst,
\&     STACK_OF(GENERAL_NAMES) *receiptList, STACK_OF(GENERAL_NAMES) *receiptsTo);
\& int CMS_add1_ReceiptRequest(CMS_SignerInfo *si, CMS_ReceiptRequest *rr);
\& int CMS_get1_ReceiptRequest(CMS_SignerInfo *si, CMS_ReceiptRequest **prr);
\& void CMS_ReceiptRequest_get0_values(CMS_ReceiptRequest *rr, ASN1_STRING **pcid,
\&                                     int *pallorfirst,
\&                                     STACK_OF(GENERAL_NAMES) **plist,
\&                                     STACK_OF(GENERAL_NAMES) **prto);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBCMS_ReceiptRequest_create0_ex()\fR creates a signed receipt request
structure. The \fBsignedContentIdentifier\fR field is set using \fIid\fR and \fIidlen\fR,
or it is set to 32 bytes of pseudo random data if \fIid\fR is NULL.
If \fIreceiptList\fR is NULL the allOrFirstTier option in \fIreceiptsFrom\fR is used
and set to the value of the \fIallorfirst\fR parameter. If \fIreceiptList\fR is not
NULL the \fIreceiptList\fR option in \fIreceiptsFrom\fR is used. The \fIreceiptsTo\fR
parameter specifies the \fIreceiptsTo\fR field value. The library context \fIlibctx\fR
is used to find the public random generator.
.PP
\&\fBCMS_ReceiptRequest_create0()\fR is similar to
\&\fBCMS_ReceiptRequest_create0_ex()\fR but uses default values of NULL for the
library context \fIlibctx\fR.
.PP
The \fBCMS_add1_ReceiptRequest()\fR function adds a signed receipt request \fBrr\fR
to SignerInfo structure \fBsi\fR.
.PP
int \fBCMS_get1_ReceiptRequest()\fR looks for a signed receipt request in \fBsi\fR, if
any is found it is decoded and written to \fBprr\fR.
.PP
\&\fBCMS_ReceiptRequest_get0_values()\fR retrieves the values of a receipt request.
The signedContentIdentifier is copied to \fBpcid\fR. If the \fBallOrFirstTier\fR
option of \fBreceiptsFrom\fR is used its value is copied to \fBpallorfirst\fR
otherwise the \fBreceiptList\fR field is copied to \fBplist\fR. The \fBreceiptsTo\fR
parameter is copied to \fBprto\fR.
.SH NOTES
.IX Header "NOTES"
For more details of the meaning of the fields see RFC2634.
.PP
The contents of a signed receipt should only be considered meaningful if the
corresponding CMS_ContentInfo structure can be successfully verified using
\&\fBCMS_verify()\fR.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBCMS_ReceiptRequest_create0_ex()\fR and \fBCMS_ReceiptRequest_create0()\fR return
a signed receipt request structure or NULL if an error occurred.
.PP
\&\fBCMS_add1_ReceiptRequest()\fR returns 1 for success or 0 if an error occurred.
.PP
\&\fBCMS_get1_ReceiptRequest()\fR returns 1 is a signed receipt request is found and
decoded. It returns 0 if a signed receipt request is not present and \-1 if
it is present but malformed.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBERR_get_error\fR\|(3), \fBCMS_sign\fR\|(3),
\&\fBCMS_sign_receipt\fR\|(3), \fBCMS_verify\fR\|(3)
\&\fBCMS_verify_receipt\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
The function \fBCMS_ReceiptRequest_create0_ex()\fR was added in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2008\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
