.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "X509_CRL_GET0_BY_SERIAL 3ossl"
.TH X509_CRL_GET0_BY_SERIAL 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
X509_CRL_get0_by_serial, X509_CRL_get0_by_cert, X509_CRL_get_REVOKED,
X509_REVOKED_get0_serialNumber, X509_REVOKED_get0_revocationDate,
X509_REVOKED_set_serialNumber, X509_REVOKED_set_revocationDate,
X509_CRL_add0_revoked, X509_CRL_sort \- CRL revoked entry utility
functions
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/x509.h>
\&
\& int X509_CRL_get0_by_serial(X509_CRL *crl,
\&                             X509_REVOKED **ret, const ASN1_INTEGER *serial);
\& int X509_CRL_get0_by_cert(X509_CRL *crl, X509_REVOKED **ret, X509 *x);
\&
\& STACK_OF(X509_REVOKED) *X509_CRL_get_REVOKED(X509_CRL *crl);
\&
\& const ASN1_INTEGER *X509_REVOKED_get0_serialNumber(const X509_REVOKED *r);
\& const ASN1_TIME *X509_REVOKED_get0_revocationDate(const X509_REVOKED *r);
\&
\& int X509_REVOKED_set_serialNumber(X509_REVOKED *r, ASN1_INTEGER *serial);
\& int X509_REVOKED_set_revocationDate(X509_REVOKED *r, ASN1_TIME *tm);
\&
\& int X509_CRL_add0_revoked(X509_CRL *crl, X509_REVOKED *rev);
\&
\& int X509_CRL_sort(X509_CRL *crl);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBX509_CRL_get0_by_serial()\fR attempts to find a revoked entry in \fIcrl\fR for
serial number \fIserial\fR. If it is successful, it sets \fI*ret\fR to the internal
pointer of the matching entry. As a result, \fI*ret\fR \fBMUST NOT\fR be freed
after the call.
.PP
\&\fBX509_CRL_get0_by_cert()\fR is similar to \fBX509_get0_by_serial()\fR except it
looks for a revoked entry using the serial number of certificate \fIx\fR.
.PP
\&\fBX509_CRL_get_REVOKED()\fR returns an internal pointer to a STACK of all
revoked entries for \fIcrl\fR.
.PP
\&\fBX509_REVOKED_get0_serialNumber()\fR returns an internal pointer to the
serial number of \fIr\fR.
.PP
\&\fBX509_REVOKED_get0_revocationDate()\fR returns an internal pointer to the
revocation date of \fIr\fR.
.PP
\&\fBX509_REVOKED_set_serialNumber()\fR sets the serial number of \fIr\fR to \fIserial\fR.
The supplied \fIserial\fR pointer is not used internally so it should be
freed after use.
.PP
\&\fBX509_REVOKED_set_revocationDate()\fR sets the revocation date of \fIr\fR to
\&\fItm\fR. The supplied \fItm\fR pointer is not used internally so it should be
freed after use.
.PP
\&\fBX509_CRL_add0_revoked()\fR appends revoked entry \fIrev\fR to CRL \fIcrl\fR. The
pointer \fIrev\fR is used internally so it \fBMUST NOT\fR be freed after the call:
it is freed when the parent CRL is freed.
.PP
\&\fBX509_CRL_sort()\fR sorts the revoked entries of \fIcrl\fR into ascending serial
number order.
.SH NOTES
.IX Header "NOTES"
Applications can determine the number of revoked entries returned by
\&\fBX509_CRL_get_REVOKED()\fR using \fBsk_X509_REVOKED_num()\fR and examine each one
in turn using \fBsk_X509_REVOKED_value()\fR.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBX509_CRL_get0_by_serial()\fR and \fBX509_CRL_get0_by_cert()\fR return 0 for failure,
1 on success except if the revoked entry has the reason \f(CW\*(C`removeFromCRL\*(C'\fR (8),
in which case 2 is returned.
.PP
\&\fBX509_CRL_get_REVOKED()\fR returns a STACK of revoked entries.
.PP
\&\fBX509_REVOKED_get0_serialNumber()\fR returns an \fBASN1_INTEGER\fR structure.
.PP
\&\fBX509_REVOKED_get0_revocationDate()\fR returns an \fBASN1_TIME\fR structure.
.PP
\&\fBX509_REVOKED_set_serialNumber()\fR, \fBX509_REVOKED_set_revocationDate()\fR,
\&\fBX509_CRL_add0_revoked()\fR and \fBX509_CRL_sort()\fR return 1 for success and 0 for
failure.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBd2i_X509\fR\|(3),
\&\fBERR_get_error\fR\|(3),
\&\fBX509_get0_signature\fR\|(3),
\&\fBX509_get_ext_d2i\fR\|(3),
\&\fBX509_get_extension_flags\fR\|(3),
\&\fBX509_get_pubkey\fR\|(3),
\&\fBX509_get_subject_name\fR\|(3),
\&\fBX509_get_version\fR\|(3),
\&\fBX509_NAME_add_entry_by_txt\fR\|(3),
\&\fBX509_NAME_ENTRY_get_object\fR\|(3),
\&\fBX509_NAME_get_index_by_NID\fR\|(3),
\&\fBX509_NAME_print_ex\fR\|(3),
\&\fBX509_new\fR\|(3),
\&\fBX509_sign\fR\|(3),
\&\fBX509V3_get_d2i\fR\|(3),
\&\fBX509_verify_cert\fR\|(3)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2015\-2022 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
