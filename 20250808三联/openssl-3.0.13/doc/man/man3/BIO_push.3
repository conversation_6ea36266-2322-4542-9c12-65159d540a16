.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "BIO_PUSH 3ossl"
.TH BIO_PUSH 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
BIO_push, BIO_pop, BIO_set_next \- add and remove BIOs from a chain
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/bio.h>
\&
\& BIO *BIO_push(BIO *b, BIO *next);
\& BIO *BIO_pop(BIO *b);
\& void BIO_set_next(BIO *b, BIO *next);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBBIO_push()\fR pushes \fIb\fR on \fInext\fR.
If \fIb\fR is NULL the function does nothing and returns \fInext\fR.
Otherwise it prepends \fIb\fR, which may be a single BIO or a chain of BIOs,
to \fInext\fR (unless \fInext\fR is NULL).
It then makes a control call on \fIb\fR and returns \fIb\fR.
.PP
\&\fBBIO_pop()\fR removes the BIO \fIb\fR from any chain is is part of.
If \fIb\fR is NULL the function does nothing and returns NULL.
Otherwise it makes a control call on \fIb\fR and
returns the next BIO in the chain, or NULL if there is no next BIO.
The removed BIO becomes a single BIO with no association with
the original chain, it can thus be freed or be made part of a different chain.
.PP
\&\fBBIO_set_next()\fR replaces the existing next BIO in a chain with the BIO pointed to
by \fInext\fR. The new chain may include some of the same BIOs from the old chain
or it may be completely different.
.SH NOTES
.IX Header "NOTES"
The names of these functions are perhaps a little misleading. \fBBIO_push()\fR
joins two BIO chains whereas \fBBIO_pop()\fR deletes a single BIO from a chain,
the deleted BIO does not need to be at the end of a chain.
.PP
The process of calling \fBBIO_push()\fR and \fBBIO_pop()\fR on a BIO may have additional
consequences (a control call is made to the affected BIOs).
Any effects will be noted in the descriptions of individual BIOs.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBBIO_push()\fR returns the head of the chain,
which usually is \fIb\fR, or \fInext\fR if \fIb\fR is NULL.
.PP
\&\fBBIO_pop()\fR returns the next BIO in the chain,
or NULL if there is no next BIO.
.SH EXAMPLES
.IX Header "EXAMPLES"
For these examples suppose \fImd1\fR and \fImd2\fR are digest BIOs,
\&\fIb64\fR is a base64 BIO and \fIf\fR is a file BIO.
.PP
If the call:
.PP
.Vb 1
\& BIO_push(b64, f);
.Ve
.PP
is made then the new chain will be \fIb64\-f\fR. After making the calls
.PP
.Vb 2
\& BIO_push(md2, b64);
\& BIO_push(md1, md2);
.Ve
.PP
the new chain is \fImd1\-md2\-b64\-f\fR. Data written to \fImd1\fR will be digested
by \fImd1\fR and \fImd2\fR, base64 encoded, and finally written to \fIf\fR.
.PP
It should be noted that reading causes data to pass in the reverse
direction, that is data is read from \fIf\fR, base64 decoded,
and digested by \fImd2\fR and then \fImd1\fR.
.PP
The call:
.PP
.Vb 1
\& BIO_pop(md2);
.Ve
.PP
will return \fIb64\fR and the new chain will be \fImd1\-b64\-f\fR.
Data can be written to and read from \fImd1\fR as before,
except that \fImd2\fR will no more be applied.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBbio\fR\|(7)
.SH HISTORY
.IX Header "HISTORY"
The \fBBIO_set_next()\fR function was added in OpenSSL 1.1.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
