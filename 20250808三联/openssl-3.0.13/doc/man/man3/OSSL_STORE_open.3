.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "OSSL_STORE_OPEN 3ossl"
.TH OSSL_STORE_OPEN 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
OSSL_STORE_CTX, OSSL_STORE_post_process_info_fn,
OSSL_STORE_open, OSSL_STORE_open_ex,
OSSL_STORE_ctrl, OSSL_STORE_load, OSSL_STORE_eof,
OSSL_STORE_error, OSSL_STORE_close
\&\- Types and functions to read objects from a URI
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/store.h>
\&
\& typedef struct ossl_store_ctx_st OSSL_STORE_CTX;
\&
\& typedef OSSL_STORE_INFO *(*OSSL_STORE_post_process_info_fn)(OSSL_STORE_INFO *,
\&                                                             void *);
\&
\& OSSL_STORE_CTX *OSSL_STORE_open(const char *uri, const UI_METHOD *ui_method,
\&                                 void *ui_data,
\&                                 OSSL_STORE_post_process_info_fn post_process,
\&                                 void *post_process_data);
\& OSSL_STORE_CTX *
\& OSSL_STORE_open_ex(const char *uri, OSSL_LIB_CTX *libctx, const char *propq,
\&                    const UI_METHOD *ui_method, void *ui_data,
\&                    const OSSL_PARAM params[],
\&                    OSSL_STORE_post_process_info_fn post_process,
\&                    void *post_process_data);
\&
\& OSSL_STORE_INFO *OSSL_STORE_load(OSSL_STORE_CTX *ctx);
\& int OSSL_STORE_eof(OSSL_STORE_CTX *ctx);
\& int OSSL_STORE_error(OSSL_STORE_CTX *ctx);
\& int OSSL_STORE_close(OSSL_STORE_CTX *ctx);
.Ve
.PP
The following function has been deprecated since OpenSSL 3.0, and can be
hidden entirely by defining \fBOPENSSL_API_COMPAT\fR with a suitable version value,
see \fBopenssl_user_macros\fR\|(7):
.PP
.Vb 1
\& int OSSL_STORE_ctrl(OSSL_STORE_CTX *ctx, int cmd, ... /* args */);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
These functions help the application to fetch supported objects (see
"SUPPORTED OBJECTS" in \fBOSSL_STORE_INFO\fR\|(3) for information on which those are)
from a given URI.
The general method to do so is to "open" the URI using \fBOSSL_STORE_open()\fR,
read each available and supported object using \fBOSSL_STORE_load()\fR as long as
\&\fBOSSL_STORE_eof()\fR hasn't been reached, and finish it off with \fBOSSL_STORE_close()\fR.
.PP
The retrieved information is stored in a \fBOSSL_STORE_INFO\fR, which is further
described in \fBOSSL_STORE_INFO\fR\|(3).
.SS Types
.IX Subsection "Types"
\&\fBOSSL_STORE_CTX\fR is a context variable that holds all the internal
information for \fBOSSL_STORE_open()\fR, \fBOSSL_STORE_open_ex()\fR,
\&\fBOSSL_STORE_load()\fR, \fBOSSL_STORE_eof()\fR and \fBOSSL_STORE_close()\fR to work
together.
.SS Functions
.IX Subsection "Functions"
\&\fBOSSL_STORE_open_ex()\fR takes a uri or path \fIuri\fR, password UI method
\&\fIui_method\fR with associated data \fIui_data\fR, and post processing
callback \fIpost_process\fR with associated data \fIpost_process_data\fR,
a library context \fIlibctx\fR with an associated property query \fIpropq\fR,
and opens a channel to the data located at the URI and returns a
\&\fBOSSL_STORE_CTX\fR with all necessary internal information.
The given \fIui_method\fR and \fIui_data\fR will be reused by all
functions that use \fBOSSL_STORE_CTX\fR when interaction is needed,
for instance to provide a password.
The auxiliary \fBOSSL_PARAM\fR\|(3) parameters in \fIparams\fR can be set to further
modify the store operation.
The given \fIpost_process\fR and \fIpost_process_data\fR will be reused by
\&\fBOSSL_STORE_load()\fR to manipulate or drop the value to be returned.
The \fIpost_process\fR function drops values by returning NULL, which
will cause \fBOSSL_STORE_load()\fR to start its process over with loading
the next object, until \fIpost_process\fR returns something other than
NULL, or the end of data is reached as indicated by \fBOSSL_STORE_eof()\fR.
.PP
\&\fBOSSL_STORE_open()\fR is similar to \fBOSSL_STORE_open_ex()\fR but uses NULL for
the \fIparams\fR, the library context \fIlibctx\fR and property query \fIpropq\fR.
.PP
\&\fBOSSL_STORE_ctrl()\fR takes a \fBOSSL_STORE_CTX\fR, and command number \fIcmd\fR and
more arguments not specified here.
The available loader specific command numbers and arguments they each
take depends on the loader that's used and is documented together with
that loader.
.PP
There are also global controls available:
.IP \fBOSSL_STORE_C_USE_SECMEM\fR 4
.IX Item "OSSL_STORE_C_USE_SECMEM"
Controls if the loader should attempt to use secure memory for any
allocated \fBOSSL_STORE_INFO\fR and its contents.
This control expects one argument, a pointer to an \fIint\fR that is expected to
have the value 1 (yes) or 0 (no).
Any other value is an error.
.PP
\&\fBOSSL_STORE_load()\fR takes a \fBOSSL_STORE_CTX\fR and tries to load the next
available object and return it wrapped with \fBOSSL_STORE_INFO\fR.
.PP
\&\fBOSSL_STORE_eof()\fR takes a \fBOSSL_STORE_CTX\fR and checks if we've reached the end
of data.
.PP
\&\fBOSSL_STORE_error()\fR takes a \fBOSSL_STORE_CTX\fR and checks if an error occurred in
the last \fBOSSL_STORE_load()\fR call.
Note that it may still be meaningful to try and load more objects, unless
\&\fBOSSL_STORE_eof()\fR shows that the end of data has been reached.
.PP
\&\fBOSSL_STORE_close()\fR takes a \fBOSSL_STORE_CTX\fR, closes the channel that was opened
by \fBOSSL_STORE_open()\fR and frees all other information that was stored in the
\&\fBOSSL_STORE_CTX\fR, as well as the \fBOSSL_STORE_CTX\fR itself.
If \fIctx\fR is NULL it does nothing.
.SH NOTES
.IX Header "NOTES"
A string without a scheme prefix (that is, a non-URI string) is
implicitly interpreted as using the \fIfile:\fR scheme.
.PP
There are some tools that can be used together with
\&\fBOSSL_STORE_open()\fR to determine if any failure is caused by an unparsable
URI, or if it's a different error (such as memory allocation
failures); if the URI was parsable but the scheme unregistered, the
top error will have the reason \f(CW\*(C`OSSL_STORE_R_UNREGISTERED_SCHEME\*(C'\fR.
.PP
These functions make no direct assumption regarding the pass phrase received
from the password callback.
The loaders may make assumptions, however.
For example, the \fBfile:\fR scheme loader inherits the assumptions made by
OpenSSL functionality that handles the different file types; this is mostly
relevant for PKCS#12 objects.
See \fBpassphrase\-encoding\fR\|(7) for further information.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBOSSL_STORE_open()\fR returns a pointer to a \fBOSSL_STORE_CTX\fR on success, or
NULL on failure.
.PP
\&\fBOSSL_STORE_load()\fR returns a pointer to a \fBOSSL_STORE_INFO\fR on success, or NULL
on error or when end of data is reached.
Use \fBOSSL_STORE_error()\fR and \fBOSSL_STORE_eof()\fR to determine the meaning of a
returned NULL.
.PP
\&\fBOSSL_STORE_eof()\fR returns 1 if the end of data has been reached
or an error occurred, 0 otherwise.
.PP
\&\fBOSSL_STORE_error()\fR returns 1 if an error occurred in an \fBOSSL_STORE_load()\fR call,
otherwise 0.
.PP
\&\fBOSSL_STORE_ctrl()\fR and \fBOSSL_STORE_close()\fR returns 1 on success, or 0 on failure.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBossl_store\fR\|(7), \fBOSSL_STORE_INFO\fR\|(3), \fBOSSL_STORE_register_loader\fR\|(3),
\&\fBpassphrase\-encoding\fR\|(7)
.SH HISTORY
.IX Header "HISTORY"
\&\fBOSSL_STORE_open_ex()\fR was added in OpenSSL 3.0.
.PP
\&\fBOSSL_STORE_CTX\fR, \fBOSSL_STORE_post_process_info_fn()\fR, \fBOSSL_STORE_open()\fR,
\&\fBOSSL_STORE_ctrl()\fR, \fBOSSL_STORE_load()\fR, \fBOSSL_STORE_eof()\fR and \fBOSSL_STORE_close()\fR
were added in OpenSSL 1.1.1.
.PP
Handling of NULL \fIctx\fR argument for \fBOSSL_STORE_close()\fR
was introduced in OpenSSL 1.1.1h.
.PP
\&\fBOSSL_STORE_open_ex()\fR was added in OpenSSL 3.0.
.PP
\&\fBOSSL_STORE_ctrl()\fR and \fBOSSL_STORE_vctrl()\fR were deprecated in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2016\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
