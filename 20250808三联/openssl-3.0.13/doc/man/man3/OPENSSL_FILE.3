.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "OPENSSL_FILE 3ossl"
.TH OPENSSL_FILE 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
OPENSSL_FILE, OPENSSL_LINE, OPENSSL_FUNC,
OPENSSL_MSTR, OPENSSL_MSTR_HELPER
\&\- generic C programming utility macros
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/macros.h>
\&
\& #define OPENSSL_FILE /* typically: _\|_FILE_\|_ */
\& #define OPENSSL_LINE /* typically: _\|_LINE_\|_ */
\& #define OPENSSL_FUNC /* typically: _\|_func_\|_ */
\&
\& #define OPENSSL_MSTR_HELPER(x) #x
\& #define OPENSSL_MSTR(x) OPENSSL_MSTR_HELPER(x)
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
The macros \fBOPENSSL_FILE\fR and \fBOPENSSL_LINE\fR
typically yield the current filename and line number during C compilation.
When \fBOPENSSL_NO_FILENAMES\fR is defined they yield \fB""\fR and \fB0\fR, respectively.
.PP
The macro \fBOPENSSL_FUNC\fR attempts to yield the name of the C function
currently being compiled, as far as language and compiler versions allow.
Otherwise, it yields "(unknown function)".
.PP
The macro \fBOPENSSL_MSTR\fR yields the expansion of the macro given as argument,
which is useful for concatenation with string constants.
The macro \fBOPENSSL_MSTR_HELPER\fR is an auxiliary macro for this purpose.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
see above
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBcrypto\fR\|(7)
.SH HISTORY
.IX Header "HISTORY"
\&\fBOPENSSL_FUNC\fR, \fBOPENSSL_MSTR\fR, and \fBOPENSSL_MSTR_HELPER\fR
were added in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2018\-2019 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
