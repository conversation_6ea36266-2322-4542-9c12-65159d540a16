.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "CTLOG_STORE_NEW 3ossl"
.TH CTLOG_STORE_NEW 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
CTLOG_STORE_new_ex,
CTLOG_STORE_new, CTLOG_STORE_free,
CTLOG_STORE_load_default_file, CTLOG_STORE_load_file \-
Create and populate a Certificate Transparency log list
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/ct.h>
\&
\& CTLOG_STORE *CTLOG_STORE_new_ex(OSSL_LIB_CTX *libctx, const char *propq);
\& CTLOG_STORE *CTLOG_STORE_new(void);
\& void CTLOG_STORE_free(CTLOG_STORE *store);
\&
\& int CTLOG_STORE_load_default_file(CTLOG_STORE *store);
\& int CTLOG_STORE_load_file(CTLOG_STORE *store, const char *file);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
A CTLOG_STORE is a container for a list of CTLOGs (Certificate Transparency
logs). The list can be loaded from one or more files and then searched by LogID
(see RFC 6962, Section 3.2, for the definition of a LogID).
.PP
\&\fBCTLOG_STORE_new_ex()\fR creates an empty list of CT logs associated with
the library context \fIlibctx\fR and the property query string \fIpropq\fR.
.PP
\&\fBCTLOG_STORE_new()\fR does the same thing as \fBCTLOG_STORE_new_ex()\fR but with
the default library context and property query string.
.PP
The CTLOG_STORE is then populated by \fBCTLOG_STORE_load_default_file()\fR or
\&\fBCTLOG_STORE_load_file()\fR. \fBCTLOG_STORE_load_default_file()\fR loads from the default
file, which is named \fIct_log_list.cnf\fR in OPENSSLDIR (see the output of
\&\fBopenssl\-version\fR\|(1)). This can be overridden using an environment variable
named \fBCTLOG_FILE\fR. \fBCTLOG_STORE_load_file()\fR loads from a caller-specified file
path instead. Both of these functions append any loaded CT logs to the
CTLOG_STORE.
.PP
The expected format of the file is:
.PP
.Vb 1
\& enabled_logs=foo,bar
\&
\& [foo]
\& description = Log 1
\& key = <base64\-encoded DER SubjectPublicKeyInfo here>
\&
\& [bar]
\& description = Log 2
\& key = <base64\-encoded DER SubjectPublicKeyInfo here>
.Ve
.PP
Once a CTLOG_STORE is no longer required, it should be passed to
\&\fBCTLOG_STORE_free()\fR. This will delete all of the CTLOGs stored within, along
with the CTLOG_STORE itself.
.SH NOTES
.IX Header "NOTES"
If there are any invalid CT logs in a file, they are skipped and the remaining
valid logs will still be added to the CTLOG_STORE. A CT log will be considered
invalid if it is missing a "key" or "description" field.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
Both \fBCTLOG_STORE_load_default_file\fR and \fBCTLOG_STORE_load_file\fR return 1 if
all CT logs in the file are successfully parsed and loaded, 0 otherwise.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBct\fR\|(7),
\&\fBCTLOG_STORE_get0_log_by_id\fR\|(3),
\&\fBSSL_CTX_set_ctlog_list_file\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
CTLOG_STORE_new_ex was added in OpenSSL 3.0. All other functions were
added in OpenSSL 1.1.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2016\-2020 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
