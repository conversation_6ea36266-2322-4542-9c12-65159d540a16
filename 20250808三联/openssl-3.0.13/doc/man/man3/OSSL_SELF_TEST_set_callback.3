.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "OSSL_SELF_TEST_SET_CALLBACK 3ossl"
.TH OSSL_SELF_TEST_SET_CALLBACK 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
OSSL_SELF_TEST_set_callback,
OSSL_SELF_TEST_get_callback \- specify a callback for processing self tests
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/self_test.h>
\&
\& void OSSL_SELF_TEST_set_callback(OSSL_LIB_CTX *ctx, OSSL_CALLBACK *cb, void *cbarg);
\& void OSSL_SELF_TEST_get_callback(OSSL_LIB_CTX *ctx, OSSL_CALLBACK **cb, void **cbarg);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
Set or gets the optional application callback (and the callback argument) that
is called during self testing.
The application callback \fBOSSL_CALLBACK\fR\|(3) is associated with a \fBOSSL_LIB_CTX\fR.
The application callback function receives information about a running self test,
and may return a result to the calling self test.
See \fBopenssl\-core.h\fR\|(7) for further information on the callback.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBOSSL_SELF_TEST_get_callback()\fR returns the callback and callback argument that
has been set via \fBOSSL_SELF_TEST_set_callback()\fR for the given library context
\&\fIctx\fR.
These returned parameters will be NULL if \fBOSSL_SELF_TEST_set_callback()\fR has
not been called.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBopenssl\-core.h\fR\|(7),
\&\fBOSSL_PROVIDER\-FIPS\fR\|(7)
\&\fBOSSL_SELF_TEST_new\fR\|(3)
\&\fBOSSL_LIB_CTX\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
The functions described here were added in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2019\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
