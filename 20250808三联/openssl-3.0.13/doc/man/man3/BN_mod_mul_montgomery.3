.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "BN_MOD_MUL_MONTGOMERY 3ossl"
.TH BN_MOD_MUL_MONTGOMERY 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
BN_mod_mul_montgomery, BN_MONT_CTX_new,
BN_MONT_CTX_free, BN_MONT_CTX_set, BN_MONT_CTX_copy,
BN_from_montgomery, BN_to_montgomery \- Montgomery multiplication
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/bn.h>
\&
\& BN_MONT_CTX *BN_MONT_CTX_new(void);
\& void BN_MONT_CTX_free(BN_MONT_CTX *mont);
\&
\& int BN_MONT_CTX_set(BN_MONT_CTX *mont, const BIGNUM *m, BN_CTX *ctx);
\& BN_MONT_CTX *BN_MONT_CTX_copy(BN_MONT_CTX *to, BN_MONT_CTX *from);
\&
\& int BN_mod_mul_montgomery(BIGNUM *r, BIGNUM *a, BIGNUM *b,
\&                           BN_MONT_CTX *mont, BN_CTX *ctx);
\&
\& int BN_from_montgomery(BIGNUM *r, BIGNUM *a, BN_MONT_CTX *mont,
\&                        BN_CTX *ctx);
\&
\& int BN_to_montgomery(BIGNUM *r, BIGNUM *a, BN_MONT_CTX *mont,
\&                      BN_CTX *ctx);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
These functions implement Montgomery multiplication. They are used
automatically when \fBBN_mod_exp\fR\|(3) is called with suitable input,
but they may be useful when several operations are to be performed
using the same modulus.
.PP
\&\fBBN_MONT_CTX_new()\fR allocates and initializes a \fBBN_MONT_CTX\fR structure.
.PP
\&\fBBN_MONT_CTX_set()\fR sets up the \fImont\fR structure from the modulus \fIm\fR
by precomputing its inverse and a value R.
.PP
\&\fBBN_MONT_CTX_copy()\fR copies the \fBBN_MONT_CTX\fR \fIfrom\fR to \fIto\fR.
.PP
\&\fBBN_MONT_CTX_free()\fR frees the components of the \fBBN_MONT_CTX\fR, and, if
it was created by \fBBN_MONT_CTX_new()\fR, also the structure itself.
If \fBmont\fR is NULL, nothing is done.
.PP
\&\fBBN_mod_mul_montgomery()\fR computes Mont(\fIa\fR,\fIb\fR):=\fIa\fR*\fIb\fR*R^\-1 and places
the result in \fIr\fR.
.PP
\&\fBBN_from_montgomery()\fR performs the Montgomery reduction \fIr\fR = \fIa\fR*R^\-1.
.PP
\&\fBBN_to_montgomery()\fR computes Mont(\fIa\fR,R^2), i.e. \fIa\fR*R.
Note that \fIa\fR must be nonnegative and smaller than the modulus.
.PP
For all functions, \fIctx\fR is a previously allocated \fBBN_CTX\fR used for
temporary variables.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBBN_MONT_CTX_new()\fR returns the newly allocated \fBBN_MONT_CTX\fR, and NULL
on error.
.PP
\&\fBBN_MONT_CTX_free()\fR has no return value.
.PP
For the other functions, 1 is returned for success, 0 on error.
The error codes can be obtained by \fBERR_get_error\fR\|(3).
.SH WARNINGS
.IX Header "WARNINGS"
The inputs must be reduced modulo \fBm\fR, otherwise the result will be
outside the expected range.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBERR_get_error\fR\|(3), \fBBN_add\fR\|(3),
\&\fBBN_CTX_new\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
\&\fBBN_MONT_CTX_init()\fR was removed in OpenSSL 1.1.0
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2020 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
