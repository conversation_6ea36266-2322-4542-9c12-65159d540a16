.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "RSA_SIGN 3ossl"
.TH RSA_SIGN 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
RSA_sign, RSA_verify \- RSA signatures
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/rsa.h>
.Ve
.PP
The following functions have been deprecated since OpenSSL 3.0, and can be
hidden entirely by defining \fBOPENSSL_API_COMPAT\fR with a suitable version value,
see \fBopenssl_user_macros\fR\|(7):
.PP
.Vb 2
\& int RSA_sign(int type, const unsigned char *m, unsigned int m_len,
\&              unsigned char *sigret, unsigned int *siglen, RSA *rsa);
\&
\& int RSA_verify(int type, const unsigned char *m, unsigned int m_len,
\&                unsigned char *sigbuf, unsigned int siglen, RSA *rsa);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
All of the functions described on this page are deprecated.
Applications should instead use \fBEVP_PKEY_sign_init\fR\|(3), \fBEVP_PKEY_sign\fR\|(3),
\&\fBEVP_PKEY_verify_init\fR\|(3) and \fBEVP_PKEY_verify\fR\|(3).
.PP
\&\fBRSA_sign()\fR signs the message digest \fBm\fR of size \fBm_len\fR using the
private key \fBrsa\fR using RSASSA\-PKCS1\-v1_5 as specified in RFC 3447. It
stores the signature in \fBsigret\fR and the signature size in \fBsiglen\fR.
\&\fBsigret\fR must point to RSA_size(\fBrsa\fR) bytes of memory.
Note that PKCS #1 adds meta-data, placing limits on the size of the
key that can be used.
See \fBRSA_private_encrypt\fR\|(3) for lower-level
operations.
.PP
\&\fBtype\fR denotes the message digest algorithm that was used to generate
\&\fBm\fR.
If \fBtype\fR is \fBNID_md5_sha1\fR,
an SSL signature (MD5 and SHA1 message digests with PKCS #1 padding
and no algorithm identifier) is created.
.PP
\&\fBRSA_verify()\fR verifies that the signature \fBsigbuf\fR of size \fBsiglen\fR
matches a given message digest \fBm\fR of size \fBm_len\fR. \fBtype\fR denotes
the message digest algorithm that was used to generate the signature.
\&\fBrsa\fR is the signer's public key.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBRSA_sign()\fR returns 1 on success and 0 for failure.
\&\fBRSA_verify()\fR returns 1 on successful verification and 0 for failure.
.PP
The error codes can be obtained by \fBERR_get_error\fR\|(3).
.SH "CONFORMING TO"
.IX Header "CONFORMING TO"
SSL, PKCS #1 v2.0
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBERR_get_error\fR\|(3),
\&\fBRSA_private_encrypt\fR\|(3),
\&\fBRSA_public_decrypt\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
All of these functions were deprecated in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
