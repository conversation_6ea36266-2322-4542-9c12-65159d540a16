.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "SMIME_READ_ASN1 3ossl"
.TH SMIME_READ_ASN1 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
SMIME_read_ASN1_ex, SMIME_read_ASN1
\&\- parse S/MIME message
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/asn1.h>
\&
\& ASN1_VALUE *SMIME_read_ASN1_ex(BIO *in, int flags, BIO **bcont,
\&                                const ASN1_ITEM *it, ASN1_VALUE **x,
\&                                OSSL_LIB_CTX *libctx, const char *propq);
\& ASN1_VALUE *SMIME_read_ASN1(BIO *in, BIO **bcont, const ASN1_ITEM *it);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBSMIME_read_ASN1_ex()\fR parses a message in S/MIME format.
.PP
\&\fIin\fR is a BIO to read the message from.
If the \fIflags\fR argument contains \fBCMS_BINARY\fR then the input is assumed to be
in binary format and is not translated to canonical form.
If in addition \fBSMIME_ASCIICRLF\fR is set then the binary input is assumed
to be followed by \fBCR\fR and \fBLF\fR characters, else only by an \fBLF\fR character.
\&\fIx\fR can be used to optionally supply
a previously created \fIit\fR ASN1_VALUE object (such as CMS_ContentInfo or PKCS7),
it can be set to NULL. Valid values that can be used by ASN.1 structure \fIit\fR
are ASN1_ITEM_rptr(PKCS7) or ASN1_ITEM_rptr(CMS_ContentInfo). Any algorithm
fetches that occur during the operation will use the \fBOSSL_LIB_CTX\fR supplied in
the \fIlibctx\fR parameter, and use the property query string \fIpropq\fR See
"ALGORITHM FETCHING" in \fBcrypto\fR\|(7) for further details about algorithm fetching.
.PP
If cleartext signing is used then the content is saved in a memory bio which is
written to \fI*bcont\fR, otherwise \fI*bcont\fR is set to NULL.
.PP
The parsed ASN1_VALUE structure is returned or NULL if an error occurred.
.PP
\&\fBSMIME_read_ASN1()\fR is similar to \fBSMIME_read_ASN1_ex()\fR but sets the value of \fIx\fR
to NULL and the value of \fIflags\fR to 0.
.SH NOTES
.IX Header "NOTES"
The higher level functions \fBSMIME_read_CMS_ex\fR\|(3) and
\&\fBSMIME_read_PKCS7_ex\fR\|(3) should be used instead of \fBSMIME_read_ASN1_ex()\fR.
.PP
To support future functionality if \fIbcont\fR is not NULL \fI*bcont\fR should be
initialized to NULL.
.SH BUGS
.IX Header "BUGS"
The MIME parser used by \fBSMIME_read_ASN1_ex()\fR is somewhat primitive. While it will
handle most S/MIME messages more complex compound formats may not work.
.PP
The use of a memory BIO to hold the signed content limits the size of message
which can be processed due to memory restraints: a streaming single pass option
should be available.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBSMIME_read_ASN1_ex()\fR and \fBSMIME_read_ASN1()\fR return a valid \fBASN1_VALUE\fR
structure or \fBNULL\fR if an error occurred. The error can be obtained from
\&\fBERR_get_error\fR\|(3).
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBERR_get_error\fR\|(3),
\&\fBSMIME_read_CMS_ex\fR\|(3),
\&\fBSMIME_read_PKCS7_ex\fR\|(3),
\&\fBSMIME_write_ASN1\fR\|(3),
\&\fBSMIME_write_ASN1_ex\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
The function \fBSMIME_read_ASN1_ex()\fR was added in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2020\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
