.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "SMIME_WRITE_ASN1 3ossl"
.TH SMIME_WRITE_ASN1 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
SMIME_write_ASN1_ex, SMIME_write_ASN1
\&\- convert structure to S/MIME format
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/asn1.h>
\&
\& int SMIME_write_ASN1_ex(BIO *out, ASN1_VALUE *val, BIO *data, int flags,
\&                         int ctype_nid, int econt_nid,
\&                         STACK_OF(X509_ALGOR) *mdalgs, const ASN1_ITEM *it,
\&                         OSSL_LIB_CTX *libctx, const char *propq);
\&
\& int SMIME_write_ASN1(BIO *out,
\&     ASN1_VALUE *val, BIO *data, int flags, int ctype_nid, int econt_nid,
\&     STACK_OF(X509_ALGOR) *mdalgs, const ASN1_ITEM *it);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBSMIME_write_ASN1_ex()\fR adds the appropriate MIME headers to an object
structure to produce an S/MIME message.
.PP
\&\fIout\fR is the BIO to write the data to. \fIvalue\fR is the appropriate ASN1_VALUE
structure (either CMS_ContentInfo or PKCS7). If streaming is enabled then the
content must be supplied via \fIdata\fR.
\&\fIflags\fR is an optional set of flags. \fIctype_nid\fR is the NID of the content
type, \fIecont_nid\fR is the NID of the embedded content type and \fImdalgs\fR is a
list of signed data digestAlgorithms. Valid values that can be used by the
ASN.1 structure \fIit\fR are ASN1_ITEM_rptr(PKCS7) or ASN1_ITEM_rptr(CMS_ContentInfo).
The library context \fIlibctx\fR and the property query \fIpropq\fR are used when
retrieving algorithms from providers.
.SH NOTES
.IX Header "NOTES"
The higher level functions \fBSMIME_write_CMS\fR\|(3) and
\&\fBSMIME_write_PKCS7\fR\|(3) should be used instead of \fBSMIME_write_ASN1()\fR.
.PP
The following flags can be passed in the \fBflags\fR parameter.
.PP
If \fBCMS_DETACHED\fR is set then cleartext signing will be used, this option only
makes sense for SignedData where \fBCMS_DETACHED\fR is also set when the \fBsign()\fR
method is called.
.PP
If the \fBCMS_TEXT\fR flag is set MIME headers for type \fBtext/plain\fR are added to
the content, this only makes sense if \fBCMS_DETACHED\fR is also set.
.PP
If the \fBCMS_STREAM\fR flag is set streaming is performed. This flag should only
be set if \fBCMS_STREAM\fR was also set in the previous call to a CMS_ContentInfo
or PKCS7 creation function.
.PP
If cleartext signing is being used and \fBCMS_STREAM\fR not set then the data must
be read twice: once to compute the signature in sign method and once to output
the S/MIME message.
.PP
If streaming is performed the content is output in BER format using indefinite
length constructed encoding except in the case of signed data with detached
content where the content is absent and DER format is used.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBSMIME_write_ASN1_ex()\fR and \fBSMIME_write_ASN1()\fR return 1 for success or
0 for failure.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBERR_get_error\fR\|(3),
\&\fBSMIME_write_CMS\fR\|(3),
\&\fBSMIME_write_PKCS7\fR\|(3)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2020 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
