.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "X509_CMP 3ossl"
.TH X509_CMP 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
X509_cmp, X509_NAME_cmp,
X509_issuer_and_serial_cmp, X509_issuer_name_cmp, X509_subject_name_cmp,
X509_CRL_cmp, X509_CRL_match
\&\- compare X509 certificates and related values
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/x509.h>
\&
\& int X509_cmp(const X509 *a, const X509 *b);
\& int X509_NAME_cmp(const X509_NAME *a, const X509_NAME *b);
\& int X509_issuer_and_serial_cmp(const X509 *a, const X509 *b);
\& int X509_issuer_name_cmp(const X509 *a, const X509 *b);
\& int X509_subject_name_cmp(const X509 *a, const X509 *b);
\& int X509_CRL_cmp(const X509_CRL *a, const X509_CRL *b);
\& int X509_CRL_match(const X509_CRL *a, const X509_CRL *b);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
This set of functions are used to compare X509 objects, including X509
certificates, X509 CRL objects and various values in an X509 certificate.
.PP
The \fBX509_cmp()\fR function compares two \fBX509\fR objects indicated by parameters
\&\fIa\fR and \fIb\fR. The comparison is based on the \fBmemcmp\fR result of the hash
values of two \fBX509\fR objects and the canonical (DER) encoding values.
.PP
The \fBX509_NAME_cmp()\fR function compares two \fBX509_NAME\fR objects indicated by
parameters \fIa\fR and \fIb\fR. The comparison is based on the \fBmemcmp\fR result of the
canonical (DER) encoding values of the two objects using \fBi2d_X509_NAME\fR\|(3).
This procedure adheres to the matching rules for Distinguished Names (DN)
given in RFC 4517 section 4.2.15 and RFC 5280 section 7.1.
In particular, the order of Relative Distinguished Names (RDNs) is relevant.
On the other hand, if an RDN is multi-valued, i.e., it contains a set of
AttributeValueAssertions (AVAs), its members are effectively not ordered.
.PP
The \fBX509_issuer_and_serial_cmp()\fR function compares the serial number and issuer
values in the given \fBX509\fR objects \fIa\fR and \fIb\fR.
.PP
The \fBX509_issuer_name_cmp()\fR, \fBX509_subject_name_cmp()\fR and \fBX509_CRL_cmp()\fR functions
are effectively wrappers of the \fBX509_NAME_cmp()\fR function. These functions compare
issuer names and subject names of the  objects, or issuers of \fBX509_CRL\fR
objects, respectively.
.IX Xref "509"
.PP
The \fBX509_CRL_match()\fR function compares two \fBX509_CRL\fR objects. Unlike the
\&\fBX509_CRL_cmp()\fR function, this function compares the whole CRL content instead
of just the issuer name.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
The \fBX509\fR comparison functions return \fB\-1\fR, \fB0\fR, or \fB1\fR if object \fIa\fR is
found to be less than, to match, or be greater than object \fIb\fR, respectively.
.PP
\&\fBX509_NAME_cmp()\fR, \fBX509_issuer_and_serial_cmp()\fR, \fBX509_issuer_name_cmp()\fR,
\&\fBX509_subject_name_cmp()\fR, \fBX509_CRL_cmp()\fR, and \fBX509_CRL_match()\fR
may return \fB\-2\fR to indicate an error.
.SH NOTES
.IX Header "NOTES"
These functions in fact utilize the underlying \fBmemcmp\fR of the C library to do
the comparison job. Data to be compared varies from DER encoding data, hash
value or \fBASN1_STRING\fR. The sign of the comparison can be used to order the
objects but it does not have a special meaning in some cases.
.PP
\&\fBX509_NAME_cmp()\fR and wrappers utilize the value \fB\-2\fR to indicate errors in some
circumstances, which could cause confusion for the applications.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBi2d_X509_NAME\fR\|(3), \fBi2d_X509\fR\|(3)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2019\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
