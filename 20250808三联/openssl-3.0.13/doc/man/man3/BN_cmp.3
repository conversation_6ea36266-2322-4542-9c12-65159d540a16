.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "BN_CMP 3ossl"
.TH BN_CMP 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
BN_cmp, BN_ucmp, BN_is_zero, BN_is_one, BN_is_word, BN_abs_is_word, BN_is_odd \- BIGNUM comparison and test functions
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/bn.h>
\&
\& int BN_cmp(const BIGNUM *a, const BIGNUM *b);
\& int BN_ucmp(const BIGNUM *a, const BIGNUM *b);
\&
\& int BN_is_zero(const BIGNUM *a);
\& int BN_is_one(const BIGNUM *a);
\& int BN_is_word(const BIGNUM *a, const BN_ULONG w);
\& int BN_abs_is_word(const BIGNUM *a, const BN_ULONG w);
\& int BN_is_odd(const BIGNUM *a);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBBN_cmp()\fR compares the numbers \fIa\fR and \fIb\fR. \fBBN_ucmp()\fR compares their
absolute values.
.PP
\&\fBBN_is_zero()\fR, \fBBN_is_one()\fR, \fBBN_is_word()\fR and \fBBN_abs_is_word()\fR test if
\&\fIa\fR equals 0, 1, \fIw\fR, or |\fIw\fR| respectively.
\&\fBBN_is_odd()\fR tests if \fIa\fR is odd.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBBN_cmp()\fR returns \-1 if \fIa\fR < \fIb\fR, 0 if \fIa\fR == \fIb\fR and 1 if
\&\fIa\fR > \fIb\fR. \fBBN_ucmp()\fR is the same using the absolute values
of \fIa\fR and \fIb\fR.
.PP
\&\fBBN_is_zero()\fR, \fBBN_is_one()\fR \fBBN_is_word()\fR, \fBBN_abs_is_word()\fR and
\&\fBBN_is_odd()\fR return 1 if the condition is true, 0 otherwise.
.SH HISTORY
.IX Header "HISTORY"
Prior to OpenSSL 1.1.0, \fBBN_is_zero()\fR, \fBBN_is_one()\fR, \fBBN_is_word()\fR,
\&\fBBN_abs_is_word()\fR and \fBBN_is_odd()\fR were macros.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
