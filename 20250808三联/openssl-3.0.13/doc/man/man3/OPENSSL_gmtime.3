.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "OPENSSL_GMTIME 3ossl"
.TH OPENSSL_GMTIME 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
OPENSSL_gmtime,
OPENSSL_gmtime_adj,
OPENSSL_gmtime_diff \- platform\-agnostic OpenSSL time routines
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/crypto.h>
\&
\& struct tm *OPENSSL_gmtime(const time_t *timer, struct tm *result);
\& int OPENSSL_gmtime_adj(struct tm *tm, int offset_day, long offset_sec);
\& int OPENSSL_gmtime_diff(int *pday, int *psec,
\&                        const struct tm *from, const struct tm *to);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBOPENSSL_gmtime()\fR returns the UTC time specified by \fItimer\fR into the provided
\&\fIresult\fR argument.
.PP
\&\fBOPENSSL_gmtime_adj()\fR adds the offsets in \fIoffset_day\fR and \fIoffset_sec\fR to \fItm\fR.
.PP
\&\fBOPENSSL_gmtime_diff()\fR calculates the difference between \fIfrom\fR and \fIto\fR.
.SH NOTES
.IX Header "NOTES"
It is an error to call \fBOPENSSL_gmtime()\fR with \fIresult\fR equal to NULL. The
contents of the time_t given by \fItimer\fR are stored into the \fIresult\fR. Calling
with \fItimer\fR equal to NULL means use the current time.
.PP
\&\fBOPENSSL_gmtime_adj()\fR converts \fItm\fR into a days and seconds value, adds the
offsets, then converts back into a \fIstruct tm\fR specified by \fItm\fR. Leap seconds
are not considered.
.PP
\&\fBOPENSSL_gmtime_diff()\fR calculates the difference between the two \fIstruct tm\fR
structures \fIfrom\fR and \fIto\fR. The difference in days is placed into \fI*pday\fR,
the remaining seconds are placed to \fI*psec\fR. The value in \fI*psec\fR will be less
than the number of seconds per day (3600). Leap seconds are not considered.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBOPENSSL_gmtime()\fR returns NULL on error, or \fIresult\fR on success.
.PP
\&\fBOPENSSL_gmtime_adj()\fR and \fBOPENSSL_gmtime_diff()\fR return 0 on error, and 1 on success.
.SH HISTORY
.IX Header "HISTORY"
\&\fBOPENSSL_gmtime()\fR, \fBOPENSSL_gmtime_adj()\fR and \fBOPENSSL_gmtime_diff()\fR have been
in OpenSSL since 1.0.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2022 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
