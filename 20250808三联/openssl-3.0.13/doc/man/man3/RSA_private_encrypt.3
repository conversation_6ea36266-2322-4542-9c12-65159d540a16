.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "RSA_PRIVATE_ENCRYPT 3ossl"
.TH RSA_PRIVATE_ENCRYPT 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
RSA_private_encrypt, RSA_public_decrypt \- low\-level signature operations
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/rsa.h>
.Ve
.PP
The following functions have been deprecated since OpenSSL 3.0, and can be
hidden entirely by defining \fBOPENSSL_API_COMPAT\fR with a suitable version value,
see \fBopenssl_user_macros\fR\|(7):
.PP
.Vb 2
\& int RSA_private_encrypt(int flen, unsigned char *from,
\&                         unsigned char *to, RSA *rsa, int padding);
\&
\& int RSA_public_decrypt(int flen, unsigned char *from,
\&                        unsigned char *to, RSA *rsa, int padding);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
Both of the functions described on this page are deprecated.
Applications should instead use \fBEVP_PKEY_sign_init_ex\fR\|(3),
\&\fBEVP_PKEY_sign\fR\|(3), \fBEVP_PKEY_verify_recover_init\fR\|(3), and
\&\fBEVP_PKEY_verify_recover\fR\|(3).
.PP
These functions handle RSA signatures at a low-level.
.PP
\&\fBRSA_private_encrypt()\fR signs the \fBflen\fR bytes at \fBfrom\fR (usually a
message digest with an algorithm identifier) using the private key
\&\fBrsa\fR and stores the signature in \fBto\fR. \fBto\fR must point to
\&\fBRSA_size(rsa)\fR bytes of memory.
.PP
\&\fBpadding\fR denotes one of the following modes:
.IP RSA_PKCS1_PADDING 4
.IX Item "RSA_PKCS1_PADDING"
PKCS #1 v1.5 padding. This function does not handle the
\&\fBalgorithmIdentifier\fR specified in PKCS #1. When generating or
verifying PKCS #1 signatures, \fBRSA_sign\fR\|(3) and \fBRSA_verify\fR\|(3) should be
used.
.IP RSA_NO_PADDING 4
.IX Item "RSA_NO_PADDING"
Raw RSA signature. This mode should \fIonly\fR be used to implement
cryptographically sound padding modes in the application code.
Signing user data directly with RSA is insecure.
.PP
\&\fBRSA_public_decrypt()\fR recovers the message digest from the \fBflen\fR
bytes long signature at \fBfrom\fR using the signer's public key
\&\fBrsa\fR. \fBto\fR must point to a memory section large enough to hold the
message digest (which is smaller than \fBRSA_size(rsa) \-
11\fR). \fBpadding\fR is the padding mode that was used to sign the data.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBRSA_private_encrypt()\fR returns the size of the signature (i.e.,
RSA_size(rsa)). \fBRSA_public_decrypt()\fR returns the size of the
recovered message digest.
.PP
On error, \-1 is returned; the error codes can be
obtained by \fBERR_get_error\fR\|(3).
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBERR_get_error\fR\|(3),
\&\fBRSA_sign\fR\|(3), \fBRSA_verify\fR\|(3),
\&\fBEVP_PKEY_sign\fR\|(3), \fBEVP_PKEY_verify_recover\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
Both of these functions were deprecated in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
