.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "X509_PUBKEY_NEW 3ossl"
.TH X509_PUBKEY_NEW 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
X509_PUBKEY_new_ex, X509_PUBKEY_new, X509_PUBKEY_free, X509_PUBKEY_dup,
X509_PUBKEY_set, X509_PUBKEY_get0, X509_PUBKEY_get,
d2i_PUBKEY_ex, d2i_PUBKEY, i2d_PUBKEY, d2i_PUBKEY_bio, d2i_PUBKEY_fp,
i2d_PUBKEY_fp, i2d_PUBKEY_bio, X509_PUBKEY_set0_param, X509_PUBKEY_get0_param,
X509_PUBKEY_eq \- SubjectPublicKeyInfo public key functions
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/x509.h>
\&
\& X509_PUBKEY *X509_PUBKEY_new_ex(OSSL_LIB_CTX *libctx, const char *propq);
\& X509_PUBKEY *X509_PUBKEY_new(void);
\& void X509_PUBKEY_free(X509_PUBKEY *a);
\& X509_PUBKEY *X509_PUBKEY_dup(const X509_PUBKEY *a);
\&
\& int X509_PUBKEY_set(X509_PUBKEY **x, EVP_PKEY *pkey);
\& EVP_PKEY *X509_PUBKEY_get0(const X509_PUBKEY *key);
\& EVP_PKEY *X509_PUBKEY_get(const X509_PUBKEY *key);
\&
\& EVP_PKEY *d2i_PUBKEY_ex(EVP_PKEY **a, const unsigned char **pp, long length,
\&                         OSSL_LIB_CTX *libctx, const char *propq);
\& EVP_PKEY *d2i_PUBKEY(EVP_PKEY **a, const unsigned char **pp, long length);
\& int i2d_PUBKEY(const EVP_PKEY *a, unsigned char **pp);
\&
\& EVP_PKEY *d2i_PUBKEY_bio(BIO *bp, EVP_PKEY **a);
\& EVP_PKEY *d2i_PUBKEY_fp(FILE *fp, EVP_PKEY **a);
\&
\& int i2d_PUBKEY_fp(const FILE *fp, EVP_PKEY *pkey);
\& int i2d_PUBKEY_bio(BIO *bp, const EVP_PKEY *pkey);
\&
\& int X509_PUBKEY_set0_param(X509_PUBKEY *pub, ASN1_OBJECT *aobj,
\&                            int ptype, void *pval,
\&                            unsigned char *penc, int penclen);
\& int X509_PUBKEY_get0_param(ASN1_OBJECT **ppkalg,
\&                            const unsigned char **pk, int *ppklen,
\&                            X509_ALGOR **pa, const X509_PUBKEY *pub);
\& int X509_PUBKEY_eq(X509_PUBKEY *a, X509_PUBKEY *b);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
The \fBX509_PUBKEY\fR structure represents the ASN.1 \fBSubjectPublicKeyInfo\fR
structure defined in RFC5280 and used in certificates and certificate requests.
.PP
\&\fBX509_PUBKEY_new_ex()\fR allocates and initializes an \fBX509_PUBKEY\fR structure
associated with the given \fBOSSL_LIB_CTX\fR in the \fIlibctx\fR parameter. Any
algorithm fetches associated with using the \fBX509_PUBKEY\fR object will use
the property query string \fIpropq\fR. See "ALGORITHM FETCHING" in \fBcrypto\fR\|(7) for
further information about algorithm fetching.
.PP
\&\fBX509_PUBKEY_new()\fR is the same as \fBX509_PUBKEY_new_ex()\fR except that the default
(NULL) \fBOSSL_LIB_CTX\fR and a NULL property query string are used.
.PP
\&\fBX509_PUBKEY_dup()\fR creates a duplicate copy of the \fBX509_PUBKEY\fR object
specified by \fIa\fR.
.PP
\&\fBX509_PUBKEY_free()\fR frees up \fBX509_PUBKEY\fR structure \fIa\fR. If \fIa\fR is NULL
nothing is done.
.PP
\&\fBX509_PUBKEY_set()\fR sets the public key in \fI*x\fR to the public key contained
in the \fBEVP_PKEY\fR structure \fIpkey\fR. If \fI*x\fR is not NULL any existing
public key structure will be freed.
.PP
\&\fBX509_PUBKEY_get0()\fR returns the public key contained in \fIkey\fR. The returned
value is an internal pointer which \fBMUST NOT\fR be freed after use.
.PP
\&\fBX509_PUBKEY_get()\fR is similar to \fBX509_PUBKEY_get0()\fR except the reference
count on the returned key is incremented so it \fBMUST\fR be freed using
\&\fBEVP_PKEY_free()\fR after use.
.PP
\&\fBd2i_PUBKEY_ex()\fR decodes an \fBEVP_PKEY\fR structure using \fBSubjectPublicKeyInfo\fR
format.  Some public key decoding implementations may use cryptographic
algorithms. In this case the supplied library context \fIlibctx\fR and property
query string \fIpropq\fR are used.
\&\fBd2i_PUBKEY()\fR does the same as \fBd2i_PUBKEY_ex()\fR except that the default
library context and property query string are used.
.PP
\&\fBi2d_PUBKEY()\fR encodes an \fBEVP_PKEY\fR structure using \fBSubjectPublicKeyInfo\fR
format.
.PP
\&\fBd2i_PUBKEY_bio()\fR, \fBd2i_PUBKEY_fp()\fR, \fBi2d_PUBKEY_bio()\fR and \fBi2d_PUBKEY_fp()\fR are
similar to \fBd2i_PUBKEY()\fR and \fBi2d_PUBKEY()\fR except they decode or encode using a
\&\fBBIO\fR or \fBFILE\fR pointer.
.PP
\&\fBX509_PUBKEY_set0_param()\fR sets the public key parameters of \fIpub\fR. The
OID associated with the algorithm is set to \fIaobj\fR. The type of the
algorithm parameters is set to \fItype\fR using the structure \fIpval\fR.
The encoding of the public key itself is set to the \fIpenclen\fR
bytes contained in buffer \fIpenc\fR. On success ownership of all the supplied
parameters is passed to \fIpub\fR so they must not be freed after the
call.
.PP
\&\fBX509_PUBKEY_get0_param()\fR retrieves the public key parameters from \fIpub\fR,
\&\fI*ppkalg\fR is set to the associated OID and the encoding consists of
\&\fI*ppklen\fR bytes at \fI*pk\fR, \fI*pa\fR is set to the associated
AlgorithmIdentifier for the public key. If the value of any of these
parameters is not required it can be set to NULL. All of the
retrieved pointers are internal and must not be freed after the
call.
.PP
\&\fBX509_PUBKEY_eq()\fR compares two \fBX509_PUBKEY\fR values.
.SH NOTES
.IX Header "NOTES"
The \fBX509_PUBKEY\fR functions can be used to encode and decode public keys
in a standard format.
.PP
In many cases applications will not call the \fBX509_PUBKEY\fR functions
directly: they will instead call wrapper functions such as \fBX509_get0_pubkey()\fR.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
If the allocation fails, \fBX509_PUBKEY_new()\fR and \fBX509_PUBKEY_dup()\fR return
NULL and set an error code that can be obtained by \fBERR_get_error\fR\|(3).
Otherwise they return a pointer to the newly allocated structure.
.PP
\&\fBX509_PUBKEY_free()\fR does not return a value.
.PP
\&\fBX509_PUBKEY_get0()\fR and \fBX509_PUBKEY_get()\fR return a pointer to an \fBEVP_PKEY\fR
structure or NULL if an error occurs.
.PP
\&\fBX509_PUBKEY_set()\fR, \fBX509_PUBKEY_set0_param()\fR and \fBX509_PUBKEY_get0_param()\fR
return 1 for success and 0 if an error occurred.
.PP
\&\fBX509_PUBKEY_eq()\fR returns 1 for equal, 0 for different, and < 0 on error.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBd2i_X509\fR\|(3),
\&\fBERR_get_error\fR\|(3),
\&\fBX509_get_pubkey\fR\|(3),
.SH HISTORY
.IX Header "HISTORY"
The \fBX509_PUBKEY_new_ex()\fR and \fBX509_PUBKEY_eq()\fR functions were added in OpenSSL
3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2016\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
