.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "S2I_ASN1_IA5STRING 3ossl"
.TH S2I_ASN1_IA5STRING 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
i2s_ASN1_IA5STRING,
s2i_ASN1_IA5STRING,
i2s_ASN1_INTEGER,
s2i_ASN1_INTEGER,
i2s_ASN1_OCTET_STRING,
s2i_ASN1_OCTET_STRING,
i2s_ASN1_ENUMERATED,
i2s_ASN1_ENUMERATED_TABLE,
i2s_ASN1_UTF8STRING,
s2i_ASN1_UTF8STRING
\&\- convert objects from/to ASN.1/string representation
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/x509v3.h>
\&
\& char *i2s_ASN1_IA5STRING(X509V3_EXT_METHOD *method, ASN1_IA5STRING *ia5);
\& ASN1_IA5STRING *s2i_ASN1_IA5STRING(X509V3_EXT_METHOD *method,
\&                                   X509V3_CTX *ctx, const char *str);
\& char *i2s_ASN1_INTEGER(X509V3_EXT_METHOD *method, const ASN1_INTEGER *a);
\& ASN1_INTEGER *s2i_ASN1_INTEGER(X509V3_EXT_METHOD *method, const char *value);
\& char *i2s_ASN1_OCTET_STRING(X509V3_EXT_METHOD *method,
\&                            const ASN1_OCTET_STRING *oct);
\& ASN1_OCTET_STRING *s2i_ASN1_OCTET_STRING(X509V3_EXT_METHOD *method,
\&                                         X509V3_CTX *ctx, const char *str);
\& char *i2s_ASN1_ENUMERATED(X509V3_EXT_METHOD *method, const ASN1_ENUMERATED *a);
\& char *i2s_ASN1_ENUMERATED_TABLE(X509V3_EXT_METHOD *method,
\&                                const ASN1_ENUMERATED *e);
\&
\& char *i2s_ASN1_UTF8STRING(X509V3_EXT_METHOD *method,
\&                           ASN1_UTF8STRING *utf8);
\& ASN1_UTF8STRING *s2i_ASN1_UTF8STRING(X509V3_EXT_METHOD *method,
\&                                      X509V3_CTX *ctx, const char *str);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
These functions convert OpenSSL objects to and from their ASN.1/string
representation. This function is used for \fBX509v3\fR extensions.
.SH NOTES
.IX Header "NOTES"
The letters \fBi\fR and \fBs\fR in \fBi2s\fR and \fBs2i\fR stand for
"internal" (that is, an internal C structure) and string respectively.
So \fBi2s_ASN1_IA5STRING\fR() converts from internal to string.
.PP
It is the caller's responsibility to free the returned string.
In the \fBi2s_ASN1_IA5STRING\fR() function the string is copied and
the ownership of the original string remains with the caller.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBi2s_ASN1_IA5STRING\fR() returns the pointer to a IA5 string
or NULL if an error occurs.
.PP
\&\fBs2i_ASN1_IA5STRING\fR() return a valid
\&\fBASN1_IA5STRING\fR structure or NULL if an error occurs.
.PP
\&\fBi2s_ASN1_INTEGER\fR() return a valid
string or NULL if an error occurs.
.PP
\&\fBs2i_ASN1_INTEGER\fR() returns the pointer to a \fBASN1_INTEGER\fR
structure or NULL if an error occurs.
.PP
\&\fBi2s_ASN1_OCTET_STRING\fR() returns the pointer to a OCTET_STRING string
or NULL if an error occurs.
.PP
\&\fBs2i_ASN1_OCTET_STRING\fR() return a valid
\&\fBASN1_OCTET_STRING\fR structure or NULL if an error occurs.
.PP
\&\fBi2s_ASN1_ENUMERATED\fR() return a valid
string or NULL if an error occurs.
.PP
\&\fBs2i_ASN1_ENUMERATED\fR() returns the pointer to a \fBASN1_ENUMERATED\fR
structure or NULL if an error occurs.
.PP
\&\fBs2i_ASN1_UTF8STRING\fR() return a valid
\&\fBASN1_UTF8STRING\fR structure or NULL if an error occurs.
.PP
\&\fBi2s_ASN1_UTF8STRING\fR() returns the pointer to a UTF\-8 string
or NULL if an error occurs.
.SH HISTORY
.IX Header "HISTORY"
\&\fBi2s_ASN1_UTF8STRING()\fR and \fBs2i_ASN1_UTF8STRING()\fR were made public in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2020\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
