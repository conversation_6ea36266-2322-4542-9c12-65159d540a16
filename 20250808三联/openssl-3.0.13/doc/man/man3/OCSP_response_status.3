.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "OCSP_RESPONSE_STATUS 3ossl"
.TH OCSP_RESPONSE_STATUS 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
OCSP_response_status, OCSP_response_get1_basic, OCSP_response_create,
OCSP_RESPONSE_free, OCSP_RESPID_set_by_name,
OCSP_RESPID_set_by_key_ex, OCSP_RESPID_set_by_key, OCSP_RESPID_match_ex,
OCSP_RESPID_match, OCSP_basic_sign, OCSP_basic_sign_ctx
\&\- OCSP response functions
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/ocsp.h>
\&
\& int OCSP_response_status(OCSP_RESPONSE *resp);
\& OCSP_BASICRESP *OCSP_response_get1_basic(OCSP_RESPONSE *resp);
\& OCSP_RESPONSE *OCSP_response_create(int status, OCSP_BASICRESP *bs);
\& void OCSP_RESPONSE_free(OCSP_RESPONSE *resp);
\&
\& int OCSP_RESPID_set_by_name(OCSP_RESPID *respid, X509 *cert);
\& int OCSP_RESPID_set_by_key_ex(OCSP_RESPID *respid, X509 *cert,
\&                               OSSL_LIB_CTX *libctx, const char *propq);
\& int OCSP_RESPID_set_by_key(OCSP_RESPID *respid, X509 *cert);
\& int OCSP_RESPID_match_ex(OCSP_RESPID *respid, X509 *cert, OSSL_LIB_CTX *libctx,
\&                          const char *propq);
\& int OCSP_RESPID_match(OCSP_RESPID *respid, X509 *cert);
\&
\& int OCSP_basic_sign(OCSP_BASICRESP *brsp, X509 *signer, EVP_PKEY *key,
\&                     const EVP_MD *dgst, STACK_OF(X509) *certs,
\&                     unsigned long flags);
\& int OCSP_basic_sign_ctx(OCSP_BASICRESP *brsp, X509 *signer, EVP_MD_CTX *ctx,
\&                         STACK_OF(X509) *certs, unsigned long flags);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBOCSP_response_status()\fR returns the OCSP response status of \fIresp\fR. It returns
one of the values: \fIOCSP_RESPONSE_STATUS_SUCCESSFUL\fR,
\&\fIOCSP_RESPONSE_STATUS_MALFORMEDREQUEST\fR,
\&\fIOCSP_RESPONSE_STATUS_INTERNALERROR\fR, \fIOCSP_RESPONSE_STATUS_TRYLATER\fR
\&\fIOCSP_RESPONSE_STATUS_SIGREQUIRED\fR, or \fIOCSP_RESPONSE_STATUS_UNAUTHORIZED\fR.
.PP
\&\fBOCSP_response_get1_basic()\fR decodes and returns the \fIOCSP_BASICRESP\fR structure
contained in \fIresp\fR.
.PP
\&\fBOCSP_response_create()\fR creates and returns an \fIOCSP_RESPONSE\fR structure for
\&\fIstatus\fR and optionally including basic response \fIbs\fR.
.PP
\&\fBOCSP_RESPONSE_free()\fR frees up OCSP response \fIresp\fR.
.PP
\&\fBOCSP_RESPID_set_by_name()\fR sets the name of the OCSP_RESPID to be the same as the
subject name in the supplied X509 certificate \fIcert\fR for the OCSP responder.
.PP
\&\fBOCSP_RESPID_set_by_key_ex()\fR sets the key of the OCSP_RESPID to be the same as the
key in the supplied X509 certificate \fIcert\fR for the OCSP responder. The key is
stored as a SHA1 hash. To calculate the hash the SHA1 algorithm is fetched using
the library ctx \fIlibctx\fR and the property query string \fIpropq\fR (see
"ALGORITHM FETCHING" in \fBcrypto\fR\|(7) for further information).
.PP
\&\fBOCSP_RESPID_set_by_key()\fR does the same as \fBOCSP_RESPID_set_by_key_ex()\fR except
that the default library context is used with an empty property query string.
.PP
Note that an OCSP_RESPID can only have one of the name, or the key set. Calling
\&\fBOCSP_RESPID_set_by_name()\fR or \fBOCSP_RESPID_set_by_key()\fR will clear any existing
setting.
.PP
\&\fBOCSP_RESPID_match_ex()\fR tests whether the OCSP_RESPID given in \fIrespid\fR matches
with the X509 certificate \fIcert\fR based on the SHA1 hash. To calculate the hash
the SHA1 algorithm is fetched using the library ctx \fIlibctx\fR and the property
query string \fIpropq\fR (see "ALGORITHM FETCHING" in \fBcrypto\fR\|(7) for further
information).
.PP
\&\fBOCSP_RESPID_match()\fR does the same as \fBOCSP_RESPID_match_ex()\fR except that the
default library context is used with an empty property query string.
.PP
\&\fBOCSP_basic_sign()\fR signs OCSP response \fIbrsp\fR using certificate \fIsigner\fR, private key
\&\fIkey\fR, digest \fIdgst\fR and additional certificates \fIcerts\fR. If the \fIflags\fR option
\&\fIOCSP_NOCERTS\fR is set then no certificates will be included in the response. If the
\&\fIflags\fR option \fIOCSP_RESPID_KEY\fR is set then the responder is identified by key ID
rather than by name. \fBOCSP_basic_sign_ctx()\fR also signs OCSP response \fIbrsp\fR but
uses the parameters contained in digest context \fIctx\fR.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBOCSP_RESPONSE_status()\fR returns a status value.
.PP
\&\fBOCSP_response_get1_basic()\fR returns an \fIOCSP_BASICRESP\fR structure pointer or
\&\fINULL\fR if an error occurred.
.PP
\&\fBOCSP_response_create()\fR returns an \fIOCSP_RESPONSE\fR structure pointer or \fINULL\fR
if an error occurred.
.PP
\&\fBOCSP_RESPONSE_free()\fR does not return a value.
.PP
\&\fBOCSP_RESPID_set_by_name()\fR, \fBOCSP_RESPID_set_by_key()\fR, \fBOCSP_basic_sign()\fR, and
\&\fBOCSP_basic_sign_ctx()\fR return 1 on success or 0
on failure.
.PP
\&\fBOCSP_RESPID_match()\fR returns 1 if the OCSP_RESPID and the X509 certificate match
or 0 otherwise.
.SH NOTES
.IX Header "NOTES"
\&\fBOCSP_response_get1_basic()\fR is only called if the status of a response is
\&\fIOCSP_RESPONSE_STATUS_SUCCESSFUL\fR.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBcrypto\fR\|(7)
\&\fBOCSP_cert_to_id\fR\|(3)
\&\fBOCSP_request_add1_nonce\fR\|(3)
\&\fBOCSP_REQUEST_new\fR\|(3)
\&\fBOCSP_resp_find_status\fR\|(3)
\&\fBOCSP_sendreq_new\fR\|(3)
\&\fBOCSP_RESPID_new\fR\|(3)
\&\fBOCSP_RESPID_free\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
The \fBOCSP_RESPID_set_by_name()\fR, \fBOCSP_RESPID_set_by_key()\fR and \fBOCSP_RESPID_match()\fR
functions were added in OpenSSL 1.1.0a.
.PP
The \fBOCSP_basic_sign_ctx()\fR function was added in OpenSSL 1.1.1.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2015\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
