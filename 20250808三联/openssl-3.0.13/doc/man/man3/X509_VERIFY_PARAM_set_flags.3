.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "X509_VERIFY_PARAM_SET_FLAGS 3ossl"
.TH X509_VERIFY_PARAM_SET_FLAGS 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
X509_VERIFY_PARAM_set_flags, X509_VERIFY_PARAM_clear_flags,
X509_VERIFY_PARAM_get_flags, X509_VERIFY_PARAM_set_purpose,
X509_VERIFY_PARAM_get_inh_flags, X509_VERIFY_PARAM_set_inh_flags,
X509_VERIFY_PARAM_set_trust, X509_VERIFY_PARAM_set_depth,
X509_VERIFY_PARAM_get_depth, X509_VERIFY_PARAM_set_auth_level,
X509_VERIFY_PARAM_get_auth_level, X509_VERIFY_PARAM_set_time,
X509_VERIFY_PARAM_get_time,
X509_VERIFY_PARAM_add0_policy, X509_VERIFY_PARAM_set1_policies,
X509_VERIFY_PARAM_get0_host,
X509_VERIFY_PARAM_set1_host, X509_VERIFY_PARAM_add1_host,
X509_VERIFY_PARAM_set_hostflags,
X509_VERIFY_PARAM_get_hostflags,
X509_VERIFY_PARAM_get0_peername,
X509_VERIFY_PARAM_get0_email, X509_VERIFY_PARAM_set1_email,
X509_VERIFY_PARAM_set1_ip, X509_VERIFY_PARAM_get1_ip_asc,
X509_VERIFY_PARAM_set1_ip_asc
\&\- X509 verification parameters
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/x509_vfy.h>
\&
\& int X509_VERIFY_PARAM_set_flags(X509_VERIFY_PARAM *param,
\&                                 unsigned long flags);
\& int X509_VERIFY_PARAM_clear_flags(X509_VERIFY_PARAM *param,
\&                                   unsigned long flags);
\& unsigned long X509_VERIFY_PARAM_get_flags(const X509_VERIFY_PARAM *param);
\&
\& int X509_VERIFY_PARAM_set_inh_flags(X509_VERIFY_PARAM *param,
\&                                     uint32_t flags);
\& uint32_t X509_VERIFY_PARAM_get_inh_flags(const X509_VERIFY_PARAM *param);
\&
\& int X509_VERIFY_PARAM_set_purpose(X509_VERIFY_PARAM *param, int purpose);
\& int X509_VERIFY_PARAM_set_trust(X509_VERIFY_PARAM *param, int trust);
\&
\& void X509_VERIFY_PARAM_set_time(X509_VERIFY_PARAM *param, time_t t);
\& time_t X509_VERIFY_PARAM_get_time(const X509_VERIFY_PARAM *param);
\&
\& int X509_VERIFY_PARAM_add0_policy(X509_VERIFY_PARAM *param,
\&                                   ASN1_OBJECT *policy);
\& int X509_VERIFY_PARAM_set1_policies(X509_VERIFY_PARAM *param,
\&                                     STACK_OF(ASN1_OBJECT) *policies);
\&
\& void X509_VERIFY_PARAM_set_depth(X509_VERIFY_PARAM *param, int depth);
\& int X509_VERIFY_PARAM_get_depth(const X509_VERIFY_PARAM *param);
\&
\& void X509_VERIFY_PARAM_set_auth_level(X509_VERIFY_PARAM *param,
\&                                       int auth_level);
\& int X509_VERIFY_PARAM_get_auth_level(const X509_VERIFY_PARAM *param);
\&
\& char *X509_VERIFY_PARAM_get0_host(X509_VERIFY_PARAM *param, int n);
\& int X509_VERIFY_PARAM_set1_host(X509_VERIFY_PARAM *param,
\&                                 const char *name, size_t namelen);
\& int X509_VERIFY_PARAM_add1_host(X509_VERIFY_PARAM *param,
\&                                 const char *name, size_t namelen);
\& void X509_VERIFY_PARAM_set_hostflags(X509_VERIFY_PARAM *param,
\&                                      unsigned int flags);
\& unsigned int X509_VERIFY_PARAM_get_hostflags(const X509_VERIFY_PARAM *param);
\& char *X509_VERIFY_PARAM_get0_peername(const X509_VERIFY_PARAM *param);
\& char *X509_VERIFY_PARAM_get0_email(X509_VERIFY_PARAM *param);
\& int X509_VERIFY_PARAM_set1_email(X509_VERIFY_PARAM *param,
\&                                  const char *email, size_t emaillen);
\& char *X509_VERIFY_PARAM_get1_ip_asc(X509_VERIFY_PARAM *param);
\& int X509_VERIFY_PARAM_set1_ip(X509_VERIFY_PARAM *param,
\&                               const unsigned char *ip, size_t iplen);
\& int X509_VERIFY_PARAM_set1_ip_asc(X509_VERIFY_PARAM *param, const char *ipasc);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
These functions manipulate the \fBX509_VERIFY_PARAM\fR structure associated with
a certificate verification operation.
.PP
The \fBX509_VERIFY_PARAM_set_flags()\fR function sets the flags in \fBparam\fR by oring
it with \fBflags\fR. See "VERIFICATION FLAGS" for a complete
description of values the \fBflags\fR parameter can take.
.PP
\&\fBX509_VERIFY_PARAM_get_flags()\fR returns the flags in \fBparam\fR.
.PP
\&\fBX509_VERIFY_PARAM_get_inh_flags()\fR returns the inheritance flags in \fBparam\fR
which specifies how verification flags are copied from one structure to
another. \fBX509_VERIFY_PARAM_set_inh_flags()\fR sets the inheritance flags.
See the \fBINHERITANCE FLAGS\fR section for a description of these bits.
.PP
\&\fBX509_VERIFY_PARAM_clear_flags()\fR clears the flags \fBflags\fR in \fBparam\fR.
.PP
\&\fBX509_VERIFY_PARAM_set_purpose()\fR sets the verification purpose in \fBparam\fR
to \fBpurpose\fR. This determines the acceptable purpose of the certificate
chain, for example \fBX509_PURPOSE_SSL_CLIENT\fR.
The purpose requirement is cleared if \fBpurpose\fR is 0.
.PP
\&\fBX509_VERIFY_PARAM_set_trust()\fR sets the trust setting in \fBparam\fR to
\&\fBtrust\fR.
.PP
\&\fBX509_VERIFY_PARAM_set_time()\fR sets the verification time in \fBparam\fR to
\&\fBt\fR. Normally the current time is used.
.PP
\&\fBX509_VERIFY_PARAM_add0_policy()\fR adds \fBpolicy\fR to the acceptable policy set.
Contrary to preexisting documentation of this function it does not enable
policy checking.
.PP
\&\fBX509_VERIFY_PARAM_set1_policies()\fR enables policy checking (it is disabled
by default) and sets the acceptable policy set to \fBpolicies\fR. Any existing
policy set is cleared. The \fBpolicies\fR parameter can be \fBNULL\fR to clear
an existing policy set.
.PP
\&\fBX509_VERIFY_PARAM_set_depth()\fR sets the maximum verification depth to \fBdepth\fR.
That is the maximum number of intermediate CA certificates that can appear in a
chain.
A maximal depth chain contains 2 more certificates than the limit, since
neither the end-entity certificate nor the trust-anchor count against this
limit.
Thus a \fBdepth\fR limit of 0 only allows the end-entity certificate to be signed
directly by the trust anchor, while with a \fBdepth\fR limit of 1 there can be one
intermediate CA certificate between the trust anchor and the end-entity
certificate.
.PP
\&\fBX509_VERIFY_PARAM_set_auth_level()\fR sets the authentication security level to
\&\fBauth_level\fR.
The authentication security level determines the acceptable signature and public
key strength when verifying certificate chains.
For a certificate chain to validate, the public keys of all the certificates
must meet the specified security level.
The signature algorithm security level is not enforced for the chain's \fItrust
anchor\fR certificate, which is either directly trusted or validated by means other
than its signature.
See \fBSSL_CTX_set_security_level\fR\|(3) for the definitions of the available
levels.
The default security level is \-1, or "not set".
At security level 0 or lower all algorithms are acceptable.
Security level 1 requires at least 80\-bit\-equivalent security and is broadly
interoperable, though it will, for example, reject MD5 signatures or RSA keys
shorter than 1024 bits.
.PP
\&\fBX509_VERIFY_PARAM_get0_host()\fR returns the \fBn\fRth expected DNS hostname that has
been set using \fBX509_VERIFY_PARAM_set1_host()\fR or \fBX509_VERIFY_PARAM_add1_host()\fR.
To obtain all names start with \fBn\fR = 0 and increment \fBn\fR as long as no NULL
pointer is returned.
.PP
\&\fBX509_VERIFY_PARAM_set1_host()\fR sets the expected DNS hostname to
\&\fBname\fR clearing any previously specified hostname.  If
\&\fBname\fR is NULL, or empty the list of hostnames is cleared, and
name checks are not performed on the peer certificate.  If \fBname\fR
is NUL-terminated, \fBnamelen\fR may be zero, otherwise \fBnamelen\fR
must be set to the length of \fBname\fR.
.PP
When a hostname is specified,
certificate verification automatically invokes \fBX509_check_host\fR\|(3)
with flags equal to the \fBflags\fR argument given to
\&\fBX509_VERIFY_PARAM_set_hostflags()\fR (default zero).  Applications
are strongly advised to use this interface in preference to explicitly
calling \fBX509_check_host\fR\|(3), hostname checks may be out of scope
with the \fBDANE\-EE\fR\|(3) certificate usage, and the internal check will
be suppressed as appropriate when DANE verification is enabled.
.PP
When the subject CommonName will not be ignored, whether as a result of the
\&\fBX509_CHECK_FLAG_ALWAYS_CHECK_SUBJECT\fR host flag, or because no DNS subject
alternative names are present in the certificate, any DNS name constraints in
issuer certificates apply to the subject CommonName as well as the subject
alternative name extension.
.PP
When the subject CommonName will be ignored, whether as a result of the
\&\fBX509_CHECK_FLAG_NEVER_CHECK_SUBJECT\fR host flag, or because some DNS subject
alternative names are present in the certificate, DNS name constraints in
issuer certificates will not be applied to the subject DN.
As described in \fBX509_check_host\fR\|(3) the \fBX509_CHECK_FLAG_NEVER_CHECK_SUBJECT\fR
flag takes precedence over the \fBX509_CHECK_FLAG_ALWAYS_CHECK_SUBJECT\fR flag.
.PP
\&\fBX509_VERIFY_PARAM_get_hostflags()\fR returns any host flags previously set via a
call to \fBX509_VERIFY_PARAM_set_hostflags()\fR.
.PP
\&\fBX509_VERIFY_PARAM_add1_host()\fR adds \fBname\fR as an additional reference
identifier that can match the peer's certificate.  Any previous names
set via \fBX509_VERIFY_PARAM_set1_host()\fR or \fBX509_VERIFY_PARAM_add1_host()\fR
are retained, no change is made if \fBname\fR is NULL or empty.  When
multiple names are configured, the peer is considered verified when
any name matches.
.PP
\&\fBX509_VERIFY_PARAM_get0_peername()\fR returns the DNS hostname or subject
CommonName from the peer certificate that matched one of the reference
identifiers.  When wildcard matching is not disabled, or when a
reference identifier specifies a parent domain (starts with ".")
rather than a hostname, the peer name may be a wildcard name or a
sub-domain of the reference identifier respectively.  The return
string is allocated by the library and is no longer valid once the
associated \fBparam\fR argument is freed.  Applications must not free
the return value.
.PP
\&\fBX509_VERIFY_PARAM_get0_email()\fR returns the expected RFC822 email address.
.PP
\&\fBX509_VERIFY_PARAM_set1_email()\fR sets the expected RFC822 email address to
\&\fBemail\fR.  If \fBemail\fR is NUL-terminated, \fBemaillen\fR may be zero, otherwise
\&\fBemaillen\fR must be set to the length of \fBemail\fR.  When an email address
is specified, certificate verification automatically invokes
\&\fBX509_check_email\fR\|(3).
.PP
\&\fBX509_VERIFY_PARAM_get1_ip_asc()\fR returns the expected IP address as a string.
The caller is responsible for freeing it.
.PP
\&\fBX509_VERIFY_PARAM_set1_ip()\fR sets the expected IP address to \fBip\fR.
The \fBip\fR argument is in binary format, in network byte-order and
\&\fBiplen\fR must be set to 4 for IPv4 and 16 for IPv6.  When an IP
address is specified, certificate verification automatically invokes
\&\fBX509_check_ip\fR\|(3).
.PP
\&\fBX509_VERIFY_PARAM_set1_ip_asc()\fR sets the expected IP address to
\&\fBipasc\fR.  The \fBipasc\fR argument is a NUL-terminal ASCII string:
dotted decimal quad for IPv4 and colon-separated hexadecimal for
IPv6.  The condensed "::" notation is supported for IPv6 addresses.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBX509_VERIFY_PARAM_set_flags()\fR, \fBX509_VERIFY_PARAM_clear_flags()\fR,
\&\fBX509_VERIFY_PARAM_set_inh_flags()\fR,
\&\fBX509_VERIFY_PARAM_set_purpose()\fR, \fBX509_VERIFY_PARAM_set_trust()\fR,
\&\fBX509_VERIFY_PARAM_add0_policy()\fR \fBX509_VERIFY_PARAM_set1_policies()\fR,
\&\fBX509_VERIFY_PARAM_set1_host()\fR, \fBX509_VERIFY_PARAM_add1_host()\fR,
\&\fBX509_VERIFY_PARAM_set1_email()\fR, \fBX509_VERIFY_PARAM_set1_ip()\fR and
\&\fBX509_VERIFY_PARAM_set1_ip_asc()\fR return 1 for success and 0 for
failure.
.PP
\&\fBX509_VERIFY_PARAM_get0_host()\fR, \fBX509_VERIFY_PARAM_get0_email()\fR, and
\&\fBX509_VERIFY_PARAM_get1_ip_asc()\fR, return the string pointer specified above
or NULL if the respective value has not been set or on error.
.PP
\&\fBX509_VERIFY_PARAM_get_flags()\fR returns the current verification flags.
.PP
\&\fBX509_VERIFY_PARAM_get_hostflags()\fR returns any current host flags.
.PP
\&\fBX509_VERIFY_PARAM_get_inh_flags()\fR returns the current inheritance flags.
.PP
\&\fBX509_VERIFY_PARAM_set_time()\fR and \fBX509_VERIFY_PARAM_set_depth()\fR do not return
values.
.PP
\&\fBX509_VERIFY_PARAM_get_depth()\fR returns the current verification depth.
.PP
\&\fBX509_VERIFY_PARAM_get_auth_level()\fR returns the current authentication security
level.
.SH "VERIFICATION FLAGS"
.IX Header "VERIFICATION FLAGS"
The verification flags consists of zero or more of the following flags
ored together.
.PP
\&\fBX509_V_FLAG_CRL_CHECK\fR enables CRL checking for the certificate chain leaf
certificate. An error occurs if a suitable CRL cannot be found.
.PP
\&\fBX509_V_FLAG_CRL_CHECK_ALL\fR enables CRL checking for the entire certificate
chain.
.PP
\&\fBX509_V_FLAG_IGNORE_CRITICAL\fR disables critical extension checking. By default
any unhandled critical extensions in certificates or (if checked) CRLs result
in a fatal error. If this flag is set unhandled critical extensions are
ignored. \fBWARNING\fR setting this option for anything other than debugging
purposes can be a security risk. Finer control over which extensions are
supported can be performed in the verification callback.
.PP
The \fBX509_V_FLAG_X509_STRICT\fR flag disables workarounds for some broken
certificates and makes the verification strictly apply \fBX509\fR rules.
.PP
\&\fBX509_V_FLAG_ALLOW_PROXY_CERTS\fR enables proxy certificate verification.
.PP
\&\fBX509_V_FLAG_POLICY_CHECK\fR enables certificate policy checking, by default
no policy checking is performed. Additional information is sent to the
verification callback relating to policy checking.
.PP
\&\fBX509_V_FLAG_EXPLICIT_POLICY\fR, \fBX509_V_FLAG_INHIBIT_ANY\fR and
\&\fBX509_V_FLAG_INHIBIT_MAP\fR set the \fBrequire explicit policy\fR, \fBinhibit any
policy\fR and \fBinhibit policy mapping\fR flags respectively as defined in
\&\fBRFC3280\fR. Policy checking is automatically enabled if any of these flags
are set.
.PP
If \fBX509_V_FLAG_NOTIFY_POLICY\fR is set and the policy checking is successful
a special status code is set to the verification callback. This permits it
to examine the valid policy tree and perform additional checks or simply
log it for debugging purposes.
.PP
By default some additional features such as indirect CRLs and CRLs signed by
different keys are disabled. If \fBX509_V_FLAG_EXTENDED_CRL_SUPPORT\fR is set
they are enabled.
.PP
If \fBX509_V_FLAG_USE_DELTAS\fR is set delta CRLs (if present) are used to
determine certificate status. If not set deltas are ignored.
.PP
\&\fBX509_V_FLAG_CHECK_SS_SIGNATURE\fR requests checking the signature of
the last certificate in a chain if the certificate is supposedly self-signed.
This is prohibited and will result in an error if it is a non-conforming CA
certificate with key usage restrictions not including the \fIkeyCertSign\fR bit.
By default this check is disabled because it doesn't
add any additional security but in some cases applications might want to
check the signature anyway. A side effect of not checking the self-signature
of such a certificate is that disabled or unsupported message digests used for
the signature are not treated as fatal errors.
.PP
When \fBX509_V_FLAG_TRUSTED_FIRST\fR is set, which is always the case since
OpenSSL 1.1.0, construction of the certificate chain
in \fBX509_verify_cert\fR\|(3) searches the trust store for issuer certificates
before searching the provided untrusted certificates.
Local issuer certificates are often more likely to satisfy local security
requirements and lead to a locally trusted root.
This is especially important when some certificates in the trust store have
explicit trust settings (see "TRUST SETTINGS" in \fBopenssl\-x509\fR\|(1)).
.PP
The \fBX509_V_FLAG_NO_ALT_CHAINS\fR flag could have been used before OpenSSL 1.1.0
to suppress checking for alternative chains.
By default, unless \fBX509_V_FLAG_TRUSTED_FIRST\fR is set, when building a
certificate chain, if the first certificate chain found is not trusted, then
OpenSSL will attempt to replace untrusted certificates supplied by the peer
with certificates from the trust store to see if an alternative chain can be
found that is trusted.
As of OpenSSL 1.1.0, with \fBX509_V_FLAG_TRUSTED_FIRST\fR always set, this option
has no effect.
.PP
The \fBX509_V_FLAG_PARTIAL_CHAIN\fR flag causes non-self-signed certificates in the
trust store to be treated as trust anchors, in the same way as self-signed
root CA certificates.
This makes it possible to trust self-issued certificates as well as certificates
issued by an intermediate CA without having to trust their ancestor root CA.
With OpenSSL 1.1.0 and later and \fBX509_V_FLAG_PARTIAL_CHAIN\fR set, chain
construction stops as soon as the first certificate contained in the trust store
is added to the chain, whether that certificate is a self-signed "root"
certificate or a not self-signed "intermediate" or self-issued certificate.
Thus, when an intermediate certificate is found in the trust store, the
verified chain passed to callbacks may be shorter than it otherwise would
be without the \fBX509_V_FLAG_PARTIAL_CHAIN\fR flag.
.PP
The \fBX509_V_FLAG_NO_CHECK_TIME\fR flag suppresses checking the validity period
of certificates and CRLs against the current time. If \fBX509_VERIFY_PARAM_set_time()\fR
is used to specify a verification time, the check is not suppressed.
.SH "INHERITANCE FLAGS"
.IX Header "INHERITANCE FLAGS"
These flags specify how parameters are "inherited" from one structure to
another.
.PP
If \fBX509_VP_FLAG_ONCE\fR is set then the current setting is zeroed
after the next call.
.PP
If \fBX509_VP_FLAG_LOCKED\fR is set then no values are copied.  This overrides
all of the following flags.
.PP
If \fBX509_VP_FLAG_DEFAULT\fR is set then anything set in the source is copied
to the destination. Effectively the values in "to" become default values
which will be used only if nothing new is set in "from".  This is the
default.
.PP
If \fBX509_VP_FLAG_OVERWRITE\fR is set then all value are copied across whether
they are set or not. Flags is still Ored though.
.PP
If \fBX509_VP_FLAG_RESET_FLAGS\fR is set then the flags value is copied instead
of ORed.
.SH NOTES
.IX Header "NOTES"
The above functions should be used to manipulate verification parameters
instead of functions which work in specific structures such as
\&\fBX509_STORE_CTX_set_flags()\fR which are likely to be deprecated in a future
release.
.SH BUGS
.IX Header "BUGS"
Delta CRL checking is currently primitive. Only a single delta can be used and
(partly due to limitations of \fBX509_STORE\fR) constructed CRLs are not
maintained.
.PP
If CRLs checking is enable CRLs are expected to be available in the
corresponding \fBX509_STORE\fR structure. No attempt is made to download
CRLs from the CRL distribution points extension.
.SH EXAMPLES
.IX Header "EXAMPLES"
Enable CRL checking when performing certificate verification during SSL
connections associated with an \fBSSL_CTX\fR structure \fBctx\fR:
.PP
.Vb 1
\& X509_VERIFY_PARAM *param;
\&
\& param = X509_VERIFY_PARAM_new();
\& X509_VERIFY_PARAM_set_flags(param, X509_V_FLAG_CRL_CHECK);
\& SSL_CTX_set1_param(ctx, param);
\& X509_VERIFY_PARAM_free(param);
.Ve
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBX509_verify_cert\fR\|(3),
\&\fBX509_check_host\fR\|(3),
\&\fBX509_check_email\fR\|(3),
\&\fBX509_check_ip\fR\|(3),
\&\fBopenssl\-x509\fR\|(1)
.SH HISTORY
.IX Header "HISTORY"
The \fBX509_V_FLAG_NO_ALT_CHAINS\fR flag was added in OpenSSL 1.1.0.
The flag \fBX509_V_FLAG_CB_ISSUER_CHECK\fR was deprecated in OpenSSL 1.1.0
and has no effect.
.PP
The \fBX509_VERIFY_PARAM_get_hostflags()\fR function was added in OpenSSL 1.1.0i.
.PP
The \fBX509_VERIFY_PARAM_get0_host()\fR, \fBX509_VERIFY_PARAM_get0_email()\fR,
and \fBX509_VERIFY_PARAM_get1_ip_asc()\fR functions were added in OpenSSL 3.0.
.PP
The function \fBX509_VERIFY_PARAM_add0_policy()\fR was historically documented as
enabling policy checking however the implementation has never done this.
The documentation was changed to align with the implementation.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2009\-2023 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
