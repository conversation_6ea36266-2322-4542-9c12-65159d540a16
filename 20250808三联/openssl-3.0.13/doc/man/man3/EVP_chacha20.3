.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "EVP_CHACHA20 3ossl"
.TH EVP_CHACHA20 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
EVP_chacha20,
EVP_chacha20_poly1305
\&\- EVP ChaCha20 stream cipher
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/evp.h>
\&
\& const EVP_CIPHER *EVP_chacha20(void);
\& const EVP_CIPHER *EVP_chacha20_poly1305(void);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
The ChaCha20 stream cipher for EVP.
.IP \fBEVP_chacha20()\fR 4
.IX Item "EVP_chacha20()"
The ChaCha20 stream cipher. The key length is 256 bits, the IV is 128 bits long.
The first 64 bits consists of a counter in little-endian order followed by a 64
bit nonce. For example a nonce of:
.Sp
0000000000000002
.Sp
With an initial counter of 42 (2a in hex) would be expressed as:
.Sp
2a000000000000000000000000000002
.IP \fBEVP_chacha20_poly1305()\fR 4
.IX Item "EVP_chacha20_poly1305()"
Authenticated encryption with ChaCha20\-Poly1305. Like \fBEVP_chacha20()\fR, the key
is 256 bits and the IV is 96 bits. This supports additional authenticated data
(AAD) and produces a 128\-bit authentication tag. See the
"AEAD Interface" in \fBEVP_EncryptInit\fR\|(3) section for more information.
.SH NOTES
.IX Header "NOTES"
Developers should be aware of the negative performance implications of
calling these functions multiple times and should consider using
\&\fBEVP_CIPHER_fetch\fR\|(3) with \fBEVP_CIPHER\-CHACHA\fR\|(7) instead.
See "Performance" in \fBcrypto\fR\|(7) for further information.
.PP
RFC 7539 <https://www.rfc-editor.org/rfc/rfc7539.html#section-2.4>
uses a 32 bit counter and a 96 bit nonce for the IV.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
These functions return an \fBEVP_CIPHER\fR structure that contains the
implementation of the symmetric cipher. See \fBEVP_CIPHER_meth_new\fR\|(3) for
details of the \fBEVP_CIPHER\fR structure.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBevp\fR\|(7),
\&\fBEVP_EncryptInit\fR\|(3),
\&\fBEVP_CIPHER_meth_new\fR\|(3)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2017\-2023 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
