.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "PEM_READ 3ossl"
.TH PEM_READ 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
PEM_write, PEM_write_bio,
PEM_read, PEM_read_bio, PEM_do_header, PEM_get_EVP_CIPHER_INFO
\&\- PEM encoding routines
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/pem.h>
\&
\& int PEM_write(FILE *fp, const char *name, const char *header,
\&               const unsigned char *data, long len);
\& int PEM_write_bio(BIO *bp, const char *name, const char *header,
\&                   const unsigned char *data, long len);
\&
\& int PEM_read(FILE *fp, char **name, char **header,
\&              unsigned char **data, long *len);
\& int PEM_read_bio(BIO *bp, char **name, char **header,
\&                  unsigned char **data, long *len);
\&
\& int PEM_get_EVP_CIPHER_INFO(char *header, EVP_CIPHER_INFO *cinfo);
\& int PEM_do_header(EVP_CIPHER_INFO *cinfo, unsigned char *data, long *len,
\&                   pem_password_cb *cb, void *u);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
These functions read and write PEM-encoded objects, using the PEM
type \fBname\fR, any additional \fBheader\fR information, and the raw
\&\fBdata\fR of length \fBlen\fR.
.PP
PEM is the term used for binary content encoding first defined in IETF
RFC 1421.  The content is a series of base64\-encoded lines, surrounded
by begin/end markers each on their own line.  For example:
.PP
.Vb 4
\& \-\-\-\-\-BEGIN PRIVATE KEY\-\-\-\-\-
\& MIICdg....
\& ... bhTQ==
\& \-\-\-\-\-END PRIVATE KEY\-\-\-\-\-
.Ve
.PP
Optional header line(s) may appear after the begin line, and their
existence depends on the type of object being written or read.
.PP
\&\fBPEM_write()\fR writes to the file \fBfp\fR, while \fBPEM_write_bio()\fR writes to
the BIO \fBbp\fR.  The \fBname\fR is the name to use in the marker, the
\&\fBheader\fR is the header value or NULL, and \fBdata\fR and \fBlen\fR specify
the data and its length.
.PP
The final \fBdata\fR buffer is typically an ASN.1 object which can be decoded with
the \fBd2i\fR function appropriate to the type \fBname\fR; see \fBd2i_X509\fR\|(3)
for examples.
.PP
\&\fBPEM_read()\fR reads from the file \fBfp\fR, while \fBPEM_read_bio()\fR reads
from the BIO \fBbp\fR.
Both skip any non-PEM data that precedes the start of the next PEM object.
When an object is successfully retrieved, the type name from the "\-\-\-\-BEGIN
<type>\-\-\-\-\-" is returned via the \fBname\fR argument, any encapsulation headers
are returned in \fBheader\fR and the base64\-decoded content and its length are
returned via \fBdata\fR and \fBlen\fR respectively.
The \fBname\fR, \fBheader\fR and \fBdata\fR pointers are allocated via \fBOPENSSL_malloc()\fR
and should be freed by the caller via \fBOPENSSL_free()\fR when no longer needed.
.PP
\&\fBPEM_get_EVP_CIPHER_INFO()\fR can be used to determine the \fBdata\fR returned by
\&\fBPEM_read()\fR or \fBPEM_read_bio()\fR is encrypted and to retrieve the associated cipher
and IV.
The caller passes a pointer to structure of type \fBEVP_CIPHER_INFO\fR via the
\&\fBcinfo\fR argument and the \fBheader\fR returned via \fBPEM_read()\fR or \fBPEM_read_bio()\fR.
If the call is successful 1 is returned and the cipher and IV are stored at the
address pointed to by \fBcinfo\fR.
When the header is malformed, or not supported or when the cipher is unknown
or some internal error happens 0 is returned.
This function is deprecated, see \fBNOTES\fR below.
.PP
\&\fBPEM_do_header()\fR can then be used to decrypt the data if the header
indicates encryption.
The \fBcinfo\fR argument is a pointer to the structure initialized by the previous
call to \fBPEM_get_EVP_CIPHER_INFO()\fR.
The \fBdata\fR and \fBlen\fR arguments are those returned by the previous call to
\&\fBPEM_read()\fR or \fBPEM_read_bio()\fR.
The \fBcb\fR and \fBu\fR arguments make it possible to override the default password
prompt function as described in \fBPEM_read_PrivateKey\fR\|(3).
On successful completion the \fBdata\fR is decrypted in place, and \fBlen\fR is
updated to indicate the plaintext length.
This function is deprecated, see \fBNOTES\fR below.
.PP
If the data is a priori known to not be encrypted, then neither \fBPEM_do_header()\fR
nor \fBPEM_get_EVP_CIPHER_INFO()\fR need be called.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBPEM_read()\fR and \fBPEM_read_bio()\fR return 1 on success and 0 on failure, the latter
includes the case when no more PEM objects remain in the input file.
To distinguish end of file from more serious errors the caller must peek at the
error stack and check for \fBPEM_R_NO_START_LINE\fR, which indicates that no more
PEM objects were found.  See \fBERR_peek_last_error\fR\|(3), \fBERR_GET_REASON\fR\|(3).
.PP
\&\fBPEM_get_EVP_CIPHER_INFO()\fR and \fBPEM_do_header()\fR return 1 on success, and 0 on
failure.
The \fBdata\fR is likely meaningless if these functions fail.
.SH NOTES
.IX Header "NOTES"
The \fBPEM_get_EVP_CIPHER_INFO()\fR and \fBPEM_do_header()\fR functions are deprecated.
This is because the underlying PEM encryption format is obsolete, and should
be avoided.
It uses an encryption format with an OpenSSL-specific key-derivation function,
which employs MD5 with an iteration count of 1!
Instead, private keys should be stored in PKCS#8 form, with a strong PKCS#5
v2.0 PBE.
See \fBPEM_write_PrivateKey\fR\|(3) and \fBd2i_PKCS8PrivateKey_bio\fR\|(3).
.PP
\&\fBPEM_do_header()\fR makes no assumption regarding the pass phrase received from the
password callback.
It will simply be treated as a byte sequence.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBERR_peek_last_error\fR\|(3), \fBERR_GET_LIB\fR\|(3),
\&\fBd2i_PKCS8PrivateKey_bio\fR\|(3),
\&\fBpassphrase\-encoding\fR\|(7)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 1998\-2020 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
