.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "DSA_METH_NEW 3ossl"
.TH DSA_METH_NEW 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
DSA_meth_new, DSA_meth_free, DSA_meth_dup, DSA_meth_get0_name,
DSA_meth_set1_name, DSA_meth_get_flags, DSA_meth_set_flags,
DSA_meth_get0_app_data, DSA_meth_set0_app_data, DSA_meth_get_sign,
DSA_meth_set_sign, DSA_meth_get_sign_setup, DSA_meth_set_sign_setup,
DSA_meth_get_verify, DSA_meth_set_verify, DSA_meth_get_mod_exp,
DSA_meth_set_mod_exp, DSA_meth_get_bn_mod_exp, DSA_meth_set_bn_mod_exp,
DSA_meth_get_init, DSA_meth_set_init, DSA_meth_get_finish, DSA_meth_set_finish,
DSA_meth_get_paramgen, DSA_meth_set_paramgen, DSA_meth_get_keygen,
DSA_meth_set_keygen \- Routines to build up DSA methods
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/dsa.h>
.Ve
.PP
The following functions have been deprecated since OpenSSL 3.0, and can be
hidden entirely by defining \fBOPENSSL_API_COMPAT\fR with a suitable version value,
see \fBopenssl_user_macros\fR\|(7):
.PP
.Vb 1
\& DSA_METHOD *DSA_meth_new(const char *name, int flags);
\&
\& void DSA_meth_free(DSA_METHOD *dsam);
\&
\& DSA_METHOD *DSA_meth_dup(const DSA_METHOD *meth);
\&
\& const char *DSA_meth_get0_name(const DSA_METHOD *dsam);
\& int DSA_meth_set1_name(DSA_METHOD *dsam, const char *name);
\&
\& int DSA_meth_get_flags(const DSA_METHOD *dsam);
\& int DSA_meth_set_flags(DSA_METHOD *dsam, int flags);
\&
\& void *DSA_meth_get0_app_data(const DSA_METHOD *dsam);
\& int DSA_meth_set0_app_data(DSA_METHOD *dsam, void *app_data);
\&
\& DSA_SIG *(*DSA_meth_get_sign(const DSA_METHOD *dsam))(const unsigned char *,
\&                                                       int, DSA *);
\& int DSA_meth_set_sign(DSA_METHOD *dsam, DSA_SIG *(*sign)(const unsigned char *,
\&                                                          int, DSA *));
\&
\& int (*DSA_meth_get_sign_setup(const DSA_METHOD *dsam))(DSA *, BN_CTX *,$
\&                                                        BIGNUM **, BIGNUM **);
\& int DSA_meth_set_sign_setup(DSA_METHOD *dsam, int (*sign_setup)(DSA *, BN_CTX *,
\&                                                                 BIGNUM **, BIGNUM **));
\&
\& int (*DSA_meth_get_verify(const DSA_METHOD *dsam))(const unsigned char *,
\&                                                    int, DSA_SIG *, DSA *);
\& int DSA_meth_set_verify(DSA_METHOD *dsam, int (*verify)(const unsigned char *,
\&                                                         int, DSA_SIG *, DSA *));
\&
\& int (*DSA_meth_get_mod_exp(const DSA_METHOD *dsam))(DSA *dsa, BIGNUM *rr, BIGNUM *a1,
\&                                                     BIGNUM *p1, BIGNUM *a2, BIGNUM *p2,
\&                                                     BIGNUM *m, BN_CTX *ctx,
\&                                                     BN_MONT_CTX *in_mont);
\& int DSA_meth_set_mod_exp(DSA_METHOD *dsam, int (*mod_exp)(DSA *dsa, BIGNUM *rr,
\&                                                           BIGNUM *a1, BIGNUM *p1,
\&                                                           BIGNUM *a2, BIGNUM *p2,
\&                                                           BIGNUM *m, BN_CTX *ctx,
\&                                                           BN_MONT_CTX *mont));
\&
\& int (*DSA_meth_get_bn_mod_exp(const DSA_METHOD *dsam))(DSA *dsa, BIGNUM *r, BIGNUM *a,
\&                                                        const BIGNUM *p, const BIGNUM *m,
\&                                                        BN_CTX *ctx, BN_MONT_CTX *mont);
\& int DSA_meth_set_bn_mod_exp(DSA_METHOD *dsam, int (*bn_mod_exp)(DSA *dsa,
\&                                                                 BIGNUM *r,
\&                                                                 BIGNUM *a,
\&                                                                 const BIGNUM *p,
\&                                                                 const BIGNUM *m,
\&                                                                 BN_CTX *ctx,
\&                                                                 BN_MONT_CTX *mont));
\&
\& int (*DSA_meth_get_init(const DSA_METHOD *dsam))(DSA *);
\& int DSA_meth_set_init(DSA_METHOD *dsam, int (*init)(DSA *));
\&
\& int (*DSA_meth_get_finish(const DSA_METHOD *dsam))(DSA *);
\& int DSA_meth_set_finish(DSA_METHOD *dsam, int (*finish)(DSA *));
\&
\& int (*DSA_meth_get_paramgen(const DSA_METHOD *dsam))(DSA *, int,
\&                                                      const unsigned char *,
\&                                                      int, int *, unsigned long *,
\&                                                      BN_GENCB *);
\& int DSA_meth_set_paramgen(DSA_METHOD *dsam,
\&                           int (*paramgen)(DSA *, int, const unsigned char *,
\&                                           int, int *, unsigned long *, BN_GENCB *));
\&
\& int (*DSA_meth_get_keygen(const DSA_METHOD *dsam))(DSA *);
\& int DSA_meth_set_keygen(DSA_METHOD *dsam, int (*keygen)(DSA *));
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
All of the functions described on this page are deprecated.
Applications and extension implementations should instead use the
OSSL_PROVIDER APIs.
.PP
The \fBDSA_METHOD\fR type is a structure used for the provision of custom DSA
implementations. It provides a set of functions used by OpenSSL for the
implementation of the various DSA capabilities.
.PP
\&\fBDSA_meth_new()\fR creates a new \fBDSA_METHOD\fR structure. It should be given a
unique \fBname\fR and a set of \fBflags\fR. The \fBname\fR should be a NULL terminated
string, which will be duplicated and stored in the \fBDSA_METHOD\fR object. It is
the callers responsibility to free the original string. The flags will be used
during the construction of a new \fBDSA\fR object based on this \fBDSA_METHOD\fR. Any
new \fBDSA\fR object will have those flags set by default.
.PP
\&\fBDSA_meth_dup()\fR creates a duplicate copy of the \fBDSA_METHOD\fR object passed as a
parameter. This might be useful for creating a new \fBDSA_METHOD\fR based on an
existing one, but with some differences.
.PP
\&\fBDSA_meth_free()\fR destroys a \fBDSA_METHOD\fR structure and frees up any memory
associated with it.
.PP
\&\fBDSA_meth_get0_name()\fR will return a pointer to the name of this DSA_METHOD. This
is a pointer to the internal name string and so should not be freed by the
caller. \fBDSA_meth_set1_name()\fR sets the name of the DSA_METHOD to \fBname\fR. The
string is duplicated and the copy is stored in the DSA_METHOD structure, so the
caller remains responsible for freeing the memory associated with the name.
.PP
\&\fBDSA_meth_get_flags()\fR returns the current value of the flags associated with this
DSA_METHOD. \fBDSA_meth_set_flags()\fR provides the ability to set these flags.
.PP
The functions \fBDSA_meth_get0_app_data()\fR and \fBDSA_meth_set0_app_data()\fR provide the
ability to associate implementation specific data with the DSA_METHOD. It is
the application's responsibility to free this data before the DSA_METHOD is
freed via a call to \fBDSA_meth_free()\fR.
.PP
\&\fBDSA_meth_get_sign()\fR and \fBDSA_meth_set_sign()\fR get and set the function used for
creating a DSA signature respectively. This function will be
called in response to the application calling \fBDSA_do_sign()\fR (or \fBDSA_sign()\fR). The
parameters for the function have the same meaning as for \fBDSA_do_sign()\fR.
.PP
\&\fBDSA_meth_get_sign_setup()\fR and \fBDSA_meth_set_sign_setup()\fR get and set the function
used for precalculating the DSA signature values \fBk^\-1\fR and \fBr\fR. This function
will be called in response to the application calling \fBDSA_sign_setup()\fR. The
parameters for the function have the same meaning as for \fBDSA_sign_setup()\fR.
.PP
\&\fBDSA_meth_get_verify()\fR and \fBDSA_meth_set_verify()\fR get and set the function used
for verifying a DSA signature respectively. This function will be called in
response to the application calling \fBDSA_do_verify()\fR (or \fBDSA_verify()\fR). The
parameters for the function have the same meaning as for \fBDSA_do_verify()\fR.
.PP
\&\fBDSA_meth_get_mod_exp()\fR and \fBDSA_meth_set_mod_exp()\fR get and set the function used
for computing the following value:
.PP
.Vb 1
\& rr = a1^p1 * a2^p2 mod m
.Ve
.PP
This function will be called by the default OpenSSL method during verification
of a DSA signature. The result is stored in the \fBrr\fR parameter. This function
may be NULL.
.PP
\&\fBDSA_meth_get_bn_mod_exp()\fR and \fBDSA_meth_set_bn_mod_exp()\fR get and set the function
used for computing the following value:
.PP
.Vb 1
\& r = a ^ p mod m
.Ve
.PP
This function will be called by the default OpenSSL function for
\&\fBDSA_sign_setup()\fR. The result is stored in the \fBr\fR parameter. This function
may be NULL.
.PP
\&\fBDSA_meth_get_init()\fR and \fBDSA_meth_set_init()\fR get and set the function used
for creating a new DSA instance respectively. This function will be
called in response to the application calling \fBDSA_new()\fR (if the current default
DSA_METHOD is this one) or \fBDSA_new_method()\fR. The \fBDSA_new()\fR and \fBDSA_new_method()\fR
functions will allocate the memory for the new DSA object, and a pointer to this
newly allocated structure will be passed as a parameter to the function. This
function may be NULL.
.PP
\&\fBDSA_meth_get_finish()\fR and \fBDSA_meth_set_finish()\fR get and set the function used
for destroying an instance of a DSA object respectively. This function will be
called in response to the application calling \fBDSA_free()\fR. A pointer to the DSA
to be destroyed is passed as a parameter. The destroy function should be used
for DSA implementation specific clean up. The memory for the DSA itself should
not be freed by this function. This function may be NULL.
.PP
\&\fBDSA_meth_get_paramgen()\fR and \fBDSA_meth_set_paramgen()\fR get and set the function
used for generating DSA parameters respectively. This function will be called in
response to the application calling \fBDSA_generate_parameters_ex()\fR (or
\&\fBDSA_generate_parameters()\fR). The parameters for the function have the same
meaning as for \fBDSA_generate_parameters_ex()\fR.
.PP
\&\fBDSA_meth_get_keygen()\fR and \fBDSA_meth_set_keygen()\fR get and set the function
used for generating a new DSA key pair respectively. This function will be
called in response to the application calling \fBDSA_generate_key()\fR. The parameter
for the function has the same meaning as for \fBDSA_generate_key()\fR.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBDSA_meth_new()\fR and \fBDSA_meth_dup()\fR return the newly allocated DSA_METHOD object
or NULL on failure.
.PP
\&\fBDSA_meth_get0_name()\fR and \fBDSA_meth_get_flags()\fR return the name and flags
associated with the DSA_METHOD respectively.
.PP
All other DSA_meth_get_*() functions return the appropriate function pointer
that has been set in the DSA_METHOD, or NULL if no such pointer has yet been
set.
.PP
\&\fBDSA_meth_set1_name()\fR and all DSA_meth_set_*() functions return 1 on success or
0 on failure.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBDSA_new\fR\|(3), \fBDSA_new\fR\|(3), \fBDSA_generate_parameters\fR\|(3), \fBDSA_generate_key\fR\|(3),
\&\fBDSA_dup_DH\fR\|(3), \fBDSA_do_sign\fR\|(3), \fBDSA_set_method\fR\|(3), \fBDSA_SIG_new\fR\|(3),
\&\fBDSA_sign\fR\|(3), \fBDSA_size\fR\|(3), \fBDSA_get0_pqg\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
The functions described here were deprecated in OpenSSL 3.0.
.PP
The functions described here were added in OpenSSL 1.1.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2016\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
