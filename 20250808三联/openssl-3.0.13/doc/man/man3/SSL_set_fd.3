.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "SSL_SET_FD 3ossl"
.TH SSL_SET_FD 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
SSL_set_fd, SSL_set_rfd, SSL_set_wfd \- connect the SSL object with a file descriptor
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/ssl.h>
\&
\& int SSL_set_fd(SSL *ssl, int fd);
\& int SSL_set_rfd(SSL *ssl, int fd);
\& int SSL_set_wfd(SSL *ssl, int fd);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBSSL_set_fd()\fR sets the file descriptor \fBfd\fR as the input/output facility
for the TLS/SSL (encrypted) side of \fBssl\fR. \fBfd\fR will typically be the
socket file descriptor of a network connection.
.PP
When performing the operation, a \fBsocket BIO\fR is automatically created to
interface between the \fBssl\fR and \fBfd\fR. The BIO and hence the SSL engine
inherit the behaviour of \fBfd\fR. If \fBfd\fR is nonblocking, the \fBssl\fR will
also have nonblocking behaviour.
.PP
If there was already a BIO connected to \fBssl\fR, \fBBIO_free()\fR will be called
(for both the reading and writing side, if different).
.PP
\&\fBSSL_set_rfd()\fR and \fBSSL_set_wfd()\fR perform the respective action, but only
for the read channel or the write channel, which can be set independently.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
The following return values can occur:
.IP 0 4
The operation failed. Check the error stack to find out why.
.IP 1 4
.IX Item "1"
The operation succeeded.
.SH NOTES
.IX Header "NOTES"
On Windows, a socket handle is a 64\-bit data type (UINT_PTR), which leads to a
compiler warning (conversion from 'SOCKET' to 'int', possible loss of data) when
passing the socket handle to SSL_set_*\fBfd()\fR. For the time being, this warning can
safely be ignored, because although the Microsoft documentation claims that the
upper limit is INVALID_SOCKET\-1 (2^64 \- 2), in practice the current \fBsocket()\fR
implementation returns an index into the kernel handle table, the size of which
is limited to 2^24.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBSSL_get_fd\fR\|(3), \fBSSL_set_bio\fR\|(3),
\&\fBSSL_connect\fR\|(3), \fBSSL_accept\fR\|(3),
\&\fBSSL_shutdown\fR\|(3), \fBssl\fR\|(7) , \fBbio\fR\|(7)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
