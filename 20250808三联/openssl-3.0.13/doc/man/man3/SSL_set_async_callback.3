.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "SSL_SET_ASYNC_CALLBACK 3ossl"
.TH SSL_SET_ASYNC_CALLBACK 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
SSL_CTX_set_async_callback,
SSL_CTX_set_async_callback_arg,
SSL_set_async_callback,
SSL_set_async_callback_arg,
SSL_get_async_status,
SSL_async_callback_fn
\&\- manage asynchronous operations
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/ssl.h>
\&
\& typedef int (*SSL_async_callback_fn)(SSL *s, void *arg);
\& int SSL_CTX_set_async_callback(SSL_CTX *ctx, SSL_async_callback_fn callback);
\& int SSL_CTX_set_async_callback_arg(SSL_CTX *ctx, void *arg);
\& int SSL_set_async_callback(SSL *s, SSL_async_callback_fn callback);
\& int SSL_set_async_callback_arg(SSL *s, void *arg);
\& int SSL_get_async_status(SSL *s, int *status);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBSSL_CTX_set_async_callback()\fR sets an asynchronous callback function. All \fBSSL\fR
objects generated based on this \fBSSL_CTX\fR will get this callback. If an engine
supports the callback mechanism, it will be automatically called if
\&\fBSSL_MODE_ASYNC\fR has been set and an asynchronous capable engine completes a
cryptography operation to notify the application to resume the paused work flow.
.PP
\&\fBSSL_CTX_set_async_callback_arg()\fR sets the callback argument.
.PP
\&\fBSSL_set_async_callback()\fR allows an application to set a callback in an
asynchronous \fBSSL\fR object, so that when an engine completes a cryptography
operation, the callback will be called to notify the application to resume the
paused work flow.
.PP
\&\fBSSL_set_async_callback_arg()\fR sets an argument for the \fBSSL\fR object when the
above callback is called.
.PP
\&\fBSSL_get_async_status()\fR returns the engine status. This function facilitates the
communication from the engine to the application. During an SSL session,
cryptographic operations are dispatched to an engine. The engine status is very
useful for an application to know if the operation has been successfully
dispatched. If the engine does not support this additional callback method,
\&\fBASYNC_STATUS_UNSUPPORTED\fR will be returned. See \fBASYNC_WAIT_CTX_set_status()\fR
for a description of all of the status values.
.PP
An example of the above functions would be the following:
.IP 1. 4
Application sets the async callback and callback data on an SSL connection
by calling \fBSSL_set_async_callback()\fR.
.IP 2. 4
Application sets \fBSSL_MODE_ASYNC\fR and makes an asynchronous SSL call
.IP 3. 4
OpenSSL submits the asynchronous request to the engine. If a retry occurs at
this point then the status within the \fBASYNC_WAIT_CTX\fR would be set and the
async callback function would be called (goto Step 7).
.IP 4. 4
The OpenSSL engine pauses the current job and returns, so that the
application can continue processing other connections.
.IP 5. 4
At a future point in time (probably via a polling mechanism or via an
interrupt) the engine will become aware that the asynchronous request has
finished processing.
.IP 6. 4
The engine will call the application's callback passing the callback data as
a parameter.
.IP 7. 4
The callback function should then run. Note: it is a requirement that the
callback function is small and nonblocking as it will be run in the context of
a polling mechanism or an interrupt.
.IP 8. 4
It is the application's responsibility via the callback function to schedule
recalling the OpenSSL asynchronous function and to continue processing.
.IP 9. 4
The callback function has the option to check the status returned via
\&\fBSSL_get_async_status()\fR to determine whether a retry happened instead of the
request being submitted, allowing different processing if required.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBSSL_CTX_set_async_callback()\fR, \fBSSL_set_async_callback()\fR,
\&\fBSSL_CTX_set_async_callback_arg()\fR, \fBSSL_CTX_set_async_callback_arg()\fR and
\&\fBSSL_get_async_status()\fR return 1 on success or 0 on error.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBssl\fR\|(7)
.SH HISTORY
.IX Header "HISTORY"
\&\fBSSL_CTX_set_async_callback()\fR, \fBSSL_CTX_set_async_callback_arg()\fR,
\&\fBSSL_set_async_callback()\fR, \fBSSL_set_async_callback_arg()\fR and
\&\fBSSL_get_async_status()\fR were first added to OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2019\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
