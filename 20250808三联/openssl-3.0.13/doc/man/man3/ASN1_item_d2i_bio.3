.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "ASN1_ITEM_D2I_BIO 3ossl"
.TH ASN1_ITEM_D2I_BIO 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
ASN1_item_d2i_ex, ASN1_item_d2i, ASN1_item_d2i_bio_ex, ASN1_item_d2i_bio,
ASN1_item_d2i_fp_ex, ASN1_item_d2i_fp, ASN1_item_i2d_mem_bio
\&\- decode and encode DER\-encoded ASN.1 structures
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/asn1.h>
\&
\& ASN1_VALUE *ASN1_item_d2i_ex(ASN1_VALUE **pval, const unsigned char **in,
\&                              long len, const ASN1_ITEM *it,
\&                              OSSL_LIB_CTX *libctx, const char *propq);
\& ASN1_VALUE *ASN1_item_d2i(ASN1_VALUE **pval, const unsigned char **in,
\&                           long len, const ASN1_ITEM *it);
\&
\& void *ASN1_item_d2i_bio_ex(const ASN1_ITEM *it, BIO *in, void *x,
\&                            OSSL_LIB_CTX *libctx, const char *propq);
\& void *ASN1_item_d2i_bio(const ASN1_ITEM *it, BIO *in, void *x);
\&
\& void *ASN1_item_d2i_fp_ex(const ASN1_ITEM *it, FILE *in, void *x,
\&                           OSSL_LIB_CTX *libctx, const char *propq);
\& void *ASN1_item_d2i_fp(const ASN1_ITEM *it, FILE *in, void *x);
\&
\& BIO *ASN1_item_i2d_mem_bio(const ASN1_ITEM *it, const ASN1_VALUE *val);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBASN1_item_d2i_ex()\fR decodes the contents of the data stored in \fI*in\fR of length
\&\fIlen\fR which must be a DER-encoded ASN.1 structure, using the ASN.1 template
\&\fIit\fR. It places the result in \fI*pval\fR unless \fIpval\fR is NULL. If \fI*pval\fR is
non-NULL on entry then the \fBASN1_VALUE\fR present there will be reused. Otherwise
a new \fBASN1_VALUE\fR will be allocated. If any algorithm fetches are required
during the process then they will use the \fBOSSL_LIB_CTX\fRprovided in the
\&\fIlibctx\fR parameter and the property query string in \fIpropq\fR. See
"ALGORITHM FETCHING" in \fBcrypto\fR\|(7) for more information about algorithm fetching.
On exit \fI*in\fR will be updated to point to the next byte in the buffer after the
decoded structure.
.PP
\&\fBASN1_item_d2i()\fR is the same as \fBASN1_item_d2i_ex()\fR except that the default
OSSL_LIB_CTX is used (i.e. NULL) and with a NULL property query string.
.PP
\&\fBASN1_item_d2i_bio_ex()\fR decodes the contents of its input BIO \fIin\fR,
which must be a DER-encoded ASN.1 structure, using the ASN.1 template \fIit\fR
and places the result in \fI*pval\fR unless \fIpval\fR is NULL.
If \fIin\fR is NULL it returns NULL, else a pointer to the parsed structure. If any
algorithm fetches are required during the process then they will use the
\&\fBOSSL_LIB_CTX\fR provided in the \fIlibctx\fR parameter and the property query
string in \fIpropq\fR. See "ALGORITHM FETCHING" in \fBcrypto\fR\|(7) for more information
about algorithm fetching.
.PP
\&\fBASN1_item_d2i_bio()\fR is the same as \fBASN1_item_d2i_bio_ex()\fR except that the
default \fBOSSL_LIB_CTX\fR is used (i.e. NULL) and with a NULL property query
string.
.PP
\&\fBASN1_item_d2i_fp_ex()\fR is the same as \fBASN1_item_d2i_bio_ex()\fR except that a FILE
pointer is provided instead of a BIO.
.PP
\&\fBASN1_item_d2i_fp()\fR is the same as \fBASN1_item_d2i_fp_ex()\fR except that the
default \fBOSSL_LIB_CTX\fR is used (i.e. NULL) and with a NULL property query
string.
.PP
\&\fBASN1_item_i2d_mem_bio()\fR encodes the given ASN.1 value \fIval\fR
using the ASN.1 template \fIit\fR and returns the result in a memory BIO.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBASN1_item_d2i_bio()\fR returns a pointer to an \fBASN1_VALUE\fR or NULL.
.PP
\&\fBASN1_item_i2d_mem_bio()\fR returns a pointer to a memory BIO or NULL on error.
.SH HISTORY
.IX Header "HISTORY"
The functions \fBASN1_item_d2i_ex()\fR, \fBASN1_item_d2i_bio_ex()\fR, \fBASN1_item_d2i_fp_ex()\fR
and \fBASN1_item_i2d_mem_bio()\fR were added in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
