.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "OPENSSL_INSTRUMENT_BUS 3ossl"
.TH OPENSSL_INSTRUMENT_BUS 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
OPENSSL_instrument_bus, OPENSSL_instrument_bus2 \- instrument references to memory bus
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 4
\& #ifdef OPENSSL_CPUID_OBJ
\& size_t OPENSSL_instrument_bus(unsigned int *vector, size_t num);
\& size_t OPENSSL_instrument_bus2(unsigned int *vector, size_t num, size_t max);
\& #endif
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
It was empirically found that timings of references to primary memory
are subject to irregular, apparently non-deterministic variations. The
subroutines in question instrument these references for purposes of
gathering randomness for random number generator. In order to make it
bus-bound a 'flush cache line' instruction is used between probes. In
addition probes are added to \fBvector\fR elements in atomic or
interlocked manner, which should contribute additional noise on
multi-processor systems. This also means that \fBvector[num]\fR should be
zeroed upon invocation (if you want to retrieve actual probe values).
.PP
\&\fBOPENSSL_instrument_bus()\fR performs \fBnum\fR probes and records the number of
oscillator cycles every probe took.
.PP
\&\fBOPENSSL_instrument_bus2()\fR on the other hand \fBaccumulates\fR consecutive
probes with the same value, i.e. in a way it records duration of
periods when probe values appeared deterministic. The subroutine
performs at most \fBmax\fR probes in attempt to fill the \fBvector[num]\fR,
with \fBmax\fR value of 0 meaning "as many as it takes."
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
Return value of 0 indicates that CPU is not capable of performing the
benchmark, either because oscillator counter or 'flush cache line' is
not available on current platform. For reference, on x86 'flush cache
line' was introduced with the SSE2 extensions.
.PP
Otherwise number of recorded values is returned.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2011\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
