.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "SSL_CTX_SESS_SET_CACHE_SIZE 3ossl"
.TH SSL_CTX_SESS_SET_CACHE_SIZE 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
SSL_CTX_sess_set_cache_size, SSL_CTX_sess_get_cache_size \- manipulate session cache size
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/ssl.h>
\&
\& long SSL_CTX_sess_set_cache_size(SSL_CTX *ctx, long t);
\& long SSL_CTX_sess_get_cache_size(SSL_CTX *ctx);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBSSL_CTX_sess_set_cache_size()\fR sets the size of the internal session cache
of context \fBctx\fR to \fBt\fR.
This value is a hint and not an absolute; see the notes below.
.PP
\&\fBSSL_CTX_sess_get_cache_size()\fR returns the currently valid session cache size.
.SH NOTES
.IX Header "NOTES"
The internal session cache size is SSL_SESSION_CACHE_MAX_SIZE_DEFAULT,
currently 1024*20, so that up to 20000 sessions can be held. This size
can be modified using the \fBSSL_CTX_sess_set_cache_size()\fR call. A special
case is the size 0, which is used for unlimited size.
.PP
If adding the session makes the cache exceed its size, then unused
sessions are dropped from the end of the cache.
Cache space may also be reclaimed by calling
\&\fBSSL_CTX_flush_sessions\fR\|(3) to remove
expired sessions.
.PP
If the size of the session cache is reduced and more sessions are already
in the session cache, old session will be removed at the next time a
session shall be added. This removal is not synchronized with the
expiration of sessions.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBSSL_CTX_sess_set_cache_size()\fR returns the previously valid size.
.PP
\&\fBSSL_CTX_sess_get_cache_size()\fR returns the currently valid size.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBssl\fR\|(7),
\&\fBSSL_CTX_set_session_cache_mode\fR\|(3),
\&\fBSSL_CTX_sess_number\fR\|(3),
\&\fBSSL_CTX_flush_sessions\fR\|(3)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2001\-2016 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
