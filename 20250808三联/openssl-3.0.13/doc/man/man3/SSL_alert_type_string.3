.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "SSL_ALERT_TYPE_STRING 3ossl"
.TH SSL_ALERT_TYPE_STRING 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
SSL_alert_type_string, SSL_alert_type_string_long, SSL_alert_desc_string, SSL_alert_desc_string_long \- get textual description of alert information
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/ssl.h>
\&
\& const char *SSL_alert_type_string(int value);
\& const char *SSL_alert_type_string_long(int value);
\&
\& const char *SSL_alert_desc_string(int value);
\& const char *SSL_alert_desc_string_long(int value);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBSSL_alert_type_string()\fR returns a one letter string indicating the
type of the alert specified by \fBvalue\fR.
.PP
\&\fBSSL_alert_type_string_long()\fR returns a string indicating the type of the alert
specified by \fBvalue\fR.
.PP
\&\fBSSL_alert_desc_string()\fR returns a two letter string as a short form
describing the reason of the alert specified by \fBvalue\fR.
.PP
\&\fBSSL_alert_desc_string_long()\fR returns a string describing the reason
of the alert specified by \fBvalue\fR.
.SH NOTES
.IX Header "NOTES"
When one side of an SSL/TLS communication wants to inform the peer about
a special situation, it sends an alert. The alert is sent as a special message
and does not influence the normal data stream (unless its contents results
in the communication being canceled).
.PP
A warning alert is sent, when a non-fatal error condition occurs. The
"close notify" alert is sent as a warning alert. Other examples for
non-fatal errors are certificate errors ("certificate expired",
"unsupported certificate"), for which a warning alert may be sent.
(The sending party may however decide to send a fatal error.) The
receiving side may cancel the connection on reception of a warning
alert on it discretion.
.PP
Several alert messages must be sent as fatal alert messages as specified
by the TLS RFC. A fatal alert always leads to a connection abort.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
The following strings can occur for \fBSSL_alert_type_string()\fR or
\&\fBSSL_alert_type_string_long()\fR:
.IP """W""/""warning""" 4
.IX Item """W""/""warning"""
.PD 0
.IP """F""/""fatal""" 4
.IX Item """F""/""fatal"""
.IP """U""/""unknown""" 4
.IX Item """U""/""unknown"""
.PD
This indicates that no support is available for this alert type.
Probably \fBvalue\fR does not contain a correct alert message.
.PP
The following strings can occur for \fBSSL_alert_desc_string()\fR or
\&\fBSSL_alert_desc_string_long()\fR:
.IP """CN""/""close notify""" 4
.IX Item """CN""/""close notify"""
The connection shall be closed. This is a warning alert.
.IP """UM""/""unexpected message""" 4
.IX Item """UM""/""unexpected message"""
An inappropriate message was received. This alert is always fatal
and should never be observed in communication between proper
implementations.
.IP """BM""/""bad record mac""" 4
.IX Item """BM""/""bad record mac"""
This alert is returned if a record is received with an incorrect
MAC. This message is always fatal.
.IP """DF""/""decompression failure""" 4
.IX Item """DF""/""decompression failure"""
The decompression function received improper input (e.g. data
that would expand to excessive length). This message is always
fatal.
.IP """HF""/""handshake failure""" 4
.IX Item """HF""/""handshake failure"""
Reception of a handshake_failure alert message indicates that the
sender was unable to negotiate an acceptable set of security
parameters given the options available. This is a fatal error.
.IP """NC""/""no certificate""" 4
.IX Item """NC""/""no certificate"""
A client, that was asked to send a certificate, does not send a certificate
(SSLv3 only).
.IP """BC""/""bad certificate""" 4
.IX Item """BC""/""bad certificate"""
A certificate was corrupt, contained signatures that did not
verify correctly, etc
.IP """UC""/""unsupported certificate""" 4
.IX Item """UC""/""unsupported certificate"""
A certificate was of an unsupported type.
.IP """CR""/""certificate revoked""" 4
.IX Item """CR""/""certificate revoked"""
A certificate was revoked by its signer.
.IP """CE""/""certificate expired""" 4
.IX Item """CE""/""certificate expired"""
A certificate has expired or is not currently valid.
.IP """CU""/""certificate unknown""" 4
.IX Item """CU""/""certificate unknown"""
Some other (unspecified) issue arose in processing the
certificate, rendering it unacceptable.
.IP """IP""/""illegal parameter""" 4
.IX Item """IP""/""illegal parameter"""
A field in the handshake was out of range or inconsistent with
other fields. This is always fatal.
.IP """DC""/""decryption failed""" 4
.IX Item """DC""/""decryption failed"""
A TLSCiphertext decrypted in an invalid way: either it wasn't an
even multiple of the block length or its padding values, when
checked, weren't correct. This message is always fatal.
.IP """RO""/""record overflow""" 4
.IX Item """RO""/""record overflow"""
A TLSCiphertext record was received which had a length more than
2^14+2048 bytes, or a record decrypted to a TLSCompressed record
with more than 2^14+1024 bytes. This message is always fatal.
.IP """CA""/""unknown CA""" 4
.IX Item """CA""/""unknown CA"""
A valid certificate chain or partial chain was received, but the
certificate was not accepted because the CA certificate could not
be located or couldn't be matched with a known, trusted CA.  This
message is always fatal.
.IP """AD""/""access denied""" 4
.IX Item """AD""/""access denied"""
A valid certificate was received, but when access control was
applied, the sender decided not to proceed with negotiation.
This message is always fatal.
.IP """DE""/""decode error""" 4
.IX Item """DE""/""decode error"""
A message could not be decoded because some field was out of the
specified range or the length of the message was incorrect. This
message is always fatal.
.IP """CY""/""decrypt error""" 4
.IX Item """CY""/""decrypt error"""
A handshake cryptographic operation failed, including being
unable to correctly verify a signature, decrypt a key exchange,
or validate a finished message.
.IP """ER""/""export restriction""" 4
.IX Item """ER""/""export restriction"""
A negotiation not in compliance with export restrictions was
detected; for example, attempting to transfer a 1024 bit
ephemeral RSA key for the RSA_EXPORT handshake method. This
message is always fatal.
.IP """PV""/""protocol version""" 4
.IX Item """PV""/""protocol version"""
The protocol version the client has attempted to negotiate is
recognized, but not supported. (For example, old protocol
versions might be avoided for security reasons). This message is
always fatal.
.IP """IS""/""insufficient security""" 4
.IX Item """IS""/""insufficient security"""
Returned instead of handshake_failure when a negotiation has
failed specifically because the server requires ciphers more
secure than those supported by the client. This message is always
fatal.
.IP """IE""/""internal error""" 4
.IX Item """IE""/""internal error"""
An internal error unrelated to the peer or the correctness of the
protocol makes it impossible to continue (such as a memory
allocation failure). This message is always fatal.
.IP """US""/""user canceled""" 4
.IX Item """US""/""user canceled"""
This handshake is being canceled for some reason unrelated to a
protocol failure. If the user cancels an operation after the
handshake is complete, just closing the connection by sending a
close_notify is more appropriate. This alert should be followed
by a close_notify. This message is generally a warning.
.IP """NR""/""no renegotiation""" 4
.IX Item """NR""/""no renegotiation"""
Sent by the client in response to a hello request or by the
server in response to a client hello after initial handshaking.
Either of these would normally lead to renegotiation; when that
is not appropriate, the recipient should respond with this alert;
at that point, the original requester can decide whether to
proceed with the connection. One case where this would be
appropriate would be where a server has spawned a process to
satisfy a request; the process might receive security parameters
(key length, authentication, etc.) at startup and it might be
difficult to communicate changes to these parameters after that
point. This message is always a warning.
.IP """UP""/""unknown PSK identity""" 4
.IX Item """UP""/""unknown PSK identity"""
Sent by the server to indicate that it does not recognize a PSK
identity or an SRP identity.
.IP """UK""/""unknown""" 4
.IX Item """UK""/""unknown"""
This indicates that no description is available for this alert type.
Probably \fBvalue\fR does not contain a correct alert message.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBssl\fR\|(7), \fBSSL_CTX_set_info_callback\fR\|(3)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2001\-2016 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
