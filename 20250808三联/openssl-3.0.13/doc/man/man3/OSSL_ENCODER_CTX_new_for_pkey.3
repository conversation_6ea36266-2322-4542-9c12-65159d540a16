.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "OSSL_ENCODER_CTX_NEW_FOR_PKEY 3ossl"
.TH OSSL_ENCODER_CTX_NEW_FOR_PKEY 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
OSSL_ENCODER_CTX_new_for_pkey,
OSSL_ENCODER_CTX_set_cipher,
OSSL_ENCODER_CTX_set_passphrase,
OSSL_ENCODER_CTX_set_pem_password_cb,
OSSL_ENCODER_CTX_set_passphrase_cb,
OSSL_ENCODER_CTX_set_passphrase_ui
\&\- Encoder routines to encode EVP_PKEYs
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/encoder.h>
\&
\& OSSL_ENCODER_CTX *
\& OSSL_ENCODER_CTX_new_for_pkey(const EVP_PKEY *pkey, int selection,
\&                               const char *output_type,
\&                               const char *output_structure,
\&                               const char *propquery);
\&
\& int OSSL_ENCODER_CTX_set_cipher(OSSL_ENCODER_CTX *ctx,
\&                                 const char *cipher_name,
\&                                 const char *propquery);
\& int OSSL_ENCODER_CTX_set_passphrase(OSSL_ENCODER_CTX *ctx,
\&                                     const unsigned char *kstr,
\&                                     size_t klen);
\& int OSSL_ENCODER_CTX_set_pem_password_cb(OSSL_ENCODER_CTX *ctx,
\&                                          pem_password_cb *cb, void *cbarg);
\& int OSSL_ENCODER_CTX_set_passphrase_ui(OSSL_ENCODER_CTX *ctx,
\&                                        const UI_METHOD *ui_method,
\&                                        void *ui_data);
\& int OSSL_ENCODER_CTX_set_passphrase_cb(OSSL_ENCODER_CTX *ctx,
\&                                        OSSL_PASSPHRASE_CALLBACK *cb,
\&                                        void *cbarg);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBOSSL_ENCODER_CTX_new_for_pkey()\fR is a utility function that creates a
\&\fBOSSL_ENCODER_CTX\fR, finds all applicable encoder implementations and sets
them up, so almost all the caller has to do next is call functions like
\&\fBOSSL_ENCODER_to_bio\fR\|(3).  \fIoutput_type\fR determines the final output
encoding, and \fIselection\fR can be used to select what parts of the \fIpkey\fR
should be included in the output.  \fIoutput_type\fR is further discussed in
"Output types" below, and \fIselection\fR is further described in
"Selections".
.PP
Internally, \fBOSSL_ENCODER_CTX_new_for_pkey()\fR uses the names from the
\&\fBEVP_KEYMGMT\fR\|(3) implementation associated with \fIpkey\fR to build a list of
applicable encoder implementations that are used to process the \fIpkey\fR into
the encoding named by \fIoutput_type\fR, with the outermost structure named by
\&\fIoutput_structure\fR if that's relevant.  All these implementations are
implicitly fetched, with \fIpropquery\fR for finer selection.
.PP
If no suitable encoder implementation is found,
\&\fBOSSL_ENCODER_CTX_new_for_pkey()\fR still creates a \fBOSSL_ENCODER_CTX\fR, but
with no associated encoder (\fBOSSL_ENCODER_CTX_get_num_encoders\fR\|(3) returns
zero).  This helps the caller to distinguish between an error when creating
the \fBOSSL_ENCODER_CTX\fR and missing encoder implementation, and allows it to
act accordingly.
.PP
\&\fBOSSL_ENCODER_CTX_set_cipher()\fR tells the implementation what cipher
should be used to encrypt encoded keys.  The cipher is given by
name \fIcipher_name\fR.  The interpretation of that \fIcipher_name\fR is
implementation dependent.  The implementation may implement the cipher
directly itself or by other implementations, or it may choose to fetch
it.  If the implementation supports fetching the cipher, then it may
use \fIpropquery\fR as properties to be queried for when fetching.
\&\fIcipher_name\fR may also be NULL, which will result in unencrypted
encoding.
.PP
\&\fBOSSL_ENCODER_CTX_set_passphrase()\fR gives the implementation a
pass phrase to use when encrypting the encoded private key.
Alternatively, a pass phrase callback may be specified with the
following functions.
.PP
\&\fBOSSL_ENCODER_CTX_set_pem_password_cb()\fR, \fBOSSL_ENCODER_CTX_set_passphrase_ui()\fR
and \fBOSSL_ENCODER_CTX_set_passphrase_cb()\fR sets up a callback method that the
implementation can use to prompt for a pass phrase, giving the caller the
choice of preferred pass phrase callback form.  These are called indirectly,
through an internal \fBOSSL_PASSPHRASE_CALLBACK\fR\|(3) function.
.SS "Output types"
.IX Subsection "Output types"
The possible \fBEVP_PKEY\fR output types depends on the available
implementations.
.PP
OpenSSL has built in implementations for the following output types:
.ie n .IP """TEXT""" 4
.el .IP \f(CWTEXT\fR 4
.IX Item "TEXT"
The output is a human readable description of the key.
\&\fBEVP_PKEY_print_private\fR\|(3), \fBEVP_PKEY_print_public\fR\|(3) and
\&\fBEVP_PKEY_print_params\fR\|(3) use this for their output.
.ie n .IP """DER""" 4
.el .IP \f(CWDER\fR 4
.IX Item "DER"
The output is the DER encoding of the \fIselection\fR of the \fIpkey\fR.
.ie n .IP """PEM""" 4
.el .IP \f(CWPEM\fR 4
.IX Item "PEM"
The output is the \fIselection\fR of the \fIpkey\fR in PEM format.
.SS Selections
.IX Subsection "Selections"
\&\fIselection\fR can be any one of the values described in
"Selections" in \fBEVP_PKEY_fromdata\fR\|(3).
.PP
These are only 'hints' since the encoder implementations are free to
determine what makes sense to include in the output, and this may depend on
the desired output.  For example, an EC key in a PKCS#8 structure doesn't
usually include the public key.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBOSSL_ENCODER_CTX_new_for_pkey()\fR returns a pointer to an \fBOSSL_ENCODER_CTX\fR,
or NULL if it couldn't be created.
.PP
\&\fBOSSL_ENCODER_CTX_set_cipher()\fR, \fBOSSL_ENCODER_CTX_set_passphrase()\fR,
\&\fBOSSL_ENCODER_CTX_set_pem_password_cb()\fR, \fBOSSL_ENCODER_CTX_set_passphrase_ui()\fR
and \fBOSSL_ENCODER_CTX_set_passphrase_cb()\fR all return 1 on success, or 0 on
failure.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBprovider\fR\|(7), \fBOSSL_ENCODER\fR\|(3), \fBOSSL_ENCODER_CTX\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
The functions described here were added in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2019\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
