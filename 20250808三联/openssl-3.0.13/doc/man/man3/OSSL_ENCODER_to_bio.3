.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "OSSL_ENCODER_TO_BIO 3ossl"
.TH OSSL_ENCODER_TO_BIO 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
OSSL_ENCODER_to_data,
OSSL_ENCODER_to_bio,
OSSL_ENCODER_to_fp
\&\- Routines to perform an encoding
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/encoder.h>
\&
\& int OSSL_ENCODER_to_data(OSSL_ENCODER_CTX *ctx, unsigned char **pdata,
\&                          size_t *pdata_len);
\& int OSSL_ENCODER_to_bio(OSSL_ENCODER_CTX *ctx, BIO *out);
\& int OSSL_ENCODER_to_fp(OSSL_ENCODER_CTX *ctx, FILE *fp);
.Ve
.PP
Feature availability macros:
.IP "\fBOSSL_ENCODER_to_fp()\fR is only available when \fBOPENSSL_NO_STDIO\fR is undefined." 4
.IX Item "OSSL_ENCODER_to_fp() is only available when OPENSSL_NO_STDIO is undefined."
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBOSSL_ENCODER_to_data()\fR runs the encoding process for the context \fIctx\fR,
with the output going to the \fI*pdata\fR and \fI*pdata_len\fR.
If \fI*pdata\fR is NULL when \fBOSSL_ENCODER_to_data()\fR is called, a buffer will be
allocated using \fBOPENSSL_zalloc\fR\|(3), and \fI*pdata\fR will be set to point at
the start of that buffer, and \fI*pdata_len\fR will be assigned its length when
\&\fBOSSL_ENCODER_to_data()\fR returns.
If \fI*pdata\fR is non-NULL when \fBOSSL_ENCODER_to_data()\fR is called, \fI*pdata_len\fR
is assumed to have its size.  In this case, \fI*pdata\fR will be set to point
after the encoded bytes, and \fI*pdata_len\fR will be assigned the number of
remaining bytes.
.PP
\&\fBOSSL_ENCODER_to_bio()\fR runs the encoding process for the context \fIctx\fR, with
the output going to the \fBBIO\fR \fIout\fR.
.PP
\&\fBOSSL_ENCODER_to_fp()\fR does the same thing as \fBOSSL_ENCODER_to_bio()\fR, except
that the output is going to the \fBFILE\fR \fIfp\fR.
.PP
For \fBOSSL_ENCODER_to_bio()\fR and \fBOSSL_ENCODER_to_fp()\fR, the application is
required to set up the \fBBIO\fR or \fBFILE\fR properly, for example to have
it in text or binary mode as is appropriate for the encoder output type.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBOSSL_ENCODER_to_bio()\fR, \fBOSSL_ENCODER_to_fp()\fR and \fBOSSL_ENCODER_to_data()\fR
return 1 on success, or 0 on failure.
.SH EXAMPLES
.IX Header "EXAMPLES"
To encode a pkey as PKCS#8 with PEM format into a bio:
.PP
.Vb 4
\& OSSL_ENCODER_CTX *ectx;
\& const char *format = "PEM";
\& const char *structure = "PrivateKeyInfo"; /* PKCS#8 structure */
\& const unsigned char *pass = "my password";
\&
\& ectx = OSSL_ENCODER_CTX_new_for_pkey(pkey,
\&                                      OSSL_KEYMGMT_SELECT_KEYPAIR
\&                                      | OSSL_KEYMGMT_SELECT_DOMAIN_PARAMETERS,
\&                                      format, structure,
\&                                      NULL);
\& if (ectx == NULL) {
\&     /* error: no suitable potential encoders found */
\& }
\& if (pass != NULL)
\&     OSSL_ENCODER_CTX_set_passphrase(ectx, pass, strlen(pass));
\& if (OSSL_ENCODER_to_bio(ectx, bio)) {
\&     /* pkey was successfully encoded into the bio */
\& } else {
\&     /* encoding failure */
\& }
\& OSSL_ENCODER_CTX_free(ectx);
.Ve
.PP
To encode a pkey as PKCS#8 with DER format encrypted with
AES\-256\-CBC into a buffer:
.PP
.Vb 6
\& OSSL_ENCODER_CTX *ectx;
\& const char *format = "DER";
\& const char *structure = "PrivateKeyInfo"; /* PKCS#8 structure */
\& const unsigned char *pass = "my password";
\& unsigned char *data = NULL;
\& size_t datalen;
\&
\& ectx = OSSL_ENCODER_CTX_new_for_pkey(pkey,
\&                                      OSSL_KEYMGMT_SELECT_KEYPAIR
\&                                      | OSSL_KEYMGMT_SELECT_DOMAIN_PARAMETERS,
\&                                      format, structure,
\&                                      NULL);
\& if (ectx == NULL) {
\&     /* error: no suitable potential encoders found */
\& }
\& if (pass != NULL) {
\&     OSSL_ENCODER_CTX_set_passphrase(ectx, pass, strlen(pass));
\&     OSSL_ENCODER_CTX_set_cipher(ctx, "AES\-256\-CBC", NULL);
\& }
\& if (OSSL_ENCODER_to_data(ectx, &data, &datalen)) {
\&     /*
\&      * pkey was successfully encoded into a newly allocated
\&      * data buffer
\&      */
\& } else {
\&     /* encoding failure */
\& }
\& OSSL_ENCODER_CTX_free(ectx);
.Ve
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBprovider\fR\|(7), \fBOSSL_ENCODER_CTX\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
The functions described here were added in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2019\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
