.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "D2I_PKCS8PRIVATEKEY_BIO 3ossl"
.TH D2I_PKCS8PRIVATEKEY_BIO 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
d2i_PKCS8PrivateKey_bio, d2i_PKCS8PrivateKey_fp,
i2d_PKCS8PrivateKey_bio, i2d_PKCS8PrivateKey_fp,
i2d_PKCS8PrivateKey_nid_bio, i2d_PKCS8PrivateKey_nid_fp \- PKCS#8 format private key functions
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/pem.h>
\&
\& EVP_PKEY *d2i_PKCS8PrivateKey_bio(BIO *bp, EVP_PKEY **x, pem_password_cb *cb, void *u);
\& EVP_PKEY *d2i_PKCS8PrivateKey_fp(FILE *fp, EVP_PKEY **x, pem_password_cb *cb, void *u);
\&
\& int i2d_PKCS8PrivateKey_bio(BIO *bp, const EVP_PKEY *x, const EVP_CIPHER *enc,
\&                             char *kstr, int klen,
\&                             pem_password_cb *cb, void *u);
\&
\& int i2d_PKCS8PrivateKey_fp(FILE *fp, const EVP_PKEY *x, const EVP_CIPHER *enc,
\&                            char *kstr, int klen,
\&                            pem_password_cb *cb, void *u);
\&
\& int i2d_PKCS8PrivateKey_nid_bio(BIO *bp, const EVP_PKEY *x, int nid,
\&                                 char *kstr, int klen,
\&                                 pem_password_cb *cb, void *u);
\&
\& int i2d_PKCS8PrivateKey_nid_fp(FILE *fp, const EVP_PKEY *x, int nid,
\&                                char *kstr, int klen,
\&                                pem_password_cb *cb, void *u);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
The PKCS#8 functions encode and decode private keys in PKCS#8 format using both
PKCS#5 v1.5 and PKCS#5 v2.0 password based encryption algorithms.
.PP
Other than the use of DER as opposed to PEM these functions are identical to the
corresponding \fBPEM\fR function as described in \fBPEM_read_PrivateKey\fR\|(3).
.SH NOTES
.IX Header "NOTES"
These functions are currently the only way to store encrypted private keys using DER format.
.PP
Currently all the functions use BIOs or FILE pointers, there are no functions which
work directly on memory: this can be readily worked around by converting the buffers
to memory BIOs, see \fBBIO_s_mem\fR\|(3) for details.
.PP
These functions make no assumption regarding the pass phrase received from the
password callback.
It will simply be treated as a byte sequence.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBd2i_PKCS8PrivateKey_bio()\fR and \fBd2i_PKCS8PrivateKey_fp()\fR return a valid \fBEVP_PKEY\fR
structure or NULL if an error occurred.
.PP
\&\fBi2d_PKCS8PrivateKey_bio()\fR, \fBi2d_PKCS8PrivateKey_fp()\fR, \fBi2d_PKCS8PrivateKey_nid_bio()\fR
and \fBi2d_PKCS8PrivateKey_nid_fp()\fR return 1 on success or 0 on error.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBPEM_read_PrivateKey\fR\|(3),
\&\fBpassphrase\-encoding\fR\|(7)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2002\-2023 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
