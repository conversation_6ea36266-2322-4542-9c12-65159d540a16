.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "X509V3_SET_CTX 3ossl"
.TH X509V3_SET_CTX 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
X509V3_set_ctx,
X509V3_set_issuer_pkey \- X.509 v3 extension generation utilities
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/x509v3.h>
\&
\& void X509V3_set_ctx(X509V3_CTX *ctx, X509 *issuer, X509 *subject,
\&                     X509_REQ *req, X509_CRL *crl, int flags);
\& int X509V3_set_issuer_pkey(X509V3_CTX *ctx, EVP_PKEY *pkey);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBX509V3_set_ctx()\fR fills in the basic fields of \fIctx\fR of type \fBX509V3_CTX\fR,
providing details potentially needed by functions producing X509 v3 extensions,
e.g., to look up values for filling in authority key identifiers.
Any of \fIsubject\fR, \fIreq\fR, or \fIcrl\fR may be provided, pointing to a certificate,
certification request, or certificate revocation list, respectively.
When constructing the subject key identifier of a certificate by computing a
hash value of its public key, the public key is taken from \fIsubject\fR or \fIreq\fR.
Similarly, when constructing subject alternative names from any email addresses
contained in a subject DN, the subject DN is taken from \fIsubject\fR or \fIreq\fR.
If \fIsubject\fR or \fIcrl\fR is provided, \fIissuer\fR should point to its issuer,
for instance to help generating an authority key identifier extension.
Note that if \fIsubject\fR is provided, \fIissuer\fR may be the same as \fIsubject\fR,
which means that \fIsubject\fR is self-issued (or even self-signed).
\&\fIflags\fR may be 0
or contain \fBX509V3_CTX_TEST\fR, which means that just the syntax of
extension definitions is to be checked without actually producing an extension,
or \fBX509V3_CTX_REPLACE\fR, which means that each X.509v3 extension added as
defined in some configuration section shall replace any already existing
extension with the same OID.
.PP
\&\fBX509V3_set_issuer_pkey()\fR explicitly sets the issuer private key of
the certificate that has been provided in \fIctx\fR.
This should be done for self-issued certificates (which may be self-signed
or not) to provide fallback data for the authority key identifier extension.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBX509V3_set_ctx()\fR and \fBX509V3_set_issuer_pkey()\fR
return 1 on success and 0 on error.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBX509_add_ext\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
\&\fBX509V3_set_issuer_pkey()\fR was added in OpenSSL 3.0.
.PP
CTX_TEST was deprecated in OpenSSL 3.0; use X509V3_CTX_TEST instead.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2015\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
