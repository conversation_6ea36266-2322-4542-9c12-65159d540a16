.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "D2I_RSAPRIVATEKEY 3ossl"
.TH D2I_RSAPRIVATEKEY 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
d2i_DSAPrivateKey,
d2i_DSAPrivateKey_bio,
d2i_DSAPrivateKey_fp,
d2i_DSAPublicKey,
d2i_DSA_PUBKEY,
d2i_DSA_PUBKEY_bio,
d2i_DSA_PUBKEY_fp,
d2i_DSAparams,
d2i_RSAPrivateKey,
d2i_RSAPrivateKey_bio,
d2i_RSAPrivateKey_fp,
d2i_RSAPublicKey,
d2i_RSAPublicKey_bio,
d2i_RSAPublicKey_fp,
d2i_RSA_PUBKEY,
d2i_RSA_PUBKEY_bio,
d2i_RSA_PUBKEY_fp,
d2i_DHparams,
d2i_DHparams_bio,
d2i_DHparams_fp,
d2i_ECParameters,
d2i_ECPrivateKey,
d2i_ECPrivateKey_bio,
d2i_ECPrivateKey_fp,
d2i_EC_PUBKEY,
d2i_EC_PUBKEY_bio,
d2i_EC_PUBKEY_fp,
i2d_RSAPrivateKey,
i2d_RSAPrivateKey_bio,
i2d_RSAPrivateKey_fp,
i2d_RSAPublicKey,
i2d_RSAPublicKey_bio,
i2d_RSAPublicKey_fp,
i2d_RSA_PUBKEY,
i2d_RSA_PUBKEY_bio,
i2d_RSA_PUBKEY_fp,
i2d_DHparams,
i2d_DHparams_bio,
i2d_DHparams_fp,
i2d_DSAPrivateKey,
i2d_DSAPrivateKey_bio,
i2d_DSAPrivateKey_fp,
i2d_DSAPublicKey,
i2d_DSA_PUBKEY,
i2d_DSA_PUBKEY_bio,
i2d_DSA_PUBKEY_fp,
i2d_DSAparams,
i2d_ECParameters,
i2d_ECPrivateKey,
i2d_ECPrivateKey_bio,
i2d_ECPrivateKey_fp,
i2d_EC_PUBKEY,
i2d_EC_PUBKEY_bio,
i2d_EC_PUBKEY_fp
\&\- DEPRECATED
.SH SYNOPSIS
.IX Header "SYNOPSIS"
The following functions have been deprecated since OpenSSL 3.0, and can be
hidden entirely by defining \fBOPENSSL_API_COMPAT\fR with a suitable version value,
see \fBopenssl_user_macros\fR\|(7):
.PP
.Vb 12
\& TYPE *d2i_TYPEPrivateKey(TYPE **a, const unsigned char **ppin, long length);
\& TYPE *d2i_TYPEPrivateKey_bio(BIO *bp, TYPE **a);
\& TYPE *d2i_TYPEPrivateKey_fp(FILE *fp, TYPE **a);
\& TYPE *d2i_TYPEPublicKey(TYPE **a, const unsigned char **ppin, long length);
\& TYPE *d2i_TYPEPublicKey_bio(BIO *bp, TYPE **a);
\& TYPE *d2i_TYPEPublicKey_fp(FILE *fp, TYPE **a);
\& TYPE *d2i_TYPEparams(TYPE **a, const unsigned char **ppin, long length);
\& TYPE *d2i_TYPEparams_bio(BIO *bp, TYPE **a);
\& TYPE *d2i_TYPEparams_fp(FILE *fp, TYPE **a);
\& TYPE *d2i_TYPE_PUBKEY(TYPE **a, const unsigned char **ppin, long length);
\& TYPE *d2i_TYPE_PUBKEY_bio(BIO *bp, TYPE **a);
\& TYPE *d2i_TYPE_PUBKEY_fp(FILE *fp, TYPE **a);
\&
\& int i2d_TYPEPrivateKey(const TYPE *a, unsigned char **ppout);
\& int i2d_TYPEPrivateKey(TYPE *a, unsigned char **ppout);
\& int i2d_TYPEPrivateKey_fp(FILE *fp, const TYPE *a);
\& int i2d_TYPEPrivateKey_fp(FILE *fp, TYPE *a);
\& int i2d_TYPEPrivateKey_bio(BIO *bp, const TYPE *a);
\& int i2d_TYPEPrivateKey_bio(BIO *bp, TYPE *a);
\& int i2d_TYPEPublicKey(const TYPE *a, unsigned char **ppout);
\& int i2d_TYPEPublicKey(TYPE *a, unsigned char **ppout);
\& int i2d_TYPEPublicKey_fp(FILE *fp, const TYPE *a);
\& int i2d_TYPEPublicKey_fp(FILE *fp, TYPE *a);
\& int i2d_TYPEPublicKey_bio(BIO *bp, const TYPE *a);
\& int i2d_TYPEPublicKey_bio(BIO *bp, TYPE *a);
\& int i2d_TYPEparams(const TYPE *a, unsigned char **ppout);
\& int i2d_TYPEparams(TYPE *a, unsigned char **ppout);
\& int i2d_TYPEparams_fp(FILE *fp, const TYPE *a);
\& int i2d_TYPEparams_fp(FILE *fp, TYPE *a);
\& int i2d_TYPEparams_bio(BIO *bp, const TYPE *a);
\& int i2d_TYPEparams_bio(BIO *bp, TYPE *a);
\& int i2d_TYPE_PUBKEY(const TYPE *a, unsigned char **ppout);
\& int i2d_TYPE_PUBKEY(TYPE *a, unsigned char **ppout);
\& int i2d_TYPE_PUBKEY_fp(FILE *fp, const TYPE *a);
\& int i2d_TYPE_PUBKEY_fp(FILE *fp, TYPE *a);
\& int i2d_TYPE_PUBKEY_bio(BIO *bp, const TYPE *a);
\& int i2d_TYPE_PUBKEY_bio(BIO *bp, TYPE *a);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
All functions described here are deprecated.  Please use \fBOSSL_DECODER\fR\|(3)
instead of the \fBd2i\fR functions and \fBOSSL_ENCODER\fR\|(3) instead of the \fBi2d\fR
functions.  See "Migration" below.
.PP
In the description here, \fR\f(BITYPE\fR\fB\fR is used a placeholder for any of the
OpenSSL datatypes, such as \fBRSA\fR.
The function parameters \fIppin\fR and \fIppout\fR are generally either both named
\&\fIpp\fR in the headers, or \fIin\fR and \fIout\fR.
.PP
All the functions here behave the way that's described in \fBd2i_X509\fR\|(3).
.PP
Please note that not all functions in the synopsis are available for all key
types.  For example, there are no \fBd2i_RSAparams()\fR or \fBi2d_RSAparams()\fR,
because the PKCS#1 \fBRSA\fR structure doesn't include any key parameters.
.PP
\&\fBd2i_\fR\f(BITYPE\fR\fBPrivateKey\fR() and derivates thereof decode DER encoded
\&\fR\f(BITYPE\fR\fB\fR private key data organized in a type specific structure.
.PP
\&\fBd2i_\fR\f(BITYPE\fR\fBPublicKey\fR() and derivates thereof decode DER encoded
\&\fR\f(BITYPE\fR\fB\fR public key data organized in a type specific structure.
.PP
\&\fBd2i_\fR\f(BITYPE\fR\fBparams\fR() and derivates thereof decode DER encoded \fR\f(BITYPE\fR\fB\fR
key parameters organized in a type specific structure.
.PP
\&\fBd2i_\fR\f(BITYPE\fR\fB_PUBKEY\fR() and derivates thereof decode DER encoded \fR\f(BITYPE\fR\fB\fR
public key data organized in a \fBSubjectPublicKeyInfo\fR structure.
.PP
\&\fBi2d_\fR\f(BITYPE\fR\fBPrivateKey\fR() and derivates thereof encode the private key
\&\fR\f(BITYPE\fR\fB\fR data into a type specific DER encoded structure.
.PP
\&\fBi2d_\fR\f(BITYPE\fR\fBPublicKey\fR() and derivates thereof encode the public key
\&\fR\f(BITYPE\fR\fB\fR data into a type specific DER encoded structure.
.PP
\&\fBi2d_\fR\f(BITYPE\fR\fBparams\fR() and derivates thereof encode the \fR\f(BITYPE\fR\fB\fR key
parameters data into a type specific DER encoded structure.
.PP
\&\fBi2d_\fR\f(BITYPE\fR\fB_PUBKEY\fR() and derivates thereof encode the public key
\&\fR\f(BITYPE\fR\fB\fR data into a DER encoded \fBSubjectPublicKeyInfo\fR structure.
.PP
For example, \fBd2i_RSAPrivateKey()\fR and \fBd2i_RSAPublicKey()\fR expects the
structure defined by PKCS#1.
Similarly, \fBi2d_RSAPrivateKey()\fR and  \fBi2d_RSAPublicKey()\fR produce DER encoded
string organized according to PKCS#1.
.SS Migration
.IX Subsection "Migration"
Migration from the diverse \fR\f(BITYPE\fR\fB\fRs requires using corresponding new
OpenSSL types.  For all \fB\fR\f(BITYPE\fR\fB\fRs described here, the corresponding new
type is \fBEVP_PKEY\fR.  The rest of this section assumes that this has been
done, exactly how to do that is described elsewhere.
.PP
There are two migration paths:
.IP \(bu 4
Replace
b<d2i_\fITYPE\fR\fBPrivateKey()\fR> with \fBd2i_PrivateKey\fR\|(3),
b<d2i_\fITYPE\fR\fBPublicKey()\fR> with \fBd2i_PublicKey\fR\|(3),
b<d2i_\fITYPE\fR\fBparams()\fR> with \fBd2i_KeyParams\fR\|(3),
b<d2i_\fITYPE\fR\fB_PUBKEY()\fR> with \fBd2i_PUBKEY\fR\|(3),
b<i2d_\fITYPE\fR\fBPrivateKey()\fR> with \fBi2d_PrivateKey\fR\|(3),
b<i2d_\fITYPE\fR\fBPublicKey()\fR> with \fBi2d_PublicKey\fR\|(3),
b<i2d_\fITYPE\fR\fBparams()\fR> with \fBi2d_KeyParams\fR\|(3),
b<i2d_\fITYPE\fR\fB_PUBKEY()\fR> with \fBi2d_PUBKEY\fR\|(3).
A caveat is that \fBi2d_PrivateKey\fR\|(3) may output a DER encoded PKCS#8
outermost structure instead of the type specific structure, and that
\&\fBd2i_PrivateKey\fR\|(3) recognises and unpacks a PKCS#8 structures.
.IP \(bu 4
Use \fBOSSL_DECODER\fR\|(3) and \fBOSSL_ENCODER\fR\|(3).  How to migrate is described
below.  All those descriptions assume that the key to be encoded is in the
variable \fIpkey\fR.
.PP
\fIMigrating \fR\f(BIi2d\fR\fI functions to \fR\f(BIOSSL_ENCODER\fR
.IX Subsection "Migrating i2d functions to OSSL_ENCODER"
.PP
The exact \fBOSSL_ENCODER\fR\|(3) output is driven by arguments rather than by
function names.  The sample code to get DER encoded output in a type
specific structure is uniform, the only things that vary are the selection
of what part of the \fBEVP_PKEY\fR should be output, and the structure.  The
\&\fBi2d\fR functions names can therefore be translated into two variables,
\&\fIselection\fR and \fIstructure\fR as follows:
.IP "\fBi2d_\fR\f(BITYPE\fR\fBPrivateKey\fR() translates into:" 4
.IX Item "i2d_TYPEPrivateKey() translates into:"
.Vb 2
\& int selection = EVP_PKEY_KEYPAIR;
\& const char *structure = "type\-specific";
.Ve
.IP "\fBi2d_\fR\f(BITYPE\fR\fBPublicKey\fR() translates into:" 4
.IX Item "i2d_TYPEPublicKey() translates into:"
.Vb 2
\& int selection = EVP_PKEY_PUBLIC_KEY;
\& const char *structure = "type\-specific";
.Ve
.IP "\fBi2d_\fR\f(BITYPE\fR\fBparams\fR() translates into:" 4
.IX Item "i2d_TYPEparams() translates into:"
.Vb 2
\& int selection = EVP_PKEY_PARAMETERS;
\& const char *structure = "type\-specific";
.Ve
.IP "\fBi2d_\fR\f(BITYPE\fR\fB_PUBKEY\fR() translates into:" 4
.IX Item "i2d_TYPE_PUBKEY() translates into:"
.Vb 2
\& int selection = EVP_PKEY_PUBLIC_KEY;
\& const char *structure = "SubjectPublicKeyInfo";
.Ve
.PP
The following sample code does the rest of the work:
.PP
.Vb 10
\& unsigned char *p = buffer;     /* |buffer| is supplied by the caller */
\& size_t len = buffer_size;      /* assumed be the size of |buffer| */
\& OSSL_ENCODER_CTX *ctx =
\&     OSSL_ENCODER_CTX_new_for_pkey(pkey, selection, "DER", structure,
\&                                   NULL, NULL);
\& if (ctx == NULL) {
\&     /* fatal error handling */
\& }
\& if (OSSL_ENCODER_CTX_get_num_encoders(ctx) == 0) {
\&     OSSL_ENCODER_CTX_free(ctx);
\&     /* non\-fatal error handling */
\& }
\& if (!OSSL_ENCODER_to_data(ctx, &p, &len)) {
\&     OSSL_ENCODER_CTX_free(ctx);
\&     /* error handling */
\& }
\& OSSL_ENCODER_CTX_free(ctx);
.Ve
.SH NOTES
.IX Header "NOTES"
The letters \fBi\fR and \fBd\fR in \fBi2d_\fR\f(BITYPE\fR() stand for
"internal" (that is, an internal C structure) and "DER" respectively.
So \fBi2d_\fR\f(BITYPE\fR\fB\fR() converts from internal to DER.
.PP
The functions can also understand \fBBER\fR forms.
.PP
The actual TYPE structure passed to \fBi2d_\fR\f(BITYPE\fR() must be a valid
populated \fB\fR\f(BITYPE\fR\fB\fR structure \-\- it \fBcannot\fR simply be fed with an
empty structure such as that returned by \fBTYPE_new()\fR.
.PP
The encoded data is in binary form and may contain embedded zeros.
Therefore, any FILE pointers or BIOs should be opened in binary mode.
Functions such as \fBstrlen()\fR will \fBnot\fR return the correct length
of the encoded structure.
.PP
The ways that \fI*ppin\fR and \fI*ppout\fR are incremented after the operation
can trap the unwary. See the \fBWARNINGS\fR section in \fBd2i_X509\fR\|(3) for some
common errors.
The reason for this-auto increment behaviour is to reflect a typical
usage of ASN1 functions: after one structure is encoded or decoded
another will be processed after it.
.PP
The following points about the data types might be useful:
.IP \fBDSA_PUBKEY\fR 4
.IX Item "DSA_PUBKEY"
Represents a DSA public key using a \fBSubjectPublicKeyInfo\fR structure.
.IP "\fBDSAPublicKey\fR, \fBDSAPrivateKey\fR" 4
.IX Item "DSAPublicKey, DSAPrivateKey"
Use a non-standard OpenSSL format and should be avoided; use \fBDSA_PUBKEY\fR,
\&\fBPEM_write_PrivateKey\fR\|(3), or similar instead.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBd2i_\fR\f(BITYPE\fR(), \fBd2i_\fR\f(BITYPE\fR\fB_bio\fR() and \fBd2i_\fR\f(BITYPE\fR\fB_fp\fR() return a valid
\&\fB\fR\f(BITYPE\fR\fB\fR structure or NULL if an error occurs.  If the "reuse" capability has
been used with a valid structure being passed in via \fIa\fR, then the object is
freed in the event of error and \fI*a\fR is set to NULL.
.PP
\&\fBi2d_\fR\f(BITYPE\fR() returns the number of bytes successfully encoded or a negative
value if an error occurs.
.PP
\&\fBi2d_\fR\f(BITYPE\fR\fB_bio\fR() and \fBi2d_\fR\f(BITYPE\fR\fB_fp\fR() return 1 for success and 0 if an
error occurs.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBOSSL_ENCODER\fR\|(3), \fBOSSL_DECODER\fR\|(3),
\&\fBd2i_PrivateKey\fR\|(3), \fBd2i_PublicKey\fR\|(3), \fBd2i_KeyParams\fR\|(3),
\&\fBd2i_PUBKEY\fR\|(3),
\&\fBi2d_PrivateKey\fR\|(3), \fBi2d_PublicKey\fR\|(3), \fBi2d_KeyParams\fR\|(3),
\&\fBi2d_PUBKEY\fR\|(3)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2020\-2023 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
