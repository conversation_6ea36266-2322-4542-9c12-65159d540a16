.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "SSL_CTX_SET_TMP_DH_CALLBACK 3ossl"
.TH SSL_CTX_SET_TMP_DH_CALLBACK 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
SSL_CTX_set_dh_auto, SSL_set_dh_auto, SSL_CTX_set0_tmp_dh_pkey,
SSL_set0_tmp_dh_pkey, SSL_CTX_set_tmp_dh_callback, SSL_CTX_set_tmp_dh,
SSL_set_tmp_dh_callback, SSL_set_tmp_dh
\&\- handle DH keys for ephemeral key exchange
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/ssl.h>
\&
\& long SSL_CTX_set_dh_auto(SSL_CTX *ctx, int onoff);
\& long SSL_set_dh_auto(SSL *s, int onoff);
\& int SSL_CTX_set0_tmp_dh_pkey(SSL_CTX *ctx, EVP_PKEY *dhpkey);
\& int SSL_set0_tmp_dh_pkey(SSL *s, EVP_PKEY *dhpkey);
.Ve
.PP
The following functions have been deprecated since OpenSSL 3.0, and can be
hidden entirely by defining \fBOPENSSL_API_COMPAT\fR with a suitable version value,
see \fBopenssl_user_macros\fR\|(7):
.PP
.Vb 4
\& void SSL_CTX_set_tmp_dh_callback(SSL_CTX *ctx,
\&                                  DH *(*tmp_dh_callback)(SSL *ssl, int is_export,
\&                                                         int keylength));
\& long SSL_CTX_set_tmp_dh(SSL_CTX *ctx, DH *dh);
\&
\& void SSL_set_tmp_dh_callback(SSL *ctx,
\&                              DH *(*tmp_dh_callback)(SSL *ssl, int is_export,
\&                                                     int keylength));
\& long SSL_set_tmp_dh(SSL *ssl, DH *dh);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
The functions described on this page are relevant for servers only.
.PP
Some ciphersuites may use ephemeral Diffie-Hellman (DH) key exchange. In these
cases, the session data is negotiated using the ephemeral/temporary DH key and
the key supplied and certified by the certificate chain is only used for
signing. Anonymous ciphers (without a permanent server key) also use ephemeral
DH keys.
.PP
Using ephemeral DH key exchange yields forward secrecy as the connection
can only be decrypted when the DH key is known. By generating a temporary
DH key inside the server application that is lost when the application
is left, it becomes impossible for an attacker to decrypt past sessions,
even if they get hold of the normal (certified) key, as this key was
only used for signing.
.PP
In order to perform a DH key exchange the server must use a DH group
(DH parameters) and generate a DH key. The server will always generate
a new DH key during the negotiation.
.PP
As generating DH parameters is extremely time consuming, an application
should not generate the parameters on the fly. DH parameters can be reused, as
the actual key is newly generated during the negotiation.
.PP
Typically applications should use well known DH parameters that have built-in
support in OpenSSL. The macros \fBSSL_CTX_set_dh_auto()\fR and \fBSSL_set_dh_auto()\fR
configure OpenSSL to use the default built-in DH parameters for the \fBSSL_CTX\fR
and \fBSSL\fR objects respectively. Passing a value of 1 in the \fIonoff\fR parameter
switches the feature on, and passing a value of 0 switches it off. The default
setting is off.
.PP
If "auto" DH parameters are switched on then the parameters will be selected to
be consistent with the size of the key associated with the server's certificate.
If there is no certificate (e.g. for PSK ciphersuites), then it it will be
consistent with the size of the negotiated symmetric cipher key.
.PP
Applications may supply their own DH parameters instead of using the built-in
values. This approach is discouraged and applications should in preference use
the built-in parameter support described above. Applications wishing to supply
their own DH parameters should call \fBSSL_CTX_set0_tmp_dh_pkey()\fR or
\&\fBSSL_set0_tmp_dh_pkey()\fR to supply the parameters for the \fBSSL_CTX\fR or \fBSSL\fR
respectively. The parameters should be supplied in the \fIdhpkey\fR argument as
an \fBEVP_PKEY\fR containing DH parameters. Ownership of the \fIdhpkey\fR value is
passed to the \fBSSL_CTX\fR or \fBSSL\fR object as a result of this call, and so the
caller should not free it if the function call is successful.
.PP
The deprecated macros \fBSSL_CTX_set_tmp_dh()\fR and \fBSSL_set_tmp_dh()\fR do the same
thing as \fBSSL_CTX_set0_tmp_dh_pkey()\fR and \fBSSL_set0_tmp_dh_pkey()\fR except that the
DH parameters are supplied in a \fBDH\fR object instead in the \fIdh\fR argument, and
ownership of the \fBDH\fR object is retained by the application. Applications
should use "auto" parameters instead, or call \fBSSL_CTX_set0_tmp_dh_pkey()\fR or
\&\fBSSL_set0_tmp_dh_pkey()\fR as appropriate.
.PP
An application may instead specify the DH parameters via a callback function
using the functions \fBSSL_CTX_set_tmp_dh_callback()\fR or \fBSSL_set_tmp_dh_callback()\fR
to set the callback for the \fBSSL_CTX\fR or \fBSSL\fR object respectively. These
functions are deprecated. Applications should instead use "auto" parameters, or
specify the parameters via \fBSSL_CTX_set0_tmp_dh_pkey()\fR or \fBSSL_set0_tmp_dh_pkey()\fR
as appropriate.
.PP
The callback will be invoked during a connection when DH parameters are
required. The \fBSSL\fR object for the current connection is supplied as an
argument. Previous versions of OpenSSL used the \fBis_export\fR and \fBkeylength\fR
arguments to control parameter generation for export and non-export
cipher suites. Modern OpenSSL does not support export ciphersuites and so these
arguments are unused and can be ignored by the callback. The callback should
return the parameters to be used in a DH object. Ownership of the DH object is
retained by the application and should later be freed.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
All of these functions/macros return 1 for success or 0 on error.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBssl\fR\|(7), \fBSSL_CTX_set_cipher_list\fR\|(3),
\&\fBSSL_CTX_set_options\fR\|(3),
\&\fBopenssl\-ciphers\fR\|(1), \fBopenssl\-dhparam\fR\|(1)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2001\-2023 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
