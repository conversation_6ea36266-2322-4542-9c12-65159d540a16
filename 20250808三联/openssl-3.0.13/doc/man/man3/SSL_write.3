.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "SSL_WRITE 3ossl"
.TH SSL_WRITE 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
SSL_write_ex, SSL_write, SSL_sendfile \- write bytes to a TLS/SSL connection
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/ssl.h>
\&
\& ossl_ssize_t SSL_sendfile(SSL *s, int fd, off_t offset, size_t size, int flags);
\& int SSL_write_ex(SSL *s, const void *buf, size_t num, size_t *written);
\& int SSL_write(SSL *ssl, const void *buf, int num);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBSSL_write_ex()\fR and \fBSSL_write()\fR write \fBnum\fR bytes from the buffer \fBbuf\fR into
the specified \fBssl\fR connection. On success \fBSSL_write_ex()\fR will store the number
of bytes written in \fB*written\fR.
.PP
\&\fBSSL_sendfile()\fR writes \fBsize\fR bytes from offset \fBoffset\fR in the file
descriptor \fBfd\fR to the specified SSL connection \fBs\fR. This function provides
efficient zero-copy semantics. \fBSSL_sendfile()\fR is available only when
Kernel TLS is enabled, which can be checked by calling \fBBIO_get_ktls_send()\fR.
It is provided here to allow users to maintain the same interface.
The meaning of \fBflags\fR is platform dependent.
Currently, under Linux it is ignored.
.SH NOTES
.IX Header "NOTES"
In the paragraphs below a "write function" is defined as one of either
\&\fBSSL_write_ex()\fR, or \fBSSL_write()\fR.
.PP
If necessary, a write function will negotiate a TLS/SSL session, if not already
explicitly performed by \fBSSL_connect\fR\|(3) or \fBSSL_accept\fR\|(3). If the peer
requests a re-negotiation, it will be performed transparently during
the write function operation. The behaviour of the write functions depends on the
underlying BIO.
.PP
For the transparent negotiation to succeed, the \fBssl\fR must have been
initialized to client or server mode. This is being done by calling
\&\fBSSL_set_connect_state\fR\|(3) or \fBSSL_set_accept_state()\fR
before the first call to a write function.
.PP
If the underlying BIO is \fBblocking\fR, the write functions will only return, once
the write operation has been finished or an error occurred.
.PP
If the underlying BIO is \fBnonblocking\fR the write functions will also return
when the underlying BIO could not satisfy the needs of the function to continue
the operation. In this case a call to \fBSSL_get_error\fR\|(3) with the
return value of the write function will yield \fBSSL_ERROR_WANT_READ\fR
or \fBSSL_ERROR_WANT_WRITE\fR. As at any time a re-negotiation is possible, a
call to a write function can also cause read operations! The calling process
then must repeat the call after taking appropriate action to satisfy the needs
of the write function. The action depends on the underlying BIO. When using a
nonblocking socket, nothing is to be done, but \fBselect()\fR can be used to check
for the required condition. When using a buffering BIO, like a BIO pair, data
must be written into or retrieved out of the BIO before being able to continue.
.PP
The write functions will only return with success when the complete contents of
\&\fBbuf\fR of length \fBnum\fR has been written. This default behaviour can be changed
with the SSL_MODE_ENABLE_PARTIAL_WRITE option of \fBSSL_CTX_set_mode\fR\|(3). When
this flag is set the write functions will also return with success when a
partial write has been successfully completed. In this case the write function
operation is considered completed. The bytes are sent and a new write call with
a new buffer (with the already sent bytes removed) must be started. A partial
write is performed with the size of a message block, which is 16kB.
.SH WARNINGS
.IX Header "WARNINGS"
When a write function call has to be repeated because \fBSSL_get_error\fR\|(3)
returned \fBSSL_ERROR_WANT_READ\fR or \fBSSL_ERROR_WANT_WRITE\fR, it must be repeated
with the same arguments.
The data that was passed might have been partially processed.
When \fBSSL_MODE_ACCEPT_MOVING_WRITE_BUFFER\fR was set using \fBSSL_CTX_set_mode\fR\|(3)
the pointer can be different, but the data and length should still be the same.
.PP
You should not call \fBSSL_write()\fR with num=0, it will return an error.
\&\fBSSL_write_ex()\fR can be called with num=0, but will not send application data to
the peer.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBSSL_write_ex()\fR will return 1 for success or 0 for failure. Success means that
all requested application data bytes have been written to the SSL connection or,
if SSL_MODE_ENABLE_PARTIAL_WRITE is in use, at least 1 application data byte has
been written to the SSL connection. Failure means that not all the requested
bytes have been written yet (if SSL_MODE_ENABLE_PARTIAL_WRITE is not in use) or
no bytes could be written to the SSL connection (if
SSL_MODE_ENABLE_PARTIAL_WRITE is in use). Failures can be retryable (e.g. the
network write buffer has temporarily filled up) or non-retryable (e.g. a fatal
network error). In the event of a failure call \fBSSL_get_error\fR\|(3) to find out
the reason which indicates whether the call is retryable or not.
.PP
For \fBSSL_write()\fR the following return values can occur:
.IP "> 0" 4
.IX Item "> 0"
The write operation was successful, the return value is the number of
bytes actually written to the TLS/SSL connection.
.IP "<= 0" 4
.IX Item "<= 0"
The write operation was not successful, because either the connection was
closed, an error occurred or action must be taken by the calling process.
Call \fBSSL_get_error()\fR with the return value \fBret\fR to find out the reason.
.Sp
Old documentation indicated a difference between 0 and \-1, and that \-1 was
retryable.
You should instead call \fBSSL_get_error()\fR to find out if it's retryable.
.PP
For \fBSSL_sendfile()\fR, the following return values can occur:
.IP ">= 0" 4
.IX Item ">= 0"
The write operation was successful, the return value is the number
of bytes of the file written to the TLS/SSL connection. The return
value can be less than \fBsize\fR for a partial write.
.IP "< 0" 4
.IX Item "< 0"
The write operation was not successful, because either the connection was
closed, an error occurred or action must be taken by the calling process.
Call \fBSSL_get_error()\fR with the return value to find out the reason.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBSSL_get_error\fR\|(3), \fBSSL_read_ex\fR\|(3), \fBSSL_read\fR\|(3)
\&\fBSSL_CTX_set_mode\fR\|(3), \fBSSL_CTX_new\fR\|(3),
\&\fBSSL_connect\fR\|(3), \fBSSL_accept\fR\|(3)
\&\fBSSL_set_connect_state\fR\|(3), \fBBIO_ctrl\fR\|(3),
\&\fBssl\fR\|(7), \fBbio\fR\|(7)
.SH HISTORY
.IX Header "HISTORY"
The \fBSSL_write_ex()\fR function was added in OpenSSL 1.1.1.
The \fBSSL_sendfile()\fR function was added in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
