.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "RSA_GET0_KEY 3ossl"
.TH RSA_GET0_KEY 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
RSA_set0_key, RSA_set0_factors, RSA_set0_crt_params, RSA_get0_key,
RSA_get0_factors, RSA_get0_crt_params,
RSA_get0_n, RSA_get0_e, RSA_get0_d, RSA_get0_p, RSA_get0_q,
RSA_get0_dmp1, RSA_get0_dmq1, RSA_get0_iqmp, RSA_get0_pss_params,
RSA_clear_flags,
RSA_test_flags, RSA_set_flags, RSA_get0_engine, RSA_get_multi_prime_extra_count,
RSA_get0_multi_prime_factors, RSA_get0_multi_prime_crt_params,
RSA_set0_multi_prime_params, RSA_get_version
\&\- Routines for getting and setting data in an RSA object
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/rsa.h>
.Ve
.PP
The following functions have been deprecated since OpenSSL 3.0, and can be
hidden entirely by defining \fBOPENSSL_API_COMPAT\fR with a suitable version value,
see \fBopenssl_user_macros\fR\|(7):
.PP
.Vb 10
\& int RSA_set0_key(RSA *r, BIGNUM *n, BIGNUM *e, BIGNUM *d);
\& int RSA_set0_factors(RSA *r, BIGNUM *p, BIGNUM *q);
\& int RSA_set0_crt_params(RSA *r, BIGNUM *dmp1, BIGNUM *dmq1, BIGNUM *iqmp);
\& void RSA_get0_key(const RSA *r,
\&                   const BIGNUM **n, const BIGNUM **e, const BIGNUM **d);
\& void RSA_get0_factors(const RSA *r, const BIGNUM **p, const BIGNUM **q);
\& void RSA_get0_crt_params(const RSA *r,
\&                          const BIGNUM **dmp1, const BIGNUM **dmq1,
\&                          const BIGNUM **iqmp);
\& const BIGNUM *RSA_get0_n(const RSA *d);
\& const BIGNUM *RSA_get0_e(const RSA *d);
\& const BIGNUM *RSA_get0_d(const RSA *d);
\& const BIGNUM *RSA_get0_p(const RSA *d);
\& const BIGNUM *RSA_get0_q(const RSA *d);
\& const BIGNUM *RSA_get0_dmp1(const RSA *r);
\& const BIGNUM *RSA_get0_dmq1(const RSA *r);
\& const BIGNUM *RSA_get0_iqmp(const RSA *r);
\& const RSA_PSS_PARAMS *RSA_get0_pss_params(const RSA *r);
\& void RSA_clear_flags(RSA *r, int flags);
\& int RSA_test_flags(const RSA *r, int flags);
\& void RSA_set_flags(RSA *r, int flags);
\& ENGINE *RSA_get0_engine(RSA *r);
\& int RSA_get_multi_prime_extra_count(const RSA *r);
\& int RSA_get0_multi_prime_factors(const RSA *r, const BIGNUM *primes[]);
\& int RSA_get0_multi_prime_crt_params(const RSA *r, const BIGNUM *exps[],
\&                                     const BIGNUM *coeffs[]);
\& int RSA_set0_multi_prime_params(RSA *r, BIGNUM *primes[], BIGNUM *exps[],
\&                                BIGNUM *coeffs[], int pnum);
\& int RSA_get_version(RSA *r);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
All of the functions described on this page are deprecated.
Applications should instead use \fBEVP_PKEY_get_bn_param\fR\|(3) for any methods that
return a \fBBIGNUM\fR. Refer to \fBEVP_PKEY\-DH\fR\|(7) for more information.
.PP
An RSA object contains the components for the public and private key,
\&\fBn\fR, \fBe\fR, \fBd\fR, \fBp\fR, \fBq\fR, \fBdmp1\fR, \fBdmq1\fR and \fBiqmp\fR.  \fBn\fR is
the modulus common to both public and private key, \fBe\fR is the public
exponent and \fBd\fR is the private exponent.  \fBp\fR, \fBq\fR, \fBdmp1\fR,
\&\fBdmq1\fR and \fBiqmp\fR are the factors for the second representation of a
private key (see PKCS#1 section 3 Key Types), where \fBp\fR and \fBq\fR are
the first and second factor of \fBn\fR and \fBdmp1\fR, \fBdmq1\fR and \fBiqmp\fR
are the exponents and coefficient for CRT calculations.
.PP
For multi-prime RSA (defined in RFC 8017), there are also one or more
\&'triplet' in an RSA object. A triplet contains three members, \fBr\fR, \fBd\fR
and \fBt\fR. \fBr\fR is the additional prime besides \fBp\fR and \fBq\fR. \fBd\fR and
\&\fBt\fR are the exponent and coefficient for CRT calculations.
.PP
The \fBn\fR, \fBe\fR and \fBd\fR parameters can be obtained by calling
\&\fBRSA_get0_key()\fR.  If they have not been set yet, then \fB*n\fR, \fB*e\fR and
\&\fB*d\fR will be set to NULL.  Otherwise, they are set to pointers to
their respective values. These point directly to the internal
representations of the values and therefore should not be freed
by the caller.
.PP
The \fBn\fR, \fBe\fR and \fBd\fR parameter values can be set by calling
\&\fBRSA_set0_key()\fR and passing the new values for \fBn\fR, \fBe\fR and \fBd\fR as
parameters to the function.  The values \fBn\fR and \fBe\fR must be non-NULL
the first time this function is called on a given RSA object. The
value \fBd\fR may be NULL. On subsequent calls any of these values may be
NULL which means the corresponding RSA field is left untouched.
Calling this function transfers the memory management of the values to
the RSA object, and therefore the values that have been passed in
should not be freed by the caller after this function has been called.
.PP
In a similar fashion, the \fBp\fR and \fBq\fR parameters can be obtained and
set with \fBRSA_get0_factors()\fR and \fBRSA_set0_factors()\fR, and the \fBdmp1\fR,
\&\fBdmq1\fR and \fBiqmp\fR parameters can be obtained and set with
\&\fBRSA_get0_crt_params()\fR and \fBRSA_set0_crt_params()\fR.
.PP
For \fBRSA_get0_key()\fR, \fBRSA_get0_factors()\fR, and \fBRSA_get0_crt_params()\fR,
NULL value BIGNUM ** output parameters are permitted. The functions
ignore NULL parameters but return values for other, non-NULL, parameters.
.PP
For multi-prime RSA, \fBRSA_get0_multi_prime_factors()\fR and \fBRSA_get0_multi_prime_params()\fR
can be used to obtain other primes and related CRT parameters. The
return values are stored in an array of \fBBIGNUM *\fR. \fBRSA_set0_multi_prime_params()\fR
sets a collect of multi-prime 'triplet' members (prime, exponent and coefficient)
into an RSA object.
.PP
Any of the values \fBn\fR, \fBe\fR, \fBd\fR, \fBp\fR, \fBq\fR, \fBdmp1\fR, \fBdmq1\fR, and \fBiqmp\fR can also be
retrieved separately by the corresponding function
\&\fBRSA_get0_n()\fR, \fBRSA_get0_e()\fR, \fBRSA_get0_d()\fR, \fBRSA_get0_p()\fR, \fBRSA_get0_q()\fR,
\&\fBRSA_get0_dmp1()\fR, \fBRSA_get0_dmq1()\fR, and \fBRSA_get0_iqmp()\fR, respectively.
.PP
\&\fBRSA_get0_pss_params()\fR is used to retrieve the RSA-PSS parameters.
.PP
\&\fBRSA_set_flags()\fR sets the flags in the \fBflags\fR parameter on the RSA
object. Multiple flags can be passed in one go (bitwise ORed together).
Any flags that are already set are left set. \fBRSA_test_flags()\fR tests to
see whether the flags passed in the \fBflags\fR parameter are currently
set in the RSA object. Multiple flags can be tested in one go. All
flags that are currently set are returned, or zero if none of the
flags are set. \fBRSA_clear_flags()\fR clears the specified flags within the
RSA object.
.PP
\&\fBRSA_get0_engine()\fR returns a handle to the ENGINE that has been set for
this RSA object, or NULL if no such ENGINE has been set.
.PP
\&\fBRSA_get_version()\fR returns the version of an RSA object \fBr\fR.
.SH NOTES
.IX Header "NOTES"
Values retrieved with \fBRSA_get0_key()\fR are owned by the RSA object used
in the call and may therefore \fInot\fR be passed to \fBRSA_set0_key()\fR.  If
needed, duplicate the received value using \fBBN_dup()\fR and pass the
duplicate.  The same applies to \fBRSA_get0_factors()\fR and \fBRSA_set0_factors()\fR
as well as \fBRSA_get0_crt_params()\fR and \fBRSA_set0_crt_params()\fR.
.PP
The caller should obtain the size by calling \fBRSA_get_multi_prime_extra_count()\fR
in advance and allocate sufficient buffer to store the return values before
calling \fBRSA_get0_multi_prime_factors()\fR and \fBRSA_get0_multi_prime_params()\fR.
.PP
\&\fBRSA_set0_multi_prime_params()\fR always clears the original multi-prime
triplets in RSA object \fBr\fR and assign the new set of triplets into it.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBRSA_set0_key()\fR, \fBRSA_set0_factors()\fR, \fBRSA_set0_crt_params()\fR and
\&\fBRSA_set0_multi_prime_params()\fR return 1 on success or 0 on failure.
.PP
\&\fBRSA_get0_n()\fR, \fBRSA_get0_e()\fR, \fBRSA_get0_d()\fR, \fBRSA_get0_p()\fR, \fBRSA_get0_q()\fR,
\&\fBRSA_get0_dmp1()\fR, \fBRSA_get0_dmq1()\fR, and \fBRSA_get0_iqmp()\fR
return the respective value.
.PP
\&\fBRSA_get0_pss_params()\fR returns a \fBRSA_PSS_PARAMS\fR pointer, or NULL if
there is none.
.PP
\&\fBRSA_get0_multi_prime_factors()\fR and \fBRSA_get0_multi_prime_crt_params()\fR return
1 on success or 0 on failure.
.PP
\&\fBRSA_get_multi_prime_extra_count()\fR returns two less than the number of primes
in use, which is 0 for traditional RSA and the number of extra primes for
multi-prime RSA.
.PP
\&\fBRSA_get_version()\fR returns \fBRSA_ASN1_VERSION_MULTI\fR for multi-prime RSA and
\&\fBRSA_ASN1_VERSION_DEFAULT\fR for normal two-prime RSA, as defined in RFC 8017.
.PP
\&\fBRSA_test_flags()\fR returns the current state of the flags in the RSA object.
.PP
\&\fBRSA_get0_engine()\fR returns the ENGINE set for the RSA object or NULL if no
ENGINE has been set.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBRSA_new\fR\|(3), \fBRSA_size\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
The \fBRSA_get0_pss_params()\fR function was added in OpenSSL 1.1.1e.
.PP
The
\&\fBRSA_get_multi_prime_extra_count()\fR, \fBRSA_get0_multi_prime_factors()\fR,
\&\fBRSA_get0_multi_prime_crt_params()\fR, \fBRSA_set0_multi_prime_params()\fR,
and \fBRSA_get_version()\fR functions were added in OpenSSL 1.1.1.
.PP
Other functions described here were added in OpenSSL 1.1.0.
.PP
All of these functions were deprecated in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2016\-2023 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
