.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "OSSL_ALGORITHM 3ossl"
.TH OSSL_ALGORITHM 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
OSSL_ALGORITHM \- OpenSSL Core type to define a fetchable algorithm
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/core.h>
\&
\& typedef struct ossl_algorithm_st OSSL_ALGORITHM;
\& struct ossl_algorithm_st {
\&     const char *algorithm_names;     /* key */
\&     const char *property_definition; /* key */
\&     const OSSL_DISPATCH *implementation;
\&     const char *algorithm_description;
\& };
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
The \fBOSSL_ALGORITHM\fR type is a \fIpublic structure\fR that describes an
algorithm that a \fBprovider\fR\|(7) provides.  Arrays of this type are returned
by providers on demand from the OpenSSL libraries to describe what
algorithms the providers provide implementations of, and with what
properties.
.PP
Arrays of this type must be terminated with a tuple where \fIalgorithm_names\fR
is NULL.
.PP
This type of array is typically returned by the provider's operation querying
function, further described in "Provider Functions" in \fBprovider\-base\fR\|(7).
.SS "\fBOSSL_ALGORITHM\fP fields"
.IX Subsection "OSSL_ALGORITHM fields"
.IP \fIalgorithm_names\fR 4
.IX Item "algorithm_names"
This string is a colon separated set of names / identities, and is used by
the appropriate fetching functionality (such as \fBEVP_CIPHER_fetch\fR\|(3),
\&\fBEVP_MD_fetch\fR\|(3), etc) to find the desired algorithm.
.Sp
Multiple names / identities allow a specific algorithm implementation to be
fetched multiple ways.  For example, the RSA algorithm has the following
known identities:
.RS 4
.IP \(bu 4
\&\f(CW\*(C`RSA\*(C'\fR
.IP \(bu 4
\&\f(CW\*(C`rsaEncryption\*(C'\fR
.Sp
This is the name of the algorithm's OBJECT IDENTIFIER (OID), as given by the
PKCS#1 RFC's ASN.1 module <https://www.rfc-editor.org/rfc/rfc8017#appendix-C>
.IP \(bu 4
\&\f(CW1.2.840.113549.1.1.1\fR
.Sp
This is the OID itself for \f(CW\*(C`rsaEncryption\*(C'\fR, in canonical decimal text form.
.RE
.RS 4
.Sp
The resulting \fIalgorithm_names\fR string would look like this:
.Sp
.Vb 1
\& "RSA:rsaEncryption:1.2.840.113549.1.1.1"
.Ve
.Sp
The OpenSSL libraries use the first of the algorithm names as the main
or canonical name, on a per algorithm implementation basis.
.Sp
See the notes "On the subject of algorithm names" below for a more in
depth discussion on \fIalgorithm_names\fR and how that may interact with
applications and libraries, including OpenSSL's.
.RE
.IP \fIproperty_definition\fR 4
.IX Item "property_definition"
This string defines a set of properties associated with a particular
algorithm implementation, and is used by the appropriate fetching
functionality (such as \fBEVP_CIPHER_fetch\fR\|(3), \fBEVP_MD_fetch\fR\|(3), etc) for
a finer grained lookup of an algorithm implementation, which is useful in
case multiple implementations of the same algorithm are available.
.Sp
See \fBproperty\fR\|(7) for a further description of the contents of this
string.
.IP \fIimplementation\fR 4
.IX Item "implementation"
Pointer to an \fBOSSL_DISPATCH\fR\|(3) array, containing pointers to the
functions of a particular algorithm implementation.
.IP \fIalgorithm_description\fR 4
.IX Item "algorithm_description"
A string with a short human-readable description of the algorithm.
.SH NOTES
.IX Header "NOTES"
.SS "On the subject of algorithm names"
.IX Subsection "On the subject of algorithm names"
Providers may find the need to register ASN.1 OIDs for algorithms using
\&\fBOBJ_create\fR\|(3) (via the \fBcore_obj_create\fR upcall described in
\&\fBprovider\-base\fR\|(7), because some application or library \-\- possibly still
the OpenSSL libraries, even \-\- use NIDs to look up algorithms.
.PP
In that scenario, you must make sure that the corresponding \fBOSSL_ALGORITHM\fR's
\&\fIalgorithm_names\fR includes both the short and the long name.
.PP
Most of the time, registering ASN.1 OIDs like this shouldn't be necessary,
and applications and libraries are encouraged to use \fBOBJ_obj2txt\fR\|(3) to
get a text representation of the OID, which may be a long or short name for
OIDs that are registered, or the OID itself in canonical decimal text form
if not (or if \fBOBJ_obj2txt\fR\|(3) is called with \fIno_name\fR = 1).
.PP
It's recommended to make sure that the corresponding \fBOSSL_ALGORITHM\fR's
\&\fIalgorithm_names\fR include known names as well as the OID itself in
canonical decimal text form.  That should cover all scenarios.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBcrypto\fR\|(7), \fBprovider\-base\fR\|(7), \fBopenssl\-core.h\fR\|(7),
\&\fBopenssl\-core_dispatch.h\fR\|(7), \fBOSSL_DISPATCH\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
\&\fBOSSL_ALGORITHM\fR was added in OpenSSL 3.0
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2022 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
