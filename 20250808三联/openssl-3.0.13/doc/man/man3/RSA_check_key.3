.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "RSA_CHECK_KEY 3ossl"
.TH RSA_CHECK_KEY 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
RSA_check_key_ex, RSA_check_key \- validate private RSA keys
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/rsa.h>
.Ve
.PP
The following functions have been deprecated since OpenSSL 3.0, and can be
hidden entirely by defining \fBOPENSSL_API_COMPAT\fR with a suitable version value,
see \fBopenssl_user_macros\fR\|(7):
.PP
.Vb 1
\& int RSA_check_key_ex(const RSA *rsa, BN_GENCB *cb);
\&
\& int RSA_check_key(const RSA *rsa);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
Both of the functions described on this page are deprecated.
Applications should instead use \fBEVP_PKEY_public_check\fR\|(3),
\&\fBEVP_PKEY_private_check\fR\|(3) and \fBEVP_PKEY_pairwise_check\fR\|(3).
.PP
\&\fBRSA_check_key_ex()\fR function validates RSA keys.
It checks that \fBp\fR and \fBq\fR are
in fact prime, and that \fBn = p*q\fR.
.PP
It does not work on RSA public keys that have only the modulus
and public exponent elements populated.
It also checks that \fBd*e = 1 mod (p\-1*q\-1)\fR,
and that \fBdmp1\fR, \fBdmq1\fR and \fBiqmp\fR are set correctly or are \fBNULL\fR.
It performs integrity checks on all
the RSA key material, so the RSA key structure must contain all the private
key data too.
Therefore, it cannot be used with any arbitrary RSA key object,
even if it is otherwise fit for regular RSA operation.
.PP
The \fBcb\fR parameter is a callback that will be invoked in the same
manner as \fBBN_is_prime_ex\fR\|(3).
.PP
\&\fBRSA_check_key()\fR is equivalent to \fBRSA_check_key_ex()\fR with a NULL \fBcb\fR.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBRSA_check_key_ex()\fR and \fBRSA_check_key()\fR
return 1 if \fBrsa\fR is a valid RSA key, and 0 otherwise.
They return \-1 if an error occurs while checking the key.
.PP
If the key is invalid or an error occurred, the reason code can be
obtained using \fBERR_get_error\fR\|(3).
.SH NOTES
.IX Header "NOTES"
Unlike most other RSA functions, this function does \fBnot\fR work
transparently with any underlying ENGINE implementation because it uses the
key data in the RSA structure directly. An ENGINE implementation can
override the way key data is stored and handled, and can even provide
support for HSM keys \- in which case the RSA structure may contain \fBno\fR
key data at all! If the ENGINE in question is only being used for
acceleration or analysis purposes, then in all likelihood the RSA key data
is complete and untouched, but this can't be assumed in the general case.
.SH BUGS
.IX Header "BUGS"
A method of verifying the RSA key using opaque RSA API functions might need
to be considered. Right now \fBRSA_check_key()\fR simply uses the RSA structure
elements directly, bypassing the RSA_METHOD table altogether (and
completely violating encapsulation and object-orientation in the process).
The best fix will probably be to introduce a "\fBcheck_key()\fR" handler to the
RSA_METHOD function table so that alternative implementations can also
provide their own verifiers.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBBN_is_prime_ex\fR\|(3),
\&\fBERR_get_error\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
All of these functions were deprecated in OpenSSL 3.0.
.PP
\&\fBRSA_check_key_ex()\fR appeared after OpenSSL 1.0.2.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
