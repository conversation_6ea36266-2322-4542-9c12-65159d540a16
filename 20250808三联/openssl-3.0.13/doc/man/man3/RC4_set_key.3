.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "RC4_SET_KEY 3ossl"
.TH RC4_SET_KEY 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
RC4_set_key, RC4 \- RC4 encryption
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/rc4.h>
.Ve
.PP
The following functions have been deprecated since OpenSSL 3.0, and can be
hidden entirely by defining \fBOPENSSL_API_COMPAT\fR with a suitable version value,
see \fBopenssl_user_macros\fR\|(7):
.PP
.Vb 1
\& void RC4_set_key(RC4_KEY *key, int len, const unsigned char *data);
\&
\& void RC4(RC4_KEY *key, unsigned long len, const unsigned char *indata,
\&          unsigned char *outdata);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
All of the functions described on this page are deprecated. Applications should
instead use \fBEVP_EncryptInit_ex\fR\|(3), \fBEVP_EncryptUpdate\fR\|(3) and
\&\fBEVP_EncryptFinal_ex\fR\|(3) or the equivalently named decrypt functions.
.PP
This library implements the Alleged RC4 cipher, which is described for
example in \fIApplied Cryptography\fR.  It is believed to be compatible
with RC4[TM], a proprietary cipher of RSA Security Inc.
.PP
RC4 is a stream cipher with variable key length.  Typically, 128 bit
(16 byte) keys are used for strong encryption, but shorter insecure
key sizes have been widely used due to export restrictions.
.PP
RC4 consists of a key setup phase and the actual encryption or
decryption phase.
.PP
\&\fBRC4_set_key()\fR sets up the \fBRC4_KEY\fR \fBkey\fR using the \fBlen\fR bytes long
key at \fBdata\fR.
.PP
\&\fBRC4()\fR encrypts or decrypts the \fBlen\fR bytes of data at \fBindata\fR using
\&\fBkey\fR and places the result at \fBoutdata\fR.  Repeated \fBRC4()\fR calls with
the same \fBkey\fR yield a continuous key stream.
.PP
Since RC4 is a stream cipher (the input is XORed with a pseudo-random
key stream to produce the output), decryption uses the same function
calls as encryption.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBRC4_set_key()\fR and \fBRC4()\fR do not return values.
.SH NOTE
.IX Header "NOTE"
Applications should use the higher level functions
\&\fBEVP_EncryptInit\fR\|(3) etc. instead of calling these
functions directly.
.PP
It is difficult to securely use stream ciphers. For example, do not perform
multiple encryptions using the same key stream.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBEVP_EncryptInit\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
All of these functions were deprecated in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
