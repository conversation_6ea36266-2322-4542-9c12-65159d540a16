.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "BIO_NEW 3ossl"
.TH BIO_NEW 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
BIO_new_ex, BIO_new, BIO_up_ref, BIO_free, BIO_vfree, BIO_free_all
\&\- BIO allocation and freeing functions
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/bio.h>
\&
\& BIO *BIO_new_ex(OSSL_LIB_CTX *libctx, const BIO_METHOD *type);
\& BIO *BIO_new(const BIO_METHOD *type);
\& int BIO_up_ref(BIO *a);
\& int BIO_free(BIO *a);
\& void BIO_vfree(BIO *a);
\& void BIO_free_all(BIO *a);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
The \fBBIO_new_ex()\fR function returns a new BIO using method \fBtype\fR associated with
the library context \fIlibctx\fR (see \fBOSSL_LIB_CTX\fR\|(3)). The library context may be
NULL to indicate the default library context.
.PP
The \fBBIO_new()\fR is the same as \fBBIO_new_ex()\fR except the default library context is
always used.
.PP
\&\fBBIO_up_ref()\fR increments the reference count associated with the BIO object.
.PP
\&\fBBIO_free()\fR frees up a single BIO, \fBBIO_vfree()\fR also frees up a single BIO
but it does not return a value.
If \fBa\fR is NULL nothing is done.
Calling \fBBIO_free()\fR may also have some effect
on the underlying I/O structure, for example it may close the file being
referred to under certain circumstances. For more details see the individual
BIO_METHOD descriptions.
.PP
\&\fBBIO_free_all()\fR frees up an entire BIO chain, it does not halt if an error
occurs freeing up an individual BIO in the chain.
If \fBa\fR is NULL nothing is done.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBBIO_new_ex()\fR and \fBBIO_new()\fR return a newly created BIO or NULL if the call fails.
.PP
\&\fBBIO_up_ref()\fR and \fBBIO_free()\fR return 1 for success and 0 for failure.
.PP
\&\fBBIO_free_all()\fR and \fBBIO_vfree()\fR do not return values.
.SH NOTES
.IX Header "NOTES"
If \fBBIO_free()\fR is called on a BIO chain it will only free one BIO resulting
in a memory leak.
.PP
Calling \fBBIO_free_all()\fR on a single BIO has the same effect as calling \fBBIO_free()\fR
on it other than the discarded return value.
.SH HISTORY
.IX Header "HISTORY"
\&\fBBIO_set()\fR was removed in OpenSSL 1.1.0 as BIO type is now opaque.
.PP
\&\fBBIO_new_ex()\fR was added in OpenSSL 3.0.
.SH EXAMPLES
.IX Header "EXAMPLES"
Create a memory BIO:
.PP
.Vb 1
\& BIO *mem = BIO_new(BIO_s_mem());
.Ve
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
