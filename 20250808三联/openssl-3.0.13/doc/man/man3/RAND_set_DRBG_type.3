.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "RAND_SET_DRBG_TYPE 3ossl"
.TH RAND_SET_DRBG_TYPE 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
RAND_set_DRBG_type,
RAND_set_seed_source_type
\&\- specify the global random number generator types
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/rand.h>
\&
\& int RAND_set_DRBG_type(OSSL_LIB_CTX *ctx, const char *drbg, const char *propq,
\&                        const char *cipher, const char *digest);
\& int RAND_set_seed_source_type(OSSL_LIB_CTX *ctx, const char *seed,
\&                               const char *propq);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBRAND_set_DRBG_type()\fR specifies the random bit generator that will be
used within the library context \fIctx\fR.  A generator of name \fIdrbg\fR
with properties \fIpropq\fR will be fetched.  It will be instantiated with
either \fIcipher\fR or \fIdigest\fR as its underlying cryptographic algorithm.
This specifies the type that will be used for the primary, public and
private random instances.
.PP
\&\fBRAND_set_seed_source_type()\fR specifies the seed source that will be used
within the library context \fIctx\fR.  The seed source of name \fIseed\fR
with properties \fIpropq\fR will be fetched and used to seed the primary
random big generator.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
These function return 1 on success and 0 on failure.
.SH NOTES
.IX Header "NOTES"
These functions must be called before the random bit generators are first
created in the library context.  They will return an error if the call
is made too late.
.PP
The default DRBG is "CTR-DRBG" using the "AES\-256\-CTR" cipher.
.PP
The default seed source is "SEED-SRC".
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBEVP_RAND\fR\|(3),
\&\fBRAND_get0_primary\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
These functions were added in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
