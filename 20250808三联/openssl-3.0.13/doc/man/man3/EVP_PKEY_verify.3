.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "EVP_PKEY_VERIFY 3ossl"
.TH EVP_PKEY_VERIFY 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
EVP_PKEY_verify_init, EVP_PKEY_verify_init_ex, EVP_PKEY_verify
\&\- signature verification using a public key algorithm
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/evp.h>
\&
\& int EVP_PKEY_verify_init(EVP_PKEY_CTX *ctx);
\& int EVP_PKEY_verify_init_ex(EVP_PKEY_CTX *ctx, const OSSL_PARAM params[]);
\& int EVP_PKEY_verify(EVP_PKEY_CTX *ctx,
\&                     const unsigned char *sig, size_t siglen,
\&                     const unsigned char *tbs, size_t tbslen);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBEVP_PKEY_verify_init()\fR initializes a public key algorithm context \fIctx\fR for
signing using the algorithm given when the context was created
using \fBEVP_PKEY_CTX_new\fR\|(3) or variants thereof.  The algorithm is used to
fetch a \fBEVP_SIGNATURE\fR method implicitly, see "Implicit fetch" in \fBprovider\fR\|(7)
for more information about implicit fetches.
.PP
\&\fBEVP_PKEY_verify_init_ex()\fR is the same as \fBEVP_PKEY_verify_init()\fR but additionally
sets the passed parameters \fIparams\fR on the context before returning.
.PP
The \fBEVP_PKEY_verify()\fR function performs a public key verification operation
using \fIctx\fR. The signature is specified using the \fIsig\fR and
\&\fIsiglen\fR parameters. The verified data (i.e. the data believed originally
signed) is specified using the \fItbs\fR and \fItbslen\fR parameters.
.SH NOTES
.IX Header "NOTES"
After the call to \fBEVP_PKEY_verify_init()\fR algorithm specific control
operations can be performed to set any appropriate parameters for the
operation.
.PP
The function \fBEVP_PKEY_verify()\fR can be called more than once on the same
context if several operations are performed using the same parameters.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBEVP_PKEY_verify_init()\fR and \fBEVP_PKEY_verify()\fR return 1 if the verification was
successful and 0 if it failed. Unlike other functions the return value 0 from
\&\fBEVP_PKEY_verify()\fR only indicates that the signature did not verify
successfully (that is tbs did not match the original data or the signature was
of invalid form) it is not an indication of a more serious error.
.PP
A negative value indicates an error other that signature verification failure.
In particular a return value of \-2 indicates the operation is not supported by
the public key algorithm.
.SH EXAMPLES
.IX Header "EXAMPLES"
Verify signature using PKCS#1 and SHA256 digest:
.PP
.Vb 2
\& #include <openssl/evp.h>
\& #include <openssl/rsa.h>
\&
\& EVP_PKEY_CTX *ctx;
\& unsigned char *md, *sig;
\& size_t mdlen, siglen;
\& EVP_PKEY *verify_key;
\&
\& /*
\&  * NB: assumes verify_key, sig, siglen md and mdlen are already set up
\&  * and that verify_key is an RSA public key
\&  */
\& ctx = EVP_PKEY_CTX_new(verify_key, NULL /* no engine */);
\& if (!ctx)
\&     /* Error occurred */
\& if (EVP_PKEY_verify_init(ctx) <= 0)
\&     /* Error */
\& if (EVP_PKEY_CTX_set_rsa_padding(ctx, RSA_PKCS1_PADDING) <= 0)
\&     /* Error */
\& if (EVP_PKEY_CTX_set_signature_md(ctx, EVP_sha256()) <= 0)
\&     /* Error */
\&
\& /* Perform operation */
\& ret = EVP_PKEY_verify(ctx, sig, siglen, md, mdlen);
\&
\& /*
\&  * ret == 1 indicates success, 0 verify failure and < 0 for some
\&  * other error.
\&  */
.Ve
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBEVP_PKEY_CTX_new\fR\|(3),
\&\fBEVP_PKEY_encrypt\fR\|(3),
\&\fBEVP_PKEY_decrypt\fR\|(3),
\&\fBEVP_PKEY_sign\fR\|(3),
\&\fBEVP_PKEY_verify_recover\fR\|(3),
\&\fBEVP_PKEY_derive\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
The \fBEVP_PKEY_verify_init()\fR and \fBEVP_PKEY_verify()\fR functions were added in
OpenSSL 1.0.0.
.PP
The \fBEVP_PKEY_verify_init_ex()\fR function was added in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2006\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
