.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "CMS_DATA_CREATE 3ossl"
.TH CMS_DATA_CREATE 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
CMS_data_create_ex, CMS_data_create
\&\- Create CMS Data object
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/cms.h>
\&
\& CMS_ContentInfo *CMS_data_create_ex(BIO *in, unsigned int flags,
\&                                     OSSL_LIB_CTX *libctx, const char *propq);
\& CMS_ContentInfo *CMS_data_create(BIO *in, unsigned int flags);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBCMS_data_create_ex()\fR creates a \fBCMS_ContentInfo\fR structure
with a type \fBNID_pkcs7_data\fR. The data is supplied via the \fIin\fR BIO.
The library context \fIlibctx\fR and the property query \fIpropq\fR are used when
retrieving algorithms from providers. The \fIflags\fR field supports the
\&\fBCMS_STREAM\fR flag. Internally \fBCMS_final()\fR is called unless \fBCMS_STREAM\fR is
specified.
.PP
The \fBCMS_ContentInfo\fR structure can be freed using \fBCMS_ContentInfo_free\fR\|(3).
.PP
\&\fBCMS_data_create()\fR is similar to \fBCMS_data_create_ex()\fR
but uses default values of NULL for the library context \fIlibctx\fR and the
property query \fIpropq\fR.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
If the allocation fails, \fBCMS_data_create_ex()\fR and \fBCMS_data_create()\fR
return NULL and set an error code that can be obtained by \fBERR_get_error\fR\|(3).
Otherwise they return a pointer to the newly allocated structure.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBERR_get_error\fR\|(3), \fBCMS_final\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
The \fBCMS_data_create_ex()\fR method was added in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2020\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
