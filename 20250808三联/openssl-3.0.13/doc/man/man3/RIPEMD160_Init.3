.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "RIPEMD160_INIT 3ossl"
.TH RIPEMD160_INIT 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
RIPEMD160, RIPEMD160_Init, RIPEMD160_Update, RIPEMD160_Final \-
RIPEMD\-160 hash function
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/ripemd.h>
.Ve
.PP
The following functions have been deprecated since OpenSSL 3.0, and can be
hidden entirely by defining \fBOPENSSL_API_COMPAT\fR with a suitable version value,
see \fBopenssl_user_macros\fR\|(7):
.PP
.Vb 2
\& unsigned char *RIPEMD160(const unsigned char *d, unsigned long n,
\&                          unsigned char *md);
\&
\& int RIPEMD160_Init(RIPEMD160_CTX *c);
\& int RIPEMD160_Update(RIPEMD160_CTX *c, const void *data, unsigned long len);
\& int RIPEMD160_Final(unsigned char *md, RIPEMD160_CTX *c);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
All of the functions described on this page are deprecated.
Applications should instead use \fBEVP_DigestInit_ex\fR\|(3), \fBEVP_DigestUpdate\fR\|(3)
and \fBEVP_DigestFinal_ex\fR\|(3).
.PP
RIPEMD\-160 is a cryptographic hash function with a
160 bit output.
.PP
\&\fBRIPEMD160()\fR computes the RIPEMD\-160 message digest of the \fBn\fR
bytes at \fBd\fR and places it in \fBmd\fR (which must have space for
RIPEMD160_DIGEST_LENGTH == 20 bytes of output). If \fBmd\fR is NULL, the digest
is placed in a static array.
.PP
The following functions may be used if the message is not completely
stored in memory:
.PP
\&\fBRIPEMD160_Init()\fR initializes a \fBRIPEMD160_CTX\fR structure.
.PP
\&\fBRIPEMD160_Update()\fR can be called repeatedly with chunks of the message to
be hashed (\fBlen\fR bytes at \fBdata\fR).
.PP
\&\fBRIPEMD160_Final()\fR places the message digest in \fBmd\fR, which must have
space for RIPEMD160_DIGEST_LENGTH == 20 bytes of output, and erases
the \fBRIPEMD160_CTX\fR.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBRIPEMD160()\fR returns a pointer to the hash value.
.PP
\&\fBRIPEMD160_Init()\fR, \fBRIPEMD160_Update()\fR and \fBRIPEMD160_Final()\fR return 1 for
success, 0 otherwise.
.SH NOTE
.IX Header "NOTE"
Applications should use the higher level functions
\&\fBEVP_DigestInit\fR\|(3) etc. instead of calling these
functions directly.
.SH "CONFORMING TO"
.IX Header "CONFORMING TO"
ISO/IEC 10118\-3:2016 Dedicated Hash-Function 1 (RIPEMD\-160).
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBEVP_DigestInit\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
All of these functions were deprecated in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
