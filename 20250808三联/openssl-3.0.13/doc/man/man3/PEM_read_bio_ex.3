.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "PEM_READ_BIO_EX 3ossl"
.TH PEM_READ_BIO_EX 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
PEM_read_bio_ex, PEM_FLAG_SECURE, PEM_FLAG_EAY_COMPATIBLE,
PEM_FLAG_ONLY_B64 \- read PEM format files with custom processing
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/pem.h>
\&
\& #define PEM_FLAG_SECURE             0x1
\& #define PEM_FLAG_EAY_COMPATIBLE     0x2
\& #define PEM_FLAG_ONLY_B64           0x4
\& int PEM_read_bio_ex(BIO *in, char **name, char **header,
\&                     unsigned char **data, long *len, unsigned int flags);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBPEM_read_bio_ex()\fR reads in PEM formatted data from an input BIO, outputting
the name of the type of contained data, the header information regarding
the possibly encrypted data, and the binary data payload (after base64 decoding).
It should generally only be used to implement PEM_read_bio_\-family functions
for specific data types or other usage, but is exposed to allow greater flexibility
over how processing is performed, if needed.
.PP
If PEM_FLAG_SECURE is set, the intermediate buffers used to read in lines of
input are allocated from the secure heap.
.PP
If PEM_FLAG_EAY_COMPATIBLE is set, a simple algorithm is used to remove whitespace
and control characters from the end of each line, so as to be compatible with
the historical behavior of \fBPEM_read_bio()\fR.
.PP
If PEM_FLAG_ONLY_B64 is set, all characters are required to be valid base64
characters (or newlines); non\-base64 characters are treated as end of input.
.PP
If neither PEM_FLAG_EAY_COMPATIBLE or PEM_FLAG_ONLY_B64 is set, control characters
are ignored.
.PP
If both PEM_FLAG_EAY_COMPATIBLE and PEM_FLAG_ONLY_B64 are set, an error is returned;
these options are not compatible with each other.
.SH NOTES
.IX Header "NOTES"
The caller must release the storage allocated for *name, *header, and *data.
If PEM_FLAG_SECURE was set, use \fBOPENSSL_secure_free()\fR; otherwise,
\&\fBOPENSSL_free()\fR is used.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBPEM_read_bio_ex()\fR returns 1 for success or 0 for failure.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBPEM_bytes_read_bio\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
The \fBPEM_read_bio_ex()\fR function was added in OpenSSL 1.1.1.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2017 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
