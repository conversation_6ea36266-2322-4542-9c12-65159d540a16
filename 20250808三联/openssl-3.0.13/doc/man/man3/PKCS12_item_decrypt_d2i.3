.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "PKCS12_ITEM_DECRYPT_D2I 3ossl"
.TH PKCS12_ITEM_DECRYPT_D2I 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
PKCS12_item_decrypt_d2i, PKCS12_item_decrypt_d2i_ex,
PKCS12_item_i2d_encrypt, PKCS12_item_i2d_encrypt_ex \- PKCS12 item
encrypt/decrypt functions
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/pkcs12.h>
\&
\& void *PKCS12_item_decrypt_d2i(const X509_ALGOR *algor, const ASN1_ITEM *it,
\&                               const char *pass, int passlen,
\&                               const ASN1_OCTET_STRING *oct, int zbuf);
\& void *PKCS12_item_decrypt_d2i_ex(const X509_ALGOR *algor, const ASN1_ITEM *it,
\&                                  const char *pass, int passlen,
\&                                  const ASN1_OCTET_STRING *oct, int zbuf,
\&                                  OSSL_LIB_CTX *libctx,
\&                                  const char *propq);
\& ASN1_OCTET_STRING *PKCS12_item_i2d_encrypt(X509_ALGOR *algor,
\&                                            const ASN1_ITEM *it,
\&                                            const char *pass, int passlen,
\&                                            void *obj, int zbuf);
\& ASN1_OCTET_STRING *PKCS12_item_i2d_encrypt_ex(X509_ALGOR *algor,
\&                                               const ASN1_ITEM *it,
\&                                               const char *pass, int passlen,
\&                                               void *obj, int zbuf,
\&                                               OSSL_LIB_CTX *ctx,
\&                                               const char *propq);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBPKCS12_item_decrypt_d2i()\fR and \fBPKCS12_item_decrypt_d2i_ex()\fR decrypt an octet
string containing an ASN.1 encoded object using the algorithm \fIalgor\fR and
password \fIpass\fR of length \fIpasslen\fR. If \fIzbuf\fR is nonzero then the output
buffer will zeroed after the decrypt.
.PP
\&\fBPKCS12_item_i2d_encrypt()\fR and \fBPKCS12_item_i2d_encrypt_ex()\fR encrypt an ASN.1
object \fIit\fR using the algorithm \fIalgor\fR and password \fIpass\fR of length
\&\fIpasslen\fR, returning an encoded object in \fIobj\fR. If \fIzbuf\fR is nonzero then
the buffer containing the input encoding will be zeroed after the encrypt.
.PP
Functions ending in \fB_ex()\fR allow for a library context \fIctx\fR and property query
\&\fIpropq\fR to be used to select algorithm implementations.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBPKCS12_item_decrypt_d2i()\fR and \fBPKCS12_item_decrypt_d2i_ex()\fR return the decrypted
object or NULL if an error occurred.
.PP
\&\fBPKCS12_item_i2d_encrypt()\fR and \fBPKCS12_item_i2d_encrypt_ex()\fR return the encrypted
data as an ASN.1 Octet String or NULL if an error occurred.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBPKCS12_pbe_crypt_ex\fR\|(3),
\&\fBPKCS8_encrypt_ex\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
\&\fBPKCS12_item_decrypt_d2i_ex()\fR and \fBPKCS12_item_i2d_encrypt_ex()\fR were added in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
