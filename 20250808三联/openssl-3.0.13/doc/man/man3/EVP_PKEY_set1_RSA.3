.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "EVP_PKEY_SET1_RSA 3ossl"
.TH EVP_PKEY_SET1_RSA 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
EVP_PKEY_set1_RSA, EVP_PKEY_set1_DSA, EVP_PKEY_set1_DH, EVP_PKEY_set1_EC_KEY,
EVP_PKEY_get1_RSA, EVP_PKEY_get1_DSA, EVP_PKEY_get1_DH, EVP_PKEY_get1_EC_KEY,
EVP_PKEY_get0_RSA, EVP_PKEY_get0_DSA, EVP_PKEY_get0_DH, EVP_PKEY_get0_EC_KEY,
EVP_PKEY_assign_RSA, EVP_PKEY_assign_DSA, EVP_PKEY_assign_DH,
EVP_PKEY_assign_EC_KEY, EVP_PKEY_assign_POLY1305, EVP_PKEY_assign_SIPHASH,
EVP_PKEY_get0_hmac, EVP_PKEY_get0_poly1305, EVP_PKEY_get0_siphash,
EVP_PKEY_get0, EVP_PKEY_type, EVP_PKEY_get_id, EVP_PKEY_get_base_id,
EVP_PKEY_set1_engine, EVP_PKEY_get0_engine,
EVP_PKEY_id, EVP_PKEY_base_id \-
EVP_PKEY assignment functions
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/evp.h>
\&
\& int EVP_PKEY_get_id(const EVP_PKEY *pkey);
\& int EVP_PKEY_get_base_id(const EVP_PKEY *pkey);
\& int EVP_PKEY_type(int type);
\&
\& #define EVP_PKEY_id EVP_PKEY_get_id
\& #define EVP_PKEY_base_id EVP_PKEY_get_base_id
.Ve
.PP
The following functions have been deprecated since OpenSSL 3.0, and can be
hidden entirely by defining \fBOPENSSL_API_COMPAT\fR with a suitable version value,
see \fBopenssl_user_macros\fR\|(7):
.PP
.Vb 4
\& int EVP_PKEY_set1_RSA(EVP_PKEY *pkey, RSA *key);
\& int EVP_PKEY_set1_DSA(EVP_PKEY *pkey, DSA *key);
\& int EVP_PKEY_set1_DH(EVP_PKEY *pkey, DH *key);
\& int EVP_PKEY_set1_EC_KEY(EVP_PKEY *pkey, EC_KEY *key);
\&
\& RSA *EVP_PKEY_get1_RSA(EVP_PKEY *pkey);
\& DSA *EVP_PKEY_get1_DSA(EVP_PKEY *pkey);
\& DH *EVP_PKEY_get1_DH(EVP_PKEY *pkey);
\& EC_KEY *EVP_PKEY_get1_EC_KEY(EVP_PKEY *pkey);
\&
\& const unsigned char *EVP_PKEY_get0_hmac(const EVP_PKEY *pkey, size_t *len);
\& const unsigned char *EVP_PKEY_get0_poly1305(const EVP_PKEY *pkey, size_t *len);
\& const unsigned char *EVP_PKEY_get0_siphash(const EVP_PKEY *pkey, size_t *len);
\& const RSA *EVP_PKEY_get0_RSA(const EVP_PKEY *pkey);
\& const DSA *EVP_PKEY_get0_DSA(const EVP_PKEY *pkey);
\& const DH *EVP_PKEY_get0_DH(const EVP_PKEY *pkey);
\& const EC_KEY *EVP_PKEY_get0_EC_KEY(const EVP_PKEY *pkey);
\& void *EVP_PKEY_get0(const EVP_PKEY *pkey);
\&
\& int EVP_PKEY_assign_RSA(EVP_PKEY *pkey, RSA *key);
\& int EVP_PKEY_assign_DSA(EVP_PKEY *pkey, DSA *key);
\& int EVP_PKEY_assign_DH(EVP_PKEY *pkey, DH *key);
\& int EVP_PKEY_assign_EC_KEY(EVP_PKEY *pkey, EC_KEY *key);
\& int EVP_PKEY_assign_POLY1305(EVP_PKEY *pkey, ASN1_OCTET_STRING *key);
\& int EVP_PKEY_assign_SIPHASH(EVP_PKEY *pkey, ASN1_OCTET_STRING *key);
\&
\& ENGINE *EVP_PKEY_get0_engine(const EVP_PKEY *pkey);
\& int EVP_PKEY_set1_engine(EVP_PKEY *pkey, ENGINE *engine);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBEVP_PKEY_get_base_id()\fR returns the type of \fIpkey\fR. For example
an RSA key will return \fBEVP_PKEY_RSA\fR.
.PP
\&\fBEVP_PKEY_get_id()\fR returns the actual NID associated with \fIpkey\fR
only if the \fIpkey\fR type isn't implemented just in a \fBprovider\fR\|(7).
Historically keys using the same algorithm could use different NIDs.
For example an RSA key could use the NIDs corresponding to
the NIDs \fBNID_rsaEncryption\fR (equivalent to \fBEVP_PKEY_RSA\fR) or
\&\fBNID_rsa\fR (equivalent to \fBEVP_PKEY_RSA2\fR). The use of
alternative non-standard NIDs is now rare so \fBEVP_PKEY_RSA2\fR et al are not
often seen in practice.
\&\fBEVP_PKEY_get_id()\fR returns \-1 (\fBEVP_PKEY_KEYMGMT\fR) if the \fIpkey\fR is
only implemented in a \fBprovider\fR\|(7).
.PP
\&\fBEVP_PKEY_type()\fR returns the underlying type of the NID \fItype\fR. For example
EVP_PKEY_type(EVP_PKEY_RSA2) will return \fBEVP_PKEY_RSA\fR.
.PP
\&\fBEVP_PKEY_set1_RSA()\fR, \fBEVP_PKEY_set1_DSA()\fR, \fBEVP_PKEY_set1_DH()\fR and
\&\fBEVP_PKEY_set1_EC_KEY()\fR set the key referenced by \fIpkey\fR to \fIkey\fR. These
functions are deprecated. Applications should instead use
\&\fBEVP_PKEY_fromdata\fR\|(3).
.PP
\&\fBEVP_PKEY_assign_RSA()\fR, \fBEVP_PKEY_assign_DSA()\fR, \fBEVP_PKEY_assign_DH()\fR,
\&\fBEVP_PKEY_assign_EC_KEY()\fR, \fBEVP_PKEY_assign_POLY1305()\fR and
\&\fBEVP_PKEY_assign_SIPHASH()\fR set the referenced key to \fIkey\fR however these use
the supplied \fIkey\fR internally and so \fIkey\fR will be freed when the parent
\&\fIpkey\fR is freed. These macros are deprecated. Applications should instead read
an EVP_PKEY directly using the OSSL_DECODER APIs (see
\&\fBOSSL_DECODER_CTX_new_for_pkey\fR\|(3)), or construct an EVP_PKEY from data using
\&\fBEVP_PKEY_fromdata\fR\|(3).
.PP
\&\fBEVP_PKEY_get1_RSA()\fR, \fBEVP_PKEY_get1_DSA()\fR, \fBEVP_PKEY_get1_DH()\fR and
\&\fBEVP_PKEY_get1_EC_KEY()\fR return the referenced key in \fIpkey\fR or NULL if the
key is not of the correct type. The returned key must be freed after use.
These functions are deprecated. Applications should instead use the EVP_PKEY
directly where possible. If access to the low level key parameters is required
then applications should use \fBEVP_PKEY_get_params\fR\|(3) and other similar
functions. To write an EVP_PKEY out use the OSSL_ENCODER APIs (see
\&\fBOSSL_ENCODER_CTX_new_for_pkey\fR\|(3)).
.PP
\&\fBEVP_PKEY_get0_hmac()\fR, \fBEVP_PKEY_get0_poly1305()\fR, \fBEVP_PKEY_get0_siphash()\fR,
\&\fBEVP_PKEY_get0_RSA()\fR, \fBEVP_PKEY_get0_DSA()\fR, \fBEVP_PKEY_get0_DH()\fR and
\&\fBEVP_PKEY_get0_EC_KEY()\fR return the referenced key in \fIpkey\fR or NULL if the
key is not of the correct type. The reference count of the returned key is
\&\fBnot\fR incremented and so the key must not be freed after use. These functions
are deprecated. Applications should instead use the EVP_PKEY directly where
possible. If access to the low level key parameters is required then
applications should use \fBEVP_PKEY_get_params\fR\|(3) and other similar functions.
To write an EVP_PKEY out use the OSSL_ENCODER APIs (see
\&\fBOSSL_ENCODER_CTX_new_for_pkey\fR\|(3)). \fBEVP_PKEY_get0()\fR returns a pointer to the
legacy key or NULL if the key is not legacy.
.PP
Note that if an EVP_PKEY was not constructed using one of the deprecated
functions such as \fBEVP_PKEY_set1_RSA()\fR, \fBEVP_PKEY_set1_DSA()\fR, \fBEVP_PKEY_set1_DH()\fR
or \fBEVP_PKEY_set1_EC_KEY()\fR, or via the similarly named \fBEVP_PKEY_assign\fR macros
described above then the internal key will be managed by a provider (see
\&\fBprovider\fR\|(7)). In that case the key returned by \fBEVP_PKEY_get1_RSA()\fR,
\&\fBEVP_PKEY_get1_DSA()\fR, \fBEVP_PKEY_get1_DH()\fR, \fBEVP_PKEY_get1_EC_KEY()\fR,
\&\fBEVP_PKEY_get0_hmac()\fR, \fBEVP_PKEY_get0_poly1305()\fR, \fBEVP_PKEY_get0_siphash()\fR,
\&\fBEVP_PKEY_get0_RSA()\fR, \fBEVP_PKEY_get0_DSA()\fR, \fBEVP_PKEY_get0_DH()\fR or
\&\fBEVP_PKEY_get0_EC_KEY()\fR will be a cached copy of the provider's key. Subsequent
updates to the provider's key will not be reflected back in the cached copy, and
updates made by an application to the returned key will not be reflected back in
the provider's key. Subsequent calls to \fBEVP_PKEY_get1_RSA()\fR,
\&\fBEVP_PKEY_get1_DSA()\fR, \fBEVP_PKEY_get1_DH()\fR and \fBEVP_PKEY_get1_EC_KEY()\fR will always
return the cached copy returned by the first call.
.PP
\&\fBEVP_PKEY_get0_engine()\fR returns a reference to the ENGINE handling \fIpkey\fR. This
function is deprecated. Applications should use providers instead of engines
(see \fBprovider\fR\|(7) for details).
.PP
\&\fBEVP_PKEY_set1_engine()\fR sets the ENGINE handling \fIpkey\fR to \fIengine\fR. It
must be called after the key algorithm and components are set up.
If \fIengine\fR does not include an \fBEVP_PKEY_METHOD\fR for \fIpkey\fR an
error occurs. This function is deprecated. Applications should use providers
instead of engines (see \fBprovider\fR\|(7) for details).
.SH WARNINGS
.IX Header "WARNINGS"
The following functions are only reliable with \fBEVP_PKEY\fRs that have
been assigned an internal key with EVP_PKEY_assign_*():
.PP
\&\fBEVP_PKEY_get_id()\fR, \fBEVP_PKEY_get_base_id()\fR, \fBEVP_PKEY_type()\fR
.PP
For EVP_PKEY key type checking purposes, \fBEVP_PKEY_is_a\fR\|(3) is more generic.
.PP
For purposes of retrieving the name of the \fBEVP_PKEY\fR the function
\&\fBEVP_PKEY_get0_type_name\fR\|(3) is more generally useful.
.PP
The keys returned from the functions \fBEVP_PKEY_get0_RSA()\fR, \fBEVP_PKEY_get0_DSA()\fR,
\&\fBEVP_PKEY_get0_DH()\fR and \fBEVP_PKEY_get0_EC_KEY()\fR were changed to have a "const"
return type in OpenSSL 3.0. As described above the keys returned may be cached
copies of the key held in a provider. Due to this, and unlike in earlier
versions of OpenSSL, they should be considered read-only copies of the key.
Updates to these keys will not be reflected back in the provider side key. The
\&\fBEVP_PKEY_get1_RSA()\fR, \fBEVP_PKEY_get1_DSA()\fR, \fBEVP_PKEY_get1_DH()\fR and
\&\fBEVP_PKEY_get1_EC_KEY()\fR functions were not changed to have a "const" return type
in order that applications can "free" the return value. However applications
should still consider them as read-only copies.
.SH NOTES
.IX Header "NOTES"
In accordance with the OpenSSL naming convention the key obtained
from or assigned to the \fIpkey\fR using the \fB1\fR functions must be
freed as well as \fIpkey\fR.
.PP
\&\fBEVP_PKEY_assign_RSA()\fR, \fBEVP_PKEY_assign_DSA()\fR, \fBEVP_PKEY_assign_DH()\fR,
\&\fBEVP_PKEY_assign_EC_KEY()\fR, \fBEVP_PKEY_assign_POLY1305()\fR
and \fBEVP_PKEY_assign_SIPHASH()\fR are implemented as macros.
.PP
\&\fBEVP_PKEY_assign_EC_KEY()\fR looks at the curve name id to determine if
the passed \fBEC_KEY\fR is an \fBSM2\fR\|(7) key, and will set the \fBEVP_PKEY\fR
type to \fBEVP_PKEY_SM2\fR in that case, instead of \fBEVP_PKEY_EC\fR.
.PP
Most applications wishing to know a key type will simply call
\&\fBEVP_PKEY_get_base_id()\fR and will not care about the actual type:
which will be identical in almost all cases.
.PP
Previous versions of this document suggested using EVP_PKEY_type(pkey\->type)
to determine the type of a key. Since \fBEVP_PKEY\fR is now opaque this
is no longer possible: the equivalent is EVP_PKEY_get_base_id(pkey).
.PP
\&\fBEVP_PKEY_set1_engine()\fR is typically used by an ENGINE returning an HSM
key as part of its routine to load a private key.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBEVP_PKEY_set1_RSA()\fR, \fBEVP_PKEY_set1_DSA()\fR, \fBEVP_PKEY_set1_DH()\fR and
\&\fBEVP_PKEY_set1_EC_KEY()\fR return 1 for success or 0 for failure.
.PP
\&\fBEVP_PKEY_get1_RSA()\fR, \fBEVP_PKEY_get1_DSA()\fR, \fBEVP_PKEY_get1_DH()\fR and
\&\fBEVP_PKEY_get1_EC_KEY()\fR return the referenced key or NULL if
an error occurred.
.PP
\&\fBEVP_PKEY_assign_RSA()\fR, \fBEVP_PKEY_assign_DSA()\fR, \fBEVP_PKEY_assign_DH()\fR,
\&\fBEVP_PKEY_assign_EC_KEY()\fR, \fBEVP_PKEY_assign_POLY1305()\fR
and \fBEVP_PKEY_assign_SIPHASH()\fR return 1 for success and 0 for failure.
.PP
\&\fBEVP_PKEY_get_base_id()\fR, \fBEVP_PKEY_get_id()\fR and \fBEVP_PKEY_type()\fR return a key
type or \fBNID_undef\fR (equivalently \fBEVP_PKEY_NONE\fR) on error.
.PP
\&\fBEVP_PKEY_set1_engine()\fR returns 1 for success and 0 for failure.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBEVP_PKEY_new\fR\|(3), \fBSM2\fR\|(7)
.SH HISTORY
.IX Header "HISTORY"
The \fBEVP_PKEY_id()\fR and \fBEVP_PKEY_base_id()\fR functions were renamed to
include \f(CW\*(C`get\*(C'\fR in their names in OpenSSL 3.0, respectively. The old names
are kept as non-deprecated alias macros.
.PP
EVP_PKEY_set1_RSA, EVP_PKEY_set1_DSA, EVP_PKEY_set1_DH, EVP_PKEY_set1_EC_KEY,
EVP_PKEY_get1_RSA, EVP_PKEY_get1_DSA, EVP_PKEY_get1_DH, EVP_PKEY_get1_EC_KEY,
EVP_PKEY_get0_RSA, EVP_PKEY_get0_DSA, EVP_PKEY_get0_DH, EVP_PKEY_get0_EC_KEY,
EVP_PKEY_assign_RSA, EVP_PKEY_assign_DSA, EVP_PKEY_assign_DH,
EVP_PKEY_assign_EC_KEY, EVP_PKEY_assign_POLY1305, EVP_PKEY_assign_SIPHASH,
EVP_PKEY_get0_hmac, EVP_PKEY_get0_poly1305, EVP_PKEY_get0_siphash,
EVP_PKEY_set1_engine and EVP_PKEY_get0_engine were deprecated in OpenSSL 3.0.
.PP
The return value from EVP_PKEY_get0_RSA, EVP_PKEY_get0_DSA, EVP_PKEY_get0_DH,
EVP_PKEY_get0_EC_KEY were made const in OpenSSL 3.0.
.PP
The function \fBEVP_PKEY_set_alias_type()\fR was previously documented on this page.
It was removed in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2002\-2023 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
