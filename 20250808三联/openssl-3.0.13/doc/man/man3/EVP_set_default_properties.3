.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "EVP_SET_DEFAULT_PROPERTIES 3ossl"
.TH EVP_SET_DEFAULT_PROPERTIES 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
EVP_set_default_properties, EVP_default_properties_enable_fips,
EVP_default_properties_is_fips_enabled
\&\- Set default properties for future algorithm fetches
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/evp.h>
\&
\& int EVP_set_default_properties(OSSL_LIB_CTX *libctx, const char *propq);
\& int EVP_default_properties_enable_fips(OSSL_LIB_CTX *libctx, int enable);
\& int EVP_default_properties_is_fips_enabled(OSSL_LIB_CTX *libctx);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBEVP_set_default_properties()\fR sets the default properties for all
future EVP algorithm fetches, implicit as well as explicit. See
"ALGORITHM FETCHING" in \fBcrypto\fR\|(7) for information about implicit and explicit
fetching.
.PP
EVP_set_default_properties stores the properties given with the string
\&\fIpropq\fR among the EVP data that's been stored in the library context
given with \fIlibctx\fR (NULL signifies the default library context).
.PP
Any previous default property for the specified library context will
be dropped.
.PP
\&\fBEVP_default_properties_enable_fips()\fR sets the 'fips=yes' to be a default property
if \fIenable\fR is non zero, otherwise it clears 'fips' from the default property
query for the given \fIlibctx\fR. It merges the fips default property query with any
existing query strings that have been set via \fBEVP_set_default_properties()\fR.
.PP
\&\fBEVP_default_properties_is_fips_enabled()\fR indicates if 'fips=yes' is a default
property for the given \fIlibctx\fR.
.SH NOTES
.IX Header "NOTES"
\&\fBEVP_set_default_properties()\fR and  \fBEVP_default_properties_enable_fips()\fR are not
thread safe. They are intended to be called only during the initialisation
phase of a \fIlibctx\fR.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBEVP_set_default_properties()\fR and  \fBEVP_default_properties_enable_fips()\fR return 1
on success, or 0 on failure. An error is placed on the error stack if a
failure occurs.
.PP
\&\fBEVP_default_properties_is_fips_enabled()\fR returns 1 if the 'fips=yes' default
property is set for the given \fIlibctx\fR, otherwise it returns 0.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBEVP_MD_fetch\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
The functions described here were added in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2019\-2022 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
