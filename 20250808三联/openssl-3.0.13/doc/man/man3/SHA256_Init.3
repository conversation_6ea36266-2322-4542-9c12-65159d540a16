.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "SHA256_INIT 3ossl"
.TH SHA256_INIT 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
SHA1, SHA1_Init, SHA1_Update, SHA1_Final, SHA224, SHA224_Init, SHA224_Update,
SHA224_Final, SHA256, SHA256_Init, SHA256_Update, SHA256_Final, SHA384,
SHA384_Init, SHA384_Update, SHA384_Final, SHA512, SHA512_Init, SHA512_Update,
SHA512_Final \- Secure Hash Algorithm
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/sha.h>
\&
\& unsigned char *SHA1(const unsigned char *data, size_t count, unsigned char *md_buf);
\& unsigned char *SHA224(const unsigned char *data, size_t count, unsigned char *md_buf);
\& unsigned char *SHA256(const unsigned char *data, size_t count, unsigned char *md_buf);
\& unsigned char *SHA384(const unsigned char *data, size_t count, unsigned char *md_buf);
\& unsigned char *SHA512(const unsigned char *data, size_t count, unsigned char *md_buf);
.Ve
.PP
The following functions have been deprecated since OpenSSL 3.0, and can be
hidden entirely by defining \fBOPENSSL_API_COMPAT\fR with a suitable version value,
see \fBopenssl_user_macros\fR\|(7):
.PP
.Vb 3
\& int SHA1_Init(SHA_CTX *c);
\& int SHA1_Update(SHA_CTX *c, const void *data, size_t len);
\& int SHA1_Final(unsigned char *md, SHA_CTX *c);
\&
\& int SHA224_Init(SHA256_CTX *c);
\& int SHA224_Update(SHA256_CTX *c, const void *data, size_t len);
\& int SHA224_Final(unsigned char *md, SHA256_CTX *c);
\&
\& int SHA256_Init(SHA256_CTX *c);
\& int SHA256_Update(SHA256_CTX *c, const void *data, size_t len);
\& int SHA256_Final(unsigned char *md, SHA256_CTX *c);
\&
\& int SHA384_Init(SHA512_CTX *c);
\& int SHA384_Update(SHA512_CTX *c, const void *data, size_t len);
\& int SHA384_Final(unsigned char *md, SHA512_CTX *c);
\&
\& int SHA512_Init(SHA512_CTX *c);
\& int SHA512_Update(SHA512_CTX *c, const void *data, size_t len);
\& int SHA512_Final(unsigned char *md, SHA512_CTX *c);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
All of the functions described on this page
except for \fBSHA1()\fR, \fBSHA224()\fR, \fBSHA256()\fR, \fBSHA384()\fR and \fBSHA512()\fR are deprecated.
Applications should instead use \fBEVP_DigestInit_ex\fR\|(3), \fBEVP_DigestUpdate\fR\|(3)
and \fBEVP_DigestFinal_ex\fR\|(3), or the quick one-shot function \fBEVP_Q_digest\fR\|(3).
\&\fBSHA1()\fR, \fBSHA224()\fR, \fBSHA256()\fR, \fBSHA384()\fR, and \fBSHA256()\fR
can continue to be used. They can also be replaced by, e.g.,
.PP
.Vb 1
\&    (EVP_Q_digest(d, n, md, NULL, NULL, "SHA256", NULL) ? md : NULL)
.Ve
.PP
SHA\-1 (Secure Hash Algorithm) is a cryptographic hash function with a
160 bit output.
.PP
\&\fBSHA1()\fR computes the SHA\-1 message digest of the \fBn\fR
bytes at \fBd\fR and places it in \fBmd\fR (which must have space for
SHA_DIGEST_LENGTH == 20 bytes of output). If \fBmd\fR is NULL, the digest
is placed in a static array. Note: setting \fBmd\fR to NULL is \fBnot thread safe\fR.
.PP
The following functions may be used if the message is not completely
stored in memory:
.PP
\&\fBSHA1_Init()\fR initializes a \fBSHA_CTX\fR structure.
.PP
\&\fBSHA1_Update()\fR can be called repeatedly with chunks of the message to
be hashed (\fBlen\fR bytes at \fBdata\fR).
.PP
\&\fBSHA1_Final()\fR places the message digest in \fBmd\fR, which must have space
for SHA_DIGEST_LENGTH == 20 bytes of output, and erases the \fBSHA_CTX\fR.
.PP
The SHA224, SHA256, SHA384 and SHA512 families of functions operate in the
same way as for the SHA1 functions. Note that SHA224 and SHA256 use a
\&\fBSHA256_CTX\fR object instead of \fBSHA_CTX\fR. SHA384 and SHA512 use \fBSHA512_CTX\fR.
The buffer \fBmd\fR must have space for the output from the SHA variant being used
(defined by SHA224_DIGEST_LENGTH, SHA256_DIGEST_LENGTH, SHA384_DIGEST_LENGTH and
SHA512_DIGEST_LENGTH). Also note that, as for the \fBSHA1()\fR function above, the
\&\fBSHA224()\fR, \fBSHA256()\fR, \fBSHA384()\fR and \fBSHA512()\fR functions are not thread safe if
\&\fBmd\fR is NULL.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBSHA1()\fR, \fBSHA224()\fR, \fBSHA256()\fR, \fBSHA384()\fR and \fBSHA512()\fR return a pointer to the hash
value.
.PP
\&\fBSHA1_Init()\fR, \fBSHA1_Update()\fR and \fBSHA1_Final()\fR and equivalent SHA224, SHA256,
SHA384 and SHA512 functions return 1 for success, 0 otherwise.
.SH "CONFORMING TO"
.IX Header "CONFORMING TO"
US Federal Information Processing Standard FIPS PUB 180\-4 (Secure Hash
Standard),
ANSI X9.30
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBEVP_Q_digest\fR\|(3),
\&\fBEVP_DigestInit\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
All of these functions except SHA*() were deprecated in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
