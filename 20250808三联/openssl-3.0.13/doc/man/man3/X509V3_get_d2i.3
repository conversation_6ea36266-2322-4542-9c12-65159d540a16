.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "X509V3_GET_D2I 3ossl"
.TH X509V3_GET_D2I 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
X509V3_get_d2i, X509V3_add1_i2d, X509V3_EXT_d2i, X509V3_EXT_i2d,
X509_get_ext_d2i, X509_add1_ext_i2d,
X509_CRL_get_ext_d2i, X509_CRL_add1_ext_i2d,
X509_REVOKED_get_ext_d2i, X509_REVOKED_add1_ext_i2d,
X509_get0_extensions, X509_CRL_get0_extensions,
X509_REVOKED_get0_extensions \- X509 extension decode and encode functions
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/x509v3.h>
\&
\& void *X509V3_get_d2i(const STACK_OF(X509_EXTENSION) *x, int nid, int *crit,
\&                      int *idx);
\& int X509V3_add1_i2d(STACK_OF(X509_EXTENSION) **x, int nid, void *value,
\&                     int crit, unsigned long flags);
\&
\& void *X509V3_EXT_d2i(X509_EXTENSION *ext);
\& X509_EXTENSION *X509V3_EXT_i2d(int ext_nid, int crit, void *ext_struc);
\&
\& void *X509_get_ext_d2i(const X509 *x, int nid, int *crit, int *idx);
\& int X509_add1_ext_i2d(X509 *x, int nid, void *value, int crit,
\&                       unsigned long flags);
\&
\& void *X509_CRL_get_ext_d2i(const X509_CRL *crl, int nid, int *crit, int *idx);
\& int X509_CRL_add1_ext_i2d(X509_CRL *crl, int nid, void *value, int crit,
\&                           unsigned long flags);
\&
\& void *X509_REVOKED_get_ext_d2i(const X509_REVOKED *r, int nid, int *crit, int *idx);
\& int X509_REVOKED_add1_ext_i2d(X509_REVOKED *r, int nid, void *value, int crit,
\&                               unsigned long flags);
\&
\& const STACK_OF(X509_EXTENSION) *X509_get0_extensions(const X509 *x);
\& const STACK_OF(X509_EXTENSION) *X509_CRL_get0_extensions(const X509_CRL *crl);
\& const STACK_OF(X509_EXTENSION) *X509_REVOKED_get0_extensions(const X509_REVOKED *r);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBX509V3_get_d2i()\fR looks for an extension with OID \fInid\fR in the extensions
\&\fIx\fR and, if found, decodes it. If \fIidx\fR is NULL then only one
occurrence of an extension is permissible, otherwise the first extension after
index \fI*idx\fR is returned and \fI*idx\fR updated to the location of the extension.
If \fIcrit\fR is not NULL then \fI*crit\fR is set to a status value: \-2 if the
extension occurs multiple times (this is only returned if \fIidx\fR is NULL),
\&\-1 if the extension could not be found, 0 if the extension is found and is
not critical and 1 if critical. A pointer to an extension specific structure
or NULL is returned.
.PP
\&\fBX509V3_add1_i2d()\fR adds extension \fIvalue\fR to STACK \fI*x\fR (allocating a new
STACK if necessary) using OID \fInid\fR and criticality \fIcrit\fR according
to \fIflags\fR.
.PP
\&\fBX509V3_EXT_d2i()\fR attempts to decode the ASN.1 data contained in extension
\&\fIext\fR and returns a pointer to an extension specific structure or NULL
if the extension could not be decoded (invalid syntax or not supported).
.PP
\&\fBX509V3_EXT_i2d()\fR encodes the extension specific structure \fIext_struc\fR
with OID \fIext_nid\fR and criticality \fIcrit\fR.
.PP
\&\fBX509_get_ext_d2i()\fR and \fBX509_add1_ext_i2d()\fR operate on the extensions of
certificate \fIx\fR. They are otherwise identical to \fBX509V3_get_d2i()\fR and
\&\fBX509V3_add1_i2d()\fR.
.PP
\&\fBX509_CRL_get_ext_d2i()\fR and \fBX509_CRL_add1_ext_i2d()\fR operate on the extensions
of CRL \fIcrl\fR. They are otherwise identical to \fBX509V3_get_d2i()\fR and
\&\fBX509V3_add1_i2d()\fR.
.PP
\&\fBX509_REVOKED_get_ext_d2i()\fR and \fBX509_REVOKED_add1_ext_i2d()\fR operate on the
extensions of \fBX509_REVOKED\fR structure \fIr\fR (i.e for CRL entry extensions).
They are otherwise identical to \fBX509V3_get_d2i()\fR and \fBX509V3_add1_i2d()\fR.
.PP
\&\fBX509_get0_extensions()\fR, \fBX509_CRL_get0_extensions()\fR and
\&\fBX509_REVOKED_get0_extensions()\fR return a STACK of all the extensions
of a certificate, a CRL or a CRL entry respectively.
.SH NOTES
.IX Header "NOTES"
In almost all cases an extension can occur at most once and multiple
occurrences is an error. Therefore, the \fIidx\fR parameter is usually NULL.
.PP
The \fIflags\fR parameter may be one of the following values.
.PP
\&\fBX509V3_ADD_DEFAULT\fR appends a new extension only if the extension does
not exist. An error is returned if the extension exists.
.PP
\&\fBX509V3_ADD_APPEND\fR appends a new extension, ignoring whether the extension
exists.
.PP
\&\fBX509V3_ADD_REPLACE\fR replaces an existing extension. If the extension does
not exist, appends a new extension.
.PP
\&\fBX509V3_ADD_REPLACE_EXISTING\fR replaces an existing extension. If the
extension does not exist, returns an error.
.PP
\&\fBX509V3_ADD_KEEP_EXISTING\fR appends a new extension only if the extension does
not exist. An error is \fBnot\fR returned if the extension exists.
.PP
\&\fBX509V3_ADD_DELETE\fR deletes and frees an existing extension. If the extension
does not exist, returns an error. No new extension is added.
.PP
If \fBX509V3_ADD_SILENT\fR is bitwise ORed with \fIflags\fR: any error returned
will not be added to the error queue.
.PP
The function \fBX509V3_get_d2i()\fR and its variants
will return NULL if the extension is not
found, occurs multiple times or cannot be decoded. It is possible to
determine the precise reason by checking the value of \fI*crit\fR.
.PP
The function \fBX509V3_add1_i2d()\fR and its variants allocate \fBX509_EXTENSION\fR
objects on STACK \fI*x\fR depending on \fIflags\fR. The \fBX509_EXTENSION\fR objects
must be explicitly freed using \fBX509_EXTENSION_free()\fR.
.SH "SUPPORTED EXTENSIONS"
.IX Header "SUPPORTED EXTENSIONS"
The following sections contain a list of all supported extensions
including their name and NID.
.SS "PKIX Certificate Extensions"
.IX Subsection "PKIX Certificate Extensions"
The following certificate extensions are defined in PKIX standards such as
RFC5280.
.PP
.Vb 3
\& Basic Constraints                  NID_basic_constraints
\& Key Usage                          NID_key_usage
\& Extended Key Usage                 NID_ext_key_usage
\&
\& Subject Key Identifier             NID_subject_key_identifier
\& Authority Key Identifier           NID_authority_key_identifier
\&
\& Private Key Usage Period           NID_private_key_usage_period
\&
\& Subject Alternative Name           NID_subject_alt_name
\& Issuer Alternative Name            NID_issuer_alt_name
\&
\& Authority Information Access       NID_info_access
\& Subject Information Access         NID_sinfo_access
\&
\& Name Constraints                   NID_name_constraints
\&
\& Certificate Policies               NID_certificate_policies
\& Policy Mappings                    NID_policy_mappings
\& Policy Constraints                 NID_policy_constraints
\& Inhibit Any Policy                 NID_inhibit_any_policy
\&
\& TLS Feature                        NID_tlsfeature
.Ve
.SS "Netscape Certificate Extensions"
.IX Subsection "Netscape Certificate Extensions"
The following are (largely obsolete) Netscape certificate extensions.
.PP
.Vb 8
\& Netscape Cert Type                 NID_netscape_cert_type
\& Netscape Base Url                  NID_netscape_base_url
\& Netscape Revocation Url            NID_netscape_revocation_url
\& Netscape CA Revocation Url         NID_netscape_ca_revocation_url
\& Netscape Renewal Url               NID_netscape_renewal_url
\& Netscape CA Policy Url             NID_netscape_ca_policy_url
\& Netscape SSL Server Name           NID_netscape_ssl_server_name
\& Netscape Comment                   NID_netscape_comment
.Ve
.SS "Miscellaneous Certificate Extensions"
.IX Subsection "Miscellaneous Certificate Extensions"
.Vb 2
\& Strong Extranet ID                 NID_sxnet
\& Proxy Certificate Information      NID_proxyCertInfo
.Ve
.SS "PKIX CRL Extensions"
.IX Subsection "PKIX CRL Extensions"
The following are CRL extensions from PKIX standards such as RFC5280.
.PP
.Vb 6
\& CRL Number                         NID_crl_number
\& CRL Distribution Points            NID_crl_distribution_points
\& Delta CRL Indicator                NID_delta_crl
\& Freshest CRL                       NID_freshest_crl
\& Invalidity Date                    NID_invalidity_date
\& Issuing Distribution Point         NID_issuing_distribution_point
.Ve
.PP
The following are CRL entry extensions from PKIX standards such as RFC5280.
.PP
.Vb 2
\& CRL Reason Code                    NID_crl_reason
\& Certificate Issuer                 NID_certificate_issuer
.Ve
.SS "OCSP Extensions"
.IX Subsection "OCSP Extensions"
.Vb 7
\& OCSP Nonce                         NID_id_pkix_OCSP_Nonce
\& OCSP CRL ID                        NID_id_pkix_OCSP_CrlID
\& Acceptable OCSP Responses          NID_id_pkix_OCSP_acceptableResponses
\& OCSP No Check                      NID_id_pkix_OCSP_noCheck
\& OCSP Archive Cutoff                NID_id_pkix_OCSP_archiveCutoff
\& OCSP Service Locator               NID_id_pkix_OCSP_serviceLocator
\& Hold Instruction Code              NID_hold_instruction_code
.Ve
.SS "Certificate Transparency Extensions"
.IX Subsection "Certificate Transparency Extensions"
The following extensions are used by certificate transparency, RFC6962
.PP
.Vb 2
\& CT Precertificate SCTs             NID_ct_precert_scts
\& CT Certificate SCTs                NID_ct_cert_scts
.Ve
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBX509V3_get_d2i()\fR, its variants, and \fBX509V3_EXT_d2i()\fR return
a pointer to an extension specific structure or NULL if an error occurs.
.PP
\&\fBX509V3_add1_i2d()\fR and its variants return 1 if the operation is successful
and 0 if it fails due to a non-fatal error (extension not found, already exists,
cannot be encoded) or \-1 due to a fatal error such as a memory allocation
failure.
.PP
\&\fBX509V3_EXT_i2d()\fR returns a pointer to an \fBX509_EXTENSION\fR structure
or NULL if an error occurs.
.PP
\&\fBX509_get0_extensions()\fR, \fBX509_CRL_get0_extensions()\fR and
\&\fBX509_REVOKED_get0_extensions()\fR return a stack of extensions. They return
NULL if no extensions are present.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBd2i_X509\fR\|(3),
\&\fBERR_get_error\fR\|(3),
\&\fBX509_CRL_get0_by_serial\fR\|(3),
\&\fBX509_get0_signature\fR\|(3),
\&\fBX509_get_ext_d2i\fR\|(3),
\&\fBX509_get_extension_flags\fR\|(3),
\&\fBX509_get_pubkey\fR\|(3),
\&\fBX509_get_subject_name\fR\|(3),
\&\fBX509_get_version\fR\|(3),
\&\fBX509_NAME_add_entry_by_txt\fR\|(3),
\&\fBX509_NAME_ENTRY_get_object\fR\|(3),
\&\fBX509_NAME_get_index_by_NID\fR\|(3),
\&\fBX509_NAME_print_ex\fR\|(3),
\&\fBX509_new\fR\|(3),
\&\fBX509_sign\fR\|(3),
\&\fBX509_verify_cert\fR\|(3)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2015\-2022 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
