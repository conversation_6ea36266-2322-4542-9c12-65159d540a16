.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "X509_VERIFY 3ossl"
.TH X509_VERIFY 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
X509_verify, X509_self_signed,
X509_REQ_verify_ex, X509_REQ_verify,
X509_CRL_verify \-
verify certificate, certificate request, or CRL signature
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/x509.h>
\&
\& int X509_verify(X509 *x, EVP_PKEY *pkey);
\& int X509_self_signed(X509 *cert, int verify_signature);
\&
\& int X509_REQ_verify_ex(X509_REQ *a, EVP_PKEY *pkey, OSSL_LIB_CTX *libctx,
\&                        const char *propq);
\& int X509_REQ_verify(X509_REQ *a, EVP_PKEY *r);
\& int X509_CRL_verify(X509_CRL *a, EVP_PKEY *r);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBX509_verify()\fR verifies the signature of certificate \fIx\fR using public key
\&\fIpkey\fR. Only the signature is checked: no other checks (such as certificate
chain validity) are performed.
.PP
\&\fBX509_self_signed()\fR checks whether certificate \fIcert\fR is self-signed.
For success the issuer and subject names must match, the components of the
authority key identifier (if present) must match the subject key identifier etc.
The signature itself is actually verified only if \fBverify_signature\fR is 1, as
for explicitly trusted certificates this verification is not worth the effort.
.PP
\&\fBX509_REQ_verify_ex()\fR, \fBX509_REQ_verify()\fR and \fBX509_CRL_verify()\fR
verify the signatures of certificate requests and CRLs, respectively.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBX509_verify()\fR,
\&\fBX509_REQ_verify_ex()\fR, \fBX509_REQ_verify()\fR and \fBX509_CRL_verify()\fR
return 1 if the signature is valid and 0 if the signature check fails.
If the signature could not be checked at all because it was ill-formed,
the certificate or the request was not complete or some other error occurred
then \-1 is returned.
.PP
\&\fBX509_self_signed()\fR returns the same values but also returns 1
if all respective fields match and \fBverify_signature\fR is 0.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBd2i_X509\fR\|(3),
\&\fBERR_get_error\fR\|(3),
\&\fBX509_CRL_get0_by_serial\fR\|(3),
\&\fBX509_get0_signature\fR\|(3),
\&\fBX509_get_ext_d2i\fR\|(3),
\&\fBX509_get_extension_flags\fR\|(3),
\&\fBX509_get_pubkey\fR\|(3),
\&\fBX509_get_subject_name\fR\|(3),
\&\fBX509_get_version\fR\|(3),
\&\fBX509_NAME_ENTRY_get_object\fR\|(3),
\&\fBX509_NAME_get_index_by_NID\fR\|(3),
\&\fBX509_NAME_print_ex\fR\|(3),
\&\fBX509V3_get_d2i\fR\|(3),
\&\fBX509_verify_cert\fR\|(3),
\&\fBOSSL_LIB_CTX\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
The \fBX509_verify()\fR, \fBX509_REQ_verify()\fR, and \fBX509_CRL_verify()\fR
functions are available in all versions of OpenSSL.
.PP
\&\fBX509_REQ_verify_ex()\fR, and \fBX509_self_signed()\fR were added in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2015\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
