.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "SSL_GROUP_TO_NAME 3ossl"
.TH SSL_GROUP_TO_NAME 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
SSL_group_to_name \- get name of group
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/ssl.h>
\&
\& const char *SSL_group_to_name(const SSL *ssl, int id);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBSSL_group_to_name()\fR is used to retrieve the TLS group name
associated with a given TLS group ID, as registered via built-in
or external providers and as returned by a call to \fBSSL_get1_groups()\fR
or \fBSSL_get_shared_group()\fR.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
If non-NULL, \fBSSL_group_to_name()\fR returns the TLS group name
corresponding to the given \fIid\fR as a NUL-terminated string.
If \fBSSL_group_to_name()\fR returns NULL, an error occurred; possibly no
corresponding tlsname was registered during provider initialisation.
.PP
Note that the return value is valid only during the lifetime of the
SSL object \fIssl\fR.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBssl\fR\|(7)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
