.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "BIO_F_BASE64 3ossl"
.TH BIO_F_BASE64 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
BIO_f_base64 \- base64 BIO filter
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 2
\& #include <openssl/bio.h>
\& #include <openssl/evp.h>
\&
\& const BIO_METHOD *BIO_f_base64(void);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBBIO_f_base64()\fR returns the base64 BIO method. This is a filter
BIO that base64 encodes any data written through it and decodes
any data read through it.
.PP
Base64 BIOs do not support \fBBIO_gets()\fR or \fBBIO_puts()\fR.
.PP
For writing, output is by default divided to lines of length 64
characters and there is always a newline at the end of output.
.PP
For reading, first line should be at most 1024
characters long. If it is longer then it is ignored completely.
Other input lines can be of any length. There must be a newline
at the end of input.
.PP
This behavior can be changed with BIO_FLAGS_BASE64_NO_NL flag.
.PP
\&\fBBIO_flush()\fR on a base64 BIO that is being written through is
used to signal that no more data is to be encoded: this is used
to flush the final block through the BIO.
.PP
The flag BIO_FLAGS_BASE64_NO_NL can be set with \fBBIO_set_flags()\fR.
For writing, it causes all data to be written on one line without
newline at the end.
For reading, it expects the data to be all on one line (with or
without a trailing newline).
.SH NOTES
.IX Header "NOTES"
Because of the format of base64 encoding the end of the encoded
block cannot always be reliably determined.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBBIO_f_base64()\fR returns the base64 BIO method.
.SH EXAMPLES
.IX Header "EXAMPLES"
Base64 encode the string "Hello World\en" and write the result
to standard output:
.PP
.Vb 2
\& BIO *bio, *b64;
\& char message[] = "Hello World \en";
\&
\& b64 = BIO_new(BIO_f_base64());
\& bio = BIO_new_fp(stdout, BIO_NOCLOSE);
\& BIO_push(b64, bio);
\& BIO_write(b64, message, strlen(message));
\& BIO_flush(b64);
\&
\& BIO_free_all(b64);
.Ve
.PP
Read Base64 encoded data from standard input and write the decoded
data to standard output:
.PP
.Vb 3
\& BIO *bio, *b64, *bio_out;
\& char inbuf[512];
\& int inlen;
\&
\& b64 = BIO_new(BIO_f_base64());
\& bio = BIO_new_fp(stdin, BIO_NOCLOSE);
\& bio_out = BIO_new_fp(stdout, BIO_NOCLOSE);
\& BIO_push(b64, bio);
\& while ((inlen = BIO_read(b64, inbuf, 512)) > 0)
\&     BIO_write(bio_out, inbuf, inlen);
\&
\& BIO_flush(bio_out);
\& BIO_free_all(b64);
.Ve
.SH BUGS
.IX Header "BUGS"
The ambiguity of EOF in base64 encoded data can cause additional
data following the base64 encoded block to be misinterpreted.
.PP
There should be some way of specifying a test that the BIO can perform
to reliably determine EOF (for example a MIME boundary).
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2022 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
