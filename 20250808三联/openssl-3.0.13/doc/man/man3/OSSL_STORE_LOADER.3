.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "OSSL_STORE_LOADER 3ossl"
.TH OSSL_STORE_LOADER 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
OSSL_STORE_LOADER,
OSSL_STORE_LOADER_fetch,
OSSL_STORE_LOADER_up_ref,
OSSL_STORE_LOADER_free,
OSSL_STORE_LOADER_get0_provider,
OSSL_STORE_LOADER_get0_properties,
OSSL_STORE_LOADER_is_a,
OSSL_STORE_LOADER_get0_description,
OSSL_STORE_LOADER_do_all_provided,
OSSL_STORE_LOADER_names_do_all,
OSSL_STORE_LOADER_CTX, OSSL_STORE_LOADER_new,
OSSL_STORE_LOADER_get0_engine, OSSL_STORE_LOADER_get0_scheme,
OSSL_STORE_LOADER_set_open, OSSL_STORE_LOADER_set_open_ex,
OSSL_STORE_LOADER_set_attach, OSSL_STORE_LOADER_set_ctrl,
OSSL_STORE_LOADER_set_expect, OSSL_STORE_LOADER_set_find,
OSSL_STORE_LOADER_set_load, OSSL_STORE_LOADER_set_eof,
OSSL_STORE_LOADER_set_error, OSSL_STORE_LOADER_set_close,
OSSL_STORE_register_loader, OSSL_STORE_unregister_loader,
OSSL_STORE_open_fn, OSSL_STORE_open_ex_fn,
OSSL_STORE_attach_fn, OSSL_STORE_ctrl_fn,
OSSL_STORE_expect_fn, OSSL_STORE_find_fn,
OSSL_STORE_load_fn, OSSL_STORE_eof_fn, OSSL_STORE_error_fn,
OSSL_STORE_close_fn \- Types and functions to manipulate, register and
unregister STORE loaders for different URI schemes
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/store.h>
\&
\& typedef struct ossl_store_loader_st OSSL_STORE_LOADER;
\&
\& OSSL_STORE_LOADER *OSSL_STORE_LOADER_fetch(OSSL_LIB_CTX *libctx,
\&                                            const char *scheme,
\&                                            const char *properties);
\& int OSSL_STORE_LOADER_up_ref(OSSL_STORE_LOADER *loader);
\& void OSSL_STORE_LOADER_free(OSSL_STORE_LOADER *loader);
\& const OSSL_PROVIDER *OSSL_STORE_LOADER_get0_provider(const OSSL_STORE_LOADER *
\&                                                 loader);
\& const char *OSSL_STORE_LOADER_get0_properties(const OSSL_STORE_LOADER *loader);
\& const char *OSSL_STORE_LOADER_get0_description(const OSSL_STORE_LOADER *loader);
\& int OSSL_STORE_LOADER_is_a(const OSSL_STORE_LOADER *loader,
\&                            const char *scheme);
\& void OSSL_STORE_LOADER_do_all_provided(OSSL_LIB_CTX *libctx,
\&                                        void (*user_fn)(OSSL_STORE_LOADER *loader,
\&                                                   void *arg),
\&                                        void *user_arg);
\& int OSSL_STORE_LOADER_names_do_all(const OSSL_STORE_LOADER *loader,
\&                                    void (*fn)(const char *name, void *data),
\&                                    void *data);
.Ve
.PP
The following functions have been deprecated since OpenSSL 3.0, and can be
hidden entirely by defining \fBOPENSSL_API_COMPAT\fR with a suitable version value,
see \fBopenssl_user_macros\fR\|(7):
.PP
.Vb 5
\& OSSL_STORE_LOADER *OSSL_STORE_LOADER_new(ENGINE *e, const char *scheme);
\& const ENGINE *OSSL_STORE_LOADER_get0_engine(const OSSL_STORE_LOADER
\&                                             *store_loader);
\& const char *OSSL_STORE_LOADER_get0_scheme(const OSSL_STORE_LOADER
\&                                           *store_loader);
\&
\& /* struct ossl_store_loader_ctx_st is defined differently by each loader */
\& typedef struct ossl_store_loader_ctx_st OSSL_STORE_LOADER_CTX;
\&
\& typedef OSSL_STORE_LOADER_CTX *(*OSSL_STORE_open_fn)(
\&     const char *uri, const UI_METHOD *ui_method, void *ui_data);
\& int OSSL_STORE_LOADER_set_open(OSSL_STORE_LOADER *store_loader,
\&                                OSSL_STORE_open_fn store_open_function);
\& typedef OSSL_STORE_LOADER_CTX *(*OSSL_STORE_open_ex_fn)(
\&     const char *uri, const UI_METHOD *ui_method, void *ui_data);
\& int OSSL_STORE_LOADER_set_open_ex
\&     (OSSL_STORE_LOADER *store_loader,
\&      OSSL_STORE_open_ex_fn store_open_ex_function);
\& typedef OSSL_STORE_LOADER_CTX *(*OSSL_STORE_attach_fn)
\&     (const OSSL_STORE_LOADER *loader, BIO *bio,
\&      OSSL_LIB_CTX *libctx, const char *propq,
\&      const UI_METHOD *ui_method, void *ui_data);
\& int OSSL_STORE_LOADER_set_attach(OSSL_STORE_LOADER *loader,
\&                                  OSSL_STORE_attach_fn attach_function);
\& typedef int (*OSSL_STORE_ctrl_fn)(OSSL_STORE_LOADER_CTX *ctx, int cmd,
\&                                   va_list args);
\& int OSSL_STORE_LOADER_set_ctrl(OSSL_STORE_LOADER *store_loader,
\&                                OSSL_STORE_ctrl_fn store_ctrl_function);
\& typedef int (*OSSL_STORE_expect_fn)(OSSL_STORE_LOADER_CTX *ctx, int expected);
\& int OSSL_STORE_LOADER_set_expect(OSSL_STORE_LOADER *loader,
\&                                  OSSL_STORE_expect_fn expect_function);
\& typedef int (*OSSL_STORE_find_fn)(OSSL_STORE_LOADER_CTX *ctx,
\&                                   OSSL_STORE_SEARCH *criteria);
\& int OSSL_STORE_LOADER_set_find(OSSL_STORE_LOADER *loader,
\&                                OSSL_STORE_find_fn find_function);
\& typedef OSSL_STORE_INFO *(*OSSL_STORE_load_fn)(OSSL_STORE_LOADER_CTX *ctx,
\&                                                UI_METHOD *ui_method,
\&                                                void *ui_data);
\& int OSSL_STORE_LOADER_set_load(OSSL_STORE_LOADER *store_loader,
\&                                OSSL_STORE_load_fn store_load_function);
\& typedef int (*OSSL_STORE_eof_fn)(OSSL_STORE_LOADER_CTX *ctx);
\& int OSSL_STORE_LOADER_set_eof(OSSL_STORE_LOADER *store_loader,
\&                               OSSL_STORE_eof_fn store_eof_function);
\& typedef int (*OSSL_STORE_error_fn)(OSSL_STORE_LOADER_CTX *ctx);
\& int OSSL_STORE_LOADER_set_error(OSSL_STORE_LOADER *store_loader,
\&                                 OSSL_STORE_error_fn store_error_function);
\& typedef int (*OSSL_STORE_close_fn)(OSSL_STORE_LOADER_CTX *ctx);
\& int OSSL_STORE_LOADER_set_close(OSSL_STORE_LOADER *store_loader,
\&                                 OSSL_STORE_close_fn store_close_function);
\& void OSSL_STORE_LOADER_free(OSSL_STORE_LOADER *store_loader);
\&
\& int OSSL_STORE_register_loader(OSSL_STORE_LOADER *loader);
\& OSSL_STORE_LOADER *OSSL_STORE_unregister_loader(const char *scheme);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBOSSL_STORE_LOADER\fR is a method for OSSL_STORE loaders, which implement
\&\fBOSSL_STORE_open()\fR, \fBOSSL_STORE_open_ex()\fR, \fBOSSL_STORE_load()\fR,
\&\fBOSSL_STORE_eof()\fR, \fBOSSL_STORE_error()\fR and \fBOSSL_STORE_close()\fR for specific
storage schemes.
.PP
\&\fBOSSL_STORE_LOADER_fetch()\fR looks for an implementation for a storage
\&\fIscheme\fR within the providers that has been loaded into the \fBOSSL_LIB_CTX\fR
given by \fIlibctx\fR, and with the properties given by \fIproperties\fR.
.PP
\&\fBOSSL_STORE_LOADER_up_ref()\fR increments the reference count for the given
\&\fIloader\fR.
.PP
\&\fBOSSL_STORE_LOADER_free()\fR decrements the reference count for the given
\&\fIloader\fR, and when the count reaches zero, frees it.
.PP
\&\fBOSSL_STORE_LOADER_get0_provider()\fR returns the provider of the given
\&\fIloader\fR.
.PP
\&\fBOSSL_STORE_LOADER_get0_properties()\fR returns the property definition associated
with the given \fIloader\fR.
.PP
\&\fBOSSL_STORE_LOADER_is_a()\fR checks if \fIloader\fR is an implementation
of an algorithm that's identifiable with \fIscheme\fR.
.PP
\&\fBOSSL_STORE_LOADER_get0_description()\fR returns a description of the \fIloader\fR, meant
for display and human consumption.  The description is at the discretion of the
\&\fIloader\fR implementation.
.PP
\&\fBOSSL_STORE_LOADER_do_all_provided()\fR traverses all store implementations
by all activated providers in the library context \fIlibctx\fR, and for each
of the implementations, calls \fIuser_fn\fR with the implementation method and
\&\fIuser_arg\fR as arguments.
.PP
\&\fBOSSL_STORE_LOADER_names_do_all()\fR traverses all names for the given
\&\fIloader\fR, and calls \fIfn\fR with each name and \fIdata\fR.
.SS "Legacy Types and Functions (deprecated)"
.IX Subsection "Legacy Types and Functions (deprecated)"
These functions help applications and engines to create loaders for
schemes they support.  These are all deprecated and discouraged in favour of
provider implementations, see \fBprovider\-storemgmt\fR\|(7).
.PP
\&\fBOSSL_STORE_LOADER_CTX\fR is a type template, to be defined by each loader
using \f(CW\*(C`struct ossl_store_loader_ctx_st { ... }\*(C'\fR.
.PP
\&\fBOSSL_STORE_open_fn\fR, \fBOSSL_STORE_open_ex_fn\fR,
\&\fBOSSL_STORE_ctrl_fn\fR, \fBOSSL_STORE_expect_fn\fR, \fBOSSL_STORE_find_fn\fR,
\&\fBOSSL_STORE_load_fn\fR, \fBOSSL_STORE_eof_fn\fR, and \fBOSSL_STORE_close_fn\fR
are the function pointer types used within a STORE loader.
The functions pointed at define the functionality of the given loader.
.IP "\fBOSSL_STORE_open_fn\fR and \fBOSSL_STORE_open_ex_fn\fR" 4
.IX Item "OSSL_STORE_open_fn and OSSL_STORE_open_ex_fn"
\&\fBOSSL_STORE_open_ex_fn\fR takes a URI and is expected to
interpret it in the best manner possible according to the scheme the
loader implements.  It also takes a \fBUI_METHOD\fR and associated data,
to be used any time something needs to be prompted for, as well as a
library context \fIlibctx\fR with an associated property query \fIpropq\fR,
to be used when fetching necessary algorithms to perform the loads.
Furthermore, this function is expected to initialize what needs to be
initialized, to create a private data store (\fBOSSL_STORE_LOADER_CTX\fR,
see above), and to return it.
If something goes wrong, this function is expected to return NULL.
.Sp
\&\fBOSSL_STORE_open_fn\fR does the same thing as
\&\fBOSSL_STORE_open_ex_fn\fR but uses NULL for the library
context \fIlibctx\fR and property query \fIpropq\fR.
.IP \fBOSSL_STORE_attach_fn\fR 4
.IX Item "OSSL_STORE_attach_fn"
This function takes a \fBBIO\fR, otherwise works like
\&\fBOSSL_STORE_open_ex_fn\fR.
.IP \fBOSSL_STORE_ctrl_fn\fR 4
.IX Item "OSSL_STORE_ctrl_fn"
This function takes a \fBOSSL_STORE_LOADER_CTX\fR pointer, a command number
\&\fIcmd\fR and a \fBva_list\fR \fIargs\fR and is used to manipulate loader
specific parameters.
.Sp
Loader specific command numbers must begin at \fBOSSL_STORE_C_CUSTOM_START\fR.
Any number below that is reserved for future globally known command
numbers.
.Sp
This function is expected to return 1 on success, 0 on error.
.IP \fBOSSL_STORE_expect_fn\fR 4
.IX Item "OSSL_STORE_expect_fn"
This function takes a \fBOSSL_STORE_LOADER_CTX\fR pointer and a \fBOSSL_STORE_INFO\fR
identity \fIexpected\fR, and is used to tell the loader what object type is
expected.
\&\fIexpected\fR may be zero to signify that no specific object type is expected.
.Sp
This function is expected to return 1 on success, 0 on error.
.IP \fBOSSL_STORE_find_fn\fR 4
.IX Item "OSSL_STORE_find_fn"
This function takes a \fBOSSL_STORE_LOADER_CTX\fR pointer and a
\&\fBOSSL_STORE_SEARCH\fR search criterion, and is used to tell the loader what
to search for.
.Sp
When called with the loader context being NULL, this function is expected
to return 1 if the loader supports the criterion, otherwise 0.
.Sp
When called with the loader context being something other than NULL, this
function is expected to return 1 on success, 0 on error.
.IP \fBOSSL_STORE_load_fn\fR 4
.IX Item "OSSL_STORE_load_fn"
This function takes a \fBOSSL_STORE_LOADER_CTX\fR pointer and a \fBUI_METHOD\fR
with associated data.
It's expected to load the next available data, mold it into a data
structure that can be wrapped in a \fBOSSL_STORE_INFO\fR using one of the
\&\fBOSSL_STORE_INFO\fR\|(3) functions.
If no more data is available or an error occurs, this function is
expected to return NULL.
The \fBOSSL_STORE_eof_fn\fR and \fBOSSL_STORE_error_fn\fR functions must indicate if
it was in fact the end of data or if an error occurred.
.Sp
Note that this function retrieves \fIone\fR data item only.
.IP \fBOSSL_STORE_eof_fn\fR 4
.IX Item "OSSL_STORE_eof_fn"
This function takes a \fBOSSL_STORE_LOADER_CTX\fR pointer and is expected to
return 1 to indicate that the end of available data has been reached.
It is otherwise expected to return 0.
.IP \fBOSSL_STORE_error_fn\fR 4
.IX Item "OSSL_STORE_error_fn"
This function takes a \fBOSSL_STORE_LOADER_CTX\fR pointer and is expected to
return 1 to indicate that an error occurred in a previous call to the
\&\fBOSSL_STORE_load_fn\fR function.
It is otherwise expected to return 0.
.IP \fBOSSL_STORE_close_fn\fR 4
.IX Item "OSSL_STORE_close_fn"
This function takes a \fBOSSL_STORE_LOADER_CTX\fR pointer and is expected to
close or shut down what needs to be closed, and finally free the
contents of the \fBOSSL_STORE_LOADER_CTX\fR pointer.
It returns 1 on success and 0 on error.
.PP
\&\fBOSSL_STORE_LOADER_new()\fR creates a new \fBOSSL_STORE_LOADER\fR.
It takes an \fBENGINE\fR \fIe\fR and a string \fIscheme\fR.
\&\fIscheme\fR must \fIalways\fR be set.
Both \fIe\fR and \fIscheme\fR are used as is and must therefore be alive as
long as the created loader is.
.PP
\&\fBOSSL_STORE_LOADER_get0_engine()\fR returns the engine of the \fIstore_loader\fR.
\&\fBOSSL_STORE_LOADER_get0_scheme()\fR returns the scheme of the \fIstore_loader\fR.
.PP
\&\fBOSSL_STORE_LOADER_set_open()\fR sets the opener function for the
\&\fIstore_loader\fR.
.PP
\&\fBOSSL_STORE_LOADER_set_open_ex()\fR sets the opener with library context
function for the \fIstore_loader\fR.
.PP
\&\fBOSSL_STORE_LOADER_set_attach()\fR sets the attacher function for the
\&\fIstore_loader\fR.
.PP
\&\fBOSSL_STORE_LOADER_set_ctrl()\fR sets the control function for the
\&\fIstore_loader\fR.
.PP
\&\fBOSSL_STORE_LOADER_set_expect()\fR sets the expect function for the
\&\fIstore_loader\fR.
.PP
\&\fBOSSL_STORE_LOADER_set_load()\fR sets the loader function for the
\&\fIstore_loader\fR.
.PP
\&\fBOSSL_STORE_LOADER_set_eof()\fR sets the end of file checker function for the
\&\fIstore_loader\fR.
.PP
\&\fBOSSL_STORE_LOADER_set_close()\fR sets the closing function for the
\&\fIstore_loader\fR.
.PP
\&\fBOSSL_STORE_LOADER_free()\fR frees the given \fIstore_loader\fR.
.PP
\&\fBOSSL_STORE_register_loader()\fR register the given \fIstore_loader\fR and
thereby makes it available for use with \fBOSSL_STORE_open()\fR,
\&\fBOSSL_STORE_open_ex()\fR, \fBOSSL_STORE_load()\fR, \fBOSSL_STORE_eof()\fR
and \fBOSSL_STORE_close()\fR.
.PP
\&\fBOSSL_STORE_unregister_loader()\fR unregister the store loader for the given
\&\fIscheme\fR.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBOSSL_STORE_LOADER_fetch()\fR returns a pointer to an OSSL_STORE_LOADER object,
or NULL on error.
.PP
\&\fBOSSL_STORE_LOADER_up_ref()\fR returns 1 on success, or 0 on error.
.PP
\&\fBOSSL_STORE_LOADER_names_do_all()\fR returns 1 if the callback was called for all
names. A return value of 0 means that the callback was not called for any names.
.PP
\&\fBOSSL_STORE_LOADER_free()\fR doesn't return any value.
.PP
\&\fBOSSL_STORE_LOADER_get0_provider()\fR returns a pointer to a provider object, or
NULL on error.
.PP
\&\fBOSSL_STORE_LOADER_get0_properties()\fR returns a pointer to a property
definition string, or NULL on error.
.PP
\&\fBOSSL_STORE_LOADER_is_a()\fR returns 1 if \fIloader\fR was identifiable,
otherwise 0.
.PP
\&\fBOSSL_STORE_LOADER_get0_description()\fR returns a pointer to a description, or NULL if
there isn't one.
.PP
The functions with the types \fBOSSL_STORE_open_fn\fR,
\&\fBOSSL_STORE_open_ex_fn\fR, \fBOSSL_STORE_ctrl_fn\fR,
\&\fBOSSL_STORE_expect_fn\fR, \fBOSSL_STORE_load_fn\fR, \fBOSSL_STORE_eof_fn\fR
and \fBOSSL_STORE_close_fn\fR have the same return values as \fBOSSL_STORE_open()\fR,
\&\fBOSSL_STORE_open_ex()\fR, \fBOSSL_STORE_ctrl()\fR, \fBOSSL_STORE_expect()\fR,
\&\fBOSSL_STORE_load()\fR, \fBOSSL_STORE_eof()\fR and \fBOSSL_STORE_close()\fR, respectively.
.PP
\&\fBOSSL_STORE_LOADER_new()\fR returns a pointer to a \fBOSSL_STORE_LOADER\fR on success,
or NULL on failure.
.PP
\&\fBOSSL_STORE_LOADER_set_open()\fR, \fBOSSL_STORE_LOADER_set_open_ex()\fR,
\&\fBOSSL_STORE_LOADER_set_ctrl()\fR, \fBOSSL_STORE_LOADER_set_load()\fR,
\&\fBOSSL_STORE_LOADER_set_eof()\fR and \fBOSSL_STORE_LOADER_set_close()\fR return 1
on success, or 0 on failure.
.PP
\&\fBOSSL_STORE_register_loader()\fR returns 1 on success, or 0 on failure.
.PP
\&\fBOSSL_STORE_unregister_loader()\fR returns the unregistered loader on success,
or NULL on failure.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBossl_store\fR\|(7), \fBOSSL_STORE_open\fR\|(3), \fBOSSL_LIB_CTX\fR\|(3),
\&\fBprovider\-storemgmt\fR\|(7)
.SH HISTORY
.IX Header "HISTORY"
\&\fBOSSL_STORE_LOADER_fetch()\fR, \fBOSSL_STORE_LOADER_up_ref()\fR,
\&\fBOSSL_STORE_LOADER_free()\fR, \fBOSSL_STORE_LOADER_get0_provider()\fR,
\&\fBOSSL_STORE_LOADER_get0_properties()\fR, \fBOSSL_STORE_LOADER_is_a()\fR,
\&\fBOSSL_STORE_LOADER_do_all_provided()\fR and
\&\fBOSSL_STORE_LOADER_names_do_all()\fR were added in OpenSSL 3.0.
.PP
\&\fBOSSL_STORE_open_ex_fn()\fR was added in OpenSSL 3.0.
.PP
\&\fBOSSL_STORE_LOADER\fR, \fBOSSL_STORE_LOADER_CTX\fR, \fBOSSL_STORE_LOADER_new()\fR,
\&\fBOSSL_STORE_LOADER_set0_scheme()\fR, \fBOSSL_STORE_LOADER_get0_scheme()\fR,
\&\fBOSSL_STORE_LOADER_get0_engine()\fR, \fBOSSL_STORE_LOADER_set_expect()\fR,
\&\fBOSSL_STORE_LOADER_set_find()\fR, \fBOSSL_STORE_LOADER_set_attach()\fR,
\&\fBOSSL_STORE_LOADER_set_open_ex()\fR, \fBOSSL_STORE_LOADER_set_open()\fR,
\&\fBOSSL_STORE_LOADER_set_ctrl()\fR,
\&\fBOSSL_STORE_LOADER_set_load()\fR, \fBOSSL_STORE_LOADER_set_eof()\fR,
\&\fBOSSL_STORE_LOADER_set_close()\fR, \fBOSSL_STORE_LOADER_free()\fR,
\&\fBOSSL_STORE_register_loader()\fR, \fBOSSL_STORE_LOADER_set_error()\fR,
\&\fBOSSL_STORE_unregister_loader()\fR, \fBOSSL_STORE_open_fn()\fR, \fBOSSL_STORE_ctrl_fn()\fR,
\&\fBOSSL_STORE_load_fn()\fR, \fBOSSL_STORE_eof_fn()\fR and \fBOSSL_STORE_close_fn()\fR
were added in OpenSSL 1.1.1, and became deprecated in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2016\-2023 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
