.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "DH_GET0_PQG 3ossl"
.TH DH_GET0_PQG 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
DH_get0_pqg, DH_set0_pqg, DH_get0_key, DH_set0_key,
DH_get0_p, DH_get0_q, DH_get0_g,
DH_get0_priv_key, DH_get0_pub_key,
DH_clear_flags, DH_test_flags, DH_set_flags, DH_get0_engine,
DH_get_length, DH_set_length \- Routines for getting and setting data in a DH object
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/dh.h>
.Ve
.PP
The following functions have been deprecated since OpenSSL 3.0, and can be
hidden entirely by defining \fBOPENSSL_API_COMPAT\fR with a suitable version value,
see \fBopenssl_user_macros\fR\|(7):
.PP
.Vb 10
\& void DH_get0_pqg(const DH *dh,
\&                  const BIGNUM **p, const BIGNUM **q, const BIGNUM **g);
\& int DH_set0_pqg(DH *dh, BIGNUM *p, BIGNUM *q, BIGNUM *g);
\& void DH_get0_key(const DH *dh,
\&                  const BIGNUM **pub_key, const BIGNUM **priv_key);
\& int DH_set0_key(DH *dh, BIGNUM *pub_key, BIGNUM *priv_key);
\& const BIGNUM *DH_get0_p(const DH *dh);
\& const BIGNUM *DH_get0_q(const DH *dh);
\& const BIGNUM *DH_get0_g(const DH *dh);
\& const BIGNUM *DH_get0_priv_key(const DH *dh);
\& const BIGNUM *DH_get0_pub_key(const DH *dh);
\& void DH_clear_flags(DH *dh, int flags);
\& int DH_test_flags(const DH *dh, int flags);
\& void DH_set_flags(DH *dh, int flags);
\&
\& long DH_get_length(const DH *dh);
\& int DH_set_length(DH *dh, long length);
\&
\& ENGINE *DH_get0_engine(DH *d);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
All of the functions described on this page are deprecated.
Applications should instead use \fBEVP_PKEY_get_bn_param\fR\|(3) for any methods that
return a \fBBIGNUM\fR. Refer to \fBEVP_PKEY\-DH\fR\|(7) for more information.
.PP
A DH object contains the parameters \fIp\fR, \fIq\fR and \fIg\fR. Note that the \fIq\fR
parameter is optional. It also contains a public key (\fIpub_key\fR) and
(optionally) a private key (\fIpriv_key\fR).
.PP
The \fIp\fR, \fIq\fR and \fIg\fR parameters can be obtained by calling \fBDH_get0_pqg()\fR.
If the parameters have not yet been set then \fI*p\fR, \fI*q\fR and \fI*g\fR will be set
to NULL. Otherwise they are set to pointers to their respective values. These
point directly to the internal representations of the values and therefore
should not be freed directly.
Any of the out parameters \fIp\fR, \fIq\fR, and \fIg\fR can be NULL, in which case no
value will be returned for that parameter.
.PP
The \fIp\fR, \fIq\fR and \fIg\fR values can be set by calling \fBDH_set0_pqg()\fR and passing
the new values for \fIp\fR, \fIq\fR and \fIg\fR as parameters to the function. Calling
this function transfers the memory management of the values to the DH object,
and therefore the values that have been passed in should not be freed directly
after this function has been called. The \fIq\fR parameter may be NULL.
\&\fBDH_set0_pqg()\fR also checks if the parameters associated with \fIp\fR and \fIg\fR and
optionally \fIq\fR are associated with known safe prime groups. If it is a safe
prime group then the value of \fIq\fR will be set to q = (p \- 1) / 2 if \fIq\fR is
NULL. The optional length parameter will be set to BN_num_bits(\fIq\fR) if \fIq\fR
is not NULL.
.PP
To get the public and private key values use the \fBDH_get0_key()\fR function. A
pointer to the public key will be stored in \fI*pub_key\fR, and a pointer to the
private key will be stored in \fI*priv_key\fR. Either may be NULL if they have not
been set yet, although if the private key has been set then the public key must
be. The values point to the internal representation of the public key and
private key values. This memory should not be freed directly.
Any of the out parameters \fIpub_key\fR and \fIpriv_key\fR can be NULL, in which case
no value will be returned for that parameter.
.PP
The public and private key values can be set using \fBDH_set0_key()\fR. Either
parameter may be NULL, which means the corresponding DH field is left
untouched. As with \fBDH_set0_pqg()\fR this function transfers the memory management
of the key values to the DH object, and therefore they should not be freed
directly after this function has been called.
.PP
Any of the values \fIp\fR, \fIq\fR, \fIg\fR, \fIpriv_key\fR, and \fIpub_key\fR can also be
retrieved separately by the corresponding function \fBDH_get0_p()\fR, \fBDH_get0_q()\fR,
\&\fBDH_get0_g()\fR, \fBDH_get0_priv_key()\fR, and \fBDH_get0_pub_key()\fR, respectively.
.PP
\&\fBDH_set_flags()\fR sets the flags in the \fIflags\fR parameter on the DH object.
Multiple flags can be passed in one go (bitwise ORed together). Any flags that
are already set are left set. \fBDH_test_flags()\fR tests to see whether the flags
passed in the \fIflags\fR parameter are currently set in the DH object. Multiple
flags can be tested in one go. All flags that are currently set are returned, or
zero if none of the flags are set. \fBDH_clear_flags()\fR clears the specified flags
within the DH object.
.PP
\&\fBDH_get0_engine()\fR returns a handle to the ENGINE that has been set for this DH
object, or NULL if no such ENGINE has been set. This function is deprecated. All
engines should be replaced by providers.
.PP
The \fBDH_get_length()\fR and \fBDH_set_length()\fR functions get and set the optional
length parameter associated with this DH object. If the length is nonzero then
it is used, otherwise it is ignored. The \fIlength\fR parameter indicates the
length of the secret exponent (private key) in bits. For safe prime groups the optional length parameter \fIlength\fR can be
set to a value greater or equal to 2 * maximum_target_security_strength(BN_num_bits(\fIp\fR))
as listed in SP800\-56Ar3 Table(s) 25 & 26.
These functions are deprecated and should be replaced with
\&\fBEVP_PKEY_CTX_set_params()\fR and \fBEVP_PKEY_get_int_param()\fR using the parameter key
\&\fBOSSL_PKEY_PARAM_DH_PRIV_LEN\fR as described in \fBEVP_PKEY\-DH\fR\|(7).
.SH NOTES
.IX Header "NOTES"
Values retrieved with \fBDH_get0_key()\fR are owned by the DH object used
in the call and may therefore \fInot\fR be passed to \fBDH_set0_key()\fR.  If
needed, duplicate the received value using \fBBN_dup()\fR and pass the
duplicate.  The same applies to \fBDH_get0_pqg()\fR and \fBDH_set0_pqg()\fR.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBDH_set0_pqg()\fR and \fBDH_set0_key()\fR return 1 on success or 0 on failure.
.PP
\&\fBDH_get0_p()\fR, \fBDH_get0_q()\fR, \fBDH_get0_g()\fR, \fBDH_get0_priv_key()\fR, and \fBDH_get0_pub_key()\fR
return the respective value, or NULL if it is unset.
.PP
\&\fBDH_test_flags()\fR returns the current state of the flags in the DH object.
.PP
\&\fBDH_get0_engine()\fR returns the ENGINE set for the DH object or NULL if no ENGINE
has been set.
.PP
\&\fBDH_get_length()\fR returns the length of the secret exponent (private key) in bits,
or zero if no such length has been explicitly set.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBDH_new\fR\|(3), \fBDH_new\fR\|(3), \fBDH_generate_parameters\fR\|(3), \fBDH_generate_key\fR\|(3),
\&\fBDH_set_method\fR\|(3), \fBDH_size\fR\|(3), \fBDH_meth_new\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
The functions described here were added in OpenSSL 1.1.0.
.PP
All of these functions were deprecated in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2016\-2023 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
