.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "BIO_FIND_TYPE 3ossl"
.TH BIO_FIND_TYPE 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
BIO_find_type, BIO_next, BIO_method_type \- BIO chain traversal
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/bio.h>
\&
\& BIO *BIO_find_type(BIO *b, int bio_type);
\& BIO *BIO_next(BIO *b);
\& int BIO_method_type(const BIO *b);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
The \fBBIO_find_type()\fR searches for a BIO of a given type in a chain, starting
at BIO \fBb\fR. If \fBtype\fR is a specific type (such as \fBBIO_TYPE_MEM\fR) then a search
is made for a BIO of that type. If \fBtype\fR is a general type (such as
\&\fBBIO_TYPE_SOURCE_SINK\fR) then the next matching BIO of the given general type is
searched for. \fBBIO_find_type()\fR returns the next matching BIO or NULL if none is
found.
.PP
The following general types are defined:
\&\fBBIO_TYPE_DESCRIPTOR\fR, \fBBIO_TYPE_FILTER\fR, and \fBBIO_TYPE_SOURCE_SINK\fR.
.PP
For a list of the specific types, see the \fI<openssl/bio.h>\fR header file.
.PP
\&\fBBIO_next()\fR returns the next BIO in a chain. It can be used to traverse all BIOs
in a chain or used in conjunction with \fBBIO_find_type()\fR to find all BIOs of a
certain type.
.PP
\&\fBBIO_method_type()\fR returns the type of a BIO.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBBIO_find_type()\fR returns a matching BIO or NULL for no match.
.PP
\&\fBBIO_next()\fR returns the next BIO in a chain.
.PP
\&\fBBIO_method_type()\fR returns the type of the BIO \fBb\fR.
.SH EXAMPLES
.IX Header "EXAMPLES"
Traverse a chain looking for digest BIOs:
.PP
.Vb 1
\& BIO *btmp;
\&
\& btmp = in_bio; /* in_bio is chain to search through */
\& do {
\&     btmp = BIO_find_type(btmp, BIO_TYPE_MD);
\&     if (btmp == NULL)
\&         break; /* Not found */
\&     /* btmp is a digest BIO, do something with it ...*/
\&     ...
\&
\&     btmp = BIO_next(btmp);
\& } while (btmp);
.Ve
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
