.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "DTLSV1_LISTEN 3ossl"
.TH DTLSV1_LISTEN 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
SSL_stateless,
DTLSv1_listen
\&\- Statelessly listen for incoming connections
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/ssl.h>
\&
\& int SSL_stateless(SSL *s);
\& int DTLSv1_listen(SSL *ssl, BIO_ADDR *peer);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBSSL_stateless()\fR statelessly listens for new incoming TLSv1.3 connections.
\&\fBDTLSv1_listen()\fR statelessly listens for new incoming DTLS connections. If a
ClientHello is received that does not contain a cookie, then they respond with a
request for a new ClientHello that does contain a cookie. If a ClientHello is
received with a cookie that is verified then the function returns in order to
enable the handshake to be completed (for example by using \fBSSL_accept()\fR).
.SH NOTES
.IX Header "NOTES"
Some transport protocols (such as UDP) can be susceptible to amplification
attacks. Unlike TCP there is no initial connection setup in UDP that
validates that the client can actually receive messages on its advertised source
address. An attacker could forge its source IP address and then send handshake
initiation messages to the server. The server would then send its response to
the forged source IP. If the response messages are larger than the original
message then the amplification attack has succeeded.
.PP
If DTLS is used over UDP (or any datagram based protocol that does not validate
the source IP) then it is susceptible to this type of attack. TLSv1.3 is
designed to operate over a stream-based transport protocol (such as TCP).
If TCP is being used then there is no need to use \fBSSL_stateless()\fR. However, some
stream-based transport protocols (e.g. QUIC) may not validate the source
address. In this case a TLSv1.3 application would be susceptible to this attack.
.PP
As a countermeasure to this issue TLSv1.3 and DTLS include a stateless cookie
mechanism. The idea is that when a client attempts to connect to a server it
sends a ClientHello message. The server responds with a HelloRetryRequest (in
TLSv1.3) or a HelloVerifyRequest (in DTLS) which contains a unique cookie. The
client then resends the ClientHello, but this time includes the cookie in the
message thus proving that the client is capable of receiving messages sent to
that address. All of this can be done by the server without allocating any
state, and thus without consuming expensive resources.
.PP
OpenSSL implements this capability via the \fBSSL_stateless()\fR and \fBDTLSv1_listen()\fR
functions. The \fBssl\fR parameter should be a newly allocated SSL object with its
read and write BIOs set, in the same way as might be done for a call to
\&\fBSSL_accept()\fR. Typically, for DTLS, the read BIO will be in an "unconnected"
state and thus capable of receiving messages from any peer.
.PP
When a ClientHello is received that contains a cookie that has been verified,
then these functions will return with the \fBssl\fR parameter updated into a state
where the handshake can be continued by a call to (for example) \fBSSL_accept()\fR.
Additionally, for \fBDTLSv1_listen()\fR, the \fBBIO_ADDR\fR pointed to by \fBpeer\fR will be
filled in with details of the peer that sent the ClientHello. If the underlying
BIO is unable to obtain the \fBBIO_ADDR\fR of the peer (for example because the BIO
does not support this), then \fB*peer\fR will be cleared and the family set to
AF_UNSPEC. Typically user code is expected to "connect" the underlying socket to
the peer and continue the handshake in a connected state.
.PP
Warning: It is essential that the calling code connects the underlying socket to
the peer after making use of \fBDTLSv1_listen()\fR. In the typical case where
\&\fBBIO_s_datagram\fR\|(3) is used, the peer address is updated when receiving a
datagram on an unconnected socket. If the socket is not connected, it can
receive datagrams from any host on the network, which will cause subsequent
outgoing datagrams transmitted by DTLS to be transmitted to that host. In other
words, failing to call \fBBIO_connect()\fR or a similar OS-specific function on a
socket means that any host on the network can cause outgoing DTLS traffic to be
redirected to it by sending a datagram to the socket in question. This does not
break the cryptographic protections of DTLS but may facilitate a
denial-of-service attack or allow unencrypted information in the DTLS handshake
to be learned by an attacker. This is due to the historical design of
\&\fBBIO_s_datagram\fR\|(3); see \fBBIO_s_datagram\fR\|(3) for details on this issue.
.PP
Once a socket has been connected, \fBBIO_ctrl_set_connected\fR\|(3) should be used to
inform the BIO that the socket is to be used in connected mode.
.PP
Prior to calling \fBDTLSv1_listen()\fR user code must ensure that cookie generation
and verification callbacks have been set up using
\&\fBSSL_CTX_set_cookie_generate_cb\fR\|(3) and \fBSSL_CTX_set_cookie_verify_cb\fR\|(3)
respectively. For \fBSSL_stateless()\fR, \fBSSL_CTX_set_stateless_cookie_generate_cb\fR\|(3)
and \fBSSL_CTX_set_stateless_cookie_verify_cb\fR\|(3) must be used instead.
.PP
Since \fBDTLSv1_listen()\fR operates entirely statelessly whilst processing incoming
ClientHellos it is unable to process fragmented messages (since this would
require the allocation of state). An implication of this is that \fBDTLSv1_listen()\fR
\&\fBonly\fR supports ClientHellos that fit inside a single datagram.
.PP
For \fBSSL_stateless()\fR if an entire ClientHello message cannot be read without the
"read" BIO becoming empty then the \fBSSL_stateless()\fR call will fail. It is the
application's responsibility to ensure that data read from the "read" BIO during
a single \fBSSL_stateless()\fR call is all from the same peer.
.PP
\&\fBSSL_stateless()\fR will fail (with a 0 return value) if some TLS version less than
TLSv1.3 is used.
.PP
Both \fBSSL_stateless()\fR and \fBDTLSv1_listen()\fR will clear the error queue when they
start.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
For \fBSSL_stateless()\fR a return value of 1 indicates success and the \fBssl\fR object
will be set up ready to continue the handshake. A return value of 0 or \-1
indicates failure. If the value is 0 then a HelloRetryRequest was sent. A value
of \-1 indicates any other error. User code may retry the \fBSSL_stateless()\fR call.
.PP
For \fBDTLSv1_listen()\fR a return value of >= 1 indicates success. The \fBssl\fR object
will be set up ready to continue the handshake.  the \fBpeer\fR value will also be
filled in.
.PP
A return value of 0 indicates a non-fatal error. This could (for
example) be because of nonblocking IO, or some invalid message having been
received from a peer. Errors may be placed on the OpenSSL error queue with
further information if appropriate. Typically user code is expected to retry the
call to \fBDTLSv1_listen()\fR in the event of a non-fatal error.
.PP
A return value of <0 indicates a fatal error. This could (for example) be
because of a failure to allocate sufficient memory for the operation.
.PP
For \fBDTLSv1_listen()\fR, prior to OpenSSL 1.1.0, fatal and non-fatal errors both
produce return codes <= 0 (in typical implementations user code treats all
errors as non-fatal), whilst return codes >0 indicate success.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBSSL_CTX_set_cookie_generate_cb\fR\|(3), \fBSSL_CTX_set_cookie_verify_cb\fR\|(3),
\&\fBSSL_CTX_set_stateless_cookie_generate_cb\fR\|(3),
\&\fBSSL_CTX_set_stateless_cookie_verify_cb\fR\|(3), \fBSSL_get_error\fR\|(3),
\&\fBSSL_accept\fR\|(3), \fBssl\fR\|(7), \fBbio\fR\|(7)
.SH HISTORY
.IX Header "HISTORY"
The \fBSSL_stateless()\fR function was added in OpenSSL 1.1.1.
.PP
The \fBDTLSv1_listen()\fR return codes were clarified in OpenSSL 1.1.0.
The type of "peer" also changed in OpenSSL 1.1.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2015\-2023 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
