.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "EVP_PKEY_FROMDATA 3ossl"
.TH EVP_PKEY_FROMDATA 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
EVP_PKEY_fromdata_init, EVP_PKEY_fromdata, EVP_PKEY_fromdata_settable
\&\- functions to create keys and key parameters from user data
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/evp.h>
\&
\& int EVP_PKEY_fromdata_init(EVP_PKEY_CTX *ctx);
\& int EVP_PKEY_fromdata(EVP_PKEY_CTX *ctx, EVP_PKEY **ppkey, int selection,
\&                       OSSL_PARAM params[]);
\& const OSSL_PARAM *EVP_PKEY_fromdata_settable(EVP_PKEY_CTX *ctx, int selection);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
The functions described here are used to create new keys from user
provided key data, such as \fIn\fR, \fIe\fR and \fId\fR for a minimal RSA
keypair.
.PP
These functions use an \fBEVP_PKEY_CTX\fR context, which should primarily
be created with \fBEVP_PKEY_CTX_new_from_name\fR\|(3) or
\&\fBEVP_PKEY_CTX_new_id\fR\|(3).
.PP
The exact key data that the user can pass depends on the key type.
These are passed as an \fBOSSL_PARAM\fR\|(3) array.
.PP
\&\fBEVP_PKEY_fromdata_init()\fR initializes a public key algorithm context
for creating a key or key parameters from user data.
.PP
\&\fBEVP_PKEY_fromdata()\fR creates the structure to store a key or key parameters,
given data from \fIparams\fR, \fIselection\fR and a context that's been initialized
with \fBEVP_PKEY_fromdata_init()\fR.  The result is written to \fI*ppkey\fR.
\&\fIselection\fR is described in "Selections".
The parameters that can be used for various types of key are as described by the
diverse "Common parameters" sections of the
\&\fBEVP_PKEY\-RSA\fR(7),
\&\fBEVP_PKEY\-DSA\fR(7),
\&\fBEVP_PKEY\-DH\fR(7),
\&\fBEVP_PKEY\-EC\fR(7),
\&\fBEVP_PKEY\-ED448\fR(7),
\&\fBEVP_PKEY\-X25519\fR(7),
\&\fBEVP_PKEY\-X448\fR(7),
and \fBEVP_PKEY\-ED25519\fR(7) pages.
.PP
\&\fBEVP_PKEY_fromdata_settable()\fR gets a constant \fBOSSL_PARAM\fR\|(3) array that describes
the settable parameters that can be used with \fBEVP_PKEY_fromdata()\fR.
\&\fIselection\fR is described in "Selections".
.PP
Parameters in the \fIparams\fR array that are not among the settable parameters
for the given \fIselection\fR are ignored.
.SS Selections
.IX Subsection "Selections"
The following constants can be used for \fIselection\fR:
.IP \fBEVP_PKEY_KEY_PARAMETERS\fR 4
.IX Item "EVP_PKEY_KEY_PARAMETERS"
Only key parameters will be selected.
.IP \fBEVP_PKEY_PUBLIC_KEY\fR 4
.IX Item "EVP_PKEY_PUBLIC_KEY"
Only public key components will be selected. This includes optional key
parameters.
.IP \fBEVP_PKEY_KEYPAIR\fR 4
.IX Item "EVP_PKEY_KEYPAIR"
Any keypair components will be selected. This includes the private key,
public key and key parameters.
.SH NOTES
.IX Header "NOTES"
These functions only work with key management methods coming from a provider.
This is the mirror function to \fBEVP_PKEY_todata\fR\|(3).
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBEVP_PKEY_fromdata_init()\fR and \fBEVP_PKEY_fromdata()\fR return 1 for success and 0 or
a negative value for failure.  In particular a return value of \-2 indicates the
operation is not supported by the public key algorithm.
.SH EXAMPLES
.IX Header "EXAMPLES"
These examples are very terse for the sake of staying on topic, which
is the \fBEVP_PKEY_fromdata()\fR set of functions.  In real applications,
BIGNUMs would be handled and converted to byte arrays with
\&\fBBN_bn2nativepad()\fR, but that's off topic here.
.SS "Creating an RSA keypair using raw key data"
.IX Subsection "Creating an RSA keypair using raw key data"
.Vb 1
\& #include <openssl/evp.h>
\&
\& /*
\&  * These are extremely small to make this example simple.  A real
\&  * and secure application will not use such small numbers.  A real
\&  * and secure application is expected to use BIGNUMs, and to build
\&  * this array dynamically.
\&  */
\& unsigned long rsa_n = 0xbc747fc5;
\& unsigned long rsa_e = 0x10001;
\& unsigned long rsa_d = 0x7b133399;
\& OSSL_PARAM params[] = {
\&     OSSL_PARAM_ulong("n", &rsa_n),
\&     OSSL_PARAM_ulong("e", &rsa_e),
\&     OSSL_PARAM_ulong("d", &rsa_d),
\&     OSSL_PARAM_END
\& };
\&
\& int main()
\& {
\&     EVP_PKEY_CTX *ctx = EVP_PKEY_CTX_new_from_name(NULL, "RSA", NULL);
\&     EVP_PKEY *pkey = NULL;
\&
\&     if (ctx == NULL
\&         || EVP_PKEY_fromdata_init(ctx) <= 0
\&         || EVP_PKEY_fromdata(ctx, &pkey, EVP_PKEY_KEYPAIR, params) <= 0)
\&         exit(1);
\&
\&     /* Do what you want with |pkey| */
\& }
.Ve
.SS "Creating an ECC keypair using raw key data"
.IX Subsection "Creating an ECC keypair using raw key data"
.Vb 3
\& #include <openssl/evp.h>
\& #include <openssl/param_build.h>
\& #include <openssl/ec.h>
\&
\& /*
\&  * Fixed data to represent the private and public key.
\&  */
\& const unsigned char priv_data[] = {
\&     0xb9, 0x2f, 0x3c, 0xe6, 0x2f, 0xfb, 0x45, 0x68,
\&     0x39, 0x96, 0xf0, 0x2a, 0xaf, 0x6c, 0xda, 0xf2,
\&     0x89, 0x8a, 0x27, 0xbf, 0x39, 0x9b, 0x7e, 0x54,
\&     0x21, 0xc2, 0xa1, 0xe5, 0x36, 0x12, 0x48, 0x5d
\& };
\& /* UNCOMPRESSED FORMAT */
\& const unsigned char pub_data[] = {
\&     POINT_CONVERSION_UNCOMPRESSED,
\&     0xcf, 0x20, 0xfb, 0x9a, 0x1d, 0x11, 0x6c, 0x5e,
\&     0x9f, 0xec, 0x38, 0x87, 0x6c, 0x1d, 0x2f, 0x58,
\&     0x47, 0xab, 0xa3, 0x9b, 0x79, 0x23, 0xe6, 0xeb,
\&     0x94, 0x6f, 0x97, 0xdb, 0xa3, 0x7d, 0xbd, 0xe5,
\&     0x26, 0xca, 0x07, 0x17, 0x8d, 0x26, 0x75, 0xff,
\&     0xcb, 0x8e, 0xb6, 0x84, 0xd0, 0x24, 0x02, 0x25,
\&     0x8f, 0xb9, 0x33, 0x6e, 0xcf, 0x12, 0x16, 0x2f,
\&     0x5c, 0xcd, 0x86, 0x71, 0xa8, 0xbf, 0x1a, 0x47
\& };
\&
\& int main()
\& {
\&     EVP_PKEY_CTX *ctx;
\&     EVP_PKEY *pkey = NULL;
\&     BIGNUM *priv;
\&     OSSL_PARAM_BLD *param_bld;
\&     OSSL_PARAM *params = NULL;
\&     int exitcode = 0;
\&
\&     priv = BN_bin2bn(priv_data, sizeof(priv_data), NULL);
\&
\&     param_bld = OSSL_PARAM_BLD_new();
\&     if (priv != NULL && param_bld != NULL
\&         && OSSL_PARAM_BLD_push_utf8_string(param_bld, "group",
\&                                            "prime256v1", 0)
\&         && OSSL_PARAM_BLD_push_BN(param_bld, "priv", priv)
\&         && OSSL_PARAM_BLD_push_octet_string(param_bld, "pub",
\&                                             pub_data, sizeof(pub_data)))
\&         params = OSSL_PARAM_BLD_to_param(param_bld);
\&
\&     ctx = EVP_PKEY_CTX_new_from_name(NULL, "EC", NULL);
\&     if (ctx == NULL
\&         || params == NULL
\&         || EVP_PKEY_fromdata_init(ctx) <= 0
\&         || EVP_PKEY_fromdata(ctx, &pkey, EVP_PKEY_KEYPAIR, params) <= 0) {
\&         exitcode = 1;
\&     } else {
\&         /* Do what you want with |pkey| */
\&     }
\&
\&     EVP_PKEY_free(pkey);
\&     EVP_PKEY_CTX_free(ctx);
\&     OSSL_PARAM_free(params);
\&     OSSL_PARAM_BLD_free(param_bld);
\&     BN_free(priv);
\&
\&     exit(exitcode);
\& }
.Ve
.SS "Finding out params for an unknown key type"
.IX Subsection "Finding out params for an unknown key type"
.Vb 2
\& #include <openssl/evp.h>
\& #include <openssl/core.h>
\&
\& /* Program expects a key type as first argument */
\& int main(int argc, char *argv[])
\& {
\&     EVP_PKEY_CTX *ctx = EVP_PKEY_CTX_new_from_name(NULL, argv[1], NULL);
\&     const OSSL_PARAM *settable_params = NULL;
\&
\&     if (ctx == NULL)
\&        exit(1);
\&    settable_params = EVP_PKEY_fromdata_settable(ctx, EVP_PKEY_KEYPAIR);
\&    if (settable_params == NULL)
\&         exit(1);
\&
\&     for (; settable_params\->key != NULL; settable_params++) {
\&         const char *datatype = NULL;
\&
\&         switch (settable_params\->data_type) {
\&         case OSSL_PARAM_INTEGER:
\&             datatype = "integer";
\&             break;
\&         case OSSL_PARAM_UNSIGNED_INTEGER:
\&             datatype = "unsigned integer";
\&             break;
\&         case OSSL_PARAM_UTF8_STRING:
\&             datatype = "printable string (utf\-8 encoding expected)";
\&             break;
\&         case OSSL_PARAM_UTF8_PTR:
\&             datatype = "printable string pointer (utf\-8 encoding expected)";
\&             break;
\&         case OSSL_PARAM_OCTET_STRING:
\&             datatype = "octet string";
\&             break;
\&         case OSSL_PARAM_OCTET_PTR:
\&             datatype = "octet string pointer";
\&             break;
\&         }
\&         printf("%s : %s ", settable_params\->key, datatype);
\&         if (settable_params\->data_size == 0)
\&             printf("(unlimited size)\en");
\&         else
\&             printf("(maximum size %zu)\en", settable_params\->data_size);
\&     }
\& }
.Ve
.PP
The descriptor \fBOSSL_PARAM\fR\|(3) returned by
\&\fBEVP_PKEY_fromdata_settable()\fR may also be used programmatically, for
example with \fBOSSL_PARAM_allocate_from_text\fR\|(3).
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBEVP_PKEY_CTX_new\fR\|(3), \fBprovider\fR\|(7), \fBEVP_PKEY_gettable_params\fR\|(3),
\&\fBOSSL_PARAM\fR\|(3), \fBEVP_PKEY_todata\fR\|(3),
\&\fBEVP_PKEY\-RSA\fR\|(7), \fBEVP_PKEY\-DSA\fR\|(7), \fBEVP_PKEY\-DH\fR\|(7), \fBEVP_PKEY\-EC\fR\|(7),
\&\fBEVP_PKEY\-ED448\fR\|(7), \fBEVP_PKEY\-X25519\fR\|(7), \fBEVP_PKEY\-X448\fR\|(7),
\&\fBEVP_PKEY\-ED25519\fR\|(7)
.SH HISTORY
.IX Header "HISTORY"
These functions were added in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2019\-2022 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
