.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "SSL_CTX_SET_MSG_CALLBACK 3ossl"
.TH SSL_CTX_SET_MSG_CALLBACK 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
SSL_CTX_set_msg_callback,
SSL_CTX_set_msg_callback_arg,
SSL_set_msg_callback,
SSL_set_msg_callback_arg
\&\- install callback for observing protocol messages
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/ssl.h>
\&
\& void SSL_CTX_set_msg_callback(SSL_CTX *ctx,
\&                               void (*cb)(int write_p, int version,
\&                                          int content_type, const void *buf,
\&                                          size_t len, SSL *ssl, void *arg));
\& void SSL_CTX_set_msg_callback_arg(SSL_CTX *ctx, void *arg);
\&
\& void SSL_set_msg_callback(SSL *ssl,
\&                           void (*cb)(int write_p, int version,
\&                                      int content_type, const void *buf,
\&                                      size_t len, SSL *ssl, void *arg));
\& void SSL_set_msg_callback_arg(SSL *ssl, void *arg);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBSSL_CTX_set_msg_callback()\fR or \fBSSL_set_msg_callback()\fR can be used to
define a message callback function \fIcb\fR for observing all SSL/TLS
protocol messages (such as handshake messages) that are received or
sent, as well as other events that occur during processing.
\&\fBSSL_CTX_set_msg_callback_arg()\fR and \fBSSL_set_msg_callback_arg()\fR
can be used to set argument \fIarg\fR to the callback function, which is
available for arbitrary application use.
.PP
\&\fBSSL_CTX_set_msg_callback()\fR and \fBSSL_CTX_set_msg_callback_arg()\fR specify
default settings that will be copied to new \fBSSL\fR objects by
\&\fBSSL_new\fR\|(3). \fBSSL_set_msg_callback()\fR and
\&\fBSSL_set_msg_callback_arg()\fR modify the actual settings of an \fBSSL\fR
object. Using a \fBNULL\fR pointer for \fIcb\fR disables the message callback.
.PP
When \fIcb\fR is called by the SSL/TLS library the function arguments have the
following meaning:
.IP \fIwrite_p\fR 4
.IX Item "write_p"
This flag is \fB0\fR when a protocol message has been received and \fB1\fR
when a protocol message has been sent.
.IP \fIversion\fR 4
.IX Item "version"
The protocol version according to which the protocol message is
interpreted by the library such as \fBTLS1_3_VERSION\fR, \fBTLS1_2_VERSION\fR etc.
This is set to 0 for the SSL3_RT_HEADER pseudo content type (see NOTES below).
.IP \fIcontent_type\fR 4
.IX Item "content_type"
This is one of the content type values defined in the protocol specification
(\fBSSL3_RT_CHANGE_CIPHER_SPEC\fR, \fBSSL3_RT_ALERT\fR, \fBSSL3_RT_HANDSHAKE\fR; but never
\&\fBSSL3_RT_APPLICATION_DATA\fR because the callback will only be called for protocol
messages). Alternatively it may be a "pseudo" content type. These pseudo
content types are used to signal some other event in the processing of data (see
NOTES below).
.IP "\fIbuf\fR, \fIlen\fR" 4
.IX Item "buf, len"
\&\fIbuf\fR points to a buffer containing the protocol message or other data (in the
case of pseudo content types), which consists of \fIlen\fR bytes. The buffer is no
longer valid after the callback function has returned.
.IP \fIssl\fR 4
.IX Item "ssl"
The \fBSSL\fR object that received or sent the message.
.IP \fIarg\fR 4
.IX Item "arg"
The user-defined argument optionally defined by
\&\fBSSL_CTX_set_msg_callback_arg()\fR or \fBSSL_set_msg_callback_arg()\fR.
.SH NOTES
.IX Header "NOTES"
Protocol messages are passed to the callback function after decryption
and fragment collection where applicable. (Thus record boundaries are
not visible.)
.PP
If processing a received protocol message results in an error,
the callback function may not be called.  For example, the callback
function will never see messages that are considered too large to be
processed.
.PP
Due to automatic protocol version negotiation, \fIversion\fR is not
necessarily the protocol version used by the sender of the message: If
a TLS 1.0 ClientHello message is received by an SSL 3.0\-only server,
\&\fIversion\fR will be \fBSSL3_VERSION\fR.
.PP
Pseudo content type values may be sent at various points during the processing
of data. The following pseudo content types are currently defined:
.IP \fBSSL3_RT_HEADER\fR 4
.IX Item "SSL3_RT_HEADER"
Used when a record is sent or received. The \fBbuf\fR contains the record header
bytes only.
.IP \fBSSL3_RT_INNER_CONTENT_TYPE\fR 4
.IX Item "SSL3_RT_INNER_CONTENT_TYPE"
Used when an encrypted TLSv1.3 record is sent or received. In encrypted TLSv1.3
records the content type in the record header is always
SSL3_RT_APPLICATION_DATA. The real content type for the record is contained in
an "inner" content type. \fBbuf\fR contains the encoded "inner" content type byte.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBSSL_CTX_set_msg_callback()\fR, \fBSSL_CTX_set_msg_callback_arg()\fR, \fBSSL_set_msg_callback()\fR
and \fBSSL_set_msg_callback_arg()\fR do not return values.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBssl\fR\|(7), \fBSSL_new\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
The pseudo content type \fBSSL3_RT_INNER_CONTENT_TYPE\fR was added in OpenSSL 1.1.1.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2001\-2018 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
