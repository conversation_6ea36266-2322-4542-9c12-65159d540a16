.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "EVP_PKEY_DIGESTSIGN_SUPPORTS_DIGEST 3ossl"
.TH EVP_PKEY_DIGESTSIGN_SUPPORTS_DIGEST 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
EVP_PKEY_digestsign_supports_digest \- indicate support for signature digest
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 3
\& #include <openssl/evp.h>
\& int EVP_PKEY_digestsign_supports_digest(EVP_PKEY *pkey, OSSL_LIB_CTX *libctx,
\&                                         const char *name, const char *propq);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
The \fBEVP_PKEY_digestsign_supports_digest()\fR function queries whether the message
digest \fIname\fR is supported for public key signature operations associated with
key \fIpkey\fR. The query is done within an optional library context \fIlibctx\fR and
with an optional property query \fIpropq\fR.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
The \fBEVP_PKEY_digestsign_supports_digest()\fR function returns 1 if the message
digest algorithm identified by \fIname\fR can be used for public key signature
operations associated with key \fIpkey\fR and 0 if it cannot be used. It returns
a negative value for failure.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBEVP_DigestSignInit_ex\fR\|(3),
.SH HISTORY
.IX Header "HISTORY"
The \fBEVP_PKEY_digestsign_supports_digest()\fR function was added in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
