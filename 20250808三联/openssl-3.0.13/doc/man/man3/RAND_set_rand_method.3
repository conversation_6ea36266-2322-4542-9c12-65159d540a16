.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "RAND_SET_RAND_METHOD 3ossl"
.TH RAND_SET_RAND_METHOD 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
RAND_set_rand_method, RAND_get_rand_method, RAND_OpenSSL \- select RAND method
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/rand.h>
.Ve
.PP
The following functions have been deprecated since OpenSSL 3.0, and can be
hidden entirely by defining \fBOPENSSL_API_COMPAT\fR with a suitable version value,
see \fBopenssl_user_macros\fR\|(7):
.PP
.Vb 1
\& RAND_METHOD *RAND_OpenSSL(void);
\&
\& int RAND_set_rand_method(const RAND_METHOD *meth);
\&
\& const RAND_METHOD *RAND_get_rand_method(void);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
All of the functions described on this page are deprecated.
Applications should instead use \fBRAND_set_DRBG_type\fR\|(3),
\&\fBEVP_RAND\fR\|(3) and \fBEVP_RAND\fR\|(7).
.PP
A \fBRAND_METHOD\fR specifies the functions that OpenSSL uses for random number
generation.
.PP
\&\fBRAND_OpenSSL()\fR returns the default \fBRAND_METHOD\fR implementation by OpenSSL.
This implementation ensures that the PRNG state is unique for each thread.
.PP
If an \fBENGINE\fR is loaded that provides the RAND API, however, it will
be used instead of the method returned by \fBRAND_OpenSSL()\fR.  This is deprecated
in OpenSSL 3.0.
.PP
\&\fBRAND_set_rand_method()\fR makes \fBmeth\fR the method for PRNG use.  If an
ENGINE was providing the method, it will be released first.
.PP
\&\fBRAND_get_rand_method()\fR returns a pointer to the current \fBRAND_METHOD\fR.
.SH "THE RAND_METHOD STRUCTURE"
.IX Header "THE RAND_METHOD STRUCTURE"
.Vb 8
\& typedef struct rand_meth_st {
\&     int (*seed)(const void *buf, int num);
\&     int (*bytes)(unsigned char *buf, int num);
\&     void (*cleanup)(void);
\&     int (*add)(const void *buf, int num, double entropy);
\&     int (*pseudorand)(unsigned char *buf, int num);
\&     int (*status)(void);
\& } RAND_METHOD;
.Ve
.PP
The fields point to functions that are used by, in order,
\&\fBRAND_seed()\fR, \fBRAND_bytes()\fR, internal RAND cleanup, \fBRAND_add()\fR, \fBRAND_pseudo_rand()\fR
and \fBRAND_status()\fR.
Each pointer may be NULL if the function is not implemented.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBRAND_set_rand_method()\fR returns 1 on success and 0 on failure.
\&\fBRAND_get_rand_method()\fR and \fBRAND_OpenSSL()\fR return pointers to the respective
methods.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBEVP_RAND\fR\|(3),
\&\fBRAND_set_DRBG_type\fR\|(3),
\&\fBRAND_bytes\fR\|(3),
\&\fBENGINE_by_id\fR\|(3),
\&\fBEVP_RAND\fR\|(7),
\&\fBRAND\fR\|(7)
.SH HISTORY
.IX Header "HISTORY"
All of these functions were deprecated in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
