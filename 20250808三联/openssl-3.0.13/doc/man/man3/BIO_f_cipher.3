.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "BIO_F_CIPHER 3ossl"
.TH BIO_F_CIPHER 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
BIO_f_cipher, BIO_set_cipher, BIO_get_cipher_status, BIO_get_cipher_ctx \- cipher BIO filter
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 2
\& #include <openssl/bio.h>
\& #include <openssl/evp.h>
\&
\& const BIO_METHOD *BIO_f_cipher(void);
\& int BIO_set_cipher(BIO *b, const EVP_CIPHER *cipher,
\&                    const unsigned char *key, const unsigned char *iv, int enc);
\& int BIO_get_cipher_status(BIO *b);
\& int BIO_get_cipher_ctx(BIO *b, EVP_CIPHER_CTX **pctx);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBBIO_f_cipher()\fR returns the cipher BIO method. This is a filter
BIO that encrypts any data written through it, and decrypts any data
read from it. It is a BIO wrapper for the cipher routines
\&\fBEVP_CipherInit()\fR, \fBEVP_CipherUpdate()\fR and \fBEVP_CipherFinal()\fR.
.PP
Cipher BIOs do not support \fBBIO_gets()\fR or \fBBIO_puts()\fR.
.PP
\&\fBBIO_flush()\fR on an encryption BIO that is being written through is
used to signal that no more data is to be encrypted: this is used
to flush and possibly pad the final block through the BIO.
.PP
\&\fBBIO_set_cipher()\fR sets the cipher of BIO \fBb\fR to \fBcipher\fR using key \fBkey\fR
and IV \fBiv\fR. \fBenc\fR should be set to 1 for encryption and zero for
decryption.
.PP
When reading from an encryption BIO the final block is automatically
decrypted and checked when EOF is detected. \fBBIO_get_cipher_status()\fR
is a \fBBIO_ctrl()\fR macro which can be called to determine whether the
decryption operation was successful.
.PP
\&\fBBIO_get_cipher_ctx()\fR is a \fBBIO_ctrl()\fR macro which retrieves the internal
BIO cipher context. The retrieved context can be used in conjunction
with the standard cipher routines to set it up. This is useful when
\&\fBBIO_set_cipher()\fR is not flexible enough for the applications needs.
.SH NOTES
.IX Header "NOTES"
When encrypting \fBBIO_flush()\fR \fBmust\fR be called to flush the final block
through the BIO. If it is not then the final block will fail a subsequent
decrypt.
.PP
When decrypting an error on the final block is signaled by a zero
return value from the read operation. A successful decrypt followed
by EOF will also return zero for the final read. \fBBIO_get_cipher_status()\fR
should be called to determine if the decrypt was successful.
.PP
As always, if \fBBIO_gets()\fR or \fBBIO_puts()\fR support is needed then it can
be achieved by preceding the cipher BIO with a buffering BIO.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBBIO_f_cipher()\fR returns the cipher BIO method.
.PP
\&\fBBIO_set_cipher()\fR returns 1 for success and 0 for failure.
.PP
\&\fBBIO_get_cipher_status()\fR returns 1 for a successful decrypt and <=0
for failure.
.PP
\&\fBBIO_get_cipher_ctx()\fR returns 1 for success and <=0 for failure.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
