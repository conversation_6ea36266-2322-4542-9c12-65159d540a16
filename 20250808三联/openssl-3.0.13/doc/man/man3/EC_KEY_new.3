.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "EC_KEY_NEW 3ossl"
.TH EC_KEY_NEW 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
EVP_EC_gen,
EC_KEY_get_method, EC_KEY_set_method, EC_KEY_new_ex,
EC_KEY_new, EC_KEY_get_flags, EC_KEY_set_flags, EC_KEY_clear_flags,
EC_KEY_new_by_curve_name_ex, EC_KEY_new_by_curve_name, EC_KEY_free,
EC_KEY_copy, EC_KEY_dup, EC_KEY_up_ref, EC_KEY_get0_engine,
EC_KEY_get0_group, EC_KEY_set_group, EC_KEY_get0_private_key,
EC_KEY_set_private_key, EC_KEY_get0_public_key, EC_KEY_set_public_key,
EC_KEY_get_conv_form,
EC_KEY_set_conv_form, EC_KEY_set_asn1_flag,
EC_KEY_decoded_from_explicit_params, EC_KEY_precompute_mult,
EC_KEY_generate_key, EC_KEY_check_key, EC_KEY_set_public_key_affine_coordinates,
EC_KEY_oct2key, EC_KEY_key2buf, EC_KEY_oct2priv, EC_KEY_priv2oct,
EC_KEY_priv2buf \- Functions for creating, destroying and manipulating
EC_KEY objects
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/ec.h>
\&
\& EVP_PKEY *EVP_EC_gen(const char *curve);
.Ve
.PP
The following functions have been deprecated since OpenSSL 3.0, and can be
hidden entirely by defining \fBOPENSSL_API_COMPAT\fR with a suitable version value,
see \fBopenssl_user_macros\fR\|(7):
.PP
.Vb 10
\& EC_KEY *EC_KEY_new_ex(OSSL_LIB_CTX *ctx, const char *propq);
\& EC_KEY *EC_KEY_new(void);
\& int EC_KEY_get_flags(const EC_KEY *key);
\& void EC_KEY_set_flags(EC_KEY *key, int flags);
\& void EC_KEY_clear_flags(EC_KEY *key, int flags);
\& EC_KEY *EC_KEY_new_by_curve_name_ex(OSSL_LIB_CTX *ctx, const char *propq,
\&                                     int nid);
\& EC_KEY *EC_KEY_new_by_curve_name(int nid);
\& void EC_KEY_free(EC_KEY *key);
\& EC_KEY *EC_KEY_copy(EC_KEY *dst, const EC_KEY *src);
\& EC_KEY *EC_KEY_dup(const EC_KEY *src);
\& int EC_KEY_up_ref(EC_KEY *key);
\& ENGINE *EC_KEY_get0_engine(const EC_KEY *eckey);
\& const EC_GROUP *EC_KEY_get0_group(const EC_KEY *key);
\& int EC_KEY_set_group(EC_KEY *key, const EC_GROUP *group);
\& const BIGNUM *EC_KEY_get0_private_key(const EC_KEY *key);
\& int EC_KEY_set_private_key(EC_KEY *key, const BIGNUM *priv_key);
\& const EC_POINT *EC_KEY_get0_public_key(const EC_KEY *key);
\& int EC_KEY_set_public_key(EC_KEY *key, const EC_POINT *pub);
\& point_conversion_form_t EC_KEY_get_conv_form(const EC_KEY *key);
\& void EC_KEY_set_conv_form(EC_KEY *eckey, point_conversion_form_t cform);
\& void EC_KEY_set_asn1_flag(EC_KEY *eckey, int asn1_flag);
\& int EC_KEY_decoded_from_explicit_params(const EC_KEY *key);
\& int EC_KEY_generate_key(EC_KEY *key);
\& int EC_KEY_check_key(const EC_KEY *key);
\& int EC_KEY_set_public_key_affine_coordinates(EC_KEY *key, BIGNUM *x, BIGNUM *y);
\& const EC_KEY_METHOD *EC_KEY_get_method(const EC_KEY *key);
\& int EC_KEY_set_method(EC_KEY *key, const EC_KEY_METHOD *meth);
\&
\& int EC_KEY_oct2key(EC_KEY *eckey, const unsigned char *buf, size_t len, BN_CTX *ctx);
\& size_t EC_KEY_key2buf(const EC_KEY *eckey, point_conversion_form_t form,
\&                       unsigned char **pbuf, BN_CTX *ctx);
\&
\& int EC_KEY_oct2priv(EC_KEY *eckey, const unsigned char *buf, size_t len);
\& size_t EC_KEY_priv2oct(const EC_KEY *eckey, unsigned char *buf, size_t len);
\&
\& size_t EC_KEY_priv2buf(const EC_KEY *eckey, unsigned char **pbuf);
\& int EC_KEY_precompute_mult(EC_KEY *key, BN_CTX *ctx);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBEVP_EC_gen()\fR generates a new EC key pair on the given \fIcurve\fR.
.PP
All of the functions described below are deprecated.
Applications should instead use \fBEVP_EC_gen()\fR, \fBEVP_PKEY_Q_keygen\fR\|(3), or
\&\fBEVP_PKEY_keygen_init\fR\|(3) and \fBEVP_PKEY_keygen\fR\|(3).
.PP
An EC_KEY represents a public key and, optionally, the associated private
key.
A new EC_KEY with no associated curve can be constructed by calling
\&\fBEC_KEY_new_ex()\fR and specifying the associated library context in \fIctx\fR
(see \fBOSSL_LIB_CTX\fR\|(3)) and property query string \fIpropq\fR.
The \fIctx\fR parameter may be NULL in which case the default library context is
used.
The reference count for the newly created EC_KEY is initially
set to 1.
A curve can be associated with the EC_KEY by calling
\&\fBEC_KEY_set_group()\fR.
.PP
\&\fBEC_KEY_new()\fR is the same as \fBEC_KEY_new_ex()\fR except that the default library
context is always used.
.PP
Alternatively a new EC_KEY can be constructed by calling
\&\fBEC_KEY_new_by_curve_name_ex()\fR and supplying the nid of the associated
curve, the library context to be used \fIctx\fR (see \fBOSSL_LIB_CTX\fR\|(3)) and any
property query string \fIpropq\fR.
The \fIctx\fR parameter may be NULL in which case the default library context is
used. The \fIpropq\fR value may also be NULL.
See \fBEC_GROUP_new\fR\|(3) for a description of curve names.
This function simply wraps calls to \fBEC_KEY_new_ex()\fR and
\&\fBEC_GROUP_new_by_curve_name_ex()\fR.
.PP
\&\fBEC_KEY_new_by_curve_name()\fR is the same as \fBEC_KEY_new_by_curve_name_ex()\fR
except that the default library context is always used and a NULL property query
string.
.PP
Calling \fBEC_KEY_free()\fR decrements the reference count for the EC_KEY object,
and if it has dropped to zero then frees the memory associated with it.  If
\&\fIkey\fR is NULL nothing is done.
.PP
\&\fBEC_KEY_copy()\fR copies the contents of the EC_KEY in \fIsrc\fR into \fIdest\fR.
.PP
\&\fBEC_KEY_dup()\fR creates a new EC_KEY object and copies \fIec_key\fR into it.
.PP
\&\fBEC_KEY_up_ref()\fR increments the reference count associated with the EC_KEY
object.
.PP
\&\fBEC_KEY_get0_engine()\fR returns a handle to the ENGINE that has been set for
this EC_KEY object.
.PP
\&\fBEC_KEY_generate_key()\fR generates a new public and private key for the supplied
\&\fIeckey\fR object. \fIeckey\fR must have an EC_GROUP object associated with it
before calling this function. The private key is a random integer (0 < priv_key
< order, where \fIorder\fR is the order of the EC_GROUP object). The public key is
an EC_POINT on the curve calculated by multiplying the generator for the
curve by the private key.
.PP
\&\fBEC_KEY_check_key()\fR performs various sanity checks on the EC_KEY object to
confirm that it is valid.
.PP
\&\fBEC_KEY_set_public_key_affine_coordinates()\fR sets the public key for \fIkey\fR based
on its affine coordinates; i.e., it constructs an EC_POINT object based on
the supplied \fIx\fR and \fIy\fR values and sets the public key to be this
EC_POINT. It also performs certain sanity checks on the key to confirm
that it is valid.
.PP
The functions \fBEC_KEY_get0_group()\fR, \fBEC_KEY_set_group()\fR,
\&\fBEC_KEY_get0_private_key()\fR, \fBEC_KEY_set_private_key()\fR, \fBEC_KEY_get0_public_key()\fR,
and \fBEC_KEY_set_public_key()\fR get and set the EC_GROUP object, the private key,
and the EC_POINT public key for the \fBkey\fR respectively. The function
\&\fBEC_KEY_set_private_key()\fR accepts NULL as the priv_key argument to securely clear
the private key component from the EC_KEY.
.PP
The functions \fBEC_KEY_get_conv_form()\fR and \fBEC_KEY_set_conv_form()\fR get and set the
point_conversion_form for the \fIkey\fR. For a description of
point_conversion_forms please see \fBEC_POINT_new\fR\|(3).
.PP
\&\fBEC_KEY_set_flags()\fR sets the flags in the \fIflags\fR parameter on the EC_KEY
object. Any flags that are already set are left set. The flags currently
defined are EC_FLAG_NON_FIPS_ALLOW and EC_FLAG_FIPS_CHECKED. In
addition there is the flag EC_FLAG_COFACTOR_ECDH which is specific to ECDH.
\&\fBEC_KEY_get_flags()\fR returns the current flags that are set for this EC_KEY.
\&\fBEC_KEY_clear_flags()\fR clears the flags indicated by the \fIflags\fR parameter; all
other flags are left in their existing state.
.PP
\&\fBEC_KEY_set_asn1_flag()\fR sets the asn1_flag on the underlying EC_GROUP object
(if set). Refer to \fBEC_GROUP_copy\fR\|(3) for further information on the
asn1_flag.
.PP
\&\fBEC_KEY_decoded_from_explicit_params()\fR returns 1 if the group of the \fIkey\fR was
decoded from data with explicitly encoded group parameters, \-1 if the \fIkey\fR
is NULL or the group parameters are missing, and 0 otherwise.
.PP
\&\fBEC_KEY_precompute_mult()\fR stores multiples of the underlying EC_GROUP generator
for faster point multiplication. See also \fBEC_POINT_add\fR\|(3).
Modern versions should instead switch to named curves which OpenSSL has
hardcoded lookup tables for.
.PP
\&\fBEC_KEY_oct2key()\fR and \fBEC_KEY_key2buf()\fR are identical to the functions
\&\fBEC_POINT_oct2point()\fR and \fBEC_POINT_point2buf()\fR except they use the public key
EC_POINT in \fIeckey\fR.
.PP
\&\fBEC_KEY_oct2priv()\fR and \fBEC_KEY_priv2oct()\fR convert between the private key
component of \fIeckey\fR and octet form. The octet form consists of the content
octets of the \fIprivateKey\fR OCTET STRING in an \fIECPrivateKey\fR ASN.1 structure.
.PP
The function \fBEC_KEY_priv2oct()\fR must be supplied with a buffer long enough to
store the octet form. The return value provides the number of octets stored.
Calling the function with a NULL buffer will not perform the conversion but
will just return the required buffer length.
.PP
The function \fBEC_KEY_priv2buf()\fR allocates a buffer of suitable length and writes
an EC_KEY to it in octet format. The allocated buffer is written to \fI*pbuf\fR
and its length is returned. The caller must free up the allocated buffer with a
call to \fBOPENSSL_free()\fR. Since the allocated buffer value is written to \fI*pbuf\fR
the \fIpbuf\fR parameter \fBMUST NOT\fR be \fBNULL\fR.
.PP
\&\fBEC_KEY_priv2buf()\fR converts an EC_KEY private key into an allocated buffer.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBEC_KEY_new_ex()\fR, \fBEC_KEY_new()\fR, \fBEC_KEY_new_by_curve_name_ex()\fR,
\&\fBEC_KEY_new_by_curve_name()\fR and \fBEC_KEY_dup()\fR return a pointer to the newly
created EC_KEY object, or NULL on error.
.PP
\&\fBEC_KEY_get_flags()\fR returns the flags associated with the EC_KEY object as an
integer.
.PP
\&\fBEC_KEY_copy()\fR returns a pointer to the destination key, or NULL on error.
.PP
\&\fBEC_KEY_get0_engine()\fR returns a pointer to an ENGINE, or NULL if it wasn't set.
.PP
\&\fBEC_KEY_up_ref()\fR, \fBEC_KEY_set_group()\fR, \fBEC_KEY_set_public_key()\fR,
\&\fBEC_KEY_precompute_mult()\fR, \fBEC_KEY_generate_key()\fR, \fBEC_KEY_check_key()\fR,
\&\fBEC_KEY_set_public_key_affine_coordinates()\fR, \fBEC_KEY_oct2key()\fR and
\&\fBEC_KEY_oct2priv()\fR return 1 on success or 0 on error.
.PP
\&\fBEC_KEY_set_private_key()\fR returns 1 on success or 0 on error except when the
priv_key argument is NULL, in that case it returns 0, for legacy compatibility,
and should not be treated as an error.
.PP
\&\fBEC_KEY_get0_group()\fR returns the EC_GROUP associated with the EC_KEY.
.PP
\&\fBEC_KEY_get0_private_key()\fR returns the private key associated with the EC_KEY.
.PP
\&\fBEC_KEY_get_conv_form()\fR return the point_conversion_form for the EC_KEY.
.PP
\&\fBEC_KEY_key2buf()\fR, \fBEC_KEY_priv2oct()\fR and \fBEC_KEY_priv2buf()\fR return the length
of the buffer or 0 on error.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBEVP_PKEY_Q_keygen\fR\|(3)
\&\fBcrypto\fR\|(7), \fBEC_GROUP_new\fR\|(3),
\&\fBEC_GROUP_copy\fR\|(3), \fBEC_POINT_new\fR\|(3),
\&\fBEC_POINT_add\fR\|(3),
\&\fBEC_GFp_simple_method\fR\|(3),
\&\fBd2i_ECPKParameters\fR\|(3),
\&\fBOSSL_LIB_CTX\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
\&\fBEVP_EC_gen()\fR was added in OpenSSL 3.0.
All other functions described here were deprecated in OpenSSL 3.0.
For replacement see \fBEVP_PKEY\-EC\fR\|(7).
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2013\-2023 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
