.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "X509_ADD_CERT 3ossl"
.TH X509_ADD_CERT 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
X509_add_cert,
X509_add_certs \-
X509 certificate list addition functions
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/x509.h>
\&
\& int X509_add_cert(STACK_OF(X509) *sk, X509 *cert, int flags);
\& int X509_add_certs(STACK_OF(X509) *sk, STACK_OF(X509) *certs, int flags);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBX509_add_cert()\fR adds a certificate \fIcert\fR to the given list \fIsk\fR.
.PP
\&\fBX509_add_certs()\fR adds a list of certificate \fIcerts\fR to the given list \fIsk\fR.
The \fIcerts\fR argument may be NULL, which implies no effect.
It does not modify the list \fIcerts\fR but
in case the \fBX509_ADD_FLAG_UP_REF\fR flag (described below) is set
the reference counters of those of its members added to \fIsk\fR are increased.
.PP
Both these functions have a \fIflags\fR parameter,
which is used to control details of the operation.
.PP
The value \fBX509_ADD_FLAG_DEFAULT\fR, which equals 0, means no special semantics.
.PP
If \fBX509_ADD_FLAG_UP_REF\fR is set then
the reference counts of those certificates added successfully are increased.
.PP
If \fBX509_ADD_FLAG_PREPEND\fR is set then the certificates are prepended to \fIsk\fR.
By default they are appended to \fIsk\fR.
In both cases the original order of the added certificates is preserved.
.PP
If \fBX509_ADD_FLAG_NO_DUP\fR is set then certificates already contained in \fIsk\fR,
which is determined using \fBX509_cmp\fR\|(3), are ignored.
.PP
If \fBX509_ADD_FLAG_NO_SS\fR is set then certificates that are marked self-signed,
which is determined using \fBX509_self_signed\fR\|(3), are ignored.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
Both functions return 1 for success and 0 for failure.
.SH NOTES
.IX Header "NOTES"
If \fBX509_add_certs()\fR is used with the flags \fBX509_ADD_FLAG_NO_DUP\fR or
\&\fBX509_ADD_FLAG_NO_SS\fR it is advisable to use also \fBX509_ADD_FLAG_UP_REF\fR
because otherwise likely not for all members of the \fIcerts\fR list
the ownership is transferred to the list of certificates \fIsk\fR.
.PP
Care should also be taken in case the \fIcerts\fR argument equals \fIsk\fR.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBX509_cmp\fR\|(3)
\&\fBX509_self_signed\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
The functions \fBX509_add_cert()\fR and \fBX509_add_certs()\fR
were added in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2019\-2023 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
