.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "TS_VERIFY_CTX_SET_CERTS 3ossl"
.TH TS_VERIFY_CTX_SET_CERTS 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
TS_VERIFY_CTX_set_certs, TS_VERIFY_CTS_set_certs
\&\- set certificates for TS response verification
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/ts.h>
\&
\& STACK_OF(X509) *TS_VERIFY_CTX_set_certs(TS_VERIFY_CTX *ctx,
\&                                         STACK_OF(X509) *certs);
\& STACK_OF(X509) *TS_VERIFY_CTS_set_certs(TS_VERIFY_CTX *ctx,
\&                                         STACK_OF(X509) *certs);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
The Time-Stamp Protocol (TSP) is defined by RFC 3161. TSP is a protocol used to
provide long term proof of the existence of a certain datum before a particular
time. TSP defines a Time Stamping Authority (TSA) and an entity who shall make
requests to the TSA. Usually the TSA is denoted as the server side and the
requesting entity is denoted as the client.
.PP
In TSP, when a server is sending a response to a client, the server normally
needs to sign the response data \- the TimeStampToken (TST) \- with its private
key. Then the client shall verify the received TST by the server's certificate
chain.
.PP
\&\fBTS_VERIFY_CTX_set_certs()\fR is used to set the server's certificate chain when
verifying a TST. \fBctx\fR is the verification context created in advance and
\&\fBcerts\fR is a stack of \fBX509\fR certificates.
.PP
\&\fBTS_VERIFY_CTS_set_certs()\fR is a misspelled version of \fBTS_VERIFY_CTX_set_certs()\fR
which takes the same parameters and returns the same result.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBTS_VERIFY_CTX_set_certs()\fR returns the stack of \fBX509\fR certificates the user
passes in via parameter \fBcerts\fR.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBOSSL_ESS_check_signing_certs\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
The spelling of \fBTS_VERIFY_CTX_set_certs()\fR was corrected in OpenSSL 3.0.0.
The misspelled version \fBTS_VERIFY_CTS_set_certs()\fR has been retained for
compatibility reasons, but it is deprecated in OpenSSL 3.0.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2019\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
