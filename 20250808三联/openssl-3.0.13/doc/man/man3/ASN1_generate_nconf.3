.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "ASN1_GENERATE_NCONF 3ossl"
.TH ASN1_GENERATE_NCONF 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
ASN1_generate_nconf, ASN1_generate_v3 \- ASN1 string generation functions
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/asn1.h>
\&
\& ASN1_TYPE *ASN1_generate_nconf(const char *str, CONF *nconf);
\& ASN1_TYPE *ASN1_generate_v3(const char *str, X509V3_CTX *cnf);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
These functions generate the ASN1 encoding of a string
in an \fBASN1_TYPE\fR structure.
.PP
\&\fIstr\fR contains the string to encode. \fInconf\fR or \fIcnf\fR contains
the optional configuration information where additional strings
will be read from. \fInconf\fR will typically come from a config
file whereas \fIcnf\fR is obtained from an \fBX509V3_CTX\fR structure,
which will typically be used by X509 v3 certificate extension
functions. \fIcnf\fR or \fInconf\fR can be set to NULL if no additional
configuration will be used.
.SH "GENERATION STRING FORMAT"
.IX Header "GENERATION STRING FORMAT"
The actual data encoded is determined by the string \fIstr\fR and
the configuration information. The general format of the string
is:
.IP [\fImodifier\fR,]\fItype\fR[:\fIvalue\fR] 4
.IX Item "[modifier,]type[:value]"
.PP
That is zero or more comma separated modifiers followed by a type
followed by an optional colon and a value. The formats of \fItype\fR,
\&\fIvalue\fR and \fImodifier\fR are explained below.
.SS "Supported Types"
.IX Subsection "Supported Types"
The supported types are listed below.
Case is not significant in the type names.
Unless otherwise specified only the \fBASCII\fR format is permissible.
.IP "\fBBOOLEAN\fR, \fBBOOL\fR" 4
.IX Item "BOOLEAN, BOOL"
This encodes a boolean type. The \fIvalue\fR string is mandatory and
should be \fBTRUE\fR or \fBFALSE\fR. Additionally \fBTRUE\fR, \fBtrue\fR, \fBY\fR,
\&\fBy\fR, \fBYES\fR, \fByes\fR, \fBFALSE\fR, \fBfalse\fR, \fBN\fR, \fBn\fR, \fBNO\fR and \fBno\fR
are acceptable.
.IP \fBNULL\fR 4
.IX Item "NULL"
Encode the \fBNULL\fR type, the \fIvalue\fR string must not be present.
.IP "\fBINTEGER\fR, \fBINT\fR" 4
.IX Item "INTEGER, INT"
Encodes an ASN1 \fBINTEGER\fR type. The \fIvalue\fR string represents
the value of the integer, it can be prefaced by a minus sign and
is normally interpreted as a decimal value unless the prefix \fB0x\fR
is included.
.IP "\fBENUMERATED\fR, \fBENUM\fR" 4
.IX Item "ENUMERATED, ENUM"
Encodes the ASN1 \fBENUMERATED\fR type, it is otherwise identical to
\&\fBINTEGER\fR.
.IP "\fBOBJECT\fR, \fBOID\fR" 4
.IX Item "OBJECT, OID"
Encodes an ASN1 \fBOBJECT IDENTIFIER\fR, the \fIvalue\fR string can be
a short name, a long name or numerical format.
.IP "\fBUTCTIME\fR, \fBUTC\fR" 4
.IX Item "UTCTIME, UTC"
Encodes an ASN1 \fBUTCTime\fR structure, the value should be in
the format \fBYYMMDDHHMMSSZ\fR.
.IP "\fBGENERALIZEDTIME\fR, \fBGENTIME\fR" 4
.IX Item "GENERALIZEDTIME, GENTIME"
Encodes an ASN1 \fBGeneralizedTime\fR structure, the value should be in
the format \fBYYYYMMDDHHMMSSZ\fR.
.IP "\fBOCTETSTRING\fR, \fBOCT\fR" 4
.IX Item "OCTETSTRING, OCT"
Encodes an ASN1 \fBOCTET STRING\fR. \fIvalue\fR represents the contents
of this structure, the format strings \fBASCII\fR and \fBHEX\fR can be
used to specify the format of \fIvalue\fR.
.IP "\fBBITSTRING\fR, \fBBITSTR\fR" 4
.IX Item "BITSTRING, BITSTR"
Encodes an ASN1 \fBBIT STRING\fR. \fIvalue\fR represents the contents
of this structure, the format strings \fBASCII\fR, \fBHEX\fR and \fBBITLIST\fR
can be used to specify the format of \fIvalue\fR.
.Sp
If the format is anything other than \fBBITLIST\fR the number of unused
bits is set to zero.
.IP "\fBUNIVERSALSTRING\fR, \fBUNIV\fR, \fBIA5\fR, \fBIA5STRING\fR, \fBUTF8\fR, \fBUTF8String\fR, \fBBMP\fR, \fBBMPSTRING\fR, \fBVISIBLESTRING\fR, \fBVISIBLE\fR, \fBPRINTABLESTRING\fR, \fBPRINTABLE\fR, \fBT61\fR, \fBT61STRING\fR, \fBTELETEXSTRING\fR, \fBGeneralString\fR, \fBNUMERICSTRING\fR, \fBNUMERIC\fR" 4
.IX Item "UNIVERSALSTRING, UNIV, IA5, IA5STRING, UTF8, UTF8String, BMP, BMPSTRING, VISIBLESTRING, VISIBLE, PRINTABLESTRING, PRINTABLE, T61, T61STRING, TELETEXSTRING, GeneralString, NUMERICSTRING, NUMERIC"
These encode the corresponding string types. \fIvalue\fR represents the
contents of this structure. The format can be \fBASCII\fR or \fBUTF8\fR.
.IP "\fBSEQUENCE\fR, \fBSEQ\fR, \fBSET\fR" 4
.IX Item "SEQUENCE, SEQ, SET"
Formats the result as an ASN1 \fBSEQUENCE\fR or \fBSET\fR type. \fIvalue\fR
should be a section name which will contain the contents. The
field names in the section are ignored and the values are in the
generated string format. If \fIvalue\fR is absent then an empty SEQUENCE
will be encoded.
.SS Modifiers
.IX Subsection "Modifiers"
Modifiers affect the following structure, they can be used to
add EXPLICIT or IMPLICIT tagging, add wrappers or to change
the string format of the final type and value. The supported
formats are documented below.
.IP "\fBEXPLICIT\fR, \fBEXP\fR" 4
.IX Item "EXPLICIT, EXP"
Add an explicit tag to the following structure. This string
should be followed by a colon and the tag value to use as a
decimal value.
.Sp
By following the number with \fBU\fR, \fBA\fR, \fBP\fR or \fBC\fR UNIVERSAL,
APPLICATION, PRIVATE or CONTEXT SPECIFIC tagging can be used,
the default is CONTEXT SPECIFIC.
.IP "\fBIMPLICIT\fR, \fBIMP\fR" 4
.IX Item "IMPLICIT, IMP"
This is the same as \fBEXPLICIT\fR except IMPLICIT tagging is used
instead.
.IP "\fBOCTWRAP\fR, \fBSEQWRAP\fR, \fBSETWRAP\fR, \fBBITWRAP\fR" 4
.IX Item "OCTWRAP, SEQWRAP, SETWRAP, BITWRAP"
The following structure is surrounded by an OCTET STRING, a SEQUENCE,
a SET or a BIT STRING respectively. For a BIT STRING the number of unused
bits is set to zero.
.IP \fBFORMAT\fR 4
.IX Item "FORMAT"
This specifies the format of the ultimate value. It should be followed
by a colon and one of the strings \fBASCII\fR, \fBUTF8\fR, \fBHEX\fR or \fBBITLIST\fR.
.Sp
If no format specifier is included then \fBASCII\fR is used. If \fBUTF8\fR is
specified then the value string must be a valid \fBUTF8\fR string. For \fBHEX\fR the
output must be a set of hex digits. \fBBITLIST\fR (which is only valid for a BIT
STRING) is a comma separated list of the indices of the set bits, all other
bits are zero.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBASN1_generate_nconf()\fR and \fBASN1_generate_v3()\fR return the encoded
data as an \fBASN1_TYPE\fR structure or NULL if an error occurred.
.PP
The error codes that can be obtained by \fBERR_get_error\fR\|(3).
.SH EXAMPLES
.IX Header "EXAMPLES"
A simple IA5String:
.PP
.Vb 1
\& IA5STRING:Hello World
.Ve
.PP
An IA5String explicitly tagged:
.PP
.Vb 1
\& EXPLICIT:0,IA5STRING:Hello World
.Ve
.PP
An IA5String explicitly tagged using APPLICATION tagging:
.PP
.Vb 1
\& EXPLICIT:0A,IA5STRING:Hello World
.Ve
.PP
A BITSTRING with bits 1 and 5 set and all others zero:
.PP
.Vb 1
\& FORMAT:BITLIST,BITSTRING:1,5
.Ve
.PP
A more complex example using a config file to produce a
SEQUENCE consisting of a BOOL an OID and a UTF8String:
.PP
.Vb 1
\& asn1 = SEQUENCE:seq_section
\&
\& [seq_section]
\&
\& field1 = BOOLEAN:TRUE
\& field2 = OID:commonName
\& field3 = UTF8:Third field
.Ve
.PP
This example produces an RSAPrivateKey structure, this is the
key contained in the file client.pem in all OpenSSL distributions
(note: the field names such as 'coeff' are ignored and are present just
for clarity):
.PP
.Vb 3
\& asn1=SEQUENCE:private_key
\& [private_key]
\& version=INTEGER:0
\&
\& n=INTEGER:0xBB6FE79432CC6EA2D8F970675A5A87BFBE1AFF0BE63E879F2AFFB93644\e
\& D4D2C6D000430DEC66ABF47829E74B8C5108623A1C0EE8BE217B3AD8D36D5EB4FCA1D9
\&
\& e=INTEGER:0x010001
\&
\& d=INTEGER:0x6F05EAD2F27FFAEC84BEC360C4B928FD5F3A9865D0FCAAD291E2A52F4A\e
\& F810DC6373278C006A0ABBA27DC8C63BF97F7E666E27C5284D7D3B1FFFE16B7A87B51D
\&
\& p=INTEGER:0xF3929B9435608F8A22C208D86795271D54EBDFB09DDEF539AB083DA912\e
\& D4BD57
\&
\& q=INTEGER:0xC50016F89DFF2561347ED1186A46E150E28BF2D0F539A1594BBD7FE467\e
\& 46EC4F
\&
\& exp1=INTEGER:0x9E7D4326C924AFC1DEA40B45650134966D6F9DFA3A7F9D698CD4ABEA\e
\& 9C0A39B9
\&
\& exp2=INTEGER:0xBA84003BB95355AFB7C50DF140C60513D0BA51D637272E355E397779\e
\& E7B2458F
\&
\& coeff=INTEGER:0x30B9E4F2AFA5AC679F920FC83F1F2DF1BAF1779CF989447FABC2F5\e
\& 628657053A
.Ve
.PP
This example is the corresponding public key in a SubjectPublicKeyInfo
structure:
.PP
.Vb 2
\& # Start with a SEQUENCE
\& asn1=SEQUENCE:pubkeyinfo
\&
\& # pubkeyinfo contains an algorithm identifier and the public key wrapped
\& # in a BIT STRING
\& [pubkeyinfo]
\& algorithm=SEQUENCE:rsa_alg
\& pubkey=BITWRAP,SEQUENCE:rsapubkey
\&
\& # algorithm ID for RSA is just an OID and a NULL
\& [rsa_alg]
\& algorithm=OID:rsaEncryption
\& parameter=NULL
\&
\& # Actual public key: modulus and exponent
\& [rsapubkey]
\& n=INTEGER:0xBB6FE79432CC6EA2D8F970675A5A87BFBE1AFF0BE63E879F2AFFB93644\e
\& D4D2C6D000430DEC66ABF47829E74B8C5108623A1C0EE8BE217B3AD8D36D5EB4FCA1D9
\&
\& e=INTEGER:0x010001
.Ve
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBERR_get_error\fR\|(3)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2002\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
