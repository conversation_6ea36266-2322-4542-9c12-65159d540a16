.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "BIO_F_READBUFFER 3ossl"
.TH BIO_F_READBUFFER 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
BIO_f_readbuffer
\&\- read only buffering BIO that supports BIO_tell() and BIO_seek()
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/bio.h>
\&
\& const BIO_METHOD *BIO_f_readbuffer(void);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBBIO_f_readbuffer()\fR returns the read buffering BIO method.
.PP
This BIO filter can be inserted on top of BIO's that do not support \fBBIO_tell()\fR
or \fBBIO_seek()\fR (e.g. A file BIO that uses stdin).
.PP
Data read from a read buffering BIO comes from an internal buffer which is
filled from the next BIO in the chain.
.PP
\&\fBBIO_gets()\fR is supported for read buffering BIOs.
Writing data to a read buffering BIO is not supported.
.PP
Calling \fBBIO_reset()\fR on a read buffering BIO does not clear any buffered data.
.SH NOTES
.IX Header "NOTES"
Read buffering BIOs implement \fBBIO_read_ex()\fR by using \fBBIO_read_ex()\fR operations
on the next BIO (e.g. a file BIO) in the chain and storing the result in an
internal buffer, from which bytes are given back to the caller as appropriate
for the call. \fBBIO_read_ex()\fR is guaranteed to give the caller the number of bytes
it asks for, unless there's an error or end of communication is reached in the
next BIO. The internal buffer can grow to cache the entire contents of the next
BIO in the chain. \fBBIO_seek()\fR uses the internal buffer, so that it can only seek
into data that is already read.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBBIO_f_readbuffer()\fR returns the read buffering BIO method.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBbio\fR\|(7),
\&\fBBIO_read\fR\|(3),
\&\fBBIO_gets\fR\|(3),
\&\fBBIO_reset\fR\|(3),
\&\fBBIO_ctrl\fR\|(3).
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
