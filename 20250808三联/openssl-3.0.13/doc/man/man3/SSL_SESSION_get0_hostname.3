.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "SSL_SESSION_GET0_HOSTNAME 3ossl"
.TH SSL_SESSION_GET0_HOSTNAME 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
SSL_SESSION_get0_hostname,
SSL_SESSION_set1_hostname,
SSL_SESSION_get0_alpn_selected,
SSL_SESSION_set1_alpn_selected
\&\- get and set SNI and ALPN data associated with a session
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/ssl.h>
\&
\& const char *SSL_SESSION_get0_hostname(const SSL_SESSION *s);
\& int SSL_SESSION_set1_hostname(SSL_SESSION *s, const char *hostname);
\&
\& void SSL_SESSION_get0_alpn_selected(const SSL_SESSION *s,
\&                                     const unsigned char **alpn,
\&                                     size_t *len);
\& int SSL_SESSION_set1_alpn_selected(SSL_SESSION *s, const unsigned char *alpn,
\&                                    size_t len);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBSSL_SESSION_get0_hostname()\fR retrieves the SNI value that was sent by the
client when the session was created if it was accepted by the server and TLSv1.2
or below was negotiated. Otherwise NULL is returned. Note that in TLSv1.3 the
SNI hostname is negotiated with each handshake including resumption handshakes
and is therefore never associated with the session.
.PP
The value returned is a pointer to memory maintained within \fBs\fR and
should not be free'd.
.PP
\&\fBSSL_SESSION_set1_hostname()\fR sets the SNI value for the hostname to a copy of
the string provided in hostname.
.PP
\&\fBSSL_SESSION_get0_alpn_selected()\fR retrieves the selected ALPN protocol for this
session and its associated length in bytes. The returned value of \fB*alpn\fR is a
pointer to memory maintained within \fBs\fR and should not be free'd.
.PP
\&\fBSSL_SESSION_set1_alpn_selected()\fR sets the ALPN protocol for this session to the
value in \fBalpn\fR which should be of length \fBlen\fR bytes. A copy of the input
value is made, and the caller retains ownership of the memory pointed to by
\&\fBalpn\fR.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBSSL_SESSION_get0_hostname()\fR returns either a string or NULL based on if there
is the SNI value sent by client.
.PP
\&\fBSSL_SESSION_set1_hostname()\fR returns 1 on success or 0 on error.
.PP
\&\fBSSL_SESSION_set1_alpn_selected()\fR returns 1 on success or 0 on error.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBssl\fR\|(7),
\&\fBd2i_SSL_SESSION\fR\|(3),
\&\fBSSL_SESSION_get_time\fR\|(3),
\&\fBSSL_SESSION_free\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
The \fBSSL_SESSION_set1_hostname()\fR, \fBSSL_SESSION_get0_alpn_selected()\fR and
\&\fBSSL_SESSION_set1_alpn_selected()\fR functions were added in OpenSSL 1.1.1.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2016\-2020 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
