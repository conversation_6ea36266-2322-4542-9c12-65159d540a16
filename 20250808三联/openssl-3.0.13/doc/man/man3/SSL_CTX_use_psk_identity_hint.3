.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "SSL_CTX_USE_PSK_IDENTITY_HINT 3ossl"
.TH SSL_CTX_USE_PSK_IDENTITY_HINT 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
SSL_psk_server_cb_func,
SSL_psk_find_session_cb_func,
SSL_CTX_use_psk_identity_hint,
SSL_use_psk_identity_hint,
SSL_CTX_set_psk_server_callback,
SSL_set_psk_server_callback,
SSL_CTX_set_psk_find_session_callback,
SSL_set_psk_find_session_callback
\&\- set PSK identity hint to use
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/ssl.h>
\&
\& typedef int (*SSL_psk_find_session_cb_func)(SSL *ssl,
\&                                             const unsigned char *identity,
\&                                             size_t identity_len,
\&                                             SSL_SESSION **sess);
\&
\&
\& void SSL_CTX_set_psk_find_session_callback(SSL_CTX *ctx,
\&                                            SSL_psk_find_session_cb_func cb);
\& void SSL_set_psk_find_session_callback(SSL *s, SSL_psk_find_session_cb_func cb);
\&
\& typedef unsigned int (*SSL_psk_server_cb_func)(SSL *ssl,
\&                                                const char *identity,
\&                                                unsigned char *psk,
\&                                                unsigned int max_psk_len);
\&
\& int SSL_CTX_use_psk_identity_hint(SSL_CTX *ctx, const char *hint);
\& int SSL_use_psk_identity_hint(SSL *ssl, const char *hint);
\&
\& void SSL_CTX_set_psk_server_callback(SSL_CTX *ctx, SSL_psk_server_cb_func cb);
\& void SSL_set_psk_server_callback(SSL *ssl, SSL_psk_server_cb_func cb);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
A server application wishing to use TLSv1.3 PSKs should set a callback
using either \fBSSL_CTX_set_psk_find_session_callback()\fR or
\&\fBSSL_set_psk_find_session_callback()\fR as appropriate.
.PP
The callback function is given a pointer to the SSL connection in \fBssl\fR and
an identity in \fBidentity\fR of length \fBidentity_len\fR. The callback function
should identify an SSL_SESSION object that provides the PSK details and store it
in \fB*sess\fR. The SSL_SESSION object should, as a minimum, set the master key,
the ciphersuite and the protocol version. See
\&\fBSSL_CTX_set_psk_use_session_callback\fR\|(3) for details.
.PP
It is also possible for the callback to succeed but not supply a PSK. In this
case no PSK will be used but the handshake will continue. To do this the
callback should return successfully and ensure that \fB*sess\fR is
NULL.
.PP
Identity hints are not relevant for TLSv1.3. A server application wishing to use
PSK ciphersuites for TLSv1.2 and below may call \fBSSL_CTX_use_psk_identity_hint()\fR
to set the given \fBNUL\fR\-terminated PSK identity hint \fBhint\fR for SSL context
object \fBctx\fR. \fBSSL_use_psk_identity_hint()\fR sets the given \fBNUL\fR\-terminated PSK
identity hint \fBhint\fR for the SSL connection object \fBssl\fR. If \fBhint\fR is
\&\fBNULL\fR the current hint from \fBctx\fR or \fBssl\fR is deleted.
.PP
In the case where PSK identity hint is \fBNULL\fR, the server does not send the
ServerKeyExchange message to the client.
.PP
A server application wishing to use PSKs for TLSv1.2 and below must provide a
callback function which is called when the server receives the
ClientKeyExchange message from the client. The purpose of the callback function
is to validate the received PSK identity and to fetch the pre-shared key used
during the connection setup phase. The callback is set using the functions
\&\fBSSL_CTX_set_psk_server_callback()\fR or \fBSSL_set_psk_server_callback()\fR. The callback
function is given the connection in parameter \fBssl\fR, \fBNUL\fR\-terminated PSK
identity sent by the client in parameter \fBidentity\fR, and a buffer \fBpsk\fR of
length \fBmax_psk_len\fR bytes where the pre-shared key is to be stored.
.PP
The callback for use in TLSv1.2 will also work in TLSv1.3 although it is
recommended to use \fBSSL_CTX_set_psk_find_session_callback()\fR
or \fBSSL_set_psk_find_session_callback()\fR for this purpose instead. If TLSv1.3 has
been negotiated then OpenSSL will first check to see if a callback has been set
via \fBSSL_CTX_set_psk_find_session_callback()\fR or \fBSSL_set_psk_find_session_callback()\fR
and it will use that in preference. If no such callback is present then it will
check to see if a callback has been set via \fBSSL_CTX_set_psk_server_callback()\fR or
\&\fBSSL_set_psk_server_callback()\fR and use that. In this case the handshake digest
will default to SHA\-256 for any returned PSK. TLSv1.3 early data exchanges are
possible in PSK connections only with the \fBSSL_psk_find_session_cb_func\fR
callback, and are not possible with the \fBSSL_psk_server_cb_func\fR callback.
.PP
A connection established via a TLSv1.3 PSK will appear as if session resumption
has occurred so that \fBSSL_session_reused\fR\|(3) will return true.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBSSL_CTX_use_psk_identity_hint()\fR and \fBSSL_use_psk_identity_hint()\fR return
1 on success, 0 otherwise.
.PP
Return values from the TLSv1.2 and below server callback are interpreted as
follows:
.IP 0 4
PSK identity was not found. An "unknown_psk_identity" alert message
will be sent and the connection setup fails.
.IP >0 4
.IX Item ">0"
PSK identity was found and the server callback has provided the PSK
successfully in parameter \fBpsk\fR. Return value is the length of
\&\fBpsk\fR in bytes. It is an error to return a value greater than
\&\fBmax_psk_len\fR.
.Sp
If the PSK identity was not found but the callback instructs the
protocol to continue anyway, the callback must provide some random
data to \fBpsk\fR and return the length of the random data, so the
connection will fail with decryption_error before it will be finished
completely.
.PP
The \fBSSL_psk_find_session_cb_func\fR callback should return 1 on success or 0 on
failure. In the event of failure the connection setup fails.
.SH NOTES
.IX Header "NOTES"
There are no known security issues with sharing the same PSK between TLSv1.2 (or
below) and TLSv1.3. However, the RFC has this note of caution:
.PP
"While there is no known way in which the same PSK might produce related output
in both versions, only limited analysis has been done.  Implementations can
ensure safety from cross-protocol related output by not reusing PSKs between
TLS 1.3 and TLS 1.2."
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBssl\fR\|(7),
\&\fBSSL_CTX_set_psk_use_session_callback\fR\|(3),
\&\fBSSL_set_psk_use_session_callback\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
\&\fBSSL_CTX_set_psk_find_session_callback()\fR and \fBSSL_set_psk_find_session_callback()\fR
were added in OpenSSL 1.1.1.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2006\-2020 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
