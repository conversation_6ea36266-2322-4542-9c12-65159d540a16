.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "OPENSSL_CONFIG 3ossl"
.TH OPENSSL_CONFIG 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
OPENSSL_config, OPENSSL_no_config \- simple OpenSSL configuration functions
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/conf.h>
.Ve
.PP
The following functions have been deprecated since OpenSSL 1.1.0, and can be
hidden entirely by defining \fBOPENSSL_API_COMPAT\fR with a suitable version value,
see \fBopenssl_user_macros\fR\|(7):
.PP
.Vb 2
\& void OPENSSL_config(const char *appname);
\& void OPENSSL_no_config(void);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBOPENSSL_config()\fR configures OpenSSL using the standard \fBopenssl.cnf\fR and
reads from the application section \fBappname\fR. If \fBappname\fR is NULL then
the default section, \fBopenssl_conf\fR, will be used.
Errors are silently ignored.
Multiple calls have no effect.
.PP
\&\fBOPENSSL_no_config()\fR disables configuration. If called before \fBOPENSSL_config()\fR
no configuration takes place.
.PP
If the application is built with \fBOPENSSL_LOAD_CONF\fR defined, then a
call to \fBOpenSSL_add_all_algorithms()\fR will implicitly call \fBOPENSSL_config()\fR
first.
.SH NOTES
.IX Header "NOTES"
The \fBOPENSSL_config()\fR function is designed to be a very simple "call it and
forget it" function.
It is however \fBmuch\fR better than nothing. Applications which need finer
control over their configuration functionality should use the configuration
functions such as \fBCONF_modules_load()\fR directly. This function is deprecated
and its use should be avoided.
Applications should instead call \fBCONF_modules_load()\fR during
initialization (that is before starting any threads).
.PP
There are several reasons why calling the OpenSSL configuration routines is
advisable. For example, to load dynamic ENGINEs from shared libraries (DSOs).
However, very few applications currently support the control interface and so
very few can load and use dynamic ENGINEs. Equally in future more sophisticated
ENGINEs will require certain control operations to customize them. If an
application calls \fBOPENSSL_config()\fR it doesn't need to know or care about
ENGINE control operations because they can be performed by editing a
configuration file.
.SH ENVIRONMENT
.IX Header "ENVIRONMENT"
.IP \fBOPENSSL_CONF\fR 4
.IX Item "OPENSSL_CONF"
The path to the config file.
Ignored in set-user-ID and set-group-ID programs.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
Neither \fBOPENSSL_config()\fR nor \fBOPENSSL_no_config()\fR return a value.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBconfig\fR\|(5),
\&\fBCONF_modules_load_file\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
The \fBOPENSSL_no_config()\fR and \fBOPENSSL_config()\fR functions were
deprecated in OpenSSL 1.1.0 by \fBOPENSSL_init_crypto()\fR.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2004\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
