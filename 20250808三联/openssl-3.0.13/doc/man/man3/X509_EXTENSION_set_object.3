.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "X509_EXTENSION_SET_OBJECT 3ossl"
.TH X509_EXTENSION_SET_OBJECT 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
X509_EXTENSION_set_object, X509_EXTENSION_set_critical,
X509_EXTENSION_set_data, X509_EXTENSION_create_by_NID,
X509_EXTENSION_create_by_OBJ, X509_EXTENSION_get_object,
X509_EXTENSION_get_critical, X509_EXTENSION_get_data \- extension utility
functions
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 3
\& int X509_EXTENSION_set_object(X509_EXTENSION *ex, const ASN1_OBJECT *obj);
\& int X509_EXTENSION_set_critical(X509_EXTENSION *ex, int crit);
\& int X509_EXTENSION_set_data(X509_EXTENSION *ex, ASN1_OCTET_STRING *data);
\&
\& X509_EXTENSION *X509_EXTENSION_create_by_NID(X509_EXTENSION **ex,
\&                                              int nid, int crit,
\&                                              ASN1_OCTET_STRING *data);
\& X509_EXTENSION *X509_EXTENSION_create_by_OBJ(X509_EXTENSION **ex,
\&                                              const ASN1_OBJECT *obj, int crit,
\&                                              ASN1_OCTET_STRING *data);
\&
\& ASN1_OBJECT *X509_EXTENSION_get_object(X509_EXTENSION *ex);
\& int X509_EXTENSION_get_critical(const X509_EXTENSION *ex);
\& ASN1_OCTET_STRING *X509_EXTENSION_get_data(X509_EXTENSION *ne);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBX509_EXTENSION_set_object()\fR sets the extension type of \fBex\fR to \fBobj\fR. The
\&\fBobj\fR pointer is duplicated internally so \fBobj\fR should be freed up after use.
.PP
\&\fBX509_EXTENSION_set_critical()\fR sets the criticality of \fBex\fR to \fBcrit\fR. If
\&\fBcrit\fR is zero the extension in non-critical otherwise it is critical.
.PP
\&\fBX509_EXTENSION_set_data()\fR sets the data in extension \fBex\fR to \fBdata\fR. The
\&\fBdata\fR pointer is duplicated internally.
.PP
\&\fBX509_EXTENSION_create_by_NID()\fR creates an extension of type \fBnid\fR,
criticality \fBcrit\fR using data \fBdata\fR. The created extension is returned and
written to \fB*ex\fR reusing or allocating a new extension if necessary so \fB*ex\fR
should either be \fBNULL\fR or a valid \fBX509_EXTENSION\fR structure it must
\&\fBnot\fR be an uninitialised pointer.
.PP
\&\fBX509_EXTENSION_create_by_OBJ()\fR is identical to \fBX509_EXTENSION_create_by_NID()\fR
except it creates and extension using \fBobj\fR instead of a NID.
.PP
\&\fBX509_EXTENSION_get_object()\fR returns the extension type of \fBex\fR as an
\&\fBASN1_OBJECT\fR pointer. The returned pointer is an internal value which must
not be freed up.
.PP
\&\fBX509_EXTENSION_get_critical()\fR returns the criticality of extension \fBex\fR it
returns \fB1\fR for critical and \fB0\fR for non-critical.
.PP
\&\fBX509_EXTENSION_get_data()\fR returns the data of extension \fBex\fR. The returned
pointer is an internal value which must not be freed up.
.SH NOTES
.IX Header "NOTES"
These functions manipulate the contents of an extension directly. Most
applications will want to parse or encode and add an extension: they should
use the extension encode and decode functions instead such as
\&\fBX509_add1_ext_i2d()\fR and \fBX509_get_ext_d2i()\fR.
.PP
The \fBdata\fR associated with an extension is the extension encoding in an
\&\fBASN1_OCTET_STRING\fR structure.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBX509_EXTENSION_set_object()\fR \fBX509_EXTENSION_set_critical()\fR and
\&\fBX509_EXTENSION_set_data()\fR return \fB1\fR for success and \fB0\fR for failure.
.PP
\&\fBX509_EXTENSION_create_by_NID()\fR and \fBX509_EXTENSION_create_by_OBJ()\fR return
an \fBX509_EXTENSION\fR pointer or \fBNULL\fR if an error occurs.
.PP
\&\fBX509_EXTENSION_get_object()\fR returns an \fBASN1_OBJECT\fR pointer.
.PP
\&\fBX509_EXTENSION_get_critical()\fR returns \fB0\fR for non-critical and \fB1\fR for
critical.
.PP
\&\fBX509_EXTENSION_get_data()\fR returns an \fBASN1_OCTET_STRING\fR pointer.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBX509V3_get_d2i\fR\|(3)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2015\-2016 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
