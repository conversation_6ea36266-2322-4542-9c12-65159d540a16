.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "SSL_CTX_SET_SRP_PASSWORD 3ossl"
.TH SSL_CTX_SET_SRP_PASSWORD 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
SSL_CTX_set_srp_username,
SSL_CTX_set_srp_password,
SSL_CTX_set_srp_strength,
SSL_CTX_set_srp_cb_arg,
SSL_CTX_set_srp_username_callback,
SSL_CTX_set_srp_client_pwd_callback,
SSL_CTX_set_srp_verify_param_callback,
SSL_set_srp_server_param,
SSL_set_srp_server_param_pw,
SSL_get_srp_g,
SSL_get_srp_N,
SSL_get_srp_username,
SSL_get_srp_userinfo
\&\- SRP control operations
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/ssl.h>
.Ve
.PP
The following functions have been deprecated since OpenSSL 3.0, and can be
hidden entirely by defining \fBOPENSSL_API_COMPAT\fR with a suitable version value,
see \fBopenssl_user_macros\fR\|(7):
.PP
.Vb 10
\& int SSL_CTX_set_srp_username(SSL_CTX *ctx, char *name);
\& int SSL_CTX_set_srp_password(SSL_CTX *ctx, char *password);
\& int SSL_CTX_set_srp_strength(SSL_CTX *ctx, int strength);
\& int SSL_CTX_set_srp_cb_arg(SSL_CTX *ctx, void *arg);
\& int SSL_CTX_set_srp_username_callback(SSL_CTX *ctx,
\&                                       int (*cb) (SSL *s, int *ad, void *arg));
\& int SSL_CTX_set_srp_client_pwd_callback(SSL_CTX *ctx,
\&                                         char *(*cb) (SSL *s, void *arg));
\& int SSL_CTX_set_srp_verify_param_callback(SSL_CTX *ctx,
\&                                           int (*cb) (SSL *s, void *arg));
\&
\& int SSL_set_srp_server_param(SSL *s, const BIGNUM *N, const BIGNUM *g,
\&                              BIGNUM *sa, BIGNUM *v, char *info);
\& int SSL_set_srp_server_param_pw(SSL *s, const char *user, const char *pass,
\&                                 const char *grp);
\&
\& BIGNUM *SSL_get_srp_g(SSL *s);
\& BIGNUM *SSL_get_srp_N(SSL *s);
\&
\& char *SSL_get_srp_username(SSL *s);
\& char *SSL_get_srp_userinfo(SSL *s);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
All of the functions described on this page are deprecated. There are no
available replacement functions at this time.
.PP
These functions provide access to SRP (Secure Remote Password) parameters,
an alternate authentication mechanism for TLS. SRP allows the use of usernames
and passwords over unencrypted channels without revealing the password to an
eavesdropper. SRP also supplies a shared secret at the end of the authentication
sequence that can be used to generate encryption keys.
.PP
The SRP protocol, version 3 is specified in RFC 2945. SRP version 6 is described
in RFC 5054 with applications to TLS authentication.
.PP
The \fBSSL_CTX_set_srp_username()\fR function sets the SRP username for \fBctx\fR. This
should be called on the client prior to creating a connection to the server.
The length of \fBname\fR must be shorter or equal to 255 characters.
.PP
The \fBSSL_CTX_set_srp_password()\fR function sets the SRP password for \fBctx\fR. This
may be called on the client prior to creating a connection to the server.
This overrides the effect of \fBSSL_CTX_set_srp_client_pwd_callback()\fR.
.PP
The \fBSSL_CTX_set_srp_strength()\fR function sets the SRP strength for \fBctx\fR. This
is the minimal length of the SRP prime in bits. If not specified 1024 is used.
If not satisfied by the server key exchange the connection will be rejected.
.PP
The \fBSSL_CTX_set_srp_cb_arg()\fR function sets an extra parameter that will
be passed to all following callbacks as \fBarg\fR.
.PP
The \fBSSL_CTX_set_srp_username_callback()\fR function sets the server side callback
that is invoked when an SRP username is found in a ClientHello.
The callback parameters are the SSL connection \fBs\fR, a writable error flag \fBad\fR
and the extra argument \fBarg\fR set by \fBSSL_CTX_set_srp_cb_arg()\fR.
This callback should setup the server for the key exchange by calling
\&\fBSSL_set_srp_server_param()\fR with the appropriate parameters for the received
username. The username can be obtained by calling \fBSSL_get_srp_username()\fR.
See \fBSRP_VBASE_init\fR\|(3) to parse the verifier file created by \fBopenssl\-srp\fR\|(1) or
\&\fBSRP_create_verifier\fR\|(3) to generate it.
The callback should return \fBSSL_ERROR_NONE\fR to proceed with the server key exchange,
\&\fBSSL3_AL_FATAL\fR for a fatal error or any value < 0 for a retryable error.
In the event of a \fBSSL3_AL_FATAL\fR the alert flag given by \fB*al\fR will be sent
back. By default this will be \fBSSL_AD_UNKNOWN_PSK_IDENTITY\fR.
.PP
The \fBSSL_CTX_set_srp_client_pwd_callback()\fR function sets the client password
callback on the client.
The callback parameters are the SSL connection \fBs\fR and the extra argument \fBarg\fR
set by \fBSSL_CTX_set_srp_cb_arg()\fR.
The callback will be called as part of the generation of the client secrets.
It should return the client password in text form or NULL to abort the connection.
The resulting memory will be freed by the library as part of the callback resolution.
This overrides the effect of \fBSSL_CTX_set_srp_password()\fR.
.PP
The \fBSSL_CTX_set_srp_verify_param_callback()\fR sets the SRP gN parameter verification
callback on the client. This allows the client to perform custom verification when
receiving the server SRP proposed parameters.
The callback parameters are the SSL connection \fBs\fR and the extra argument \fBarg\fR
set by \fBSSL_CTX_set_srp_cb_arg()\fR.
The callback should return a positive value to accept the server parameters.
Returning 0 or a negative value will abort the connection. The server parameters
can be obtained by calling \fBSSL_get_srp_N()\fR and \fBSSL_get_srp_g()\fR.
Sanity checks are already performed by the library after the handshake
(B % N non zero, check against the strength parameter) and are not necessary.
If no callback is set the g and N parameters will be checked against
known RFC 5054 values.
.PP
The \fBSSL_set_srp_server_param()\fR function sets all SRP parameters for
the connection \fBs\fR. \fBN\fR and \fBg\fR are the SRP group parameters, \fBsa\fR is the
user salt, \fBv\fR the password verifier and \fBinfo\fR is the optional user info.
.PP
The \fBSSL_set_srp_server_param_pw()\fR function sets all SRP parameters for the
connection \fBs\fR by generating a random salt and a password verifier.
\&\fBuser\fR is the username, \fBpass\fR the password and \fBgrp\fR the SRP group parameters
identifier for \fBSRP_get_default_gN\fR\|(3).
.PP
The \fBSSL_get_srp_g()\fR function returns the SRP group generator for \fBs\fR, or from
the underlying SSL_CTX if it is NULL.
.PP
The \fBSSL_get_srp_N()\fR function returns the SRP prime for \fBs\fR, or from
the underlying SSL_CTX if it is NULL.
.PP
The \fBSSL_get_srp_username()\fR function returns the SRP username for \fBs\fR, or from
the underlying SSL_CTX if it is NULL.
.PP
The \fBSSL_get_srp_userinfo()\fR function returns the SRP user info for \fBs\fR, or from
the underlying SSL_CTX if it is NULL.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
All SSL_CTX_set_* functions return 1 on success and 0 on failure.
.PP
\&\fBSSL_set_srp_server_param()\fR returns 1 on success and \-1 on failure.
.PP
The SSL_get_SRP_* functions return a pointer to the requested data, the memory
is owned by the library and should not be freed by the caller.
.SH EXAMPLES
.IX Header "EXAMPLES"
Setup SRP parameters on the client:
.PP
.Vb 1
\& #include <openssl/ssl.h>
\&
\& const char *username = "username";
\& const char *password = "password";
\&
\& SSL_CTX *ctx = SSL_CTX_new(TLS_client_method());
\& if (!ctx)
\&     /* Error */
\& if (!SSL_CTX_set_srp_username(ctx, username))
\&     /* Error */
\& if (!SSL_CTX_set_srp_password(ctx, password))
\&     /* Error */
.Ve
.PP
Setup SRP server with verifier file:
.PP
.Vb 2
\& #include <openssl/srp.h>
\& #include <openssl/ssl.h>
\&
\& const char *srpvfile = "password.srpv";
\&
\& int srpServerCallback(SSL *s, int *ad, void *arg)
\& {
\&     SRP_VBASE *srpData = (SRP_VBASE*) arg;
\&     char *username = SSL_get_srp_username(s);
\&
\&     SRP_user_pwd *user_pwd = SRP_VBASE_get1_by_user(srpData, username);
\&     if (!user_pwd)
\&         /* Error */
\&         return SSL3_AL_FATAL;
\&
\&     if (SSL_set_srp_server_param(s, user_pwd\->N, user_pwd\->g,
\&         user_pwd\->s, user_pwd\->v, user_pwd\->info) < 0)
\&         /* Error */
\&
\&     SRP_user_pwd_free(user_pwd);
\&     return SSL_ERROR_NONE;
\& }
\&
\& SSL_CTX *ctx = SSL_CTX_new(TLS_server_method());
\& if (!ctx)
\&     /* Error */
\&
\& /*
\&  * seedKey should contain a NUL terminated sequence
\&  * of random non NUL bytes
\&  */
\& const char *seedKey;
\&
\& SRP_VBASE *srpData = SRP_VBASE_new(seedKey);
\& if (SRP_VBASE_init(srpData, (char*) srpvfile) != SRP_NO_ERROR)
\&    /* Error */
\&
\& SSL_CTX_set_srp_cb_arg(ctx, srpData);
\& SSL_CTX_set_srp_username_callback(ctx, srpServerCallback);
.Ve
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBssl\fR\|(7),
\&\fBopenssl\-srp\fR\|(1),
\&\fBSRP_VBASE_new\fR\|(3),
\&\fBSRP_create_verifier\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
These functions were added in OpenSSL 1.0.1 and deprecated in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2018\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
