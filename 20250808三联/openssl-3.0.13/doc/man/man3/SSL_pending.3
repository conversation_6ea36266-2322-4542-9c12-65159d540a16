.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "SSL_PENDING 3ossl"
.TH SSL_PENDING 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
SSL_pending, SSL_has_pending \- check for readable bytes buffered in an
SSL object
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/ssl.h>
\&
\& int SSL_pending(const SSL *ssl);
\& int SSL_has_pending(const SSL *s);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
Data is received in whole blocks known as records from the peer. A whole record
is processed (e.g. decrypted) in one go and is buffered by OpenSSL until it is
read by the application via a call to \fBSSL_read_ex\fR\|(3) or \fBSSL_read\fR\|(3).
.PP
\&\fBSSL_pending()\fR returns the number of bytes which have been processed, buffered
and are available inside \fBssl\fR for immediate read.
.PP
If the \fBSSL\fR object's \fIread_ahead\fR flag is set (see
\&\fBSSL_CTX_set_read_ahead\fR\|(3)), additional protocol bytes (beyond the current
record) may have been read containing more TLS/SSL records. This also applies to
DTLS and pipelining (see \fBSSL_CTX_set_split_send_fragment\fR\|(3)). These
additional bytes will be buffered by OpenSSL but will remain unprocessed until
they are needed. As these bytes are still in an unprocessed state \fBSSL_pending()\fR
will ignore them. Therefore, it is possible for no more bytes to be readable from
the underlying BIO (because OpenSSL has already read them) and for \fBSSL_pending()\fR
to return 0, even though readable application data bytes are available (because
the data is in unprocessed buffered records).
.PP
\&\fBSSL_has_pending()\fR returns 1 if \fBs\fR has buffered data (whether processed or
unprocessed) and 0 otherwise. Note that it is possible for \fBSSL_has_pending()\fR to
return 1, and then a subsequent call to \fBSSL_read_ex()\fR or \fBSSL_read()\fR to return no
data because the unprocessed buffered data when processed yielded no application
data (for example this can happen during renegotiation). It is also possible in
this scenario for \fBSSL_has_pending()\fR to continue to return 1 even after an
\&\fBSSL_read_ex()\fR or \fBSSL_read()\fR call because the buffered and unprocessed data is
not yet processable (e.g. because OpenSSL has only received a partial record so
far).
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBSSL_pending()\fR returns the number of buffered and processed application data
bytes that are pending and are available for immediate read. \fBSSL_has_pending()\fR
returns 1 if there is buffered record data in the SSL object and 0 otherwise.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBSSL_read_ex\fR\|(3), \fBSSL_read\fR\|(3), \fBSSL_CTX_set_read_ahead\fR\|(3),
\&\fBSSL_CTX_set_split_send_fragment\fR\|(3), \fBssl\fR\|(7)
.SH HISTORY
.IX Header "HISTORY"
The \fBSSL_has_pending()\fR function was added in OpenSSL 1.1.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2020 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
