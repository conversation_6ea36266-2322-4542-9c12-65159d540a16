.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "EVP_PBE_CIPHERINIT 3ossl"
.TH EVP_PBE_CIPHERINIT 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
EVP_PBE_CipherInit, EVP_PBE_CipherInit_ex,
EVP_PBE_find, EVP_PBE_find_ex,
EVP_PBE_alg_add_type, EVP_PBE_alg_add \- Password based encryption routines
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/evp.h>
\&
\& int EVP_PBE_CipherInit(ASN1_OBJECT *pbe_obj, const char *pass, int passlen,
\&                        ASN1_TYPE *param, EVP_CIPHER_CTX *ctx, int en_de);
\& int EVP_PBE_CipherInit_ex(ASN1_OBJECT *pbe_obj, const char *pass, int passlen,
\&                           ASN1_TYPE *param, EVP_CIPHER_CTX *ctx, int en_de,
\&                           OSSL_LIB_CTX *libctx, const char *propq);
\&
\& int EVP_PBE_find(int type, int pbe_nid, int *pcnid, int *pmnid,
\&                  EVP_PBE_KEYGEN **pkeygen);
\& int EVP_PBE_find_ex(int type, int pbe_nid, int *pcnid, int *pmnid,
\&                     EVP_PBE_KEYGEN **pkeygen, EVP_PBE_KEYGEN_EX **keygen_ex);
\&
\& int EVP_PBE_alg_add_type(int pbe_type, int pbe_nid, int cipher_nid,
\&                          int md_nid, EVP_PBE_KEYGEN *keygen);
\& int EVP_PBE_alg_add(int nid, const EVP_CIPHER *cipher, const EVP_MD *md,
\&                     EVP_PBE_KEYGEN *keygen);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
.SS "PBE operations"
.IX Subsection "PBE operations"
\&\fBEVP_PBE_CipherInit()\fR and \fBEVP_PBE_CipherInit_ex()\fR initialise an \fBEVP_CIPHER_CTX\fR
\&\fIctx\fR for encryption (\fIen_de\fR=1) or decryption (\fIen_de\fR=0) using the password
\&\fIpass\fR of length \fIpasslen\fR. The PBE algorithm type and parameters are extracted
from an OID \fIpbe_obj\fR and parameters \fIparam\fR.
.PP
\&\fBEVP_PBE_CipherInit_ex()\fR also allows the application to specify a library context
\&\fIlibctx\fR and property query \fIpropq\fR to select appropriate algorithm
implementations.
.SS "PBE algorithm search"
.IX Subsection "PBE algorithm search"
\&\fBEVP_PBE_find()\fR and \fBEVP_PBE_find_ex()\fR search for a matching algorithm using two parameters:
.PP
1. An algorithm type \fItype\fR which can be:
.IP \(bu 4
EVP_PBE_TYPE_OUTER \- A PBE algorithm
.IP \(bu 4
EVP_PBE_TYPE_PRF \- A pseudo-random function
.IP \(bu 4
EVP_PBE_TYPE_KDF \- A key derivation function
.PP
2. A \fIpbe_nid\fR which can represent the algorithm identifier with parameters e.g.
\&\fBNID_pbeWithSHA1AndRC2_CBC\fR or an algorithm class e.g. \fBNID_pbes2\fR.
.PP
They return the algorithm's cipher ID \fIpcnid\fR, digest ID \fIpmnid\fR and a key
generation function for the algorithm \fIpkeygen\fR. \fBEVP_PBE_CipherInit_ex()\fR also
returns an extended key generation function \fIkeygen_ex\fR which takes a library
context and property query.
.PP
If a NULL is supplied for any of \fIpcnid\fR, \fIpmnid\fR, \fIpkeygen\fR or \fIpkeygen_ex\fR
then this parameter is not returned.
.SS "PBE algorithm add"
.IX Subsection "PBE algorithm add"
\&\fBEVP_PBE_alg_add_type()\fR and \fBEVP_PBE_alg_add()\fR add an algorithm to the list
of known algorithms. Their parameters have the same meaning as for
\&\fBEVP_PBE_find()\fR and \fBEVP_PBE_find_ex()\fR functions.
.SH NOTES
.IX Header "NOTES"
The arguments \fIpbe_obj\fR and \fIparam\fR to \fBEVP_PBE_CipherInit()\fR and \fBEVP_PBE_CipherInit_ex()\fR
together form an \fBX509_ALGOR\fR and can often be extracted directly from this structure.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
Return value is 1 for success and 0 if an error occurred.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBPKCS5_PBE_keyivgen\fR\|(3),
\&\fBPKCS12_PBE_keyivgen_ex\fR\|(3),
\&\fBPKCS5_v2_PBE_keyivgen_ex\fR\|(3),
\&\fBPKCS12_pbe_crypt_ex\fR\|(3),
\&\fBPKCS12_create_ex\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
\&\fBEVP_PBE_CipherInit_ex()\fR and \fBEVP_PBE_find_ex()\fR were added in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2021\-2022 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
