.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "SSL_CTX_SET_CERT_CB 3ossl"
.TH SSL_CTX_SET_CERT_CB 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
SSL_CTX_set_cert_cb, SSL_set_cert_cb \- handle certificate callback function
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/ssl.h>
\&
\& void SSL_CTX_set_cert_cb(SSL_CTX *c, int (*cert_cb)(SSL *ssl, void *arg),
\&                          void *arg);
\& void SSL_set_cert_cb(SSL *s, int (*cert_cb)(SSL *ssl, void *arg), void *arg);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBSSL_CTX_set_cert_cb()\fR and \fBSSL_set_cert_cb()\fR sets the \fIcert_cb\fR callback,
\&\fIarg\fR value is pointer which is passed to the application callback.
.PP
When \fIcert_cb\fR is NULL, no callback function is used.
.PP
\&\fIcert_cb\fR is the application defined callback. It is called before a
certificate will be used by a client or server. The callback can then inspect
the passed \fIssl\fR structure and set or clear any appropriate certificates. If
the callback is successful it \fBMUST\fR return 1 even if no certificates have
been set. A zero is returned on error which will abort the handshake with a
fatal internal error alert. A negative return value will suspend the handshake
and the handshake function will return immediately.
\&\fBSSL_get_error\fR\|(3) will return SSL_ERROR_WANT_X509_LOOKUP to
indicate, that the handshake was suspended. The next call to the handshake
function will again lead to the call of \fIcert_cb\fR. It is the job of the
\&\fIcert_cb\fR to store information about the state of the last call,
if required to continue.
.SH NOTES
.IX Header "NOTES"
An application will typically call \fBSSL_use_certificate()\fR and
\&\fBSSL_use_PrivateKey()\fR to set the end entity certificate and private key.
It can add intermediate and optionally the root CA certificates using
\&\fBSSL_add1_chain_cert()\fR.
.PP
It might also call \fBSSL_certs_clear()\fR to delete any certificates associated
with the \fBSSL\fR object.
.PP
The certificate callback functionality supersedes the (largely broken)
functionality provided by the old client certificate callback interface.
It is \fBalways\fR called even is a certificate is already set so the callback
can modify or delete the existing certificate.
.PP
A more advanced callback might examine the handshake parameters and set
whatever chain is appropriate. For example a legacy client supporting only
TLSv1.0 might receive a certificate chain signed using SHA1 whereas a
TLSv1.2 or later client which advertises support for SHA256 could receive a
chain using SHA256.
.PP
Normal server sanity checks are performed on any certificates set
by the callback. So if an EC chain is set for a curve the client does not
support it will \fBnot\fR be used.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBSSL_CTX_set_cert_cb()\fR and \fBSSL_set_cert_cb()\fR do not return values.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBssl\fR\|(7), \fBSSL_use_certificate\fR\|(3),
\&\fBSSL_add1_chain_cert\fR\|(3),
\&\fBSSL_get_client_CA_list\fR\|(3),
\&\fBSSL_clear\fR\|(3), \fBSSL_free\fR\|(3)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2014\-2020 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
