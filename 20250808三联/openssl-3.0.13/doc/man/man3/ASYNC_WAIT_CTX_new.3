.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "ASYNC_WAIT_CTX_NEW 3ossl"
.TH ASYNC_WAIT_CTX_NEW 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
ASYNC_WAIT_CTX_new, ASYNC_WAIT_CTX_free, ASYNC_WAIT_CTX_set_wait_fd,
ASYNC_WAIT_CTX_get_fd, ASYNC_WAIT_CTX_get_all_fds,
ASYNC_WAIT_CTX_get_changed_fds, ASYNC_WAIT_CTX_clear_fd,
ASYNC_WAIT_CTX_set_callback, ASYNC_WAIT_CTX_get_callback,
ASYNC_WAIT_CTX_set_status, ASYNC_WAIT_CTX_get_status, ASYNC_callback_fn,
ASYNC_STATUS_UNSUPPORTED, ASYNC_STATUS_ERR, ASYNC_STATUS_OK,
ASYNC_STATUS_EAGAIN
\&\- functions to manage waiting for asynchronous jobs to complete
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/async.h>
\&
\& #define ASYNC_STATUS_UNSUPPORTED    0
\& #define ASYNC_STATUS_ERR            1
\& #define ASYNC_STATUS_OK             2
\& #define ASYNC_STATUS_EAGAIN         3
\& typedef int (*ASYNC_callback_fn)(void *arg);
\& ASYNC_WAIT_CTX *ASYNC_WAIT_CTX_new(void);
\& void ASYNC_WAIT_CTX_free(ASYNC_WAIT_CTX *ctx);
\& int ASYNC_WAIT_CTX_set_wait_fd(ASYNC_WAIT_CTX *ctx, const void *key,
\&                                OSSL_ASYNC_FD fd,
\&                                void *custom_data,
\&                                void (*cleanup)(ASYNC_WAIT_CTX *, const void *,
\&                                                OSSL_ASYNC_FD, void *));
\& int ASYNC_WAIT_CTX_get_fd(ASYNC_WAIT_CTX *ctx, const void *key,
\&                           OSSL_ASYNC_FD *fd, void **custom_data);
\& int ASYNC_WAIT_CTX_get_all_fds(ASYNC_WAIT_CTX *ctx, OSSL_ASYNC_FD *fd,
\&                                size_t *numfds);
\& int ASYNC_WAIT_CTX_get_changed_fds(ASYNC_WAIT_CTX *ctx, OSSL_ASYNC_FD *addfd,
\&                                    size_t *numaddfds, OSSL_ASYNC_FD *delfd,
\&                                    size_t *numdelfds);
\& int ASYNC_WAIT_CTX_clear_fd(ASYNC_WAIT_CTX *ctx, const void *key);
\& int ASYNC_WAIT_CTX_set_callback(ASYNC_WAIT_CTX *ctx,
\&                                 ASYNC_callback_fn callback,
\&                                 void *callback_arg);
\& int ASYNC_WAIT_CTX_get_callback(ASYNC_WAIT_CTX *ctx,
\&                                 ASYNC_callback_fn *callback,
\&                                 void **callback_arg);
\& int ASYNC_WAIT_CTX_set_status(ASYNC_WAIT_CTX *ctx, int status);
\& int ASYNC_WAIT_CTX_get_status(ASYNC_WAIT_CTX *ctx);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
For an overview of how asynchronous operations are implemented in OpenSSL see
\&\fBASYNC_start_job\fR\|(3). An \fBASYNC_WAIT_CTX\fR object represents an asynchronous
"session", i.e. a related set of crypto operations. For example in SSL terms
this would have a one-to-one correspondence with an SSL connection.
.PP
Application code must create an \fBASYNC_WAIT_CTX\fR using the \fBASYNC_WAIT_CTX_new()\fR
function prior to calling \fBASYNC_start_job()\fR (see \fBASYNC_start_job\fR\|(3)). When
the job is started it is associated with the \fBASYNC_WAIT_CTX\fR for the duration
of that job. An \fBASYNC_WAIT_CTX\fR should only be used for one \fBASYNC_JOB\fR at
any one time, but can be reused after an \fBASYNC_JOB\fR has finished for a
subsequent \fBASYNC_JOB\fR. When the session is complete (e.g. the SSL connection
is closed), application code cleans up with \fBASYNC_WAIT_CTX_free()\fR.
.PP
\&\fBASYNC_WAIT_CTX\fRs can have "wait" file descriptors associated with them.
Calling \fBASYNC_WAIT_CTX_get_all_fds()\fR and passing in a pointer to an
\&\fBASYNC_WAIT_CTX\fR in the \fIctx\fR parameter will return the wait file descriptors
associated with that job in \fI*fd\fR. The number of file descriptors returned will
be stored in \fI*numfds\fR. It is the caller's responsibility to ensure that
sufficient memory has been allocated in \fI*fd\fR to receive all the file
descriptors. Calling \fBASYNC_WAIT_CTX_get_all_fds()\fR with a NULL \fIfd\fR value will
return no file descriptors but will still populate \fI*numfds\fR. Therefore,
application code is typically expected to call this function twice: once to get
the number of fds, and then again when sufficient memory has been allocated. If
only one asynchronous engine is being used then normally this call will only
ever return one fd. If multiple asynchronous engines are being used then more
could be returned.
.PP
The function \fBASYNC_WAIT_CTX_get_changed_fds()\fR can be used to detect if any fds
have changed since the last call time \fBASYNC_start_job()\fR returned \fBASYNC_PAUSE\fR
(or since the \fBASYNC_WAIT_CTX\fR was created if no \fBASYNC_PAUSE\fR result has
been received). The \fInumaddfds\fR and \fInumdelfds\fR parameters will be populated
with the number of fds added or deleted respectively. \fI*addfd\fR and \fI*delfd\fR
will be populated with the list of added and deleted fds respectively. Similarly
to \fBASYNC_WAIT_CTX_get_all_fds()\fR either of these can be NULL, but if they are not
NULL then the caller is responsible for ensuring sufficient memory is allocated.
.PP
Implementers of async aware code (e.g. engines) are encouraged to return a
stable fd for the lifetime of the \fBASYNC_WAIT_CTX\fR in order to reduce the
"churn" of regularly changing fds \- although no guarantees of this are provided
to applications.
.PP
Applications can wait for the file descriptor to be ready for "read" using a
system function call such as select or poll (being ready for "read" indicates
that the job should be resumed). If no file descriptor is made available then an
application will have to periodically "poll" the job by attempting to restart it
to see if it is ready to continue.
.PP
Async aware code (e.g. engines) can get the current \fBASYNC_WAIT_CTX\fR from the
job via \fBASYNC_get_wait_ctx\fR\|(3) and provide a file descriptor to use for
waiting on by calling \fBASYNC_WAIT_CTX_set_wait_fd()\fR. Typically this would be done
by an engine immediately prior to calling \fBASYNC_pause_job()\fR and not by end user
code. An existing association with a file descriptor can be obtained using
\&\fBASYNC_WAIT_CTX_get_fd()\fR and cleared using \fBASYNC_WAIT_CTX_clear_fd()\fR. Both of
these functions requires a \fIkey\fR value which is unique to the async aware
code.  This could be any unique value but a good candidate might be the
\&\fBENGINE *\fR for the engine. The \fIcustom_data\fR parameter can be any value, and
will be returned in a subsequent call to \fBASYNC_WAIT_CTX_get_fd()\fR. The
\&\fBASYNC_WAIT_CTX_set_wait_fd()\fR function also expects a pointer to a "cleanup"
routine. This can be NULL but if provided will automatically get called when
the \fBASYNC_WAIT_CTX\fR is freed, and gives the engine the opportunity to close
the fd or any other resources. Note: The "cleanup" routine does not get called
if the fd is cleared directly via a call to \fBASYNC_WAIT_CTX_clear_fd()\fR.
.PP
An example of typical usage might be an async capable engine. User code would
initiate cryptographic operations. The engine would initiate those operations
asynchronously and then call \fBASYNC_WAIT_CTX_set_wait_fd()\fR followed by
\&\fBASYNC_pause_job()\fR to return control to the user code. The user code can then
perform other tasks or wait for the job to be ready by calling "select" or other
similar function on the wait file descriptor. The engine can signal to the user
code that the job should be resumed by making the wait file descriptor
"readable". Once resumed the engine should clear the wake signal on the wait
file descriptor.
.PP
As well as a file descriptor, user code may also be notified via a callback. The
callback and data pointers are stored within the \fBASYNC_WAIT_CTX\fR along with an
additional status field that can be used for the notification of retries from an
engine. This additional method can be used when the user thinks that a file
descriptor is too costly in terms of CPU cycles or in some context where a file
descriptor is not appropriate.
.PP
\&\fBASYNC_WAIT_CTX_set_callback()\fR sets the callback and the callback argument. The
callback will be called to notify user code when an engine completes a
cryptography operation. It is a requirement that the callback function is small
and nonblocking as it will be run in the context of a polling mechanism or an
interrupt.
.PP
\&\fBASYNC_WAIT_CTX_get_callback()\fR returns the callback set in the \fBASYNC_WAIT_CTX\fR
structure.
.PP
\&\fBASYNC_WAIT_CTX_set_status()\fR allows an engine to set the current engine status.
The possible status values are the following:
.IP \fBASYNC_STATUS_UNSUPPORTED\fR 4
.IX Item "ASYNC_STATUS_UNSUPPORTED"
The engine does not support the callback mechanism. This is the default value.
The engine must call \fBASYNC_WAIT_CTX_set_status()\fR to set the status to some value
other than \fBASYNC_STATUS_UNSUPPORTED\fR if it intends to enable the callback
mechanism.
.IP \fBASYNC_STATUS_ERR\fR 4
.IX Item "ASYNC_STATUS_ERR"
The engine has a fatal problem with this request. The user code should clean up
this session.
.IP \fBASYNC_STATUS_OK\fR 4
.IX Item "ASYNC_STATUS_OK"
The request has been successfully submitted.
.IP \fBASYNC_STATUS_EAGAIN\fR 4
.IX Item "ASYNC_STATUS_EAGAIN"
The engine has some problem which will be recovered soon, such as a buffer is
full, so user code should resume the job.
.PP
\&\fBASYNC_WAIT_CTX_get_status()\fR allows user code to obtain the current status value.
If the status is any value other than \fBASYNC_STATUS_OK\fR then the user code
should not expect to receive a callback from the engine even if one has been
set.
.PP
An example of the usage of the callback method might be the following. User
code would initiate cryptographic operations, and the engine code would dispatch
this operation to hardware, and if the dispatch is successful, then the engine
code would call \fBASYNC_pause_job()\fR to return control to the user code. After
that, user code can perform other tasks. When the hardware completes the
operation, normally it is detected by a polling function or an interrupt, as the
user code set a callback by calling \fBASYNC_WAIT_CTX_set_callback()\fR previously,
then the registered callback will be called.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBASYNC_WAIT_CTX_new()\fR returns a pointer to the newly allocated \fBASYNC_WAIT_CTX\fR
or NULL on error.
.PP
ASYNC_WAIT_CTX_set_wait_fd, ASYNC_WAIT_CTX_get_fd, ASYNC_WAIT_CTX_get_all_fds,
ASYNC_WAIT_CTX_get_changed_fds, ASYNC_WAIT_CTX_clear_fd,
ASYNC_WAIT_CTX_set_callback, ASYNC_WAIT_CTX_get_callback and
ASYNC_WAIT_CTX_set_status all return 1 on success or 0 on error.
\&\fBASYNC_WAIT_CTX_get_status()\fR returns the engine status.
.SH NOTES
.IX Header "NOTES"
On Windows platforms the \fI<openssl/async.h>\fR header is dependent on some
of the types customarily made available by including \fI<windows.h>\fR. The
application developer is likely to require control over when the latter
is included, commonly as one of the first included headers. Therefore,
it is defined as an application developer's responsibility to include
\&\fI<windows.h>\fR prior to \fI<openssl/async.h>\fR.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBcrypto\fR\|(7), \fBASYNC_start_job\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
\&\fBASYNC_WAIT_CTX_new()\fR, \fBASYNC_WAIT_CTX_free()\fR, \fBASYNC_WAIT_CTX_set_wait_fd()\fR,
\&\fBASYNC_WAIT_CTX_get_fd()\fR, \fBASYNC_WAIT_CTX_get_all_fds()\fR,
\&\fBASYNC_WAIT_CTX_get_changed_fds()\fR and \fBASYNC_WAIT_CTX_clear_fd()\fR
were added in OpenSSL 1.1.0.
.PP
\&\fBASYNC_WAIT_CTX_set_callback()\fR, \fBASYNC_WAIT_CTX_get_callback()\fR,
\&\fBASYNC_WAIT_CTX_set_status()\fR, and \fBASYNC_WAIT_CTX_get_status()\fR
were added in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2016\-2023 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
