.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "X509_REQ_GET_EXTENSIONS 3ossl"
.TH X509_REQ_GET_EXTENSIONS 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
X509_REQ_get_extensions,
X509_REQ_add_extensions, X509_REQ_add_extensions_nid
\&\- handle X.509 extension attributes of a CSR
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/x509.h>
\&
\& STACK_OF(X509_EXTENSION) *X509_REQ_get_extensions(X509_REQ *req);
\& int X509_REQ_add_extensions(X509_REQ *req, const STACK_OF(X509_EXTENSION) *exts);
\& int X509_REQ_add_extensions_nid(X509_REQ *req,
\&                                 const STACK_OF(X509_EXTENSION) *exts, int nid);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBX509_REQ_get_extensions()\fR returns the first list of X.509 extensions
found in the attributes of \fIreq\fR.
The returned list is empty if there are no such extensions in \fIreq\fR.
The caller is responsible for freeing the list obtained.
.PP
\&\fBX509_REQ_add_extensions()\fR adds to \fIreq\fR a list of X.509 extensions \fIexts\fR,
which must not be NULL, using the default \fBNID_ext_req\fR.
This function must not be called more than once on the same \fIreq\fR.
.PP
\&\fBX509_REQ_add_extensions_nid()\fR is like \fBX509_REQ_add_extensions()\fR
except that \fInid\fR is used to identify the extensions attribute.
This function must not be called more than once with the same \fIreq\fR and \fInid\fR.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBX509_REQ_get_extensions()\fR returns a pointer to \fBSTACK_OF(X509_EXTENSION)\fR
or NULL on error.
.PP
\&\fBX509_REQ_add_extensions()\fR and \fBX509_REQ_add_extensions_nid()\fR
return 1 on success, 0 on error.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2022\-2024 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
