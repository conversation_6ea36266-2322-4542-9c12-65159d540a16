.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "OSSL_ENCODER_CTX 3ossl"
.TH OSSL_ENCODER_CTX 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
OSSL_ENCODER_CTX,
OSSL_ENCODER_CTX_new,
OSSL_ENCODER_settable_ctx_params,
OSSL_ENCODER_CTX_set_params,
OSSL_ENCODER_CTX_free,
OSSL_ENCODER_CTX_set_selection,
OSSL_ENCODER_CTX_set_output_type,
OSSL_ENCODER_CTX_set_output_structure,
OSSL_ENCODER_CTX_add_encoder,
OSSL_ENCODER_CTX_add_extra,
OSSL_ENCODER_CTX_get_num_encoders,
OSSL_ENCODER_INSTANCE,
OSSL_ENCODER_INSTANCE_get_encoder,
OSSL_ENCODER_INSTANCE_get_encoder_ctx,
OSSL_ENCODER_INSTANCE_get_output_type,
OSSL_ENCODER_INSTANCE_get_output_structure,
OSSL_ENCODER_CONSTRUCT,
OSSL_ENCODER_CLEANUP,
OSSL_ENCODER_CTX_set_construct,
OSSL_ENCODER_CTX_set_construct_data,
OSSL_ENCODER_CTX_set_cleanup
\&\- Encoder context routines
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/encoder.h>
\&
\& typedef struct ossl_encoder_ctx_st OSSL_ENCODER_CTX;
\&
\& OSSL_ENCODER_CTX *OSSL_ENCODER_CTX_new();
\& const OSSL_PARAM *OSSL_ENCODER_settable_ctx_params(OSSL_ENCODER *encoder);
\& int OSSL_ENCODER_CTX_set_params(OSSL_ENCODER_CTX *ctx,
\&                                 const OSSL_PARAM params[]);
\& void OSSL_ENCODER_CTX_free(OSSL_ENCODER_CTX *ctx);
\&
\& int OSSL_ENCODER_CTX_set_selection(OSSL_ENCODER_CTX *ctx, int selection);
\& int OSSL_ENCODER_CTX_set_output_type(OSSL_ENCODER_CTX *ctx,
\&                                      const char *output_type);
\& int OSSL_ENCODER_CTX_set_output_structure(OSSL_ENCODER_CTX *ctx,
\&                                           const char *output_structure);
\&
\& int OSSL_ENCODER_CTX_add_encoder(OSSL_ENCODER_CTX *ctx, OSSL_ENCODER *encoder);
\& int OSSL_ENCODER_CTX_add_extra(OSSL_ENCODER_CTX *ctx,
\&                                OSSL_LIB_CTX *libctx, const char *propq);
\& int OSSL_ENCODER_CTX_get_num_encoders(OSSL_ENCODER_CTX *ctx);
\&
\& typedef struct ossl_encoder_instance_st OSSL_ENCODER_INSTANCE;
\& OSSL_ENCODER *
\& OSSL_ENCODER_INSTANCE_get_encoder(OSSL_ENCODER_INSTANCE *encoder_inst);
\& void *
\& OSSL_ENCODER_INSTANCE_get_encoder_ctx(OSSL_ENCODER_INSTANCE *encoder_inst);
\& const char *
\& OSSL_ENCODER_INSTANCE_get_output_type(OSSL_ENCODER_INSTANCE *encoder_inst);
\& const char *
\& OSSL_ENCODER_INSTANCE_get_output_structure(OSSL_ENCODER_INSTANCE *encoder_inst);
\&
\& typedef const void *OSSL_ENCODER_CONSTRUCT(OSSL_ENCODER_INSTANCE *encoder_inst,
\&                                            void *construct_data);
\& typedef void OSSL_ENCODER_CLEANUP(void *construct_data);
\&
\& int OSSL_ENCODER_CTX_set_construct(OSSL_ENCODER_CTX *ctx,
\&                                    OSSL_ENCODER_CONSTRUCT *construct);
\& int OSSL_ENCODER_CTX_set_construct_data(OSSL_ENCODER_CTX *ctx,
\&                                         void *construct_data);
\& int OSSL_ENCODER_CTX_set_cleanup(OSSL_ENCODER_CTX *ctx,
\&                                  OSSL_ENCODER_CLEANUP *cleanup);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
Encoding an input object to the desired encoding may be done with a chain of
encoder implementations, which means that the output from one encoder may be
the input for the next in the chain.  The \fBOSSL_ENCODER_CTX\fR holds all the
data about these encoders.  This allows having generic format encoders such
as DER to PEM, as well as more specialized encoders like RSA to DER.
.PP
The final output type must be given, and a chain of encoders must end with
an implementation that produces that output type.
.PP
At the beginning of the encoding process, a constructor provided by the
caller is called to ensure that there is an appropriate provider-side object
to start with.
The constructor is set with \fBOSSL_ENCODER_CTX_set_construct()\fR.
.PP
\&\fBOSSL_ENCODER_INSTANCE\fR is an opaque structure that contains data about the
encoder that is going to be used, and that may be useful for the
constructor.  There are some functions to extract data from this type,
described in "Constructor" below.
.SS Functions
.IX Subsection "Functions"
\&\fBOSSL_ENCODER_CTX_new()\fR creates a \fBOSSL_ENCODER_CTX\fR.
.PP
\&\fBOSSL_ENCODER_settable_ctx_params()\fR returns an \fBOSSL_PARAM\fR\|(3)
array of parameter descriptors.
.PP
\&\fBOSSL_ENCODER_CTX_set_params()\fR attempts to set parameters specified
with an \fBOSSL_PARAM\fR\|(3) array \fIparams\fR.  Parameters that the
implementation doesn't recognise should be ignored.
.PP
\&\fBOSSL_ENCODER_CTX_free()\fR frees the given context \fIctx\fR.
.PP
\&\fBOSSL_ENCODER_CTX_add_encoder()\fR populates the \fBOSSL_ENCODER_CTX\fR
\&\fIctx\fR with a encoder, to be used to encode an input object.
.PP
\&\fBOSSL_ENCODER_CTX_add_extra()\fR finds encoders that further encodes output
from already added encoders, and adds them as well.  This is used to build
encoder chains.
.PP
\&\fBOSSL_ENCODER_CTX_set_output_type()\fR sets the ending output type.  This must
be specified, and determines if a complete encoder chain is available.
.PP
\&\fBOSSL_ENCODER_CTX_set_output_structure()\fR sets the desired output structure.
This may be used to determines what encoder implementations may be used.
Depending on the type of object being encoded, the output structure may
not be relevant.
.PP
\&\fBOSSL_ENCODER_CTX_get_num_encoders()\fR gets the number of encoders currently
added to the context \fIctx\fR.
.PP
\&\fBOSSL_ENCODER_CTX_set_construct()\fR sets the constructor \fIconstruct\fR.
.PP
\&\fBOSSL_ENCODER_CTX_set_construct_data()\fR sets the constructor data that is
passed to the constructor every time it's called.
.PP
\&\fBOSSL_ENCODER_CTX_set_cleanup()\fR sets the constructor data \fIcleanup\fR
function.  This is called by \fBOSSL_ENCODER_CTX_free\fR\|(3).
.SS Constructor
.IX Subsection "Constructor"
A \fBOSSL_ENCODER_CONSTRUCT\fR gets the following arguments:
.IP \fIencoder_inst\fR 4
.IX Item "encoder_inst"
The \fBOSSL_ENCODER_INSTANCE\fR for the encoder from which the constructor gets
its data.
.IP \fIconstruct_data\fR 4
.IX Item "construct_data"
The pointer that was set with \fBOSSL_ENCODE_CTX_set_construct_data()\fR.
.PP
The constructor is expected to return a valid (non-NULL) pointer to a
provider-native object that can be used as first input of an encoding chain,
or NULL to indicate that an error has occurred.
.PP
These utility functions may be used by a constructor:
.PP
\&\fBOSSL_ENCODER_INSTANCE_get_encoder()\fR can be used to get the encoder
implementation of the encoder instance \fIencoder_inst\fR.
.PP
\&\fBOSSL_ENCODER_INSTANCE_get_encoder_ctx()\fR can be used to get the encoder
implementation's provider context of the encoder instance \fIencoder_inst\fR.
.PP
\&\fBOSSL_ENCODER_INSTANCE_get_output_type()\fR can be used to get the output type
for the encoder implementation of the encoder instance \fIencoder_inst\fR.
This will never be NULL.
.PP
\&\fBOSSL_ENCODER_INSTANCE_get_output_structure()\fR can be used to get the output
structure for the encoder implementation of the encoder instance
\&\fIencoder_inst\fR.
This may be NULL.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBOSSL_ENCODER_CTX_new()\fR returns a pointer to a \fBOSSL_ENCODER_CTX\fR, or NULL
if the context structure couldn't be allocated.
.PP
\&\fBOSSL_ENCODER_settable_ctx_params()\fR returns an \fBOSSL_PARAM\fR\|(3) array, or
NULL if none is available.
.PP
\&\fBOSSL_ENCODER_CTX_set_params()\fR returns 1 if all recognised parameters were
valid, or 0 if one of them was invalid or caused some other failure in the
implementation.
.PP
\&\fBOSSL_ENCODER_CTX_add_encoder()\fR, \fBOSSL_ENCODER_CTX_add_extra()\fR,
\&\fBOSSL_ENCODER_CTX_set_construct()\fR, \fBOSSL_ENCODER_CTX_set_construct_data()\fR and
\&\fBOSSL_ENCODER_CTX_set_cleanup()\fR return 1 on success, or 0 on failure.
.PP
\&\fBOSSL_ENCODER_CTX_get_num_encoders()\fR returns the current number of encoders.
It returns 0 if \fIctx\fR is NULL.
.PP
\&\fBOSSL_ENCODER_INSTANCE_get_encoder()\fR returns an \fBOSSL_ENCODER\fR pointer on
success, or NULL on failure.
.PP
\&\fBOSSL_ENCODER_INSTANCE_get_encoder_ctx()\fR returns a provider context pointer on
success, or NULL on failure.
.PP
\&\fBOSSL_ENCODER_INSTANCE_get_output_type()\fR returns a string with the name of the
input type, if relevant.  NULL is a valid returned value.
.PP
\&\fBOSSL_ENCODER_INSTANCE_get_output_type()\fR returns a string with the name of the
output type.
.PP
\&\fBOSSL_ENCODER_INSTANCE_get_output_structure()\fR returns a string with the name
of the output structure.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBprovider\fR\|(7), \fBOSSL_ENCODER\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
The functions described here were added in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2019\-2023 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
