.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "BIO_ADDR 3ossl"
.TH BIO_ADDR 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
BIO_ADDR, BIO_ADDR_new, BIO_ADDR_clear, BIO_ADDR_free, BIO_ADDR_rawmake,
BIO_ADDR_family, BIO_ADDR_rawaddress, BIO_ADDR_rawport,
BIO_ADDR_hostname_string, BIO_ADDR_service_string,
BIO_ADDR_path_string \- BIO_ADDR routines
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 2
\& #include <sys/types.h>
\& #include <openssl/bio.h>
\&
\& typedef union bio_addr_st BIO_ADDR;
\&
\& BIO_ADDR *BIO_ADDR_new(void);
\& void BIO_ADDR_free(BIO_ADDR *);
\& void BIO_ADDR_clear(BIO_ADDR *ap);
\& int BIO_ADDR_rawmake(BIO_ADDR *ap, int family,
\&                      const void *where, size_t wherelen, unsigned short port);
\& int BIO_ADDR_family(const BIO_ADDR *ap);
\& int BIO_ADDR_rawaddress(const BIO_ADDR *ap, void *p, size_t *l);
\& unsigned short BIO_ADDR_rawport(const BIO_ADDR *ap);
\& char *BIO_ADDR_hostname_string(const BIO_ADDR *ap, int numeric);
\& char *BIO_ADDR_service_string(const BIO_ADDR *ap, int numeric);
\& char *BIO_ADDR_path_string(const BIO_ADDR *ap);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
The \fBBIO_ADDR\fR type is a wrapper around all types of socket
addresses that OpenSSL deals with, currently transparently
supporting AF_INET, AF_INET6 and AF_UNIX according to what's
available on the platform at hand.
.PP
\&\fBBIO_ADDR_new()\fR creates a new unfilled \fBBIO_ADDR\fR, to be used
with routines that will fill it with information, such as
\&\fBBIO_accept_ex()\fR.
.PP
\&\fBBIO_ADDR_free()\fR frees a \fBBIO_ADDR\fR created with \fBBIO_ADDR_new()\fR.
.PP
\&\fBBIO_ADDR_clear()\fR clears any data held within the provided \fBBIO_ADDR\fR and sets
it back to an uninitialised state.
.PP
\&\fBBIO_ADDR_rawmake()\fR takes a protocol \fBfamily\fR, a byte array of
size \fBwherelen\fR with an address in network byte order pointed at
by \fBwhere\fR and a port number in network byte order in \fBport\fR (except
for the \fBAF_UNIX\fR protocol family, where \fBport\fR is meaningless and
therefore ignored) and populates the given \fBBIO_ADDR\fR with them.
In case this creates a \fBAF_UNIX\fR \fBBIO_ADDR\fR, \fBwherelen\fR is expected
to be the length of the path string (not including the terminating
NUL, such as the result of a call to \fBstrlen()\fR).
Read on about the addresses in "RAW ADDRESSES" below.
.PP
\&\fBBIO_ADDR_family()\fR returns the protocol family of the given
\&\fBBIO_ADDR\fR.  The possible non-error results are one of the
constants AF_INET, AF_INET6 and AF_UNIX. It will also return AF_UNSPEC if the
BIO_ADDR has not been initialised.
.PP
\&\fBBIO_ADDR_rawaddress()\fR will write the raw address of the given
\&\fBBIO_ADDR\fR in the area pointed at by \fBp\fR if \fBp\fR is non-NULL,
and will set \fB*l\fR to be the amount of bytes the raw address
takes up if \fBl\fR is non-NULL.
A technique to only find out the size of the address is a call
with \fBp\fR set to \fBNULL\fR.  The raw address will be in network byte
order, most significant byte first.
In case this is a \fBAF_UNIX\fR \fBBIO_ADDR\fR, \fBl\fR gets the length of the
path string (not including the terminating NUL, such as the result of
a call to \fBstrlen()\fR).
Read on about the addresses in "RAW ADDRESSES" below.
.PP
\&\fBBIO_ADDR_rawport()\fR returns the raw port of the given \fBBIO_ADDR\fR.
The raw port will be in network byte order.
.PP
\&\fBBIO_ADDR_hostname_string()\fR returns a character string with the
hostname of the given \fBBIO_ADDR\fR.  If \fBnumeric\fR is 1, the string
will contain the numerical form of the address.  This only works for
\&\fBBIO_ADDR\fR of the protocol families AF_INET and AF_INET6.  The
returned string has been allocated on the heap and must be freed
with \fBOPENSSL_free()\fR.
.PP
\&\fBBIO_ADDR_service_string()\fR returns a character string with the
service name of the port of the given \fBBIO_ADDR\fR.  If \fBnumeric\fR
is 1, the string will contain the port number.  This only works
for \fBBIO_ADDR\fR of the protocol families AF_INET and AF_INET6.  The
returned string has been allocated on the heap and must be freed
with \fBOPENSSL_free()\fR.
.PP
\&\fBBIO_ADDR_path_string()\fR returns a character string with the path
of the given \fBBIO_ADDR\fR.  This only works for \fBBIO_ADDR\fR of the
protocol family AF_UNIX.  The returned string has been allocated
on the heap and must be freed with \fBOPENSSL_free()\fR.
.SH "RAW ADDRESSES"
.IX Header "RAW ADDRESSES"
Both \fBBIO_ADDR_rawmake()\fR and \fBBIO_ADDR_rawaddress()\fR take a pointer to a
network byte order address of a specific site.  Internally, those are
treated as a pointer to \fBstruct in_addr\fR (for \fBAF_INET\fR), \fBstruct
in6_addr\fR (for \fBAF_INET6\fR) or \fBchar *\fR (for \fBAF_UNIX\fR), all
depending on the protocol family the address is for.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
The string producing functions \fBBIO_ADDR_hostname_string()\fR,
\&\fBBIO_ADDR_service_string()\fR and \fBBIO_ADDR_path_string()\fR will
return \fBNULL\fR on error and leave an error indication on the
OpenSSL error stack.
.PP
All other functions described here return 0 or \fBNULL\fR when the
information they should return isn't available.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBBIO_connect\fR\|(3), \fBBIO_s_connect\fR\|(3)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2016\-2020 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
