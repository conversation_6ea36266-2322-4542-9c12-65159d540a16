.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "CMS_SIGN 3ossl"
.TH CMS_SIGN 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
CMS_sign, CMS_sign_ex \- create a CMS SignedData structure
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/cms.h>
\&
\& CMS_ContentInfo *CMS_sign_ex(X509 *signcert, EVP_PKEY *pkey,
\&                              STACK_OF(X509) *certs, BIO *data,
\&                              unsigned int flags, OSSL_LIB_CTX *ctx,
\&                              const char *propq);
\& CMS_ContentInfo *CMS_sign(X509 *signcert, EVP_PKEY *pkey, STACK_OF(X509) *certs,
\&                           BIO *data, unsigned int flags);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBCMS_sign_ex()\fR creates and returns a CMS SignedData structure.
\&\fIsigncert\fR is the certificate to sign with, \fIpkey\fR is the corresponding
private key. \fIcerts\fR is an optional additional set of certificates to include
in the CMS structure (for example any intermediate CAs in the chain). The
library context \fIlibctx\fR and the property query \fIpropq\fR are used when
retrieving algorithms from providers. Any or all of these parameters can be
\&\fBNULL\fR, see \fBNOTES\fR below.
.PP
The data to be signed is read from BIO \fBdata\fR.
.PP
\&\fBflags\fR is an optional set of flags.
.PP
\&\fBCMS_sign()\fR is similar to \fBCMS_sign_ex()\fR but uses default values of NULL
for the library context \fIlibctx\fR and the property query \fIpropq\fR.
.SH NOTES
.IX Header "NOTES"
Any of the following flags (ored together) can be passed in the \fBflags\fR
parameter.
.PP
Many S/MIME clients expect the signed content to include valid MIME headers. If
the \fBCMS_TEXT\fR flag is set MIME headers for type \fBtext/plain\fR are prepended
to the data.
.PP
If \fBCMS_NOCERTS\fR is set the signer's certificate will not be included in the
CMS_ContentInfo structure, the signer's certificate must still be supplied in
the \fBsigncert\fR parameter though. This can reduce the size of the signature if
the signers certificate can be obtained by other means: for example a
previously signed message.
.PP
The data being signed is included in the CMS_ContentInfo structure, unless
\&\fBCMS_DETACHED\fR is set in which case it is omitted. This is used for
CMS_ContentInfo detached signatures which are used in S/MIME plaintext signed
messages for example.
.PP
Normally the supplied content is translated into MIME canonical format (as
required by the S/MIME specifications) if \fBCMS_BINARY\fR is set no translation
occurs. This option should be used if the supplied data is in binary format
otherwise the translation will corrupt it.
.PP
The SignedData structure includes several CMS signedAttributes including the
signing time, the CMS content type and the supported list of ciphers in an
SMIMECapabilities attribute. If \fBCMS_NOATTR\fR is set then no signedAttributes
will be used. If \fBCMS_NOSMIMECAP\fR is set then just the SMIMECapabilities are
omitted.
.PP
If present the SMIMECapabilities attribute indicates support for the following
algorithms in preference order: 256 bit AES, Gost R3411\-94, Gost 28147\-89, 192
bit AES, 128 bit AES, triple DES, 128 bit RC2, 64 bit RC2, DES and 40 bit RC2.
If any of these algorithms is not available then it will not be included:
for example the GOST algorithms will not be included if the GOST ENGINE is
not loaded.
.PP
OpenSSL will by default identify signing certificates using issuer name
and serial number. If \fBCMS_USE_KEYID\fR is set it will use the subject key
identifier value instead. An error occurs if the signing certificate does not
have a subject key identifier extension.
.PP
If the flags \fBCMS_STREAM\fR is set then the returned \fBCMS_ContentInfo\fR
structure is just initialized ready to perform the signing operation. The
signing is however \fBnot\fR performed and the data to be signed is not read from
the \fBdata\fR parameter. Signing is deferred until after the data has been
written. In this way data can be signed in a single pass.
.PP
If the \fBCMS_PARTIAL\fR flag is set a partial \fBCMS_ContentInfo\fR structure is
output to which additional signers and capabilities can be added before
finalization.
.PP
If the flag \fBCMS_STREAM\fR is set the returned \fBCMS_ContentInfo\fR structure is
\&\fBnot\fR complete and outputting its contents via a function that does not
properly finalize the \fBCMS_ContentInfo\fR structure will give unpredictable
results.
.PP
Several functions including \fBSMIME_write_CMS()\fR, \fBi2d_CMS_bio_stream()\fR,
\&\fBPEM_write_bio_CMS_stream()\fR finalize the structure. Alternatively finalization
can be performed by obtaining the streaming ASN1 \fBBIO\fR directly using
\&\fBBIO_new_CMS()\fR.
.PP
If a signer is specified it will use the default digest for the signing
algorithm. This is \fBSHA1\fR for both RSA and DSA keys.
.PP
If \fBsigncert\fR and \fBpkey\fR are NULL then a certificates only CMS structure is
output.
.PP
The function \fBCMS_sign()\fR is a basic CMS signing function whose output will be
suitable for many purposes. For finer control of the output format the
\&\fBcerts\fR, \fBsigncert\fR and \fBpkey\fR parameters can all be \fBNULL\fR and the
\&\fBCMS_PARTIAL\fR flag set. Then one or more signers can be added using the
function \fBCMS_add1_signer()\fR, non default digests can be used and custom
attributes added. \fBCMS_final()\fR must then be called to finalize the
structure if streaming is not enabled.
.SH BUGS
.IX Header "BUGS"
Some attributes such as counter signatures are not supported.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBCMS_sign_ex()\fR and \fBCMS_sign()\fR return either a valid CMS_ContentInfo
structure or NULL if an error occurred. The error can be obtained from
\&\fBERR_get_error\fR\|(3).
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBERR_get_error\fR\|(3), \fBCMS_verify\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
The \fBCMS_STREAM\fR flag is only supported for detached data in OpenSSL 0.9.8,
it is supported for embedded data in OpenSSL 1.0.0 and later.
.PP
The \fBCMS_sign_ex()\fR method was added in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2008\-2023 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
