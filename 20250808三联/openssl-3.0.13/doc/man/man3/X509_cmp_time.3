.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "X509_CMP_TIME 3ossl"
.TH X509_CMP_TIME 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
X509_cmp_time, X509_cmp_current_time, X509_cmp_timeframe,
X509_time_adj, X509_time_adj_ex, X509_gmtime_adj
\&\- X509 time functions
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 8
\& int X509_cmp_time(const ASN1_TIME *asn1_time, time_t *in_tm);
\& int X509_cmp_current_time(const ASN1_TIME *asn1_time);
\& int X509_cmp_timeframe(const X509_VERIFY_PARAM *vpm,
\&                        const ASN1_TIME *start, const ASN1_TIME *end);
\& ASN1_TIME *X509_time_adj(ASN1_TIME *asn1_time, long offset_sec, time_t *in_tm);
\& ASN1_TIME *X509_time_adj_ex(ASN1_TIME *asn1_time, int offset_day, long
\&                             offset_sec, time_t *in_tm);
\& ASN1_TIME *X509_gmtime_adj(ASN1_TIME *asn1_time, long offset_sec);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBX509_cmp_time()\fR compares the ASN1_TIME in \fIasn1_time\fR with the time
in <in_tm>.
.PP
\&\fBX509_cmp_current_time()\fR compares the ASN1_TIME in
\&\fIasn1_time\fR with the current time, expressed as time_t.
.PP
\&\fBX509_cmp_timeframe()\fR compares the given time period with the reference time
included in the verification parameters \fIvpm\fR if they are not NULL and contain
\&\fBX509_V_FLAG_USE_CHECK_TIME\fR; else the current time is used as reference time.
.PP
\&\fBX509_time_adj_ex()\fR sets the ASN1_TIME structure \fIasn1_time\fR to the time
\&\fIoffset_day\fR and \fIoffset_sec\fR after \fIin_tm\fR.
.PP
\&\fBX509_time_adj()\fR sets the ASN1_TIME structure \fIasn1_time\fR to the time
\&\fIoffset_sec\fR after \fIin_tm\fR. This method can only handle second
offsets up to the capacity of long, so the newer \fBX509_time_adj_ex()\fR
API should be preferred.
.PP
In both methods, if \fIasn1_time\fR is NULL, a new ASN1_TIME structure
is allocated and returned.
.PP
In all methods, if \fIin_tm\fR is NULL, the current time, expressed as
time_t, is used.
.PP
\&\fIasn1_time\fR must satisfy the ASN1_TIME format mandated by RFC 5280,
i.e., its format must be either YYMMDDHHMMSSZ or YYYYMMDDHHMMSSZ.
.PP
\&\fBX509_gmtime_adj()\fR sets the ASN1_TIME structure \fIasn1_time\fR to the time
\&\fIoffset_sec\fR after the current time. It is equivalent to calling
\&\fBX509_time_adj()\fR with the last parameter as NULL.
.SH BUGS
.IX Header "BUGS"
Unlike many standard comparison functions, \fBX509_cmp_time()\fR and
\&\fBX509_cmp_current_time()\fR return 0 on error.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBX509_cmp_time()\fR and \fBX509_cmp_current_time()\fR return \-1 if \fIasn1_time\fR
is earlier than, or equal to, \fIin_tm\fR (resp. current time), and 1
otherwise. These methods return 0 on error.
.PP
\&\fBX509_cmp_timeframe()\fR returns 0 if \fIvpm\fR is not NULL and the verification
parameters do not contain \fBX509_V_FLAG_USE_CHECK_TIME\fR
but do contain \fBX509_V_FLAG_NO_CHECK_TIME\fR. Otherwise it returns
1 if the end time is not NULL and the reference time (which has determined as
stated above) is past the end time, \-1 if the start time is not NULL and the
reference time is before, else 0 to indicate that the reference time is in range
(implying that the end time is not before the start time if both are present).
.PP
\&\fBX509_time_adj()\fR, \fBX509_time_adj_ex()\fR and \fBX509_gmtime_adj()\fR return a pointer to
the updated ASN1_TIME structure, and NULL on error.
.SH HISTORY
.IX Header "HISTORY"
\&\fBX509_cmp_timeframe()\fR was added in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2017\-2022 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
