.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "BIO_PRINTF 3ossl"
.TH BIO_PRINTF 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
BIO_printf, BIO_vprintf, BIO_snprintf, BIO_vsnprintf
\&\- formatted output to a BIO
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/bio.h>
\&
\& int BIO_printf(BIO *bio, const char *format, ...);
\& int BIO_vprintf(BIO *bio, const char *format, va_list args);
\&
\& int BIO_snprintf(char *buf, size_t n, const char *format, ...);
\& int BIO_vsnprintf(char *buf, size_t n, const char *format, va_list args);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBBIO_printf()\fR is similar to the standard C \fBprintf()\fR function, except that
the output is sent to the specified BIO, \fIbio\fR, rather than standard
output.  All common format specifiers are supported.
.PP
\&\fBBIO_vprintf()\fR is similar to the \fBvprintf()\fR function found on many platforms,
the output is sent to the specified BIO, \fIbio\fR, rather than standard
output.  All common format specifiers are supported. The argument
list \fIargs\fR is a stdarg argument list.
.PP
\&\fBBIO_snprintf()\fR is for platforms that do not have the common \fBsnprintf()\fR
function. It is like \fBsprintf()\fR except that the size parameter, \fIn\fR,
specifies the size of the output buffer.
.PP
\&\fBBIO_vsnprintf()\fR is to \fBBIO_snprintf()\fR as \fBBIO_vprintf()\fR is to \fBBIO_printf()\fR.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
All functions return the number of bytes written, or \-1 on error.
For \fBBIO_snprintf()\fR and \fBBIO_vsnprintf()\fR this includes when the output
buffer is too small.
.SH NOTES
.IX Header "NOTES"
Except when \fIn\fR is 0, both \fBBIO_snprintf()\fR and \fBBIO_vsnprintf()\fR always
terminate their output with \f(CW\*(Aq\e0\*(Aq\fR.  This includes cases where \-1 is
returned, such as when there is insufficient space to output the whole
string.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2017\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
