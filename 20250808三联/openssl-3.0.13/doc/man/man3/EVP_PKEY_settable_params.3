.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "EVP_PKEY_SETTABLE_PARAMS 3ossl"
.TH EVP_PKEY_SETTABLE_PARAMS 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
EVP_PKEY_settable_params, EVP_PKEY_set_params,
EVP_PKEY_set_int_param, EVP_PKEY_set_size_t_param, EVP_PKEY_set_bn_param,
EVP_PKEY_set_utf8_string_param, EVP_PKEY_set_octet_string_param
\&\- set key parameters into a key
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/evp.h>
\&
\& const OSSL_PARAM *EVP_PKEY_settable_params(const EVP_PKEY *pkey);
\& int EVP_PKEY_set_params(EVP_PKEY *pkey, OSSL_PARAM params[]);
\& int EVP_PKEY_set_int_param(EVP_PKEY *pkey, const char *key_name, int in);
\& int EVP_PKEY_set_size_t_param(EVP_PKEY *pkey, const char *key_name, size_t in);
\& int EVP_PKEY_set_bn_param(EVP_PKEY *pkey, const char *key_name,
\&                           const BIGNUM *bn);
\& int EVP_PKEY_set_utf8_string_param(EVP_PKEY *pkey, const char *key_name,
\&                                    const char *str);
\& int EVP_PKEY_set_octet_string_param(EVP_PKEY *pkey, const char *key_name,
\&                                     const unsigned char *buf, size_t bsize);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
These functions can be used to set additional parameters into an existing
\&\fBEVP_PKEY\fR.
.PP
\&\fBEVP_PKEY_set_params()\fR sets one or more \fIparams\fR into a \fIpkey\fR.
See \fBOSSL_PARAM\fR\|(3) for information about parameters.
.PP
\&\fBEVP_PKEY_settable_params()\fR returns a constant list of \fIparams\fR indicating
the names and types of key parameters that can be set.
See \fBOSSL_PARAM\fR\|(3) for information about parameters.
.PP
\&\fBEVP_PKEY_set_int_param()\fR sets an integer value \fIin\fR into a key \fIpkey\fR for the
associated field \fIkey_name\fR.
.PP
\&\fBEVP_PKEY_set_size_t_param()\fR sets an size_t value \fIin\fR into a key \fIpkey\fR for
the associated field \fIkey_name\fR.
.PP
\&\fBEVP_PKEY_set_bn_param()\fR sets the BIGNUM value \fIbn\fR into a key \fIpkey\fR for the
associated field \fIkey_name\fR.
.PP
\&\fBEVP_PKEY_set_utf8_string_param()\fR sets the UTF8 string \fIstr\fR into a key \fIpkey\fR
for the associated field \fIkey_name\fR.
.PP
\&\fBEVP_PKEY_set_octet_string_param()\fR sets the octet string value \fIbuf\fR with a
size \fIbsize\fR into a key \fIpkey\fR for the associated field \fIkey_name\fR.
.SH NOTES
.IX Header "NOTES"
These functions only work for \fBEVP_PKEY\fRs that contain a provider side key.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBEVP_PKEY_settable_params()\fR returns NULL on error or if it is not supported,
.PP
All other methods return 1 if a value was successfully set, or 0 if
there was an error.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBEVP_PKEY_gettable_params\fR\|(3),
\&\fBEVP_PKEY_CTX_new\fR\|(3), \fBprovider\-keymgmt\fR\|(7), \fBOSSL_PARAM\fR\|(3),
.SH HISTORY
.IX Header "HISTORY"
These functions were added in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2020\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
