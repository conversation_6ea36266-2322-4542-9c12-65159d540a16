.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "ASN1_OBJECT_NEW 3ossl"
.TH ASN1_OBJECT_NEW 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
ASN1_OBJECT_new, ASN1_OBJECT_free \- object allocation functions
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/asn1.h>
\&
\& ASN1_OBJECT *ASN1_OBJECT_new(void);
\& void ASN1_OBJECT_free(ASN1_OBJECT *a);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
The \fBASN1_OBJECT\fR allocation routines, allocate and free an
\&\fBASN1_OBJECT\fR structure, which represents an ASN1 OBJECT IDENTIFIER.
.PP
\&\fBASN1_OBJECT_new()\fR allocates and initializes an \fBASN1_OBJECT\fR structure.
.PP
\&\fBASN1_OBJECT_free()\fR frees up the \fBASN1_OBJECT\fR structure \fIa\fR.
If \fIa\fR is NULL, nothing is done.
.SH NOTES
.IX Header "NOTES"
Although \fBASN1_OBJECT_new()\fR allocates a new \fBASN1_OBJECT\fR structure it
is almost never used in applications. The ASN1 object utility functions
such as \fBOBJ_nid2obj()\fR are used instead.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
If the allocation fails, \fBASN1_OBJECT_new()\fR returns NULL and sets an error
code that can be obtained by \fBERR_get_error\fR\|(3).
Otherwise it returns a pointer to the newly allocated structure.
.PP
\&\fBASN1_OBJECT_free()\fR returns no value.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBERR_get_error\fR\|(3), \fBd2i_ASN1_OBJECT\fR\|(3)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2002\-2016 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
