.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "OPENSSL_HEXCHAR2INT 3ossl"
.TH OPENSSL_HEXCHAR2INT 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
OPENSSL_hexchar2int,
OPENSSL_hexstr2buf_ex, OPENSSL_hexstr2buf,
OPENSSL_buf2hexstr_ex, OPENSSL_buf2hexstr
\&\- Hex encoding and decoding functions
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/crypto.h>
\&
\& int OPENSSL_hexchar2int(unsigned char c);
\& int OPENSSL_hexstr2buf_ex(unsigned char *buf, size_t buf_n, long *buflen,
\&                           const char *str, const char sep);
\& unsigned char *OPENSSL_hexstr2buf(const char *str, long *len);
\& int OPENSSL_buf2hexstr_ex(char *str, size_t str_n, size_t *strlength,
\&                           const unsigned char *buf, long buflen,
\&                           const char sep);
\& char *OPENSSL_buf2hexstr(const unsigned char *buf, long buflen);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBOPENSSL_hexchar2int()\fR converts a hexadecimal character to its numeric
equivalent.
.PP
\&\fBOPENSSL_hexstr2buf_ex()\fR decodes the hex string \fBstr\fR and places the
resulting string of bytes in the given \fIbuf\fR.
The character \fIsep\fR is the separator between the bytes, setting this to '\e0'
means that there is no separator.
\&\fIbuf_n\fR gives the size of the buffer.
If \fIbuflen\fR is not NULL, it is filled in with the result length.
To find out how large the result will be, call this function with NULL
for \fIbuf\fR.
Colons between two-character hex "bytes" are accepted and ignored.
An odd number of hex digits is an error.
.PP
\&\fBOPENSSL_hexstr2buf()\fR does the same thing as \fBOPENSSL_hexstr2buf_ex()\fR,
but allocates the space for the result, and returns the result. It uses a
default separator of ':'.
The memory is allocated by calling \fBOPENSSL_malloc()\fR and should be
released by calling \fBOPENSSL_free()\fR.
.PP
\&\fBOPENSSL_buf2hexstr_ex()\fR encodes the contents of the given \fIbuf\fR with
length \fIbuflen\fR and places the resulting hexadecimal character string
in the given \fIstr\fR.
The character \fIsep\fR is the separator between the bytes, setting this to '\e0'
means that there is no separator.
\&\fIstr_n\fR gives the size of the of the string buffer.
If \fIstrlength\fR is not NULL, it is filled in with the result length.
To find out how large the result will be, call this function with NULL
for \fIstr\fR.
.PP
\&\fBOPENSSL_buf2hexstr()\fR does the same thing as \fBOPENSSL_buf2hexstr_ex()\fR,
but allocates the space for the result, and returns the result. It uses a
default separator of ':'.
The memory is allocated by calling \fBOPENSSL_malloc()\fR and should be
released by calling \fBOPENSSL_free()\fR.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
OPENSSL_hexchar2int returns the value of a decoded hex character,
or \-1 on error.
.PP
\&\fBOPENSSL_buf2hexstr()\fR and \fBOPENSSL_hexstr2buf()\fR
return a pointer to allocated memory, or NULL on error.
.PP
\&\fBOPENSSL_buf2hexstr_ex()\fR and \fBOPENSSL_hexstr2buf_ex()\fR return 1 on
success, or 0 on error.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2016\-2022 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
