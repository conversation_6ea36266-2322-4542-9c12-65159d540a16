.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "ASN1_TYPE_GET 3ossl"
.TH ASN1_TYPE_GET 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
ASN1_TYPE_get, ASN1_TYPE_set, ASN1_TYPE_set1, ASN1_TYPE_cmp, ASN1_TYPE_unpack_sequence, ASN1_TYPE_pack_sequence \- ASN1_TYPE utility
functions
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/asn1.h>
\&
\& int ASN1_TYPE_get(const ASN1_TYPE *a);
\& void ASN1_TYPE_set(ASN1_TYPE *a, int type, void *value);
\& int ASN1_TYPE_set1(ASN1_TYPE *a, int type, const void *value);
\& int ASN1_TYPE_cmp(const ASN1_TYPE *a, const ASN1_TYPE *b);
\&
\& void *ASN1_TYPE_unpack_sequence(const ASN1_ITEM *it, const ASN1_TYPE *t);
\& ASN1_TYPE *ASN1_TYPE_pack_sequence(const ASN1_ITEM *it, void *s,
\&                                    ASN1_TYPE **t);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
These functions allow an \fBASN1_TYPE\fR structure to be manipulated. The
\&\fBASN1_TYPE\fR structure can contain any ASN.1 type or constructed type
such as a SEQUENCE: it is effectively equivalent to the ASN.1 ANY type.
.PP
\&\fBASN1_TYPE_get()\fR returns the type of \fIa\fR or 0 if it fails.
.PP
\&\fBASN1_TYPE_set()\fR sets the value of \fIa\fR to \fItype\fR and \fIvalue\fR. This
function uses the pointer \fIvalue\fR internally so it must \fBnot\fR be freed
up after the call.
.PP
\&\fBASN1_TYPE_set1()\fR sets the value of \fIa\fR to \fItype\fR a copy of \fIvalue\fR.
.PP
\&\fBASN1_TYPE_cmp()\fR compares ASN.1 types \fIa\fR and \fIb\fR and returns 0 if
they are identical and nonzero otherwise.
.PP
\&\fBASN1_TYPE_unpack_sequence()\fR attempts to parse the SEQUENCE present in
\&\fIt\fR using the ASN.1 structure \fIit\fR. If successful it returns a pointer
to the ASN.1 structure corresponding to \fIit\fR which must be freed by the
caller. If it fails it return NULL.
.PP
\&\fBASN1_TYPE_pack_sequence()\fR attempts to encode the ASN.1 structure \fIs\fR
corresponding to \fIit\fR into an \fBASN1_TYPE\fR. If successful the encoded
\&\fBASN1_TYPE\fR is returned. If \fIt\fR and \fI*t\fR are not NULL the encoded type
is written to \fIt\fR overwriting any existing data. If \fIt\fR is not NULL
but \fI*t\fR is NULL the returned \fBASN1_TYPE\fR is written to \fI*t\fR.
.SH NOTES
.IX Header "NOTES"
The type and meaning of the \fIvalue\fR parameter for \fBASN1_TYPE_set()\fR and
\&\fBASN1_TYPE_set1()\fR is determined by the \fItype\fR parameter.
If \fItype\fR is \fBV_ASN1_NULL\fR \fIvalue\fR is ignored. If \fItype\fR is
\&\fBV_ASN1_BOOLEAN\fR
then the boolean is set to TRUE if \fIvalue\fR is not NULL. If \fItype\fR is
\&\fBV_ASN1_OBJECT\fR then value is an \fBASN1_OBJECT\fR structure. Otherwise \fItype\fR
is and \fBASN1_STRING\fR structure. If \fItype\fR corresponds to a primitive type
(or a string type) then the contents of the \fBASN1_STRING\fR contain the content
octets of the type. If \fItype\fR corresponds to a constructed type or
a tagged type (\fBV_ASN1_SEQUENCE\fR, \fBV_ASN1_SET\fR or \fBV_ASN1_OTHER\fR) then the
\&\fBASN1_STRING\fR contains the entire ASN.1 encoding verbatim (including tag and
length octets).
.PP
\&\fBASN1_TYPE_cmp()\fR may not return zero if two types are equivalent but have
different encodings. For example the single content octet of the boolean TRUE
value under BER can have any nonzero encoding but \fBASN1_TYPE_cmp()\fR will
only return zero if the values are the same.
.PP
If either or both of the parameters passed to \fBASN1_TYPE_cmp()\fR is NULL the
return value is nonzero. Technically if both parameters are NULL the two
types could be absent OPTIONAL fields and so should match, however, passing
NULL values could also indicate a programming error (for example an
unparsable type which returns NULL) for types which do \fBnot\fR match. So
applications should handle the case of two absent values separately.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBASN1_TYPE_get()\fR returns the type of the \fBASN1_TYPE\fR argument.
.PP
\&\fBASN1_TYPE_set()\fR does not return a value.
.PP
\&\fBASN1_TYPE_set1()\fR returns 1 for success and 0 for failure.
.PP
\&\fBASN1_TYPE_cmp()\fR returns 0 if the types are identical and nonzero otherwise.
.PP
\&\fBASN1_TYPE_unpack_sequence()\fR returns a pointer to an ASN.1 structure or
NULL on failure.
.PP
\&\fBASN1_TYPE_pack_sequence()\fR return an \fBASN1_TYPE\fR structure if it succeeds or
NULL on failure.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2015\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
