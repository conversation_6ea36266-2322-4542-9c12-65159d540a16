.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "OSSL_PARAM_DUP 3ossl"
.TH OSSL_PARAM_DUP 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
OSSL_PARAM_dup, OSSL_PARAM_merge, OSSL_PARAM_free
\&\- OSSL_PARAM array copy functions
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/params.h>
\&
\& OSSL_PARAM *OSSL_PARAM_dup(const OSSL_PARAM *params);
\& OSSL_PARAM *OSSL_PARAM_merge(const OSSL_PARAM *params, const OSSL_PARAM *params1);
\& void OSSL_PARAM_free(OSSL_PARAM *params);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
Algorithm parameters can be exported/imported from/to providers using arrays of
\&\fBOSSL_PARAM\fR\|(3). The following utility functions allow the parameters to be
duplicated and merged with other \fBOSSL_PARAM\fR\|(3) to assist in this process.
.PP
\&\fBOSSL_PARAM_dup()\fR duplicates the parameter array \fIparams\fR. This function does a
deep copy of the data.
.PP
\&\fBOSSL_PARAM_merge()\fR merges the parameter arrays \fIparams\fR and \fIparams1\fR into a
new parameter array. If \fIparams\fR and \fIparams1\fR contain values with the same
\&'key' then the value from \fIparams1\fR will replace the \fIparam\fR value. This
function does a shallow copy of the parameters. Either \fIparams\fR or \fIparams1\fR
may be NULL. The behaviour of the merge is unpredictable if \fIparams\fR and
\&\fIparams1\fR contain the same key, and there are multiple entries within either
array that have the same key.
.PP
\&\fBOSSL_PARAM_free()\fR frees the parameter array \fIparams\fR that was created using
\&\fBOSSL_PARAM_dup()\fR, \fBOSSL_PARAM_merge()\fR or \fBOSSL_PARAM_BLD_to_param()\fR.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
The functions \fBOSSL_PARAM_dup()\fR and \fBOSSL_PARAM_merge()\fR return a newly allocated
\&\fBOSSL_PARAM\fR\|(3) array, or NULL if there was an error. If both parameters are NULL
 then NULL is returned.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBOSSL_PARAM\fR\|(3), \fBOSSL_PARAM_BLD\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
The functions were added in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
