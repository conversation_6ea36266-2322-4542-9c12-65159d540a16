.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "PKCS7_DECRYPT 3ossl"
.TH PKCS7_DECRYPT 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
PKCS7_decrypt \- decrypt content from a PKCS#7 envelopedData structure
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/pkcs7.h>
\&
\& int PKCS7_decrypt(PKCS7 *p7, EVP_PKEY *pkey, X509 *cert, BIO *data, int flags);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBPKCS7_decrypt()\fR extracts and decrypts the content from a PKCS#7 envelopedData
structure. \fBpkey\fR is the private key of the recipient, \fBcert\fR is the
recipients certificate, \fBdata\fR is a BIO to write the content to and
\&\fBflags\fR is an optional set of flags.
.SH NOTES
.IX Header "NOTES"
Although the recipients certificate is not needed to decrypt the data it is needed
to locate the appropriate (of possible several) recipients in the PKCS#7 structure.
.PP
The following flags can be passed in the \fBflags\fR parameter.
.PP
If the \fBPKCS7_TEXT\fR flag is set MIME headers for type \fBtext/plain\fR are deleted
from the content. If the content is not of type \fBtext/plain\fR then an error is
returned.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBPKCS7_decrypt()\fR returns either 1 for success or 0 for failure.
The error can be obtained from \fBERR_get_error\fR\|(3)
.SH BUGS
.IX Header "BUGS"
\&\fBPKCS7_decrypt()\fR must be passed the correct recipient key and certificate. It would
be better if it could look up the correct key and certificate from a database.
.PP
The lack of single pass processing and need to hold all data in memory as
mentioned in \fBPKCS7_sign()\fR also applies to \fBPKCS7_verify()\fR.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBERR_get_error\fR\|(3), \fBPKCS7_encrypt\fR\|(3)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2002\-2016 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
