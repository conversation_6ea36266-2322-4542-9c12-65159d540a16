.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "SSL_CTX_SET0_CA_LIST 3ossl"
.TH SSL_CTX_SET0_CA_LIST 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
SSL_CTX_set_client_CA_list,
SSL_set_client_CA_list,
SSL_get_client_CA_list,
SSL_CTX_get_client_CA_list,
SSL_CTX_add_client_CA,
SSL_add_client_CA,
SSL_set0_CA_list,
SSL_CTX_set0_CA_list,
SSL_get0_CA_list,
SSL_CTX_get0_CA_list,
SSL_add1_to_CA_list,
SSL_CTX_add1_to_CA_list,
SSL_get0_peer_CA_list
\&\- get or set CA list
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/ssl.h>
\&
\& void SSL_CTX_set_client_CA_list(SSL_CTX *ctx, STACK_OF(X509_NAME) *list);
\& void SSL_set_client_CA_list(SSL *s, STACK_OF(X509_NAME) *list);
\& STACK_OF(X509_NAME) *SSL_get_client_CA_list(const SSL *s);
\& STACK_OF(X509_NAME) *SSL_CTX_get_client_CA_list(const SSL_CTX *ctx);
\& int SSL_CTX_add_client_CA(SSL_CTX *ctx, X509 *cacert);
\& int SSL_add_client_CA(SSL *ssl, X509 *cacert);
\&
\& void SSL_CTX_set0_CA_list(SSL_CTX *ctx, STACK_OF(X509_NAME) *name_list);
\& void SSL_set0_CA_list(SSL *s, STACK_OF(X509_NAME) *name_list);
\& const STACK_OF(X509_NAME) *SSL_CTX_get0_CA_list(const SSL_CTX *ctx);
\& const STACK_OF(X509_NAME) *SSL_get0_CA_list(const SSL *s);
\& int SSL_CTX_add1_to_CA_list(SSL_CTX *ctx, const X509 *x);
\& int SSL_add1_to_CA_list(SSL *ssl, const X509 *x);
\&
\& const STACK_OF(X509_NAME) *SSL_get0_peer_CA_list(const SSL *s);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
The functions described here set and manage the list of CA names that are sent
between two communicating peers.
.PP
For TLS versions 1.2 and earlier the list of CA names is only sent from the
server to the client when requesting a client certificate. So any list of CA
names set is never sent from client to server and the list of CA names retrieved
by \fBSSL_get0_peer_CA_list()\fR is always \fBNULL\fR.
.PP
For TLS 1.3 the list of CA names is sent using the \fBcertificate_authorities\fR
extension and may be sent by a client (in the ClientHello message) or by
a server (when requesting a certificate).
.PP
In most cases it is not necessary to set CA names on the client side. The list
of CA names that are acceptable to the client will be sent in plaintext to the
server. This has privacy implications and may also have performance implications
if the list is large. This optional capability was introduced as part of TLSv1.3
and therefore setting CA names on the client side will have no impact if that
protocol version has been disabled. Most servers do not need this and so this
should be avoided unless required.
.PP
The "client CA list" functions below only have an effect when called on the
server side.
.PP
\&\fBSSL_CTX_set_client_CA_list()\fR sets the \fBlist\fR of CAs sent to the client when
requesting a client certificate for \fBctx\fR. Ownership of \fBlist\fR is transferred
to \fBctx\fR and it should not be freed by the caller.
.PP
\&\fBSSL_set_client_CA_list()\fR sets the \fBlist\fR of CAs sent to the client when
requesting a client certificate for the chosen \fBssl\fR, overriding the
setting valid for \fBssl\fR's SSL_CTX object. Ownership of \fBlist\fR is transferred
to \fBs\fR and it should not be freed by the caller.
.PP
\&\fBSSL_CTX_get_client_CA_list()\fR returns the list of client CAs explicitly set for
\&\fBctx\fR using \fBSSL_CTX_set_client_CA_list()\fR. The returned list should not be freed
by the caller.
.PP
\&\fBSSL_get_client_CA_list()\fR returns the list of client CAs explicitly
set for \fBssl\fR using \fBSSL_set_client_CA_list()\fR or \fBssl\fR's SSL_CTX object with
\&\fBSSL_CTX_set_client_CA_list()\fR, when in server mode. In client mode,
SSL_get_client_CA_list returns the list of client CAs sent from the server, if
any. The returned list should not be freed by the caller.
.PP
\&\fBSSL_CTX_add_client_CA()\fR adds the CA name extracted from \fBcacert\fR to the
list of CAs sent to the client when requesting a client certificate for
\&\fBctx\fR.
.PP
\&\fBSSL_add_client_CA()\fR adds the CA name extracted from \fBcacert\fR to the
list of CAs sent to the client when requesting a client certificate for
the chosen \fBssl\fR, overriding the setting valid for \fBssl\fR's SSL_CTX object.
.PP
\&\fBSSL_get0_peer_CA_list()\fR retrieves the list of CA names (if any) the peer
has sent. This can be called on either the server or the client side. The
returned list should not be freed by the caller.
.PP
The "generic CA list" functions below are very similar to the "client CA
list" functions except that they have an effect on both the server and client
sides. The lists of CA names managed are separate \- so you cannot (for example)
set CA names using the "client CA list" functions and then get them using the
"generic CA list" functions. Where a mix of the two types of functions has been
used on the server side then the "client CA list" functions take precedence.
Typically, on the server side, the "client CA list " functions should be used in
preference. As noted above in most cases it is not necessary to set CA names on
the client side.
.PP
\&\fBSSL_CTX_set0_CA_list()\fR sets the list of CAs to be sent to the peer to
\&\fBname_list\fR. Ownership of \fBname_list\fR is transferred to \fBctx\fR and
it should not be freed by the caller.
.PP
\&\fBSSL_set0_CA_list()\fR sets the list of CAs to be sent to the peer to \fBname_list\fR
overriding any list set in the parent \fBSSL_CTX\fR of \fBs\fR. Ownership of
\&\fBname_list\fR is transferred to \fBs\fR and it should not be freed by the caller.
.PP
\&\fBSSL_CTX_get0_CA_list()\fR retrieves any previously set list of CAs set for
\&\fBctx\fR. The returned list should not be freed by the caller.
.PP
\&\fBSSL_get0_CA_list()\fR retrieves any previously set list of CAs set for
\&\fBs\fR or if none are set the list from the parent \fBSSL_CTX\fR is retrieved. The
returned list should not be freed by the caller.
.PP
\&\fBSSL_CTX_add1_to_CA_list()\fR appends the CA subject name extracted from \fBx\fR to the
list of CAs sent to peer for \fBctx\fR.
.PP
\&\fBSSL_add1_to_CA_list()\fR appends the CA subject name extracted from \fBx\fR to the
list of CAs sent to the peer for \fBs\fR, overriding the setting in the parent
\&\fBSSL_CTX\fR.
.SH NOTES
.IX Header "NOTES"
When a TLS/SSL server requests a client certificate (see
\&\fBSSL_CTX_set_verify\|(3)\fR), it sends a list of CAs, for which it will accept
certificates, to the client.
.PP
This list must explicitly be set using \fBSSL_CTX_set_client_CA_list()\fR or
\&\fBSSL_CTX_set0_CA_list()\fR for \fBctx\fR and \fBSSL_set_client_CA_list()\fR or
\&\fBSSL_set0_CA_list()\fR for the specific \fBssl\fR. The list specified
overrides the previous setting. The CAs listed do not become trusted (\fBlist\fR
only contains the names, not the complete certificates); use
\&\fBSSL_CTX_load_verify_locations\fR\|(3) to additionally load them for verification.
.PP
If the list of acceptable CAs is compiled in a file, the
\&\fBSSL_load_client_CA_file\fR\|(3) function can be used to help to import the
necessary data.
.PP
\&\fBSSL_CTX_add_client_CA()\fR, \fBSSL_CTX_add1_to_CA_list()\fR, \fBSSL_add_client_CA()\fR and
\&\fBSSL_add1_to_CA_list()\fR can be used to add additional items the list of CAs. If no
list was specified before using \fBSSL_CTX_set_client_CA_list()\fR,
\&\fBSSL_CTX_set0_CA_list()\fR, \fBSSL_set_client_CA_list()\fR or \fBSSL_set0_CA_list()\fR, a
new CA list for \fBctx\fR or \fBssl\fR (as appropriate) is opened.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBSSL_CTX_set_client_CA_list()\fR, \fBSSL_set_client_CA_list()\fR,
\&\fBSSL_CTX_set_client_CA_list()\fR, \fBSSL_set_client_CA_list()\fR, \fBSSL_CTX_set0_CA_list()\fR
and \fBSSL_set0_CA_list()\fR do not return a value.
.PP
\&\fBSSL_CTX_get_client_CA_list()\fR, \fBSSL_get_client_CA_list()\fR, \fBSSL_CTX_get0_CA_list()\fR
and \fBSSL_get0_CA_list()\fR return a stack of CA names or \fBNULL\fR is no CA names are
set.
.PP
\&\fBSSL_CTX_add_client_CA()\fR,\fBSSL_add_client_CA()\fR, \fBSSL_CTX_add1_to_CA_list()\fR and
\&\fBSSL_add1_to_CA_list()\fR return 1 for success and 0 for failure.
.PP
\&\fBSSL_get0_peer_CA_list()\fR returns a stack of CA names sent by the peer or
\&\fBNULL\fR or an empty stack if no list was sent.
.SH EXAMPLES
.IX Header "EXAMPLES"
Scan all certificates in \fBCAfile\fR and list them as acceptable CAs:
.PP
.Vb 1
\& SSL_CTX_set_client_CA_list(ctx, SSL_load_client_CA_file(CAfile));
.Ve
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBssl\fR\|(7),
\&\fBSSL_load_client_CA_file\fR\|(3),
\&\fBSSL_CTX_load_verify_locations\fR\|(3)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2018 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
