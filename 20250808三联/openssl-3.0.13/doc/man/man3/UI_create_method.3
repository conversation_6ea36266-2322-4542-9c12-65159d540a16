.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "UI_CREATE_METHOD 3ossl"
.TH UI_CREATE_METHOD 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
UI_METHOD,
UI_create_method, UI_destroy_method, UI_method_set_opener,
UI_method_set_writer, UI_method_set_flusher, UI_method_set_reader,
UI_method_set_closer, UI_method_set_data_duplicator,
UI_method_set_prompt_constructor, UI_method_set_ex_data,
UI_method_get_opener, UI_method_get_writer, UI_method_get_flusher,
UI_method_get_reader, UI_method_get_closer,
UI_method_get_data_duplicator, UI_method_get_data_destructor,
UI_method_get_prompt_constructor, UI_method_get_ex_data \- user
interface method creation and destruction
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/ui.h>
\&
\& typedef struct ui_method_st UI_METHOD;
\&
\& UI_METHOD *UI_create_method(const char *name);
\& void UI_destroy_method(UI_METHOD *ui_method);
\& int UI_method_set_opener(UI_METHOD *method, int (*opener) (UI *ui));
\& int UI_method_set_writer(UI_METHOD *method,
\&                          int (*writer) (UI *ui, UI_STRING *uis));
\& int UI_method_set_flusher(UI_METHOD *method, int (*flusher) (UI *ui));
\& int UI_method_set_reader(UI_METHOD *method,
\&                          int (*reader) (UI *ui, UI_STRING *uis));
\& int UI_method_set_closer(UI_METHOD *method, int (*closer) (UI *ui));
\& int UI_method_set_data_duplicator(UI_METHOD *method,
\&                                   void *(*duplicator) (UI *ui, void *ui_data),
\&                                   void (*destructor)(UI *ui, void *ui_data));
\& int UI_method_set_prompt_constructor(UI_METHOD *method,
\&                                      char *(*prompt_constructor) (UI *ui,
\&                                                                   const char
\&                                                                   *object_desc,
\&                                                                   const char
\&                                                                   *object_name));
\& int UI_method_set_ex_data(UI_METHOD *method, int idx, void *data);
\& int (*UI_method_get_opener(const UI_METHOD *method)) (UI *);
\& int (*UI_method_get_writer(const UI_METHOD *method)) (UI *, UI_STRING *);
\& int (*UI_method_get_flusher(const UI_METHOD *method)) (UI *);
\& int (*UI_method_get_reader(const UI_METHOD *method)) (UI *, UI_STRING *);
\& int (*UI_method_get_closer(const UI_METHOD *method)) (UI *);
\& char *(*UI_method_get_prompt_constructor(const UI_METHOD *method))
\&     (UI *, const char *, const char *);
\& void *(*UI_method_get_data_duplicator(const UI_METHOD *method)) (UI *, void *);
\& void (*UI_method_get_data_destructor(const UI_METHOD *method)) (UI *, void *);
\& const void *UI_method_get_ex_data(const UI_METHOD *method, int idx);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
A method contains a few functions that implement the low-level of the
User Interface.
These functions are:
.IP "an opener" 4
.IX Item "an opener"
This function takes a reference to a UI and starts a session, for
example by opening a channel to a tty, or by creating a dialog box.
.IP "a writer" 4
.IX Item "a writer"
This function takes a reference to a UI and a UI String, and writes
the string where appropriate, maybe to the tty, maybe added as a field
label in a dialog box.
Note that this gets fed all strings associated with a UI, one after
the other, so care must be taken which ones it actually uses.
.IP "a flusher" 4
.IX Item "a flusher"
This function takes a reference to a UI, and flushes everything that
has been output so far.
For example, if the method builds up a dialog box, this can be used to
actually display it and accepting input ended with a pressed button.
.IP "a reader" 4
.IX Item "a reader"
This function takes a reference to a UI and a UI string and reads off
the given prompt, maybe from the tty, maybe from a field in a dialog
box.
Note that this gets fed all strings associated with a UI, one after
the other, so care must be taken which ones it actually uses.
.IP "a closer" 4
.IX Item "a closer"
This function takes a reference to a UI, and closes the session, maybe
by closing the channel to the tty, maybe by destroying a dialog box.
.PP
All of these functions are expected to return 0 on error, 1 on
success, or \-1 on out-off-band events, for example if some prompting
has been cancelled (by pressing Ctrl-C, for example).
Only the flusher or the reader are expected to return \-1.
If returned by another of the functions, it's treated as if 0 was
returned.
.PP
Regarding the writer and the reader, don't assume the former should
only write and don't assume the latter should only read.
This depends on the needs of the method.
.PP
For example, a typical tty reader wouldn't write the prompts in the
write, but would rather do so in the reader, because of the sequential
nature of prompting on a tty.
This is how the \fBUI_OpenSSL()\fR method does it.
.PP
In contrast, a method that builds up a dialog box would add all prompt
text in the writer, have all input read in the flusher and store the
results in some temporary buffer, and finally have the reader just
fetch those results.
.PP
The central function that uses these method functions is \fBUI_process()\fR,
and it does it in five steps:
.IP 1. 4
Open the session using the opener function if that one's defined.
If an error occurs, jump to 5.
.IP 2. 4
For every UI String associated with the UI, call the writer function
if that one's defined.
If an error occurs, jump to 5.
.IP 3. 4
Flush everything using the flusher function if that one's defined.
If an error occurs, jump to 5.
.IP 4. 4
For every UI String associated with the UI, call the reader function
if that one's defined.
If an error occurs, jump to 5.
.IP 5. 4
Close the session using the closer function if that one's defined.
.PP
\&\fBUI_create_method()\fR creates a new UI method with a given \fBname\fR.
.PP
\&\fBUI_destroy_method()\fR destroys the given UI method \fBui_method\fR.
.PP
\&\fBUI_method_set_opener()\fR, \fBUI_method_set_writer()\fR,
\&\fBUI_method_set_flusher()\fR, \fBUI_method_set_reader()\fR and
\&\fBUI_method_set_closer()\fR set the five main method function to the given
function pointer.
.PP
\&\fBUI_method_set_data_duplicator()\fR sets the user data duplicator and destructor.
See \fBUI_dup_user_data\fR\|(3).
.PP
\&\fBUI_method_set_prompt_constructor()\fR sets the prompt constructor.
See \fBUI_construct_prompt\fR\|(3).
.PP
\&\fBUI_method_set_ex_data()\fR sets application specific data with a given
EX_DATA index.
See \fBCRYPTO_get_ex_new_index\fR\|(3) for general information on how to
get that index.
.PP
\&\fBUI_method_get_opener()\fR, \fBUI_method_get_writer()\fR,
\&\fBUI_method_get_flusher()\fR, \fBUI_method_get_reader()\fR,
\&\fBUI_method_get_closer()\fR, \fBUI_method_get_data_duplicator()\fR,
\&\fBUI_method_get_data_destructor()\fR and \fBUI_method_get_prompt_constructor()\fR
return the different method functions.
.PP
\&\fBUI_method_get_ex_data()\fR returns the application data previously stored
with \fBUI_method_set_ex_data()\fR.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBUI_create_method()\fR returns a UI_METHOD pointer on success, NULL on
error.
.PP
\&\fBUI_method_set_opener()\fR, \fBUI_method_set_writer()\fR,
\&\fBUI_method_set_flusher()\fR, \fBUI_method_set_reader()\fR,
\&\fBUI_method_set_closer()\fR, \fBUI_method_set_data_duplicator()\fR and
\&\fBUI_method_set_prompt_constructor()\fR
return 0 on success, \-1 if the given \fBmethod\fR is NULL.
.PP
\&\fBUI_method_set_ex_data()\fR returns 1 on success and 0 on error (because
\&\fBCRYPTO_set_ex_data()\fR does so).
.PP
\&\fBUI_method_get_opener()\fR, \fBUI_method_get_writer()\fR,
\&\fBUI_method_get_flusher()\fR, \fBUI_method_get_reader()\fR,
\&\fBUI_method_get_closer()\fR, \fBUI_method_get_data_duplicator()\fR,
\&\fBUI_method_get_data_destructor()\fR and \fBUI_method_get_prompt_constructor()\fR
return the requested function pointer if it's set in the method,
otherwise NULL.
.PP
\&\fBUI_method_get_ex_data()\fR returns a pointer to the application specific
data associated with the method.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBUI\fR\|(3), \fBCRYPTO_get_ex_data\fR\|(3), \fBUI_STRING\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
The \fBUI_method_set_data_duplicator()\fR, \fBUI_method_get_data_duplicator()\fR
and \fBUI_method_get_data_destructor()\fR functions were added in OpenSSL 1.1.1.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2001\-2020 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
