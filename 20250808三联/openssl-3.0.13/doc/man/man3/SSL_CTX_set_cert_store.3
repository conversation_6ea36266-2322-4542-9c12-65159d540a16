.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "SSL_CTX_SET_CERT_STORE 3ossl"
.TH SSL_CTX_SET_CERT_STORE 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
SSL_CTX_set_cert_store, SSL_CTX_set1_cert_store, SSL_CTX_get_cert_store \- manipulate X509 certificate verification storage
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/ssl.h>
\&
\& void SSL_CTX_set_cert_store(SSL_CTX *ctx, X509_STORE *store);
\& void SSL_CTX_set1_cert_store(SSL_CTX *ctx, X509_STORE *store);
\& X509_STORE *SSL_CTX_get_cert_store(const SSL_CTX *ctx);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBSSL_CTX_set_cert_store()\fR sets/replaces the certificate verification storage
of \fBctx\fR to/with \fBstore\fR. If another X509_STORE object is currently
set in \fBctx\fR, it will be \fBX509_STORE_free()\fRed.
.PP
\&\fBSSL_CTX_set1_cert_store()\fR sets/replaces the certificate verification storage
of \fBctx\fR to/with \fBstore\fR. The \fBstore\fR's reference count is incremented.
If another X509_STORE object is currently set in \fBctx\fR, it will be \fBX509_STORE_free()\fRed.
.PP
\&\fBSSL_CTX_get_cert_store()\fR returns a pointer to the current certificate
verification storage.
.SH NOTES
.IX Header "NOTES"
In order to verify the certificates presented by the peer, trusted CA
certificates must be accessed. These CA certificates are made available
via lookup methods, handled inside the X509_STORE. From the X509_STORE
the X509_STORE_CTX used when verifying certificates is created.
.PP
Typically the trusted certificate store is handled indirectly via using
\&\fBSSL_CTX_load_verify_locations\fR\|(3).
Using the \fBSSL_CTX_set_cert_store()\fR and \fBSSL_CTX_get_cert_store()\fR functions
it is possible to manipulate the X509_STORE object beyond the
\&\fBSSL_CTX_load_verify_locations\fR\|(3)
call.
.PP
Currently no detailed documentation on how to use the X509_STORE
object is available. Not all members of the X509_STORE are used when
the verification takes place. So will e.g. the \fBverify_callback()\fR be
overridden with the \fBverify_callback()\fR set via the
\&\fBSSL_CTX_set_verify\fR\|(3) family of functions.
This document must therefore be updated when documentation about the
X509_STORE object and its handling becomes available.
.PP
\&\fBSSL_CTX_set_cert_store()\fR does not increment the \fBstore\fR's reference
count, so it should not be used to assign an X509_STORE that is owned
by another SSL_CTX.
.PP
To share X509_STOREs between two SSL_CTXs, use \fBSSL_CTX_get_cert_store()\fR
to get the X509_STORE from the first SSL_CTX, and then use
\&\fBSSL_CTX_set1_cert_store()\fR to assign to the second SSL_CTX and
increment the reference count of the X509_STORE.
.SH RESTRICTIONS
.IX Header "RESTRICTIONS"
The X509_STORE structure used by an SSL_CTX is used for verifying peer
certificates and building certificate chains, it is also shared by
every child SSL structure. Applications wanting finer control can use
functions such as \fBSSL_CTX_set1_verify_cert_store()\fR instead.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBSSL_CTX_set_cert_store()\fR does not return diagnostic output.
.PP
\&\fBSSL_CTX_set1_cert_store()\fR does not return diagnostic output.
.PP
\&\fBSSL_CTX_get_cert_store()\fR returns the current setting.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBssl\fR\|(7),
\&\fBSSL_CTX_load_verify_locations\fR\|(3),
\&\fBSSL_CTX_set_verify\fR\|(3)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2001\-2016 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
