.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "SSL_CTX_NEW 3ossl"
.TH SSL_CTX_NEW 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
TLSv1_2_method, TLSv1_2_server_method, TLSv1_2_client_method,
SSL_CTX_new, SSL_CTX_new_ex, SSL_CTX_up_ref, SSLv3_method,
SSLv3_server_method, SSLv3_client_method, TLSv1_method, TLSv1_server_method,
TLSv1_client_method, TLSv1_1_method, TLSv1_1_server_method,
TLSv1_1_client_method, TLS_method, TLS_server_method, TLS_client_method,
SSLv23_method, SSLv23_server_method, SSLv23_client_method, DTLS_method,
DTLS_server_method, DTLS_client_method, DTLSv1_method, DTLSv1_server_method,
DTLSv1_client_method, DTLSv1_2_method, DTLSv1_2_server_method,
DTLSv1_2_client_method
\&\- create a new SSL_CTX object as framework for TLS/SSL or DTLS enabled
functions
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/ssl.h>
\&
\& SSL_CTX *SSL_CTX_new_ex(OSSL_LIB_CTX *libctx, const char *propq,
\&                         const SSL_METHOD *method);
\& SSL_CTX *SSL_CTX_new(const SSL_METHOD *method);
\& int SSL_CTX_up_ref(SSL_CTX *ctx);
\&
\& const SSL_METHOD *TLS_method(void);
\& const SSL_METHOD *TLS_server_method(void);
\& const SSL_METHOD *TLS_client_method(void);
\&
\& const SSL_METHOD *SSLv23_method(void);
\& const SSL_METHOD *SSLv23_server_method(void);
\& const SSL_METHOD *SSLv23_client_method(void);
\&
\& #ifndef OPENSSL_NO_SSL3_METHOD
\& const SSL_METHOD *SSLv3_method(void);
\& const SSL_METHOD *SSLv3_server_method(void);
\& const SSL_METHOD *SSLv3_client_method(void);
\& #endif
\&
\& #ifndef OPENSSL_NO_TLS1_METHOD
\& const SSL_METHOD *TLSv1_method(void);
\& const SSL_METHOD *TLSv1_server_method(void);
\& const SSL_METHOD *TLSv1_client_method(void);
\& #endif
\&
\& #ifndef OPENSSL_NO_TLS1_1_METHOD
\& const SSL_METHOD *TLSv1_1_method(void);
\& const SSL_METHOD *TLSv1_1_server_method(void);
\& const SSL_METHOD *TLSv1_1_client_method(void);
\& #endif
\&
\& #ifndef OPENSSL_NO_TLS1_2_METHOD
\& const SSL_METHOD *TLSv1_2_method(void);
\& const SSL_METHOD *TLSv1_2_server_method(void);
\& const SSL_METHOD *TLSv1_2_client_method(void);
\& #endif
\&
\& const SSL_METHOD *DTLS_method(void);
\& const SSL_METHOD *DTLS_server_method(void);
\& const SSL_METHOD *DTLS_client_method(void);
\&
\& #ifndef OPENSSL_NO_DTLS1_METHOD
\& const SSL_METHOD *DTLSv1_method(void);
\& const SSL_METHOD *DTLSv1_server_method(void);
\& const SSL_METHOD *DTLSv1_client_method(void);
\& #endif
\&
\& #ifndef OPENSSL_NO_DTLS1_2_METHOD
\& const SSL_METHOD *DTLSv1_2_method(void);
\& const SSL_METHOD *DTLSv1_2_server_method(void);
\& const SSL_METHOD *DTLSv1_2_client_method(void);
\& #endif
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBSSL_CTX_new_ex()\fR creates a new \fBSSL_CTX\fR object, which holds various
configuration and data relevant to SSL/TLS or DTLS session establishment.
These are later inherited by the \fBSSL\fR object representing an active session.
The \fImethod\fR parameter specifies whether the context will be used for the
client or server side or both \- for details see the "NOTES" below.
The library context \fIlibctx\fR (see \fBOSSL_LIB_CTX\fR\|(3)) is used to provide the
cryptographic algorithms needed for the session. Any cryptographic algorithms
that are used by any \fBSSL\fR objects created from this \fBSSL_CTX\fR will be fetched
from the \fIlibctx\fR using the property query string \fIpropq\fR (see
"ALGORITHM FETCHING" in \fBcrypto\fR\|(7). Either or both the \fIlibctx\fR or \fIpropq\fR
parameters may be NULL.
.PP
\&\fBSSL_CTX_new()\fR does the same as \fBSSL_CTX_new_ex()\fR except that the default
library context is used and no property query string is specified.
.PP
An \fBSSL_CTX\fR object is reference counted. Creating an \fBSSL_CTX\fR object for the
first time increments the reference count. Freeing the \fBSSL_CTX\fR (using
SSL_CTX_free) decrements it. When the reference count drops to zero, any memory
or resources allocated to the \fBSSL_CTX\fR object are freed. \fBSSL_CTX_up_ref()\fR
increments the reference count for an existing \fBSSL_CTX\fR structure.
.PP
An \fBSSL_CTX\fR object should not be changed after it is used to create any \fBSSL\fR
objects or from multiple threads concurrently, since the implementation does not
provide serialization of access for these cases.
.SH NOTES
.IX Header "NOTES"
On session establishment, by default, no peer credentials verification is done.
This must be explicitly requested, typically using \fBSSL_CTX_set_verify\fR\|(3).
For verifying peer certificates many options can be set using various functions
such as \fBSSL_CTX_load_verify_locations\fR\|(3) and \fBSSL_CTX_set1_param\fR\|(3).
The \fBX509_VERIFY_PARAM_set_purpose\fR\|(3) function can be used, also in conjunction
with \fBSSL_CTX_get0_param\fR\|(3), to set the intended purpose of the session.
The default is \fBX509_PURPOSE_SSL_SERVER\fR on the client side
and \fBX509_PURPOSE_SSL_CLIENT\fR on the server side.
.PP
The SSL_CTX object uses \fImethod\fR as the connection method.
Three method variants are available: a generic method (for either client or
server use), a server-only method, and a client-only method.
.PP
The \fImethod\fR parameter of \fBSSL_CTX_new_ex()\fR and \fBSSL_CTX_new()\fR
can be one of the following:
.IP "\fBTLS_method()\fR, \fBTLS_server_method()\fR, \fBTLS_client_method()\fR" 4
.IX Item "TLS_method(), TLS_server_method(), TLS_client_method()"
These are the general-purpose \fIversion-flexible\fR SSL/TLS methods.
The actual protocol version used will be negotiated to the highest version
mutually supported by the client and the server.
The supported protocols are SSLv3, TLSv1, TLSv1.1, TLSv1.2 and TLSv1.3.
Applications should use these methods, and avoid the version-specific
methods described below, which are deprecated.
.IP "\fBSSLv23_method()\fR, \fBSSLv23_server_method()\fR, \fBSSLv23_client_method()\fR" 4
.IX Item "SSLv23_method(), SSLv23_server_method(), SSLv23_client_method()"
These functions do not exist anymore, they have been renamed to
\&\fBTLS_method()\fR, \fBTLS_server_method()\fR and \fBTLS_client_method()\fR respectively.
Currently, the old function calls are renamed to the corresponding new
ones by preprocessor macros, to ensure that existing code which uses the
old function names still compiles. However, using the old function names
is deprecated and new code should call the new functions instead.
.IP "\fBTLSv1_2_method()\fR, \fBTLSv1_2_server_method()\fR, \fBTLSv1_2_client_method()\fR" 4
.IX Item "TLSv1_2_method(), TLSv1_2_server_method(), TLSv1_2_client_method()"
A TLS/SSL connection established with these methods will only understand the
TLSv1.2 protocol. These methods are deprecated.
.IP "\fBTLSv1_1_method()\fR, \fBTLSv1_1_server_method()\fR, \fBTLSv1_1_client_method()\fR" 4
.IX Item "TLSv1_1_method(), TLSv1_1_server_method(), TLSv1_1_client_method()"
A TLS/SSL connection established with these methods will only understand the
TLSv1.1 protocol.  These methods are deprecated.
.IP "\fBTLSv1_method()\fR, \fBTLSv1_server_method()\fR, \fBTLSv1_client_method()\fR" 4
.IX Item "TLSv1_method(), TLSv1_server_method(), TLSv1_client_method()"
A TLS/SSL connection established with these methods will only understand the
TLSv1 protocol. These methods are deprecated.
.IP "\fBSSLv3_method()\fR, \fBSSLv3_server_method()\fR, \fBSSLv3_client_method()\fR" 4
.IX Item "SSLv3_method(), SSLv3_server_method(), SSLv3_client_method()"
A TLS/SSL connection established with these methods will only understand the
SSLv3 protocol.
The SSLv3 protocol is deprecated and should not be used.
.IP "\fBDTLS_method()\fR, \fBDTLS_server_method()\fR, \fBDTLS_client_method()\fR" 4
.IX Item "DTLS_method(), DTLS_server_method(), DTLS_client_method()"
These are the version-flexible DTLS methods.
Currently supported protocols are DTLS 1.0 and DTLS 1.2.
.IP "\fBDTLSv1_2_method()\fR, \fBDTLSv1_2_server_method()\fR, \fBDTLSv1_2_client_method()\fR" 4
.IX Item "DTLSv1_2_method(), DTLSv1_2_server_method(), DTLSv1_2_client_method()"
These are the version-specific methods for DTLSv1.2.
These methods are deprecated.
.IP "\fBDTLSv1_method()\fR, \fBDTLSv1_server_method()\fR, \fBDTLSv1_client_method()\fR" 4
.IX Item "DTLSv1_method(), DTLSv1_server_method(), DTLSv1_client_method()"
These are the version-specific methods for DTLSv1.
These methods are deprecated.
.PP
\&\fBSSL_CTX_new()\fR initializes the list of ciphers, the session cache setting, the
callbacks, the keys and certificates and the options to their default values.
.PP
\&\fBTLS_method()\fR, \fBTLS_server_method()\fR, \fBTLS_client_method()\fR, \fBDTLS_method()\fR,
\&\fBDTLS_server_method()\fR and \fBDTLS_client_method()\fR are the \fIversion-flexible\fR
methods.
All other methods only support one specific protocol version.
Use the \fIversion-flexible\fR methods instead of the version specific methods.
.PP
If you want to limit the supported protocols for the version flexible
methods you can use \fBSSL_CTX_set_min_proto_version\fR\|(3),
\&\fBSSL_set_min_proto_version\fR\|(3), \fBSSL_CTX_set_max_proto_version\fR\|(3) and
\&\fBSSL_set_max_proto_version\fR\|(3) functions.
Using these functions it is possible to choose e.g. \fBTLS_server_method()\fR
and be able to negotiate with all possible clients, but to only
allow newer protocols like TLS 1.0, TLS 1.1, TLS 1.2 or TLS 1.3.
.PP
The list of protocols available can also be limited using the
\&\fBSSL_OP_NO_SSLv3\fR, \fBSSL_OP_NO_TLSv1\fR, \fBSSL_OP_NO_TLSv1_1\fR,
\&\fBSSL_OP_NO_TLSv1_3\fR, \fBSSL_OP_NO_TLSv1_2\fR and \fBSSL_OP_NO_TLSv1_3\fR
options of the
\&\fBSSL_CTX_set_options\fR\|(3) or \fBSSL_set_options\fR\|(3) functions, but this approach
is not recommended. Clients should avoid creating "holes" in the set of
protocols they support. When disabling a protocol, make sure that you also
disable either all previous or all subsequent protocol versions.
In clients, when a protocol version is disabled without disabling \fIall\fR
previous protocol versions, the effect is to also disable all subsequent
protocol versions.
.PP
The SSLv3 protocol is deprecated and should generally not be used.
Applications should typically use \fBSSL_CTX_set_min_proto_version\fR\|(3) to set
the minimum protocol to at least \fBTLS1_VERSION\fR.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
The following return values can occur:
.IP NULL 4
.IX Item "NULL"
The creation of a new SSL_CTX object failed. Check the error stack to find out
the reason.
.IP "Pointer to an SSL_CTX object" 4
.IX Item "Pointer to an SSL_CTX object"
The return value points to an allocated SSL_CTX object.
.Sp
\&\fBSSL_CTX_up_ref()\fR returns 1 for success and 0 for failure.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBSSL_CTX_set_options\fR\|(3), \fBSSL_CTX_free\fR\|(3),
\&\fBSSL_CTX_set_verify\fR\|(3), \fBSSL_CTX_set1_param\fR\|(3), \fBSSL_CTX_get0_param\fR\|(3),
\&\fBSSL_connect\fR\|(3), \fBSSL_accept\fR\|(3),
\&\fBSSL_CTX_set_min_proto_version\fR\|(3), \fBssl\fR\|(7), \fBSSL_set_connect_state\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
Support for SSLv2 and the corresponding \fBSSLv2_method()\fR,
\&\fBSSLv2_server_method()\fR and \fBSSLv2_client_method()\fR functions where
removed in OpenSSL 1.1.0.
.PP
\&\fBSSLv23_method()\fR, \fBSSLv23_server_method()\fR and \fBSSLv23_client_method()\fR
were deprecated and the preferred \fBTLS_method()\fR, \fBTLS_server_method()\fR
and \fBTLS_client_method()\fR functions were added in OpenSSL 1.1.0.
.PP
All version-specific methods were deprecated in OpenSSL 1.1.0.
.PP
\&\fBSSL_CTX_new_ex()\fR was added in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2023 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
