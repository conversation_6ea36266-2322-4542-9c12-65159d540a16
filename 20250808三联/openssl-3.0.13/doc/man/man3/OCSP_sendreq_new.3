.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "OCSP_SENDREQ_NEW 3ossl"
.TH OCSP_SENDREQ_NEW 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
OCSP_REQ_CTX,
OCSP_sendreq_new,
OCSP_sendreq_nbio,
OCSP_sendreq_bio,
OCSP_REQ_CTX_i2d,
OCSP_REQ_CTX_add1_header,
OCSP_REQ_CTX_free,
OCSP_set_max_response_length,
OCSP_REQ_CTX_set1_req
\&\- OCSP responder query functions
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/ocsp.h>
\&
\& OSSL_HTTP_REQ_CTX *OCSP_sendreq_new(BIO *io, const char *path,
\&                                     const OCSP_REQUEST *req, int buf_size);
\& OCSP_RESPONSE *OCSP_sendreq_bio(BIO *io, const char *path, OCSP_REQUEST *req);
.Ve
.PP
The following functions have been deprecated since OpenSSL 3.0, and can be
hidden entirely by defining \fBOPENSSL_API_COMPAT\fR with a suitable version value,
see \fBopenssl_user_macros\fR\|(7):
.PP
.Vb 8
\& typedef OSSL_HTTP_REQ_CTX OCSP_REQ_CTX;
\& int OCSP_sendreq_nbio(OCSP_RESPONSE **presp, OSSL_HTTP_REQ_CTX *rctx);
\& int OCSP_REQ_CTX_i2d(OCSP_REQ_CT *rctx, const ASN1_ITEM *it, ASN1_VALUE *req);
\& int OCSP_REQ_CTX_add1_header(OCSP_REQ_CT *rctx,
\&                              const char *name, const char *value);
\& void OCSP_REQ_CTX_free(OCSP_REQ_CTX *rctx);
\& void OCSP_set_max_response_length(OCSP_REQ_CT *rctx, unsigned long len);
\& int OCSP_REQ_CTX_set1_req(OCSP_REQ_CTX *rctx, const OCSP_REQUEST *req);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
These functions perform an OCSP POST request / response transfer over HTTP,
using the HTTP request functions described in \fBOSSL_HTTP_REQ_CTX\fR\|(3).
.PP
The function \fBOCSP_sendreq_new()\fR builds a complete \fBOSSL_HTTP_REQ_CTX\fR structure
with the \fBBIO\fR \fIio\fR to be used for requests and response, the URL path \fIpath\fR,
optionally the OCSP request \fIreq\fR, and a response header maximum line length
of \fIbuf_size\fR. If \fIbuf_size\fR is zero a default value of 4KiB is used.
The \fIreq\fR may be set to NULL and provided later using \fBOCSP_REQ_CTX_set1_req()\fR
or \fBOSSL_HTTP_REQ_CTX_set1_req\fR\|(3).
The \fIio\fR and \fIpath\fR arguments to \fBOCSP_sendreq_new()\fR correspond to the
components of the URL.
For example if the responder URL is \f(CW\*(C`http://example.com/ocspreq\*(C'\fR the BIO
\&\fIio\fR should haven been connected to host \f(CW\*(C`example.com\*(C'\fR on port 80 and \fIpath\fR
should be set to \f(CW\*(C`/ocspreq\*(C'\fR.
.PP
\&\fBOCSP_sendreq_nbio()\fR attempts to send the request prepared in \fIrctx\fR
and to gather the response via HTTP, using the BIO \fIio\fR and \fIpath\fR
that were given when calling \fBOCSP_sendreq_new()\fR.
If the operation gets completed it assigns the response,
a pointer to a \fBOCSP_RESPONSE\fR structure, in \fI*presp\fR.
The function may need to be called again if its result is \-1, which indicates
\&\fBBIO_should_retry\fR\|(3).  In such a case it is advisable to sleep a little in
between, using \fBBIO_wait\fR\|(3) on the read BIO to prevent a busy loop.
.PP
\&\fBOCSP_sendreq_bio()\fR combines \fBOCSP_sendreq_new()\fR with as many calls of
\&\fBOCSP_sendreq_nbio()\fR as needed and then \fBOCSP_REQ_CTX_free()\fR, with a
response header maximum line length 4k. It waits indefinitely on a response.
It does not support setting a timeout or adding headers and is retained
for compatibility; use \fBOSSL_HTTP_transfer\fR\|(3) instead.
.PP
OCSP_REQ_CTX_i2d(rctx, it, req) is equivalent to the following:
.PP
.Vb 1
\&  OSSL_HTTP_REQ_CTX_set1_req(rctx, "application/ocsp\-request", it, req)
.Ve
.PP
OCSP_REQ_CTX_set1_req(rctx, req) is equivalent to the following:
.PP
.Vb 3
\& OSSL_HTTP_REQ_CTX_set1_req(rctx, "application/ocsp\-request",
\&                            ASN1_ITEM_rptr(OCSP_REQUEST),
\&                            (const ASN1_VALUE *)req)
.Ve
.PP
The deprecated type and the remaining deprecated functions
have been superseded by the following equivalents:
\&\fBOCSP_REQ_CTX\fR by \fBOSSL_HTTP_REQ_CTX\fR\|(3),
\&\fBOCSP_REQ_CTX_add1_header()\fR by \fBOSSL_HTTP_REQ_CTX_add1_header\fR\|(3),
\&\fBOCSP_REQ_CTX_free()\fR by \fBOSSL_HTTP_REQ_CTX_free\fR\|(3), and
\&\fBOCSP_set_max_response_length()\fR by
\&\fBOSSL_HTTP_REQ_CTX_set_max_response_length\fR\|(3).
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBOCSP_sendreq_new()\fR returns a valid \fBOSSL_HTTP_REQ_CTX\fR structure or NULL
if an error occurred.
.PP
\&\fBOCSP_sendreq_nbio()\fR returns 1 for success, 0 on error, \-1 if retry is needed.
.PP
\&\fBOCSP_sendreq_bio()\fR returns the \fBOCSP_RESPONSE\fR structure sent by the
responder or NULL if an error occurred.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBOSSL_HTTP_REQ_CTX\fR\|(3), \fBOSSL_HTTP_transfer\fR\|(3),
\&\fBOCSP_cert_to_id\fR\|(3),
\&\fBOCSP_request_add1_nonce\fR\|(3),
\&\fBOCSP_REQUEST_new\fR\|(3),
\&\fBOCSP_resp_find_status\fR\|(3),
\&\fBOCSP_response_status\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
\&\fBOCSP_REQ_CTX\fR,
\&\fBOCSP_REQ_CTX_i2d()\fR,
\&\fBOCSP_REQ_CTX_add1_header()\fR,
\&\fBOCSP_REQ_CTX_free()\fR,
\&\fBOCSP_set_max_response_length()\fR,
and \fBOCSP_REQ_CTX_set1_req()\fR
were deprecated in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2015\-2023 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
