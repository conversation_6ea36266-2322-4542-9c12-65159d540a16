.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "PKCS8_ENCRYPT 3ossl"
.TH PKCS8_ENCRYPT 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
PKCS8_decrypt, PKCS8_decrypt_ex, PKCS8_encrypt, PKCS8_encrypt_ex,
PKCS8_set0_pbe, PKCS8_set0_pbe_ex \- PKCS8 encrypt/decrypt functions
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/x509.h>
\&
\& PKCS8_PRIV_KEY_INFO *PKCS8_decrypt(const X509_SIG *p8, const char *pass,
\&                                    int passlen);
\& PKCS8_PRIV_KEY_INFO *PKCS8_decrypt_ex(const X509_SIG *p8, const char *pass,
\&                                       int passlen, OSSL_LIB_CTX *ctx,
\&                                       const char *propq);
\& X509_SIG *PKCS8_encrypt(int pbe_nid, const EVP_CIPHER *cipher,
\&                         const char *pass, int passlen, unsigned char *salt,
\&                         int saltlen, int iter, PKCS8_PRIV_KEY_INFO *p8);
\& X509_SIG *PKCS8_encrypt_ex(int pbe_nid, const EVP_CIPHER *cipher,
\&                            const char *pass, int passlen, unsigned char *salt,
\&                            int saltlen, int iter, PKCS8_PRIV_KEY_INFO *p8,
\&                            OSSL_LIB_CTX *ctx, const char *propq);
\& X509_SIG *PKCS8_set0_pbe(const char *pass, int passlen,
\&                         PKCS8_PRIV_KEY_INFO *p8inf, X509_ALGOR *pbe);
\& X509_SIG *PKCS8_set0_pbe_ex(const char *pass, int passlen,
\&                             PKCS8_PRIV_KEY_INFO *p8inf, X509_ALGOR *pbe,
\&                             OSSL_LIB_CTX *ctx);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBPKCS8_encrypt()\fR and \fBPKCS8_encrypt_ex()\fR perform encryption of an object \fIp8\fR using
the password \fIpass\fR of length \fIpasslen\fR, salt \fIsalt\fR of length \fIsaltlen\fR
and iteration count \fIiter\fR.
The resulting \fBX509_SIG\fR contains the encoded algorithm parameters and encrypted
key.
.PP
\&\fBPKCS8_decrypt()\fR and \fBPKCS8_decrypt_ex()\fR perform decryption of an \fBX509_SIG\fR in
\&\fIp8\fR using the password \fIpass\fR of length \fIpasslen\fR along with algorithm
parameters obtained from the \fIp8\fR.
.PP
\&\fBPKCS8_set0_pbe()\fR and \fBPKCS8_set0_pbe_ex()\fR perform encryption of the \fIp8inf\fR
using the password \fIpass\fR of length \fIpasslen\fR and parameters \fIpbe\fR.
.PP
Functions ending in \fB_ex()\fR allow for a library context \fIctx\fR and property query
\&\fIpropq\fR to be used to select algorithm implementations.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBPKCS8_encrypt()\fR, \fBPKCS8_encrypt_ex()\fR, \fBPKCS8_set0_pbe()\fR and \fBPKCS8_set0_pbe_ex()\fR
return an encrypted key in a \fBX509_SIG\fR structure or NULL if an error occurs.
.PP
\&\fBPKCS8_decrypt()\fR and \fBPKCS8_decrypt_ex()\fR return a \fBPKCS8_PRIV_KEY_INFO\fR or NULL
if an error occurs.
.SH "CONFORMING TO"
.IX Header "CONFORMING TO"
IETF RFC 7292 (<https://tools.ietf.org/html/rfc7292>)
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBcrypto\fR\|(7)
.SH HISTORY
.IX Header "HISTORY"
\&\fBPKCS8_decrypt_ex()\fR, \fBPKCS8_encrypt_ex()\fR and \fBPKCS8_set0_pbe_ex()\fR were added in
OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
