.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "SSL_CTX_SET_SECURITY_LEVEL 3ossl"
.TH SSL_CTX_SET_SECURITY_LEVEL 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
SSL_CTX_set_security_level, SSL_set_security_level, SSL_CTX_get_security_level, SSL_get_security_level, SSL_CTX_set_security_callback, SSL_set_security_callback, SSL_CTX_get_security_callback, SSL_get_security_callback, SSL_CTX_set0_security_ex_data, SSL_set0_security_ex_data, SSL_CTX_get0_security_ex_data, SSL_get0_security_ex_data \- SSL/TLS security framework
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/ssl.h>
\&
\& void SSL_CTX_set_security_level(SSL_CTX *ctx, int level);
\& void SSL_set_security_level(SSL *s, int level);
\&
\& int SSL_CTX_get_security_level(const SSL_CTX *ctx);
\& int SSL_get_security_level(const SSL *s);
\&
\& void SSL_CTX_set_security_callback(SSL_CTX *ctx,
\&                                    int (*cb)(SSL *s, SSL_CTX *ctx, int op,
\&                                              int bits, int nid,
\&                                              void *other, void *ex));
\&
\& void SSL_set_security_callback(SSL *s, int (*cb)(SSL *s, SSL_CTX *ctx, int op,
\&                                                  int bits, int nid,
\&                                                  void *other, void *ex));
\&
\& int (*SSL_CTX_get_security_callback(const SSL_CTX *ctx))(SSL *s, SSL_CTX *ctx, int op,
\&                                                          int bits, int nid, void *other,
\&                                                          void *ex);
\& int (*SSL_get_security_callback(const SSL *s))(SSL *s, SSL_CTX *ctx, int op,
\&                                                int bits, int nid, void *other,
\&                                                void *ex);
\&
\& void SSL_CTX_set0_security_ex_data(SSL_CTX *ctx, void *ex);
\& void SSL_set0_security_ex_data(SSL *s, void *ex);
\&
\& void *SSL_CTX_get0_security_ex_data(const SSL_CTX *ctx);
\& void *SSL_get0_security_ex_data(const SSL *s);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
The functions \fBSSL_CTX_set_security_level()\fR and \fBSSL_set_security_level()\fR set
the security level to \fBlevel\fR. If not set the library default security level
is used.
.PP
The functions \fBSSL_CTX_get_security_level()\fR and \fBSSL_get_security_level()\fR
retrieve the current security level.
.PP
\&\fBSSL_CTX_set_security_callback()\fR, \fBSSL_set_security_callback()\fR,
\&\fBSSL_CTX_get_security_callback()\fR and \fBSSL_get_security_callback()\fR get or set
the security callback associated with \fBctx\fR or \fBs\fR. If not set a default
security callback is used. The meaning of the parameters and the behaviour
of the default callbacks is described below.
.PP
\&\fBSSL_CTX_set0_security_ex_data()\fR, \fBSSL_set0_security_ex_data()\fR,
\&\fBSSL_CTX_get0_security_ex_data()\fR and \fBSSL_get0_security_ex_data()\fR set the
extra data pointer passed to the \fBex\fR parameter of the callback. This
value is passed to the callback verbatim and can be set to any convenient
application specific value.
.SH "DEFAULT CALLBACK BEHAVIOUR"
.IX Header "DEFAULT CALLBACK BEHAVIOUR"
If an application doesn't set its own security callback the default
callback is used. It is intended to provide sane defaults. The meaning
of each level is described below.
.IP "\fBLevel 0\fR" 4
.IX Item "Level 0"
Everything is permitted. This retains compatibility with previous versions of
OpenSSL.
.IP "\fBLevel 1\fR" 4
.IX Item "Level 1"
The security level corresponds to a minimum of 80 bits of security. Any
parameters offering below 80 bits of security are excluded. As a result RSA,
DSA and DH keys shorter than 1024 bits and ECC keys shorter than 160 bits
are prohibited. All export cipher suites are prohibited since they all offer
less than 80 bits of security. SSL version 2 is prohibited. Any cipher suite
using MD5 for the MAC is also prohibited. Note that signatures using SHA1
and MD5 are also forbidden at this level as they have less than 80 security
bits.
.IP "\fBLevel 2\fR" 4
.IX Item "Level 2"
Security level set to 112 bits of security. As a result RSA, DSA and DH keys
shorter than 2048 bits and ECC keys shorter than 224 bits are prohibited.
In addition to the level 1 exclusions any cipher suite using RC4 is also
prohibited. SSL version 3 is also not allowed. Compression is disabled.
.IP "\fBLevel 3\fR" 4
.IX Item "Level 3"
Security level set to 128 bits of security. As a result RSA, DSA and DH keys
shorter than 3072 bits and ECC keys shorter than 256 bits are prohibited.
In addition to the level 2 exclusions cipher suites not offering forward
secrecy are prohibited. TLS versions below 1.1 are not permitted. Session
tickets are disabled.
.IP "\fBLevel 4\fR" 4
.IX Item "Level 4"
Security level set to 192 bits of security. As a result RSA, DSA and
DH keys shorter than 7680 bits and ECC keys shorter than 384 bits are
prohibited.  Cipher suites using SHA1 for the MAC are prohibited. TLS
versions below 1.2 are not permitted.
.IP "\fBLevel 5\fR" 4
.IX Item "Level 5"
Security level set to 256 bits of security. As a result RSA, DSA and DH keys
shorter than 15360 bits and ECC keys shorter than 512 bits are prohibited.
.SH "APPLICATION DEFINED SECURITY CALLBACKS"
.IX Header "APPLICATION DEFINED SECURITY CALLBACKS"
\&\fIDocumentation to be provided.\fR
.SH NOTES
.IX Header "NOTES"
The default security level can be configured when OpenSSL is compiled by
setting \fB\-DOPENSSL_TLS_SECURITY_LEVEL=level\fR. If not set then 1 is used.
.PP
The security framework disables or reject parameters inconsistent with the
set security level. In the past this was difficult as applications had to set
a number of distinct parameters (supported ciphers, supported curves supported
signature algorithms) to achieve this end and some cases (DH parameter size
for example) could not be checked at all.
.PP
By setting an appropriate security level much of this complexity can be
avoided.
.PP
The bits of security limits affect all relevant parameters including
cipher suite encryption algorithms, supported ECC curves, supported
signature algorithms, DH parameter sizes, certificate key sizes and
signature algorithms. This limit applies no matter what other custom
settings an application has set: so if the cipher suite is set to \fBALL\fR
then only cipher suites consistent with the security level are permissible.
.PP
See SP800\-57 for how the security limits are related to individual
algorithms.
.PP
Some security levels require large key sizes for non-ECC public key
algorithms which can severely degrade performance. For example 256 bits
of security requires the use of RSA keys of at least 15360 bits in size.
.PP
Some restrictions can be gracefully handled: for example cipher suites
offering insufficient security are not sent by the client and will not
be selected by the server. Other restrictions such as the peer certificate
key size or the DH parameter size will abort the handshake with a fatal
alert.
.PP
Attempts to set certificates or parameters with insufficient security are
also blocked. For example trying to set a certificate using a 512 bit RSA key
or a certificate with a signature with SHA1 digest at level 1 using
\&\fBSSL_CTX_use_certificate()\fR. Applications which do not check the return values
for errors will misbehave: for example it might appear that a certificate is
not set at all because it had been rejected.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBSSL_CTX_set_security_level()\fR and \fBSSL_set_security_level()\fR do not return values.
.PP
\&\fBSSL_CTX_get_security_level()\fR and \fBSSL_get_security_level()\fR return a integer that
represents the security level with \fBSSL_CTX\fR or \fBSSL\fR, respectively.
.PP
\&\fBSSL_CTX_set_security_callback()\fR and \fBSSL_set_security_callback()\fR do not return
values.
.PP
\&\fBSSL_CTX_get_security_callback()\fR and \fBSSL_get_security_callback()\fR return the pointer
to the security callback or NULL if the callback is not set.
.PP
\&\fBSSL_CTX_get0_security_ex_data()\fR and \fBSSL_get0_security_ex_data()\fR return the extra
data pointer or NULL if the ex data is not set.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBssl\fR\|(7)
.SH HISTORY
.IX Header "HISTORY"
These functions were added in OpenSSL 1.1.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2014\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
