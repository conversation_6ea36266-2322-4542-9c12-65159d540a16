.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "CMS_SIGNED_GET_ATTR 3ossl"
.TH CMS_SIGNED_GET_ATTR 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
CMS_signed_get_attr_count,
CMS_signed_get_attr_by_NID, CMS_signed_get_attr_by_OBJ, CMS_signed_get_attr,
CMS_signed_delete_attr,
CMS_signed_add1_attr, CMS_signed_add1_attr_by_OBJ,
CMS_signed_add1_attr_by_NID, CMS_signed_add1_attr_by_txt,
CMS_signed_get0_data_by_OBJ,
CMS_unsigned_get_attr_count,
CMS_unsigned_get_attr_by_NID, CMS_unsigned_get_attr_by_OBJ,
CMS_unsigned_get_attr, CMS_unsigned_delete_attr,
CMS_unsigned_add1_attr, CMS_unsigned_add1_attr_by_OBJ,
CMS_unsigned_add1_attr_by_NID, CMS_unsigned_add1_attr_by_txt,
CMS_unsigned_get0_data_by_OBJ
\&\- CMS signed and unsigned attribute functions
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/cms.h>
\&
\& int CMS_signed_get_attr_count(const CMS_SignerInfo *si);
\& int CMS_signed_get_attr_by_NID(const CMS_SignerInfo *si, int nid,
\&                                int lastpos);
\& int CMS_signed_get_attr_by_OBJ(const CMS_SignerInfo *si, const ASN1_OBJECT *obj,
\&                                int lastpos);
\& X509_ATTRIBUTE *CMS_signed_get_attr(const CMS_SignerInfo *si, int loc);
\& X509_ATTRIBUTE *CMS_signed_delete_attr(CMS_SignerInfo *si, int loc);
\& int CMS_signed_add1_attr(CMS_SignerInfo *si, X509_ATTRIBUTE *attr);
\& int CMS_signed_add1_attr_by_OBJ(CMS_SignerInfo *si,
\&                                 const ASN1_OBJECT *obj, int type,
\&                                 const void *bytes, int len);
\& int CMS_signed_add1_attr_by_NID(CMS_SignerInfo *si,
\&                                 int nid, int type,
\&                                 const void *bytes, int len);
\& int CMS_signed_add1_attr_by_txt(CMS_SignerInfo *si,
\&                                 const char *attrname, int type,
\&                                 const void *bytes, int len);
\& void *CMS_signed_get0_data_by_OBJ(const CMS_SignerInfo *si,
\&                                   const ASN1_OBJECT *oid,
\&                                   int lastpos, int type);
\&
\& int CMS_unsigned_get_attr_count(const CMS_SignerInfo *si);
\& int CMS_unsigned_get_attr_by_NID(const CMS_SignerInfo *si, int nid,
\&                                  int lastpos);
\& int CMS_unsigned_get_attr_by_OBJ(const CMS_SignerInfo *si,
\&                                  const ASN1_OBJECT *obj, int lastpos);
\& X509_ATTRIBUTE *CMS_unsigned_get_attr(const CMS_SignerInfo *si, int loc);
\& X509_ATTRIBUTE *CMS_unsigned_delete_attr(CMS_SignerInfo *si, int loc);
\& int CMS_unsigned_add1_attr(CMS_SignerInfo *si, X509_ATTRIBUTE *attr);
\& int CMS_unsigned_add1_attr_by_OBJ(CMS_SignerInfo *si,
\&                                   const ASN1_OBJECT *obj, int type,
\&                                   const void *bytes, int len);
\& int CMS_unsigned_add1_attr_by_NID(CMS_SignerInfo *si,
\&                                   int nid, int type,
\&                                   const void *bytes, int len);
\& int CMS_unsigned_add1_attr_by_txt(CMS_SignerInfo *si,
\&                                   const char *attrname, int type,
\&                                   const void *bytes, int len);
\& void *CMS_unsigned_get0_data_by_OBJ(CMS_SignerInfo *si, ASN1_OBJECT *oid,
\&                                     int lastpos, int type);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
CMS_signerInfo contains separate attribute lists for signed and unsigned
attributes. Each \fBCMS_signed_XXX()\fR function is used for signed attributes, and
each \fBCMS_unsigned_XXX()\fR function is used for unsigned attributes.
Since the \fBCMS_unsigned_XXX()\fR functions work in the same way as the
\&\fBCMS_signed_XXX()\fR equivalents, only the \fBCMS_signed_XXX()\fR functions are
described below.
.PP
\&\fBCMS_signed_get_attr_by_OBJ()\fR finds the location of the first matching object
\&\fIobj\fR in the SignerInfo's \fIsi\fR signed attribute list. The search starts at the
position after \fIlastpos\fR. If the returned value is positive then it can be used
on the next call to \fBCMS_signed_get_attr_by_OBJ()\fR as the value of \fIlastpos\fR in
order to iterate through the remaining attributes. \fIlastpos\fR can be set to any
negative value on the first call, in order to start searching from the start of
the signed attribute list.
.PP
\&\fBCMS_signed_get_attr_by_NID()\fR is similar to \fBCMS_signed_get_attr_by_OBJ()\fR except
that it passes the numerical identifier (NID) \fInid\fR associated with the object.
See <openssl/obj_mac.h> for a list of NID_*.
.PP
\&\fBCMS_signed_get_attr()\fR returns the \fBX509_ATTRIBUTE\fR object at index \fIloc\fR in the
\&\fIsi\fR signed attribute list. \fIloc\fR should be in the range from 0 to
\&\fBCMS_signed_get_attr_count()\fR \- 1.
.PP
\&\fBCMS_signed_delete_attr()\fR removes the \fBX509_ATTRIBUTE\fR object at index \fIloc\fR in
the \fIsi\fR signed attribute list. An error occurs if the \fIsi\fR attribute list
is NULL.
.PP
\&\fBCMS_signed_add1_attr()\fR pushes a copy of the passed in \fBX509_ATTRIBUTE\fR object
to the \fIsi\fR signed attribute list. A new signed attribute list is created if
required. An error occurs if \fIattr\fR is NULL.
.PP
\&\fBCMS_signed_add1_attr_by_OBJ()\fR creates a new signed \fBX509_ATTRIBUTE\fR using
\&\fBX509_ATTRIBUTE_set1_object()\fR and \fBX509_ATTRIBUTE_set1_data()\fR to assign a new
\&\fIobj\fR with type \fItype\fR and data \fIbytes\fR of length \fIlen\fR and then pushes it
to the \fIkey\fR object's attribute list.
.PP
\&\fBCMS_signed_add1_attr_by_NID()\fR is similar to \fBCMS_signed_add1_attr_by_OBJ()\fR except
that it passes the numerical identifier (NID) \fInid\fR associated with the object.
See <openssl/obj_mac.h> for a list of NID_*.
.PP
\&\fBCMS_signed_add1_attr_by_txt()\fR is similar to \fBCMS_signed_add1_attr_by_OBJ()\fR
except that it passes a name \fIattrname\fR associated with the object.
See <openssl/obj_mac.h> for a list of SN_* names.
.PP
\&\fBCMS_signed_get0_data_by_OBJ()\fR finds the first attribute in a \fIsi\fR signed
attributes list that matches the \fIobj\fR starting at index \fIlastpos\fR
and returns the data retrieved from the found attributes first \fBASN1_TYPE\fR
object. An error will occur if the attribute type \fItype\fR does not match the
type of the \fBASN1_TYPE\fR object OR if \fItype\fR is either \fBV_ASN1_BOOLEAN\fR or
\&\fBV_ASN1_NULL\fR OR the attribute is not found.
If \fIlastpos\fR is less than \-1 then an error will occur if there are multiple
objects in the signed attribute list that match \fIobj\fR.
If \fIlastpos\fR is less than \-2 then an error will occur if there is more than
one \fBASN1_TYPE\fR object in the found signed attribute.
.PP
Refer to \fBX509_ATTRIBUTE\fR\|(3) for information related to attributes.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
The \fBCMS_unsigned_XXX()\fR functions return values are similar to those of the
equivalent \fBCMS_signed_XXX()\fR functions.
.PP
\&\fBCMS_signed_get_attr_count()\fR returns the number of signed attributes in the
SignerInfo \fIsi\fR, or \-1 if the signed attribute list is NULL.
.PP
\&\fBCMS_signed_get_attr_by_OBJ()\fR returns \-1 if either the signed attribute list of
\&\fIsi\fR is empty OR if \fIobj\fR is not found, otherwise it returns the location of
the \fIobj\fR in the SignerInfo's \fIsi\fR signed attribute list.
.PP
\&\fBCMS_signed_get_attr_by_NID()\fR is similar to \fBCMS_signed_get_attr_by_OBJ()\fR except
that it returns \-2 if the \fInid\fR is not known by OpenSSL.
.PP
\&\fBCMS_signed_get_attr()\fR returns either a signed \fBX509_ATTRIBUTE\fR or NULL on error.
.PP
\&\fBCMS_signed_delete_attr()\fR returns either the removed signed \fBX509_ATTRIBUTE\fR or
NULL if there is a error.
.PP
\&\fBCMS_signed_add1_attr()\fR, \fBCMS_signed_add1_attr_by_OBJ()\fR,
\&\fBCMS_signed_add1_attr_by_NID()\fR, \fBCMS_signed_add1_attr_by_txt()\fR,
return 1 on success or 0 on error.
.PP
\&\fBCMS_signed_get0_data_by_OBJ()\fR returns the data retrieved from the found
signed attributes first \fBASN1_TYPE\fR object, or NULL if an error occurs.
.SH NOTES
.IX Header "NOTES"
Some attributes are added automatically during the signing process.
.PP
Calling \fBCMS_SignerInfo_sign()\fR adds the NID_pkcs9_signingTime signed
attribute.
.PP
Calling \fBCMS_final()\fR, \fBCMS_final_digest()\fR or \fBCMS_dataFinal()\fR adds the
NID_pkcs9_messageDigest signed attribute.
.PP
The NID_pkcs9_contentType signed attribute is always added if the
NID_pkcs9_signingTime attribute is added.
.PP
Calling \fBCMS_sign_ex()\fR, \fBCMS_sign_receipt()\fR or \fBCMS_add1_signer()\fR may add
attributes depending on the flags parameter. See \fBCMS_add1_signer\fR\|(3) for
more information.
.PP
OpenSSL applies special rules for the following attribute NIDs:
.IP "CMS Signed Attributes" 4
.IX Item "CMS Signed Attributes"
NID_pkcs9_contentType
NID_pkcs9_messageDigest
NID_pkcs9_signingTime
.IP "ESS Signed Attributes" 4
.IX Item "ESS Signed Attributes"
NID_id_smime_aa_signingCertificate
NID_id_smime_aa_signingCertificateV2
NID_id_smime_aa_receiptRequest
.IP "CMS Unsigned Attributes" 4
.IX Item "CMS Unsigned Attributes"
NID_pkcs9_countersignature
.PP
\&\fBCMS_signed_add1_attr()\fR, \fBCMS_signed_add1_attr_by_OBJ()\fR,
\&\fBCMS_signed_add1_attr_by_NID()\fR, \fBCMS_signed_add1_attr_by_txt()\fR
and the equivalent \fBCMS_unsigned_add1_attrXXX()\fR functions allow
duplicate attributes to be added. The attribute rules are not checked
during these function calls, and are deferred until the sign or verify process
(i.e. during calls to any of \fBCMS_sign_ex()\fR, \fBCMS_sign()\fR, \fBCMS_sign_receipt()\fR,
\&\fBCMS_add1_signer()\fR, \fBCMS_Final()\fR, \fBCMS_dataFinal()\fR, \fBCMS_final_digest()\fR,
\&\fBCMS_verify()\fR, \fBCMS_verify_receipt()\fR or \fBCMS_SignedData_verify()\fR).
.PP
For CMS attribute rules see RFC 5652 Section 11.
For ESS attribute rules see RFC 2634 Section 1.3.4 and RFC 5035 Section 5.4.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBX509_ATTRIBUTE\fR\|(3)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2023\-2024 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
