.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "X509_CHECK_PURPOSE 3ossl"
.TH X509_CHECK_PURPOSE 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
X509_check_purpose \- Check the purpose of a certificate
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/x509v3.h>
\&
\& int X509_check_purpose(X509 *x, int id, int ca);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
This function checks if certificate \fIx\fR was created with the purpose
represented by \fIid\fR. If \fIca\fR is nonzero, then certificate \fIx\fR is
checked to determine if it's a possible CA with various levels of certainty
possibly returned. The certificate \fIx\fR must be a complete certificate
otherwise the function returns an error.
.PP
Below are the potential ID's that can be checked:
.PP
.Vb 9
\& # define X509_PURPOSE_SSL_CLIENT        1
\& # define X509_PURPOSE_SSL_SERVER        2
\& # define X509_PURPOSE_NS_SSL_SERVER     3
\& # define X509_PURPOSE_SMIME_SIGN        4
\& # define X509_PURPOSE_SMIME_ENCRYPT     5
\& # define X509_PURPOSE_CRL_SIGN          6
\& # define X509_PURPOSE_ANY               7
\& # define X509_PURPOSE_OCSP_HELPER       8
\& # define X509_PURPOSE_TIMESTAMP_SIGN    9
.Ve
.PP
The checks performed take into account the X.509 extensions
keyUsage, extendedKeyUsage, and basicConstraints.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
For non-CA checks
.IP "\-1 an error condition has occurred" 4
.IX Item "-1 an error condition has occurred"
.PD 0
.IP " 1 if the certificate was created to perform the purpose represented by \fIid\fR" 4
.IX Item " 1 if the certificate was created to perform the purpose represented by id"
.IP " 0 if the certificate was not created to perform the purpose represented by \fIid\fR" 4
.IX Item " 0 if the certificate was not created to perform the purpose represented by id"
.PD
.PP
For CA checks the below integers could be returned with the following meanings:
.IP "\-1 an error condition has occurred" 4
.IX Item "-1 an error condition has occurred"
.PD 0
.IP " 0 not a CA or does not have the purpose represented by \fIid\fR" 4
.IX Item " 0 not a CA or does not have the purpose represented by id"
.IP " 1 is a CA." 4
.IX Item " 1 is a CA."
.IP " 2 Only possible in old versions of openSSL when basicConstraints are absent. New versions will not return this value. May be a CA" 4
.IX Item " 2 Only possible in old versions of openSSL when basicConstraints are absent. New versions will not return this value. May be a CA"
.IP " 3 basicConstraints absent but self signed V1." 4
.IX Item " 3 basicConstraints absent but self signed V1."
.IP " 4 basicConstraints absent but keyUsage present and keyCertSign asserted." 4
.IX Item " 4 basicConstraints absent but keyUsage present and keyCertSign asserted."
.IP " 5 legacy Netscape specific CA Flags present" 4
.IX Item " 5 legacy Netscape specific CA Flags present"
.PD
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2019\-2021 The OpenSSL Project Authors. All Rights Reserved.
Licensed under the Apache License 2.0 (the "License"). You may not use this
file except in compliance with the License. You can obtain a copy in the file
LICENSE in the source distribution or at <https://www.openssl.org/source/license.html>.
