.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "SCT_PRINT 3ossl"
.TH SCT_PRINT 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
SCT_print, SCT_LIST_print, SCT_validation_status_string \-
Prints Signed Certificate Timestamps in a human\-readable way
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/ct.h>
\&
\& void SCT_print(const SCT *sct, BIO *out, int indent, const CTLOG_STORE *logs);
\& void SCT_LIST_print(const STACK_OF(SCT) *sct_list, BIO *out, int indent,
\&                     const char *separator, const CTLOG_STORE *logs);
\& const char *SCT_validation_status_string(const SCT *sct);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBSCT_print()\fR prints a single Signed Certificate Timestamp (SCT) to a \fBBIO\fR in
a human-readable format. \fBSCT_LIST_print()\fR prints an entire list of SCTs in a
similar way. A separator can be specified to delimit each SCT in the output.
.PP
The output can be indented by a specified number of spaces. If a \fBCTLOG_STORE\fR
is provided, it will be used to print the description of the CT log that issued
each SCT (if that log is in the CTLOG_STORE). Alternatively, NULL can be passed
as the CTLOG_STORE parameter to disable this feature.
.PP
\&\fBSCT_validation_status_string()\fR will return the validation status of an SCT as
a human-readable string. Call \fBSCT_validate()\fR or \fBSCT_LIST_validate()\fR
beforehand in order to set the validation status of an SCT first.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBSCT_validation_status_string()\fR returns a NUL-terminated string representing
the validation status of an \fBSCT\fR object.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBct\fR\|(7),
\&\fBbio\fR\|(7),
\&\fBCTLOG_STORE_new\fR\|(3),
\&\fBSCT_validate\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
These functions were added in OpenSSL 1.1.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2016\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
