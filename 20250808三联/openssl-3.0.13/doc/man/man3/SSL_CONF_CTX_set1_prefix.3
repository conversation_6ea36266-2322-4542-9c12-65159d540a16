.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "SSL_CONF_CTX_SET1_PREFIX 3ossl"
.TH SSL_CONF_CTX_SET1_PREFIX 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
SSL_CONF_CTX_set1_prefix \- Set configuration context command prefix
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/ssl.h>
\&
\& unsigned int SSL_CONF_CTX_set1_prefix(SSL_CONF_CTX *cctx, const char *prefix);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
The function \fBSSL_CONF_CTX_set1_prefix()\fR sets the command prefix of \fBcctx\fR
to \fBprefix\fR. If \fBprefix\fR is \fBNULL\fR it is restored to the default value.
.SH NOTES
.IX Header "NOTES"
Command prefixes alter the commands recognised by subsequent \fBSSL_CONF_cmd()\fR
calls. For example for files, if the prefix "SSL" is set then command names
such as "SSLProtocol", "SSLOptions" etc. are recognised instead of "Protocol"
and "Options". Similarly for command lines if the prefix is "\-\-ssl\-" then
"\-\-ssl\-no_tls1_2" is recognised instead of "\-no_tls1_2".
.PP
If the \fBSSL_CONF_FLAG_CMDLINE\fR flag is set then prefix checks are case
sensitive and "\-" is the default. In the unlikely even an application
explicitly wants to set no prefix it must be explicitly set to "".
.PP
If the \fBSSL_CONF_FLAG_FILE\fR flag is set then prefix checks are case
insensitive and no prefix is the default.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBSSL_CONF_CTX_set1_prefix()\fR returns 1 for success and 0 for failure.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBssl\fR\|(7),
\&\fBSSL_CONF_CTX_new\fR\|(3),
\&\fBSSL_CONF_CTX_set_flags\fR\|(3),
\&\fBSSL_CONF_CTX_set_ssl_ctx\fR\|(3),
\&\fBSSL_CONF_cmd\fR\|(3),
\&\fBSSL_CONF_cmd_argv\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
These functions were added in OpenSSL 1.0.2.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2012\-2016 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
