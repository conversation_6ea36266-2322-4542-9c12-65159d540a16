.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "SSL_CTX_SET1_CURVES 3ossl"
.TH SSL_CTX_SET1_CURVES 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
SSL_CTX_set1_groups, SSL_CTX_set1_groups_list, SSL_set1_groups,
SSL_set1_groups_list, SSL_get1_groups, SSL_get_shared_group,
SSL_get_negotiated_group, SSL_CTX_set1_curves, SSL_CTX_set1_curves_list,
SSL_set1_curves, SSL_set1_curves_list, SSL_get1_curves, SSL_get_shared_curve
\&\- EC supported curve functions
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/ssl.h>
\&
\& int SSL_CTX_set1_groups(SSL_CTX *ctx, int *glist, int glistlen);
\& int SSL_CTX_set1_groups_list(SSL_CTX *ctx, char *list);
\&
\& int SSL_set1_groups(SSL *ssl, int *glist, int glistlen);
\& int SSL_set1_groups_list(SSL *ssl, char *list);
\&
\& int SSL_get1_groups(SSL *ssl, int *groups);
\& int SSL_get_shared_group(SSL *s, int n);
\& int SSL_get_negotiated_group(SSL *s);
\&
\& int SSL_CTX_set1_curves(SSL_CTX *ctx, int *clist, int clistlen);
\& int SSL_CTX_set1_curves_list(SSL_CTX *ctx, char *list);
\&
\& int SSL_set1_curves(SSL *ssl, int *clist, int clistlen);
\& int SSL_set1_curves_list(SSL *ssl, char *list);
\&
\& int SSL_get1_curves(SSL *ssl, int *curves);
\& int SSL_get_shared_curve(SSL *s, int n);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
For all of the functions below that set the supported groups there must be at
least one group in the list. A number of these functions identify groups via a
unique integer NID value. However, support for some groups may be added by
external providers. In this case there will be no NID assigned for the group.
When setting such groups applications should use the "list" form of these
functions (i.e. \fBSSL_CTX_set1_groups_list()\fR and SSL_set1_groups_list).
.PP
\&\fBSSL_CTX_set1_groups()\fR sets the supported groups for \fBctx\fR to \fBglistlen\fR
groups in the array \fBglist\fR. The array consist of all NIDs of groups in
preference order. For a TLS client the groups are used directly in the
supported groups extension. For a TLS server the groups are used to
determine the set of shared groups. Currently supported groups for
\&\fBTLSv1.3\fR are \fBNID_X9_62_prime256v1\fR, \fBNID_secp384r1\fR, \fBNID_secp521r1\fR,
\&\fBNID_X25519\fR, \fBNID_X448\fR, \fBNID_ffdhe2048\fR, \fBNID_ffdhe3072\fR,
\&\fBNID_ffdhe4096\fR, \fBNID_ffdhe6144\fR and \fBNID_ffdhe8192\fR.
.PP
\&\fBSSL_CTX_set1_groups_list()\fR sets the supported groups for \fBctx\fR to
string \fBlist\fR. The string is a colon separated list of group NIDs or
names, for example "P\-521:P\-384:P\-256:X25519:ffdhe2048". Currently supported
groups for \fBTLSv1.3\fR are \fBP\-256\fR, \fBP\-384\fR, \fBP\-521\fR, \fBX25519\fR, \fBX448\fR,
\&\fBffdhe2048\fR, \fBffdhe3072\fR, \fBffdhe4096\fR, \fBffdhe6144\fR, \fBffdhe8192\fR. Support
for other groups may be added by external providers.
.PP
\&\fBSSL_set1_groups()\fR and \fBSSL_set1_groups_list()\fR are similar except they set
supported groups for the SSL structure \fBssl\fR.
.PP
\&\fBSSL_get1_groups()\fR returns the set of supported groups sent by a client
in the supported groups extension. It returns the total number of
supported groups. The \fBgroups\fR parameter can be \fBNULL\fR to simply
return the number of groups for memory allocation purposes. The
\&\fBgroups\fR array is in the form of a set of group NIDs in preference
order. It can return zero if the client did not send a supported groups
extension. If a supported group NID is unknown then the value is set to the
bitwise OR of TLSEXT_nid_unknown (0x1000000) and the id of the group.
.PP
\&\fBSSL_get_shared_group()\fR returns the NID of the shared group \fBn\fR for a
server-side SSL \fBssl\fR. If \fBn\fR is \-1 then the total number of shared groups is
returned, which may be zero. Other than for diagnostic purposes,
most applications will only be interested in the first shared group
so \fBn\fR is normally set to zero. If the value \fBn\fR is out of range,
NID_undef is returned. If the NID for the shared group is unknown then the value
is set to the bitwise OR of TLSEXT_nid_unknown (0x1000000) and the id of the
group.
.PP
\&\fBSSL_get_negotiated_group()\fR returns the NID of the negotiated group used for
the handshake key exchange process.  For TLSv1.3 connections this typically
reflects the state of the current connection, though in the case of PSK-only
resumption, the returned value will be from a previous connection.  For earlier
TLS versions, when a session has been resumed, it always reflects the group
used for key exchange during the initial handshake (otherwise it is from the
current, non-resumption, connection).  This can be called by either client or
server. If the NID for the shared group is unknown then the value is set to the
bitwise OR of TLSEXT_nid_unknown (0x1000000) and the id of the group.
.PP
All these functions are implemented as macros.
.PP
The curve functions are synonyms for the equivalently named group functions and
are identical in every respect. They exist because, prior to TLS1.3, there was
only the concept of supported curves. In TLS1.3 this was renamed to supported
groups, and extended to include Diffie Hellman groups. The group functions
should be used in preference.
.SH NOTES
.IX Header "NOTES"
If an application wishes to make use of several of these functions for
configuration purposes either on a command line or in a file it should
consider using the SSL_CONF interface instead of manually parsing options.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBSSL_CTX_set1_groups()\fR, \fBSSL_CTX_set1_groups_list()\fR, \fBSSL_set1_groups()\fR and
\&\fBSSL_set1_groups_list()\fR, return 1 for success and 0 for failure.
.PP
\&\fBSSL_get1_groups()\fR returns the number of groups, which may be zero.
.PP
\&\fBSSL_get_shared_group()\fR returns the NID of shared group \fBn\fR or NID_undef if there
is no shared group \fBn\fR; or the total number of shared groups if \fBn\fR
is \-1.
.PP
When called on a client \fBssl\fR, \fBSSL_get_shared_group()\fR has no meaning and
returns \-1.
.PP
\&\fBSSL_get_negotiated_group()\fR returns the NID of the negotiated group used for
key exchange, or NID_undef if there was no negotiated group.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBssl\fR\|(7),
\&\fBSSL_CTX_add_extra_chain_cert\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
The curve functions were added in OpenSSL 1.0.2. The equivalent group
functions were added in OpenSSL 1.1.1. The \fBSSL_get_negotiated_group()\fR function
was added in OpenSSL 3.0.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2013\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
