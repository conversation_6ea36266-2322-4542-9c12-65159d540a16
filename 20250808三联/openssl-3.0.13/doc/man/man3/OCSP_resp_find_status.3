.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "OCSP_RESP_FIND_STATUS 3ossl"
.TH OCSP_RESP_FIND_STATUS 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
OCSP_resp_find_status, OCSP_resp_count,
OCSP_resp_get0, OCSP_resp_find, OCSP_single_get0_status,
OCSP_resp_get0_produced_at, OCSP_resp_get0_signature,
OCSP_resp_get0_tbs_sigalg, OCSP_resp_get0_respdata,
OCSP_resp_get0_certs, OCSP_resp_get0_signer,
OCSP_resp_get0_id, OCSP_resp_get1_id,
OCSP_check_validity, OCSP_basic_verify
\&\- OCSP response utility functions
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/ocsp.h>
\&
\& int OCSP_resp_find_status(OCSP_BASICRESP *bs, OCSP_CERTID *id, int *status,
\&                           int *reason,
\&                           ASN1_GENERALIZEDTIME **revtime,
\&                           ASN1_GENERALIZEDTIME **thisupd,
\&                           ASN1_GENERALIZEDTIME **nextupd);
\&
\& int OCSP_resp_count(OCSP_BASICRESP *bs);
\& OCSP_SINGLERESP *OCSP_resp_get0(OCSP_BASICRESP *bs, int idx);
\& int OCSP_resp_find(OCSP_BASICRESP *bs, OCSP_CERTID *id, int last);
\& int OCSP_single_get0_status(OCSP_SINGLERESP *single, int *reason,
\&                             ASN1_GENERALIZEDTIME **revtime,
\&                             ASN1_GENERALIZEDTIME **thisupd,
\&                             ASN1_GENERALIZEDTIME **nextupd);
\&
\& const ASN1_GENERALIZEDTIME *OCSP_resp_get0_produced_at(
\&                             const OCSP_BASICRESP* single);
\&
\& const ASN1_OCTET_STRING *OCSP_resp_get0_signature(const OCSP_BASICRESP *bs);
\& const X509_ALGOR *OCSP_resp_get0_tbs_sigalg(const OCSP_BASICRESP *bs);
\& const OCSP_RESPDATA *OCSP_resp_get0_respdata(const OCSP_BASICRESP *bs);
\& const STACK_OF(X509) *OCSP_resp_get0_certs(const OCSP_BASICRESP *bs);
\&
\& int OCSP_resp_get0_signer(OCSP_BASICRESP *bs, X509 **signer,
\&                           STACK_OF(X509) *extra_certs);
\&
\& int OCSP_resp_get0_id(const OCSP_BASICRESP *bs,
\&                       const ASN1_OCTET_STRING **pid,
\&                       const X509_NAME **pname);
\& int OCSP_resp_get1_id(const OCSP_BASICRESP *bs,
\&                       ASN1_OCTET_STRING **pid,
\&                       X509_NAME **pname);
\&
\& int OCSP_check_validity(ASN1_GENERALIZEDTIME *thisupd,
\&                         ASN1_GENERALIZEDTIME *nextupd,
\&                         long sec, long maxsec);
\&
\& int OCSP_basic_verify(OCSP_BASICRESP *bs, STACK_OF(X509) *certs,
\&                      X509_STORE *st, unsigned long flags);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBOCSP_resp_find_status()\fR searches \fIbs\fR for an OCSP response for \fIid\fR. If it is
successful the fields of the response are returned in \fI*status\fR, \fI*reason\fR,
\&\fI*revtime\fR, \fI*thisupd\fR and \fI*nextupd\fR.  The \fI*status\fR value will be one of
\&\fBV_OCSP_CERTSTATUS_GOOD\fR, \fBV_OCSP_CERTSTATUS_REVOKED\fR or
\&\fBV_OCSP_CERTSTATUS_UNKNOWN\fR. The \fI*reason\fR and \fI*revtime\fR fields are only
set if the status is \fBV_OCSP_CERTSTATUS_REVOKED\fR. If set the \fI*reason\fR field
will be set to the revocation reason which will be one of
\&\fBOCSP_REVOKED_STATUS_NOSTATUS\fR, \fBOCSP_REVOKED_STATUS_UNSPECIFIED\fR,
\&\fBOCSP_REVOKED_STATUS_KEYCOMPROMISE\fR, \fBOCSP_REVOKED_STATUS_CACOMPROMISE\fR,
\&\fBOCSP_REVOKED_STATUS_AFFILIATIONCHANGED\fR, \fBOCSP_REVOKED_STATUS_SUPERSEDED\fR,
\&\fBOCSP_REVOKED_STATUS_CESSATIONOFOPERATION\fR,
\&\fBOCSP_REVOKED_STATUS_CERTIFICATEHOLD\fR or \fBOCSP_REVOKED_STATUS_REMOVEFROMCRL\fR.
.PP
\&\fBOCSP_resp_count()\fR returns the number of \fBOCSP_SINGLERESP\fR structures in \fIbs\fR.
.PP
\&\fBOCSP_resp_get0()\fR returns the \fBOCSP_SINGLERESP\fR structure in \fIbs\fR corresponding
to index \fIidx\fR, where \fIidx\fR runs from 0 to OCSP_resp_count(bs) \- 1.
.PP
\&\fBOCSP_resp_find()\fR searches \fIbs\fR for \fIid\fR and returns the index of the first
matching entry after \fIlast\fR or starting from the beginning if \fIlast\fR is \-1.
.PP
\&\fBOCSP_single_get0_status()\fR extracts the fields of \fIsingle\fR in \fI*reason\fR,
\&\fI*revtime\fR, \fI*thisupd\fR and \fI*nextupd\fR.
.PP
\&\fBOCSP_resp_get0_produced_at()\fR extracts the \fBproducedAt\fR field from the
single response \fIbs\fR.
.PP
\&\fBOCSP_resp_get0_signature()\fR returns the signature from \fIbs\fR.
.PP
\&\fBOCSP_resp_get0_tbs_sigalg()\fR returns the \fBsignatureAlgorithm\fR from \fIbs\fR.
.PP
\&\fBOCSP_resp_get0_respdata()\fR returns the \fBtbsResponseData\fR from \fIbs\fR.
.PP
\&\fBOCSP_resp_get0_certs()\fR returns any certificates included in \fIbs\fR.
.PP
\&\fBOCSP_resp_get0_signer()\fR attempts to retrieve the certificate that directly
signed \fIbs\fR.  The OCSP protocol does not require that this certificate
is included in the \fBcerts\fR field of the response, so additional certificates
can be supplied via the \fIextra_certs\fR if the certificates that may have
signed the response are known via some out-of-band mechanism.
.PP
\&\fBOCSP_resp_get0_id()\fR gets the responder id of \fIbs\fR. If the responder ID is
a name then <*pname> is set to the name and \fI*pid\fR is set to NULL. If the
responder ID is by key ID then \fI*pid\fR is set to the key ID and \fI*pname\fR
is set to NULL.
.PP
\&\fBOCSP_resp_get1_id()\fR is the same as \fBOCSP_resp_get0_id()\fR
but leaves ownership of \fI*pid\fR and \fI*pname\fR with the caller,
who is responsible for freeing them unless the function returns 0.
.PP
\&\fBOCSP_check_validity()\fR checks the validity of its \fIthisupd\fR and \fInextupd\fR
arguments, which will be typically obtained from \fBOCSP_resp_find_status()\fR or
\&\fBOCSP_single_get0_status()\fR. If \fIsec\fR is nonzero it indicates how many seconds
leeway should be allowed in the check. If \fImaxsec\fR is positive it indicates
the maximum age of \fIthisupd\fR in seconds.
.PP
\&\fBOCSP_basic_verify()\fR checks that the basic response message \fIbs\fR is correctly
signed and that the signer certificate can be validated. It takes \fIst\fR as
the trusted store and \fIcerts\fR as a set of untrusted intermediate certificates.
The function first tries to find the signer certificate of the response
in \fIcerts\fR. It then searches the certificates the responder may have included
in \fIbs\fR unless \fIflags\fR contains \fBOCSP_NOINTERN\fR.
It fails if the signer certificate cannot be found.
Next, unless \fIflags\fR contains \fBOCSP_NOSIGS\fR, the function checks
the signature of \fIbs\fR and fails on error. Then the function already returns
success if \fIflags\fR contains \fBOCSP_NOVERIFY\fR or if the signer certificate
was found in \fIcerts\fR and \fIflags\fR contains \fBOCSP_TRUSTOTHER\fR.
Otherwise the function continues by validating the signer certificate.
If \fIflags\fR contains \fBOCSP_PARTIAL_CHAIN\fR it takes intermediate CA
certificates in \fIst\fR as trust anchors.
For more details, see the description of \fBX509_V_FLAG_PARTIAL_CHAIN\fR
in "VERIFICATION FLAGS" in \fBX509_VERIFY_PARAM_set_flags\fR\|(3).
If \fIflags\fR contains \fBOCSP_NOCHAIN\fR it ignores all certificates in \fIcerts\fR
and in \fIbs\fR, else it takes them as untrusted intermediate CA certificates
and uses them for constructing the validation path for the signer certificate.
Certificate revocation status checks using CRLs is disabled during path validation
if the signer certificate contains the \fBid-pkix-ocsp-no-check\fR extension.
After successful path
validation the function returns success if the \fBOCSP_NOCHECKS\fR flag is set.
Otherwise it verifies that the signer certificate meets the OCSP issuer
criteria including potential delegation. If this does not succeed and the
\&\fBOCSP_NOEXPLICIT\fR flag is not set the function checks for explicit
trust for OCSP signing in the root CA certificate.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBOCSP_resp_find_status()\fR returns 1 if \fIid\fR is found in \fIbs\fR and 0 otherwise.
.PP
\&\fBOCSP_resp_count()\fR returns the total number of \fBOCSP_SINGLERESP\fR fields in \fIbs\fR
or \-1 on error.
.PP
\&\fBOCSP_resp_get0()\fR returns a pointer to an \fBOCSP_SINGLERESP\fR structure or
NULL on error, such as \fIidx\fR being out of range.
.PP
\&\fBOCSP_resp_find()\fR returns the index of \fIid\fR in \fIbs\fR (which may be 0)
or \-1 on error, such as when \fIid\fR was not found.
.PP
\&\fBOCSP_single_get0_status()\fR returns the status of \fIsingle\fR or \-1 if an error
occurred.
.PP
\&\fBOCSP_resp_get0_produced_at()\fR returns the \fBproducedAt\fR field from \fIbs\fR.
.PP
\&\fBOCSP_resp_get0_signature()\fR returns the signature from \fIbs\fR.
.PP
\&\fBOCSP_resp_get0_tbs_sigalg()\fR returns the \fBsignatureAlgorithm\fR field from \fIbs\fR.
.PP
\&\fBOCSP_resp_get0_respdata()\fR returns the \fBtbsResponseData\fR field from \fIbs\fR.
.PP
\&\fBOCSP_resp_get0_certs()\fR returns any certificates included in \fIbs\fR.
.PP
\&\fBOCSP_resp_get0_signer()\fR returns 1 if the signing certificate was located,
or 0 if not found or on error.
.PP
\&\fBOCSP_resp_get0_id()\fR and \fBOCSP_resp_get1_id()\fR return 1 on success, 0 on failure.
.PP
\&\fBOCSP_check_validity()\fR returns 1 if \fIthisupd\fR and \fInextupd\fR are valid time
values and the current time + \fIsec\fR is not before \fIthisupd\fR and,
if \fImaxsec\fR >= 0, the current time \- \fImaxsec\fR is not past \fInextupd\fR.
Otherwise it returns 0 to indicate an error.
.PP
\&\fBOCSP_basic_verify()\fR returns 1 on success, 0 on verification not successful,
or \-1 on a fatal error such as malloc failure.
.SH NOTES
.IX Header "NOTES"
Applications will typically call \fBOCSP_resp_find_status()\fR using the certificate
ID of interest and then check its validity using \fBOCSP_check_validity()\fR. They
can then take appropriate action based on the status of the certificate.
.PP
An OCSP response for a certificate contains \fBthisUpdate\fR and \fBnextUpdate\fR
fields. Normally the current time should be between these two values. To
account for clock skew the \fImaxsec\fR field can be set to nonzero in
\&\fBOCSP_check_validity()\fR. Some responders do not set the \fBnextUpdate\fR field, this
would otherwise mean an ancient response would be considered valid: the
\&\fImaxsec\fR parameter to \fBOCSP_check_validity()\fR can be used to limit the permitted
age of responses.
.PP
The values written to \fI*revtime\fR, \fI*thisupd\fR and \fI*nextupd\fR by
\&\fBOCSP_resp_find_status()\fR and \fBOCSP_single_get0_status()\fR are internal pointers
which MUST NOT be freed up by the calling application. Any or all of these
parameters can be set to NULL if their value is not required.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBcrypto\fR\|(7),
\&\fBOCSP_cert_to_id\fR\|(3),
\&\fBOCSP_request_add1_nonce\fR\|(3),
\&\fBOCSP_REQUEST_new\fR\|(3),
\&\fBOCSP_response_status\fR\|(3),
\&\fBOCSP_sendreq_new\fR\|(3),
\&\fBX509_VERIFY_PARAM_set_flags\fR\|(3)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2015\-2023 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
