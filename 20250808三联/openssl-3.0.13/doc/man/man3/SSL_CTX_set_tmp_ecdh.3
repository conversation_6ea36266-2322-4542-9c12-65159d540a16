.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "SSL_CTX_SET_TMP_ECDH 3ossl"
.TH SSL_CTX_SET_TMP_ECDH 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
SSL_CTX_set_tmp_ecdh, SSL_set_tmp_ecdh, SSL_CTX_set_ecdh_auto, SSL_set_ecdh_auto
\&\- handle ECDH keys for ephemeral key exchange
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/ssl.h>
\&
\& long SSL_CTX_set_tmp_ecdh(SSL_CTX *ctx, const EC_KEY *ecdh);
\& long SSL_set_tmp_ecdh(SSL *ssl, const EC_KEY *ecdh);
\&
\& long SSL_CTX_set_ecdh_auto(SSL_CTX *ctx, int state);
\& long SSL_set_ecdh_auto(SSL *ssl, int state);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBSSL_CTX_set_tmp_ecdh()\fR sets ECDH parameters to be used to be \fBecdh\fR.
The key is inherited by all \fBssl\fR objects created from \fBctx\fR.
This macro is deprecated in favor of \fBSSL_CTX_set1_groups\fR\|(3).
.PP
\&\fBSSL_set_tmp_ecdh()\fR sets the parameters only for \fBssl\fR.
This macro is deprecated in favor of \fBSSL_set1_groups\fR\|(3).
.PP
\&\fBSSL_CTX_set_ecdh_auto()\fR and \fBSSL_set_ecdh_auto()\fR are deprecated and
have no effect.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBSSL_CTX_set_tmp_ecdh()\fR and \fBSSL_set_tmp_ecdh()\fR return 1 on success and 0
on failure.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBssl\fR\|(7), \fBSSL_CTX_set1_curves\fR\|(3), \fBSSL_CTX_set_cipher_list\fR\|(3),
\&\fBSSL_CTX_set_options\fR\|(3), \fBSSL_CTX_set_tmp_dh_callback\fR\|(3),
\&\fBopenssl\-ciphers\fR\|(1), \fBopenssl\-ecparam\fR\|(1)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2018 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
