.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "OSSL_ESS_CHECK_SIGNING_CERTS 3ossl"
.TH OSSL_ESS_CHECK_SIGNING_CERTS 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
OSSL_ESS_signing_cert_new_init,
OSSL_ESS_signing_cert_v2_new_init,
OSSL_ESS_check_signing_certs
\&\- Enhanced Security Services (ESS) functions
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/ess.h>
\&
\& ESS_SIGNING_CERT *OSSL_ESS_signing_cert_new_init(const X509 *signcert,
\&                                                  const STACK_OF(X509) *certs,
\&                                                  int set_issuer_serial);
\& ESS_SIGNING_CERT_V2 *OSSL_ESS_signing_cert_v2_new_init(const EVP_MD *hash_alg,
\&                                                        const X509 *signcert,
\&                                                        const
\&                                                        STACK_OF(X509) *certs,
\&                                                        int set_issuer_serial);
\& int OSSL_ESS_check_signing_certs(const ESS_SIGNING_CERT *ss,
\&                                  const ESS_SIGNING_CERT_V2 *ssv2,
\&                                  const STACK_OF(X509) *chain,
\&                                  int require_signing_cert);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBOSSL_ESS_signing_cert_new_init()\fR generates a new \fBESS_SIGNING_CERT\fR structure
referencing the given \fIsigncert\fR and any given further \fIcerts\fR
using their SHA\-1 fingerprints.
If \fIset_issuer_serial\fR is nonzero then also the issuer and serial number
of \fIsigncert\fR are included in the \fBESS_CERT_ID\fR as the \fBissuerSerial\fR field.
For all members of \fIcerts\fR the  \fBissuerSerial\fR field is always included.
.PP
\&\fBOSSL_ESS_signing_cert_v2_new_init()\fR is the same as
\&\fBOSSL_ESS_signing_cert_new_init()\fR except that it uses the given \fIhash_alg\fR and
generates a \fBESS_SIGNING_CERT_V2\fR structure with \fBESS_CERT_ID_V2\fR elements.
.PP
\&\fBOSSL_ESS_check_signing_certs()\fR checks if the validation chain \fIchain\fR contains
the certificates required by the identifiers given in \fIss\fR and/or \fIssv2\fR.
If \fIrequire_signing_cert\fR is nonzero, \fIss\fR or \fIssv2\fR must not be NULL.
If both \fIss\fR and \fIssv2\fR are not NULL, they are evaluated independently.
The list of certificate identifiers in \fIss\fR is of type \fBESS_CERT_ID\fR,
while the list contained in \fIssv2\fR is of type \fBESS_CERT_ID_V2\fR.
As far as these lists are present, they must be nonempty.
The certificate identified by their first entry must be the first element of
\&\fIchain\fR, i.e. the signer certificate.
Any further certificates referenced in the list must also be found in \fIchain\fR.
The matching is done using the given certificate hash algorithm and value.
In addition to the checks required by RFCs 2624 and 5035,
if the \fBissuerSerial\fR field is included in an \fBESSCertID\fR or \fBESSCertIDv2\fR
it must match the certificate issuer and serial number attributes.
.SH NOTES
.IX Header "NOTES"
ESS has been defined in RFC 2634, which has been updated in RFC 5035
(ESS version 2) to support hash algorithms other than SHA\-1.
This is used for TSP (RFC 3161) and CAdES-BES (informational RFC 5126).
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBOSSL_ESS_signing_cert_new_init()\fR and \fBOSSL_ESS_signing_cert_v2_new_init()\fR
return a pointer to the new structure or NULL on malloc failure.
.PP
\&\fBOSSL_ESS_check_signing_certs()\fR returns 1 on success,
0 if a required certificate cannot be found, \-1 on other error.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBTS_VERIFY_CTX_set_certs\fR\|(3),
\&\fBCMS_verify\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
\&\fBOSSL_ESS_signing_cert_new_init()\fR, \fBOSSL_ESS_signing_cert_v2_new_init()\fR, and
\&\fBOSSL_ESS_check_signing_certs()\fR were added in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2021\-2023 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
