.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "X509_STORE_SET_VERIFY_CB_FUNC 3ossl"
.TH X509_STORE_SET_VERIFY_CB_FUNC 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
X509_STORE_set_lookup_crls_cb,
X509_STORE_set_verify_func,
X509_STORE_get_cleanup,
X509_STORE_set_cleanup,
X509_STORE_get_lookup_crls,
X509_STORE_set_lookup_crls,
X509_STORE_get_lookup_certs,
X509_STORE_set_lookup_certs,
X509_STORE_get_check_policy,
X509_STORE_set_check_policy,
X509_STORE_get_cert_crl,
X509_STORE_set_cert_crl,
X509_STORE_get_check_crl,
X509_STORE_set_check_crl,
X509_STORE_get_get_crl,
X509_STORE_set_get_crl,
X509_STORE_get_check_revocation,
X509_STORE_set_check_revocation,
X509_STORE_get_check_issued,
X509_STORE_set_check_issued,
X509_STORE_CTX_get1_issuer,
X509_STORE_get_get_issuer,
X509_STORE_set_get_issuer,
X509_STORE_CTX_get_verify,
X509_STORE_set_verify,
X509_STORE_get_verify_cb,
X509_STORE_set_verify_cb_func, X509_STORE_set_verify_cb,
X509_STORE_CTX_cert_crl_fn, X509_STORE_CTX_check_crl_fn,
X509_STORE_CTX_check_issued_fn, X509_STORE_CTX_check_policy_fn,
X509_STORE_CTX_check_revocation_fn, X509_STORE_CTX_cleanup_fn,
X509_STORE_CTX_get_crl_fn, X509_STORE_CTX_get_issuer_fn,
X509_STORE_CTX_lookup_certs_fn, X509_STORE_CTX_lookup_crls_fn
\&\- set verification callback
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/x509_vfy.h>
\&
\& typedef int (*X509_STORE_CTX_get_issuer_fn)(X509 **issuer,
\&                                             X509_STORE_CTX *ctx, X509 *x);
\& typedef int (*X509_STORE_CTX_check_issued_fn)(X509_STORE_CTX *ctx,
\&                                               X509 *x, X509 *issuer);
\& typedef int (*X509_STORE_CTX_check_revocation_fn)(X509_STORE_CTX *ctx);
\& typedef int (*X509_STORE_CTX_get_crl_fn)(X509_STORE_CTX *ctx,
\&                                          X509_CRL **crl, X509 *x);
\& typedef int (*X509_STORE_CTX_check_crl_fn)(X509_STORE_CTX *ctx, X509_CRL *crl);
\& typedef int (*X509_STORE_CTX_cert_crl_fn)(X509_STORE_CTX *ctx,
\&                                           X509_CRL *crl, X509 *x);
\& typedef int (*X509_STORE_CTX_check_policy_fn)(X509_STORE_CTX *ctx);
\& typedef STACK_OF(X509) *(*X509_STORE_CTX_lookup_certs_fn)(X509_STORE_CTX *ctx,
\&                                                           const X509_NAME *nm);
\& typedef STACK_OF(X509_CRL) *(*X509_STORE_CTX_lookup_crls_fn)(const
\&                                                              X509_STORE_CTX *ctx,
\&                                                              const X509_NAME *nm);
\& typedef int (*X509_STORE_CTX_cleanup_fn)(X509_STORE_CTX *ctx);
\&
\& void X509_STORE_set_verify_cb(X509_STORE *ctx,
\&                               X509_STORE_CTX_verify_cb verify_cb);
\& X509_STORE_CTX_verify_cb X509_STORE_get_verify_cb(const X509_STORE_CTX *ctx);
\&
\& void X509_STORE_set_verify(X509_STORE *ctx, X509_STORE_CTX_verify_fn verify);
\& X509_STORE_CTX_verify_fn X509_STORE_CTX_get_verify(const X509_STORE_CTX *ctx);
\&
\& int X509_STORE_CTX_get1_issuer(X509 **issuer, X509_STORE_CTX *ctx, X509 *x);
\& X509_STORE_CTX_get_issuer_fn X509_STORE_get_get_issuer(const X509_STORE_CTX *ctx);
\& void X509_STORE_set_get_issuer(X509_STORE *ctx,
\&                                X509_STORE_CTX_get_issuer_fn get_issuer);
\&
\& void X509_STORE_set_check_issued(X509_STORE *ctx,
\&                                  X509_STORE_CTX_check_issued_fn check_issued);
\& X509_STORE_CTX_check_issued_fn
\&     X509_STORE_get_check_issued(const X509_STORE_CTX *ctx);
\&
\& void X509_STORE_set_check_revocation(X509_STORE *ctx,
\&                                      X509_STORE_CTX_check_revocation_fn check_revocation);
\& X509_STORE_CTX_check_revocation_fn
\&     X509_STORE_get_check_revocation(const X509_STORE_CTX *ctx);
\&
\& void X509_STORE_set_get_crl(X509_STORE *ctx,
\&                             X509_STORE_CTX_get_crl_fn get_crl);
\& X509_STORE_CTX_get_crl_fn X509_STORE_get_get_crl(const X509_STORE_CTX *ctx);
\&
\& void X509_STORE_set_check_crl(X509_STORE *ctx,
\&                               X509_STORE_CTX_check_crl_fn check_crl);
\& X509_STORE_CTX_check_crl_fn
\&     X509_STORE_get_check_crl(const X509_STORE_CTX *ctx);
\&
\& void X509_STORE_set_cert_crl(X509_STORE *ctx,
\&                              X509_STORE_CTX_cert_crl_fn cert_crl);
\& X509_STORE_CTX_cert_crl_fn X509_STORE_get_cert_crl(const X509_STORE_CTX *ctx);
\&
\& void X509_STORE_set_check_policy(X509_STORE *ctx,
\&                                  X509_STORE_CTX_check_policy_fn check_policy);
\& X509_STORE_CTX_check_policy_fn
\&     X509_STORE_get_check_policy(const X509_STORE_CTX *ctx);
\&
\& void X509_STORE_set_lookup_certs(X509_STORE *ctx,
\&                                  X509_STORE_CTX_lookup_certs_fn lookup_certs);
\& X509_STORE_CTX_lookup_certs_fn
\&     X509_STORE_get_lookup_certs(const X509_STORE_CTX *ctx);
\&
\& void X509_STORE_set_lookup_crls(X509_STORE *ctx,
\&                                 X509_STORE_CTX_lookup_crls_fn lookup_crls);
\& X509_STORE_CTX_lookup_crls_fn
\&     X509_STORE_get_lookup_crls(const X509_STORE_CTX *ctx);
\&
\& void X509_STORE_set_cleanup(X509_STORE *ctx,
\&                             X509_STORE_CTX_cleanup_fn cleanup);
\& X509_STORE_CTX_cleanup_fn X509_STORE_get_cleanup(const X509_STORE_CTX *ctx);
\&
\& /* Aliases */
\& void X509_STORE_set_verify_cb_func(X509_STORE *st,
\&                                    X509_STORE_CTX_verify_cb verify_cb);
\& void X509_STORE_set_verify_func(X509_STORE *ctx,
\&                                 X509_STORE_CTX_verify_fn verify);
\& void X509_STORE_set_lookup_crls_cb(X509_STORE *ctx,
\&                                    X509_STORE_CTX_lookup_crls_fn lookup_crls);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBX509_STORE_set_verify_cb()\fR sets the verification callback of \fIctx\fR to
\&\fIverify_cb\fR overwriting the previous callback.
The callback assigned with this function becomes a default for the one
that can be assigned directly to the corresponding \fBX509_STORE_CTX\fR,
please see \fBX509_STORE_CTX_set_verify_cb\fR\|(3) for further information.
.PP
\&\fBX509_STORE_set_verify()\fR sets the final chain verification function for
\&\fIctx\fR to \fIverify\fR.
Its purpose is to go through the chain of certificates and check that
all signatures are valid and that the current time is within the
limits of each certificate's first and last validity time.
The final chain verification functions must return 0 on failure and 1
on success.
\&\fIIf no chain verification function is provided, the internal default
function will be used instead.\fR
.PP
\&\fBX509_STORE_CTX_get1_issuer()\fR tries to find a certificate from the \fIstore\fR
component of \fIctx\fR with a subject name matching the issuer name of \fIx\fR.
On success it assigns to \fI*issuer\fR the first match that is currently valid,
or at least the most recently expired match if there is no currently valid one.
If the function returns 1 the caller is responsible for freeing \fI*issuer\fR.
.PP
\&\fBX509_STORE_set_get_issuer()\fR sets the function \fIget_issuer\fR
to get the "best" candidate issuer certificate of the given certificate \fIx\fR.
When such a certificate is found, \fIget_issuer\fR must up-ref and assign it
to \fI*issuer\fR and then return 1.
Otherwise \fIget_issuer\fR must return 0 if not found and \-1 (or 0) on failure.
If \fBX509_STORE_set_get_issuer()\fR is not used or \fIget_issuer\fR is NULL
then \fBX509_STORE_CTX_get1_issuer()\fR is used as the default implementation.
.PP
\&\fBX509_STORE_set_check_issued()\fR sets the function to check that a given
certificate \fIx\fR is issued by the issuer certificate \fIissuer\fR.
This function must return 0 on failure (among others if \fIx\fR hasn't
been issued with \fIissuer\fR) and 1 on success.
\&\fIIf no function to get the issuer is provided, the internal default
function will be used instead.\fR
.PP
\&\fBX509_STORE_set_check_revocation()\fR sets the revocation checking
function.
Its purpose is to look through the final chain and check the
revocation status for each certificate.
It must return 0 on failure and 1 on success.
\&\fIIf no function to get the issuer is provided, the internal default
function will be used instead.\fR
.PP
\&\fBX509_STORE_set_get_crl()\fR sets the function to get the crl for a given
certificate \fIx\fR.
When found, the crl must be assigned to \fI*crl\fR.
This function must return 0 on failure and 1 on success.
\&\fIIf no function to get the issuer is provided, the internal default
function will be used instead.\fR
.PP
\&\fBX509_STORE_set_check_crl()\fR sets the function to check the validity of
the given \fIcrl\fR.
This function must return 0 on failure and 1 on success.
\&\fIIf no function to get the issuer is provided, the internal default
function will be used instead.\fR
.PP
\&\fBX509_STORE_set_cert_crl()\fR sets the function to check the revocation
status of the given certificate \fIx\fR against the given \fIcrl\fR.
This function must return 0 on failure and 1 on success.
\&\fIIf no function to get the issuer is provided, the internal default
function will be used instead.\fR
.PP
\&\fBX509_STORE_set_check_policy()\fR sets the function to check the policies
of all the certificates in the final chain..
This function must return 0 on failure and 1 on success.
\&\fIIf no function to get the issuer is provided, the internal default
function will be used instead.\fR
.PP
\&\fBX509_STORE_set_lookup_certs()\fR and \fBX509_STORE_set_lookup_crls()\fR set the
functions to look up all the certs or all the CRLs that match the
given name \fInm\fR.
These functions return NULL on failure and a pointer to a stack of
certificates (\fBX509\fR) or to a stack of CRLs (\fBX509_CRL\fR) on
success.
\&\fIIf no function to get the issuer is provided, the internal default
function will be used instead.\fR
.PP
\&\fBX509_STORE_set_cleanup()\fR sets the final cleanup function, which is
called when the context (\fBX509_STORE_CTX\fR) is being torn down.
This function doesn't return any value.
\&\fIIf no function to get the issuer is provided, the internal default
function will be used instead.\fR
.PP
\&\fBX509_STORE_get_verify_cb()\fR, \fBX509_STORE_CTX_get_verify()\fR,
\&\fBX509_STORE_get_get_issuer()\fR, \fBX509_STORE_get_check_issued()\fR,
\&\fBX509_STORE_get_check_revocation()\fR, \fBX509_STORE_get_get_crl()\fR,
\&\fBX509_STORE_get_check_crl()\fR, \fBX509_STORE_set_verify()\fR,
\&\fBX509_STORE_set_get_issuer()\fR, \fBX509_STORE_get_cert_crl()\fR,
\&\fBX509_STORE_get_check_policy()\fR, \fBX509_STORE_get_lookup_certs()\fR,
\&\fBX509_STORE_get_lookup_crls()\fR and \fBX509_STORE_get_cleanup()\fR all return
the function pointer assigned with \fBX509_STORE_set_check_issued()\fR,
\&\fBX509_STORE_set_check_revocation()\fR, \fBX509_STORE_set_get_crl()\fR,
\&\fBX509_STORE_set_check_crl()\fR, \fBX509_STORE_set_cert_crl()\fR,
\&\fBX509_STORE_set_check_policy()\fR, \fBX509_STORE_set_lookup_certs()\fR,
\&\fBX509_STORE_set_lookup_crls()\fR and \fBX509_STORE_set_cleanup()\fR, or NULL if
no assignment has been made.
.PP
\&\fBX509_STORE_set_verify_cb_func()\fR, \fBX509_STORE_set_verify_func()\fR and
\&\fBX509_STORE_set_lookup_crls_cb()\fR are aliases for
\&\fBX509_STORE_set_verify_cb()\fR, \fBX509_STORE_set_verify()\fR and
X509_STORE_set_lookup_crls, available as macros for backward
compatibility.
.SH NOTES
.IX Header "NOTES"
All the callbacks from a \fBX509_STORE\fR are inherited by the
corresponding \fBX509_STORE_CTX\fR structure when it is initialized.
See \fBX509_STORE_CTX_set_verify_cb\fR\|(3) for further details.
.SH BUGS
.IX Header "BUGS"
The macro version of this function was the only one available before
OpenSSL 1.0.0.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
The X509_STORE_set_*() functions do not return a value.
.PP
The X509_STORE_get_*() functions return a pointer of the appropriate
function type.
.PP
\&\fBX509_STORE_CTX_get1_issuer()\fR returns
1 if a suitable certificate is found, 0 if not found, \-1 on other error.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBX509_STORE_CTX_set_verify_cb\fR\|(3), \fBX509_STORE_CTX_get0_chain\fR\|(3),
\&\fBX509_STORE_CTX_verify_cb\fR\|(3), \fBX509_STORE_CTX_verify_fn\fR\|(3),
\&\fBCMS_verify\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
The \fBX509_STORE_set_verify_cb()\fR function was added in OpenSSL 1.0.0.
.PP
The functions
\&\fBX509_STORE_set_verify_cb()\fR, \fBX509_STORE_get_verify_cb()\fR,
\&\fBX509_STORE_set_verify()\fR, \fBX509_STORE_CTX_get_verify()\fR,
\&\fBX509_STORE_set_get_issuer()\fR, \fBX509_STORE_get_get_issuer()\fR,
\&\fBX509_STORE_set_check_issued()\fR, \fBX509_STORE_get_check_issued()\fR,
\&\fBX509_STORE_set_check_revocation()\fR, \fBX509_STORE_get_check_revocation()\fR,
\&\fBX509_STORE_set_get_crl()\fR, \fBX509_STORE_get_get_crl()\fR,
\&\fBX509_STORE_set_check_crl()\fR, \fBX509_STORE_get_check_crl()\fR,
\&\fBX509_STORE_set_cert_crl()\fR, \fBX509_STORE_get_cert_crl()\fR,
\&\fBX509_STORE_set_check_policy()\fR, \fBX509_STORE_get_check_policy()\fR,
\&\fBX509_STORE_set_lookup_certs()\fR, \fBX509_STORE_get_lookup_certs()\fR,
\&\fBX509_STORE_set_lookup_crls()\fR, \fBX509_STORE_get_lookup_crls()\fR,
\&\fBX509_STORE_set_cleanup()\fR and \fBX509_STORE_get_cleanup()\fR
were added in OpenSSL 1.1.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2009\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
