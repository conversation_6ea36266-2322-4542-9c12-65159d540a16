.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "PKCS12_KEY_GEN_UTF8_EX 3ossl"
.TH PKCS12_KEY_GEN_UTF8_EX 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
PKCS12_key_gen_asc, PKCS12_key_gen_asc_ex,
PKCS12_key_gen_uni, PKCS12_key_gen_uni_ex,
PKCS12_key_gen_utf8, PKCS12_key_gen_utf8_ex \- PKCS#12 Password based key derivation
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/pkcs12.h>
\&
\& int PKCS12_key_gen_asc(const char *pass, int passlen, unsigned char *salt,
\&                        int saltlen, int id, int iter, int n,
\&                        unsigned char *out, const EVP_MD *md_type);
\& int PKCS12_key_gen_asc_ex(const char *pass, int passlen, unsigned char *salt,
\&                           int saltlen, int id, int iter, int n,
\&                           unsigned char *out, const EVP_MD *md_type,
\&                           OSSL_LIB_CTX *ctx, const char *propq);
\& int PKCS12_key_gen_uni(unsigned char *pass, int passlen, unsigned char *salt,
\&                        int saltlen, int id, int iter, int n,
\&                        unsigned char *out, const EVP_MD *md_type);
\& int PKCS12_key_gen_uni_ex(unsigned char *pass, int passlen, unsigned char *salt,
\&                           int saltlen, int id, int iter, int n,
\&                           unsigned char *out, const EVP_MD *md_type,
\&                           OSSL_LIB_CTX *ctx, const char *propq);
\& int PKCS12_key_gen_utf8(const char *pass, int passlen, unsigned char *salt,
\&                         int saltlen, int id, int iter, int n,
\&                         unsigned char *out, const EVP_MD *md_type);
\& int PKCS12_key_gen_utf8_ex(const char *pass, int passlen, unsigned char *salt,
\&                            int saltlen, int id, int iter, int n,
\&                            unsigned char *out, const EVP_MD *md_type,
\&                            OSSL_LIB_CTX *ctx, const char *propq);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
These methods perform a key derivation according to PKCS#12 (RFC7292)
with an input password \fIpass\fR of length \fIpasslen\fR, a salt \fIsalt\fR of length
\&\fIsaltlen\fR, an iteration count \fIiter\fR and a digest algorithm \fImd_type\fR.
The ID byte \fIid\fR determines how the resulting key is intended to be used:
.IP \(bu 4
If ID=1, then the pseudorandom bits being produced are to be used
as key material for performing encryption or decryption.
.IP \(bu 4
If ID=2, then the pseudorandom bits being produced are to be used
as an IV (Initial Value) for encryption or decryption.
.IP \(bu 4
If ID=3, then the pseudorandom bits being produced are to be used
as an integrity key for MACing.
.PP
The intended format of the supplied password is determined by the method chosen:
.IP \(bu 4
\&\fBPKCS12_key_gen_asc()\fR and \fBPKCS12_key_gen_asc_ex()\fR expect an ASCII-formatted password.
.IP \(bu 4
\&\fBPKCS12_key_gen_uni()\fR and \fBPKCS12_key_gen_uni_ex()\fR expect a Unicode-formatted password.
.IP \(bu 4
\&\fBPKCS12_key_gen_utf8()\fR and \fBPKCS12_key_gen_utf8_ex()\fR expect a UTF\-8 encoded password.
.PP
\&\fIpass\fR is the password used in the derivation of length \fIpasslen\fR. \fIpass\fR
is an optional parameter and can be NULL. If \fIpasslen\fR is \-1, then the
function will calculate the length of \fIpass\fR using \fBstrlen()\fR.
.PP
\&\fIsalt\fR is the salt used in the derivation of length \fIsaltlen\fR. If the
\&\fIsalt\fR is NULL, then \fIsaltlen\fR must be 0. The function will not
attempt to calculate the length of the \fIsalt\fR because it is not assumed to
be NULL terminated.
.PP
\&\fIiter\fR is the iteration count and its value should be greater than or
equal to 1. RFC 2898 suggests an iteration count of at least 1000. Any
\&\fIiter\fR less than 1 is treated as a single iteration.
.PP
\&\fIdigest\fR is the message digest function used in the derivation.
.PP
The derived key will be written to \fIout\fR. The size of the \fIout\fR buffer
is specified via \fIn\fR.
.PP
Functions ending in \fB_ex()\fR allow for a library context \fIctx\fR and property query
\&\fIpropq\fR to be used to select algorithm implementations.
.SH NOTES
.IX Header "NOTES"
A typical application of this function is to derive keying material for an
encryption algorithm from a password in the \fIpass\fR, a salt in \fIsalt\fR,
and an iteration count.
.PP
Increasing the \fIiter\fR parameter slows down the algorithm which makes it
harder for an attacker to perform a brute force attack using a large number
of candidate passwords.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
Returns 1 on success or 0 on error.
.SH "CONFORMING TO"
.IX Header "CONFORMING TO"
IETF RFC 7292 (<https://tools.ietf.org/html/rfc7292>)
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBPKCS12_create_ex\fR\|(3),
\&\fBPKCS12_pbe_crypt_ex\fR\|(3),
\&\fBpassphrase\-encoding\fR\|(7)
.SH HISTORY
.IX Header "HISTORY"
\&\fBPKCS12_key_gen_asc_ex()\fR, \fBPKCS12_key_gen_uni_ex()\fR and \fBPKCS12_key_gen_utf8_ex()\fR
were added in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
