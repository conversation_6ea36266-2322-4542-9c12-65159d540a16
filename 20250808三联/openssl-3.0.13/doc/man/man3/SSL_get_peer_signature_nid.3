.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "SSL_GET_PEER_SIGNATURE_NID 3ossl"
.TH SSL_GET_PEER_SIGNATURE_NID 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
SSL_get_peer_signature_nid, SSL_get_peer_signature_type_nid,
SSL_get_signature_nid, SSL_get_signature_type_nid \- get TLS message signing
types
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/ssl.h>
\&
\& int SSL_get_peer_signature_nid(SSL *ssl, int *psig_nid);
\& int SSL_get_peer_signature_type_nid(const SSL *ssl, int *psigtype_nid);
\& int SSL_get_signature_nid(SSL *ssl, int *psig_nid);
\& int SSL_get_signature_type_nid(const SSL *ssl, int *psigtype_nid);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBSSL_get_peer_signature_nid()\fR sets \fB*psig_nid\fR to the NID of the digest used
by the peer to sign TLS messages. It is implemented as a macro.
.PP
\&\fBSSL_get_peer_signature_type_nid()\fR sets \fB*psigtype_nid\fR to the signature
type used by the peer to sign TLS messages. Currently the signature type
is the NID of the public key type used for signing except for PSS signing
where it is \fBEVP_PKEY_RSA_PSS\fR. To differentiate between
\&\fBrsa_pss_rsae_*\fR and \fBrsa_pss_pss_*\fR signatures, it's necessary to check
the type of public key in the peer's certificate.
.PP
\&\fBSSL_get_signature_nid()\fR and \fBSSL_get_signature_type_nid()\fR return the equivalent
information for the local end of the connection.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
These functions return 1 for success and 0 for failure. There are several
possible reasons for failure: the cipher suite has no signature (e.g. it
uses RSA key exchange or is anonymous), the TLS version is below 1.2 or
the functions were called too early, e.g. before the peer signed a message.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBssl\fR\|(7), \fBSSL_get_peer_certificate\fR\|(3),
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2017\-2018 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
