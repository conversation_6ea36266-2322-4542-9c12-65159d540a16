.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "SSL_SET_CONNECT_STATE 3ossl"
.TH SSL_SET_CONNECT_STATE 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
SSL_set_connect_state, SSL_set_accept_state, SSL_is_server
\&\- functions for manipulating and examining the client or server mode of an SSL object
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/ssl.h>
\&
\& void SSL_set_connect_state(SSL *ssl);
\&
\& void SSL_set_accept_state(SSL *ssl);
\&
\& int SSL_is_server(const SSL *ssl);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBSSL_set_connect_state()\fR sets \fBssl\fR to work in client mode.
.PP
\&\fBSSL_set_accept_state()\fR sets \fBssl\fR to work in server mode.
.PP
\&\fBSSL_is_server()\fR checks if \fBssl\fR is working in server mode.
.SH NOTES
.IX Header "NOTES"
When the SSL_CTX object was created with \fBSSL_CTX_new\fR\|(3),
it was either assigned a dedicated client method, a dedicated server
method, or a generic method, that can be used for both client and
server connections. (The method might have been changed with
\&\fBSSL_CTX_set_ssl_version\fR\|(3) or
\&\fBSSL_set_ssl_method\fR\|(3).)
.PP
When beginning a new handshake, the SSL engine must know whether it must
call the connect (client) or accept (server) routines. Even though it may
be clear from the method chosen, whether client or server mode was
requested, the handshake routines must be explicitly set.
.PP
When using the \fBSSL_connect\fR\|(3) or
\&\fBSSL_accept\fR\|(3) routines, the correct handshake
routines are automatically set. When performing a transparent negotiation
using \fBSSL_write_ex\fR\|(3), \fBSSL_write\fR\|(3), \fBSSL_read_ex\fR\|(3), or \fBSSL_read\fR\|(3),
the handshake routines must be explicitly set in advance using either
\&\fBSSL_set_connect_state()\fR or \fBSSL_set_accept_state()\fR.
.PP
If \fBSSL_is_server()\fR is called before \fBSSL_set_connect_state()\fR or
\&\fBSSL_set_accept_state()\fR is called (either automatically or explicitly),
the result depends on what method was used when SSL_CTX was created with
\&\fBSSL_CTX_new\fR\|(3). If a generic method or a dedicated server method was
passed to \fBSSL_CTX_new\fR\|(3), \fBSSL_is_server()\fR returns 1; otherwise, it returns 0.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBSSL_set_connect_state()\fR and \fBSSL_set_accept_state()\fR do not return diagnostic
information.
.PP
\&\fBSSL_is_server()\fR returns 1 if \fBssl\fR is working in server mode or 0 for client mode.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBssl\fR\|(7), \fBSSL_new\fR\|(3), \fBSSL_CTX_new\fR\|(3),
\&\fBSSL_connect\fR\|(3), \fBSSL_accept\fR\|(3),
\&\fBSSL_write_ex\fR\|(3), \fBSSL_write\fR\|(3), \fBSSL_read_ex\fR\|(3), \fBSSL_read\fR\|(3),
\&\fBSSL_do_handshake\fR\|(3),
\&\fBSSL_CTX_set_ssl_version\fR\|(3)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2001\-2017 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
