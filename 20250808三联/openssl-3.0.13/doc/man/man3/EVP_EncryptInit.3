.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "EVP_ENCRYPTINIT 3ossl"
.TH EVP_ENCRYPTINIT 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
EVP_CIPHER_fetch,
EVP_CIPHER_up_ref,
EVP_CIPHER_free,
EVP_CIPHER_CTX_new,
EVP_CIPHER_CTX_reset,
EVP_CIPHER_CTX_free,
EVP_EncryptInit_ex,
EVP_EncryptInit_ex2,
EVP_EncryptUpdate,
EVP_EncryptFinal_ex,
EVP_DecryptInit_ex,
EVP_DecryptInit_ex2,
EVP_DecryptUpdate,
EVP_DecryptFinal_ex,
EVP_CipherInit_ex,
EVP_CipherInit_ex2,
EVP_CipherUpdate,
EVP_CipherFinal_ex,
EVP_CIPHER_CTX_set_key_length,
EVP_CIPHER_CTX_ctrl,
EVP_EncryptInit,
EVP_EncryptFinal,
EVP_DecryptInit,
EVP_DecryptFinal,
EVP_CipherInit,
EVP_CipherFinal,
EVP_Cipher,
EVP_get_cipherbyname,
EVP_get_cipherbynid,
EVP_get_cipherbyobj,
EVP_CIPHER_is_a,
EVP_CIPHER_get0_name,
EVP_CIPHER_get0_description,
EVP_CIPHER_names_do_all,
EVP_CIPHER_get0_provider,
EVP_CIPHER_get_nid,
EVP_CIPHER_get_params,
EVP_CIPHER_gettable_params,
EVP_CIPHER_get_block_size,
EVP_CIPHER_get_key_length,
EVP_CIPHER_get_iv_length,
EVP_CIPHER_get_flags,
EVP_CIPHER_get_mode,
EVP_CIPHER_get_type,
EVP_CIPHER_CTX_cipher,
EVP_CIPHER_CTX_get0_cipher,
EVP_CIPHER_CTX_get1_cipher,
EVP_CIPHER_CTX_get0_name,
EVP_CIPHER_CTX_get_nid,
EVP_CIPHER_CTX_get_params,
EVP_CIPHER_gettable_ctx_params,
EVP_CIPHER_CTX_gettable_params,
EVP_CIPHER_CTX_set_params,
EVP_CIPHER_settable_ctx_params,
EVP_CIPHER_CTX_settable_params,
EVP_CIPHER_CTX_get_block_size,
EVP_CIPHER_CTX_get_key_length,
EVP_CIPHER_CTX_get_iv_length,
EVP_CIPHER_CTX_get_tag_length,
EVP_CIPHER_CTX_get_app_data,
EVP_CIPHER_CTX_set_app_data,
EVP_CIPHER_CTX_flags,
EVP_CIPHER_CTX_set_flags,
EVP_CIPHER_CTX_clear_flags,
EVP_CIPHER_CTX_test_flags,
EVP_CIPHER_CTX_get_type,
EVP_CIPHER_CTX_get_mode,
EVP_CIPHER_CTX_get_num,
EVP_CIPHER_CTX_set_num,
EVP_CIPHER_CTX_is_encrypting,
EVP_CIPHER_param_to_asn1,
EVP_CIPHER_asn1_to_param,
EVP_CIPHER_CTX_set_padding,
EVP_enc_null,
EVP_CIPHER_do_all_provided,
EVP_CIPHER_nid,
EVP_CIPHER_name,
EVP_CIPHER_block_size,
EVP_CIPHER_key_length,
EVP_CIPHER_iv_length,
EVP_CIPHER_flags,
EVP_CIPHER_mode,
EVP_CIPHER_type,
EVP_CIPHER_CTX_encrypting,
EVP_CIPHER_CTX_nid,
EVP_CIPHER_CTX_block_size,
EVP_CIPHER_CTX_key_length,
EVP_CIPHER_CTX_iv_length,
EVP_CIPHER_CTX_tag_length,
EVP_CIPHER_CTX_num,
EVP_CIPHER_CTX_type,
EVP_CIPHER_CTX_mode
\&\- EVP cipher routines
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/evp.h>
\&
\& EVP_CIPHER *EVP_CIPHER_fetch(OSSL_LIB_CTX *ctx, const char *algorithm,
\&                              const char *properties);
\& int EVP_CIPHER_up_ref(EVP_CIPHER *cipher);
\& void EVP_CIPHER_free(EVP_CIPHER *cipher);
\& EVP_CIPHER_CTX *EVP_CIPHER_CTX_new(void);
\& int EVP_CIPHER_CTX_reset(EVP_CIPHER_CTX *ctx);
\& void EVP_CIPHER_CTX_free(EVP_CIPHER_CTX *ctx);
\&
\& int EVP_EncryptInit_ex(EVP_CIPHER_CTX *ctx, const EVP_CIPHER *type,
\&                        ENGINE *impl, const unsigned char *key, const unsigned char *iv);
\& int EVP_EncryptInit_ex2(EVP_CIPHER_CTX *ctx, const EVP_CIPHER *type,
\&                         const unsigned char *key, const unsigned char *iv,
\&                         const OSSL_PARAM params[]);
\& int EVP_EncryptUpdate(EVP_CIPHER_CTX *ctx, unsigned char *out,
\&                       int *outl, const unsigned char *in, int inl);
\& int EVP_EncryptFinal_ex(EVP_CIPHER_CTX *ctx, unsigned char *out, int *outl);
\&
\& int EVP_DecryptInit_ex(EVP_CIPHER_CTX *ctx, const EVP_CIPHER *type,
\&                        ENGINE *impl, const unsigned char *key, const unsigned char *iv);
\& int EVP_DecryptInit_ex2(EVP_CIPHER_CTX *ctx, const EVP_CIPHER *type,
\&                         const unsigned char *key, const unsigned char *iv,
\&                         const OSSL_PARAM params[]);
\& int EVP_DecryptUpdate(EVP_CIPHER_CTX *ctx, unsigned char *out,
\&                       int *outl, const unsigned char *in, int inl);
\& int EVP_DecryptFinal_ex(EVP_CIPHER_CTX *ctx, unsigned char *outm, int *outl);
\&
\& int EVP_CipherInit_ex(EVP_CIPHER_CTX *ctx, const EVP_CIPHER *type,
\&                       ENGINE *impl, const unsigned char *key, const unsigned char *iv, int enc);
\& int EVP_CipherInit_ex2(EVP_CIPHER_CTX *ctx, const EVP_CIPHER *type,
\&                        const unsigned char *key, const unsigned char *iv,
\&                        int enc, const OSSL_PARAM params[]);
\& int EVP_CipherUpdate(EVP_CIPHER_CTX *ctx, unsigned char *out,
\&                      int *outl, const unsigned char *in, int inl);
\& int EVP_CipherFinal_ex(EVP_CIPHER_CTX *ctx, unsigned char *outm, int *outl);
\&
\& int EVP_EncryptInit(EVP_CIPHER_CTX *ctx, const EVP_CIPHER *type,
\&                     const unsigned char *key, const unsigned char *iv);
\& int EVP_EncryptFinal(EVP_CIPHER_CTX *ctx, unsigned char *out, int *outl);
\&
\& int EVP_DecryptInit(EVP_CIPHER_CTX *ctx, const EVP_CIPHER *type,
\&                     const unsigned char *key, const unsigned char *iv);
\& int EVP_DecryptFinal(EVP_CIPHER_CTX *ctx, unsigned char *outm, int *outl);
\&
\& int EVP_CipherInit(EVP_CIPHER_CTX *ctx, const EVP_CIPHER *type,
\&                    const unsigned char *key, const unsigned char *iv, int enc);
\& int EVP_CipherFinal(EVP_CIPHER_CTX *ctx, unsigned char *outm, int *outl);
\&
\& int EVP_Cipher(EVP_CIPHER_CTX *ctx, unsigned char *out,
\&                const unsigned char *in, unsigned int inl);
\&
\& int EVP_CIPHER_CTX_set_padding(EVP_CIPHER_CTX *x, int padding);
\& int EVP_CIPHER_CTX_set_key_length(EVP_CIPHER_CTX *x, int keylen);
\& int EVP_CIPHER_CTX_ctrl(EVP_CIPHER_CTX *ctx, int cmd, int p1, void *p2);
\& int EVP_CIPHER_CTX_rand_key(EVP_CIPHER_CTX *ctx, unsigned char *key);
\& void EVP_CIPHER_CTX_set_flags(EVP_CIPHER_CTX *ctx, int flags);
\& void EVP_CIPHER_CTX_clear_flags(EVP_CIPHER_CTX *ctx, int flags);
\& int EVP_CIPHER_CTX_test_flags(const EVP_CIPHER_CTX *ctx, int flags);
\&
\& const EVP_CIPHER *EVP_get_cipherbyname(const char *name);
\& const EVP_CIPHER *EVP_get_cipherbynid(int nid);
\& const EVP_CIPHER *EVP_get_cipherbyobj(const ASN1_OBJECT *a);
\&
\& int EVP_CIPHER_get_nid(const EVP_CIPHER *e);
\& int EVP_CIPHER_is_a(const EVP_CIPHER *cipher, const char *name);
\& int EVP_CIPHER_names_do_all(const EVP_CIPHER *cipher,
\&                             void (*fn)(const char *name, void *data),
\&                             void *data);
\& const char *EVP_CIPHER_get0_name(const EVP_CIPHER *cipher);
\& const char *EVP_CIPHER_get0_description(const EVP_CIPHER *cipher);
\& const OSSL_PROVIDER *EVP_CIPHER_get0_provider(const EVP_CIPHER *cipher);
\& int EVP_CIPHER_get_block_size(const EVP_CIPHER *e);
\& int EVP_CIPHER_get_key_length(const EVP_CIPHER *e);
\& int EVP_CIPHER_get_iv_length(const EVP_CIPHER *e);
\& unsigned long EVP_CIPHER_get_flags(const EVP_CIPHER *e);
\& unsigned long EVP_CIPHER_get_mode(const EVP_CIPHER *e);
\& int EVP_CIPHER_get_type(const EVP_CIPHER *cipher);
\&
\& const EVP_CIPHER *EVP_CIPHER_CTX_get0_cipher(const EVP_CIPHER_CTX *ctx);
\& EVP_CIPHER *EVP_CIPHER_CTX_get1_cipher(const EVP_CIPHER_CTX *ctx);
\& int EVP_CIPHER_CTX_get_nid(const EVP_CIPHER_CTX *ctx);
\& const char *EVP_CIPHER_CTX_get0_name(const EVP_CIPHER_CTX *ctx);
\&
\& int EVP_CIPHER_get_params(EVP_CIPHER *cipher, OSSL_PARAM params[]);
\& int EVP_CIPHER_CTX_set_params(EVP_CIPHER_CTX *ctx, const OSSL_PARAM params[]);
\& int EVP_CIPHER_CTX_get_params(EVP_CIPHER_CTX *ctx, OSSL_PARAM params[]);
\& const OSSL_PARAM *EVP_CIPHER_gettable_params(const EVP_CIPHER *cipher);
\& const OSSL_PARAM *EVP_CIPHER_settable_ctx_params(const EVP_CIPHER *cipher);
\& const OSSL_PARAM *EVP_CIPHER_gettable_ctx_params(const EVP_CIPHER *cipher);
\& const OSSL_PARAM *EVP_CIPHER_CTX_settable_params(EVP_CIPHER_CTX *ctx);
\& const OSSL_PARAM *EVP_CIPHER_CTX_gettable_params(EVP_CIPHER_CTX *ctx);
\& int EVP_CIPHER_CTX_get_block_size(const EVP_CIPHER_CTX *ctx);
\& int EVP_CIPHER_CTX_get_key_length(const EVP_CIPHER_CTX *ctx);
\& int EVP_CIPHER_CTX_get_iv_length(const EVP_CIPHER_CTX *ctx);
\& int EVP_CIPHER_CTX_get_tag_length(const EVP_CIPHER_CTX *ctx);
\& void *EVP_CIPHER_CTX_get_app_data(const EVP_CIPHER_CTX *ctx);
\& void EVP_CIPHER_CTX_set_app_data(const EVP_CIPHER_CTX *ctx, void *data);
\& int EVP_CIPHER_CTX_get_type(const EVP_CIPHER_CTX *ctx);
\& int EVP_CIPHER_CTX_get_mode(const EVP_CIPHER_CTX *ctx);
\& int EVP_CIPHER_CTX_get_num(const EVP_CIPHER_CTX *ctx);
\& int EVP_CIPHER_CTX_set_num(EVP_CIPHER_CTX *ctx, int num);
\& int EVP_CIPHER_CTX_is_encrypting(const EVP_CIPHER_CTX *ctx);
\&
\& int EVP_CIPHER_param_to_asn1(EVP_CIPHER_CTX *c, ASN1_TYPE *type);
\& int EVP_CIPHER_asn1_to_param(EVP_CIPHER_CTX *c, ASN1_TYPE *type);
\&
\& void EVP_CIPHER_do_all_provided(OSSL_LIB_CTX *libctx,
\&                                 void (*fn)(EVP_CIPHER *cipher, void *arg),
\&                                 void *arg);
\&
\& #define EVP_CIPHER_nid EVP_CIPHER_get_nid
\& #define EVP_CIPHER_name EVP_CIPHER_get0_name
\& #define EVP_CIPHER_block_size EVP_CIPHER_get_block_size
\& #define EVP_CIPHER_key_length EVP_CIPHER_get_key_length
\& #define EVP_CIPHER_iv_length EVP_CIPHER_get_iv_length
\& #define EVP_CIPHER_flags EVP_CIPHER_get_flags
\& #define EVP_CIPHER_mode EVP_CIPHER_get_mode
\& #define EVP_CIPHER_type EVP_CIPHER_get_type
\& #define EVP_CIPHER_CTX_encrypting EVP_CIPHER_CTX_is_encrypting
\& #define EVP_CIPHER_CTX_nid EVP_CIPHER_CTX_get_nid
\& #define EVP_CIPHER_CTX_block_size EVP_CIPHER_CTX_get_block_size
\& #define EVP_CIPHER_CTX_key_length EVP_CIPHER_CTX_get_key_length
\& #define EVP_CIPHER_CTX_iv_length EVP_CIPHER_CTX_get_iv_length
\& #define EVP_CIPHER_CTX_tag_length EVP_CIPHER_CTX_get_tag_length
\& #define EVP_CIPHER_CTX_num EVP_CIPHER_CTX_get_num
\& #define EVP_CIPHER_CTX_type EVP_CIPHER_CTX_get_type
\& #define EVP_CIPHER_CTX_mode EVP_CIPHER_CTX_get_mode
.Ve
.PP
The following function has been deprecated since OpenSSL 3.0, and can be
hidden entirely by defining \fBOPENSSL_API_COMPAT\fR with a suitable version value,
see \fBopenssl_user_macros\fR\|(7):
.PP
.Vb 1
\& const EVP_CIPHER *EVP_CIPHER_CTX_cipher(const EVP_CIPHER_CTX *ctx);
.Ve
.PP
The following function has been deprecated since OpenSSL 1.1.0, and can be
hidden entirely by defining \fBOPENSSL_API_COMPAT\fR with a suitable version value,
see \fBopenssl_user_macros\fR\|(7):
.PP
.Vb 1
\& int EVP_CIPHER_CTX_flags(const EVP_CIPHER_CTX *ctx);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
The EVP cipher routines are a high-level interface to certain
symmetric ciphers.
.PP
The \fBEVP_CIPHER\fR type is a structure for cipher method implementation.
.IP \fBEVP_CIPHER_fetch()\fR 4
.IX Item "EVP_CIPHER_fetch()"
Fetches the cipher implementation for the given \fIalgorithm\fR from any provider
offering it, within the criteria given by the \fIproperties\fR.
See "ALGORITHM FETCHING" in \fBcrypto\fR\|(7) for further information.
.Sp
The returned value must eventually be freed with \fBEVP_CIPHER_free()\fR.
.Sp
Fetched \fBEVP_CIPHER\fR structures are reference counted.
.IP \fBEVP_CIPHER_up_ref()\fR 4
.IX Item "EVP_CIPHER_up_ref()"
Increments the reference count for an \fBEVP_CIPHER\fR structure.
.IP \fBEVP_CIPHER_free()\fR 4
.IX Item "EVP_CIPHER_free()"
Decrements the reference count for the fetched \fBEVP_CIPHER\fR structure.
If the reference count drops to 0 then the structure is freed.
.IP \fBEVP_CIPHER_CTX_new()\fR 4
.IX Item "EVP_CIPHER_CTX_new()"
Allocates and returns a cipher context.
.IP \fBEVP_CIPHER_CTX_free()\fR 4
.IX Item "EVP_CIPHER_CTX_free()"
Clears all information from a cipher context and frees any allocated memory
associated with it, including \fIctx\fR itself. This function should be called after
all operations using a cipher are complete so sensitive information does not
remain in memory.
.IP \fBEVP_CIPHER_CTX_ctrl()\fR 4
.IX Item "EVP_CIPHER_CTX_ctrl()"
\&\fIThis is a legacy method.\fR \fBEVP_CIPHER_CTX_set_params()\fR and
\&\fBEVP_CIPHER_CTX_get_params()\fR is the mechanism that should be used to set and get
parameters that are used by providers.
.Sp
Performs cipher-specific control actions on context \fIctx\fR. The control command
is indicated in \fIcmd\fR and any additional arguments in \fIp1\fR and \fIp2\fR.
\&\fBEVP_CIPHER_CTX_ctrl()\fR must be called after \fBEVP_CipherInit_ex2()\fR. Other restrictions
may apply depending on the control type and cipher implementation.
.Sp
If this function happens to be used with a fetched \fBEVP_CIPHER\fR, it will
translate the controls that are known to OpenSSL into \fBOSSL_PARAM\fR\|(3)
parameters with keys defined by OpenSSL and call \fBEVP_CIPHER_CTX_get_params()\fR or
\&\fBEVP_CIPHER_CTX_set_params()\fR as is appropriate for each control command.
.Sp
See "CONTROLS" below for more information, including what translations are
being done.
.IP \fBEVP_CIPHER_get_params()\fR 4
.IX Item "EVP_CIPHER_get_params()"
Retrieves the requested list of algorithm \fIparams\fR from a CIPHER \fIcipher\fR.
See "PARAMETERS" below for more information.
.IP \fBEVP_CIPHER_CTX_get_params()\fR 4
.IX Item "EVP_CIPHER_CTX_get_params()"
Retrieves the requested list of \fIparams\fR from CIPHER context \fIctx\fR.
See "PARAMETERS" below for more information.
.IP \fBEVP_CIPHER_CTX_set_params()\fR 4
.IX Item "EVP_CIPHER_CTX_set_params()"
Sets the list of \fIparams\fR into a CIPHER context \fIctx\fR.
See "PARAMETERS" below for more information.
.IP \fBEVP_CIPHER_gettable_params()\fR 4
.IX Item "EVP_CIPHER_gettable_params()"
Get a constant \fBOSSL_PARAM\fR\|(3) array that describes the retrievable parameters
that can be used with \fBEVP_CIPHER_get_params()\fR.
.IP "\fBEVP_CIPHER_gettable_ctx_params()\fR and \fBEVP_CIPHER_CTX_gettable_params()\fR" 4
.IX Item "EVP_CIPHER_gettable_ctx_params() and EVP_CIPHER_CTX_gettable_params()"
Get a constant \fBOSSL_PARAM\fR\|(3) array that describes the retrievable parameters
that can be used with \fBEVP_CIPHER_CTX_get_params()\fR.
\&\fBEVP_CIPHER_gettable_ctx_params()\fR returns the parameters that can be retrieved
from the algorithm, whereas \fBEVP_CIPHER_CTX_gettable_params()\fR returns the
parameters that can be retrieved in the context's current state.
.IP "\fBEVP_CIPHER_settable_ctx_params()\fR and \fBEVP_CIPHER_CTX_settable_params()\fR" 4
.IX Item "EVP_CIPHER_settable_ctx_params() and EVP_CIPHER_CTX_settable_params()"
Get a constant \fBOSSL_PARAM\fR\|(3) array that describes the settable parameters
that can be used with \fBEVP_CIPHER_CTX_set_params()\fR.
\&\fBEVP_CIPHER_settable_ctx_params()\fR returns the parameters that can be set from the
algorithm, whereas \fBEVP_CIPHER_CTX_settable_params()\fR returns the parameters that
can be set in the context's current state.
.IP \fBEVP_EncryptInit_ex2()\fR 4
.IX Item "EVP_EncryptInit_ex2()"
Sets up cipher context \fIctx\fR for encryption with cipher \fItype\fR. \fItype\fR is
typically supplied by calling \fBEVP_CIPHER_fetch()\fR. \fItype\fR may also be set
using legacy functions such as \fBEVP_aes_256_cbc()\fR, but this is not recommended
for new applications. \fIkey\fR is the symmetric key to use and \fIiv\fR is the IV to
use (if necessary), the actual number of bytes used for the key and IV depends
on the cipher. The parameters \fIparams\fR will be set on the context after
initialisation. It is possible to set all parameters to NULL except \fItype\fR in
an initial call and supply the remaining parameters in subsequent calls, all of
which have \fItype\fR set to NULL. This is done when the default cipher parameters
are not appropriate.
For \fBEVP_CIPH_GCM_MODE\fR the IV will be generated internally if it is not
specified.
.IP \fBEVP_EncryptInit_ex()\fR 4
.IX Item "EVP_EncryptInit_ex()"
This legacy function is similar to \fBEVP_EncryptInit_ex2()\fR when \fIimpl\fR is NULL.
The implementation of the \fItype\fR from the \fIimpl\fR engine will be used if it
exists.
.IP \fBEVP_EncryptUpdate()\fR 4
.IX Item "EVP_EncryptUpdate()"
Encrypts \fIinl\fR bytes from the buffer \fIin\fR and writes the encrypted version to
\&\fIout\fR. The pointers \fIout\fR and \fIin\fR may point to the same location, in which
case the encryption will be done in-place. If \fIout\fR and \fIin\fR point to different
locations, the two buffers must be disjoint, otherwise the operation might fail
or the outcome might be undefined.
.Sp
This function can be called multiple times to encrypt successive blocks
of data. The amount of data written depends on the block alignment of the
encrypted data.
For most ciphers and modes, the amount of data written can be anything
from zero bytes to (inl + cipher_block_size \- 1) bytes.
For wrap cipher modes, the amount of data written can be anything
from zero bytes to (inl + cipher_block_size) bytes.
For stream ciphers, the amount of data written can be anything from zero
bytes to inl bytes.
Thus, the buffer pointed to by \fIout\fR must contain sufficient room for the
operation being performed.
The actual number of bytes written is placed in \fIoutl\fR.
.Sp
If padding is enabled (the default) then \fBEVP_EncryptFinal_ex()\fR encrypts
the "final" data, that is any data that remains in a partial block.
It uses standard block padding (aka PKCS padding) as described in
the NOTES section, below. The encrypted
final data is written to \fIout\fR which should have sufficient space for
one cipher block. The number of bytes written is placed in \fIoutl\fR. After
this function is called the encryption operation is finished and no further
calls to \fBEVP_EncryptUpdate()\fR should be made.
.Sp
If padding is disabled then \fBEVP_EncryptFinal_ex()\fR will not encrypt any more
data and it will return an error if any data remains in a partial block:
that is if the total data length is not a multiple of the block size.
.IP "\fBEVP_DecryptInit_ex2()\fR, \fBEVP_DecryptInit_ex()\fR, \fBEVP_DecryptUpdate()\fR and \fBEVP_DecryptFinal_ex()\fR" 4
.IX Item "EVP_DecryptInit_ex2(), EVP_DecryptInit_ex(), EVP_DecryptUpdate() and EVP_DecryptFinal_ex()"
These functions are the corresponding decryption operations.
\&\fBEVP_DecryptFinal()\fR will return an error code if padding is enabled and the
final block is not correctly formatted. The parameters and restrictions are
identical to the encryption operations except that if padding is enabled the
decrypted data buffer \fIout\fR passed to \fBEVP_DecryptUpdate()\fR should have
sufficient room for (\fIinl\fR + cipher_block_size) bytes unless the cipher block
size is 1 in which case \fIinl\fR bytes is sufficient.
.IP "\fBEVP_CipherInit_ex2()\fR, \fBEVP_CipherInit_ex()\fR, \fBEVP_CipherUpdate()\fR and \fBEVP_CipherFinal_ex()\fR" 4
.IX Item "EVP_CipherInit_ex2(), EVP_CipherInit_ex(), EVP_CipherUpdate() and EVP_CipherFinal_ex()"
These functions can be used for decryption or encryption. The operation
performed depends on the value of the \fIenc\fR parameter. It should be set to 1
for encryption, 0 for decryption and \-1 to leave the value unchanged
(the actual value of 'enc' being supplied in a previous call).
.IP \fBEVP_CIPHER_CTX_reset()\fR 4
.IX Item "EVP_CIPHER_CTX_reset()"
Clears all information from a cipher context and free up any allocated memory
associated with it, except the \fIctx\fR itself. This function should be called
anytime \fIctx\fR is reused by another
\&\fBEVP_CipherInit()\fR / \fBEVP_CipherUpdate()\fR / \fBEVP_CipherFinal()\fR series of calls.
.IP "\fBEVP_EncryptInit()\fR, \fBEVP_DecryptInit()\fR and \fBEVP_CipherInit()\fR" 4
.IX Item "EVP_EncryptInit(), EVP_DecryptInit() and EVP_CipherInit()"
Behave in a similar way to \fBEVP_EncryptInit_ex()\fR, \fBEVP_DecryptInit_ex()\fR and
\&\fBEVP_CipherInit_ex()\fR except if the \fItype\fR is not a fetched cipher they use the
default implementation of the \fItype\fR.
.IP "\fBEVP_EncryptFinal()\fR, \fBEVP_DecryptFinal()\fR and \fBEVP_CipherFinal()\fR" 4
.IX Item "EVP_EncryptFinal(), EVP_DecryptFinal() and EVP_CipherFinal()"
Identical to \fBEVP_EncryptFinal_ex()\fR, \fBEVP_DecryptFinal_ex()\fR and
\&\fBEVP_CipherFinal_ex()\fR. In previous releases they also cleaned up
the \fIctx\fR, but this is no longer done and \fBEVP_CIPHER_CTX_cleanup()\fR
must be called to free any context resources.
.IP \fBEVP_Cipher()\fR 4
.IX Item "EVP_Cipher()"
Encrypts or decrypts a maximum \fIinl\fR amount of bytes from \fIin\fR and leaves the
result in \fIout\fR.
.Sp
For legacy ciphers \- If the cipher doesn't have the flag
\&\fBEVP_CIPH_FLAG_CUSTOM_CIPHER\fR set, then \fIinl\fR must be a multiple of
\&\fBEVP_CIPHER_get_block_size()\fR.  If it isn't, the result is undefined.  If the cipher
has that flag set, then \fIinl\fR can be any size.
.Sp
Due to the constraints of the API contract of this function it shouldn't be used
in applications, please consider using \fBEVP_CipherUpdate()\fR and
\&\fBEVP_CipherFinal_ex()\fR instead.
.IP "\fBEVP_get_cipherbyname()\fR, \fBEVP_get_cipherbynid()\fR and \fBEVP_get_cipherbyobj()\fR" 4
.IX Item "EVP_get_cipherbyname(), EVP_get_cipherbynid() and EVP_get_cipherbyobj()"
Returns an \fBEVP_CIPHER\fR structure when passed a cipher name, a cipher \fBNID\fR or
an \fBASN1_OBJECT\fR structure respectively.
.Sp
\&\fBEVP_get_cipherbyname()\fR will return NULL for algorithms such as "AES\-128\-SIV",
"AES\-128\-CBC\-CTS" and "CAMELLIA\-128\-CBC\-CTS" which were previously only
accessible via low level interfaces.
.Sp
The \fBEVP_get_cipherbyname()\fR function is present for backwards compatibility with
OpenSSL prior to version 3 and is different to the \fBEVP_CIPHER_fetch()\fR function
since it does not attempt to "fetch" an implementation of the cipher.
Additionally, it only knows about ciphers that are built-in to OpenSSL and have
an associated NID. Similarly \fBEVP_get_cipherbynid()\fR and \fBEVP_get_cipherbyobj()\fR
also return objects without an associated implementation.
.Sp
When the cipher objects returned by these functions are used (such as in a call
to \fBEVP_EncryptInit_ex()\fR) an implementation of the cipher will be implicitly
fetched from the loaded providers. This fetch could fail if no suitable
implementation is available. Use \fBEVP_CIPHER_fetch()\fR instead to explicitly fetch
the algorithm and an associated implementation from a provider.
.Sp
See "ALGORITHM FETCHING" in \fBcrypto\fR\|(7) for more information about fetching.
.Sp
The cipher objects returned from these functions do not need to be freed with
\&\fBEVP_CIPHER_free()\fR.
.IP "\fBEVP_CIPHER_get_nid()\fR and \fBEVP_CIPHER_CTX_get_nid()\fR" 4
.IX Item "EVP_CIPHER_get_nid() and EVP_CIPHER_CTX_get_nid()"
Return the NID of a cipher when passed an \fBEVP_CIPHER\fR or \fBEVP_CIPHER_CTX\fR
structure.  The actual NID value is an internal value which may not have a
corresponding OBJECT IDENTIFIER.
.IP "\fBEVP_CIPHER_CTX_set_flags()\fR, \fBEVP_CIPHER_CTX_clear_flags()\fR and \fBEVP_CIPHER_CTX_test_flags()\fR" 4
.IX Item "EVP_CIPHER_CTX_set_flags(), EVP_CIPHER_CTX_clear_flags() and EVP_CIPHER_CTX_test_flags()"
Sets, clears and tests \fIctx\fR flags.  See "FLAGS" below for more information.
.Sp
For provided ciphers \fBEVP_CIPHER_CTX_set_flags()\fR should be called only after the
fetched cipher has been assigned to the \fIctx\fR. It is recommended to use
"PARAMETERS" instead.
.IP \fBEVP_CIPHER_CTX_set_padding()\fR 4
.IX Item "EVP_CIPHER_CTX_set_padding()"
Enables or disables padding. This function should be called after the context
is set up for encryption or decryption with \fBEVP_EncryptInit_ex2()\fR,
\&\fBEVP_DecryptInit_ex2()\fR or \fBEVP_CipherInit_ex2()\fR. By default encryption operations
are padded using standard block padding and the padding is checked and removed
when decrypting. If the \fIpad\fR parameter is zero then no padding is
performed, the total amount of data encrypted or decrypted must then
be a multiple of the block size or an error will occur.
.IP "\fBEVP_CIPHER_get_key_length()\fR and \fBEVP_CIPHER_CTX_get_key_length()\fR" 4
.IX Item "EVP_CIPHER_get_key_length() and EVP_CIPHER_CTX_get_key_length()"
Return the key length of a cipher when passed an \fBEVP_CIPHER\fR or
\&\fBEVP_CIPHER_CTX\fR structure. The constant \fBEVP_MAX_KEY_LENGTH\fR is the maximum
key length for all ciphers. Note: although \fBEVP_CIPHER_get_key_length()\fR is fixed for
a given cipher, the value of \fBEVP_CIPHER_CTX_get_key_length()\fR may be different for
variable key length ciphers.
.IP \fBEVP_CIPHER_CTX_set_key_length()\fR 4
.IX Item "EVP_CIPHER_CTX_set_key_length()"
Sets the key length of the cipher context.
If the cipher is a fixed length cipher then attempting to set the key
length to any value other than the fixed value is an error.
.IP "\fBEVP_CIPHER_get_iv_length()\fR and \fBEVP_CIPHER_CTX_get_iv_length()\fR" 4
.IX Item "EVP_CIPHER_get_iv_length() and EVP_CIPHER_CTX_get_iv_length()"
Return the IV length of a cipher when passed an \fBEVP_CIPHER\fR or
\&\fBEVP_CIPHER_CTX\fR. It will return zero if the cipher does not use an IV.
The constant \fBEVP_MAX_IV_LENGTH\fR is the maximum IV length for all ciphers.
.IP \fBEVP_CIPHER_CTX_get_tag_length()\fR 4
.IX Item "EVP_CIPHER_CTX_get_tag_length()"
Returns the tag length of an AEAD cipher when passed a \fBEVP_CIPHER_CTX\fR. It will
return zero if the cipher does not support a tag. It returns a default value if
the tag length has not been set.
.IP "\fBEVP_CIPHER_get_block_size()\fR and \fBEVP_CIPHER_CTX_get_block_size()\fR" 4
.IX Item "EVP_CIPHER_get_block_size() and EVP_CIPHER_CTX_get_block_size()"
Return the block size of a cipher when passed an \fBEVP_CIPHER\fR or
\&\fBEVP_CIPHER_CTX\fR structure. The constant \fBEVP_MAX_BLOCK_LENGTH\fR is also the
maximum block length for all ciphers.
.IP "\fBEVP_CIPHER_get_type()\fR and \fBEVP_CIPHER_CTX_get_type()\fR" 4
.IX Item "EVP_CIPHER_get_type() and EVP_CIPHER_CTX_get_type()"
Return the type of the passed cipher or context. This "type" is the actual NID
of the cipher OBJECT IDENTIFIER and as such it ignores the cipher parameters
(40 bit RC2 and 128 bit RC2 have the same NID). If the cipher does not have an
object identifier or does not have ASN1 support this function will return
\&\fBNID_undef\fR.
.IP \fBEVP_CIPHER_is_a()\fR 4
.IX Item "EVP_CIPHER_is_a()"
Returns 1 if \fIcipher\fR is an implementation of an algorithm that's identifiable
with \fIname\fR, otherwise 0. If \fIcipher\fR is a legacy cipher (it's the return
value from the likes of \fBEVP_aes128()\fR rather than the result of an
\&\fBEVP_CIPHER_fetch()\fR), only cipher names registered with the default library
context (see \fBOSSL_LIB_CTX\fR\|(3)) will be considered.
.IP "\fBEVP_CIPHER_get0_name()\fR and \fBEVP_CIPHER_CTX_get0_name()\fR" 4
.IX Item "EVP_CIPHER_get0_name() and EVP_CIPHER_CTX_get0_name()"
Return the name of the passed cipher or context.  For fetched ciphers with
multiple names, only one of them is returned. See also \fBEVP_CIPHER_names_do_all()\fR.
.IP \fBEVP_CIPHER_names_do_all()\fR 4
.IX Item "EVP_CIPHER_names_do_all()"
Traverses all names for the \fIcipher\fR, and calls \fIfn\fR with each name and
\&\fIdata\fR.  This is only useful with fetched \fBEVP_CIPHER\fRs.
.IP \fBEVP_CIPHER_get0_description()\fR 4
.IX Item "EVP_CIPHER_get0_description()"
Returns a description of the cipher, meant for display and human consumption.
The description is at the discretion of the cipher implementation.
.IP \fBEVP_CIPHER_get0_provider()\fR 4
.IX Item "EVP_CIPHER_get0_provider()"
Returns an \fBOSSL_PROVIDER\fR pointer to the provider that implements the given
\&\fBEVP_CIPHER\fR.
.IP \fBEVP_CIPHER_CTX_get0_cipher()\fR 4
.IX Item "EVP_CIPHER_CTX_get0_cipher()"
Returns the \fBEVP_CIPHER\fR structure when passed an \fBEVP_CIPHER_CTX\fR structure.
\&\fBEVP_CIPHER_CTX_get1_cipher()\fR is the same except the ownership is passed to
the caller.
.IP "\fBEVP_CIPHER_get_mode()\fR and \fBEVP_CIPHER_CTX_get_mode()\fR" 4
.IX Item "EVP_CIPHER_get_mode() and EVP_CIPHER_CTX_get_mode()"
Return the block cipher mode:
EVP_CIPH_ECB_MODE, EVP_CIPH_CBC_MODE, EVP_CIPH_CFB_MODE, EVP_CIPH_OFB_MODE,
EVP_CIPH_CTR_MODE, EVP_CIPH_GCM_MODE, EVP_CIPH_CCM_MODE, EVP_CIPH_XTS_MODE,
EVP_CIPH_WRAP_MODE, EVP_CIPH_OCB_MODE or EVP_CIPH_SIV_MODE.
If the cipher is a stream cipher then EVP_CIPH_STREAM_CIPHER is returned.
.IP \fBEVP_CIPHER_get_flags()\fR 4
.IX Item "EVP_CIPHER_get_flags()"
Returns any flags associated with the cipher. See "FLAGS"
for a list of currently defined flags.
.IP "\fBEVP_CIPHER_CTX_get_num()\fR and \fBEVP_CIPHER_CTX_set_num()\fR" 4
.IX Item "EVP_CIPHER_CTX_get_num() and EVP_CIPHER_CTX_set_num()"
Gets or sets the cipher specific "num" parameter for the associated \fIctx\fR.
Built-in ciphers typically use this to track how much of the current underlying block
has been "used" already.
.IP \fBEVP_CIPHER_CTX_is_encrypting()\fR 4
.IX Item "EVP_CIPHER_CTX_is_encrypting()"
Reports whether the \fIctx\fR is being used for encryption or decryption.
.IP \fBEVP_CIPHER_CTX_flags()\fR 4
.IX Item "EVP_CIPHER_CTX_flags()"
A deprecated macro calling \f(CW\*(C`EVP_CIPHER_get_flags(EVP_CIPHER_CTX_get0_cipher(ctx))\*(C'\fR.
Do not use.
.IP \fBEVP_CIPHER_param_to_asn1()\fR 4
.IX Item "EVP_CIPHER_param_to_asn1()"
Sets the AlgorithmIdentifier "parameter" based on the passed cipher. This will
typically include any parameters and an IV. The cipher IV (if any) must be set
when this call is made. This call should be made before the cipher is actually
"used" (before any \fBEVP_EncryptUpdate()\fR, \fBEVP_DecryptUpdate()\fR calls for example).
This function may fail if the cipher does not have any ASN1 support.
.IP \fBEVP_CIPHER_asn1_to_param()\fR 4
.IX Item "EVP_CIPHER_asn1_to_param()"
Sets the cipher parameters based on an ASN1 AlgorithmIdentifier "parameter".
The precise effect depends on the cipher. In the case of \fBRC2\fR, for example,
it will set the IV and effective key length.
This function should be called after the base cipher type is set but before
the key is set. For example \fBEVP_CipherInit()\fR will be called with the IV and
key set to NULL, \fBEVP_CIPHER_asn1_to_param()\fR will be called and finally
\&\fBEVP_CipherInit()\fR again with all parameters except the key set to NULL. It is
possible for this function to fail if the cipher does not have any ASN1 support
or the parameters cannot be set (for example the RC2 effective key length
is not supported.
.IP \fBEVP_CIPHER_CTX_rand_key()\fR 4
.IX Item "EVP_CIPHER_CTX_rand_key()"
Generates a random key of the appropriate length based on the cipher context.
The \fBEVP_CIPHER\fR can provide its own random key generation routine to support
keys of a specific form. \fIkey\fR must point to a buffer at least as big as the
value returned by \fBEVP_CIPHER_CTX_get_key_length()\fR.
.IP \fBEVP_CIPHER_do_all_provided()\fR 4
.IX Item "EVP_CIPHER_do_all_provided()"
Traverses all ciphers implemented by all activated providers in the given
library context \fIlibctx\fR, and for each of the implementations, calls the given
function \fIfn\fR with the implementation method and the given \fIarg\fR as argument.
.SH PARAMETERS
.IX Header "PARAMETERS"
See \fBOSSL_PARAM\fR\|(3) for information about passing parameters.
.SS "Gettable EVP_CIPHER parameters"
.IX Subsection "Gettable EVP_CIPHER parameters"
When \fBEVP_CIPHER_fetch()\fR is called it internally calls \fBEVP_CIPHER_get_params()\fR
and caches the results.
.PP
\&\fBEVP_CIPHER_get_params()\fR can be used with the following \fBOSSL_PARAM\fR\|(3) keys:
.IP """mode"" (\fBOSSL_CIPHER_PARAM_MODE\fR) <unsigned integer>" 4
.IX Item """mode"" (OSSL_CIPHER_PARAM_MODE) <unsigned integer>"
Gets the mode for the associated cipher algorithm \fIcipher\fR.
See "\fBEVP_CIPHER_get_mode()\fR and \fBEVP_CIPHER_CTX_get_mode()\fR" for a list of valid modes.
Use \fBEVP_CIPHER_get_mode()\fR to retrieve the cached value.
.IP """keylen"" (\fBOSSL_CIPHER_PARAM_KEYLEN\fR) <unsigned integer>" 4
.IX Item """keylen"" (OSSL_CIPHER_PARAM_KEYLEN) <unsigned integer>"
Gets the key length for the associated cipher algorithm \fIcipher\fR.
Use \fBEVP_CIPHER_get_key_length()\fR to retrieve the cached value.
.IP """ivlen"" (\fBOSSL_CIPHER_PARAM_IVLEN\fR) <unsigned integer>" 4
.IX Item """ivlen"" (OSSL_CIPHER_PARAM_IVLEN) <unsigned integer>"
Gets the IV length for the associated cipher algorithm \fIcipher\fR.
Use \fBEVP_CIPHER_get_iv_length()\fR to retrieve the cached value.
.IP """blocksize"" (\fBOSSL_CIPHER_PARAM_BLOCK_SIZE\fR) <unsigned integer>" 4
.IX Item """blocksize"" (OSSL_CIPHER_PARAM_BLOCK_SIZE) <unsigned integer>"
Gets the block size for the associated cipher algorithm \fIcipher\fR.
The block size should be 1 for stream ciphers.
Note that the block size for a cipher may be different to the block size for
the underlying encryption/decryption primitive.
For example AES in CTR mode has a block size of 1 (because it operates like a
stream cipher), even though AES has a block size of 16.
Use \fBEVP_CIPHER_get_block_size()\fR to retrieve the cached value.
.IP """aead"" (\fBOSSL_CIPHER_PARAM_AEAD\fR) <integer>" 4
.IX Item """aead"" (OSSL_CIPHER_PARAM_AEAD) <integer>"
Gets 1 if this is an AEAD cipher algorithm, otherwise it gets 0.
Use (EVP_CIPHER_get_flags(cipher) & EVP_CIPH_FLAG_AEAD_CIPHER) to retrieve the
cached value.
.IP """custom-iv"" (\fBOSSL_CIPHER_PARAM_CUSTOM_IV\fR) <integer>" 4
.IX Item """custom-iv"" (OSSL_CIPHER_PARAM_CUSTOM_IV) <integer>"
Gets 1 if the cipher algorithm \fIcipher\fR has a custom IV, otherwise it gets 0.
Storing and initializing the IV is left entirely to the implementation, if a
custom IV is used.
Use (EVP_CIPHER_get_flags(cipher) & EVP_CIPH_CUSTOM_IV) to retrieve the
cached value.
.IP """cts"" (\fBOSSL_CIPHER_PARAM_CTS\fR) <integer>" 4
.IX Item """cts"" (OSSL_CIPHER_PARAM_CTS) <integer>"
Gets 1 if the cipher algorithm \fIcipher\fR uses ciphertext stealing,
otherwise it gets 0.
This is currently used to indicate that the cipher is a one shot that only
allows a single call to \fBEVP_CipherUpdate()\fR.
Use (EVP_CIPHER_get_flags(cipher) & EVP_CIPH_FLAG_CTS) to retrieve the
cached value.
.IP """tls-multi"" (\fBOSSL_CIPHER_PARAM_TLS1_MULTIBLOCK\fR) <integer>" 4
.IX Item """tls-multi"" (OSSL_CIPHER_PARAM_TLS1_MULTIBLOCK) <integer>"
Gets 1 if the cipher algorithm \fIcipher\fR supports interleaving of crypto blocks,
otherwise it gets 0. The interleaving is an optimization only applicable to certain
TLS ciphers.
Use (EVP_CIPHER_get_flags(cipher) & EVP_CIPH_FLAG_TLS1_1_MULTIBLOCK) to retrieve the
cached value.
.IP """has-randkey"" (\fBOSSL_CIPHER_PARAM_HAS_RANDKEY\fR) <integer>" 4
.IX Item """has-randkey"" (OSSL_CIPHER_PARAM_HAS_RANDKEY) <integer>"
Gets 1 if the cipher algorithm \fIcipher\fR supports the gettable EVP_CIPHER_CTX
parameter \fBOSSL_CIPHER_PARAM_RANDOM_KEY\fR. Only DES and 3DES set this to 1,
all other OpenSSL ciphers return 0.
.SS "Gettable and Settable EVP_CIPHER_CTX parameters"
.IX Subsection "Gettable and Settable EVP_CIPHER_CTX parameters"
The following \fBOSSL_PARAM\fR\|(3) keys can be used with both \fBEVP_CIPHER_CTX_get_params()\fR
and \fBEVP_CIPHER_CTX_set_params()\fR.
.IP """padding"" (\fBOSSL_CIPHER_PARAM_PADDING\fR) <unsigned integer>" 4
.IX Item """padding"" (OSSL_CIPHER_PARAM_PADDING) <unsigned integer>"
Gets or sets the padding mode for the cipher context \fIctx\fR.
Padding is enabled if the value is 1, and disabled if the value is 0.
See also \fBEVP_CIPHER_CTX_set_padding()\fR.
.IP """num"" (\fBOSSL_CIPHER_PARAM_NUM\fR) <unsigned integer>" 4
.IX Item """num"" (OSSL_CIPHER_PARAM_NUM) <unsigned integer>"
Gets or sets the cipher specific "num" parameter for the cipher context \fIctx\fR.
Built-in ciphers typically use this to track how much of the current underlying
block has been "used" already.
See also \fBEVP_CIPHER_CTX_get_num()\fR and \fBEVP_CIPHER_CTX_set_num()\fR.
.IP """keylen"" (\fBOSSL_CIPHER_PARAM_KEYLEN\fR) <unsigned integer>" 4
.IX Item """keylen"" (OSSL_CIPHER_PARAM_KEYLEN) <unsigned integer>"
Gets or sets the key length for the cipher context \fIctx\fR.
The length of the "keylen" parameter should not exceed that of a \fBsize_t\fR.
See also \fBEVP_CIPHER_CTX_get_key_length()\fR and \fBEVP_CIPHER_CTX_set_key_length()\fR.
.IP """tag"" (\fBOSSL_CIPHER_PARAM_AEAD_TAG\fR) <octet string>" 4
.IX Item """tag"" (OSSL_CIPHER_PARAM_AEAD_TAG) <octet string>"
Gets or sets the AEAD tag for the associated cipher context \fIctx\fR.
See "AEAD Interface" in \fBEVP_EncryptInit\fR\|(3).
.IP """keybits"" (\fBOSSL_CIPHER_PARAM_RC2_KEYBITS\fR) <unsigned integer>" 4
.IX Item """keybits"" (OSSL_CIPHER_PARAM_RC2_KEYBITS) <unsigned integer>"
Gets or sets the effective keybits used for a RC2 cipher.
The length of the "keybits" parameter should not exceed that of a \fBsize_t\fR.
.IP """rounds"" (\fBOSSL_CIPHER_PARAM_ROUNDS\fR) <unsigned integer>" 4
.IX Item """rounds"" (OSSL_CIPHER_PARAM_ROUNDS) <unsigned integer>"
Gets or sets the number of rounds to be used for a cipher.
This is used by the RC5 cipher.
.IP """alg_id_param"" (\fBOSSL_CIPHER_PARAM_ALGORITHM_ID_PARAMS\fR) <octet string>" 4
.IX Item """alg_id_param"" (OSSL_CIPHER_PARAM_ALGORITHM_ID_PARAMS) <octet string>"
Used to pass the DER encoded AlgorithmIdentifier parameter to or from
the cipher implementation.  Functions like \fBEVP_CIPHER_param_to_asn1\fR\|(3)
and \fBEVP_CIPHER_asn1_to_param\fR\|(3) use this parameter for any implementation
that has the flag \fBEVP_CIPH_FLAG_CUSTOM_ASN1\fR set.
.IP """cts_mode"" (\fBOSSL_CIPHER_PARAM_CTS_MODE\fR) <UTF8 string>" 4
.IX Item """cts_mode"" (OSSL_CIPHER_PARAM_CTS_MODE) <UTF8 string>"
Gets or sets the cipher text stealing mode. For all modes the output size is the
same as the input size. The input length must be greater than or equal to the
block size. (The block size for AES and CAMELLIA is 16 bytes).
.Sp
Valid values for the mode are:
.RS 4
.IP """CS1""" 4
.IX Item """CS1"""
The NIST variant of cipher text stealing.
For input lengths that are multiples of the block size it is equivalent to
using a "AES-XXX-CBC" or "CAMELLIA-XXX-CBC" cipher otherwise the second last
cipher text block is a partial block.
.IP """CS2""" 4
.IX Item """CS2"""
For input lengths that are multiples of the block size it is equivalent to
using a "AES-XXX-CBC" or "CAMELLIA-XXX-CBC" cipher, otherwise it is the same as
"CS3" mode.
.IP """CS3""" 4
.IX Item """CS3"""
The Kerberos5 variant of cipher text stealing which always swaps the last
cipher text block with the previous block (which may be a partial or full block
depending on the input length). If the input length is exactly one full block
then this is equivalent to using a "AES-XXX-CBC" or "CAMELLIA-XXX-CBC" cipher.
.RE
.RS 4
.Sp
The default is "CS1".
This is only supported for "AES\-128\-CBC\-CTS", "AES\-192\-CBC\-CTS", "AES\-256\-CBC\-CTS",
"CAMELLIA\-128\-CBC\-CTS", "CAMELLIA\-192\-CBC\-CTS" and "CAMELLIA\-256\-CBC\-CTS".
.RE
.IP """tls1multi_interleave"" (\fBOSSL_CIPHER_PARAM_TLS1_MULTIBLOCK_INTERLEAVE\fR) <unsigned integer>" 4
.IX Item """tls1multi_interleave"" (OSSL_CIPHER_PARAM_TLS1_MULTIBLOCK_INTERLEAVE) <unsigned integer>"
Sets or gets the number of records being sent in one go for a tls1 multiblock
cipher operation (either 4 or 8 records).
.SS "Gettable EVP_CIPHER_CTX parameters"
.IX Subsection "Gettable EVP_CIPHER_CTX parameters"
The following \fBOSSL_PARAM\fR\|(3) keys can be used with \fBEVP_CIPHER_CTX_get_params()\fR:
.IP """ivlen"" (\fBOSSL_CIPHER_PARAM_IVLEN\fR and <\fBOSSL_CIPHER_PARAM_AEAD_IVLEN\fR) <unsigned integer>" 4
.IX Item """ivlen"" (OSSL_CIPHER_PARAM_IVLEN and <OSSL_CIPHER_PARAM_AEAD_IVLEN) <unsigned integer>"
Gets the IV length for the cipher context \fIctx\fR.
The length of the "ivlen" parameter should not exceed that of a \fBsize_t\fR.
See also \fBEVP_CIPHER_CTX_get_iv_length()\fR.
.IP """iv"" (\fBOSSL_CIPHER_PARAM_IV\fR) <octet string OR octet ptr>" 4
.IX Item """iv"" (OSSL_CIPHER_PARAM_IV) <octet string OR octet ptr>"
Gets the IV used to initialize the associated cipher context \fIctx\fR.
See also \fBEVP_CIPHER_CTX_get_original_iv()\fR.
.IP """updated-iv"" (\fBOSSL_CIPHER_PARAM_UPDATED_IV\fR) <octet string OR octet ptr>" 4
.IX Item """updated-iv"" (OSSL_CIPHER_PARAM_UPDATED_IV) <octet string OR octet ptr>"
Gets the updated pseudo-IV state for the associated cipher context, e.g.,
the previous ciphertext block for CBC mode or the iteratively encrypted IV
value for OFB mode.  Note that octet pointer access is deprecated and is
provided only for backwards compatibility with historical libcrypto APIs.
See also \fBEVP_CIPHER_CTX_get_updated_iv()\fR.
.IP """randkey"" (\fBOSSL_CIPHER_PARAM_RANDOM_KEY\fR) <octet string>" 4
.IX Item """randkey"" (OSSL_CIPHER_PARAM_RANDOM_KEY) <octet string>"
Gets an implementation specific randomly generated key for the associated
cipher context \fIctx\fR. This is currently only supported by DES and 3DES (which set
the key to odd parity).
.IP """taglen"" (\fBOSSL_CIPHER_PARAM_AEAD_TAGLEN\fR) <unsigned integer>" 4
.IX Item """taglen"" (OSSL_CIPHER_PARAM_AEAD_TAGLEN) <unsigned integer>"
Gets the tag length to be used for an AEAD cipher for the associated cipher
context \fIctx\fR. It gets a default value if it has not been set.
The length of the "taglen" parameter should not exceed that of a \fBsize_t\fR.
See also \fBEVP_CIPHER_CTX_get_tag_length()\fR.
.IP """tlsaadpad"" (\fBOSSL_CIPHER_PARAM_AEAD_TLS1_AAD_PAD\fR) <unsigned integer>" 4
.IX Item """tlsaadpad"" (OSSL_CIPHER_PARAM_AEAD_TLS1_AAD_PAD) <unsigned integer>"
Gets the length of the tag that will be added to a TLS record for the AEAD
tag for the associated cipher context \fIctx\fR.
The length of the "tlsaadpad" parameter should not exceed that of a \fBsize_t\fR.
.IP """tlsivgen"" (\fBOSSL_CIPHER_PARAM_AEAD_TLS1_GET_IV_GEN\fR) <octet string>" 4
.IX Item """tlsivgen"" (OSSL_CIPHER_PARAM_AEAD_TLS1_GET_IV_GEN) <octet string>"
Gets the invocation field generated for encryption.
Can only be called after "tlsivfixed" is set.
This is only used for GCM mode.
.IP """tls1multi_enclen"" (\fBOSSL_CIPHER_PARAM_TLS1_MULTIBLOCK_ENC_LEN\fR) <unsigned integer>" 4
.IX Item """tls1multi_enclen"" (OSSL_CIPHER_PARAM_TLS1_MULTIBLOCK_ENC_LEN) <unsigned integer>"
Get the total length of the record returned from the "tls1multi_enc" operation.
.IP """tls1multi_maxbufsz"" (\fBOSSL_CIPHER_PARAM_TLS1_MULTIBLOCK_MAX_BUFSIZE\fR) <unsigned integer>" 4
.IX Item """tls1multi_maxbufsz"" (OSSL_CIPHER_PARAM_TLS1_MULTIBLOCK_MAX_BUFSIZE) <unsigned integer>"
Gets the maximum record length for a TLS1 multiblock cipher operation.
The length of the "tls1multi_maxbufsz" parameter should not exceed that of a \fBsize_t\fR.
.IP """tls1multi_aadpacklen"" (\fBOSSL_CIPHER_PARAM_TLS1_MULTIBLOCK_AAD_PACKLEN\fR) <unsigned integer>" 4
.IX Item """tls1multi_aadpacklen"" (OSSL_CIPHER_PARAM_TLS1_MULTIBLOCK_AAD_PACKLEN) <unsigned integer>"
Gets the result of running the "tls1multi_aad" operation.
.IP """tls-mac"" (\fBOSSL_CIPHER_PARAM_TLS_MAC\fR) <octet ptr>" 4
.IX Item """tls-mac"" (OSSL_CIPHER_PARAM_TLS_MAC) <octet ptr>"
Used to pass the TLS MAC data.
.SS "Settable EVP_CIPHER_CTX parameters"
.IX Subsection "Settable EVP_CIPHER_CTX parameters"
The following \fBOSSL_PARAM\fR\|(3) keys can be used with \fBEVP_CIPHER_CTX_set_params()\fR:
.IP """mackey"" (\fBOSSL_CIPHER_PARAM_AEAD_MAC_KEY\fR) <octet string>" 4
.IX Item """mackey"" (OSSL_CIPHER_PARAM_AEAD_MAC_KEY) <octet string>"
Sets the MAC key used by composite AEAD ciphers such as AES\-CBC\-HMAC\-SHA256.
.IP """speed"" (\fBOSSL_CIPHER_PARAM_SPEED\fR) <unsigned integer>" 4
.IX Item """speed"" (OSSL_CIPHER_PARAM_SPEED) <unsigned integer>"
Sets the speed option for the associated cipher context. This is only supported
by AES SIV ciphers which disallow multiple operations by default.
Setting "speed" to 1 allows another encrypt or decrypt operation to be
performed. This is used for performance testing.
.IP """use-bits"" (\fBOSSL_CIPHER_PARAM_USE_BITS\fR) <unsigned integer>" 4
.IX Item """use-bits"" (OSSL_CIPHER_PARAM_USE_BITS) <unsigned integer>"
Determines if the input length \fIinl\fR passed to \fBEVP_EncryptUpdate()\fR,
\&\fBEVP_DecryptUpdate()\fR and \fBEVP_CipherUpdate()\fR is the number of bits or number of bytes.
Setting "use-bits" to 1 uses bits. The default is in bytes.
This is only used for \fBCFB1\fR ciphers.
.Sp
This can be set using EVP_CIPHER_CTX_set_flags(ctx, EVP_CIPH_FLAG_LENGTH_BITS).
.IP """tls-version"" (\fBOSSL_CIPHER_PARAM_TLS_VERSION\fR) <integer>" 4
.IX Item """tls-version"" (OSSL_CIPHER_PARAM_TLS_VERSION) <integer>"
Sets the TLS version.
.IP """tls-mac-size"" (\fBOSSL_CIPHER_PARAM_TLS_MAC_SIZE\fR) <unsigned integer>" 4
.IX Item """tls-mac-size"" (OSSL_CIPHER_PARAM_TLS_MAC_SIZE) <unsigned integer>"
Set the TLS MAC size.
.IP """tlsaad"" (\fBOSSL_CIPHER_PARAM_AEAD_TLS1_AAD\fR) <octet string>" 4
.IX Item """tlsaad"" (OSSL_CIPHER_PARAM_AEAD_TLS1_AAD) <octet string>"
Sets TLSv1.2 AAD information for the associated cipher context \fIctx\fR.
TLSv1.2 AAD information is always 13 bytes in length and is as defined for the
"additional_data" field described in section ******* of RFC5246.
.IP """tlsivfixed"" (\fBOSSL_CIPHER_PARAM_AEAD_TLS1_IV_FIXED\fR) <octet string>" 4
.IX Item """tlsivfixed"" (OSSL_CIPHER_PARAM_AEAD_TLS1_IV_FIXED) <octet string>"
Sets the fixed portion of an IV for an AEAD cipher used in a TLS record
encryption/ decryption for the associated cipher context.
TLS record encryption/decryption always occurs "in place" so that the input and
output buffers are always the same memory location.
AEAD IVs in TLSv1.2 consist of an implicit "fixed" part and an explicit part
that varies with every record.
Setting a TLS fixed IV changes a cipher to encrypt/decrypt TLS records.
TLS records are encrypted/decrypted using a single OSSL_FUNC_cipher_cipher call per
record.
For a record decryption the first bytes of the input buffer will be the explicit
part of the IV and the final bytes of the input buffer will be the AEAD tag.
The length of the explicit part of the IV and the tag length will depend on the
cipher in use and will be defined in the RFC for the relevant ciphersuite.
In order to allow for "in place" decryption the plaintext output should be
written to the same location in the output buffer that the ciphertext payload
was read from, i.e. immediately after the explicit IV.
.Sp
When encrypting a record the first bytes of the input buffer should be empty to
allow space for the explicit IV, as will the final bytes where the tag will
be written.
The length of the input buffer will include the length of the explicit IV, the
payload, and the tag bytes.
The cipher implementation should generate the explicit IV and write it to the
beginning of the output buffer, do "in place" encryption of the payload and
write that to the output buffer, and finally add the tag onto the end of the
output buffer.
.Sp
Whether encrypting or decrypting the value written to \fI*outl\fR in the
OSSL_FUNC_cipher_cipher call should be the length of the payload excluding the explicit
IV length and the tag length.
.IP """tlsivinv"" (\fBOSSL_CIPHER_PARAM_AEAD_TLS1_SET_IV_INV\fR) <octet string>" 4
.IX Item """tlsivinv"" (OSSL_CIPHER_PARAM_AEAD_TLS1_SET_IV_INV) <octet string>"
Sets the invocation field used for decryption.
Can only be called after "tlsivfixed" is set.
This is only used for GCM mode.
.IP """tls1multi_enc"" (\fBOSSL_CIPHER_PARAM_TLS1_MULTIBLOCK_ENC\fR) <octet string>" 4
.IX Item """tls1multi_enc"" (OSSL_CIPHER_PARAM_TLS1_MULTIBLOCK_ENC) <octet string>"
Triggers a multiblock TLS1 encrypt operation for a TLS1 aware cipher that
supports sending 4 or 8 records in one go.
The cipher performs both the MAC and encrypt stages and constructs the record
headers itself.
"tls1multi_enc" supplies the output buffer for the encrypt operation,
"tls1multi_encin" & "tls1multi_interleave" must also be set in order to supply
values to the encrypt operation.
.IP """tls1multi_encin"" (\fBOSSL_CIPHER_PARAM_TLS1_MULTIBLOCK_ENC_IN\fR) <octet string>" 4
.IX Item """tls1multi_encin"" (OSSL_CIPHER_PARAM_TLS1_MULTIBLOCK_ENC_IN) <octet string>"
Supplies the data to encrypt for a TLS1 multiblock cipher operation.
.IP """tls1multi_maxsndfrag"" (\fBOSSL_CIPHER_PARAM_TLS1_MULTIBLOCK_MAX_SEND_FRAGMENT\fR) <unsigned integer>" 4
.IX Item """tls1multi_maxsndfrag"" (OSSL_CIPHER_PARAM_TLS1_MULTIBLOCK_MAX_SEND_FRAGMENT) <unsigned integer>"
Sets the maximum send fragment size for a TLS1 multiblock cipher operation.
It must be set before using "tls1multi_maxbufsz".
The length of the "tls1multi_maxsndfrag" parameter should not exceed that of a \fBsize_t\fR.
.IP """tls1multi_aad"" (\fBOSSL_CIPHER_PARAM_TLS1_MULTIBLOCK_AAD\fR) <octet string>" 4
.IX Item """tls1multi_aad"" (OSSL_CIPHER_PARAM_TLS1_MULTIBLOCK_AAD) <octet string>"
Sets the authenticated additional data used by a TLS1 multiblock cipher operation.
The supplied data consists of 13 bytes of record data containing:
Bytes 0\-7: The sequence number of the first record
Byte 8: The record type
Byte 9\-10: The protocol version
Byte 11\-12: Input length (Always 0)
.Sp
"tls1multi_interleave" must also be set for this operation.
.SH CONTROLS
.IX Header "CONTROLS"
The Mappings from \fBEVP_CIPHER_CTX_ctrl()\fR identifiers to PARAMETERS are listed
in the following section. See the "PARAMETERS" section for more details.
.PP
\&\fBEVP_CIPHER_CTX_ctrl()\fR can be used to send the following standard controls:
.IP "EVP_CTRL_AEAD_SET_IVLEN and EVP_CTRL_GET_IVLEN" 4
.IX Item "EVP_CTRL_AEAD_SET_IVLEN and EVP_CTRL_GET_IVLEN"
When used with a fetched \fBEVP_CIPHER\fR, \fBEVP_CIPHER_CTX_set_params()\fR and
\&\fBEVP_CIPHER_CTX_get_params()\fR get called with an \fBOSSL_PARAM\fR\|(3) item with the
key "ivlen" (\fBOSSL_CIPHER_PARAM_IVLEN\fR).
.IP EVP_CTRL_AEAD_SET_IV_FIXED 4
.IX Item "EVP_CTRL_AEAD_SET_IV_FIXED"
When used with a fetched \fBEVP_CIPHER\fR, \fBEVP_CIPHER_CTX_set_params()\fR gets called
with an \fBOSSL_PARAM\fR\|(3) item with the key "tlsivfixed"
(\fBOSSL_CIPHER_PARAM_AEAD_TLS1_IV_FIXED\fR).
.IP EVP_CTRL_AEAD_SET_MAC_KEY 4
.IX Item "EVP_CTRL_AEAD_SET_MAC_KEY"
When used with a fetched \fBEVP_CIPHER\fR, \fBEVP_CIPHER_CTX_set_params()\fR gets called
with an \fBOSSL_PARAM\fR\|(3) item with the key "mackey"
(\fBOSSL_CIPHER_PARAM_AEAD_MAC_KEY\fR).
.IP "EVP_CTRL_AEAD_SET_TAG and EVP_CTRL_AEAD_GET_TAG" 4
.IX Item "EVP_CTRL_AEAD_SET_TAG and EVP_CTRL_AEAD_GET_TAG"
When used with a fetched \fBEVP_CIPHER\fR, \fBEVP_CIPHER_CTX_set_params()\fR and
\&\fBEVP_CIPHER_CTX_get_params()\fR get called with an \fBOSSL_PARAM\fR\|(3) item with the
key "tag" (\fBOSSL_CIPHER_PARAM_AEAD_TAG\fR).
.IP EVP_CTRL_CCM_SET_L 4
.IX Item "EVP_CTRL_CCM_SET_L"
When used with a fetched \fBEVP_CIPHER\fR, \fBEVP_CIPHER_CTX_set_params()\fR gets called
with an \fBOSSL_PARAM\fR\|(3) item with the key "ivlen" (\fBOSSL_CIPHER_PARAM_IVLEN\fR)
with a value of (15 \- L)
.IP EVP_CTRL_COPY 4
.IX Item "EVP_CTRL_COPY"
There is no OSSL_PARAM mapping for this. Use \fBEVP_CIPHER_CTX_copy()\fR instead.
.IP EVP_CTRL_GCM_SET_IV_INV 4
.IX Item "EVP_CTRL_GCM_SET_IV_INV"
When used with a fetched \fBEVP_CIPHER\fR, \fBEVP_CIPHER_CTX_set_params()\fR gets called
with an \fBOSSL_PARAM\fR\|(3) item with the key "tlsivinv"
(\fBOSSL_CIPHER_PARAM_AEAD_TLS1_SET_IV_INV\fR).
.IP EVP_CTRL_RAND_KEY 4
.IX Item "EVP_CTRL_RAND_KEY"
When used with a fetched \fBEVP_CIPHER\fR, \fBEVP_CIPHER_CTX_set_params()\fR gets called
with an \fBOSSL_PARAM\fR\|(3) item with the key "randkey"
(\fBOSSL_CIPHER_PARAM_RANDOM_KEY\fR).
.IP EVP_CTRL_SET_KEY_LENGTH 4
.IX Item "EVP_CTRL_SET_KEY_LENGTH"
When used with a fetched \fBEVP_CIPHER\fR, \fBEVP_CIPHER_CTX_set_params()\fR gets called
with an \fBOSSL_PARAM\fR\|(3) item with the key "keylen" (\fBOSSL_CIPHER_PARAM_KEYLEN\fR).
.IP "EVP_CTRL_SET_RC2_KEY_BITS and EVP_CTRL_GET_RC2_KEY_BITS" 4
.IX Item "EVP_CTRL_SET_RC2_KEY_BITS and EVP_CTRL_GET_RC2_KEY_BITS"
When used with a fetched \fBEVP_CIPHER\fR, \fBEVP_CIPHER_CTX_set_params()\fR and
\&\fBEVP_CIPHER_CTX_get_params()\fR get called with an \fBOSSL_PARAM\fR\|(3) item with the
key "keybits" (\fBOSSL_CIPHER_PARAM_RC2_KEYBITS\fR).
.IP "EVP_CTRL_SET_RC5_ROUNDS and EVP_CTRL_GET_RC5_ROUNDS" 4
.IX Item "EVP_CTRL_SET_RC5_ROUNDS and EVP_CTRL_GET_RC5_ROUNDS"
When used with a fetched \fBEVP_CIPHER\fR, \fBEVP_CIPHER_CTX_set_params()\fR and
\&\fBEVP_CIPHER_CTX_get_params()\fR get called with an \fBOSSL_PARAM\fR\|(3) item with the
key "rounds" (\fBOSSL_CIPHER_PARAM_ROUNDS\fR).
.IP EVP_CTRL_SET_SPEED 4
.IX Item "EVP_CTRL_SET_SPEED"
When used with a fetched \fBEVP_CIPHER\fR, \fBEVP_CIPHER_CTX_set_params()\fR gets called
with an \fBOSSL_PARAM\fR\|(3) item with the key "speed" (\fBOSSL_CIPHER_PARAM_SPEED\fR).
.IP EVP_CTRL_GCM_IV_GEN 4
.IX Item "EVP_CTRL_GCM_IV_GEN"
When used with a fetched \fBEVP_CIPHER\fR, \fBEVP_CIPHER_CTX_get_params()\fR gets called
with an \fBOSSL_PARAM\fR\|(3) item with the key
"tlsivgen" (\fBOSSL_CIPHER_PARAM_AEAD_TLS1_GET_IV_GEN\fR).
.IP EVP_CTRL_AEAD_TLS1_AAD 4
.IX Item "EVP_CTRL_AEAD_TLS1_AAD"
When used with a fetched \fBEVP_CIPHER\fR, \fBEVP_CIPHER_CTX_set_params()\fR get called
with an \fBOSSL_PARAM\fR\|(3) item with the key
"tlsaad" (\fBOSSL_CIPHER_PARAM_AEAD_TLS1_AAD\fR)
followed by \fBEVP_CIPHER_CTX_get_params()\fR with a key of
"tlsaadpad" (\fBOSSL_CIPHER_PARAM_AEAD_TLS1_AAD_PAD\fR).
.IP EVP_CTRL_TLS1_1_MULTIBLOCK_MAX_BUFSIZE 4
.IX Item "EVP_CTRL_TLS1_1_MULTIBLOCK_MAX_BUFSIZE"
When used with a fetched \fBEVP_CIPHER\fR,
\&\fBEVP_CIPHER_CTX_set_params()\fR gets called with an \fBOSSL_PARAM\fR\|(3) item with the
key OSSL_CIPHER_PARAM_TLS1_MULTIBLOCK_MAX_SEND_FRAGMENT
followed by \fBEVP_CIPHER_CTX_get_params()\fR with a key of
"tls1multi_maxbufsz" (\fBOSSL_CIPHER_PARAM_TLS1_MULTIBLOCK_MAX_BUFSIZE\fR).
.IP EVP_CTRL_TLS1_1_MULTIBLOCK_AAD 4
.IX Item "EVP_CTRL_TLS1_1_MULTIBLOCK_AAD"
When used with a fetched \fBEVP_CIPHER\fR, \fBEVP_CIPHER_CTX_set_params()\fR gets called
with \fBOSSL_PARAM\fR\|(3) items with the keys
"tls1multi_aad" (\fBOSSL_CIPHER_PARAM_TLS1_MULTIBLOCK_AAD\fR) and
"tls1multi_interleave" (\fBOSSL_CIPHER_PARAM_TLS1_MULTIBLOCK_INTERLEAVE\fR)
followed by \fBEVP_CIPHER_CTX_get_params()\fR with keys of
"tls1multi_aadpacklen" (\fBOSSL_CIPHER_PARAM_TLS1_MULTIBLOCK_AAD_PACKLEN\fR) and
"tls1multi_interleave" (\fBOSSL_CIPHER_PARAM_TLS1_MULTIBLOCK_INTERLEAVE\fR).
.IP EVP_CTRL_TLS1_1_MULTIBLOCK_ENCRYPT 4
.IX Item "EVP_CTRL_TLS1_1_MULTIBLOCK_ENCRYPT"
When used with a fetched \fBEVP_CIPHER\fR, \fBEVP_CIPHER_CTX_set_params()\fR gets called
with \fBOSSL_PARAM\fR\|(3) items with the keys
"tls1multi_enc" (\fBOSSL_CIPHER_PARAM_TLS1_MULTIBLOCK_ENC\fR),
"tls1multi_encin" (\fBOSSL_CIPHER_PARAM_TLS1_MULTIBLOCK_ENC_IN\fR) and
"tls1multi_interleave" (\fBOSSL_CIPHER_PARAM_TLS1_MULTIBLOCK_INTERLEAVE\fR),
followed by \fBEVP_CIPHER_CTX_get_params()\fR with a key of
"tls1multi_enclen" (\fBOSSL_CIPHER_PARAM_TLS1_MULTIBLOCK_ENC_LEN\fR).
.SH FLAGS
.IX Header "FLAGS"
\&\fBEVP_CIPHER_CTX_set_flags()\fR, \fBEVP_CIPHER_CTX_clear_flags()\fR and \fBEVP_CIPHER_CTX_test_flags()\fR.
can be used to manipulate and test these \fBEVP_CIPHER_CTX\fR flags:
.IP EVP_CIPH_NO_PADDING 4
.IX Item "EVP_CIPH_NO_PADDING"
Used by \fBEVP_CIPHER_CTX_set_padding()\fR.
.Sp
See also "Gettable and Settable EVP_CIPHER_CTX parameters" "padding"
.IP EVP_CIPH_FLAG_LENGTH_BITS 4
.IX Item "EVP_CIPH_FLAG_LENGTH_BITS"
See "Settable EVP_CIPHER_CTX parameters" "use-bits".
.IP EVP_CIPHER_CTX_FLAG_WRAP_ALLOW 4
.IX Item "EVP_CIPHER_CTX_FLAG_WRAP_ALLOW"
Used for Legacy purposes only. This flag needed to be set to indicate the
cipher handled wrapping.
.PP
\&\fBEVP_CIPHER_flags()\fR uses the following flags that
have mappings to "Gettable EVP_CIPHER parameters":
.IP EVP_CIPH_FLAG_AEAD_CIPHER 4
.IX Item "EVP_CIPH_FLAG_AEAD_CIPHER"
See "Gettable EVP_CIPHER parameters" "aead".
.IP EVP_CIPH_CUSTOM_IV 4
.IX Item "EVP_CIPH_CUSTOM_IV"
See "Gettable EVP_CIPHER parameters" "custom-iv".
.IP EVP_CIPH_FLAG_CTS 4
.IX Item "EVP_CIPH_FLAG_CTS"
See "Gettable EVP_CIPHER parameters" "cts".
.IP EVP_CIPH_FLAG_TLS1_1_MULTIBLOCK; 4
.IX Item "EVP_CIPH_FLAG_TLS1_1_MULTIBLOCK;"
See "Gettable EVP_CIPHER parameters" "tls-multi".
.IP EVP_CIPH_RAND_KEY 4
.IX Item "EVP_CIPH_RAND_KEY"
See "Gettable EVP_CIPHER parameters" "has-randkey".
.PP
\&\fBEVP_CIPHER_flags()\fR uses the following flags for legacy purposes only:
.IP EVP_CIPH_VARIABLE_LENGTH 4
.IX Item "EVP_CIPH_VARIABLE_LENGTH"
.PD 0
.IP EVP_CIPH_FLAG_CUSTOM_CIPHER 4
.IX Item "EVP_CIPH_FLAG_CUSTOM_CIPHER"
.IP EVP_CIPH_ALWAYS_CALL_INIT 4
.IX Item "EVP_CIPH_ALWAYS_CALL_INIT"
.IP EVP_CIPH_CTRL_INIT 4
.IX Item "EVP_CIPH_CTRL_INIT"
.IP EVP_CIPH_CUSTOM_KEY_LENGTH 4
.IX Item "EVP_CIPH_CUSTOM_KEY_LENGTH"
.IP EVP_CIPH_CUSTOM_COPY 4
.IX Item "EVP_CIPH_CUSTOM_COPY"
.IP EVP_CIPH_FLAG_DEFAULT_ASN1 4
.IX Item "EVP_CIPH_FLAG_DEFAULT_ASN1"
.PD
See \fBEVP_CIPHER_meth_set_flags\fR\|(3) for further information related to the above
flags.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBEVP_CIPHER_fetch()\fR returns a pointer to a \fBEVP_CIPHER\fR for success
and \fBNULL\fR for failure.
.PP
\&\fBEVP_CIPHER_up_ref()\fR returns 1 for success or 0 otherwise.
.PP
\&\fBEVP_CIPHER_CTX_new()\fR returns a pointer to a newly created
\&\fBEVP_CIPHER_CTX\fR for success and \fBNULL\fR for failure.
.PP
\&\fBEVP_EncryptInit_ex2()\fR, \fBEVP_EncryptUpdate()\fR and \fBEVP_EncryptFinal_ex()\fR
return 1 for success and 0 for failure.
.PP
\&\fBEVP_DecryptInit_ex2()\fR and \fBEVP_DecryptUpdate()\fR return 1 for success and 0 for failure.
\&\fBEVP_DecryptFinal_ex()\fR returns 0 if the decrypt failed or 1 for success.
.PP
\&\fBEVP_CipherInit_ex2()\fR and \fBEVP_CipherUpdate()\fR return 1 for success and 0 for failure.
\&\fBEVP_CipherFinal_ex()\fR returns 0 for a decryption failure or 1 for success.
.PP
\&\fBEVP_Cipher()\fR returns 1 on success or 0 on failure, if the flag
\&\fBEVP_CIPH_FLAG_CUSTOM_CIPHER\fR is not set for the cipher.
\&\fBEVP_Cipher()\fR returns the number of bytes written to \fIout\fR for encryption / decryption, or
the number of bytes authenticated in a call specifying AAD for an AEAD cipher, if the flag
\&\fBEVP_CIPH_FLAG_CUSTOM_CIPHER\fR is set for the cipher.
.PP
\&\fBEVP_CIPHER_CTX_reset()\fR returns 1 for success and 0 for failure.
.PP
\&\fBEVP_get_cipherbyname()\fR, \fBEVP_get_cipherbynid()\fR and \fBEVP_get_cipherbyobj()\fR
return an \fBEVP_CIPHER\fR structure or NULL on error.
.PP
\&\fBEVP_CIPHER_get_nid()\fR and \fBEVP_CIPHER_CTX_get_nid()\fR return a NID.
.PP
\&\fBEVP_CIPHER_get_block_size()\fR and \fBEVP_CIPHER_CTX_get_block_size()\fR return the
block size.
.PP
\&\fBEVP_CIPHER_get_key_length()\fR and \fBEVP_CIPHER_CTX_get_key_length()\fR return the key
length.
.PP
\&\fBEVP_CIPHER_CTX_set_padding()\fR always returns 1.
.PP
\&\fBEVP_CIPHER_get_iv_length()\fR and \fBEVP_CIPHER_CTX_get_iv_length()\fR return the IV
length or zero if the cipher does not use an IV.
.PP
\&\fBEVP_CIPHER_CTX_get_tag_length()\fR return the tag length or zero if the cipher
does not use a tag.
.PP
\&\fBEVP_CIPHER_get_type()\fR and \fBEVP_CIPHER_CTX_get_type()\fR return the NID of the
cipher's OBJECT IDENTIFIER or NID_undef if it has no defined
OBJECT IDENTIFIER.
.PP
\&\fBEVP_CIPHER_CTX_cipher()\fR returns an \fBEVP_CIPHER\fR structure.
.PP
\&\fBEVP_CIPHER_CTX_get_num()\fR returns a nonnegative num value or
\&\fBEVP_CTRL_RET_UNSUPPORTED\fR if the implementation does not support the call
or on any other error.
.PP
\&\fBEVP_CIPHER_CTX_set_num()\fR returns 1 on success and 0 if the implementation
does not support the call or on any other error.
.PP
\&\fBEVP_CIPHER_CTX_is_encrypting()\fR returns 1 if the \fIctx\fR is set up for encryption
0 otherwise.
.PP
\&\fBEVP_CIPHER_param_to_asn1()\fR and \fBEVP_CIPHER_asn1_to_param()\fR return greater
than zero for success and zero or a negative number on failure.
.PP
\&\fBEVP_CIPHER_CTX_rand_key()\fR returns 1 for success and zero or a negative number
for failure.
.PP
\&\fBEVP_CIPHER_names_do_all()\fR returns 1 if the callback was called for all names.
A return value of 0 means that the callback was not called for any names.
.SH "CIPHER LISTING"
.IX Header "CIPHER LISTING"
All algorithms have a fixed key length unless otherwise stated.
.PP
Refer to "SEE ALSO" for the full list of ciphers available through the EVP
interface.
.IP \fBEVP_enc_null()\fR 4
.IX Item "EVP_enc_null()"
Null cipher: does nothing.
.SH "AEAD INTERFACE"
.IX Header "AEAD INTERFACE"
The EVP interface for Authenticated Encryption with Associated Data (AEAD)
modes are subtly altered and several additional \fIctrl\fR operations are supported
depending on the mode specified.
.PP
To specify additional authenticated data (AAD), a call to \fBEVP_CipherUpdate()\fR,
\&\fBEVP_EncryptUpdate()\fR or \fBEVP_DecryptUpdate()\fR should be made with the output
parameter \fIout\fR set to \fBNULL\fR. In this case, on success, the parameter
\&\fIoutl\fR is set to the number of bytes authenticated.
.PP
When decrypting, the return value of \fBEVP_DecryptFinal()\fR or \fBEVP_CipherFinal()\fR
indicates whether the operation was successful. If it does not indicate success,
the authentication operation has failed and any output data \fBMUST NOT\fR be used
as it is corrupted.
.SS "GCM and OCB Modes"
.IX Subsection "GCM and OCB Modes"
The following \fIctrl\fRs are supported in GCM and OCB modes.
.IP "EVP_CIPHER_CTX_ctrl(ctx, EVP_CTRL_AEAD_SET_IVLEN, ivlen, NULL)" 4
.IX Item "EVP_CIPHER_CTX_ctrl(ctx, EVP_CTRL_AEAD_SET_IVLEN, ivlen, NULL)"
Sets the IV length. This call can only be made before specifying an IV. If
not called a default IV length is used.
.Sp
For GCM AES and OCB AES the default is 12 (i.e. 96 bits). For OCB mode the
maximum is 15.
.IP "EVP_CIPHER_CTX_ctrl(ctx, EVP_CTRL_AEAD_GET_TAG, taglen, tag)" 4
.IX Item "EVP_CIPHER_CTX_ctrl(ctx, EVP_CTRL_AEAD_GET_TAG, taglen, tag)"
Writes \f(CW\*(C`taglen\*(C'\fR bytes of the tag value to the buffer indicated by \f(CW\*(C`tag\*(C'\fR.
This call can only be made when encrypting data and \fBafter\fR all data has been
processed (e.g. after an \fBEVP_EncryptFinal()\fR call).
.Sp
For OCB, \f(CW\*(C`taglen\*(C'\fR must either be 16 or the value previously set via
\&\fBEVP_CTRL_AEAD_SET_TAG\fR.
.IP "EVP_CIPHER_CTX_ctrl(ctx, EVP_CTRL_AEAD_SET_TAG, taglen, tag)" 4
.IX Item "EVP_CIPHER_CTX_ctrl(ctx, EVP_CTRL_AEAD_SET_TAG, taglen, tag)"
When decrypting, this call sets the expected tag to \f(CW\*(C`taglen\*(C'\fR bytes from \f(CW\*(C`tag\*(C'\fR.
\&\f(CW\*(C`taglen\*(C'\fR must be between 1 and 16 inclusive.
The tag must be set prior to any call to \fBEVP_DecryptFinal()\fR or
\&\fBEVP_DecryptFinal_ex()\fR.
.Sp
For GCM, this call is only valid when decrypting data.
.Sp
For OCB, this call is valid when decrypting data to set the expected tag,
and when encrypting to set the desired tag length.
.Sp
In OCB mode, calling this when encrypting with \f(CW\*(C`tag\*(C'\fR set to \f(CW\*(C`NULL\*(C'\fR sets the
tag length. The tag length can only be set before specifying an IV. If this is
not called prior to setting the IV during encryption, then a default tag length
is used.
.Sp
For OCB AES, the default tag length is 16 (i.e. 128 bits).  It is also the
maximum tag length for OCB.
.SS "CCM Mode"
.IX Subsection "CCM Mode"
The EVP interface for CCM mode is similar to that of the GCM mode but with a
few additional requirements and different \fIctrl\fR values.
.PP
For CCM mode, the total plaintext or ciphertext length \fBMUST\fR be passed to
\&\fBEVP_CipherUpdate()\fR, \fBEVP_EncryptUpdate()\fR or \fBEVP_DecryptUpdate()\fR with the output
and input parameters (\fIin\fR and \fIout\fR) set to \fBNULL\fR and the length passed in
the \fIinl\fR parameter.
.PP
The following \fIctrl\fRs are supported in CCM mode.
.IP "EVP_CIPHER_CTX_ctrl(ctx, EVP_CTRL_AEAD_SET_TAG, taglen, tag)" 4
.IX Item "EVP_CIPHER_CTX_ctrl(ctx, EVP_CTRL_AEAD_SET_TAG, taglen, tag)"
This call is made to set the expected \fBCCM\fR tag value when decrypting or
the length of the tag (with the \f(CW\*(C`tag\*(C'\fR parameter set to NULL) when encrypting.
The tag length is often referred to as \fBM\fR. If not set a default value is
used (12 for AES). When decrypting, the tag needs to be set before passing
in data to be decrypted, but as in GCM and OCB mode, it can be set after
passing additional authenticated data (see "AEAD INTERFACE").
.IP "EVP_CIPHER_CTX_ctrl(ctx, EVP_CTRL_CCM_SET_L, ivlen, NULL)" 4
.IX Item "EVP_CIPHER_CTX_ctrl(ctx, EVP_CTRL_CCM_SET_L, ivlen, NULL)"
Sets the CCM \fBL\fR value. If not set a default is used (8 for AES).
.IP "EVP_CIPHER_CTX_ctrl(ctx, EVP_CTRL_AEAD_SET_IVLEN, ivlen, NULL)" 4
.IX Item "EVP_CIPHER_CTX_ctrl(ctx, EVP_CTRL_AEAD_SET_IVLEN, ivlen, NULL)"
Sets the CCM nonce (IV) length. This call can only be made before specifying a
nonce value. The nonce length is given by \fB15 \- L\fR so it is 7 by default for
AES.
.SS "SIV Mode"
.IX Subsection "SIV Mode"
For SIV mode ciphers the behaviour of the EVP interface is subtly
altered and several additional ctrl operations are supported.
.PP
To specify any additional authenticated data (AAD) and/or a Nonce, a call to
\&\fBEVP_CipherUpdate()\fR, \fBEVP_EncryptUpdate()\fR or \fBEVP_DecryptUpdate()\fR should be made
with the output parameter \fIout\fR set to \fBNULL\fR.
.PP
RFC5297 states that the Nonce is the last piece of AAD before the actual
encrypt/decrypt takes place. The API does not differentiate the Nonce from
other AAD.
.PP
When decrypting the return value of \fBEVP_DecryptFinal()\fR or \fBEVP_CipherFinal()\fR
indicates if the operation was successful. If it does not indicate success
the authentication operation has failed and any output data \fBMUST NOT\fR
be used as it is corrupted.
.PP
The API does not store the the SIV (Synthetic Initialization Vector) in
the cipher text. Instead, it is stored as the tag within the EVP_CIPHER_CTX.
The SIV must be retrieved from the context after encryption, and set into
the context before decryption.
.PP
This differs from RFC5297 in that the cipher output from encryption, and
the cipher input to decryption, does not contain the SIV. This also means
that the plain text and cipher text lengths are identical.
.PP
The following ctrls are supported in SIV mode, and are used to get and set
the Synthetic Initialization Vector:
.IP "EVP_CIPHER_CTX_ctrl(ctx, EVP_CTRL_AEAD_GET_TAG, taglen, tag);" 4
.IX Item "EVP_CIPHER_CTX_ctrl(ctx, EVP_CTRL_AEAD_GET_TAG, taglen, tag);"
Writes \fItaglen\fR bytes of the tag value (the Synthetic Initialization Vector)
to the buffer indicated by \fItag\fR. This call can only be made when encrypting
data and \fBafter\fR all data has been processed (e.g. after an \fBEVP_EncryptFinal()\fR
call). For SIV mode the taglen must be 16.
.IP "EVP_CIPHER_CTX_ctrl(ctx, EVP_CTRL_AEAD_SET_TAG, taglen, tag);" 4
.IX Item "EVP_CIPHER_CTX_ctrl(ctx, EVP_CTRL_AEAD_SET_TAG, taglen, tag);"
Sets the expected tag (the Synthetic Initialization Vector) to \fItaglen\fR
bytes from \fItag\fR. This call is only legal when decrypting data and must be
made \fBbefore\fR any data is processed (e.g. before any \fBEVP_DecryptUpdate()\fR
calls). For SIV mode the taglen must be 16.
.PP
SIV mode makes two passes over the input data, thus, only one call to
\&\fBEVP_CipherUpdate()\fR, \fBEVP_EncryptUpdate()\fR or \fBEVP_DecryptUpdate()\fR should be made
with \fIout\fR set to a non\-\fBNULL\fR value. A call to \fBEVP_DecryptFinal()\fR or
\&\fBEVP_CipherFinal()\fR is not required, but will indicate if the update
operation succeeded.
.SS ChaCha20\-Poly1305
.IX Subsection "ChaCha20-Poly1305"
The following \fIctrl\fRs are supported for the ChaCha20\-Poly1305 AEAD algorithm.
.IP "EVP_CIPHER_CTX_ctrl(ctx, EVP_CTRL_AEAD_SET_IVLEN, ivlen, NULL)" 4
.IX Item "EVP_CIPHER_CTX_ctrl(ctx, EVP_CTRL_AEAD_SET_IVLEN, ivlen, NULL)"
Sets the nonce length. This call is now redundant since the only valid value
is the default length of 12 (i.e. 96 bits).
Prior to OpenSSL 3.0 a nonce of less than 12 bytes could be used to automatically
pad the iv with leading 0 bytes to make it 12 bytes in length.
.IP "EVP_CIPHER_CTX_ctrl(ctx, EVP_CTRL_AEAD_GET_TAG, taglen, tag)" 4
.IX Item "EVP_CIPHER_CTX_ctrl(ctx, EVP_CTRL_AEAD_GET_TAG, taglen, tag)"
Writes \f(CW\*(C`taglen\*(C'\fR bytes of the tag value to the buffer indicated by \f(CW\*(C`tag\*(C'\fR.
This call can only be made when encrypting data and \fBafter\fR all data has been
processed (e.g. after an \fBEVP_EncryptFinal()\fR call).
.Sp
\&\f(CW\*(C`taglen\*(C'\fR specified here must be 16 (\fBPOLY1305_BLOCK_SIZE\fR, i.e. 128\-bits) or
less.
.IP "EVP_CIPHER_CTX_ctrl(ctx, EVP_CTRL_AEAD_SET_TAG, taglen, tag)" 4
.IX Item "EVP_CIPHER_CTX_ctrl(ctx, EVP_CTRL_AEAD_SET_TAG, taglen, tag)"
Sets the expected tag to \f(CW\*(C`taglen\*(C'\fR bytes from \f(CW\*(C`tag\*(C'\fR.
The tag length can only be set before specifying an IV.
\&\f(CW\*(C`taglen\*(C'\fR must be between 1 and 16 (\fBPOLY1305_BLOCK_SIZE\fR) inclusive.
This call is only valid when decrypting data.
.SH NOTES
.IX Header "NOTES"
Where possible the \fBEVP\fR interface to symmetric ciphers should be used in
preference to the low-level interfaces. This is because the code then becomes
transparent to the cipher used and much more flexible. Additionally, the
\&\fBEVP\fR interface will ensure the use of platform specific cryptographic
acceleration such as AES-NI (the low-level interfaces do not provide the
guarantee).
.PP
PKCS padding works by adding \fBn\fR padding bytes of value \fBn\fR to make the total
length of the encrypted data a multiple of the block size. Padding is always
added so if the data is already a multiple of the block size \fBn\fR will equal
the block size. For example if the block size is 8 and 11 bytes are to be
encrypted then 5 padding bytes of value 5 will be added.
.PP
When decrypting the final block is checked to see if it has the correct form.
.PP
Although the decryption operation can produce an error if padding is enabled,
it is not a strong test that the input data or key is correct. A random block
has better than 1 in 256 chance of being of the correct format and problems with
the input data earlier on will not produce a final decrypt error.
.PP
If padding is disabled then the decryption operation will always succeed if
the total amount of data decrypted is a multiple of the block size.
.PP
The functions \fBEVP_EncryptInit()\fR, \fBEVP_EncryptInit_ex()\fR,
\&\fBEVP_EncryptFinal()\fR, \fBEVP_DecryptInit()\fR, \fBEVP_DecryptInit_ex()\fR,
\&\fBEVP_CipherInit()\fR, \fBEVP_CipherInit_ex()\fR and \fBEVP_CipherFinal()\fR are obsolete
but are retained for compatibility with existing code. New code should
use \fBEVP_EncryptInit_ex2()\fR, \fBEVP_EncryptFinal_ex()\fR, \fBEVP_DecryptInit_ex2()\fR,
\&\fBEVP_DecryptFinal_ex()\fR, \fBEVP_CipherInit_ex2()\fR and \fBEVP_CipherFinal_ex()\fR
because they can reuse an existing context without allocating and freeing
it up on each call.
.PP
There are some differences between functions \fBEVP_CipherInit()\fR and
\&\fBEVP_CipherInit_ex()\fR, significant in some circumstances. \fBEVP_CipherInit()\fR fills
the passed context object with zeros.  As a consequence, \fBEVP_CipherInit()\fR does
not allow step-by-step initialization of the ctx when the \fIkey\fR and \fIiv\fR are
passed in separate calls. It also means that the flags set for the CTX are
removed, and it is especially important for the
\&\fBEVP_CIPHER_CTX_FLAG_WRAP_ALLOW\fR flag treated specially in
\&\fBEVP_CipherInit_ex()\fR.
.PP
Ignoring failure returns of the \fBEVP_CIPHER_CTX\fR initialization functions can
lead to subsequent undefined behavior when calling the functions that update or
finalize the context. The only valid calls on the \fBEVP_CIPHER_CTX\fR when
initialization fails are calls that attempt another initialization of the
context or release the context.
.PP
\&\fBEVP_get_cipherbynid()\fR, and \fBEVP_get_cipherbyobj()\fR are implemented as macros.
.SH BUGS
.IX Header "BUGS"
\&\fBEVP_MAX_KEY_LENGTH\fR and \fBEVP_MAX_IV_LENGTH\fR only refer to the internal
ciphers with default key lengths. If custom ciphers exceed these values the
results are unpredictable. This is because it has become standard practice to
define a generic key as a fixed unsigned char array containing
\&\fBEVP_MAX_KEY_LENGTH\fR bytes.
.PP
The ASN1 code is incomplete (and sometimes inaccurate) it has only been tested
for certain common S/MIME ciphers (RC2, DES, triple DES) in CBC mode.
.SH EXAMPLES
.IX Header "EXAMPLES"
Encrypt a string using IDEA:
.PP
.Vb 10
\& int do_crypt(char *outfile)
\& {
\&     unsigned char outbuf[1024];
\&     int outlen, tmplen;
\&     /*
\&      * Bogus key and IV: we\*(Aqd normally set these from
\&      * another source.
\&      */
\&     unsigned char key[] = {0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15};
\&     unsigned char iv[] = {1,2,3,4,5,6,7,8};
\&     char intext[] = "Some Crypto Text";
\&     EVP_CIPHER_CTX *ctx;
\&     FILE *out;
\&
\&     ctx = EVP_CIPHER_CTX_new();
\&     if (!EVP_EncryptInit_ex2(ctx, EVP_idea_cbc(), key, iv, NULL)) {
\&         /* Error */
\&         EVP_CIPHER_CTX_free(ctx);
\&         return 0;
\&     }
\&
\&     if (!EVP_EncryptUpdate(ctx, outbuf, &outlen, intext, strlen(intext))) {
\&         /* Error */
\&         EVP_CIPHER_CTX_free(ctx);
\&         return 0;
\&     }
\&     /*
\&      * Buffer passed to EVP_EncryptFinal() must be after data just
\&      * encrypted to avoid overwriting it.
\&      */
\&     if (!EVP_EncryptFinal_ex(ctx, outbuf + outlen, &tmplen)) {
\&         /* Error */
\&         EVP_CIPHER_CTX_free(ctx);
\&         return 0;
\&     }
\&     outlen += tmplen;
\&     EVP_CIPHER_CTX_free(ctx);
\&     /*
\&      * Need binary mode for fopen because encrypted data is
\&      * binary data. Also cannot use strlen() on it because
\&      * it won\*(Aqt be NUL terminated and may contain embedded
\&      * NULs.
\&      */
\&     out = fopen(outfile, "wb");
\&     if (out == NULL) {
\&         /* Error */
\&         return 0;
\&     }
\&     fwrite(outbuf, 1, outlen, out);
\&     fclose(out);
\&     return 1;
\& }
.Ve
.PP
The ciphertext from the above example can be decrypted using the \fBopenssl\fR
utility with the command line (shown on two lines for clarity):
.PP
.Vb 2
\& openssl idea \-d \e
\&     \-K 000102030405060708090A0B0C0D0E0F \-iv 0102030405060708 <filename
.Ve
.PP
General encryption and decryption function example using FILE I/O and AES128
with a 128\-bit key:
.PP
.Vb 12
\& int do_crypt(FILE *in, FILE *out, int do_encrypt)
\& {
\&     /* Allow enough space in output buffer for additional block */
\&     unsigned char inbuf[1024], outbuf[1024 + EVP_MAX_BLOCK_LENGTH];
\&     int inlen, outlen;
\&     EVP_CIPHER_CTX *ctx;
\&     /*
\&      * Bogus key and IV: we\*(Aqd normally set these from
\&      * another source.
\&      */
\&     unsigned char key[] = "0123456789abcdeF";
\&     unsigned char iv[] = "1234567887654321";
\&
\&     /* Don\*(Aqt set key or IV right away; we want to check lengths */
\&     ctx = EVP_CIPHER_CTX_new();
\&     if (!EVP_CipherInit_ex2(ctx, EVP_aes_128_cbc(), NULL, NULL,
\&                             do_encrypt, NULL)) {
\&         /* Error */
\&         EVP_CIPHER_CTX_free(ctx);
\&         return 0;
\&     }
\&     OPENSSL_assert(EVP_CIPHER_CTX_get_key_length(ctx) == 16);
\&     OPENSSL_assert(EVP_CIPHER_CTX_get_iv_length(ctx) == 16);
\&
\&     /* Now we can set key and IV */
\&     if (!EVP_CipherInit_ex2(ctx, NULL, key, iv, do_encrypt, NULL)) {
\&         /* Error */
\&         EVP_CIPHER_CTX_free(ctx);
\&         return 0;
\&     }
\&
\&     for (;;) {
\&         inlen = fread(inbuf, 1, 1024, in);
\&         if (inlen <= 0)
\&             break;
\&         if (!EVP_CipherUpdate(ctx, outbuf, &outlen, inbuf, inlen)) {
\&             /* Error */
\&             EVP_CIPHER_CTX_free(ctx);
\&             return 0;
\&         }
\&         fwrite(outbuf, 1, outlen, out);
\&     }
\&     if (!EVP_CipherFinal_ex(ctx, outbuf, &outlen)) {
\&         /* Error */
\&         EVP_CIPHER_CTX_free(ctx);
\&         return 0;
\&     }
\&     fwrite(outbuf, 1, outlen, out);
\&
\&     EVP_CIPHER_CTX_free(ctx);
\&     return 1;
\& }
.Ve
.PP
Encryption using AES-CBC with a 256\-bit key with "CS1" ciphertext stealing.
.PP
.Vb 10
\& int encrypt(const unsigned char *key, const unsigned char *iv,
\&             const unsigned char *msg, size_t msg_len, unsigned char *out)
\& {
\&    /*
\&     * This assumes that key size is 32 bytes and the iv is 16 bytes.
\&     * For ciphertext stealing mode the length of the ciphertext "out" will be
\&     * the same size as the plaintext size "msg_len".
\&     * The "msg_len" can be any size >= 16.
\&     */
\&     int ret = 0, encrypt = 1, outlen, len;
\&     EVP_CIPHER_CTX *ctx = NULL;
\&     EVP_CIPHER *cipher = NULL;
\&     OSSL_PARAM params[2];
\&
\&     ctx = EVP_CIPHER_CTX_new();
\&     cipher = EVP_CIPHER_fetch(NULL, "AES\-256\-CBC\-CTS", NULL);
\&     if (ctx == NULL || cipher == NULL)
\&         goto err;
\&
\&     /*
\&      * The default is "CS1" so this is not really needed,
\&      * but would be needed to set either "CS2" or "CS3".
\&      */
\&     params[0] = OSSL_PARAM_construct_utf8_string(OSSL_CIPHER_PARAM_CTS_MODE,
\&                                                  "CS1", 0);
\&     params[1] = OSSL_PARAM_construct_end();
\&
\&     if (!EVP_CipherInit_ex2(ctx, cipher, key, iv, encrypt, params))
\&         goto err;
\&
\&     /* NOTE: CTS mode does not support multiple calls to EVP_CipherUpdate() */
\&     if (!EVP_CipherUpdate(ctx, out, &outlen, msg, msg_len))
\&         goto err;
\&      if (!EVP_CipherFinal_ex(ctx, out + outlen, &len))
\&         goto err;
\&     ret = 1;
\& err:
\&     EVP_CIPHER_free(cipher);
\&     EVP_CIPHER_CTX_free(ctx);
\&     return ret;
\& }
.Ve
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBevp\fR\|(7),
\&\fBproperty\fR\|(7),
"ALGORITHM FETCHING" in \fBcrypto\fR\|(7),
\&\fBprovider\-cipher\fR\|(7),
\&\fBlife_cycle\-cipher\fR\|(7)
.PP
Supported ciphers are listed in:
.PP
\&\fBEVP_aes_128_gcm\fR\|(3),
\&\fBEVP_aria_128_gcm\fR\|(3),
\&\fBEVP_bf_cbc\fR\|(3),
\&\fBEVP_camellia_128_ecb\fR\|(3),
\&\fBEVP_cast5_cbc\fR\|(3),
\&\fBEVP_chacha20\fR\|(3),
\&\fBEVP_des_cbc\fR\|(3),
\&\fBEVP_desx_cbc\fR\|(3),
\&\fBEVP_idea_cbc\fR\|(3),
\&\fBEVP_rc2_cbc\fR\|(3),
\&\fBEVP_rc4\fR\|(3),
\&\fBEVP_rc5_32_12_16_cbc\fR\|(3),
\&\fBEVP_seed_cbc\fR\|(3),
\&\fBEVP_sm4_cbc\fR\|(3),
.SH HISTORY
.IX Header "HISTORY"
Support for OCB mode was added in OpenSSL 1.1.0.
.PP
\&\fBEVP_CIPHER_CTX\fR was made opaque in OpenSSL 1.1.0.  As a result,
\&\fBEVP_CIPHER_CTX_reset()\fR appeared and \fBEVP_CIPHER_CTX_cleanup()\fR
disappeared.  \fBEVP_CIPHER_CTX_init()\fR remains as an alias for
\&\fBEVP_CIPHER_CTX_reset()\fR.
.PP
The \fBEVP_CIPHER_CTX_cipher()\fR function was deprecated in OpenSSL 3.0; use
\&\fBEVP_CIPHER_CTX_get0_cipher()\fR instead.
.PP
The \fBEVP_EncryptInit_ex2()\fR, \fBEVP_DecryptInit_ex2()\fR, \fBEVP_CipherInit_ex2()\fR,
\&\fBEVP_CIPHER_fetch()\fR, \fBEVP_CIPHER_free()\fR, \fBEVP_CIPHER_up_ref()\fR,
\&\fBEVP_CIPHER_CTX_get0_cipher()\fR, \fBEVP_CIPHER_CTX_get1_cipher()\fR,
\&\fBEVP_CIPHER_get_params()\fR, \fBEVP_CIPHER_CTX_set_params()\fR,
\&\fBEVP_CIPHER_CTX_get_params()\fR, \fBEVP_CIPHER_gettable_params()\fR,
\&\fBEVP_CIPHER_settable_ctx_params()\fR, \fBEVP_CIPHER_gettable_ctx_params()\fR,
\&\fBEVP_CIPHER_CTX_settable_params()\fR and \fBEVP_CIPHER_CTX_gettable_params()\fR
functions were added in 3.0.
.PP
The \fBEVP_CIPHER_nid()\fR, \fBEVP_CIPHER_name()\fR, \fBEVP_CIPHER_block_size()\fR,
\&\fBEVP_CIPHER_key_length()\fR, \fBEVP_CIPHER_iv_length()\fR, \fBEVP_CIPHER_flags()\fR,
\&\fBEVP_CIPHER_mode()\fR, \fBEVP_CIPHER_type()\fR, \fBEVP_CIPHER_CTX_nid()\fR,
\&\fBEVP_CIPHER_CTX_block_size()\fR, \fBEVP_CIPHER_CTX_key_length()\fR,
\&\fBEVP_CIPHER_CTX_iv_length()\fR, \fBEVP_CIPHER_CTX_tag_length()\fR,
\&\fBEVP_CIPHER_CTX_num()\fR, \fBEVP_CIPHER_CTX_type()\fR, and \fBEVP_CIPHER_CTX_mode()\fR
functions were renamed to include \f(CW\*(C`get\*(C'\fR or \f(CW\*(C`get0\*(C'\fR in their names in
OpenSSL 3.0, respectively. The old names are kept as non-deprecated
alias macros.
.PP
The \fBEVP_CIPHER_CTX_encrypting()\fR function was renamed to
\&\fBEVP_CIPHER_CTX_is_encrypting()\fR in OpenSSL 3.0. The old name is kept as
non-deprecated alias macro.
.PP
The \fBEVP_CIPHER_CTX_flags()\fR macro was deprecated in OpenSSL 1.1.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2023 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
