.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "BIO_S_MEM 3ossl"
.TH BIO_S_MEM 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
BIO_s_secmem,
BIO_s_mem, BIO_set_mem_eof_return, BIO_get_mem_data, BIO_set_mem_buf,
BIO_get_mem_ptr, BIO_new_mem_buf \- memory BIO
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/bio.h>
\&
\& const BIO_METHOD *BIO_s_mem(void);
\& const BIO_METHOD *BIO_s_secmem(void);
\&
\& BIO_set_mem_eof_return(BIO *b, int v);
\& long BIO_get_mem_data(BIO *b, char **pp);
\& BIO_set_mem_buf(BIO *b, BUF_MEM *bm, int c);
\& BIO_get_mem_ptr(BIO *b, BUF_MEM **pp);
\&
\& BIO *BIO_new_mem_buf(const void *buf, int len);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBBIO_s_mem()\fR returns the memory BIO method function.
.PP
A memory BIO is a source/sink BIO which uses memory for its I/O. Data
written to a memory BIO is stored in a BUF_MEM structure which is extended
as appropriate to accommodate the stored data.
.PP
\&\fBBIO_s_secmem()\fR is like \fBBIO_s_mem()\fR except that the secure heap is used
for buffer storage.
.PP
Any data written to a memory BIO can be recalled by reading from it.
Unless the memory BIO is read only any data read from it is deleted from
the BIO.
.PP
Memory BIOs support \fBBIO_gets()\fR and \fBBIO_puts()\fR.
.PP
If the BIO_CLOSE flag is set when a memory BIO is freed then the underlying
BUF_MEM structure is also freed.
.PP
Calling \fBBIO_reset()\fR on a read write memory BIO clears any data in it if the
flag BIO_FLAGS_NONCLEAR_RST is not set, otherwise it just restores the read
pointer to the state it was just after the last write was performed and the
data can be read again. On a read only BIO it similarly restores the BIO to
its original state and the read only data can be read again.
.PP
\&\fBBIO_eof()\fR is true if no data is in the BIO.
.PP
\&\fBBIO_ctrl_pending()\fR returns the number of bytes currently stored.
.PP
\&\fBBIO_set_mem_eof_return()\fR sets the behaviour of memory BIO \fBb\fR when it is
empty. If the \fBv\fR is zero then an empty memory BIO will return EOF (that is
it will return zero and BIO_should_retry(b) will be false. If \fBv\fR is non
zero then it will return \fBv\fR when it is empty and it will set the read retry
flag (that is BIO_read_retry(b) is true). To avoid ambiguity with a normal
positive return value \fBv\fR should be set to a negative value, typically \-1.
.PP
\&\fBBIO_get_mem_data()\fR sets *\fBpp\fR to a pointer to the start of the memory BIOs data
and returns the total amount of data available. It is implemented as a macro.
Note the pointer returned by this call is informative, no transfer of ownership
of this memory is implied.  See notes on \fBBIO_set_close()\fR.
.PP
\&\fBBIO_set_mem_buf()\fR sets the internal BUF_MEM structure to \fBbm\fR and sets the
close flag to \fBc\fR, that is \fBc\fR should be either BIO_CLOSE or BIO_NOCLOSE.
It is a macro.
.PP
\&\fBBIO_get_mem_ptr()\fR places the underlying BUF_MEM structure in *\fBpp\fR. It is
a macro.
.PP
\&\fBBIO_new_mem_buf()\fR creates a memory BIO using \fBlen\fR bytes of data at \fBbuf\fR,
if \fBlen\fR is \-1 then the \fBbuf\fR is assumed to be nul terminated and its
length is determined by \fBstrlen\fR. The BIO is set to a read only state and
as a result cannot be written to. This is useful when some data needs to be
made available from a static area of memory in the form of a BIO. The
supplied data is read directly from the supplied buffer: it is \fBnot\fR copied
first, so the supplied area of memory must be unchanged until the BIO is freed.
.SH NOTES
.IX Header "NOTES"
Writes to memory BIOs will always succeed if memory is available: that is
their size can grow indefinitely.
.PP
Every write after partial read (not all data in the memory buffer was read)
to a read write memory BIO will have to move the unread data with an internal
copy operation, if a BIO contains a lot of data and it is read in small
chunks intertwined with writes the operation can be very slow. Adding
a buffering BIO to the chain can speed up the process.
.PP
Calling \fBBIO_set_mem_buf()\fR on a BIO created with \fBBIO_new_secmem()\fR will
give undefined results, including perhaps a program crash.
.PP
Switching the memory BIO from read write to read only is not supported and
can give undefined results including a program crash. There are two notable
exceptions to the rule. The first one is to assign a static memory buffer
immediately after BIO creation and set the BIO as read only.
.PP
The other supported sequence is to start with read write BIO then temporarily
switch it to read only and call \fBBIO_reset()\fR on the read only BIO immediately
before switching it back to read write. Before the BIO is freed it must be
switched back to the read write mode.
.PP
Calling \fBBIO_get_mem_ptr()\fR on read only BIO will return a BUF_MEM that
contains only the remaining data to be read. If the close status of the
BIO is set to BIO_NOCLOSE, before freeing the BUF_MEM the data pointer
in it must be set to NULL as the data pointer does not point to an
allocated memory.
.PP
Calling \fBBIO_reset()\fR on a read write memory BIO with BIO_FLAGS_NONCLEAR_RST
flag set can have unexpected outcome when the reads and writes to the
BIO are intertwined. As documented above the BIO will be reset to the
state after the last completed write operation. The effects of reads
preceding that write operation cannot be undone.
.PP
Calling \fBBIO_get_mem_ptr()\fR prior to a \fBBIO_reset()\fR call with
BIO_FLAGS_NONCLEAR_RST set has the same effect as a write operation.
.PP
Calling \fBBIO_set_close()\fR with BIO_NOCLOSE orphans the BUF_MEM internal to the
BIO, _not_ its actual data buffer. See the examples section for the proper
method for claiming ownership of the data pointer for a deferred free operation.
.SH BUGS
.IX Header "BUGS"
There should be an option to set the maximum size of a memory BIO.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBBIO_s_mem()\fR and \fBBIO_s_secmem()\fR return a valid memory \fBBIO_METHOD\fR structure.
.PP
\&\fBBIO_set_mem_eof_return()\fR, \fBBIO_set_mem_buf()\fR and \fBBIO_get_mem_ptr()\fR
return 1 on success or a value which is less than or equal to 0 if an error occurred.
.PP
\&\fBBIO_get_mem_data()\fR returns the total number of bytes available on success,
0 if b is NULL, or a negative value in case of other errors.
.PP
\&\fBBIO_new_mem_buf()\fR returns a valid \fBBIO\fR structure on success or NULL on error.
.SH EXAMPLES
.IX Header "EXAMPLES"
Create a memory BIO and write some data to it:
.PP
.Vb 1
\& BIO *mem = BIO_new(BIO_s_mem());
\&
\& BIO_puts(mem, "Hello World\en");
.Ve
.PP
Create a read only memory BIO:
.PP
.Vb 2
\& char data[] = "Hello World";
\& BIO *mem = BIO_new_mem_buf(data, \-1);
.Ve
.PP
Extract the BUF_MEM structure from a memory BIO and then free up the BIO:
.PP
.Vb 1
\& BUF_MEM *bptr;
\&
\& BIO_get_mem_ptr(mem, &bptr);
\& BIO_set_close(mem, BIO_NOCLOSE); /* So BIO_free() leaves BUF_MEM alone */
\& BIO_free(mem);
.Ve
.PP
Extract the BUF_MEM ptr, claim ownership of the internal data and free the BIO
and BUF_MEM structure:
.PP
.Vb 2
\& BUF_MEM *bptr;
\& char *data;
\&
\& BIO_get_mem_data(bio, &data);
\& BIO_get_mem_ptr(bio, &bptr);
\& BIO_set_close(mem, BIO_NOCLOSE); /* So BIO_free orphans BUF_MEM */
\& BIO_free(bio);
\& bptr\->data = NULL; /* Tell BUF_MEM to orphan data */
\& BUF_MEM_free(bptr);
\& ...
\& free(data);
.Ve
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2023 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
