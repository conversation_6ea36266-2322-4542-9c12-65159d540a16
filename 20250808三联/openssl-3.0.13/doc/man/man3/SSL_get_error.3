.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "SSL_GET_ERROR 3ossl"
.TH SSL_GET_ERROR 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
SSL_get_error \- obtain result code for TLS/SSL I/O operation
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/ssl.h>
\&
\& int SSL_get_error(const SSL *ssl, int ret);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBSSL_get_error()\fR returns a result code (suitable for the C "switch"
statement) for a preceding call to \fBSSL_connect()\fR, \fBSSL_accept()\fR, \fBSSL_do_handshake()\fR,
\&\fBSSL_read_ex()\fR, \fBSSL_read()\fR, \fBSSL_peek_ex()\fR, \fBSSL_peek()\fR, \fBSSL_shutdown()\fR,
\&\fBSSL_write_ex()\fR or \fBSSL_write()\fR on \fBssl\fR.  The value returned by that TLS/SSL I/O
function must be passed to \fBSSL_get_error()\fR in parameter \fBret\fR.
.PP
In addition to \fBssl\fR and \fBret\fR, \fBSSL_get_error()\fR inspects the
current thread's OpenSSL error queue.  Thus, \fBSSL_get_error()\fR must be
used in the same thread that performed the TLS/SSL I/O operation, and no
other OpenSSL function calls should appear in between.  The current
thread's error queue must be empty before the TLS/SSL I/O operation is
attempted, or \fBSSL_get_error()\fR will not work reliably.
.SH NOTES
.IX Header "NOTES"
Some TLS implementations do not send a close_notify alert on shutdown.
.PP
On an unexpected EOF, versions before OpenSSL 3.0 returned
\&\fBSSL_ERROR_SYSCALL\fR, nothing was added to the error stack, and errno was 0.
Since OpenSSL 3.0 the returned error is \fBSSL_ERROR_SSL\fR with a meaningful
error on the error stack (SSL_R_UNEXPECTED_EOF_WHILE_READING). This error reason
code may be used for control flow decisions (see the man page for
\&\fBERR_GET_REASON\fR\|(3) for further details on this).
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
The following return values can currently occur:
.IP SSL_ERROR_NONE 4
.IX Item "SSL_ERROR_NONE"
The TLS/SSL I/O operation completed.  This result code is returned
if and only if \fBret > 0\fR.
.IP SSL_ERROR_ZERO_RETURN 4
.IX Item "SSL_ERROR_ZERO_RETURN"
The TLS/SSL peer has closed the connection for writing by sending the
close_notify alert.
No more data can be read.
Note that \fBSSL_ERROR_ZERO_RETURN\fR does not necessarily
indicate that the underlying transport has been closed.
.Sp
This error can also appear when the option \fBSSL_OP_IGNORE_UNEXPECTED_EOF\fR
is set. See \fBSSL_CTX_set_options\fR\|(3) for more details.
.IP "SSL_ERROR_WANT_READ, SSL_ERROR_WANT_WRITE" 4
.IX Item "SSL_ERROR_WANT_READ, SSL_ERROR_WANT_WRITE"
The operation did not complete and can be retried later.
.Sp
\&\fBSSL_ERROR_WANT_READ\fR is returned when the last operation was a read
operation from a nonblocking \fBBIO\fR.
It means that not enough data was available at this time to complete the
operation.
If at a later time the underlying \fBBIO\fR has data available for reading the same
function can be called again.
.Sp
\&\fBSSL_read()\fR and \fBSSL_read_ex()\fR can also set \fBSSL_ERROR_WANT_READ\fR when there is
still unprocessed data available at either the \fBSSL\fR or the \fBBIO\fR layer, even
for a blocking \fBBIO\fR.
See \fBSSL_read\fR\|(3) for more information.
.Sp
\&\fBSSL_ERROR_WANT_WRITE\fR is returned when the last operation was a write
to a nonblocking \fBBIO\fR and it was unable to sent all data to the \fBBIO\fR.
When the \fBBIO\fR is writable again, the same function can be called again.
.Sp
Note that the retry may again lead to an \fBSSL_ERROR_WANT_READ\fR or
\&\fBSSL_ERROR_WANT_WRITE\fR condition.
There is no fixed upper limit for the number of iterations that
may be necessary until progress becomes visible at application
protocol level.
.Sp
It is safe to call \fBSSL_read()\fR or \fBSSL_read_ex()\fR when more data is available
even when the call that set this error was an \fBSSL_write()\fR or \fBSSL_write_ex()\fR.
However, if the call was an \fBSSL_write()\fR or \fBSSL_write_ex()\fR, it should be called
again to continue sending the application data. If you get \fBSSL_ERROR_WANT_WRITE\fR
from \fBSSL_write()\fR or \fBSSL_write_ex()\fR then you should not do any other operation
that could trigger \fBIO\fR other than to repeat the previous \fBSSL_write()\fR call.
.Sp
For socket \fBBIO\fRs (e.g. when \fBSSL_set_fd()\fR was used), \fBselect()\fR or
\&\fBpoll()\fR on the underlying socket can be used to find out when the
TLS/SSL I/O function should be retried.
.Sp
Caveat: Any TLS/SSL I/O function can lead to either of
\&\fBSSL_ERROR_WANT_READ\fR and \fBSSL_ERROR_WANT_WRITE\fR.
In particular,
\&\fBSSL_read_ex()\fR, \fBSSL_read()\fR, \fBSSL_peek_ex()\fR, or \fBSSL_peek()\fR may want to write data
and \fBSSL_write()\fR or \fBSSL_write_ex()\fR may want to read data.
This is mainly because
TLS/SSL handshakes may occur at any time during the protocol (initiated by
either the client or the server); \fBSSL_read_ex()\fR, \fBSSL_read()\fR, \fBSSL_peek_ex()\fR,
\&\fBSSL_peek()\fR, \fBSSL_write_ex()\fR, and \fBSSL_write()\fR will handle any pending handshakes.
.IP "SSL_ERROR_WANT_CONNECT, SSL_ERROR_WANT_ACCEPT" 4
.IX Item "SSL_ERROR_WANT_CONNECT, SSL_ERROR_WANT_ACCEPT"
The operation did not complete; the same TLS/SSL I/O function should be
called again later. The underlying BIO was not connected yet to the peer
and the call would block in \fBconnect()\fR/\fBaccept()\fR. The SSL function should be
called again when the connection is established. These messages can only
appear with a \fBBIO_s_connect()\fR or \fBBIO_s_accept()\fR BIO, respectively.
In order to find out, when the connection has been successfully established,
on many platforms \fBselect()\fR or \fBpoll()\fR for writing on the socket file descriptor
can be used.
.IP SSL_ERROR_WANT_X509_LOOKUP 4
.IX Item "SSL_ERROR_WANT_X509_LOOKUP"
The operation did not complete because an application callback set by
\&\fBSSL_CTX_set_client_cert_cb()\fR has asked to be called again.
The TLS/SSL I/O function should be called again later.
Details depend on the application.
.IP SSL_ERROR_WANT_ASYNC 4
.IX Item "SSL_ERROR_WANT_ASYNC"
The operation did not complete because an asynchronous engine is still
processing data. This will only occur if the mode has been set to SSL_MODE_ASYNC
using \fBSSL_CTX_set_mode\fR\|(3) or \fBSSL_set_mode\fR\|(3) and an asynchronous capable
engine is being used. An application can determine whether the engine has
completed its processing using \fBselect()\fR or \fBpoll()\fR on the asynchronous wait file
descriptor. This file descriptor is available by calling
\&\fBSSL_get_all_async_fds\fR\|(3) or \fBSSL_get_changed_async_fds\fR\|(3). The TLS/SSL I/O
function should be called again later. The function \fBmust\fR be called from the
same thread that the original call was made from.
.IP SSL_ERROR_WANT_ASYNC_JOB 4
.IX Item "SSL_ERROR_WANT_ASYNC_JOB"
The asynchronous job could not be started because there were no async jobs
available in the pool (see \fBASYNC_init_thread\fR\|(3)). This will only occur if the
mode has been set to SSL_MODE_ASYNC using \fBSSL_CTX_set_mode\fR\|(3) or
\&\fBSSL_set_mode\fR\|(3) and a maximum limit has been set on the async job pool
through a call to \fBASYNC_init_thread\fR\|(3). The application should retry the
operation after a currently executing asynchronous operation for the current
thread has completed.
.IP SSL_ERROR_WANT_CLIENT_HELLO_CB 4
.IX Item "SSL_ERROR_WANT_CLIENT_HELLO_CB"
The operation did not complete because an application callback set by
\&\fBSSL_CTX_set_client_hello_cb()\fR has asked to be called again.
The TLS/SSL I/O function should be called again later.
Details depend on the application.
.IP SSL_ERROR_SYSCALL 4
.IX Item "SSL_ERROR_SYSCALL"
Some non-recoverable, fatal I/O error occurred. The OpenSSL error queue may
contain more information on the error. For socket I/O on Unix systems, consult
\&\fBerrno\fR for details. If this error occurs then no further I/O operations should
be performed on the connection and \fBSSL_shutdown()\fR must not be called.
.Sp
This value can also be returned for other errors, check the error queue for
details.
.IP SSL_ERROR_SSL 4
.IX Item "SSL_ERROR_SSL"
A non-recoverable, fatal error in the SSL library occurred, usually a protocol
error.  The OpenSSL error queue contains more information on the error. If this
error occurs then no further I/O operations should be performed on the
connection and \fBSSL_shutdown()\fR must not be called.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBssl\fR\|(7)
.SH HISTORY
.IX Header "HISTORY"
The SSL_ERROR_WANT_ASYNC error code was added in OpenSSL 1.1.0.
The SSL_ERROR_WANT_CLIENT_HELLO_CB error code was added in OpenSSL 1.1.1.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2024 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
