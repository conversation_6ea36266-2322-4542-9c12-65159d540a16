.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "OSSL_LIB_CTX 3ossl"
.TH OSSL_LIB_CTX 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
OSSL_LIB_CTX, OSSL_LIB_CTX_new, OSSL_LIB_CTX_new_from_dispatch,
OSSL_LIB_CTX_new_child, OSSL_LIB_CTX_free, OSSL_LIB_CTX_load_config,
OSSL_LIB_CTX_get0_global_default, OSSL_LIB_CTX_set0_default
\&\- OpenSSL library context
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/crypto.h>
\&
\& typedef struct ossl_lib_ctx_st OSSL_LIB_CTX;
\&
\& OSSL_LIB_CTX *OSSL_LIB_CTX_new(void);
\& OSSL_LIB_CTX *OSSL_LIB_CTX_new_from_dispatch(const OSSL_CORE_HANDLE *handle,
\&                                              const OSSL_DISPATCH *in);
\& OSSL_LIB_CTX *OSSL_LIB_CTX_new_child(const OSSL_CORE_HANDLE *handle,
\&                                      const OSSL_DISPATCH *in);
\& int OSSL_LIB_CTX_load_config(OSSL_LIB_CTX *ctx, const char *config_file);
\& void OSSL_LIB_CTX_free(OSSL_LIB_CTX *ctx);
\& OSSL_LIB_CTX *OSSL_LIB_CTX_get0_global_default(void);
\& OSSL_LIB_CTX *OSSL_LIB_CTX_set0_default(OSSL_LIB_CTX *ctx);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBOSSL_LIB_CTX\fR is an internal OpenSSL library context type.
Applications may allocate their own, but may also use NULL to use
a default context with functions that take an \fBOSSL_LIB_CTX\fR
argument.
.PP
When a non default library context is in use care should be taken with
multi-threaded applications to properly clean up thread local resources before
the OSSL_LIB_CTX is freed.
See \fBOPENSSL_thread_stop_ex\fR\|(3) for more information.
.PP
\&\fBOSSL_LIB_CTX_new()\fR creates a new OpenSSL library context.
.PP
\&\fBOSSL_LIB_CTX_new_from_dispatch()\fR creates a new OpenSSL library context
initialised to use callbacks from the OSSL_DISPATCH structure. This is primarily
useful for provider authors. The \fIhandle\fR and dispatch structure arguments
passed should be the same ones as passed to a provider's
OSSL_provider_init function. Some OpenSSL functions, such as
\&\fBBIO_new_from_core_bio\fR\|(3), require the library context to be created in this
way in order to work.
.PP
\&\fBOSSL_LIB_CTX_new_child()\fR is only useful to provider authors and does the same
thing as \fBOSSL_LIB_CTX_new_from_dispatch()\fR except that it additionally links the
new library context to the application library context. The new library context
is a full library context in its own right, but will have all the same providers
available to it that are available in the application library context (without
having to reload them). If the application loads or unloads providers from the
application library context then this will be automatically mirrored in the
child library context.
.PP
In addition providers that are not loaded in the parent library context can be
explicitly loaded into the child library context independently from the parent
library context. Providers loaded independently in this way will not be mirrored
in the parent library context and will not be affected if the parent library
context subsequently loads the same provider.
.PP
A provider may call the function \fBOSSL_PROVIDER_load\fR\|(3) with the child library
context as required. If the provider already exists due to it being mirrored
from the parent library context then it will remain available and its reference
count will be increased. If \fBOSSL_PROVIDER_load\fR\|(3) is called in this way then
\&\fBOSSL_PROVIDER_unload\fR\|(3) should be subsequently called to decrement the
reference count. \fBOSSL_PROVIDER_unload\fR\|(3) must not be called for a provider in
the child library context that did not have an earlier \fBOSSL_PROVIDER_load\fR\|(3)
call for that provider in that child library context.
.PP
In addition to providers, a child library context will also mirror the default
properties (set via \fBEVP_set_default_properties\fR\|(3)) from the parent library
context. If \fBEVP_set_default_properties\fR\|(3) is called directly on a child
library context then the new properties will override anything from the parent
library context and mirroring of the properties will stop.
.PP
When \fBOSSL_LIB_CTX_new_child()\fR is called from within the scope of a provider's
\&\fBOSSL_provider_init\fR function the currently initialising provider is not yet
available in the application's library context and therefore will similarly not
yet be available in the newly constructed child library context. As soon as the
\&\fBOSSL_provider_init\fR function returns then the new provider is available in the
application's library context and will be similarly mirrored in the child
library context.
.PP
\&\fBOSSL_LIB_CTX_load_config()\fR loads a configuration file using the given \fIctx\fR.
This can be used to associate a library context with providers that are loaded
from a configuration.
.PP
\&\fBOSSL_LIB_CTX_free()\fR frees the given \fIctx\fR, unless it happens to be the
default OpenSSL library context.
.PP
\&\fBOSSL_LIB_CTX_get0_global_default()\fR returns a concrete (non NULL) reference to
the global default library context.
.PP
\&\fBOSSL_LIB_CTX_set0_default()\fR sets the default OpenSSL library context to be
\&\fIctx\fR in the current thread.  The previous default library context is
returned.  Care should be taken by the caller to restore the previous
default library context with a subsequent call of this function. If \fIctx\fR is
NULL then no change is made to the default library context, but a pointer to
the current library context is still returned. On a successful call of this
function the returned value will always be a concrete (non NULL) library
context.
.PP
Care should be taken when changing the default library context and starting
async jobs (see \fBASYNC_start_job\fR\|(3)), as the default library context when
the job is started will be used throughout the lifetime of an async job, no
matter how the calling thread makes further default library context changes
in the mean time.  This means that the calling thread must not free the
library context that was the default at the start of the async job before
that job has finished.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBOSSL_LIB_CTX_new()\fR, \fBOSSL_LIB_CTX_get0_global_default()\fR and
\&\fBOSSL_LIB_CTX_set0_default()\fR return a library context pointer on success, or NULL
on error.
.PP
\&\fBOSSL_LIB_CTX_free()\fR doesn't return any value.
.PP
\&\fBOSSL_LIB_CTX_load_config()\fR returns 1 on success, 0 on error.
.SH HISTORY
.IX Header "HISTORY"
All of the functions described on this page were added in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2019\-2022 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
