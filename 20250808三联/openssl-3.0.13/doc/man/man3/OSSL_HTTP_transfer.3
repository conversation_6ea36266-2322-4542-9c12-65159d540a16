.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "OSSL_HTTP_TRANSFER 3ossl"
.TH OSSL_HTTP_TRANSFER 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
OSSL_HTTP_open,
OSSL_HTTP_bio_cb_t,
OSSL_HTTP_proxy_connect,
OSSL_HTTP_set1_request,
OSSL_HTTP_exchange,
OSSL_HTTP_get,
OSSL_HTTP_transfer,
OSSL_HTTP_close
\&\-  HTTP client high\-level functions
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/http.h>
\&
\& typedef BIO *(*OSSL_HTTP_bio_cb_t)(BIO *bio, void *arg,
\&                                    int connect, int detail);
\& OSSL_HTTP_REQ_CTX *OSSL_HTTP_open(const char *server, const char *port,
\&                                   const char *proxy, const char *no_proxy,
\&                                   int use_ssl, BIO *bio, BIO *rbio,
\&                                   OSSL_HTTP_bio_cb_t bio_update_fn, void *arg,
\&                                   int buf_size, int overall_timeout);
\& int OSSL_HTTP_proxy_connect(BIO *bio, const char *server, const char *port,
\&                             const char *proxyuser, const char *proxypass,
\&                             int timeout, BIO *bio_err, const char *prog);
\& int OSSL_HTTP_set1_request(OSSL_HTTP_REQ_CTX *rctx, const char *path,
\&                            const STACK_OF(CONF_VALUE) *headers,
\&                            const char *content_type, BIO *req,
\&                            const char *expected_content_type, int expect_asn1,
\&                            size_t max_resp_len, int timeout, int keep_alive);
\& BIO *OSSL_HTTP_exchange(OSSL_HTTP_REQ_CTX *rctx, char **redirection_url);
\& BIO *OSSL_HTTP_get(const char *url, const char *proxy, const char *no_proxy,
\&                    BIO *bio, BIO *rbio,
\&                    OSSL_HTTP_bio_cb_t bio_update_fn, void *arg,
\&                    int buf_size, const STACK_OF(CONF_VALUE) *headers,
\&                    const char *expected_content_type, int expect_asn1,
\&                    size_t max_resp_len, int timeout);
\& BIO *OSSL_HTTP_transfer(OSSL_HTTP_REQ_CTX **prctx,
\&                         const char *server, const char *port,
\&                         const char *path, int use_ssl,
\&                         const char *proxy, const char *no_proxy,
\&                         BIO *bio, BIO *rbio,
\&                         OSSL_HTTP_bio_cb_t bio_update_fn, void *arg,
\&                         int buf_size, const STACK_OF(CONF_VALUE) *headers,
\&                         const char *content_type, BIO *req,
\&                         const char *expected_content_type, int expect_asn1,
\&                         size_t max_resp_len, int timeout, int keep_alive);
\& int OSSL_HTTP_close(OSSL_HTTP_REQ_CTX *rctx, int ok);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBOSSL_HTTP_open()\fR initiates an HTTP session using the \fIbio\fR argument if not
NULL, else by connecting to a given \fIserver\fR optionally via a \fIproxy\fR.
.PP
Typically the OpenSSL build supports sockets and the \fIbio\fR parameter is NULL.
In this case \fIrbio\fR must be NULL as well and the \fIserver\fR must be non-NULL.
The function creates a network BIO internally using \fBBIO_new_connect\fR\|(3)
for connecting to the given server and the optionally given \fIport\fR,
defaulting to 80 for HTTP or 443 for HTTPS.
Then this internal BIO is used for setting up a connection
and for exchanging one or more request and response.
If \fIbio\fR is given and \fIrbio\fR is NULL then this \fIbio\fR is used instead.
If both \fIbio\fR and \fIrbio\fR are given (which may be memory BIOs for instance)
then no explicit connection is set up, but
\&\fIbio\fR is used for writing requests and \fIrbio\fR for reading responses.
As soon as the client has flushed \fIbio\fR the server must be ready to provide
a response or indicate a waiting condition via \fIrbio\fR.
.PP
If \fIbio\fR is given, it is an error to provide \fIproxy\fR or \fIno_proxy\fR arguments,
while \fIserver\fR and \fIport\fR arguments may be given to support diagnostic output.
If \fIbio\fR is NULL the optional \fIproxy\fR parameter can be used to set an
HTTP(S) proxy to use (unless overridden by "no_proxy" settings).
If TLS is not used this defaults to the environment variable \f(CW\*(C`http_proxy\*(C'\fR
if set, else \f(CW\*(C`HTTP_PROXY\*(C'\fR.
If \fIuse_ssl\fR != 0 it defaults to \f(CW\*(C`https_proxy\*(C'\fR if set, else \f(CW\*(C`HTTPS_PROXY\*(C'\fR.
An empty proxy string \f(CW""\fR forbids using a proxy.
Else the format is
\&\f(CW\*(C`[http[s]://][userinfo@]host[:port][/path][?query][#fragment]\*(C'\fR,
where any userinfo, path, query, and fragment given is ignored.
The default proxy port number is 80, or 443 in case "https:" is given.
The HTTP client functions connect via the given proxy unless the \fIserver\fR
is found in the optional list \fIno_proxy\fR of proxy hostnames (if not NULL;
default is the environment variable \f(CW\*(C`no_proxy\*(C'\fR if set, else \f(CW\*(C`NO_PROXY\*(C'\fR).
Proxying plain HTTP is supported directly,
while using a proxy for HTTPS connections requires a suitable callback function
such as \fBOSSL_HTTP_proxy_connect()\fR, described below.
.PP
If \fIuse_ssl\fR is nonzero a TLS connection is requested
and the \fIbio_update_fn\fR parameter must be provided.
.PP
The parameter \fIbio_update_fn\fR, which is optional if \fIuse_ssl\fR is 0,
may be used to modify the connection BIO used by the HTTP client,
but cannot be used when both \fIbio\fR and \fIrbio\fR are given.
\&\fIbio_update_fn\fR is a BIO connect/disconnect callback function with prototype
.PP
.Vb 1
\& BIO *(*OSSL_HTTP_bio_cb_t)(BIO *bio, void *arg, int connect, int detail)
.Ve
.PP
The callback function may modify the BIO provided in the \fIbio\fR argument,
whereby it may make use of a custom defined argument \fIarg\fR,
which may for instance point to an \fBSSL_CTX\fR structure.
During connection establishment, just after calling \fBBIO_do_connect_retry()\fR, the
callback function is invoked with the \fIconnect\fR argument being 1 and
\&\fIdetail\fR being 1 if \fIuse_ssl\fR is nonzero (i.e., HTTPS is requested), else 0.
On disconnect \fIconnect\fR is 0 and \fIdetail\fR is 1 if no error occurred, else 0.
For instance, on connect the callback may push an SSL BIO to implement HTTPS;
after disconnect it may do some diagnostic output and pop and free the SSL BIO.
.PP
The callback function must return either the potentially modified BIO \fIbio\fR.
or NULL to indicate failure, in which case it should not modify the BIO.
.PP
Here is a simple example that supports TLS connections (but not via a proxy):
.PP
.Vb 5
\& BIO *http_tls_cb(BIO *bio, void *arg, int connect, int detail)
\& {
\&     if (connect && detail) { /* connecting with TLS */
\&         SSL_CTX *ctx = (SSL_CTX *)arg;
\&         BIO *sbio = BIO_new_ssl(ctx, 1);
\&
\&         bio = sbio != NULL ? BIO_push(sbio, bio) : NULL;
\&     } else if (!connect) { /* disconnecting */
\&         BIO *hbio;
\&
\&         if (!detail) { /* an error has occurred */
\&             /* optionally add diagnostics here */
\&         }
\&         BIO_ssl_shutdown(bio);
\&         hbio = BIO_pop(bio);
\&         BIO_free(bio); /* SSL BIO */
\&         bio = hbio;
\&     }
\&     return bio;
\& }
.Ve
.PP
After disconnect the modified BIO will be deallocated using \fBBIO_free_all()\fR.
.PP
The \fIbuf_size\fR parameter specifies the response header maximum line length.
A value <= 0 means that the \fBOSSL_HTTP_DEFAULT_MAX_LINE_LEN\fR (4KiB) is used.
\&\fIbuf_size\fR is also used as the number of content bytes that are read at a time.
.PP
If the \fIoverall_timeout\fR parameter is > 0 this indicates the maximum number of
seconds the overall HTTP transfer (i.e., connection setup if needed,
sending requests, and receiving responses) is allowed to take until completion.
A value <= 0 enables waiting indefinitely, i.e., no timeout.
.PP
\&\fBOSSL_HTTP_proxy_connect()\fR may be used by an above BIO connect callback function
to set up an SSL/TLS connection via an HTTPS proxy.
It promotes the given BIO \fIbio\fR representing a connection
pre-established with a TLS proxy using the HTTP CONNECT method,
optionally using proxy client credentials \fIproxyuser\fR and \fIproxypass\fR,
to connect with TLS protection ultimately to \fIserver\fR and \fIport\fR.
If the \fIport\fR argument is NULL or the empty string it defaults to "443".
If the \fItimeout\fR parameter is > 0 this indicates the maximum number of
seconds the connection setup is allowed to take.
A value <= 0 enables waiting indefinitely, i.e., no timeout.
Since this function is typically called by applications such as
\&\fBopenssl\-s_client\fR\|(1) it uses the \fIbio_err\fR and \fIprog\fR parameters (unless
NULL) to print additional diagnostic information in a user-oriented way.
.PP
\&\fBOSSL_HTTP_set1_request()\fR sets up in \fIrctx\fR the request header and content data
and expectations on the response using the following parameters.
If <rctx> indicates using a proxy for HTTP (but not HTTPS), the server host
(and optionally port) needs to be placed in the header; thus it must be present
in \fIrctx\fR.
For backward compatibility, the server (and optional port) may also be given in
the \fIpath\fR argument beginning with \f(CW\*(C`http://\*(C'\fR (thus giving an absoluteURI).
If \fIpath\fR is NULL it defaults to "/".
If \fIreq\fR is NULL the HTTP GET method will be used to send the request
else HTTP POST with the contents of \fIreq\fR and optional \fIcontent_type\fR, where
the length of the data in \fIreq\fR does not need to be determined in advance: the
BIO will be read on-the-fly while sending the request, which supports streaming.
The optional list \fIheaders\fR may contain additional custom HTTP header lines.
If the parameter \fIexpected_content_type\fR
is not NULL then the client will check that the given content type string
is included in the HTTP header of the response and return an error if not.
If the \fIexpect_asn1\fR parameter is nonzero,
a structure in ASN.1 encoding will be expected as response content.
The \fImax_resp_len\fR parameter specifies the maximum allowed
response content length, where the value 0 indicates no limit.
If the \fItimeout\fR parameter is > 0 this indicates the maximum number of seconds
the subsequent HTTP transfer (sending the request and receiving a response)
is allowed to take.
A value of 0 enables waiting indefinitely, i.e., no timeout.
A value < 0 indicates that the \fIoverall_timeout\fR parameter value given
when opening the HTTP transfer will be used instead.
If \fIkeep_alive\fR is 0 the connection is not kept open
after receiving a response, which is the default behavior for HTTP 1.0.
If the value is 1 or 2 then a persistent connection is requested.
If the value is 2 then a persistent connection is required,
i.e., an error occurs in case the server does not grant it.
.PP
\&\fBOSSL_HTTP_exchange()\fR exchanges any form of HTTP request and response
as specified by \fIrctx\fR, which must include both connection and request data,
typically set up using \fBOSSL_HTTP_open()\fR and \fBOSSL_HTTP_set1_request()\fR.
It implements the core of the functions described below.
If the HTTP method is GET and \fIredirection_url\fR
is not NULL the latter pointer is used to provide any new location that
the server may return with HTTP code 301 (MOVED_PERMANENTLY) or 302 (FOUND).
In this case the function returns NULL and the caller is
responsible for deallocating the URL with \fBOPENSSL_free\fR\|(3).
If the response header contains one or more "Content-Length" header lines and/or
an ASN.1\-encoded response is expected, which should include a total length,
the length indications received are checked for consistency
and for not exceeding any given maximum response length.
If an ASN.1\-encoded response is expected, the function returns on success
the contents buffered in a memory BIO, which does not support streaming.
Otherwise it returns directly the read BIO that holds the response contents,
which allows a response of indefinite length and may support streaming.
The caller is responsible for freeing the BIO pointer obtained.
.PP
\&\fBOSSL_HTTP_get()\fR uses HTTP GET to obtain data from \fIbio\fR if non-NULL,
else from the server contained in the \fIurl\fR, and returns it as a BIO.
It supports redirection via HTTP status code 301 or 302.  It is meant for
transfers with a single round trip, so does not support persistent connections.
If \fIbio\fR is non-NULL, any host and port components in the \fIurl\fR are not used
for connecting but the hostname is used, as usual, for the \f(CW\*(C`Host\*(C'\fR header.
Any userinfo and fragment components in the \fIurl\fR are ignored.
Any query component is handled as part of the path component.
If the scheme component of the \fIurl\fR is \f(CW\*(C`https\*(C'\fR a TLS connection is requested
and the \fIbio_update_fn\fR, as described for \fBOSSL_HTTP_open()\fR, must be provided.
Also the remaining parameters are interpreted as described for \fBOSSL_HTTP_open()\fR
and \fBOSSL_HTTP_set1_request()\fR, respectively.
The caller is responsible for freeing the BIO pointer obtained.
.PP
\&\fBOSSL_HTTP_transfer()\fR exchanges an HTTP request and response
over a connection managed via \fIprctx\fR without supporting redirection.
It combines \fBOSSL_HTTP_open()\fR, \fBOSSL_HTTP_set1_request()\fR, \fBOSSL_HTTP_exchange()\fR,
and \fBOSSL_HTTP_close()\fR.
If \fIprctx\fR is not NULL it reuses any open connection represented by a non-NULL
\&\fI*prctx\fR.  It keeps the connection open if a persistent connection is requested
or required and this was granted by the server, else it closes the connection
and assigns NULL to \fI*prctx\fR.
The remaining parameters are interpreted as described for \fBOSSL_HTTP_open()\fR
and \fBOSSL_HTTP_set1_request()\fR, respectively.
The caller is responsible for freeing the BIO pointer obtained.
.PP
\&\fBOSSL_HTTP_close()\fR closes the connection and releases \fIrctx\fR.
The \fIok\fR parameter is passed to any BIO update function
given during setup as described above for \fBOSSL_HTTP_open()\fR.
It must be 1 if no error occurred during the HTTP transfer and 0 otherwise.
.SH NOTES
.IX Header "NOTES"
The names of the environment variables used by this implementation:
\&\f(CW\*(C`http_proxy\*(C'\fR, \f(CW\*(C`HTTP_PROXY\*(C'\fR, \f(CW\*(C`https_proxy\*(C'\fR, \f(CW\*(C`HTTPS_PROXY\*(C'\fR, \f(CW\*(C`no_proxy\*(C'\fR, and
\&\f(CW\*(C`NO_PROXY\*(C'\fR, have been chosen for maximal compatibility with
other HTTP client implementations such as wget, curl, and git.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBOSSL_HTTP_open()\fR returns on success a \fBOSSL_HTTP_REQ_CTX\fR, else NULL.
.PP
\&\fBOSSL_HTTP_proxy_connect()\fR and \fBOSSL_HTTP_set1_request()\fR
return 1 on success, 0 on error.
.PP
On success, \fBOSSL_HTTP_exchange()\fR, \fBOSSL_HTTP_get()\fR, and \fBOSSL_HTTP_transfer()\fR
return a memory BIO that buffers all the data received if an ASN.1\-encoded
response is expected, otherwise a BIO that may support streaming.
The BIO must be freed by the caller.
On failure, they return NULL.
Failure conditions include connection/transfer timeout, parse errors, etc.
The caller is responsible for freeing the BIO pointer obtained.
.PP
\&\fBOSSL_HTTP_close()\fR returns 0 if anything went wrong while disconnecting, else 1.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBOSSL_HTTP_parse_url\fR\|(3), \fBBIO_new_connect\fR\|(3),
\&\fBASN1_item_i2d_mem_bio\fR\|(3), \fBASN1_item_d2i_bio\fR\|(3),
\&\fBOSSL_HTTP_is_alive\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
All the functions described here were added in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2019\-2023 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
