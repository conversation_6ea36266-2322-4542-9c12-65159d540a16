.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "SSL_CTX_SESS_SET_GET_CB 3ossl"
.TH SSL_CTX_SESS_SET_GET_CB 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
SSL_CTX_sess_set_new_cb, SSL_CTX_sess_set_remove_cb, SSL_CTX_sess_set_get_cb, SSL_CTX_sess_get_new_cb, SSL_CTX_sess_get_remove_cb, SSL_CTX_sess_get_get_cb \- provide callback functions for server side external session caching
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/ssl.h>
\&
\& void SSL_CTX_sess_set_new_cb(SSL_CTX *ctx,
\&                              int (*new_session_cb)(SSL *, SSL_SESSION *));
\& void SSL_CTX_sess_set_remove_cb(SSL_CTX *ctx,
\&                                 void (*remove_session_cb)(SSL_CTX *ctx,
\&                                                           SSL_SESSION *));
\& void SSL_CTX_sess_set_get_cb(SSL_CTX *ctx,
\&                              SSL_SESSION (*get_session_cb)(SSL *,
\&                                                            const unsigned char *,
\&                                                            int, int *));
\&
\& int (*SSL_CTX_sess_get_new_cb(SSL_CTX *ctx))(struct ssl_st *ssl,
\&                                              SSL_SESSION *sess);
\& void (*SSL_CTX_sess_get_remove_cb(SSL_CTX *ctx))(struct ssl_ctx_st *ctx,
\&                                                  SSL_SESSION *sess);
\& SSL_SESSION *(*SSL_CTX_sess_get_get_cb(SSL_CTX *ctx))(struct ssl_st *ssl,
\&                                                       const unsigned char *data,
\&                                                       int len, int *copy);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBSSL_CTX_sess_set_new_cb()\fR sets the callback function that is
called whenever a new session was negotiated.
.PP
\&\fBSSL_CTX_sess_set_remove_cb()\fR sets the callback function that is
called whenever a session is removed by the SSL engine.  For example,
this can occur because a session is considered faulty or has become obsolete
because of exceeding the timeout value.
.PP
\&\fBSSL_CTX_sess_set_get_cb()\fR sets the callback function that is called
whenever a TLS client proposed to resume a session but the session
could not be found in the internal session cache (see
\&\fBSSL_CTX_set_session_cache_mode\fR\|(3)).
(TLS server only.)
.PP
\&\fBSSL_CTX_sess_get_new_cb()\fR, \fBSSL_CTX_sess_get_remove_cb()\fR, and
\&\fBSSL_CTX_sess_get_get_cb()\fR retrieve the function pointers set by the
corresponding set callback functions. If a callback function has not been
set, the NULL pointer is returned.
.SH NOTES
.IX Header "NOTES"
In order to allow external session caching, synchronization with the internal
session cache is realized via callback functions. Inside these callback
functions, session can be saved to disk or put into a database using the
\&\fBd2i_SSL_SESSION\fR\|(3) interface.
.PP
The \fBnew_session_cb()\fR is called whenever a new session has been negotiated and
session caching is enabled (see \fBSSL_CTX_set_session_cache_mode\fR\|(3)).  The
\&\fBnew_session_cb()\fR is passed the \fBssl\fR connection and the nascent
ssl session \fBsess\fR.
Since sessions are reference-counted objects, the reference count on the
session is incremented before the callback, on behalf of the application.  If
the callback returns \fB0\fR, the session will be immediately removed from the
internal cache and the reference count released. If the callback returns \fB1\fR,
the application retains the reference (for an entry in the
application-maintained "external session cache"), and is responsible for
calling \fBSSL_SESSION_free()\fR when the session reference is no longer in use.
.PP
Note that in TLSv1.3, sessions are established after the main
handshake has completed. The server decides when to send the client the session
information and this may occur some time after the end of the handshake (or not
at all). This means that applications should expect the \fBnew_session_cb()\fR
function to be invoked during the handshake (for <= TLSv1.2) or after the
handshake (for TLSv1.3). It is also possible in TLSv1.3 for multiple sessions to
be established with a single connection. In these case the \fBnew_session_cb()\fR
function will be invoked multiple times.
.PP
In TLSv1.3 it is recommended that each SSL_SESSION object is only used for
resumption once. One way of enforcing that is for applications to call
\&\fBSSL_CTX_remove_session\fR\|(3) after a session has been used.
.PP
The \fBremove_session_cb()\fR is called whenever the SSL engine removes a session
from the internal cache. This can happen when the session is removed because
it is expired or when a connection was not shutdown cleanly. It also happens
for all sessions in the internal session cache when
\&\fBSSL_CTX_free\fR\|(3) is called. The \fBremove_session_cb()\fR is passed
the \fBctx\fR and the ssl session \fBsess\fR. It does not provide any feedback.
.PP
The \fBget_session_cb()\fR is only called on SSL/TLS servers, and is given
the session id
proposed by the client. The \fBget_session_cb()\fR is always called, even when
session caching was disabled. The \fBget_session_cb()\fR is passed the
\&\fBssl\fR connection and the session id of length \fBlength\fR at the memory location
\&\fBdata\fR. By setting the parameter \fBcopy\fR to \fB1\fR, the callback can require the
SSL engine to increment the reference count of the SSL_SESSION object;
setting \fBcopy\fR to \fB0\fR causes the reference count to remain unchanged.
If the \fBget_session_cb()\fR does not write to \fBcopy\fR, the reference count
is incremented and the session must be explicitly freed with
\&\fBSSL_SESSION_free\fR\|(3).
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBSSL_CTX_sess_get_new_cb()\fR, \fBSSL_CTX_sess_get_remove_cb()\fR and \fBSSL_CTX_sess_get_get_cb()\fR
return different callback function pointers respectively.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBssl\fR\|(7), \fBd2i_SSL_SESSION\fR\|(3),
\&\fBSSL_CTX_set_session_cache_mode\fR\|(3),
\&\fBSSL_CTX_flush_sessions\fR\|(3),
\&\fBSSL_SESSION_free\fR\|(3),
\&\fBSSL_CTX_free\fR\|(3)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2001\-2020 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
