.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "SSL_READ 3ossl"
.TH SSL_READ 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
SSL_read_ex, SSL_read, SSL_peek_ex, SSL_peek
\&\- read bytes from a TLS/SSL connection
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/ssl.h>
\&
\& int SSL_read_ex(SSL *ssl, void *buf, size_t num, size_t *readbytes);
\& int SSL_read(SSL *ssl, void *buf, int num);
\&
\& int SSL_peek_ex(SSL *ssl, void *buf, size_t num, size_t *readbytes);
\& int SSL_peek(SSL *ssl, void *buf, int num);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBSSL_read_ex()\fR and \fBSSL_read()\fR try to read \fBnum\fR bytes from the specified \fBssl\fR
into the buffer \fBbuf\fR. On success \fBSSL_read_ex()\fR will store the number of bytes
actually read in \fB*readbytes\fR.
.PP
\&\fBSSL_peek_ex()\fR and \fBSSL_peek()\fR are identical to \fBSSL_read_ex()\fR and \fBSSL_read()\fR
respectively except no bytes are actually removed from the underlying BIO during
the read, so that a subsequent call to \fBSSL_read_ex()\fR or \fBSSL_read()\fR will yield
at least the same bytes.
.SH NOTES
.IX Header "NOTES"
In the paragraphs below a "read function" is defined as one of \fBSSL_read_ex()\fR,
\&\fBSSL_read()\fR, \fBSSL_peek_ex()\fR or \fBSSL_peek()\fR.
.PP
If necessary, a read function will negotiate a TLS/SSL session, if not already
explicitly performed by \fBSSL_connect\fR\|(3) or \fBSSL_accept\fR\|(3). If the
peer requests a re-negotiation, it will be performed transparently during
the read function operation. The behaviour of the read functions depends on the
underlying BIO.
.PP
For the transparent negotiation to succeed, the \fBssl\fR must have been
initialized to client or server mode. This is being done by calling
\&\fBSSL_set_connect_state\fR\|(3) or \fBSSL_set_accept_state()\fR before the first
invocation of a read function.
.PP
The read functions work based on the SSL/TLS records. The data are received in
records (with a maximum record size of 16kB). Only when a record has been
completely received, can it be processed (decryption and check of integrity).
Therefore, data that was not retrieved at the last read call can still be
buffered inside the SSL layer and will be retrieved on the next read
call. If \fBnum\fR is higher than the number of bytes buffered then the read
functions will return with the bytes buffered. If no more bytes are in the
buffer, the read functions will trigger the processing of the next record.
Only when the record has been received and processed completely will the read
functions return reporting success. At most the contents of one record will
be returned. As the size of an SSL/TLS record may exceed the maximum packet size
of the underlying transport (e.g. TCP), it may be necessary to read several
packets from the transport layer before the record is complete and the read call
can succeed.
.PP
If \fBSSL_MODE_AUTO_RETRY\fR has been switched off and a non-application data
record has been processed, the read function can return and set the error to
\&\fBSSL_ERROR_WANT_READ\fR.
In this case there might still be unprocessed data available in the \fBBIO\fR.
If read ahead was set using \fBSSL_CTX_set_read_ahead\fR\|(3), there might also still
be unprocessed data available in the \fBSSL\fR.
This behaviour can be controlled using the \fBSSL_CTX_set_mode\fR\|(3) call.
.PP
If the underlying BIO is \fBblocking\fR, a read function will only return once the
read operation has been finished or an error occurred, except when a
non-application data record has been processed and \fBSSL_MODE_AUTO_RETRY\fR is
not set.
Note that if \fBSSL_MODE_AUTO_RETRY\fR is set and only non-application data is
available the call will hang.
.PP
If the underlying BIO is \fBnonblocking\fR, a read function will also return when
the underlying BIO could not satisfy the needs of the function to continue the
operation.
In this case a call to \fBSSL_get_error\fR\|(3) with the
return value of the read function will yield \fBSSL_ERROR_WANT_READ\fR or
\&\fBSSL_ERROR_WANT_WRITE\fR.
As at any time it's possible that non-application data needs to be sent,
a read function can also cause write operations.
The calling process then must repeat the call after taking appropriate action
to satisfy the needs of the read function.
The action depends on the underlying BIO.
When using a nonblocking socket, nothing is to be done, but \fBselect()\fR can be
used to check for the required condition.
When using a buffering BIO, like a BIO pair, data must be written into or
retrieved out of the BIO before being able to continue.
.PP
\&\fBSSL_pending\fR\|(3) can be used to find out whether there
are buffered bytes available for immediate retrieval.
In this case the read function can be called without blocking or actually
receiving new data from the underlying socket.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBSSL_read_ex()\fR and \fBSSL_peek_ex()\fR will return 1 for success or 0 for failure.
Success means that 1 or more application data bytes have been read from the SSL
connection.
Failure means that no bytes could be read from the SSL connection.
Failures can be retryable (e.g. we are waiting for more bytes to
be delivered by the network) or non-retryable (e.g. a fatal network error).
In the event of a failure call \fBSSL_get_error\fR\|(3) to find out the reason which
indicates whether the call is retryable or not.
.PP
For \fBSSL_read()\fR and \fBSSL_peek()\fR the following return values can occur:
.IP "> 0" 4
.IX Item "> 0"
The read operation was successful.
The return value is the number of bytes actually read from the TLS/SSL
connection.
.IP "<= 0" 4
.IX Item "<= 0"
The read operation was not successful, because either the connection was closed,
an error occurred or action must be taken by the calling process.
Call \fBSSL_get_error\fR\|(3) with the return value \fBret\fR to find out the reason.
.Sp
Old documentation indicated a difference between 0 and \-1, and that \-1 was
retryable.
You should instead call \fBSSL_get_error()\fR to find out if it's retryable.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBSSL_get_error\fR\|(3), \fBSSL_write_ex\fR\|(3),
\&\fBSSL_CTX_set_mode\fR\|(3), \fBSSL_CTX_new\fR\|(3),
\&\fBSSL_connect\fR\|(3), \fBSSL_accept\fR\|(3)
\&\fBSSL_set_connect_state\fR\|(3),
\&\fBSSL_pending\fR\|(3),
\&\fBSSL_shutdown\fR\|(3), \fBSSL_set_shutdown\fR\|(3),
\&\fBssl\fR\|(7), \fBbio\fR\|(7)
.SH HISTORY
.IX Header "HISTORY"
The \fBSSL_read_ex()\fR and \fBSSL_peek_ex()\fR functions were added in OpenSSL 1.1.1.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2020 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
