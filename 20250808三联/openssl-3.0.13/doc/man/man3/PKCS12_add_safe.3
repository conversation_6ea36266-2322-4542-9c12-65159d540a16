.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "PKCS12_ADD_SAFE 3ossl"
.TH PKCS12_ADD_SAFE 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
PKCS12_add_safe, PKCS12_add_safe_ex,
PKCS12_add_safes, PKCS12_add_safes_ex \- Create and add objects to a PKCS#12 structure
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/pkcs12.h>
\&
\& int PKCS12_add_safe(STACK_OF(PKCS7) **psafes, STACK_OF(PKCS12_SAFEBAG) *bags,
\&                    int safe_nid, int iter, const char *pass);
\& int PKCS12_add_safe_ex(STACK_OF(PKCS7) **psafes, STACK_OF(PKCS12_SAFEBAG) *bags,
\&                        int safe_nid, int iter, const char *pass,
\&                        OSSL_LIB_CTX *ctx, const char *propq);
\&
\& PKCS12 *PKCS12_add_safes(STACK_OF(PKCS7) *safes, int p7_nid);
\& PKCS12 *PKCS12_add_safes_ex(STACK_OF(PKCS7) *safes, int p7_nid,
\&                             OSSL_LIB_CTX *ctx, const char *propq);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBPKCS12_add_safe()\fR creates a new PKCS7 contentInfo containing the supplied
\&\fBPKCS12_SAFEBAG\fRs and adds this to a set of PKCS7 contentInfos. Its type
depends on the value of \fBsafe_nid\fR:
.IP \(bu 4
If \fIsafe_nid\fR is \-1, a plain PKCS7 \fIdata\fR contentInfo is created.
.IP \(bu 4
If \fIsafe_nid\fR is a valid PBE algorithm NID, a PKCS7 \fBencryptedData\fR
contentInfo is created. The algorithm uses \fIpass\fR as the passphrase and \fIiter\fR
as the iteration count. If \fIiter\fR is zero then a default value for iteration
count of 2048 is used.
.IP \(bu 4
If \fIsafe_nid\fR is 0, a PKCS7 \fBencryptedData\fR contentInfo is created using
a default encryption algorithm, currently \fBNID_pbe_WithSHA1And3_Key_TripleDES_CBC\fR.
.PP
\&\fBPKCS12_add_safe_ex()\fR is identical to \fBPKCS12_add_safe()\fR but allows for a library
context \fIctx\fR and property query \fIpropq\fR to be used to select algorithm
implementations.
.PP
\&\fBPKCS12_add_safes()\fR creates a \fBPKCS12\fR structure containing the supplied set of
PKCS7 contentInfos. The \fIsafes\fR are enclosed first within a PKCS7 contentInfo
of type \fIp7_nid\fR. Currently the only supported type is \fBNID_pkcs7_data\fR.
.PP
\&\fBPKCS12_add_safes_ex()\fR is identical to \fBPKCS12_add_safes()\fR but allows for a
library context \fIctx\fR and property query \fIpropq\fR to be used to select
algorithm implementations.
.SH NOTES
.IX Header "NOTES"
\&\fBPKCS12_add_safe()\fR makes assumptions regarding the encoding of the given pass
phrase.
See \fBpassphrase\-encoding\fR\|(7) for more information.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBPKCS12_add_safe()\fR returns a value of 1 indicating success or 0 for failure.
.PP
\&\fBPKCS12_add_safes()\fR returns a valid \fBPKCS12\fR structure or NULL if an error occurred.
.SH "CONFORMING TO"
.IX Header "CONFORMING TO"
IETF RFC 7292 (<https://tools.ietf.org/html/rfc7292>)
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBPKCS12_create\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
\&\fBPKCS12_add_safe_ex()\fR and \fBPKCS12_add_safes_ex()\fR were added in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2020\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
