.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "PKCS7_SIGN 3ossl"
.TH PKCS7_SIGN 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
PKCS7_sign_ex, PKCS7_sign
\&\- create a PKCS#7 signedData structure
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/pkcs7.h>
\&
\& PKCS7 *PKCS7_sign_ex(X509 *signcert, EVP_PKEY *pkey, STACK_OF(X509) *certs,
\&                      BIO *data, int flags, OSSL_LIB_CTX *libctx,
\&                      const char *propq);
\& PKCS7 *PKCS7_sign(X509 *signcert, EVP_PKEY *pkey, STACK_OF(X509) *certs,
\&                   BIO *data, int flags);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBPKCS7_sign_ex()\fR creates and returns a PKCS#7 signedData structure.
\&\fIsigncert\fR is the certificate to sign with, \fIpkey\fR is the corresponding
private key. \fIcerts\fR is an optional set of extra certificates to include
in the PKCS#7 structure (for example any intermediate CAs in the chain).
The library context \fIlibctx\fR and property query \fIpropq\fR are used when
retrieving algorithms from providers.
.PP
The data to be signed is read from BIO \fIdata\fR.
.PP
\&\fIflags\fR is an optional set of flags.
.PP
Any of the following flags (ored together) can be passed in the \fIflags\fR
parameter.
.PP
Many S/MIME clients expect the signed content to include valid MIME headers. If
the \fBPKCS7_TEXT\fR flag is set MIME headers for type \f(CW\*(C`text/plain\*(C'\fR are prepended
to the data.
.PP
If \fBPKCS7_NOCERTS\fR is set the signer's certificate and the extra \fIcerts\fR
will not be included in the PKCS7 structure.
The signer's certificate must still be supplied in the \fIsigncert\fR parameter
though. This can reduce the size of the signatures if the signer's certificates
can be obtained by other means: for example a previously signed message.
.PP
The data being signed is included in the PKCS7 structure, unless
\&\fBPKCS7_DETACHED\fR is set in which case it is omitted. This is used for PKCS7
detached signatures which are used in S/MIME plaintext signed messages for
example.
.PP
Normally the supplied content is translated into MIME canonical format (as
required by the S/MIME specifications) if \fBPKCS7_BINARY\fR is set no translation
occurs. This option should be used if the supplied data is in binary format
otherwise the translation will corrupt it.
.PP
The signedData structure includes several PKCS#7 authenticatedAttributes
including the signing time, the PKCS#7 content type and the supported list of
ciphers in an SMIMECapabilities attribute. If \fBPKCS7_NOATTR\fR is set then no
authenticatedAttributes will be used. If \fBPKCS7_NOSMIMECAP\fR is set then just
the SMIMECapabilities are omitted.
.PP
If present the SMIMECapabilities attribute indicates support for the following
algorithms: triple DES, 128 bit RC2, 64 bit RC2, DES and 40 bit RC2. If any of
these algorithms is disabled then it will not be included.
.PP
If the flags \fBPKCS7_STREAM\fR is set then the returned \fBPKCS7\fR structure is
just initialized ready to perform the signing operation. The signing is however
\&\fBnot\fR performed and the data to be signed is not read from the \fIdata\fR
parameter. Signing is deferred until after the data has been written. In this
way data can be signed in a single pass.
.PP
If the \fBPKCS7_PARTIAL\fR flag is set a partial \fBPKCS7\fR structure is output to
which additional signers and capabilities can be added before finalization.
.PP
If the flag \fBPKCS7_STREAM\fR is set the returned \fBPKCS7\fR structure is \fBnot\fR
complete and outputting its contents via a function that does not properly
finalize the \fBPKCS7\fR structure will give unpredictable results.
.PP
Several functions including \fBSMIME_write_PKCS7()\fR, \fBi2d_PKCS7_bio_stream()\fR,
\&\fBPEM_write_bio_PKCS7_stream()\fR finalize the structure. Alternatively finalization
can be performed by obtaining the streaming ASN1 \fBBIO\fR directly using
\&\fBBIO_new_PKCS7()\fR.
.PP
If a signer is specified it will use the default digest for the signing
algorithm. This is \fBSHA1\fR for both RSA and DSA keys.
.PP
The \fIcerts\fR, \fIsigncert\fR and \fIpkey\fR parameters can all be
NULL if the \fBPKCS7_PARTIAL\fR flag is set. One or more signers can be added
using the function \fBPKCS7_sign_add_signer()\fR. \fBPKCS7_final()\fR must also be
called to finalize the structure if streaming is not enabled. Alternative
signing digests can also be specified using this method.
.PP
If \fIsigncert\fR and \fIpkey\fR are NULL then a certificates only
PKCS#7 structure is output.
.PP
In versions of OpenSSL before 1.0.0 the \fIsigncert\fR and \fIpkey\fR parameters must
not be NULL.
.PP
\&\fBPKCS7_sign()\fR is like \fBPKCS7_sign_ex()\fR except that it uses default values of
NULL for the library context \fIlibctx\fR and the property query \fIpropq\fR.
This is retained for API backward compatibility.
.SH BUGS
.IX Header "BUGS"
Some advanced attributes such as counter signatures are not supported.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBPKCS7_sign_ex()\fR and \fBPKCS7_sign()\fR return either a valid PKCS7 structure
or NULL if an error occurred.  The error can be obtained from \fBERR_get_error\fR\|(3).
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBERR_get_error\fR\|(3), \fBPKCS7_verify\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
The function \fBPKCS7_sign_ex()\fR was added in OpenSSL 3.0.
.PP
The \fBPKCS7_PARTIAL\fR flag, and the ability for \fIcerts\fR, \fIsigncert\fR,
and \fIpkey\fR parameters to be NULL were added in OpenSSL 1.0.0.
.PP
The \fBPKCS7_STREAM\fR flag was added in OpenSSL 1.0.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2002\-2023 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
