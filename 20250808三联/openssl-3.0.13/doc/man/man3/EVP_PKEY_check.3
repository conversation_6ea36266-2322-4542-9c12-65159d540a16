.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "EVP_PKEY_CHECK 3ossl"
.TH EVP_PKEY_CHECK 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
EVP_PKEY_check, EVP_PKEY_param_check, EVP_PKEY_param_check_quick,
EVP_PKEY_public_check, EVP_PKEY_public_check_quick, EVP_PKEY_private_check,
EVP_PKEY_pairwise_check
\&\- key and parameter validation functions
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/evp.h>
\&
\& int EVP_PKEY_check(EVP_PKEY_CTX *ctx);
\& int EVP_PKEY_param_check(EVP_PKEY_CTX *ctx);
\& int EVP_PKEY_param_check_quick(EVP_PKEY_CTX *ctx);
\& int EVP_PKEY_public_check(EVP_PKEY_CTX *ctx);
\& int EVP_PKEY_public_check_quick(EVP_PKEY_CTX *ctx);
\& int EVP_PKEY_private_check(EVP_PKEY_CTX *ctx);
\& int EVP_PKEY_pairwise_check(EVP_PKEY_CTX *ctx);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBEVP_PKEY_param_check()\fR validates the parameters component of the key
given by \fBctx\fR. This check will always succeed for key types that do not have
parameters.
.PP
\&\fBEVP_PKEY_param_check_quick()\fR validates the parameters component of the key
given by \fBctx\fR like \fBEVP_PKEY_param_check()\fR does. However some algorithm
implementations may offer a quicker form of validation that omits some checks in
order to perform a lightweight sanity check of the key. If a quicker form is not
provided then this function call does the same thing as \fBEVP_PKEY_param_check()\fR.
.PP
\&\fBEVP_PKEY_public_check()\fR validates the public component of the key given by \fBctx\fR.
.PP
\&\fBEVP_PKEY_public_check_quick()\fR validates the public component of the key
given by \fBctx\fR like \fBEVP_PKEY_public_check()\fR does. However some algorithm
implementations may offer a quicker form of validation that omits some checks in
order to perform a lightweight sanity check of the key. If a quicker form is not
provided then this function call does the same thing as \fBEVP_PKEY_public_check()\fR.
.PP
\&\fBEVP_PKEY_private_check()\fR validates the private component of the key given by \fBctx\fR.
.PP
\&\fBEVP_PKEY_pairwise_check()\fR validates that the public and private components have
the correct mathematical relationship to each other for the key given by \fBctx\fR.
.PP
\&\fBEVP_PKEY_check()\fR is an alias for the \fBEVP_PKEY_pairwise_check()\fR function.
.SH NOTES
.IX Header "NOTES"
Key validation used by the OpenSSL FIPS provider complies with the rules
within SP800\-56A and SP800\-56B. For backwards compatibility reasons the OpenSSL
default provider may use checks that are not as restrictive for certain key types.
For further information see "DSA key validation" in \fBEVP_PKEY\-DSA\fR\|(7),
"DH key validation" in \fBEVP_PKEY\-DH\fR\|(7), "EC key validation" in \fBEVP_PKEY\-EC\fR\|(7) and
"RSA key validation" in \fBEVP_PKEY\-RSA\fR\|(7).
.PP
Refer to SP800\-56A and SP800\-56B for rules relating to when these functions
should be called during key establishment.
It is not necessary to call these functions after locally calling an approved key
generation method, but may be required for assurance purposes when receiving
keys from a third party.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
All functions return 1 for success or others for failure.
They return \-2 if the operation is not supported for the specific algorithm.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBEVP_PKEY_CTX_new\fR\|(3),
\&\fBEVP_PKEY_fromdata\fR\|(3),
\&\fBEVP_PKEY\-DH\fR\|(7),
\&\fBEVP_PKEY\-FFC\fR\|(7),
\&\fBEVP_PKEY\-DSA\fR\|(7),
\&\fBEVP_PKEY\-EC\fR\|(7),
\&\fBEVP_PKEY\-RSA\fR\|(7),
.SH HISTORY
.IX Header "HISTORY"
\&\fBEVP_PKEY_check()\fR, \fBEVP_PKEY_public_check()\fR and \fBEVP_PKEY_param_check()\fR were added
in OpenSSL 1.1.1.
.PP
\&\fBEVP_PKEY_param_check_quick()\fR, \fBEVP_PKEY_public_check_quick()\fR,
\&\fBEVP_PKEY_private_check()\fR and \fBEVP_PKEY_pairwise_check()\fR were added in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2006\-2022 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
