.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "SSL_CTX_SET_RECORD_PADDING_CALLBACK 3ossl"
.TH SSL_CTX_SET_RECORD_PADDING_CALLBACK 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
SSL_CTX_set_record_padding_callback,
SSL_set_record_padding_callback,
SSL_CTX_set_record_padding_callback_arg,
SSL_set_record_padding_callback_arg,
SSL_CTX_get_record_padding_callback_arg,
SSL_get_record_padding_callback_arg,
SSL_CTX_set_block_padding,
SSL_set_block_padding \- install callback to specify TLS 1.3 record padding
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/ssl.h>
\&
\& void SSL_CTX_set_record_padding_callback(SSL_CTX *ctx, size_t (*cb)(SSL *s, int type, size_t len, void *arg));
\& int SSL_set_record_padding_callback(SSL *ssl, size_t (*cb)(SSL *s, int type, size_t len, void *arg));
\&
\& void SSL_CTX_set_record_padding_callback_arg(SSL_CTX *ctx, void *arg);
\& void *SSL_CTX_get_record_padding_callback_arg(const SSL_CTX *ctx);
\&
\& void SSL_set_record_padding_callback_arg(SSL *ssl, void *arg);
\& void *SSL_get_record_padding_callback_arg(const SSL *ssl);
\&
\& int SSL_CTX_set_block_padding(SSL_CTX *ctx, size_t block_size);
\& int SSL_set_block_padding(SSL *ssl, size_t block_size);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBSSL_CTX_set_record_padding_callback()\fR or \fBSSL_set_record_padding_callback()\fR
can be used to assign a callback function \fIcb\fR to specify the padding
for TLS 1.3 records. The value set in \fBctx\fR is copied to a new SSL by \fBSSL_new()\fR.
Kernel TLS is not possible if the record padding callback is set, and the callback
function cannot be set if Kernel TLS is already configured for the current SSL object.
.PP
\&\fBSSL_CTX_set_record_padding_callback_arg()\fR and \fBSSL_set_record_padding_callback_arg()\fR
assign a value \fBarg\fR that is passed to the callback when it is invoked. The value
set in \fBctx\fR is copied to a new SSL by \fBSSL_new()\fR.
.PP
\&\fBSSL_CTX_get_record_padding_callback_arg()\fR and \fBSSL_get_record_padding_callback_arg()\fR
retrieve the \fBarg\fR value that is passed to the callback.
.PP
\&\fBSSL_CTX_set_block_padding()\fR and \fBSSL_set_block_padding()\fR pads the record to a multiple
of the \fBblock_size\fR. A \fBblock_size\fR of 0 or 1 disables block padding. The limit of
\&\fBblock_size\fR is SSL3_RT_MAX_PLAIN_LENGTH.
.PP
The callback is invoked for every record before encryption.
The \fBtype\fR parameter is the TLS record type that is being processed; may be
one of SSL3_RT_APPLICATION_DATA, SSL3_RT_HANDSHAKE, or SSL3_RT_ALERT.
The \fBlen\fR parameter is the current plaintext length of the record before encryption.
The \fBarg\fR parameter is the value set via \fBSSL_CTX_set_record_padding_callback_arg()\fR
or \fBSSL_set_record_padding_callback_arg()\fR.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
The \fBSSL_CTX_get_record_padding_callback_arg()\fR and \fBSSL_get_record_padding_callback_arg()\fR
functions return the \fBarg\fR value assigned in the corresponding set functions.
.PP
The \fBSSL_CTX_set_block_padding()\fR and \fBSSL_set_block_padding()\fR functions return 1 on success
or 0 if \fBblock_size\fR is too large.
.PP
The \fBcb\fR returns the number of padding bytes to add to the record. A return of 0
indicates no padding will be added. A return value that causes the record to
exceed the maximum record size (SSL3_RT_MAX_PLAIN_LENGTH) will pad out to the
maximum record size.
.PP
The \fBSSL_CTX_get_record_padding_callback_arg()\fR function returns 1 on success or 0 if
the callback function is not set because Kernel TLS is configured for the SSL object.
.SH NOTES
.IX Header "NOTES"
The default behavior is to add no padding to the record.
.PP
A user-supplied padding callback function will override the behavior set by
\&\fBSSL_set_block_padding()\fR or \fBSSL_CTX_set_block_padding()\fR. Setting the user-supplied
callback to NULL will restore the configured block padding behavior.
.PP
These functions only apply to TLS 1.3 records being written.
.PP
Padding bytes are not added in constant-time.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBssl\fR\|(7), \fBSSL_new\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
The record padding API was added for TLS 1.3 support in OpenSSL 1.1.1.
.PP
The return type of \fBSSL_CTX_set_record_padding_callback()\fR function was
changed to int in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2017\-2020 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
