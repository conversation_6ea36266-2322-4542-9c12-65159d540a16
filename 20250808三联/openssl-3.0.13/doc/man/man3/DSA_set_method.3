.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "DSA_SET_METHOD 3ossl"
.TH DSA_SET_METHOD 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
DSA_set_default_method, DSA_get_default_method,
DSA_set_method, DSA_new_method, DSA_OpenSSL \- select DSA method
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/dsa.h>
.Ve
.PP
The following functions have been deprecated since OpenSSL 3.0, and can be
hidden entirely by defining \fBOPENSSL_API_COMPAT\fR with a suitable version value,
see \fBopenssl_user_macros\fR\|(7):
.PP
.Vb 1
\& void DSA_set_default_method(const DSA_METHOD *meth);
\&
\& const DSA_METHOD *DSA_get_default_method(void);
\&
\& int DSA_set_method(DSA *dsa, const DSA_METHOD *meth);
\&
\& DSA *DSA_new_method(ENGINE *engine);
\&
\& const DSA_METHOD *DSA_OpenSSL(void);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
All of the functions described on this page are deprecated.
Applications should providers instead of method overrides.
.PP
A \fBDSA_METHOD\fR specifies the functions that OpenSSL uses for DSA
operations. By modifying the method, alternative implementations
such as hardware accelerators may be used. IMPORTANT: See the NOTES section for
important information about how these DSA API functions are affected by the use
of \fBENGINE\fR API calls.
.PP
Initially, the default DSA_METHOD is the OpenSSL internal implementation,
as returned by \fBDSA_OpenSSL()\fR.
.PP
\&\fBDSA_set_default_method()\fR makes \fBmeth\fR the default method for all DSA
structures created later.
\&\fBNB\fR: This is true only whilst no ENGINE has
been set as a default for DSA, so this function is no longer recommended.
This function is not thread-safe and should not be called at the same time
as other OpenSSL functions.
.PP
\&\fBDSA_get_default_method()\fR returns a pointer to the current default
DSA_METHOD. However, the meaningfulness of this result is dependent on
whether the ENGINE API is being used, so this function is no longer
recommended.
.PP
\&\fBDSA_set_method()\fR selects \fBmeth\fR to perform all operations using the key
\&\fBrsa\fR. This will replace the DSA_METHOD used by the DSA key and if the
previous method was supplied by an ENGINE, the handle to that ENGINE will
be released during the change. It is possible to have DSA keys that only
work with certain DSA_METHOD implementations (e.g. from an ENGINE module
that supports embedded hardware-protected keys), and in such cases
attempting to change the DSA_METHOD for the key can have unexpected
results. See \fBDSA_meth_new\fR\|(3) for information on constructing custom DSA_METHOD
objects;
.PP
\&\fBDSA_new_method()\fR allocates and initializes a DSA structure so that \fBengine\fR
will be used for the DSA operations. If \fBengine\fR is NULL, the default engine
for DSA operations is used, and if no default ENGINE is set, the DSA_METHOD
controlled by \fBDSA_set_default_method()\fR is used.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBDSA_OpenSSL()\fR and \fBDSA_get_default_method()\fR return pointers to the respective
\&\fBDSA_METHOD\fRs.
.PP
\&\fBDSA_set_default_method()\fR returns no value.
.PP
\&\fBDSA_set_method()\fR returns nonzero if the provided \fBmeth\fR was successfully set as
the method for \fBdsa\fR (including unloading the ENGINE handle if the previous
method was supplied by an ENGINE).
.PP
\&\fBDSA_new_method()\fR returns NULL and sets an error code that can be
obtained by \fBERR_get_error\fR\|(3) if the allocation
fails. Otherwise it returns a pointer to the newly allocated structure.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBDSA_new\fR\|(3), \fBDSA_new\fR\|(3), \fBDSA_meth_new\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
All of these functions were deprecated in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
