.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "CMS_GET0_TYPE 3ossl"
.TH CMS_GET0_TYPE 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
CMS_get0_type, CMS_set1_eContentType, CMS_get0_eContentType, CMS_get0_content \- get and set CMS content types and content
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/cms.h>
\&
\& const ASN1_OBJECT *CMS_get0_type(const CMS_ContentInfo *cms);
\& int CMS_set1_eContentType(CMS_ContentInfo *cms, const ASN1_OBJECT *oid);
\& const ASN1_OBJECT *CMS_get0_eContentType(CMS_ContentInfo *cms);
\& ASN1_OCTET_STRING **CMS_get0_content(CMS_ContentInfo *cms);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBCMS_get0_type()\fR returns the content type of a CMS_ContentInfo structure as
an ASN1_OBJECT pointer. An application can then decide how to process the
CMS_ContentInfo structure based on this value.
.PP
\&\fBCMS_set1_eContentType()\fR sets the embedded content type of a CMS_ContentInfo
structure. It should be called with CMS functions (such as \fBCMS_sign\fR\|(3),
\&\fBCMS_encrypt\fR\|(3))
with the \fBCMS_PARTIAL\fR
flag and \fBbefore\fR the structure is finalised, otherwise the results are
undefined.
.PP
ASN1_OBJECT *\fBCMS_get0_eContentType()\fR returns a pointer to the embedded
content type.
.PP
\&\fBCMS_get0_content()\fR returns a pointer to the \fBASN1_OCTET_STRING\fR pointer
containing the embedded content.
.SH NOTES
.IX Header "NOTES"
As the \fB0\fR implies \fBCMS_get0_type()\fR, \fBCMS_get0_eContentType()\fR and
\&\fBCMS_get0_content()\fR return internal pointers which should \fBnot\fR be freed up.
\&\fBCMS_set1_eContentType()\fR copies the supplied OID and it \fBshould\fR be freed up
after use.
.PP
The \fBASN1_OBJECT\fR values returned can be converted to an integer \fBNID\fR value
using \fBOBJ_obj2nid()\fR. For the currently supported content types the following
values are returned:
.PP
.Vb 6
\& NID_pkcs7_data
\& NID_pkcs7_signed
\& NID_pkcs7_digest
\& NID_id_smime_ct_compressedData:
\& NID_pkcs7_encrypted
\& NID_pkcs7_enveloped
.Ve
.PP
The return value of \fBCMS_get0_content()\fR is a pointer to the \fBASN1_OCTET_STRING\fR
content pointer. That means that for example:
.PP
.Vb 1
\& ASN1_OCTET_STRING **pconf = CMS_get0_content(cms);
.Ve
.PP
\&\fB*pconf\fR could be NULL if there is no embedded content. Applications can
access, modify or create the embedded content in a \fBCMS_ContentInfo\fR structure
using this function. Applications usually will not need to modify the
embedded content as it is normally set by higher level functions.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBCMS_get0_type()\fR and \fBCMS_get0_eContentType()\fR return an ASN1_OBJECT structure.
.PP
\&\fBCMS_set1_eContentType()\fR returns 1 for success or 0 if an error occurred.  The
error can be obtained from \fBERR_get_error\fR\|(3).
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBERR_get_error\fR\|(3)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2008\-2016 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
