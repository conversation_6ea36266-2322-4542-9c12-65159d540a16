.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "OSSL_CMP_STATUSINFO_NEW 3ossl"
.TH OSSL_CMP_STATUSINFO_NEW 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
OSSL_CMP_STATUSINFO_new,
OSSL_CMP_snprint_PKIStatusInfo,
OSSL_CMP_CTX_snprint_PKIStatus
\&\- function(s) for managing the CMP PKIStatus
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/cmp.h>
\&
\& OSSL_CMP_PKISI *OSSL_CMP_STATUSINFO_new(int status, int fail_info,
\&                                         const char *text);
\& char *OSSL_CMP_snprint_PKIStatusInfo(const OSSL_CMP_PKISI *statusInfo,
\&                                      char *buf, size_t bufsize);
\& char *OSSL_CMP_CTX_snprint_PKIStatus(const OSSL_CMP_CTX *ctx, char *buf,
\&                                      size_t bufsize);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
This is the PKIStatus API for using CMP (Certificate Management Protocol) with
OpenSSL.
.PP
\&\fBOSSL_CMP_STATUSINFO_new()\fR creates a new PKIStatusInfo structure
and fills in the given values.
It sets the status field to \fIstatus\fR,
copies \fItext\fR (unless it is NULL) to statusString,
and interprets \fIfail_info\fR as bit pattern for the failInfo field.
.PP
\&\fBOSSL_CMP_snprint_PKIStatusInfo()\fR places a human-readable string
representing the given statusInfo
in the given buffer, with the given maximal length.
.PP
\&\fBOSSL_CMP_CTX_snprint_PKIStatus()\fR places a human-readable string
representing the PKIStatusInfo components of the CMP context \fIctx\fR
in the given buffer, with the given maximal length.
.SH NOTES
.IX Header "NOTES"
CMP is defined in RFC 4210 (and CRMF in RFC 4211).
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBOSSL_CMP_STATUSINFO_new()\fR
returns a pointer to the structure on success, or NULL on error.
.PP
\&\fBOSSL_CMP_snprint_PKIStatusInfo()\fR and
\&\fBOSSL_CMP_CTX_snprint_PKIStatus()\fR
return a copy of the buffer pointer containing the string or NULL on error.
.SH HISTORY
.IX Header "HISTORY"
The OpenSSL CMP support was added in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2007\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
