.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "BIO_S_FILE 3ossl"
.TH BIO_S_FILE 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
BIO_s_file, BIO_new_file, BIO_new_fp, BIO_set_fp, BIO_get_fp,
BIO_read_filename, BIO_write_filename, BIO_append_filename,
BIO_rw_filename \- FILE bio
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/bio.h>
\&
\& const BIO_METHOD *BIO_s_file(void);
\& BIO *BIO_new_file(const char *filename, const char *mode);
\& BIO *BIO_new_fp(FILE *stream, int flags);
\&
\& BIO_set_fp(BIO *b, FILE *fp, int flags);
\& BIO_get_fp(BIO *b, FILE **fpp);
\&
\& int BIO_read_filename(BIO *b, char *name);
\& int BIO_write_filename(BIO *b, char *name);
\& int BIO_append_filename(BIO *b, char *name);
\& int BIO_rw_filename(BIO *b, char *name);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBBIO_s_file()\fR returns the BIO file method. As its name implies it
is a wrapper round the stdio FILE structure and it is a
source/sink BIO.
.PP
Calls to \fBBIO_read_ex()\fR and \fBBIO_write_ex()\fR read and write data to the
underlying stream. \fBBIO_gets()\fR and \fBBIO_puts()\fR are supported on file BIOs.
.PP
\&\fBBIO_flush()\fR on a file BIO calls the \fBfflush()\fR function on the wrapped
stream.
.PP
\&\fBBIO_reset()\fR attempts to change the file pointer to the start of file
using fseek(stream, 0, 0).
.PP
\&\fBBIO_seek()\fR sets the file pointer to position \fBofs\fR from start of file
using fseek(stream, ofs, 0).
.PP
\&\fBBIO_eof()\fR calls \fBfeof()\fR.
.PP
Setting the BIO_CLOSE flag calls \fBfclose()\fR on the stream when the BIO
is freed.
.PP
\&\fBBIO_new_file()\fR creates a new file BIO with mode \fBmode\fR the meaning
of \fBmode\fR is the same as the stdio function \fBfopen()\fR. The BIO_CLOSE
flag is set on the returned BIO.
.PP
\&\fBBIO_new_fp()\fR creates a file BIO wrapping \fBstream\fR. Flags can be:
BIO_CLOSE, BIO_NOCLOSE (the close flag) BIO_FP_TEXT (sets the underlying
stream to text mode, default is binary: this only has any effect under
Win32).
.PP
\&\fBBIO_set_fp()\fR sets the fp of a file BIO to \fBfp\fR. \fBflags\fR has the same
meaning as in \fBBIO_new_fp()\fR, it is a macro.
.PP
\&\fBBIO_get_fp()\fR retrieves the fp of a file BIO, it is a macro.
.PP
\&\fBBIO_seek()\fR is a macro that sets the position pointer to \fBoffset\fR bytes
from the start of file.
.PP
\&\fBBIO_tell()\fR returns the value of the position pointer.
.PP
\&\fBBIO_read_filename()\fR, \fBBIO_write_filename()\fR, \fBBIO_append_filename()\fR and
\&\fBBIO_rw_filename()\fR set the file BIO \fBb\fR to use file \fBname\fR for
reading, writing, append or read write respectively.
.SH NOTES
.IX Header "NOTES"
When wrapping stdout, stdin or stderr the underlying stream should not
normally be closed so the BIO_NOCLOSE flag should be set.
.PP
Because the file BIO calls the underlying stdio functions any quirks
in stdio behaviour will be mirrored by the corresponding BIO.
.PP
On Windows BIO_new_files reserves for the filename argument to be
UTF\-8 encoded. In other words if you have to make it work in multi\-
lingual environment, encode filenames in UTF\-8.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBBIO_s_file()\fR returns the file BIO method.
.PP
\&\fBBIO_new_file()\fR and \fBBIO_new_fp()\fR return a file BIO or NULL if an error
occurred.
.PP
\&\fBBIO_set_fp()\fR and \fBBIO_get_fp()\fR return 1 for success or <=0 for failure
(although the current implementation never return 0).
.PP
\&\fBBIO_seek()\fR returns 0 for success or negative values for failure.
.PP
\&\fBBIO_tell()\fR returns the current file position or negative values for failure.
.PP
\&\fBBIO_read_filename()\fR, \fBBIO_write_filename()\fR, \fBBIO_append_filename()\fR and
\&\fBBIO_rw_filename()\fR return 1 for success or <=0 for failure.
.SH EXAMPLES
.IX Header "EXAMPLES"
File BIO "hello world":
.PP
.Vb 1
\& BIO *bio_out;
\&
\& bio_out = BIO_new_fp(stdout, BIO_NOCLOSE);
\& BIO_printf(bio_out, "Hello World\en");
.Ve
.PP
Alternative technique:
.PP
.Vb 1
\& BIO *bio_out;
\&
\& bio_out = BIO_new(BIO_s_file());
\& if (bio_out == NULL)
\&     /* Error */
\& if (BIO_set_fp(bio_out, stdout, BIO_NOCLOSE) <= 0)
\&     /* Error */
\& BIO_printf(bio_out, "Hello World\en");
.Ve
.PP
Write to a file:
.PP
.Vb 1
\& BIO *out;
\&
\& out = BIO_new_file("filename.txt", "w");
\& if (!out)
\&     /* Error */
\& BIO_printf(out, "Hello World\en");
\& BIO_free(out);
.Ve
.PP
Alternative technique:
.PP
.Vb 1
\& BIO *out;
\&
\& out = BIO_new(BIO_s_file());
\& if (out == NULL)
\&     /* Error */
\& if (BIO_write_filename(out, "filename.txt") <= 0)
\&     /* Error */
\& BIO_printf(out, "Hello World\en");
\& BIO_free(out);
.Ve
.SH BUGS
.IX Header "BUGS"
\&\fBBIO_reset()\fR and \fBBIO_seek()\fR are implemented using \fBfseek()\fR on the underlying
stream. The return value for \fBfseek()\fR is 0 for success or \-1 if an error
occurred this differs from other types of BIO which will typically return
1 for success and a non positive value if an error occurred.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBBIO_seek\fR\|(3), \fBBIO_tell\fR\|(3),
\&\fBBIO_reset\fR\|(3), \fBBIO_flush\fR\|(3),
\&\fBBIO_read_ex\fR\|(3),
\&\fBBIO_write_ex\fR\|(3), \fBBIO_puts\fR\|(3),
\&\fBBIO_gets\fR\|(3), \fBBIO_printf\fR\|(3),
\&\fBBIO_set_close\fR\|(3), \fBBIO_get_close\fR\|(3)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
