.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "X509_GET_VERSION 3ossl"
.TH X509_GET_VERSION 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
X509_get_version, X509_set_version, X509_REQ_get_version, X509_REQ_set_version,
X509_CRL_get_version, X509_CRL_set_version \- get or set certificate,
certificate request or CRL version
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/x509.h>
\&
\& long X509_get_version(const X509 *x);
\& int X509_set_version(X509 *x, long version);
\&
\& long X509_REQ_get_version(const X509_REQ *req);
\& int X509_REQ_set_version(X509_REQ *x, long version);
\&
\& long X509_CRL_get_version(const X509_CRL *crl);
\& int X509_CRL_set_version(X509_CRL *x, long version);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBX509_get_version()\fR returns the numerical value of the version field of
certificate \fBx\fR. These correspond to the constants \fBX509_VERSION_1\fR,
\&\fBX509_VERSION_2\fR, and \fBX509_VERSION_3\fR. Note: the values of these constants
are defined by standards (X.509 et al) to be one less than the certificate
version. So \fBX509_VERSION_3\fR has value 2 and \fBX509_VERSION_1\fR has value 0.
.PP
\&\fBX509_set_version()\fR sets the numerical value of the version field of certificate
\&\fBx\fR to \fBversion\fR.
.PP
Similarly \fBX509_REQ_get_version()\fR, \fBX509_REQ_set_version()\fR,
\&\fBX509_CRL_get_version()\fR and \fBX509_CRL_set_version()\fR get and set the version
number of certificate requests and CRLs. They use constants
\&\fBX509_REQ_VERSION_1\fR, \fBX509_CRL_VERSION_1\fR, and \fBX509_CRL_VERSION_2\fR.
.SH NOTES
.IX Header "NOTES"
The version field of certificates, certificate requests and CRLs has a
DEFAULT value of \fBv1\|(0)\fR meaning the field should be omitted for version
1. This is handled transparently by these functions.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBX509_get_version()\fR, \fBX509_REQ_get_version()\fR and \fBX509_CRL_get_version()\fR
return the numerical value of the version field.
.PP
\&\fBX509_set_version()\fR, \fBX509_REQ_set_version()\fR and \fBX509_CRL_set_version()\fR
return 1 for success and 0 for failure.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBd2i_X509\fR\|(3),
\&\fBERR_get_error\fR\|(3),
\&\fBX509_CRL_get0_by_serial\fR\|(3),
\&\fBX509_get0_signature\fR\|(3),
\&\fBX509_get_ext_d2i\fR\|(3),
\&\fBX509_get_extension_flags\fR\|(3),
\&\fBX509_get_pubkey\fR\|(3),
\&\fBX509_get_subject_name\fR\|(3),
\&\fBX509_NAME_add_entry_by_txt\fR\|(3),
\&\fBX509_NAME_ENTRY_get_object\fR\|(3),
\&\fBX509_NAME_get_index_by_NID\fR\|(3),
\&\fBX509_NAME_print_ex\fR\|(3),
\&\fBX509_new\fR\|(3),
\&\fBX509_sign\fR\|(3),
\&\fBX509V3_get_d2i\fR\|(3),
\&\fBX509_verify_cert\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
\&\fBX509_get_version()\fR, \fBX509_REQ_get_version()\fR and \fBX509_CRL_get_version()\fR are
functions in OpenSSL 1.1.0, in previous versions they were macros.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2015\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
