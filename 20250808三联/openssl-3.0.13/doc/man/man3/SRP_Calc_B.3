.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "SRP_CALC_B 3ossl"
.TH SRP_CALC_B 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
SRP_Calc_server_key,
SRP_Calc_A,
SRP_Calc_B_ex,
SRP_Calc_B,
SRP_Calc_u_ex,
SRP_Calc_u,
SRP_Calc_x_ex,
SRP_Calc_x,
SRP_Calc_client_key_ex,
SRP_Calc_client_key
\&\- SRP authentication primitives
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/srp.h>
.Ve
.PP
The following functions have been deprecated since OpenSSL 3.0, and can be
hidden entirely by defining \fBOPENSSL_API_COMPAT\fR with a suitable version value,
see \fBopenssl_user_macros\fR\|(7):
.PP
.Vb 7
\& /* server side .... */
\& BIGNUM *SRP_Calc_server_key(const BIGNUM *A, const BIGNUM *v, const BIGNUM *u,
\&                             const BIGNUM *b, const BIGNUM *N);
\& BIGNUM *SRP_Calc_B_ex(const BIGNUM *b, const BIGNUM *N, const BIGNUM *g,
\&                       const BIGNUM *v, OSSL_LIB_CTX *libctx, const char *propq);
\& BIGNUM *SRP_Calc_B(const BIGNUM *b, const BIGNUM *N, const BIGNUM *g,
\&                   const BIGNUM *v);
\&
\& BIGNUM *SRP_Calc_u_ex(const BIGNUM *A, const BIGNUM *B, const BIGNUM *N,
\&                       OSSL_LIB_CTX *libctx, const char *propq);
\& BIGNUM *SRP_Calc_u(const BIGNUM *A, const BIGNUM *B, const BIGNUM *N);
\&
\& /* client side .... */
\& BIGNUM *SRP_Calc_client_key_ex(const BIGNUM *N, const BIGNUM *B, const BIGNUM *g,
\&                             const BIGNUM *x, const BIGNUM *a, const BIGNUM *u,
\&                             OSSL_LIB_CTX *libctx, const char *propq);
\& BIGNUM *SRP_Calc_client_key(const BIGNUM *N, const BIGNUM *B, const BIGNUM *g,
\&                             const BIGNUM *x, const BIGNUM *a, const BIGNUM *u);
\& BIGNUM *SRP_Calc_x_ex(const BIGNUM *s, const char *user, const char *pass,
\&                       OSSL_LIB_CTX *libctx, const char *propq);
\& BIGNUM *SRP_Calc_x(const BIGNUM *s, const char *user, const char *pass);
\& BIGNUM *SRP_Calc_A(const BIGNUM *a, const BIGNUM *N, const BIGNUM *g);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
All of the functions described on this page are deprecated. There are no
available replacement functions at this time.
.PP
The SRP functions described on this page are used to calculate various
parameters and keys used by SRP as defined in RFC2945. The server key and \fIB\fR
and \fIu\fR parameters are used on the server side and are calculated via
\&\fBSRP_Calc_server_key()\fR, \fBSRP_Calc_B_ex()\fR, \fBSRP_Calc_B()\fR, \fBSRP_Calc_u_ex()\fR and
\&\fBSRP_Calc_u()\fR. The client key and \fBx\fR and \fBA\fR parameters are used on the
client side and are calculated via the functions \fBSRP_Calc_client_key_ex()\fR,
\&\fBSRP_Calc_client_key()\fR, \fBSRP_Calc_x_ex()\fR, \fBSRP_Calc_x()\fR and \fBSRP_Calc_A()\fR. See
RFC2945 for a detailed description of their usage and the meaning of the various
BIGNUM parameters to these functions.
.PP
Most of these functions come in two forms. Those that take a \fIlibctx\fR and
\&\fIpropq\fR parameter, and those that don't. Any cryptogrpahic functions that
are fetched and used during the calculation use the provided \fIlibctx\fR and
\&\fIpropq\fR. See "ALGORITHM FETCHING" in \fBcrypto\fR\|(7) for more details. The variants
that do not take a \fIlibctx\fR and \fIpropq\fR parameter use the default library
context and property query string. The \fBSRP_Calc_server_key()\fR and \fBSRP_Calc_A()\fR
functions do not have a form that takes \fIlibctx\fR or \fIpropq\fR parameters because
they do not need to fetch any cryptographic algorithms.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
All these functions return the calculated key or parameter, or NULL on error.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBopenssl\-srp\fR\|(1),
\&\fBSRP_VBASE_new\fR\|(3),
\&\fBSRP_user_pwd_new\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
SRP_Calc_B_ex, SRP_Calc_u_ex, SRP_Calc_client_key_ex and SRP_Calc_x_ex were
introduced in OpenSSL 3.0.
.PP
All of the other functions were added in OpenSSL 1.0.1.
.PP
All of these functions were deprecated in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2020\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
