.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "X509_GET_PUBKEY 3ossl"
.TH X509_GET_PUBKEY 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
X509_get_pubkey, X509_get0_pubkey, X509_set_pubkey, X509_get_X509_PUBKEY,
X509_REQ_get_pubkey, X509_REQ_get0_pubkey, X509_REQ_set_pubkey,
X509_REQ_get_X509_PUBKEY \- get or set certificate or certificate request
public key
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/x509.h>
\&
\& EVP_PKEY *X509_get_pubkey(X509 *x);
\& EVP_PKEY *X509_get0_pubkey(const X509 *x);
\& int X509_set_pubkey(X509 *x, EVP_PKEY *pkey);
\& X509_PUBKEY *X509_get_X509_PUBKEY(const X509 *x);
\&
\& EVP_PKEY *X509_REQ_get_pubkey(X509_REQ *req);
\& EVP_PKEY *X509_REQ_get0_pubkey(X509_REQ *req);
\& int X509_REQ_set_pubkey(X509_REQ *x, EVP_PKEY *pkey);
\& X509_PUBKEY *X509_REQ_get_X509_PUBKEY(X509_REQ *x);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBX509_get_pubkey()\fR attempts to decode the public key for certificate \fBx\fR. If
successful it returns the public key as an \fBEVP_PKEY\fR pointer with its
reference count incremented: this means the returned key must be freed up
after use. \fBX509_get0_pubkey()\fR is similar except it does \fBnot\fR increment
the reference count of the returned \fBEVP_PKEY\fR so it must not be freed up
after use.
.PP
\&\fBX509_get_X509_PUBKEY()\fR returns an internal pointer to the \fBX509_PUBKEY\fR
structure which encodes the certificate of \fBx\fR. The returned value
must not be freed up after use.
.PP
\&\fBX509_set_pubkey()\fR attempts to set the public key for certificate \fBx\fR to
\&\fBpkey\fR. The key \fBpkey\fR should be freed up after use.
.PP
\&\fBX509_REQ_get_pubkey()\fR, \fBX509_REQ_get0_pubkey()\fR, \fBX509_REQ_set_pubkey()\fR and
\&\fBX509_REQ_get_X509_PUBKEY()\fR are similar but operate on certificate request \fBreq\fR.
.SH NOTES
.IX Header "NOTES"
The first time a public key is decoded the \fBEVP_PKEY\fR structure is
cached in the certificate or certificate request itself. Subsequent calls
return the cached structure with its reference count incremented to
improve performance.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBX509_get_pubkey()\fR, \fBX509_get0_pubkey()\fR, \fBX509_get_X509_PUBKEY()\fR,
\&\fBX509_REQ_get_pubkey()\fR and \fBX509_REQ_get_X509_PUBKEY()\fR return a public key or
\&\fBNULL\fR if an error occurred.
.PP
\&\fBX509_set_pubkey()\fR and \fBX509_REQ_set_pubkey()\fR return 1 for success and 0
for failure.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBd2i_X509\fR\|(3),
\&\fBERR_get_error\fR\|(3),
\&\fBX509_CRL_get0_by_serial\fR\|(3),
\&\fBX509_get0_signature\fR\|(3),
\&\fBX509_get_ext_d2i\fR\|(3),
\&\fBX509_get_extension_flags\fR\|(3),
\&\fBX509_get_subject_name\fR\|(3),
\&\fBX509_get_version\fR\|(3),
\&\fBX509_NAME_add_entry_by_txt\fR\|(3),
\&\fBX509_NAME_ENTRY_get_object\fR\|(3),
\&\fBX509_NAME_get_index_by_NID\fR\|(3),
\&\fBX509_NAME_print_ex\fR\|(3),
\&\fBX509_new\fR\|(3),
\&\fBX509_sign\fR\|(3),
\&\fBX509V3_get_d2i\fR\|(3),
\&\fBX509_verify_cert\fR\|(3)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2015\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
