.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "OPENSSL_INIT_SSL 3ossl"
.TH OPENSSL_INIT_SSL 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
OPENSSL_init_ssl \- OpenSSL (libssl and libcrypto) initialisation
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/ssl.h>
\&
\& int OPENSSL_init_ssl(uint64_t opts, const OPENSSL_INIT_SETTINGS *settings);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
During normal operation OpenSSL (libssl and libcrypto) will allocate various
resources at start up that must, subsequently, be freed on close down of the
library. Additionally some resources are allocated on a per thread basis (if the
application is multi-threaded), and these resources must be freed prior to the
thread closing.
.PP
As of version 1.1.0 OpenSSL will automatically allocate all resources that it
needs so no explicit initialisation is required. Similarly it will also
automatically deinitialise as required.
.PP
However, there may be situations when explicit initialisation is desirable or
needed, for example when some nondefault initialisation is required. The
function \fBOPENSSL_init_ssl()\fR can be used for this purpose. Calling
this function will explicitly initialise BOTH libcrypto and libssl. To
explicitly initialise ONLY libcrypto see the
\&\fBOPENSSL_init_crypto\fR\|(3) function.
.PP
Numerous internal OpenSSL functions call \fBOPENSSL_init_ssl()\fR.
Therefore, in order to perform nondefault initialisation,
\&\fBOPENSSL_init_ssl()\fR MUST be called by application code prior to
any other OpenSSL function calls.
.PP
The \fBopts\fR parameter specifies which aspects of libssl and libcrypto should be
initialised. Valid options for libcrypto are described on the
\&\fBOPENSSL_init_crypto\fR\|(3) page. In addition to any libcrypto
specific option the following libssl options can also be used:
.IP OPENSSL_INIT_NO_LOAD_SSL_STRINGS 4
.IX Item "OPENSSL_INIT_NO_LOAD_SSL_STRINGS"
Suppress automatic loading of the libssl error strings. This option is
not a default option. Once selected subsequent calls to
\&\fBOPENSSL_init_ssl()\fR with the option
\&\fBOPENSSL_INIT_LOAD_SSL_STRINGS\fR will be ignored.
.IP OPENSSL_INIT_LOAD_SSL_STRINGS 4
.IX Item "OPENSSL_INIT_LOAD_SSL_STRINGS"
Automatic loading of the libssl error strings. This option is a
default option. Once selected subsequent calls to
\&\fBOPENSSL_init_ssl()\fR with the option
\&\fBOPENSSL_INIT_LOAD_SSL_STRINGS\fR will be ignored.
.PP
\&\fBOPENSSL_init_ssl()\fR takes a \fBsettings\fR parameter which can be used to
set parameter values.  See \fBOPENSSL_init_crypto\fR\|(3) for details.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
The function \fBOPENSSL_init_ssl()\fR returns 1 on success or 0 on error.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBOPENSSL_init_crypto\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
The \fBOPENSSL_init_ssl()\fR function was added in OpenSSL 1.1.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2016\-2020 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
