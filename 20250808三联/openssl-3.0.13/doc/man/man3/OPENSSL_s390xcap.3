.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "OPENSSL_S390XCAP 3ossl"
.TH OPENSSL_S390XCAP 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
OPENSSL_s390xcap \- the IBM z processor capabilities vector
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& env OPENSSL_s390xcap=... <application>
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
libcrypto supports z/Architecture instruction set extensions. These
extensions are denoted by individual bits in the capabilities vector.
When libcrypto is initialized, the bits returned by the STFLE instruction
and by the QUERY functions are stored in the vector.
.PP
To change the set of instructions available to an application, you can
set the \fBOPENSSL_s390xcap\fR environment variable before you start the
application. After initialization, the capability vector is ANDed bitwise
with a mask which is derived from the environment variable.
.PP
The environment variable is a semicolon-separated list of tokens which is
processed from left to right (whitespace is ignored):
.PP
.Vb 1
\& OPENSSL_s390xcap="<tok1>;<tok2>;..."
.Ve
.PP
There are three types of tokens:
.IP <string> 4
.IX Item "<string>"
The name of a processor generation. A bit in the environment variable's
mask is set to one if and only if the specified processor generation
implements the corresponding instruction set extension. Possible values
are \fBz900\fR, \fBz990\fR, \fBz9\fR, \fBz10\fR, \fBz196\fR, \fBzEC12\fR, \fBz13\fR, \fBz14\fR
and \fBz15\fR.
.IP <string>:<mask>:<mask> 4
.IX Item "<string>:<mask>:<mask>"
The name of an instruction followed by two 64\-bit masks. The part of the
environment variable's mask corresponding to the specified instruction is
set to the specified 128\-bit mask. Possible values are \fBkimd\fR, \fBklmd\fR,
\&\fBkm\fR, \fBkmc\fR, \fBkmac\fR, \fBkmctr\fR, \fBkmo\fR, \fBkmf\fR, \fBprno\fR, \fBkma\fR, \fBpcc\fR
and \fBkdsa\fR.
.IP stfle:<mask>:<mask>:<mask> 4
.IX Item "stfle:<mask>:<mask>:<mask>"
Store-facility-list-extended (stfle) followed by three 64\-bit masks. The
part of the environment variable's mask corresponding to the stfle
instruction is set to the specified 192\-bit mask.
.PP
The 64\-bit masks are specified in hexadecimal notation. The 0x prefix is
optional. Prefix a mask with a tilde, \f(CW\*(C`~\*(C'\fR, to denote a bitwise NOT operation.
.PP
The following is a list of significant bits for each instruction. Colon
rows separate the individual 64\-bit masks. The bit numbers in the first
column are consistent with [1], that is, 0 denotes the leftmost bit and
the numbering is continuous across 64\-bit mask boundaries.
.PP
.Vb 1
\&      Bit     Mask     Facility/Function
\&
\& stfle:
\&      # 17    1<<46    message\-security assist
\&      # 25    1<<38    store\-clock\-fast facility
\&      :
\&      # 76    1<<51    message\-security assist extension 3
\&      # 77    1<<50    message\-security assist extension 4
\&      :
\&      #129    1<<62    vector facility
\&      #134    1<<57    vector packed decimal facility
\&      #135    1<<56    vector enhancements facility 1
\&      #146    1<<45    message\-security assist extension 8
\&      #155    1<<36    message\-security assist extension 9
\&
\& kimd :
\&      #  1    1<<62    KIMD\-SHA\-1
\&      #  2    1<<61    KIMD\-SHA\-256
\&      #  3    1<<60    KIMD\-SHA\-512
\&      # 32    1<<31    KIMD\-SHA3\-224
\&      # 33    1<<30    KIMD\-SHA3\-256
\&      # 34    1<<29    KIMD\-SHA3\-384
\&      # 35    1<<28    KIMD\-SHA3\-512
\&      # 36    1<<27    KIMD\-SHAKE\-128
\&      # 37    1<<26    KIMD\-SHAKE\-256
\&      :
\&      # 65    1<<62    KIMD\-GHASH
\&
\& klmd :
\&      # 32    1<<31    KLMD\-SHA3\-224
\&      # 33    1<<30    KLMD\-SHA3\-256
\&      # 34    1<<29    KLMD\-SHA3\-384
\&      # 35    1<<28    KLMD\-SHA3\-512
\&      # 36    1<<27    KLMD\-SHAKE\-128
\&      # 37    1<<26    KLMD\-SHAKE\-256
\&      :
\&
\& km   :
\&      # 18    1<<45    KM\-AES\-128
\&      # 19    1<<44    KM\-AES\-192
\&      # 20    1<<43    KM\-AES\-256
\&      # 50    1<<13    KM\-XTS\-AES\-128
\&      # 52    1<<11    KM\-XTS\-AES\-256
\&      :
\&
\& kmc  :
\&      # 18    1<<45    KMC\-AES\-128
\&      # 19    1<<44    KMC\-AES\-192
\&      # 20    1<<43    KMC\-AES\-256
\&      :
\&
\& kmac :
\&      # 18    1<<45    KMAC\-AES\-128
\&      # 19    1<<44    KMAC\-AES\-192
\&      # 20    1<<43    KMAC\-AES\-256
\&      :
\&
\& kmctr:
\&      :
\&
\& kmo  :
\&      # 18    1<<45    KMO\-AES\-128
\&      # 19    1<<44    KMO\-AES\-192
\&      # 20    1<<43    KMO\-AES\-256
\&      :
\&
\& kmf  :
\&      # 18    1<<45    KMF\-AES\-128
\&      # 19    1<<44    KMF\-AES\-192
\&      # 20    1<<43    KMF\-AES\-256
\&      :
\&
\& prno :
\&      :
\&
\& kma  :
\&      # 18    1<<45    KMA\-GCM\-AES\-128
\&      # 19    1<<44    KMA\-GCM\-AES\-192
\&      # 20    1<<43    KMA\-GCM\-AES\-256
\&      :
\&
\& pcc  :
\&      :
\&      # 64    1<<63    PCC\-Scalar\-Multiply\-P256
\&      # 65    1<<62    PCC\-Scalar\-Multiply\-P384
\&      # 66    1<<61    PCC\-Scalar\-Multiply\-P521
\&      # 72    1<<55    PCC\-Scalar\-Multiply\-Ed25519
\&      # 73    1<<54    PCC\-Scalar\-Multiply\-Ed448
\&      # 80    1<<47    PCC\-Scalar\-Multiply\-X25519
\&      # 81    1<<46    PCC\-Scalar\-Multiply\-X448
\&
\& kdsa :
\&      #  1    1<<62    KDSA\-ECDSA\-Verify\-P256
\&      #  2    1<<61    KDSA\-ECDSA\-Verify\-P384
\&      #  3    1<<60    KDSA\-ECDSA\-Verify\-P521
\&      #  9    1<<54    KDSA\-ECDSA\-Sign\-P256
\&      # 10    1<<53    KDSA\-ECDSA\-Sign\-P384
\&      # 11    1<<52    KDSA\-ECDSA\-Sign\-P521
\&      # 32    1<<31    KDSA\-EdDSA\-Verify\-Ed25519
\&      # 36    1<<27    KDSA\-EdDSA\-Verify\-Ed448
\&      # 40    1<<23    KDSA\-EdDSA\-Sign\-Ed25519
\&      # 44    1<<19    KDSA\-EdDSA\-Sign\-Ed448
\&      :
.Ve
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
Not available.
.SH EXAMPLES
.IX Header "EXAMPLES"
Disables all instruction set extensions which the z196 processor does not implement:
.PP
.Vb 1
\& OPENSSL_s390xcap="z196"
.Ve
.PP
Disables the vector facility:
.PP
.Vb 1
\& OPENSSL_s390xcap="stfle:~0:~0:~0x4000000000000000"
.Ve
.PP
Disables the KM-XTS-AES and the KIMD-SHAKE function codes:
.PP
.Vb 1
\& OPENSSL_s390xcap="km:~0x2800:~0;kimd:~0xc000000:~0"
.Ve
.SH "SEE ALSO"
.IX Header "SEE ALSO"
[1] z/Architecture Principles of Operation, SA22\-7832\-12
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2018\-2020 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
