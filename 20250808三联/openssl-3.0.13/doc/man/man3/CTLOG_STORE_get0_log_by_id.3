.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "CTLOG_STORE_GET0_LOG_BY_ID 3ossl"
.TH CTLOG_STORE_GET0_LOG_BY_ID 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
CTLOG_STORE_get0_log_by_id \-
Get a Certificate Transparency log from a CTLOG_STORE
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/ct.h>
\&
\& const CTLOG *CTLOG_STORE_get0_log_by_id(const CTLOG_STORE *store,
\&                                         const uint8_t *log_id,
\&                                         size_t log_id_len);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
A Signed Certificate Timestamp (SCT) identifies the Certificate Transparency
(CT) log that issued it using the log's LogID (see RFC 6962, Section 3.2).
Therefore, it is useful to be able to look up more information about a log
(e.g. its public key) using this LogID.
.PP
\&\fBCTLOG_STORE_get0_log_by_id()\fR provides a way to do this. It will find a CTLOG
in a CTLOG_STORE that has a given LogID.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBCTLOG_STORE_get0_log_by_id\fR returns a CTLOG with the given LogID, if it
exists in the given CTLOG_STORE, otherwise it returns NULL.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBct\fR\|(7),
\&\fBCTLOG_STORE_new\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
The \fBCTLOG_STORE_get0_log_by_id()\fR function was added in OpenSSL 1.1.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2016 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
