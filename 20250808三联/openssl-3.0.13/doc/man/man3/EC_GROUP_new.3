.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "EC_GROUP_NEW 3ossl"
.TH EC_GROUP_NEW 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
EC_GROUP_get_ecparameters,
EC_GROUP_get_ecpkparameters,
EC_GROUP_new_from_params,
EC_GROUP_new_from_ecparameters,
EC_GROUP_new_from_ecpkparameters,
EC_GROUP_new,
EC_GROUP_free,
EC_GROUP_clear_free,
EC_GROUP_new_curve_GFp,
EC_GROUP_new_curve_GF2m,
EC_GROUP_new_by_curve_name_ex,
EC_GROUP_new_by_curve_name,
EC_GROUP_set_curve,
EC_GROUP_get_curve,
EC_GROUP_set_curve_GFp,
EC_GROUP_get_curve_GFp,
EC_GROUP_set_curve_GF2m,
EC_GROUP_get_curve_GF2m,
EC_get_builtin_curves,
OSSL_EC_curve_nid2name \-
Functions for creating and destroying EC_GROUP objects
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/ec.h>
\&
\& EC_GROUP *EC_GROUP_new_from_params(const OSSL_PARAM params[],
\&                                    OSSL_LIB_CTX *libctx, const char *propq);
\& EC_GROUP *EC_GROUP_new_from_ecparameters(const ECPARAMETERS *params);
\& EC_GROUP *EC_GROUP_new_from_ecpkparameters(const ECPKPARAMETERS *params);
\& void EC_GROUP_free(EC_GROUP *group);
\&
\& EC_GROUP *EC_GROUP_new_curve_GFp(const BIGNUM *p, const BIGNUM *a,
\&                                  const BIGNUM *b, BN_CTX *ctx);
\& EC_GROUP *EC_GROUP_new_curve_GF2m(const BIGNUM *p, const BIGNUM *a,
\&                                   const BIGNUM *b, BN_CTX *ctx);
\& EC_GROUP *EC_GROUP_new_by_curve_name_ex(OSSL_LIB_CTX *libctx, const char *propq,
\&                                         int nid);
\& EC_GROUP *EC_GROUP_new_by_curve_name(int nid);
\&
\& int EC_GROUP_set_curve(EC_GROUP *group, const BIGNUM *p, const BIGNUM *a,
\&                        const BIGNUM *b, BN_CTX *ctx);
\& int EC_GROUP_get_curve(const EC_GROUP *group, BIGNUM *p, BIGNUM *a, BIGNUM *b,
\&                        BN_CTX *ctx);
\&
\& ECPARAMETERS *EC_GROUP_get_ecparameters(const EC_GROUP *group,
\&                                         ECPARAMETERS *params);
\& ECPKPARAMETERS *EC_GROUP_get_ecpkparameters(const EC_GROUP *group,
\&                                             ECPKPARAMETERS *params);
\&
\& size_t EC_get_builtin_curves(EC_builtin_curve *r, size_t nitems);
\& const char *OSSL_EC_curve_nid2name(int nid);
.Ve
.PP
The following functions have been deprecated since OpenSSL 3.0, and can be
hidden entirely by defining \fBOPENSSL_API_COMPAT\fR with a suitable version value,
see \fBopenssl_user_macros\fR\|(7):
.PP
.Vb 2
\& EC_GROUP *EC_GROUP_new(const EC_METHOD *meth);
\& void EC_GROUP_clear_free(EC_GROUP *group);
\&
\& int EC_GROUP_set_curve_GFp(EC_GROUP *group, const BIGNUM *p,
\&                            const BIGNUM *a, const BIGNUM *b, BN_CTX *ctx);
\& int EC_GROUP_get_curve_GFp(const EC_GROUP *group, BIGNUM *p,
\&                            BIGNUM *a, BIGNUM *b, BN_CTX *ctx);
\& int EC_GROUP_set_curve_GF2m(EC_GROUP *group, const BIGNUM *p,
\&                             const BIGNUM *a, const BIGNUM *b, BN_CTX *ctx);
\& int EC_GROUP_get_curve_GF2m(const EC_GROUP *group, BIGNUM *p,
\&                             BIGNUM *a, BIGNUM *b, BN_CTX *ctx);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
Within the library there are two forms of elliptic curve that are of interest.
The first form is those defined over the prime field Fp. The elements of Fp are
the integers 0 to p\-1, where p is a prime number. This gives us a revised
elliptic curve equation as follows:
.PP
y^2 mod p = x^3 +ax + b mod p
.PP
The second form is those defined over a binary field F2^m where the elements of
the field are integers of length at most m bits. For this form the elliptic
curve equation is modified to:
.PP
y^2 + xy = x^3 + ax^2 + b (where b != 0)
.PP
Operations in a binary field are performed relative to an
\&\fBirreducible polynomial\fR. All such curves with OpenSSL use a trinomial or a
pentanomial for this parameter.
.PP
Although deprecated since OpenSSL 3.0 and should no longer be used,
a new curve can be constructed by calling \fBEC_GROUP_new()\fR, using the
implementation provided by \fImeth\fR (see \fBEC_GFp_simple_method\fR\|(3)) and
associated with the library context \fIctx\fR (see \fBOSSL_LIB_CTX\fR\|(3)).
The \fIctx\fR parameter may be NULL in which case the default library context is
used.
It is then necessary to call \fBEC_GROUP_set_curve()\fR to set the curve parameters.
Applications should instead use one of the other EC_GROUP_new_* constructors.
.PP
\&\fBEC_GROUP_new_from_params()\fR creates a group with parameters specified by \fIparams\fR.
The library context \fIlibctx\fR (see \fBOSSL_LIB_CTX\fR\|(3)) and property query string
\&\fIpropq\fR are used to fetch algorithms from providers.
\&\fIparams\fR may be either a list of explicit params or a named group,
The values for \fIctx\fR and \fIpropq\fR may be NULL.
The \fIparams\fR that can be used are described in
\&\fBEVP_PKEY\-EC\fR(7).
.PP
\&\fBEC_GROUP_new_from_ecparameters()\fR will create a group from the
specified \fIparams\fR and
\&\fBEC_GROUP_new_from_ecpkparameters()\fR will create a group from the specific PK
\&\fIparams\fR.
.PP
\&\fBEC_GROUP_set_curve()\fR sets the curve parameters \fIp\fR, \fIa\fR and \fIb\fR. For a curve
over Fp \fIp\fR is the prime for the field. For a curve over F2^m \fIp\fR represents
the irreducible polynomial \- each bit represents a term in the polynomial.
Therefore, there will either be three or five bits set dependent on whether the
polynomial is a trinomial or a pentanomial.
In either case, \fIa\fR and \fIb\fR represents the coefficients a and b from the
relevant equation introduced above.
.PP
\&\fBEC_group_get_curve()\fR obtains the previously set curve parameters.
.PP
\&\fBEC_GROUP_set_curve_GFp()\fR and \fBEC_GROUP_set_curve_GF2m()\fR are synonyms for
\&\fBEC_GROUP_set_curve()\fR. They are defined for backwards compatibility only and
should not be used.
.PP
\&\fBEC_GROUP_get_curve_GFp()\fR and \fBEC_GROUP_get_curve_GF2m()\fR are synonyms for
\&\fBEC_GROUP_get_curve()\fR. They are defined for backwards compatibility only and
should not be used.
.PP
The functions \fBEC_GROUP_new_curve_GFp()\fR and \fBEC_GROUP_new_curve_GF2m()\fR are
shortcuts for calling \fBEC_GROUP_new()\fR and then the \fBEC_GROUP_set_curve()\fR function.
An appropriate default implementation method will be used.
.PP
Whilst the library can be used to create any curve using the functions described
above, there are also a number of predefined curves that are available. In order
to obtain a list of all of the predefined curves, call the function
\&\fBEC_get_builtin_curves()\fR. The parameter \fIr\fR should be an array of
EC_builtin_curve structures of size \fInitems\fR. The function will populate the
\&\fIr\fR array with information about the built-in curves. If \fInitems\fR is less than
the total number of curves available, then the first \fInitems\fR curves will be
returned. Otherwise the total number of curves will be provided. The return
value is the total number of curves available (whether that number has been
populated in \fIr\fR or not). Passing a NULL \fIr\fR, or setting \fInitems\fR to 0 will
do nothing other than return the total number of curves available.
The EC_builtin_curve structure is defined as follows:
.PP
.Vb 4
\& typedef struct {
\&        int nid;
\&        const char *comment;
\&        } EC_builtin_curve;
.Ve
.PP
Each EC_builtin_curve item has a unique integer id (\fInid\fR), and a human
readable comment string describing the curve.
.PP
In order to construct a built-in curve use the function
\&\fBEC_GROUP_new_by_curve_name_ex()\fR and provide the \fInid\fR of the curve to
be constructed, the associated library context to be used in \fIctx\fR (see
\&\fBOSSL_LIB_CTX\fR\|(3)) and any property query string in \fIpropq\fR. The \fIctx\fR value
may be NULL in which case the default library context is used. The \fIpropq\fR
value may also be NULL.
.PP
\&\fBEC_GROUP_new_by_curve_name()\fR is the same as
\&\fBEC_GROUP_new_by_curve_name_ex()\fR except that the default library context
is always used along with a NULL property query string.
.PP
\&\fBEC_GROUP_free()\fR frees the memory associated with the EC_GROUP.
If \fIgroup\fR is NULL nothing is done.
.PP
\&\fBEC_GROUP_clear_free()\fR is deprecated: it was meant to destroy any sensitive data
held within the EC_GROUP and then free its memory, but since all the data stored
in the EC_GROUP is public anyway, this function is unnecessary.
Its use can be safely replaced with \fBEC_GROUP_free()\fR.
If \fIgroup\fR is NULL nothing is done.
.PP
\&\fBOSSL_EC_curve_nid2name()\fR converts a curve \fInid\fR into the corresponding name.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
All EC_GROUP_new* functions return a pointer to the newly constructed group, or
NULL on error.
.PP
\&\fBEC_get_builtin_curves()\fR returns the number of built-in curves that are
available.
.PP
\&\fBEC_GROUP_set_curve_GFp()\fR, \fBEC_GROUP_get_curve_GFp()\fR, \fBEC_GROUP_set_curve_GF2m()\fR,
\&\fBEC_GROUP_get_curve_GF2m()\fR return 1 on success or 0 on error.
.PP
\&\fBOSSL_EC_curve_nid2name()\fR returns a character string constant, or NULL on error.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBcrypto\fR\|(7), \fBEC_GROUP_copy\fR\|(3),
\&\fBEC_POINT_new\fR\|(3), \fBEC_POINT_add\fR\|(3), \fBEC_KEY_new\fR\|(3),
\&\fBEC_GFp_simple_method\fR\|(3), \fBd2i_ECPKParameters\fR\|(3),
\&\fBOSSL_LIB_CTX\fR\|(3), \fBEVP_PKEY\-EC\fR\|(7)
.SH HISTORY
.IX Header "HISTORY"
.IP \(bu 2
\&\fBEC_GROUP_new()\fR was deprecated in OpenSSL 3.0.
.Sp
\&\fBEC_GROUP_new_by_curve_name_ex()\fR and \fBEC_GROUP_new_from_params()\fR were
added in OpenSSL 3.0.
.IP \(bu 2
\&\fBEC_GROUP_clear_free()\fR was deprecated in OpenSSL 3.0; use \fBEC_GROUP_free()\fR
instead.
.IP \(bu 2

.Sp
.Vb 3
\& EC_GROUP_set_curve_GFp(), EC_GROUP_get_curve_GFp(),
\& EC_GROUP_set_curve_GF2m() and EC_GROUP_get_curve_GF2m() were deprecated in
\& OpenSSL 3.0; use EC_GROUP_set_curve() and EC_GROUP_get_curve() instead.
.Ve
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2013\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
