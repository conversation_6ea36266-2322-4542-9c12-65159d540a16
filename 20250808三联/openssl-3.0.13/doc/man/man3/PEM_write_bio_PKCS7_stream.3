.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "PEM_WRITE_BIO_PKCS7_STREAM 3ossl"
.TH PEM_WRITE_BIO_PKCS7_STREAM 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
PEM_write_bio_PKCS7_stream \- output PKCS7 structure in PEM format
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/pkcs7.h>
\&
\& int PEM_write_bio_PKCS7_stream(BIO *out, PKCS7 *p7, BIO *data, int flags);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBPEM_write_bio_PKCS7_stream()\fR outputs a PKCS7 structure in PEM format.
.PP
It is otherwise identical to the function \fBSMIME_write_PKCS7()\fR.
.SH NOTES
.IX Header "NOTES"
This function is effectively a version of the \fBPEM_write_bio_PKCS7()\fR supporting
streaming.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBPEM_write_bio_PKCS7_stream()\fR returns 1 for success or 0 for failure.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBERR_get_error\fR\|(3), \fBPKCS7_sign\fR\|(3),
\&\fBPKCS7_verify\fR\|(3), \fBPKCS7_encrypt\fR\|(3)
\&\fBPKCS7_decrypt\fR\|(3),
\&\fBSMIME_write_PKCS7\fR\|(3),
\&\fBi2d_PKCS7_bio_stream\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
The \fBPEM_write_bio_PKCS7_stream()\fR function was added in OpenSSL 1.0.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2007\-2016 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
