.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "EVP_MD5 3ossl"
.TH EVP_MD5 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
EVP_md5,
EVP_md5_sha1
\&\- MD5 For EVP
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/evp.h>
\&
\& const EVP_MD *EVP_md5(void);
\& const EVP_MD *EVP_md5_sha1(void);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
MD5 is a cryptographic hash function standardized in RFC 1321 and designed by
Ronald Rivest.
.PP
The CMU Software Engineering Institute considers MD5 unsuitable for further
use since its security has been severely compromised.
.IP \fBEVP_md5()\fR 4
.IX Item "EVP_md5()"
The MD5 algorithm which produces a 128\-bit output from a given input.
.IP \fBEVP_md5_sha1()\fR 4
.IX Item "EVP_md5_sha1()"
A hash algorithm of SSL v3 that combines MD5 with SHA\-1 as described in RFC
6101.
.Sp
WARNING: this algorithm is not intended for non-SSL usage.
.SH NOTES
.IX Header "NOTES"
Developers should be aware of the negative performance implications of
calling these functions multiple times and should consider using
\&\fBEVP_MD_fetch\fR\|(3) with \fBEVP_MD\-MD5\fR\|(7) or \fBEVP_MD\-MD5\-SHA1\fR\|(7) instead.
See "Performance" in \fBcrypto\fR\|(7) for further information.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
These functions return a \fBEVP_MD\fR structure that contains the
implementation of the message digest. See \fBEVP_MD_meth_new\fR\|(3) for
details of the \fBEVP_MD\fR structure.
.SH "CONFORMING TO"
.IX Header "CONFORMING TO"
IETF RFC 1321.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBevp\fR\|(7),
\&\fBEVP_DigestInit\fR\|(3)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2017\-2023 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
