.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "EVP_PKEY_CTX_SET_PARAMS 3ossl"
.TH EVP_PKEY_CTX_SET_PARAMS 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
EVP_PKEY_CTX_set_params,
EVP_PKEY_CTX_settable_params,
EVP_PKEY_CTX_get_params,
EVP_PKEY_CTX_gettable_params
\&\- provider parameter passing operations
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/evp.h>
\&
\& int EVP_PKEY_CTX_set_params(EVP_PKEY_CTX *ctx, const OSSL_PARAM *params);
\& const OSSL_PARAM *EVP_PKEY_CTX_settable_params(const EVP_PKEY_CTX *ctx);
\& int EVP_PKEY_CTX_get_params(EVP_PKEY_CTX *ctx, OSSL_PARAM *params);
\& const OSSL_PARAM *EVP_PKEY_CTX_gettable_params(const EVP_PKEY_CTX *ctx);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
The \fBEVP_PKEY_CTX_get_params()\fR and \fBEVP_PKEY_CTX_set_params()\fR functions allow
transfer of arbitrary key parameters to and from providers.
Not all parameters may be supported by all providers.
See \fBOSSL_PROVIDER\fR\|(3) for more information on providers.
See \fBOSSL_PARAM\fR\|(3) for more information on parameters.
These functions must only be called after the EVP_PKEY_CTX has been initialised
for use in an operation.
These methods replace the \fBEVP_PKEY_CTX_ctrl()\fR mechanism. (EVP_PKEY_CTX_ctrl now
calls these methods internally to interact with providers).
.PP
\&\fBEVP_PKEY_CTX_gettable_params()\fR and \fBEVP_PKEY_CTX_settable_params()\fR get a
constant \fBOSSL_PARAM\fR\|(3) array that describes the gettable and
settable parameters for the current algorithm implementation, i.e. parameters
that can be used with \fBEVP_PKEY_CTX_get_params()\fR and \fBEVP_PKEY_CTX_set_params()\fR
respectively.
These functions must only be called after the EVP_PKEY_CTX has been initialised
for use in an operation.
.SS Parameters
.IX Subsection "Parameters"
Examples of EVP_PKEY parameters include the following:
.PP
"Common parameters" in \fBprovider\-keymgmt\fR\|(7)
"Key Exchange parameters" in \fBprovider\-keyexch\fR\|(7)
"Signature parameters" in \fBprovider\-signature\fR\|(7)
.PP
"Common RSA parameters" in \fBEVP_PKEY\-RSA\fR\|(7)
"RSA key generation parameters" in \fBEVP_PKEY\-RSA\fR\|(7)
"FFC parameters" in \fBEVP_PKEY\-FFC\fR\|(7)
"FFC key generation parameters" in \fBEVP_PKEY\-FFC\fR\|(7)
"DSA parameters" in \fBEVP_PKEY\-DSA\fR\|(7)
"DSA key generation parameters" in \fBEVP_PKEY\-DSA\fR\|(7)
"DH parameters" in \fBEVP_PKEY\-DH\fR\|(7)
"DH key generation parameters" in \fBEVP_PKEY\-DH\fR\|(7)
"Common EC parameters" in \fBEVP_PKEY\-EC\fR\|(7)
"Common X25519, X448, ED25519 and ED448 parameters" in \fBEVP_PKEY\-X25519\fR\|(7)
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBEVP_PKEY_CTX_set_params()\fR returns 1 for success or 0 otherwise.
\&\fBEVP_PKEY_CTX_settable_params()\fR returns an OSSL_PARAM array on success or NULL on
error.
It may also return NULL if there are no settable parameters available.
.PP
All other functions and macros described on this page return a positive value
for success and 0 or a negative value for failure. In particular a return value
of \-2 indicates the operation is not supported by the public key algorithm.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBEVP_PKEY_CTX_new\fR\|(3),
\&\fBEVP_PKEY_encrypt\fR\|(3),
\&\fBEVP_PKEY_decrypt\fR\|(3),
\&\fBEVP_PKEY_sign\fR\|(3),
\&\fBEVP_PKEY_verify\fR\|(3),
\&\fBEVP_PKEY_verify_recover\fR\|(3),
\&\fBEVP_PKEY_derive\fR\|(3),
\&\fBEVP_PKEY_keygen\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
All functions were added in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2020\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
