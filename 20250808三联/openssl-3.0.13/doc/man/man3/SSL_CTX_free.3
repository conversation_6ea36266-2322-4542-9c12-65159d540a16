.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "SSL_CTX_FREE 3ossl"
.TH SSL_CTX_FREE 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
SSL_CTX_free \- free an allocated SSL_CTX object
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/ssl.h>
\&
\& void SSL_CTX_free(SSL_CTX *ctx);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBSSL_CTX_free()\fR decrements the reference count of \fBctx\fR, and removes the
SSL_CTX object pointed to by \fBctx\fR and frees up the allocated memory if the reference count has reached 0.
.PP
It also calls the \fBfree()\fRing procedures for indirectly affected items, if
applicable: the session cache, the list of ciphers, the list of Client CAs,
the certificates and keys.
.PP
If \fBctx\fR is NULL nothing is done.
.SH WARNINGS
.IX Header "WARNINGS"
If a session-remove callback is set (\fBSSL_CTX_sess_set_remove_cb()\fR), this
callback will be called for each session being freed from \fBctx\fR's
session cache. This implies, that all corresponding sessions from an
external session cache are removed as well. If this is not desired, the user
should explicitly unset the callback by calling
SSL_CTX_sess_set_remove_cb(\fBctx\fR, NULL) prior to calling \fBSSL_CTX_free()\fR.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBSSL_CTX_free()\fR does not provide diagnostic information.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBSSL_CTX_new\fR\|(3), \fBssl\fR\|(7),
\&\fBSSL_CTX_sess_set_get_cb\fR\|(3)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2016 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
