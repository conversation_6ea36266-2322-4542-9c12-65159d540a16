.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "SSL_CTX_USE_CERTIFICATE 3ossl"
.TH SSL_CTX_USE_CERTIFICATE 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
SSL_CTX_use_certificate, SSL_CTX_use_certificate_ASN1,
SSL_CTX_use_certificate_file, SSL_use_certificate, SSL_use_certificate_ASN1,
SSL_use_certificate_file, SSL_CTX_use_certificate_chain_file,
SSL_use_certificate_chain_file,
SSL_CTX_use_PrivateKey, SSL_CTX_use_PrivateKey_ASN1,
SSL_CTX_use_PrivateKey_file, SSL_CTX_use_RSAPrivateKey,
SSL_CTX_use_RSAPrivateKey_ASN1, SSL_CTX_use_RSAPrivateKey_file,
SSL_use_PrivateKey_file, SSL_use_PrivateKey_ASN1, SSL_use_PrivateKey,
SSL_use_RSAPrivateKey, SSL_use_RSAPrivateKey_ASN1,
SSL_use_RSAPrivateKey_file, SSL_CTX_check_private_key, SSL_check_private_key,
SSL_CTX_use_cert_and_key, SSL_use_cert_and_key
\&\- load certificate and key data
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/ssl.h>
\&
\& int SSL_CTX_use_certificate(SSL_CTX *ctx, X509 *x);
\& int SSL_CTX_use_certificate_ASN1(SSL_CTX *ctx, int len, const unsigned char *d);
\& int SSL_CTX_use_certificate_file(SSL_CTX *ctx, const char *file, int type);
\& int SSL_use_certificate(SSL *ssl, X509 *x);
\& int SSL_use_certificate_ASN1(SSL *ssl, const unsigned char *d, int len);
\& int SSL_use_certificate_file(SSL *ssl, const char *file, int type);
\&
\& int SSL_CTX_use_certificate_chain_file(SSL_CTX *ctx, const char *file);
\& int SSL_use_certificate_chain_file(SSL *ssl, const char *file);
\&
\& int SSL_CTX_use_PrivateKey(SSL_CTX *ctx, EVP_PKEY *pkey);
\& int SSL_CTX_use_PrivateKey_ASN1(int pk, SSL_CTX *ctx, const unsigned char *d,
\&                                 long len);
\& int SSL_CTX_use_PrivateKey_file(SSL_CTX *ctx, const char *file, int type);
\& int SSL_CTX_use_RSAPrivateKey(SSL_CTX *ctx, RSA *rsa);
\& int SSL_CTX_use_RSAPrivateKey_ASN1(SSL_CTX *ctx, const unsigned char *d, long len);
\& int SSL_CTX_use_RSAPrivateKey_file(SSL_CTX *ctx, const char *file, int type);
\& int SSL_use_PrivateKey(SSL *ssl, EVP_PKEY *pkey);
\& int SSL_use_PrivateKey_ASN1(int pk, SSL *ssl, const unsigned char *d, long len);
\& int SSL_use_PrivateKey_file(SSL *ssl, const char *file, int type);
\& int SSL_use_RSAPrivateKey(SSL *ssl, RSA *rsa);
\& int SSL_use_RSAPrivateKey_ASN1(SSL *ssl, const unsigned char *d, long len);
\& int SSL_use_RSAPrivateKey_file(SSL *ssl, const char *file, int type);
\&
\& int SSL_CTX_check_private_key(const SSL_CTX *ctx);
\& int SSL_check_private_key(const SSL *ssl);
\&
\& int SSL_CTX_use_cert_and_key(SSL_CTX *ctx, X509 *x, EVP_PKEY *pkey, STACK_OF(X509) *chain, int override);
\& int SSL_use_cert_and_key(SSL *ssl, X509 *x, EVP_PKEY *pkey, STACK_OF(X509) *chain, int override);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
These functions load the certificates and private keys into the SSL_CTX
or SSL object, respectively.
.PP
The SSL_CTX_* class of functions loads the certificates and keys into the
SSL_CTX object \fBctx\fR. The information is passed to SSL objects \fBssl\fR
created from \fBctx\fR with \fBSSL_new\fR\|(3) by copying, so that
changes applied to \fBctx\fR do not propagate to already existing SSL objects.
.PP
The SSL_* class of functions only loads certificates and keys into a
specific SSL object. The specific information is kept, when
\&\fBSSL_clear\fR\|(3) is called for this SSL object.
.PP
\&\fBSSL_CTX_use_certificate()\fR loads the certificate \fBx\fR into \fBctx\fR,
\&\fBSSL_use_certificate()\fR loads \fBx\fR into \fBssl\fR. The rest of the
certificates needed to form the complete certificate chain can be
specified using the
\&\fBSSL_CTX_add_extra_chain_cert\fR\|(3)
function.
.PP
\&\fBSSL_CTX_use_certificate_ASN1()\fR loads the ASN1 encoded certificate from
the memory location \fBd\fR (with length \fBlen\fR) into \fBctx\fR,
\&\fBSSL_use_certificate_ASN1()\fR loads the ASN1 encoded certificate into \fBssl\fR.
.PP
\&\fBSSL_CTX_use_certificate_file()\fR loads the first certificate stored in \fBfile\fR
into \fBctx\fR. The formatting \fBtype\fR of the certificate must be specified
from the known types SSL_FILETYPE_PEM, SSL_FILETYPE_ASN1.
\&\fBSSL_use_certificate_file()\fR loads the certificate from \fBfile\fR into \fBssl\fR.
See the NOTES section on why \fBSSL_CTX_use_certificate_chain_file()\fR
should be preferred.
.PP
\&\fBSSL_CTX_use_certificate_chain_file()\fR loads a certificate chain from
\&\fBfile\fR into \fBctx\fR. The certificates must be in PEM format and must
be sorted starting with the subject's certificate (actual client or server
certificate), followed by intermediate CA certificates if applicable, and
ending at the highest level (root) CA. \fBSSL_use_certificate_chain_file()\fR is
similar except it loads the certificate chain into \fBssl\fR.
.PP
\&\fBSSL_CTX_use_PrivateKey()\fR adds \fBpkey\fR as private key to \fBctx\fR.
\&\fBSSL_CTX_use_RSAPrivateKey()\fR adds the private key \fBrsa\fR of type RSA
to \fBctx\fR. \fBSSL_use_PrivateKey()\fR adds \fBpkey\fR as private key to \fBssl\fR;
\&\fBSSL_use_RSAPrivateKey()\fR adds \fBrsa\fR as private key of type RSA to \fBssl\fR.
If a certificate has already been set and the private key does not belong
to the certificate an error is returned. To change a [certificate/private\-key]
pair, the new certificate needs to be set first with \fBSSL_use_certificate()\fR or
\&\fBSSL_CTX_use_certificate()\fR before setting the private key with
\&\fBSSL_CTX_use_PrivateKey()\fR or \fBSSL_use_PrivateKey()\fR.
.PP
\&\fBSSL_CTX_use_cert_and_key()\fR and \fBSSL_use_cert_and_key()\fR assign the X.509
certificate \fBx\fR, private key \fBkey\fR, and certificate \fBchain\fR onto the
corresponding \fBssl\fR or \fBctx\fR. The \fBpkey\fR argument must be the private
key of the X.509 certificate \fBx\fR. If the \fBoverride\fR argument is 0, then
\&\fBx\fR, \fBpkey\fR and \fBchain\fR are set only if all were not previously set.
If \fBoverride\fR is non\-0, then the certificate, private key and chain certs
are always set. If \fBpkey\fR is NULL, then the public key of \fBx\fR is used as
the private key. This is intended to be used with hardware (via the ENGINE
interface) that stores the private key securely, such that it cannot be
accessed by OpenSSL. The reference count of the public key is incremented
(twice if there is no private key); it is not copied nor duplicated. This
allows all private key validations checks to succeed without an actual
private key being assigned via \fBSSL_CTX_use_PrivateKey()\fR, etc.
.PP
\&\fBSSL_CTX_use_PrivateKey_ASN1()\fR adds the private key of type \fBpk\fR
stored at memory location \fBd\fR (length \fBlen\fR) to \fBctx\fR.
\&\fBSSL_CTX_use_RSAPrivateKey_ASN1()\fR adds the private key of type RSA
stored at memory location \fBd\fR (length \fBlen\fR) to \fBctx\fR.
\&\fBSSL_use_PrivateKey_ASN1()\fR and \fBSSL_use_RSAPrivateKey_ASN1()\fR add the private
key to \fBssl\fR.
.PP
\&\fBSSL_CTX_use_PrivateKey_file()\fR adds the first private key found in
\&\fBfile\fR to \fBctx\fR. The formatting \fBtype\fR of the private key must be specified
from the known types SSL_FILETYPE_PEM, SSL_FILETYPE_ASN1.
\&\fBSSL_CTX_use_RSAPrivateKey_file()\fR adds the first private RSA key found in
\&\fBfile\fR to \fBctx\fR. \fBSSL_use_PrivateKey_file()\fR adds the first private key found
in \fBfile\fR to \fBssl\fR; \fBSSL_use_RSAPrivateKey_file()\fR adds the first private
RSA key found to \fBssl\fR.
.PP
\&\fBSSL_CTX_check_private_key()\fR checks the consistency of a private key with
the corresponding certificate loaded into \fBctx\fR. If more than one
key/certificate pair (RSA/DSA) is installed, the last item installed will
be checked. If e.g. the last item was an RSA certificate or key, the RSA
key/certificate pair will be checked. \fBSSL_check_private_key()\fR performs
the same check for \fBssl\fR. If no key/certificate was explicitly added for
this \fBssl\fR, the last item added into \fBctx\fR will be checked.
.SH NOTES
.IX Header "NOTES"
The internal certificate store of OpenSSL can hold several private
key/certificate pairs at a time. The certificate used depends on the
cipher selected, see also \fBSSL_CTX_set_cipher_list\fR\|(3).
.PP
When reading certificates and private keys from file, files of type
SSL_FILETYPE_ASN1 (also known as \fBDER\fR, binary encoding) can only contain
one certificate or private key, consequently
\&\fBSSL_CTX_use_certificate_chain_file()\fR is only applicable to PEM formatting.
Files of type SSL_FILETYPE_PEM can contain more than one item.
.PP
\&\fBSSL_CTX_use_certificate_chain_file()\fR adds the first certificate found
in the file to the certificate store. The other certificates are added
to the store of chain certificates using \fBSSL_CTX_add1_chain_cert\fR\|(3).
Note: versions of OpenSSL before 1.0.2 only had a single
certificate chain store for all certificate types, OpenSSL 1.0.2 and later
have a separate chain store for each type. \fBSSL_CTX_use_certificate_chain_file()\fR
should be used instead of the \fBSSL_CTX_use_certificate_file()\fR function in order
to allow the use of complete certificate chains even when no trusted CA
storage is used or when the CA issuing the certificate shall not be added to
the trusted CA storage.
.PP
If additional certificates are needed to complete the chain during the
TLS negotiation, CA certificates are additionally looked up in the
locations of trusted CA certificates, see
\&\fBSSL_CTX_load_verify_locations\fR\|(3).
.PP
The private keys loaded from file can be encrypted. In order to successfully
load encrypted keys, a function returning the passphrase must have been
supplied, see
\&\fBSSL_CTX_set_default_passwd_cb\fR\|(3).
(Certificate files might be encrypted as well from the technical point
of view, it however does not make sense as the data in the certificate
is considered public anyway.)
.PP
All of the functions to set a new certificate will replace any existing
certificate of the same type that has already been set. Similarly all of the
functions to set a new private key will replace any private key that has already
been set. Applications should call \fBSSL_CTX_check_private_key\fR\|(3) or
\&\fBSSL_check_private_key\fR\|(3) as appropriate after loading a new certificate and
private key to confirm that the certificate and key match.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
On success, the functions return 1.
Otherwise check out the error stack to find out the reason.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBssl\fR\|(7), \fBSSL_new\fR\|(3), \fBSSL_clear\fR\|(3),
\&\fBSSL_CTX_load_verify_locations\fR\|(3),
\&\fBSSL_CTX_set_default_passwd_cb\fR\|(3),
\&\fBSSL_CTX_set_cipher_list\fR\|(3),
\&\fBSSL_CTX_set_client_CA_list\fR\|(3),
\&\fBSSL_CTX_set_client_cert_cb\fR\|(3),
\&\fBSSL_CTX_add_extra_chain_cert\fR\|(3)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2022 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
