.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "OSSL_DECODER_CTX 3ossl"
.TH OSSL_DECODER_CTX 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
OSSL_DECODER_CTX,
OSSL_DECODER_CTX_new,
OSSL_DECODER_settable_ctx_params,
OSSL_DECODER_CTX_set_params,
OSSL_DECODER_CTX_free,
OSSL_DECODER_CTX_set_selection,
OSSL_DECODER_CTX_set_input_type,
OSSL_DECODER_CTX_set_input_structure,
OSSL_DECODER_CTX_add_decoder,
OSSL_DECODER_CTX_add_extra,
OSSL_DECODER_CTX_get_num_decoders,
OSSL_DECODER_INSTANCE,
OSSL_DECODER_CONSTRUCT,
OSSL_DECODER_CLEANUP,
OSSL_DECODER_CTX_set_construct,
OSSL_DECODER_CTX_set_construct_data,
OSSL_DECODER_CTX_set_cleanup,
OSSL_DECODER_CTX_get_construct,
OSSL_DECODER_CTX_get_construct_data,
OSSL_DECODER_CTX_get_cleanup,
OSSL_DECODER_export,
OSSL_DECODER_INSTANCE_get_decoder,
OSSL_DECODER_INSTANCE_get_decoder_ctx,
OSSL_DECODER_INSTANCE_get_input_type,
OSSL_DECODER_INSTANCE_get_input_structure
\&\- Decoder context routines
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/decoder.h>
\&
\& typedef struct ossl_decoder_ctx_st OSSL_DECODER_CTX;
\&
\& OSSL_DECODER_CTX *OSSL_DECODER_CTX_new(void);
\& const OSSL_PARAM *OSSL_DECODER_settable_ctx_params(OSSL_DECODER *decoder);
\& int OSSL_DECODER_CTX_set_params(OSSL_DECODER_CTX *ctx,
\&                                 const OSSL_PARAM params[]);
\& void OSSL_DECODER_CTX_free(OSSL_DECODER_CTX *ctx);
\&
\& int OSSL_DECODER_CTX_set_selection(OSSL_DECODER_CTX *ctx, int selection);
\& int OSSL_DECODER_CTX_set_input_type(OSSL_DECODER_CTX *ctx,
\&                                     const char *input_type);
\& int OSSL_DECODER_CTX_set_input_structure(OSSL_DECODER_CTX *ctx,
\&                                          const char *input_structure);
\& int OSSL_DECODER_CTX_add_decoder(OSSL_DECODER_CTX *ctx, OSSL_DECODER *decoder);
\& int OSSL_DECODER_CTX_add_extra(OSSL_DECODER_CTX *ctx, 
\&                                OSSL_LIB_CTX *libctx, 
\&                                const char *propq);
\& int OSSL_DECODER_CTX_get_num_decoders(OSSL_DECODER_CTX *ctx);
\&
\& typedef struct ossl_decoder_instance_st OSSL_DECODER_INSTANCE;
\& OSSL_DECODER *
\& OSSL_DECODER_INSTANCE_get_decoder(OSSL_DECODER_INSTANCE *decoder_inst);
\& void *
\& OSSL_DECODER_INSTANCE_get_decoder_ctx(OSSL_DECODER_INSTANCE *decoder_inst);
\& const char *
\& OSSL_DECODER_INSTANCE_get_input_type(OSSL_DECODER_INSTANCE *decoder_inst);
\& OSSL_DECODER_INSTANCE_get_input_structure(OSSL_DECODER_INSTANCE *decoder_inst,
\&                                           int *was_set);
\&
\& typedef int OSSL_DECODER_CONSTRUCT(OSSL_DECODER_INSTANCE *decoder_inst,
\&                                    const OSSL_PARAM *object,
\&                                    void *construct_data);
\& typedef void OSSL_DECODER_CLEANUP(void *construct_data);
\&
\& int OSSL_DECODER_CTX_set_construct(OSSL_DECODER_CTX *ctx,
\&                                    OSSL_DECODER_CONSTRUCT *construct);
\& int OSSL_DECODER_CTX_set_construct_data(OSSL_DECODER_CTX *ctx,
\&                                         void *construct_data);
\& int OSSL_DECODER_CTX_set_cleanup(OSSL_DECODER_CTX *ctx,
\&                                  OSSL_DECODER_CLEANUP *cleanup);
\& OSSL_DECODER_CONSTRUCT *OSSL_DECODER_CTX_get_construct(OSSL_DECODER_CTX *ctx);
\& void *OSSL_DECODER_CTX_get_construct_data(OSSL_DECODER_CTX *ctx);
\& OSSL_DECODER_CLEANUP *OSSL_DECODER_CTX_get_cleanup(OSSL_DECODER_CTX *ctx);
\&
\& int OSSL_DECODER_export(OSSL_DECODER_INSTANCE *decoder_inst,
\&                         void *reference, size_t reference_sz,
\&                         OSSL_CALLBACK *export_cb, void *export_cbarg);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
The \fBOSSL_DECODER_CTX\fR holds data about multiple decoders, as needed to
figure out what the input data is and to attempt to unpack it into one of
several possible related results.  This also includes chaining decoders, so
the output from one can become the input for another.  This allows having
generic format decoders such as PEM to DER, as well as more specialized
decoders like DER to RSA.
.PP
The chains may be limited by specifying an input type, which is considered a
starting point.  This is both considered by \fBOSSL_DECODER_CTX_add_extra()\fR,
which will stop adding one more decoder implementations when it has already
added those that take the specified input type, and functions like
\&\fBOSSL_DECODER_from_bio\fR\|(3), which will only start the decoding process with
the decoder implementations that take that input type.  For example, if the
input type is set to \f(CW\*(C`DER\*(C'\fR, a PEM to DER decoder will be ignored.
.PP
The input type can also be NULL, which means that the caller doesn't know
what type of input they have.  In this case, \fBOSSL_DECODER_from_bio()\fR will
simply try with one decoder implementation after the other, and thereby
discover what kind of input the caller gave it.
.PP
For every decoding done, even an intermediary one, a constructor provided by
the caller is called to attempt to construct an appropriate type / structure
that the caller knows how to handle from the current decoding result.
The constructor is set with \fBOSSL_DECODER_CTX_set_construct()\fR.
.PP
\&\fBOSSL_DECODER_INSTANCE\fR is an opaque structure that contains data about the
decoder that was just used, and that may be useful for the constructor.
There are some functions to extract data from this type, described further
down.
.SS Functions
.IX Subsection "Functions"
\&\fBOSSL_DECODER_CTX_new()\fR creates a new empty \fBOSSL_DECODER_CTX\fR.
.PP
\&\fBOSSL_DECODER_settable_ctx_params()\fR returns an \fBOSSL_PARAM\fR\|(3) array of
parameter descriptors.
.PP
\&\fBOSSL_DECODER_CTX_set_params()\fR attempts to set parameters specified with an
\&\fBOSSL_PARAM\fR\|(3) array \fIparams\fR.  These parameters are passed to all
decoders that have been added to the \fIctx\fR so far.  Parameters that an
implementation doesn't recognise should be ignored by it.
.PP
\&\fBOSSL_DECODER_CTX_free()\fR frees the given context \fIctx\fR.
.PP
\&\fBOSSL_DECODER_CTX_add_decoder()\fR populates the \fBOSSL_DECODER_CTX\fR \fIctx\fR with
a decoder, to be used to attempt to decode some encoded input.
.PP
\&\fBOSSL_DECODER_CTX_add_extra()\fR finds decoders that generate input for already
added decoders, and adds them as well.  This is used to build decoder
chains.
.PP
\&\fBOSSL_DECODER_CTX_set_input_type()\fR sets the starting input type.  This limits
the decoder chains to be considered, as explained in the general description
above.
.PP
\&\fBOSSL_DECODER_CTX_set_input_structure()\fR sets the name of the structure that
the input is expected to have.  This may be used to determines what decoder
implementations may be used.  NULL is a valid input structure, when it's not
relevant, or when the decoder implementations are expected to figure it out.
.PP
\&\fBOSSL_DECODER_CTX_get_num_decoders()\fR gets the number of decoders currently
added to the context \fIctx\fR.
.PP
\&\fBOSSL_DECODER_CTX_set_construct()\fR sets the constructor \fIconstruct\fR.
.PP
\&\fBOSSL_DECODER_CTX_set_construct_data()\fR sets the constructor data that is
passed to the constructor every time it's called.
.PP
\&\fBOSSL_DECODER_CTX_set_cleanup()\fR sets the constructor data \fIcleanup\fR
function.  This is called by \fBOSSL_DECODER_CTX_free\fR\|(3).
.PP
\&\fBOSSL_DECODER_CTX_get_construct()\fR, \fBOSSL_DECODER_CTX_get_construct_data()\fR and
\&\fBOSSL_DECODER_CTX_get_cleanup()\fR return the values that have been set by
\&\fBOSSL_DECODER_CTX_set_construct()\fR, \fBOSSL_DECODER_CTX_set_construct_data()\fR and
\&\fBOSSL_DECODER_CTX_set_cleanup()\fR respectively.
.PP
\&\fBOSSL_DECODER_export()\fR is a fallback function for constructors that cannot
use the data they get directly for diverse reasons.  It takes the same
decode instance \fIdecoder_inst\fR that the constructor got and an object
\&\fIreference\fR, unpacks the object which it refers to, and exports it by
creating an \fBOSSL_PARAM\fR\|(3) array that it then passes to \fIexport_cb\fR,
along with \fIexport_arg\fR.
.SS Constructor
.IX Subsection "Constructor"
A \fBOSSL_DECODER_CONSTRUCT\fR gets the following arguments:
.IP \fIdecoder_inst\fR 4
.IX Item "decoder_inst"
The \fBOSSL_DECODER_INSTANCE\fR for the decoder from which the constructor gets
its data.
.IP \fIobject\fR 4
.IX Item "object"
A provider-native object abstraction produced by the decoder.  Further
information on the provider-native object abstraction can be found in
\&\fBprovider\-object\fR\|(7).
.IP \fIconstruct_data\fR 4
.IX Item "construct_data"
The pointer that was set with \fBOSSL_DECODE_CTX_set_construct_data()\fR.
.PP
The constructor is expected to return 1 when the data it receives can be
constructed, otherwise 0.
.PP
These utility functions may be used by a constructor:
.PP
\&\fBOSSL_DECODER_INSTANCE_get_decoder()\fR can be used to get the decoder
implementation from a decoder instance \fIdecoder_inst\fR.
.PP
\&\fBOSSL_DECODER_INSTANCE_get_decoder_ctx()\fR can be used to get the decoder
implementation's provider context from a decoder instance \fIdecoder_inst\fR.
.PP
\&\fBOSSL_DECODER_INSTANCE_get_input_type()\fR can be used to get the decoder
implementation's input type from a decoder instance \fIdecoder_inst\fR.
.PP
\&\fBOSSL_DECODER_INSTANCE_get_input_structure()\fR can be used to get the input
structure for the decoder implementation from a decoder instance
\&\fIdecoder_inst\fR.
This may be NULL.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBOSSL_DECODER_CTX_new()\fR returns a pointer to a \fBOSSL_DECODER_CTX\fR, or NULL
if the context structure couldn't be allocated.
.PP
\&\fBOSSL_DECODER_settable_ctx_params()\fR returns an \fBOSSL_PARAM\fR\|(3) array, or
NULL if none is available.
.PP
\&\fBOSSL_DECODER_CTX_set_params()\fR returns 1 if all recognised parameters were
valid, or 0 if one of them was invalid or caused some other failure in the
implementation.
.PP
\&\fBOSSL_DECODER_CTX_add_decoder()\fR, \fBOSSL_DECODER_CTX_add_extra()\fR,
\&\fBOSSL_DECODER_CTX_set_construct()\fR, \fBOSSL_DECODER_CTX_set_construct_data()\fR and
\&\fBOSSL_DECODER_CTX_set_cleanup()\fR return 1 on success, or 0 on failure.
.PP
\&\fBOSSL_DECODER_CTX_get_construct()\fR, \fBOSSL_DECODER_CTX_get_construct_data()\fR and
\&\fBOSSL_DECODER_CTX_get_cleanup()\fR return the current pointers to the
constructor, the constructor data and the cleanup functions, respectively.
.PP
\&\fBOSSL_DECODER_CTX_num_decoders()\fR returns the current number of decoders.  It
returns 0 if \fIctx\fR is NULL.
.PP
\&\fBOSSL_DECODER_export()\fR returns 1 on success, or 0 on failure.
.PP
\&\fBOSSL_DECODER_INSTANCE_decoder()\fR returns an \fBOSSL_DECODER\fR pointer on
success, or NULL on failure.
.PP
\&\fBOSSL_DECODER_INSTANCE_decoder_ctx()\fR returns a provider context pointer on
success, or NULL on failure.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBprovider\fR\|(7), \fBOSSL_DECODER\fR\|(3), \fBOSSL_DECODER_from_bio\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
The functions described here were added in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2020\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
