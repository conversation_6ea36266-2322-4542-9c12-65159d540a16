.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "RSA_PADDING_ADD_PKCS1_TYPE_1 3ossl"
.TH RSA_PADDING_ADD_PKCS1_TYPE_1 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
RSA_padding_add_PKCS1_type_1, RSA_padding_check_PKCS1_type_1,
RSA_padding_add_PKCS1_type_2, RSA_padding_check_PKCS1_type_2,
RSA_padding_add_PKCS1_OAEP, RSA_padding_check_PKCS1_OAEP,
RSA_padding_add_PKCS1_OAEP_mgf1, RSA_padding_check_PKCS1_OAEP_mgf1,
RSA_padding_add_none, RSA_padding_check_none \- asymmetric encryption
padding
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/rsa.h>
.Ve
.PP
The following functions have been deprecated since OpenSSL 3.0, and can be
hidden entirely by defining \fBOPENSSL_API_COMPAT\fR with a suitable version value,
see \fBopenssl_user_macros\fR\|(7):
.PP
.Vb 2
\& int RSA_padding_add_PKCS1_type_1(unsigned char *to, int tlen,
\&                                  const unsigned char *f, int fl);
\&
\& int RSA_padding_check_PKCS1_type_1(unsigned char *to, int tlen,
\&                                    const unsigned char *f, int fl, int rsa_len);
\&
\& int RSA_padding_add_PKCS1_type_2(unsigned char *to, int tlen,
\&                                  const unsigned char *f, int fl);
\&
\& int RSA_padding_check_PKCS1_type_2(unsigned char *to, int tlen,
\&                                    const unsigned char *f, int fl, int rsa_len);
\&
\& int RSA_padding_add_PKCS1_OAEP(unsigned char *to, int tlen,
\&                                const unsigned char *f, int fl,
\&                                const unsigned char *p, int pl);
\&
\& int RSA_padding_check_PKCS1_OAEP(unsigned char *to, int tlen,
\&                                  const unsigned char *f, int fl, int rsa_len,
\&                                  const unsigned char *p, int pl);
\&
\& int RSA_padding_add_PKCS1_OAEP_mgf1(unsigned char *to, int tlen,
\&                                     const unsigned char *f, int fl,
\&                                     const unsigned char *p, int pl,
\&                                     const EVP_MD *md, const EVP_MD *mgf1md);
\&
\& int RSA_padding_check_PKCS1_OAEP_mgf1(unsigned char *to, int tlen,
\&                                       const unsigned char *f, int fl, int rsa_len,
\&                                       const unsigned char *p, int pl,
\&                                       const EVP_MD *md, const EVP_MD *mgf1md);
\&
\& int RSA_padding_add_none(unsigned char *to, int tlen,
\&                          const unsigned char *f, int fl);
\&
\& int RSA_padding_check_none(unsigned char *to, int tlen,
\&                            const unsigned char *f, int fl, int rsa_len);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
All of the functions described on this page are deprecated.
Applications should instead use the EVP PKEY APIs.
.PP
The \fBRSA_padding_xxx_xxx()\fR functions are called from the RSA encrypt,
decrypt, sign and verify functions. Normally they should not be called
from application programs.
.PP
However, they can also be called directly to implement padding for other
asymmetric ciphers. \fBRSA_padding_add_PKCS1_OAEP()\fR and
\&\fBRSA_padding_check_PKCS1_OAEP()\fR may be used in an application combined
with \fBRSA_NO_PADDING\fR in order to implement OAEP with an encoding
parameter.
.PP
\&\fBRSA_padding_add_xxx()\fR encodes \fBfl\fR bytes from \fBf\fR so as to fit into
\&\fBtlen\fR bytes and stores the result at \fBto\fR. An error occurs if \fBfl\fR
does not meet the size requirements of the encoding method.
.PP
The following encoding methods are implemented:
.IP PKCS1_type_1 4
.IX Item "PKCS1_type_1"
PKCS #1 v2.0 EMSA\-PKCS1\-v1_5 (PKCS #1 v1.5 block type 1); used for signatures
.IP PKCS1_type_2 4
.IX Item "PKCS1_type_2"
PKCS #1 v2.0 EME\-PKCS1\-v1_5 (PKCS #1 v1.5 block type 2)
.IP PKCS1_OAEP 4
.IX Item "PKCS1_OAEP"
PKCS #1 v2.0 EME-OAEP
.IP none 4
.IX Item "none"
simply copy the data
.PP
The random number generator must be seeded prior to calling
\&\fBRSA_padding_add_xxx()\fR.
If the automatic seeding or reseeding of the OpenSSL CSPRNG fails due to
external circumstances (see \fBRAND\fR\|(7)), the operation will fail.
.PP
\&\fBRSA_padding_check_xxx()\fR verifies that the \fBfl\fR bytes at \fBf\fR contain
a valid encoding for a \fBrsa_len\fR byte RSA key in the respective
encoding method and stores the recovered data of at most \fBtlen\fR bytes
(for \fBRSA_NO_PADDING\fR: of size \fBtlen\fR)
at \fBto\fR.
.PP
For \fBRSA_padding_xxx_OAEP()\fR, \fBp\fR points to the encoding parameter
of length \fBpl\fR. \fBp\fR may be \fBNULL\fR if \fBpl\fR is 0.
.PP
For \fBRSA_padding_xxx_OAEP_mgf1()\fR, \fBmd\fR points to the md hash,
if \fBmd\fR is \fBNULL\fR that means md=sha1, and \fBmgf1md\fR points to
the mgf1 hash, if \fBmgf1md\fR is \fBNULL\fR that means mgf1md=md.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
The \fBRSA_padding_add_xxx()\fR functions return 1 on success, 0 on error.
The \fBRSA_padding_check_xxx()\fR functions return the length of the
recovered data, \-1 on error. Error codes can be obtained by calling
\&\fBERR_get_error\fR\|(3).
.SH WARNINGS
.IX Header "WARNINGS"
The result of \fBRSA_padding_check_PKCS1_type_2()\fR is a very sensitive
information which can potentially be used to mount a Bleichenbacher
padding oracle attack. This is an inherent weakness in the PKCS #1
v1.5 padding design. Prefer PKCS1_OAEP padding. If that is not
possible, the result of \fBRSA_padding_check_PKCS1_type_2()\fR should be
checked in constant time if it matches the expected length of the
plaintext and additionally some application specific consistency
checks on the plaintext need to be performed in constant time.
If the plaintext is rejected it must be kept secret which of the
checks caused the application to reject the message.
Do not remove the zero-padding from the decrypted raw RSA data
which was computed by \fBRSA_private_decrypt()\fR with \fBRSA_NO_PADDING\fR,
as this would create a small timing side channel which could be
used to mount a Bleichenbacher attack against any padding mode
including PKCS1_OAEP.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBRSA_public_encrypt\fR\|(3),
\&\fBRSA_private_decrypt\fR\|(3),
\&\fBRSA_sign\fR\|(3), \fBRSA_verify\fR\|(3),
\&\fBRAND\fR\|(7)
.SH HISTORY
.IX Header "HISTORY"
All of these functions were deprecated in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
