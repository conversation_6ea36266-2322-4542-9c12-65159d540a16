.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "SSL_CTX_SET_READ_AHEAD 3ossl"
.TH SSL_CTX_SET_READ_AHEAD 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
SSL_CTX_set_read_ahead, SSL_CTX_get_read_ahead,
SSL_set_read_ahead, SSL_get_read_ahead,
SSL_CTX_get_default_read_ahead
\&\- manage whether to read as many input bytes as possible
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/ssl.h>
\&
\& void SSL_set_read_ahead(SSL *s, int yes);
\& int SSL_get_read_ahead(const SSL *s);
\&
\& SSL_CTX_set_read_ahead(SSL_CTX *ctx, int yes);
\& long SSL_CTX_get_read_ahead(SSL_CTX *ctx);
\& long SSL_CTX_get_default_read_ahead(SSL_CTX *ctx);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBSSL_CTX_set_read_ahead()\fR and \fBSSL_set_read_ahead()\fR set whether we should read as
many input bytes as possible (for nonblocking reads) or not. For example if
\&\fBx\fR bytes are currently required by OpenSSL, but \fBy\fR bytes are available from
the underlying BIO (where \fBy\fR > \fBx\fR), then OpenSSL will read all \fBy\fR bytes
into its buffer (providing that the buffer is large enough) if reading ahead is
on, or \fBx\fR bytes otherwise.
Setting the parameter \fByes\fR to 0 turns reading ahead is off, other values turn
it on.
\&\fBSSL_CTX_set_default_read_ahead()\fR is identical to \fBSSL_CTX_set_read_ahead()\fR.
.PP
\&\fBSSL_CTX_get_read_ahead()\fR and \fBSSL_get_read_ahead()\fR indicate whether reading
ahead has been set or not.
\&\fBSSL_CTX_get_default_read_ahead()\fR is identical to \fBSSL_CTX_get_read_ahead()\fR.
.SH NOTES
.IX Header "NOTES"
These functions have no impact when used with DTLS. The return values for
\&\fBSSL_CTX_get_read_head()\fR and \fBSSL_get_read_ahead()\fR are undefined for DTLS. Setting
\&\fBread_ahead\fR can impact the behaviour of the \fBSSL_pending()\fR function
(see \fBSSL_pending\fR\|(3)).
.PP
Since \fBSSL_read()\fR can return \fBSSL_ERROR_WANT_READ\fR for non-application data
records, and \fBSSL_has_pending()\fR can't tell the difference between processed and
unprocessed data, it's recommended that if read ahead is turned on that
\&\fBSSL_MODE_AUTO_RETRY\fR is not turned off using \fBSSL_CTX_clear_mode()\fR.
That will prevent getting \fBSSL_ERROR_WANT_READ\fR when there is still a complete
record available that hasn't been processed.
.PP
If the application wants to continue to use the underlying transport (e.g. TCP
connection) after the SSL connection is finished using \fBSSL_shutdown()\fR reading
ahead should be turned off.
Otherwise the SSL structure might read data that it shouldn't.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBSSL_get_read_ahead()\fR and \fBSSL_CTX_get_read_ahead()\fR return 0 if reading ahead is off,
and non zero otherwise.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBssl\fR\|(7), \fBSSL_pending\fR\|(3)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2015\-2020 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
