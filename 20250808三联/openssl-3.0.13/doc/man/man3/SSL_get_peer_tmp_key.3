.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "SSL_GET_PEER_TMP_KEY 3ossl"
.TH SSL_GET_PEER_TMP_KEY 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
SSL_get_peer_tmp_key, SSL_get_server_tmp_key, SSL_get_tmp_key \- get information
about temporary keys used during a handshake
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/ssl.h>
\&
\& long SSL_get_peer_tmp_key(SSL *ssl, EVP_PKEY **key);
\& long SSL_get_server_tmp_key(SSL *ssl, EVP_PKEY **key);
\& long SSL_get_tmp_key(SSL *ssl, EVP_PKEY **key);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBSSL_get_peer_tmp_key()\fR returns the temporary key provided by the peer and
used during key exchange. For example, if ECDHE is in use, then this represents
the peer's public ECDHE key. On success a pointer to the key is stored in
\&\fB*key\fR. It is the caller's responsibility to free this key after use using
\&\fBEVP_PKEY_free\fR\|(3).
.PP
\&\fBSSL_get_server_tmp_key()\fR is a backwards compatibility alias for
\&\fBSSL_get_peer_tmp_key()\fR.
Under that name it worked just on the client side of the connection, its
behaviour on the server end is release-dependent.
.PP
\&\fBSSL_get_tmp_key()\fR returns the equivalent information for the local
end of the connection.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
All these functions return 1 on success and 0 otherwise.
.SH NOTES
.IX Header "NOTES"
This function is implemented as a macro.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBssl\fR\|(7), \fBEVP_PKEY_free\fR\|(3)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2017\-2018 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
