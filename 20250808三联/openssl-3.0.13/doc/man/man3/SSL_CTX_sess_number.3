.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "SSL_CTX_SESS_NUMBER 3ossl"
.TH SSL_CTX_SESS_NUMBER 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
SSL_CTX_sess_number, SSL_CTX_sess_connect, SSL_CTX_sess_connect_good, SSL_CTX_sess_connect_renegotiate, SSL_CTX_sess_accept, SSL_CTX_sess_accept_good, SSL_CTX_sess_accept_renegotiate, SSL_CTX_sess_hits, SSL_CTX_sess_cb_hits, SSL_CTX_sess_misses, SSL_CTX_sess_timeouts, SSL_CTX_sess_cache_full \- obtain session cache statistics
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/ssl.h>
\&
\& long SSL_CTX_sess_number(SSL_CTX *ctx);
\& long SSL_CTX_sess_connect(SSL_CTX *ctx);
\& long SSL_CTX_sess_connect_good(SSL_CTX *ctx);
\& long SSL_CTX_sess_connect_renegotiate(SSL_CTX *ctx);
\& long SSL_CTX_sess_accept(SSL_CTX *ctx);
\& long SSL_CTX_sess_accept_good(SSL_CTX *ctx);
\& long SSL_CTX_sess_accept_renegotiate(SSL_CTX *ctx);
\& long SSL_CTX_sess_hits(SSL_CTX *ctx);
\& long SSL_CTX_sess_cb_hits(SSL_CTX *ctx);
\& long SSL_CTX_sess_misses(SSL_CTX *ctx);
\& long SSL_CTX_sess_timeouts(SSL_CTX *ctx);
\& long SSL_CTX_sess_cache_full(SSL_CTX *ctx);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBSSL_CTX_sess_number()\fR returns the current number of sessions in the internal
session cache.
.PP
\&\fBSSL_CTX_sess_connect()\fR returns the number of started SSL/TLS handshakes in
client mode.
.PP
\&\fBSSL_CTX_sess_connect_good()\fR returns the number of successfully established
SSL/TLS sessions in client mode.
.PP
\&\fBSSL_CTX_sess_connect_renegotiate()\fR returns the number of started renegotiations
in client mode.
.PP
\&\fBSSL_CTX_sess_accept()\fR returns the number of started SSL/TLS handshakes in
server mode.
.PP
\&\fBSSL_CTX_sess_accept_good()\fR returns the number of successfully established
SSL/TLS sessions in server mode.
.PP
\&\fBSSL_CTX_sess_accept_renegotiate()\fR returns the number of started renegotiations
in server mode.
.PP
\&\fBSSL_CTX_sess_hits()\fR returns the number of successfully reused sessions.
In client mode a session set with \fBSSL_set_session\fR\|(3)
successfully reused is counted as a hit. In server mode a session successfully
retrieved from internal or external cache is counted as a hit.
.PP
\&\fBSSL_CTX_sess_cb_hits()\fR returns the number of successfully retrieved sessions
from the external session cache in server mode.
.PP
\&\fBSSL_CTX_sess_misses()\fR returns the number of sessions proposed by clients
that were not found in the internal session cache in server mode.
.PP
\&\fBSSL_CTX_sess_timeouts()\fR returns the number of sessions proposed by clients
and either found in the internal or external session cache in server mode,
 but that were invalid due to timeout. These sessions are not included in
the \fBSSL_CTX_sess_hits()\fR count.
.PP
\&\fBSSL_CTX_sess_cache_full()\fR returns the number of sessions that were removed
because the maximum session cache size was exceeded.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
The functions return the values indicated in the DESCRIPTION section.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBssl\fR\|(7), \fBSSL_set_session\fR\|(3),
\&\fBSSL_CTX_set_session_cache_mode\fR\|(3)
\&\fBSSL_CTX_sess_set_cache_size\fR\|(3)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2001\-2016 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
