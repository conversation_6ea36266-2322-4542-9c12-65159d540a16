.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "EVP_CIPHER_CTX_GET_ORIGINAL_IV 3ossl"
.TH EVP_CIPHER_CTX_GET_ORIGINAL_IV 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
EVP_CIPHER_CTX_get_original_iv, EVP_CIPHER_CTX_get_updated_iv,
EVP_CIPHER_CTX_iv, EVP_CIPHER_CTX_original_iv,
EVP_CIPHER_CTX_iv_noconst \- Routines to inspect EVP_CIPHER_CTX IV data
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/evp.h>
\&
\& int EVP_CIPHER_CTX_get_original_iv(EVP_CIPHER_CTX *ctx, void *buf, size_t len);
\& int EVP_CIPHER_CTX_get_updated_iv(EVP_CIPHER_CTX *ctx, void *buf, size_t len);
.Ve
.PP
The following functions have been deprecated since OpenSSL 3.0, and can be
hidden entirely by defining \fBOPENSSL_API_COMPAT\fR with a suitable version value,
see \fBopenssl_user_macros\fR\|(7):
.PP
.Vb 3
\& const unsigned char *EVP_CIPHER_CTX_iv(const EVP_CIPHER_CTX *ctx);
\& const unsigned char *EVP_CIPHER_CTX_original_iv(const EVP_CIPHER_CTX *ctx);
\& unsigned char *EVP_CIPHER_CTX_iv_noconst(EVP_CIPHER_CTX *ctx);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBEVP_CIPHER_CTX_get_original_iv()\fR and \fBEVP_CIPHER_CTX_get_updated_iv()\fR copy
initialization vector (IV) information from the \fBEVP_CIPHER_CTX\fR into the
caller-supplied buffer. \fBEVP_CIPHER_CTX_get_iv_length\fR\|(3) can be used to
determine an appropriate buffer size, and if the supplied buffer is too small,
an error will be returned (and no data copied).
\&\fBEVP_CIPHER_CTX_get_original_iv()\fR accesses the ("original") IV that was
supplied when the \fBEVP_CIPHER_CTX\fR was initialized, and
\&\fBEVP_CIPHER_CTX_get_updated_iv()\fR accesses the current "IV state"
of the cipher, which is updated during cipher operation for certain cipher modes
(e.g., CBC and OFB).
.PP
The functions \fBEVP_CIPHER_CTX_iv()\fR, \fBEVP_CIPHER_CTX_original_iv()\fR, and
\&\fBEVP_CIPHER_CTX_iv_noconst()\fR are deprecated functions that provide similar (at
a conceptual level) functionality.  \fBEVP_CIPHER_CTX_iv()\fR returns a pointer to
the beginning of the "IV state" as maintained internally in the
\&\fBEVP_CIPHER_CTX\fR; \fBEVP_CIPHER_CTX_original_iv()\fR returns a pointer to the
beginning of the ("original") IV, as maintained by the \fBEVP_CIPHER_CTX\fR, that
was provided when the \fBEVP_CIPHER_CTX\fR was initialized; and
\&\fBEVP_CIPHER_CTX_get_iv_noconst()\fR is the same as \fBEVP_CIPHER_CTX_iv()\fR but has a
different return type for the pointer.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBEVP_CIPHER_CTX_get_original_iv()\fR and \fBEVP_CIPHER_CTX_get_updated_iv()\fR return 1
on success and 0 on failure.
.PP
The functions \fBEVP_CIPHER_CTX_iv()\fR, \fBEVP_CIPHER_CTX_original_iv()\fR, and
\&\fBEVP_CIPHER_CTX_iv_noconst()\fR return a pointer to an IV as an array of bytes on
success, and NULL on failure.
.SH HISTORY
.IX Header "HISTORY"
\&\fBEVP_CIPHER_CTX_get_original_iv()\fR and \fBEVP_CIPHER_CTX_get_updated_iv()\fR were added
in OpenSSL 3.0.0.
.PP
\&\fBEVP_CIPHER_CTX_iv()\fR, \fBEVP_CIPHER_CTX_original_iv()\fR, and
\&\fBEVP_CIPHER_CTX_iv_noconst()\fR were added in OpenSSL 1.1.0, and were deprecated
in OpenSSL 3.0.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2020\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
