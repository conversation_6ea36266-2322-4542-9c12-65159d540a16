.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "BUF_MEM_NEW 3ossl"
.TH BUF_MEM_NEW 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
BUF_MEM_new, BUF_MEM_new_ex, BUF_MEM_free, BUF_MEM_grow,
BUF_MEM_grow_clean, BUF_reverse
\&\- simple character array structure
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/buffer.h>
\&
\& BUF_MEM *BUF_MEM_new(void);
\&
\& BUF_MEM *BUF_MEM_new_ex(unsigned long flags);
\&
\& void BUF_MEM_free(BUF_MEM *a);
\&
\& int BUF_MEM_grow(BUF_MEM *str, int len);
\& size_t BUF_MEM_grow_clean(BUF_MEM *str, size_t len);
\&
\& void BUF_reverse(unsigned char *out, const unsigned char *in, size_t size);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
The buffer library handles simple character arrays. Buffers are used for
various purposes in the library, most notably memory BIOs.
.PP
\&\fBBUF_MEM_new()\fR allocates a new buffer of zero size.
.PP
\&\fBBUF_MEM_new_ex()\fR allocates a buffer with the specified flags.
The flag \fBBUF_MEM_FLAG_SECURE\fR specifies that the \fBdata\fR pointer
should be allocated on the secure heap; see \fBCRYPTO_secure_malloc\fR\|(3).
.PP
\&\fBBUF_MEM_free()\fR frees up an already existing buffer. The data is zeroed
before freeing up in case the buffer contains sensitive data.
.PP
\&\fBBUF_MEM_grow()\fR changes the size of an already existing buffer to
\&\fBlen\fR. Any data already in the buffer is preserved if it increases in
size.
.PP
\&\fBBUF_MEM_grow_clean()\fR is similar to \fBBUF_MEM_grow()\fR but it sets any free'd
or additionally-allocated memory to zero.
.PP
\&\fBBUF_reverse()\fR reverses \fBsize\fR bytes at \fBin\fR into \fBout\fR.  If \fBin\fR
is NULL, the array is reversed in-place.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBBUF_MEM_new()\fR returns the buffer or NULL on error.
.PP
\&\fBBUF_MEM_free()\fR has no return value.
.PP
\&\fBBUF_MEM_grow()\fR and \fBBUF_MEM_grow_clean()\fR return
zero on error or the new size (i.e., \fBlen\fR).
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBbio\fR\|(7),
\&\fBCRYPTO_secure_malloc\fR\|(3).
.SH HISTORY
.IX Header "HISTORY"
The \fBBUF_MEM_new_ex()\fR function was added in OpenSSL 1.1.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2018 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
