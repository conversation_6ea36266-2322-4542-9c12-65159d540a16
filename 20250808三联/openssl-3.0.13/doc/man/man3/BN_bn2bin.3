.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "BN_BN2BIN 3ossl"
.TH BN_BN2BIN 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
BN_bn2binpad,
BN_bn2bin, BN_bin2bn, BN_bn2lebinpad, BN_lebin2bn,
BN_bn2nativepad, BN_native2bn, BN_bn2hex, BN_bn2dec, BN_hex2bn, BN_dec2bn,
BN_print, BN_print_fp, BN_bn2mpi, BN_mpi2bn \- format conversions
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/bn.h>
\&
\& int BN_bn2bin(const BIGNUM *a, unsigned char *to);
\& int BN_bn2binpad(const BIGNUM *a, unsigned char *to, int tolen);
\& BIGNUM *BN_bin2bn(const unsigned char *s, int len, BIGNUM *ret);
\&
\& int BN_bn2lebinpad(const BIGNUM *a, unsigned char *to, int tolen);
\& BIGNUM *BN_lebin2bn(const unsigned char *s, int len, BIGNUM *ret);
\&
\& int BN_bn2nativepad(const BIGNUM *a, unsigned char *to, int tolen);
\& BIGNUM *BN_native2bn(const unsigned char *s, int len, BIGNUM *ret);
\&
\& char *BN_bn2hex(const BIGNUM *a);
\& char *BN_bn2dec(const BIGNUM *a);
\& int BN_hex2bn(BIGNUM **a, const char *str);
\& int BN_dec2bn(BIGNUM **a, const char *str);
\&
\& int BN_print(BIO *fp, const BIGNUM *a);
\& int BN_print_fp(FILE *fp, const BIGNUM *a);
\&
\& int BN_bn2mpi(const BIGNUM *a, unsigned char *to);
\& BIGNUM *BN_mpi2bn(unsigned char *s, int len, BIGNUM *ret);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBBN_bn2bin()\fR converts the absolute value of \fBa\fR into big-endian form
and stores it at \fBto\fR. \fBto\fR must point to BN_num_bytes(\fBa\fR) bytes of
memory.
.PP
\&\fBBN_bn2binpad()\fR also converts the absolute value of \fBa\fR into big-endian form
and stores it at \fBto\fR. \fBtolen\fR indicates the length of the output buffer
\&\fBto\fR. The result is padded with zeros if necessary. If \fBtolen\fR is less than
BN_num_bytes(\fBa\fR) an error is returned.
.PP
\&\fBBN_bin2bn()\fR converts the positive integer in big-endian form of length
\&\fBlen\fR at \fBs\fR into a \fBBIGNUM\fR and places it in \fBret\fR. If \fBret\fR is
NULL, a new \fBBIGNUM\fR is created.
.PP
\&\fBBN_bn2lebinpad()\fR and \fBBN_lebin2bn()\fR are identical to \fBBN_bn2binpad()\fR and
\&\fBBN_bin2bn()\fR except the buffer is in little-endian format.
.PP
\&\fBBN_bn2nativepad()\fR and \fBBN_native2bn()\fR are identical to \fBBN_bn2binpad()\fR and
\&\fBBN_bin2bn()\fR except the buffer is in native format, i.e. most significant
byte first on big-endian platforms, and least significant byte first on
little-endian platforms.
.PP
\&\fBBN_bn2hex()\fR and \fBBN_bn2dec()\fR return printable strings containing the
hexadecimal and decimal encoding of \fBa\fR respectively. For negative
numbers, the string is prefaced with a leading '\-'. The string must be
freed later using \fBOPENSSL_free()\fR.
.PP
\&\fBBN_hex2bn()\fR takes as many characters as possible from the string \fBstr\fR,
including the leading character '\-' which means negative, to form a valid
hexadecimal number representation and converts them to a \fBBIGNUM\fR and
stores it in **\fBa\fR. If *\fBa\fR is NULL, a new \fBBIGNUM\fR is created. If
\&\fBa\fR is NULL, it only computes the length of valid representation.
A "negative zero" is converted to zero.
\&\fBBN_dec2bn()\fR is the same using the decimal system.
.PP
\&\fBBN_print()\fR and \fBBN_print_fp()\fR write the hexadecimal encoding of \fBa\fR,
with a leading '\-' for negative numbers, to the \fBBIO\fR or \fBFILE\fR
\&\fBfp\fR.
.PP
\&\fBBN_bn2mpi()\fR and \fBBN_mpi2bn()\fR convert \fBBIGNUM\fRs from and to a format
that consists of the number's length in bytes represented as a 4\-byte
big-endian number, and the number itself in big-endian format, where
the most significant bit signals a negative number (the representation
of numbers with the MSB set is prefixed with null byte).
.PP
\&\fBBN_bn2mpi()\fR stores the representation of \fBa\fR at \fBto\fR, where \fBto\fR
must be large enough to hold the result. The size can be determined by
calling BN_bn2mpi(\fBa\fR, NULL).
.PP
\&\fBBN_mpi2bn()\fR converts the \fBlen\fR bytes long representation at \fBs\fR to
a \fBBIGNUM\fR and stores it at \fBret\fR, or in a newly allocated \fBBIGNUM\fR
if \fBret\fR is NULL.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBBN_bn2bin()\fR returns the length of the big-endian number placed at \fBto\fR.
\&\fBBN_bin2bn()\fR returns the \fBBIGNUM\fR, NULL on error.
.PP
\&\fBBN_bn2binpad()\fR, \fBBN_bn2lebinpad()\fR, and \fBBN_bn2nativepad()\fR return the number of bytes written or \-1 if the supplied
buffer is too small.
.PP
\&\fBBN_bn2hex()\fR and \fBBN_bn2dec()\fR return a NUL-terminated string, or NULL
on error. \fBBN_hex2bn()\fR and \fBBN_dec2bn()\fR return the number of characters
used in parsing, or 0 on error, in which
case no new \fBBIGNUM\fR will be created.
.PP
\&\fBBN_print_fp()\fR and \fBBN_print()\fR return 1 on success, 0 on write errors.
.PP
\&\fBBN_bn2mpi()\fR returns the length of the representation. \fBBN_mpi2bn()\fR
returns the \fBBIGNUM\fR, and NULL on error.
.PP
The error codes can be obtained by \fBERR_get_error\fR\|(3).
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBERR_get_error\fR\|(3), \fBBN_zero\fR\|(3),
\&\fBASN1_INTEGER_to_BN\fR\|(3),
\&\fBBN_num_bytes\fR\|(3)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
