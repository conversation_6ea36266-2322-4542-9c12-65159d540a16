.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "SRP_VBASE_NEW 3ossl"
.TH SRP_VBASE_NEW 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
SRP_VBASE_new,
SRP_VBASE_free,
SRP_VBASE_init,
SRP_VBASE_add0_user,
SRP_VBASE_get1_by_user,
SRP_VBASE_get_by_user
\&\- Functions to create and manage a stack of SRP user verifier information
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/srp.h>
.Ve
.PP
The following functions have been deprecated since OpenSSL 3.0, and can be
hidden entirely by defining \fBOPENSSL_API_COMPAT\fR with a suitable version value,
see \fBopenssl_user_macros\fR\|(7):
.PP
.Vb 2
\& SRP_VBASE *SRP_VBASE_new(char *seed_key);
\& void SRP_VBASE_free(SRP_VBASE *vb);
\&
\& int SRP_VBASE_init(SRP_VBASE *vb, char *verifier_file);
\&
\& int SRP_VBASE_add0_user(SRP_VBASE *vb, SRP_user_pwd *user_pwd);
\& SRP_user_pwd *SRP_VBASE_get1_by_user(SRP_VBASE *vb, char *username);
\& SRP_user_pwd *SRP_VBASE_get_by_user(SRP_VBASE *vb, char *username);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
All of the functions described on this page are deprecated. There are no
available replacement functions at this time.
.PP
The \fBSRP_VBASE_new()\fR function allocates a structure to store server side SRP
verifier information.
If \fBseed_key\fR is not NULL a copy is stored and used to generate dummy parameters
for users that are not found by \fBSRP_VBASE_get1_by_user()\fR. This allows the server
to hide the fact that it doesn't have a verifier for a particular username,
as described in section ******* 'Unknown SRP' of RFC 5054.
The seed string should contain random NUL terminated binary data (therefore
the random data should not contain NUL bytes!).
.PP
The \fBSRP_VBASE_free()\fR function frees up the \fBvb\fR structure.
If \fBvb\fR is NULL, nothing is done.
.PP
The \fBSRP_VBASE_init()\fR function parses the information in a verifier file and
populates the \fBvb\fR structure.
The verifier file is a text file containing multiple entries, whose format is:
flag base64(verifier) base64(salt) username gNid userinfo(optional)
where the flag can be 'V' (valid) or 'R' (revoked).
Note that the base64 encoding used here is non-standard so it is recommended
to use \fBopenssl\-srp\fR\|(1) to generate this file.
.PP
The \fBSRP_VBASE_add0_user()\fR function adds the \fBuser_pwd\fR verifier information
to the \fBvb\fR structure. See \fBSRP_user_pwd_new\fR\|(3) to create and populate this
record.
The library takes ownership of \fBuser_pwd\fR, it should not be freed by the caller.
.PP
The \fBSRP_VBASE_get1_by_user()\fR function returns the password info for the user
whose username matches \fBusername\fR. It replaces the deprecated
\&\fBSRP_VBASE_get_by_user()\fR.
If no matching user is found but a seed_key and default gN parameters have been
set, dummy authentication information is generated from the seed_key, allowing
the server to hide the fact that it doesn't have a verifier for a particular
username. When using SRP as a TLS authentication mechanism, this will cause
the handshake to proceed normally but the first client will be rejected with
a "bad_record_mac" alert, as if the password was incorrect.
If no matching user is found and the seed_key is not set, NULL is returned.
Ownership of the returned pointer is released to the caller, it must be freed
with \fBSRP_user_pwd_free()\fR.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBSRP_VBASE_init()\fR returns \fBSRP_NO_ERROR\fR (0) on success and a positive value
on failure.
The error codes are \fBSRP_ERR_OPEN_FILE\fR if the file could not be opened,
\&\fBSRP_ERR_VBASE_INCOMPLETE_FILE\fR if the file could not be parsed,
\&\fBSRP_ERR_MEMORY\fR on memory allocation failure and \fBSRP_ERR_VBASE_BN_LIB\fR
for invalid decoded parameter values.
.PP
\&\fBSRP_VBASE_add0_user()\fR returns 1 on success and 0 on failure.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBopenssl\-srp\fR\|(1),
\&\fBSRP_create_verifier\fR\|(3),
\&\fBSRP_user_pwd_new\fR\|(3),
\&\fBSSL_CTX_set_srp_password\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
The \fBSRP_VBASE_add0_user()\fR function was added in OpenSSL 3.0.
.PP
All other functions were added in OpenSSL 1.0.1.
.PP
All of these functions were deprecated in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2018\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
