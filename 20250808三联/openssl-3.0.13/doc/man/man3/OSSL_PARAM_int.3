.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "OSSL_PARAM_INT 3ossl"
.TH OSSL_PARAM_INT 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
OSSL_PARAM_double, OSSL_PARAM_int, OSSL_PARAM_int32, OSSL_PARAM_int64,
OSSL_PARAM_long, OSSL_PARAM_size_t, OSSL_PARAM_time_t, OSSL_PARAM_uint,
OSSL_PARAM_uint32, OSSL_PARAM_uint64, OSSL_PARAM_ulong, OSSL_PARAM_BN,
OSSL_PARAM_utf8_string, OSSL_PARAM_octet_string, OSSL_PARAM_utf8_ptr,
OSSL_PARAM_octet_ptr,
OSSL_PARAM_END, OSSL_PARAM_DEFN,
OSSL_PARAM_construct_double, OSSL_PARAM_construct_int,
OSSL_PARAM_construct_int32, OSSL_PARAM_construct_int64,
OSSL_PARAM_construct_long, OSSL_PARAM_construct_size_t,
OSSL_PARAM_construct_time_t, OSSL_PARAM_construct_uint,
OSSL_PARAM_construct_uint32, OSSL_PARAM_construct_uint64,
OSSL_PARAM_construct_ulong, OSSL_PARAM_construct_BN,
OSSL_PARAM_construct_utf8_string, OSSL_PARAM_construct_utf8_ptr,
OSSL_PARAM_construct_octet_string, OSSL_PARAM_construct_octet_ptr,
OSSL_PARAM_construct_end,
OSSL_PARAM_locate, OSSL_PARAM_locate_const,
OSSL_PARAM_get_double, OSSL_PARAM_get_int, OSSL_PARAM_get_int32,
OSSL_PARAM_get_int64, OSSL_PARAM_get_long, OSSL_PARAM_get_size_t,
OSSL_PARAM_get_time_t, OSSL_PARAM_get_uint, OSSL_PARAM_get_uint32,
OSSL_PARAM_get_uint64, OSSL_PARAM_get_ulong, OSSL_PARAM_get_BN,
OSSL_PARAM_get_utf8_string, OSSL_PARAM_get_octet_string,
OSSL_PARAM_get_utf8_ptr, OSSL_PARAM_get_octet_ptr,
OSSL_PARAM_get_utf8_string_ptr, OSSL_PARAM_get_octet_string_ptr,
OSSL_PARAM_set_double, OSSL_PARAM_set_int, OSSL_PARAM_set_int32,
OSSL_PARAM_set_int64, OSSL_PARAM_set_long, OSSL_PARAM_set_size_t,
OSSL_PARAM_set_time_t, OSSL_PARAM_set_uint, OSSL_PARAM_set_uint32,
OSSL_PARAM_set_uint64, OSSL_PARAM_set_ulong, OSSL_PARAM_set_BN,
OSSL_PARAM_set_utf8_string, OSSL_PARAM_set_octet_string,
OSSL_PARAM_set_utf8_ptr, OSSL_PARAM_set_octet_ptr,
OSSL_PARAM_UNMODIFIED, OSSL_PARAM_modified, OSSL_PARAM_set_all_unmodified
\&\- OSSL_PARAM helpers
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/params.h>
\&
\& /*
\&  * TYPE in function names is one of:
\&  * double, int, int32, int64, long, size_t, time_t, uint, uint32, uint64, ulong
\&  * Corresponding TYPE in function arguments is one of:
\&  * double, int, int32_t, int64_t, long, size_t, time_t, unsigned int, uint32_t,
\&  * uint64_t, unsigned long
\&  */
\&
\& #define OSSL_PARAM_TYPE(key, address)
\& #define OSSL_PARAM_BN(key, address, size)
\& #define OSSL_PARAM_utf8_string(key, address, size)
\& #define OSSL_PARAM_octet_string(key, address, size)
\& #define OSSL_PARAM_utf8_ptr(key, address, size)
\& #define OSSL_PARAM_octet_ptr(key, address, size)
\& #define OSSL_PARAM_END
\&
\& #define OSSL_PARAM_UNMODIFIED
\&
\& #define OSSL_PARAM_DEFN(key, type, addr, sz)    \e
\&    { (key), (type), (addr), (sz), OSSL_PARAM_UNMODIFIED }
\&
\& OSSL_PARAM OSSL_PARAM_construct_TYPE(const char *key, TYPE *buf);
\& OSSL_PARAM OSSL_PARAM_construct_BN(const char *key, unsigned char *buf,
\&                                    size_t bsize);
\& OSSL_PARAM OSSL_PARAM_construct_utf8_string(const char *key, char *buf,
\&                                             size_t bsize);
\& OSSL_PARAM OSSL_PARAM_construct_octet_string(const char *key, void *buf,
\&                                              size_t bsize);
\& OSSL_PARAM OSSL_PARAM_construct_utf8_ptr(const char *key, char **buf,
\&                                          size_t bsize);
\& OSSL_PARAM OSSL_PARAM_construct_octet_ptr(const char *key, void **buf,
\&                                           size_t bsize);
\& OSSL_PARAM OSSL_PARAM_construct_end(void);
\&
\& OSSL_PARAM *OSSL_PARAM_locate(OSSL_PARAM *array, const char *key);
\& const OSSL_PARAM *OSSL_PARAM_locate_const(const OSSL_PARAM *array,
\&                                           const char *key);
\&
\& int OSSL_PARAM_get_TYPE(const OSSL_PARAM *p, TYPE *val);
\& int OSSL_PARAM_set_TYPE(OSSL_PARAM *p, TYPE val);
\&
\& int OSSL_PARAM_get_BN(const OSSL_PARAM *p, BIGNUM **val);
\& int OSSL_PARAM_set_BN(OSSL_PARAM *p, const BIGNUM *val);
\&
\& int OSSL_PARAM_get_utf8_string(const OSSL_PARAM *p, char **val,
\&                                size_t max_len);
\& int OSSL_PARAM_set_utf8_string(OSSL_PARAM *p, const char *val);
\&
\& int OSSL_PARAM_get_octet_string(const OSSL_PARAM *p, void **val,
\&                                 size_t max_len, size_t *used_len);
\& int OSSL_PARAM_set_octet_string(OSSL_PARAM *p, const void *val, size_t len);
\&
\& int OSSL_PARAM_get_utf8_ptr(const OSSL_PARAM *p, const char **val);
\& int OSSL_PARAM_set_utf8_ptr(OSSL_PARAM *p, const char *val);
\&
\& int OSSL_PARAM_get_octet_ptr(const OSSL_PARAM *p, const void **val,
\&                              size_t *used_len);
\& int OSSL_PARAM_set_octet_ptr(OSSL_PARAM *p, const void *val,
\&                              size_t used_len);
\&
\& int OSSL_PARAM_get_utf8_string_ptr(const OSSL_PARAM *p, const char **val);
\& int OSSL_PARAM_get_octet_string_ptr(const OSSL_PARAM *p, const void **val,
\&                                     size_t *used_len);
\&
\& int OSSL_PARAM_modified(const OSSL_PARAM *param);
\& void OSSL_PARAM_set_all_unmodified(OSSL_PARAM *params);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
A collection of utility functions that simplify and add type safety to the
\&\fBOSSL_PARAM\fR\|(3) arrays.  The following \fR\f(BITYPE\fR\fB\fR names are supported:
.IP \(bu 2
double
.IP \(bu 2
int
.IP \(bu 2
int32 (int32_t)
.IP \(bu 2
int64 (int64_t)
.IP \(bu 2
long int (long)
.IP \(bu 2
time_t
.IP \(bu 2
size_t
.IP \(bu 2
uint32 (uint32_t)
.IP \(bu 2
uint64 (uint64_t)
.IP \(bu 2
unsigned int (uint)
.IP \(bu 2
unsigned long int (ulong)
.PP
\&\fBOSSL_PARAM_TYPE()\fR are a series of macros designed to assist initialising an
array of \fBOSSL_PARAM\fR\|(3) structures.
Each of these macros defines a parameter of the specified \fR\f(BITYPE\fR\fB\fR with the
provided \fIkey\fR and parameter variable \fIaddress\fR.
.PP
\&\fBOSSL_PARAM_utf8_string()\fR, \fBOSSL_PARAM_octet_string()\fR, \fBOSSL_PARAM_utf8_ptr()\fR,
\&\fBOSSL_PARAM_octet_ptr()\fR, \fBOSSL_PARAM_BN()\fR are macros that provide support
for defining UTF8 strings, OCTET strings and big numbers.
A parameter with name \fIkey\fR is defined.
The storage for this parameter is at \fIaddress\fR and is of \fIsize\fR bytes.
.PP
OSSL_PARAM_END provides an end of parameter list marker.
This should terminate all \fBOSSL_PARAM\fR\|(3) arrays.
.PP
The \fBOSSL_PARAM_DEFN()\fR macro provides the ability to construct a single
\&\fBOSSL_PARAM\fR\|(3) (typically used in the construction of \fBOSSL_PARAM\fR arrays). The
\&\fIkey\fR, \fItype\fR, \fIaddr\fR and \fIsz\fR arguments correspond to the \fIkey\fR,
\&\fIdata_type\fR, \fIdata\fR and \fIdata_size\fR fields of the \fBOSSL_PARAM\fR\|(3) structure as
described on the \fBOSSL_PARAM\fR\|(3) page.
.PP
\&\fBOSSL_PARAM_construct_TYPE()\fR are a series of functions that create \fBOSSL_PARAM\fR\|(3)
records dynamically.
A parameter with name \fIkey\fR is created.
The parameter will use storage pointed to by \fIbuf\fR and return size of \fIret\fR.
.PP
\&\fBOSSL_PARAM_construct_BN()\fR is a function that constructs a large integer
\&\fBOSSL_PARAM\fR\|(3) structure.
A parameter with name \fIkey\fR, storage \fIbuf\fR, size \fIbsize\fR and return
size \fIrsize\fR is created.
.PP
\&\fBOSSL_PARAM_construct_utf8_string()\fR is a function that constructs a UTF8
string \fBOSSL_PARAM\fR\|(3) structure.
A parameter with name \fIkey\fR, storage \fIbuf\fR and size \fIbsize\fR is created.
If \fIbsize\fR is zero, the string length is determined using \fBstrlen\fR\|(3).
Generally pass zero for \fIbsize\fR instead of calling \fBstrlen\fR\|(3) yourself.
.PP
\&\fBOSSL_PARAM_construct_octet_string()\fR is a function that constructs an OCTET
string \fBOSSL_PARAM\fR\|(3) structure.
A parameter with name \fIkey\fR, storage \fIbuf\fR and size \fIbsize\fR is created.
.PP
\&\fBOSSL_PARAM_construct_utf8_ptr()\fR is a function that constructs a UTF8 string
pointer \fBOSSL_PARAM\fR\|(3) structure.
A parameter with name \fIkey\fR, storage pointer \fI*buf\fR and size \fIbsize\fR
is created.
.PP
\&\fBOSSL_PARAM_construct_octet_ptr()\fR is a function that constructs an OCTET string
pointer \fBOSSL_PARAM\fR\|(3) structure.
A parameter with name \fIkey\fR, storage pointer \fI*buf\fR and size \fIbsize\fR
is created.
.PP
\&\fBOSSL_PARAM_construct_end()\fR is a function that constructs the terminating
\&\fBOSSL_PARAM\fR\|(3) structure.
.PP
\&\fBOSSL_PARAM_locate()\fR is a function that searches an \fIarray\fR of parameters for
the one matching the \fIkey\fR name.
.PP
\&\fBOSSL_PARAM_locate_const()\fR behaves exactly like \fBOSSL_PARAM_locate()\fR except for
the presence of \fIconst\fR for the \fIarray\fR argument and its return value.
.PP
\&\fBOSSL_PARAM_get_TYPE()\fR retrieves a value of type \fR\f(BITYPE\fR\fB\fR from the parameter
\&\fIp\fR.
The value is copied to the address \fIval\fR.
Type coercion takes place as discussed in the NOTES section.
.PP
\&\fBOSSL_PARAM_set_TYPE()\fR stores a value \fIval\fR of type \fR\f(BITYPE\fR\fB\fR into the
parameter \fIp\fR.
If the parameter's \fIdata\fR field is NULL, then only its \fIreturn_size\fR field
will be assigned the size the parameter's \fIdata\fR buffer should have.
Type coercion takes place as discussed in the NOTES section.
.PP
\&\fBOSSL_PARAM_get_BN()\fR retrieves a BIGNUM from the parameter pointed to by \fIp\fR.
The BIGNUM referenced by \fIval\fR is updated and is allocated if \fI*val\fR is
NULL.
.PP
\&\fBOSSL_PARAM_set_BN()\fR stores the BIGNUM \fIval\fR into the parameter \fIp\fR.
If the parameter's \fIdata\fR field is NULL, then only its \fIreturn_size\fR field
will be assigned the size the parameter's \fIdata\fR buffer should have.
.PP
\&\fBOSSL_PARAM_get_utf8_string()\fR retrieves a UTF8 string from the parameter
pointed to by \fIp\fR.
The string is stored into \fI*val\fR with a size limit of \fImax_len\fR,
which must be large enough to accommodate a terminating NUL byte,
otherwise this function will fail.
If \fI*val\fR is NULL, memory is allocated for the string (including the
terminating  NUL byte) and \fImax_len\fR is ignored.
If memory is allocated by this function, it must be freed by the caller.
.PP
\&\fBOSSL_PARAM_set_utf8_string()\fR sets a UTF8 string from the parameter pointed to
by \fIp\fR to the value referenced by \fIval\fR.
If the parameter's \fIdata\fR field isn't NULL, its \fIdata_size\fR must indicate
that the buffer is large enough to accommodate the string that \fIval\fR points at,
not including the terminating NUL byte, or this function will fail.
A terminating NUL byte is added only if the parameter's \fIdata_size\fR indicates
the buffer is longer than the string length, otherwise the string will not be
NUL terminated.
If the parameter's \fIdata\fR field is NULL, then only its \fIreturn_size\fR field
will be assigned the minimum size the parameter's \fIdata\fR buffer should have
to accommodate the string, not including a terminating NUL byte.
.PP
\&\fBOSSL_PARAM_get_octet_string()\fR retrieves an OCTET string from the parameter
pointed to by \fIp\fR.
The OCTETs are either stored into \fI*val\fR with a length limit of \fImax_len\fR or,
in the case when \fI*val\fR is NULL, memory is allocated and
\&\fImax_len\fR is ignored. \fI*used_len\fR is populated with the number of OCTETs
stored. If \fIval\fR is NULL then the OCTETS are not stored, but \fI*used_len\fR is
still populated.
If memory is allocated by this function, it must be freed by the caller.
.PP
\&\fBOSSL_PARAM_set_octet_string()\fR sets an OCTET string from the parameter
pointed to by \fIp\fR to the value referenced by \fIval\fR.
If the parameter's \fIdata\fR field is NULL, then only its \fIreturn_size\fR field
will be assigned the size the parameter's \fIdata\fR buffer should have.
.PP
\&\fBOSSL_PARAM_get_utf8_ptr()\fR retrieves the UTF8 string pointer from the parameter
referenced by \fIp\fR and stores it in \fI*val\fR.
.PP
\&\fBOSSL_PARAM_set_utf8_ptr()\fR sets the UTF8 string pointer in the parameter
referenced by \fIp\fR to the values \fIval\fR.
.PP
\&\fBOSSL_PARAM_get_octet_ptr()\fR retrieves the OCTET string pointer from the parameter
referenced by \fIp\fR and stores it in \fI*val\fR.
The length of the OCTET string is stored in \fI*used_len\fR.
.PP
\&\fBOSSL_PARAM_set_octet_ptr()\fR sets the OCTET string pointer in the parameter
referenced by \fIp\fR to the values \fIval\fR.
The length of the OCTET string is provided by \fIused_len\fR.
.PP
\&\fBOSSL_PARAM_get_utf8_string_ptr()\fR retrieves the pointer to a UTF8 string from
the parameter pointed to by \fIp\fR, and stores that pointer in \fI*val\fR.
This is different from \fBOSSL_PARAM_get_utf8_string()\fR, which copies the
string.
.PP
\&\fBOSSL_PARAM_get_octet_string_ptr()\fR retrieves the pointer to a octet string
from the parameter pointed to by \fIp\fR, and stores that pointer in \fI*val\fR,
along with the string's length in \fI*used_len\fR.
This is different from \fBOSSL_PARAM_get_octet_string()\fR, which copies the
string.
.PP
The OSSL_PARAM_UNMODIFIED macro is used to detect if a parameter was set.  On
creation, via either the macros or construct calls, the \fIreturn_size\fR field
is set to this.  If the parameter is set using the calls defined herein, the
\&\fIreturn_size\fR field is changed.
.PP
\&\fBOSSL_PARAM_modified()\fR queries if the parameter \fIparam\fR has been set or not
using the calls defined herein.
.PP
\&\fBOSSL_PARAM_set_all_unmodified()\fR resets the unused indicator for all parameters
in the array \fIparams\fR.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBOSSL_PARAM_construct_TYPE()\fR, \fBOSSL_PARAM_construct_BN()\fR,
\&\fBOSSL_PARAM_construct_utf8_string()\fR, \fBOSSL_PARAM_construct_octet_string()\fR,
\&\fBOSSL_PARAM_construct_utf8_ptr()\fR and \fBOSSL_PARAM_construct_octet_ptr()\fR
return a populated \fBOSSL_PARAM\fR\|(3) structure.
.PP
\&\fBOSSL_PARAM_locate()\fR and \fBOSSL_PARAM_locate_const()\fR return a pointer to
the matching \fBOSSL_PARAM\fR\|(3) object.  They return NULL on error or when
no object matching \fIkey\fR exists in the \fIarray\fR.
.PP
\&\fBOSSL_PARAM_modified()\fR returns 1 if the parameter was set and 0 otherwise.
.PP
All other functions return 1 on success and 0 on failure.
.SH NOTES
.IX Header "NOTES"
Native types will be converted as required only if the value is exactly
representable by the target type or parameter.
Apart from that, the functions must be used appropriately for the
expected type of the parameter.
.PP
\&\fBOSSL_PARAM_get_BN()\fR and \fBOSSL_PARAM_set_BN()\fR currently only support
nonnegative \fBBIGNUM\fRs, and by consequence, only
\&\fBOSSL_PARAM_UNSIGNED_INTEGER\fR.  \fBOSSL_PARAM_construct_BN()\fR currently
constructs an \fBOSSL_PARAM\fR\|(3) structure with the data type
\&\fBOSSL_PARAM_UNSIGNED_INTEGER\fR.
.PP
For \fBOSSL_PARAM_construct_utf8_ptr()\fR and \fBOSSL_PARAM_consstruct_octet_ptr()\fR,
\&\fIbsize\fR is not relevant if the purpose is to send the \fBOSSL_PARAM\fR\|(3) array
to a \fIresponder\fR, i.e. to get parameter data back.
In that case, \fIbsize\fR can safely be given zero.
See "DESCRIPTION" in \fBOSSL_PARAM\fR\|(3) for further information on the
possible purposes.
.SH EXAMPLES
.IX Header "EXAMPLES"
Reusing the examples from \fBOSSL_PARAM\fR\|(3) to just show how
\&\fBOSSL_PARAM\fR\|(3) arrays can be handled using the macros and functions
defined herein.
.SS "Example 1"
.IX Subsection "Example 1"
This example is for setting parameters on some object:
.PP
.Vb 1
\&    #include <openssl/core.h>
\&
\&    const char *foo = "some string";
\&    size_t foo_l = strlen(foo);
\&    const char bar[] = "some other string";
\&    const OSSL_PARAM set[] = {
\&        OSSL_PARAM_utf8_ptr("foo", &foo, foo_l),
\&        OSSL_PARAM_utf8_string("bar", bar, sizeof(bar) \- 1),
\&        OSSL_PARAM_END
\&    };
.Ve
.SS "Example 2"
.IX Subsection "Example 2"
This example is for requesting parameters on some object, and also
demonstrates that the requester isn't obligated to request all
available parameters:
.PP
.Vb 7
\&    const char *foo = NULL;
\&    char bar[1024];
\&    OSSL_PARAM request[] = {
\&        OSSL_PARAM_utf8_ptr("foo", &foo, 0),
\&        OSSL_PARAM_utf8_string("bar", bar, sizeof(bar)),
\&        OSSL_PARAM_END
\&    };
.Ve
.PP
A \fIresponder\fR that receives this array (as \f(CW\*(C`params\*(C'\fR in this example)
could fill in the parameters like this:
.PP
.Vb 1
\&    /* OSSL_PARAM *params */
\&
\&    OSSL_PARAM *p;
\&
\&    if ((p = OSSL_PARAM_locate(params, "foo")) != NULL)
\&        OSSL_PARAM_set_utf8_ptr(p, "foo value");
\&    if ((p = OSSL_PARAM_locate(params, "bar")) != NULL)
\&        OSSL_PARAM_set_utf8_string(p, "bar value");
\&    if ((p = OSSL_PARAM_locate(params, "cookie")) != NULL)
\&        OSSL_PARAM_set_utf8_ptr(p, "cookie value");
.Ve
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBopenssl\-core.h\fR\|(7), \fBOSSL_PARAM\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
These APIs were introduced in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2019\-2023 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
