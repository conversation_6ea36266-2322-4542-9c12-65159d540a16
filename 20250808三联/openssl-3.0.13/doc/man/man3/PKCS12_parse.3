.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "PKCS12_PARSE 3ossl"
.TH PKCS12_PARSE 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
PKCS12_parse \- parse a PKCS#12 structure
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/pkcs12.h>
\&
\& int PKCS12_parse(PKCS12 *p12, const char *pass, EVP_PKEY **pkey, X509 **cert,
\&                  STACK_OF(X509) **ca);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBPKCS12_parse()\fR parses a PKCS12 structure.
.PP
\&\fBp12\fR is the \fBPKCS12\fR structure to parse. \fBpass\fR is the passphrase to use.
If successful the private key will be written to \fB*pkey\fR, the corresponding
certificate to \fB*cert\fR and any additional certificates to \fB*ca\fR.
.SH NOTES
.IX Header "NOTES"
Each of the parameters \fBpkey\fR, \fBcert\fR, and \fBca\fR can be NULL in which case
the private key, the corresponding certificate, or the additional certificates,
respectively, will be discarded.
If any of \fBpkey\fR and \fBcert\fR is non-NULL the variable it points to is
initialized.
If \fBca\fR is non-NULL and \fB*ca\fR is NULL a new STACK will be allocated.
If \fBca\fR is non-NULL and \fB*ca\fR is a valid STACK
then additional certificates are appended in the given order to \fB*ca\fR.
.PP
The \fBfriendlyName\fR and \fBlocalKeyID\fR attributes (if present) on each
certificate will be stored in the \fBalias\fR and \fBkeyid\fR attributes of the
\&\fBX509\fR structure.
.PP
The parameter \fBpass\fR is interpreted as a string in the UTF\-8 encoding. If it
is not valid UTF\-8, then it is assumed to be ISO8859\-1 instead.
.PP
In particular, this means that passwords in the locale character set
(or code page on Windows) must potentially be converted to UTF\-8 before
use. This may include passwords from local text files, or input from
the terminal or command line. Refer to the documentation of
\&\fBUI_OpenSSL\fR\|(3), for example.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBPKCS12_parse()\fR returns 1 for success and zero if an error occurred.
.PP
The error can be obtained from \fBERR_get_error\fR\|(3)
.SH BUGS
.IX Header "BUGS"
Only a single private key and corresponding certificate is returned by this
function. More complex PKCS#12 files with multiple private keys will only
return the first match.
.PP
Only \fBfriendlyName\fR and \fBlocalKeyID\fR attributes are currently stored in
certificates. Other attributes are discarded.
.PP
Attributes currently cannot be stored in the private key \fBEVP_PKEY\fR structure.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBd2i_PKCS12\fR\|(3),
\&\fBpassphrase\-encoding\fR\|(7)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2002\-2020 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
