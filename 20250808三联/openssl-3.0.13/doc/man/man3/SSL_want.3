.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "SSL_WANT 3ossl"
.TH SSL_WANT 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
SSL_want, SSL_want_nothing, SSL_want_read, SSL_want_write,
SSL_want_x509_lookup, SSL_want_retry_verify, SSL_want_async, SSL_want_async_job,
SSL_want_client_hello_cb \- obtain state information TLS/SSL I/O operation
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/ssl.h>
\&
\& int SSL_want(const SSL *ssl);
\& int SSL_want_nothing(const SSL *ssl);
\& int SSL_want_read(const SSL *ssl);
\& int SSL_want_write(const SSL *ssl);
\& int SSL_want_x509_lookup(const SSL *ssl);
\& int SSL_want_retry_verify(const SSL *ssl);
\& int SSL_want_async(const SSL *ssl);
\& int SSL_want_async_job(const SSL *ssl);
\& int SSL_want_client_hello_cb(const SSL *ssl);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBSSL_want()\fR returns state information for the SSL object \fBssl\fR.
.PP
The other SSL_want_*() calls are shortcuts for the possible states returned
by \fBSSL_want()\fR.
.SH NOTES
.IX Header "NOTES"
\&\fBSSL_want()\fR examines the internal state information of the SSL object. Its
return values are similar to that of \fBSSL_get_error\fR\|(3).
Unlike \fBSSL_get_error\fR\|(3), which also evaluates the
error queue, the results are obtained by examining an internal state flag
only. The information must therefore only be used for normal operation under
nonblocking I/O. Error conditions are not handled and must be treated
using \fBSSL_get_error\fR\|(3).
.PP
The result returned by \fBSSL_want()\fR should always be consistent with
the result of \fBSSL_get_error\fR\|(3).
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
The following return values can currently occur for \fBSSL_want()\fR:
.IP SSL_NOTHING 4
.IX Item "SSL_NOTHING"
There is no data to be written or to be read.
.IP SSL_WRITING 4
.IX Item "SSL_WRITING"
There are data in the SSL buffer that must be written to the underlying
\&\fBBIO\fR layer in order to complete the actual SSL_*() operation.
A call to \fBSSL_get_error\fR\|(3) should return \fBSSL_ERROR_WANT_WRITE\fR.
.IP SSL_READING 4
.IX Item "SSL_READING"
More data must be read from the underlying \fBBIO\fR layer in order to
complete the actual SSL_*() operation.
A call to \fBSSL_get_error\fR\|(3) should return \fBSSL_ERROR_WANT_READ\fR.
.IP SSL_X509_LOOKUP 4
.IX Item "SSL_X509_LOOKUP"
The operation did not complete because an application callback set by
\&\fBSSL_CTX_set_client_cert_cb()\fR has asked to be called again.
A call to \fBSSL_get_error\fR\|(3) should return \fBSSL_ERROR_WANT_X509_LOOKUP\fR.
.IP SSL_RETRY_VERIFY 4
.IX Item "SSL_RETRY_VERIFY"
The operation did not complete because a certificate verification callback
has asked to be called again via \fBSSL_set_retry_verify\fR\|(3).
A call to \fBSSL_get_error\fR\|(3) should return \fBSSL_ERROR_WANT_RETRY_VERIFY\fR.
.IP SSL_ASYNC_PAUSED 4
.IX Item "SSL_ASYNC_PAUSED"
An asynchronous operation partially completed and was then paused. See
\&\fBSSL_get_all_async_fds\fR\|(3). A call to \fBSSL_get_error\fR\|(3) should return
\&\fBSSL_ERROR_WANT_ASYNC\fR.
.IP SSL_ASYNC_NO_JOBS 4
.IX Item "SSL_ASYNC_NO_JOBS"
The asynchronous job could not be started because there were no async jobs
available in the pool (see \fBASYNC_init_thread\fR\|(3)). A call to \fBSSL_get_error\fR\|(3)
should return \fBSSL_ERROR_WANT_ASYNC_JOB\fR.
.IP SSL_CLIENT_HELLO_CB 4
.IX Item "SSL_CLIENT_HELLO_CB"
The operation did not complete because an application callback set by
\&\fBSSL_CTX_set_client_hello_cb()\fR has asked to be called again.
A call to \fBSSL_get_error\fR\|(3) should return \fBSSL_ERROR_WANT_CLIENT_HELLO_CB\fR.
.PP
\&\fBSSL_want_nothing()\fR, \fBSSL_want_read()\fR, \fBSSL_want_write()\fR,
\&\fBSSL_want_x509_lookup()\fR, \fBSSL_want_retry_verify()\fR,
\&\fBSSL_want_async()\fR, \fBSSL_want_async_job()\fR, and \fBSSL_want_client_hello_cb()\fR
return 1 when the corresponding condition is true or 0 otherwise.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBssl\fR\|(7), \fBSSL_get_error\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
The \fBSSL_want_client_hello_cb()\fR function and the SSL_CLIENT_HELLO_CB return value
were added in OpenSSL 1.1.1.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2001\-2022 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
