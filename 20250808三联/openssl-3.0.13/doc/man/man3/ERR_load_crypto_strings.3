.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "ERR_LOAD_CRYPTO_STRINGS 3ossl"
.TH ERR_LOAD_CRYPTO_STRINGS 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
ERR_load_crypto_strings, SSL_load_error_strings, ERR_free_strings \-
load and free error strings
.SH SYNOPSIS
.IX Header "SYNOPSIS"
The following functions have been deprecated since OpenSSL 1.1.0, and can be
hidden entirely by defining \fBOPENSSL_API_COMPAT\fR with a suitable version value,
see \fBopenssl_user_macros\fR\|(7):
.PP
.Vb 1
\& #include <openssl/err.h>
\&
\& void ERR_load_crypto_strings(void);
\& void ERR_free_strings(void);
\&
\& #include <openssl/ssl.h>
\&
\& void SSL_load_error_strings(void);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBERR_load_crypto_strings()\fR registers the error strings for all
\&\fBlibcrypto\fR functions. \fBSSL_load_error_strings()\fR does the same,
but also registers the \fBlibssl\fR error strings.
.PP
In versions prior to OpenSSL 1.1.0,
\&\fBERR_free_strings()\fR releases any resources created by the above functions.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBERR_load_crypto_strings()\fR, \fBSSL_load_error_strings()\fR and
\&\fBERR_free_strings()\fR return no values.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBERR_error_string\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
The \fBERR_load_crypto_strings()\fR, \fBSSL_load_error_strings()\fR, and
\&\fBERR_free_strings()\fR functions were deprecated in OpenSSL 1.1.0 by
\&\fBOPENSSL_init_crypto()\fR and \fBOPENSSL_init_ssl()\fR and should not be used.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
