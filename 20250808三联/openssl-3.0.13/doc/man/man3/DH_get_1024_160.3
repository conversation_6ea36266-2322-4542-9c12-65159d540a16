.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "DH_GET_1024_160 3ossl"
.TH DH_GET_1024_160 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
DH_get_1024_160,
DH_get_2048_224,
DH_get_2048_256,
BN_get0_nist_prime_192,
BN_get0_nist_prime_224,
BN_get0_nist_prime_256,
BN_get0_nist_prime_384,
BN_get0_nist_prime_521,
BN_get_rfc2409_prime_768,
BN_get_rfc2409_prime_1024,
BN_get_rfc3526_prime_1536,
BN_get_rfc3526_prime_2048,
BN_get_rfc3526_prime_3072,
BN_get_rfc3526_prime_4096,
BN_get_rfc3526_prime_6144,
BN_get_rfc3526_prime_8192
\&\- Create standardized public primes or DH pairs
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/dh.h>
\&
\& const BIGNUM *BN_get0_nist_prime_192(void);
\& const BIGNUM *BN_get0_nist_prime_224(void);
\& const BIGNUM *BN_get0_nist_prime_256(void);
\& const BIGNUM *BN_get0_nist_prime_384(void);
\& const BIGNUM *BN_get0_nist_prime_521(void);
\&
\& BIGNUM *BN_get_rfc2409_prime_768(BIGNUM *bn);
\& BIGNUM *BN_get_rfc2409_prime_1024(BIGNUM *bn);
\& BIGNUM *BN_get_rfc3526_prime_1536(BIGNUM *bn);
\& BIGNUM *BN_get_rfc3526_prime_2048(BIGNUM *bn);
\& BIGNUM *BN_get_rfc3526_prime_3072(BIGNUM *bn);
\& BIGNUM *BN_get_rfc3526_prime_4096(BIGNUM *bn);
\& BIGNUM *BN_get_rfc3526_prime_6144(BIGNUM *bn);
\& BIGNUM *BN_get_rfc3526_prime_8192(BIGNUM *bn);
.Ve
.PP
The following functions have been deprecated since OpenSSL 3.0, and can be
hidden entirely by defining \fBOPENSSL_API_COMPAT\fR with a suitable version value,
see \fBopenssl_user_macros\fR\|(7):
.PP
.Vb 1
\& #include <openssl/dh.h>
\&
\& DH *DH_get_1024_160(void);
\& DH *DH_get_2048_224(void);
\& DH *DH_get_2048_256(void);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBDH_get_1024_160()\fR, \fBDH_get_2048_224()\fR, and \fBDH_get_2048_256()\fR each return
a DH object for the IETF RFC 5114 value. These functions are deprecated.
Applications should instead use \fBEVP_PKEY_CTX_set_dh_rfc5114()\fR and
\&\fBEVP_PKEY_CTX_set_dhx_rfc5114()\fR as described in \fBEVP_PKEY_CTX_ctrl\fR\|(3) or
by setting the \fBOSSL_PKEY_PARAM_GROUP_NAME\fR as specified in
"DH parameters" in \fBEVP_PKEY\-DH\fR\|(7)) to one of "dh_1024_160", "dh_2048_224" or
"dh_2048_256".
.PP
\&\fBBN_get0_nist_prime_192()\fR, \fBBN_get0_nist_prime_224()\fR, \fBBN_get0_nist_prime_256()\fR,
\&\fBBN_get0_nist_prime_384()\fR, and \fBBN_get0_nist_prime_521()\fR functions return
a BIGNUM for the specific NIST prime curve (e.g., P\-256).
.PP
\&\fBBN_get_rfc2409_prime_768()\fR, \fBBN_get_rfc2409_prime_1024()\fR,
\&\fBBN_get_rfc3526_prime_1536()\fR, \fBBN_get_rfc3526_prime_2048()\fR,
\&\fBBN_get_rfc3526_prime_3072()\fR, \fBBN_get_rfc3526_prime_4096()\fR,
\&\fBBN_get_rfc3526_prime_6144()\fR, and \fBBN_get_rfc3526_prime_8192()\fR functions
return a BIGNUM for the specified size from IETF RFC 2409.  If \fBbn\fR
is not NULL, the BIGNUM will be set into that location as well.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
Defined above.
.SH HISTORY
.IX Header "HISTORY"
The functions \fBDH_get_1024_160()\fR, \fBDH_get_2048_224()\fR and \fBDH_get_2048_256()\fR were
deprecated in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2016\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
