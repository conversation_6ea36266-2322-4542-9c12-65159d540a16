.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "SSL_READ_EARLY_DATA 3ossl"
.TH SSL_READ_EARLY_DATA 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
SSL_set_max_early_data,
SSL_CTX_set_max_early_data,
SSL_get_max_early_data,
SSL_CTX_get_max_early_data,
SSL_set_recv_max_early_data,
SSL_CTX_set_recv_max_early_data,
SSL_get_recv_max_early_data,
SSL_CTX_get_recv_max_early_data,
SSL_SESSION_get_max_early_data,
SSL_SESSION_set_max_early_data,
SSL_write_early_data,
SSL_read_early_data,
SSL_get_early_data_status,
SSL_allow_early_data_cb_fn,
SSL_CTX_set_allow_early_data_cb,
SSL_set_allow_early_data_cb
\&\- functions for sending and receiving early data
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/ssl.h>
\&
\& int SSL_CTX_set_max_early_data(SSL_CTX *ctx, uint32_t max_early_data);
\& uint32_t SSL_CTX_get_max_early_data(const SSL_CTX *ctx);
\& int SSL_set_max_early_data(SSL *s, uint32_t max_early_data);
\& uint32_t SSL_get_max_early_data(const SSL *s);
\&
\& int SSL_CTX_set_recv_max_early_data(SSL_CTX *ctx, uint32_t recv_max_early_data);
\& uint32_t SSL_CTX_get_recv_max_early_data(const SSL_CTX *ctx);
\& int SSL_set_recv_max_early_data(SSL *s, uint32_t recv_max_early_data);
\& uint32_t SSL_get_recv_max_early_data(const SSL *s);
\&
\& uint32_t SSL_SESSION_get_max_early_data(const SSL_SESSION *s);
\& int SSL_SESSION_set_max_early_data(SSL_SESSION *s, uint32_t max_early_data);
\&
\& int SSL_write_early_data(SSL *s, const void *buf, size_t num, size_t *written);
\&
\& int SSL_read_early_data(SSL *s, void *buf, size_t num, size_t *readbytes);
\&
\& int SSL_get_early_data_status(const SSL *s);
\&
\&
\& typedef int (*SSL_allow_early_data_cb_fn)(SSL *s, void *arg);
\&
\& void SSL_CTX_set_allow_early_data_cb(SSL_CTX *ctx,
\&                                      SSL_allow_early_data_cb_fn cb,
\&                                      void *arg);
\& void SSL_set_allow_early_data_cb(SSL *s,
\&                                  SSL_allow_early_data_cb_fn cb,
\&                                  void *arg);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
These functions are used to send and receive early data where TLSv1.3 has been
negotiated. Early data can be sent by the client immediately after its initial
ClientHello without having to wait for the server to complete the handshake.
Early data can be sent if a session has previously been established with the
server or when establishing a new session using an out-of-band PSK, and only
when the server is known to support it. Additionally these functions can be used
to send data from the server to the client when the client has not yet completed
the authentication stage of the handshake.
.PP
Early data has weaker security properties than other data sent over an SSL/TLS
connection. In particular the data does not have forward secrecy. There are also
additional considerations around replay attacks (see "REPLAY PROTECTION"
below). For these reasons extreme care should be exercised when using early
data. For specific details, consult the TLS 1.3 specification.
.PP
When a server receives early data it may opt to immediately respond by sending
application data back to the client. Data sent by the server at this stage is
done before the full handshake has been completed. Specifically the client's
authentication messages have not yet been received, i.e. the client is
unauthenticated at this point and care should be taken when using this
capability.
.PP
A server or client can determine whether the full handshake has been completed
or not by calling \fBSSL_is_init_finished\fR\|(3).
.PP
On the client side, the function \fBSSL_SESSION_get_max_early_data()\fR can be used to
determine if a session established with a server can be used to send early data.
If the session cannot be used then this function will return 0. Otherwise it
will return the maximum number of early data bytes that can be sent.
.PP
The function \fBSSL_SESSION_set_max_early_data()\fR sets the maximum number of early
data bytes that can be sent for a session. This would typically be used when
creating a PSK session file (see \fBSSL_CTX_set_psk_use_session_callback\fR\|(3)). If
using a ticket based PSK then this is set automatically to the value provided by
the server.
.PP
A client uses the function \fBSSL_write_early_data()\fR to send early data. This
function is similar to the \fBSSL_write_ex\fR\|(3) function, but with the following
differences. See \fBSSL_write_ex\fR\|(3) for information on how to write bytes to
the underlying connection, and how to handle any errors that may arise. This
page describes the differences between \fBSSL_write_early_data()\fR and
\&\fBSSL_write_ex\fR\|(3).
.PP
When called by a client, \fBSSL_write_early_data()\fR must be the first IO function
called on a new connection, i.e. it must occur before any calls to
\&\fBSSL_write_ex\fR\|(3), \fBSSL_read_ex\fR\|(3), \fBSSL_connect\fR\|(3), \fBSSL_do_handshake\fR\|(3)
or other similar functions. It may be called multiple times to stream data to
the server, but the total number of bytes written must not exceed the value
returned from \fBSSL_SESSION_get_max_early_data()\fR. Once the initial
\&\fBSSL_write_early_data()\fR call has completed successfully the client may interleave
calls to \fBSSL_read_ex\fR\|(3) and \fBSSL_read\fR\|(3) with calls to
\&\fBSSL_write_early_data()\fR as required.
.PP
If \fBSSL_write_early_data()\fR fails you should call \fBSSL_get_error\fR\|(3) to determine
the correct course of action, as for \fBSSL_write_ex\fR\|(3).
.PP
When the client no longer wishes to send any more early data then it should
complete the handshake by calling a function such as \fBSSL_connect\fR\|(3) or
\&\fBSSL_do_handshake\fR\|(3). Alternatively you can call a standard write function
such as \fBSSL_write_ex\fR\|(3), which will transparently complete the connection and
write the requested data.
.PP
A server may choose to ignore early data that has been sent to it. Once the
connection has been completed you can determine whether the server accepted or
rejected the early data by calling \fBSSL_get_early_data_status()\fR. This will return
SSL_EARLY_DATA_ACCEPTED if the data was accepted, SSL_EARLY_DATA_REJECTED if it
was rejected or SSL_EARLY_DATA_NOT_SENT if no early data was sent. This function
may be called by either the client or the server.
.PP
A server uses the \fBSSL_read_early_data()\fR function to receive early data on a
connection for which early data has been enabled using
\&\fBSSL_CTX_set_max_early_data()\fR or \fBSSL_set_max_early_data()\fR. As for
\&\fBSSL_write_early_data()\fR, this must be the first IO function
called on a connection, i.e. it must occur before any calls to
\&\fBSSL_write_ex\fR\|(3), \fBSSL_read_ex\fR\|(3), \fBSSL_accept\fR\|(3), \fBSSL_do_handshake\fR\|(3),
or other similar functions.
.PP
\&\fBSSL_read_early_data()\fR is similar to \fBSSL_read_ex\fR\|(3) with the following
differences. Refer to \fBSSL_read_ex\fR\|(3) for full details.
.PP
\&\fBSSL_read_early_data()\fR may return 3 possible values:
.IP SSL_READ_EARLY_DATA_ERROR 4
.IX Item "SSL_READ_EARLY_DATA_ERROR"
This indicates an IO or some other error occurred. This should be treated in the
same way as a 0 return value from \fBSSL_read_ex\fR\|(3).
.IP SSL_READ_EARLY_DATA_SUCCESS 4
.IX Item "SSL_READ_EARLY_DATA_SUCCESS"
This indicates that early data was successfully read. This should be treated in
the same way as a 1 return value from \fBSSL_read_ex\fR\|(3). You should continue to
call \fBSSL_read_early_data()\fR to read more data.
.IP SSL_READ_EARLY_DATA_FINISH 4
.IX Item "SSL_READ_EARLY_DATA_FINISH"
This indicates that no more early data can be read. It may be returned on the
first call to \fBSSL_read_early_data()\fR if the client has not sent any early data,
or if the early data was rejected.
.PP
Once the initial \fBSSL_read_early_data()\fR call has completed successfully (i.e. it
has returned SSL_READ_EARLY_DATA_SUCCESS or SSL_READ_EARLY_DATA_FINISH) then the
server may choose to write data immediately to the unauthenticated client using
\&\fBSSL_write_early_data()\fR. If \fBSSL_read_early_data()\fR returned
SSL_READ_EARLY_DATA_FINISH then in some situations (e.g. if the client only
supports TLSv1.2) the handshake may have already been completed and calls
to \fBSSL_write_early_data()\fR are not allowed. Call \fBSSL_is_init_finished\fR\|(3) to
determine whether the handshake has completed or not. If the handshake is still
in progress then the server may interleave calls to \fBSSL_write_early_data()\fR with
calls to \fBSSL_read_early_data()\fR as required.
.PP
Servers must not call \fBSSL_read_ex\fR\|(3), \fBSSL_read\fR\|(3), \fBSSL_write_ex\fR\|(3) or
\&\fBSSL_write\fR\|(3)  until \fBSSL_read_early_data()\fR has returned with
SSL_READ_EARLY_DATA_FINISH. Once it has done so the connection to the client
still needs to be completed. Complete the connection by calling a function such
as \fBSSL_accept\fR\|(3) or \fBSSL_do_handshake\fR\|(3). Alternatively you can call a
standard read function such as \fBSSL_read_ex\fR\|(3), which will transparently
complete the connection and read the requested data. Note that it is an error to
attempt to complete the connection before \fBSSL_read_early_data()\fR has returned
SSL_READ_EARLY_DATA_FINISH.
.PP
Only servers may call \fBSSL_read_early_data()\fR.
.PP
Calls to \fBSSL_read_early_data()\fR may, in certain circumstances, complete the
connection immediately without further need to call a function such as
\&\fBSSL_accept\fR\|(3). This can happen if the client is using a protocol version less
than TLSv1.3. Applications can test for this by calling
\&\fBSSL_is_init_finished\fR\|(3). Alternatively, applications may choose to call
\&\fBSSL_accept\fR\|(3) anyway. Such a call will successfully return immediately with no
further action taken.
.PP
When a session is created between a server and a client the server will specify
the maximum amount of any early data that it will accept on any future
connection attempt. By default the server does not accept early data; a
server may indicate support for early data by calling
\&\fBSSL_CTX_set_max_early_data()\fR or
\&\fBSSL_set_max_early_data()\fR to set it for the whole SSL_CTX or an individual SSL
object respectively. The \fBmax_early_data\fR parameter specifies the maximum
amount of early data in bytes that is permitted to be sent on a single
connection. Similarly the \fBSSL_CTX_get_max_early_data()\fR and
\&\fBSSL_get_max_early_data()\fR functions can be used to obtain the current maximum
early data settings for the SSL_CTX and SSL objects respectively. Generally a
server application will either use both of \fBSSL_read_early_data()\fR and
\&\fBSSL_CTX_set_max_early_data()\fR (or \fBSSL_set_max_early_data()\fR), or neither of them,
since there is no practical benefit from using only one of them. If the maximum
early data setting for a server is nonzero then replay protection is
automatically enabled (see "REPLAY PROTECTION" below).
.PP
If the server rejects the early data sent by a client then it will skip over
the data that is sent. The maximum amount of received early data that is skipped
is controlled by the recv_max_early_data setting. If a client sends more than
this then the connection will abort. This value can be set by calling
\&\fBSSL_CTX_set_recv_max_early_data()\fR or \fBSSL_set_recv_max_early_data()\fR. The current
value for this setting can be obtained by calling
\&\fBSSL_CTX_get_recv_max_early_data()\fR or \fBSSL_get_recv_max_early_data()\fR. The default
value for this setting is 16,384 bytes.
.PP
The recv_max_early_data value also has an impact on early data that is accepted.
The amount of data that is accepted will always be the lower of the
max_early_data for the session and the recv_max_early_data setting for the
server. If a client sends more data than this then the connection will abort.
.PP
The configured value for max_early_data on a server may change over time as
required. However, clients may have tickets containing the previously configured
max_early_data value. The recv_max_early_data should always be equal to or
higher than any recently configured max_early_data value in order to avoid
aborted connections. The recv_max_early_data should never be set to less than
the current configured max_early_data value.
.PP
Some server applications may wish to have more control over whether early data
is accepted or not, for example to mitigate replay risks (see "REPLAY PROTECTION"
below) or to decline early_data when the server is heavily loaded. The functions
\&\fBSSL_CTX_set_allow_early_data_cb()\fR and \fBSSL_set_allow_early_data_cb()\fR set a
callback which is called at a point in the handshake immediately before a
decision is made to accept or reject early data. The callback is provided with a
pointer to the user data argument that was provided when the callback was first
set. Returning 1 from the callback will allow early data and returning 0 will
reject it. Note that the OpenSSL library may reject early data for other reasons
in which case this callback will not get called. Notably, the built-in replay
protection feature will still be used even if a callback is present unless it
has been explicitly disabled using the SSL_OP_NO_ANTI_REPLAY option. See
"REPLAY PROTECTION" below.
.SH NOTES
.IX Header "NOTES"
The whole purpose of early data is to enable a client to start sending data to
the server before a full round trip of network traffic has occurred. Application
developers should ensure they consider optimisation of the underlying TCP socket
to obtain a performant solution. For example Nagle's algorithm is commonly used
by operating systems in an attempt to avoid lots of small TCP packets. In many
scenarios this is beneficial for performance, but it does not work well with the
early data solution as implemented in OpenSSL. In Nagle's algorithm the OS will
buffer outgoing TCP data if a TCP packet has already been sent which we have not
yet received an ACK for from the peer. The buffered data will only be
transmitted if enough data to fill an entire TCP packet is accumulated, or if
the ACK is received from the peer. The initial ClientHello will be sent in the
first TCP packet along with any data from the first call to
\&\fBSSL_write_early_data()\fR. If the amount of data written will exceed the size of a
single TCP packet, or if there are more calls to \fBSSL_write_early_data()\fR then
that additional data will be sent in subsequent TCP packets which will be
buffered by the OS and not sent until an ACK is received for the first packet
containing the ClientHello. This means the early data is not actually
sent until a complete round trip with the server has occurred which defeats the
objective of early data.
.PP
In many operating systems the TCP_NODELAY socket option is available to disable
Nagle's algorithm. If an application opts to disable Nagle's algorithm
consideration should be given to turning it back on again after the handshake is
complete if appropriate.
.PP
In rare circumstances, it may be possible for a client to have a session that
reports a max early data value greater than 0, but where the server does not
support this. For example, this can occur if a server has had its configuration
changed to accept a lower max early data value such as by calling
\&\fBSSL_CTX_set_recv_max_early_data()\fR. Another example is if a server used to
support TLSv1.3 but was later downgraded to TLSv1.2. Sending early data to such
a server will cause the connection to abort. Clients that encounter an aborted
connection while sending early data may want to retry the connection without
sending early data as this does not happen automatically. A client will have to
establish a new transport layer connection to the server and attempt the SSL/TLS
connection again but without sending early data. Note that it is inadvisable to
retry with a lower maximum protocol version.
.SH "REPLAY PROTECTION"
.IX Header "REPLAY PROTECTION"
When early data is in use the TLS protocol provides no security guarantees that
the same early data was not replayed across multiple connections. As a
mitigation for this issue OpenSSL automatically enables replay protection if the
server is configured with a nonzero max early data value. With replay
protection enabled sessions are forced to be single use only. If a client
attempts to reuse a session ticket more than once, then the second and
subsequent attempts will fall back to a full handshake (and any early data that
was submitted will be ignored). Note that single use tickets are enforced even
if a client does not send any early data.
.PP
The replay protection mechanism relies on the internal OpenSSL server session
cache (see \fBSSL_CTX_set_session_cache_mode\fR\|(3)). When replay protection is
being used the server will operate as if the SSL_OP_NO_TICKET option had been
selected (see \fBSSL_CTX_set_options\fR\|(3)). Sessions will be added to the cache
whenever a session ticket is issued. When a client attempts to resume the
session, OpenSSL will check for its presence in the internal cache. If it exists
then the resumption is allowed and the session is removed from the cache. If it
does not exist then the resumption is not allowed and a full handshake will
occur.
.PP
Note that some applications may maintain an external cache of sessions (see
\&\fBSSL_CTX_sess_set_new_cb\fR\|(3) and similar functions). It is the application's
responsibility to ensure that any sessions in the external cache are also
populated in the internal cache and that once removed from the internal cache
they are similarly removed from the external cache. Failing to do this could
result in an application becoming vulnerable to replay attacks. Note that
OpenSSL will lock the internal cache while a session is removed but that lock is
not held when the remove session callback (see \fBSSL_CTX_sess_set_remove_cb\fR\|(3))
is called. This could result in a small amount of time where the session has
been removed from the internal cache but is still available in the external
cache. Applications should be designed with this in mind in order to minimise
the possibility of replay attacks.
.PP
The OpenSSL replay protection does not apply to external Pre Shared Keys (PSKs)
(e.g. see \fBSSL_CTX_set_psk_find_session_callback\fR\|(3)). Therefore, extreme caution
should be applied when combining external PSKs with early data.
.PP
Some applications may mitigate the replay risks in other ways. For those
applications it is possible to turn off the built-in replay protection feature
using the \fBSSL_OP_NO_ANTI_REPLAY\fR option. See \fBSSL_CTX_set_options\fR\|(3) for
details. Applications can also set a callback to make decisions about accepting
early data or not. See \fBSSL_CTX_set_allow_early_data_cb()\fR above for details.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBSSL_write_early_data()\fR returns 1 for success or 0 for failure. In the event of a
failure call \fBSSL_get_error\fR\|(3) to determine the correct course of action.
.PP
\&\fBSSL_read_early_data()\fR returns SSL_READ_EARLY_DATA_ERROR for failure,
SSL_READ_EARLY_DATA_SUCCESS for success with more data to read and
SSL_READ_EARLY_DATA_FINISH for success with no more to data be read. In the
event of a failure call \fBSSL_get_error\fR\|(3) to determine the correct course of
action.
.PP
\&\fBSSL_get_max_early_data()\fR, \fBSSL_CTX_get_max_early_data()\fR and
\&\fBSSL_SESSION_get_max_early_data()\fR return the maximum number of early data bytes
that may be sent.
.PP
\&\fBSSL_set_max_early_data()\fR, \fBSSL_CTX_set_max_early_data()\fR and
\&\fBSSL_SESSION_set_max_early_data()\fR return 1 for success or 0 for failure.
.PP
\&\fBSSL_get_early_data_status()\fR returns SSL_EARLY_DATA_ACCEPTED if early data was
accepted by the server, SSL_EARLY_DATA_REJECTED if early data was rejected by
the server, or SSL_EARLY_DATA_NOT_SENT if no early data was sent.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBSSL_get_error\fR\|(3),
\&\fBSSL_write_ex\fR\|(3),
\&\fBSSL_read_ex\fR\|(3),
\&\fBSSL_connect\fR\|(3),
\&\fBSSL_accept\fR\|(3),
\&\fBSSL_do_handshake\fR\|(3),
\&\fBSSL_CTX_set_psk_use_session_callback\fR\|(3),
\&\fBssl\fR\|(7)
.SH HISTORY
.IX Header "HISTORY"
All of the functions described above were added in OpenSSL 1.1.1.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2017\-2020 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
