.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "D2I_PRIVATEKEY 3ossl"
.TH D2I_PRIVATEKEY 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
d2i_PrivateKey_ex, d2i_PrivateKey, d2i_PublicKey, d2i_KeyParams,
d2i_AutoPrivateKey_ex,  d2i_AutoPrivateKey, i2d_PrivateKey, i2d_PublicKey,
i2d_KeyParams, i2d_KeyParams_bio, d2i_PrivateKey_ex_bio, d2i_PrivateKey_bio,
d2i_PrivateKey_ex_fp, d2i_PrivateKey_fp, d2i_KeyParams_bio, i2d_PrivateKey_bio,
i2d_PrivateKey_fp
\&\- decode and encode functions for reading and saving EVP_PKEY structures
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/evp.h>
\&
\& EVP_PKEY *d2i_PrivateKey_ex(int type, EVP_PKEY **a, const unsigned char **pp,
\&                             long length, OSSL_LIB_CTX *libctx,
\&                             const char *propq);
\& EVP_PKEY *d2i_PrivateKey(int type, EVP_PKEY **a, const unsigned char **pp,
\&                          long length);
\& EVP_PKEY *d2i_PublicKey(int type, EVP_PKEY **a, const unsigned char **pp,
\&                         long length);
\& EVP_PKEY *d2i_KeyParams(int type, EVP_PKEY **a, const unsigned char **pp,
\&                         long length);
\& EVP_PKEY *d2i_AutoPrivateKey_ex(EVP_PKEY **a, const unsigned char **pp,
\&                                 long length, OSSL_LIB_CTX *libctx,
\&                                 const char *propq);
\& EVP_PKEY *d2i_AutoPrivateKey(EVP_PKEY **a, const unsigned char **pp,
\&                              long length);
\&
\& int i2d_PrivateKey(const EVP_PKEY *a, unsigned char **pp);
\& int i2d_PublicKey(const EVP_PKEY *a, unsigned char **pp);
\& int i2d_KeyParams(const EVP_PKEY *a, unsigned char **pp);
\& int i2d_KeyParams_bio(BIO *bp, const EVP_PKEY *pkey);
\& EVP_PKEY *d2i_KeyParams_bio(int type, EVP_PKEY **a, BIO *in);
\&
\&
\& #include <openssl/x509.h>
\&
\& EVP_PKEY *d2i_PrivateKey_ex_bio(BIO *bp, EVP_PKEY **a, OSSL_LIB_CTX *libctx,
\&                                 const char *propq);
\& EVP_PKEY *d2i_PrivateKey_bio(BIO *bp, EVP_PKEY **a);
\& EVP_PKEY *d2i_PrivateKey_ex_fp(FILE *fp, EVP_PKEY **a, OSSL_LIB_CTX *libctx,
\&                                const char *propq);
\& EVP_PKEY *d2i_PrivateKey_fp(FILE *fp, EVP_PKEY **a);
\&
\& int i2d_PrivateKey_bio(BIO *bp, const EVP_PKEY *pkey);
\& int i2d_PrivateKey_fp(FILE *fp, const EVP_PKEY *pkey);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBd2i_PrivateKey_ex()\fR decodes a private key using algorithm \fItype\fR. It attempts
to use any key-specific format or PKCS#8 unencrypted PrivateKeyInfo format.
The \fItype\fR parameter should be a public key algorithm constant such as
\&\fBEVP_PKEY_RSA\fR. An error occurs if the decoded key does not match \fItype\fR. Some
private key decoding implementations may use cryptographic algorithms (for
example to automatically derive the public key if it is not explicitly
included in the encoding). In this case the supplied library context \fIlibctx\fR
and property query string \fIpropq\fR are used.
If successful and the \fIa\fR parameter is not NULL the function assigns the
returned \fBEVP_PKEY\fR structure pointer to \fI*a\fR, overwriting any previous value.
.PP
\&\fBd2i_PrivateKey()\fR does the same as \fBd2i_PrivateKey_ex()\fR except that the default
library context and property query string are used.
\&\fBd2i_PublicKey()\fR does the same for public keys.
\&\fBd2i_KeyParams()\fR does the same for key parameters.
.PP
The \fBd2i_PrivateKey_ex_bio()\fR and \fBd2i_PrivateKey_bio()\fR functions are similar to
\&\fBd2i_PrivateKey_ex()\fR and \fBd2i_PrivateKey()\fR respectively except that they decode
the data read from the given BIO. The \fBd2i_PrivateKey_ex_fp()\fR and
\&\fBd2i_PrivateKey_fp()\fR functions are the same except that they read the data from
the given FILE.
.PP
\&\fBd2i_AutoPrivateKey_ex()\fR and \fBd2i_AutoPrivateKey()\fR are similar to
\&\fBd2i_PrivateKey_ex()\fR and \fBd2i_PrivateKey()\fR respectively except that they attempt
to automatically detect the private key format.
.PP
\&\fBi2d_PrivateKey()\fR encodes \fIa\fR. It uses a key specific format or, if none is
defined for that key type, PKCS#8 unencrypted PrivateKeyInfo format.
\&\fBi2d_PublicKey()\fR does the same for public keys.
\&\fBi2d_KeyParams()\fR does the same for key parameters.
These functions are similar to the \fBd2i_X509()\fR functions; see \fBd2i_X509\fR\|(3).
\&\fBi2d_PrivateKey_bio()\fR and \fBi2d_PrivateKey_fp()\fR do the same thing except that they
encode to a \fBBIO\fR or \fBFILE\fR respectively. Again, these work similarly to the
functions described in \fBd2i_X509\fR\|(3).
.SH NOTES
.IX Header "NOTES"
All the functions that operate on data in memory update the data pointer \fI*pp\fR
after a successful operation, just like the other d2i and i2d functions;
see \fBd2i_X509\fR\|(3).
.PP
All these functions use DER format and unencrypted keys. Applications wishing
to encrypt or decrypt private keys should use other functions such as
\&\fBd2i_PKCS8PrivateKey()\fR instead.
.PP
To decode a key with type \fBEVP_PKEY_EC\fR, \fBd2i_PublicKey()\fR requires \fI*a\fR to be
a non-NULL EVP_PKEY structure assigned an EC_KEY structure referencing the proper
EC_GROUP.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
The \fBd2i_PrivateKey_ex()\fR, \fBd2i_PrivateKey()\fR, \fBd2i_AutoPrivateKey_ex()\fR,
\&\fBd2i_AutoPrivateKey()\fR, \fBd2i_PrivateKey_ex_bio()\fR, \fBd2i_PrivateKey_bio()\fR,
\&\fBd2i_PrivateKey_ex_fp()\fR, \fBd2i_PrivateKey_fp()\fR, \fBd2i_PublicKey()\fR, \fBd2i_KeyParams()\fR
and \fBd2i_KeyParams_bio()\fR functions return a valid \fBEVP_PKEY\fR structure or NULL if
an error occurs. The error code can be obtained by calling \fBERR_get_error\fR\|(3).
.PP
\&\fBi2d_PrivateKey()\fR, \fBi2d_PublicKey()\fR and \fBi2d_KeyParams()\fR return the number of
bytes successfully encoded or a negative value if an error occurs. The error
code can be obtained by calling \fBERR_get_error\fR\|(3).
.PP
\&\fBi2d_PrivateKey_bio()\fR, \fBi2d_PrivateKey_fp()\fR and \fBi2d_KeyParams_bio()\fR return 1 if
successfully encoded or zero if an error occurs.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBcrypto\fR\|(7),
\&\fBd2i_PKCS8PrivateKey_bio\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
\&\fBd2i_PrivateKey_ex()\fR, \fBd2i_PrivateKey_ex_bio()\fR, \fBd2i_PrivateKey_ex_fp()\fR, and
\&\fBd2i_AutoPrivateKey_ex()\fR were added in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2017\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
