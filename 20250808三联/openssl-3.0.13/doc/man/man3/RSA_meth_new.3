.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "RSA_METH_NEW 3ossl"
.TH RSA_METH_NEW 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
RSA_meth_get0_app_data, RSA_meth_set0_app_data,
RSA_meth_new, RSA_meth_free, RSA_meth_dup, RSA_meth_get0_name,
RSA_meth_set1_name, RSA_meth_get_flags, RSA_meth_set_flags,
RSA_meth_get_pub_enc,
RSA_meth_set_pub_enc, RSA_meth_get_pub_dec, RSA_meth_set_pub_dec,
RSA_meth_get_priv_enc, RSA_meth_set_priv_enc, RSA_meth_get_priv_dec,
RSA_meth_set_priv_dec, RSA_meth_get_mod_exp, RSA_meth_set_mod_exp,
RSA_meth_get_bn_mod_exp, RSA_meth_set_bn_mod_exp, RSA_meth_get_init,
RSA_meth_set_init, RSA_meth_get_finish, RSA_meth_set_finish,
RSA_meth_get_sign, RSA_meth_set_sign, RSA_meth_get_verify,
RSA_meth_set_verify, RSA_meth_get_keygen, RSA_meth_set_keygen,
RSA_meth_get_multi_prime_keygen, RSA_meth_set_multi_prime_keygen
\&\- Routines to build up RSA methods
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/rsa.h>
.Ve
.PP
The following functions have been deprecated since OpenSSL 3.0, and can be
hidden entirely by defining \fBOPENSSL_API_COMPAT\fR with a suitable version value,
see \fBopenssl_user_macros\fR\|(7):
.PP
.Vb 2
\& RSA_METHOD *RSA_meth_new(const char *name, int flags);
\& void RSA_meth_free(RSA_METHOD *meth);
\&
\& RSA_METHOD *RSA_meth_dup(const RSA_METHOD *meth);
\&
\& const char *RSA_meth_get0_name(const RSA_METHOD *meth);
\& int RSA_meth_set1_name(RSA_METHOD *meth, const char *name);
\&
\& int RSA_meth_get_flags(const RSA_METHOD *meth);
\& int RSA_meth_set_flags(RSA_METHOD *meth, int flags);
\&
\& void *RSA_meth_get0_app_data(const RSA_METHOD *meth);
\& int RSA_meth_set0_app_data(RSA_METHOD *meth, void *app_data);
\&
\& int (*RSA_meth_get_pub_enc(const RSA_METHOD *meth))(int flen, const unsigned char *from,
\&                                                     unsigned char *to, RSA *rsa, int padding);
\& int RSA_meth_set_pub_enc(RSA_METHOD *rsa,
\&                          int (*pub_enc)(int flen, const unsigned char *from,
\&                                         unsigned char *to, RSA *rsa,
\&                                         int padding));
\&
\& int (*RSA_meth_get_pub_dec(const RSA_METHOD *meth))
\&     (int flen, const unsigned char *from,
\&      unsigned char *to, RSA *rsa, int padding);
\& int RSA_meth_set_pub_dec(RSA_METHOD *rsa,
\&                          int (*pub_dec)(int flen, const unsigned char *from,
\&                                         unsigned char *to, RSA *rsa,
\&                                         int padding));
\&
\& int (*RSA_meth_get_priv_enc(const RSA_METHOD *meth))(int flen, const unsigned char *from,
\&                                                      unsigned char *to, RSA *rsa,
\&                                                      int padding);
\& int RSA_meth_set_priv_enc(RSA_METHOD *rsa,
\&                           int (*priv_enc)(int flen, const unsigned char *from,
\&                                           unsigned char *to, RSA *rsa, int padding));
\&
\& int (*RSA_meth_get_priv_dec(const RSA_METHOD *meth))(int flen, const unsigned char *from,
\&                                                      unsigned char *to, RSA *rsa,
\&                                                      int padding);
\& int RSA_meth_set_priv_dec(RSA_METHOD *rsa,
\&                           int (*priv_dec)(int flen, const unsigned char *from,
\&                                           unsigned char *to, RSA *rsa, int padding));
\&
\& /* Can be null */
\& int (*RSA_meth_get_mod_exp(const RSA_METHOD *meth))(BIGNUM *r0, const BIGNUM *i,
\&                                                     RSA *rsa, BN_CTX *ctx);
\& int RSA_meth_set_mod_exp(RSA_METHOD *rsa,
\&                          int (*mod_exp)(BIGNUM *r0, const BIGNUM *i, RSA *rsa,
\&                                         BN_CTX *ctx));
\&
\& /* Can be null */
\& int (*RSA_meth_get_bn_mod_exp(const RSA_METHOD *meth))(BIGNUM *r, const BIGNUM *a,
\&                                                        const BIGNUM *p, const BIGNUM *m,
\&                                                        BN_CTX *ctx, BN_MONT_CTX *m_ctx);
\& int RSA_meth_set_bn_mod_exp(RSA_METHOD *rsa,
\&                             int (*bn_mod_exp)(BIGNUM *r, const BIGNUM *a,
\&                                               const BIGNUM *p, const BIGNUM *m,
\&                                               BN_CTX *ctx, BN_MONT_CTX *m_ctx));
\&
\& /* called at new */
\& int (*RSA_meth_get_init(const RSA_METHOD *meth) (RSA *rsa);
\& int RSA_meth_set_init(RSA_METHOD *rsa, int (*init (RSA *rsa));
\&
\& /* called at free */
\& int (*RSA_meth_get_finish(const RSA_METHOD *meth))(RSA *rsa);
\& int RSA_meth_set_finish(RSA_METHOD *rsa, int (*finish)(RSA *rsa));
\&
\& int (*RSA_meth_get_sign(const RSA_METHOD *meth))(int type, const unsigned char *m,
\&                                                  unsigned int m_length,
\&                                                  unsigned char *sigret,
\&                                                  unsigned int *siglen, const RSA *rsa);
\& int RSA_meth_set_sign(RSA_METHOD *rsa,
\&                       int (*sign)(int type, const unsigned char *m,
\&                                   unsigned int m_length, unsigned char *sigret,
\&                                   unsigned int *siglen, const RSA *rsa));
\&
\& int (*RSA_meth_get_verify(const RSA_METHOD *meth))(int dtype, const unsigned char *m,
\&                                                    unsigned int m_length,
\&                                                    const unsigned char *sigbuf,
\&                                                    unsigned int siglen, const RSA *rsa);
\& int RSA_meth_set_verify(RSA_METHOD *rsa,
\&                         int (*verify)(int dtype, const unsigned char *m,
\&                                       unsigned int m_length,
\&                                       const unsigned char *sigbuf,
\&                                       unsigned int siglen, const RSA *rsa));
\&
\& int (*RSA_meth_get_keygen(const RSA_METHOD *meth))(RSA *rsa, int bits, BIGNUM *e,
\&                                                    BN_GENCB *cb);
\& int RSA_meth_set_keygen(RSA_METHOD *rsa,
\&                         int (*keygen)(RSA *rsa, int bits, BIGNUM *e,
\&                                       BN_GENCB *cb));
\&
\& int (*RSA_meth_get_multi_prime_keygen(const RSA_METHOD *meth))(RSA *rsa, int bits,
\&                                                                int primes, BIGNUM *e,
\&                                                                BN_GENCB *cb);
\&
\& int RSA_meth_set_multi_prime_keygen(RSA_METHOD *meth,
\&                                     int (*keygen) (RSA *rsa, int bits,
\&                                                    int primes, BIGNUM *e,
\&                                                    BN_GENCB *cb));
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
All of the functions described on this page are deprecated.
Applications should instead use the OSSL_PROVIDER APIs.
.PP
The \fBRSA_METHOD\fR type is a structure used for the provision of custom
RSA implementations. It provides a set of functions used by OpenSSL
for the implementation of the various RSA capabilities.
.PP
\&\fBRSA_meth_new()\fR creates a new \fBRSA_METHOD\fR structure. It should be
given a unique \fBname\fR and a set of \fBflags\fR. The \fBname\fR should be a
NULL terminated string, which will be duplicated and stored in the
\&\fBRSA_METHOD\fR object. It is the callers responsibility to free the
original string. The flags will be used during the construction of a
new \fBRSA\fR object based on this \fBRSA_METHOD\fR. Any new \fBRSA\fR object
will have those flags set by default.
.PP
\&\fBRSA_meth_dup()\fR creates a duplicate copy of the \fBRSA_METHOD\fR object
passed as a parameter. This might be useful for creating a new
\&\fBRSA_METHOD\fR based on an existing one, but with some differences.
.PP
\&\fBRSA_meth_free()\fR destroys an \fBRSA_METHOD\fR structure and frees up any
memory associated with it.
.PP
\&\fBRSA_meth_get0_name()\fR will return a pointer to the name of this
RSA_METHOD. This is a pointer to the internal name string and so
should not be freed by the caller. \fBRSA_meth_set1_name()\fR sets the name
of the RSA_METHOD to \fBname\fR. The string is duplicated and the copy is
stored in the RSA_METHOD structure, so the caller remains responsible
for freeing the memory associated with the name.
.PP
\&\fBRSA_meth_get_flags()\fR returns the current value of the flags associated
with this RSA_METHOD. \fBRSA_meth_set_flags()\fR provides the ability to set
these flags.
.PP
The functions \fBRSA_meth_get0_app_data()\fR and \fBRSA_meth_set0_app_data()\fR
provide the ability to associate implementation specific data with the
RSA_METHOD. It is the application's responsibility to free this data
before the RSA_METHOD is freed via a call to \fBRSA_meth_free()\fR.
.PP
\&\fBRSA_meth_get_sign()\fR and \fBRSA_meth_set_sign()\fR get and set the function
used for creating an RSA signature respectively. This function will be
called in response to the application calling \fBRSA_sign()\fR. The
parameters for the function have the same meaning as for \fBRSA_sign()\fR.
.PP
\&\fBRSA_meth_get_verify()\fR and \fBRSA_meth_set_verify()\fR get and set the
function used for verifying an RSA signature respectively. This
function will be called in response to the application calling
\&\fBRSA_verify()\fR. The parameters for the function have the same meaning as
for \fBRSA_verify()\fR.
.PP
\&\fBRSA_meth_get_mod_exp()\fR and \fBRSA_meth_set_mod_exp()\fR get and set the
function used for CRT computations.
.PP
\&\fBRSA_meth_get_bn_mod_exp()\fR and \fBRSA_meth_set_bn_mod_exp()\fR get and set
the function used for CRT computations, specifically the following
value:
.PP
.Vb 1
\& r = a ^ p mod m
.Ve
.PP
Both the \fBmod_exp()\fR and \fBbn_mod_exp()\fR functions are called by the
default OpenSSL method during encryption, decryption, signing and
verification.
.PP
\&\fBRSA_meth_get_init()\fR and \fBRSA_meth_set_init()\fR get and set the function
used for creating a new RSA instance respectively. This function will
be called in response to the application calling \fBRSA_new()\fR (if the
current default RSA_METHOD is this one) or \fBRSA_new_method()\fR. The
\&\fBRSA_new()\fR and \fBRSA_new_method()\fR functions will allocate the memory for
the new RSA object, and a pointer to this newly allocated structure
will be passed as a parameter to the function. This function may be
NULL.
.PP
\&\fBRSA_meth_get_finish()\fR and \fBRSA_meth_set_finish()\fR get and set the
function used for destroying an instance of an RSA object respectively.
This function will be called in response to the application calling
\&\fBRSA_free()\fR. A pointer to the RSA to be destroyed is passed as a
parameter. The destroy function should be used for RSA implementation
specific clean up. The memory for the RSA itself should not be freed
by this function. This function may be NULL.
.PP
\&\fBRSA_meth_get_keygen()\fR and \fBRSA_meth_set_keygen()\fR get and set the
function used for generating a new RSA key pair respectively. This
function will be called in response to the application calling
\&\fBRSA_generate_key_ex()\fR. The parameter for the function has the same
meaning as for \fBRSA_generate_key_ex()\fR.
.PP
\&\fBRSA_meth_get_multi_prime_keygen()\fR and \fBRSA_meth_set_multi_prime_keygen()\fR get
and set the function used for generating a new multi-prime RSA key pair
respectively. This function will be called in response to the application calling
\&\fBRSA_generate_multi_prime_key()\fR. The parameter for the function has the same
meaning as for \fBRSA_generate_multi_prime_key()\fR.
.PP
\&\fBRSA_meth_get_pub_enc()\fR, \fBRSA_meth_set_pub_enc()\fR,
\&\fBRSA_meth_get_pub_dec()\fR, \fBRSA_meth_set_pub_dec()\fR,
\&\fBRSA_meth_get_priv_enc()\fR, \fBRSA_meth_set_priv_enc()\fR,
\&\fBRSA_meth_get_priv_dec()\fR, \fBRSA_meth_set_priv_dec()\fR get and set the
functions used for public and private key encryption and decryption.
These functions will be called in response to the application calling
\&\fBRSA_public_encrypt()\fR, \fBRSA_private_decrypt()\fR, \fBRSA_private_encrypt()\fR and
\&\fBRSA_public_decrypt()\fR and take the same parameters as those.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBRSA_meth_new()\fR and \fBRSA_meth_dup()\fR return the newly allocated
RSA_METHOD object or NULL on failure.
.PP
\&\fBRSA_meth_get0_name()\fR and \fBRSA_meth_get_flags()\fR return the name and
flags associated with the RSA_METHOD respectively.
.PP
All other RSA_meth_get_*() functions return the appropriate function
pointer that has been set in the RSA_METHOD, or NULL if no such
pointer has yet been set.
.PP
RSA_meth_set1_name and all RSA_meth_set_*() functions return 1 on
success or 0 on failure.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBRSA_new\fR\|(3), \fBRSA_generate_key_ex\fR\|(3), \fBRSA_sign\fR\|(3),
\&\fBRSA_set_method\fR\|(3), \fBRSA_size\fR\|(3), \fBRSA_get0_key\fR\|(3),
\&\fBRSA_generate_multi_prime_key\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
All of these functions were deprecated in OpenSSL 3.0.
.PP
\&\fBRSA_meth_get_multi_prime_keygen()\fR and \fBRSA_meth_set_multi_prime_keygen()\fR were
added in OpenSSL 1.1.1.
.PP
Other functions described here were added in OpenSSL 1.1.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2016\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
