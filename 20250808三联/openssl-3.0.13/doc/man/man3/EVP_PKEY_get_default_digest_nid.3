.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "EVP_PKEY_GET_DEFAULT_DIGEST_NID 3ossl"
.TH EVP_PKEY_GET_DEFAULT_DIGEST_NID 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
EVP_PKEY_get_default_digest_nid, EVP_PKEY_get_default_digest_name
\&\- get default signature digest
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/evp.h>
\&
\& int EVP_PKEY_get_default_digest_name(EVP_PKEY *pkey,
\&                                      char *mdname, size_t mdname_sz);
\& int EVP_PKEY_get_default_digest_nid(EVP_PKEY *pkey, int *pnid);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBEVP_PKEY_get_default_digest_name()\fR fills in the default message digest
name for the public key signature operations associated with key
\&\fIpkey\fR into \fImdname\fR, up to at most \fImdname_sz\fR bytes including the
ending NUL byte.  The name could be \f(CW"UNDEF"\fR, signifying that a digest
must (for return value 2) or may (for return value 1) be left unspecified.
.PP
\&\fBEVP_PKEY_get_default_digest_nid()\fR sets \fIpnid\fR to the default message
digest NID for the public key signature operations associated with key
\&\fIpkey\fR.  Note that some signature algorithms (i.e. Ed25519 and Ed448)
do not use a digest during signing.  In this case \fIpnid\fR will be set
to NID_undef.  This function is only reliable for legacy keys, which
are keys with a \fBEVP_PKEY_ASN1_METHOD\fR; these keys have typically
been loaded from engines, or created with \fBEVP_PKEY_assign_RSA\fR\|(3) or
similar.
.SH NOTES
.IX Header "NOTES"
For all current standard OpenSSL public key algorithms SHA256 is returned.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBEVP_PKEY_get_default_digest_name()\fR and \fBEVP_PKEY_get_default_digest_nid()\fR
both return 1 if the message digest is advisory (that is other digests
can be used) and 2 if it is mandatory (other digests can not be used).
They return 0 or a negative value for failure.  In particular a return
value of \-2 indicates the operation is not supported by the public key
algorithm.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBEVP_PKEY_CTX_new\fR\|(3),
\&\fBEVP_PKEY_sign\fR\|(3),
\&\fBEVP_PKEY_digestsign_supports_digest\fR\|(3),
\&\fBEVP_PKEY_verify\fR\|(3),
\&\fBEVP_PKEY_verify_recover\fR\|(3),
.SH HISTORY
.IX Header "HISTORY"
This function was added in OpenSSL 1.0.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2006\-2023 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
