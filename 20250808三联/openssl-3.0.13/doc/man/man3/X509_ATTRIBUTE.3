.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "X509_ATTRIBUTE 3ossl"
.TH X509_ATTRIBUTE 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
X509_ATTRIBUTE, X509at_get_attr,
X509at_get_attr_count, X509at_get_attr_by_NID, X509at_get_attr_by_OBJ,
X509at_delete_attr,
X509at_add1_attr,
X509at_add1_attr_by_OBJ, X509at_add1_attr_by_NID, X509at_add1_attr_by_txt,
X509at_get0_data_by_OBJ,
X509_ATTRIBUTE_create, X509_ATTRIBUTE_create_by_NID,
X509_ATTRIBUTE_create_by_OBJ, X509_ATTRIBUTE_create_by_txt,
X509_ATTRIBUTE_set1_object, X509_ATTRIBUTE_set1_data,
X509_ATTRIBUTE_count,
X509_ATTRIBUTE_get0_data, X509_ATTRIBUTE_get0_object, X509_ATTRIBUTE_get0_type
\&\- X509 attribute functions
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/x509.h>
\&
\& typedef struct x509_attributes_st X509_ATTRIBUTE;
\&
\& int X509at_get_attr_count(const STACK_OF(X509_ATTRIBUTE) *x);
\& int X509at_get_attr_by_NID(const STACK_OF(X509_ATTRIBUTE) *x, int nid,
\&                            int lastpos);
\& int X509at_get_attr_by_OBJ(const STACK_OF(X509_ATTRIBUTE) *sk,
\&                            const ASN1_OBJECT *obj, int lastpos);
\& X509_ATTRIBUTE *X509at_get_attr(const STACK_OF(X509_ATTRIBUTE) *x, int loc);
\& X509_ATTRIBUTE *X509at_delete_attr(STACK_OF(X509_ATTRIBUTE) *x, int loc);
\& STACK_OF(X509_ATTRIBUTE) *X509at_add1_attr(STACK_OF(X509_ATTRIBUTE) **x,
\&                                            X509_ATTRIBUTE *attr);
\& STACK_OF(X509_ATTRIBUTE) *X509at_add1_attr_by_OBJ(STACK_OF(X509_ATTRIBUTE)
\&                                                   **x, const ASN1_OBJECT *obj,
\&                                                   int type,
\&                                                   const unsigned char *bytes,
\&                                                   int len);
\& STACK_OF(X509_ATTRIBUTE) *X509at_add1_attr_by_NID(STACK_OF(X509_ATTRIBUTE)
\&                                                   **x, int nid, int type,
\&                                                   const unsigned char *bytes,
\&                                                   int len);
\& STACK_OF(X509_ATTRIBUTE) *X509at_add1_attr_by_txt(STACK_OF(X509_ATTRIBUTE)
\&                                                   **x, const char *attrname,
\&                                                   int type,
\&                                                   const unsigned char *bytes,
\&                                                   int len);
\& void *X509at_get0_data_by_OBJ(const STACK_OF(X509_ATTRIBUTE) *x,
\&                               const ASN1_OBJECT *obj, int lastpos, int type);
\& X509_ATTRIBUTE *X509_ATTRIBUTE_create(int nid, int atrtype, void *value);
\& X509_ATTRIBUTE *X509_ATTRIBUTE_create_by_NID(X509_ATTRIBUTE **attr, int nid,
\&                                              int atrtype, const void *data,
\&                                              int len);
\& X509_ATTRIBUTE *X509_ATTRIBUTE_create_by_OBJ(X509_ATTRIBUTE **attr,
\&                                              const ASN1_OBJECT *obj,
\&                                              int atrtype, const void *data,
\&                                              int len);
\& X509_ATTRIBUTE *X509_ATTRIBUTE_create_by_txt(X509_ATTRIBUTE **attr,
\&                                              const char *atrname, int type,
\&                                              const unsigned char *bytes,
\&                                              int len);
\& int X509_ATTRIBUTE_set1_object(X509_ATTRIBUTE *attr, const ASN1_OBJECT *obj);
\& int X509_ATTRIBUTE_set1_data(X509_ATTRIBUTE *attr, int attrtype,
\&                              const void *data, int len);
\& void *X509_ATTRIBUTE_get0_data(X509_ATTRIBUTE *attr, int idx, int atrtype,
\&                                void *data);
\& int X509_ATTRIBUTE_count(const X509_ATTRIBUTE *attr);
\& ASN1_OBJECT *X509_ATTRIBUTE_get0_object(X509_ATTRIBUTE *attr);
\& ASN1_TYPE *X509_ATTRIBUTE_get0_type(X509_ATTRIBUTE *attr, int idx);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBX509_ATTRIBUTE\fR objects are used by many standards including X509, X509_REQ,
PKCS12, PKCS8, PKCS7 and CMS.
.PP
The \fBX509_ATTRIBUTE\fR object is used to represent the ASN.1 Attribute as defined
in RFC 5280, i.e.
.PP
.Vb 3
\& Attribute ::= SEQUENCE {
\&   type             AttributeType,
\&   values    SET OF AttributeValue }
\&
\& AttributeType ::= OBJECT IDENTIFIER
\& AttributeValue ::= ANY \-\- DEFINED BY AttributeType
.Ve
.PP
For example CMS defines the signing-time attribute as:
.PP
.Vb 2
\&  id\-signingTime OBJECT IDENTIFIER ::= { iso(1) member\-body(2)
\&      us(840) rsadsi(113549) pkcs(1) pkcs9(9) 5 }
\&
\&  SigningTime ::= Time
\&
\&  Time ::= CHOICE {
\&    utcTime UTCTime,
\&    generalizedTime GeneralizedTime }
.Ve
.PP
In OpenSSL \fBAttributeType\fR maps to an \fBASN1_OBJECT\fR object
and \fBAttributeValue\fR maps to a list of \fBASN1_TYPE\fR objects.
.PP
The following functions are used for \fBX509_ATTRIBUTE\fR objects.
.PP
\&\fBX509at_get_attr_by_OBJ()\fR finds the location of the first matching object \fIobj\fR
in a list of attributes \fIsk\fR. The search starts at the position after \fIlastpos\fR.
If the returned value is positive then it can be used on the next call to
\&\fBX509at_get_attr_by_OBJ()\fR as the value of \fIlastpos\fR in order to iterate through
the remaining attributes. \fIlastpos\fR can be set to any negative value on the
first call, in order to start searching from the start of the list.
.PP
\&\fBX509at_get_attr_by_NID()\fR is similar to \fBX509at_get_attr_by_OBJ()\fR except that it
passes the numerical identifier (NID) \fInid\fR associated with the object.
See <openssl/obj_mac.h> for a list of NID_*.
.PP
\&\fBX509at_get_attr()\fR returns the \fBX509_ATTRIBUTE\fR object at index \fIloc\fR in the
list of attributes \fIx\fR. \fIloc\fR should be in the range from 0 to
\&\fBX509at_get_attr_count()\fR \- 1.
.PP
\&\fBX509at_delete_attr()\fR removes the \fBX509_ATTRIBUTE\fR object at index \fIloc\fR in
the list of attributes \fIx\fR.
.PP
\&\fBX509at_add1_attr()\fR pushes a copy of the passed in \fBX509_ATTRIBUTE\fR object
to the list \fIx\fR.
Both \fIx\fR and \fIattr\fR must be non NULL or an error will occur.
If \fI*x\fR is NULL then a new list is created, otherwise it uses the
passed in list. An error will occur if an existing attribute (with the same
attribute type) already exists in the attribute list.
.PP
\&\fBX509at_add1_attr_by_OBJ()\fR creates a new \fBX509_ATTRIBUTE\fR using
\&\fBX509_ATTRIBUTE_set1_object()\fR and \fBX509_ATTRIBUTE_set1_data()\fR to assign a new
\&\fIobj\fR with type \fItype\fR and data \fIbytes\fR of length \fIlen\fR and then pushes it
to the attribute list \fIx\fR. Both \fIx\fR and \fIattr\fR must be non NULL or an error
will occur. If \fI*x\fR is NULL then a new attribute list is created. If \fIobj\fR
already exists in the attribute list then an error occurs.
.PP
\&\fBX509at_add1_attr_by_NID()\fR is similar to \fBX509at_add1_attr_by_OBJ()\fR except that it
passes the numerical identifier (NID) \fInid\fR associated with the object.
See <openssl/obj_mac.h> for a list of NID_*.
.PP
\&\fBX509at_add1_attr_by_txt()\fR is similar to \fBX509at_add1_attr_by_OBJ()\fR except that it
passes a name \fIattrname\fR associated with the object.
See <openssl/obj_mac.h> for a list of SN_* names.
.PP
\&\fBX509_ATTRIBUTE_set1_object()\fR assigns a \fBASN1_OBJECT\fR \fIobj\fR
to the attribute \fIattr\fR. If \fIattr\fR contained an existing \fBASN1_OBJECT\fR then
it is freed. An error occurs if either \fIattr\fR or \fIobj\fR are NULL, or if
the passed in \fIobj\fR cannot be duplicated.
.PP
\&\fBX509_ATTRIBUTE_set1_data()\fR pushes a new \fBASN1_TYPE\fR object onto the \fIattr\fR
attributes list. The new object is assigned a copy of the data in \fIdata\fR of
size \fIlen\fR.
If \fIattrtype\fR has flag \fIMBSTRING_FLAG\fR set then a table lookup using the
\&\fIattr\fR attributes NID is used to set an \fBASN1_STRING\fR using
\&\fBASN1_STRING_set_by_NID()\fR, and the passed in \fIdata\fR must be in the format
required for that object type or an error will occur.
If \fIlen\fR is not \-1 then internally \fBASN1_STRING_type_new()\fR is
used with the passed in \fIattrtype\fR.
If \fIattrtype\fR is 0 the call does nothing except return 1.
.PP
\&\fBX509_ATTRIBUTE_create()\fR creates a new \fBX509_ATTRIBUTE\fR using the \fInid\fR
to set the \fBASN1_OBJECT\fR OID and the \fIatrtype\fR and \fIvalue\fR to set the
\&\fBASN1_TYPE\fR.
.PP
\&\fBX509_ATTRIBUTE_create_by_OBJ()\fR uses \fBX509_ATTRIBUTE_set1_object()\fR and
\&\fBX509_ATTRIBUTE_set1_data()\fR to assign a new \fIobj\fR with type \fIatrtype\fR and
data \fIdata\fR of length \fIlen\fR. If the passed in attribute \fIattr\fR OR \fI*attr\fR is
NULL then a new \fBX509_ATTRIBUTE\fR will be returned, otherwise the passed in
\&\fBX509_ATTRIBUTE\fR is used. Note that the ASN1_OBJECT \fIobj\fR is pushed onto the
attributes existing list of objects, which could be an issue if the attributes
<ASN1_OBJECT> was different.
.PP
\&\fBX509_ATTRIBUTE_create_by_NID()\fR is similar to \fBX509_ATTRIBUTE_create_by_OBJ()\fR
except that it passes the numerical identifier (NID) \fInid\fR associated with the
object. See <openssl/obj_mac.h> for a list of NID_*.
.PP
\&\fBX509_ATTRIBUTE_create_by_txt()\fR is similar to \fBX509_ATTRIBUTE_create_by_OBJ()\fR
except that it passes a name \fIatrname\fR associated with the
object. See <openssl/obj_mac.h> for a list of SN_* names.
.PP
\&\fBX509_ATTRIBUTE_count()\fR returns the number of \fBASN1_TYPE\fR objects in an
attribute \fIattr\fR.
.PP
\&\fBX509_ATTRIBUTE_get0_type()\fR returns the \fBASN1_TYPE\fR object at index \fIidx\fR in
the attribute list \fIattr\fR. \fIidx\fR should be in the
range of 0 to \fBX509_ATTRIBUTE_count()\fR \- 1 or an error will occur.
.PP
\&\fBX509_ATTRIBUTE_get0_data()\fR returns the data of an \fBASN1_TYPE\fR object at
index \fIidx\fR in the attribute \fIattr\fR. \fIdata\fR is unused and can be set to NULL.
An error will occur if the attribute type \fIatrtype\fR does not match the type of
the \fBASN1_TYPE\fR object at index \fIidx\fR OR if \fIatrtype\fR is either
\&\fBV_ASN1_BOOLEAN\fR or \fBV_ASN1_NULL\fR OR if the \fIidx\fR is not in the
range 0 to \fBX509_ATTRIBUTE_count()\fR \- 1.
.PP
\&\fBX509at_get0_data_by_OBJ()\fR finds the first attribute in an attribute list \fIx\fR
that matches the \fIobj\fR starting at index \fIlastpos\fR and returns the data
retrieved from the found attributes first \fBASN1_TYPE\fR object. An error will
occur if the attribute type \fItype\fR does not match the type of the \fBASN1_TYPE\fR
object OR if \fItype\fR is either \fBV_ASN1_BOOLEAN\fR or \fBV_ASN1_NULL\fR OR the
attribute is not found.
If \fIlastpos\fR is less than \-1 then an error will occur if there are multiple
objects in the list \fIx\fR that match \fIobj\fR.
If \fIlastpos\fR is less than \-2 then an error will occur if there is more than
one \fBASN1_TYPE\fR object in the found attribute.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBX509at_get_attr_count()\fR returns the number of attributes in the list \fIx\fR or \-1
if \fIx\fR is NULL.
.PP
\&\fBX509at_get_attr_by_OBJ()\fR returns \-1 if either the list is empty OR the object
is not found, otherwise it returns the location of the object in the list.
.PP
\&\fBX509at_get_attr_by_NID()\fR is similar to \fBX509at_get_attr_by_OBJ()\fR, except that
it returns \-2 if the \fInid\fR is not known by OpenSSL.
.PP
\&\fBX509at_get_attr()\fR returns either an \fBX509_ATTRIBUTE\fR or NULL if there is a error.
.PP
\&\fBX509at_delete_attr()\fR returns either the removed \fBX509_ATTRIBUTE\fR or NULL if
there is a error.
.PP
\&\fBX509_ATTRIBUTE_count()\fR returns \-1 on error, otherwise it returns the number
of \fBASN1_TYPE\fR elements.
.PP
\&\fBX509_ATTRIBUTE_get0_type()\fR returns NULL on error, otherwise it returns a
\&\fBASN1_TYPE\fR object.
.PP
\&\fBX509_ATTRIBUTE_get0_data()\fR returns NULL if an error occurs,
otherwise it returns the data associated with an \fBASN1_TYPE\fR object.
.PP
\&\fBX509_ATTRIBUTE_set1_object()\fR and \fBX509_ATTRIBUTE_set1_data()\fR returns 1 on
success, or 0 otherwise.
.PP
\&\fBX509_ATTRIBUTE_create()\fR, \fBX509_ATTRIBUTE_create_by_OBJ()\fR,
\&\fBX509_ATTRIBUTE_create_by_NID()\fR and \fBX509_ATTRIBUTE_create_by_txt()\fR return either
a \fBX509_ATTRIBUTE\fR on success, or NULL if there is a error.
.PP
\&\fBX509at_add1_attr()\fR, \fBX509at_add1_attr_by_OBJ()\fR, \fBX509at_add1_attr_by_NID()\fR and
\&\fBX509at_add1_attr_by_txt()\fR return NULL on error, otherwise they return a list
of \fBX509_ATTRIBUTE\fR.
.PP
\&\fBX509at_get0_data_by_OBJ()\fR returns the data retrieved from the found attributes
first \fBASN1_TYPE\fR object, or NULL if an error occurs.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBASN1_TYPE_get\fR\|(3),
\&\fBASN1_INTEGER_get\fR\|(3),
\&\fBASN1_ENUMERATED_get\fR\|(3),
\&\fBASN1_STRING_get0_data\fR\|(3),
\&\fBASN1_STRING_length\fR\|(3),
\&\fBASN1_STRING_type\fR\|(3),
\&\fBX509_REQ_get_attr\fR\|(3),
\&\fBEVP_PKEY_get_attr\fR\|(3),
\&\fBCMS_signed_get_attr\fR\|(3),
\&\fBPKCS8_pkey_get0_attrs\fR\|(3),
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2023\-2024 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
