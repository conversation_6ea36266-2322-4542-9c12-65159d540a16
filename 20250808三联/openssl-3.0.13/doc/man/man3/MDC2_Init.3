.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "MDC2_INIT 3ossl"
.TH MDC2_INIT 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
MDC2, MDC2_Init, MDC2_Update, MDC2_Final \- MDC2 hash function
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/mdc2.h>
.Ve
.PP
The following functions have been deprecated since OpenSSL 3.0, and can be
hidden entirely by defining \fBOPENSSL_API_COMPAT\fR with a suitable version value,
see \fBopenssl_user_macros\fR\|(7):
.PP
.Vb 2
\& unsigned char *MDC2(const unsigned char *d, unsigned long n,
\&                     unsigned char *md);
\&
\& int MDC2_Init(MDC2_CTX *c);
\& int MDC2_Update(MDC2_CTX *c, const unsigned char *data,
\&                 unsigned long len);
\& int MDC2_Final(unsigned char *md, MDC2_CTX *c);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
All of the functions described on this page are deprecated.
Applications should instead use \fBEVP_DigestInit_ex\fR\|(3), \fBEVP_DigestUpdate\fR\|(3)
and \fBEVP_DigestFinal_ex\fR\|(3).
.PP
MDC2 is a method to construct hash functions with 128 bit output from
block ciphers.  These functions are an implementation of MDC2 with
DES.
.PP
\&\fBMDC2()\fR computes the MDC2 message digest of the \fBn\fR
bytes at \fBd\fR and places it in \fBmd\fR (which must have space for
MDC2_DIGEST_LENGTH == 16 bytes of output). If \fBmd\fR is NULL, the digest
is placed in a static array.
.PP
The following functions may be used if the message is not completely
stored in memory:
.PP
\&\fBMDC2_Init()\fR initializes a \fBMDC2_CTX\fR structure.
.PP
\&\fBMDC2_Update()\fR can be called repeatedly with chunks of the message to
be hashed (\fBlen\fR bytes at \fBdata\fR).
.PP
\&\fBMDC2_Final()\fR places the message digest in \fBmd\fR, which must have space
for MDC2_DIGEST_LENGTH == 16 bytes of output, and erases the \fBMDC2_CTX\fR.
.PP
Applications should use the higher level functions
\&\fBEVP_DigestInit\fR\|(3) etc. instead of calling the
hash functions directly.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBMDC2()\fR returns a pointer to the hash value.
.PP
\&\fBMDC2_Init()\fR, \fBMDC2_Update()\fR and \fBMDC2_Final()\fR return 1 for success, 0 otherwise.
.SH "CONFORMING TO"
.IX Header "CONFORMING TO"
ISO/IEC 10118\-2:2000 Hash-Function 2, with DES as the underlying block cipher.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBEVP_DigestInit\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
All of these functions were deprecated in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
