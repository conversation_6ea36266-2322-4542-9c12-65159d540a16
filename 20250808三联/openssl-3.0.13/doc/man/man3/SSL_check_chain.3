.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "SSL_CHECK_CHAIN 3ossl"
.TH SSL_CHECK_CHAIN 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
SSL_check_chain \- check certificate chain suitability
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/ssl.h>
\&
\& int SSL_check_chain(SSL *s, X509 *x, EVP_PKEY *pk, STACK_OF(X509) *chain);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBSSL_check_chain()\fR checks whether certificate \fBx\fR, private key \fBpk\fR and
certificate chain \fBchain\fR is suitable for use with the current session
\&\fBs\fR.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBSSL_check_chain()\fR returns a bitmap of flags indicating the validity of the
chain.
.PP
\&\fBCERT_PKEY_VALID\fR: the chain can be used with the current session.
If this flag is \fBnot\fR set then the certificate will never be used even
if the application tries to set it because it is inconsistent with the
peer preferences.
.PP
\&\fBCERT_PKEY_SIGN\fR: the EE key can be used for signing.
.PP
\&\fBCERT_PKEY_EE_SIGNATURE\fR: the signature algorithm of the EE certificate is
acceptable.
.PP
\&\fBCERT_PKEY_CA_SIGNATURE\fR: the signature algorithms of all CA certificates
are acceptable.
.PP
\&\fBCERT_PKEY_EE_PARAM\fR: the parameters of the end entity certificate are
acceptable (e.g. it is a supported curve).
.PP
\&\fBCERT_PKEY_CA_PARAM\fR: the parameters of all CA certificates are acceptable.
.PP
\&\fBCERT_PKEY_EXPLICIT_SIGN\fR: the end entity certificate algorithm
can be used explicitly for signing (i.e. it is mentioned in the signature
algorithms extension).
.PP
\&\fBCERT_PKEY_ISSUER_NAME\fR: the issuer name is acceptable. This is only
meaningful for client authentication.
.PP
\&\fBCERT_PKEY_CERT_TYPE\fR: the certificate type is acceptable. Only meaningful
for client authentication.
.PP
\&\fBCERT_PKEY_SUITEB\fR: chain is suitable for Suite B use.
.SH NOTES
.IX Header "NOTES"
\&\fBSSL_check_chain()\fR must be called in servers after a client hello message or in
clients after a certificate request message. It will typically be called
in the certificate callback.
.PP
An application wishing to support multiple certificate chains may call this
function on each chain in turn: starting with the one it considers the
most secure. It could then use the chain of the first set which returns
suitable flags.
.PP
As a minimum the flag \fBCERT_PKEY_VALID\fR must be set for a chain to be
usable. An application supporting multiple chains with different CA signature
algorithms may also wish to check \fBCERT_PKEY_CA_SIGNATURE\fR too. If no
chain is suitable a server should fall back to the most secure chain which
sets \fBCERT_PKEY_VALID\fR.
.PP
The validity of a chain is determined by checking if it matches a supported
signature algorithm, supported curves and in the case of client authentication
certificate types and issuer names.
.PP
Since the supported signature algorithms extension is only used in TLS 1.2,
TLS 1.3 and DTLS 1.2 the results for earlier versions of TLS and DTLS may not
be very useful. Applications may wish to specify a different "legacy" chain
for earlier versions of TLS or DTLS.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBSSL_CTX_set_cert_cb\fR\|(3),
\&\fBssl\fR\|(7)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2015\-2018 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
