.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "EVP_PKEY_TODATA 3ossl"
.TH EVP_PKEY_TODATA 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
EVP_PKEY_todata, EVP_PKEY_export
\&\- functions to return keys as an array of key parameters
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/evp.h>
\&
\& int EVP_PKEY_todata(const EVP_PKEY *pkey, int selection, OSSL_PARAM **params);
\& int EVP_PKEY_export(const EVP_PKEY *pkey, int selection,
\&                     OSSL_CALLBACK *export_cb, void *export_cbarg);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
The functions described here are used to extract \fBEVP_PKEY\fR key values as an
array of \fBOSSL_PARAM\fR\|(3).
.PP
\&\fBEVP_PKEY_todata()\fR extracts values from a key \fIpkey\fR using the \fIselection\fR.
\&\fIselection\fR is described in "Selections" in \fBEVP_PKEY_fromdata\fR\|(3).
\&\fBOSSL_PARAM_free\fR\|(3) should be used to free the returned parameters in
\&\fI*params\fR.
.PP
\&\fBEVP_PKEY_export()\fR is similar to \fBEVP_PKEY_todata()\fR but uses a callback
\&\fIexport_cb\fR that gets passed the value of \fIexport_cbarg\fR.
See \fBopenssl\-core.h\fR\|(7) for more information about the callback. Note that the
\&\fBOSSL_PARAM\fR\|(3) array that is passed to the callback is not persistent after the
callback returns. The user must preserve the items of interest, or use
\&\fBEVP_PKEY_todata()\fR if persistence is required.
.SH NOTES
.IX Header "NOTES"
These functions only work with key management methods coming from a provider.
This is the mirror function to \fBEVP_PKEY_fromdata\fR\|(3).
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBEVP_PKEY_todata()\fR and \fBEVP_PKEY_export()\fR return 1 for success and 0 for failure.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBOSSL_PARAM\fR\|(3), \fBopenssl\-core.h\fR\|(7),
\&\fBEVP_PKEY_fromdata\fR\|(3),
\&\fBEVP_PKEY\-RSA\fR\|(7), \fBEVP_PKEY\-DSA\fR\|(7), \fBEVP_PKEY\-DH\fR\|(7), \fBEVP_PKEY\-EC\fR\|(7),
\&\fBEVP_PKEY\-ED448\fR\|(7), \fBEVP_PKEY\-X25519\fR\|(7), \fBEVP_PKEY\-X448\fR\|(7),
\&\fBEVP_PKEY\-ED25519\fR\|(7)
.SH HISTORY
.IX Header "HISTORY"
These functions were added in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2021\-2023 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
