.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "OPENSSL_VERSION 3ossl"
.TH OPENSSL_VERSION 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
OPENSSL_VERSION_MAJOR, OPENSSL_VERSION_MINOR, OPENSSL_VERSION_PATCH,
OPENSSL_VERSION_PRE_RELEASE, OPENSSL_VERSION_BUILD_METADATA,
OPENSSL_VERSION_TEXT, OPENSSL_VERSION_PREREQ, OPENSSL_version_major,
OPENSSL_version_minor, OPENSSL_version_patch, OPENSSL_version_pre_release,
OPENSSL_version_build_metadata, OpenSSL_version, OPENSSL_VERSION_NUMBER,
OpenSSL_version_num, OPENSSL_info
\&\- get OpenSSL version number and other information
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/opensslv.h>
\&
\& #define OPENSSL_VERSION_MAJOR  x
\& #define OPENSSL_VERSION_MINOR  y
\& #define OPENSSL_VERSION_PATCH  z
\&
\& /* The definitions here are typical release values */
\& #define OPENSSL_VERSION_PRE_RELEASE ""
\& #define OPENSSL_VERSION_BUILD_METADATA ""
\&
\& #define OPENSSL_VERSION_TEXT "OpenSSL x.y.z xx XXX xxxx"
\&
\& #define OPENSSL_VERSION_PREREQ(maj,min)
\&
\& #include <openssl/crypto.h>
\&
\& unsigned int OPENSSL_version_major(void);
\& unsigned int OPENSSL_version_minor(void);
\& unsigned int OPENSSL_version_patch(void);
\& const char *OPENSSL_version_pre_release(void);
\& const char *OPENSSL_version_build_metadata(void);
\&
\& const char *OpenSSL_version(int t);
\&
\& const char *OPENSSL_info(int t);
\&
\& /* from openssl/opensslv.h */
\& #define OPENSSL_VERSION_NUMBER 0xnnnnnnnnL
\&
\& /* from openssl/crypto.h */
\& unsigned long OpenSSL_version_num();
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
.SS Macros
.IX Subsection "Macros"
The three macros \fBOPENSSL_VERSION_MAJOR\fR, \fBOPENSSL_VERSION_MINOR\fR and
\&\fBOPENSSL_VERSION_PATCH\fR represent the three parts of a version
identifier, \fR\f(BIMAJOR\fR\fB.\fR\f(BIMINOR\fR\fB.\fR\f(BIPATCH\fR\fB\fR.
.PP
The macro \fBOPENSSL_VERSION_PRE_RELEASE\fR is an added bit of text that
indicates that this is a pre-release version, such as \f(CW"\-dev"\fR for an
ongoing development snapshot or \f(CW"\-alpha3"\fR for an alpha release.
The value must be a string.
.PP
The macro \fBOPENSSL_VERSION_BUILD_METADATA\fR is extra information, reserved
for other parties, such as \f(CW"+fips"\fR, or \f(CW"+vendor.1"\fR).
The OpenSSL project will not touch this macro (will leave it an empty string).
The value must be a string.
.PP
\&\fBOPENSSL_VERSION_STR\fR is a convenience macro to get the short version
identifier string, \f(CW"\fR\f(CIMAJOR\fR\f(CW.\fR\f(CIMINOR\fR\f(CW.\fR\f(CIPATCH\fR\f(CW"\fR.
.PP
\&\fBOPENSSL_FULL_VERSION_STR\fR is a convenience macro to get the longer
version identifier string, which combines \fBOPENSSL_VERSION_STR\fR,
\&\fBOPENSSL_VERSION_PRE_RELEASE\fR and \fBOPENSSL_VERSION_BUILD_METADATA\fR.
.PP
\&\fBOPENSSL_VERSION_TEXT\fR is a convenience macro to get a full descriptive
version text, which includes \fBOPENSSL_FULL_VERSION_STR\fR and the release
date.
.PP
\&\fBOPENSSL_VERSION_PREREQ\fR is a useful macro for checking whether the OpenSSL
version for the headers in use is at least at the given pre-requisite major
(\fBmaj\fR) and minor (\fBmin\fR) number or not. It will evaluate to true if the
header version number (\fBOPENSSL_VERSION_MAJOR\fR.\fBOPENSSL_VERSION_MINOR\fR) is
greater than or equal to \fBmaj\fR.\fBmin\fR.
.PP
\&\fBOPENSSL_VERSION_NUMBER\fR is a combination of the major, minor and
patch version into a single integer 0xMNN00PP0L, where:
.IP M 4
.IX Item "M"
is the number from \fBOPENSSL_VERSION_MAJOR\fR, in hexadecimal notation
.IP NN 4
.IX Item "NN"
is the number from \fBOPENSSL_VERSION_MINOR\fR, in hexadecimal notation
.IP PP 4
.IX Item "PP"
is the number from \fBOPENSSL_VERSION_PATCH\fR, in hexadecimal notation
.SS Functions
.IX Subsection "Functions"
\&\fBOPENSSL_version_major()\fR, \fBOPENSSL_version_minor()\fR, \fBOPENSSL_version_patch()\fR,
\&\fBOPENSSL_version_pre_release()\fR, and \fBOPENSSL_version_build_metadata()\fR return
the values of the macros above for the build of the library, respectively.
.PP
\&\fBOpenSSL_version()\fR returns different strings depending on \fIt\fR:
.IP OPENSSL_VERSION 4
.IX Item "OPENSSL_VERSION"
The value of \fBOPENSSL_VERSION_TEXT\fR
.IP OPENSSL_VERSION_STRING 4
.IX Item "OPENSSL_VERSION_STRING"
The value of \fBOPENSSL_VERSION_STR\fR
.IP OPENSSL_FULL_VERSION_STRING 4
.IX Item "OPENSSL_FULL_VERSION_STRING"
The value of \fBOPENSSL_FULL_VERSION_STR\fR
.IP OPENSSL_CFLAGS 4
.IX Item "OPENSSL_CFLAGS"
The compiler flags set for the compilation process in the form
\&\f(CW\*(C`compiler: ...\*(C'\fR  if available, or \f(CW\*(C`compiler: information not available\*(C'\fR
otherwise.
.IP OPENSSL_BUILT_ON 4
.IX Item "OPENSSL_BUILT_ON"
The date of the build process in the form \f(CW\*(C`built on: ...\*(C'\fR if available
or \f(CW\*(C`built on: date not available\*(C'\fR otherwise.
The date would not be available in a reproducible build, for example.
.IP OPENSSL_PLATFORM 4
.IX Item "OPENSSL_PLATFORM"
The "Configure" target of the library build in the form \f(CW\*(C`platform: ...\*(C'\fR
if available, or \f(CW\*(C`platform: information not available\*(C'\fR otherwise.
.IP OPENSSL_DIR 4
.IX Item "OPENSSL_DIR"
The \fBOPENSSLDIR\fR setting of the library build in the form \f(CW\*(C`OPENSSLDIR: "..."\*(C'\fR
if available, or \f(CW\*(C`OPENSSLDIR: N/A\*(C'\fR otherwise.
.IP OPENSSL_ENGINES_DIR 4
.IX Item "OPENSSL_ENGINES_DIR"
The \fBENGINESDIR\fR setting of the library build in the form \f(CW\*(C`ENGINESDIR: "..."\*(C'\fR
if available, or \f(CW\*(C`ENGINESDIR: N/A\*(C'\fR otherwise. This option is deprecated in
OpenSSL 3.0.
.IP OPENSSL_MODULES_DIR 4
.IX Item "OPENSSL_MODULES_DIR"
The \fBMODULESDIR\fR setting of the library build in the form \f(CW\*(C`MODULESDIR: "..."\*(C'\fR
if available, or \f(CW\*(C`MODULESDIR: N/A\*(C'\fR otherwise.
.IP OPENSSL_CPU_INFO 4
.IX Item "OPENSSL_CPU_INFO"
The current OpenSSL cpu settings.
This is the current setting of the cpu capability flags. It is usually
automatically configured but may be set via an environment variable.
The value has the same syntax as the environment variable.
For x86 the string looks like \f(CW\*(C`CPUINFO: OPENSSL_ia32cap=0x123:0x456\*(C'\fR
or \f(CW\*(C`CPUINFO: N/A\*(C'\fR if not available.
.PP
For an unknown \fIt\fR, the text \f(CW\*(C`not available\*(C'\fR is returned.
.PP
\&\fBOPENSSL_info()\fR also returns different strings depending on \fIt\fR:
.IP OPENSSL_INFO_CONFIG_DIR 4
.IX Item "OPENSSL_INFO_CONFIG_DIR"
The configured \f(CW\*(C`OPENSSLDIR\*(C'\fR, which is the default location for
OpenSSL configuration files.
.IP OPENSSL_INFO_ENGINES_DIR 4
.IX Item "OPENSSL_INFO_ENGINES_DIR"
The configured \f(CW\*(C`ENGINESDIR\*(C'\fR, which is the default location for
OpenSSL engines.
.IP OPENSSL_INFO_MODULES_DIR 4
.IX Item "OPENSSL_INFO_MODULES_DIR"
The configured \f(CW\*(C`MODULESDIR\*(C'\fR, which is the default location for
dynamically loadable OpenSSL modules other than engines.
.IP OPENSSL_INFO_DSO_EXTENSION 4
.IX Item "OPENSSL_INFO_DSO_EXTENSION"
The configured dynamically loadable module extension.
.IP OPENSSL_INFO_DIR_FILENAME_SEPARATOR 4
.IX Item "OPENSSL_INFO_DIR_FILENAME_SEPARATOR"
The separator between a directory specification and a filename.
Note that on some operating systems, this is not the same as the
separator between directory elements.
.IP OPENSSL_INFO_LIST_SEPARATOR 4
.IX Item "OPENSSL_INFO_LIST_SEPARATOR"
The OpenSSL list separator.
This is typically used in strings that are lists of items, such as the
value of the environment variable \f(CW$PATH\fR on Unix (where the
separator is \f(CW\*(C`:\*(C'\fR) or \f(CW\*(C`%PATH%\*(C'\fR on Windows (where the separator is
\&\f(CW\*(C`;\*(C'\fR).
.IP OPENSSL_INFO_CPU_SETTINGS 4
.IX Item "OPENSSL_INFO_CPU_SETTINGS"
The current OpenSSL cpu settings.
This is the current setting of the cpu capability flags. It is usually
automatically configured but may be set via an environment variable.
The value has the same syntax as the environment variable.
For x86 the string looks like \f(CW\*(C`OPENSSL_ia32cap=0x123:0x456\*(C'\fR.
.PP
For an unknown \fIt\fR, NULL is returned.
.PP
\&\fBOpenSSL_version_num()\fR returns the value of \fBOPENSSL_VERSION_NUMBER\fR.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBOPENSSL_version_major()\fR, \fBOPENSSL_version_minor()\fR and \fBOPENSSL_version_patch()\fR
return the version number parts as integers.
.PP
\&\fBOPENSSL_version_pre_release()\fR and \fBOPENSSL_version_build_metadata()\fR return
the values of \fBOPENSSL_VERSION_PRE_RELEASE\fR and
\&\fBOPENSSL_VERSION_BUILD_METADATA\fR respectively as constant strings.
For any of them that is undefined, the empty string is returned.
.PP
\&\fBOpenSSL_version()\fR returns constant strings.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBcrypto\fR\|(7)
.SH HISTORY
.IX Header "HISTORY"
The macros and functions described here were added in OpenSSL 3.0,
except for OPENSSL_VERSION_NUMBER and \fBOpenSSL_version_num()\fR.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2018\-2022 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
