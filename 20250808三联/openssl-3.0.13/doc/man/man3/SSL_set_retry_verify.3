.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "SSL_SET_RETRY_VERIFY 3ossl"
.TH SSL_SET_RETRY_VERIFY 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
SSL_set_retry_verify \- indicate that certificate verification should be retried
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/ssl.h>
\&
\& int SSL_set_retry_verify(SSL *ssl);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBSSL_set_retry_verify()\fR should be called from the certificate verification
callback on a client when the application wants to indicate that the handshake
should be suspended and the control should be returned to the application.
\&\fBSSL_want_retry_verify\fR\|(3) will return 1 as a consequence until the handshake
is resumed again by the application, retrying the verification step.
.PP
Please refer to \fBSSL_CTX_set_cert_verify_callback\fR\|(3) for further details.
.SH NOTES
.IX Header "NOTES"
The effect of calling \fBSSL_set_retry_verify()\fR outside of the certificate
verification callback on the client side is undefined.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
SSL_set_retry \fBverify()\fR returns 1 on success, 0 otherwise.
.SH EXAMPLES
.IX Header "EXAMPLES"
The following code snippet shows how to obtain the \fBSSL\fR object associated
with the \fBX509_STORE_CTX\fR to call the \fBSSL_set_retry_verify()\fR function:
.PP
.Vb 2
\&    int idx = SSL_get_ex_data_X509_STORE_CTX_idx();
\&    SSL *ssl;
\&
\&    /* this should not happen but check anyway */
\&    if (idx < 0
\&        || (ssl = X509_STORE_CTX_get_ex_data(ctx, idx)) == NULL) 
\&        return 0;
\&
\&    if (/* we need to retry verification callback */)
\&        return SSL_set_retry_verify(ssl);
\&
\&    /* do normal processing of the verification callback */
.Ve
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBssl\fR\|(7), \fBSSL_connect\fR\|(3), \fBSSL_CTX_set_cert_verify_callback\fR\|(3),
\&\fBSSL_want_retry_verify\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
\&\fBSSL_set_retry_verify()\fR was added in OpenSSL 3.0.2 to replace backwards
incompatible handling of a negative return value from the verification
callback.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2022 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
