.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "SSL_LOAD_CLIENT_CA_FILE 3ossl"
.TH SSL_LOAD_CLIENT_CA_FILE 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
SSL_load_client_CA_file_ex, SSL_load_client_CA_file,
SSL_add_file_cert_subjects_to_stack,
SSL_add_dir_cert_subjects_to_stack,
SSL_add_store_cert_subjects_to_stack
\&\- load certificate names
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/ssl.h>
\&
\& STACK_OF(X509_NAME) *SSL_load_client_CA_file_ex(const char *file,
\&                                                 OSSL_LIB_CTX *libctx,
\&                                                 const char *propq);
\& STACK_OF(X509_NAME) *SSL_load_client_CA_file(const char *file);
\&
\& int SSL_add_file_cert_subjects_to_stack(STACK_OF(X509_NAME) *stack,
\&                                         const char *file);
\& int SSL_add_dir_cert_subjects_to_stack(STACK_OF(X509_NAME) *stack,
\&                                        const char *dir);
\& int SSL_add_store_cert_subjects_to_stack(STACK_OF(X509_NAME) *stack,
\&                                          const char *store);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBSSL_load_client_CA_file_ex()\fR reads certificates from \fIfile\fR and returns
a STACK_OF(X509_NAME) with the subject names found. The library context \fIlibctx\fR
and property query \fIpropq\fR are used when fetching algorithms from providers.
.PP
\&\fBSSL_load_client_CA_file()\fR is similar to \fBSSL_load_client_CA_file_ex()\fR
but uses NULL for the library context \fIlibctx\fR and property query \fIpropq\fR.
.PP
\&\fBSSL_add_file_cert_subjects_to_stack()\fR reads certificates from \fIfile\fR,
and adds their subject name to the already existing \fIstack\fR.
.PP
\&\fBSSL_add_dir_cert_subjects_to_stack()\fR reads certificates from every
file in the directory \fIdir\fR, and adds their subject name to the
already existing \fIstack\fR.
.PP
\&\fBSSL_add_store_cert_subjects_to_stack()\fR loads certificates from the
\&\fIstore\fR URI, and adds their subject name to the already existing
\&\fIstack\fR.
.SH NOTES
.IX Header "NOTES"
\&\fBSSL_load_client_CA_file()\fR reads a file of PEM formatted certificates and
extracts the X509_NAMES of the certificates found. While the name suggests
the specific usage as support function for
\&\fBSSL_CTX_set_client_CA_list\fR\|(3),
it is not limited to CA certificates.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
The following return values can occur:
.IP NULL 4
.IX Item "NULL"
The operation failed, check out the error stack for the reason.
.IP "Pointer to STACK_OF(X509_NAME)" 4
.IX Item "Pointer to STACK_OF(X509_NAME)"
Pointer to the subject names of the successfully read certificates.
.SH EXAMPLES
.IX Header "EXAMPLES"
Load names of CAs from file and use it as a client CA list:
.PP
.Vb 2
\& SSL_CTX *ctx;
\& STACK_OF(X509_NAME) *cert_names;
\&
\& ...
\& cert_names = SSL_load_client_CA_file("/path/to/CAfile.pem");
\& if (cert_names != NULL)
\&     SSL_CTX_set_client_CA_list(ctx, cert_names);
\& else
\&     /* error */
\& ...
.Ve
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBssl\fR\|(7),
\&\fBossl_store\fR\|(7),
\&\fBSSL_CTX_set_client_CA_list\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
\&\fBSSL_load_client_CA_file_ex()\fR and \fBSSL_add_store_cert_subjects_to_stack()\fR
were added in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
