.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "SSL_SESSION_HAS_TICKET 3ossl"
.TH SSL_SESSION_HAS_TICKET 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
SSL_SESSION_get0_ticket,
SSL_SESSION_has_ticket, SSL_SESSION_get_ticket_lifetime_hint
\&\- get details about the ticket associated with a session
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/ssl.h>
\&
\& int SSL_SESSION_has_ticket(const SSL_SESSION *s);
\& unsigned long SSL_SESSION_get_ticket_lifetime_hint(const SSL_SESSION *s);
\& void SSL_SESSION_get0_ticket(const SSL_SESSION *s, const unsigned char **tick,
\&                              size_t *len);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBSSL_SESSION_has_ticket()\fR returns 1 if there is a Session Ticket associated with
this session, and 0 otherwise.
.PP
SSL_SESSION_get_ticket_lifetime_hint returns the lifetime hint in seconds
associated with the session ticket.
.PP
SSL_SESSION_get0_ticket obtains a pointer to the ticket associated with a
session. The length of the ticket is written to \fB*len\fR. If \fBtick\fR is non
NULL then a pointer to the ticket is written to \fB*tick\fR. The pointer is only
valid while the connection is in use. The session (and hence the ticket pointer)
may also become invalid as a result of a call to \fBSSL_CTX_flush_sessions()\fR.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBSSL_SESSION_has_ticket()\fR returns 1 if session ticket exists or 0 otherwise.
.PP
\&\fBSSL_SESSION_get_ticket_lifetime_hint()\fR returns the number of seconds.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBssl\fR\|(7),
\&\fBd2i_SSL_SESSION\fR\|(3),
\&\fBSSL_SESSION_get_time\fR\|(3),
\&\fBSSL_SESSION_free\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
The \fBSSL_SESSION_has_ticket()\fR, \fBSSL_SESSION_get_ticket_lifetime_hint()\fR
and \fBSSL_SESSION_get0_ticket()\fR functions were added in OpenSSL 1.1.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2015\-2018 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
