.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "SRP_CREATE_VERIFIER 3ossl"
.TH SRP_CREATE_VERIFIER 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
SRP_create_verifier_ex,
SRP_create_verifier,
SRP_create_verifier_BN_ex,
SRP_create_verifier_BN,
SRP_check_known_gN_param,
SRP_get_default_gN
\&\- SRP authentication primitives
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/srp.h>
.Ve
.PP
The following functions have been deprecated since OpenSSL 3.0, and can be
hidden entirely by defining \fBOPENSSL_API_COMPAT\fR with a suitable version value,
see \fBopenssl_user_macros\fR\|(7):
.PP
.Vb 11
\& int SRP_create_verifier_BN_ex(const char *user, const char *pass, BIGNUM **salt,
\&                               BIGNUM **verifier, const BIGNUM *N,
\&                               const BIGNUM *g, OSSL_LIB_CTX *libctx,
\&                               const char *propq);
\& char *SRP_create_verifier_BN(const char *user, const char *pass, BIGNUM **salt,
\&                              BIGNUM **verifier, const BIGNUM *N, const BIGNUM *g);
\& char *SRP_create_verifier_ex(const char *user, const char *pass, char **salt,
\&                              char **verifier, const char *N, const char *g,
\&                              OSSL_LIB_CTX *libctx, const char *propq);
\& char *SRP_create_verifier(const char *user, const char *pass, char **salt,
\&                           char **verifier, const char *N, const char *g);
\&
\& char *SRP_check_known_gN_param(const BIGNUM *g, const BIGNUM *N);
\& SRP_gN *SRP_get_default_gN(const char *id);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
All of the functions described on this page are deprecated. There are no
available replacement functions at this time.
.PP
The \fBSRP_create_verifier_BN_ex()\fR function creates an SRP password verifier from
the supplied parameters as defined in section 2.4 of RFC 5054 using the library
context \fIlibctx\fR and property query string \fIpropq\fR. Any cryptographic
algorithms that need to be fetched will use the \fIlibctx\fR and \fIpropq\fR. See
"ALGORITHM FETCHING" in \fBcrypto\fR\|(7).
.PP
\&\fBSRP_create_verifier_BN()\fR is the same as \fBSRP_create_verifier_BN_ex()\fR except the
default library context and property query string is used.
.PP
On successful exit \fI*verifier\fR will point to a newly allocated BIGNUM containing
the verifier and (if a salt was not provided) \fI*salt\fR will be populated with a
newly allocated BIGNUM containing a random salt. If \fI*salt\fR is not NULL then
the provided salt is used instead.
The caller is responsible for freeing the allocated \fI*salt\fR and \fI*verifier\fR
BIGNUMS (use \fBBN_free\fR\|(3)).
.PP
The \fBSRP_create_verifier()\fR function is similar to \fBSRP_create_verifier_BN()\fR but
all numeric parameters are in a non-standard base64 encoding originally designed
for compatibility with libsrp. This is mainly present for historical compatibility
and its use is discouraged.
It is possible to pass NULL as \fIN\fR and an SRP group id as \fIg\fR instead to
load the appropriate gN values (see \fBSRP_get_default_gN()\fR).
If both \fIN\fR and \fIg\fR are NULL the 8192\-bit SRP group parameters are used.
The caller is responsible for freeing the allocated \fI*salt\fR and \fI*verifier\fR
(use \fBOPENSSL_free\fR\|(3)).
.PP
The \fBSRP_check_known_gN_param()\fR function checks that \fIg\fR and \fIN\fR are valid
SRP group parameters from RFC 5054 appendix A.
.PP
The \fBSRP_get_default_gN()\fR function returns the gN parameters for the RFC 5054 \fIid\fR
SRP group size.
The known ids are "1024", "1536", "2048", "3072", "4096", "6144" and "8192".
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBSRP_create_verifier_BN_ex()\fR and \fBSRP_create_verifier_BN()\fR return 1 on success and
0 on failure.
.PP
\&\fBSRP_create_verifier_ex()\fR and \fBSRP_create_verifier()\fR return NULL on failure and a
non-NULL value on success:
"*" if \fIN\fR is not NULL, the selected group id otherwise. This value should
not be freed.
.PP
\&\fBSRP_check_known_gN_param()\fR returns the text representation of the group id
(i.e. the prime bit size) or NULL if the arguments are not valid SRP group parameters.
This value should not be freed.
.PP
\&\fBSRP_get_default_gN()\fR returns NULL if \fIid\fR is not a valid group size,
or the 8192\-bit group parameters if \fIid\fR is NULL.
.SH EXAMPLES
.IX Header "EXAMPLES"
Generate and store a 8192 bit password verifier (error handling
omitted for clarity):
.PP
.Vb 2
\& #include <openssl/bn.h>
\& #include <openssl/srp.h>
\&
\& const char *username = "username";
\& const char *password = "password";
\&
\& SRP_VBASE *srpData = SRP_VBASE_new(NULL);
\&
\& SRP_gN *gN = SRP_get_default_gN("8192");
\&
\& BIGNUM *salt = NULL, *verifier = NULL;
\& SRP_create_verifier_BN_ex(username, password, &salt, &verifier, gN\->N, gN\->g,
\&                           NULL, NULL);
\&
\& SRP_user_pwd *pwd = SRP_user_pwd_new();
\& SRP_user_pwd_set1_ids(pwd, username, NULL);
\& SRP_user_pwd_set0_sv(pwd, salt, verifier);
\& SRP_user_pwd_set_gN(pwd, gN\->g, gN\->N);
\&
\& SRP_VBASE_add0_user(srpData, pwd);
.Ve
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBopenssl\-srp\fR\|(1),
\&\fBSRP_VBASE_new\fR\|(3),
\&\fBSRP_user_pwd_new\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
\&\fBSRP_create_verifier_BN_ex()\fR and \fBSRP_create_verifier_ex()\fR were introduced in
OpenSSL 3.0. All other functions were added in OpenSSL 1.0.1.
.PP
All of these functions were deprecated in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2018\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
