.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "SCT_NEW 3ossl"
.TH SCT_NEW 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
SCT_new, SCT_new_from_base64, SCT_free, SCT_LIST_free,
SCT_get_version, SCT_set_version,
SCT_get_log_entry_type, SCT_set_log_entry_type,
SCT_get0_log_id, SCT_set0_log_id, SCT_set1_log_id,
SCT_get_timestamp, SCT_set_timestamp,
SCT_get_signature_nid, SCT_set_signature_nid,
SCT_get0_signature, SCT_set0_signature, SCT_set1_signature,
SCT_get0_extensions, SCT_set0_extensions, SCT_set1_extensions,
SCT_get_source, SCT_set_source
\&\- A Certificate Transparency Signed Certificate Timestamp
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/ct.h>
\&
\& typedef enum {
\&     CT_LOG_ENTRY_TYPE_NOT_SET = \-1,
\&     CT_LOG_ENTRY_TYPE_X509 = 0,
\&     CT_LOG_ENTRY_TYPE_PRECERT = 1
\& } ct_log_entry_type_t;
\&
\& typedef enum {
\&     SCT_VERSION_NOT_SET = \-1,
\&     SCT_VERSION_V1 = 0
\& } sct_version_t;
\&
\& typedef enum {
\&     SCT_SOURCE_UNKNOWN,
\&     SCT_SOURCE_TLS_EXTENSION,
\&     SCT_SOURCE_X509V3_EXTENSION,
\&     SCT_SOURCE_OCSP_STAPLED_RESPONSE
\& } sct_source_t;
\&
\& SCT *SCT_new(void);
\& SCT *SCT_new_from_base64(unsigned char version,
\&                          const char *logid_base64,
\&                          ct_log_entry_type_t entry_type,
\&                          uint64_t timestamp,
\&                          const char *extensions_base64,
\&                          const char *signature_base64);
\&
\& void SCT_free(SCT *sct);
\& void SCT_LIST_free(STACK_OF(SCT) *a);
\&
\& sct_version_t SCT_get_version(const SCT *sct);
\& int SCT_set_version(SCT *sct, sct_version_t version);
\&
\& ct_log_entry_type_t SCT_get_log_entry_type(const SCT *sct);
\& int SCT_set_log_entry_type(SCT *sct, ct_log_entry_type_t entry_type);
\&
\& size_t SCT_get0_log_id(const SCT *sct, unsigned char **log_id);
\& int SCT_set0_log_id(SCT *sct, unsigned char *log_id, size_t log_id_len);
\& int SCT_set1_log_id(SCT *sct, const unsigned char *log_id, size_t log_id_len);
\&
\& uint64_t SCT_get_timestamp(const SCT *sct);
\& void SCT_set_timestamp(SCT *sct, uint64_t timestamp);
\&
\& int SCT_get_signature_nid(const SCT *sct);
\& int SCT_set_signature_nid(SCT *sct, int nid);
\&
\& size_t SCT_get0_signature(const SCT *sct, unsigned char **sig);
\& void SCT_set0_signature(SCT *sct, unsigned char *sig, size_t sig_len);
\& int SCT_set1_signature(SCT *sct, const unsigned char *sig, size_t sig_len);
\&
\& size_t SCT_get0_extensions(const SCT *sct, unsigned char **ext);
\& void SCT_set0_extensions(SCT *sct, unsigned char *ext, size_t ext_len);
\& int SCT_set1_extensions(SCT *sct, const unsigned char *ext, size_t ext_len);
\&
\& sct_source_t SCT_get_source(const SCT *sct);
\& int SCT_set_source(SCT *sct, sct_source_t source);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
Signed Certificate Timestamps (SCTs) are defined by RFC 6962, Section 3.2.
They constitute a promise by a Certificate Transparency (CT) log to publicly
record a certificate. By cryptographically verifying that a log did indeed issue
an SCT, some confidence can be gained that the certificate is publicly known.
.PP
An internal representation of an SCT can be created in one of two ways.
The first option is to create a blank SCT, using \fBSCT_new()\fR, and then populate
it using:
.IP \(bu 2
\&\fBSCT_set_version()\fR to set the SCT version.
.Sp
Only SCT_VERSION_V1 is currently supported.
.IP \(bu 2
\&\fBSCT_set_log_entry_type()\fR to set the type of certificate the SCT was issued for:
.Sp
\&\fBCT_LOG_ENTRY_TYPE_X509\fR for a normal certificate.
\&\fBCT_LOG_ENTRY_TYPE_PRECERT\fR for a pre-certificate.
.IP \(bu 2
\&\fBSCT_set0_log_id()\fR or \fBSCT_set1_log_id()\fR to set the LogID of the CT log that the SCT came from.
.Sp
The former takes ownership, whereas the latter makes a copy.
See RFC 6962, Section 3.2 for the definition of LogID.
.IP \(bu 2
\&\fBSCT_set_timestamp()\fR to set the time the SCT was issued (time in milliseconds
since the Unix Epoch).
.IP \(bu 2
\&\fBSCT_set_signature_nid()\fR to set the NID of the signature.
.IP \(bu 2
\&\fBSCT_set0_signature()\fR or \fBSCT_set1_signature()\fR to set the raw signature value.
.Sp
The former takes ownership, whereas the latter makes a copy.
.IP \(bu 2
\&\fBSCT_set0_extensions()\fR or \fBSCT_set1_extensions\fR to provide SCT extensions.
.Sp
The former takes ownership, whereas the latter makes a copy.
.PP
Alternatively, the SCT can be pre-populated from the following data using
\&\fBSCT_new_from_base64()\fR:
.IP \(bu 2
The SCT version (only SCT_VERSION_V1 is currently supported).
.IP \(bu 2
The LogID (see RFC 6962, Section 3.2), base64 encoded.
.IP \(bu 2
The type of certificate the SCT was issued for:
\&\fBCT_LOG_ENTRY_TYPE_X509\fR for a normal certificate.
\&\fBCT_LOG_ENTRY_TYPE_PRECERT\fR for a pre-certificate.
.IP \(bu 2
The time that the SCT was issued (time in milliseconds since the Unix Epoch).
.IP \(bu 2
The SCT extensions, base64 encoded.
.IP \(bu 2
The SCT signature, base64 encoded.
.PP
\&\fBSCT_set_source()\fR can be used to record where the SCT was found
(TLS extension, X.509 certificate extension or OCSP response). This is not
required for verifying the SCT.
.SH NOTES
.IX Header "NOTES"
Some of the setters return int, instead of void. These will all return 1 on
success, 0 on failure. They will not make changes on failure.
.PP
All of the setters will reset the validation status of the SCT to
SCT_VALIDATION_STATUS_NOT_SET (see \fBSCT_validate\fR\|(3)).
.PP
\&\fBSCT_set_source()\fR will call \fBSCT_set_log_entry_type()\fR if the type of
certificate the SCT was issued for can be inferred from where the SCT was found.
For example, an SCT found in an X.509 extension must have been issued for a pre\-
certificate.
.PP
\&\fBSCT_set_source()\fR will not refuse unknown values.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBSCT_set_version()\fR returns 1 if the specified version is supported, 0 otherwise.
.PP
\&\fBSCT_set_log_entry_type()\fR returns 1 if the specified log entry type is supported, 0 otherwise.
.PP
\&\fBSCT_set0_log_id()\fR and \fBSCT_set1_log_id\fR return 1 if the specified LogID is a
valid SHA\-256 hash, 0 otherwise. Additionally, \fBSCT_set1_log_id\fR returns 0 if
malloc fails.
.PP
\&\fBSCT_set_signature_nid\fR returns 1 if the specified NID is supported, 0 otherwise.
.PP
\&\fBSCT_set1_extensions\fR and \fBSCT_set1_signature\fR return 1 if the supplied buffer
is copied successfully, 0 otherwise (i.e. if malloc fails).
.PP
\&\fBSCT_set_source\fR returns 1 on success, 0 otherwise.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBct\fR\|(7),
\&\fBSCT_validate\fR\|(3),
\&\fBOBJ_nid2obj\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
These functions were added in OpenSSL 1.1.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2016\-2017 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
