.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "BIO_F_PREFIX 3ossl"
.TH BIO_F_PREFIX 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
BIO_f_prefix, BIO_set_prefix, BIO_set_indent, BIO_get_indent
\&\- prefix BIO filter
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/bio.h>
\&
\& const BIO_METHOD *BIO_f_prefix(void);
\& long BIO_set_prefix(BIO *b, const char *prefix);
\& long BIO_set_indent(BIO *b, long indent);
\& long BIO_get_indent(BIO *b);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBBIO_f_cipher()\fR returns the prefix BIO method. This is a filter for
text output, where each line gets automatically prefixed and indented
according to user input.
.PP
The prefix and the indentation are combined.  For each line of output
going through this filter, the prefix is output first, then the amount
of additional spaces indicated by the indentation, and then the line
itself.
.PP
By default, there is no prefix, and indentation is set to 0.
.PP
\&\fBBIO_set_prefix()\fR sets the prefix to be used for future lines of
text, using \fIprefix\fR.  \fIprefix\fR may be NULL, signifying that there
should be no prefix.  If \fIprefix\fR isn't NULL, this function makes a
copy of it.
.PP
\&\fBBIO_set_indent()\fR sets the indentation to be used for future lines of
text, using \fIindent\fR.  Negative values are not allowed.
.PP
\&\fBBIO_get_indent()\fR gets the current indentation.
.SH NOTES
.IX Header "NOTES"
\&\fBBIO_set_prefix()\fR, \fBBIO_set_indent()\fR and \fBBIO_get_indent()\fR are
implemented as macros.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBBIO_f_prefix()\fR returns the prefix BIO method.
.PP
\&\fBBIO_set_prefix()\fR returns 1 if the prefix was correctly set, or <=0 on
failure.
.PP
\&\fBBIO_set_indent()\fR returns 1 if the prefix was correctly set, or <=0 on
failure.
.PP
\&\fBBIO_get_indent()\fR returns the current indentation, or a negative value for failure.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBbio\fR\|(7)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2019\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
