.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "B2I_PVK_BIO_EX 3ossl"
.TH B2I_PVK_BIO_EX 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
b2i_PVK_bio, b2i_PVK_bio_ex, i2b_PVK_bio, i2b_PVK_bio_ex \- Decode and encode
functions for reading and writing MSBLOB format private keys
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/pem.h>
\&
\& EVP_PKEY *b2i_PVK_bio(BIO *in, pem_password_cb *cb, void *u);
\& EVP_PKEY *b2i_PVK_bio_ex(BIO *in, pem_password_cb *cb, void *u,
\&                          OSSL_LIB_CTX *libctx, const char *propq);
\& int i2b_PVK_bio(BIO *out, const EVP_PKEY *pk, int enclevel,
\&                 pem_password_cb *cb, void *u);
\& int i2b_PVK_bio_ex(BIO *out, const EVP_PKEY *pk, int enclevel,
\&                    pem_password_cb *cb, void *u,
\&                    OSSL_LIB_CTX *libctx, const char *propq);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBb2i_PVK_bio_ex()\fR decodes a private key of MSBLOB format read from a \fBBIO\fR. It
attempts to automatically determine the key type. If the key is encrypted then
\&\fIcb\fR is called with the user data \fIu\fR in order to obtain a password to decrypt
the key. The supplied library context \fIlibctx\fR and property query
string \fIpropq\fR are used in any decrypt operation.
.PP
\&\fBb2i_PVK_bio()\fR does the same as \fBb2i_PVK_bio_ex()\fR except that the default
library context and property query string are used.
.PP
\&\fBi2b_PVK_bio_ex()\fR encodes \fIpk\fR using MSBLOB format. If \fIenclevel\fR is 1 then
a password obtained via \fIpem_password_cb\fR is used to encrypt the private key.
If \fIenclevel\fR is 0 then no encryption is applied. The user data in \fIu\fR is
passed to the password callback. The supplied library context \fIlibctx\fR and
property query string \fIpropq\fR are used in any decrypt operation.
.PP
\&\fBi2b_PVK_bio()\fR does the same as \fBi2b_PVK_bio_ex()\fR except that the default
library context and property query string are used.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
The \fBb2i_PVK_bio()\fR and \fBb2i_PVK_bio_ex()\fR functions return a valid \fBEVP_KEY\fR
structure or \fBNULL\fR if an error occurs. The error code can be obtained by calling
\&\fBERR_get_error\fR\|(3).
.PP
\&\fBi2b_PVK_bio()\fR and \fBi2b_PVK_bio_ex()\fR return the number of bytes successfully
encoded or a negative value if an error occurs. The error code can be obtained
by calling \fBERR_get_error\fR\|(3).
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBcrypto\fR\|(7),
\&\fBd2i_PKCS8PrivateKey_bio\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
\&\fBb2i_PVK_bio_ex()\fR and \fBi2b_PVK_bio_ex()\fR were added in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
