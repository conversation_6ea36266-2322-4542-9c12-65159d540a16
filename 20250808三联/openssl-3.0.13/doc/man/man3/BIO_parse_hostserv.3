.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "BIO_PARSE_HOSTSERV 3ossl"
.TH BIO_PARSE_HOSTSERV 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
BIO_hostserv_priorities,
BIO_parse_hostserv
\&\- utility routines to parse a standard host and service string
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/bio.h>
\&
\& enum BIO_hostserv_priorities {
\&     BIO_PARSE_PRIO_HOST, BIO_PARSE_PRIO_SERV
\& };
\& int BIO_parse_hostserv(const char *hostserv, char **host, char **service,
\&                        enum BIO_hostserv_priorities hostserv_prio);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBBIO_parse_hostserv()\fR will parse the information given in \fBhostserv\fR,
create strings with the hostname and service name and give those
back via \fBhost\fR and \fBservice\fR.  Those will need to be freed after
they are used.  \fBhostserv_prio\fR helps determine if \fBhostserv\fR shall
be interpreted primarily as a hostname or a service name in ambiguous
cases.
.PP
The syntax the \fBBIO_parse_hostserv()\fR recognises is:
.PP
.Vb 7
\& host + \*(Aq:\*(Aq + service
\& host + \*(Aq:\*(Aq + \*(Aq*\*(Aq
\& host + \*(Aq:\*(Aq
\&        \*(Aq:\*(Aq + service
\& \*(Aq*\*(Aq  + \*(Aq:\*(Aq + service
\& host
\& service
.Ve
.PP
The host part can be a name or an IP address.  If it's a IPv6
address, it MUST be enclosed in brackets, such as '[::1]'.
.PP
The service part can be a service name or its port number.  A service name
will be mapped to a port number using the system function \fBgetservbyname()\fR.
.PP
The returned values will depend on the given \fBhostserv\fR string
and \fBhostserv_prio\fR, as follows:
.PP
.Vb 5
\& host + \*(Aq:\*(Aq + service  => *host = "host", *service = "service"
\& host + \*(Aq:\*(Aq + \*(Aq*\*(Aq      => *host = "host", *service = NULL
\& host + \*(Aq:\*(Aq            => *host = "host", *service = NULL
\&        \*(Aq:\*(Aq + service  => *host = NULL, *service = "service"
\&  \*(Aq*\*(Aq + \*(Aq:\*(Aq + service  => *host = NULL, *service = "service"
\&
\& in case no \*(Aq:\*(Aq is present in the string, the result depends on
\& hostserv_prio, as follows:
\&
\& when hostserv_prio == BIO_PARSE_PRIO_HOST
\& host                 => *host = "host", *service untouched
\&
\& when hostserv_prio == BIO_PARSE_PRIO_SERV
\& service              => *host untouched, *service = "service"
.Ve
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBBIO_parse_hostserv()\fR returns 1 on success or 0 on error.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBBIO_ADDRINFO\fR\|(3)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2016\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
