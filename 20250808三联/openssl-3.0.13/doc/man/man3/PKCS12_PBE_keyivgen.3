.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "PKCS12_PBE_KEYIVGEN 3ossl"
.TH PKCS12_PBE_KEYIVGEN 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
PKCS12_PBE_keyivgen, PKCS12_PBE_keyivgen_ex,
PKCS12_pbe_crypt, PKCS12_pbe_crypt_ex \- PKCS#12 Password based encryption
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/evp.h>
\&
\& int PKCS12_PBE_keyivgen(EVP_CIPHER_CTX *ctx, const char *pass, int passlen,
\&                         ASN1_TYPE *param, const EVP_CIPHER *cipher,
\&                         const EVP_MD *md_type, int en_de);
\& int PKCS12_PBE_keyivgen_ex(EVP_CIPHER_CTX *ctx, const char *pass, int passlen,
\&                            ASN1_TYPE *param, const EVP_CIPHER *cipher,
\&                            const EVP_MD *md_type, int en_de,
\&                            OSSL_LIB_CTX *libctx, const char *propq);
\& unsigned char *PKCS12_pbe_crypt(const X509_ALGOR *algor,
\&                                 const char *pass, int passlen,
\&                                 const unsigned char *in, int inlen,
\&                                 unsigned char **data, int *datalen,
\&                                 int en_de);
\& unsigned char *PKCS12_pbe_crypt_ex(const X509_ALGOR *algor,
\&                                    const char *pass, int passlen,
\&                                    const unsigned char *in, int inlen,
\&                                    unsigned char **data, int *datalen,
\&                                    int en_de, OSSL_LIB_CTX *libctx,
\&                                    const char *propq);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBPKCS12_PBE_keyivgen()\fR and \fBPKCS12_PBE_keyivgen_ex()\fR take a password \fIpass\fR of
length \fIpasslen\fR, parameters \fIparam\fR and a message digest function \fImd_type\fR
and perform a key derivation according to PKCS#12. The resulting key is
then used to initialise the cipher context \fIctx\fR with a cipher \fIcipher\fR for
encryption (\fIen_de\fR=1) or decryption (\fIen_de\fR=0).
.PP
\&\fBPKCS12_PBE_keyivgen_ex()\fR also allows the application to specify a library context
\&\fIlibctx\fR and property query \fIpropq\fR to select appropriate algorithm
implementations.
.PP
\&\fBPKCS12_pbe_crypt()\fR and \fBPKCS12_pbe_crypt_ex()\fR will encrypt or decrypt a buffer
based on the algorithm in \fIalgor\fR and password \fIpass\fR of length \fIpasslen\fR.
The input is from \fIin\fR of length \fIinlen\fR and output is into a malloc'd buffer
returned in \fI*data\fR of length \fIdatalen\fR. The operation is determined by \fIen_de\fR,
encryption (\fIen_de\fR=1) or decryption (\fIen_de\fR=0).
.PP
\&\fBPKCS12_pbe_crypt_ex()\fR allows the application to specify a library context
\&\fIlibctx\fR and property query \fIpropq\fR to select appropriate algorithm
implementations.
.PP
\&\fIpass\fR is the password used in the derivation of length \fIpasslen\fR. \fIpass\fR
is an optional parameter and can be NULL. If \fIpasslen\fR is \-1, then the
function will calculate the length of \fIpass\fR using \fBstrlen()\fR.
.PP
\&\fIsalt\fR is the salt used in the derivation of length \fIsaltlen\fR. If the
\&\fIsalt\fR is NULL, then \fIsaltlen\fR must be 0. The function will not
attempt to calculate the length of the \fIsalt\fR because it is not assumed to
be NULL terminated.
.PP
\&\fIiter\fR is the iteration count and its value should be greater than or
equal to 1. RFC 2898 suggests an iteration count of at least 1000. Any
\&\fIiter\fR less than 1 is treated as a single iteration.
.PP
\&\fIdigest\fR is the message digest function used in the derivation.
.PP
Functions ending in \fB_ex()\fR take optional parameters \fIlibctx\fR and \fIpropq\fR which
are used to select appropriate algorithm implementations.
.SH NOTES
.IX Header "NOTES"
The functions are typically used in PKCS#12 to encrypt objects.
.PP
These functions make no assumption regarding the given password.
It will simply be treated as a byte sequence.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBPKCS12_PBE_keyivgen()\fR, \fBPKCS12_PBE_keyivgen_ex()\fR return 1 on success or 0 on error.
.PP
\&\fBPKCS12_pbe_crypt()\fR and \fBPKCS12_pbe_crypt_ex()\fR return a buffer containing the
output or NULL if an error occurred.
.SH "CONFORMING TO"
.IX Header "CONFORMING TO"
IETF RFC 7292 (<https://tools.ietf.org/html/rfc7292>)
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBEVP_PBE_CipherInit_ex\fR\|(3),
\&\fBPKCS8_encrypt_ex\fR\|(3),
\&\fBpassphrase\-encoding\fR\|(7)
.SH HISTORY
.IX Header "HISTORY"
\&\fBPKCS12_PBE_keyivgen_ex()\fR and \fBPKCS12_pbe_crypt_ex()\fR were added in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2014\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
