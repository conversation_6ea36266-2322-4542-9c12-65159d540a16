.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "OPENSSL_LH_COMPFUNC 3ossl"
.TH OPENSSL_LH_COMPFUNC 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
LHASH, DECLARE_LHASH_OF,
OPENSSL_LH_COMPFUNC, OPENSSL_LH_HASHFUNC, OPENSSL_LH_DOALL_FUNC,
LHASH_DOALL_ARG_FN_TYPE,
IMPLEMENT_LHASH_HASH_FN, IMPLEMENT_LHASH_COMP_FN,
lh_TYPE_new, lh_TYPE_free, lh_TYPE_flush,
lh_TYPE_insert, lh_TYPE_delete, lh_TYPE_retrieve,
lh_TYPE_doall, lh_TYPE_doall_arg, lh_TYPE_num_items, lh_TYPE_get_down_load,
lh_TYPE_set_down_load, lh_TYPE_error,
OPENSSL_LH_new, OPENSSL_LH_free,  OPENSSL_LH_flush,
OPENSSL_LH_insert, OPENSSL_LH_delete, OPENSSL_LH_retrieve,
OPENSSL_LH_doall, OPENSSL_LH_doall_arg, OPENSSL_LH_num_items,
OPENSSL_LH_get_down_load, OPENSSL_LH_set_down_load, OPENSSL_LH_error
\&\- dynamic hash table
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/lhash.h>
\&
\& DECLARE_LHASH_OF(TYPE);
\&
\& LHASH_OF(TYPE) *lh_TYPE_new(OPENSSL_LH_HASHFUNC hash, OPENSSL_LH_COMPFUNC compare);
\& void lh_TYPE_free(LHASH_OF(TYPE) *table);
\& void lh_TYPE_flush(LHASH_OF(TYPE) *table);
\&
\& TYPE *lh_TYPE_insert(LHASH_OF(TYPE) *table, TYPE *data);
\& TYPE *lh_TYPE_delete(LHASH_OF(TYPE) *table, TYPE *data);
\& TYPE *lh_TYPE_retrieve(LHASH_OF(TYPE) *table, TYPE *data);
\&
\& void lh_TYPE_doall(LHASH_OF(TYPE) *table, OPENSSL_LH_DOALL_FUNC func);
\& void lh_TYPE_doall_arg(LHASH_OF(TYPE) *table, OPENSSL_LH_DOALL_FUNCARG func,
\&                        TYPE *arg);
\&
\& unsigned long lh_TYPE_num_items(OPENSSL_LHASH *lh);
\& unsigned long lh_TYPE_get_down_load(OPENSSL_LHASH *lh);
\& void lh_TYPE_set_down_load(OPENSSL_LHASH *lh, unsigned long dl);
\&
\& int lh_TYPE_error(LHASH_OF(TYPE) *table);
\&
\& typedef int (*OPENSSL_LH_COMPFUNC)(const void *, const void *);
\& typedef unsigned long (*OPENSSL_LH_HASHFUNC)(const void *);
\& typedef void (*OPENSSL_LH_DOALL_FUNC)(const void *);
\& typedef void (*LHASH_DOALL_ARG_FN_TYPE)(const void *, const void *);
\&
\& OPENSSL_LHASH *OPENSSL_LH_new(OPENSSL_LH_HASHFUNC h, OPENSSL_LH_COMPFUNC c);
\& void OPENSSL_LH_free(OPENSSL_LHASH *lh);
\& void OPENSSL_LH_flush(OPENSSL_LHASH *lh);
\&
\& void *OPENSSL_LH_insert(OPENSSL_LHASH *lh, void *data);
\& void *OPENSSL_LH_delete(OPENSSL_LHASH *lh, const void *data);
\& void *OPENSSL_LH_retrieve(OPENSSL_LHASH *lh, const void *data);
\&
\& void OPENSSL_LH_doall(OPENSSL_LHASH *lh, OPENSSL_LH_DOALL_FUNC func);
\& void OPENSSL_LH_doall_arg(OPENSSL_LHASH *lh, OPENSSL_LH_DOALL_FUNCARG func, void *arg);
\&
\& unsigned long OPENSSL_LH_num_items(OPENSSL_LHASH *lh);
\& unsigned long OPENSSL_LH_get_down_load(OPENSSL_LHASH *lh);
\& void OPENSSL_LH_set_down_load(OPENSSL_LHASH *lh, unsigned long dl);
\&
\& int OPENSSL_LH_error(OPENSSL_LHASH *lh);
\&
\& #define LH_LOAD_MULT   /* integer constant */
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
This library implements type-checked dynamic hash tables. The hash
table entries can be arbitrary structures. Usually they consist of key
and value fields.  In the description here, \fR\f(BITYPE\fR\fB\fR is used a placeholder
for any of the OpenSSL datatypes, such as \fISSL_SESSION\fR.
.PP
\&\fBlh_\fR\f(BITYPE\fR\fB_new\fR() creates a new \fBLHASH_OF\fR(\fR\f(BITYPE\fR\fB\fR) structure to store
arbitrary data entries, and specifies the 'hash' and 'compare'
callbacks to be used in organising the table's entries.  The \fIhash\fR
callback takes a pointer to a table entry as its argument and returns
an unsigned long hash value for its key field.  The hash value is
normally truncated to a power of 2, so make sure that your hash
function returns well mixed low order bits.  The \fIcompare\fR callback
takes two arguments (pointers to two hash table entries), and returns
0 if their keys are equal, nonzero otherwise.
.PP
If your hash table
will contain items of some particular type and the \fIhash\fR and
\&\fIcompare\fR callbacks hash/compare these types, then the
\&\fBIMPLEMENT_LHASH_HASH_FN\fR and \fBIMPLEMENT_LHASH_COMP_FN\fR macros can be
used to create callback wrappers of the prototypes required by
\&\fBlh_\fR\f(BITYPE\fR\fB_new\fR() as shown in this example:
.PP
.Vb 11
\& /*
\&  * Implement the hash and compare functions; "stuff" can be any word.
\&  */
\& static unsigned long stuff_hash(const TYPE *a)
\& {
\&     ...
\& }
\& static int stuff_cmp(const TYPE *a, const TYPE *b)
\& {
\&     ...
\& }
\&
\& /*
\&  * Implement the wrapper functions.
\&  */
\& static IMPLEMENT_LHASH_HASH_FN(stuff, TYPE)
\& static IMPLEMENT_LHASH_COMP_FN(stuff, TYPE)
.Ve
.PP
If the type is going to be used in several places, the following macros
can be used in a common header file to declare the function wrappers:
.PP
.Vb 2
\& DECLARE_LHASH_HASH_FN(stuff, TYPE)
\& DECLARE_LHASH_COMP_FN(stuff, TYPE)
.Ve
.PP
Then a hash table of \fR\f(BITYPE\fR\fB\fR objects can be created using this:
.PP
.Vb 1
\& LHASH_OF(TYPE) *htable;
\&
\& htable = B<lh_I<TYPE>_new>(LHASH_HASH_FN(stuff), LHASH_COMP_FN(stuff));
.Ve
.PP
\&\fBlh_\fR\f(BITYPE\fR\fB_free\fR() frees the \fBLHASH_OF\fR(\fR\f(BITYPE\fR\fB\fR) structure
\&\fItable\fR. Allocated hash table entries will not be freed; consider
using \fBlh_\fR\f(BITYPE\fR\fB_doall\fR() to deallocate any remaining entries in the
hash table (see below).
.PP
\&\fBlh_\fR\f(BITYPE\fR\fB_flush\fR() empties the \fBLHASH_OF\fR(\fR\f(BITYPE\fR\fB\fR) structure \fItable\fR. New
entries can be added to the flushed table.  Allocated hash table entries
will not be freed; consider using \fBlh_\fR\f(BITYPE\fR\fB_doall\fR() to deallocate any
remaining entries in the hash table (see below).
.PP
\&\fBlh_\fR\f(BITYPE\fR\fB_insert\fR() inserts the structure pointed to by \fIdata\fR into
\&\fItable\fR.  If there already is an entry with the same key, the old
value is replaced. Note that \fBlh_\fR\f(BITYPE\fR\fB_insert\fR() stores pointers, the
data are not copied.
.PP
\&\fBlh_\fR\f(BITYPE\fR\fB_delete\fR() deletes an entry from \fItable\fR.
.PP
\&\fBlh_\fR\f(BITYPE\fR\fB_retrieve\fR() looks up an entry in \fItable\fR. Normally, \fIdata\fR
is a structure with the key field(s) set; the function will return a
pointer to a fully populated structure.
.PP
\&\fBlh_\fR\f(BITYPE\fR\fB_doall\fR() will, for every entry in the hash table, call
\&\fIfunc\fR with the data item as its parameter.
For example:
.PP
.Vb 2
\& /* Cleans up resources belonging to \*(Aqa\*(Aq (this is implemented elsewhere) */
\& void TYPE_cleanup_doall(TYPE *a);
\&
\& /* Implement a prototype\-compatible wrapper for "TYPE_cleanup" */
\& IMPLEMENT_LHASH_DOALL_FN(TYPE_cleanup, TYPE)
\&
\& /* Call "TYPE_cleanup" against all items in a hash table. */
\& lh_TYPE_doall(hashtable, LHASH_DOALL_FN(TYPE_cleanup));
\&
\& /* Then the hash table itself can be deallocated */
\& lh_TYPE_free(hashtable);
.Ve
.PP
\&\fBlh_\fR\f(BITYPE\fR\fB_doall_arg\fR() is the same as \fBlh_\fR\f(BITYPE\fR\fB_doall\fR() except that
\&\fIfunc\fR will be called with \fIarg\fR as the second argument and \fIfunc\fR
should be of type \fBLHASH_DOALL_ARG_FN\fR(\fR\f(BITYPE\fR\fB\fR) (a callback prototype
that is passed both the table entry and an extra argument).  As with
\&\fBlh_doall()\fR, you can instead choose to declare your callback with a
prototype matching the types you are dealing with and use the
declare/implement macros to create compatible wrappers that cast
variables before calling your type-specific callbacks.  An example of
this is demonstrated here (printing all hash table entries to a BIO
that is provided by the caller):
.PP
.Vb 2
\& /* Prints item \*(Aqa\*(Aq to \*(Aqoutput_bio\*(Aq (this is implemented elsewhere) */
\& void TYPE_print_doall_arg(const TYPE *a, BIO *output_bio);
\&
\& /* Implement a prototype\-compatible wrapper for "TYPE_print" */
\& static IMPLEMENT_LHASH_DOALL_ARG_FN(TYPE, const TYPE, BIO)
\&
\& /* Print out the entire hashtable to a particular BIO */
\& lh_TYPE_doall_arg(hashtable, LHASH_DOALL_ARG_FN(TYPE_print), BIO,
\&                   logging_bio);
.Ve
.PP
Note that it is by default \fBnot\fR safe to use \fBlh_\fR\f(BITYPE\fR\fB_delete\fR() inside a
callback passed to \fBlh_\fR\f(BITYPE\fR\fB_doall\fR() or \fBlh_\fR\f(BITYPE\fR\fB_doall_arg\fR(). The
reason for this is that deleting an item from the hash table may result in the
hash table being contracted to a smaller size and rehashed.
\&\fBlh_\fR\f(BITYPE\fR\fB_doall\fR() and \fBlh_\fR\f(BITYPE\fR\fB_doall_arg\fR() are unsafe and will exhibit
undefined behaviour under these conditions, as these functions assume the hash
table size and bucket pointers do not change during the call.
.PP
If it is desired to use \fBlh_\fR\f(BITYPE\fR\fB_doall\fR() or \fBlh_\fR\f(BITYPE\fR\fB_doall_arg\fR() with
\&\fBlh_\fR\f(BITYPE\fR\fB_delete\fR(), it is essential that you call
\&\fBlh_\fR\f(BITYPE\fR\fB_set_down_load\fR() with a \fIdown_load\fR argument of 0 first. This
disables hash table contraction and guarantees that it will be safe to delete
items from a hash table during a call to \fBlh_\fR\f(BITYPE\fR\fB_doall\fR() or
\&\fBlh_\fR\f(BITYPE\fR\fB_doall_arg\fR().
.PP
It is never safe to call \fBlh_\fR\f(BITYPE\fR\fB_insert\fR() during a call to
\&\fBlh_\fR\f(BITYPE\fR\fB_doall\fR() or \fBlh_\fR\f(BITYPE\fR\fB_doall_arg\fR().
.PP
\&\fBlh_\fR\f(BITYPE\fR\fB_error\fR() can be used to determine if an error occurred in the last
operation.
.PP
\&\fBlh_\fR\f(BITYPE\fR\fB_num_items\fR() returns the number of items in the hash table.
.PP
\&\fBlh_\fR\f(BITYPE\fR\fB_get_down_load\fR() and \fBlh_\fR\f(BITYPE\fR\fB_set_down_load\fR() get and set the
factor used to determine when the hash table is contracted. The factor is the
load factor at or below which hash table contraction will occur, multiplied by
\&\fBLH_LOAD_MULT\fR, where the load factor is the number of items divided by the
number of nodes. Setting this value to 0 disables hash table contraction.
.PP
\&\fBOPENSSL_LH_new()\fR is the same as the \fBlh_\fR\f(BITYPE\fR\fB_new\fR() except that it is not
type specific. So instead of returning an \fBLHASH_OF(\fR\f(BITYPE\fR\fB)\fR value it returns
a \fBvoid *\fR. In the same way the functions \fBOPENSSL_LH_free()\fR,
\&\fBOPENSSL_LH_flush()\fR, \fBOPENSSL_LH_insert()\fR, \fBOPENSSL_LH_delete()\fR,
\&\fBOPENSSL_LH_retrieve()\fR, \fBOPENSSL_LH_doall()\fR, \fBOPENSSL_LH_doall_arg()\fR,
\&\fBOPENSSL_LH_num_items()\fR, \fBOPENSSL_LH_get_down_load()\fR, \fBOPENSSL_LH_set_down_load()\fR
and \fBOPENSSL_LH_error()\fR are equivalent to the similarly named \fBlh_\fR\f(BITYPE\fR
functions except that they return or use a \fBvoid *\fR where the equivalent
\&\fBlh_\fR\f(BITYPE\fR\fB\fR function returns or uses a \fB\fR\f(BITYPE\fR\fB *\fR or \fBLHASH_OF(\fR\f(BITYPE\fR\fB) *\fR.
\&\fBlh_\fR\f(BITYPE\fR\fB\fR functions are implemented as type checked wrappers around the
\&\fBOPENSSL_LH\fR functions. Most applications should not call the \fBOPENSSL_LH\fR
functions directly.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBlh_\fR\f(BITYPE\fR\fB_new\fR() and \fBOPENSSL_LH_new()\fR return NULL on error, otherwise a
pointer to the new \fBLHASH\fR structure.
.PP
When a hash table entry is replaced, \fBlh_\fR\f(BITYPE\fR\fB_insert\fR() or
\&\fBOPENSSL_LH_insert()\fR return the value being replaced. NULL is returned on normal
operation and on error.
.PP
\&\fBlh_\fR\f(BITYPE\fR\fB_delete\fR() and \fBOPENSSL_LH_delete()\fR return the entry being deleted.
NULL is returned if there is no such value in the hash table.
.PP
\&\fBlh_\fR\f(BITYPE\fR\fB_retrieve\fR() and \fBOPENSSL_LH_retrieve()\fR return the hash table entry
if it has been found, NULL otherwise.
.PP
\&\fBlh_\fR\f(BITYPE\fR\fB_error\fR() and \fBOPENSSL_LH_error()\fR return 1 if an error occurred in
the last operation, 0 otherwise. It's meaningful only after non-retrieve
operations.
.PP
\&\fBlh_\fR\f(BITYPE\fR\fB_free\fR(), \fBOPENSSL_LH_free()\fR, \fBlh_\fR\f(BITYPE\fR\fB_flush\fR(),
\&\fBOPENSSL_LH_flush()\fR, \fBlh_\fR\f(BITYPE\fR\fB_doall\fR() \fBOPENSSL_LH_doall()\fR,
\&\fBlh_\fR\f(BITYPE\fR\fB_doall_arg\fR() and \fBOPENSSL_LH_doall_arg()\fR return no values.
.SH NOTE
.IX Header "NOTE"
The LHASH code is not thread safe. All updating operations, as well as
\&\fBlh_\fR\f(BITYPE\fR\fB_error\fR() or \fBOPENSSL_LH_error()\fR calls must be performed under
a write lock. All retrieve operations should be performed under a read lock,
\&\fIunless\fR accurate usage statistics are desired. In which case, a write lock
should be used for retrieve operations as well. For output of the usage
statistics, using the functions from \fBOPENSSL_LH_stats\fR\|(3), a read lock
suffices.
.PP
The LHASH code regards table entries as constant data.  As such, it
internally represents \fBlh_insert()\fR'd items with a "const void *"
pointer type.  This is why callbacks such as those used by \fBlh_doall()\fR
and \fBlh_doall_arg()\fR declare their prototypes with "const", even for the
parameters that pass back the table items' data pointers \- for
consistency, user-provided data is "const" at all times as far as the
LHASH code is concerned.  However, as callers are themselves providing
these pointers, they can choose whether they too should be treating
all such parameters as constant.
.PP
As an example, a hash table may be maintained by code that, for
reasons of encapsulation, has only "const" access to the data being
indexed in the hash table (i.e. it is returned as "const" from
elsewhere in their code) \- in this case the LHASH prototypes are
appropriate as-is.  Conversely, if the caller is responsible for the
life-time of the data in question, then they may well wish to make
modifications to table item passed back in the \fBlh_doall()\fR or
\&\fBlh_doall_arg()\fR callbacks (see the "TYPE_cleanup" example above).  If
so, the caller can either cast the "const" away (if they're providing
the raw callbacks themselves) or use the macros to declare/implement
the wrapper functions without "const" types.
.PP
Callers that only have "const" access to data they're indexing in a
table, yet declare callbacks without constant types (or cast the
"const" away themselves), are therefore creating their own risks/bugs
without being encouraged to do so by the API.  On a related note,
those auditing code should pay special attention to any instances of
DECLARE/IMPLEMENT_LHASH_DOALL_[ARG_]_FN macros that provide types
without any "const" qualifiers.
.SH BUGS
.IX Header "BUGS"
\&\fBlh_\fR\f(BITYPE\fR\fB_insert\fR() and \fBOPENSSL_LH_insert()\fR return NULL both for success
and error.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBOPENSSL_LH_stats\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
In OpenSSL 1.0.0, the lhash interface was revamped for better
type checking.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2022 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
