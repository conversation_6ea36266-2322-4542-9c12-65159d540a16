.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "CMS_DECRYPT 3ossl"
.TH CMS_DECRYPT 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
CMS_decrypt, CMS_decrypt_set1_pkey_and_peer,
CMS_decrypt_set1_pkey, CMS_decrypt_set1_password
\&\- decrypt content from a CMS envelopedData structure
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/cms.h>
\&
\& int CMS_decrypt(CMS_ContentInfo *cms, EVP_PKEY *pkey, X509 *cert,
\&                 BIO *dcont, BIO *out, unsigned int flags);
\& int CMS_decrypt_set1_pkey_and_peer(CMS_ContentInfo *cms,
\&                 EVP_PKEY *pk, X509 *cert, X509 *peer);
\& int CMS_decrypt_set1_pkey(CMS_ContentInfo *cms, EVP_PKEY *pk, X509 *cert);
\& int CMS_decrypt_set1_password(CMS_ContentInfo *cms,
\&                               unsigned char *pass, ossl_ssize_t passlen);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBCMS_decrypt()\fR extracts the decrypted content from a CMS EnvelopedData
or AuthEnvelopedData structure.
It uses \fBCMS_decrypt_set1_pkey()\fR to decrypt the content
with the recipient private key \fIpkey\fR if \fIpkey\fR is not NULL.
In this case, it is recommended to provide the associated certificate
in \fIcert\fR \- see the NOTES below.
\&\fIout\fR is a BIO to write the content to and
\&\fIflags\fR is an optional set of flags.
If \fIpkey\fR is NULL the function assumes that decryption was already done
(e.g., using \fBCMS_decrypt_set1_pkey()\fR or \fBCMS_decrypt_set1_password()\fR) and just
provides the content unless \fIcert\fR, \fIdcont\fR, and \fIout\fR are NULL as well.
The \fIdcont\fR parameter is used in the rare case where the encrypted content
is detached. It will normally be set to NULL.
.PP
\&\fBCMS_decrypt_set1_pkey_and_peer()\fR decrypts the CMS_ContentInfo structure \fIcms\fR
using the private key \fIpkey\fR, the corresponding certificate \fIcert\fR, which is
recommended to be supplied but may be NULL,
and the (optional) originator certificate \fIpeer\fR.
On success, it also records in \fIcms\fR the decryption key \fIpkey\fR, and this
should be followed by \f(CW\*(C`CMS_decrypt(cms, NULL, NULL, dcont, out, flags)\*(C'\fR.
This call deallocates any decryption key stored in \fIcms\fR.
.PP
\&\fBCMS_decrypt_set1_pkey()\fR is the same as
\&\fBCMS_decrypt_set1_pkey_and_peer()\fR with \fIpeer\fR being NULL.
.PP
\&\fBCMS_decrypt_set1_password()\fR decrypts the CMS_ContentInfo structure \fIcms\fR
using the secret \fIpass\fR of length \fIpasslen\fR.
On success, it also records in \fIcms\fR the decryption key used, and this
should be followed by \f(CW\*(C`CMS_decrypt(cms, NULL, NULL, dcont, out, flags)\*(C'\fR.
This call deallocates any decryption key stored in \fIcms\fR.
.SH NOTES
.IX Header "NOTES"
Although the recipients certificate is not needed to decrypt the data it is
needed to locate the appropriate (of possible several) recipients in the CMS
structure.
.PP
If \fIcert\fR is set to NULL all possible recipients are tried. This case however
is problematic. To thwart the MMA attack (Bleichenbacher's attack on
PKCS #1 v1.5 RSA padding) all recipients are tried whether they succeed or
not. If no recipient succeeds then a random symmetric key is used to decrypt
the content: this will typically output garbage and may (but is not guaranteed
to) ultimately return a padding error only. If \fBCMS_decrypt()\fR just returned an
error when all recipient encrypted keys failed to decrypt an attacker could
use this in a timing attack. If the special flag \fBCMS_DEBUG_DECRYPT\fR is set
then the above behaviour is modified and an error \fBis\fR returned if no
recipient encrypted key can be decrypted \fBwithout\fR generating a random
content encryption key. Applications should use this flag with
\&\fBextreme caution\fR especially in automated gateways as it can leave them
open to attack.
.PP
It is possible to determine the correct recipient key by other means (for
example looking them up in a database) and setting them in the CMS structure
in advance using the CMS utility functions such as \fBCMS_set1_pkey()\fR,
or use \fBCMS_decrypt_set1_password()\fR if the recipient has a symmetric key.
In these cases both \fIcert\fR and \fIpkey\fR should be set to NULL.
.PP
To process KEKRecipientInfo types \fBCMS_set1_key()\fR or \fBCMS_RecipientInfo_set0_key()\fR
and \fBCMS_RecipientInfo_decrypt()\fR should be called before \fBCMS_decrypt()\fR and
\&\fIcert\fR and \fIpkey\fR set to NULL.
.PP
The following flags can be passed in the \fIflags\fR parameter.
.PP
If the \fBCMS_TEXT\fR flag is set MIME headers for type \f(CW\*(C`text/plain\*(C'\fR are deleted
from the content. If the content is not of type \f(CW\*(C`text/plain\*(C'\fR then an error is
returned.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBCMS_decrypt()\fR, \fBCMS_decrypt_set1_pkey_and_peer()\fR,
\&\fBCMS_decrypt_set1_pkey()\fR, and \fBCMS_decrypt_set1_password()\fR
return either 1 for success or 0 for failure.
The error can be obtained from \fBERR_get_error\fR\|(3).
.SH BUGS
.IX Header "BUGS"
The \fBset1_\fR part of these function names is misleading
and should better read: \fBwith_\fR.
.PP
The lack of single pass processing and the need to hold all data in memory as
mentioned in \fBCMS_verify()\fR also applies to \fBCMS_decrypt()\fR.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBERR_get_error\fR\|(3), \fBCMS_encrypt\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
\&\fBCMS_decrypt_set1_pkey_and_peer()\fR and \fBCMS_decrypt_set1_password()\fR
were added in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2008\-2023 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
