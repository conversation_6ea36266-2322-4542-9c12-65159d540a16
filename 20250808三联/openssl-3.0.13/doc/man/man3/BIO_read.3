.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "BIO_READ 3ossl"
.TH BIO_READ 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
BIO_read_ex, BIO_write_ex, BIO_read, BIO_write,
BIO_gets, BIO_get_line, BIO_puts
\&\- BIO I/O functions
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/bio.h>
\&
\& int BIO_read_ex(BIO *b, void *data, size_t dlen, size_t *readbytes);
\& int BIO_write_ex(BIO *b, const void *data, size_t dlen, size_t *written);
\&
\& int BIO_read(BIO *b, void *data, int dlen);
\& int BIO_gets(BIO *b, char *buf, int size);
\& int BIO_get_line(BIO *b, char *buf, int size);
\& int BIO_write(BIO *b, const void *data, int dlen);
\& int BIO_puts(BIO *b, const char *buf);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBBIO_read_ex()\fR attempts to read \fIdlen\fR bytes from BIO \fIb\fR and places the data
in \fIdata\fR. If any bytes were successfully read then the number of bytes read is
stored in \fI*readbytes\fR.
.PP
\&\fBBIO_write_ex()\fR attempts to write \fIdlen\fR bytes from \fIdata\fR to BIO \fIb\fR.
If successful then the number of bytes written is stored in \fI*written\fR
unless \fIwritten\fR is NULL.
.PP
\&\fBBIO_read()\fR attempts to read \fIlen\fR bytes from BIO \fIb\fR and places
the data in \fIbuf\fR.
.PP
\&\fBBIO_gets()\fR performs the BIOs "gets" operation and places the data
in \fIbuf\fR. Usually this operation will attempt to read a line of data
from the BIO of maximum length \fIsize\-1\fR. There are exceptions to this,
however; for example, \fBBIO_gets()\fR on a digest BIO will calculate and
return the digest and other BIOs may not support \fBBIO_gets()\fR at all.
The returned string is always NUL-terminated and the '\en' is preserved
if present in the input data.
On binary input there may be NUL characters within the string;
in this case the return value (if nonnegative) may give an incorrect length.
.PP
\&\fBBIO_get_line()\fR attempts to read from BIO \fIb\fR a line of data up to the next '\en'
or the maximum length \fIsize\-1\fR is reached and places the data in \fIbuf\fR.
The returned string is always NUL-terminated and the '\en' is preserved
if present in the input data.
On binary input there may be NUL characters within the string;
in this case the return value (if nonnegative) gives the actual length read.
For implementing this, unfortunately the data needs to be read byte-by-byte.
.PP
\&\fBBIO_write()\fR attempts to write \fIlen\fR bytes from \fIbuf\fR to BIO \fIb\fR.
.PP
\&\fBBIO_puts()\fR attempts to write a NUL-terminated string \fIbuf\fR to BIO \fIb\fR.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBBIO_read_ex()\fR returns 1 if data was successfully read, and 0 otherwise.
.PP
\&\fBBIO_write_ex()\fR returns 1 if no error was encountered writing data, 0 otherwise.
Requesting to write 0 bytes is not considered an error.
.PP
\&\fBBIO_write()\fR returns \-2 if the "write" operation is not implemented by the BIO
or \-1 on other errors.
Otherwise it returns the number of bytes written.
This may be 0 if the BIO \fIb\fR is NULL or \fIdlen <= 0\fR.
.PP
\&\fBBIO_gets()\fR returns \-2 if the "gets" operation is not implemented by the BIO
or \-1 on other errors.
Otherwise it typically returns the amount of data read,
but depending on the implementation it may return only the length up to
the first NUL character contained in the data read.
In any case the trailing NUL that is added after the data read
is not included in the length returned.
.PP
All other functions return either the amount of data successfully read or
written (if the return value is positive) or that no data was successfully
read or written if the result is 0 or \-1. If the return value is \-2 then
the operation is not implemented in the specific BIO type.
.SH NOTES
.IX Header "NOTES"
A 0 or \-1 return is not necessarily an indication of an error. In
particular when the source/sink is nonblocking or of a certain type
it may merely be an indication that no data is currently available and that
the application should retry the operation later.
.PP
One technique sometimes used with blocking sockets is to use a system call
(such as \fBselect()\fR, \fBpoll()\fR or equivalent) to determine when data is available
and then call \fBread()\fR to read the data. The equivalent with BIOs (that is call
\&\fBselect()\fR on the underlying I/O structure and then call \fBBIO_read()\fR to
read the data) should \fBnot\fR be used because a single call to \fBBIO_read()\fR
can cause several reads (and writes in the case of SSL BIOs) on the underlying
I/O structure and may block as a result. Instead \fBselect()\fR (or equivalent)
should be combined with non blocking I/O so successive reads will request
a retry instead of blocking.
.PP
See \fBBIO_should_retry\fR\|(3) for details of how to
determine the cause of a retry and other I/O issues.
.PP
If the "gets" method is not supported by a BIO then \fBBIO_get_line()\fR can be used.
It is also possible to make \fBBIO_gets()\fR usable even if the "gets" method is not
supported by adding a buffering BIO \fBBIO_f_buffer\fR\|(3) to the chain.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBBIO_should_retry\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
\&\fBBIO_gets()\fR on 1.1.0 and older when called on \fBBIO_fd()\fR based BIO did not
keep the '\en' at the end of the line in the buffer.
.PP
\&\fBBIO_get_line()\fR was added in OpenSSL 3.0.
.PP
\&\fBBIO_write_ex()\fR returns 1 if the size of the data to write is 0 and the
\&\fIwritten\fR parameter of the function can be NULL since OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2023 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
