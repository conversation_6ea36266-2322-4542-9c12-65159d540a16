.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "ERR_SET_MARK 3ossl"
.TH ERR_SET_MARK 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
ERR_set_mark, ERR_clear_last_mark, ERR_pop_to_mark
\&\- set mark, clear mark and pop errors until mark
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/err.h>
\&
\& int ERR_set_mark(void);
\& int ERR_pop_to_mark(void);
\& int ERR_clear_last_mark(void);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBERR_set_mark()\fR sets a mark on the current topmost error record if there
is one.
.PP
\&\fBERR_pop_to_mark()\fR will pop the top of the error stack until a mark is found.
The mark is then removed.  If there is no mark, the whole stack is removed.
.PP
\&\fBERR_clear_last_mark()\fR removes the last mark added if there is one.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBERR_set_mark()\fR returns 0 if the error stack is empty, otherwise 1.
.PP
\&\fBERR_clear_last_mark()\fR and \fBERR_pop_to_mark()\fR return 0 if there was no mark in the
error stack, which implies that the stack became empty, otherwise 1.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2003\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
