.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "BN_NUM_BYTES 3ossl"
.TH BN_NUM_BYTES 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
BN_num_bits, BN_num_bytes, BN_num_bits_word \- get BIGNUM size
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/bn.h>
\&
\& int BN_num_bytes(const BIGNUM *a);
\&
\& int BN_num_bits(const BIGNUM *a);
\&
\& int BN_num_bits_word(BN_ULONG w);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBBN_num_bytes()\fR returns the size of a \fBBIGNUM\fR in bytes.
.PP
\&\fBBN_num_bits_word()\fR returns the number of significant bits in a word.
If we take 0x00000432 as an example, it returns 11, not 16, not 32.
Basically, except for a zero, it returns \fIfloor(log2(w))+1\fR.
.PP
\&\fBBN_num_bits()\fR returns the number of significant bits in a \fBBIGNUM\fR,
following the same principle as \fBBN_num_bits_word()\fR.
.PP
\&\fBBN_num_bytes()\fR is a macro.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
The size.
.SH NOTES
.IX Header "NOTES"
Some have tried using \fBBN_num_bits()\fR on individual numbers in RSA keys,
DH keys and DSA keys, and found that they don't always come up with
the number of bits they expected (something like 512, 1024, 2048,
\&...).  This is because generating a number with some specific number
of bits doesn't always set the highest bits, thereby making the number
of \fIsignificant\fR bits a little lower.  If you want to know the "key
size" of such a key, either use functions like \fBRSA_size()\fR, \fBDH_size()\fR
and \fBDSA_size()\fR, or use \fBBN_num_bytes()\fR and multiply with 8 (although
there's no real guarantee that will match the "key size", just a lot
more probability).
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBDH_size\fR\|(3), \fBDSA_size\fR\|(3),
\&\fBRSA_size\fR\|(3)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2017 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
