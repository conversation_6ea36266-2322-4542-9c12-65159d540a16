.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "X509_SIGN 3ossl"
.TH X509_SIGN 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
X509_sign, X509_sign_ctx,
X509_REQ_sign, X509_REQ_sign_ctx,
X509_CRL_sign, X509_CRL_sign_ctx \-
sign certificate, certificate request, or CRL signature
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/x509.h>
\&
\& int X509_sign(X509 *x, EVP_PKEY *pkey, const EVP_MD *md);
\& int X509_sign_ctx(X509 *x, EVP_MD_CTX *ctx);
\&
\& int X509_REQ_sign(X509_REQ *x, EVP_PKEY *pkey, const EVP_MD *md);
\& int X509_REQ_sign_ctx(X509_REQ *x, EVP_MD_CTX *ctx);
\&
\& int X509_CRL_sign(X509_CRL *x, EVP_PKEY *pkey, const EVP_MD *md);
\& int X509_CRL_sign_ctx(X509_CRL *x, EVP_MD_CTX *ctx);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBX509_sign()\fR signs certificate \fIx\fR using private key \fIpkey\fR and message
digest \fImd\fR and sets the signature in \fIx\fR. \fBX509_sign_ctx()\fR also signs
certificate \fIx\fR but uses the parameters contained in digest context \fIctx\fR.
.PP
\&\fBX509_REQ_sign()\fR, \fBX509_REQ_sign_ctx()\fR,
\&\fBX509_CRL_sign()\fR, and \fBX509_CRL_sign_ctx()\fR
sign certificate requests and CRLs, respectively.
.SH NOTES
.IX Header "NOTES"
\&\fBX509_sign_ctx()\fR is used where the default parameters for the corresponding
public key and digest are not suitable. It can be used to sign keys using
RSA-PSS for example.
.PP
For efficiency reasons and to work around ASN.1 encoding issues the encoding
of the signed portion of a certificate, certificate request and CRL is cached
internally. If the signed portion of the structure is modified the encoding
is not always updated meaning a stale version is sometimes used. This is not
normally a problem because modifying the signed portion will invalidate the
signature and signing will always update the encoding.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
All functions return the size of the signature
in bytes for success and zero for failure.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBERR_get_error\fR\|(3),
\&\fBX509_NAME_add_entry_by_txt\fR\|(3),
\&\fBX509_new\fR\|(3),
\&\fBX509_verify_cert\fR\|(3),
\&\fBX509_verify\fR\|(3),
\&\fBX509_REQ_verify_ex\fR\|(3), \fBX509_REQ_verify\fR\|(3),
\&\fBX509_CRL_verify\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
The \fBX509_sign()\fR, \fBX509_REQ_sign()\fR and \fBX509_CRL_sign()\fR functions are
available in all versions of OpenSSL.
.PP
The \fBX509_sign_ctx()\fR, \fBX509_REQ_sign_ctx()\fR
and \fBX509_CRL_sign_ctx()\fR functions were added in OpenSSL 1.0.1.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2015\-2020 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
