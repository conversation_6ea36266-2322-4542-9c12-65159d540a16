.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "TS_RESP_CTX_NEW 3ossl"
.TH TS_RESP_CTX_NEW 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
TS_RESP_CTX_new_ex, TS_RESP_CTX_new,
TS_RESP_CTX_free \- Timestamp response context object creation
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/ts.h>
\&
\& TS_RESP_CTX *TS_RESP_CTX_new_ex(OSSL_LIB_CTX *libctx, const char *propq);
\& TS_RESP_CTX *TS_RESP_CTX_new(void);
\& void TS_RESP_CTX_free(TS_RESP_CTX *ctx);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
Creates a response context that can be used for generating responses.
.PP
\&\fBTS_RESP_CTX_new_ex()\fR allocates and initializes a TS_RESP_CTX structure with a
library context of \fIlibctx\fR and a property query of \fIpropq\fR.
The library context and property query can be used to select which providers
supply the fetched algorithms.
.PP
\&\fBTS_RESP_CTX_new()\fR is similar to \fBTS_RESP_CTX_new_ex()\fR but sets the library context
and property query to NULL. This results in the default (NULL) library context
being used for any operations requiring algorithm fetches.
.PP
\&\fBTS_RESP_CTX_free()\fR frees the \fBTS_RESP_CTX\fR object \fIctx\fR.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
If the allocation fails, \fBTS_RESP_CTX_new_ex()\fR and \fBTS_RESP_CTX_new()\fR return NULL,
otherwise it returns a pointer to the newly allocated structure.
.SH HISTORY
.IX Header "HISTORY"
The function \fBTS_RESP_CTX_new_ex()\fR was added in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
