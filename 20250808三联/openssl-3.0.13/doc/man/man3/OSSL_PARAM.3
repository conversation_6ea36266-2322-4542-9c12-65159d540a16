.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "OSSL_PARAM 3ossl"
.TH OSSL_PARAM 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
OSSL_PARAM \- a structure to pass or request object parameters
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/core.h>
\&
\& typedef struct ossl_param_st OSSL_PARAM;
\& struct ossl_param_st {
\&     const char *key;             /* the name of the parameter */
\&     unsigned char data_type;     /* declare what kind of content is in data */
\&     void *data;                  /* value being passed in or out */
\&     size_t data_size;            /* data size */
\&     size_t return_size;          /* returned size */
\& };
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBOSSL_PARAM\fR is a type that allows passing arbitrary data for some
object between two parties that have no or very little shared
knowledge about their respective internal structures for that object.
.PP
A typical usage example could be an application that wants to set some
parameters for an object, or wants to find out some parameters of an
object.
.PP
Arrays of this type can be used for the following purposes:
.IP \(bu 4
Setting parameters for some object
.Sp
The caller sets up the \fBOSSL_PARAM\fR array and calls some function
(the \fIsetter\fR) that has intimate knowledge about the object that can
take the data from the \fBOSSL_PARAM\fR array and assign them in a
suitable form for the internal structure of the object.
.IP \(bu 4
Request parameters of some object
.Sp
The caller (the \fIrequester\fR) sets up the \fBOSSL_PARAM\fR array and
calls some function (the \fIresponder\fR) that has intimate knowledge
about the object, which can take the internal data of the object and
copy (possibly convert) that to the memory prepared by the
\&\fIrequester\fR and pointed at with the \fBOSSL_PARAM\fR \fIdata\fR.
.IP \(bu 4
Request parameter descriptors
.Sp
The caller gets an array of constant \fBOSSL_PARAM\fR, which describe
available parameters and some of their properties; name, data type and
expected data size.
For a detailed description of each field for this use, see the field
descriptions below.
.Sp
The caller may then use the information from this descriptor array to
build up its own \fBOSSL_PARAM\fR array to pass down to a \fIsetter\fR or
\&\fIresponder\fR.
.PP
Normally, the order of the an \fBOSSL_PARAM\fR array is not relevant.
However, if the \fIresponder\fR can handle multiple elements with the
same key, those elements must be handled in the order they are in.
.PP
An \fBOSSL_PARAM\fR array must have a terminating element, where \fIkey\fR
is NULL.  The usual full terminating template is:
.PP
.Vb 1
\&    { NULL, 0, NULL, 0, 0 }
.Ve
.PP
This can also be specified using \fBOSSL_PARAM_END\fR\|(3).
.SS "Functional support"
.IX Subsection "Functional support"
Libcrypto offers a limited set of helper functions to handle
\&\fBOSSL_PARAM\fR items and arrays, please see \fBOSSL_PARAM_get_int\fR\|(3).
Developers are free to extend or replace those as they see fit.
.SS "\fBOSSL_PARAM\fP fields"
.IX Subsection "OSSL_PARAM fields"
.IP \fIkey\fR 4
.IX Item "key"
The identity of the parameter in the form of a string.
.Sp
In an \fBOSSL_PARAM\fR array, an item with this field set to NULL is
considered a terminating item.
.IP \fIdata_type\fR 4
.IX Item "data_type"
The \fIdata_type\fR is a value that describes the type and organization of
the data.
See "Supported types" below for a description of the types.
.IP \fIdata\fR 4
.IX Item "data"
.PD 0
.IP \fIdata_size\fR 4
.IX Item "data_size"
.PD
\&\fIdata\fR is a pointer to the memory where the parameter data is (when
setting parameters) or shall (when requesting parameters) be stored,
and \fIdata_size\fR is its size in bytes.
The organization of the data depends on the parameter type and flag.
.Sp
The \fIdata_size\fR needs special attention with the parameter type
\&\fBOSSL_PARAM_UTF8_STRING\fR in relation to C strings.  When setting
parameters, the size should be set to the length of the string, not
counting the terminating NUL byte.  When requesting parameters, the
size should be set to the size of the buffer to be populated, which
should accommodate enough space for a terminating NUL byte.
.Sp
When \fIrequesting parameters\fR, it's acceptable for \fIdata\fR to be NULL.
This can be used by the \fIrequester\fR to figure out dynamically exactly
how much buffer space is needed to store the parameter data.
In this case, \fIdata_size\fR is ignored.
.Sp
When the \fBOSSL_PARAM\fR is used as a parameter descriptor, \fIdata\fR
should be ignored.
If \fIdata_size\fR is zero, it means that an arbitrary data size is
accepted, otherwise it specifies the maximum size allowed.
.IP \fIreturn_size\fR 4
.IX Item "return_size"
When an array of \fBOSSL_PARAM\fR is used to request data, the
\&\fIresponder\fR must set this field to indicate size of the parameter
data, including padding as the case may be.
In case the \fIdata_size\fR is an unsuitable size for the data, the
\&\fIresponder\fR must still set this field to indicate the minimum data
size required.
(further notes on this in "NOTES" below).
.Sp
When the \fBOSSL_PARAM\fR is used as a parameter descriptor,
\&\fIreturn_size\fR should be ignored.
.PP
\&\fBNOTE:\fR
.PP
The key names and associated types are defined by the entity that
offers these parameters, i.e. names for parameters provided by the
OpenSSL libraries are defined by the libraries, and names for
parameters provided by providers are defined by those providers,
except for the pointer form of strings (see data type descriptions
below).
Entities that want to set or request parameters need to know what
those keys are and of what type, any functionality between those two
entities should remain oblivious and just pass the \fBOSSL_PARAM\fR array
along.
.SS "Supported types"
.IX Subsection "Supported types"
The \fIdata_type\fR field can be one of the following types:
.IP \fBOSSL_PARAM_INTEGER\fR 4
.IX Item "OSSL_PARAM_INTEGER"
.PD 0
.IP \fBOSSL_PARAM_UNSIGNED_INTEGER\fR 4
.IX Item "OSSL_PARAM_UNSIGNED_INTEGER"
.PD
The parameter data is an integer (signed or unsigned) of arbitrary
length, organized in native form, i.e. most significant byte first on
Big-Endian systems, and least significant byte first on Little-Endian
systems.
.IP \fBOSSL_PARAM_REAL\fR 4
.IX Item "OSSL_PARAM_REAL"
The parameter data is a floating point value in native form.
.IP \fBOSSL_PARAM_UTF8_STRING\fR 4
.IX Item "OSSL_PARAM_UTF8_STRING"
The parameter data is a printable string.
.IP \fBOSSL_PARAM_OCTET_STRING\fR 4
.IX Item "OSSL_PARAM_OCTET_STRING"
The parameter data is an arbitrary string of bytes.
.IP \fBOSSL_PARAM_UTF8_PTR\fR 4
.IX Item "OSSL_PARAM_UTF8_PTR"
The parameter data is a pointer to a printable string.
.Sp
The difference between this and \fBOSSL_PARAM_UTF8_STRING\fR is that \fIdata\fR
doesn't point directly at the data, but to a pointer that points to the data.
.Sp
If there is any uncertainty about which to use, \fBOSSL_PARAM_UTF8_STRING\fR is
almost certainly the correct choice.
.Sp
This is used to indicate that constant data is or will be passed,
and there is therefore no need to copy the data that is passed, just
the pointer to it.
.Sp
\&\fIdata_size\fR must be set to the size of the data, not the size of the
pointer to the data.
If this is used in a parameter request,
\&\fIdata_size\fR is not relevant.  However, the \fIresponder\fR will set
\&\fIreturn_size\fR to the size of the data.
.Sp
Note that the use of this type is \fBfragile\fR and can only be safely
used for data that remains constant and in a constant location for a
long enough duration (such as the life-time of the entity that
offers these parameters).
.IP \fBOSSL_PARAM_OCTET_PTR\fR 4
.IX Item "OSSL_PARAM_OCTET_PTR"
The parameter data is a pointer to an arbitrary string of bytes.
.Sp
The difference between this and \fBOSSL_PARAM_OCTET_STRING\fR is that
\&\fIdata\fR doesn't point directly at the data, but to a pointer that
points to the data.
.Sp
If there is any uncertainty about which to use, \fBOSSL_PARAM_OCTET_STRING\fR is
almost certainly the correct choice.
.Sp
This is used to indicate that constant data is or will be passed, and
there is therefore no need to copy the data that is passed, just the
pointer to it.
.Sp
\&\fIdata_size\fR must be set to the size of the data, not the size of the
pointer to the data.
If this is used in a parameter request,
\&\fIdata_size\fR is not relevant.  However, the \fIresponder\fR will set
\&\fIreturn_size\fR to the size of the data.
.Sp
Note that the use of this type is \fBfragile\fR and can only be safely
used for data that remains constant and in a constant location for a
long enough duration (such as the life-time of the entity that
offers these parameters).
.SH NOTES
.IX Header "NOTES"
Both when setting and requesting parameters, the functions that are
called will have to decide what is and what is not an error.
The recommended behaviour is:
.IP \(bu 4
Keys that a \fIsetter\fR or \fIresponder\fR doesn't recognise should simply
be ignored.
That in itself isn't an error.
.IP \(bu 4
If the keys that a called \fIsetter\fR recognises form a consistent
enough set of data, that call should succeed.
.IP \(bu 4
Apart from the \fIreturn_size\fR, a \fIresponder\fR must never change the fields
of an \fBOSSL_PARAM\fR.
To return a value, it should change the contents of the memory that
\&\fIdata\fR points at.
.IP \(bu 4
If the data type for a key that it's associated with is incorrect,
the called function may return an error.
.Sp
The called function may also try to convert the data to a suitable
form (for example, it's plausible to pass a large number as an octet
string, so even though a given key is defined as an
\&\fBOSSL_PARAM_UNSIGNED_INTEGER\fR, is plausible to pass the value as an
\&\fBOSSL_PARAM_OCTET_STRING\fR), but this is in no way mandatory.
.IP \(bu 4
If \fIdata\fR for a \fBOSSL_PARAM_OCTET_STRING\fR or a
\&\fBOSSL_PARAM_UTF8_STRING\fR is NULL, the \fIresponder\fR should
set \fIreturn_size\fR to the size of the item to be returned
and return success. Later the responder will be called again
with \fIdata\fR pointing at the place for the value to be put.
.IP \(bu 4
If a \fIresponder\fR finds that some data sizes are too small for the
requested data, it must set \fIreturn_size\fR for each such
\&\fBOSSL_PARAM\fR item to the minimum required size, and eventually return
an error.
.IP \(bu 4
For the integer type parameters (\fBOSSL_PARAM_UNSIGNED_INTEGER\fR and
\&\fBOSSL_PARAM_INTEGER\fR), a \fIresponder\fR may choose to return an error
if the \fIdata_size\fR isn't a suitable size (even if \fIdata_size\fR is
bigger than needed).  If the \fIresponder\fR finds the size suitable, it
must fill all \fIdata_size\fR bytes and ensure correct padding for the
native endianness, and set \fIreturn_size\fR to the same value as
\&\fIdata_size\fR.
.SH EXAMPLES
.IX Header "EXAMPLES"
A couple of examples to just show how \fBOSSL_PARAM\fR arrays could be
set up.
.PP
\fIExample 1\fR
.IX Subsection "Example 1"
.PP
This example is for setting parameters on some object:
.PP
.Vb 1
\&    #include <openssl/core.h>
\&
\&    const char *foo = "some string";
\&    size_t foo_l = strlen(foo);
\&    const char bar[] = "some other string";
\&    OSSL_PARAM set[] = {
\&        { "foo", OSSL_PARAM_UTF8_PTR, &foo, foo_l, 0 },
\&        { "bar", OSSL_PARAM_UTF8_STRING, (void *)&bar, sizeof(bar) \- 1, 0 },
\&        { NULL, 0, NULL, 0, 0 }
\&    };
.Ve
.PP
\fIExample 2\fR
.IX Subsection "Example 2"
.PP
This example is for requesting parameters on some object:
.PP
.Vb 9
\&    const char *foo = NULL;
\&    size_t foo_l;
\&    char bar[1024];
\&    size_t bar_l;
\&    OSSL_PARAM request[] = {
\&        { "foo", OSSL_PARAM_UTF8_PTR, &foo, 0 /*irrelevant*/, 0 },
\&        { "bar", OSSL_PARAM_UTF8_STRING, &bar, sizeof(bar), 0 },
\&        { NULL, 0, NULL, 0, 0 }
\&    };
.Ve
.PP
A \fIresponder\fR that receives this array (as \fIparams\fR in this example)
could fill in the parameters like this:
.PP
.Vb 1
\&    /* OSSL_PARAM *params */
\&
\&    int i;
\&
\&    for (i = 0; params[i].key != NULL; i++) {
\&        if (strcmp(params[i].key, "foo") == 0) {
\&            *(char **)params[i].data = "foo value";
\&            params[i].return_size = 9; /* length of "foo value" string */
\&        } else if (strcmp(params[i].key, "bar") == 0) {
\&            memcpy(params[i].data, "bar value", 10);
\&            params[i].return_size = 9; /* length of "bar value" string */
\&        }
\&        /* Ignore stuff we don\*(Aqt know */
\&    }
.Ve
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBopenssl\-core.h\fR\|(7), \fBOSSL_PARAM_get_int\fR\|(3), \fBOSSL_PARAM_dup\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
\&\fBOSSL_PARAM\fR was added in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2019\-2023 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
