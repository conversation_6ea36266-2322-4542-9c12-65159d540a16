.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "EVP_CIPHER_METH_NEW 3ossl"
.TH EVP_CIPHER_METH_NEW 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
EVP_CIPHER_meth_new, EVP_CIPHER_meth_dup, EVP_CIPHER_meth_free,
EVP_CIPHER_meth_set_iv_length, EVP_CIPHER_meth_set_flags,
EVP_CIPHER_meth_set_impl_ctx_size, EVP_CIPHER_meth_set_init,
EVP_CIPHER_meth_set_do_cipher, EVP_CIPHER_meth_set_cleanup,
EVP_CIPHER_meth_set_set_asn1_params, EVP_CIPHER_meth_set_get_asn1_params,
EVP_CIPHER_meth_set_ctrl, EVP_CIPHER_meth_get_init,
EVP_CIPHER_meth_get_do_cipher, EVP_CIPHER_meth_get_cleanup,
EVP_CIPHER_meth_get_set_asn1_params, EVP_CIPHER_meth_get_get_asn1_params,
EVP_CIPHER_meth_get_ctrl
\&\- Routines to build up EVP_CIPHER methods
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/evp.h>
.Ve
.PP
The following functions have been deprecated since OpenSSL 3.0, and can be
hidden entirely by defining \fBOPENSSL_API_COMPAT\fR with a suitable version value,
see \fBopenssl_user_macros\fR\|(7):
.PP
.Vb 3
\& EVP_CIPHER *EVP_CIPHER_meth_new(int cipher_type, int block_size, int key_len);
\& EVP_CIPHER *EVP_CIPHER_meth_dup(const EVP_CIPHER *cipher);
\& void EVP_CIPHER_meth_free(EVP_CIPHER *cipher);
\&
\& int EVP_CIPHER_meth_set_iv_length(EVP_CIPHER *cipher, int iv_len);
\& int EVP_CIPHER_meth_set_flags(EVP_CIPHER *cipher, unsigned long flags);
\& int EVP_CIPHER_meth_set_impl_ctx_size(EVP_CIPHER *cipher, int ctx_size);
\& int EVP_CIPHER_meth_set_init(EVP_CIPHER *cipher,
\&                              int (*init)(EVP_CIPHER_CTX *ctx,
\&                                          const unsigned char *key,
\&                                          const unsigned char *iv,
\&                                          int enc));
\& int EVP_CIPHER_meth_set_do_cipher(EVP_CIPHER *cipher,
\&                                   int (*do_cipher)(EVP_CIPHER_CTX *ctx,
\&                                                    unsigned char *out,
\&                                                    const unsigned char *in,
\&                                                    size_t inl));
\& int EVP_CIPHER_meth_set_cleanup(EVP_CIPHER *cipher,
\&                                 int (*cleanup)(EVP_CIPHER_CTX *));
\& int EVP_CIPHER_meth_set_set_asn1_params(EVP_CIPHER *cipher,
\&                                         int (*set_asn1_parameters)(EVP_CIPHER_CTX *,
\&                                                                    ASN1_TYPE *));
\& int EVP_CIPHER_meth_set_get_asn1_params(EVP_CIPHER *cipher,
\&                                         int (*get_asn1_parameters)(EVP_CIPHER_CTX *,
\&                                                                    ASN1_TYPE *));
\& int EVP_CIPHER_meth_set_ctrl(EVP_CIPHER *cipher,
\&                              int (*ctrl)(EVP_CIPHER_CTX *, int type,
\&                                          int arg, void *ptr));
\&
\& int (*EVP_CIPHER_meth_get_init(const EVP_CIPHER *cipher))(EVP_CIPHER_CTX *ctx,
\&                                                           const unsigned char *key,
\&                                                           const unsigned char *iv,
\&                                                           int enc);
\& int (*EVP_CIPHER_meth_get_do_cipher(const EVP_CIPHER *cipher))(EVP_CIPHER_CTX *ctx,
\&                                                                unsigned char *out,
\&                                                                const unsigned char *in,
\&                                                                size_t inl);
\& int (*EVP_CIPHER_meth_get_cleanup(const EVP_CIPHER *cipher))(EVP_CIPHER_CTX *);
\& int (*EVP_CIPHER_meth_get_set_asn1_params(const EVP_CIPHER *cipher))(EVP_CIPHER_CTX *,
\&                                                                      ASN1_TYPE *);
\& int (*EVP_CIPHER_meth_get_get_asn1_params(const EVP_CIPHER *cipher))(EVP_CIPHER_CTX *,
\&                                                                      ASN1_TYPE *);
\& int (*EVP_CIPHER_meth_get_ctrl(const EVP_CIPHER *cipher))(EVP_CIPHER_CTX *,
\&                                                           int type, int arg,
\&                                                           void *ptr);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
All of the functions described on this page are deprecated.
Applications should instead use the OSSL_PROVIDER APIs.
.PP
The \fBEVP_CIPHER\fR type is a structure for symmetric cipher method
implementation.
.PP
\&\fBEVP_CIPHER_meth_new()\fR creates a new \fBEVP_CIPHER\fR structure.
.PP
\&\fBEVP_CIPHER_meth_dup()\fR creates a copy of \fBcipher\fR.
.PP
\&\fBEVP_CIPHER_meth_free()\fR destroys a \fBEVP_CIPHER\fR structure.
.PP
\&\fBEVP_CIPHER_meth_set_iv_length()\fR sets the length of the IV.
This is only needed when the implemented cipher mode requires it.
.PP
\&\fBEVP_CIPHER_meth_set_flags()\fR sets the flags to describe optional
behaviours in the particular \fBcipher\fR.
With the exception of cipher modes, of which only one may be present,
several flags can be or'd together.
The available flags are:
.IP "EVP_CIPH_STREAM_CIPHER, EVP_CIPH_ECB_MODE EVP_CIPH_CBC_MODE, EVP_CIPH_CFB_MODE, EVP_CIPH_OFB_MODE, EVP_CIPH_CTR_MODE, EVP_CIPH_GCM_MODE, EVP_CIPH_CCM_MODE, EVP_CIPH_XTS_MODE, EVP_CIPH_WRAP_MODE, EVP_CIPH_OCB_MODE, EVP_CIPH_SIV_MODE" 4
.IX Item "EVP_CIPH_STREAM_CIPHER, EVP_CIPH_ECB_MODE EVP_CIPH_CBC_MODE, EVP_CIPH_CFB_MODE, EVP_CIPH_OFB_MODE, EVP_CIPH_CTR_MODE, EVP_CIPH_GCM_MODE, EVP_CIPH_CCM_MODE, EVP_CIPH_XTS_MODE, EVP_CIPH_WRAP_MODE, EVP_CIPH_OCB_MODE, EVP_CIPH_SIV_MODE"
The cipher mode.
.IP EVP_CIPH_VARIABLE_LENGTH 4
.IX Item "EVP_CIPH_VARIABLE_LENGTH"
This cipher is of variable length.
.IP EVP_CIPH_CUSTOM_IV 4
.IX Item "EVP_CIPH_CUSTOM_IV"
Storing and initialising the IV is left entirely to the
implementation.
.IP EVP_CIPH_ALWAYS_CALL_INIT 4
.IX Item "EVP_CIPH_ALWAYS_CALL_INIT"
Set this if the implementation's \fBinit()\fR function should be called even
if \fBkey\fR is \fBNULL\fR.
.IP EVP_CIPH_CTRL_INIT 4
.IX Item "EVP_CIPH_CTRL_INIT"
Set this to have the implementation's \fBctrl()\fR function called with
command code \fBEVP_CTRL_INIT\fR early in its setup.
.IP EVP_CIPH_CUSTOM_KEY_LENGTH 4
.IX Item "EVP_CIPH_CUSTOM_KEY_LENGTH"
Checking and setting the key length after creating the \fBEVP_CIPHER\fR
is left to the implementation.
Whenever someone uses \fBEVP_CIPHER_CTX_set_key_length()\fR on a
\&\fBEVP_CIPHER\fR with this flag set, the implementation's \fBctrl()\fR function
will be called with the control code \fBEVP_CTRL_SET_KEY_LENGTH\fR and
the key length in \fBarg\fR.
.IP EVP_CIPH_NO_PADDING 4
.IX Item "EVP_CIPH_NO_PADDING"
Don't use standard block padding.
.IP EVP_CIPH_RAND_KEY 4
.IX Item "EVP_CIPH_RAND_KEY"
Making a key with random content is left to the implementation.
This is done by calling the implementation's \fBctrl()\fR function with the
control code \fBEVP_CTRL_RAND_KEY\fR and the pointer to the key memory
storage in \fBptr\fR.
.IP EVP_CIPH_CUSTOM_COPY 4
.IX Item "EVP_CIPH_CUSTOM_COPY"
Set this to have the implementation's \fBctrl()\fR function called with
command code \fBEVP_CTRL_COPY\fR at the end of \fBEVP_CIPHER_CTX_copy()\fR.
The intended use is for further things to deal with after the
implementation specific data block has been copied.
The destination \fBEVP_CIPHER_CTX\fR is passed to the control with the
\&\fBptr\fR parameter.
The implementation specific data block is reached with
\&\fBEVP_CIPHER_CTX_get_cipher_data()\fR.
.IP EVP_CIPH_FLAG_DEFAULT_ASN1 4
.IX Item "EVP_CIPH_FLAG_DEFAULT_ASN1"
Use the default EVP routines to pass IV to and from ASN.1.
.IP EVP_CIPH_FLAG_LENGTH_BITS 4
.IX Item "EVP_CIPH_FLAG_LENGTH_BITS"
Signals that the length of the input buffer for encryption /
decryption is to be understood as the number of bits instead of
bytes for this implementation.
This is only useful for CFB1 ciphers.
.IP EVP_CIPH_FLAG_CTS 4
.IX Item "EVP_CIPH_FLAG_CTS"
Indicates that the cipher uses ciphertext stealing. This is currently
used to indicate that the cipher is a one shot that only allows a single call to
\&\fBEVP_CipherUpdate()\fR.
.IP EVP_CIPH_FLAG_CUSTOM_CIPHER 4
.IX Item "EVP_CIPH_FLAG_CUSTOM_CIPHER"
This indicates that the implementation takes care of everything,
including padding, buffering and finalization.
The EVP routines will simply give them control and do nothing more.
.IP EVP_CIPH_FLAG_AEAD_CIPHER 4
.IX Item "EVP_CIPH_FLAG_AEAD_CIPHER"
This indicates that this is an AEAD cipher implementation.
.IP EVP_CIPH_FLAG_TLS1_1_MULTIBLOCK 4
.IX Item "EVP_CIPH_FLAG_TLS1_1_MULTIBLOCK"
Allow interleaving of crypto blocks, a particular optimization only applicable
to certain TLS ciphers.
.PP
\&\fBEVP_CIPHER_meth_set_impl_ctx_size()\fR sets the size of the EVP_CIPHER's
implementation context so that it can be automatically allocated.
.PP
\&\fBEVP_CIPHER_meth_set_init()\fR sets the cipher init function for
\&\fBcipher\fR.
The cipher init function is called by \fBEVP_CipherInit()\fR,
\&\fBEVP_CipherInit_ex()\fR, \fBEVP_EncryptInit()\fR, \fBEVP_EncryptInit_ex()\fR,
\&\fBEVP_DecryptInit()\fR, \fBEVP_DecryptInit_ex()\fR.
.PP
\&\fBEVP_CIPHER_meth_set_do_cipher()\fR sets the cipher function for
\&\fBcipher\fR.
The cipher function is called by \fBEVP_CipherUpdate()\fR,
\&\fBEVP_EncryptUpdate()\fR, \fBEVP_DecryptUpdate()\fR, \fBEVP_CipherFinal()\fR,
\&\fBEVP_EncryptFinal()\fR, \fBEVP_EncryptFinal_ex()\fR, \fBEVP_DecryptFinal()\fR and
\&\fBEVP_DecryptFinal_ex()\fR.
.PP
\&\fBEVP_CIPHER_meth_set_cleanup()\fR sets the function for \fBcipher\fR to do
extra cleanup before the method's private data structure is cleaned
out and freed.
Note that the cleanup function is passed a \fBEVP_CIPHER_CTX *\fR, the
private data structure is then available with
\&\fBEVP_CIPHER_CTX_get_cipher_data()\fR.
This cleanup function is called by \fBEVP_CIPHER_CTX_reset()\fR and
\&\fBEVP_CIPHER_CTX_free()\fR.
.PP
\&\fBEVP_CIPHER_meth_set_set_asn1_params()\fR sets the function for \fBcipher\fR
to set the AlgorithmIdentifier "parameter" based on the passed cipher.
This function is called by \fBEVP_CIPHER_param_to_asn1()\fR.
\&\fBEVP_CIPHER_meth_set_get_asn1_params()\fR sets the function for \fBcipher\fR
that sets the cipher parameters based on an ASN.1 AlgorithmIdentifier
"parameter".
Both these functions are needed when there is a need for custom data
(more or other than the cipher IV).
They are called by \fBEVP_CIPHER_param_to_asn1()\fR and
\&\fBEVP_CIPHER_asn1_to_param()\fR respectively if defined.
.PP
\&\fBEVP_CIPHER_meth_set_ctrl()\fR sets the control function for \fBcipher\fR.
.PP
\&\fBEVP_CIPHER_meth_get_init()\fR, \fBEVP_CIPHER_meth_get_do_cipher()\fR,
\&\fBEVP_CIPHER_meth_get_cleanup()\fR, \fBEVP_CIPHER_meth_get_set_asn1_params()\fR,
\&\fBEVP_CIPHER_meth_get_get_asn1_params()\fR and \fBEVP_CIPHER_meth_get_ctrl()\fR
are all used to retrieve the method data given with the
EVP_CIPHER_meth_set_*() functions above.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBEVP_CIPHER_meth_new()\fR and \fBEVP_CIPHER_meth_dup()\fR return a pointer to a
newly created \fBEVP_CIPHER\fR, or NULL on failure.
All EVP_CIPHER_meth_set_*() functions return 1.
All EVP_CIPHER_meth_get_*() functions return pointers to their
respective \fBcipher\fR function.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBEVP_EncryptInit\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
All of these functions were deprecated in OpenSSL 3.0.
.PP
The functions described here were added in OpenSSL 1.1.0.
The \fBEVP_CIPHER\fR structure created with these functions became reference
counted in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2016\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
