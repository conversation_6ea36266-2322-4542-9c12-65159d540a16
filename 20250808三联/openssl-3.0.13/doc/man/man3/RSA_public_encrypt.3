.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "RSA_PUBLIC_ENCRYPT 3ossl"
.TH RSA_PUBLIC_ENCRYPT 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
RSA_public_encrypt, RSA_private_decrypt \- RSA public key cryptography
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/rsa.h>
.Ve
.PP
The following functions have been deprecated since OpenSSL 3.0, and can be
hidden entirely by defining \fBOPENSSL_API_COMPAT\fR with a suitable version value,
see \fBopenssl_user_macros\fR\|(7):
.PP
.Vb 2
\& int RSA_public_encrypt(int flen, const unsigned char *from,
\&                        unsigned char *to, RSA *rsa, int padding);
\&
\& int RSA_private_decrypt(int flen, const unsigned char *from,
\&                         unsigned char *to, RSA *rsa, int padding);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
Both of the functions described on this page are deprecated.
Applications should instead use \fBEVP_PKEY_encrypt_init_ex\fR\|(3),
\&\fBEVP_PKEY_encrypt\fR\|(3), \fBEVP_PKEY_decrypt_init_ex\fR\|(3) and
\&\fBEVP_PKEY_decrypt\fR\|(3).
.PP
\&\fBRSA_public_encrypt()\fR encrypts the \fBflen\fR bytes at \fBfrom\fR (usually a
session key) using the public key \fBrsa\fR and stores the ciphertext in
\&\fBto\fR. \fBto\fR must point to RSA_size(\fBrsa\fR) bytes of memory.
.PP
\&\fBpadding\fR denotes one of the following modes:
.IP RSA_PKCS1_PADDING 4
.IX Item "RSA_PKCS1_PADDING"
PKCS #1 v1.5 padding. This currently is the most widely used mode.
However, it is highly recommended to use RSA_PKCS1_OAEP_PADDING in
new applications. SEE WARNING BELOW.
.IP RSA_PKCS1_OAEP_PADDING 4
.IX Item "RSA_PKCS1_OAEP_PADDING"
EME-OAEP as defined in PKCS #1 v2.0 with SHA\-1, MGF1 and an empty
encoding parameter. This mode is recommended for all new applications.
.IP RSA_NO_PADDING 4
.IX Item "RSA_NO_PADDING"
Raw RSA encryption. This mode should \fIonly\fR be used to implement
cryptographically sound padding modes in the application code.
Encrypting user data directly with RSA is insecure.
.PP
\&\fBflen\fR must not be more than RSA_size(\fBrsa\fR) \- 11 for the PKCS #1 v1.5
based padding modes, not more than RSA_size(\fBrsa\fR) \- 42 for
RSA_PKCS1_OAEP_PADDING and exactly RSA_size(\fBrsa\fR) for RSA_NO_PADDING.
When a padding mode other than RSA_NO_PADDING is in use, then
\&\fBRSA_public_encrypt()\fR will include some random bytes into the ciphertext
and therefore the ciphertext will be different each time, even if the
plaintext and the public key are exactly identical.
The returned ciphertext in \fBto\fR will always be zero padded to exactly
RSA_size(\fBrsa\fR) bytes.
\&\fBto\fR and \fBfrom\fR may overlap.
.PP
\&\fBRSA_private_decrypt()\fR decrypts the \fBflen\fR bytes at \fBfrom\fR using the
private key \fBrsa\fR and stores the plaintext in \fBto\fR. \fBflen\fR should
be equal to RSA_size(\fBrsa\fR) but may be smaller, when leading zero
bytes are in the ciphertext. Those are not important and may be removed,
but \fBRSA_public_encrypt()\fR does not do that. \fBto\fR must point
to a memory section large enough to hold the maximal possible decrypted
data (which is equal to RSA_size(\fBrsa\fR) for RSA_NO_PADDING,
RSA_size(\fBrsa\fR) \- 11 for the PKCS #1 v1.5 based padding modes and
RSA_size(\fBrsa\fR) \- 42 for RSA_PKCS1_OAEP_PADDING).
\&\fBpadding\fR is the padding mode that was used to encrypt the data.
\&\fBto\fR and \fBfrom\fR may overlap.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBRSA_public_encrypt()\fR returns the size of the encrypted data (i.e.,
RSA_size(\fBrsa\fR)). \fBRSA_private_decrypt()\fR returns the size of the
recovered plaintext. A return value of 0 is not an error and
means only that the plaintext was empty.
.PP
On error, \-1 is returned; the error codes can be
obtained by \fBERR_get_error\fR\|(3).
.SH WARNINGS
.IX Header "WARNINGS"
Decryption failures in the RSA_PKCS1_PADDING mode leak information
which can potentially be used to mount a Bleichenbacher padding oracle
attack. This is an inherent weakness in the PKCS #1 v1.5 padding
design. Prefer RSA_PKCS1_OAEP_PADDING.
.SH "CONFORMING TO"
.IX Header "CONFORMING TO"
SSL, PKCS #1 v2.0
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBERR_get_error\fR\|(3), \fBRAND_bytes\fR\|(3),
\&\fBRSA_size\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
Both of these functions were deprecated in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
