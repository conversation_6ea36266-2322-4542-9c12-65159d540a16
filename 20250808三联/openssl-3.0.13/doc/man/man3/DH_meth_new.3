.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "DH_METH_NEW 3ossl"
.TH DH_METH_NEW 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
DH_meth_new, DH_meth_free, DH_meth_dup, DH_meth_get0_name, DH_meth_set1_name,
DH_meth_get_flags, DH_meth_set_flags, DH_meth_get0_app_data,
DH_meth_set0_app_data, DH_meth_get_generate_key, DH_meth_set_generate_key,
DH_meth_get_compute_key, DH_meth_set_compute_key, DH_meth_get_bn_mod_exp,
DH_meth_set_bn_mod_exp, DH_meth_get_init, DH_meth_set_init, DH_meth_get_finish,
DH_meth_set_finish, DH_meth_get_generate_params,
DH_meth_set_generate_params \- Routines to build up DH methods
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/dh.h>
.Ve
.PP
The following functions have been deprecated since OpenSSL 3.0, and can be
hidden entirely by defining \fBOPENSSL_API_COMPAT\fR with a suitable version value,
see \fBopenssl_user_macros\fR\|(7):
.PP
.Vb 1
\& DH_METHOD *DH_meth_new(const char *name, int flags);
\&
\& void DH_meth_free(DH_METHOD *dhm);
\&
\& DH_METHOD *DH_meth_dup(const DH_METHOD *dhm);
\&
\& const char *DH_meth_get0_name(const DH_METHOD *dhm);
\& int DH_meth_set1_name(DH_METHOD *dhm, const char *name);
\&
\& int DH_meth_get_flags(const DH_METHOD *dhm);
\& int DH_meth_set_flags(DH_METHOD *dhm, int flags);
\&
\& void *DH_meth_get0_app_data(const DH_METHOD *dhm);
\& int DH_meth_set0_app_data(DH_METHOD *dhm, void *app_data);
\&
\& int (*DH_meth_get_generate_key(const DH_METHOD *dhm))(DH *);
\& int DH_meth_set_generate_key(DH_METHOD *dhm, int (*generate_key)(DH *));
\&
\& int (*DH_meth_get_compute_key(const DH_METHOD *dhm))
\&     (unsigned char *key, const BIGNUM *pub_key, DH *dh);
\& int DH_meth_set_compute_key(DH_METHOD *dhm,
\&     int (*compute_key)(unsigned char *key, const BIGNUM *pub_key, DH *dh));
\&
\& int (*DH_meth_get_bn_mod_exp(const DH_METHOD *dhm))
\&     (const DH *dh, BIGNUM *r, const BIGNUM *a, const BIGNUM *p,
\&      const BIGNUM *m, BN_CTX *ctx, BN_MONT_CTX *m_ctx);
\& int DH_meth_set_bn_mod_exp(DH_METHOD *dhm,
\&     int (*bn_mod_exp)(const DH *dh, BIGNUM *r, const BIGNUM *a,
\&                       const BIGNUM *p, const BIGNUM *m, BN_CTX *ctx,
\&                       BN_MONT_CTX *m_ctx));
\&
\& int (*DH_meth_get_init(const DH_METHOD *dhm))(DH *);
\& int DH_meth_set_init(DH_METHOD *dhm, int (*init)(DH *));
\&
\& int (*DH_meth_get_finish(const DH_METHOD *dhm))(DH *);
\& int DH_meth_set_finish(DH_METHOD *dhm, int (*finish)(DH *));
\&
\& int (*DH_meth_get_generate_params(const DH_METHOD *dhm))
\&     (DH *, int, int, BN_GENCB *);
\& int DH_meth_set_generate_params(DH_METHOD *dhm,
\&     int (*generate_params)(DH *, int, int, BN_GENCB *));
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
All of the functions described on this page are deprecated.
Applications should instead use the provider APIs.
.PP
The \fBDH_METHOD\fR type is a structure used for the provision of custom DH
implementations. It provides a set of functions used by OpenSSL for the
implementation of the various DH capabilities.
.PP
\&\fBDH_meth_new()\fR creates a new \fBDH_METHOD\fR structure. It should be given a
unique \fBname\fR and a set of \fBflags\fR. The \fBname\fR should be a NULL terminated
string, which will be duplicated and stored in the \fBDH_METHOD\fR object. It is
the callers responsibility to free the original string. The flags will be used
during the construction of a new \fBDH\fR object based on this \fBDH_METHOD\fR. Any
new \fBDH\fR object will have those flags set by default.
.PP
\&\fBDH_meth_dup()\fR creates a duplicate copy of the \fBDH_METHOD\fR object passed as a
parameter. This might be useful for creating a new \fBDH_METHOD\fR based on an
existing one, but with some differences.
.PP
\&\fBDH_meth_free()\fR destroys a \fBDH_METHOD\fR structure and frees up any memory
associated with it.
.PP
\&\fBDH_meth_get0_name()\fR will return a pointer to the name of this DH_METHOD. This
is a pointer to the internal name string and so should not be freed by the
caller. \fBDH_meth_set1_name()\fR sets the name of the DH_METHOD to \fBname\fR. The
string is duplicated and the copy is stored in the DH_METHOD structure, so the
caller remains responsible for freeing the memory associated with the name.
.PP
\&\fBDH_meth_get_flags()\fR returns the current value of the flags associated with this
DH_METHOD. \fBDH_meth_set_flags()\fR provides the ability to set these flags.
.PP
The functions \fBDH_meth_get0_app_data()\fR and \fBDH_meth_set0_app_data()\fR provide the
ability to associate implementation specific data with the DH_METHOD. It is
the application's responsibility to free this data before the DH_METHOD is
freed via a call to \fBDH_meth_free()\fR.
.PP
\&\fBDH_meth_get_generate_key()\fR and \fBDH_meth_set_generate_key()\fR get and set the
function used for generating a new DH key pair respectively. This function will
be called in response to the application calling \fBDH_generate_key()\fR. The
parameter for the function has the same meaning as for \fBDH_generate_key()\fR.
.PP
\&\fBDH_meth_get_compute_key()\fR and \fBDH_meth_set_compute_key()\fR get and set the
function used for computing a new DH shared secret respectively. This function
will be called in response to the application calling \fBDH_compute_key()\fR. The
parameters for the function have the same meaning as for \fBDH_compute_key()\fR.
.PP
\&\fBDH_meth_get_bn_mod_exp()\fR and \fBDH_meth_set_bn_mod_exp()\fR get and set the function
used for computing the following value:
.PP
.Vb 1
\& r = a ^ p mod m
.Ve
.PP
This function will be called by the default OpenSSL function for
\&\fBDH_generate_key()\fR. The result is stored in the \fBr\fR parameter. This function
may be NULL unless using the default generate key function, in which case it
must be present.
.PP
\&\fBDH_meth_get_init()\fR and \fBDH_meth_set_init()\fR get and set the function used
for creating a new DH instance respectively. This function will be
called in response to the application calling \fBDH_new()\fR (if the current default
DH_METHOD is this one) or \fBDH_new_method()\fR. The \fBDH_new()\fR and \fBDH_new_method()\fR
functions will allocate the memory for the new DH object, and a pointer to this
newly allocated structure will be passed as a parameter to the function. This
function may be NULL.
.PP
\&\fBDH_meth_get_finish()\fR and \fBDH_meth_set_finish()\fR get and set the function used
for destroying an instance of a DH object respectively. This function will be
called in response to the application calling \fBDH_free()\fR. A pointer to the DH
to be destroyed is passed as a parameter. The destroy function should be used
for DH implementation specific clean up. The memory for the DH itself should
not be freed by this function. This function may be NULL.
.PP
\&\fBDH_meth_get_generate_params()\fR and \fBDH_meth_set_generate_params()\fR get and set the
function used for generating DH parameters respectively. This function will be
called in response to the application calling \fBDH_generate_parameters_ex()\fR (or
\&\fBDH_generate_parameters()\fR). The parameters for the function have the same
meaning as for \fBDH_generate_parameters_ex()\fR. This function may be NULL.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBDH_meth_new()\fR and \fBDH_meth_dup()\fR return the newly allocated DH_METHOD object
or NULL on failure.
.PP
\&\fBDH_meth_get0_name()\fR and \fBDH_meth_get_flags()\fR return the name and flags
associated with the DH_METHOD respectively.
.PP
All other DH_meth_get_*() functions return the appropriate function pointer
that has been set in the DH_METHOD, or NULL if no such pointer has yet been
set.
.PP
\&\fBDH_meth_set1_name()\fR and all DH_meth_set_*() functions return 1 on success or
0 on failure.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBDH_new\fR\|(3), \fBDH_new\fR\|(3), \fBDH_generate_parameters\fR\|(3), \fBDH_generate_key\fR\|(3),
\&\fBDH_set_method\fR\|(3), \fBDH_size\fR\|(3), \fBDH_get0_pqg\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
All of these functions were deprecated in OpenSSL 3.0.
.PP
The functions described here were added in OpenSSL 1.1.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2016\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
