.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "UI_NEW 3ossl"
.TH UI_NEW 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
UI,
UI_new, UI_new_method, UI_free, UI_add_input_string, UI_dup_input_string,
UI_add_verify_string, UI_dup_verify_string, UI_add_input_boolean,
UI_dup_input_boolean, UI_add_info_string, UI_dup_info_string,
UI_add_error_string, UI_dup_error_string, UI_construct_prompt,
UI_add_user_data, UI_dup_user_data, UI_get0_user_data, UI_get0_result,
UI_get_result_length,
UI_process, UI_ctrl, UI_set_default_method, UI_get_default_method,
UI_get_method, UI_set_method, UI_OpenSSL, UI_null \- user interface
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/ui.h>
\&
\& typedef struct ui_st UI;
\&
\& UI *UI_new(void);
\& UI *UI_new_method(const UI_METHOD *method);
\& void UI_free(UI *ui);
\&
\& int UI_add_input_string(UI *ui, const char *prompt, int flags,
\&                         char *result_buf, int minsize, int maxsize);
\& int UI_dup_input_string(UI *ui, const char *prompt, int flags,
\&                         char *result_buf, int minsize, int maxsize);
\& int UI_add_verify_string(UI *ui, const char *prompt, int flags,
\&                          char *result_buf, int minsize, int maxsize,
\&                          const char *test_buf);
\& int UI_dup_verify_string(UI *ui, const char *prompt, int flags,
\&                          char *result_buf, int minsize, int maxsize,
\&                          const char *test_buf);
\& int UI_add_input_boolean(UI *ui, const char *prompt, const char *action_desc,
\&                          const char *ok_chars, const char *cancel_chars,
\&                          int flags, char *result_buf);
\& int UI_dup_input_boolean(UI *ui, const char *prompt, const char *action_desc,
\&                          const char *ok_chars, const char *cancel_chars,
\&                          int flags, char *result_buf);
\& int UI_add_info_string(UI *ui, const char *text);
\& int UI_dup_info_string(UI *ui, const char *text);
\& int UI_add_error_string(UI *ui, const char *text);
\& int UI_dup_error_string(UI *ui, const char *text);
\&
\& char *UI_construct_prompt(UI *ui_method,
\&                           const char *phrase_desc, const char *object_name);
\&
\& void *UI_add_user_data(UI *ui, void *user_data);
\& int UI_dup_user_data(UI *ui, void *user_data);
\& void *UI_get0_user_data(UI *ui);
\&
\& const char *UI_get0_result(UI *ui, int i);
\& int UI_get_result_length(UI *ui, int i);
\&
\& int UI_process(UI *ui);
\&
\& int UI_ctrl(UI *ui, int cmd, long i, void *p, void (*f)());
\&
\& void UI_set_default_method(const UI_METHOD *meth);
\& const UI_METHOD *UI_get_default_method(void);
\& const UI_METHOD *UI_get_method(UI *ui);
\& const UI_METHOD *UI_set_method(UI *ui, const UI_METHOD *meth);
\&
\& UI_METHOD *UI_OpenSSL(void);
\& const UI_METHOD *UI_null(void);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
UI stands for User Interface, and is general purpose set of routines to
prompt the user for text-based information.  Through user-written methods
(see \fBUI_create_method\fR\|(3)), prompting can be done in any way
imaginable, be it plain text prompting, through dialog boxes or from a
cell phone.
.PP
All the functions work through a context of the type UI.  This context
contains all the information needed to prompt correctly as well as a
reference to a UI_METHOD, which is an ordered vector of functions that
carry out the actual prompting.
.PP
The first thing to do is to create a UI with \fBUI_new()\fR or \fBUI_new_method()\fR,
then add information to it with the UI_add or UI_dup functions.  Also,
user-defined random data can be passed down to the underlying method
through calls to \fBUI_add_user_data()\fR or \fBUI_dup_user_data()\fR.  The default
UI method doesn't care about these data, but other methods might.  Finally,
use \fBUI_process()\fR to actually perform the prompting and \fBUI_get0_result()\fR
and \fBUI_get_result_length()\fR to find the result to the prompt and its length.
.PP
A UI can contain more than one prompt, which are performed in the given
sequence.  Each prompt gets an index number which is returned by the
UI_add and UI_dup functions, and has to be used to get the corresponding
result with \fBUI_get0_result()\fR and \fBUI_get_result_length()\fR.
.PP
\&\fBUI_process()\fR can be called more than once on the same UI, thereby allowing
a UI to have a long lifetime, but can just as well have a short lifetime.
.PP
The functions are as follows:
.PP
\&\fBUI_new()\fR creates a new UI using the default UI method.  When done with
this UI, it should be freed using \fBUI_free()\fR.
.PP
\&\fBUI_new_method()\fR creates a new UI using the given UI method.  When done with
this UI, it should be freed using \fBUI_free()\fR.
.PP
\&\fBUI_OpenSSL()\fR returns the built-in UI method (note: not necessarily the
default one, since the default can be changed.  See further on).  This
method is the most machine/OS dependent part of OpenSSL and normally
generates the most problems when porting.
.PP
\&\fBUI_null()\fR returns a UI method that does nothing.  Its use is to avoid
getting internal defaults for passed UI_METHOD pointers.
.PP
\&\fBUI_free()\fR removes a UI from memory, along with all other pieces of memory
that's connected to it, like duplicated input strings, results and others.
If \fBui\fR is NULL nothing is done.
.PP
\&\fBUI_add_input_string()\fR and \fBUI_add_verify_string()\fR add a prompt to the UI,
as well as flags and a result buffer and the desired minimum and maximum
sizes of the result, not counting the final NUL character.  The given
information is used to prompt for information, for example a password,
and to verify a password (i.e. having the user enter it twice and check
that the same string was entered twice).  \fBUI_add_verify_string()\fR takes
and extra argument that should be a pointer to the result buffer of the
input string that it's supposed to verify, or verification will fail.
.PP
\&\fBUI_add_input_boolean()\fR adds a prompt to the UI that's supposed to be answered
in a boolean way, with a single character for yes and a different character
for no.  A set of characters that can be used to cancel the prompt is given
as well.  The prompt itself is divided in two, one part being the
descriptive text (given through the \fIprompt\fR argument) and one describing
the possible answers (given through the \fIaction_desc\fR argument).
.PP
\&\fBUI_add_info_string()\fR and \fBUI_add_error_string()\fR add strings that are shown at
the same time as the prompt for extra information or to show an error string.
The difference between the two is only conceptual.  With the built-in method,
there's no technical difference between them.  Other methods may make a
difference between them, however.
.PP
The flags currently supported are \fBUI_INPUT_FLAG_ECHO\fR, which is relevant for
\&\fBUI_add_input_string()\fR and will have the users response be echoed (when
prompting for a password, this flag should obviously not be used, and
\&\fBUI_INPUT_FLAG_DEFAULT_PWD\fR, which means that a default password of some
sort will be used (completely depending on the application and the UI
method).
.PP
\&\fBUI_dup_input_string()\fR, \fBUI_dup_verify_string()\fR, \fBUI_dup_input_boolean()\fR,
\&\fBUI_dup_info_string()\fR and \fBUI_dup_error_string()\fR are basically the same
as their UI_add counterparts, except that they make their own copies
of all strings.
.PP
\&\fBUI_construct_prompt()\fR is a helper function that can be used to create
a prompt from two pieces of information: a phrase description \fIphrase_desc\fR
and an object name \fIobject_name\fR, where the latter may be NULL.
The default constructor (if there is none provided by the method used)
creates a string "Enter \fIphrase_desc\fR for \fIobject_name\fR:"
where the " for \fIobject_name\fR" part is left out if \fIobject_name\fR is NULL.
With the description "pass phrase" and the filename "foo.key", that becomes
"Enter pass phrase for foo.key:".  Other methods may create whatever
string and may include encodings that will be processed by the other
method functions.
.PP
\&\fBUI_add_user_data()\fR adds a user data pointer for the method to use at any
time.  The built-in UI method doesn't care about this info.  Note that several
calls to this function doesn't add data, it replaces the previous blob
with the one given as argument.
.PP
\&\fBUI_dup_user_data()\fR duplicates the user data and works as an alternative
to \fBUI_add_user_data()\fR when the user data needs to be preserved for a longer
duration, perhaps even the lifetime of the application.  The UI object takes
ownership of this duplicate and will free it whenever it gets replaced or
the UI is destroyed.  \fBUI_dup_user_data()\fR returns 0 on success, or \-1 on memory
allocation failure or if the method doesn't have a duplicator function.
.PP
\&\fBUI_get0_user_data()\fR retrieves the data that has last been given to the
UI with \fBUI_add_user_data()\fR or UI_dup_user_data.
.PP
\&\fBUI_get0_result()\fR returns a pointer to the result buffer associated with
the information indexed by \fIi\fR.
.PP
\&\fBUI_get_result_length()\fR returns the length of the result buffer associated with
the information indexed by \fIi\fR.
.PP
\&\fBUI_process()\fR goes through the information given so far, does all the printing
and prompting and returns the final status, which is \-2 on out-of-band events
(Interrupt, Cancel, ...), \-1 on error and 0 on success.
.PP
\&\fBUI_ctrl()\fR adds extra control for the application author.  For now, it
understands two commands: \fBUI_CTRL_PRINT_ERRORS\fR, which makes \fBUI_process()\fR
print the OpenSSL error stack as part of processing the UI, and
\&\fBUI_CTRL_IS_REDOABLE\fR, which returns a flag saying if the used UI can
be used again or not.
.PP
\&\fBUI_set_default_method()\fR changes the default UI method to the one given.
This function is not thread-safe and should not be called at the same time
as other OpenSSL functions.
.PP
\&\fBUI_get_default_method()\fR returns a pointer to the current default UI method.
.PP
\&\fBUI_get_method()\fR returns the UI method associated with a given UI.
.PP
\&\fBUI_set_method()\fR changes the UI method associated with a given UI.
.SH NOTES
.IX Header "NOTES"
The resulting strings that the built in method \fBUI_OpenSSL()\fR generate
are assumed to be encoded according to the current locale or (for
Windows) code page.
For applications having different demands, these strings need to be
converted appropriately by the caller.
For Windows, if the \fBOPENSSL_WIN32_UTF8\fR environment variable is set,
the built-in method \fBUI_OpenSSL()\fR will produce UTF\-8 encoded strings
instead.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBUI_new()\fR and \fBUI_new_method()\fR return a valid \fBUI\fR structure or NULL if an error
occurred.
.PP
\&\fBUI_add_input_string()\fR, \fBUI_dup_input_string()\fR, \fBUI_add_verify_string()\fR,
\&\fBUI_dup_verify_string()\fR, \fBUI_add_input_boolean()\fR, \fBUI_dup_input_boolean()\fR,
\&\fBUI_add_info_string()\fR, \fBUI_dup_info_string()\fR, \fBUI_add_error_string()\fR
and \fBUI_dup_error_string()\fR return a positive number on success or a value which
is less than or equal to 0 otherwise.
.PP
\&\fBUI_construct_prompt()\fR returns a string or NULL if an error occurred.
.PP
\&\fBUI_dup_user_data()\fR returns 0 on success or \-1 on error.
.PP
\&\fBUI_get0_result()\fR returns a string or NULL on error.
.PP
\&\fBUI_get_result_length()\fR returns a positive integer or 0 on success; otherwise it
returns \-1 on error.
.PP
\&\fBUI_process()\fR returns 0 on success or a negative value on error.
.PP
\&\fBUI_ctrl()\fR returns a mask on success or \-1 on error.
.PP
\&\fBUI_get_default_method()\fR, \fBUI_get_method()\fR, \fBUI_OpenSSL()\fR, \fBUI_null()\fR and
\&\fBUI_set_method()\fR return either a valid \fBUI_METHOD\fR structure or NULL
respectively.
.SH HISTORY
.IX Header "HISTORY"
The \fBUI_dup_user_data()\fR function was added in OpenSSL 1.1.1.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2001\-2020 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
