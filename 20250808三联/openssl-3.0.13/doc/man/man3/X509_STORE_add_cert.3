.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "X509_STORE_ADD_CERT 3ossl"
.TH X509_STORE_ADD_CERT 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
X509_STORE,
X509_STORE_add_cert, X509_STORE_add_crl, X509_STORE_set_depth,
X509_STORE_set_flags, X509_STORE_set_purpose, X509_STORE_set_trust,
X509_STORE_add_lookup,
X509_STORE_load_file_ex, X509_STORE_load_file, X509_STORE_load_path,
X509_STORE_load_store_ex, X509_STORE_load_store,
X509_STORE_set_default_paths_ex, X509_STORE_set_default_paths,
X509_STORE_load_locations_ex, X509_STORE_load_locations
\&\- X509_STORE manipulation
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/x509_vfy.h>
\&
\& typedef x509_store_st X509_STORE;
\&
\& int X509_STORE_add_cert(X509_STORE *ctx, X509 *x);
\& int X509_STORE_add_crl(X509_STORE *ctx, X509_CRL *x);
\& int X509_STORE_set_depth(X509_STORE *store, int depth);
\& int X509_STORE_set_flags(X509_STORE *ctx, unsigned long flags);
\& int X509_STORE_set_purpose(X509_STORE *ctx, int purpose);
\& int X509_STORE_set_trust(X509_STORE *ctx, int trust);
\&
\& X509_LOOKUP *X509_STORE_add_lookup(X509_STORE *store,
\&                                    X509_LOOKUP_METHOD *meth);
\&
\& int X509_STORE_set_default_paths_ex(X509_STORE *ctx, OSSL_LIB_CTX *libctx,
\&                                     const char *propq);
\& int X509_STORE_set_default_paths(X509_STORE *ctx);
\& int X509_STORE_load_file_ex(X509_STORE *ctx, const char *file,
\&                             OSSL_LIB_CTX *libctx, const char *propq);
\& int X509_STORE_load_file(X509_STORE *ctx, const char *file);
\& int X509_STORE_load_path(X509_STORE *ctx, const char *dir);
\& int X509_STORE_load_store_ex(X509_STORE *ctx, const char *uri,
\&                              OSSL_LIB_CTX *libctx, const char *propq);
\& int X509_STORE_load_store(X509_STORE *ctx, const char *uri);
\& int X509_STORE_load_locations_ex(X509_STORE *ctx, const char *file,
\&                                  const char *dir, OSSL_LIB_CTX *libctx,
\&                                  const char *propq);
\& int X509_STORE_load_locations(X509_STORE *ctx,
\&                               const char *file, const char *dir);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
The \fBX509_STORE\fR structure is intended to be a consolidated mechanism for
holding information about X.509 certificates and CRLs, and constructing
and validating chains of certificates terminating in trusted roots.
It admits multiple lookup mechanisms and efficient scaling performance
with large numbers of certificates, and a great deal of flexibility in
how validation and policy checks are performed.
.PP
Details of the chain building and checking process are described in
"Certification Path Building" in \fBopenssl\-verification\-options\fR\|(1) and
"Certification Path Validation" in \fBopenssl\-verification\-options\fR\|(1).
.PP
\&\fBX509_STORE_new\fR\|(3) creates an empty \fBX509_STORE\fR structure, which contains
no information about trusted certificates or where such certificates
are located on disk, and is generally not usable.  Normally, trusted
certificates will be added to the \fBX509_STORE\fR to prepare it for use,
via mechanisms such as \fBX509_STORE_add_lookup()\fR and \fBX509_LOOKUP_file()\fR, or
\&\fBPEM_read_bio_X509_AUX()\fR and \fBX509_STORE_add_cert()\fR.  CRLs can also be added,
and many behaviors configured as desired.
.PP
Once the \fBX509_STORE\fR is suitably configured, \fBX509_STORE_CTX_new()\fR is
used to instantiate a single-use \fBX509_STORE_CTX\fR for each chain-building
and verification operation.  That process includes providing the end-entity
certificate to be verified and an additional set of untrusted certificates
that may be used in chain-building.  As such, it is expected that the
certificates included in the \fBX509_STORE\fR are certificates that represent
trusted entities such as root certificate authorities (CAs).
OpenSSL represents these trusted certificates internally as \fBX509\fR objects
with an associated \fBX509_CERT_AUX\fR, as are produced by
\&\fBPEM_read_bio_X509_AUX()\fR and similar routines that refer to X509_AUX.
The public interfaces that operate on such trusted certificates still
operate on pointers to \fBX509\fR objects, though.
.PP
\&\fBX509_STORE_add_cert()\fR and \fBX509_STORE_add_crl()\fR add the respective object
to the \fBX509_STORE\fR's local storage.  Untrusted objects should not be
added in this way.  The added object's reference count is incremented by one,
hence the caller retains ownership of the object and needs to free it when it
is no longer needed.
.PP
\&\fBX509_STORE_set_depth()\fR, \fBX509_STORE_set_flags()\fR, \fBX509_STORE_set_purpose()\fR,
\&\fBX509_STORE_set_trust()\fR, and \fBX509_STORE_set1_param()\fR set the default values
for the corresponding values used in certificate chain validation.  Their
behavior is documented in the corresponding \fBX509_VERIFY_PARAM\fR manual
pages, e.g., \fBX509_VERIFY_PARAM_set_depth\fR\|(3).
.PP
\&\fBX509_STORE_add_lookup()\fR finds or creates a \fBX509_LOOKUP\fR\|(3) with the
\&\fBX509_LOOKUP_METHOD\fR\|(3) \fImeth\fR and adds it to the \fBX509_STORE\fR
\&\fIstore\fR.  This also associates the \fBX509_STORE\fR with the lookup, so
\&\fBX509_LOOKUP\fR functions can look up objects in that store.
.PP
\&\fBX509_STORE_load_file_ex()\fR loads trusted certificate(s) into an
\&\fBX509_STORE\fR from a given file. The library context \fIlibctx\fR and property
query \fIpropq\fR are used when fetching algorithms from providers.
.PP
\&\fBX509_STORE_load_file()\fR is similar to \fBX509_STORE_load_file_ex()\fR but
uses NULL for the library context \fIlibctx\fR and property query \fIpropq\fR.
.PP
\&\fBX509_STORE_load_path()\fR loads trusted certificate(s) into an
\&\fBX509_STORE\fR from a given directory path.
The certificates in the directory must be in hashed form, as
documented in \fBX509_LOOKUP_hash_dir\fR\|(3).
.PP
\&\fBX509_STORE_load_store_ex()\fR loads trusted certificate(s) into an
\&\fBX509_STORE\fR from a store at a given URI. The library context \fIlibctx\fR and
property query \fIpropq\fR are used when fetching algorithms from providers.
.PP
\&\fBX509_STORE_load_store()\fR is similar to \fBX509_STORE_load_store_ex()\fR but
uses NULL for the library context \fIlibctx\fR and property query \fIpropq\fR.
.PP
\&\fBX509_STORE_load_locations_ex()\fR combines
\&\fBX509_STORE_load_file_ex()\fR and \fBX509_STORE_load_path()\fR for a given file
and/or directory path.
It is permitted to specify just a file, just a directory, or both
paths.
.PP
\&\fBX509_STORE_load_locations()\fR is similar to \fBX509_STORE_load_locations_ex()\fR
but uses NULL for the library context \fIlibctx\fR and property query \fIpropq\fR.
.PP
\&\fBX509_STORE_set_default_paths_ex()\fR is somewhat misnamed, in that it does
not set what default paths should be used for loading certificates.  Instead,
it loads certificates into the \fBX509_STORE\fR from the hardcoded default
paths. The library context \fIlibctx\fR and property query \fIpropq\fR are used when
fetching algorithms from providers.
.PP
\&\fBX509_STORE_set_default_paths()\fR is similar to
\&\fBX509_STORE_set_default_paths_ex()\fR but uses NULL for the library
context \fIlibctx\fR and property query \fIpropq\fR.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBX509_STORE_add_cert()\fR, \fBX509_STORE_add_crl()\fR, \fBX509_STORE_set_depth()\fR,
\&\fBX509_STORE_set_flags()\fR, \fBX509_STORE_set_purpose()\fR, \fBX509_STORE_set_trust()\fR,
\&\fBX509_STORE_load_file_ex()\fR, \fBX509_STORE_load_file()\fR,
\&\fBX509_STORE_load_path()\fR,
\&\fBX509_STORE_load_store_ex()\fR, \fBX509_STORE_load_store()\fR,
\&\fBX509_STORE_load_locations_ex()\fR, \fBX509_STORE_load_locations()\fR,
\&\fBX509_STORE_set_default_paths_ex()\fR and \fBX509_STORE_set_default_paths()\fR
return 1 on success or 0 on failure.
.PP
\&\fBX509_STORE_add_lookup()\fR returns the found or created
\&\fBX509_LOOKUP\fR\|(3), or NULL on error.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBX509_LOOKUP_hash_dir\fR\|(3).
\&\fBX509_VERIFY_PARAM_set_depth\fR\|(3).
\&\fBX509_STORE_new\fR\|(3),
\&\fBX509_STORE_get0_param\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
The functions \fBX509_STORE_set_default_paths_ex()\fR,
\&\fBX509_STORE_load_file_ex()\fR, \fBX509_STORE_load_store_ex()\fR and
\&\fBX509_STORE_load_locations_ex()\fR were added in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2017\-2022 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
