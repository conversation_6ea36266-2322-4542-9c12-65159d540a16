.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "ASN1_EXTERN_FUNCS 3ossl"
.TH ASN1_EXTERN_FUNCS 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
ASN1_EXTERN_FUNCS, ASN1_ex_d2i, ASN1_ex_d2i_ex, ASN1_ex_i2d, ASN1_ex_new_func,
ASN1_ex_new_ex_func, ASN1_ex_free_func, ASN1_ex_print_func,
IMPLEMENT_EXTERN_ASN1
\&\- ASN.1 external function support
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/asn1t.h>
\&
\& typedef int ASN1_ex_d2i(ASN1_VALUE **pval, const unsigned char **in, long len,
\&                         const ASN1_ITEM *it, int tag, int aclass, char opt,
\&                         ASN1_TLC *ctx);
\& typedef int ASN1_ex_d2i_ex(ASN1_VALUE **pval, const unsigned char **in, long len,
\&                            const ASN1_ITEM *it, int tag, int aclass, char opt,
\&                            ASN1_TLC *ctx, OSSL_LIB_CTX *libctx,
\&                            const char *propq);
\& typedef int ASN1_ex_i2d(const ASN1_VALUE **pval, unsigned char **out,
\&                         const ASN1_ITEM *it, int tag, int aclass);
\& typedef int ASN1_ex_new_func(ASN1_VALUE **pval, const ASN1_ITEM *it);
\& typedef int ASN1_ex_new_ex_func(ASN1_VALUE **pval, const ASN1_ITEM *it,
\&                                 OSSL_LIB_CTX *libctx, const char *propq);
\& typedef void ASN1_ex_free_func(ASN1_VALUE **pval, const ASN1_ITEM *it);
\& typedef int ASN1_ex_print_func(BIO *out, const ASN1_VALUE **pval,
\&                                int indent, const char *fname,
\&                                const ASN1_PCTX *pctx);
\&
\& struct ASN1_EXTERN_FUNCS_st {
\&    void *app_data;
\&    ASN1_ex_new_func *asn1_ex_new;
\&    ASN1_ex_free_func *asn1_ex_free;
\&    ASN1_ex_free_func *asn1_ex_clear;
\&    ASN1_ex_d2i *asn1_ex_d2i;
\&    ASN1_ex_i2d *asn1_ex_i2d;
\&    ASN1_ex_print_func *asn1_ex_print;
\&    ASN1_ex_new_ex_func *asn1_ex_new_ex;
\&    ASN1_ex_d2i_ex *asn1_ex_d2i_ex;
\& };
\& typedef struct ASN1_EXTERN_FUNCS_st ASN1_EXTERN_FUNCS;
\&
\& #define IMPLEMENT_EXTERN_ASN1(sname, tag, fptrs)
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
ASN.1 data structures templates are typically defined in OpenSSL using a series
of macros such as \fBASN1_SEQUENCE()\fR, \fBASN1_SEQUENCE_END()\fR and so on. Instead
templates can also be defined based entirely on external functions. These
external functions are called to perform operations such as creating a new
\&\fBASN1_VALUE\fR or converting an \fBASN1_VALUE\fR to or from DER encoding.
.PP
The macro \fBIMPLEMENT_EXTERN_ASN1()\fR can be used to create such an externally
defined structure. The name of the structure should be supplied in the \fIsname\fR
parameter. The tag for the structure (e.g. typically \fBV_ASN1_SEQUENCE\fR) should
be supplied in the \fItag\fR parameter. Finally a pointer to an
\&\fBASN1_EXTERN_FUNCS\fR structure should be supplied in the \fIfptrs\fR parameter.
.PP
The \fBASN1_EXTERN_FUNCS\fR structure has the following entries.
.IP \fIapp_data\fR 4
.IX Item "app_data"
A pointer to arbitrary application specific data.
.IP \fIasn1_ex_new\fR 4
.IX Item "asn1_ex_new"
A "new" function responsible for constructing a new \fBASN1_VALUE\fR object. The
newly constructed value should be stored in \fI*pval\fR. The \fIit\fR parameter is a
pointer to the \fBASN1_ITEM\fR template object created via the
\&\fBIMPLEMENT_EXTERN_ASN1()\fR macro.
.Sp
Returns a positive value on success or 0 on error.
.IP \fIasn1_ex_free\fR 4
.IX Item "asn1_ex_free"
A "free" function responsible for freeing the \fBASN1_VALUE\fR passed in \fI*pval\fR
that was previously allocated via a "new" function. The \fIit\fR parameter is a
pointer to the \fBASN1_ITEM\fR template object created via the
\&\fBIMPLEMENT_EXTERN_ASN1()\fR macro.
.IP \fIasn1_ex_clear\fR 4
.IX Item "asn1_ex_clear"
A "clear" function responsible for clearing any data in the \fBASN1_VALUE\fR passed
in \fI*pval\fR and making it suitable for reuse. The \fIit\fR parameter is a pointer
to the \fBASN1_ITEM\fR template object created via the \fBIMPLEMENT_EXTERN_ASN1()\fR
macro.
.IP \fIasn1_ex_d2i\fR 4
.IX Item "asn1_ex_d2i"
A "d2i" function responsible for converting DER data with the tag \fItag\fR and
class \fIclass\fR into an \fBASN1_VALUE\fR. If \fI*pval\fR is non-NULL then the
\&\fBASN_VALUE\fR it points to should be reused. Otherwise a new \fBASN1_VALUE\fR
should be allocated and stored in \fI*pval\fR. \fI*in\fR points to the DER data to be
decoded and \fIlen\fR is the length of that data. After decoding \fI*in\fR should be
updated to point at the next byte after the decoded data. If the \fBASN1_VALUE\fR
is considered optional in this context then \fIopt\fR will be nonzero. Otherwise
it will be zero. The \fIit\fR parameter is a pointer to the \fBASN1_ITEM\fR template
object created via the \fBIMPLEMENT_EXTERN_ASN1()\fR macro. A pointer to the current
\&\fBASN1_TLC\fR context (which may be required for other ASN1 function calls) is
passed in the \fIctx\fR parameter.
.Sp
The \fIasn1_ex_d2i\fR entry may be NULL if \fIasn1_ex_d2i_ex\fR has been specified
instead.
.Sp
Returns <= 0 on error or a positive value on success.
.IP \fIasn1_ex_i2d\fR 4
.IX Item "asn1_ex_i2d"
An "i2d" function responsible for converting an \fBASN1_VALUE\fR into DER encoding.
On entry \fI*pval\fR will contain the \fBASN1_VALUE\fR to be encoded. If default
tagging is to be used then \fItag\fR will be \-1 on entry. Otherwise if implicit
tagging should be used then \fItag\fR and \fIaclass\fR will be the tag and associated
class.
.Sp
If \fIout\fR is not NULL then this function should write the DER encoded data to
the buffer in \fI*out\fR, and then increment \fI*out\fR to point to immediately after
the data just written.
.Sp
If \fIout\fR is NULL then no data should be written but the length calculated and
returned as if it were.
.Sp
The \fIasn1_ex_i2d\fR entry may be NULL if \fIasn1_ex_i2d_ex\fR has been specified
instead.
.Sp
The return value should be negative if a fatal error occurred, or 0 if a
non-fatal error occurred. Otherwise it should return the length of the encoded
data.
.IP \fIasn1_ex_print\fR 4
.IX Item "asn1_ex_print"
A "print" function. \fIout\fR is the BIO to print the output to. \fI*pval\fR is the
\&\fBASN1_VALUE\fR to be printed. \fIindent\fR is the number of spaces of indenting to
be printed before any data is printed. \fIfname\fR is currently unused and is
always "". \fIpctx\fR is a pointer to the \fBASN1_PCTX\fR for the print operation.
.Sp
Returns 0 on error or a positive value on success. If the return value is 2 then
an additional newline will be printed after the data printed by this function.
.IP \fIasn1_ex_new_ex\fR 4
.IX Item "asn1_ex_new_ex"
This is the same as \fIasn1_ex_new\fR except that it is additionally passed the
OSSL_LIB_CTX to be used in \fIlibctx\fR and any property query string to be used
for algorithm fetching in the \fIpropq\fR parameter. See
"ALGORITHM FETCHING" in \fBcrypto\fR\|(7) for further details. If \fIasn1_ex_new_ex\fR is
non NULL, then it will always be called in preference to \fIasn1_ex_new\fR.
.IP \fIasn1_ex_d2i_ex\fR 4
.IX Item "asn1_ex_d2i_ex"
This is the same as \fIasn1_ex_d2i\fR except that it is additionally passed the
OSSL_LIB_CTX to be used in \fIlibctx\fR and any property query string to be used
for algorithm fetching in the \fIpropq\fR parameter. See
"ALGORITHM FETCHING" in \fBcrypto\fR\|(7) for further details. If \fIasn1_ex_d2i_ex\fR is
non NULL, then it will always be called in preference to \fIasn1_ex_d2i\fR.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
Return values for the various callbacks are as described above.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBASN1_item_new_ex\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
The \fIasn1_ex_new_ex\fR and \fIasn1_ex_d2i_ex\fR callbacks were added in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
