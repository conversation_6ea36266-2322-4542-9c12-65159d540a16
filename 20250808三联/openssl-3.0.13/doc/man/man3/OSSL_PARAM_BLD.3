.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "OSSL_PARAM_BLD 3ossl"
.TH OSSL_PARAM_BLD 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
OSSL_PARAM_BLD, OSSL_PARAM_BLD_new, OSSL_PARAM_BLD_to_param,
OSSL_PARAM_BLD_free, OSSL_PARAM_BLD_push_int,
OSSL_PARAM_BLD_push_uint, OSSL_PARAM_BLD_push_long,
OSSL_PARAM_BLD_push_ulong, OSSL_PARAM_BLD_push_int32,
OSSL_PARAM_BLD_push_uint32, OSSL_PARAM_BLD_push_int64,
OSSL_PARAM_BLD_push_uint64, OSSL_PARAM_BLD_push_size_t,
OSSL_PARAM_BLD_push_time_t, OSSL_PARAM_BLD_push_double,
OSSL_PARAM_BLD_push_BN, OSSL_PARAM_BLD_push_BN_pad,
OSSL_PARAM_BLD_push_utf8_string, OSSL_PARAM_BLD_push_utf8_ptr,
OSSL_PARAM_BLD_push_octet_string, OSSL_PARAM_BLD_push_octet_ptr
\&\- functions to assist in the creation of OSSL_PARAM arrays
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/param_build.h>
\&
\& typedef struct OSSL_PARAM_BLD;
\&
\& OSSL_PARAM_BLD *OSSL_PARAM_BLD_new(void);
\& OSSL_PARAM *OSSL_PARAM_BLD_to_param(OSSL_PARAM_BLD *bld);
\& void OSSL_PARAM_BLD_free(OSSL_PARAM_BLD *bld);
\&
\& int OSSL_PARAM_BLD_push_TYPE(OSSL_PARAM_BLD *bld, const char *key, TYPE val);
\&
\& int OSSL_PARAM_BLD_push_BN(OSSL_PARAM_BLD *bld, const char *key,
\&                            const BIGNUM *bn);
\& int OSSL_PARAM_BLD_push_BN_pad(OSSL_PARAM_BLD *bld, const char *key,
\&                                const BIGNUM *bn, size_t sz);
\&
\& int OSSL_PARAM_BLD_push_utf8_string(OSSL_PARAM_BLD *bld, const char *key,
\&                                     const char *buf, size_t bsize);
\& int OSSL_PARAM_BLD_push_utf8_ptr(OSSL_PARAM_BLD *bld, const char *key,
\&                                  char *buf, size_t bsize);
\& int OSSL_PARAM_BLD_push_octet_string(OSSL_PARAM_BLD *bld, const char *key,
\&                                      const void *buf, size_t bsize);
\& int OSSL_PARAM_BLD_push_octet_ptr(OSSL_PARAM_BLD *bld, const char *key,
\&                                   void *buf, size_t bsize);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
A collection of utility functions that simplify the creation of OSSL_PARAM
arrays.  The \fR\f(BITYPE\fR\fB\fR names are as per \fBOSSL_PARAM_int\fR\|(3).
.PP
\&\fBOSSL_PARAM_BLD_new()\fR allocates and initialises a new OSSL_PARAM_BLD structure
so that values can be added.
Any existing values are cleared.
.PP
\&\fBOSSL_PARAM_BLD_free()\fR deallocates the memory allocates by \fBOSSL_PARAM_BLD_new()\fR.
.PP
\&\fBOSSL_PARAM_BLD_to_param()\fR converts a built up OSSL_PARAM_BLD structure
\&\fIbld\fR into an allocated OSSL_PARAM array.
The OSSL_PARAM array and all associated storage must be freed by calling
\&\fBOSSL_PARAM_free()\fR with the functions return value.
\&\fBOSSL_PARAM_BLD_free()\fR can safely be called any time after this function is.
.PP
\&\fBOSSL_PARAM_BLD_push_\fR\f(BITYPE\fR() are a series of functions which will create
OSSL_PARAM objects of the specified size and correct type for the \fIval\fR
argument.
\&\fIval\fR is stored by value and an expression or auto variable can be used.
.PP
\&\fBOSSL_PARAM_BLD_push_BN()\fR is a function that will create an OSSL_PARAM object
that holds the specified BIGNUM \fIbn\fR.
If \fIbn\fR is marked as being securely allocated, its OSSL_PARAM representation
will also be securely allocated.
The \fIbn\fR argument is stored by reference and the underlying BIGNUM object
must exist until after \fBOSSL_PARAM_BLD_to_param()\fR has been called.
.PP
\&\fBOSSL_PARAM_BLD_push_BN_pad()\fR is a function that will create an OSSL_PARAM object
that holds the specified BIGNUM \fIbn\fR.
The object will be padded to occupy exactly \fIsz\fR bytes, if insufficient space
is specified an error results.
If \fIbn\fR is marked as being securely allocated, its OSSL_PARAM representation
will also be securely allocated.
The \fIbn\fR argument is stored by reference and the underlying BIGNUM object
must exist until after \fBOSSL_PARAM_BLD_to_param()\fR has been called.
.PP
\&\fBOSSL_PARAM_BLD_push_utf8_string()\fR is a function that will create an OSSL_PARAM
object that references the UTF8 string specified by \fIbuf\fR.
The length of the string \fIbsize\fR should not include the terminating NUL byte.
If it is zero then it will be calculated.
The string that \fIbuf\fR points to is stored by reference and must remain in
scope until after \fBOSSL_PARAM_BLD_to_param()\fR has been called.
.PP
\&\fBOSSL_PARAM_BLD_push_octet_string()\fR is a function that will create an OSSL_PARAM
object that references the octet string specified by \fIbuf\fR and <bsize>.
The memory that \fIbuf\fR points to is stored by reference and must remain in
scope until after \fBOSSL_PARAM_BLD_to_param()\fR has been called.
.PP
\&\fBOSSL_PARAM_BLD_push_utf8_ptr()\fR is a function that will create an OSSL_PARAM
object that references the UTF8 string specified by \fIbuf\fR.
The length of the string \fIbsize\fR should not include the terminating NUL byte.
If it is zero then it will be calculated.
The string \fIbuf\fR points to is stored by reference and must remain in
scope until the OSSL_PARAM array is freed.
.PP
\&\fBOSSL_PARAM_BLD_push_octet_ptr()\fR is a function that will create an OSSL_PARAM
object that references the octet string specified by \fIbuf\fR.
The memory \fIbuf\fR points to is stored by reference and must remain in
scope until the OSSL_PARAM array is freed.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBOSSL_PARAM_BLD_new()\fR returns the allocated OSSL_PARAM_BLD structure, or NULL
on error.
.PP
\&\fBOSSL_PARAM_BLD_to_param()\fR returns the allocated OSSL_PARAM array, or NULL
on error.
.PP
All of the OSSL_PARAM_BLD_push_TYPE functions return 1 on success and 0
on error.
.SH NOTES
.IX Header "NOTES"
\&\fBOSSL_PARAM_BLD_push_BN()\fR and \fBOSSL_PARAM_BLD_push_BN_pad()\fR currently only
support nonnegative \fBBIGNUM\fRs.  They return an error on negative \fBBIGNUM\fRs.
.SH EXAMPLES
.IX Header "EXAMPLES"
Both examples creating an OSSL_PARAM array that contains an RSA key.
For both, the predefined key variables are:
.PP
.Vb 6
\&    BIGNUM *n;           /* modulus */
\&    unsigned int e;      /* public exponent */
\&    BIGNUM *d;           /* private exponent */
\&    BIGNUM *p, *q;       /* first two prime factors */
\&    BIGNUM *dmp1, *dmq1; /* first two CRT exponents */
\&    BIGNUM *iqmp;        /* first CRT coefficient */
.Ve
.SS "Example 1"
.IX Subsection "Example 1"
This example shows how to create an OSSL_PARAM array that contains an RSA
private key.
.PP
.Vb 2
\&    OSSL_PARAM_BLD *bld = OSSL_PARAM_BLD_new();
\&    OSSL_PARAM *params = NULL;
\&
\&    if (bld == NULL
\&        || !OSSL_PARAM_BLD_push_BN(bld, "n", n)
\&        || !OSSL_PARAM_BLD_push_uint(bld, "e", e)
\&        || !OSSL_PARAM_BLD_push_BN(bld, "d", d)
\&        || !OSSL_PARAM_BLD_push_BN(bld, "rsa\-factor1", p)
\&        || !OSSL_PARAM_BLD_push_BN(bld, "rsa\-factor2", q)
\&        || !OSSL_PARAM_BLD_push_BN(bld, "rsa\-exponent1", dmp1)
\&        || !OSSL_PARAM_BLD_push_BN(bld, "rsa\-exponent2", dmq1)
\&        || !OSSL_PARAM_BLD_push_BN(bld, "rsa\-coefficient1", iqmp)
\&        || (params = OSSL_PARAM_BLD_to_param(bld)) == NULL)
\&        goto err;
\&    OSSL_PARAM_BLD_free(bld);
\&    /* Use params */
\&    ...
\&    OSSL_PARAM_free(params);
.Ve
.SS "Example 2"
.IX Subsection "Example 2"
This example shows how to create an OSSL_PARAM array that contains an RSA
public key.
.PP
.Vb 2
\&    OSSL_PARAM_BLD *bld = OSSL_PARAM_BLD_new();
\&    OSSL_PARAM *params = NULL;
\&
\&    if (nld == NULL
\&        || !OSSL_PARAM_BLD_push_BN(bld, "n", n)
\&        || !OSSL_PARAM_BLD_push_uint(bld, "e", e)
\&        || (params = OSSL_PARAM_BLD_to_param(bld)) == NULL)
\&        goto err;
\&    OSSL_PARAM_BLD_free(bld);
\&    /* Use params */
\&    ...
\&    OSSL_PARAM_free(params);
.Ve
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBOSSL_PARAM_int\fR\|(3), \fBOSSL_PARAM\fR\|(3), \fBOSSL_PARAM_free\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
The functions described here were all added in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2019\-2022 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
