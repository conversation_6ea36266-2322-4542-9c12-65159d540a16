.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "SRP_USER_PWD_NEW 3ossl"
.TH SRP_USER_PWD_NEW 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
SRP_user_pwd_new,
SRP_user_pwd_free,
SRP_user_pwd_set1_ids,
SRP_user_pwd_set_gN,
SRP_user_pwd_set0_sv
\&\- Functions to create a record of SRP user verifier information
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/srp.h>
.Ve
.PP
The following functions have been deprecated since OpenSSL 3.0, and can be
hidden entirely by defining \fBOPENSSL_API_COMPAT\fR with a suitable version value,
see \fBopenssl_user_macros\fR\|(7):
.PP
.Vb 2
\& SRP_user_pwd *SRP_user_pwd_new(void);
\& void SRP_user_pwd_free(SRP_user_pwd *user_pwd);
\&
\& int SRP_user_pwd_set1_ids(SRP_user_pwd *user_pwd, const char *id, const char *info);
\& void SRP_user_pwd_set_gN(SRP_user_pwd *user_pwd, const BIGNUM *g, const BIGNUM *N);
\& int SRP_user_pwd_set0_sv(SRP_user_pwd *user_pwd, BIGNUM *s, BIGNUM *v);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
All of the functions described on this page are deprecated. There are no
available replacement functions at this time.
.PP
The \fBSRP_user_pwd_new()\fR function allocates a structure to store a user verifier
record.
.PP
The \fBSRP_user_pwd_free()\fR function frees up the \fBuser_pwd\fR structure.
If \fBuser_pwd\fR is NULL, nothing is done.
.PP
The \fBSRP_user_pwd_set1_ids()\fR function sets the username to \fBid\fR and the optional
user info to \fBinfo\fR for \fBuser_pwd\fR.
The library allocates new copies of \fBid\fR and \fBinfo\fR, the caller still
owns the original memory.
.PP
The \fBSRP_user_pwd_set0_sv()\fR function sets the user salt to \fBs\fR and the verifier
to \fBv\fR for \fBuser_pwd\fR.
The library takes ownership of the values, they should not be freed by the caller.
.PP
The \fBSRP_user_pwd_set_gN()\fR function sets the SRP group parameters for \fBuser_pwd\fR.
The memory is not freed by \fBSRP_user_pwd_free()\fR, the caller must make sure it is
freed once it is no longer used.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBSRP_user_pwd_set1_ids()\fR returns 1 on success and 0 on failure or if \fBid\fR was NULL.
.PP
\&\fBSRP_user_pwd_set0_sv()\fR returns 1 if both \fBs\fR and \fBv\fR are not NULL, 0 otherwise.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBopenssl\-srp\fR\|(1),
\&\fBSRP_create_verifier\fR\|(3),
\&\fBSRP_VBASE_new\fR\|(3),
\&\fBSSL_CTX_set_srp_password\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
These functions were made public in OpenSSL 3.0 and are deprecated.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2018\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
