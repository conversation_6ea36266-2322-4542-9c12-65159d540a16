.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "OSSL_CRMF_MSG_GET0_TMPL 3ossl"
.TH OSSL_CRMF_MSG_GET0_TMPL 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
OSSL_CRMF_MSG_get0_tmpl,
OSSL_CRMF_CERTTEMPLATE_get0_serialNumber,
OSSL_CRMF_CERTTEMPLATE_get0_subject,
OSSL_CRMF_CERTTEMPLATE_get0_issuer,
OSSL_CRMF_CERTTEMPLATE_get0_extensions,
OSSL_CRMF_CERTID_get0_serialNumber,
OSSL_CRMF_CERTID_get0_issuer,
OSSL_CRMF_ENCRYPTEDVALUE_get1_encCert,
OSSL_CRMF_MSG_get_certReqId
\&\- functions reading from CRMF CertReqMsg structures
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/crmf.h>
\&
\& OSSL_CRMF_CERTTEMPLATE *OSSL_CRMF_MSG_get0_tmpl(const OSSL_CRMF_MSG *crm);
\& const ASN1_INTEGER
\& *OSSL_CRMF_CERTTEMPLATE_get0_serialNumber(const OSSL_CRMF_CERTTEMPLATE *tmpl);
\& const X509_NAME
\& *OSSL_CRMF_CERTTEMPLATE_get0_subject(const OSSL_CRMF_CERTTEMPLATE *tmpl);
\& const X509_NAME
\& *OSSL_CRMF_CERTTEMPLATE_get0_issuer(const OSSL_CRMF_CERTTEMPLATE *tmpl);
\& X509_EXTENSIONS
\& *OSSL_CRMF_CERTTEMPLATE_get0_extensions(const OSSL_CRMF_CERTTEMPLATE *tmpl);
\&
\& const ASN1_INTEGER
\& *OSSL_CRMF_CERTID_get0_serialNumber(const OSSL_CRMF_CERTID *cid);
\& const X509_NAME *OSSL_CRMF_CERTID_get0_issuer(const OSSL_CRMF_CERTID *cid);
\&
\& X509
\& *OSSL_CRMF_ENCRYPTEDVALUE_get1_encCert(const OSSL_CRMF_ENCRYPTEDVALUE *ecert,
\&                                        OSSL_LIB_CTX *libctx, const char *propq,
\&                                        EVP_PKEY *pkey);
\&
\& int OSSL_CRMF_MSG_get_certReqId(const OSSL_CRMF_MSG *crm);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBOSSL_CRMF_MSG_get0_tmpl()\fR retrieves the certificate template of \fIcrm\fR.
.PP
\&\fBOSSL_CRMF_CERTTEMPLATE_get0_serialNumber()\fR retrieves the serialNumber of the
given certificate template \fItmpl\fR.
.PP
\&\fBOSSL_CRMF_CERTTEMPLATE_get0_subject()\fR retrieves the subject name of the
given certificate template \fItmpl\fR.
.PP
\&\fBOSSL_CRMF_CERTTEMPLATE_get0_issuer()\fR retrieves the issuer name of the
given certificate template \fItmpl\fR.
.PP
\&\fBOSSL_CRMF_CERTTEMPLATE_get0_extensions()\fR retrieves the X.509 extensions
of the given certificate template \fItmpl\fR, or NULL if not present.
.PP
OSSL_CRMF_CERTID_get0_serialNumber retrieves the serialNumber
of the given CertId \fIcid\fR.
.PP
OSSL_CRMF_CERTID_get0_issuer retrieves the issuer name
of the given CertId \fIcid\fR, which must be of ASN.1 type GEN_DIRNAME.
.PP
\&\fBOSSL_CRMF_ENCRYPTEDVALUE_get1_encCert()\fR decrypts the certificate in the given
encryptedValue \fIecert\fR, using the private key \fIpkey\fR, library context
\&\fIlibctx\fR and property query string \fIpropq\fR (see \fBOSSL_LIB_CTX\fR\|(3)).
This is needed for the indirect POPO method as in RFC 4210 section *******.
The function returns the decrypted certificate as a copy, leaving its ownership
with the caller, who is responsible for freeing it.
.PP
\&\fBOSSL_CRMF_MSG_get_certReqId()\fR retrieves the certReqId of \fIcrm\fR.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBOSSL_CRMF_MSG_get_certReqId()\fR returns the certificate request ID as a
nonnegative integer or \-1 on error.
.PP
All other functions return a pointer with the intended result or NULL on error.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
RFC 4211
.SH HISTORY
.IX Header "HISTORY"
The OpenSSL CRMF support was added in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2007\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
