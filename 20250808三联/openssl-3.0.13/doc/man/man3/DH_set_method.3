.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "DH_SET_METHOD 3ossl"
.TH DH_SET_METHOD 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
DH_set_default_method, DH_get_default_method,
DH_set_method, DH_new_method, DH_OpenSSL \- select DH method
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/dh.h>
.Ve
.PP
The following functions have been deprecated since OpenSSL 3.0, and can be
hidden entirely by defining \fBOPENSSL_API_COMPAT\fR with a suitable version value,
see \fBopenssl_user_macros\fR\|(7):
.PP
.Vb 1
\& void DH_set_default_method(const DH_METHOD *meth);
\&
\& const DH_METHOD *DH_get_default_method(void);
\&
\& int DH_set_method(DH *dh, const DH_METHOD *meth);
\&
\& DH *DH_new_method(ENGINE *engine);
\&
\& const DH_METHOD *DH_OpenSSL(void);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
All of the functions described on this page are deprecated.
Applications should instead use the provider APIs.
.PP
A \fBDH_METHOD\fR specifies the functions that OpenSSL uses for Diffie-Hellman
operations. By modifying the method, alternative implementations
such as hardware accelerators may be used. IMPORTANT: See the NOTES section for
important information about how these DH API functions are affected by the use
of \fBENGINE\fR API calls.
.PP
Initially, the default DH_METHOD is the OpenSSL internal implementation, as
returned by \fBDH_OpenSSL()\fR.
.PP
\&\fBDH_set_default_method()\fR makes \fBmeth\fR the default method for all DH
structures created later.
\&\fBNB\fR: This is true only whilst no ENGINE has been set
as a default for DH, so this function is no longer recommended.
This function is not thread-safe and should not be called at the same time
as other OpenSSL functions.
.PP
\&\fBDH_get_default_method()\fR returns a pointer to the current default DH_METHOD.
However, the meaningfulness of this result is dependent on whether the ENGINE
API is being used, so this function is no longer recommended.
.PP
\&\fBDH_set_method()\fR selects \fBmeth\fR to perform all operations using the key \fBdh\fR.
This will replace the DH_METHOD used by the DH key and if the previous method
was supplied by an ENGINE, the handle to that ENGINE will be released during the
change. It is possible to have DH keys that only work with certain DH_METHOD
implementations (e.g. from an ENGINE module that supports embedded
hardware-protected keys), and in such cases attempting to change the DH_METHOD
for the key can have unexpected results.
.PP
\&\fBDH_new_method()\fR allocates and initializes a DH structure so that \fBengine\fR will
be used for the DH operations. If \fBengine\fR is NULL, the default ENGINE for DH
operations is used, and if no default ENGINE is set, the DH_METHOD controlled by
\&\fBDH_set_default_method()\fR is used.
.PP
A new DH_METHOD object may be constructed using \fBDH_meth_new()\fR (see
\&\fBDH_meth_new\fR\|(3)).
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBDH_OpenSSL()\fR and \fBDH_get_default_method()\fR return pointers to the respective
\&\fBDH_METHOD\fRs.
.PP
\&\fBDH_set_default_method()\fR returns no value.
.PP
\&\fBDH_set_method()\fR returns nonzero if the provided \fBmeth\fR was successfully set as
the method for \fBdh\fR (including unloading the ENGINE handle if the previous
method was supplied by an ENGINE).
.PP
\&\fBDH_new_method()\fR returns NULL and sets an error code that can be obtained by
\&\fBERR_get_error\fR\|(3) if the allocation fails. Otherwise it
returns a pointer to the newly allocated structure.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBDH_new\fR\|(3), \fBDH_new\fR\|(3), \fBDH_meth_new\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
All of these functions were deprecated in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
