.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "EVP_PKEY_CTX_SET_HKDF_MD 3ossl"
.TH EVP_PKEY_CTX_SET_HKDF_MD 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
EVP_PKEY_CTX_set_hkdf_md, EVP_PKEY_CTX_set1_hkdf_salt,
EVP_PKEY_CTX_set1_hkdf_key, EVP_PKEY_CTX_add1_hkdf_info,
EVP_PKEY_CTX_set_hkdf_mode \-
HMAC\-based Extract\-and\-Expand key derivation algorithm
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/kdf.h>
\&
\& int EVP_PKEY_CTX_set_hkdf_mode(EVP_PKEY_CTX *pctx, int mode);
\&
\& int EVP_PKEY_CTX_set_hkdf_md(EVP_PKEY_CTX *pctx, const EVP_MD *md);
\&
\& int EVP_PKEY_CTX_set1_hkdf_salt(EVP_PKEY_CTX *pctx, unsigned char *salt,
\&                                 int saltlen);
\&
\& int EVP_PKEY_CTX_set1_hkdf_key(EVP_PKEY_CTX *pctx, unsigned char *key,
\&                                int keylen);
\&
\& int EVP_PKEY_CTX_add1_hkdf_info(EVP_PKEY_CTX *pctx, unsigned char *info,
\&                                 int infolen);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
The EVP_PKEY_HKDF algorithm implements the HKDF key derivation function.
HKDF follows the "extract-then-expand" paradigm, where the KDF logically
consists of two modules. The first stage takes the input keying material
and "extracts" from it a fixed-length pseudorandom key K. The second stage
"expands" the key K into several additional pseudorandom keys (the output
of the KDF).
.PP
\&\fBEVP_PKEY_CTX_set_hkdf_mode()\fR sets the mode for the HKDF operation. There
are three modes that are currently defined:
.IP EVP_PKEY_HKDEF_MODE_EXTRACT_AND_EXPAND 4
.IX Item "EVP_PKEY_HKDEF_MODE_EXTRACT_AND_EXPAND"
This is the default mode. Calling \fBEVP_PKEY_derive\fR\|(3) on an EVP_PKEY_CTX set
up for HKDF will perform an extract followed by an expand operation in one go.
The derived key returned will be the result after the expand operation. The
intermediate fixed-length pseudorandom key K is not returned.
.Sp
In this mode the digest, key, salt and info values must be set before a key is
derived or an error occurs.
.IP EVP_PKEY_HKDEF_MODE_EXTRACT_ONLY 4
.IX Item "EVP_PKEY_HKDEF_MODE_EXTRACT_ONLY"
In this mode calling \fBEVP_PKEY_derive\fR\|(3) will just perform the extract
operation. The value returned will be the intermediate fixed-length pseudorandom
key K.
.Sp
The digest, key and salt values must be set before a key is derived or an
error occurs.
.IP EVP_PKEY_HKDEF_MODE_EXPAND_ONLY 4
.IX Item "EVP_PKEY_HKDEF_MODE_EXPAND_ONLY"
In this mode calling \fBEVP_PKEY_derive\fR\|(3) will just perform the expand
operation. The input key should be set to the intermediate fixed-length
pseudorandom key K returned from a previous extract operation.
.Sp
The digest, key and info values must be set before a key is derived or an
error occurs.
.PP
\&\fBEVP_PKEY_CTX_set_hkdf_md()\fR sets the message digest associated with the HKDF.
.PP
\&\fBEVP_PKEY_CTX_set1_hkdf_salt()\fR sets the salt to \fBsaltlen\fR bytes of the
buffer \fBsalt\fR. Any existing value is replaced.
.PP
\&\fBEVP_PKEY_CTX_set1_hkdf_key()\fR sets the key to \fBkeylen\fR bytes of the buffer
\&\fBkey\fR. Any existing value is replaced.
.PP
\&\fBEVP_PKEY_CTX_add1_hkdf_info()\fR sets the info value to \fBinfolen\fR bytes of the
buffer \fBinfo\fR. If a value is already set, it is appended to the existing
value.
.SH "STRING CTRLS"
.IX Header "STRING CTRLS"
HKDF also supports string based control operations via
\&\fBEVP_PKEY_CTX_ctrl_str\fR\|(3).
The \fBtype\fR parameter "md" uses the supplied \fBvalue\fR as the name of the digest
algorithm to use.
The \fBtype\fR parameter "mode" uses the values "EXTRACT_AND_EXPAND",
"EXTRACT_ONLY" and "EXPAND_ONLY" to determine the mode to use.
The \fBtype\fR parameters "salt", "key" and "info" use the supplied \fBvalue\fR
parameter as a \fBseed\fR, \fBkey\fR or \fBinfo\fR value.
The names "hexsalt", "hexkey" and "hexinfo" are similar except they take a hex
string which is converted to binary.
.SH NOTES
.IX Header "NOTES"
A context for HKDF can be obtained by calling:
.PP
.Vb 1
\& EVP_PKEY_CTX *pctx = EVP_PKEY_CTX_new_id(EVP_PKEY_HKDF, NULL);
.Ve
.PP
The total length of the info buffer cannot exceed 2048 bytes in length: this
should be more than enough for any normal use of HKDF.
.PP
The output length of an HKDF expand operation is specified via the length
parameter to the \fBEVP_PKEY_derive\fR\|(3) function.
Since the HKDF output length is variable, passing a \fBNULL\fR buffer as a means
to obtain the requisite length is not meaningful with HKDF in any mode that
performs an expand operation. Instead, the caller must allocate a buffer of the
desired length, and pass that buffer to \fBEVP_PKEY_derive\fR\|(3) along with (a
pointer initialized to) the desired length. Passing a \fBNULL\fR buffer to obtain
the length is allowed when using EVP_PKEY_HKDEF_MODE_EXTRACT_ONLY.
.PP
Optimised versions of HKDF can be implemented in an ENGINE.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
All these functions return 1 for success and 0 or a negative value for failure.
In particular a return value of \-2 indicates the operation is not supported by
the public key algorithm.
.SH EXAMPLES
.IX Header "EXAMPLES"
This example derives 10 bytes using SHA\-256 with the secret key "secret",
salt value "salt" and info value "label":
.PP
.Vb 4
\& EVP_PKEY_CTX *pctx;
\& unsigned char out[10];
\& size_t outlen = sizeof(out);
\& pctx = EVP_PKEY_CTX_new_id(EVP_PKEY_HKDF, NULL);
\&
\& if (EVP_PKEY_derive_init(pctx) <= 0)
\&     /* Error */
\& if (EVP_PKEY_CTX_set_hkdf_md(pctx, EVP_sha256()) <= 0)
\&     /* Error */
\& if (EVP_PKEY_CTX_set1_hkdf_salt(pctx, "salt", 4) <= 0)
\&     /* Error */
\& if (EVP_PKEY_CTX_set1_hkdf_key(pctx, "secret", 6) <= 0)
\&     /* Error */
\& if (EVP_PKEY_CTX_add1_hkdf_info(pctx, "label", 5) <= 0)
\&     /* Error */
\& if (EVP_PKEY_derive(pctx, out, &outlen) <= 0)
\&     /* Error */
.Ve
.SH "CONFORMING TO"
.IX Header "CONFORMING TO"
RFC 5869
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBEVP_PKEY_CTX_new\fR\|(3),
\&\fBEVP_PKEY_CTX_ctrl_str\fR\|(3),
\&\fBEVP_PKEY_derive\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
All of the functions described here were converted from macros to functions in
OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2016\-2022 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
