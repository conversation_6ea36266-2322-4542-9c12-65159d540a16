.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "CMS_ENCRYPT 3ossl"
.TH CMS_ENCRYPT 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
CMS_encrypt_ex, CMS_encrypt \- create a CMS envelopedData structure
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/cms.h>
\&
\& CMS_ContentInfo *CMS_encrypt_ex(STACK_OF(X509) *certs, BIO *in,
\&                                 const EVP_CIPHER *cipher, unsigned int flags,
\&                                 OSSL_LIB_CTX *libctx, const char *propq);
\& CMS_ContentInfo *CMS_encrypt(STACK_OF(X509) *certs, BIO *in,
\&                              const EVP_CIPHER *cipher, unsigned int flags);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBCMS_encrypt_ex()\fR creates and returns a CMS EnvelopedData or
AuthEnvelopedData structure. \fIcerts\fR is a list of recipient certificates.
\&\fIin\fR is the content to be encrypted. \fIcipher\fR is the symmetric cipher to use.
\&\fIflags\fR is an optional set of flags. The library context \fIlibctx\fR and the
property query \fIpropq\fR are used internally when retrieving algorithms from
providers.
.PP
Only certificates carrying RSA, Diffie-Hellman or EC keys are supported by this
function.
.PP
\&\fBEVP_des_ede3_cbc()\fR (triple DES) is the algorithm of choice for S/MIME use
because most clients will support it.
.PP
The algorithm passed in the \fBcipher\fR parameter must support ASN1 encoding of
its parameters. If the cipher mode is GCM, then an AuthEnvelopedData structure
containing MAC is used. Otherwise an EnvelopedData structure is used. Currently
the AES variants with GCM mode are the only supported AEAD algorithms.
.PP
Many browsers implement a "sign and encrypt" option which is simply an S/MIME
envelopedData containing an S/MIME signed message. This can be readily produced
by storing the S/MIME signed message in a memory BIO and passing it to
\&\fBCMS_encrypt()\fR.
.PP
The following flags can be passed in the \fBflags\fR parameter.
.PP
If the \fBCMS_TEXT\fR flag is set MIME headers for type \fBtext/plain\fR are
prepended to the data.
.PP
Normally the supplied content is translated into MIME canonical format (as
required by the S/MIME specifications) if \fBCMS_BINARY\fR is set no translation
occurs. This option should be used if the supplied data is in binary format
otherwise the translation will corrupt it. If \fBCMS_BINARY\fR is set then
\&\fBCMS_TEXT\fR is ignored.
.PP
OpenSSL will by default identify recipient certificates using issuer name
and serial number. If \fBCMS_USE_KEYID\fR is set it will use the subject key
identifier value instead. An error occurs if all recipient certificates do not
have a subject key identifier extension.
.PP
If the \fBCMS_STREAM\fR flag is set a partial \fBCMS_ContentInfo\fR structure is
returned suitable for streaming I/O: no data is read from the BIO \fBin\fR.
.PP
If the \fBCMS_PARTIAL\fR flag is set a partial \fBCMS_ContentInfo\fR structure is
returned to which additional recipients and attributes can be added before
finalization.
.PP
The data being encrypted is included in the CMS_ContentInfo structure, unless
\&\fBCMS_DETACHED\fR is set in which case it is omitted. This is rarely used in
practice and is not supported by \fBSMIME_write_CMS()\fR.
.PP
If the flag \fBCMS_STREAM\fR is set the returned \fBCMS_ContentInfo\fR structure is
\&\fBnot\fR complete and outputting its contents via a function that does not
properly finalize the \fBCMS_ContentInfo\fR structure will give unpredictable
results.
.PP
Several functions including \fBSMIME_write_CMS()\fR, \fBi2d_CMS_bio_stream()\fR,
\&\fBPEM_write_bio_CMS_stream()\fR finalize the structure. Alternatively finalization
can be performed by obtaining the streaming ASN1 \fBBIO\fR directly using
\&\fBBIO_new_CMS()\fR.
.PP
The recipients specified in \fBcerts\fR use a CMS KeyTransRecipientInfo info
structure. KEKRecipientInfo is also supported using the flag \fBCMS_PARTIAL\fR
and \fBCMS_add0_recipient_key()\fR.
.PP
The parameter \fBcerts\fR may be NULL if \fBCMS_PARTIAL\fR is set and recipients
added later using \fBCMS_add1_recipient_cert()\fR or \fBCMS_add0_recipient_key()\fR.
.PP
\&\fBCMS_encrypt()\fR is similar to \fBCMS_encrypt_ex()\fR but uses default values
of NULL for the library context \fIlibctx\fR and the property query \fIpropq\fR.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBCMS_encrypt_ex()\fR and \fBCMS_encrypt()\fR return either a CMS_ContentInfo
structure or NULL if an error occurred. The error can be obtained from
\&\fBERR_get_error\fR\|(3).
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBERR_get_error\fR\|(3), \fBCMS_decrypt\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
The function \fBCMS_encrypt_ex()\fR was added in OpenSSL 3.0.
.PP
The \fBCMS_STREAM\fR flag was first supported in OpenSSL 1.0.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2008\-2020 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
