.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "OSSL_TRACE_SET_CHANNEL 3ossl"
.TH OSSL_TRACE_SET_CHANNEL 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
OSSL_trace_set_channel, OSSL_trace_set_prefix, OSSL_trace_set_suffix,
OSSL_trace_set_callback, OSSL_trace_cb \- Enabling trace output
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/trace.h>
\&
\& typedef size_t (*OSSL_trace_cb)(const char *buf, size_t cnt,
\&                                 int category, int cmd, void *data);
\&
\& void OSSL_trace_set_channel(int category, BIO *bio);
\& void OSSL_trace_set_prefix(int category, const char *prefix);
\& void OSSL_trace_set_suffix(int category, const char *suffix);
\& void OSSL_trace_set_callback(int category, OSSL_trace_cb cb, void  *data);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
If available (see "NOTES" below), the application can request
internal trace output.
This output comes in form of free text for humans to read.
.PP
The trace output is divided into categories which can be
enabled individually.
Every category can be enabled individually by attaching a so called
\&\fItrace channel\fR to it, which in the simplest case is just a BIO object
to which the application can write the tracing output for this category.
Alternatively, the application can provide a tracer callback in order to
get more finegrained trace information. This callback will be wrapped
internally by a dedicated BIO object.
.PP
For the tracing code, both trace channel types are indistinguishable.
These are called a \fIsimple trace channel\fR and a \fIcallback trace channel\fR,
respectively.
.SS Functions
.IX Subsection "Functions"
\&\fBOSSL_trace_set_channel()\fR is used to enable the given trace \f(CW\*(C`category\*(C'\fR
by attaching the \fBBIO\fR \fIbio\fR object as (simple) trace channel.
On success the ownership of the BIO is transferred to the channel,
so the caller must not free it directly.
.PP
\&\fBOSSL_trace_set_prefix()\fR and \fBOSSL_trace_set_suffix()\fR can be used to add
an extra line for each channel, to be output before and after group of
tracing output.
What constitutes an output group is decided by the code that produces
the output.
The lines given here are considered immutable; for more dynamic
tracing prefixes, consider setting a callback with
\&\fBOSSL_trace_set_callback()\fR instead.
.PP
\&\fBOSSL_trace_set_callback()\fR is used to enable the given trace
\&\fIcategory\fR by giving it the tracer callback \fIcb\fR with the associated
data \fIdata\fR, which will simply be passed through to \fIcb\fR whenever
it's called. The callback function is internally wrapped by a
dedicated BIO object, the so called \fIcallback trace channel\fR.
This should be used when it's desirable to do form the trace output to
something suitable for application needs where a prefix and suffix
line aren't enough.
.PP
\&\fBOSSL_trace_set_channel()\fR and \fBOSSL_trace_set_callback()\fR are mutually
exclusive, calling one of them will clear whatever was set by the
previous call.
.PP
Calling \fBOSSL_trace_set_channel()\fR with NULL for \fIchannel\fR or
\&\fBOSSL_trace_set_callback()\fR with NULL for \fIcb\fR disables tracing for
the given \fIcategory\fR.
.SS "Trace callback"
.IX Subsection "Trace callback"
The tracer callback must return a \fBsize_t\fR, which must be zero on
error and otherwise return the number of bytes that were output.
It receives a text buffer \fIbuf\fR with \fIcnt\fR bytes of text, as well as
the \fIcategory\fR, a control number \fIcmd\fR, and the \fIdata\fR that was
passed to \fBOSSL_trace_set_callback()\fR.
.PP
The possible control numbers are:
.IP \fBOSSL_TRACE_CTRL_BEGIN\fR 4
.IX Item "OSSL_TRACE_CTRL_BEGIN"
The callback is called from \fBOSSL_trace_begin()\fR, which gives the
callback the possibility to output a dynamic starting line, or set a
prefix that should be output at the beginning of each line, or
something other.
.IP \fBOSSL_TRACE_CTRL_WRITE\fR 4
.IX Item "OSSL_TRACE_CTRL_WRITE"
This callback is called whenever data is written to the BIO by some
regular BIO output routine.
An arbitrary number of \fBOSSL_TRACE_CTRL_WRITE\fR callbacks can occur
inside a group marked by a pair of \fBOSSL_TRACE_CTRL_BEGIN\fR and
\&\fBOSSL_TRACE_CTRL_END\fR calls, but never outside such a group.
.IP \fBOSSL_TRACE_CTRL_END\fR 4
.IX Item "OSSL_TRACE_CTRL_END"
The callback is called from \fBOSSL_trace_end()\fR, which gives the callback
the possibility to output a dynamic ending line, or reset the line
prefix that was set with \fBOSSL_TRACE_CTRL_BEGIN\fR, or something other.
.SS "Trace categories"
.IX Subsection "Trace categories"
The trace categories are simple numbers available through macros.
.IP \fBOSSL_TRACE_CATEGORY_TRACE\fR 4
.IX Item "OSSL_TRACE_CATEGORY_TRACE"
Traces the OpenSSL trace API itself.
.Sp
More precisely, this will generate trace output any time a new
trace hook is set.
.IP \fBOSSL_TRACE_CATEGORY_INIT\fR 4
.IX Item "OSSL_TRACE_CATEGORY_INIT"
Traces OpenSSL library initialization and cleanup.
.Sp
This needs special care, as OpenSSL will do automatic cleanup after
exit from \f(CWmain()\fR, and any tracing output done during this cleanup
will be lost if the tracing channel or callback were cleaned away
prematurely.
A suggestion is to make such cleanup part of a function that's
registered very early with \fBatexit\fR\|(3).
.IP \fBOSSL_TRACE_CATEGORY_TLS\fR 4
.IX Item "OSSL_TRACE_CATEGORY_TLS"
Traces the TLS/SSL protocol.
.IP \fBOSSL_TRACE_CATEGORY_TLS_CIPHER\fR 4
.IX Item "OSSL_TRACE_CATEGORY_TLS_CIPHER"
Traces the ciphers used by the TLS/SSL protocol.
.IP \fBOSSL_TRACE_CATEGORY_CONF\fR 4
.IX Item "OSSL_TRACE_CATEGORY_CONF"
Traces details about the provider and engine configuration.
.IP \fBOSSL_TRACE_CATEGORY_ENGINE_TABLE\fR 4
.IX Item "OSSL_TRACE_CATEGORY_ENGINE_TABLE"
Traces the ENGINE algorithm table selection.
.Sp
More precisely, functions like \fBENGINE_get_pkey_asn1_meth_engine()\fR,
\&\fBENGINE_get_pkey_meth_engine()\fR, \fBENGINE_get_cipher_engine()\fR,
\&\fBENGINE_get_digest_engine()\fR, will generate trace summaries of the
handling of internal tables.
.IP \fBOSSL_TRACE_CATEGORY_ENGINE_REF_COUNT\fR 4
.IX Item "OSSL_TRACE_CATEGORY_ENGINE_REF_COUNT"
Traces the ENGINE reference counting.
.Sp
More precisely, both reference counts in the ENGINE structure will be
monitored with a line of trace output generated for each change.
.IP \fBOSSL_TRACE_CATEGORY_PKCS5V2\fR 4
.IX Item "OSSL_TRACE_CATEGORY_PKCS5V2"
Traces PKCS#5 v2 key generation.
.IP \fBOSSL_TRACE_CATEGORY_PKCS12_KEYGEN\fR 4
.IX Item "OSSL_TRACE_CATEGORY_PKCS12_KEYGEN"
Traces PKCS#12 key generation.
.IP \fBOSSL_TRACE_CATEGORY_PKCS12_DECRYPT\fR 4
.IX Item "OSSL_TRACE_CATEGORY_PKCS12_DECRYPT"
Traces PKCS#12 decryption.
.IP \fBOSSL_TRACE_CATEGORY_X509V3_POLICY\fR 4
.IX Item "OSSL_TRACE_CATEGORY_X509V3_POLICY"
Traces X509v3 policy processing.
.Sp
More precisely, this generates the complete policy tree at various
point during evaluation.
.IP \fBOSSL_TRACE_CATEGORY_BN_CTX\fR 4
.IX Item "OSSL_TRACE_CATEGORY_BN_CTX"
Traces BIGNUM context operations.
.IP \fBOSSL_TRACE_CATEGORY_CMP\fR 4
.IX Item "OSSL_TRACE_CATEGORY_CMP"
Traces CMP client and server activity.
.IP \fBOSSL_TRACE_CATEGORY_STORE\fR 4
.IX Item "OSSL_TRACE_CATEGORY_STORE"
Traces STORE operations.
.IP \fBOSSL_TRACE_CATEGORY_DECODER\fR 4
.IX Item "OSSL_TRACE_CATEGORY_DECODER"
Traces decoder operations.
.IP \fBOSSL_TRACE_CATEGORY_ENCODER\fR 4
.IX Item "OSSL_TRACE_CATEGORY_ENCODER"
Traces encoder operations.
.IP \fBOSSL_TRACE_CATEGORY_REF_COUNT\fR 4
.IX Item "OSSL_TRACE_CATEGORY_REF_COUNT"
Traces decrementing certain ASN.1 structure references.
.PP
There is also \fBOSSL_TRACE_CATEGORY_ALL\fR, which works as a fallback
and can be used to get \fIall\fR trace output.
.PP
Note, however, that in this case all trace output will effectively be
associated with the 'ALL' category, which is undesirable if the
application intends to include the category name in the trace output.
In this case it is better to register separate channels for each
trace category instead.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBOSSL_trace_set_channel()\fR, \fBOSSL_trace_set_prefix()\fR,
\&\fBOSSL_trace_set_suffix()\fR, and \fBOSSL_trace_set_callback()\fR return 1 on
success, or 0 on failure.
.SH EXAMPLES
.IX Header "EXAMPLES"
In all examples below, the trace producing code is assumed to be
the following:
.PP
.Vb 3
\& int foo = 42;
\& const char bar[] = { 0,  1,  2,  3,  4,  5,  6,  7,
\&                      8,  9, 10, 11, 12, 13, 14, 15 };
\&
\& OSSL_TRACE_BEGIN(TLS) {
\&     BIO_puts(trc_out, "foo: ");
\&     BIO_printf(trc_out, "%d\en", foo);
\&     BIO_dump(trc_out, bar, sizeof(bar));
\& } OSSL_TRACE_END(TLS);
.Ve
.SS "Simple example"
.IX Subsection "Simple example"
An example with just a channel and constant prefix / suffix.
.PP
.Vb 6
\& int main(int argc, char *argv[])
\& {
\&     BIO *err = BIO_new_fp(stderr, BIO_NOCLOSE | BIO_FP_TEXT);
\&     OSSL_trace_set_channel(OSSL_TRACE_CATEGORY_SSL, err);
\&     OSSL_trace_set_prefix(OSSL_TRACE_CATEGORY_SSL, "BEGIN TRACE[TLS]");
\&     OSSL_trace_set_suffix(OSSL_TRACE_CATEGORY_SSL, "END TRACE[TLS]");
\&
\&     /* ... work ... */
\& }
.Ve
.PP
When the trace producing code above is performed, this will be output
on standard error:
.PP
.Vb 4
\& BEGIN TRACE[TLS]
\& foo: 42
\& 0000 \- 00 01 02 03 04 05 06 07\-08 09 0a 0b 0c 0d 0e 0f   ................
\& END TRACE[TLS]
.Ve
.SS "Advanced example"
.IX Subsection "Advanced example"
This example uses the callback, and depends on pthreads functionality.
.PP
.Vb 5
\& static size_t cb(const char *buf, size_t cnt,
\&                 int category, int cmd, void *vdata)
\& {
\&     BIO *bio = vdata;
\&     const char *label = NULL;
\&
\&     switch (cmd) {
\&     case OSSL_TRACE_CTRL_BEGIN:
\&         label = "BEGIN";
\&         break;
\&     case OSSL_TRACE_CTRL_END:
\&         label = "END";
\&         break;
\&     }
\&
\&     if (label != NULL) {
\&         union {
\&             pthread_t tid;
\&             unsigned long ltid;
\&         } tid;
\&
\&         tid.tid = pthread_self();
\&         BIO_printf(bio, "%s TRACE[%s]:%lx\en",
\&                    label, OSSL_trace_get_category_name(category), tid.ltid);
\&     }
\&     return (size_t)BIO_puts(bio, buf);
\& }
\&
\& int main(int argc, char *argv[])
\& {
\&     BIO *err = BIO_new_fp(stderr, BIO_NOCLOSE | BIO_FP_TEXT);
\&     OSSL_trace_set_callback(OSSL_TRACE_CATEGORY_SSL, cb, err);
\&
\&     /* ... work ... */
\& }
.Ve
.PP
The output is almost the same as for the simple example above.
.PP
.Vb 4
\& BEGIN TRACE[TLS]:7f9eb0193b80
\& foo: 42
\& 0000 \- 00 01 02 03 04 05 06 07\-08 09 0a 0b 0c 0d 0e 0f   ................
\& END TRACE[TLS]:7f9eb0193b80
.Ve
.SH NOTES
.IX Header "NOTES"
.SS "Configure Tracing"
.IX Subsection "Configure Tracing"
By default, the OpenSSL library is built with tracing disabled. To
use the tracing functionality documented here, it is therefore
necessary to configure and build OpenSSL with the 'enable\-trace' option.
.PP
When the library is built with tracing disabled, the macro
\&\fBOPENSSL_NO_TRACE\fR is defined in \fI<openssl/opensslconf.h>\fR and all
functions described here are inoperational, i.e. will do nothing.
.SH HISTORY
.IX Header "HISTORY"
\&\fBOSSL_trace_set_channel()\fR, \fBOSSL_trace_set_prefix()\fR,
\&\fBOSSL_trace_set_suffix()\fR, and \fBOSSL_trace_set_callback()\fR were all added
in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2019\-2023 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
