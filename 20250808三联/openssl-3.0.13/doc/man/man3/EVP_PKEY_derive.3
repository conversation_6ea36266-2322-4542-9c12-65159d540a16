.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "EVP_PKEY_DERIVE 3ossl"
.TH EVP_PKEY_DERIVE 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
EVP_PKEY_derive_init, EVP_PKEY_derive_init_ex,
EVP_PKEY_derive_set_peer_ex, EVP_PKEY_derive_set_peer, EVP_PKEY_derive
\&\- derive public key algorithm shared secret
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/evp.h>
\&
\& int EVP_PKEY_derive_init(EVP_PKEY_CTX *ctx);
\& int EVP_PKEY_derive_init_ex(EVP_PKEY_CTX *ctx, const OSSL_PARAM params[]);
\& int EVP_PKEY_derive_set_peer_ex(EVP_PKEY_CTX *ctx, EVP_PKEY *peer,
\&                                 int validate_peer);
\& int EVP_PKEY_derive_set_peer(EVP_PKEY_CTX *ctx, EVP_PKEY *peer);
\& int EVP_PKEY_derive(EVP_PKEY_CTX *ctx, unsigned char *key, size_t *keylen);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBEVP_PKEY_derive_init()\fR initializes a public key algorithm context \fIctx\fR for
shared secret derivation using the algorithm given when the context was created
using \fBEVP_PKEY_CTX_new\fR\|(3) or variants thereof.  The algorithm is used to
fetch a \fBEVP_KEYEXCH\fR method implicitly, see "Implicit fetch" in \fBprovider\fR\|(7) for
more information about implicit fetches.
.PP
\&\fBEVP_PKEY_derive_init_ex()\fR is the same as \fBEVP_PKEY_derive_init()\fR but additionally
sets the passed parameters \fIparams\fR on the context before returning.
.PP
\&\fBEVP_PKEY_derive_set_peer_ex()\fR sets the peer key: this will normally
be a public key. The \fIvalidate_peer\fR will validate the public key if this value
is non zero.
.PP
\&\fBEVP_PKEY_derive_set_peer()\fR is similar to \fBEVP_PKEY_derive_set_peer_ex()\fR with
\&\fIvalidate_peer\fR set to 1.
.PP
\&\fBEVP_PKEY_derive()\fR derives a shared secret using \fIctx\fR.
If \fIkey\fR is NULL then the maximum size of the output buffer is written to the
\&\fIkeylen\fR parameter. If \fIkey\fR is not NULL then before the call the \fIkeylen\fR
parameter should contain the length of the \fIkey\fR buffer, if the call is
successful the shared secret is written to \fIkey\fR and the amount of data
written to \fIkeylen\fR.
.SH NOTES
.IX Header "NOTES"
After the call to \fBEVP_PKEY_derive_init()\fR, algorithm
specific control operations can be performed to set any appropriate parameters
for the operation.
.PP
The function \fBEVP_PKEY_derive()\fR can be called more than once on the same
context if several operations are performed using the same parameters.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBEVP_PKEY_derive_init()\fR and \fBEVP_PKEY_derive()\fR return 1
for success and 0 or a negative value for failure.
In particular a return value of \-2 indicates the operation is not supported by
the public key algorithm.
.SH EXAMPLES
.IX Header "EXAMPLES"
Derive shared secret (for example DH or EC keys):
.PP
.Vb 2
\& #include <openssl/evp.h>
\& #include <openssl/rsa.h>
\&
\& EVP_PKEY_CTX *ctx;
\& ENGINE *eng;
\& unsigned char *skey;
\& size_t skeylen;
\& EVP_PKEY *pkey, *peerkey;
\& /* NB: assumes pkey, eng, peerkey have been already set up */
\&
\& ctx = EVP_PKEY_CTX_new(pkey, eng);
\& if (!ctx)
\&     /* Error occurred */
\& if (EVP_PKEY_derive_init(ctx) <= 0)
\&     /* Error */
\& if (EVP_PKEY_derive_set_peer(ctx, peerkey) <= 0)
\&     /* Error */
\&
\& /* Determine buffer length */
\& if (EVP_PKEY_derive(ctx, NULL, &skeylen) <= 0)
\&     /* Error */
\&
\& skey = OPENSSL_malloc(skeylen);
\&
\& if (!skey)
\&     /* malloc failure */
\&
\& if (EVP_PKEY_derive(ctx, skey, &skeylen) <= 0)
\&     /* Error */
\&
\& /* Shared secret is skey bytes written to buffer skey */
.Ve
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBEVP_PKEY_CTX_new\fR\|(3),
\&\fBEVP_PKEY_encrypt\fR\|(3),
\&\fBEVP_PKEY_decrypt\fR\|(3),
\&\fBEVP_PKEY_sign\fR\|(3),
\&\fBEVP_PKEY_verify\fR\|(3),
\&\fBEVP_PKEY_verify_recover\fR\|(3),
\&\fBEVP_KEYEXCH_fetch\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
The \fBEVP_PKEY_derive_init()\fR, \fBEVP_PKEY_derive_set_peer()\fR and \fBEVP_PKEY_derive()\fR
functions were originally added in OpenSSL 1.0.0.
.PP
The \fBEVP_PKEY_derive_init_ex()\fR and \fBEVP_PKEY_derive_set_peer_ex()\fR functions were
added in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2006\-2023 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
