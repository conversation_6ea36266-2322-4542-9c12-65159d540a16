.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "EVP_SIGNINIT 3ossl"
.TH EVP_SIGNINIT 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
EVP_SignInit, EVP_SignInit_ex, EVP_SignUpdate,
EVP_SignFinal_ex, EVP_SignFinal
\&\- EVP signing functions
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/evp.h>
\&
\& int EVP_SignInit_ex(EVP_MD_CTX *ctx, const EVP_MD *type, ENGINE *impl);
\& int EVP_SignUpdate(EVP_MD_CTX *ctx, const void *d, unsigned int cnt);
\& int EVP_SignFinal_ex(EVP_MD_CTX *ctx, unsigned char *md, unsigned int *s,
\&                      EVP_PKEY *pkey, OSSL_LIB_CTX *libctx, const char *propq);
\& int EVP_SignFinal(EVP_MD_CTX *ctx, unsigned char *sig, unsigned int *s,
\&                   EVP_PKEY *pkey);
\&
\& void EVP_SignInit(EVP_MD_CTX *ctx, const EVP_MD *type);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
The EVP signature routines are a high-level interface to digital
signatures.
.PP
\&\fBEVP_SignInit_ex()\fR sets up signing context \fIctx\fR to use digest
\&\fItype\fR from \fBENGINE\fR \fIimpl\fR. \fIctx\fR must be created with
\&\fBEVP_MD_CTX_new()\fR before calling this function.
.PP
\&\fBEVP_SignUpdate()\fR hashes \fIcnt\fR bytes of data at \fId\fR into the
signature context \fIctx\fR. This function can be called several times on the
same \fIctx\fR to include additional data.
.PP
\&\fBEVP_SignFinal_ex()\fR signs the data in \fIctx\fR using the private key
\&\fIpkey\fR and places the signature in \fIsig\fR. The library context \fIlibctx\fR and
property query \fIpropq\fR are used when creating a context to use with the key
\&\fIpkey\fR. \fIsig\fR must be at least \f(CWEVP_PKEY_get_size(pkey)\fR bytes in size.
\&\fIs\fR is an OUT parameter, and not used as an IN parameter.
The number of bytes of data written (i.e. the length of the signature)
will be written to the integer at \fIs\fR, at most \f(CWEVP_PKEY_get_size(pkey)\fR
bytes will be written.
.PP
\&\fBEVP_SignFinal()\fR is similar to \fBEVP_SignFinal_ex()\fR but uses default
values of NULL for the library context \fIlibctx\fR and the property query \fIpropq\fR.
.PP
\&\fBEVP_SignInit()\fR initializes a signing context \fIctx\fR to use the default
implementation of digest \fItype\fR.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBEVP_SignInit_ex()\fR, \fBEVP_SignUpdate()\fR, \fBEVP_SignFinal_ex()\fR and
\&\fBEVP_SignFinal()\fR return 1 for success and 0 for failure.
.PP
The error codes can be obtained by \fBERR_get_error\fR\|(3).
.SH NOTES
.IX Header "NOTES"
The \fBEVP\fR interface to digital signatures should almost always be used in
preference to the low-level interfaces. This is because the code then becomes
transparent to the algorithm used and much more flexible.
.PP
When signing with some private key types the random number generator must
be seeded. If the automatic seeding or reseeding of the OpenSSL CSPRNG fails
due to external circumstances (see \fBRAND\fR\|(7)), the operation will fail.
.PP
The call to \fBEVP_SignFinal()\fR internally finalizes a copy of the digest context.
This means that calls to \fBEVP_SignUpdate()\fR and \fBEVP_SignFinal()\fR can be called
later to digest and sign additional data.
.PP
Since only a copy of the digest context is ever finalized the context must
be cleaned up after use by calling \fBEVP_MD_CTX_free()\fR or a memory leak
will occur.
.SH BUGS
.IX Header "BUGS"
Older versions of this documentation wrongly stated that calls to
\&\fBEVP_SignUpdate()\fR could not be made after calling \fBEVP_SignFinal()\fR.
.PP
Since the private key is passed in the call to \fBEVP_SignFinal()\fR any error
relating to the private key (for example an unsuitable key and digest
combination) will not be indicated until after potentially large amounts of
data have been passed through \fBEVP_SignUpdate()\fR.
.PP
It is not possible to change the signing parameters using these function.
.PP
The previous two bugs are fixed in the newer EVP_DigestSign*() functions.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBEVP_PKEY_get_size\fR\|(3), \fBEVP_PKEY_get_bits\fR\|(3),
\&\fBEVP_PKEY_get_security_bits\fR\|(3),
\&\fBEVP_VerifyInit\fR\|(3),
\&\fBEVP_DigestInit\fR\|(3),
\&\fBevp\fR\|(7), \fBHMAC\fR\|(3), \fBMD2\fR\|(3),
\&\fBMD5\fR\|(3), \fBMDC2\fR\|(3), \fBRIPEMD160\fR\|(3),
\&\fBSHA1\fR\|(3), \fBopenssl\-dgst\fR\|(1)
.SH HISTORY
.IX Header "HISTORY"
The function \fBEVP_SignFinal_ex()\fR was added in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
