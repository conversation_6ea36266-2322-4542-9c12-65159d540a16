.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "RSA_SIZE 3ossl"
.TH RSA_SIZE 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
RSA_size, RSA_bits, RSA_security_bits \- get RSA modulus size or security bits
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/rsa.h>
\&
\& int RSA_bits(const RSA *rsa);
.Ve
.PP
The following functions have been deprecated since OpenSSL 3.0, and can be
hidden entirely by defining \fBOPENSSL_API_COMPAT\fR with a suitable version value,
see \fBopenssl_user_macros\fR\|(7):
.PP
.Vb 1
\& int RSA_size(const RSA *rsa);
\&
\& int RSA_security_bits(const RSA *rsa);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBRSA_bits()\fR returns the number of significant bits.
.PP
\&\fBrsa\fR and \fBrsa\->n\fR must not be \fBNULL\fR.
.PP
The remaining functions described on this page are deprecated.
Applications should instead use \fBEVP_PKEY_get_size\fR\|(3), \fBEVP_PKEY_get_bits\fR\|(3)
and \fBEVP_PKEY_get_security_bits\fR\|(3).
.PP
\&\fBRSA_size()\fR returns the RSA modulus size in bytes. It can be used to
determine how much memory must be allocated for an RSA encrypted
value.
.PP
\&\fBRSA_security_bits()\fR returns the number of security bits of the given \fBrsa\fR
key. See \fBBN_security_bits\fR\|(3).
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBRSA_bits()\fR returns the number of bits in the key.
.PP
\&\fBRSA_size()\fR returns the size of modulus in bytes.
.PP
\&\fBRSA_security_bits()\fR returns the number of security bits.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBBN_num_bits\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
The \fBRSA_size()\fR and \fBRSA_security_bits()\fR functions were deprecated in OpenSSL 3.0.
.PP
The \fBRSA_bits()\fR function was added in OpenSSL 1.1.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
