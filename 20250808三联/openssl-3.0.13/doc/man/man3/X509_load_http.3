.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "X509_LOAD_HTTP 3ossl"
.TH X509_LOAD_HTTP 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
X509_load_http,
X509_http_nbio,
X509_CRL_load_http,
X509_CRL_http_nbio
\&\- certificate and CRL loading functions
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/x509.h>
\&
\& X509 *X509_load_http(const char *url, BIO *bio, BIO *rbio, int timeout);
\& X509_CRL *X509_CRL_load_http(const char *url, BIO *bio, BIO *rbio, int timeout);
.Ve
.PP
The following macros have been deprecated since OpenSSL 3.0, and can be
hidden entirely by defining \fBOPENSSL_API_COMPAT\fR with a suitable version value,
see \fBopenssl_user_macros\fR\|(7):
.PP
.Vb 2
\& #define X509_http_nbio(rctx, pcert)
\& #define X509_CRL_http_nbio(rctx, pcrl)
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBX509_load_http()\fR and \fBX509_CRL_load_http()\fR loads a certificate or a CRL,
respectively, in ASN.1 format using HTTP from the given \fBurl\fR.
.PP
If \fBbio\fR is given and \fBrbio\fR is NULL then this BIO is used instead of an
internal one for connecting, writing the request, and reading the response.
If both \fBbio\fR and \fBrbio\fR are given (which may be memory BIOs, for instance)
then no explicit connection is attempted,
\&\fBbio\fR is used for writing the request, and \fBrbio\fR for reading the response.
.PP
If the \fBtimeout\fR parameter is > 0 this indicates the maximum number of seconds
to wait until the transfer is complete.
A value of 0 enables waiting indefinitely,
while a value < 0 immediately leads to a timeout condition.
.PP
\&\fBX509_http_nbio()\fR and \fBX509_CRL_http_nbio()\fR are macros for backward compatibility
that have the same effect as the functions above but with infinite timeout
and without the possibility to specify custom BIOs.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
On success the function yield the loaded value, else NULL.
Error conditions include connection/transfer timeout, parse errors, etc.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBOSSL_HTTP_get\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
\&\fBX509_load_http()\fR and \fBX509_CRL_load_http()\fR were added in OpenSSL 3.0.
\&\fBX509_http_nbio()\fR and \fBX509_CRL_http_nbio()\fR were deprecated in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2019\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
