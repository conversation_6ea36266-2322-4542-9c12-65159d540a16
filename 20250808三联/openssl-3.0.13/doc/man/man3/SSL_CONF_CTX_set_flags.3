.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "SSL_CONF_CTX_SET_FLAGS 3ossl"
.TH SSL_CONF_CTX_SET_FLAGS 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
SSL_CONF_CTX_set_flags, SSL_CONF_CTX_clear_flags \- Set or clear SSL configuration context flags
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/ssl.h>
\&
\& unsigned int SSL_CONF_CTX_set_flags(SSL_CONF_CTX *cctx, unsigned int flags);
\& unsigned int SSL_CONF_CTX_clear_flags(SSL_CONF_CTX *cctx, unsigned int flags);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
The function \fBSSL_CONF_CTX_set_flags()\fR sets \fBflags\fR in the context \fBcctx\fR.
.PP
The function \fBSSL_CONF_CTX_clear_flags()\fR clears \fBflags\fR in the context \fBcctx\fR.
.SH NOTES
.IX Header "NOTES"
The flags set affect how subsequent calls to \fBSSL_CONF_cmd()\fR or
\&\fBSSL_CONF_argv()\fR behave.
.PP
Currently the following \fBflags\fR values are recognised:
.IP "SSL_CONF_FLAG_CMDLINE, SSL_CONF_FLAG_FILE" 4
.IX Item "SSL_CONF_FLAG_CMDLINE, SSL_CONF_FLAG_FILE"
recognise options intended for command line or configuration file use. At
least one of these flags must be set.
.IP "SSL_CONF_FLAG_CLIENT, SSL_CONF_FLAG_SERVER" 4
.IX Item "SSL_CONF_FLAG_CLIENT, SSL_CONF_FLAG_SERVER"
recognise options intended for use in SSL/TLS clients or servers. One or
both of these flags must be set.
.IP SSL_CONF_FLAG_CERTIFICATE 4
.IX Item "SSL_CONF_FLAG_CERTIFICATE"
recognise certificate and private key options.
.IP SSL_CONF_FLAG_REQUIRE_PRIVATE 4
.IX Item "SSL_CONF_FLAG_REQUIRE_PRIVATE"
If this option is set then if a private key is not specified for a certificate
it will attempt to load a private key from the certificate file when
\&\fBSSL_CONF_CTX_finish()\fR is called. If a key cannot be loaded from the certificate
file an error occurs.
.IP SSL_CONF_FLAG_SHOW_ERRORS 4
.IX Item "SSL_CONF_FLAG_SHOW_ERRORS"
indicate errors relating to unrecognised options or missing arguments in
the error queue. If this option isn't set such errors are only reflected
in the return values of \fBSSL_CONF_set_cmd()\fR or \fBSSL_CONF_set_argv()\fR
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBSSL_CONF_CTX_set_flags()\fR and \fBSSL_CONF_CTX_clear_flags()\fR returns the new flags
value after setting or clearing flags.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBssl\fR\|(7),
\&\fBSSL_CONF_CTX_new\fR\|(3),
\&\fBSSL_CONF_CTX_set_ssl_ctx\fR\|(3),
\&\fBSSL_CONF_CTX_set1_prefix\fR\|(3),
\&\fBSSL_CONF_cmd\fR\|(3),
\&\fBSSL_CONF_cmd_argv\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
These functions were added in OpenSSL 1.0.2.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2012\-2016 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
