.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "RSA_GENERATE_KEY 3ossl"
.TH RSA_GENERATE_KEY 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
EVP_RSA_gen,
RSA_generate_key_ex, RSA_generate_key,
RSA_generate_multi_prime_key \- generate RSA key pair
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/rsa.h>
\&
\& EVP_PKEY *EVP_RSA_gen(unsigned int bits);
.Ve
.PP
The following functions have been deprecated since OpenSSL 3.0, and can be
hidden entirely by defining \fBOPENSSL_API_COMPAT\fR with a suitable version value,
see \fBopenssl_user_macros\fR\|(7):
.PP
.Vb 2
\& int RSA_generate_key_ex(RSA *rsa, int bits, BIGNUM *e, BN_GENCB *cb);
\& int RSA_generate_multi_prime_key(RSA *rsa, int bits, int primes, BIGNUM *e, BN_GENCB *cb);
.Ve
.PP
The following function has been deprecated since OpenSSL 0.9.8, and can be
hidden entirely by defining \fBOPENSSL_API_COMPAT\fR with a suitable version value,
see \fBopenssl_user_macros\fR\|(7):
.PP
.Vb 2
\& RSA *RSA_generate_key(int bits, unsigned long e,
\&                       void (*callback)(int, int, void *), void *cb_arg);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBEVP_RSA_gen()\fR generates a new RSA key pair with modulus size \fIbits\fR.
.PP
All of the functions described below are deprecated.
Applications should instead use \fBEVP_RSA_gen()\fR, \fBEVP_PKEY_Q_keygen\fR\|(3), or
\&\fBEVP_PKEY_keygen_init\fR\|(3) and \fBEVP_PKEY_keygen\fR\|(3).
.PP
\&\fBRSA_generate_key_ex()\fR generates a 2\-prime RSA key pair and stores it in the
\&\fBRSA\fR structure provided in \fIrsa\fR.
.PP
\&\fBRSA_generate_multi_prime_key()\fR generates a multi-prime RSA key pair and stores
it in the \fBRSA\fR structure provided in \fIrsa\fR. The number of primes is given by
the \fIprimes\fR parameter.
If the automatic seeding or reseeding of the OpenSSL CSPRNG fails due to
external circumstances (see \fBRAND\fR\|(7)), the operation will fail.
.PP
The modulus size will be of length \fIbits\fR, the number of primes to form the
modulus will be \fIprimes\fR, and the public exponent will be \fIe\fR. Key sizes
with \fInum\fR < 1024 should be considered insecure. The exponent is an odd
number, typically 3, 17 or 65537.
.PP
In order to maintain adequate security level, the maximum number of permitted
\&\fIprimes\fR depends on modulus bit length:
.PP
.Vb 3
\&   <1024 | >=1024 | >=4096 | >=8192
\&   \-\-\-\-\-\-+\-\-\-\-\-\-\-\-+\-\-\-\-\-\-\-\-+\-\-\-\-\-\-\-
\&     2   |   3    |   4    |   5
.Ve
.PP
A callback function may be used to provide feedback about the
progress of the key generation. If \fIcb\fR is not NULL, it
will be called as follows using the \fBBN_GENCB_call()\fR function
described on the \fBBN_generate_prime\fR\|(3) page.
.PP
\&\fBRSA_generate_key()\fR is similar to \fBRSA_generate_key_ex()\fR but
expects an old-style callback function; see
\&\fBBN_generate_prime\fR\|(3) for information on the old-style callback.
.IP \(bu 2
While a random prime number is generated, it is called as
described in \fBBN_generate_prime\fR\|(3).
.IP \(bu 2
When the n\-th randomly generated prime is rejected as not
suitable for the key, \fIBN_GENCB_call(cb, 2, n)\fR is called.
.IP \(bu 2
When a random p has been found with p\-1 relatively prime to \fIe\fR,
it is called as \fIBN_GENCB_call(cb, 3, 0)\fR.
.PP
The process is then repeated for prime q and other primes (if any)
with \fIBN_GENCB_call(cb, 3, i)\fR where \fIi\fR indicates the i\-th prime.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBEVP_RSA_gen()\fR returns an \fIEVP_PKEY\fR or NULL on failure.
.PP
\&\fBRSA_generate_multi_prime_key()\fR returns 1 on success or 0 on error.
\&\fBRSA_generate_key_ex()\fR returns 1 on success or 0 on error.
The error codes can be obtained by \fBERR_get_error\fR\|(3).
.PP
\&\fBRSA_generate_key()\fR returns a pointer to the RSA structure or
NULL if the key generation fails.
.SH BUGS
.IX Header "BUGS"
\&\fIBN_GENCB_call(cb, 2, x)\fR is used with two different meanings.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBEVP_PKEY_Q_keygen\fR\|(3)
\&\fBBN_generate_prime\fR\|(3), \fBERR_get_error\fR\|(3),
\&\fBRAND_bytes\fR\|(3), \fBRAND\fR\|(7)
.SH HISTORY
.IX Header "HISTORY"
\&\fBEVP_RSA_gen()\fR was added in OpenSSL 3.0.
All other functions described here were deprecated in OpenSSL 3.0.
For replacement see \fBEVP_PKEY\-RSA\fR\|(7).
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
