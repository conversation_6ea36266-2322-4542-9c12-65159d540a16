.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "NCONF_NEW_EX 3ossl"
.TH NCONF_NEW_EX 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
NCONF_new_ex, NCONF_new, NCONF_free, NCONF_default, NCONF_load,
NCONF_get0_libctx, NCONF_get_section, NCONF_get_section_names
\&\- functionality to Load and parse configuration files manually
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/conf.h>
\&
\& typedef struct {
\&     char *section;
\&     char *name;
\&     char *value;
\& } CONF_VALUE;
\&
\& CONF *NCONF_new_ex(OSSL_LIB_CTX *libctx, CONF_METHOD *meth);
\& CONF *NCONF_new(CONF_METHOD *meth);
\& void NCONF_free(CONF *conf);
\& CONF_METHOD *NCONF_default(void);
\& int NCONF_load(CONF *conf, const char *file, long *eline);
\& OSSL_LIB_CTX *NCONF_get0_libctx(const CONF *conf);
\&
\& STACK_OF(CONF_VALUE) *NCONF_get_section(const CONF *conf, const char *name);
\& STACK_OF(OPENSSL_CSTRING) *NCONF_get_section_names(const CONF *conf);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBNCONF_new_ex()\fR creates a new CONF object in heap memory and assigns to
it a context \fIlibctx\fR that can be used during loading. If the method table
\&\fImeth\fR is set to NULL then the default value of \fBNCONF_default()\fR is used.
.PP
\&\fBNCONF_new()\fR is similar to \fBNCONF_new_ex()\fR but sets the \fIlibctx\fR to NULL.
.PP
\&\fBNCONF_free()\fR frees the data associated with \fIconf\fR and then frees the \fIconf\fR
object.
.PP
\&\fBNCONF_load()\fR parses the file named \fIfilename\fR and adds the values found to
\&\fIconf\fR. If an error occurs \fIfile\fR and \fIeline\fR list the file and line that
the load failed on if they are not NULL.
.PP
\&\fBNCONF_default()\fR gets the default method table for processing a configuration file.
.PP
\&\fBNCONF_get0_libctx()\fR gets the library context associated with the \fIconf\fR
parameter.
.PP
\&\fBNCONF_get_section_names()\fR gets the names of the sections associated with
the \fIconf\fR as \fBSTACK_OF(OPENSSL_CSTRING)\fR strings. The individual strings
are associated with the \fIconf\fR and will be invalid after \fIconf\fR is
freed. The returned stack must be freed with \fBsk_OPENSSL_CSTRING_free()\fR.
.PP
\&\fBNCONF_get_section()\fR gets the config values associated with the \fIconf\fR from
the config section \fIname\fR as \fBSTACK_OF(CONF_VALUE)\fR structures. The returned
stack is associated with the \fIconf\fR and will be invalid after \fIconf\fR
is freed. It must not be freed by the caller.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBNCONF_load()\fR returns 1 on success or 0 on error.
.PP
\&\fBNCONF_new_ex()\fR and \fBNCONF_new()\fR return a newly created \fICONF\fR object
or NULL if an error occurs.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBCONF_modules_load_file\fR\|(3),
.SH HISTORY
.IX Header "HISTORY"
\&\fBNCONF_new_ex()\fR, \fBNCONF_get0_libctx()\fR, and \fBNCONF_get_section_names()\fR were added
in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2020\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
