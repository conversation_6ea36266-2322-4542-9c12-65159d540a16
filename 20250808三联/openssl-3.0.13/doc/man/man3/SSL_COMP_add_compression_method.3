.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "SSL_COMP_ADD_COMPRESSION_METHOD 3ossl"
.TH SSL_COMP_ADD_COMPRESSION_METHOD 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
SSL_COMP_add_compression_method, SSL_COMP_get_compression_methods,
SSL_COMP_get0_name, SSL_COMP_get_id, SSL_COMP_free_compression_methods
\&\- handle SSL/TLS integrated compression methods
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/ssl.h>
\&
\& int SSL_COMP_add_compression_method(int id, COMP_METHOD *cm);
\& STACK_OF(SSL_COMP) *SSL_COMP_get_compression_methods(void);
\& const char *SSL_COMP_get0_name(const SSL_COMP *comp);
\& int SSL_COMP_get_id(const SSL_COMP *comp);
.Ve
.PP
The following function has been deprecated since OpenSSL 1.1.0, and can be
hidden entirely by defining \fBOPENSSL_API_COMPAT\fR with a suitable version value,
see \fBopenssl_user_macros\fR\|(7):
.PP
.Vb 1
\& void SSL_COMP_free_compression_methods(void);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBSSL_COMP_add_compression_method()\fR adds the compression method \fBcm\fR with
the identifier \fBid\fR to the list of available compression methods. This
list is globally maintained for all SSL operations within this application.
It cannot be set for specific SSL_CTX or SSL objects.
.PP
\&\fBSSL_COMP_get_compression_methods()\fR returns a stack of all of the available
compression methods or NULL on error.
.PP
\&\fBSSL_COMP_get0_name()\fR returns the name of the compression method \fBcomp\fR.
.PP
\&\fBSSL_COMP_get_id()\fR returns the id of the compression method \fBcomp\fR.
.PP
\&\fBSSL_COMP_free_compression_methods()\fR releases any resources acquired to
maintain the internal table of compression methods.
.SH NOTES
.IX Header "NOTES"
The TLS standard (or SSLv3) allows the integration of compression methods
into the communication. The TLS RFC does however not specify compression
methods or their corresponding identifiers, so there is currently no compatible
way to integrate compression with unknown peers. It is therefore currently not
recommended to integrate compression into applications. Applications for
non-public use may agree on certain compression methods. Using different
compression methods with the same identifier will lead to connection failure.
.PP
An OpenSSL client speaking a protocol that allows compression (SSLv3, TLSv1)
will unconditionally send the list of all compression methods enabled with
\&\fBSSL_COMP_add_compression_method()\fR to the server during the handshake.
Unlike the mechanisms to set a cipher list, there is no method available to
restrict the list of compression method on a per connection basis.
.PP
An OpenSSL server will match the identifiers listed by a client against
its own compression methods and will unconditionally activate compression
when a matching identifier is found. There is no way to restrict the list
of compression methods supported on a per connection basis.
.PP
If enabled during compilation, the OpenSSL library will have the
\&\fBCOMP_zlib()\fR compression method available.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBSSL_COMP_add_compression_method()\fR may return the following values:
.IP 0 4
The operation succeeded.
.IP 1 4
.IX Item "1"
The operation failed. Check the error queue to find out the reason.
.PP
\&\fBSSL_COMP_get_compression_methods()\fR returns the stack of compressions methods or
NULL on error.
.PP
\&\fBSSL_COMP_get0_name()\fR returns the name of the compression method or NULL on error.
.PP
\&\fBSSL_COMP_get_id()\fR returns the name of the compression method or \-1 on error.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBssl\fR\|(7)
.SH HISTORY
.IX Header "HISTORY"
The \fBSSL_COMP_free_compression_methods()\fR function was deprecated in OpenSSL 1.1.0.
The \fBSSL_COMP_get0_name()\fR and \fBSSL_comp_get_id()\fR functions were added in OpenSSL 1.1.0d.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2001\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
