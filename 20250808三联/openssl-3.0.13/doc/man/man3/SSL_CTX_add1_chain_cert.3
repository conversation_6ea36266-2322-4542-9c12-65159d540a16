.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "SSL_CTX_ADD1_CHAIN_CERT 3ossl"
.TH SSL_CTX_ADD1_CHAIN_CERT 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
SSL_CTX_set0_chain, SSL_CTX_set1_chain, SSL_CTX_add0_chain_cert,
SSL_CTX_add1_chain_cert, SSL_CTX_get0_chain_certs, SSL_CTX_clear_chain_certs,
SSL_set0_chain, SSL_set1_chain, SSL_add0_chain_cert, SSL_add1_chain_cert,
SSL_get0_chain_certs, SSL_clear_chain_certs, SSL_CTX_build_cert_chain,
SSL_build_cert_chain, SSL_CTX_select_current_cert,
SSL_select_current_cert, SSL_CTX_set_current_cert, SSL_set_current_cert \- extra
chain certificate processing
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/ssl.h>
\&
\& int SSL_CTX_set0_chain(SSL_CTX *ctx, STACK_OF(X509) *sk);
\& int SSL_CTX_set1_chain(SSL_CTX *ctx, STACK_OF(X509) *sk);
\& int SSL_CTX_add0_chain_cert(SSL_CTX *ctx, X509 *x509);
\& int SSL_CTX_add1_chain_cert(SSL_CTX *ctx, X509 *x509);
\& int SSL_CTX_get0_chain_certs(SSL_CTX *ctx, STACK_OF(X509) **sk);
\& int SSL_CTX_clear_chain_certs(SSL_CTX *ctx);
\&
\& int SSL_set0_chain(SSL *ssl, STACK_OF(X509) *sk);
\& int SSL_set1_chain(SSL *ssl, STACK_OF(X509) *sk);
\& int SSL_add0_chain_cert(SSL *ssl, X509 *x509);
\& int SSL_add1_chain_cert(SSL *ssl, X509 *x509);
\& int SSL_get0_chain_certs(SSL *ssl, STACK_OF(X509) **sk);
\& int SSL_clear_chain_certs(SSL *ssl);
\&
\& int SSL_CTX_build_cert_chain(SSL_CTX *ctx, flags);
\& int SSL_build_cert_chain(SSL *ssl, flags);
\&
\& int SSL_CTX_select_current_cert(SSL_CTX *ctx, X509 *x509);
\& int SSL_select_current_cert(SSL *ssl, X509 *x509);
\& int SSL_CTX_set_current_cert(SSL_CTX *ctx, long op);
\& int SSL_set_current_cert(SSL *ssl, long op);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBSSL_CTX_set0_chain()\fR and \fBSSL_CTX_set1_chain()\fR set the certificate chain
associated with the current certificate of \fBctx\fR to \fBsk\fR.
.PP
\&\fBSSL_CTX_add0_chain_cert()\fR and \fBSSL_CTX_add1_chain_cert()\fR append the single
certificate \fBx509\fR to the chain associated with the current certificate of
\&\fBctx\fR.
.PP
\&\fBSSL_CTX_get0_chain_certs()\fR retrieves the chain associated with the current
certificate of \fBctx\fR.
.PP
\&\fBSSL_CTX_clear_chain_certs()\fR clears any existing chain associated with the
current certificate of \fBctx\fR.  (This is implemented by calling
\&\fBSSL_CTX_set0_chain()\fR with \fBsk\fR set to \fBNULL\fR).
.PP
\&\fBSSL_CTX_build_cert_chain()\fR builds the certificate chain for \fBctx\fR.
Normally this uses the chain store
or the verify store if the chain store is not set.
If the function is successful the built chain will replace any existing chain.
The \fBflags\fR parameter can be set to \fBSSL_BUILD_CHAIN_FLAG_UNTRUSTED\fR to use
existing chain certificates as untrusted CAs, \fBSSL_BUILD_CHAIN_FLAG_NO_ROOT\fR
to omit the root CA from the built chain, \fBSSL_BUILD_CHAIN_FLAG_CHECK\fR to
use all existing chain certificates only to build the chain (effectively
sanity checking and rearranging them if necessary), the flag
\&\fBSSL_BUILD_CHAIN_FLAG_IGNORE_ERROR\fR ignores any errors during verification:
if flag \fBSSL_BUILD_CHAIN_FLAG_CLEAR_ERROR\fR is also set verification errors
are cleared from the error queue.
Details of the chain building process are described in
"Certification Path Building" in \fBopenssl\-verification\-options\fR\|(1).
.PP
Each of these functions operates on the \fIcurrent\fR end entity
(i.e. server or client) certificate. This is the last certificate loaded or
selected on the corresponding \fBctx\fR structure.
.PP
\&\fBSSL_CTX_select_current_cert()\fR selects \fBx509\fR as the current end entity
certificate, but only if \fBx509\fR has already been loaded into \fBctx\fR using a
function such as \fBSSL_CTX_use_certificate()\fR.
.PP
\&\fBSSL_set0_chain()\fR, \fBSSL_set1_chain()\fR, \fBSSL_add0_chain_cert()\fR,
\&\fBSSL_add1_chain_cert()\fR, \fBSSL_get0_chain_certs()\fR, \fBSSL_clear_chain_certs()\fR,
\&\fBSSL_build_cert_chain()\fR, \fBSSL_select_current_cert()\fR and \fBSSL_set_current_cert()\fR
are similar except they apply to SSL structure \fBssl\fR.
.PP
\&\fBSSL_CTX_set_current_cert()\fR changes the current certificate to a value based
on the \fBop\fR argument. Currently \fBop\fR can be \fBSSL_CERT_SET_FIRST\fR to use
the first valid certificate or \fBSSL_CERT_SET_NEXT\fR to set the next valid
certificate after the current certificate. These two operations can be
used to iterate over all certificates in an \fBSSL_CTX\fR structure.
.PP
\&\fBSSL_set_current_cert()\fR also supports the option \fBSSL_CERT_SET_SERVER\fR.
If \fBssl\fR is a server and has sent a certificate to a connected client
this option sets that certificate to the current certificate and returns 1.
If the negotiated cipher suite is anonymous (and thus no certificate will
be sent) 2 is returned and the current certificate is unchanged. If \fBssl\fR
is not a server or a certificate has not been sent 0 is returned and
the current certificate is unchanged.
.PP
All these functions are implemented as macros. Those containing a \fB1\fR
increment the reference count of the supplied certificate or chain so it must
be freed at some point after the operation. Those containing a \fB0\fR do
not increment reference counts and the supplied certificate or chain
\&\fBMUST NOT\fR be freed after the operation.
.SH NOTES
.IX Header "NOTES"
The chains associate with an SSL_CTX structure are copied to any SSL
structures when \fBSSL_new()\fR is called. SSL structures will not be affected
by any chains subsequently changed in the parent SSL_CTX.
.PP
One chain can be set for each key type supported by a server. So, for example,
an RSA and a DSA certificate can (and often will) have different chains.
.PP
The functions \fBSSL_CTX_build_cert_chain()\fR and \fBSSL_build_cert_chain()\fR can
be used to check application configuration and to ensure any necessary
subordinate CAs are sent in the correct order. Misconfigured applications
sending incorrect certificate chains often cause problems with peers.
.PP
For example an application can add any set of certificates using
\&\fBSSL_CTX_use_certificate_chain_file()\fR then call \fBSSL_CTX_build_cert_chain()\fR
with the option \fBSSL_BUILD_CHAIN_FLAG_CHECK\fR to check and reorder them.
.PP
Applications can issue non fatal warnings when checking chains by setting
the flag \fBSSL_BUILD_CHAIN_FLAG_IGNORE_ERRORS\fR and checking the return
value.
.PP
Calling \fBSSL_CTX_build_cert_chain()\fR or \fBSSL_build_cert_chain()\fR is more
efficient than the automatic chain building as it is only performed once.
Automatic chain building is performed on each new session.
.PP
If any certificates are added using these functions no certificates added
using \fBSSL_CTX_add_extra_chain_cert()\fR will be used.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBSSL_set_current_cert()\fR with \fBSSL_CERT_SET_SERVER\fR return 1 for success, 2 if
no server certificate is used because the cipher suites is anonymous and 0
for failure.
.PP
\&\fBSSL_CTX_build_cert_chain()\fR and \fBSSL_build_cert_chain()\fR return 1 for success
and 0 for failure. If the flag \fBSSL_BUILD_CHAIN_FLAG_IGNORE_ERROR\fR and
a verification error occurs then 2 is returned.
.PP
All other functions return 1 for success and 0 for failure.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBssl\fR\|(7),
\&\fBSSL_CTX_add_extra_chain_cert\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
These functions were added in OpenSSL 1.0.2.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2013\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
