.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "BIO_F_BUFFER 3ossl"
.TH BIO_F_BUFFER 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
BIO_get_buffer_num_lines,
BIO_set_read_buffer_size,
BIO_set_write_buffer_size,
BIO_set_buffer_size,
BIO_set_buffer_read_data,
BIO_f_buffer
\&\- buffering BIO
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/bio.h>
\&
\& const BIO_METHOD *BIO_f_buffer(void);
\&
\& long BIO_get_buffer_num_lines(BIO *b);
\& long BIO_set_read_buffer_size(BIO *b, long size);
\& long BIO_set_write_buffer_size(BIO *b, long size);
\& long BIO_set_buffer_size(BIO *b, long size);
\& long BIO_set_buffer_read_data(BIO *b, void *buf, long num);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBBIO_f_buffer()\fR returns the buffering BIO method.
.PP
Data written to a buffering BIO is buffered and periodically written
to the next BIO in the chain. Data read from a buffering BIO comes from
an internal buffer which is filled from the next BIO in the chain.
Both \fBBIO_gets()\fR and \fBBIO_puts()\fR are supported.
.PP
Calling \fBBIO_reset()\fR on a buffering BIO clears any buffered data.
.PP
\&\fBBIO_get_buffer_num_lines()\fR returns the number of lines currently buffered.
.PP
\&\fBBIO_set_read_buffer_size()\fR, \fBBIO_set_write_buffer_size()\fR and \fBBIO_set_buffer_size()\fR
set the read, write or both read and write buffer sizes to \fBsize\fR. The initial
buffer size is DEFAULT_BUFFER_SIZE, currently 4096. Any attempt to reduce the
buffer size below DEFAULT_BUFFER_SIZE is ignored. Any buffered data is cleared
when the buffer is resized.
.PP
\&\fBBIO_set_buffer_read_data()\fR clears the read buffer and fills it with \fBnum\fR
bytes of \fBbuf\fR. If \fBnum\fR is larger than the current buffer size the buffer
is expanded.
.SH NOTES
.IX Header "NOTES"
These functions, other than \fBBIO_f_buffer()\fR, are implemented as macros.
.PP
Buffering BIOs implement \fBBIO_read_ex()\fR and \fBBIO_gets()\fR by using
\&\fBBIO_read_ex()\fR operations on the next BIO in the chain and storing the
result in an internal buffer, from which bytes are given back to the
caller as appropriate for the call; a \fBBIO_gets()\fR is guaranteed to give
the caller a whole line, and \fBBIO_read_ex()\fR is guaranteed to give the
caller the number of bytes it asks for, unless there's an error or end
of communication is reached in the next BIO.  By prepending a
buffering BIO to a chain it is therefore possible to provide
\&\fBBIO_gets()\fR or exact size \fBBIO_read_ex()\fR functionality if the following
BIOs do not support it.
.PP
Do not add more than one \fBBIO_f_buffer()\fR to a BIO chain.  The result of
doing so will force a full read of the size of the internal buffer of
the top \fBBIO_f_buffer()\fR, which is 4 KiB at a minimum.
.PP
Data is only written to the next BIO in the chain when the write buffer fills
or when \fBBIO_flush()\fR is called. It is therefore important to call \fBBIO_flush()\fR
whenever any pending data should be written such as when removing a buffering
BIO using \fBBIO_pop()\fR. \fBBIO_flush()\fR may need to be retried if the ultimate
source/sink BIO is non blocking.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBBIO_f_buffer()\fR returns the buffering BIO method.
.PP
\&\fBBIO_get_buffer_num_lines()\fR returns the number of lines buffered (may be 0) or
a negative value in case of errors.
.PP
\&\fBBIO_set_read_buffer_size()\fR, \fBBIO_set_write_buffer_size()\fR and \fBBIO_set_buffer_size()\fR
return 1 if the buffer was successfully resized or <=0 for failure.
.PP
\&\fBBIO_set_buffer_read_data()\fR returns 1 if the data was set correctly or <=0 if
there was an error.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBbio\fR\|(7),
\&\fBBIO_reset\fR\|(3),
\&\fBBIO_flush\fR\|(3),
\&\fBBIO_pop\fR\|(3),
\&\fBBIO_ctrl\fR\|(3).
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
