.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "EVP_PKEY_CTX_NEW 3ossl"
.TH EVP_PKEY_CTX_NEW 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
EVP_PKEY_CTX_new, EVP_PKEY_CTX_new_id, EVP_PKEY_CTX_new_from_name,
EVP_PKEY_CTX_new_from_pkey, EVP_PKEY_CTX_dup, EVP_PKEY_CTX_free,
EVP_PKEY_CTX_is_a
\&\- public key algorithm context functions
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/evp.h>
\&
\& EVP_PKEY_CTX *EVP_PKEY_CTX_new(EVP_PKEY *pkey, ENGINE *e);
\& EVP_PKEY_CTX *EVP_PKEY_CTX_new_id(int id, ENGINE *e);
\& EVP_PKEY_CTX *EVP_PKEY_CTX_new_from_name(OSSL_LIB_CTX *libctx,
\&                                          const char *name,
\&                                          const char *propquery);
\& EVP_PKEY_CTX *EVP_PKEY_CTX_new_from_pkey(OSSL_LIB_CTX *libctx,
\&                                          EVP_PKEY *pkey,
\&                                          const char *propquery);
\& EVP_PKEY_CTX *EVP_PKEY_CTX_dup(const EVP_PKEY_CTX *ctx);
\& void EVP_PKEY_CTX_free(EVP_PKEY_CTX *ctx);
\& int EVP_PKEY_CTX_is_a(EVP_PKEY_CTX *ctx, const char *keytype);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
The \fBEVP_PKEY_CTX_new()\fR function allocates public key algorithm context using
the \fIpkey\fR key type and ENGINE \fIe\fR.
.PP
The \fBEVP_PKEY_CTX_new_id()\fR function allocates public key algorithm context
using the key type specified by \fIid\fR and ENGINE \fIe\fR.
.PP
The \fBEVP_PKEY_CTX_new_from_name()\fR function allocates a public key algorithm
context using the library context \fIlibctx\fR (see \fBOSSL_LIB_CTX\fR\|(3)), the
key type specified by \fIname\fR and the property query \fIpropquery\fR.  None
of the arguments are duplicated, so they  must remain unchanged for the
lifetime of the returned \fBEVP_PKEY_CTX\fR or of any of its duplicates.  Read
further about the possible names in "NOTES" below.
.PP
The \fBEVP_PKEY_CTX_new_from_pkey()\fR function allocates a public key algorithm
context using the library context \fIlibctx\fR (see \fBOSSL_LIB_CTX\fR\|(3)) and the
algorithm specified by \fIpkey\fR and the property query \fIpropquery\fR. None of the
arguments are duplicated, so they must remain unchanged for the lifetime of the
returned \fBEVP_PKEY_CTX\fR or any of its duplicates.
.PP
\&\fBEVP_PKEY_CTX_new_id()\fR and \fBEVP_PKEY_CTX_new_from_name()\fR are normally
used when no \fBEVP_PKEY\fR structure is associated with the operations,
for example during parameter generation or key generation for some
algorithms.
.PP
\&\fBEVP_PKEY_CTX_dup()\fR duplicates the context \fIctx\fR. It is not supported for a
keygen operation.
.PP
\&\fBEVP_PKEY_CTX_free()\fR frees up the context \fIctx\fR.
If \fIctx\fR is NULL, nothing is done.
.PP
\&\fBEVP_PKEY_is_a()\fR checks if the key type associated with \fIctx\fR is \fIkeytype\fR.
.SH NOTES
.IX Header "NOTES"
.SS "On \fBEVP_PKEY_CTX\fP"
.IX Subsection "On EVP_PKEY_CTX"
The \fBEVP_PKEY_CTX\fR structure is an opaque public key algorithm context used
by the OpenSSL high-level public key API. Contexts \fBMUST NOT\fR be shared between
threads: that is it is not permissible to use the same context simultaneously
in two threads.
.SS "On Key Types"
.IX Subsection "On Key Types"
We mention "key type" in this manual, which is the same
as "algorithm" in most cases, allowing either term to be used
interchangeably.  There are algorithms where the \fIkey type\fR and the
\&\fIalgorithm\fR of the operations that use the keys are not the same,
such as EC keys being used for ECDSA and ECDH operations.
.PP
Key types are given in two different manners:
.IP "Legacy NID or EVP_PKEY type" 4
.IX Item "Legacy NID or EVP_PKEY type"
This is the \fIid\fR used with \fBEVP_PKEY_CTX_new_id()\fR.
.Sp
These are \fBEVP_PKEY_RSA\fR, \fBEVP_PKEY_RSA_PSS\fR, \fBEVP_PKEY_DSA\fR,
\&\fBEVP_PKEY_DH\fR, \fBEVP_PKEY_EC\fR, \fBEVP_PKEY_SM2\fR, \fBEVP_PKEY_X25519\fR,
\&\fBEVP_PKEY_X448\fR, and are used by legacy methods.
.IP "Name strings" 4
.IX Item "Name strings"
This is the \fIname\fR used with \fBEVP_PKEY_CTX_new_from_name()\fR.
.Sp
These are names like "RSA", "DSA", and what's available depends on what
providers are currently accessible.
.Sp
The OpenSSL providers offer a set of key types available this way, please
see \fBOSSL_PROVIDER\-FIPS\fR\|(7) and \fBOSSL_PROVIDER\-default\fR\|(7) and related
documentation for more information.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBEVP_PKEY_CTX_new()\fR, \fBEVP_PKEY_CTX_new_id()\fR and \fBEVP_PKEY_CTX_dup()\fR return either
the newly allocated \fBEVP_PKEY_CTX\fR structure or \fBNULL\fR if an error occurred.
.PP
\&\fBEVP_PKEY_CTX_free()\fR does not return a value.
.PP
\&\fBEVP_PKEY_CTX_is_a()\fR returns 1 for true and 0 for false.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBEVP_PKEY_new\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
The \fBEVP_PKEY_CTX_new()\fR, \fBEVP_PKEY_CTX_new_id()\fR, \fBEVP_PKEY_CTX_dup()\fR and
\&\fBEVP_PKEY_CTX_free()\fR functions were added in OpenSSL 1.0.0.
.PP
The \fBEVP_PKEY_CTX_new_from_name()\fR and \fBEVP_PKEY_CTX_new_from_pkey()\fR functions were
added in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2006\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
