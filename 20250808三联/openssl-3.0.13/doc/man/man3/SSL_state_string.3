.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "SSL_STATE_STRING 3ossl"
.TH SSL_STATE_STRING 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
SSL_state_string, SSL_state_string_long \- get textual description of state of an SSL object
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/ssl.h>
\&
\& const char *SSL_state_string(const SSL *ssl);
\& const char *SSL_state_string_long(const SSL *ssl);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBSSL_state_string()\fR returns an abbreviated string indicating the current state
of the SSL object \fBssl\fR. The returned NUL-terminated string contains 6 or fewer characters.
.PP
\&\fBSSL_state_string_long()\fR returns a descriptive string indicating the current state of
the SSL object \fBssl\fR.
.SH NOTES
.IX Header "NOTES"
During its use, an SSL objects passes several states. The state is internally
maintained. Querying the state information is not very informative before
or when a connection has been established. It however can be of significant
interest during the handshake.
.PP
When using nonblocking sockets, the function call performing the handshake
may return with SSL_ERROR_WANT_READ or SSL_ERROR_WANT_WRITE condition,
so that SSL_state_string[_long]() may be called.
.PP
For both blocking or nonblocking sockets, the details state information
can be used within the info_callback function set with the
\&\fBSSL_set_info_callback()\fR call.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
Detailed description of possible states to be included later.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBssl\fR\|(7), \fBSSL_CTX_set_info_callback\fR\|(3)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2001\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
