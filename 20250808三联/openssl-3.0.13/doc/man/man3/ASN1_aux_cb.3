.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "ASN1_AUX_CB 3ossl"
.TH ASN1_AUX_CB 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
ASN1_AUX, ASN1_PRINT_ARG, ASN1_STREAM_ARG, ASN1_aux_cb, ASN1_aux_const_cb
\&\- ASN.1 auxiliary data
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/asn1t.h>
\&
\& struct ASN1_AUX_st {
\&     void *app_data;
\&     int flags;
\&     int ref_offset;             /* Offset of reference value */
\&     int ref_lock;               /* Offset to an CRYPTO_RWLOCK */
\&     ASN1_aux_cb *asn1_cb;
\&     int enc_offset;             /* Offset of ASN1_ENCODING structure */
\&     ASN1_aux_const_cb *asn1_const_cb; /* for ASN1_OP_I2D_ and ASN1_OP_PRINT_ */
\& };
\& typedef struct ASN1_AUX_st ASN1_AUX;
\&
\& struct ASN1_PRINT_ARG_st {
\&     BIO *out;
\&     int indent;
\&     const ASN1_PCTX *pctx;
\& };
\& typedef struct ASN1_PRINT_ARG_st ASN1_PRINT_ARG;
\&
\& struct ASN1_STREAM_ARG_st {
\&     BIO *out;
\&     BIO *ndef_bio;
\&     unsigned char **boundary;
\& };
\& typedef struct ASN1_STREAM_ARG_st ASN1_STREAM_ARG;
\&
\& typedef int ASN1_aux_cb(int operation, ASN1_VALUE **in, const ASN1_ITEM *it,
\&                         void *exarg);
\& typedef int ASN1_aux_const_cb(int operation, const ASN1_VALUE **in,
\&                               const ASN1_ITEM *it, void *exarg);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
ASN.1 data structures can be associated with an \fBASN1_AUX\fR object to supply
additional information about the ASN.1 structure. An \fBASN1_AUX\fR structure is
associated with the structure during the definition of the ASN.1 template. For
example an \fBASN1_AUX\fR structure will be associated by using one of the various
ASN.1 template definition macros that supply auxiliary information such as
\&\fBASN1_SEQUENCE_enc()\fR, \fBASN1_SEQUENCE_ref()\fR, \fBASN1_SEQUENCE_cb_const_cb()\fR,
\&\fBASN1_SEQUENCE_const_cb()\fR, \fBASN1_SEQUENCE_cb()\fR or \fBASN1_NDEF_SEQUENCE_cb()\fR.
.PP
An \fBASN1_AUX\fR structure contains the following information.
.IP \fIapp_data\fR 4
.IX Item "app_data"
Arbitrary application data
.IP \fIflags\fR 4
.IX Item "flags"
Flags which indicate the auxiliarly functionality supported.
.Sp
The \fBASN1_AFLG_REFCOUNT\fR flag indicates that objects support reference counting.
.Sp
The \fBASN1_AFLG_ENCODING\fR flag indicates that the original encoding of the
object will be saved.
.Sp
The \fBASN1_AFLG_BROKEN\fR flag is a work around for broken encoders where the
sequence length value may not be correct. This should generally not be used.
.Sp
The \fBASN1_AFLG_CONST_CB\fR flag indicates that the "const" form of the
\&\fBASN1_AUX\fR callback should be used in preference to the non-const form.
.IP \fIref_offset\fR 4
.IX Item "ref_offset"
If the \fBASN1_AFLG_REFCOUNT\fR flag is set then this value is assumed to be an
offset into the \fBASN1_VALUE\fR structure where a \fBCRYPTO_REF_COUNT\fR may be
found for the purposes of reference counting.
.IP \fIref_lock\fR 4
.IX Item "ref_lock"
If the \fBASN1_AFLG_REFCOUNT\fR flag is set then this value is assumed to be an
offset into the \fBASN1_VALUE\fR structure where a \fBCRYPTO_RWLOCK\fR may be
found for the purposes of reference counting.
.IP \fIasn1_cb\fR 4
.IX Item "asn1_cb"
A callback that will be invoked at various points during the processing of
the the \fBASN1_VALLUE\fR. See below for further details.
.IP \fIenc_offset\fR 4
.IX Item "enc_offset"
Offset into the \fBASN1_VALUE\fR object where the original encoding of the object
will be saved if the \fBASN1_AFLG_ENCODING\fR flag has been set.
.IP \fIasn1_const_cb\fR 4
.IX Item "asn1_const_cb"
A callback that will be invoked at various points during the processing of
the the \fBASN1_VALLUE\fR. This is used in preference to the \fIasn1_cb\fR callback if
the \fBASN1_AFLG_CONST_CB\fR flag is set. See below for further details.
.PP
During the processing of an \fBASN1_VALUE\fR object the callbacks set via
\&\fIasn1_cb\fR or \fIasn1_const_cb\fR will be invoked as a result of various events
indicated via the \fIoperation\fR parameter. The value of \fI*in\fR will be the
\&\fBASN1_VALUE\fR object being processed based on the template in \fIit\fR. An
additional operation specific parameter may be passed in \fIexarg\fR. The currently
supported operations are as follows. The callbacks should return a positive
value on success or zero on error, unless otherwise noted below.
.IP \fBASN1_OP_NEW_PRE\fR 4
.IX Item "ASN1_OP_NEW_PRE"
Invoked when processing a \fBCHOICE\fR, \fBSEQUENCE\fR or \fBNDEF_SEQUENCE\fR structure
prior to an \fBASN1_VALUE\fR object being allocated. The callback may allocate the
\&\fBASN1_VALUE\fR itself and store it in \fI*pval\fR. If it does so it should return 2
from the callback. On error it should return 0.
.IP \fBASN1_OP_NEW_POST\fR 4
.IX Item "ASN1_OP_NEW_POST"
Invoked when processing a \fBCHOICE\fR, \fBSEQUENCE\fR or \fBNDEF_SEQUENCE\fR structure
after an \fBASN1_VALUE\fR object has been allocated. The allocated object is in
\&\fI*pval\fR.
.IP \fBASN1_OP_FREE_PRE\fR 4
.IX Item "ASN1_OP_FREE_PRE"
Invoked when processing a \fBCHOICE\fR, \fBSEQUENCE\fR or \fBNDEF_SEQUENCE\fR structure
immediately before an \fBASN1_VALUE\fR is freed. If the callback originally
constructed the \fBASN1_VALUE\fR via \fBASN1_OP_NEW_PRE\fR then it should free it at
this point and return 2 from the callback. Otherwise it should return 1 for
success or 0 on error.
.IP \fBASN1_OP_FREE_POST\fR 4
.IX Item "ASN1_OP_FREE_POST"
Invoked when processing a \fBCHOICE\fR, \fBSEQUENCE\fR or \fBNDEF_SEQUENCE\fR structure
immediately after \fBASN1_VALUE\fR sub-structures are freed.
.IP \fBASN1_OP_D2I_PRE\fR 4
.IX Item "ASN1_OP_D2I_PRE"
Invoked when processing a \fBCHOICE\fR, \fBSEQUENCE\fR or \fBNDEF_SEQUENCE\fR structure
immediately before a "d2i" operation for the \fBASN1_VALUE\fR.
.IP \fBASN1_OP_D2I_POST\fR 4
.IX Item "ASN1_OP_D2I_POST"
Invoked when processing a \fBCHOICE\fR, \fBSEQUENCE\fR or \fBNDEF_SEQUENCE\fR structure
immediately after a "d2i" operation for the \fBASN1_VALUE\fR.
.IP \fBASN1_OP_I2D_PRE\fR 4
.IX Item "ASN1_OP_I2D_PRE"
Invoked when processing a \fBCHOICE\fR, \fBSEQUENCE\fR or \fBNDEF_SEQUENCE\fR structure
immediately before a "i2d" operation for the \fBASN1_VALUE\fR.
.IP \fBASN1_OP_I2D_POST\fR 4
.IX Item "ASN1_OP_I2D_POST"
Invoked when processing a \fBCHOICE\fR, \fBSEQUENCE\fR or \fBNDEF_SEQUENCE\fR structure
immediately after a "i2d" operation for the \fBASN1_VALUE\fR.
.IP \fBASN1_OP_PRINT_PRE\fR 4
.IX Item "ASN1_OP_PRINT_PRE"
Invoked when processing a \fBSEQUENCE\fR or \fBNDEF_SEQUENCE\fR structure immediately
before printing the \fBASN1_VALUE\fR. The \fIexarg\fR argument will be a pointer to an
\&\fBASN1_PRINT_ARG\fR structure (see below).
.IP \fBASN1_OP_PRINT_POST\fR 4
.IX Item "ASN1_OP_PRINT_POST"
Invoked when processing a \fBSEQUENCE\fR or \fBNDEF_SEQUENCE\fR structure immediately
after printing the \fBASN1_VALUE\fR. The \fIexarg\fR argument will be a pointer to an
\&\fBASN1_PRINT_ARG\fR structure (see below).
.IP \fBASN1_OP_STREAM_PRE\fR 4
.IX Item "ASN1_OP_STREAM_PRE"
Invoked immediately prior to streaming the \fBASN1_VALUE\fR data using indefinite
length encoding. The \fIexarg\fR argument will be a pointer to a \fBASN1_STREAM_ARG\fR
structure (see below).
.IP \fBASN1_OP_STREAM_POST\fR 4
.IX Item "ASN1_OP_STREAM_POST"
Invoked immediately after streaming the \fBASN1_VALUE\fR data using indefinite
length encoding. The \fIexarg\fR argument will be a pointer to a \fBASN1_STREAM_ARG\fR
structure (see below).
.IP \fBASN1_OP_DETACHED_PRE\fR 4
.IX Item "ASN1_OP_DETACHED_PRE"
Invoked immediately prior to processing the \fBASN1_VALUE\fR data as a "detached"
value (as used in CMS and PKCS7). The \fIexarg\fR argument will be a pointer to a
\&\fBASN1_STREAM_ARG\fR structure (see below).
.IP \fBASN1_OP_DETACHED_POST\fR 4
.IX Item "ASN1_OP_DETACHED_POST"
Invoked immediately after processing the \fBASN1_VALUE\fR data as a "detached"
value (as used in CMS and PKCS7). The \fIexarg\fR argument will be a pointer to a
\&\fBASN1_STREAM_ARG\fR structure (see below).
.IP \fBASN1_OP_DUP_PRE\fR 4
.IX Item "ASN1_OP_DUP_PRE"
Invoked immediate prior to an ASN1_VALUE being duplicated via a call to
\&\fBASN1_item_dup()\fR.
.IP \fBASN1_OP_DUP_POST\fR 4
.IX Item "ASN1_OP_DUP_POST"
Invoked immediate after to an ASN1_VALUE has been duplicated via a call to
\&\fBASN1_item_dup()\fR.
.IP \fBASN1_OP_GET0_LIBCTX\fR 4
.IX Item "ASN1_OP_GET0_LIBCTX"
Invoked in order to obtain the \fBOSSL_LIB_CTX\fR associated with an \fBASN1_VALUE\fR
if any. A pointer to an \fBOSSL_LIB_CTX\fR should be stored in \fI*exarg\fR if such
a value exists.
.IP \fBASN1_OP_GET0_PROPQ\fR 4
.IX Item "ASN1_OP_GET0_PROPQ"
Invoked in order to obtain the property query string associated with an
\&\fBASN1_VALUE\fR if any. A pointer to the property query string should be stored in
\&\fI*exarg\fR if such a value exists.
.PP
An \fBASN1_PRINT_ARG\fR object is used during processing of \fBASN1_OP_PRINT_PRE\fR
and \fBASN1_OP_PRINT_POST\fR callback operations. It contains the following
information.
.IP \fIout\fR 4
.IX Item "out"
The \fBBIO\fR being used to print the data out.
.IP \fIndef_bio\fR 4
.IX Item "ndef_bio"
The current number of indent spaces that should be used for printing this data.
.IP \fIpctx\fR 4
.IX Item "pctx"
The context for the \fBASN1_PCTX\fR operation.
.PP
An \fBASN1_STREAM_ARG\fR object is used during processing of \fBASN1_OP_STREAM_PRE\fR,
\&\fBASN1_OP_STREAM_POST\fR, \fBASN1_OP_DETACHED_PRE\fR and \fBASN1_OP_DETACHED_POST\fR
callback operations. It contains the following information.
.IP \fIout\fR 4
.IX Item "out"
The \fBBIO\fR to stream through
.IP \fIndef_bio\fR 4
.IX Item "ndef_bio"
The \fBBIO\fR with filters appended
.IP \fIboundary\fR 4
.IX Item "boundary"
The streaming I/O boundary.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
The callbacks return 0 on error and a positive value on success. Some operations
require specific positive success values as noted above.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBASN1_item_new_ex\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
The \fBASN1_aux_const_cb()\fR callback and the \fBASN1_OP_GET0_LIBCTX\fR and
\&\fBASN1_OP_GET0_PROPQ\fR operation types were added in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2021\-2023 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
