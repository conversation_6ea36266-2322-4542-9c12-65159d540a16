.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "SSL_CTX_SET_NUM_TICKETS 3ossl"
.TH SSL_CTX_SET_NUM_TICKETS 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
SSL_set_num_tickets,
SSL_get_num_tickets,
SSL_CTX_set_num_tickets,
SSL_CTX_get_num_tickets,
SSL_new_session_ticket
\&\- control the number of TLSv1.3 session tickets that are issued
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/ssl.h>
\&
\& int SSL_set_num_tickets(SSL *s, size_t num_tickets);
\& size_t SSL_get_num_tickets(const SSL *s);
\& int SSL_CTX_set_num_tickets(SSL_CTX *ctx, size_t num_tickets);
\& size_t SSL_CTX_get_num_tickets(const SSL_CTX *ctx);
\& int SSL_new_session_ticket(SSL *s);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBSSL_CTX_set_num_tickets()\fR and \fBSSL_set_num_tickets()\fR can be called for a server
application and set the number of TLSv1.3 session tickets that will be sent to
the client after a full handshake. Set the desired value (which could be 0) in
the \fBnum_tickets\fR argument. Typically these functions should be called before
the start of the handshake.
.PP
The default number of tickets is 2. Following a resumption the number of tickets
issued will never be more than 1 regardless of the value set via
\&\fBSSL_set_num_tickets()\fR or \fBSSL_CTX_set_num_tickets()\fR. If \fBnum_tickets\fR is set to
0 then no tickets will be issued for either a normal connection or a resumption.
.PP
Tickets are also issued on receipt of a post-handshake certificate from the
client following a request by the server using
\&\fBSSL_verify_client_post_handshake\fR\|(3). These new tickets will be associated
with the updated client identity (i.e. including their certificate and
verification status). The number of tickets issued will normally be the same as
was used for the initial handshake. If the initial handshake was a full
handshake then \fBSSL_set_num_tickets()\fR can be called again prior to calling
\&\fBSSL_verify_client_post_handshake()\fR to update the number of tickets that will be
sent.
.PP
To issue tickets after other events (such as application-layer changes),
\&\fBSSL_new_session_ticket()\fR is used by a server application to request that a new
ticket be sent when it is safe to do so.  New tickets are only allowed to be
sent in this manner after the initial handshake has completed, and only for
TLS 1.3 connections.  By default, the ticket generation and transmission are
delayed until the server is starting a new write operation, so that it is
bundled with other application data being written and properly aligned to a
record boundary.  If the connection was at a record boundary when
\&\fBSSL_new_session_ticket()\fR was called, the ticket can be sent immediately
(without waiting for the next application write) by calling
\&\fBSSL_do_handshake()\fR.  \fBSSL_new_session_ticket()\fR can be called more than once to
request additional tickets be sent; all such requests are queued and written
together when it is safe to do so and triggered by \fBSSL_write()\fR or
\&\fBSSL_do_handshake()\fR.  Note that a successful return from
\&\fBSSL_new_session_ticket()\fR indicates only that the request to send a ticket was
processed, not that the ticket itself was sent.  To be notified when the
ticket itself is sent, a new-session callback can be registered with
\&\fBSSL_CTX_sess_set_new_cb\fR\|(3) that will be invoked as the ticket or tickets
are generated.
.PP
\&\fBSSL_CTX_get_num_tickets()\fR and \fBSSL_get_num_tickets()\fR return the number of
tickets set by a previous call to \fBSSL_CTX_set_num_tickets()\fR or
\&\fBSSL_set_num_tickets()\fR, or 2 if no such call has been made.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBSSL_CTX_set_num_tickets()\fR, \fBSSL_set_num_tickets()\fR, and
\&\fBSSL_new_session_ticket()\fR return 1 on success or 0 on failure.
.PP
\&\fBSSL_CTX_get_num_tickets()\fR and \fBSSL_get_num_tickets()\fR return the number of tickets
that have been previously set.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBssl\fR\|(7)
.SH HISTORY
.IX Header "HISTORY"
\&\fBSSL_new_session_ticket()\fR was added in OpenSSL 3.0.0.
\&\fBSSL_set_num_tickets()\fR, \fBSSL_get_num_tickets()\fR, \fBSSL_CTX_set_num_tickets()\fR, and
\&\fBSSL_CTX_get_num_tickets()\fR were added in OpenSSL 1.1.1.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2018\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
