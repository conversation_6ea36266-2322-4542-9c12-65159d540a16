.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "EVP_PKEY_COPY_PARAMETERS 3ossl"
.TH EVP_PKEY_COPY_PARAMETERS 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
EVP_PKEY_missing_parameters, EVP_PKEY_copy_parameters, EVP_PKEY_parameters_eq,
EVP_PKEY_cmp_parameters, EVP_PKEY_eq,
EVP_PKEY_cmp \- public key parameter and comparison functions
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/evp.h>
\&
\& int EVP_PKEY_missing_parameters(const EVP_PKEY *pkey);
\& int EVP_PKEY_copy_parameters(EVP_PKEY *to, const EVP_PKEY *from);
\&
\& int EVP_PKEY_parameters_eq(const EVP_PKEY *a, const EVP_PKEY *b);
\& int EVP_PKEY_eq(const EVP_PKEY *a, const EVP_PKEY *b);
.Ve
.PP
The following functions have been deprecated since OpenSSL 3.0, and can be
hidden entirely by defining \fBOPENSSL_API_COMPAT\fR with a suitable version value,
see \fBopenssl_user_macros\fR\|(7):
.PP
.Vb 2
\& int EVP_PKEY_cmp_parameters(const EVP_PKEY *a, const EVP_PKEY *b);
\& int EVP_PKEY_cmp(const EVP_PKEY *a, const EVP_PKEY *b);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
The function \fBEVP_PKEY_missing_parameters()\fR returns 1 if the public key
parameters of \fBpkey\fR are missing and 0 if they are present or the algorithm
doesn't use parameters.
.PP
The function \fBEVP_PKEY_copy_parameters()\fR copies the parameters from key
\&\fBfrom\fR to key \fBto\fR. An error is returned if the parameters are missing in
\&\fBfrom\fR or present in both \fBfrom\fR and \fBto\fR and mismatch. If the parameters
in \fBfrom\fR and \fBto\fR are both present and match this function has no effect.
.PP
The function \fBEVP_PKEY_parameters_eq()\fR checks the parameters of keys
\&\fBa\fR and \fBb\fR for equality.
.PP
The function \fBEVP_PKEY_eq()\fR checks the keys \fBa\fR and \fBb\fR for equality,
including their parameters if they are available.
.SH NOTES
.IX Header "NOTES"
The main purpose of the functions \fBEVP_PKEY_missing_parameters()\fR and
\&\fBEVP_PKEY_copy_parameters()\fR is to handle public keys in certificates where the
parameters are sometimes omitted from a public key if they are inherited from
the CA that signed it.
.PP
The deprecated functions \fBEVP_PKEY_cmp()\fR and \fBEVP_PKEY_cmp_parameters()\fR differ in
their return values compared to other \fB_cmp()\fR functions. They are aliases for
\&\fBEVP_PKEY_eq()\fR and \fBEVP_PKEY_parameters_eq()\fR.
.PP
The function \fBEVP_PKEY_cmp()\fR previously only checked the key parameters
(if there are any) and the public key, assuming that there always was
a public key and that private key equality could be derived from that.
Because it's no longer assumed that the private key in an \fBEVP_PKEY\fR\|(3) is
always accompanied by a public key, the comparison can not rely on public
key comparison alone.
.PP
Instead, \fBEVP_PKEY_eq()\fR (and therefore also \fBEVP_PKEY_cmp()\fR) now compares:
.IP 1. 4
the key parameters (if there are any)
.IP 2. 4
the public keys or the private keys of the two \fBEVP_PKEY\fRs, depending on
what they both contain.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
The function \fBEVP_PKEY_missing_parameters()\fR returns 1 if the public key
parameters of \fBpkey\fR are missing and 0 if they are present or the algorithm
doesn't use parameters.
.PP
These functions \fBEVP_PKEY_copy_parameters()\fR returns 1 for success and 0 for
failure.
.PP
The functions \fBEVP_PKEY_cmp_parameters()\fR, \fBEVP_PKEY_parameters_eq()\fR,
\&\fBEVP_PKEY_cmp()\fR and \fBEVP_PKEY_eq()\fR return 1 if their
inputs match, 0 if they don't match, \-1 if the key types are different and
\&\-2 if the operation is not supported.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBEVP_PKEY_CTX_new\fR\|(3),
\&\fBEVP_PKEY_keygen\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
The \fBEVP_PKEY_cmp()\fR and \fBEVP_PKEY_cmp_parameters()\fR functions were deprecated in
OpenSSL 3.0.
.PP
The \fBEVP_PKEY_eq()\fR and \fBEVP_PKEY_parameters_eq()\fR were added in OpenSSL 3.0 to
replace \fBEVP_PKEY_cmp()\fR and \fBEVP_PKEY_cmp_parameters()\fR.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2006\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
