.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "SSL_GET_CERTIFICATE 3ossl"
.TH SSL_GET_CERTIFICATE 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
SSL_get_certificate, SSL_get_privatekey \- retrieve TLS/SSL certificate and
private key
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/ssl.h>
\&
\& X509 *SSL_get_certificate(const SSL *s);
\& EVP_PKEY *SSL_get_privatekey(const SSL *s);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBSSL_get_certificate()\fR returns a pointer to an \fBX509\fR object representing a
certificate used as the local peer's identity.
.PP
Multiple certificates can be configured; for example, a server might have both
RSA and ECDSA certificates. The certificate which is returned by
\&\fBSSL_get_certificate()\fR is determined as follows:
.IP \(bu 4
If it is called before certificate selection has occurred, it returns the most
recently added certificate, or NULL if no certificate has been added.
.IP \(bu 4
After certificate selection has occurred, it returns the certificate which was
selected during the handshake, or NULL if no certificate was selected (for
example, on a client where no client certificate is in use).
.PP
Certificate selection occurs during the handshake; therefore, the value returned
by \fBSSL_get_certificate()\fR during any callback made during the handshake process
will depend on whether that callback is made before or after certificate
selection occurs.
.PP
A specific use for \fBSSL_get_certificate()\fR is inside a callback set via a call to
\&\fBSSL_CTX_set_tlsext_status_cb\fR\|(3). This callback occurs after certificate
selection, where it can be used to examine a server's chosen certificate, for
example for the purpose of identifying a certificate's OCSP responder URL so
that an OCSP response can be obtained.
.PP
\&\fBSSL_get_privatekey()\fR returns a pointer to the \fBEVP_PKEY\fR object corresponding
to the certificate returned by \fBSSL_get_certificate()\fR, if any.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
These functions return pointers to their respective objects, or NULL if no such
object is available. Returned objects are owned by the SSL object and should not
be freed by users of these functions.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBssl\fR\|(7), \fBSSL_CTX_set_tlsext_status_cb\fR\|(3)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2001\-2022 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
