.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "OPENSSL-KDF 1ossl"
.TH OPENSSL-KDF 1ossl 2025-08-08 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
openssl\-kdf \- perform Key Derivation Function operations
.SH SYNOPSIS
.IX Header "SYNOPSIS"
\&\fBopenssl kdf\fR
[\fB\-help\fR]
[\fB\-cipher\fR]
[\fB\-digest\fR]
[\fB\-mac\fR]
[\fB\-kdfopt\fR \fInm\fR:\fIv\fR]
[\fB\-keylen\fR \fInum\fR]
[\fB\-out\fR \fIfilename\fR]
[\fB\-binary\fR]
[\fB\-provider\fR \fIname\fR]
[\fB\-provider\-path\fR \fIpath\fR]
[\fB\-propquery\fR \fIpropq\fR]
\&\fIkdf_name\fR
.SH DESCRIPTION
.IX Header "DESCRIPTION"
The key derivation functions generate a derived key from either a secret or
password.
.SH OPTIONS
.IX Header "OPTIONS"
.IP \fB\-help\fR 4
.IX Item "-help"
Print a usage message.
.IP "\fB\-keylen\fR \fInum\fR" 4
.IX Item "-keylen num"
The output size of the derived key. This field is required.
.IP "\fB\-out\fR \fIfilename\fR" 4
.IX Item "-out filename"
Filename to output to, or standard output by default.
.IP \fB\-binary\fR 4
.IX Item "-binary"
Output the derived key in binary form. Uses hexadecimal text format if not specified.
.IP "\fB\-cipher\fR \fIname\fR" 4
.IX Item "-cipher name"
Specify the cipher to be used by the KDF.
Not all KDFs require a cipher and it is an error to use this option in such
cases.
.IP "\fB\-digest\fR \fIname\fR" 4
.IX Item "-digest name"
Specify the digest to be used by the KDF.
Not all KDFs require a digest and it is an error to use this option in such
cases.
To see the list of supported digests, use \f(CW\*(C`openssl list \-digest\-commands\*(C'\fR.
.IP "\fB\-mac\fR \fIname\fR" 4
.IX Item "-mac name"
Specify the MAC to be used by the KDF.
Not all KDFs require a MAC and it is an error to use this option in such
cases.
.IP "\fB\-kdfopt\fR \fInm\fR:\fIv\fR" 4
.IX Item "-kdfopt nm:v"
Passes options to the KDF algorithm.
A comprehensive list of parameters can be found in "PARAMETERS" in \fBEVP_KDF\fR\|(3).
Common parameter names used by \fBEVP_KDF_CTX_set_params()\fR are:
.RS 4
.IP \fBkey:\fR\fIstring\fR 4
.IX Item "key:string"
Specifies the secret key as an alphanumeric string (use if the key contains
printable characters only).
The string length must conform to any restrictions of the KDF algorithm.
A key must be specified for most KDF algorithms.
.IP \fBhexkey:\fR\fIstring\fR 4
.IX Item "hexkey:string"
Alternative to the \fBkey:\fR option where
the secret key is specified in hexadecimal form (two hex digits per byte).
.IP \fBpass:\fR\fIstring\fR 4
.IX Item "pass:string"
Specifies the password as an alphanumeric string (use if the password contains
printable characters only).
The password must be specified for PBKDF2 and scrypt.
.IP \fBhexpass:\fR\fIstring\fR 4
.IX Item "hexpass:string"
Alternative to the \fBpass:\fR option where
the password is specified in hexadecimal form (two hex digits per byte).
.IP \fBsalt:\fR\fIstring\fR 4
.IX Item "salt:string"
Specifies a non-secret unique cryptographic salt as an alphanumeric string
(use if it contains printable characters only).
The length must conform to any restrictions of the KDF algorithm.
A salt parameter is required for several KDF algorithms,
such as \fBEVP_KDF\-PBKDF2\fR\|(7).
.IP \fBhexsalt:\fR\fIstring\fR 4
.IX Item "hexsalt:string"
Alternative to the \fBsalt:\fR option where
the salt is specified in hexadecimal form (two hex digits per byte).
.IP \fBinfo:\fR\fIstring\fR 4
.IX Item "info:string"
Some KDF implementations, such as \fBEVP_KDF\-HKDF\fR\|(7), take an 'info' parameter
for binding the derived key material
to application\- and context-specific information.
Specifies the info, fixed info, other info or shared info argument
as an alphanumeric string (use if it contains printable characters only).
The length must conform to any restrictions of the KDF algorithm.
.IP \fBhexinfo:\fR\fIstring\fR 4
.IX Item "hexinfo:string"
Alternative to the \fBinfo:\fR option where
the info is specified in hexadecimal form (two hex digits per byte).
.IP \fBdigest:\fR\fIstring\fR 4
.IX Item "digest:string"
This option is identical to the \fB\-digest\fR option.
.IP \fBcipher:\fR\fIstring\fR 4
.IX Item "cipher:string"
This option is identical to the \fB\-cipher\fR option.
.IP \fBmac:\fR\fIstring\fR 4
.IX Item "mac:string"
This option is identical to the \fB\-mac\fR option.
.RE
.RS 4
.RE
.IP "\fB\-provider\fR \fIname\fR" 4
.IX Item "-provider name"
.PD 0
.IP "\fB\-provider\-path\fR \fIpath\fR" 4
.IX Item "-provider-path path"
.IP "\fB\-propquery\fR \fIpropq\fR" 4
.IX Item "-propquery propq"
.PD
See "Provider Options" in \fBopenssl\fR\|(1), \fBprovider\fR\|(7), and \fBproperty\fR\|(7).
.IP \fIkdf_name\fR 4
.IX Item "kdf_name"
Specifies the name of a supported KDF algorithm which will be used.
The supported algorithms names include TLS1\-PRF, HKDF, SSKDF, PBKDF2,
SSHKDF, X942KDF\-ASN1, X942KDF\-CONCAT, X963KDF and SCRYPT.
.SH EXAMPLES
.IX Header "EXAMPLES"
Use TLS1\-PRF to create a hex-encoded derived key from a secret key and seed:
.PP
.Vb 2
\&    openssl kdf \-keylen 16 \-kdfopt digest:SHA2\-256 \-kdfopt key:secret \e
\&                \-kdfopt seed:seed TLS1\-PRF
.Ve
.PP
Use HKDF to create a hex-encoded derived key from a secret key, salt and info:
.PP
.Vb 2
\&    openssl kdf \-keylen 10 \-kdfopt digest:SHA2\-256 \-kdfopt key:secret \e
\&                \-kdfopt salt:salt \-kdfopt info:label HKDF
.Ve
.PP
Use SSKDF with KMAC to create a hex-encoded derived key from a secret key, salt and info:
.PP
.Vb 3
\&    openssl kdf \-keylen 64 \-kdfopt mac:KMAC\-128 \-kdfopt maclen:20 \e
\&                \-kdfopt hexkey:b74a149a161545 \-kdfopt hexinfo:348a37a2 \e
\&                \-kdfopt hexsalt:3638271ccd68a2 SSKDF
.Ve
.PP
Use SSKDF with HMAC to create a hex-encoded derived key from a secret key, salt and info:
.PP
.Vb 3
\&    openssl kdf \-keylen 16 \-kdfopt mac:HMAC \-kdfopt digest:SHA2\-256 \e
\&                \-kdfopt hexkey:b74a149a \-kdfopt hexinfo:348a37a2 \e
\&                \-kdfopt hexsalt:3638271c SSKDF
.Ve
.PP
Use SSKDF with Hash to create a hex-encoded derived key from a secret key, salt and info:
.PP
.Vb 3
\&    openssl kdf \-keylen 14 \-kdfopt digest:SHA2\-256 \e
\&                \-kdfopt hexkey:6dbdc23f045488 \e
\&                \-kdfopt hexinfo:a1b2c3d4 SSKDF
.Ve
.PP
Use SSHKDF to create a hex-encoded derived key from a secret key, hash and session_id:
.PP
.Vb 5
\&    openssl kdf \-keylen 16 \-kdfopt digest:SHA2\-256 \e
\&                \-kdfopt hexkey:0102030405 \e
\&                \-kdfopt hexxcghash:06090A \e
\&                \-kdfopt hexsession_id:01020304 \e
\&                \-kdfopt type:A SSHKDF
.Ve
.PP
Use PBKDF2 to create a hex-encoded derived key from a password and salt:
.PP
.Vb 2
\&    openssl kdf \-keylen 32 \-kdfopt digest:SHA256 \-kdfopt pass:password \e
\&                \-kdfopt salt:salt \-kdfopt iter:2 PBKDF2
.Ve
.PP
Use scrypt to create a hex-encoded derived key from a password and salt:
.PP
.Vb 3
\&    openssl kdf \-keylen 64 \-kdfopt pass:password \-kdfopt salt:NaCl \e
\&                \-kdfopt n:1024 \-kdfopt r:8 \-kdfopt p:16 \e
\&                \-kdfopt maxmem_bytes:10485760 SCRYPT
.Ve
.SH NOTES
.IX Header "NOTES"
The KDF mechanisms that are available will depend on the options
used when building OpenSSL.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBopenssl\fR\|(1),
\&\fBopenssl\-pkeyutl\fR\|(1),
\&\fBEVP_KDF\fR\|(3),
\&\fBEVP_KDF\-SCRYPT\fR\|(7),
\&\fBEVP_KDF\-TLS1_PRF\fR\|(7),
\&\fBEVP_KDF\-PBKDF2\fR\|(7),
\&\fBEVP_KDF\-HKDF\fR\|(7),
\&\fBEVP_KDF\-SS\fR\|(7),
\&\fBEVP_KDF\-SSHKDF\fR\|(7),
\&\fBEVP_KDF\-X942\-ASN1\fR\|(7),
\&\fBEVP_KDF\-X942\-CONCAT\fR\|(7),
\&\fBEVP_KDF\-X963\fR\|(7)
.SH HISTORY
.IX Header "HISTORY"
Added in OpenSSL 3.0
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2019\-2023 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
