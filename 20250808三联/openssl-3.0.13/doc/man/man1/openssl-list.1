.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "OPENSSL-LIST 1ossl"
.TH OPENSSL-LIST 1ossl 2025-08-08 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
openssl\-list \- list algorithms and features
.SH SYNOPSIS
.IX Header "SYNOPSIS"
\&\fBopenssl list\fR
[\fB\-help\fR]
[\fB\-verbose\fR]
[\fB\-select\fR \fIname\fR]
[\fB\-1\fR]
[\fB\-commands\fR]
[\fB\-standard\-commands\fR]
[\fB\-digest\-algorithms\fR]
[\fB\-digest\-commands\fR]
[\fB\-kdf\-algorithms\fR]
[\fB\-mac\-algorithms\fR]
[\fB\-random\-instances\fR]
[\fB\-random\-generators\fR]
[\fB\-cipher\-algorithms\fR]
[\fB\-cipher\-commands\fR]
[\fB\-encoders\fR]
[\fB\-decoders\fR]
[\fB\-key\-managers\fR]
[\fB\-key\-exchange\-algorithms\fR]
[\fB\-kem\-algorithms\fR]
[\fB\-signature\-algorithms\fR]
[\fB\-asymcipher\-algorithms\fR]
[\fB\-public\-key\-algorithms\fR]
[\fB\-public\-key\-methods\fR]
[\fB\-store\-loaders\fR]
[\fB\-providers\fR]
[\fB\-engines\fR]
[\fB\-disabled\fR]
[\fB\-objects\fR]
[\fB\-options\fR \fIcommand\fR]
[\fB\-provider\fR \fIname\fR]
[\fB\-provider\-path\fR \fIpath\fR]
[\fB\-propquery\fR \fIpropq\fR]
.SH DESCRIPTION
.IX Header "DESCRIPTION"
This command is used to generate list of algorithms or disabled
features.
.SH OPTIONS
.IX Header "OPTIONS"
.IP \fB\-help\fR 4
.IX Item "-help"
Display a usage message.
.IP \fB\-verbose\fR 4
.IX Item "-verbose"
Displays extra information.
The options below where verbosity applies say a bit more about what that means.
.IP "\fB\-select\fR \fIname\fR" 4
.IX Item "-select name"
Only list algorithms that match this name.
.IP \fB\-1\fR 4
.IX Item "-1"
List the commands, digest-commands, or cipher-commands in a single column.
If used, this option must be given first.
.IP \fB\-commands\fR 4
.IX Item "-commands"
Display a list of standard commands.
.IP \fB\-standard\-commands\fR 4
.IX Item "-standard-commands"
List of standard commands.
.IP \fB\-digest\-commands\fR 4
.IX Item "-digest-commands"
This option is deprecated. Use \fBdigest-algorithms\fR instead.
.Sp
Display a list of message digest commands, which are typically used
as input to the \fBopenssl\-dgst\fR\|(1) or \fBopenssl\-speed\fR\|(1) commands.
.IP \fB\-cipher\-commands\fR 4
.IX Item "-cipher-commands"
This option is deprecated. Use \fBcipher-algorithms\fR instead.
.Sp
Display a list of cipher commands, which are typically used as input
to the \fBopenssl\-enc\fR\|(1) or \fBopenssl\-speed\fR\|(1) commands.
.IP "\fB\-cipher\-algorithms\fR, \fB\-digest\-algorithms\fR, \fB\-kdf\-algorithms\fR, \fB\-mac\-algorithms\fR," 4
.IX Item "-cipher-algorithms, -digest-algorithms, -kdf-algorithms, -mac-algorithms,"
Display a list of symmetric cipher, digest, kdf and mac algorithms.
See "Display of algorithm names" for a description of how names are
displayed.
.Sp
In verbose mode, the algorithms provided by a provider will get additional
information on what parameters each implementation supports.
.IP \fB\-random\-instances\fR 4
.IX Item "-random-instances"
List the primary, public and private random number generator details.
.IP \fB\-random\-generators\fR 4
.IX Item "-random-generators"
Display a list of random number generators.
See "Display of algorithm names" for a description of how names are
displayed.
.IP \fB\-encoders\fR 4
.IX Item "-encoders"
Display a list of encoders.
See "Display of algorithm names" for a description of how names are
displayed.
.Sp
In verbose mode, the algorithms provided by a provider will get additional
information on what parameters each implementation supports.
.IP \fB\-decoders\fR 4
.IX Item "-decoders"
Display a list of decoders.
See "Display of algorithm names" for a description of how names are
displayed.
.Sp
In verbose mode, the algorithms provided by a provider will get additional
information on what parameters each implementation supports.
.IP \fB\-public\-key\-algorithms\fR 4
.IX Item "-public-key-algorithms"
Display a list of public key algorithms, with each algorithm as
a block of multiple lines, all but the first are indented.
The options \fBkey-exchange-algorithms\fR, \fBkem-algorithms\fR,
\&\fBsignature-algorithms\fR, and \fBasymcipher-algorithms\fR will display similar info.
.IP \fB\-public\-key\-methods\fR 4
.IX Item "-public-key-methods"
Display a list of public key methods.
.IP \fB\-key\-managers\fR 4
.IX Item "-key-managers"
Display a list of key managers.
.IP \fB\-key\-exchange\-algorithms\fR 4
.IX Item "-key-exchange-algorithms"
Display a list of key exchange algorithms.
.IP \fB\-kem\-algorithms\fR 4
.IX Item "-kem-algorithms"
Display a list of key encapsulation algorithms.
.IP \fB\-signature\-algorithms\fR 4
.IX Item "-signature-algorithms"
Display a list of signature algorithms.
.IP \fB\-asymcipher\-algorithms\fR 4
.IX Item "-asymcipher-algorithms"
Display a list of asymmetric cipher algorithms.
.IP \fB\-store\-loaders\fR 4
.IX Item "-store-loaders"
Display a list of store loaders.
.IP \fB\-providers\fR 4
.IX Item "-providers"
Display a list of all loaded providers with their names, version and status.
.Sp
In verbose mode, the full version and all provider parameters will additionally
be displayed.
.IP \fB\-engines\fR 4
.IX Item "-engines"
This option is deprecated.
.Sp
Display a list of loaded engines.
.IP \fB\-disabled\fR 4
.IX Item "-disabled"
Display a list of disabled features, those that were compiled out
of the installation.
.IP \fB\-objects\fR 4
.IX Item "-objects"
Display a list of built in objects, i.e. OIDs with names.  They're listed in the
format described in "ASN1 Object Configuration Module" in \fBconfig\fR\|(5).
.IP "\fB\-options\fR \fIcommand\fR" 4
.IX Item "-options command"
Output a two-column list of the options accepted by the specified \fIcommand\fR.
The first is the option name, and the second is a one-character indication
of what type of parameter it takes, if any.
This is an internal option, used for checking that the documentation
is complete.
.IP "\fB\-provider\fR \fIname\fR" 4
.IX Item "-provider name"
.PD 0
.IP "\fB\-provider\-path\fR \fIpath\fR" 4
.IX Item "-provider-path path"
.IP "\fB\-propquery\fR \fIpropq\fR" 4
.IX Item "-propquery propq"
.PD
See "Provider Options" in \fBopenssl\fR\|(1), \fBprovider\fR\|(7), and \fBproperty\fR\|(7).
.SS "Display of algorithm names"
.IX Subsection "Display of algorithm names"
Algorithm names may be displayed in one of two manners:
.IP "Legacy implementations" 4
.IX Item "Legacy implementations"
Legacy implementations will simply display the main name of the
algorithm on a line of its own, or in the form \f(CW\*(C`<foo \*(C'\fR bar>> to show
that \f(CW\*(C`foo\*(C'\fR is an alias for the main name, \f(CW\*(C`bar\*(C'\fR
.IP "Provided implementations" 4
.IX Item "Provided implementations"
Implementations from a provider are displayed like this if the
implementation is labeled with a single name:
.Sp
.Vb 1
\& foo @ bar
.Ve
.Sp
or like this if it's labeled with multiple names:
.Sp
.Vb 1
\& { foo1, foo2 } @bar
.Ve
.Sp
In both cases, \f(CW\*(C`bar\*(C'\fR is the name of the provider.
.SH HISTORY
.IX Header "HISTORY"
The \fB\-engines\fR, \fB\-digest\-commands\fR, and \fB\-cipher\-commands\fR options
were deprecated in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2016\-2022 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
