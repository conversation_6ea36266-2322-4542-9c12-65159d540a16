.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "TSGET 1ossl"
.TH TSGET 1ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
tsget \- Time Stamping HTTP/HTTPS client
.SH SYNOPSIS
.IX Header "SYNOPSIS"
\&\fBtsget\fR
\&\fB\-h\fR \fIserver_url\fR
[\fB\-e\fR \fIextension\fR]
[\fB\-o\fR \fIoutput\fR]
[\fB\-v\fR]
[\fB\-d\fR]
[\fB\-k\fR \fIprivate_key.pem\fR]
[\fB\-p\fR \fIkey_password\fR]
[\fB\-c\fR \fIclient_cert.pem\fR]
[\fB\-C\fR \fICA_certs.pem\fR]
[\fB\-P\fR \fICA_path\fR]
[\fB\-r\fR \fIfiles\fR]
[\fB\-g\fR \fIEGD_socket\fR]
[\fIrequest\fR ...]
.SH DESCRIPTION
.IX Header "DESCRIPTION"
This command can be used for sending a timestamp request, as specified
in RFC 3161, to a timestamp server over HTTP or HTTPS and storing the
timestamp response in a file. It cannot be used for creating the requests
and verifying responses, you have to use \fBopenssl\-ts\fR\|(1) to do that. This
command can send several requests to the server without closing the TCP
connection if more than one requests are specified on the command line.
.PP
This command sends the following HTTP request for each timestamp request:
.PP
.Vb 7
\&        POST url HTTP/1.1
\&        User\-Agent: OpenTSA tsget.pl/<version>
\&        Host: <host>:<port>
\&        Pragma: no\-cache
\&        Content\-Type: application/timestamp\-query
\&        Accept: application/timestamp\-reply
\&        Content\-Length: length of body
\&
\&        ...binary request specified by the user...
.Ve
.PP
It expects a response of type application/timestamp\-reply, which is
written to a file without any interpretation.
.SH OPTIONS
.IX Header "OPTIONS"
.IP "\fB\-h\fR \fIserver_url\fR" 4
.IX Item "-h server_url"
The URL of the HTTP/HTTPS server listening for timestamp requests.
.IP "\fB\-e\fR \fIextension\fR" 4
.IX Item "-e extension"
If the \fB\-o\fR option is not given this argument specifies the extension of the
output files. The base name of the output file will be the same as those of
the input files. Default extension is \fI.tsr\fR. (Optional)
.IP "\fB\-o\fR \fIoutput\fR" 4
.IX Item "-o output"
This option can be specified only when just one request is sent to the
server. The timestamp response will be written to the given output file. '\-'
means standard output. In case of multiple timestamp requests or the absence
of this argument the names of the output files will be derived from the names
of the input files and the default or specified extension argument. (Optional)
.IP \fB\-v\fR 4
.IX Item "-v"
The name of the currently processed request is printed on standard
error. (Optional)
.IP \fB\-d\fR 4
.IX Item "-d"
Switches on verbose mode for the underlying perl module WWW::Curl::Easy.
You can see detailed debug messages for the connection. (Optional)
.IP "\fB\-k\fR \fIprivate_key.pem\fR" 4
.IX Item "-k private_key.pem"
(HTTPS) In case of certificate-based client authentication over HTTPS
\&\fIprivate_key.pem\fR must contain the private key of the user. The private key
file can optionally be protected by a passphrase. The \fB\-c\fR option must also
be specified. (Optional)
.IP "\fB\-p\fR \fIkey_password\fR" 4
.IX Item "-p key_password"
(HTTPS) Specifies the passphrase for the private key specified by the \fB\-k\fR
argument. If this option is omitted and the key is passphrase protected,
it will be prompted for. (Optional)
.IP "\fB\-c\fR \fIclient_cert.pem\fR" 4
.IX Item "-c client_cert.pem"
(HTTPS) In case of certificate-based client authentication over HTTPS
\&\fIclient_cert.pem\fR must contain the X.509 certificate of the user.  The \fB\-k\fR
option must also be specified. If this option is not specified no
certificate-based client authentication will take place. (Optional)
.IP "\fB\-C\fR \fICA_certs.pem\fR" 4
.IX Item "-C CA_certs.pem"
(HTTPS) The trusted CA certificate store. The certificate chain of the peer's
certificate must include one of the CA certificates specified in this file.
Either option \fB\-C\fR or option \fB\-P\fR must be given in case of HTTPS. (Optional)
.IP "\fB\-P\fR \fICA_path\fR" 4
.IX Item "-P CA_path"
(HTTPS) The path containing the trusted CA certificates to verify the peer's
certificate. The directory must be prepared with \fBopenssl\-rehash\fR\|(1). Either
option \fB\-C\fR or option \fB\-P\fR must be given in case of HTTPS. (Optional)
.IP "\fB\-r\fR \fIfiles\fR" 4
.IX Item "-r files"
See "Random State Options" in \fBopenssl\fR\|(1) for more information.
.IP "\fB\-g\fR \fIEGD_socket\fR" 4
.IX Item "-g EGD_socket"
The name of an EGD socket to get random data from. (Optional)
.IP "\fIrequest\fR ..." 4
.IX Item "request ..."
List of files containing RFC 3161 DER-encoded timestamp requests. If no
requests are specified only one request will be sent to the server and it will
be read from the standard input.
(Optional)
.SH "ENVIRONMENT VARIABLES"
.IX Header "ENVIRONMENT VARIABLES"
The \fBTSGET\fR environment variable can optionally contain default
arguments. The content of this variable is added to the list of command line
arguments.
.SH EXAMPLES
.IX Header "EXAMPLES"
The examples below presume that \fIfile1.tsq\fR and \fIfile2.tsq\fR contain valid
timestamp requests, tsa.opentsa.org listens at port 8080 for HTTP requests
and at port 8443 for HTTPS requests, the TSA service is available at the /tsa
absolute path.
.PP
Get a timestamp response for \fIfile1.tsq\fR over HTTP, output is written to
\&\fIfile1.tsr\fR:
.PP
.Vb 1
\&  tsget \-h http://tsa.opentsa.org:8080/tsa file1.tsq
.Ve
.PP
Get a timestamp response for \fIfile1.tsq\fR and \fIfile2.tsq\fR over HTTP showing
progress, output is written to \fIfile1.reply\fR and \fIfile2.reply\fR respectively:
.PP
.Vb 2
\&  tsget \-h http://tsa.opentsa.org:8080/tsa \-v \-e .reply \e
\&        file1.tsq file2.tsq
.Ve
.PP
Create a timestamp request, write it to \fIfile3.tsq\fR, send it to the server and
write the response to \fIfile3.tsr\fR:
.PP
.Vb 3
\&  openssl ts \-query \-data file3.txt \-cert | tee file3.tsq \e
\&        | tsget \-h http://tsa.opentsa.org:8080/tsa \e
\&        \-o file3.tsr
.Ve
.PP
Get a timestamp response for \fIfile1.tsq\fR over HTTPS without client
authentication:
.PP
.Vb 2
\&  tsget \-h https://tsa.opentsa.org:8443/tsa \e
\&        \-C cacerts.pem file1.tsq
.Ve
.PP
Get a timestamp response for \fIfile1.tsq\fR over HTTPS with certificate-based
client authentication (it will ask for the passphrase if \fIclient_key.pem\fR is
protected):
.PP
.Vb 2
\&  tsget \-h https://tsa.opentsa.org:8443/tsa \-C cacerts.pem \e
\&        \-k client_key.pem \-c client_cert.pem file1.tsq
.Ve
.PP
You can shorten the previous command line if you make use of the \fBTSGET\fR
environment variable. The following commands do the same as the previous
example:
.PP
.Vb 4
\&  TSGET=\*(Aq\-h https://tsa.opentsa.org:8443/tsa \-C cacerts.pem \e
\&        \-k client_key.pem \-c client_cert.pem\*(Aq
\&  export TSGET
\&  tsget file1.tsq
.Ve
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBopenssl\fR\|(1),
\&\fBopenssl\-ts\fR\|(1),
WWW::Curl::Easy,
<https://www.rfc\-editor.org/rfc/rfc3161.html>
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2006\-2020 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
