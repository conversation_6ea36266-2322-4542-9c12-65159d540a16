.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "OPENSSL-MAC 1ossl"
.TH OPENSSL-MAC 1ossl 2025-08-08 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
openssl\-mac \- perform Message Authentication Code operations
.SH SYNOPSIS
.IX Header "SYNOPSIS"
\&\fBopenssl mac\fR
[\fB\-help\fR]
[\fB\-cipher\fR]
[\fB\-digest\fR]
[\fB\-macopt\fR]
[\fB\-in\fR \fIfilename\fR]
[\fB\-out\fR \fIfilename\fR]
[\fB\-binary\fR]
[\fB\-provider\fR \fIname\fR]
[\fB\-provider\-path\fR \fIpath\fR]
[\fB\-propquery\fR \fIpropq\fR]
\&\fImac_name\fR
.SH DESCRIPTION
.IX Header "DESCRIPTION"
The message authentication code functions output the MAC of a supplied input
file.
.SH OPTIONS
.IX Header "OPTIONS"
.IP \fB\-help\fR 4
.IX Item "-help"
Print a usage message.
.IP "\fB\-in\fR \fIfilename\fR" 4
.IX Item "-in filename"
Input filename to calculate a MAC for, or standard input by default.
Standard input is used if the filename is '\-'.
Files and standard input are expected to be in binary format.
.IP "\fB\-out\fR \fIfilename\fR" 4
.IX Item "-out filename"
Filename to output to, or standard output by default.
.IP \fB\-binary\fR 4
.IX Item "-binary"
Output the MAC in binary form. Uses hexadecimal text format if not specified.
.IP "\fB\-cipher\fR \fIname\fR" 4
.IX Item "-cipher name"
Used by CMAC and GMAC to specify the cipher algorithm.
For CMAC it must be one of AES\-128\-CBC, AES\-192\-CBC, AES\-256\-CBC or
DES\-EDE3\-CBC.
For GMAC it should be a GCM mode cipher e.g. AES\-128\-GCM.
.IP "\fB\-digest\fR \fIname\fR" 4
.IX Item "-digest name"
Used by HMAC as an alphanumeric string (use if the key contains printable
characters only).
The string length must conform to any restrictions of the MAC algorithm.
To see the list of supported digests, use \f(CW\*(C`openssl list \-digest\-commands\*(C'\fR.
.IP "\fB\-macopt\fR \fInm\fR:\fIv\fR" 4
.IX Item "-macopt nm:v"
Passes options to the MAC algorithm.
A comprehensive list of controls can be found in the EVP_MAC implementation
documentation.
Common parameter names used by \fBEVP_MAC_CTX_get_params()\fR are:
.RS 4
.IP \fBkey:\fR\fIstring\fR 4
.IX Item "key:string"
Specifies the MAC key as an alphanumeric string (use if the key contains
printable characters only).
The string length must conform to any restrictions of the MAC algorithm.
A key must be specified for every MAC algorithm.
.IP \fBhexkey:\fR\fIstring\fR 4
.IX Item "hexkey:string"
Specifies the MAC key in hexadecimal form (two hex digits per byte).
The key length must conform to any restrictions of the MAC algorithm.
A key must be specified for every MAC algorithm.
.IP \fBiv:\fR\fIstring\fR 4
.IX Item "iv:string"
Used by GMAC to specify an IV as an alphanumeric string (use if the IV contains
printable characters only).
.IP \fBhexiv:\fR\fIstring\fR 4
.IX Item "hexiv:string"
Used by GMAC to specify an IV in hexadecimal form (two hex digits per byte).
.IP \fBsize:\fR\fIint\fR 4
.IX Item "size:int"
Used by KMAC128 or KMAC256 to specify an output length.
The default sizes are 32 or 64 bytes respectively.
.IP \fBcustom:\fR\fIstring\fR 4
.IX Item "custom:string"
Used by KMAC128 or KMAC256 to specify a customization string.
The default is the empty string "".
.IP \fBdigest:\fR\fIstring\fR 4
.IX Item "digest:string"
This option is identical to the \fB\-digest\fR option.
.IP \fBcipher:\fR\fIstring\fR 4
.IX Item "cipher:string"
This option is identical to the \fB\-cipher\fR option.
.RE
.RS 4
.RE
.IP "\fB\-provider\fR \fIname\fR" 4
.IX Item "-provider name"
.PD 0
.IP "\fB\-provider\-path\fR \fIpath\fR" 4
.IX Item "-provider-path path"
.IP "\fB\-propquery\fR \fIpropq\fR" 4
.IX Item "-propquery propq"
.PD
See "Provider Options" in \fBopenssl\fR\|(1), \fBprovider\fR\|(7), and \fBproperty\fR\|(7).
.IP \fImac_name\fR 4
.IX Item "mac_name"
Specifies the name of a supported MAC algorithm which will be used.
To see the list of supported MAC's use the command \f(CW\*(C`openssl list
\&\-mac\-algorithms\*(C'\fR.
.SH EXAMPLES
.IX Header "EXAMPLES"
To create a hex-encoded HMAC\-SHA1 MAC of a file and write to stdout: \e
 openssl mac \-digest SHA1 \e
         \-macopt hexkey:000102030405060708090A0B0C0D0E0F10111213 \e
         \-in msg.bin HMAC
.PP
To create a SipHash MAC from a file with a binary file output: \e
 openssl mac \-macopt hexkey:000102030405060708090A0B0C0D0E0F \e
         \-in msg.bin \-out out.bin \-binary SipHash
.PP
To create a hex-encoded CMAC\-AES\-128\-CBC MAC from a file:\e
 openssl mac \-cipher AES\-128\-CBC \e
         \-macopt hexkey:77A77FAF290C1FA30C683DF16BA7A77B \e
         \-in msg.bin CMAC
.PP
To create a hex-encoded KMAC128 MAC from a file with a Customisation String
\&'Tag' and output length of 16: \e
 openssl mac \-macopt custom:Tag \-macopt hexkey:40414243444546 \e
         \-macopt size:16 \-in msg.bin KMAC128
.PP
To create a hex-encoded GMAC\-AES\-128\-GCM with a IV from a file: \e
 openssl mac \-cipher AES\-128\-GCM \-macopt hexiv:E0E00F19FED7BA0136A797F3 \e
         \-macopt hexkey:77A77FAF290C1FA30C683DF16BA7A77B \-in msg.bin GMAC
.SH NOTES
.IX Header "NOTES"
The MAC mechanisms that are available will depend on the options
used when building OpenSSL.
Use \f(CW\*(C`openssl list \-mac\-algorithms\*(C'\fR to list them.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBopenssl\fR\|(1),
\&\fBEVP_MAC\fR\|(3),
\&\fBEVP_MAC\-CMAC\fR\|(7),
\&\fBEVP_MAC\-GMAC\fR\|(7),
\&\fBEVP_MAC\-HMAC\fR\|(7),
\&\fBEVP_MAC\-KMAC\fR\|(7),
\&\fBEVP_MAC\-Siphash\fR\|(7),
\&\fBEVP_MAC\-Poly1305\fR\|(7)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2018\-2022 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
