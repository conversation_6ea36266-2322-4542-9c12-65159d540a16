.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "OPENSSL-SPEED 1ossl"
.TH OPENSSL-SPEED 1ossl 2025-08-08 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
openssl\-speed \- test library performance
.SH SYNOPSIS
.IX Header "SYNOPSIS"
\&\fBopenssl speed\fR
[\fB\-help\fR]
[\fB\-elapsed\fR]
[\fB\-evp\fR \fIalgo\fR]
[\fB\-hmac\fR \fIalgo\fR]
[\fB\-cmac\fR \fIalgo\fR]
[\fB\-mb\fR]
[\fB\-aead\fR]
[\fB\-multi\fR \fInum\fR]
[\fB\-async_jobs\fR \fInum\fR]
[\fB\-misalign\fR \fInum\fR]
[\fB\-decrypt\fR]
[\fB\-primes\fR \fInum\fR]
[\fB\-seconds\fR \fInum\fR]
[\fB\-bytes\fR \fInum\fR]
[\fB\-mr\fR]
[\fB\-rand\fR \fIfiles\fR]
[\fB\-writerand\fR \fIfile\fR]
[\fB\-engine\fR \fIid\fR]
[\fB\-provider\fR \fIname\fR]
[\fB\-provider\-path\fR \fIpath\fR]
[\fB\-propquery\fR \fIpropq\fR]
[\fIalgorithm\fR ...]
.SH DESCRIPTION
.IX Header "DESCRIPTION"
This command is used to test the performance of cryptographic algorithms.
.SH OPTIONS
.IX Header "OPTIONS"
.IP \fB\-help\fR 4
.IX Item "-help"
Print out a usage message.
.IP \fB\-elapsed\fR 4
.IX Item "-elapsed"
When calculating operations\- or bytes-per-second, use wall-clock time
instead of CPU user time as divisor. It can be useful when testing speed
of hardware engines.
.IP "\fB\-evp\fR \fIalgo\fR" 4
.IX Item "-evp algo"
Use the specified cipher or message digest algorithm via the EVP interface.
If \fIalgo\fR is an AEAD cipher, then you can pass \fB\-aead\fR to benchmark a
TLS-like sequence. And if \fIalgo\fR is a multi-buffer capable cipher, e.g.
aes\-128\-cbc\-hmac\-sha1, then \fB\-mb\fR will time multi-buffer operation.
.Sp
To see the algorithms supported with this option, use
\&\f(CW\*(C`openssl list \-digest\-algorithms\*(C'\fR or \f(CW\*(C`openssl list \-cipher\-algorithms\*(C'\fR
command.
.IP "\fB\-multi\fR \fInum\fR" 4
.IX Item "-multi num"
Run multiple operations in parallel.
.IP "\fB\-async_jobs\fR \fInum\fR" 4
.IX Item "-async_jobs num"
Enable async mode and start specified number of jobs.
.IP "\fB\-misalign\fR \fInum\fR" 4
.IX Item "-misalign num"
Misalign the buffers by the specified number of bytes.
.IP "\fB\-hmac\fR \fIdigest\fR" 4
.IX Item "-hmac digest"
Time the HMAC algorithm using the specified message digest.
.IP "\fB\-cmac\fR \fIcipher\fR" 4
.IX Item "-cmac cipher"
Time the CMAC algorithm using the specified cipher e.g.
\&\f(CW\*(C`openssl speed \-cmac aes128\*(C'\fR.
.IP \fB\-decrypt\fR 4
.IX Item "-decrypt"
Time the decryption instead of encryption. Affects only the EVP testing.
.IP \fB\-mb\fR 4
.IX Item "-mb"
Enable multi-block mode on EVP-named cipher.
.IP \fB\-aead\fR 4
.IX Item "-aead"
Benchmark EVP-named AEAD cipher in TLS-like sequence.
.IP "\fB\-primes\fR \fInum\fR" 4
.IX Item "-primes num"
Generate a \fInum\fR\-prime RSA key and use it to run the benchmarks. This option
is only effective if RSA algorithm is specified to test.
.IP "\fB\-seconds\fR \fInum\fR" 4
.IX Item "-seconds num"
Run benchmarks for \fInum\fR seconds.
.IP "\fB\-bytes\fR \fInum\fR" 4
.IX Item "-bytes num"
Run benchmarks on \fInum\fR\-byte buffers. Affects ciphers, digests and the CSPRNG.
The limit on the size of the buffer is INT_MAX \- 64 bytes, which for a 32\-bit 
int would be ********** bytes.
.IP \fB\-mr\fR 4
.IX Item "-mr"
Produce the summary in a mechanical, machine-readable, format.
.IP "\fB\-rand\fR \fIfiles\fR, \fB\-writerand\fR \fIfile\fR" 4
.IX Item "-rand files, -writerand file"
See "Random State Options" in \fBopenssl\fR\|(1) for details.
.IP "\fB\-engine\fR \fIid\fR" 4
.IX Item "-engine id"
See "Engine Options" in \fBopenssl\fR\|(1).
This option is deprecated.
.IP "\fB\-provider\fR \fIname\fR" 4
.IX Item "-provider name"
.PD 0
.IP "\fB\-provider\-path\fR \fIpath\fR" 4
.IX Item "-provider-path path"
.IP "\fB\-propquery\fR \fIpropq\fR" 4
.IX Item "-propquery propq"
.PD
See "Provider Options" in \fBopenssl\fR\|(1), \fBprovider\fR\|(7), and \fBproperty\fR\|(7).
.IP "\fIalgorithm\fR ..." 4
.IX Item "algorithm ..."
If any \fIalgorithm\fR is given, then those algorithms are tested, otherwise a
pre-compiled grand selection is tested.
.SH BUGS
.IX Header "BUGS"
The \fIalgorithm\fR can be selected only from a pre-compiled subset of things
that the \f(CW\*(C`openssl speed\*(C'\fR command knows about. To test any additional digest
or cipher algorithm supported by OpenSSL use the \f(CW\*(C`\-evp\*(C'\fR option.
.PP
There is no way to test the speed of any additional public key algorithms
supported by third party providers with the \f(CW\*(C`openssl speed\*(C'\fR command.
.SH HISTORY
.IX Header "HISTORY"
The \fB\-engine\fR option was deprecated in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2022 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
