.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "OPENSSL-REHASH 1ossl"
.TH OPENSSL-REHASH 1ossl 2025-08-08 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
openssl\-rehash, c_rehash \- Create symbolic links to files named by the hash
values
.SH SYNOPSIS
.IX Header "SYNOPSIS"
\&\fBopenssl\fR
\&\fBrehash\fR
[\fB\-h\fR]
[\fB\-help\fR]
[\fB\-old\fR]
[\fB\-compat\fR]
[\fB\-n\fR]
[\fB\-v\fR]
[\fB\-provider\fR \fIname\fR]
[\fB\-provider\-path\fR \fIpath\fR]
[\fB\-propquery\fR \fIpropq\fR]
[\fIdirectory\fR] ...
.PP
\&\fBc_rehash\fR
[\fB\-h\fR]
[\fB\-help\fR]
[\fB\-old\fR]
[\fB\-n\fR]
[\fB\-v\fR]
[\fB\-provider\fR \fIname\fR]
[\fB\-provider\-path\fR \fIpath\fR]
[\fB\-propquery\fR \fIpropq\fR]
[\fIdirectory\fR] ...
.SH DESCRIPTION
.IX Header "DESCRIPTION"
This command is generally equivalent to the external
script \fBc_rehash\fR,
except for minor differences noted below.
.PP
\&\fBopenssl rehash\fR scans directories and calculates a hash value of
each \fI.pem\fR, \fI.crt\fR, \fI.cer\fR, or \fI.crl\fR
file in the specified directory list and creates symbolic links
for each file, where the name of the link is the hash value.
(If the platform does not support symbolic links, a copy is made.)
This command is useful as many programs that use OpenSSL require
directories to be set up like this in order to find certificates.
.PP
If any directories are named on the command line, then those are
processed in turn. If not, then the \fBSSL_CERT_DIR\fR environment variable
is consulted; this should be a colon-separated list of directories,
like the Unix \fBPATH\fR variable.
If that is not set then the default directory (installation-specific
but often \fI/usr/local/ssl/certs\fR) is processed.
.PP
In order for a directory to be processed, the user must have write
permissions on that directory, otherwise an error will be generated.
.PP
The links created are of the form \fIHHHHHHHH.D\fR, where each \fIH\fR
is a hexadecimal character and \fID\fR is a single decimal digit.
When a directory is processed, all links in it that have a name
in that syntax are first removed, even if they are being used for
some other purpose.
To skip the removal step, use the \fB\-n\fR flag.
Hashes for CRL's look similar except the letter \fBr\fR appears after
the period, like this: \fIHHHHHHHH.\fR\fBr\fR\fID\fR.
.PP
Multiple objects may have the same hash; they will be indicated by
incrementing the \fID\fR value. Duplicates are found by comparing the
full SHA\-1 fingerprint. A warning will be displayed if a duplicate
is found.
.PP
A warning will also be displayed if there are files that
cannot be parsed as either a certificate or a CRL or if
more than one such object appears in the file.
.SS "Script Configuration"
.IX Subsection "Script Configuration"
The \fBc_rehash\fR script
uses the \fBopenssl\fR program to compute the hashes and
fingerprints. If not found in the user's \fBPATH\fR, then set the
\&\fBOPENSSL\fR environment variable to the full pathname.
Any program can be used, it will be invoked as follows for either
a certificate or CRL:
.PP
.Vb 2
\&  $OPENSSL x509 \-hash \-fingerprint \-noout \-in FILENAME
\&  $OPENSSL crl \-hash \-fingerprint \-noout \-in FILENAME
.Ve
.PP
where \fIFILENAME\fR is the filename. It must output the hash of the
file on the first line, and the fingerprint on the second,
optionally prefixed with some text and an equals sign.
.SH OPTIONS
.IX Header "OPTIONS"
.IP "\fB\-help\fR \fB\-h\fR" 4
.IX Item "-help -h"
Display a brief usage message.
.IP \fB\-old\fR 4
.IX Item "-old"
Use old-style hashing (MD5, as opposed to SHA\-1) for generating
links to be used for releases before 1.0.0.
Note that current versions will not use the old style.
.IP \fB\-n\fR 4
.IX Item "-n"
Do not remove existing links.
This is needed when keeping new and old-style links in the same directory.
.IP \fB\-compat\fR 4
.IX Item "-compat"
Generate links for both old-style (MD5) and new-style (SHA1) hashing.
This allows releases before 1.0.0 to use these links along-side newer
releases.
.IP \fB\-v\fR 4
.IX Item "-v"
Print messages about old links removed and new links created.
By default, this command only lists each directory as it is processed.
.IP "\fB\-provider\fR \fIname\fR" 4
.IX Item "-provider name"
.PD 0
.IP "\fB\-provider\-path\fR \fIpath\fR" 4
.IX Item "-provider-path path"
.IP "\fB\-propquery\fR \fIpropq\fR" 4
.IX Item "-propquery propq"
.PD
See "Provider Options" in \fBopenssl\fR\|(1), \fBprovider\fR\|(7), and \fBproperty\fR\|(7).
.SH ENVIRONMENT
.IX Header "ENVIRONMENT"
.IP \fBOPENSSL\fR 4
.IX Item "OPENSSL"
The path to an executable to use to generate hashes and
fingerprints (see above).
.IP \fBSSL_CERT_DIR\fR 4
.IX Item "SSL_CERT_DIR"
Colon separated list of directories to operate on.
Ignored if directories are listed on the command line.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBopenssl\fR\|(1),
\&\fBopenssl\-crl\fR\|(1),
\&\fBopenssl\-x509\fR\|(1)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2015\-2020 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
