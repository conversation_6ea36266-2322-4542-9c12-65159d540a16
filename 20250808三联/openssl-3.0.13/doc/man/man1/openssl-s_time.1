.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "OPENSSL-S_TIME 1ossl"
.TH OPENSSL-S_TIME 1ossl 2025-08-08 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
openssl\-s_time \- SSL/TLS performance timing program
.SH SYNOPSIS
.IX Header "SYNOPSIS"
\&\fBopenssl\fR \fBs_time\fR
[\fB\-help\fR]
[\fB\-connect\fR \fIhost\fR:\fIport\fR]
[\fB\-www\fR \fIpage\fR]
[\fB\-cert\fR \fIfilename\fR]
[\fB\-key\fR \fIfilename\fR]
[\fB\-reuse\fR]
[\fB\-new\fR]
[\fB\-verify\fR \fIdepth\fR]
[\fB\-time\fR \fIseconds\fR]
[\fB\-ssl3\fR]
[\fB\-tls1\fR]
[\fB\-tls1_1\fR]
[\fB\-tls1_2\fR]
[\fB\-tls1_3\fR]
[\fB\-bugs\fR]
[\fB\-cipher\fR \fIcipherlist\fR]
[\fB\-ciphersuites\fR \fIval\fR]
[\fB\-nameopt\fR \fIoption\fR]
[\fB\-cafile\fR \fIfile\fR]
[\fB\-CAfile\fR \fIfile\fR]
[\fB\-no\-CAfile\fR]
[\fB\-CApath\fR \fIdir\fR]
[\fB\-no\-CApath\fR]
[\fB\-CAstore\fR \fIuri\fR]
[\fB\-no\-CAstore\fR]
[\fB\-provider\fR \fIname\fR]
[\fB\-provider\-path\fR \fIpath\fR]
[\fB\-propquery\fR \fIpropq\fR]
.SH DESCRIPTION
.IX Header "DESCRIPTION"
This command implements a generic SSL/TLS client which
connects to a remote host using SSL/TLS. It can request a page from the server
and includes the time to transfer the payload data in its timing measurements.
It measures the number of connections within a given timeframe, the amount of
data transferred (if any), and calculates the average time spent for one
connection.
.SH OPTIONS
.IX Header "OPTIONS"
.IP \fB\-help\fR 4
.IX Item "-help"
Print out a usage message.
.IP "\fB\-connect\fR \fIhost\fR:\fIport\fR" 4
.IX Item "-connect host:port"
This specifies the host and optional port to connect to.
.IP "\fB\-www\fR \fIpage\fR" 4
.IX Item "-www page"
This specifies the page to GET from the server. A value of '/' gets the
\&\fIindex.html\fR page. If this parameter is not specified, then this command
will only perform the handshake to establish SSL connections but not transfer
any payload data.
.IP "\fB\-cert\fR \fIcertname\fR" 4
.IX Item "-cert certname"
The certificate to use, if one is requested by the server. The default is
not to use a certificate. The file is in PEM format.
.IP "\fB\-key\fR \fIkeyfile\fR" 4
.IX Item "-key keyfile"
The private key to use. If not specified then the certificate file will
be used. The file is in PEM format.
.IP "\fB\-verify\fR \fIdepth\fR" 4
.IX Item "-verify depth"
The verify depth to use. This specifies the maximum length of the
server certificate chain and turns on server certificate verification.
Currently the verify operation continues after errors so all the problems
with a certificate chain can be seen. As a side effect the connection
will never fail due to a server certificate verify failure.
.IP \fB\-new\fR 4
.IX Item "-new"
Performs the timing test using a new session ID for each connection.
If neither \fB\-new\fR nor \fB\-reuse\fR are specified, they are both on by default
and executed in sequence.
.IP \fB\-reuse\fR 4
.IX Item "-reuse"
Performs the timing test using the same session ID; this can be used as a test
that session caching is working. If neither \fB\-new\fR nor \fB\-reuse\fR are
specified, they are both on by default and executed in sequence.
.IP \fB\-bugs\fR 4
.IX Item "-bugs"
There are several known bugs in SSL and TLS implementations. Adding this
option enables various workarounds.
.IP "\fB\-cipher\fR \fIcipherlist\fR" 4
.IX Item "-cipher cipherlist"
This allows the TLSv1.2 and below cipher list sent by the client to be modified.
This list will be combined with any TLSv1.3 ciphersuites that have been
configured. Although the server determines which cipher suite is used it should
take the first supported cipher in the list sent by the client. See
\&\fBopenssl\-ciphers\fR\|(1) for more information.
.IP "\fB\-ciphersuites\fR \fIval\fR" 4
.IX Item "-ciphersuites val"
This allows the TLSv1.3 ciphersuites sent by the client to be modified. This
list will be combined with any TLSv1.2 and below ciphersuites that have been
configured. Although the server determines which cipher suite is used it should
take the first supported cipher in the list sent by the client. See
\&\fBopenssl\-ciphers\fR\|(1) for more information. The format for this list is a
simple colon (":") separated list of TLSv1.3 ciphersuite names.
.IP "\fB\-time\fR \fIlength\fR" 4
.IX Item "-time length"
Specifies how long (in seconds) this command should establish connections
and optionally transfer payload data from a server. Server and client
performance and the link speed determine how many connections it
can establish.
.IP "\fB\-nameopt\fR \fIoption\fR" 4
.IX Item "-nameopt option"
This specifies how the subject or issuer names are displayed.
See \fBopenssl\-namedisplay\-options\fR\|(1) for details.
.IP "\fB\-CAfile\fR \fIfile\fR, \fB\-no\-CAfile\fR, \fB\-CApath\fR \fIdir\fR, \fB\-no\-CApath\fR, \fB\-CAstore\fR \fIuri\fR, \fB\-no\-CAstore\fR" 4
.IX Item "-CAfile file, -no-CAfile, -CApath dir, -no-CApath, -CAstore uri, -no-CAstore"
See "Trusted Certificate Options" in \fBopenssl\-verification\-options\fR\|(1) for details.
.IP "\fB\-provider\fR \fIname\fR" 4
.IX Item "-provider name"
.PD 0
.IP "\fB\-provider\-path\fR \fIpath\fR" 4
.IX Item "-provider-path path"
.IP "\fB\-propquery\fR \fIpropq\fR" 4
.IX Item "-propquery propq"
.PD
See "Provider Options" in \fBopenssl\fR\|(1), \fBprovider\fR\|(7), and \fBproperty\fR\|(7).
.IP "\fB\-cafile\fR \fIfile\fR" 4
.IX Item "-cafile file"
This is an obsolete synonym for \fB\-CAfile\fR.
.IP "\fB\-ssl3\fR, \fB\-tls1\fR, \fB\-tls1_1\fR, \fB\-tls1_2\fR, \fB\-tls1_3\fR" 4
.IX Item "-ssl3, -tls1, -tls1_1, -tls1_2, -tls1_3"
See "TLS Version Options" in \fBopenssl\fR\|(1).
.SH NOTES
.IX Header "NOTES"
This command can be used to measure the performance of an SSL connection.
To connect to an SSL HTTP server and get the default page the command
.PP
.Vb 1
\& openssl s_time \-connect servername:443 \-www / \-CApath yourdir \-CAfile yourfile.pem \-cipher commoncipher [\-ssl3]
.Ve
.PP
would typically be used (https uses port 443). \fIcommoncipher\fR is a cipher to
which both client and server can agree, see the \fBopenssl\-ciphers\fR\|(1) command
for details.
.PP
If the handshake fails then there are several possible causes, if it is
nothing obvious like no client certificate then the \fB\-bugs\fR and
\&\fB\-ssl3\fR options can be tried
in case it is a buggy server. In particular you should play with these
options \fBbefore\fR submitting a bug report to an OpenSSL mailing list.
.PP
A frequent problem when attempting to get client certificates working
is that a web client complains it has no certificates or gives an empty
list to choose from. This is normally because the server is not sending
the clients certificate authority in its "acceptable CA list" when it
requests a certificate. By using \fBopenssl\-s_client\fR\|(1) the CA list can be
viewed and checked. However, some servers only request client authentication
after a specific URL is requested. To obtain the list in this case it
is necessary to use the \fB\-prexit\fR option of \fBopenssl\-s_client\fR\|(1) and
send an HTTP request for an appropriate page.
.PP
If a certificate is specified on the command line using the \fB\-cert\fR
option it will not be used unless the server specifically requests
a client certificate. Therefore, merely including a client certificate
on the command line is no guarantee that the certificate works.
.SH BUGS
.IX Header "BUGS"
Because this program does not have all the options of the
\&\fBopenssl\-s_client\fR\|(1) program to turn protocols on and off, you may not
be able to measure the performance of all protocols with all servers.
.PP
The \fB\-verify\fR option should really exit if the server verification
fails.
.SH HISTORY
.IX Header "HISTORY"
The \fB\-cafile\fR option was deprecated in OpenSSL 3.0.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBopenssl\fR\|(1),
\&\fBopenssl\-s_client\fR\|(1),
\&\fBopenssl\-s_server\fR\|(1),
\&\fBopenssl\-ciphers\fR\|(1),
\&\fBossl_store\-file\fR\|(7)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2004\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
