.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "OPENSSL-GENRSA 1ossl"
.TH OPENSSL-GENRSA 1ossl 2025-08-08 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
openssl\-genrsa \- generate an RSA private key
.SH SYNOPSIS
.IX Header "SYNOPSIS"
\&\fBopenssl\fR \fBgenrsa\fR
[\fB\-help\fR]
[\fB\-out\fR \fIfilename\fR]
[\fB\-passout\fR \fIarg\fR]
[\fB\-aes128\fR]
[\fB\-aes192\fR]
[\fB\-aes256\fR]
[\fB\-aria128\fR]
[\fB\-aria192\fR]
[\fB\-aria256\fR]
[\fB\-camellia128\fR]
[\fB\-camellia192\fR]
[\fB\-camellia256\fR]
[\fB\-des\fR]
[\fB\-des3\fR]
[\fB\-idea\fR]
[\fB\-F4\fR]
[\fB\-f4\fR]
[\fB\-3\fR]
[\fB\-primes\fR \fInum\fR]
[\fB\-verbose\fR]
[\fB\-traditional\fR]
[\fB\-rand\fR \fIfiles\fR]
[\fB\-writerand\fR \fIfile\fR]
[\fB\-engine\fR \fIid\fR]
[\fB\-provider\fR \fIname\fR]
[\fB\-provider\-path\fR \fIpath\fR]
[\fB\-propquery\fR \fIpropq\fR]
[\fBnumbits\fR]
.SH DESCRIPTION
.IX Header "DESCRIPTION"
This command generates an RSA private key.
.SH OPTIONS
.IX Header "OPTIONS"
.IP \fB\-help\fR 4
.IX Item "-help"
Print out a usage message.
.IP "\fB\-out\fR \fIfilename\fR" 4
.IX Item "-out filename"
Output the key to the specified file. If this argument is not specified then
standard output is used.
.IP "\fB\-passout\fR \fIarg\fR" 4
.IX Item "-passout arg"
The output file password source. For more information about the format
see \fBopenssl\-passphrase\-options\fR\|(1).
.IP "\fB\-aes128\fR, \fB\-aes192\fR, \fB\-aes256\fR, \fB\-aria128\fR, \fB\-aria192\fR, \fB\-aria256\fR, \fB\-camellia128\fR, \fB\-camellia192\fR, \fB\-camellia256\fR, \fB\-des\fR, \fB\-des3\fR, \fB\-idea\fR" 4
.IX Item "-aes128, -aes192, -aes256, -aria128, -aria192, -aria256, -camellia128, -camellia192, -camellia256, -des, -des3, -idea"
These options encrypt the private key with specified
cipher before outputting it. If none of these options is
specified no encryption is used. If encryption is used a pass phrase is prompted
for if it is not supplied via the \fB\-passout\fR argument.
.IP "\fB\-F4\fR, \fB\-f4\fR, \fB\-3\fR" 4
.IX Item "-F4, -f4, -3"
The public exponent to use, either 65537 or 3. The default is 65537.
The \fB\-3\fR option has been deprecated.
.IP "\fB\-primes\fR \fInum\fR" 4
.IX Item "-primes num"
Specify the number of primes to use while generating the RSA key. The \fInum\fR
parameter must be a positive integer that is greater than 1 and less than 16.
If \fInum\fR is greater than 2, then the generated key is called a 'multi\-prime'
RSA key, which is defined in RFC 8017.
.IP \fB\-verbose\fR 4
.IX Item "-verbose"
Print extra details about the operations being performed.
.IP \fB\-traditional\fR 4
.IX Item "-traditional"
Write the key using the traditional PKCS#1 format instead of the PKCS#8 format.
.IP "\fB\-rand\fR \fIfiles\fR, \fB\-writerand\fR \fIfile\fR" 4
.IX Item "-rand files, -writerand file"
See "Random State Options" in \fBopenssl\fR\|(1) for details.
.IP "\fB\-engine\fR \fIid\fR" 4
.IX Item "-engine id"
See "Engine Options" in \fBopenssl\fR\|(1).
This option is deprecated.
.IP "\fB\-provider\fR \fIname\fR" 4
.IX Item "-provider name"
.PD 0
.IP "\fB\-provider\-path\fR \fIpath\fR" 4
.IX Item "-provider-path path"
.IP "\fB\-propquery\fR \fIpropq\fR" 4
.IX Item "-propquery propq"
.PD
See "Provider Options" in \fBopenssl\fR\|(1), \fBprovider\fR\|(7), and \fBproperty\fR\|(7).
.IP \fBnumbits\fR 4
.IX Item "numbits"
The size of the private key to generate in bits. This must be the last option
specified. The default is 2048 and values less than 512 are not allowed.
.SH NOTES
.IX Header "NOTES"
RSA private key generation essentially involves the generation of two or more
prime numbers. When generating a private key various symbols will be output to
indicate the progress of the generation. A \fB.\fR represents each number which
has passed an initial sieve test, \fB+\fR means a number has passed a single
round of the Miller-Rabin primality test, \fB*\fR means the current prime starts
a regenerating progress due to some failed tests. A newline means that the number
has passed all the prime tests (the actual number depends on the key size).
.PP
Because key generation is a random process the time taken to generate a key
may vary somewhat. But in general, more primes lead to less generation time
of a key.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBopenssl\fR\|(1),
\&\fBopenssl\-genpkey\fR\|(1),
\&\fBopenssl\-gendsa\fR\|(1)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2023 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
