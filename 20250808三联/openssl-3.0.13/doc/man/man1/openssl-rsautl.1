.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "OPENSSL-RSAUTL 1ossl"
.TH OPENSSL-RSAUTL 1ossl 2025-08-08 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
openssl\-rsautl \- RSA command
.SH SYNOPSIS
.IX Header "SYNOPSIS"
\&\fBopenssl\fR \fBrsautl\fR
[\fB\-help\fR]
[\fB\-in\fR \fIfile\fR]
[\fB\-passin\fR \fIarg\fR]
[\fB\-rev\fR]
[\fB\-out\fR \fIfile\fR]
[\fB\-inkey\fR \fIfilename\fR|\fIuri\fR]
[\fB\-keyform\fR \fBDER\fR|\fBPEM\fR|\fBP12\fR|\fBENGINE\fR]
[\fB\-pubin\fR]
[\fB\-certin\fR]
[\fB\-sign\fR]
[\fB\-verify\fR]
[\fB\-encrypt\fR]
[\fB\-decrypt\fR]
[\fB\-pkcs\fR]
[\fB\-x931\fR]
[\fB\-oaep\fR]
[\fB\-raw\fR]
[\fB\-hexdump\fR]
[\fB\-asn1parse\fR]
[\fB\-engine\fR \fIid\fR]
[\fB\-rand\fR \fIfiles\fR]
[\fB\-writerand\fR \fIfile\fR]
[\fB\-provider\fR \fIname\fR]
[\fB\-provider\-path\fR \fIpath\fR]
[\fB\-propquery\fR \fIpropq\fR]
.SH DESCRIPTION
.IX Header "DESCRIPTION"
This command has been deprecated.
The \fBopenssl\-pkeyutl\fR\|(1) command should be used instead.
.PP
This command can be used to sign, verify, encrypt and decrypt
data using the RSA algorithm.
.SH OPTIONS
.IX Header "OPTIONS"
.IP \fB\-help\fR 4
.IX Item "-help"
Print out a usage message.
.IP "\fB\-in\fR \fIfilename\fR" 4
.IX Item "-in filename"
This specifies the input filename to read data from or standard input
if this option is not specified.
.IP "\fB\-passin\fR \fIarg\fR" 4
.IX Item "-passin arg"
The passphrase used in the output file.
See see \fBopenssl\-passphrase\-options\fR\|(1).
.IP \fB\-rev\fR 4
.IX Item "-rev"
Reverse the order of the input.
.IP "\fB\-out\fR \fIfilename\fR" 4
.IX Item "-out filename"
Specifies the output filename to write to or standard output by
default.
.IP "\fB\-inkey\fR \fIfilename\fR|\fIuri\fR" 4
.IX Item "-inkey filename|uri"
The input key, by default it should be an RSA private key.
.IP "\fB\-keyform\fR \fBDER\fR|\fBPEM\fR|\fBP12\fR|\fBENGINE\fR" 4
.IX Item "-keyform DER|PEM|P12|ENGINE"
The key format; unspecified by default.
See \fBopenssl\-format\-options\fR\|(1) for details.
.IP \fB\-pubin\fR 4
.IX Item "-pubin"
The input file is an RSA public key.
.IP \fB\-certin\fR 4
.IX Item "-certin"
The input is a certificate containing an RSA public key.
.IP \fB\-sign\fR 4
.IX Item "-sign"
Sign the input data and output the signed result. This requires
an RSA private key.
.IP \fB\-verify\fR 4
.IX Item "-verify"
Verify the input data and output the recovered data.
.IP \fB\-encrypt\fR 4
.IX Item "-encrypt"
Encrypt the input data using an RSA public key.
.IP \fB\-decrypt\fR 4
.IX Item "-decrypt"
Decrypt the input data using an RSA private key.
.IP "\fB\-pkcs\fR, \fB\-oaep\fR, \fB\-x931\fR, \fB\-raw\fR" 4
.IX Item "-pkcs, -oaep, -x931, -raw"
The padding to use: PKCS#1 v1.5 (the default), PKCS#1 OAEP,
ANSI X9.31, or no padding, respectively.
For signatures, only \fB\-pkcs\fR and \fB\-raw\fR can be used.
.IP \fB\-hexdump\fR 4
.IX Item "-hexdump"
Hex dump the output data.
.IP \fB\-asn1parse\fR 4
.IX Item "-asn1parse"
Parse the ASN.1 output data, this is useful when combined with the
\&\fB\-verify\fR option.
.IP "\fB\-engine\fR \fIid\fR" 4
.IX Item "-engine id"
See "Engine Options" in \fBopenssl\fR\|(1).
This option is deprecated.
.IP "\fB\-rand\fR \fIfiles\fR, \fB\-writerand\fR \fIfile\fR" 4
.IX Item "-rand files, -writerand file"
See "Random State Options" in \fBopenssl\fR\|(1) for details.
.IP "\fB\-provider\fR \fIname\fR" 4
.IX Item "-provider name"
.PD 0
.IP "\fB\-provider\-path\fR \fIpath\fR" 4
.IX Item "-provider-path path"
.IP "\fB\-propquery\fR \fIpropq\fR" 4
.IX Item "-propquery propq"
.PD
See "Provider Options" in \fBopenssl\fR\|(1), \fBprovider\fR\|(7), and \fBproperty\fR\|(7).
.SH NOTES
.IX Header "NOTES"
Since this command uses the RSA algorithm directly, it can only be
used to sign or verify small pieces of data.
.SH EXAMPLES
.IX Header "EXAMPLES"
Examples equivalent to these can be found in the documentation for the
non-deprecated \fBopenssl\-pkeyutl\fR\|(1) command.
.PP
Sign some data using a private key:
.PP
.Vb 1
\& openssl rsautl \-sign \-in file \-inkey key.pem \-out sig
.Ve
.PP
Recover the signed data
.PP
.Vb 1
\& openssl rsautl \-verify \-in sig \-inkey key.pem
.Ve
.PP
Examine the raw signed data:
.PP
.Vb 1
\& openssl rsautl \-verify \-in sig \-inkey key.pem \-raw \-hexdump
\&
\& 0000 \- 00 01 ff ff ff ff ff ff\-ff ff ff ff ff ff ff ff   ................
\& 0010 \- ff ff ff ff ff ff ff ff\-ff ff ff ff ff ff ff ff   ................
\& 0020 \- ff ff ff ff ff ff ff ff\-ff ff ff ff ff ff ff ff   ................
\& 0030 \- ff ff ff ff ff ff ff ff\-ff ff ff ff ff ff ff ff   ................
\& 0040 \- ff ff ff ff ff ff ff ff\-ff ff ff ff ff ff ff ff   ................
\& 0050 \- ff ff ff ff ff ff ff ff\-ff ff ff ff ff ff ff ff   ................
\& 0060 \- ff ff ff ff ff ff ff ff\-ff ff ff ff ff ff ff ff   ................
\& 0070 \- ff ff ff ff 00 68 65 6c\-6c 6f 20 77 6f 72 6c 64   .....hello world
.Ve
.PP
The PKCS#1 block formatting is evident from this. If this was done using
encrypt and decrypt the block would have been of type 2 (the second byte)
and random padding data visible instead of the 0xff bytes.
.PP
It is possible to analyse the signature of certificates using this
command in conjunction with \fBopenssl\-asn1parse\fR\|(1). Consider the self signed
example in \fIcerts/pca\-cert.pem\fR. Running \fBopenssl\-asn1parse\fR\|(1) as follows
yields:
.PP
.Vb 1
\& openssl asn1parse \-in pca\-cert.pem
\&
\&    0:d=0  hl=4 l= 742 cons: SEQUENCE
\&    4:d=1  hl=4 l= 591 cons:  SEQUENCE
\&    8:d=2  hl=2 l=   3 cons:   cont [ 0 ]
\&   10:d=3  hl=2 l=   1 prim:    INTEGER           :02
\&   13:d=2  hl=2 l=   1 prim:   INTEGER           :00
\&   16:d=2  hl=2 l=  13 cons:   SEQUENCE
\&   18:d=3  hl=2 l=   9 prim:    OBJECT            :md5WithRSAEncryption
\&   29:d=3  hl=2 l=   0 prim:    NULL
\&   31:d=2  hl=2 l=  92 cons:   SEQUENCE
\&   33:d=3  hl=2 l=  11 cons:    SET
\&   35:d=4  hl=2 l=   9 cons:     SEQUENCE
\&   37:d=5  hl=2 l=   3 prim:      OBJECT            :countryName
\&   42:d=5  hl=2 l=   2 prim:      PRINTABLESTRING   :AU
\&  ....
\&  599:d=1  hl=2 l=  13 cons:  SEQUENCE
\&  601:d=2  hl=2 l=   9 prim:   OBJECT            :md5WithRSAEncryption
\&  612:d=2  hl=2 l=   0 prim:   NULL
\&  614:d=1  hl=3 l= 129 prim:  BIT STRING
.Ve
.PP
The final BIT STRING contains the actual signature. It can be extracted with:
.PP
.Vb 1
\& openssl asn1parse \-in pca\-cert.pem \-out sig \-noout \-strparse 614
.Ve
.PP
The certificate public key can be extracted with:
.PP
.Vb 1
\& openssl x509 \-in test/testx509.pem \-pubkey \-noout >pubkey.pem
.Ve
.PP
The signature can be analysed with:
.PP
.Vb 1
\& openssl rsautl \-in sig \-verify \-asn1parse \-inkey pubkey.pem \-pubin
\&
\&    0:d=0  hl=2 l=  32 cons: SEQUENCE
\&    2:d=1  hl=2 l=  12 cons:  SEQUENCE
\&    4:d=2  hl=2 l=   8 prim:   OBJECT            :md5
\&   14:d=2  hl=2 l=   0 prim:   NULL
\&   16:d=1  hl=2 l=  16 prim:  OCTET STRING
\&      0000 \- f3 46 9e aa 1a 4a 73 c9\-37 ea 93 00 48 25 08 b5   .F...Js.7...H%..
.Ve
.PP
This is the parsed version of an ASN1 DigestInfo structure. It can be seen that
the digest used was md5. The actual part of the certificate that was signed can
be extracted with:
.PP
.Vb 1
\& openssl asn1parse \-in pca\-cert.pem \-out tbs \-noout \-strparse 4
.Ve
.PP
and its digest computed with:
.PP
.Vb 2
\& openssl md5 \-c tbs
\& MD5(tbs)= f3:46:9e:aa:1a:4a:73:c9:37:ea:93:00:48:25:08:b5
.Ve
.PP
which it can be seen agrees with the recovered value above.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBopenssl\fR\|(1),
\&\fBopenssl\-pkeyutl\fR\|(1),
\&\fBopenssl\-dgst\fR\|(1),
\&\fBopenssl\-rsa\fR\|(1),
\&\fBopenssl\-genrsa\fR\|(1)
.SH HISTORY
.IX Header "HISTORY"
This command was deprecated in OpenSSL 3.0.
.PP
The \fB\-engine\fR option was deprecated in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2023 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
