.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "OPENSSL-CMP 1ossl"
.TH OPENSSL-CMP 1ossl 2025-08-08 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
openssl\-cmp \- Certificate Management Protocol (CMP, RFC 4210) application
.SH SYNOPSIS
.IX Header "SYNOPSIS"
\&\fBopenssl\fR \fBcmp\fR
[\fB\-help\fR]
[\fB\-config\fR \fIfilename\fR]
[\fB\-section\fR \fInames\fR]
[\fB\-verbosity\fR \fIlevel\fR]
.PP
Generic message options:
.PP
[\fB\-cmd\fR \fIir|cr|kur|p10cr|rr|genm\fR]
[\fB\-infotype\fR \fIname\fR]
[\fB\-geninfo\fR \fIOID:int:N\fR]
.PP
Certificate enrollment options:
.PP
[\fB\-newkey\fR \fIfilename\fR|\fIuri\fR]
[\fB\-newkeypass\fR \fIarg\fR]
[\fB\-subject\fR \fIname\fR]
[\fB\-issuer\fR \fIname\fR]
[\fB\-days\fR \fInumber\fR]
[\fB\-reqexts\fR \fIname\fR]
[\fB\-sans\fR \fIspec\fR]
[\fB\-san_nodefault\fR]
[\fB\-policies\fR \fIname\fR]
[\fB\-policy_oids\fR \fInames\fR]
[\fB\-policy_oids_critical\fR]
[\fB\-popo\fR \fInumber\fR]
[\fB\-csr\fR \fIfilename\fR]
[\fB\-out_trusted\fR \fIfilenames\fR|\fIuris\fR]
[\fB\-implicit_confirm\fR]
[\fB\-disable_confirm\fR]
[\fB\-certout\fR \fIfilename\fR]
[\fB\-chainout\fR \fIfilename\fR]
.PP
Certificate enrollment and revocation options:
.PP
[\fB\-oldcert\fR \fIfilename\fR|\fIuri\fR]
[\fB\-revreason\fR \fInumber\fR]
.PP
Message transfer options:
.PP
[\fB\-server\fR \fI[http[s]://][userinfo@]host[:port][/path][?query][#fragment]\fR]
[\fB\-proxy\fR \fI[http[s]://][userinfo@]host[:port][/path][?query][#fragment]\fR]
[\fB\-no_proxy\fR \fIaddresses\fR]
[\fB\-recipient\fR \fIname\fR]
[\fB\-path\fR \fIremote_path\fR]
[\fB\-keep_alive\fR \fIvalue\fR]
[\fB\-msg_timeout\fR \fIseconds\fR]
[\fB\-total_timeout\fR \fIseconds\fR]
.PP
Server authentication options:
.PP
[\fB\-trusted\fR \fIfilenames\fR|\fIuris\fR]
[\fB\-untrusted\fR \fIfilenames\fR|\fIuris\fR]
[\fB\-srvcert\fR \fIfilename\fR|\fIuri\fR]
[\fB\-expect_sender\fR \fIname\fR]
[\fB\-ignore_keyusage\fR]
[\fB\-unprotected_errors\fR]
[\fB\-extracertsout\fR \fIfilename\fR]
[\fB\-cacertsout\fR \fIfilename\fR]
.PP
Client authentication and protection options:
.PP
[\fB\-ref\fR \fIvalue\fR]
[\fB\-secret\fR \fIarg\fR]
[\fB\-cert\fR \fIfilename\fR|\fIuri\fR]
[\fB\-own_trusted\fR \fIfilenames\fR|\fIuris\fR]
[\fB\-key\fR \fIfilename\fR|\fIuri\fR]
[\fB\-keypass\fR \fIarg\fR]
[\fB\-digest\fR \fIname\fR]
[\fB\-mac\fR \fIname\fR]
[\fB\-extracerts\fR \fIfilenames\fR|\fIuris\fR]
[\fB\-unprotected_requests\fR]
.PP
Credentials format options:
.PP
[\fB\-certform\fR \fIPEM|DER\fR]
[\fB\-keyform\fR \fIPEM|DER|P12|ENGINE\fR]
[\fB\-otherpass\fR \fIarg\fR]
[\fB\-engine\fR \fIid\fR]
[\fB\-provider\fR \fIname\fR]
[\fB\-provider\-path\fR \fIpath\fR]
[\fB\-propquery\fR \fIpropq\fR]
.PP
Random state options:
.PP
[\fB\-rand\fR \fIfiles\fR]
[\fB\-writerand\fR \fIfile\fR]
.PP
TLS connection options:
.PP
[\fB\-tls_used\fR]
[\fB\-tls_cert\fR \fIfilename\fR|\fIuri\fR]
[\fB\-tls_key\fR \fIfilename\fR|\fIuri\fR]
[\fB\-tls_keypass\fR \fIarg\fR]
[\fB\-tls_extra\fR \fIfilenames\fR|\fIuris\fR]
[\fB\-tls_trusted\fR \fIfilenames\fR|\fIuris\fR]
[\fB\-tls_host\fR \fIname\fR]
.PP
Client-side debugging options:
.PP
[\fB\-batch\fR]
[\fB\-repeat\fR \fInumber\fR]
[\fB\-reqin\fR \fIfilenames\fR]
[\fB\-reqin_new_tid\fR]
[\fB\-reqout\fR \fIfilenames\fR]
[\fB\-rspin\fR \fIfilenames\fR]
[\fB\-rspout\fR \fIfilenames\fR]
[\fB\-use_mock_srv\fR]
.PP
Mock server options:
.PP
[\fB\-port\fR \fInumber\fR]
[\fB\-max_msgs\fR \fInumber\fR]
[\fB\-srv_ref\fR \fIvalue\fR]
[\fB\-srv_secret\fR \fIarg\fR]
[\fB\-srv_cert\fR \fIfilename\fR|\fIuri\fR]
[\fB\-srv_key\fR \fIfilename\fR|\fIuri\fR]
[\fB\-srv_keypass\fR \fIarg\fR]
[\fB\-srv_trusted\fR \fIfilenames\fR|\fIuris\fR]
[\fB\-srv_untrusted\fR \fIfilenames\fR|\fIuris\fR]
[\fB\-rsp_cert\fR \fIfilename\fR|\fIuri\fR]
[\fB\-rsp_extracerts\fR \fIfilenames\fR|\fIuris\fR]
[\fB\-rsp_capubs\fR \fIfilenames\fR|\fIuris\fR]
[\fB\-poll_count\fR \fInumber\fR]
[\fB\-check_after\fR \fInumber\fR]
[\fB\-grant_implicitconf\fR]
[\fB\-pkistatus\fR \fInumber\fR]
[\fB\-failure\fR \fInumber\fR]
[\fB\-failurebits\fR \fInumber\fR]
[\fB\-statusstring\fR \fIarg\fR]
[\fB\-send_error\fR]
[\fB\-send_unprotected\fR]
[\fB\-send_unprot_err\fR]
[\fB\-accept_unprotected\fR]
[\fB\-accept_unprot_err\fR]
[\fB\-accept_raverified\fR]
.PP
Certificate verification options, for both CMP and TLS:
.PP
[\fB\-allow_proxy_certs\fR]
[\fB\-attime\fR \fItimestamp\fR]
[\fB\-no_check_time\fR]
[\fB\-check_ss_sig\fR]
[\fB\-crl_check\fR]
[\fB\-crl_check_all\fR]
[\fB\-explicit_policy\fR]
[\fB\-extended_crl\fR]
[\fB\-ignore_critical\fR]
[\fB\-inhibit_any\fR]
[\fB\-inhibit_map\fR]
[\fB\-partial_chain\fR]
[\fB\-policy\fR \fIarg\fR]
[\fB\-policy_check\fR]
[\fB\-policy_print\fR]
[\fB\-purpose\fR \fIpurpose\fR]
[\fB\-suiteB_128\fR]
[\fB\-suiteB_128_only\fR]
[\fB\-suiteB_192\fR]
[\fB\-trusted_first\fR]
[\fB\-no_alt_chains\fR]
[\fB\-use_deltas\fR]
[\fB\-auth_level\fR \fInum\fR]
[\fB\-verify_depth\fR \fInum\fR]
[\fB\-verify_email\fR \fIemail\fR]
[\fB\-verify_hostname\fR \fIhostname\fR]
[\fB\-verify_ip\fR \fIip\fR]
[\fB\-verify_name\fR \fIname\fR]
[\fB\-x509_strict\fR]
[\fB\-issuer_checks\fR]
.SH DESCRIPTION
.IX Header "DESCRIPTION"
The \fBcmp\fR command is a client implementation for the Certificate
Management Protocol (CMP) as defined in RFC4210.
It can be used to request certificates from a CA server,
update their certificates,
request certificates to be revoked, and perform other types of CMP requests.
.SH OPTIONS
.IX Header "OPTIONS"
.IP \fB\-help\fR 4
.IX Item "-help"
Display a summary of all options
.IP "\fB\-config\fR \fIfilename\fR" 4
.IX Item "-config filename"
Configuration file to use.
An empty string \f(CW""\fR means none.
Default filename is from the environment variable \f(CW\*(C`OPENSSL_CONF\*(C'\fR.
.IP "\fB\-section\fR \fInames\fR" 4
.IX Item "-section names"
Section(s) to use within config file defining CMP options.
An empty string \f(CW""\fR means no specific section.
Default is \f(CW\*(C`cmp\*(C'\fR.
.Sp
Multiple section names may be given, separated by commas and/or whitespace
(where in the latter case the whole argument must be enclosed in "...").
Contents of sections named later may override contents of sections named before.
In any case, as usual, the \f(CW\*(C`[default]\*(C'\fR section and finally the unnamed
section (as far as present) can provide per-option fallback values.
.IP "\fB\-verbosity\fR \fIlevel\fR" 4
.IX Item "-verbosity level"
Level of verbosity for logging, error output, etc.
0 = EMERG, 1 = ALERT, 2 = CRIT, 3 = ERR, 4 = WARN, 5 = NOTE,
6 = INFO, 7 = DEBUG, 8 = TRACE.
Defaults to 6 = INFO.
.SS "Generic message options"
.IX Subsection "Generic message options"
.IP "\fB\-cmd\fR \fIir|cr|kur|p10cr|rr|genm\fR" 4
.IX Item "-cmd ir|cr|kur|p10cr|rr|genm"
CMP command to execute.
Currently implemented commands are:
.RS 4
.IP "ir \   \- Initialization Request" 8
.IX Item "ir \  - Initialization Request"
.PD 0
.IP "cr \   \- Certificate Request" 8
.IX Item "cr \  - Certificate Request"
.IP "p10cr \- PKCS#10 Certification Request (for legacy support)" 8
.IX Item "p10cr - PKCS#10 Certification Request (for legacy support)"
.IP "kur \ \ \- Key Update Request" 8
.IX Item "kur \ \ - Key Update Request"
.IP "rr \   \- Revocation Request" 8
.IX Item "rr \  - Revocation Request"
.IP "genm  \- General Message" 8
.IX Item "genm - General Message"
.RE
.RS 4
.PD
.Sp
\&\fBir\fR requests initialization of an end entity into a PKI hierarchy
by issuing a first certificate.
.Sp
\&\fBcr\fR requests issuing an additional certificate for an end entity already
initialized to the PKI hierarchy.
.Sp
\&\fBp10cr\fR requests issuing an additional certificate similarly to \fBcr\fR
but using legacy PKCS#10 CSR format.
.Sp
\&\fBkur\fR requests a (key) update for an existing certificate.
.Sp
\&\fBrr\fR requests revocation of an existing certificate.
.Sp
\&\fBgenm\fR requests information using a General Message, where optionally
included \fBInfoTypeAndValue\fRs may be used to state which info is of interest.
Upon receipt of the General Response, information about all received
ITAV \fBinfoType\fRs is printed to stdout.
.RE
.IP "\fB\-infotype\fR \fIname\fR" 4
.IX Item "-infotype name"
Set InfoType name to use for requesting specific info in \fBgenm\fR,
e.g., \f(CW\*(C`signKeyPairTypes\*(C'\fR.
.IP "\fB\-geninfo\fR \fIOID:int:N\fR" 4
.IX Item "-geninfo OID:int:N"
generalInfo integer values to place in request PKIHeader with given OID,
e.g., \f(CW\*(C`*******:int:56789\*(C'\fR.
.SS "Certificate enrollment options"
.IX Subsection "Certificate enrollment options"
.IP "\fB\-newkey\fR \fIfilename\fR|\fIuri\fR" 4
.IX Item "-newkey filename|uri"
The source of the private or public key for the certificate being requested.
Defaults to the public key in the PKCS#10 CSR given with the \fB\-csr\fR option,
the public key of the reference certificate, or the current client key.
.Sp
The public portion of the key is placed in the certification request.
.Sp
Unless \fB\-cmd\fR \fIp10cr\fR, \fB\-popo\fR \fI\-1\fR, or \fB\-popo\fR \fI0\fR is given, the
private key will be needed as well to provide the proof of possession (POPO),
where the \fB\-key\fR option may provide a fallback.
.IP "\fB\-newkeypass\fR \fIarg\fR" 4
.IX Item "-newkeypass arg"
Pass phrase source for the key given with the \fB\-newkey\fR option.
If not given here, the password will be prompted for if needed.
.Sp
For more information about the format of \fIarg\fR see
\&\fBopenssl\-passphrase\-options\fR\|(1).
.IP "\fB\-subject\fR \fIname\fR" 4
.IX Item "-subject name"
X509 Distinguished Name (DN) of subject to use in the requested certificate
template.
If the NULL-DN (\f(CW"/"\fR) is given then no subject is placed in the template.
Default is the subject DN of any PKCS#10 CSR given with the \fB\-csr\fR option.
For KUR, a further fallback is the subject DN
of the reference certificate (see \fB\-oldcert\fR) if provided.
This fallback is used for IR and CR only if no SANs are set.
.Sp
If provided and neither \fB\-cert\fR nor \fB\-oldcert\fR is given,
the subject DN is used as fallback sender of outgoing CMP messages.
.Sp
The argument must be formatted as \fI/type0=value0/type1=value1/type2=...\fR.
Special characters may be escaped by \f(CW\*(C`\e\*(C'\fR (backslash); whitespace is retained.
Empty values are permitted, but the corresponding type will not be included.
Giving a single \f(CW\*(C`/\*(C'\fR will lead to an empty sequence of RDNs (a NULL-DN).
Multi-valued RDNs can be formed by placing a \f(CW\*(C`+\*(C'\fR character instead of a \f(CW\*(C`/\*(C'\fR
between the AttributeValueAssertions (AVAs) that specify the members of the set.
Example:
.Sp
\&\f(CW\*(C`/DC=org/DC=OpenSSL/DC=users/UID=123456+CN=John Doe\*(C'\fR
.IP "\fB\-issuer\fR \fIname\fR" 4
.IX Item "-issuer name"
X509 issuer Distinguished Name (DN) of the CA server
to place in the requested certificate template in IR/CR/KUR.
If the NULL-DN (\f(CW"/"\fR) is given then no issuer is placed in the template.
.Sp
If provided and neither \fB\-recipient\fR nor \fB\-srvcert\fR is given,
the issuer DN is used as fallback recipient of outgoing CMP messages.
.Sp
The argument must be formatted as \fI/type0=value0/type1=value1/type2=...\fR.
For details see the description of the \fB\-subject\fR option.
.IP "\fB\-days\fR \fInumber\fR" 4
.IX Item "-days number"
Number of days the new certificate is requested to be valid for, counting from
the current time of the host.
Also triggers the explicit request that the
validity period starts from the current time (as seen by the host).
.IP "\fB\-reqexts\fR \fIname\fR" 4
.IX Item "-reqexts name"
Name of section in OpenSSL config file defining certificate request extensions.
If the \fB\-csr\fR option is present, these extensions augment the extensions
contained the given PKCS#10 CSR, overriding any extensions with same OIDs.
.IP "\fB\-sans\fR \fIspec\fR" 4
.IX Item "-sans spec"
One or more IP addresses, DNS names, or URIs separated by commas or whitespace
(where in the latter case the whole argument must be enclosed in "...")
to add as Subject Alternative Name(s) (SAN) certificate request extension.
If the special element "critical" is given the SANs are flagged as critical.
Cannot be used if any Subject Alternative Name extension is set via \fB\-reqexts\fR.
.IP \fB\-san_nodefault\fR 4
.IX Item "-san_nodefault"
When Subject Alternative Names are not given via \fB\-sans\fR
nor defined via \fB\-reqexts\fR,
they are copied by default from the reference certificate (see \fB\-oldcert\fR).
This can be disabled by giving the \fB\-san_nodefault\fR option.
.IP "\fB\-policies\fR \fIname\fR" 4
.IX Item "-policies name"
Name of section in OpenSSL config file defining policies to be set
as certificate request extension.
This option cannot be used together with \fB\-policy_oids\fR.
.IP "\fB\-policy_oids\fR \fInames\fR" 4
.IX Item "-policy_oids names"
One or more OID(s), separated by commas and/or whitespace
(where in the latter case the whole argument must be enclosed in "...")
to add as certificate policies request extension.
This option cannot be used together with \fB\-policies\fR.
.IP \fB\-policy_oids_critical\fR 4
.IX Item "-policy_oids_critical"
Flag the policies given with \fB\-policy_oids\fR as critical.
.IP "\fB\-popo\fR \fInumber\fR" 4
.IX Item "-popo number"
Proof-of-possession (POPO) method to use for IR/CR/KUR; values: \f(CW\-1\fR..<2> where
\&\f(CW\-1\fR = NONE, \f(CW0\fR = RAVERIFIED, \f(CW1\fR = SIGNATURE (default), \f(CW2\fR = KEYENC.
.Sp
Note that a signature-based POPO can only be produced if a private key
is provided via the \fB\-newkey\fR or \fB\-key\fR options.
.IP "\fB\-csr\fR \fIfilename\fR" 4
.IX Item "-csr filename"
PKCS#10 CSR in PEM or DER format containing a certificate request.
With \fB\-cmd\fR \fIp10cr\fR it is used directly in a legacy P10CR message.
.Sp
When used with \fB\-cmd\fR \fIir\fR, \fIcr\fR, or \fIkur\fR,
it is transformed into the respective regular CMP request.
In this case, a private key must be provided (with \fB\-newkey\fR or \fB\-key\fR)
for the proof of possession (unless \fB\-popo\fR \fI\-1\fR or \fB\-popo\fR \fI0\fR is used)
and the respective public key is placed in the certification request
(rather than taking over the public key contained in the PKCS#10 CSR).
.Sp
PKCS#10 CSR input may also be used with \fB\-cmd\fR \fIrr\fR
to specify the certificate to be revoked
via the included subject name and public key.
.IP "\fB\-out_trusted\fR \fIfilenames\fR|\fIuris\fR" 4
.IX Item "-out_trusted filenames|uris"
Trusted certificate(s) to use for validating the newly enrolled certificate.
During this verification, any certificate status checking is disabled.
.Sp
Multiple sources may be given, separated by commas and/or whitespace
(where in the latter case the whole argument must be enclosed in "...").
Each source may contain multiple certificates.
.Sp
The certificate verification options
\&\fB\-verify_hostname\fR, \fB\-verify_ip\fR, and \fB\-verify_email\fR
only affect the certificate verification enabled via this option.
.IP \fB\-implicit_confirm\fR 4
.IX Item "-implicit_confirm"
Request implicit confirmation of newly enrolled certificates.
.IP \fB\-disable_confirm\fR 4
.IX Item "-disable_confirm"
Do not send certificate confirmation message for newly enrolled certificate
without requesting implicit confirmation
to cope with broken servers not supporting implicit confirmation correctly.
\&\fBWARNING:\fR This leads to behavior violating RFC 4210.
.IP "\fB\-certout\fR \fIfilename\fR" 4
.IX Item "-certout filename"
The file where the newly enrolled certificate should be saved.
.IP "\fB\-chainout\fR \fIfilename\fR" 4
.IX Item "-chainout filename"
The file where the chain of the newly enrolled certificate should be saved.
.SS "Certificate enrollment and revocation options"
.IX Subsection "Certificate enrollment and revocation options"
.IP "\fB\-oldcert\fR \fIfilename\fR|\fIuri\fR" 4
.IX Item "-oldcert filename|uri"
The certificate to be updated (i.e., renewed or re-keyed) in Key Update Request
(KUR) messages or to be revoked in Revocation Request (RR) messages.
For KUR the certificate to be updated defaults to \fB\-cert\fR,
and the resulting certificate is called \fIreference certificate\fR.
For RR the certificate to be revoked can also be specified using \fB\-csr\fR.
.Sp
The reference certificate, if any, is also used for
deriving default subject DN and Subject Alternative Names and the
default issuer entry in the requested certificate template of an IR/CR/KUR.
Its public key is used as a fallback in the template of certification requests.
Its subject is used as sender of outgoing messages if \fB\-cert\fR is not given.
Its issuer is used as default recipient in CMP message headers
if neither \fB\-recipient\fR, \fB\-srvcert\fR, nor \fB\-issuer\fR is given.
.IP "\fB\-revreason\fR \fInumber\fR" 4
.IX Item "-revreason number"
Set CRLReason to be included in revocation request (RR); values: \f(CW0\fR..\f(CW10\fR
or \f(CW\-1\fR for none (which is the default).
.Sp
Reason numbers defined in RFC 5280 are:
.Sp
.Vb 10
\&   CRLReason ::= ENUMERATED {
\&        unspecified             (0),
\&        keyCompromise           (1),
\&        cACompromise            (2),
\&        affiliationChanged      (3),
\&        superseded              (4),
\&        cessationOfOperation    (5),
\&        certificateHold         (6),
\&        \-\- value 7 is not used
\&        removeFromCRL           (8),
\&        privilegeWithdrawn      (9),
\&        aACompromise           (10)
\&    }
.Ve
.SS "Message transfer options"
.IX Subsection "Message transfer options"
.IP "\fB\-server\fR \fI[http[s]://][userinfo@]host[:port][/path][?query][#fragment]\fR" 4
.IX Item "-server [http[s]://][userinfo@]host[:port][/path][?query][#fragment]"
The DNS hostname or IP address and optionally port
of the CMP server to connect to using HTTP(S).
This option excludes \fI\-port\fR and \fI\-use_mock_srv\fR.
It is ignored if \fI\-rspin\fR is given with enough filename arguments.
.Sp
The scheme \f(CW\*(C`https\*(C'\fR may be given only if the \fB\-tls_used\fR option is used.
In this case the default port is 443, else 80.
The optional userinfo and fragment components are ignored.
Any given query component is handled as part of the path component.
If a path is included it provides the default value for the \fB\-path\fR option.
.IP "\fB\-proxy\fR \fI[http[s]://][userinfo@]host[:port][/path][?query][#fragment]\fR" 4
.IX Item "-proxy [http[s]://][userinfo@]host[:port][/path][?query][#fragment]"
The HTTP(S) proxy server to use for reaching the CMP server unless \fB\-no_proxy\fR
applies, see below.
The proxy port defaults to 80 or 443 if the scheme is \f(CW\*(C`https\*(C'\fR; apart from that
the optional \f(CW\*(C`http://\*(C'\fR or \f(CW\*(C`https://\*(C'\fR prefix is ignored (note that TLS may be
selected by \fB\-tls_used\fR), as well as any path, userinfo, and query, and fragment
components.
Defaults to the environment variable \f(CW\*(C`http_proxy\*(C'\fR if set, else \f(CW\*(C`HTTP_PROXY\*(C'\fR
in case no TLS is used, otherwise \f(CW\*(C`https_proxy\*(C'\fR if set, else \f(CW\*(C`HTTPS_PROXY\*(C'\fR.
This option is ignored if \fI\-server\fR is not given.
.IP "\fB\-no_proxy\fR \fIaddresses\fR" 4
.IX Item "-no_proxy addresses"
List of IP addresses and/or DNS names of servers
not to use an HTTP(S) proxy for, separated by commas and/or whitespace
(where in the latter case the whole argument must be enclosed in "...").
Default is from the environment variable \f(CW\*(C`no_proxy\*(C'\fR if set, else \f(CW\*(C`NO_PROXY\*(C'\fR.
This option is ignored if \fI\-server\fR is not given.
.IP "\fB\-recipient\fR \fIname\fR" 4
.IX Item "-recipient name"
Distinguished Name (DN) to use in the recipient field of CMP request message
headers, i.e., the CMP server (usually the addressed CA).
.Sp
The recipient field in the header of a CMP message is mandatory.
If not given explicitly the recipient is determined in the following order:
the subject of the CMP server certificate given with the \fB\-srvcert\fR option,
the \fB\-issuer\fR option,
the issuer of the certificate given with the \fB\-oldcert\fR option,
the issuer of the CMP client certificate (\fB\-cert\fR option),
as far as any of those is present, else the NULL-DN as last resort.
.Sp
The argument must be formatted as \fI/type0=value0/type1=value1/type2=...\fR.
For details see the description of the \fB\-subject\fR option.
.IP "\fB\-path\fR \fIremote_path\fR" 4
.IX Item "-path remote_path"
HTTP path at the CMP server (aka CMP alias) to use for POST requests.
Defaults to any path given with \fB\-server\fR, else \f(CW"/"\fR.
.IP "\fB\-keep_alive\fR \fIvalue\fR" 4
.IX Item "-keep_alive value"
If the given value is 0 then HTTP connections are not kept open
after receiving a response, which is the default behavior for HTTP 1.0.
If the value is 1 or 2 then persistent connections are requested.
If the value is 2 then persistent connections are required,
i.e., in case the server does not grant them an error occurs.
The default value is 1, which means preferring to keep the connection open.
.IP "\fB\-msg_timeout\fR \fIseconds\fR" 4
.IX Item "-msg_timeout seconds"
Number of seconds a CMP request-response message round trip
is allowed to take before a timeout error is returned.
A value <= 0 means no limitation (waiting indefinitely).
Default is to use the \fB\-total_timeout\fR setting.
.IP "\fB\-total_timeout\fR \fIseconds\fR" 4
.IX Item "-total_timeout seconds"
Maximum total number of seconds a transaction may take,
including polling etc.
A value <= 0 means no limitation (waiting indefinitely).
Default is 0.
.SS "Server authentication options"
.IX Subsection "Server authentication options"
.IP "\fB\-trusted\fR \fIfilenames\fR|\fIuris\fR" 4
.IX Item "-trusted filenames|uris"
The certificate(s), typically of root CAs, the client shall use as trust anchors
when validating signature-based protection of CMP response messages.
This option is ignored if the \fB\-srvcert\fR option is given as well.
It provides more flexibility than \fB\-srvcert\fR because the CMP protection
certificate of the server is not pinned but may be any certificate
from which a chain to one of the given trust anchors can be constructed.
.Sp
If none of \fB\-trusted\fR, \fB\-srvcert\fR, and \fB\-secret\fR is given, message validation
errors will be thrown unless \fB\-unprotected_errors\fR permits an exception.
.Sp
Multiple sources may be given, separated by commas and/or whitespace
(where in the latter case the whole argument must be enclosed in "...").
Each source may contain multiple certificates.
.Sp
The certificate verification options
\&\fB\-verify_hostname\fR, \fB\-verify_ip\fR, and \fB\-verify_email\fR
have no effect on the certificate verification enabled via this option.
.IP "\fB\-untrusted\fR \fIfilenames\fR|\fIuris\fR" 4
.IX Item "-untrusted filenames|uris"
Non-trusted intermediate CA certificate(s).
Any extra certificates given with the \fB\-cert\fR option are appended to it.
All these certificates may be useful for cert path construction
for the own CMP signer certificate (to include in the extraCerts field of
request messages) and for the TLS client certificate (if TLS is enabled)
as well as for chain building
when validating server certificates (checking signature-based
CMP message protection) and when validating newly enrolled certificates.
.Sp
Multiple filenames or URLs may be given, separated by commas and/or whitespace.
Each source may contain multiple certificates.
.IP "\fB\-srvcert\fR \fIfilename\fR|\fIuri\fR" 4
.IX Item "-srvcert filename|uri"
The specific CMP server certificate to expect and directly trust (even if it is
expired) when verifying signature-based protection of CMP response messages.
This pins the accepted server and results in ignoring the \fB\-trusted\fR option.
.Sp
If set, the subject of the certificate is also used
as default value for the recipient of CMP requests
and as default value for the expected sender of CMP responses.
.IP "\fB\-expect_sender\fR \fIname\fR" 4
.IX Item "-expect_sender name"
Distinguished Name (DN) expected in the sender field of incoming CMP messages.
Defaults to the subject DN of the pinned \fB\-srvcert\fR, if any.
.Sp
This can be used to make sure that only a particular entity is accepted as
CMP message signer, and attackers are not able to use arbitrary certificates
of a trusted PKI hierarchy to fraudulently pose as a CMP server.
Note that this option gives slightly more freedom than setting the \fB\-srvcert\fR,
which pins the server to the holder of a particular certificate, while the
expected sender name will continue to match after updates of the server cert.
.Sp
The argument must be formatted as \fI/type0=value0/type1=value1/type2=...\fR.
For details see the description of the \fB\-subject\fR option.
.IP \fB\-ignore_keyusage\fR 4
.IX Item "-ignore_keyusage"
Ignore key usage restrictions in CMP signer certificates when validating
signature-based protection of incoming CMP messages.
By default, \f(CW\*(C`digitalSignature\*(C'\fR must be allowed by CMP signer certificates.
.IP \fB\-unprotected_errors\fR 4
.IX Item "-unprotected_errors"
Accept missing or invalid protection of negative responses from the server.
This applies to the following message types and contents:
.RS 4
.IP \(bu 4
error messages
.IP \(bu 4
negative certificate responses (IP/CP/KUP)
.IP \(bu 4
negative revocation responses (RP)
.IP \(bu 4
negative PKIConf messages
.RE
.RS 4
.Sp
\&\fBWARNING:\fR This setting leads to unspecified behavior and it is meant
exclusively to allow interoperability with server implementations violating
RFC 4210, e.g.:
.IP \(bu 4
section ******* allows exceptions from protecting only for special
cases:
"There MAY be cases in which the PKIProtection BIT STRING is deliberately not
used to protect a message [...] because other protection, external to PKIX, will
be applied instead."
.IP \(bu 4
section 5.3.21 is clear on ErrMsgContent: "The CA MUST always sign it
with a signature key."
.IP \(bu 4
appendix D.4 shows PKIConf message having protection
.RE
.RS 4
.RE
.IP "\fB\-extracertsout\fR \fIfilename\fR" 4
.IX Item "-extracertsout filename"
The file where to save all certificates contained in the extraCerts field
of the last received response message (except for pollRep and PKIConf).
.IP "\fB\-cacertsout\fR \fIfilename\fR" 4
.IX Item "-cacertsout filename"
The file where to save any CA certificates contained in the caPubs field of
the last received certificate response (i.e., IP, CP, or KUP) message.
.SS "Client authentication options"
.IX Subsection "Client authentication options"
.IP "\fB\-ref\fR \fIvalue\fR" 4
.IX Item "-ref value"
Reference number/string/value to use as fallback senderKID; this is required
if no sender name can be determined from the \fB\-cert\fR or <\-subject> options and
is typically used when authenticating with pre-shared key (password-based MAC).
.IP "\fB\-secret\fR \fIarg\fR" 4
.IX Item "-secret arg"
Provides the source of a secret value to use with MAC-based message protection.
This takes precedence over the \fB\-cert\fR and \fB\-key\fR options.
The secret is used for creating MAC-based protection of outgoing messages
and for validating incoming messages that have MAC-based protection.
The algorithm used by default is Password-Based Message Authentication Code (PBM)
as defined in RFC 4210 section *******.
.Sp
For more information about the format of \fIarg\fR see
\&\fBopenssl\-passphrase\-options\fR\|(1).
.IP "\fB\-cert\fR \fIfilename\fR|\fIuri\fR" 4
.IX Item "-cert filename|uri"
The client's current CMP signer certificate.
Requires the corresponding key to be given with \fB\-key\fR.
.Sp
The subject and the public key contained in this certificate
serve as fallback values in the certificate template of IR/CR/KUR messages.
.Sp
The subject of this certificate will be used as sender of outgoing CMP messages,
while the subject of \fB\-oldcert\fR or \fB\-subjectName\fR may provide fallback values.
.Sp
The issuer of this certificate is used as one of the recipient fallback values
and as fallback issuer entry in the certificate template of IR/CR/KUR messages.
.Sp
When performing signature-based message protection,
this "protection certificate", also called "signer certificate",
will be included first in the extraCerts field of outgoing messages
and the signature is done with the corresponding key.
In Initialization Request (IR) messages this can be used for authenticating
using an external entity certificate as defined in appendix E.7 of RFC 4210.
.Sp
For Key Update Request (KUR) messages this is also used as
the certificate to be updated if the \fB\-oldcert\fR option is not given.
.Sp
If the file includes further certs, they are appended to the untrusted certs
because they typically constitute the chain of the client certificate, which
is included in the extraCerts field in signature-protected request messages.
.IP "\fB\-own_trusted\fR \fIfilenames\fR|\fIuris\fR" 4
.IX Item "-own_trusted filenames|uris"
If this list of certificates is provided then the chain built for
the client-side CMP signer certificate given with the \fB\-cert\fR option
is verified using the given certificates as trust anchors.
.Sp
Multiple sources may be given, separated by commas and/or whitespace
(where in the latter case the whole argument must be enclosed in "...").
Each source may contain multiple certificates.
.Sp
The certificate verification options
\&\fB\-verify_hostname\fR, \fB\-verify_ip\fR, and \fB\-verify_email\fR
have no effect on the certificate verification enabled via this option.
.IP "\fB\-key\fR \fIfilename\fR|\fIuri\fR" 4
.IX Item "-key filename|uri"
The corresponding private key file for the client's current certificate given in
the \fB\-cert\fR option.
This will be used for signature-based message protection unless the \fB\-secret\fR
option indicating MAC-based protection or \fB\-unprotected_requests\fR is given.
.Sp
It is also used as a fallback for the \fB\-newkey\fR option with IR/CR/KUR messages.
.IP "\fB\-keypass\fR \fIarg\fR" 4
.IX Item "-keypass arg"
Pass phrase source for the private key given with the \fB\-key\fR option.
Also used for \fB\-cert\fR and \fB\-oldcert\fR in case it is an encrypted PKCS#12 file.
If not given here, the password will be prompted for if needed.
.Sp
For more information about the format of \fIarg\fR see
\&\fBopenssl\-passphrase\-options\fR\|(1).
.IP "\fB\-digest\fR \fIname\fR" 4
.IX Item "-digest name"
Specifies name of supported digest to use in RFC 4210's MSG_SIG_ALG
and as the one-way function (OWF) in \f(CW\*(C`MSG_MAC_ALG\*(C'\fR.
If applicable, this is used for message protection and
proof-of-possession (POPO) signatures.
To see the list of supported digests, use \f(CW\*(C`openssl list \-digest\-commands\*(C'\fR.
Defaults to \f(CW\*(C`sha256\*(C'\fR.
.IP "\fB\-mac\fR \fIname\fR" 4
.IX Item "-mac name"
Specifies the name of the MAC algorithm in \f(CW\*(C`MSG_MAC_ALG\*(C'\fR.
To get the names of supported MAC algorithms use \f(CW\*(C`openssl list \-mac\-algorithms\*(C'\fR
and possibly combine such a name with the name of a supported digest algorithm,
e.g., hmacWithSHA256.
Defaults to \f(CW\*(C`hmac\-sha1\*(C'\fR as per RFC 4210.
.IP "\fB\-extracerts\fR \fIfilenames\fR|\fIuris\fR" 4
.IX Item "-extracerts filenames|uris"
Certificates to append in the extraCerts field when sending messages.
They can be used as the default CMP signer certificate chain to include.
.Sp
Multiple sources may be given, separated by commas and/or whitespace
(where in the latter case the whole argument must be enclosed in "...").
Each source may contain multiple certificates.
.IP \fB\-unprotected_requests\fR 4
.IX Item "-unprotected_requests"
Send request messages without CMP-level protection.
.SS "Credentials format options"
.IX Subsection "Credentials format options"
.IP "\fB\-certform\fR \fIPEM|DER\fR" 4
.IX Item "-certform PEM|DER"
File format to use when saving a certificate to a file.
Default value is PEM.
.IP "\fB\-keyform\fR \fIPEM|DER|P12|ENGINE\fR" 4
.IX Item "-keyform PEM|DER|P12|ENGINE"
The format of the key input; unspecified by default.
See "Format Options" in \fBopenssl\fR\|(1) for details.
.IP "\fB\-otherpass\fR \fIarg\fR" 4
.IX Item "-otherpass arg"
Pass phrase source for certificate given with the \fB\-trusted\fR, \fB\-untrusted\fR,
\&\fB\-own_trusted\fR, \fB\-srvcert\fR, \fB\-out_trusted\fR, \fB\-extracerts\fR,
\&\fB\-srv_trusted\fR, \fB\-srv_untrusted\fR, \fB\-rsp_extracerts\fR, \fB\-rsp_capubs\fR,
\&\fB\-tls_extra\fR, and \fB\-tls_trusted\fR options.
If not given here, the password will be prompted for if needed.
.Sp
For more information about the format of \fIarg\fR see
\&\fBopenssl\-passphrase\-options\fR\|(1).
.IP "\fB\-engine\fR \fIid\fR" 4
.IX Item "-engine id"
See "Engine Options" in \fBopenssl\fR\|(1).
This option is deprecated.
.Sp
As an alternative to using this combination:
.Sp
.Vb 1
\&    \-engine {engineid} \-key {keyid} \-keyform ENGINE
.Ve
.Sp
\&... it's also possible to just give the key ID in URI form to \fB\-key\fR,
like this:
.Sp
.Vb 1
\&    \-key org.openssl.engine:{engineid}:{keyid}
.Ve
.Sp
This applies to all options specifying keys: \fB\-key\fR, \fB\-newkey\fR, and
\&\fB\-tls_key\fR.
.SS "Provider options"
.IX Subsection "Provider options"
.IP "\fB\-provider\fR \fIname\fR" 4
.IX Item "-provider name"
.PD 0
.IP "\fB\-provider\-path\fR \fIpath\fR" 4
.IX Item "-provider-path path"
.IP "\fB\-propquery\fR \fIpropq\fR" 4
.IX Item "-propquery propq"
.PD
See "Provider Options" in \fBopenssl\fR\|(1), \fBprovider\fR\|(7), and \fBproperty\fR\|(7).
.SS "Random state options"
.IX Subsection "Random state options"
.IP "\fB\-rand\fR \fIfiles\fR, \fB\-writerand\fR \fIfile\fR" 4
.IX Item "-rand files, -writerand file"
See "Random State Options" in \fBopenssl\fR\|(1) for details.
.SS "TLS connection options"
.IX Subsection "TLS connection options"
.IP \fB\-tls_used\fR 4
.IX Item "-tls_used"
Enable using TLS (even when other TLS-related options are not set)
for message exchange with CMP server via HTTP.
This option is not supported with the \fI\-port\fR option.
It is ignored if the \fI\-server\fR option is not given or \fI\-use_mock_srv\fR is given
or \fI\-rspin\fR is given with enough filename arguments.
.Sp
The following TLS-related options are ignored
if \fB\-tls_used\fR is not given or does not take effect.
.IP "\fB\-tls_cert\fR \fIfilename\fR|\fIuri\fR" 4
.IX Item "-tls_cert filename|uri"
Client's TLS certificate.
If the source includes further certs they are used (along with \fB\-untrusted\fR
certs) for constructing the client cert chain provided to the TLS server.
.IP "\fB\-tls_key\fR \fIfilename\fR|\fIuri\fR" 4
.IX Item "-tls_key filename|uri"
Private key for the client's TLS certificate.
.IP "\fB\-tls_keypass\fR \fIarg\fR" 4
.IX Item "-tls_keypass arg"
Pass phrase source for client's private TLS key \fB\-tls_key\fR.
Also used for \fB\-tls_cert\fR in case it is an encrypted PKCS#12 file.
If not given here, the password will be prompted for if needed.
.Sp
For more information about the format of \fIarg\fR see
\&\fBopenssl\-passphrase\-options\fR\|(1).
.IP "\fB\-tls_extra\fR \fIfilenames\fR|\fIuris\fR" 4
.IX Item "-tls_extra filenames|uris"
Extra certificates to provide to TLS server during TLS handshake
.IP "\fB\-tls_trusted\fR \fIfilenames\fR|\fIuris\fR" 4
.IX Item "-tls_trusted filenames|uris"
Trusted certificate(s) to use for validating the TLS server certificate.
This implies hostname validation.
.Sp
Multiple sources may be given, separated by commas and/or whitespace
(where in the latter case the whole argument must be enclosed in "...").
Each source may contain multiple certificates.
.Sp
The certificate verification options
\&\fB\-verify_hostname\fR, \fB\-verify_ip\fR, and \fB\-verify_email\fR
have no effect on the certificate verification enabled via this option.
.IP "\fB\-tls_host\fR \fIname\fR" 4
.IX Item "-tls_host name"
Address to be checked during hostname validation.
This may be a DNS name or an IP address.
If not given it defaults to the \fB\-server\fR address.
.SS "Client-side debugging options"
.IX Subsection "Client-side debugging options"
.IP \fB\-batch\fR 4
.IX Item "-batch"
Do not interactively prompt for input, for instance when a password is needed.
This can be useful for batch processing and testing.
.IP "\fB\-repeat\fR \fInumber\fR" 4
.IX Item "-repeat number"
Invoke the command the given positive number of times with the same parameters.
Default is one invocation.
.IP "\fB\-reqin\fR \fIfilenames\fR" 4
.IX Item "-reqin filenames"
Take the sequence of CMP requests to send to the server from the given file(s)
rather than from the sequence of requests produced internally.
.Sp
This option is ignored if the \fB\-rspin\fR option is given
because in the latter case no requests are actually sent.
.Sp
Multiple filenames may be given, separated by commas and/or whitespace
(where in the latter case the whole argument must be enclosed in "...").
.Sp
The files are read as far as needed to complete the transaction
and filenames have been provided.  If more requests are needed,
the remaining ones are taken from the items at the respective position
in the sequence of requests produced internally.
.Sp
The client needs to update the recipNonce field in the given requests (except
for the first one) in order to satisfy the checks to be performed by the server.
This causes re-protection (if protecting requests is required).
.IP \fB\-reqin_new_tid\fR 4
.IX Item "-reqin_new_tid"
Use a fresh transactionID for CMP request messages read using \fB\-reqin\fR,
which causes their reprotection (if protecting requests is required).
This may be needed in case the sequence of requests is reused
and the CMP server complains that the transaction ID has already been used.
.IP "\fB\-reqout\fR \fIfilenames\fR" 4
.IX Item "-reqout filenames"
Save the sequence of CMP requests created by the client to the given file(s).
These requests are not sent to the server if the \fB\-reqin\fR option is used, too.
.Sp
Multiple filenames may be given, separated by commas and/or whitespace.
.Sp
Files are written as far as needed to save the transaction
and filenames have been provided.
If the transaction contains more requests, the remaining ones are not saved.
.IP "\fB\-rspin\fR \fIfilenames\fR" 4
.IX Item "-rspin filenames"
Process the sequence of CMP responses provided in the given file(s),
not contacting any given server,
as long as enough filenames are provided to complete the transaction.
.Sp
Multiple filenames may be given, separated by commas and/or whitespace.
.Sp
Any server specified via the \fI\-server\fR or \fI\-use_mock_srv\fR options is contacted
only if more responses are needed to complete the transaction.
In this case the transaction will fail
unless the server has been prepared to continue the already started transaction.
.IP "\fB\-rspout\fR \fIfilenames\fR" 4
.IX Item "-rspout filenames"
Save the sequence of actually used CMP responses to the given file(s).
These have been received from the server unless \fB\-rspin\fR takes effect.
.Sp
Multiple filenames may be given, separated by commas and/or whitespace.
.Sp
Files are written as far as needed to save the responses
contained in the transaction and filenames have been provided.
If the transaction contains more responses, the remaining ones are not saved.
.IP \fB\-use_mock_srv\fR 4
.IX Item "-use_mock_srv"
Test the client using the internal CMP server mock-up at API level,
bypassing socket-based transfer via HTTP.
This excludes the \fB\-server\fR and \fB\-port\fR options.
.SS "Mock server options"
.IX Subsection "Mock server options"
.IP "\fB\-port\fR \fInumber\fR" 4
.IX Item "-port number"
Act as HTTP-based CMP server mock-up listening on the given port.
This excludes the \fB\-server\fR and \fB\-use_mock_srv\fR options.
The \fB\-rspin\fR, \fB\-rspout\fR, \fB\-reqin\fR, and \fB\-reqout\fR options
so far are not supported in this mode.
.IP "\fB\-max_msgs\fR \fInumber\fR" 4
.IX Item "-max_msgs number"
Maximum number of CMP (request) messages the CMP HTTP server mock-up
should handle, which must be nonnegative.
The default value is 0, which means that no limit is imposed.
In any case the server terminates on internal errors, but not when it
detects a CMP-level error that it can successfully answer with an error message.
.IP "\fB\-srv_ref\fR \fIvalue\fR" 4
.IX Item "-srv_ref value"
Reference value to use as senderKID of server in case no \fB\-srv_cert\fR is given.
.IP "\fB\-srv_secret\fR \fIarg\fR" 4
.IX Item "-srv_secret arg"
Password source for server authentication with a pre-shared key (secret).
.IP "\fB\-srv_cert\fR \fIfilename\fR|\fIuri\fR" 4
.IX Item "-srv_cert filename|uri"
Certificate of the server.
.IP "\fB\-srv_key\fR \fIfilename\fR|\fIuri\fR" 4
.IX Item "-srv_key filename|uri"
Private key used by the server for signing messages.
.IP "\fB\-srv_keypass\fR \fIarg\fR" 4
.IX Item "-srv_keypass arg"
Server private key (and cert) file pass phrase source.
.IP "\fB\-srv_trusted\fR \fIfilenames\fR|\fIuris\fR" 4
.IX Item "-srv_trusted filenames|uris"
Trusted certificates for client authentication.
.Sp
The certificate verification options
\&\fB\-verify_hostname\fR, \fB\-verify_ip\fR, and \fB\-verify_email\fR
have no effect on the certificate verification enabled via this option.
.IP "\fB\-srv_untrusted\fR \fIfilenames\fR|\fIuris\fR" 4
.IX Item "-srv_untrusted filenames|uris"
Intermediate CA certs that may be useful when validating client certificates.
.IP "\fB\-rsp_cert\fR \fIfilename\fR|\fIuri\fR" 4
.IX Item "-rsp_cert filename|uri"
Certificate to be returned as mock enrollment result.
.IP "\fB\-rsp_extracerts\fR \fIfilenames\fR|\fIuris\fR" 4
.IX Item "-rsp_extracerts filenames|uris"
Extra certificates to be included in mock certification responses.
.IP "\fB\-rsp_capubs\fR \fIfilenames\fR|\fIuris\fR" 4
.IX Item "-rsp_capubs filenames|uris"
CA certificates to be included in mock Initialization Response (IP) message.
.IP "\fB\-poll_count\fR \fInumber\fR" 4
.IX Item "-poll_count number"
Number of times the client must poll before receiving a certificate.
.IP "\fB\-check_after\fR \fInumber\fR" 4
.IX Item "-check_after number"
The checkAfter value (number of seconds to wait) to include in poll response.
.IP \fB\-grant_implicitconf\fR 4
.IX Item "-grant_implicitconf"
Grant implicit confirmation of newly enrolled certificate.
.IP "\fB\-pkistatus\fR \fInumber\fR" 4
.IX Item "-pkistatus number"
PKIStatus to be included in server response.
Valid range is 0 (accepted) .. 6 (keyUpdateWarning).
.IP "\fB\-failure\fR \fInumber\fR" 4
.IX Item "-failure number"
A single failure info bit number to be included in server response.
Valid range is 0 (badAlg) .. 26 (duplicateCertReq).
.IP "\fB\-failurebits\fR \fInumber\fR Number representing failure bits to be included in server response. Valid range is 0 .. 2^27 \- 1." 4
.IX Item "-failurebits number Number representing failure bits to be included in server response. Valid range is 0 .. 2^27 - 1."
.PD 0
.IP "\fB\-statusstring\fR \fIarg\fR" 4
.IX Item "-statusstring arg"
.PD
Text to be included as status string in server response.
.IP \fB\-send_error\fR 4
.IX Item "-send_error"
Force server to reply with error message.
.IP \fB\-send_unprotected\fR 4
.IX Item "-send_unprotected"
Send response messages without CMP-level protection.
.IP \fB\-send_unprot_err\fR 4
.IX Item "-send_unprot_err"
In case of negative responses, server shall send unprotected error messages,
certificate responses (IP/CP/KUP), and revocation responses (RP).
WARNING: This setting leads to behavior violating RFC 4210.
.IP \fB\-accept_unprotected\fR 4
.IX Item "-accept_unprotected"
Accept missing or invalid protection of requests.
.IP \fB\-accept_unprot_err\fR 4
.IX Item "-accept_unprot_err"
Accept unprotected error messages from client.
So far this has no effect because the server does not accept any error messages.
.IP \fB\-accept_raverified\fR 4
.IX Item "-accept_raverified"
Accept RAVERIFED as proof of possession (POPO).
.SS "Certificate verification options, for both CMP and TLS"
.IX Subsection "Certificate verification options, for both CMP and TLS"
.IP "\fB\-allow_proxy_certs\fR, \fB\-attime\fR, \fB\-no_check_time\fR, \fB\-check_ss_sig\fR, \fB\-crl_check\fR, \fB\-crl_check_all\fR, \fB\-explicit_policy\fR, \fB\-extended_crl\fR, \fB\-ignore_critical\fR, \fB\-inhibit_any\fR, \fB\-inhibit_map\fR, \fB\-no_alt_chains\fR, \fB\-partial_chain\fR, \fB\-policy\fR, \fB\-policy_check\fR, \fB\-policy_print\fR, \fB\-purpose\fR, \fB\-suiteB_128\fR, \fB\-suiteB_128_only\fR, \fB\-suiteB_192\fR, \fB\-trusted_first\fR, \fB\-use_deltas\fR, \fB\-auth_level\fR, \fB\-verify_depth\fR, \fB\-verify_email\fR, \fB\-verify_hostname\fR, \fB\-verify_ip\fR, \fB\-verify_name\fR, \fB\-x509_strict\fR \fB\-issuer_checks\fR" 4
.IX Item "-allow_proxy_certs, -attime, -no_check_time, -check_ss_sig, -crl_check, -crl_check_all, -explicit_policy, -extended_crl, -ignore_critical, -inhibit_any, -inhibit_map, -no_alt_chains, -partial_chain, -policy, -policy_check, -policy_print, -purpose, -suiteB_128, -suiteB_128_only, -suiteB_192, -trusted_first, -use_deltas, -auth_level, -verify_depth, -verify_email, -verify_hostname, -verify_ip, -verify_name, -x509_strict -issuer_checks"
Set various options of certificate chain verification.
See "Verification Options" in \fBopenssl\-verification\-options\fR\|(1) for details.
.Sp
The certificate verification options
\&\fB\-verify_hostname\fR, \fB\-verify_ip\fR, and \fB\-verify_email\fR
only affect the certificate verification enabled via the \fB\-out_trusted\fR option.
.SH NOTES
.IX Header "NOTES"
When a client obtains from a CMP server CA certificates that it is going to
trust, for instance via the \f(CW\*(C`caPubs\*(C'\fR field of a certificate response,
authentication of the CMP server is particularly critical.
So special care must be taken setting up server authentication
using \fB\-trusted\fR and related options for certificate-based authentication
or \fB\-secret\fR for MAC-based protection.
.PP
When setting up CMP configurations and experimenting with enrollment options
typically various errors occur until the configuration is correct and complete.
When the CMP server reports an error the client will by default
check the protection of the CMP response message.
Yet some CMP services tend not to protect negative responses.
In this case the client will reject them, and thus their contents are not shown
although they usually contain hints that would be helpful for diagnostics.
For assisting in such cases the CMP client offers a workaround via the
\&\fB\-unprotected_errors\fR option, which allows accepting such negative messages.
.SH EXAMPLES
.IX Header "EXAMPLES"
.SS "Simple examples using the default OpenSSL configuration file"
.IX Subsection "Simple examples using the default OpenSSL configuration file"
This CMP client implementation comes with demonstrative CMP sections
in the example configuration file \fIopenssl/apps/openssl.cnf\fR,
which can be used to interact conveniently with the Insta Demo CA.
.PP
In order to enroll an initial certificate from that CA it is sufficient
to issue the following shell commands.
.PP
.Vb 1
\&  export OPENSSL_CONF=/path/to/openssl/apps/openssl.cnf
.Ve
.PP
.Vb 2
\&  openssl genrsa \-out insta.priv.pem
\&  openssl cmp \-section insta
.Ve
.PP
This should produce the file \fIinsta.cert.pem\fR containing a new certificate
for the private key held in \fIinsta.priv.pem\fR.
It can be viewed using, e.g.,
.PP
.Vb 1
\&  openssl x509 \-noout \-text \-in insta.cert.pem
.Ve
.PP
In case the network setup requires using an HTTP proxy it may be given as usual
via the environment variable \fBhttp_proxy\fR or via the \fB\-proxy\fR option in the
configuration file or the CMP command-line argument \fB\-proxy\fR, for example
.PP
.Vb 1
\&  \-proxy http://192.168.1.1:8080
.Ve
.PP
In the Insta Demo CA scenario both clients and the server may use the pre-shared
secret \fIinsta\fR and the reference value \fI3078\fR to authenticate to each other.
.PP
Alternatively, CMP messages may be protected in signature-based manner,
where the trust anchor in this case is \fIinsta.ca.crt\fR
and the client may use any certificate already obtained from that CA,
as specified in the \fB[signature]\fR section of the example configuration.
This can be used in combination with the \fB[insta]\fR section simply by
.PP
.Vb 1
\&  openssl cmp \-section insta,signature
.Ve
.PP
By default the CMP IR message type is used, yet CR works equally here.
This may be specified directly at the command line:
.PP
.Vb 1
\&  openssl cmp \-section insta \-cmd cr
.Ve
.PP
or by referencing in addition the \fB[cr]\fR section of the example configuration:
.PP
.Vb 1
\&  openssl cmp \-section insta,cr
.Ve
.PP
In order to update the enrolled certificate one may call
.PP
.Vb 1
\&  openssl cmp \-section insta,kur
.Ve
.PP
using MAC-based protection with PBM or
.PP
.Vb 1
\&  openssl cmp \-section insta,kur,signature
.Ve
.PP
using signature-based protection.
.PP
In a similar way any previously enrolled certificate may be revoked by
.PP
.Vb 1
\&  openssl cmp \-section insta,rr \-trusted insta.ca.crt
.Ve
.PP
or
.PP
.Vb 1
\&  openssl cmp \-section insta,rr,signature
.Ve
.PP
Many more options can be given in the configuration file
and/or on the command line.
For instance, the \fB\-reqexts\fR CLI option may refer to a section in the
configuration file defining X.509 extensions to use in certificate requests,
such as \f(CW\*(C`v3_req\*(C'\fR in \fIopenssl/apps/openssl.cnf\fR:
.PP
.Vb 1
\&  openssl cmp \-section insta,cr \-reqexts v3_req
.Ve
.SS "Certificate enrollment"
.IX Subsection "Certificate enrollment"
The following examples do not make use of a configuration file at first.
They assume that a CMP server can be contacted on the local TCP port 80
and accepts requests under the alias \fI/pkix/\fR.
.PP
For enrolling its very first certificate the client generates a client key
and sends an initial request message to the local CMP server
using a pre-shared secret key for mutual authentication.
In this example the client does not have the CA certificate yet,
so we specify the name of the CA with the \fB\-recipient\fR option
and save any CA certificates that we may receive in the \f(CW\*(C`capubs.pem\*(C'\fR file.
.PP
In below command line usage examples the \f(CW\*(C`\e\*(C'\fR at line ends is used just
for formatting; each of the command invocations should be on a single line.
.PP
.Vb 5
\&  openssl genrsa \-out cl_key.pem
\&  openssl cmp \-cmd ir \-server 127.0.0.1:80/pkix/ \-recipient "/CN=CMPserver" \e
\&    \-ref 1234 \-secret pass:1234\-5678 \e
\&    \-newkey cl_key.pem \-subject "/CN=MyName" \e
\&    \-cacertsout capubs.pem \-certout cl_cert.pem
.Ve
.SS "Certificate update"
.IX Subsection "Certificate update"
Then, when the client certificate and its related key pair needs to be updated,
the client can send a key update request taking the certs in \f(CW\*(C`capubs.pem\*(C'\fR
as trusted for authenticating the server and using the previous cert and key
for its own authentication.
Then it can start using the new cert and key.
.PP
.Vb 6
\&  openssl genrsa \-out cl_key_new.pem
\&  openssl cmp \-cmd kur \-server 127.0.0.1:80/pkix/ \e
\&    \-trusted capubs.pem \e
\&    \-cert cl_cert.pem \-key cl_key.pem \e
\&    \-newkey cl_key_new.pem \-certout cl_cert.pem
\&  cp cl_key_new.pem cl_key.pem
.Ve
.PP
This command sequence can be repeated as often as needed.
.SS "Requesting information from CMP server"
.IX Subsection "Requesting information from CMP server"
Requesting "all relevant information" with an empty General Message.
This prints information about all received ITAV \fBinfoType\fRs to stdout.
.PP
.Vb 2
\&  openssl cmp \-cmd genm \-server 127.0.0.1/pkix/ \-recipient "/CN=CMPserver" \e
\&    \-ref 1234 \-secret pass:1234\-5678
.Ve
.SS "Using a custom configuration file"
.IX Subsection "Using a custom configuration file"
For CMP client invocations, in particular for certificate enrollment,
usually many parameters need to be set, which is tedious and error-prone to do
on the command line.
Therefore, the client offers the possibility to read
options from sections of the OpenSSL config file, usually called \fIopenssl.cnf\fR.
The values found there can still be extended and even overridden by any
subsequently loaded sections and on the command line.
.PP
After including in the configuration file the following sections:
.PP
.Vb 8
\&  [cmp]
\&  server = 127.0.0.1
\&  path = pkix/
\&  trusted = capubs.pem
\&  cert = cl_cert.pem
\&  key = cl_key.pem
\&  newkey = cl_key.pem
\&  certout = cl_cert.pem
\&
\&  [init]
\&  recipient = "/CN=CMPserver"
\&  trusted =
\&  cert =
\&  key =
\&  ref = 1234
\&  secret = pass:1234\-5678\-1234\-567
\&  subject = "/CN=MyName"
\&  cacertsout = capubs.pem
.Ve
.PP
the above enrollment transactions reduce to
.PP
.Vb 2
\&  openssl cmp \-section cmp,init
\&  openssl cmp \-cmd kur \-newkey cl_key_new.pem
.Ve
.PP
and the above transaction using a general message reduces to
.PP
.Vb 1
\&  openssl cmp \-section cmp,init \-cmd genm
.Ve
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBopenssl\-genrsa\fR\|(1), \fBopenssl\-ecparam\fR\|(1), \fBopenssl\-list\fR\|(1),
\&\fBopenssl\-req\fR\|(1), \fBopenssl\-x509\fR\|(1), \fBx509v3_config\fR\|(5)
.SH HISTORY
.IX Header "HISTORY"
The \fBcmp\fR application was added in OpenSSL 3.0.
.PP
The \fB\-engine option\fR was deprecated in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2007\-2023 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
