.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "OPENSSL-PASSPHRASE-OPTIONS 1ossl"
.TH OPENSSL-PASSPHRASE-OPTIONS 1ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
openssl\-passphrase\-options \- Pass phrase options
.SH SYNOPSIS
.IX Header "SYNOPSIS"
\&\fBopenssl\fR
\&\fIcommand\fR
[ \fIoptions\fR ... ]
[ \fIparameters\fR ... ]
.SH DESCRIPTION
.IX Header "DESCRIPTION"
Several OpenSSL commands accept password arguments, typically using \fB\-passin\fR
and \fB\-passout\fR for input and output passwords respectively. These allow
the password to be obtained from a variety of sources. Both of these
options take a single argument whose format is described below. If no
password argument is given and a password is required then the user is
prompted to enter one: this will typically be read from the current
terminal with echoing turned off.
.PP
Note that character encoding may be relevant, please see
\&\fBpassphrase\-encoding\fR\|(7).
.SH OPTIONS
.IX Header "OPTIONS"
.SS "Pass Phrase Option Arguments"
.IX Subsection "Pass Phrase Option Arguments"
Pass phrase arguments can be formatted as follows.
.IP \fBpass:\fR\fIpassword\fR 4
.IX Item "pass:password"
The actual password is \fIpassword\fR. Since the password is visible
to utilities (like 'ps' under Unix) this form should only be used
where security is not important.
.IP \fBenv:\fR\fIvar\fR 4
.IX Item "env:var"
Obtain the password from the environment variable \fIvar\fR. Since
the environment of other processes is visible on certain platforms
(e.g. ps under certain Unix OSes) this option should be used with caution.
.IP \fBfile:\fR\fIpathname\fR 4
.IX Item "file:pathname"
The first line of \fIpathname\fR is the password. If the same \fIpathname\fR
argument is supplied to \fB\-passin\fR and \fB\-passout\fR arguments then the first
line will be used for the input password and the next line for the output
password. \fIpathname\fR need not refer to a regular file: it could for example
refer to a device or named pipe.
.IP \fBfd:\fR\fInumber\fR 4
.IX Item "fd:number"
Read the password from the file descriptor \fInumber\fR. This can be used to
send the data via a pipe for example.
.IP \fBstdin\fR 4
.IX Item "stdin"
Read the password from standard input.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2020 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
