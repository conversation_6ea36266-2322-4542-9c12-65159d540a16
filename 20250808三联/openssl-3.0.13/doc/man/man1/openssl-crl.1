.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "OPENSSL-CRL 1ossl"
.TH OPENSSL-CRL 1ossl 2025-08-08 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
openssl\-crl \- CRL command
.SH SYNOPSIS
.IX Header "SYNOPSIS"
\&\fBopenssl\fR \fBcrl\fR
[\fB\-help\fR]
[\fB\-inform\fR \fBDER\fR|\fBPEM\fR]
[\fB\-outform\fR \fBDER\fR|\fBPEM\fR]
[\fB\-key\fR \fIfilename\fR]
[\fB\-keyform\fR \fBDER\fR|\fBPEM\fR|\fBP12\fR]
[\fB\-dateopt\fR]
[\fB\-text\fR]
[\fB\-in\fR \fIfilename\fR]
[\fB\-out\fR \fIfilename\fR]
[\fB\-gendelta\fR \fIfilename\fR]
[\fB\-badsig\fR]
[\fB\-verify\fR]
[\fB\-noout\fR]
[\fB\-hash\fR]
[\fB\-hash_old\fR]
[\fB\-fingerprint\fR]
[\fB\-crlnumber\fR]
[\fB\-issuer\fR]
[\fB\-lastupdate\fR]
[\fB\-nextupdate\fR]
[\fB\-nameopt\fR \fIoption\fR]
[\fB\-CAfile\fR \fIfile\fR]
[\fB\-no\-CAfile\fR]
[\fB\-CApath\fR \fIdir\fR]
[\fB\-no\-CApath\fR]
[\fB\-CAstore\fR \fIuri\fR]
[\fB\-no\-CAstore\fR]
[\fB\-provider\fR \fIname\fR]
[\fB\-provider\-path\fR \fIpath\fR]
[\fB\-propquery\fR \fIpropq\fR]
.SH DESCRIPTION
.IX Header "DESCRIPTION"
This command processes CRL files in DER or PEM format.
.SH OPTIONS
.IX Header "OPTIONS"
.IP \fB\-help\fR 4
.IX Item "-help"
Print out a usage message.
.IP "\fB\-inform\fR \fBDER\fR|\fBPEM\fR" 4
.IX Item "-inform DER|PEM"
The CRL input format; unspecified by default.
See \fBopenssl\-format\-options\fR\|(1) for details.
.IP "\fB\-outform\fR \fBDER\fR|\fBPEM\fR" 4
.IX Item "-outform DER|PEM"
The CRL output format; the default is \fBPEM\fR.
See \fBopenssl\-format\-options\fR\|(1) for details.
.IP "\fB\-key\fR \fIfilename\fR" 4
.IX Item "-key filename"
The private key to be used to sign the CRL.
.IP "\fB\-keyform\fR \fBDER\fR|\fBPEM\fR|\fBP12\fR" 4
.IX Item "-keyform DER|PEM|P12"
The format of the private key file; unspecified by default.
See \fBopenssl\-format\-options\fR\|(1) for details.
.IP "\fB\-in\fR \fIfilename\fR" 4
.IX Item "-in filename"
This specifies the input filename to read from or standard input if this
option is not specified.
.IP "\fB\-out\fR \fIfilename\fR" 4
.IX Item "-out filename"
Specifies the output filename to write to or standard output by
default.
.IP "\fB\-gendelta\fR \fIfilename\fR" 4
.IX Item "-gendelta filename"
Output a comparison of the main CRL and the one specified here.
.IP \fB\-badsig\fR 4
.IX Item "-badsig"
Corrupt the signature before writing it; this can be useful
for testing.
.IP \fB\-dateopt\fR 4
.IX Item "-dateopt"
Specify the date output format. Values are: rfc_822 and iso_8601.
Defaults to rfc_822.
.IP \fB\-text\fR 4
.IX Item "-text"
Print out the CRL in text form.
.IP \fB\-verify\fR 4
.IX Item "-verify"
Verify the signature in the CRL.
.IP \fB\-noout\fR 4
.IX Item "-noout"
Don't output the encoded version of the CRL.
.IP \fB\-fingerprint\fR 4
.IX Item "-fingerprint"
Output the fingerprint of the CRL.
.IP \fB\-crlnumber\fR 4
.IX Item "-crlnumber"
Output the number of the CRL.
.IP \fB\-hash\fR 4
.IX Item "-hash"
Output a hash of the issuer name. This can be use to lookup CRLs in
a directory by issuer name.
.IP \fB\-hash_old\fR 4
.IX Item "-hash_old"
Outputs the "hash" of the CRL issuer name using the older algorithm
as used by OpenSSL before version 1.0.0.
.IP \fB\-issuer\fR 4
.IX Item "-issuer"
Output the issuer name.
.IP \fB\-lastupdate\fR 4
.IX Item "-lastupdate"
Output the lastUpdate field.
.IP \fB\-nextupdate\fR 4
.IX Item "-nextupdate"
Output the nextUpdate field.
.IP "\fB\-nameopt\fR \fIoption\fR" 4
.IX Item "-nameopt option"
This specifies how the subject or issuer names are displayed.
See \fBopenssl\-namedisplay\-options\fR\|(1) for details.
.IP "\fB\-CAfile\fR \fIfile\fR, \fB\-no\-CAfile\fR, \fB\-CApath\fR \fIdir\fR, \fB\-no\-CApath\fR, \fB\-CAstore\fR \fIuri\fR, \fB\-no\-CAstore\fR" 4
.IX Item "-CAfile file, -no-CAfile, -CApath dir, -no-CApath, -CAstore uri, -no-CAstore"
See "Trusted Certificate Options" in \fBopenssl\-verification\-options\fR\|(1) for details.
.IP "\fB\-provider\fR \fIname\fR" 4
.IX Item "-provider name"
.PD 0
.IP "\fB\-provider\-path\fR \fIpath\fR" 4
.IX Item "-provider-path path"
.IP "\fB\-propquery\fR \fIpropq\fR" 4
.IX Item "-propquery propq"
.PD
See "Provider Options" in \fBopenssl\fR\|(1), \fBprovider\fR\|(7), and \fBproperty\fR\|(7).
.SH EXAMPLES
.IX Header "EXAMPLES"
Convert a CRL file from PEM to DER:
.PP
.Vb 1
\& openssl crl \-in crl.pem \-outform DER \-out crl.der
.Ve
.PP
Output the text form of a DER encoded certificate:
.PP
.Vb 1
\& openssl crl \-in crl.der \-text \-noout
.Ve
.SH BUGS
.IX Header "BUGS"
Ideally it should be possible to create a CRL using appropriate options
and files too.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBopenssl\fR\|(1),
\&\fBopenssl\-crl2pkcs7\fR\|(1),
\&\fBopenssl\-ca\fR\|(1),
\&\fBopenssl\-x509\fR\|(1),
\&\fBossl_store\-file\fR\|(7)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
