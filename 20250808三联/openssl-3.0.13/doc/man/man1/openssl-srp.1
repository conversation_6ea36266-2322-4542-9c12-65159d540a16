.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "OPENSSL-SRP 1ossl"
.TH OPENSSL-SRP 1ossl 2025-08-08 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
openssl\-srp \- maintain SRP password file
.SH SYNOPSIS
.IX Header "SYNOPSIS"
\&\fBopenssl srp\fR
[\fB\-help\fR]
[\fB\-verbose\fR]
[\fB\-add\fR]
[\fB\-modify\fR]
[\fB\-delete\fR]
[\fB\-list\fR]
[\fB\-name\fR \fIsection\fR]
[\fB\-srpvfile\fR \fIfile\fR]
[\fB\-gn\fR \fIidentifier\fR]
[\fB\-userinfo\fR \fItext\fR]
[\fB\-passin\fR \fIarg\fR]
[\fB\-passout\fR \fIarg\fR]
[\fB\-engine\fR \fIid\fR]
[\fB\-rand\fR \fIfiles\fR]
[\fB\-writerand\fR \fIfile\fR]
[\fB\-provider\fR \fIname\fR]
[\fB\-provider\-path\fR \fIpath\fR]
[\fB\-propquery\fR \fIpropq\fR]
[\fB\-config\fR \fIconfigfile\fR]
[\fIuser\fR ...]
.SH DESCRIPTION
.IX Header "DESCRIPTION"
This command is deprecated. It is used to maintain an SRP (secure remote
password) file. At most one of the \fB\-add\fR, \fB\-modify\fR, \fB\-delete\fR, and \fB\-list\fR
options can be specified.
These options take zero or more usernames as parameters and perform the
appropriate operation on the SRP file.
For \fB\-list\fR, if no \fIuser\fR is given then all users are displayed.
.PP
The configuration file to use, and the section within the file, can be
specified with the \fB\-config\fR and \fB\-name\fR flags, respectively.
.SH OPTIONS
.IX Header "OPTIONS"
.IP \fB\-help\fR 4
.IX Item "-help"
Display an option summary.
.IP \fB\-verbose\fR 4
.IX Item "-verbose"
Generate verbose output while processing.
.IP \fB\-add\fR 4
.IX Item "-add"
Add a user and SRP verifier.
.IP \fB\-modify\fR 4
.IX Item "-modify"
Modify the SRP verifier of an existing user.
.IP \fB\-delete\fR 4
.IX Item "-delete"
Delete user from verifier file.
.IP \fB\-list\fR 4
.IX Item "-list"
List users.
.IP \fB\-name\fR 4
.IX Item "-name"
The particular SRP definition to use.
.IP "\fB\-srpvfile\fR \fIfile\fR" 4
.IX Item "-srpvfile file"
If the config file is not specified,
\&\fB\-srpvfile\fR can be used to specify the file to operate on.
.IP \fB\-gn\fR 4
.IX Item "-gn"
Specifies the \fBg\fR and \fBN\fR values, using one of
the strengths defined in IETF RFC 5054.
.IP \fB\-userinfo\fR 4
.IX Item "-userinfo"
specifies additional information to add when
adding or modifying a user.
.IP "\fB\-passin\fR \fIarg\fR, \fB\-passout\fR \fIarg\fR" 4
.IX Item "-passin arg, -passout arg"
The password source for the input and output file.
For more information about the format of \fBarg\fR
see \fBopenssl\-passphrase\-options\fR\|(1).
.IP "\fB\-engine\fR \fIid\fR" 4
.IX Item "-engine id"
See "Engine Options" in \fBopenssl\fR\|(1).
This option is deprecated.
.IP "\fB\-rand\fR \fIfiles\fR, \fB\-writerand\fR \fIfile\fR" 4
.IX Item "-rand files, -writerand file"
See "Random State Options" in \fBopenssl\fR\|(1) for details.
.IP "\fB\-provider\fR \fIname\fR" 4
.IX Item "-provider name"
.PD 0
.IP "\fB\-provider\-path\fR \fIpath\fR" 4
.IX Item "-provider-path path"
.IP "\fB\-propquery\fR \fIpropq\fR" 4
.IX Item "-propquery propq"
.PD
See "Provider Options" in \fBopenssl\fR\|(1), \fBprovider\fR\|(7), and \fBproperty\fR\|(7).
.IP "\fB\-config\fR \fIconfigfile\fR" 4
.IX Item "-config configfile"
See "Configuration Option" in \fBopenssl\fR\|(1).
.Sp
[\fB\-rand\fR \fIfiles\fR]
[\fB\-writerand\fR \fIfile\fR]
.SH HISTORY
.IX Header "HISTORY"
The \fB\-engine\fR option was deprecated in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2017\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
