.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "OPENSSL-GENPKEY 1ossl"
.TH OPENSSL-GENPKEY 1ossl 2025-08-08 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
openssl\-genpkey \- generate a private key
.SH SYNOPSIS
.IX Header "SYNOPSIS"
\&\fBopenssl\fR \fBgenpkey\fR
[\fB\-help\fR]
[\fB\-out\fR \fIfilename\fR]
[\fB\-outform\fR \fBDER\fR|\fBPEM\fR]
[\fB\-quiet\fR]
[\fB\-pass\fR \fIarg\fR]
[\fB\-\fR\f(BIcipher\fR]
[\fB\-paramfile\fR \fIfile\fR]
[\fB\-algorithm\fR \fIalg\fR]
[\fB\-pkeyopt\fR \fIopt\fR:\fIvalue\fR]
[\fB\-genparam\fR]
[\fB\-text\fR]
[\fB\-engine\fR \fIid\fR]
[\fB\-provider\fR \fIname\fR]
[\fB\-provider\-path\fR \fIpath\fR]
[\fB\-propquery\fR \fIpropq\fR]
[\fB\-config\fR \fIconfigfile\fR]
.SH DESCRIPTION
.IX Header "DESCRIPTION"
This command generates a private key.
.SH OPTIONS
.IX Header "OPTIONS"
.IP \fB\-help\fR 4
.IX Item "-help"
Print out a usage message.
.IP "\fB\-out\fR \fIfilename\fR" 4
.IX Item "-out filename"
Output the key to the specified file. If this argument is not specified then
standard output is used.
.IP "\fB\-outform\fR \fBDER\fR|\fBPEM\fR" 4
.IX Item "-outform DER|PEM"
The output format, except when \fB\-genparam\fR is given; the default is \fBPEM\fR.
See \fBopenssl\-format\-options\fR\|(1) for details.
.Sp
When \fB\-genparam\fR is given, \fB\-outform\fR is ignored.
.IP \fB\-quiet\fR 4
.IX Item "-quiet"
Do not output "status dots" while generating keys.
.IP "\fB\-pass\fR \fIarg\fR" 4
.IX Item "-pass arg"
The output file password source. For more information about the format of \fIarg\fR
see \fBopenssl\-passphrase\-options\fR\|(1).
.IP \fB\-\fR\f(BIcipher\fR 4
.IX Item "-cipher"
This option encrypts the private key with the supplied cipher. Any algorithm
name accepted by \fBEVP_get_cipherbyname()\fR is acceptable such as \fBdes3\fR.
.IP "\fB\-algorithm\fR \fIalg\fR" 4
.IX Item "-algorithm alg"
Public key algorithm to use such as RSA, DSA, DH or DHX. If used this option must
precede any \fB\-pkeyopt\fR options. The options \fB\-paramfile\fR and \fB\-algorithm\fR
are mutually exclusive. Engines may add algorithms in addition to the standard
built-in ones.
.Sp
Valid built-in algorithm names for private key generation are RSA, RSA-PSS, EC,
X25519, X448, ED25519 and ED448.
.Sp
Valid built-in algorithm names for parameter generation (see the \fB\-genparam\fR
option) are DH, DSA and EC.
.Sp
Note that the algorithm name X9.42 DH may be used as a synonym for DHX keys and
PKCS#3 refers to DH Keys. Some options are not shared between DH and DHX keys.
.IP "\fB\-pkeyopt\fR \fIopt\fR:\fIvalue\fR" 4
.IX Item "-pkeyopt opt:value"
Set the public key algorithm option \fIopt\fR to \fIvalue\fR. The precise set of
options supported depends on the public key algorithm used and its
implementation. See "KEY GENERATION OPTIONS" and
"PARAMETER GENERATION OPTIONS" below for more details.
.IP \fB\-genparam\fR 4
.IX Item "-genparam"
Generate a set of parameters instead of a private key. If used this option must
precede any \fB\-algorithm\fR, \fB\-paramfile\fR or \fB\-pkeyopt\fR options.
.IP "\fB\-paramfile\fR \fIfilename\fR" 4
.IX Item "-paramfile filename"
Some public key algorithms generate a private key based on a set of parameters.
They can be supplied using this option. If this option is used the public key
algorithm used is determined by the parameters. If used this option must
precede any \fB\-pkeyopt\fR options. The options \fB\-paramfile\fR and \fB\-algorithm\fR
are mutually exclusive.
.IP \fB\-text\fR 4
.IX Item "-text"
Print an (unencrypted) text representation of private and public keys and
parameters along with the PEM or DER structure.
.IP "\fB\-engine\fR \fIid\fR" 4
.IX Item "-engine id"
See "Engine Options" in \fBopenssl\fR\|(1).
This option is deprecated.
.IP "\fB\-provider\fR \fIname\fR" 4
.IX Item "-provider name"
.PD 0
.IP "\fB\-provider\-path\fR \fIpath\fR" 4
.IX Item "-provider-path path"
.IP "\fB\-propquery\fR \fIpropq\fR" 4
.IX Item "-propquery propq"
.PD
See "Provider Options" in \fBopenssl\fR\|(1), \fBprovider\fR\|(7), and \fBproperty\fR\|(7).
.IP "\fB\-config\fR \fIconfigfile\fR" 4
.IX Item "-config configfile"
See "Configuration Option" in \fBopenssl\fR\|(1).
.SH "KEY GENERATION OPTIONS"
.IX Header "KEY GENERATION OPTIONS"
The options supported by each algorithm and indeed each implementation of an
algorithm can vary. The options for the OpenSSL implementations are detailed
below. There are no key generation options defined for the X25519, X448, ED25519
or ED448 algorithms.
.SS "RSA Key Generation Options"
.IX Subsection "RSA Key Generation Options"
.IP \fBrsa_keygen_bits:numbits\fR 4
.IX Item "rsa_keygen_bits:numbits"
The number of bits in the generated key. If not specified 2048 is used.
.IP \fBrsa_keygen_primes:numprimes\fR 4
.IX Item "rsa_keygen_primes:numprimes"
The number of primes in the generated key. If not specified 2 is used.
.IP \fBrsa_keygen_pubexp:value\fR 4
.IX Item "rsa_keygen_pubexp:value"
The RSA public exponent value. This can be a large decimal or
hexadecimal value if preceded by \f(CW\*(C`0x\*(C'\fR. Default value is 65537.
.SS "RSA-PSS Key Generation Options"
.IX Subsection "RSA-PSS Key Generation Options"
Note: by default an \fBRSA-PSS\fR key has no parameter restrictions.
.IP "\fBrsa_keygen_bits\fR:\fInumbits\fR, \fBrsa_keygen_primes\fR:\fInumprimes\fR, \fBrsa_keygen_pubexp\fR:\fIvalue\fR" 4
.IX Item "rsa_keygen_bits:numbits, rsa_keygen_primes:numprimes, rsa_keygen_pubexp:value"
These options have the same meaning as the \fBRSA\fR algorithm.
.IP \fBrsa_pss_keygen_md\fR:\fIdigest\fR 4
.IX Item "rsa_pss_keygen_md:digest"
If set the key is restricted and can only use \fIdigest\fR for signing.
.IP \fBrsa_pss_keygen_mgf1_md\fR:\fIdigest\fR 4
.IX Item "rsa_pss_keygen_mgf1_md:digest"
If set the key is restricted and can only use \fIdigest\fR as it's MGF1
parameter.
.IP \fBrsa_pss_keygen_saltlen\fR:\fIlen\fR 4
.IX Item "rsa_pss_keygen_saltlen:len"
If set the key is restricted and \fIlen\fR specifies the minimum salt length.
.SS "EC Key Generation Options"
.IX Subsection "EC Key Generation Options"
The EC key generation options can also be used for parameter generation.
.IP \fBec_paramgen_curve\fR:\fIcurve\fR 4
.IX Item "ec_paramgen_curve:curve"
The EC curve to use. OpenSSL supports NIST curve names such as "P\-256".
.IP \fBec_param_enc\fR:\fIencoding\fR 4
.IX Item "ec_param_enc:encoding"
The encoding to use for parameters. The \fIencoding\fR parameter must be either
\&\fBnamed_curve\fR or \fBexplicit\fR. The default value is \fBnamed_curve\fR.
.SS "DH Key Generation Options"
.IX Subsection "DH Key Generation Options"
.IP \fBgroup\fR:\fIname\fR 4
.IX Item "group:name"
The \fBparamfile\fR option is not required if a named group is used here.
See the "DH Parameter Generation Options" section below.
.SH "PARAMETER GENERATION OPTIONS"
.IX Header "PARAMETER GENERATION OPTIONS"
The options supported by each algorithm and indeed each implementation of an
algorithm can vary. The options for the OpenSSL implementations are detailed
below.
.SS "DSA Parameter Generation Options"
.IX Subsection "DSA Parameter Generation Options"
.IP \fBdsa_paramgen_bits\fR:\fInumbits\fR 4
.IX Item "dsa_paramgen_bits:numbits"
The number of bits in the generated prime. If not specified 2048 is used.
.IP \fBdsa_paramgen_q_bits\fR:\fInumbits\fR 4
.IX Item "dsa_paramgen_q_bits:numbits"
.PD 0
.IP \fBqbits\fR:\fInumbits\fR 4
.IX Item "qbits:numbits"
.PD
The number of bits in the q parameter. Must be one of 160, 224 or 256. If not
specified 224 is used.
.IP \fBdsa_paramgen_md\fR:\fIdigest\fR 4
.IX Item "dsa_paramgen_md:digest"
.PD 0
.IP \fBdigest\fR:\fIdigest\fR 4
.IX Item "digest:digest"
.PD
The digest to use during parameter generation. Must be one of \fBsha1\fR, \fBsha224\fR
or \fBsha256\fR. If set, then the number of bits in \fBq\fR will match the output size
of the specified digest and the \fBdsa_paramgen_q_bits\fR parameter will be
ignored. If not set, then a digest will be used that gives an output matching
the number of bits in \fBq\fR, i.e. \fBsha1\fR if q length is 160, \fBsha224\fR if it 224
or \fBsha256\fR if it is 256.
.IP \fBproperties\fR:\fIquery\fR 4
.IX Item "properties:query"
The \fIdigest\fR property \fIquery\fR string to use when fetching a digest from a provider.
.IP \fBtype\fR:\fItype\fR 4
.IX Item "type:type"
The type of generation to use. Set this to 1 to use legacy FIPS186\-2 parameter
generation. The default of 0 uses FIPS186\-4 parameter generation.
.IP \fBgindex\fR:\fIindex\fR 4
.IX Item "gindex:index"
The index to use for canonical generation and verification of the generator g.
Set this to a positive value ranging from 0..255 to use this mode. Larger values
will only use the bottom byte.
This \fIindex\fR must then be reused during key validation to verify the value of g.
If this value is not set then g is not verifiable. The default value is \-1.
.IP \fBhexseed\fR:\fIseed\fR 4
.IX Item "hexseed:seed"
The seed \fIseed\fR data to use instead of generating a random seed internally.
This should be used for testing purposes only. This will either produced fixed
values for the generated parameters OR it will fail if the seed did not
generate valid primes.
.SS "DH Parameter Generation Options"
.IX Subsection "DH Parameter Generation Options"
For most use cases it is recommended to use the \fBgroup\fR option rather than
the \fBtype\fR options. Note that the \fBgroup\fR option is not used by default if
no parameter generation options are specified.
.IP \fBgroup\fR:\fIname\fR 4
.IX Item "group:name"
.PD 0
.IP \fBdh_param\fR:\fIname\fR 4
.IX Item "dh_param:name"
.PD
Use a named DH group to select constant values for the DH parameters.
All other options will be ignored if this value is set.
.Sp
Valid values that are associated with the \fBalgorithm\fR of \fB"DH"\fR are:
"ffdhe2048", "ffdhe3072", "ffdhe4096", "ffdhe6144", "ffdhe8192",
"modp_1536", "modp_2048", "modp_3072", "modp_4096", "modp_6144", "modp_8192".
.Sp
Valid values that are associated with the \fBalgorithm\fR of \fB"DHX"\fR are the
RFC5114 names "dh_1024_160", "dh_2048_224", "dh_2048_256".
.IP \fBdh_rfc5114\fR:\fInum\fR 4
.IX Item "dh_rfc5114:num"
If this option is set, then the appropriate RFC5114 parameters are used
instead of generating new parameters. The value \fInum\fR can be one of
1, 2 or 3 that are equivalent to using the option \fBgroup\fR with one of
"dh_1024_160", "dh_2048_224" or "dh_2048_256".
All other options will be ignored if this value is set.
.IP \fBpbits\fR:\fInumbits\fR 4
.IX Item "pbits:numbits"
.PD 0
.IP \fBdh_paramgen_prime_len\fR:\fInumbits\fR 4
.IX Item "dh_paramgen_prime_len:numbits"
.PD
The number of bits in the prime parameter \fIp\fR. The default is 2048.
.IP \fBqbits\fR:\fInumbits\fR 4
.IX Item "qbits:numbits"
.PD 0
.IP \fBdh_paramgen_subprime_len\fR:\fInumbits\fR 4
.IX Item "dh_paramgen_subprime_len:numbits"
.PD
The number of bits in the sub prime parameter \fIq\fR. The default is 224.
Only relevant if used in conjunction with the \fBdh_paramgen_type\fR option to
generate DHX parameters.
.IP \fBsafeprime-generator\fR:\fIvalue\fR 4
.IX Item "safeprime-generator:value"
.PD 0
.IP \fBdh_paramgen_generator\fR:\fIvalue\fR 4
.IX Item "dh_paramgen_generator:value"
.PD
The value to use for the generator \fIg\fR. The default is 2.
The \fBalgorithm\fR option must be \fB"DH"\fR for this parameter to be used.
.IP \fBtype\fR:\fIstring\fR 4
.IX Item "type:string"
The type name of DH parameters to generate. Valid values are:
.RS 4
.IP """generator""" 4
.IX Item """generator"""
Use a safe prime generator with the option \fBsafeprime_generator\fR
The \fBalgorithm\fR option must be \fB"DH"\fR.
.IP """fips186_4""" 4
.IX Item """fips186_4"""
FIPS186\-4 parameter generation.
The \fBalgorithm\fR option must be \fB"DHX"\fR.
.IP """fips186_2""" 4
.IX Item """fips186_2"""
FIPS186\-4 parameter generation.
The \fBalgorithm\fR option must be \fB"DHX"\fR.
.IP """group""" 4
.IX Item """group"""
Can be used with the option \fBpbits\fR to select one of
"ffdhe2048", "ffdhe3072", "ffdhe4096", "ffdhe6144" or "ffdhe8192".
The \fBalgorithm\fR option must be \fB"DH"\fR.
.IP """default""" 4
.IX Item """default"""
Selects a default type based on the \fBalgorithm\fR. This is used by the
OpenSSL default provider to set the type for backwards compatibility.
If \fBalgorithm\fR is \fB"DH"\fR then \fB"generator"\fR is used.
If \fBalgorithm\fR is \fB"DHX"\fR then \fB"fips186_2"\fR is used.
.RE
.RS 4
.RE
.IP \fBdh_paramgen_type\fR:\fIvalue\fR 4
.IX Item "dh_paramgen_type:value"
The type of DH parameters to generate. Valid values are 0, 1, 2 or 3
which correspond to setting the option \fBtype\fR to
"generator", "fips186_2", "fips186_4" or "group".
.IP \fBdigest\fR:\fIdigest\fR 4
.IX Item "digest:digest"
The digest to use during parameter generation. Must be one of \fBsha1\fR, \fBsha224\fR
or \fBsha256\fR. If set, then the number of bits in \fBqbits\fR will match the output
size of the specified digest and the \fBqbits\fR parameter will be
ignored. If not set, then a digest will be used that gives an output matching
the number of bits in \fBq\fR, i.e. \fBsha1\fR if q length is 160, \fBsha224\fR if it is
224 or \fBsha256\fR if it is 256.
This is only used by "fips186_4" and "fips186_2" key generation.
.IP \fBproperties\fR:\fIquery\fR 4
.IX Item "properties:query"
The \fIdigest\fR property \fIquery\fR string to use when fetching a digest from a provider.
This is only used by "fips186_4" and "fips186_2" key generation.
.IP \fBgindex\fR:\fIindex\fR 4
.IX Item "gindex:index"
The index to use for canonical generation and verification of the generator g.
Set this to a positive value ranging from 0..255 to use this mode. Larger values
will only use the bottom byte.
This \fIindex\fR must then be reused during key validation to verify the value of g.
If this value is not set then g is not verifiable. The default value is \-1.
This is only used by "fips186_4" and "fips186_2" key generation.
.IP \fBhexseed\fR:\fIseed\fR 4
.IX Item "hexseed:seed"
The seed \fIseed\fR data to use instead of generating a random seed internally.
This should be used for testing purposes only. This will either produced fixed
values for the generated parameters OR it will fail if the seed did not
generate valid primes.
This is only used by "fips186_4" and "fips186_2" key generation.
.SS "EC Parameter Generation Options"
.IX Subsection "EC Parameter Generation Options"
The EC parameter generation options are the same as for key generation. See
"EC Key Generation Options" above.
.SH NOTES
.IX Header "NOTES"
The use of the genpkey program is encouraged over the algorithm specific
utilities because additional algorithm options and ENGINE provided algorithms
can be used.
.SH EXAMPLES
.IX Header "EXAMPLES"
Generate an RSA private key using default parameters:
.PP
.Vb 1
\& openssl genpkey \-algorithm RSA \-out key.pem
.Ve
.PP
Encrypt output private key using 128 bit AES and the passphrase "hello":
.PP
.Vb 1
\& openssl genpkey \-algorithm RSA \-out key.pem \-aes\-128\-cbc \-pass pass:hello
.Ve
.PP
Generate a 2048 bit RSA key using 3 as the public exponent:
.PP
.Vb 2
\& openssl genpkey \-algorithm RSA \-out key.pem \e
\&     \-pkeyopt rsa_keygen_bits:2048 \-pkeyopt rsa_keygen_pubexp:3
.Ve
.PP
Generate 2048 bit DSA parameters that can be validated: The output values for
gindex and seed are required for key validation purposes and are not saved to
the output pem file).
.PP
.Vb 2
\& openssl genpkey \-genparam \-algorithm DSA \-out dsap.pem \-pkeyopt pbits:2048 \e
\&     \-pkeyopt qbits:224 \-pkeyopt digest:SHA256 \-pkeyopt gindex:1 \-text
.Ve
.PP
Generate DSA key from parameters:
.PP
.Vb 1
\& openssl genpkey \-paramfile dsap.pem \-out dsakey.pem
.Ve
.PP
Generate 4096 bit DH Key using safe prime group ffdhe4096:
.PP
.Vb 1
\& openssl genpkey \-algorithm DH \-out dhkey.pem \-pkeyopt group:ffdhe4096
.Ve
.PP
Generate 2048 bit X9.42 DH key with 256 bit subgroup using RFC5114 group3:
.PP
.Vb 1
\& openssl genpkey \-algorithm DHX \-out dhkey.pem \-pkeyopt dh_rfc5114:3
.Ve
.PP
Generate a DH key using a DH parameters file:
.PP
.Vb 1
\& openssl genpkey \-paramfile dhp.pem \-out dhkey.pem
.Ve
.PP
Output DH parameters for safe prime group ffdhe2048:
.PP
.Vb 1
\& openssl genpkey \-genparam \-algorithm DH \-out dhp.pem \-pkeyopt group:ffdhe2048
.Ve
.PP
Output 2048 bit X9.42 DH parameters with 224 bit subgroup using RFC5114 group2:
.PP
.Vb 1
\& openssl genpkey \-genparam \-algorithm DHX \-out dhp.pem \-pkeyopt dh_rfc5114:2
.Ve
.PP
Output 2048 bit X9.42 DH parameters with 224 bit subgroup using FIP186\-4 keygen:
.PP
.Vb 3
\& openssl genpkey \-genparam \-algorithm DHX \-out dhp.pem \-text \e
\&     \-pkeyopt pbits:2048 \-pkeyopt qbits:224 \-pkeyopt digest:SHA256 \e
\&     \-pkeyopt gindex:1 \-pkeyopt dh_paramgen_type:2
.Ve
.PP
Output 1024 bit X9.42 DH parameters with 160 bit subgroup using FIP186\-2 keygen:
.PP
.Vb 3
\& openssl genpkey \-genparam \-algorithm DHX \-out dhp.pem \-text \e
\&     \-pkeyopt pbits:1024 \-pkeyopt qbits:160 \-pkeyopt digest:SHA1 \e
\&     \-pkeyopt gindex:1 \-pkeyopt dh_paramgen_type:1
.Ve
.PP
Output 2048 bit DH parameters:
.PP
.Vb 2
\& openssl genpkey \-genparam \-algorithm DH \-out dhp.pem \e
\&     \-pkeyopt dh_paramgen_prime_len:2048
.Ve
.PP
Output 2048 bit DH parameters using a generator:
.PP
.Vb 3
\& openssl genpkey \-genparam \-algorithm DH \-out dhpx.pem \e
\&     \-pkeyopt dh_paramgen_prime_len:2048 \e
\&     \-pkeyopt dh_paramgen_type:1
.Ve
.PP
Generate EC parameters:
.PP
.Vb 3
\& openssl genpkey \-genparam \-algorithm EC \-out ecp.pem \e
\&        \-pkeyopt ec_paramgen_curve:secp384r1 \e
\&        \-pkeyopt ec_param_enc:named_curve
.Ve
.PP
Generate EC key from parameters:
.PP
.Vb 1
\& openssl genpkey \-paramfile ecp.pem \-out eckey.pem
.Ve
.PP
Generate EC key directly:
.PP
.Vb 3
\& openssl genpkey \-algorithm EC \-out eckey.pem \e
\&        \-pkeyopt ec_paramgen_curve:P\-384 \e
\&        \-pkeyopt ec_param_enc:named_curve
.Ve
.PP
Generate an X25519 private key:
.PP
.Vb 1
\& openssl genpkey \-algorithm X25519 \-out xkey.pem
.Ve
.PP
Generate an ED448 private key:
.PP
.Vb 1
\& openssl genpkey \-algorithm ED448 \-out xkey.pem
.Ve
.SH HISTORY
.IX Header "HISTORY"
The ability to use NIST curve names, and to generate an EC key directly,
were added in OpenSSL 1.0.2.
The ability to generate X25519 keys was added in OpenSSL 1.1.0.
The ability to generate X448, ED25519 and ED448 keys was added in OpenSSL 1.1.1.
.PP
The \fB\-engine\fR option was deprecated in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2006\-2023 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
