.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "OPENSSL-NAMEDISPLAY-OPTIONS 1ossl"
.TH OPENSSL-NAMEDISPLAY-OPTIONS 1ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
openssl\-namedisplay\-options \- Distinguished name display options
.SH SYNOPSIS
.IX Header "SYNOPSIS"
\&\fBopenssl\fR
\&\fIcommand\fR
[ \fIoptions\fR ... ]
[ \fIparameters\fR ... ]
.SH DESCRIPTION
.IX Header "DESCRIPTION"
OpenSSL provides fine-grain control over how the subject and issuer DN's are
displayed.
This is specified by using the \fB\-nameopt\fR option, which takes a
comma-separated list of options from the following set.
An option may be preceded by a minus sign, \f(CW\*(C`\-\*(C'\fR, to turn it off.
The default value is \f(CW\*(C`oneline\*(C'\fR.
The first four are the most commonly used.
.SH OPTIONS
.IX Header "OPTIONS"
.SS "Name Format Option Arguments"
.IX Subsection "Name Format Option Arguments"
The DN output format can be fine tuned with the following flags.
.IP \fBcompat\fR 4
.IX Item "compat"
Display the name using an old format from previous OpenSSL versions.
.IP \fBRFC2253\fR 4
.IX Item "RFC2253"
Display the name using the format defined in RFC 2253.
It is equivalent to \fBesc_2253\fR, \fBesc_ctrl\fR, \fBesc_msb\fR, \fButf8\fR,
\&\fBdump_nostr\fR, \fBdump_unknown\fR, \fBdump_der\fR, \fBsep_comma_plus\fR, \fBdn_rev\fR
and \fBsname\fR.
.IP \fBoneline\fR 4
.IX Item "oneline"
Display the name in one line, using a format that is more readable
RFC 2253.
It is equivalent to \fBesc_2253\fR, \fBesc_ctrl\fR, \fBesc_msb\fR, \fButf8\fR,
\&\fBdump_nostr\fR, \fBdump_der\fR, \fBuse_quote\fR, \fBsep_comma_plus_space\fR,
\&\fBspace_eq\fR and \fBsname\fR options.
.IP \fBmultiline\fR 4
.IX Item "multiline"
Display the name using multiple lines.
It is equivalent to \fBesc_ctrl\fR, \fBesc_msb\fR, \fBsep_multiline\fR, \fBspace_eq\fR,
\&\fBlname\fR and \fBalign\fR.
.IP \fBesc_2253\fR 4
.IX Item "esc_2253"
Escape the "special" characters in a field, as required by RFC 2253.
That is, any of the characters \f(CW\*(C`,+"<>;\*(C'\fR, \f(CW\*(C`#\*(C'\fR at the beginning of
a string and leading or trailing spaces.
.IP \fBesc_2254\fR 4
.IX Item "esc_2254"
Escape the "special" characters in a field as required by RFC 2254 in a field.
That is, the \fBNUL\fR character and of \f(CW\*(C`()*\*(C'\fR.
.IP \fBesc_ctrl\fR 4
.IX Item "esc_ctrl"
Escape non-printable ASCII characters, codes less than 0x20 (space)
or greater than 0x7F (DELETE). They are displayed using RFC 2253 \f(CW\*(C`\eXX\*(C'\fR
notation where \fBXX\fR are the two hex digits representing the character value.
.IP \fBesc_msb\fR 4
.IX Item "esc_msb"
Escape any characters with the most significant bit set, that is with
values larger than 127, as described in \fBesc_ctrl\fR.
.IP \fBuse_quote\fR 4
.IX Item "use_quote"
Escapes some characters by surrounding the entire string with quotation
marks, \f(CW\*(C`"\*(C'\fR.
Without this option, individual special characters are preceded with
a backslash character, \f(CW\*(C`\e\*(C'\fR.
.IP \fButf8\fR 4
.IX Item "utf8"
Convert all strings to UTF\-8 format first as required by RFC 2253.
If the output device is UTF\-8 compatible, then using this option (and
not setting \fBesc_msb\fR) may give the correct display of multibyte
characters.
If this option is not set, then multibyte characters larger than 0xFF
will be output as \f(CW\*(C`\eUXXXX\*(C'\fR for 16 bits or \f(CW\*(C`\eWXXXXXXXX\*(C'\fR for 32 bits.
In addition, any UTF8Strings will be converted to their character form first.
.IP \fBignore_type\fR 4
.IX Item "ignore_type"
This option does not attempt to interpret multibyte characters in any
way. That is, the content octets are merely dumped as though one octet
represents each character. This is useful for diagnostic purposes but
will result in rather odd looking output.
.IP \fBshow_type\fR 4
.IX Item "show_type"
Display the type of the ASN1 character string before the value,
such as \f(CW\*(C`BMPSTRING: Hello World\*(C'\fR.
.IP \fBdump_der\fR 4
.IX Item "dump_der"
Any fields that would be output in hex format are displayed using
the DER encoding of the field.
If not set, just the content octets are displayed.
Either way, the \fB#XXXX...\fR format of RFC 2253 is used.
.IP \fBdump_nostr\fR 4
.IX Item "dump_nostr"
Dump non-character strings, such as ASN.1 \fBOCTET STRING\fR.
If this option is not set, then non character string types will be displayed
as though each content octet represents a single character.
.IP \fBdump_all\fR 4
.IX Item "dump_all"
Dump all fields. When this used with \fBdump_der\fR, this allows the
DER encoding of the structure to be unambiguously determined.
.IP \fBdump_unknown\fR 4
.IX Item "dump_unknown"
Dump any field whose OID is not recognised by OpenSSL.
.IP "\fBsep_comma_plus\fR, \fBsep_comma_plus_space\fR, \fBsep_semi_plus_space\fR, \fBsep_multiline\fR" 4
.IX Item "sep_comma_plus, sep_comma_plus_space, sep_semi_plus_space, sep_multiline"
Specify the field separators. The first word is used between the
Relative Distinguished Names (RDNs) and the second is between
multiple Attribute Value Assertions (AVAs). Multiple AVAs are
very rare and their use is discouraged.
The options ending in "space" additionally place a space after the separator to make it more readable.
The \fBsep_multiline\fR starts each field on its own line, and uses "plus space"
for the AVA separator.
It also indents the fields by four characters.
The default value is \fBsep_comma_plus_space\fR.
.IP \fBdn_rev\fR 4
.IX Item "dn_rev"
Reverse the fields of the DN as required by RFC 2253.
This also reverses the order of multiple AVAs in a field, but this is
permissible as there is no ordering on values.
.IP "\fBnofname\fR, \fBsname\fR, \fBlname\fR, \fBoid\fR" 4
.IX Item "nofname, sname, lname, oid"
Specify how the field name is displayed.
\&\fBnofname\fR does not display the field at all.
\&\fBsname\fR uses the "short name" form (CN for commonName for example).
\&\fBlname\fR uses the long form.
\&\fBoid\fR represents the OID in numerical form and is useful for
diagnostic purpose.
.IP \fBalign\fR 4
.IX Item "align"
Align field values for a more readable output. Only usable with
\&\fBsep_multiline\fR.
.IP \fBspace_eq\fR 4
.IX Item "space_eq"
Places spaces round the equal sign, \f(CW\*(C`=\*(C'\fR, character which follows the field
name.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2020 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
