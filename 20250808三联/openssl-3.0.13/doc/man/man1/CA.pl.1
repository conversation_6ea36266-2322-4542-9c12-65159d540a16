.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "CA.PL 1ossl"
.TH CA.PL 1ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
CA.pl \- friendlier interface for OpenSSL certificate programs
.SH SYNOPSIS
.IX Header "SYNOPSIS"
\&\fBCA.pl\fR
\&\fB\-?\fR |
\&\fB\-h\fR |
\&\fB\-help\fR
.PP
\&\fBCA.pl\fR
\&\fB\-newcert\fR |
\&\fB\-newreq\fR |
\&\fB\-newreq\-nodes\fR |
\&\fB\-xsign\fR |
\&\fB\-sign\fR |
\&\fB\-signCA\fR |
\&\fB\-signcert\fR |
\&\fB\-crl\fR |
\&\fB\-newca\fR
[\fB\-extra\-\fR\f(BIcmd\fR \fIparameter\fR]
.PP
\&\fBCA.pl\fR \fB\-pkcs12\fR [\fIcertname\fR]
.PP
\&\fBCA.pl\fR \fB\-verify\fR \fIcertfile\fR ...
.PP
\&\fBCA.pl\fR \fB\-revoke\fR \fIcertfile\fR [\fIreason\fR]
.SH DESCRIPTION
.IX Header "DESCRIPTION"
The \fBCA.pl\fR script is a perl script that supplies the relevant command line
arguments to the \fBopenssl\fR\|(1) command for some common certificate operations.
It is intended to simplify the process of certificate creation and management
by the use of some simple options.
.PP
The script is intended as a simple front end for the \fBopenssl\fR\|(1) program for
use by a beginner. Its behaviour isn't always what is wanted. For more control
over the behaviour of the certificate commands call the \fBopenssl\fR\|(1) command
directly.
.PP
Most of the filenames mentioned below can be modified by editing the
\&\fBCA.pl\fR script.
.PP
Under some environments it may not be possible to run the \fBCA.pl\fR script
directly (for example Win32) and the default configuration file location may
be wrong. In this case the command:
.PP
.Vb 1
\& perl \-S CA.pl
.Ve
.PP
can be used and the \fBOPENSSL_CONF\fR environment variable can be set to point to
the correct path of the configuration file.
.SH OPTIONS
.IX Header "OPTIONS"
.IP "\fB\-?\fR, \fB\-h\fR, \fB\-help\fR" 4
.IX Item "-?, -h, -help"
Prints a usage message.
.IP \fB\-newcert\fR 4
.IX Item "-newcert"
Creates a new self signed certificate. The private key is written to the file
\&\fInewkey.pem\fR and the request written to the file \fInewreq.pem\fR.
Invokes \fBopenssl\-req\fR\|(1).
.IP \fB\-newreq\fR 4
.IX Item "-newreq"
Creates a new certificate request. The private key is written to the file
\&\fInewkey.pem\fR and the request written to the file \fInewreq.pem\fR.
Executes \fBopenssl\-req\fR\|(1) under the hood.
.IP \fB\-newreq\-nodes\fR 4
.IX Item "-newreq-nodes"
Is like \fB\-newreq\fR except that the private key will not be encrypted.
Uses \fBopenssl\-req\fR\|(1).
.IP \fB\-newca\fR 4
.IX Item "-newca"
Creates a new CA hierarchy for use with the \fBca\fR program (or the \fB\-signcert\fR
and \fB\-xsign\fR options). The user is prompted to enter the filename of the CA
certificates (which should also contain the private key) or by hitting ENTER
details of the CA will be prompted for. The relevant files and directories
are created in a directory called \fIdemoCA\fR in the current directory.
Uses \fBopenssl\-req\fR\|(1) and \fBopenssl\-ca\fR\|(1).
.Sp
If the \fIdemoCA\fR directory already exists then the \fB\-newca\fR command will not
overwrite it and will do nothing. This can happen if a previous call using
the \fB\-newca\fR option terminated abnormally. To get the correct behaviour
delete the directory if it already exists.
.IP \fB\-pkcs12\fR 4
.IX Item "-pkcs12"
Create a PKCS#12 file containing the user certificate, private key and CA
certificate. It expects the user certificate and private key to be in the
file \fInewcert.pem\fR and the CA certificate to be in the file \fIdemoCA/cacert.pem\fR,
it creates a file \fInewcert.p12\fR. This command can thus be called after the
\&\fB\-sign\fR option. The PKCS#12 file can be imported directly into a browser.
If there is an additional argument on the command line it will be used as the
"friendly name" for the certificate (which is typically displayed in the browser
list box), otherwise the name "My Certificate" is used.
Delegates work to \fBopenssl\-pkcs12\fR\|(1).
.IP "\fB\-sign\fR, \fB\-signcert\fR, \fB\-xsign\fR" 4
.IX Item "-sign, -signcert, -xsign"
Calls the \fBopenssl\-ca\fR\|(1) command to sign a certificate request. It expects the
request to be in the file \fInewreq.pem\fR. The new certificate is written to the
file \fInewcert.pem\fR except in the case of the \fB\-xsign\fR option when it is
written to standard output.
.IP \fB\-signCA\fR 4
.IX Item "-signCA"
This option is the same as the \fB\-sign\fR option except it uses the
configuration file section \fBv3_ca\fR and so makes the signed request a
valid CA certificate. This is useful when creating intermediate CA from
a root CA.  Extra params are passed to \fBopenssl\-ca\fR\|(1).
.IP \fB\-signcert\fR 4
.IX Item "-signcert"
This option is the same as \fB\-sign\fR except it expects a self signed certificate
to be present in the file \fInewreq.pem\fR.
Extra params are passed to \fBopenssl\-x509\fR\|(1) and \fBopenssl\-ca\fR\|(1).
.IP \fB\-crl\fR 4
.IX Item "-crl"
Generate a CRL. Executes \fBopenssl\-ca\fR\|(1).
.IP "\fB\-revoke\fR \fIcertfile\fR [\fIreason\fR]" 4
.IX Item "-revoke certfile [reason]"
Revoke the certificate contained in the specified \fBcertfile\fR. An optional
reason may be specified, and must be one of: \fBunspecified\fR,
\&\fBkeyCompromise\fR, \fBCACompromise\fR, \fBaffiliationChanged\fR, \fBsuperseded\fR,
\&\fBcessationOfOperation\fR, \fBcertificateHold\fR, or \fBremoveFromCRL\fR.
Leverages \fBopenssl\-ca\fR\|(1).
.IP \fB\-verify\fR 4
.IX Item "-verify"
Verifies certificates against the CA certificate for \fIdemoCA\fR. If no
certificates are specified on the command line it tries to verify the file
\&\fInewcert.pem\fR.  Invokes \fBopenssl\-verify\fR\|(1).
.IP "\fB\-extra\-\fR\f(BIcmd\fR \fIparameter\fR" 4
.IX Item "-extra-cmd parameter"
For each option \fBextra\-\fR\f(BIcmd\fR, pass \fIparameter\fR to the \fBopenssl\fR\|(1)
sub-command with the same name as \fIcmd\fR, if that sub-command is invoked.
For example, if \fBopenssl\-req\fR\|(1) is invoked, the \fIparameter\fR given with
\&\fB\-extra\-req\fR will be passed to it.
For multi-word parameters, either repeat the option or quote the \fIparameters\fR
so it looks like one word to your shell.
See the individual command documentation for more information.
.SH EXAMPLES
.IX Header "EXAMPLES"
Create a CA hierarchy:
.PP
.Vb 1
\& CA.pl \-newca
.Ve
.PP
Complete certificate creation example: create a CA, create a request, sign
the request and finally create a PKCS#12 file containing it.
.PP
.Vb 4
\& CA.pl \-newca
\& CA.pl \-newreq
\& CA.pl \-sign
\& CA.pl \-pkcs12 "My Test Certificate"
.Ve
.SH ENVIRONMENT
.IX Header "ENVIRONMENT"
The environment variable \fBOPENSSL\fR may be used to specify the name of
the OpenSSL program. It can be a full pathname, or a relative one.
.PP
The environment variable \fBOPENSSL_CONFIG\fR may be used to specify a
configuration option and value to the \fBreq\fR and \fBca\fR commands invoked by
this script. It's value should be the option and pathname, as in
\&\f(CW\*(C`\-config /path/to/conf\-file\*(C'\fR.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBopenssl\fR\|(1),
\&\fBopenssl\-x509\fR\|(1),
\&\fBopenssl\-ca\fR\|(1),
\&\fBopenssl\-req\fR\|(1),
\&\fBopenssl\-pkcs12\fR\|(1),
\&\fBconfig\fR\|(5)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
