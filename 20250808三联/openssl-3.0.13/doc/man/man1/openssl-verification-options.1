.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "OPENSSL-VERIFICATION-OPTIONS 1ossl"
.TH OPENSSL-VERIFICATION-OPTIONS 1ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
openssl\-verification\-options \- generic X.509 certificate verification options
.SH SYNOPSIS
.IX Header "SYNOPSIS"
\&\fBopenssl\fR
\&\fIcommand\fR
[ \fIoptions\fR ... ]
[ \fIparameters\fR ... ]
.SH DESCRIPTION
.IX Header "DESCRIPTION"
There are many situations where X.509 certificates are verified
within the OpenSSL libraries and in various OpenSSL commands.
.PP
Certificate verification is implemented by \fBX509_verify_cert\fR\|(3).
It is a complicated process consisting of a number of steps
and depending on numerous options.
The most important of them are detailed in the following sections.
.PP
In a nutshell, a valid chain of certificates needs to be built up and verified
starting from the \fItarget certificate\fR that is to be verified
and ending in a certificate that due to some policy is trusted.
Verification is done relative to the given \fIpurpose\fR, which is the intended use
of the target certificate, such as SSL server, or by default for any purpose.
.PP
The details of how each OpenSSL command handles errors
are documented on the specific command page.
.PP
DANE support is documented in \fBopenssl\-s_client\fR\|(1),
\&\fBSSL_CTX_dane_enable\fR\|(3), \fBSSL_set1_host\fR\|(3),
\&\fBX509_VERIFY_PARAM_set_flags\fR\|(3), and \fBX509_check_host\fR\|(3).
.SS "Trust Anchors"
.IX Subsection "Trust Anchors"
In general, according to RFC 4158 and RFC 5280, a \fItrust anchor\fR is
any public key and related subject distinguished name (DN) that
for some reason is considered trusted
and thus is acceptable as the root of a chain of certificates.
.PP
In practice, trust anchors are given in the form of certificates,
where their essential fields are the public key and the subject DN.
In addition to the requirements in RFC 5280,
OpenSSL checks the validity period of such certificates
and makes use of some further fields.
In particular, the subject key identifier extension, if present,
is used for matching trust anchors during chain building.
.PP
In the most simple and common case, trust anchors are by default
all self-signed "root" CA certificates that are placed in the \fItrust store\fR,
which is a collection of certificates that are trusted for certain uses.
This is akin to what is used in the trust stores of Mozilla Firefox,
or Apple's and Microsoft's certificate stores, ...
.PP
From the OpenSSL perspective, a trust anchor is a certificate
that should be augmented with an explicit designation for which
uses of a target certificate the certificate may serve as a trust anchor.
In PEM encoding, this is indicated by the \f(CW\*(C`TRUSTED CERTIFICATE\*(C'\fR string.
Such a designation provides a set of positive trust attributes
explicitly stating trust for the listed purposes
and/or a set of negative trust attributes
explicitly rejecting the use for the listed purposes.
The purposes are encoded using the values defined for the extended key usages
(EKUs) that may be given in X.509 extensions of end-entity certificates.
See also the "Extended Key Usage" section below.
.PP
The currently recognized uses are
\&\fBclientAuth\fR (SSL client use), \fBserverAuth\fR (SSL server use),
\&\fBemailProtection\fR (S/MIME email use), \fBcodeSigning\fR (object signer use),
\&\fBOCSPSigning\fR (OCSP responder use), \fBOCSP\fR (OCSP request use),
\&\fBtimeStamping\fR (TSA server use), and \fBanyExtendedKeyUsage\fR.
As of OpenSSL 1.1.0, the last of these blocks all uses when rejected or
enables all uses when trusted.
.PP
A certificate, which may be CA certificate or an end-entity certificate,
is considered a trust anchor for the given use
if and only if all the following conditions hold:
.IP \(bu 4
It is an an element of the trust store.
.IP \(bu 4
It does not have a negative trust attribute rejecting the given use.
.IP \(bu 4
It has a positive trust attribute accepting the given use
or (by default) one of the following compatibility conditions apply:
It is self-signed or the \fB\-partial_chain\fR option is given
(which corresponds to the \fBX509_V_FLAG_PARTIAL_CHAIN\fR flag being set).
.SS "Certification Path Building"
.IX Subsection "Certification Path Building"
First, a certificate chain is built up starting from the target certificate
and ending in a trust anchor.
.PP
The chain is built up iteratively, looking up in turn
a certificate with suitable key usage that
matches as an issuer of the current "subject" certificate as described below.
If there is such a certificate, the first one found that is currently valid
is taken, otherwise the one that expired most recently of all such certificates.
For efficiency, no backtracking is performed, thus
any further candidate issuer certificates that would match equally are ignored.
.PP
When a self-signed certificate has been added, chain construction stops.
In this case it must fully match a trust anchor, otherwise chain building fails.
.PP
A candidate issuer certificate matches a subject certificate
if all of the following conditions hold:
.IP \(bu 4
Its subject name matches the issuer name of the subject certificate.
.IP \(bu 4
If the subject certificate has an authority key identifier extension,
each of its sub-fields equals the corresponding subject key identifier, serial
number, and issuer field of the candidate issuer certificate,
as far as the respective fields are present in both certificates.
.IP \(bu 4
The certificate signature algorithm used to sign the subject certificate
is supported and
equals the public key algorithm of the candidate issuer certificate.
.PP
The lookup first searches for issuer certificates in the trust store.
If it does not find a match there it consults
the list of untrusted ("intermediate" CA) certificates, if provided.
.SS "Certification Path Validation"
.IX Subsection "Certification Path Validation"
When the certificate chain building process was successful
the chain components and their links are checked thoroughly.
.PP
The first step is to check that each certificate is well-formed.
Part of these checks are enabled only if the \fB\-x509_strict\fR option is given.
.PP
The second step is to check the extensions of every untrusted certificate
for consistency with the supplied purpose.
If the \fB\-purpose\fR option is not given then no such checks are done
except for SSL/TLS connection setup,
where by default \f(CW\*(C`sslserver\*(C'\fR or \f(CW\*(C`sslclient\*(C'\fR, are checked.
The target or "leaf" certificate, as well as any other untrusted certificates,
must have extensions compatible with the specified purpose.
All certificates except the target or "leaf" must also be valid CA certificates.
The precise extensions required are described in more detail in
"CERTIFICATE EXTENSIONS" in \fBopenssl\-x509\fR\|(1).
.PP
The third step is to check the trust settings on the last certificate
(which typically is a self-signed root CA certificate).
It must be trusted for the given use.
For compatibility with previous versions of OpenSSL, a self-signed certificate
with no trust attributes is considered to be valid for all uses.
.PP
The fourth, and final, step is to check the validity of the certificate chain.
For each element in the chain, including the root CA certificate,
the validity period as specified by the \f(CW\*(C`notBefore\*(C'\fR and \f(CW\*(C`notAfter\*(C'\fR fields
is checked against the current system time.
The \fB\-attime\fR flag may be used to use a reference time other than "now."
The certificate signature is checked as well
(except for the signature of the typically self-signed root CA certificate,
which is verified only if the \fB\-check_ss_sig\fR option is given).
When verifying a certificate signature
the keyUsage extension (if present) of the candidate issuer certificate
is checked to permit digitalSignature for signing proxy certificates
or to permit keyCertSign for signing other certificates, respectively.
If all operations complete successfully then certificate is considered
valid. If any operation fails then the certificate is not valid.
.SH OPTIONS
.IX Header "OPTIONS"
.SS "Trusted Certificate Options"
.IX Subsection "Trusted Certificate Options"
The following options specify how to supply the certificates
that can be used as trust anchors for certain uses.
As mentioned, a collection of such certificates is called a \fItrust store\fR.
.PP
Note that OpenSSL does not provide a default set of trust anchors.  Many
Linux distributions include a system default and configure OpenSSL to point
to that.  Mozilla maintains an influential trust store that can be found at
<https://www.mozilla.org/en\-US/about/governance/policies/security\-group/certs/>.
.PP
The certificates to add to the trust store
can be specified using following options.
.IP "\fB\-CAfile\fR \fIfile\fR" 4
.IX Item "-CAfile file"
Load the specified file which contains a certificate
or several of them in case the input is in PEM or PKCS#12 format.
PEM-encoded certificates may also have trust attributes set.
.IP \fB\-no\-CAfile\fR 4
.IX Item "-no-CAfile"
Do not load the default file of trusted certificates.
.IP "\fB\-CApath\fR \fIdir\fR" 4
.IX Item "-CApath dir"
Use the specified directory as a collection of trusted certificates,
i.e., a trust store.
Files should be named with the hash value of the X.509 SubjectName of each
certificate. This is so that the library can extract the IssuerName,
hash it, and directly lookup the file to get the issuer certificate.
See \fBopenssl\-rehash\fR\|(1) for information on creating this type of directory.
.IP \fB\-no\-CApath\fR 4
.IX Item "-no-CApath"
Do not use the default directory of trusted certificates.
.IP "\fB\-CAstore\fR \fIuri\fR" 4
.IX Item "-CAstore uri"
Use \fIuri\fR as a store of CA certificates.
The URI may indicate a single certificate, as well as a collection of them.
With URIs in the \f(CW\*(C`file:\*(C'\fR scheme, this acts as \fB\-CAfile\fR or
\&\fB\-CApath\fR, depending on if the URI indicates a single file or
directory.
See \fBossl_store\-file\fR\|(7) for more information on the \f(CW\*(C`file:\*(C'\fR scheme.
.Sp
These certificates are also used when building the server certificate
chain (for example with \fBopenssl\-s_server\fR\|(1)) or client certificate
chain (for example with \fBopenssl\-s_time\fR\|(1)).
.IP \fB\-no\-CAstore\fR 4
.IX Item "-no-CAstore"
Do not use the default store of trusted CA certificates.
.SS "Verification Options"
.IX Subsection "Verification Options"
The certificate verification can be fine-tuned with the following flags.
.IP \fB\-verbose\fR 4
.IX Item "-verbose"
Print extra information about the operations being performed.
.IP "\fB\-attime\fR \fItimestamp\fR" 4
.IX Item "-attime timestamp"
Perform validation checks using time specified by \fItimestamp\fR and not
current system time. \fItimestamp\fR is the number of seconds since
January 1, 1970 (i.e., the Unix Epoch).
.IP \fB\-no_check_time\fR 4
.IX Item "-no_check_time"
This option suppresses checking the validity period of certificates and CRLs
against the current time. If option \fB\-attime\fR is used to specify
a verification time, the check is not suppressed.
.IP \fB\-x509_strict\fR 4
.IX Item "-x509_strict"
This disables non-compliant workarounds for broken certificates.
Thus errors are thrown on certificates not compliant with RFC 5280.
.Sp
When this option is set,
among others, the following certificate well-formedness conditions are checked:
.RS 4
.IP \(bu 4
The basicConstraints of CA certificates must be marked critical.
.IP \(bu 4
CA certificates must explicitly include the keyUsage extension.
.IP \(bu 4
If a pathlenConstraint is given the key usage keyCertSign must be allowed.
.IP \(bu 4
The pathlenConstraint must not be given for non-CA certificates.
.IP \(bu 4
The issuer name of any certificate must not be empty.
.IP \(bu 4
The subject name of CA certs, certs with keyUsage crlSign, and certs
without subjectAlternativeName must not be empty.
.IP \(bu 4
If a subjectAlternativeName extension is given it must not be empty.
.IP \(bu 4
The signatureAlgorithm field and the cert signature must be consistent.
.IP \(bu 4
Any given authorityKeyIdentifier and any given subjectKeyIdentifier
must not be marked critical.
.IP \(bu 4
The authorityKeyIdentifier must be given for X.509v3 certs unless they
are self-signed.
.IP \(bu 4
The subjectKeyIdentifier must be given for all X.509v3 CA certs.
.RE
.RS 4
.RE
.IP \fB\-ignore_critical\fR 4
.IX Item "-ignore_critical"
Normally if an unhandled critical extension is present that is not
supported by OpenSSL the certificate is rejected (as required by RFC5280).
If this option is set critical extensions are ignored.
.IP \fB\-issuer_checks\fR 4
.IX Item "-issuer_checks"
Ignored.
.IP \fB\-crl_check\fR 4
.IX Item "-crl_check"
Checks end entity certificate validity by attempting to look up a valid CRL.
If a valid CRL cannot be found an error occurs.
.IP \fB\-crl_check_all\fR 4
.IX Item "-crl_check_all"
Checks the validity of \fBall\fR certificates in the chain by attempting
to look up valid CRLs.
.IP \fB\-use_deltas\fR 4
.IX Item "-use_deltas"
Enable support for delta CRLs.
.IP \fB\-extended_crl\fR 4
.IX Item "-extended_crl"
Enable extended CRL features such as indirect CRLs and alternate CRL
signing keys.
.IP "\fB\-suiteB_128_only\fR, \fB\-suiteB_128\fR, \fB\-suiteB_192\fR" 4
.IX Item "-suiteB_128_only, -suiteB_128, -suiteB_192"
Enable the Suite B mode operation at 128 bit Level of Security, 128 bit or
192 bit, or only 192 bit Level of Security respectively.
See RFC6460 for details. In particular the supported signature algorithms are
reduced to support only ECDSA and SHA256 or SHA384 and only the elliptic curves
P\-256 and P\-384.
.IP "\fB\-auth_level\fR \fIlevel\fR" 4
.IX Item "-auth_level level"
Set the certificate chain authentication security level to \fIlevel\fR.
The authentication security level determines the acceptable signature and
public key strength when verifying certificate chains.  For a certificate
chain to validate, the public keys of all the certificates must meet the
specified security \fIlevel\fR.  The signature algorithm security level is
enforced for all the certificates in the chain except for the chain's
\&\fItrust anchor\fR, which is either directly trusted or validated by means
other than its signature.  See \fBSSL_CTX_set_security_level\fR\|(3) for the
definitions of the available levels.  The default security level is \-1,
or "not set".  At security level 0 or lower all algorithms are acceptable.
Security level 1 requires at least 80\-bit\-equivalent security and is broadly
interoperable, though it will, for example, reject MD5 signatures or RSA
keys shorter than 1024 bits.
.IP \fB\-partial_chain\fR 4
.IX Item "-partial_chain"
Allow verification to succeed if an incomplete chain can be built.
That is, a chain ending in a certificate that normally would not be trusted
(because it has no matching positive trust attributes and is not self-signed)
but is an element of the trust store.
This certificate may be self-issued or belong to an intermediate CA.
.IP \fB\-check_ss_sig\fR 4
.IX Item "-check_ss_sig"
Verify the signature of
the last certificate in a chain if the certificate is supposedly self-signed.
This is prohibited and will result in an error if it is a non-conforming CA
certificate with key usage restrictions not including the keyCertSign bit.
This verification is disabled by default because it doesn't add any security.
.IP \fB\-allow_proxy_certs\fR 4
.IX Item "-allow_proxy_certs"
Allow the verification of proxy certificates.
.IP \fB\-trusted_first\fR 4
.IX Item "-trusted_first"
As of OpenSSL 1.1.0 this option is on by default and cannot be disabled.
.Sp
When constructing the certificate chain, the trusted certificates specified
via \fB\-CAfile\fR, \fB\-CApath\fR, \fB\-CAstore\fR or \fB\-trusted\fR are always used
before any certificates specified via \fB\-untrusted\fR.
.IP \fB\-no_alt_chains\fR 4
.IX Item "-no_alt_chains"
As of OpenSSL 1.1.0, since \fB\-trusted_first\fR always on, this option has no
effect.
.IP "\fB\-trusted\fR \fIfile\fR" 4
.IX Item "-trusted file"
Parse \fIfile\fR as a set of one or more certificates.
Each of them qualifies as trusted if has a suitable positive trust attribute
or it is self-signed or the \fB\-partial_chain\fR option is specified.
This option implies the \fB\-no\-CAfile\fR, \fB\-no\-CApath\fR, and \fB\-no\-CAstore\fR options
and it cannot be used with the \fB\-CAfile\fR, \fB\-CApath\fR or \fB\-CAstore\fR options, so
only certificates specified using the \fB\-trusted\fR option are trust anchors.
This option may be used multiple times.
.IP "\fB\-untrusted\fR \fIfile\fR" 4
.IX Item "-untrusted file"
Parse \fIfile\fR as a set of one or more certificates.
All certificates (typically of intermediate CAs) are considered untrusted
and may be used to
construct a certificate chain from the target certificate to a trust anchor.
This option may be used multiple times.
.IP "\fB\-policy\fR \fIarg\fR" 4
.IX Item "-policy arg"
Enable policy processing and add \fIarg\fR to the user-initial-policy-set (see
RFC5280). The policy \fIarg\fR can be an object name an OID in numeric form.
This argument can appear more than once.
.IP \fB\-explicit_policy\fR 4
.IX Item "-explicit_policy"
Set policy variable require-explicit-policy (see RFC5280).
.IP \fB\-policy_check\fR 4
.IX Item "-policy_check"
Enables certificate policy processing.
.IP \fB\-policy_print\fR 4
.IX Item "-policy_print"
Print out diagnostics related to policy processing.
.IP \fB\-inhibit_any\fR 4
.IX Item "-inhibit_any"
Set policy variable inhibit-any-policy (see RFC5280).
.IP \fB\-inhibit_map\fR 4
.IX Item "-inhibit_map"
Set policy variable inhibit-policy-mapping (see RFC5280).
.IP "\fB\-purpose\fR \fIpurpose\fR" 4
.IX Item "-purpose purpose"
The intended use for the certificate.
Currently defined purposes are \f(CW\*(C`sslclient\*(C'\fR, \f(CW\*(C`sslserver\*(C'\fR, \f(CW\*(C`nssslserver\*(C'\fR,
\&\f(CW\*(C`smimesign\*(C'\fR, \f(CW\*(C`smimeencrypt\*(C'\fR, \f(CW\*(C`crlsign\*(C'\fR, \f(CW\*(C`ocsphelper\*(C'\fR, \f(CW\*(C`timestampsign\*(C'\fR,
and \f(CW\*(C`any\*(C'\fR.
If peer certificate verification is enabled, by default the TLS implementation
as well as the commands \fBs_client\fR and \fBs_server\fR check for consistency
with TLS server or TLS client use, respectively.
.Sp
While IETF RFC 5280 says that \fBid-kp-serverAuth\fR and \fBid-kp-clientAuth\fR
are only for WWW use, in practice they are used for all kinds of TLS clients
and servers, and this is what OpenSSL assumes as well.
.IP "\fB\-verify_depth\fR \fInum\fR" 4
.IX Item "-verify_depth num"
Limit the certificate chain to \fInum\fR intermediate CA certificates.
A maximal depth chain can have up to \fInum\fR+2 certificates, since neither the
end-entity certificate nor the trust-anchor certificate count against the
\&\fB\-verify_depth\fR limit.
.IP "\fB\-verify_email\fR \fIemail\fR" 4
.IX Item "-verify_email email"
Verify if \fIemail\fR matches the email address in Subject Alternative Name or
the email in the subject Distinguished Name.
.IP "\fB\-verify_hostname\fR \fIhostname\fR" 4
.IX Item "-verify_hostname hostname"
Verify if \fIhostname\fR matches DNS name in Subject Alternative Name or
Common Name in the subject certificate.
.IP "\fB\-verify_ip\fR \fIip\fR" 4
.IX Item "-verify_ip ip"
Verify if \fIip\fR matches the IP address in Subject Alternative Name of
the subject certificate.
.IP "\fB\-verify_name\fR \fIname\fR" 4
.IX Item "-verify_name name"
Use default verification policies like trust model and required certificate
policies identified by \fIname\fR.
The trust model determines which auxiliary trust or reject OIDs are applicable
to verifying the given certificate chain.
They can be given using the \fB\-addtrust\fR and \fB\-addreject\fR options
for \fBopenssl\-x509\fR\|(1).
Supported policy names include: \fBdefault\fR, \fBpkcs7\fR, \fBsmime_sign\fR,
\&\fBssl_client\fR, \fBssl_server\fR.
These mimics the combinations of purpose and trust settings used in SSL, CMS
and S/MIME.
As of OpenSSL 1.1.0, the trust model is inferred from the purpose when not
specified, so the \fB\-verify_name\fR options are functionally equivalent to the
corresponding \fB\-purpose\fR settings.
.SS "Extended Verification Options"
.IX Subsection "Extended Verification Options"
Sometimes there may be more than one certificate chain leading to an
end-entity certificate.
This usually happens when a root or intermediate CA signs a certificate
for another a CA in other organization.
Another reason is when a CA might have intermediates that use two different
signature formats, such as a SHA\-1 and a SHA\-256 digest.
.PP
The following options can be used to provide data that will allow the
OpenSSL command to generate an alternative chain.
.IP "\fB\-xkey\fR \fIinfile\fR, \fB\-xcert\fR \fIinfile\fR, \fB\-xchain\fR" 4
.IX Item "-xkey infile, -xcert infile, -xchain"
Specify an extra certificate, private key and certificate chain. These behave
in the same manner as the \fB\-cert\fR, \fB\-key\fR and \fB\-cert_chain\fR options.  When
specified, the callback returning the first valid chain will be in use by the
client.
.IP \fB\-xchain_build\fR 4
.IX Item "-xchain_build"
Specify whether the application should build the certificate chain to be
provided to the server for the extra certificates via the \fB\-xkey\fR,
\&\fB\-xcert\fR, and \fB\-xchain\fR options.
.IP "\fB\-xcertform\fR \fBDER\fR|\fBPEM\fR|\fBP12\fR" 4
.IX Item "-xcertform DER|PEM|P12"
The input format for the extra certificate.
This option has no effect and is retained for backward compatibility only.
.IP "\fB\-xkeyform\fR \fBDER\fR|\fBPEM\fR|\fBP12\fR" 4
.IX Item "-xkeyform DER|PEM|P12"
The input format for the extra key.
This option has no effect and is retained for backward compatibility only.
.SS "Certificate Extensions"
.IX Subsection "Certificate Extensions"
Options like \fB\-purpose\fR lead to checking the certificate extensions,
which determine what the target certificate and intermediate CA certificates
can be used for.
.PP
\fIBasic Constraints\fR
.IX Subsection "Basic Constraints"
.PP
The basicConstraints extension CA flag is used to determine whether the
certificate can be used as a CA. If the CA flag is true then it is a CA,
if the CA flag is false then it is not a CA. \fBAll\fR CAs should have the
CA flag set to true.
.PP
If the basicConstraints extension is absent,
which includes the case that it is an X.509v1 certificate,
then the certificate is considered to be a "possible CA" and
other extensions are checked according to the intended use of the certificate.
The treatment of certificates without basicConstraints as a CA
is presently supported, but this could change in the future.
.PP
\fIKey Usage\fR
.IX Subsection "Key Usage"
.PP
If the keyUsage extension is present then additional restraints are
made on the uses of the certificate. A CA certificate \fBmust\fR have the
keyCertSign bit set if the keyUsage extension is present.
.PP
\fIExtended Key Usage\fR
.IX Subsection "Extended Key Usage"
.PP
The extKeyUsage (EKU) extension places additional restrictions on the
certificate uses. If this extension is present (whether critical or not)
the key can only be used for the purposes specified.
.PP
A complete description of each check is given below. The comments about
basicConstraints and keyUsage and X.509v1 certificates above apply to \fBall\fR
CA certificates.
.IP "\fBSSL Client\fR" 4
.IX Item "SSL Client"
The extended key usage extension must be absent or include the "web client
authentication" OID.  The keyUsage extension must be absent or it must have the
digitalSignature bit set.  The Netscape certificate type must be absent
or it must have the SSL client bit set.
.IP "\fBSSL Client CA\fR" 4
.IX Item "SSL Client CA"
The extended key usage extension must be absent or include the "web client
authentication" OID.
The Netscape certificate type must be absent or it must have the SSL CA bit set.
This is used as a work around if the basicConstraints extension is absent.
.IP "\fBSSL Server\fR" 4
.IX Item "SSL Server"
The extended key usage extension must be absent or include the "web server
authentication" and/or one of the SGC OIDs.  The keyUsage extension must be
absent or it
must have the digitalSignature, the keyEncipherment set or both bits set.
The Netscape certificate type must be absent or have the SSL server bit set.
.IP "\fBSSL Server CA\fR" 4
.IX Item "SSL Server CA"
The extended key usage extension must be absent or include the "web server
authentication" and/or one of the SGC OIDs.  The Netscape certificate type must
be absent or the SSL CA bit must be set.
This is used as a work around if the basicConstraints extension is absent.
.IP "\fBNetscape SSL Server\fR" 4
.IX Item "Netscape SSL Server"
For Netscape SSL clients to connect to an SSL server it must have the
keyEncipherment bit set if the keyUsage extension is present. This isn't
always valid because some cipher suites use the key for digital signing.
Otherwise it is the same as a normal SSL server.
.IP "\fBCommon S/MIME Client Tests\fR" 4
.IX Item "Common S/MIME Client Tests"
The extended key usage extension must be absent or include the "email
protection" OID.  The Netscape certificate type must be absent or should have the
S/MIME bit set. If the S/MIME bit is not set in the Netscape certificate type
then the SSL client bit is tolerated as an alternative but a warning is shown.
This is because some Verisign certificates don't set the S/MIME bit.
.IP "\fBS/MIME Signing\fR" 4
.IX Item "S/MIME Signing"
In addition to the common S/MIME client tests the digitalSignature bit or
the nonRepudiation bit must be set if the keyUsage extension is present.
.IP "\fBS/MIME Encryption\fR" 4
.IX Item "S/MIME Encryption"
In addition to the common S/MIME tests the keyEncipherment bit must be set
if the keyUsage extension is present.
.IP "\fBS/MIME CA\fR" 4
.IX Item "S/MIME CA"
The extended key usage extension must be absent or include the "email
protection" OID.  The Netscape certificate type must be absent or must have the
S/MIME CA bit set.
This is used as a work around if the basicConstraints extension is absent.
.IP "\fBCRL Signing\fR" 4
.IX Item "CRL Signing"
The keyUsage extension must be absent or it must have the CRL signing bit
set.
.IP "\fBCRL Signing CA\fR" 4
.IX Item "CRL Signing CA"
The normal CA tests apply. Except in this case the basicConstraints extension
must be present.
.SH BUGS
.IX Header "BUGS"
The issuer checks still suffer from limitations in the underlying X509_LOOKUP
API.  One consequence of this is that trusted certificates with matching
subject name must appear in a file (as specified by the \fB\-CAfile\fR option),
a directory (as specified by \fB\-CApath\fR),
or a store (as specified by \fB\-CAstore\fR).
If there are multiple such matches, possibly in multiple locations,
only the first one (in the mentioned order of locations) is recognised.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBX509_verify_cert\fR\|(3),
\&\fBopenssl\-verify\fR\|(1),
\&\fBopenssl\-ocsp\fR\|(1),
\&\fBopenssl\-ts\fR\|(1),
\&\fBopenssl\-s_client\fR\|(1),
\&\fBopenssl\-s_server\fR\|(1),
\&\fBopenssl\-smime\fR\|(1),
\&\fBopenssl\-cmp\fR\|(1),
\&\fBopenssl\-cms\fR\|(1)
.SH HISTORY
.IX Header "HISTORY"
The checks enabled by \fB\-x509_strict\fR have been extended in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2023 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
