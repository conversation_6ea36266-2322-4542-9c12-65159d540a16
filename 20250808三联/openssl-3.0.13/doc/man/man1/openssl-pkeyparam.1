.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "OPENSSL-PKEYPARAM 1ossl"
.TH OPENSSL-PKEYPARAM 1ossl 2025-08-08 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
openssl\-pkeyparam \- public key algorithm parameter processing command
.SH SYNOPSIS
.IX Header "SYNOPSIS"
\&\fBopenssl\fR \fBpkeyparam\fR
[\fB\-help\fR]
[\fB\-in\fR \fIfilename\fR]
[\fB\-out\fR \fIfilename\fR]
[\fB\-text\fR]
[\fB\-noout\fR]
[\fB\-check\fR]
[\fB\-engine\fR \fIid\fR]
[\fB\-provider\fR \fIname\fR]
[\fB\-provider\-path\fR \fIpath\fR]
[\fB\-propquery\fR \fIpropq\fR]
.SH DESCRIPTION
.IX Header "DESCRIPTION"
This command processes public key algorithm parameters.
They can be checked for correctness and their components printed out.
.SH OPTIONS
.IX Header "OPTIONS"
.IP \fB\-help\fR 4
.IX Item "-help"
Print out a usage message.
.IP "\fB\-in\fR \fIfilename\fR" 4
.IX Item "-in filename"
This specifies the input filename to read parameters from or standard input if
this option is not specified.
.IP "\fB\-out\fR \fIfilename\fR" 4
.IX Item "-out filename"
This specifies the output filename to write parameters to or standard output if
this option is not specified.
.IP \fB\-text\fR 4
.IX Item "-text"
Prints out the parameters in plain text in addition to the encoded version.
.IP \fB\-noout\fR 4
.IX Item "-noout"
Do not output the encoded version of the parameters.
.IP \fB\-check\fR 4
.IX Item "-check"
This option checks the correctness of parameters.
.IP "\fB\-engine\fR \fIid\fR" 4
.IX Item "-engine id"
See "Engine Options" in \fBopenssl\fR\|(1).
This option is deprecated.
.IP "\fB\-provider\fR \fIname\fR" 4
.IX Item "-provider name"
.PD 0
.IP "\fB\-provider\-path\fR \fIpath\fR" 4
.IX Item "-provider-path path"
.IP "\fB\-propquery\fR \fIpropq\fR" 4
.IX Item "-propquery propq"
.PD
See "Provider Options" in \fBopenssl\fR\|(1), \fBprovider\fR\|(7), and \fBproperty\fR\|(7).
.SH EXAMPLES
.IX Header "EXAMPLES"
Print out text version of parameters:
.PP
.Vb 1
\& openssl pkeyparam \-in param.pem \-text
.Ve
.SH NOTES
.IX Header "NOTES"
There are no \fB\-inform\fR or \fB\-outform\fR options for this command because only
PEM format is supported because the key type is determined by the PEM headers.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBopenssl\fR\|(1),
\&\fBopenssl\-genpkey\fR\|(1),
\&\fBopenssl\-rsa\fR\|(1),
\&\fBopenssl\-pkcs8\fR\|(1),
\&\fBopenssl\-dsa\fR\|(1),
\&\fBopenssl\-genrsa\fR\|(1),
\&\fBopenssl\-gendsa\fR\|(1)
.SH HISTORY
.IX Header "HISTORY"
The \fB\-engine\fR option was deprecated in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2006\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
