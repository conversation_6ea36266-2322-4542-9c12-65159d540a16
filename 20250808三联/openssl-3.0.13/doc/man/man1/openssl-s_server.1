.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "OPENSSL-S_SERVER 1ossl"
.TH OPENSSL-S_SERVER 1ossl 2025-08-08 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
openssl\-s_server \- SSL/TLS server program
.SH SYNOPSIS
.IX Header "SYNOPSIS"
\&\fBopenssl\fR \fBs_server\fR
[\fB\-help\fR]
[\fB\-port\fR \fI+int\fR]
[\fB\-accept\fR \fIval\fR]
[\fB\-unix\fR \fIval\fR]
[\fB\-4\fR]
[\fB\-6\fR]
[\fB\-unlink\fR]
[\fB\-context\fR \fIval\fR]
[\fB\-verify\fR \fIint\fR]
[\fB\-Verify\fR \fIint\fR]
[\fB\-cert\fR \fIinfile\fR]
[\fB\-cert2\fR \fIinfile\fR]
[\fB\-certform\fR \fBDER\fR|\fBPEM\fR|\fBP12\fR]
[\fB\-cert_chain\fR \fIinfile\fR]
[\fB\-build_chain\fR]
[\fB\-serverinfo\fR \fIval\fR]
[\fB\-key\fR \fIfilename\fR|\fIuri\fR]
[\fB\-key2\fR \fIfilename\fR|\fIuri\fR]
[\fB\-keyform\fR \fBDER\fR|\fBPEM\fR|\fBP12\fR|\fBENGINE\fR]
[\fB\-pass\fR \fIval\fR]
[\fB\-dcert\fR \fIinfile\fR]
[\fB\-dcertform\fR \fBDER\fR|\fBPEM\fR|\fBP12\fR]
[\fB\-dcert_chain\fR \fIinfile\fR]
[\fB\-dkey\fR \fIfilename\fR|\fIuri\fR]
[\fB\-dkeyform\fR \fBDER\fR|\fBPEM\fR|\fBP12\fR|\fBENGINE\fR]
[\fB\-dpass\fR \fIval\fR]
[\fB\-nbio_test\fR]
[\fB\-crlf\fR]
[\fB\-debug\fR]
[\fB\-msg\fR]
[\fB\-msgfile\fR \fIoutfile\fR]
[\fB\-state\fR]
[\fB\-nocert\fR]
[\fB\-quiet\fR]
[\fB\-no_resume_ephemeral\fR]
[\fB\-www\fR]
[\fB\-WWW\fR]
[\fB\-http_server_binmode\fR]
[\fB\-no_ca_names\fR]
[\fB\-ignore_unexpected_eof\fR]
[\fB\-servername\fR]
[\fB\-servername_fatal\fR]
[\fB\-tlsextdebug\fR]
[\fB\-HTTP\fR]
[\fB\-id_prefix\fR \fIval\fR]
[\fB\-keymatexport\fR \fIval\fR]
[\fB\-keymatexportlen\fR \fI+int\fR]
[\fB\-CRL\fR \fIinfile\fR]
[\fB\-CRLform\fR \fBDER\fR|\fBPEM\fR]
[\fB\-crl_download\fR]
[\fB\-chainCAfile\fR \fIinfile\fR]
[\fB\-chainCApath\fR \fIdir\fR]
[\fB\-chainCAstore\fR \fIuri\fR]
[\fB\-verifyCAfile\fR \fIinfile\fR]
[\fB\-verifyCApath\fR \fIdir\fR]
[\fB\-verifyCAstore\fR \fIuri\fR]
[\fB\-no_cache\fR]
[\fB\-ext_cache\fR]
[\fB\-verify_return_error\fR]
[\fB\-verify_quiet\fR]
[\fB\-ign_eof\fR]
[\fB\-no_ign_eof\fR]
[\fB\-no_etm\fR]
[\fB\-status\fR]
[\fB\-status_verbose\fR]
[\fB\-status_timeout\fR \fIint\fR]
[\fB\-proxy\fR \fI[http[s]://][userinfo@]host[:port][/path]\fR]
[\fB\-no_proxy\fR \fIaddresses\fR]
[\fB\-status_url\fR \fIval\fR]
[\fB\-status_file\fR \fIinfile\fR]
[\fB\-ssl_config\fR \fIval\fR]
[\fB\-trace\fR]
[\fB\-security_debug\fR]
[\fB\-security_debug_verbose\fR]
[\fB\-brief\fR]
[\fB\-rev\fR]
[\fB\-async\fR]
[\fB\-max_send_frag\fR \fI+int\fR]
[\fB\-split_send_frag\fR \fI+int\fR]
[\fB\-max_pipelines\fR \fI+int\fR]
[\fB\-naccept\fR \fI+int\fR]
[\fB\-read_buf\fR \fI+int\fR]
[\fB\-bugs\fR]
[\fB\-no_comp\fR]
[\fB\-comp\fR]
[\fB\-no_ticket\fR]
[\fB\-serverpref\fR]
[\fB\-legacy_renegotiation\fR]
[\fB\-no_renegotiation\fR]
[\fB\-no_resumption_on_reneg\fR]
[\fB\-allow_no_dhe_kex\fR]
[\fB\-prioritize_chacha\fR]
[\fB\-strict\fR]
[\fB\-sigalgs\fR \fIval\fR]
[\fB\-client_sigalgs\fR \fIval\fR]
[\fB\-groups\fR \fIval\fR]
[\fB\-curves\fR \fIval\fR]
[\fB\-named_curve\fR \fIval\fR]
[\fB\-cipher\fR \fIval\fR]
[\fB\-ciphersuites\fR \fIval\fR]
[\fB\-dhparam\fR \fIinfile\fR]
[\fB\-record_padding\fR \fIval\fR]
[\fB\-debug_broken_protocol\fR]
[\fB\-nbio\fR]
[\fB\-psk_identity\fR \fIval\fR]
[\fB\-psk_hint\fR \fIval\fR]
[\fB\-psk\fR \fIval\fR]
[\fB\-psk_session\fR \fIfile\fR]
[\fB\-srpvfile\fR \fIinfile\fR]
[\fB\-srpuserseed\fR \fIval\fR]
[\fB\-timeout\fR]
[\fB\-mtu\fR \fI+int\fR]
[\fB\-listen\fR]
[\fB\-sctp\fR]
[\fB\-sctp_label_bug\fR]
[\fB\-use_srtp\fR \fIval\fR]
[\fB\-no_dhe\fR]
[\fB\-nextprotoneg\fR \fIval\fR]
[\fB\-alpn\fR \fIval\fR]
[\fB\-sendfile\fR]
[\fB\-keylogfile\fR \fIoutfile\fR]
[\fB\-recv_max_early_data\fR \fIint\fR]
[\fB\-max_early_data\fR \fIint\fR]
[\fB\-early_data\fR]
[\fB\-stateless\fR]
[\fB\-anti_replay\fR]
[\fB\-no_anti_replay\fR]
[\fB\-num_tickets\fR]
[\fB\-nameopt\fR \fIoption\fR]
[\fB\-no_ssl3\fR]
[\fB\-no_tls1\fR]
[\fB\-no_tls1_1\fR]
[\fB\-no_tls1_2\fR]
[\fB\-no_tls1_3\fR]
[\fB\-ssl3\fR]
[\fB\-tls1\fR]
[\fB\-tls1_1\fR]
[\fB\-tls1_2\fR]
[\fB\-tls1_3\fR]
[\fB\-dtls\fR]
[\fB\-dtls1\fR]
[\fB\-dtls1_2\fR]
[\fB\-allow_proxy_certs\fR]
[\fB\-attime\fR \fItimestamp\fR]
[\fB\-no_check_time\fR]
[\fB\-check_ss_sig\fR]
[\fB\-crl_check\fR]
[\fB\-crl_check_all\fR]
[\fB\-explicit_policy\fR]
[\fB\-extended_crl\fR]
[\fB\-ignore_critical\fR]
[\fB\-inhibit_any\fR]
[\fB\-inhibit_map\fR]
[\fB\-partial_chain\fR]
[\fB\-policy\fR \fIarg\fR]
[\fB\-policy_check\fR]
[\fB\-policy_print\fR]
[\fB\-purpose\fR \fIpurpose\fR]
[\fB\-suiteB_128\fR]
[\fB\-suiteB_128_only\fR]
[\fB\-suiteB_192\fR]
[\fB\-trusted_first\fR]
[\fB\-no_alt_chains\fR]
[\fB\-use_deltas\fR]
[\fB\-auth_level\fR \fInum\fR]
[\fB\-verify_depth\fR \fInum\fR]
[\fB\-verify_email\fR \fIemail\fR]
[\fB\-verify_hostname\fR \fIhostname\fR]
[\fB\-verify_ip\fR \fIip\fR]
[\fB\-verify_name\fR \fIname\fR]
[\fB\-x509_strict\fR]
[\fB\-issuer_checks\fR]
[\fB\-bugs\fR]
[\fB\-no_comp\fR]
[\fB\-comp\fR]
[\fB\-no_ticket\fR]
[\fB\-serverpref\fR]
[\fB\-client_renegotiation\fR]
[\fB\-legacy_renegotiation\fR]
[\fB\-no_renegotiation\fR]
[\fB\-no_resumption_on_reneg\fR]
[\fB\-legacy_server_connect\fR]
[\fB\-no_legacy_server_connect\fR]
[\fB\-no_etm\fR]
[\fB\-allow_no_dhe_kex\fR]
[\fB\-prioritize_chacha\fR]
[\fB\-strict\fR]
[\fB\-sigalgs\fR \fIalgs\fR]
[\fB\-client_sigalgs\fR \fIalgs\fR]
[\fB\-groups\fR \fIgroups\fR]
[\fB\-curves\fR \fIcurves\fR]
[\fB\-named_curve\fR \fIcurve\fR]
[\fB\-cipher\fR \fIciphers\fR]
[\fB\-ciphersuites\fR \fI1.3ciphers\fR]
[\fB\-min_protocol\fR \fIminprot\fR]
[\fB\-max_protocol\fR \fImaxprot\fR]
[\fB\-record_padding\fR \fIpadding\fR]
[\fB\-debug_broken_protocol\fR]
[\fB\-no_middlebox\fR]
[\fB\-xkey\fR \fIinfile\fR]
[\fB\-xcert\fR \fIfile\fR]
[\fB\-xchain\fR \fIfile\fR]
[\fB\-xchain_build\fR \fIfile\fR]
[\fB\-xcertform\fR \fBDER\fR|\fBPEM\fR]>
[\fB\-xkeyform\fR \fBDER\fR|\fBPEM\fR]>
[\fB\-CAfile\fR \fIfile\fR]
[\fB\-no\-CAfile\fR]
[\fB\-CApath\fR \fIdir\fR]
[\fB\-no\-CApath\fR]
[\fB\-CAstore\fR \fIuri\fR]
[\fB\-no\-CAstore\fR]
[\fB\-rand\fR \fIfiles\fR]
[\fB\-writerand\fR \fIfile\fR]
[\fB\-engine\fR \fIid\fR]
[\fB\-provider\fR \fIname\fR]
[\fB\-provider\-path\fR \fIpath\fR]
[\fB\-propquery\fR \fIpropq\fR]
.SH DESCRIPTION
.IX Header "DESCRIPTION"
This command implements a generic SSL/TLS server which
listens for connections on a given port using SSL/TLS.
.SH OPTIONS
.IX Header "OPTIONS"
In addition to the options below, this command also supports
the common and server only options documented
"Supported Command Line Commands" in \fBSSL_CONF_cmd\fR\|(3)
.IP \fB\-help\fR 4
.IX Item "-help"
Print out a usage message.
.IP "\fB\-port\fR \fI+int\fR" 4
.IX Item "-port +int"
The TCP port to listen on for connections. If not specified 4433 is used.
.IP "\fB\-accept\fR \fIval\fR" 4
.IX Item "-accept val"
The optional TCP host and port to listen on for connections. If not specified, *:4433 is used.
.IP "\fB\-unix\fR \fIval\fR" 4
.IX Item "-unix val"
Unix domain socket to accept on.
.IP \fB\-4\fR 4
.IX Item "-4"
Use IPv4 only.
.IP \fB\-6\fR 4
.IX Item "-6"
Use IPv6 only.
.IP \fB\-unlink\fR 4
.IX Item "-unlink"
For \-unix, unlink any existing socket first.
.IP "\fB\-context\fR \fIval\fR" 4
.IX Item "-context val"
Sets the SSL context id. It can be given any string value. If this option
is not present a default value will be used.
.IP "\fB\-verify\fR \fIint\fR, \fB\-Verify\fR \fIint\fR" 4
.IX Item "-verify int, -Verify int"
The verify depth to use. This specifies the maximum length of the
client certificate chain and makes the server request a certificate from
the client. With the \fB\-verify\fR option a certificate is requested but the
client does not have to send one, with the \fB\-Verify\fR option the client
must supply a certificate or an error occurs.
.Sp
If the cipher suite cannot request a client certificate (for example an
anonymous cipher suite or PSK) this option has no effect.
.IP "\fB\-cert\fR \fIinfile\fR" 4
.IX Item "-cert infile"
The certificate to use, most servers cipher suites require the use of a
certificate and some require a certificate with a certain public key type:
for example the DSS cipher suites require a certificate containing a DSS
(DSA) key. If not specified then the filename \fIserver.pem\fR will be used.
.IP "\fB\-cert2\fR \fIinfile\fR" 4
.IX Item "-cert2 infile"
The certificate file to use for servername; default is \f(CW\*(C`server2.pem\*(C'\fR.
.IP "\fB\-certform\fR \fBDER\fR|\fBPEM\fR|\fBP12\fR" 4
.IX Item "-certform DER|PEM|P12"
The server certificate file format; unspecified by default.
See \fBopenssl\-format\-options\fR\|(1) for details.
.IP \fB\-cert_chain\fR 4
.IX Item "-cert_chain"
A file or URI of untrusted certificates to use when attempting to build the
certificate chain related to the certificate specified via the \fB\-cert\fR option.
The input can be in PEM, DER, or PKCS#12 format.
.IP \fB\-build_chain\fR 4
.IX Item "-build_chain"
Specify whether the application should build the server certificate chain to be
provided to the client.
.IP "\fB\-serverinfo\fR \fIval\fR" 4
.IX Item "-serverinfo val"
A file containing one or more blocks of PEM data.  Each PEM block
must encode a TLS ServerHello extension (2 bytes type, 2 bytes length,
followed by "length" bytes of extension data).  If the client sends
an empty TLS ClientHello extension matching the type, the corresponding
ServerHello extension will be returned.
.IP "\fB\-key\fR \fIfilename\fR|\fIuri\fR" 4
.IX Item "-key filename|uri"
The private key to use. If not specified then the certificate file will
be used.
.IP "\fB\-key2\fR \fIfilename\fR|\fIuri\fR" 4
.IX Item "-key2 filename|uri"
The private Key file to use for servername if not given via \fB\-cert2\fR.
.IP "\fB\-keyform\fR \fBDER\fR|\fBPEM\fR|\fBP12\fR|\fBENGINE\fR" 4
.IX Item "-keyform DER|PEM|P12|ENGINE"
The key format; unspecified by default.
See \fBopenssl\-format\-options\fR\|(1) for details.
.IP "\fB\-pass\fR \fIval\fR" 4
.IX Item "-pass val"
The private key and certificate file password source.
For more information about the format of \fIval\fR,
see \fBopenssl\-passphrase\-options\fR\|(1).
.IP "\fB\-dcert\fR \fIinfile\fR, \fB\-dkey\fR \fIfilename\fR|\fIuri\fR" 4
.IX Item "-dcert infile, -dkey filename|uri"
Specify an additional certificate and private key, these behave in the
same manner as the \fB\-cert\fR and \fB\-key\fR options except there is no default
if they are not specified (no additional certificate and key is used). As
noted above some cipher suites require a certificate containing a key of
a certain type. Some cipher suites need a certificate carrying an RSA key
and some a DSS (DSA) key. By using RSA and DSS certificates and keys
a server can support clients which only support RSA or DSS cipher suites
by using an appropriate certificate.
.IP \fB\-dcert_chain\fR 4
.IX Item "-dcert_chain"
A file or URI of untrusted certificates to use when attempting to build the
server certificate chain when a certificate specified via the \fB\-dcert\fR option
is in use.
The input can be in PEM, DER, or PKCS#12 format.
.IP "\fB\-dcertform\fR \fBDER\fR|\fBPEM\fR|\fBP12\fR" 4
.IX Item "-dcertform DER|PEM|P12"
The format of the additional certificate file; unspecified by default.
See \fBopenssl\-format\-options\fR\|(1) for details.
.IP "\fB\-dkeyform\fR \fBDER\fR|\fBPEM\fR|\fBP12\fR|\fBENGINE\fR" 4
.IX Item "-dkeyform DER|PEM|P12|ENGINE"
The format of the additional private key; unspecified by default.
See \fBopenssl\-format\-options\fR\|(1) for details.
.IP "\fB\-dpass\fR \fIval\fR" 4
.IX Item "-dpass val"
The passphrase for the additional private key and certificate.
For more information about the format of \fIval\fR,
see \fBopenssl\-passphrase\-options\fR\|(1).
.IP \fB\-nbio_test\fR 4
.IX Item "-nbio_test"
Tests non blocking I/O.
.IP \fB\-crlf\fR 4
.IX Item "-crlf"
This option translated a line feed from the terminal into CR+LF.
.IP \fB\-debug\fR 4
.IX Item "-debug"
Print extensive debugging information including a hex dump of all traffic.
.IP \fB\-security_debug\fR 4
.IX Item "-security_debug"
Print output from SSL/TLS security framework.
.IP \fB\-security_debug_verbose\fR 4
.IX Item "-security_debug_verbose"
Print more output from SSL/TLS security framework
.IP \fB\-msg\fR 4
.IX Item "-msg"
Show all protocol messages with hex dump.
.IP "\fB\-msgfile\fR \fIoutfile\fR" 4
.IX Item "-msgfile outfile"
File to send output of \fB\-msg\fR or \fB\-trace\fR to, default standard output.
.IP \fB\-state\fR 4
.IX Item "-state"
Prints the SSL session states.
.IP "\fB\-CRL\fR \fIinfile\fR" 4
.IX Item "-CRL infile"
The CRL file to use.
.IP "\fB\-CRLform\fR \fBDER\fR|\fBPEM\fR" 4
.IX Item "-CRLform DER|PEM"
The CRL file format; unspecified by default.
See \fBopenssl\-format\-options\fR\|(1) for details.
.IP \fB\-crl_download\fR 4
.IX Item "-crl_download"
Download CRLs from distribution points given in CDP extensions of certificates
.IP "\fB\-verifyCAfile\fR \fIfilename\fR" 4
.IX Item "-verifyCAfile filename"
A file in PEM format CA containing trusted certificates to use
for verifying client certificates.
.IP "\fB\-verifyCApath\fR \fIdir\fR" 4
.IX Item "-verifyCApath dir"
A directory containing trusted certificates to use
for verifying client certificates.
This directory must be in "hash format",
see \fBopenssl\-verify\fR\|(1) for more information.
.IP "\fB\-verifyCAstore\fR \fIuri\fR" 4
.IX Item "-verifyCAstore uri"
The URI of a store containing trusted certificates to use
for verifying client certificates.
.IP "\fB\-chainCAfile\fR \fIfile\fR" 4
.IX Item "-chainCAfile file"
A file in PEM format containing trusted certificates to use
when attempting to build the server certificate chain.
.IP "\fB\-chainCApath\fR \fIdir\fR" 4
.IX Item "-chainCApath dir"
A directory containing trusted certificates to use
for building the server certificate chain provided to the client.
This directory must be in "hash format",
see \fBopenssl\-verify\fR\|(1) for more information.
.IP "\fB\-chainCAstore\fR \fIuri\fR" 4
.IX Item "-chainCAstore uri"
The URI of a store containing trusted certificates to use
for building the server certificate chain provided to the client.
The URI may indicate a single certificate, as well as a collection of them.
With URIs in the \f(CW\*(C`file:\*(C'\fR scheme, this acts as \fB\-chainCAfile\fR or
\&\fB\-chainCApath\fR, depending on if the URI indicates a directory or a
single file.
See \fBossl_store\-file\fR\|(7) for more information on the \f(CW\*(C`file:\*(C'\fR scheme.
.IP \fB\-nocert\fR 4
.IX Item "-nocert"
If this option is set then no certificate is used. This restricts the
cipher suites available to the anonymous ones (currently just anonymous
DH).
.IP \fB\-quiet\fR 4
.IX Item "-quiet"
Inhibit printing of session and certificate information.
.IP \fB\-no_resume_ephemeral\fR 4
.IX Item "-no_resume_ephemeral"
Disable caching and tickets if ephemeral (EC)DH is used.
.IP \fB\-tlsextdebug\fR 4
.IX Item "-tlsextdebug"
Print a hex dump of any TLS extensions received from the server.
.IP \fB\-www\fR 4
.IX Item "-www"
Sends a status message back to the client when it connects. This includes
information about the ciphers used and various session parameters.
The output is in HTML format so this option can be used with a web browser.
The special URL \f(CW\*(C`/renegcert\*(C'\fR turns on client cert validation, and \f(CW\*(C`/reneg\*(C'\fR
tells the server to request renegotiation.
The \fB\-early_data\fR option cannot be used with this option.
.IP "\fB\-WWW\fR, \fB\-HTTP\fR" 4
.IX Item "-WWW, -HTTP"
Emulates a simple web server. Pages will be resolved relative to the
current directory, for example if the URL \f(CW\*(C`https://myhost/page.html\*(C'\fR is
requested the file \fI./page.html\fR will be sent.
If the \fB\-HTTP\fR flag is used, the files are sent directly, and should contain
any HTTP response headers (including status response line).
If the \fB\-WWW\fR option is used,
the response headers are generated by the server, and the file extension is
examined to determine the \fBContent-Type\fR header.
Extensions of \f(CW\*(C`html\*(C'\fR, \f(CW\*(C`htm\*(C'\fR, and \f(CW\*(C`php\*(C'\fR are \f(CW\*(C`text/html\*(C'\fR and all others are
\&\f(CW\*(C`text/plain\*(C'\fR.
In addition, the special URL \f(CW\*(C`/stats\*(C'\fR will return status
information like the \fB\-www\fR option.
Neither of these options can be used in conjunction with \fB\-early_data\fR.
.IP \fB\-http_server_binmode\fR 4
.IX Item "-http_server_binmode"
When acting as web-server (using option \fB\-WWW\fR or \fB\-HTTP\fR) open files requested
by the client in binary mode.
.IP \fB\-no_ca_names\fR 4
.IX Item "-no_ca_names"
Disable TLS Extension CA Names. You may want to disable it for security reasons
or for compatibility with some Windows TLS implementations crashing when this
extension is larger than 1024 bytes.
.IP \fB\-ignore_unexpected_eof\fR 4
.IX Item "-ignore_unexpected_eof"
Some TLS implementations do not send the mandatory close_notify alert on
shutdown. If the application tries to wait for the close_notify alert but the
peer closes the connection without sending it, an error is generated. When this
option is enabled the peer does not need to send the close_notify alert and a
closed connection will be treated as if the close_notify alert was received.
For more information on shutting down a connection, see \fBSSL_shutdown\fR\|(3).
.IP \fB\-servername\fR 4
.IX Item "-servername"
Servername for HostName TLS extension.
.IP \fB\-servername_fatal\fR 4
.IX Item "-servername_fatal"
On servername mismatch send fatal alert (default: warning alert).
.IP "\fB\-id_prefix\fR \fIval\fR" 4
.IX Item "-id_prefix val"
Generate SSL/TLS session IDs prefixed by \fIval\fR. This is mostly useful
for testing any SSL/TLS code (e.g. proxies) that wish to deal with multiple
servers, when each of which might be generating a unique range of session
IDs (e.g. with a certain prefix).
.IP \fB\-keymatexport\fR 4
.IX Item "-keymatexport"
Export keying material using label.
.IP \fB\-keymatexportlen\fR 4
.IX Item "-keymatexportlen"
Export the given number of bytes of keying material; default 20.
.IP \fB\-no_cache\fR 4
.IX Item "-no_cache"
Disable session cache.
.IP \fB\-ext_cache\fR. 4
.IX Item "-ext_cache."
Disable internal cache, set up and use external cache.
.IP \fB\-verify_return_error\fR 4
.IX Item "-verify_return_error"
Verification errors normally just print a message but allow the
connection to continue, for debugging purposes.
If this option is used, then verification errors close the connection.
.IP \fB\-verify_quiet\fR 4
.IX Item "-verify_quiet"
No verify output except verify errors.
.IP \fB\-ign_eof\fR 4
.IX Item "-ign_eof"
Ignore input EOF (default: when \fB\-quiet\fR).
.IP \fB\-no_ign_eof\fR 4
.IX Item "-no_ign_eof"
Do not ignore input EOF.
.IP \fB\-no_etm\fR 4
.IX Item "-no_etm"
Disable Encrypt-then-MAC negotiation.
.IP \fB\-status\fR 4
.IX Item "-status"
Enables certificate status request support (aka OCSP stapling).
.IP \fB\-status_verbose\fR 4
.IX Item "-status_verbose"
Enables certificate status request support (aka OCSP stapling) and gives
a verbose printout of the OCSP response.
.IP "\fB\-status_timeout\fR \fIint\fR" 4
.IX Item "-status_timeout int"
Sets the timeout for OCSP response to \fIint\fR seconds.
.IP "\fB\-proxy\fR \fI[http[s]://][userinfo@]host[:port][/path]\fR" 4
.IX Item "-proxy [http[s]://][userinfo@]host[:port][/path]"
The HTTP(S) proxy server to use for reaching the OCSP server unless \fB\-no_proxy\fR
applies, see below.
The proxy port defaults to 80 or 443 if the scheme is \f(CW\*(C`https\*(C'\fR; apart from that
the optional \f(CW\*(C`http://\*(C'\fR or \f(CW\*(C`https://\*(C'\fR prefix is ignored,
as well as any userinfo and path components.
Defaults to the environment variable \f(CW\*(C`http_proxy\*(C'\fR if set, else \f(CW\*(C`HTTP_PROXY\*(C'\fR
in case no TLS is used, otherwise \f(CW\*(C`https_proxy\*(C'\fR if set, else \f(CW\*(C`HTTPS_PROXY\*(C'\fR.
.IP "\fB\-no_proxy\fR \fIaddresses\fR" 4
.IX Item "-no_proxy addresses"
List of IP addresses and/or DNS names of servers
not to use an HTTP(S) proxy for, separated by commas and/or whitespace
(where in the latter case the whole argument must be enclosed in "...").
Default is from the environment variable \f(CW\*(C`no_proxy\*(C'\fR if set, else \f(CW\*(C`NO_PROXY\*(C'\fR.
.IP "\fB\-status_url\fR \fIval\fR" 4
.IX Item "-status_url val"
Sets a fallback responder URL to use if no responder URL is present in the
server certificate. Without this option an error is returned if the server
certificate does not contain a responder address.
The optional userinfo and fragment URL components are ignored.
Any given query component is handled as part of the path component.
.IP "\fB\-status_file\fR \fIinfile\fR" 4
.IX Item "-status_file infile"
Overrides any OCSP responder URLs from the certificate and always provides the
OCSP Response stored in the file. The file must be in DER format.
.IP "\fB\-ssl_config\fR \fIval\fR" 4
.IX Item "-ssl_config val"
Configure SSL_CTX using the given configuration value.
.IP \fB\-trace\fR 4
.IX Item "-trace"
Show verbose trace output of protocol messages.
.IP \fB\-brief\fR 4
.IX Item "-brief"
Provide a brief summary of connection parameters instead of the normal verbose
output.
.IP \fB\-rev\fR 4
.IX Item "-rev"
Simple echo server that sends back received text reversed. Also sets \fB\-brief\fR.
Cannot be used in conjunction with \fB\-early_data\fR.
.IP \fB\-async\fR 4
.IX Item "-async"
Switch on asynchronous mode. Cryptographic operations will be performed
asynchronously. This will only have an effect if an asynchronous capable engine
is also used via the \fB\-engine\fR option. For test purposes the dummy async engine
(dasync) can be used (if available).
.IP "\fB\-max_send_frag\fR \fI+int\fR" 4
.IX Item "-max_send_frag +int"
The maximum size of data fragment to send.
See \fBSSL_CTX_set_max_send_fragment\fR\|(3) for further information.
.IP "\fB\-split_send_frag\fR \fI+int\fR" 4
.IX Item "-split_send_frag +int"
The size used to split data for encrypt pipelines. If more data is written in
one go than this value then it will be split into multiple pipelines, up to the
maximum number of pipelines defined by max_pipelines. This only has an effect if
a suitable cipher suite has been negotiated, an engine that supports pipelining
has been loaded, and max_pipelines is greater than 1. See
\&\fBSSL_CTX_set_split_send_fragment\fR\|(3) for further information.
.IP "\fB\-max_pipelines\fR \fI+int\fR" 4
.IX Item "-max_pipelines +int"
The maximum number of encrypt/decrypt pipelines to be used. This will only have
an effect if an engine has been loaded that supports pipelining (e.g. the dasync
engine) and a suitable cipher suite has been negotiated. The default value is 1.
See \fBSSL_CTX_set_max_pipelines\fR\|(3) for further information.
.IP "\fB\-naccept\fR \fI+int\fR" 4
.IX Item "-naccept +int"
The server will exit after receiving the specified number of connections,
default unlimited.
.IP "\fB\-read_buf\fR \fI+int\fR" 4
.IX Item "-read_buf +int"
The default read buffer size to be used for connections. This will only have an
effect if the buffer size is larger than the size that would otherwise be used
and pipelining is in use (see \fBSSL_CTX_set_default_read_buffer_len\fR\|(3) for
further information).
.IP \fB\-bugs\fR 4
.IX Item "-bugs"
There are several known bugs in SSL and TLS implementations. Adding this
option enables various workarounds.
.IP \fB\-no_comp\fR 4
.IX Item "-no_comp"
Disable negotiation of TLS compression.
TLS compression is not recommended and is off by default as of
OpenSSL 1.1.0.
.IP \fB\-comp\fR 4
.IX Item "-comp"
Enable negotiation of TLS compression.
This option was introduced in OpenSSL 1.1.0.
TLS compression is not recommended and is off by default as of
OpenSSL 1.1.0.
.IP \fB\-no_ticket\fR 4
.IX Item "-no_ticket"
Disable RFC4507bis session ticket support. This option has no effect if TLSv1.3
is negotiated. See \fB\-num_tickets\fR.
.IP \fB\-num_tickets\fR 4
.IX Item "-num_tickets"
Control the number of tickets that will be sent to the client after a full
handshake in TLSv1.3. The default number of tickets is 2. This option does not
affect the number of tickets sent after a resumption handshake.
.IP \fB\-serverpref\fR 4
.IX Item "-serverpref"
Use the server's cipher preferences, rather than the client's preferences.
.IP \fB\-prioritize_chacha\fR 4
.IX Item "-prioritize_chacha"
Prioritize ChaCha ciphers when preferred by clients. Requires \fB\-serverpref\fR.
.IP \fB\-no_resumption_on_reneg\fR 4
.IX Item "-no_resumption_on_reneg"
Set the \fBSSL_OP_NO_SESSION_RESUMPTION_ON_RENEGOTIATION\fR option.
.IP "\fB\-client_sigalgs\fR \fIval\fR" 4
.IX Item "-client_sigalgs val"
Signature algorithms to support for client certificate authentication
(colon-separated list).
.IP "\fB\-named_curve\fR \fIval\fR" 4
.IX Item "-named_curve val"
Specifies the elliptic curve to use. NOTE: this is single curve, not a list.
For a list of all possible curves, use:
.Sp
.Vb 1
\&    $ openssl ecparam \-list_curves
.Ve
.IP "\fB\-cipher\fR \fIval\fR" 4
.IX Item "-cipher val"
This allows the list of TLSv1.2 and below ciphersuites used by the server to be
modified. This list is combined with any TLSv1.3 ciphersuites that have been
configured. When the client sends a list of supported ciphers the first client
cipher also included in the server list is used. Because the client specifies
the preference order, the order of the server cipherlist is irrelevant. See
\&\fBopenssl\-ciphers\fR\|(1) for more information.
.IP "\fB\-ciphersuites\fR \fIval\fR" 4
.IX Item "-ciphersuites val"
This allows the list of TLSv1.3 ciphersuites used by the server to be modified.
This list is combined with any TLSv1.2 and below ciphersuites that have been
configured. When the client sends a list of supported ciphers the first client
cipher also included in the server list is used. Because the client specifies
the preference order, the order of the server cipherlist is irrelevant. See
\&\fBopenssl\-ciphers\fR\|(1) command for more information. The format for this list is
a simple colon (":") separated list of TLSv1.3 ciphersuite names.
.IP "\fB\-dhparam\fR \fIinfile\fR" 4
.IX Item "-dhparam infile"
The DH parameter file to use. The ephemeral DH cipher suites generate keys
using a set of DH parameters. If not specified then an attempt is made to
load the parameters from the server certificate file.
If this fails then a static set of parameters hard coded into this command
will be used.
.IP \fB\-nbio\fR 4
.IX Item "-nbio"
Turns on non blocking I/O.
.IP \fB\-timeout\fR 4
.IX Item "-timeout"
Enable timeouts.
.IP \fB\-mtu\fR 4
.IX Item "-mtu"
Set link-layer MTU.
.IP "\fB\-psk_identity\fR \fIval\fR" 4
.IX Item "-psk_identity val"
Expect the client to send PSK identity \fIval\fR when using a PSK
cipher suite, and warn if they do not.  By default, the expected PSK
identity is the string "Client_identity".
.IP "\fB\-psk_hint\fR \fIval\fR" 4
.IX Item "-psk_hint val"
Use the PSK identity hint \fIval\fR when using a PSK cipher suite.
.IP "\fB\-psk\fR \fIval\fR" 4
.IX Item "-psk val"
Use the PSK key \fIval\fR when using a PSK cipher suite. The key is
given as a hexadecimal number without leading 0x, for example \-psk
1a2b3c4d.
This option must be provided in order to use a PSK cipher.
.IP "\fB\-psk_session\fR \fIfile\fR" 4
.IX Item "-psk_session file"
Use the pem encoded SSL_SESSION data stored in \fIfile\fR as the basis of a PSK.
Note that this will only work if TLSv1.3 is negotiated.
.IP \fB\-srpvfile\fR 4
.IX Item "-srpvfile"
The verifier file for SRP.
This option is deprecated.
.IP \fB\-srpuserseed\fR 4
.IX Item "-srpuserseed"
A seed string for a default user salt.
This option is deprecated.
.IP \fB\-listen\fR 4
.IX Item "-listen"
This option can only be used in conjunction with one of the DTLS options above.
With this option, this command will listen on a UDP port for incoming
connections.
Any ClientHellos that arrive will be checked to see if they have a cookie in
them or not.
Any without a cookie will be responded to with a HelloVerifyRequest.
If a ClientHello with a cookie is received then this command will
connect to that peer and complete the handshake.
.IP \fB\-sctp\fR 4
.IX Item "-sctp"
Use SCTP for the transport protocol instead of UDP in DTLS. Must be used in
conjunction with \fB\-dtls\fR, \fB\-dtls1\fR or \fB\-dtls1_2\fR. This option is only
available where OpenSSL has support for SCTP enabled.
.IP \fB\-sctp_label_bug\fR 4
.IX Item "-sctp_label_bug"
Use the incorrect behaviour of older OpenSSL implementations when computing
endpoint-pair shared secrets for DTLS/SCTP. This allows communication with
older broken implementations but breaks interoperability with correct
implementations. Must be used in conjunction with \fB\-sctp\fR. This option is only
available where OpenSSL has support for SCTP enabled.
.IP \fB\-use_srtp\fR 4
.IX Item "-use_srtp"
Offer SRTP key management with a colon-separated profile list.
.IP \fB\-no_dhe\fR 4
.IX Item "-no_dhe"
If this option is set then no DH parameters will be loaded effectively
disabling the ephemeral DH cipher suites.
.IP "\fB\-alpn\fR \fIval\fR, \fB\-nextprotoneg\fR \fIval\fR" 4
.IX Item "-alpn val, -nextprotoneg val"
These flags enable the Application-Layer Protocol Negotiation
or Next Protocol Negotiation (NPN) extension, respectively. ALPN is the
IETF standard and replaces NPN.
The \fIval\fR list is a comma-separated list of supported protocol
names.  The list should contain the most desirable protocols first.
Protocol names are printable ASCII strings, for example "http/1.1" or
"spdy/3".
The flag \fB\-nextprotoneg\fR cannot be specified if \fB\-tls1_3\fR is used.
.IP \fB\-sendfile\fR 4
.IX Item "-sendfile"
If this option is set and KTLS is enabled, \fBSSL_sendfile()\fR will be used
instead of \fBBIO_write()\fR to send the HTTP response requested by a client.
This option is only valid if \fB\-WWW\fR or \fB\-HTTP\fR is specified.
.IP "\fB\-keylogfile\fR \fIoutfile\fR" 4
.IX Item "-keylogfile outfile"
Appends TLS secrets to the specified keylog file such that external programs
(like Wireshark) can decrypt TLS connections.
.IP "\fB\-max_early_data\fR \fIint\fR" 4
.IX Item "-max_early_data int"
Change the default maximum early data bytes that are specified for new sessions
and any incoming early data (when used in conjunction with the \fB\-early_data\fR
flag). The default value is approximately 16k. The argument must be an integer
greater than or equal to 0.
.IP "\fB\-recv_max_early_data\fR \fIint\fR" 4
.IX Item "-recv_max_early_data int"
Specify the hard limit on the maximum number of early data bytes that will
be accepted.
.IP \fB\-early_data\fR 4
.IX Item "-early_data"
Accept early data where possible. Cannot be used in conjunction with \fB\-www\fR,
\&\fB\-WWW\fR, \fB\-HTTP\fR or \fB\-rev\fR.
.IP \fB\-stateless\fR 4
.IX Item "-stateless"
Require TLSv1.3 cookies.
.IP "\fB\-anti_replay\fR, \fB\-no_anti_replay\fR" 4
.IX Item "-anti_replay, -no_anti_replay"
Switches replay protection on or off, respectively. Replay protection is on by
default unless overridden by a configuration file. When it is on, OpenSSL will
automatically detect if a session ticket has been used more than once, TLSv1.3
has been negotiated, and early data is enabled on the server. A full handshake
is forced if a session ticket is used a second or subsequent time. Any early
data that was sent will be rejected.
.IP "\fB\-nameopt\fR \fIoption\fR" 4
.IX Item "-nameopt option"
This specifies how the subject or issuer names are displayed.
See \fBopenssl\-namedisplay\-options\fR\|(1) for details.
.IP "\fB\-no_ssl3\fR, \fB\-no_tls1\fR, \fB\-no_tls1_1\fR, \fB\-no_tls1_2\fR, \fB\-no_tls1_3\fR, \fB\-ssl3\fR, \fB\-tls1\fR, \fB\-tls1_1\fR, \fB\-tls1_2\fR, \fB\-tls1_3\fR" 4
.IX Item "-no_ssl3, -no_tls1, -no_tls1_1, -no_tls1_2, -no_tls1_3, -ssl3, -tls1, -tls1_1, -tls1_2, -tls1_3"
See "TLS Version Options" in \fBopenssl\fR\|(1).
.IP "\fB\-dtls\fR, \fB\-dtls1\fR, \fB\-dtls1_2\fR" 4
.IX Item "-dtls, -dtls1, -dtls1_2"
These specify the use of DTLS instead of TLS.
See "TLS Version Options" in \fBopenssl\fR\|(1).
.IP "\fB\-bugs\fR, \fB\-comp\fR, \fB\-no_comp\fR, \fB\-no_ticket\fR, \fB\-serverpref\fR, \fB\-client_renegotiation\fR, \fB\-legacy_renegotiation\fR, \fB\-no_renegotiation\fR, \fB\-no_resumption_on_reneg\fR, \fB\-legacy_server_connect\fR, \fB\-no_legacy_server_connect\fR, \fB\-no_etm\fR \fB\-allow_no_dhe_kex\fR, \fB\-prioritize_chacha\fR, \fB\-strict\fR, \fB\-sigalgs\fR \fIalgs\fR, \fB\-client_sigalgs\fR \fIalgs\fR, \fB\-groups\fR \fIgroups\fR, \fB\-curves\fR \fIcurves\fR, \fB\-named_curve\fR \fIcurve\fR, \fB\-cipher\fR \fIciphers\fR, \fB\-ciphersuites\fR \fI1.3ciphers\fR, \fB\-min_protocol\fR \fIminprot\fR, \fB\-max_protocol\fR \fImaxprot\fR, \fB\-record_padding\fR \fIpadding\fR, \fB\-debug_broken_protocol\fR, \fB\-no_middlebox\fR" 4
.IX Item "-bugs, -comp, -no_comp, -no_ticket, -serverpref, -client_renegotiation, -legacy_renegotiation, -no_renegotiation, -no_resumption_on_reneg, -legacy_server_connect, -no_legacy_server_connect, -no_etm -allow_no_dhe_kex, -prioritize_chacha, -strict, -sigalgs algs, -client_sigalgs algs, -groups groups, -curves curves, -named_curve curve, -cipher ciphers, -ciphersuites 1.3ciphers, -min_protocol minprot, -max_protocol maxprot, -record_padding padding, -debug_broken_protocol, -no_middlebox"
See "SUPPORTED COMMAND LINE COMMANDS" in \fBSSL_CONF_cmd\fR\|(3) for details.
.IP "\fB\-xkey\fR \fIinfile\fR, \fB\-xcert\fR \fIfile\fR, \fB\-xchain\fR \fIfile\fR, \fB\-xchain_build\fR \fIfile\fR, \fB\-xcertform\fR \fBDER\fR|\fBPEM\fR, \fB\-xkeyform\fR \fBDER\fR|\fBPEM\fR" 4
.IX Item "-xkey infile, -xcert file, -xchain file, -xchain_build file, -xcertform DER|PEM, -xkeyform DER|PEM"
Set extended certificate verification options.
See "Extended Verification Options" in \fBopenssl\-verification\-options\fR\|(1) for details.
.IP "\fB\-CAfile\fR \fIfile\fR, \fB\-no\-CAfile\fR, \fB\-CApath\fR \fIdir\fR, \fB\-no\-CApath\fR, \fB\-CAstore\fR \fIuri\fR, \fB\-no\-CAstore\fR" 4
.IX Item "-CAfile file, -no-CAfile, -CApath dir, -no-CApath, -CAstore uri, -no-CAstore"
See "Trusted Certificate Options" in \fBopenssl\-verification\-options\fR\|(1) for details.
.IP "\fB\-rand\fR \fIfiles\fR, \fB\-writerand\fR \fIfile\fR" 4
.IX Item "-rand files, -writerand file"
See "Random State Options" in \fBopenssl\fR\|(1) for details.
.IP "\fB\-engine\fR \fIid\fR" 4
.IX Item "-engine id"
See "Engine Options" in \fBopenssl\fR\|(1).
This option is deprecated.
.IP "\fB\-provider\fR \fIname\fR" 4
.IX Item "-provider name"
.PD 0
.IP "\fB\-provider\-path\fR \fIpath\fR" 4
.IX Item "-provider-path path"
.IP "\fB\-propquery\fR \fIpropq\fR" 4
.IX Item "-propquery propq"
.PD
See "Provider Options" in \fBopenssl\fR\|(1), \fBprovider\fR\|(7), and \fBproperty\fR\|(7).
.IP "\fB\-allow_proxy_certs\fR, \fB\-attime\fR, \fB\-no_check_time\fR, \fB\-check_ss_sig\fR, \fB\-crl_check\fR, \fB\-crl_check_all\fR, \fB\-explicit_policy\fR, \fB\-extended_crl\fR, \fB\-ignore_critical\fR, \fB\-inhibit_any\fR, \fB\-inhibit_map\fR, \fB\-no_alt_chains\fR, \fB\-partial_chain\fR, \fB\-policy\fR, \fB\-policy_check\fR, \fB\-policy_print\fR, \fB\-purpose\fR, \fB\-suiteB_128\fR, \fB\-suiteB_128_only\fR, \fB\-suiteB_192\fR, \fB\-trusted_first\fR, \fB\-use_deltas\fR, \fB\-auth_level\fR, \fB\-verify_depth\fR, \fB\-verify_email\fR, \fB\-verify_hostname\fR, \fB\-verify_ip\fR, \fB\-verify_name\fR, \fB\-x509_strict\fR \fB\-issuer_checks\fR" 4
.IX Item "-allow_proxy_certs, -attime, -no_check_time, -check_ss_sig, -crl_check, -crl_check_all, -explicit_policy, -extended_crl, -ignore_critical, -inhibit_any, -inhibit_map, -no_alt_chains, -partial_chain, -policy, -policy_check, -policy_print, -purpose, -suiteB_128, -suiteB_128_only, -suiteB_192, -trusted_first, -use_deltas, -auth_level, -verify_depth, -verify_email, -verify_hostname, -verify_ip, -verify_name, -x509_strict -issuer_checks"
Set various options of certificate chain verification.
See "Verification Options" in \fBopenssl\-verification\-options\fR\|(1) for details.
.Sp
If the server requests a client certificate, then
verification errors are displayed, for debugging, but the command will
proceed unless the \fB\-verify_return_error\fR option is used.
.SH "CONNECTED COMMANDS"
.IX Header "CONNECTED COMMANDS"
If a connection request is established with an SSL client and neither the
\&\fB\-www\fR nor the \fB\-WWW\fR option has been used then normally any data received
from the client is displayed and any key presses will be sent to the client.
.PP
Certain commands are also recognized which perform special operations. These
commands are a letter which must appear at the start of a line. They are listed
below.
.IP \fBq\fR 4
.IX Item "q"
End the current SSL connection but still accept new connections.
.IP \fBQ\fR 4
.IX Item "Q"
End the current SSL connection and exit.
.IP \fBr\fR 4
.IX Item "r"
Renegotiate the SSL session (TLSv1.2 and below only).
.IP \fBR\fR 4
.IX Item "R"
Renegotiate the SSL session and request a client certificate (TLSv1.2 and below
only).
.IP \fBP\fR 4
.IX Item "P"
Send some plain text down the underlying TCP connection: this should
cause the client to disconnect due to a protocol violation.
.IP \fBS\fR 4
.IX Item "S"
Print out some session cache status information.
.IP \fBk\fR 4
.IX Item "k"
Send a key update message to the client (TLSv1.3 only)
.IP \fBK\fR 4
.IX Item "K"
Send a key update message to the client and request one back (TLSv1.3 only)
.IP \fBc\fR 4
.IX Item "c"
Send a certificate request to the client (TLSv1.3 only)
.SH NOTES
.IX Header "NOTES"
This command can be used to debug SSL clients. To accept connections
from a web browser the command:
.PP
.Vb 1
\& openssl s_server \-accept 443 \-www
.Ve
.PP
can be used for example.
.PP
Although specifying an empty list of CAs when requesting a client certificate
is strictly speaking a protocol violation, some SSL clients interpret this to
mean any CA is acceptable. This is useful for debugging purposes.
.PP
The session parameters can printed out using the \fBopenssl\-sess_id\fR\|(1) command.
.SH BUGS
.IX Header "BUGS"
Because this program has a lot of options and also because some of the
techniques used are rather old, the C source for this command is rather
hard to read and not a model of how things should be done.
A typical SSL server program would be much simpler.
.PP
The output of common ciphers is wrong: it just gives the list of ciphers that
OpenSSL recognizes and the client supports.
.PP
There should be a way for this command to print out details
of any unknown cipher suites a client says it supports.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBopenssl\fR\|(1),
\&\fBopenssl\-sess_id\fR\|(1),
\&\fBopenssl\-s_client\fR\|(1),
\&\fBopenssl\-ciphers\fR\|(1),
\&\fBSSL_CONF_cmd\fR\|(3),
\&\fBSSL_CTX_set_max_send_fragment\fR\|(3),
\&\fBSSL_CTX_set_split_send_fragment\fR\|(3),
\&\fBSSL_CTX_set_max_pipelines\fR\|(3),
\&\fBossl_store\-file\fR\|(7)
.SH HISTORY
.IX Header "HISTORY"
The \-no_alt_chains option was added in OpenSSL 1.1.0.
.PP
The
\&\-allow\-no\-dhe\-kex and \-prioritize_chacha options were added in OpenSSL 1.1.1.
.PP
The \fB\-srpvfile\fR, \fB\-srpuserseed\fR, and \fB\-engine\fR
option were deprecated in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2022 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
