.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "OPENSSL-PKEY 1ossl"
.TH OPENSSL-PKEY 1ossl 2025-08-08 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
openssl\-pkey \- public or private key processing command
.SH SYNOPSIS
.IX Header "SYNOPSIS"
\&\fBopenssl\fR \fBpkey\fR
[\fB\-help\fR]
[\fB\-engine\fR \fIid\fR]
[\fB\-provider\fR \fIname\fR]
[\fB\-provider\-path\fR \fIpath\fR]
[\fB\-propquery\fR \fIpropq\fR]
[\fB\-check\fR]
[\fB\-pubcheck\fR]
[\fB\-in\fR \fIfilename\fR|\fIuri\fR]
[\fB\-inform\fR \fBDER\fR|\fBPEM\fR|\fBP12\fR|\fBENGINE\fR]
[\fB\-passin\fR \fIarg\fR]
[\fB\-pubin\fR]
[\fB\-out\fR \fIfilename\fR]
[\fB\-outform\fR \fBDER\fR|\fBPEM\fR]
[\fB\-\fR\f(BIcipher\fR]
[\fB\-passout\fR \fIarg\fR]
[\fB\-traditional\fR]
[\fB\-pubout\fR]
[\fB\-noout\fR]
[\fB\-text\fR]
[\fB\-text_pub\fR]
[\fB\-ec_conv_form\fR \fIarg\fR]
[\fB\-ec_param_enc\fR \fIarg\fR]
.SH DESCRIPTION
.IX Header "DESCRIPTION"
This command processes public or private keys. They can be
converted between various forms and their components printed.
.SH OPTIONS
.IX Header "OPTIONS"
.SS "General options"
.IX Subsection "General options"
.IP \fB\-help\fR 4
.IX Item "-help"
Print out a usage message.
.IP "\fB\-engine\fR \fIid\fR" 4
.IX Item "-engine id"
See "Engine Options" in \fBopenssl\fR\|(1).
This option is deprecated.
.IP "\fB\-provider\fR \fIname\fR" 4
.IX Item "-provider name"
.PD 0
.IP "\fB\-provider\-path\fR \fIpath\fR" 4
.IX Item "-provider-path path"
.IP "\fB\-propquery\fR \fIpropq\fR" 4
.IX Item "-propquery propq"
.PD
See "Provider Options" in \fBopenssl\fR\|(1), \fBprovider\fR\|(7), and \fBproperty\fR\|(7).
.IP \fB\-check\fR 4
.IX Item "-check"
This option checks the consistency of a key pair for both public and private
components.
.IP \fB\-pubcheck\fR 4
.IX Item "-pubcheck"
This option checks the correctness of either a public key
or the public component of a key pair.
.SS "Input options"
.IX Subsection "Input options"
.IP "\fB\-in\fR \fIfilename\fR|\fIuri\fR" 4
.IX Item "-in filename|uri"
This specifies the input to read a key from
or standard input if this option is not specified.
If the key input is encrypted and \fB\-passin\fR is not given
a pass phrase will be prompted for.
.IP "\fB\-inform\fR \fBDER\fR|\fBPEM\fR|\fBP12\fR|\fBENGINE\fR" 4
.IX Item "-inform DER|PEM|P12|ENGINE"
The key input format; unspecified by default.
See \fBopenssl\-format\-options\fR\|(1) for details.
.IP "\fB\-passin\fR \fIarg\fR" 4
.IX Item "-passin arg"
The password source for the key input.
.Sp
For more information about the format of \fBarg\fR
see \fBopenssl\-passphrase\-options\fR\|(1).
.IP \fB\-pubin\fR 4
.IX Item "-pubin"
By default a private key is read from the input.
With this option only the public components are read.
.SS "Output options"
.IX Subsection "Output options"
.IP "\fB\-out\fR \fIfilename\fR" 4
.IX Item "-out filename"
This specifies the output filename to save the encoded and/or text output of key
or standard output if this option is not specified.
If any cipher option is set but no \fB\-passout\fR is given
then a pass phrase will be prompted for.
The output filename should \fBnot\fR be the same as the input filename.
.IP "\fB\-outform\fR \fBDER\fR|\fBPEM\fR" 4
.IX Item "-outform DER|PEM"
The key output format; the default is \fBPEM\fR.
See \fBopenssl\-format\-options\fR\|(1) for details.
.IP \fB\-\fR\f(BIcipher\fR 4
.IX Item "-cipher"
Encrypt the PEM encoded private key with the supplied cipher. Any algorithm
name accepted by \fBEVP_get_cipherbyname()\fR is acceptable such as \fBaes128\fR.
Encryption is not supported for DER output.
.IP "\fB\-passout\fR \fIarg\fR" 4
.IX Item "-passout arg"
The password source for the output file.
.Sp
For more information about the format of \fBarg\fR
see \fBopenssl\-passphrase\-options\fR\|(1).
.IP \fB\-traditional\fR 4
.IX Item "-traditional"
Normally a private key is written using standard format: this is PKCS#8 form
with the appropriate encryption algorithm (if any). If the \fB\-traditional\fR
option is specified then the older "traditional" format is used instead.
.IP \fB\-pubout\fR 4
.IX Item "-pubout"
By default the private and public key is output;
this option restricts the output to the public components.
This option is automatically set if the input is a public key.
.Sp
When combined with \fB\-text\fR, this is equivalent to \fB\-text_pub\fR.
.IP \fB\-noout\fR 4
.IX Item "-noout"
Do not output the key in encoded form.
.IP \fB\-text\fR 4
.IX Item "-text"
Output the various key components in plain text
(possibly in addition to the PEM encoded form).
This cannot be combined with encoded output in DER format.
.IP \fB\-text_pub\fR 4
.IX Item "-text_pub"
Output in text form only the public key components (also for private keys).
This cannot be combined with encoded output in DER format.
.IP "\fB\-ec_conv_form\fR \fIarg\fR" 4
.IX Item "-ec_conv_form arg"
This option only applies to elliptic-curve based keys.
.Sp
This specifies how the points on the elliptic curve are converted
into octet strings. Possible values are: \fBcompressed\fR (the default
value), \fBuncompressed\fR and \fBhybrid\fR. For more information regarding
the point conversion forms please read the X9.62 standard.
\&\fBNote\fR Due to patent issues the \fBcompressed\fR option is disabled
by default for binary curves and can be enabled by defining
the preprocessor macro \fBOPENSSL_EC_BIN_PT_COMP\fR at compile time.
.IP "\fB\-ec_param_enc\fR \fIarg\fR" 4
.IX Item "-ec_param_enc arg"
This option only applies to elliptic curve based public and private keys.
.Sp
This specifies how the elliptic curve parameters are encoded.
Possible value are: \fBnamed_curve\fR, i.e. the ec parameters are
specified by an OID, or \fBexplicit\fR where the ec parameters are
explicitly given (see RFC 3279 for the definition of the
EC parameters structures). The default value is \fBnamed_curve\fR.
\&\fBNote\fR the \fBimplicitlyCA\fR alternative, as specified in RFC 3279,
is currently not implemented in OpenSSL.
.SH EXAMPLES
.IX Header "EXAMPLES"
To remove the pass phrase on a private key:
.PP
.Vb 1
\& openssl pkey \-in key.pem \-out keyout.pem
.Ve
.PP
To encrypt a private key using triple DES:
.PP
.Vb 1
\& openssl pkey \-in key.pem \-des3 \-out keyout.pem
.Ve
.PP
To convert a private key from PEM to DER format:
.PP
.Vb 1
\& openssl pkey \-in key.pem \-outform DER \-out keyout.der
.Ve
.PP
To print out the components of a private key to standard output:
.PP
.Vb 1
\& openssl pkey \-in key.pem \-text \-noout
.Ve
.PP
To print out the public components of a private key to standard output:
.PP
.Vb 1
\& openssl pkey \-in key.pem \-text_pub \-noout
.Ve
.PP
To just output the public part of a private key:
.PP
.Vb 1
\& openssl pkey \-in key.pem \-pubout \-out pubkey.pem
.Ve
.PP
To change the EC parameters encoding to \fBexplicit\fR:
.PP
.Vb 1
\& openssl pkey \-in key.pem \-ec_param_enc explicit \-out keyout.pem
.Ve
.PP
To change the EC point conversion form to \fBcompressed\fR:
.PP
.Vb 1
\& openssl pkey \-in key.pem \-ec_conv_form compressed \-out keyout.pem
.Ve
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBopenssl\fR\|(1),
\&\fBopenssl\-genpkey\fR\|(1),
\&\fBopenssl\-rsa\fR\|(1),
\&\fBopenssl\-pkcs8\fR\|(1),
\&\fBopenssl\-dsa\fR\|(1),
\&\fBopenssl\-genrsa\fR\|(1),
\&\fBopenssl\-gendsa\fR\|(1)
.SH HISTORY
.IX Header "HISTORY"
The \fB\-engine\fR option was deprecated in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2006\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
