.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "OPENSSL-PKCS12 1ossl"
.TH OPENSSL-PKCS12 1ossl 2025-08-08 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
openssl\-pkcs12 \- PKCS#12 file command
.SH SYNOPSIS
.IX Header "SYNOPSIS"
\&\fBopenssl\fR \fBpkcs12\fR
[\fB\-help\fR]
[\fB\-passin\fR \fIarg\fR]
[\fB\-passout\fR \fIarg\fR]
[\fB\-password\fR \fIarg\fR]
[\fB\-twopass\fR]
[\fB\-in\fR \fIfilename\fR|\fIuri\fR]
[\fB\-out\fR \fIfilename\fR]
[\fB\-nokeys\fR]
[\fB\-nocerts\fR]
[\fB\-noout\fR]
[\fB\-legacy\fR]
[\fB\-engine\fR \fIid\fR]
[\fB\-provider\fR \fIname\fR]
[\fB\-provider\-path\fR \fIpath\fR]
[\fB\-propquery\fR \fIpropq\fR]
[\fB\-rand\fR \fIfiles\fR]
[\fB\-writerand\fR \fIfile\fR]
.PP
PKCS#12 input (parsing) options:
[\fB\-info\fR]
[\fB\-nomacver\fR]
[\fB\-clcerts\fR]
[\fB\-cacerts\fR]
.PP
[\fB\-aes128\fR]
[\fB\-aes192\fR]
[\fB\-aes256\fR]
[\fB\-aria128\fR]
[\fB\-aria192\fR]
[\fB\-aria256\fR]
[\fB\-camellia128\fR]
[\fB\-camellia192\fR]
[\fB\-camellia256\fR]
[\fB\-des\fR]
[\fB\-des3\fR]
[\fB\-idea\fR]
[\fB\-noenc\fR]
[\fB\-nodes\fR]
.PP
PKCS#12 output (export) options:
.PP
[\fB\-export\fR]
[\fB\-inkey\fR \fIfilename\fR|\fIuri\fR]
[\fB\-certfile\fR \fIfilename\fR]
[\fB\-passcerts\fR \fIarg\fR]
[\fB\-chain\fR]
[\fB\-untrusted\fR \fIfilename\fR]
[\fB\-CAfile\fR \fIfile\fR]
[\fB\-no\-CAfile\fR]
[\fB\-CApath\fR \fIdir\fR]
[\fB\-no\-CApath\fR]
[\fB\-CAstore\fR \fIuri\fR]
[\fB\-no\-CAstore\fR]
[\fB\-name\fR \fIname\fR]
[\fB\-caname\fR \fIname\fR]
[\fB\-CSP\fR \fIname\fR]
[\fB\-LMK\fR]
[\fB\-keyex\fR]
[\fB\-keysig\fR]
[\fB\-keypbe\fR \fIcipher\fR]
[\fB\-certpbe\fR \fIcipher\fR]
[\fB\-descert\fR]
[\fB\-macalg\fR \fIdigest\fR]
[\fB\-iter\fR \fIcount\fR]
[\fB\-noiter\fR]
[\fB\-nomaciter\fR]
[\fB\-maciter\fR]
[\fB\-nomac\fR]
.SH DESCRIPTION
.IX Header "DESCRIPTION"
This command allows PKCS#12 files (sometimes referred to as
PFX files) to be created and parsed. PKCS#12 files are used by several
programs including Netscape, MSIE and MS Outlook.
.SH OPTIONS
.IX Header "OPTIONS"
There are a lot of options the meaning of some depends of whether a PKCS#12 file
is being created or parsed. By default a PKCS#12 file is parsed.
A PKCS#12 file can be created by using the \fB\-export\fR option (see below).
The PKCS#12 export encryption and MAC options such as \fB\-certpbe\fR and \fB\-iter\fR
and many further options such as \fB\-chain\fR are relevant only with \fB\-export\fR.
Conversely, the options regarding encryption of private keys when outputting
PKCS#12 input are relevant only when the \fB\-export\fR option is not given.
.PP
The default encryption algorithm is AES\-256\-CBC with PBKDF2 for key derivation.
.PP
When encountering problems loading legacy PKCS#12 files that involve,
for example, RC2\-40\-CBC,
try using the \fB\-legacy\fR option and, if needed, the \fB\-provider\-path\fR option.
.IP \fB\-help\fR 4
.IX Item "-help"
Print out a usage message.
.IP "\fB\-passin\fR \fIarg\fR" 4
.IX Item "-passin arg"
The password source for the input, and for encrypting any private keys that
are output.
For more information about the format of \fBarg\fR
see \fBopenssl\-passphrase\-options\fR\|(1).
.IP "\fB\-passout\fR \fIarg\fR" 4
.IX Item "-passout arg"
The password source for output files.
.IP "\fB\-password\fR \fIarg\fR" 4
.IX Item "-password arg"
With \fB\-export\fR, \fB\-password\fR is equivalent to \fB\-passout\fR,
otherwise it is equivalent to \fB\-passin\fR.
.IP \fB\-twopass\fR 4
.IX Item "-twopass"
Prompt for separate integrity and encryption passwords: most software
always assumes these are the same so this option will render such
PKCS#12 files unreadable. Cannot be used in combination with the options
\&\fB\-password\fR, \fB\-passin\fR if importing from PKCS#12, or \fB\-passout\fR if exporting.
.IP \fB\-nokeys\fR 4
.IX Item "-nokeys"
No private keys will be output.
.IP \fB\-nocerts\fR 4
.IX Item "-nocerts"
No certificates will be output.
.IP \fB\-noout\fR 4
.IX Item "-noout"
This option inhibits all credentials output,
and so the input is just verified.
.IP \fB\-legacy\fR 4
.IX Item "-legacy"
Use legacy mode of operation and automatically load the legacy provider.
If OpenSSL is not installed system-wide,
it is necessary to also use, for example, \f(CW\*(C`\-provider\-path ./providers\*(C'\fR
or to set the environment variable \fBOPENSSL_MODULES\fR
to point to the directory where the providers can be found.
.Sp
In the legacy mode, the default algorithm for certificate encryption
is RC2_CBC or 3DES_CBC depending on whether the RC2 cipher is enabled
in the build. The default algorithm for private key encryption is 3DES_CBC.
If the legacy option is not specified, then the legacy provider is not loaded
and the default encryption algorithm for both certificates and private keys is
AES_256_CBC with PBKDF2 for key derivation.
.IP "\fB\-engine\fR \fIid\fR" 4
.IX Item "-engine id"
See "Engine Options" in \fBopenssl\fR\|(1).
This option is deprecated.
.IP "\fB\-provider\fR \fIname\fR" 4
.IX Item "-provider name"
.PD 0
.IP "\fB\-provider\-path\fR \fIpath\fR" 4
.IX Item "-provider-path path"
.IP "\fB\-propquery\fR \fIpropq\fR" 4
.IX Item "-propquery propq"
.PD
See "Provider Options" in \fBopenssl\fR\|(1), \fBprovider\fR\|(7), and \fBproperty\fR\|(7).
.IP "\fB\-rand\fR \fIfiles\fR, \fB\-writerand\fR \fIfile\fR" 4
.IX Item "-rand files, -writerand file"
See "Random State Options" in \fBopenssl\fR\|(1) for details.
.SS "PKCS#12 input (parsing) options"
.IX Subsection "PKCS#12 input (parsing) options"
.IP "\fB\-in\fR \fIfilename\fR|\fIuri\fR" 4
.IX Item "-in filename|uri"
This specifies the input filename or URI.
Standard input is used by default.
Without the \fB\-export\fR option this must be PKCS#12 file to be parsed.
For use with the \fB\-export\fR option
see the "PKCS#12 output (export) options" section.
.IP "\fB\-out\fR \fIfilename\fR" 4
.IX Item "-out filename"
The filename to write certificates and private keys to, standard output by
default.  They are all written in PEM format.
.IP \fB\-info\fR 4
.IX Item "-info"
Output additional information about the PKCS#12 file structure, algorithms
used and iteration counts.
.IP \fB\-nomacver\fR 4
.IX Item "-nomacver"
Don't attempt to verify the integrity MAC.
.IP \fB\-clcerts\fR 4
.IX Item "-clcerts"
Only output client certificates (not CA certificates).
.IP \fB\-cacerts\fR 4
.IX Item "-cacerts"
Only output CA certificates (not client certificates).
.IP "\fB\-aes128\fR, \fB\-aes192\fR, \fB\-aes256\fR" 4
.IX Item "-aes128, -aes192, -aes256"
Use AES to encrypt private keys before outputting.
.IP "\fB\-aria128\fR, \fB\-aria192\fR, \fB\-aria256\fR" 4
.IX Item "-aria128, -aria192, -aria256"
Use ARIA to encrypt private keys before outputting.
.IP "\fB\-camellia128\fR, \fB\-camellia192\fR, \fB\-camellia256\fR" 4
.IX Item "-camellia128, -camellia192, -camellia256"
Use Camellia to encrypt private keys before outputting.
.IP \fB\-des\fR 4
.IX Item "-des"
Use DES to encrypt private keys before outputting.
.IP \fB\-des3\fR 4
.IX Item "-des3"
Use triple DES to encrypt private keys before outputting.
.IP \fB\-idea\fR 4
.IX Item "-idea"
Use IDEA to encrypt private keys before outputting.
.IP \fB\-noenc\fR 4
.IX Item "-noenc"
Don't encrypt private keys at all.
.IP \fB\-nodes\fR 4
.IX Item "-nodes"
This option is deprecated since OpenSSL 3.0; use \fB\-noenc\fR instead.
.SS "PKCS#12 output (export) options"
.IX Subsection "PKCS#12 output (export) options"
.IP \fB\-export\fR 4
.IX Item "-export"
This option specifies that a PKCS#12 file will be created rather than
parsed.
.IP "\fB\-out\fR \fIfilename\fR" 4
.IX Item "-out filename"
This specifies filename to write the PKCS#12 file to. Standard output is used
by default.
.IP "\fB\-in\fR \fIfilename\fR|\fIuri\fR" 4
.IX Item "-in filename|uri"
This specifies the input filename or URI.
Standard input is used by default.
With the \fB\-export\fR option this is a file with certificates and a key,
or a URI that refers to a key accessed via an engine.
The order of credentials in a file doesn't matter but one private key and
its corresponding certificate should be present. If additional
certificates are present they will also be included in the PKCS#12 output file.
.IP "\fB\-inkey\fR \fIfilename\fR|\fIuri\fR" 4
.IX Item "-inkey filename|uri"
The private key input for PKCS12 output.
If this option is not specified then the input file (\fB\-in\fR argument) must
contain a private key.
If no engine is used, the argument is taken as a file.
If the \fB\-engine\fR option is used or the URI has prefix \f(CW\*(C`org.openssl.engine:\*(C'\fR
then the rest of the URI is taken as key identifier for the given engine.
.IP "\fB\-certfile\fR \fIfilename\fR" 4
.IX Item "-certfile filename"
An input file with extra certificates to be added to the PKCS#12 output
if the \fB\-export\fR option is given.
.IP "\fB\-passcerts\fR \fIarg\fR" 4
.IX Item "-passcerts arg"
The password source for certificate input such as \fB\-certfile\fR
and \fB\-untrusted\fR.
For more information about the format of \fBarg\fR see
\&\fBopenssl\-passphrase\-options\fR\|(1).
.IP \fB\-chain\fR 4
.IX Item "-chain"
If this option is present then the certificate chain of the end entity
certificate is built and included in the PKCS#12 output file.
The end entity certificate is the first one read from the \fB\-in\fR file
if no key is given, else the first certificate matching the given key.
The standard CA trust store is used for chain building,
as well as any untrusted CA certificates given with the \fB\-untrusted\fR option.
.IP "\fB\-untrusted\fR \fIfilename\fR" 4
.IX Item "-untrusted filename"
An input file of untrusted certificates that may be used
for chain building, which is relevant only when a PKCS#12 file is created
with the \fB\-export\fR option and the \fB\-chain\fR option is given as well.
Any certificates that are actually part of the chain are added to the output.
.IP "\fB\-CAfile\fR \fIfile\fR, \fB\-no\-CAfile\fR, \fB\-CApath\fR \fIdir\fR, \fB\-no\-CApath\fR, \fB\-CAstore\fR \fIuri\fR, \fB\-no\-CAstore\fR" 4
.IX Item "-CAfile file, -no-CAfile, -CApath dir, -no-CApath, -CAstore uri, -no-CAstore"
See "Trusted Certificate Options" in \fBopenssl\-verification\-options\fR\|(1) for details.
.IP "\fB\-name\fR \fIfriendlyname\fR" 4
.IX Item "-name friendlyname"
This specifies the "friendly name" for the certificates and private key. This
name is typically displayed in list boxes by software importing the file.
.IP "\fB\-caname\fR \fIfriendlyname\fR" 4
.IX Item "-caname friendlyname"
This specifies the "friendly name" for other certificates. This option may be
used multiple times to specify names for all certificates in the order they
appear. Netscape ignores friendly names on other certificates whereas MSIE
displays them.
.IP "\fB\-CSP\fR \fIname\fR" 4
.IX Item "-CSP name"
Write \fIname\fR as a Microsoft CSP name.
The password source for the input, and for encrypting any private keys that
are output.
For more information about the format of \fBarg\fR
see \fBopenssl\-passphrase\-options\fR\|(1).
.IP \fB\-LMK\fR 4
.IX Item "-LMK"
Add the "Local Key Set" identifier to the attributes.
.IP \fB\-keyex\fR|\fB\-keysig\fR 4
.IX Item "-keyex|-keysig"
Specifies that the private key is to be used for key exchange or just signing.
This option is only interpreted by MSIE and similar MS software. Normally
"export grade" software will only allow 512 bit RSA keys to be used for
encryption purposes but arbitrary length keys for signing. The \fB\-keysig\fR
option marks the key for signing only. Signing only keys can be used for
S/MIME signing, authenticode (ActiveX control signing)  and SSL client
authentication, however, due to a bug only MSIE 5.0 and later support
the use of signing only keys for SSL client authentication.
.IP "\fB\-keypbe\fR \fIalg\fR, \fB\-certpbe\fR \fIalg\fR" 4
.IX Item "-keypbe alg, -certpbe alg"
These options allow the algorithm used to encrypt the private key and
certificates to be selected. Any PKCS#5 v1.5 or PKCS#12 PBE algorithm name
can be used (see "NOTES" section for more information). If a cipher name
(as output by \f(CW\*(C`openssl list \-cipher\-algorithms\*(C'\fR) is specified then it
is used with PKCS#5 v2.0. For interoperability reasons it is advisable to only
use PKCS#12 algorithms.
.Sp
Special value \f(CW\*(C`NONE\*(C'\fR disables encryption of the private key and certificates.
.IP \fB\-descert\fR 4
.IX Item "-descert"
Encrypt the certificates using triple DES. By default the private
key and the certificates are encrypted using AES\-256\-CBC unless
the '\-legacy' option is used. If '\-descert' is used with the '\-legacy'
then both, the private key and the certificates are encrypted using triple DES.
.IP "\fB\-macalg\fR \fIdigest\fR" 4
.IX Item "-macalg digest"
Specify the MAC digest algorithm. If not included SHA256 will be used.
.IP "\fB\-iter\fR \fIcount\fR" 4
.IX Item "-iter count"
This option specifies the iteration count for the encryption key and MAC. The
default value is 2048.
.Sp
To discourage attacks by using large dictionaries of common passwords the
algorithm that derives keys from passwords can have an iteration count applied
to it: this causes a certain part of the algorithm to be repeated and slows it
down. The MAC is used to check the file integrity but since it will normally
have the same password as the keys and certificates it could also be attacked.
.IP "\fB\-noiter\fR, \fB\-nomaciter\fR" 4
.IX Item "-noiter, -nomaciter"
By default both encryption and MAC iteration counts are set to 2048, using
these options the MAC and encryption iteration counts can be set to 1, since
this reduces the file security you should not use these options unless you
really have to. Most software supports both MAC and encryption iteration counts.
MSIE 4.0 doesn't support MAC iteration counts so it needs the \fB\-nomaciter\fR
option.
.IP \fB\-maciter\fR 4
.IX Item "-maciter"
This option is included for compatibility with previous versions, it used
to be needed to use MAC iterations counts but they are now used by default.
.IP \fB\-nomac\fR 4
.IX Item "-nomac"
Do not attempt to provide the MAC integrity. This can be useful with the FIPS
provider as the PKCS12 MAC requires PKCS12KDF which is not an approved FIPS
algorithm and cannot be supported by the FIPS provider.
.SH NOTES
.IX Header "NOTES"
Although there are a large number of options most of them are very rarely
used. For PKCS#12 file parsing only \fB\-in\fR and \fB\-out\fR need to be used
for PKCS#12 file creation \fB\-export\fR and \fB\-name\fR are also used.
.PP
If none of the \fB\-clcerts\fR, \fB\-cacerts\fR or \fB\-nocerts\fR options are present
then all certificates will be output in the order they appear in the input
PKCS#12 files. There is no guarantee that the first certificate present is
the one corresponding to the private key.
Certain software which tries to get a private key and the corresponding
certificate might assume that the first certificate in the file is the one
corresponding to the private key, but that may not always be the case.
Using the \fB\-clcerts\fR option will solve this problem by only
outputting the certificate corresponding to the private key. If the CA
certificates are required then they can be output to a separate file using
the \fB\-nokeys\fR \fB\-cacerts\fR options to just output CA certificates.
.PP
The \fB\-keypbe\fR and \fB\-certpbe\fR algorithms allow the precise encryption
algorithms for private keys and certificates to be specified. Normally
the defaults are fine but occasionally software can't handle triple DES
encrypted private keys, then the option \fB\-keypbe\fR \fIPBE\-SHA1\-RC2\-40\fR can
be used to reduce the private key encryption to 40 bit RC2. A complete
description of all algorithms is contained in \fBopenssl\-pkcs8\fR\|(1).
.PP
Prior 1.1 release passwords containing non-ASCII characters were encoded
in non-compliant manner, which limited interoperability, in first hand
with Windows. But switching to standard-compliant password encoding
poses problem accessing old data protected with broken encoding. For
this reason even legacy encodings is attempted when reading the
data. If you use PKCS#12 files in production application you are advised
to convert the data, because implemented heuristic approach is not
MT-safe, its sole goal is to facilitate the data upgrade with this
command.
.SH EXAMPLES
.IX Header "EXAMPLES"
Parse a PKCS#12 file and output it to a PEM file:
.PP
.Vb 1
\& openssl pkcs12 \-in file.p12 \-out file.pem
.Ve
.PP
Output only client certificates to a file:
.PP
.Vb 1
\& openssl pkcs12 \-in file.p12 \-clcerts \-out file.pem
.Ve
.PP
Don't encrypt the private key:
.PP
.Vb 1
\& openssl pkcs12 \-in file.p12 \-out file.pem \-noenc
.Ve
.PP
Print some info about a PKCS#12 file:
.PP
.Vb 1
\& openssl pkcs12 \-in file.p12 \-info \-noout
.Ve
.PP
Print some info about a PKCS#12 file in legacy mode:
.PP
.Vb 1
\& openssl pkcs12 \-in file.p12 \-info \-noout \-legacy
.Ve
.PP
Create a PKCS#12 file from a PEM file that may contain a key and certificates:
.PP
.Vb 1
\& openssl pkcs12 \-export \-in file.pem \-out file.p12 \-name "My PSE"
.Ve
.PP
Include some extra certificates:
.PP
.Vb 2
\& openssl pkcs12 \-export \-in file.pem \-out file.p12 \-name "My PSE" \e
\&  \-certfile othercerts.pem
.Ve
.PP
Export a PKCS#12 file with data from a certificate PEM file and from a further
PEM file containing a key, with default algorithms as in the legacy provider:
.PP
.Vb 1
\& openssl pkcs12 \-export \-in cert.pem \-inkey key.pem \-out file.p12 \-legacy
.Ve
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBopenssl\fR\|(1),
\&\fBopenssl\-pkcs8\fR\|(1),
\&\fBossl_store\-file\fR\|(7)
.SH HISTORY
.IX Header "HISTORY"
The \fB\-engine\fR option was deprecated in OpenSSL 3.0.
The \fB\-nodes\fR option was deprecated in OpenSSL 3.0, too; use \fB\-noenc\fR instead.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2022 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
