.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "OPENSSL-REQ 1ossl"
.TH OPENSSL-REQ 1ossl 2025-08-08 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
openssl\-req \- PKCS#10 certificate request and certificate generating command
.SH SYNOPSIS
.IX Header "SYNOPSIS"
\&\fBopenssl\fR \fBreq\fR
[\fB\-help\fR]
[\fB\-inform\fR \fBDER\fR|\fBPEM\fR]
[\fB\-outform\fR \fBDER\fR|\fBPEM\fR]
[\fB\-in\fR \fIfilename\fR]
[\fB\-passin\fR \fIarg\fR]
[\fB\-out\fR \fIfilename\fR]
[\fB\-passout\fR \fIarg\fR]
[\fB\-text\fR]
[\fB\-pubkey\fR]
[\fB\-noout\fR]
[\fB\-verify\fR]
[\fB\-modulus\fR]
[\fB\-new\fR]
[\fB\-newkey\fR \fIarg\fR]
[\fB\-pkeyopt\fR \fIopt\fR:\fIvalue\fR]
[\fB\-noenc\fR]
[\fB\-nodes\fR]
[\fB\-key\fR \fIfilename\fR|\fIuri\fR]
[\fB\-keyform\fR \fBDER\fR|\fBPEM\fR|\fBP12\fR|\fBENGINE\fR]
[\fB\-keyout\fR \fIfilename\fR]
[\fB\-keygen_engine\fR \fIid\fR]
[\fB\-\fR\f(BIdigest\fR]
[\fB\-config\fR \fIfilename\fR]
[\fB\-section\fR \fIname\fR]
[\fB\-x509\fR]
[\fB\-CA\fR \fIfilename\fR|\fIuri\fR]
[\fB\-CAkey\fR \fIfilename\fR|\fIuri\fR]
[\fB\-days\fR \fIn\fR]
[\fB\-set_serial\fR \fIn\fR]
[\fB\-newhdr\fR]
[\fB\-copy_extensions\fR \fIarg\fR]
[\fB\-addext\fR \fIext\fR]
[\fB\-extensions\fR \fIsection\fR]
[\fB\-reqexts\fR \fIsection\fR]
[\fB\-precert\fR]
[\fB\-utf8\fR]
[\fB\-reqopt\fR]
[\fB\-subject\fR]
[\fB\-subj\fR \fIarg\fR]
[\fB\-multivalue\-rdn\fR]
[\fB\-sigopt\fR \fInm\fR:\fIv\fR]
[\fB\-vfyopt\fR \fInm\fR:\fIv\fR]
[\fB\-batch\fR]
[\fB\-verbose\fR]
[\fB\-nameopt\fR \fIoption\fR]
[\fB\-rand\fR \fIfiles\fR]
[\fB\-writerand\fR \fIfile\fR]
[\fB\-engine\fR \fIid\fR]
[\fB\-provider\fR \fIname\fR]
[\fB\-provider\-path\fR \fIpath\fR]
[\fB\-propquery\fR \fIpropq\fR]
.SH DESCRIPTION
.IX Header "DESCRIPTION"
This command primarily creates and processes certificate requests (CSRs)
in PKCS#10 format. It can additionally create self-signed certificates
for use as root CAs for example.
.SH OPTIONS
.IX Header "OPTIONS"
.IP \fB\-help\fR 4
.IX Item "-help"
Print out a usage message.
.IP "\fB\-inform\fR \fBDER\fR|\fBPEM\fR, \fB\-outform\fR \fBDER\fR|\fBPEM\fR" 4
.IX Item "-inform DER|PEM, -outform DER|PEM"
The input and output formats; unspecified by default.
See \fBopenssl\-format\-options\fR\|(1) for details.
.Sp
The data is a PKCS#10 object.
.IP "\fB\-in\fR \fIfilename\fR" 4
.IX Item "-in filename"
This specifies the input filename to read a request from.
This defaults to standard input unless \fB\-x509\fR or \fB\-CA\fR is specified.
A request is only read if the creation options
(\fB\-new\fR or \fB\-newkey\fR or \fB\-precert\fR) are not specified.
.IP "\fB\-sigopt\fR \fInm\fR:\fIv\fR" 4
.IX Item "-sigopt nm:v"
Pass options to the signature algorithm during sign operations.
Names and values of these options are algorithm-specific.
.IP "\fB\-vfyopt\fR \fInm\fR:\fIv\fR" 4
.IX Item "-vfyopt nm:v"
Pass options to the signature algorithm during verify operations.
Names and values of these options are algorithm-specific.
.IP "\fB\-passin\fR \fIarg\fR" 4
.IX Item "-passin arg"
The password source for private key and certificate input.
For more information about the format of \fBarg\fR
see \fBopenssl\-passphrase\-options\fR\|(1).
.IP "\fB\-passout\fR \fIarg\fR" 4
.IX Item "-passout arg"
The password source for the output file.
For more information about the format of \fBarg\fR
see \fBopenssl\-passphrase\-options\fR\|(1).
.IP "\fB\-out\fR \fIfilename\fR" 4
.IX Item "-out filename"
This specifies the output filename to write to or standard output by default.
.IP \fB\-text\fR 4
.IX Item "-text"
Prints out the certificate request in text form.
.IP \fB\-subject\fR 4
.IX Item "-subject"
Prints out the certificate request subject
(or certificate subject if \fB\-x509\fR is in use).
.IP \fB\-pubkey\fR 4
.IX Item "-pubkey"
Prints out the public key.
.IP \fB\-noout\fR 4
.IX Item "-noout"
This option prevents output of the encoded version of the certificate request.
.IP \fB\-modulus\fR 4
.IX Item "-modulus"
Prints out the value of the modulus of the public key contained in the request.
.IP \fB\-verify\fR 4
.IX Item "-verify"
Verifies the self-signature on the request.
.IP \fB\-new\fR 4
.IX Item "-new"
This option generates a new certificate request. It will prompt
the user for the relevant field values. The actual fields
prompted for and their maximum and minimum sizes are specified
in the configuration file and any requested extensions.
.Sp
If the \fB\-key\fR option is not given it will generate a new private key
using information specified in the configuration file or given with
the \fB\-newkey\fR and \fB\-pkeyopt\fR options,
else by default an RSA key with 2048 bits length.
.IP "\fB\-newkey\fR \fIarg\fR" 4
.IX Item "-newkey arg"
This option is used to generate a new private key unless \fB\-key\fR is given.
It is subsequently used as if it was given using the \fB\-key\fR option.
.Sp
This option implies the \fB\-new\fR flag to create a new certificate request
or a new certificate in case \fB\-x509\fR is given.
.Sp
The argument takes one of several forms.
.Sp
[\fBrsa:\fR]\fInbits\fR generates an RSA key \fInbits\fR in size.
If \fInbits\fR is omitted, i.e., \fB\-newkey\fR \fBrsa\fR is specified,
the default key size specified in the configuration file
with the \fBdefault_bits\fR option is used if present, else 2048.
.Sp
All other algorithms support the \fB\-newkey\fR \fIalgname\fR:\fIfile\fR form, where
\&\fIfile\fR is an algorithm parameter file, created with \f(CW\*(C`openssl genpkey \-genparam\*(C'\fR
or an X.509 certificate for a key with appropriate algorithm.
.Sp
\&\fBparam:\fR\fIfile\fR generates a key using the parameter file or certificate
\&\fIfile\fR, the algorithm is determined by the parameters.
.Sp
\&\fIalgname\fR[:\fIfile\fR] generates a key using the given algorithm \fIalgname\fR.
If a parameter file \fIfile\fR is given then the parameters specified there
are used, where the algorithm parameters must match \fIalgname\fR.
If algorithm parameters are not given,
any necessary parameters should be specified via the \fB\-pkeyopt\fR option.
.Sp
\&\fBdsa:\fR\fIfilename\fR generates a DSA key using the parameters
in the file \fIfilename\fR. \fBec:\fR\fIfilename\fR generates EC key (usable both with
ECDSA or ECDH algorithms), \fBgost2001:\fR\fIfilename\fR generates GOST R
34.10\-2001 key (requires \fBgost\fR engine configured in the configuration
file). If just \fBgost2001\fR is specified a parameter set should be
specified by \fB\-pkeyopt\fR \fIparamset:X\fR
.IP "\fB\-pkeyopt\fR \fIopt\fR:\fIvalue\fR" 4
.IX Item "-pkeyopt opt:value"
Set the public key algorithm option \fIopt\fR to \fIvalue\fR. The precise set of
options supported depends on the public key algorithm used and its
implementation.
See "KEY GENERATION OPTIONS" in \fBopenssl\-genpkey\fR\|(1) for more details.
.IP "\fB\-key\fR \fIfilename\fR|\fIuri\fR" 4
.IX Item "-key filename|uri"
This option provides the private key for signing a new certificate or
certificate request.
Unless \fB\-in\fR is given, the corresponding public key is placed in
the new certificate or certificate request, resulting in a self-signature.
.Sp
For certificate signing this option is overridden by the \fB\-CA\fR option.
.Sp
This option also accepts PKCS#8 format private keys for PEM format files.
.IP "\fB\-keyform\fR \fBDER\fR|\fBPEM\fR|\fBP12\fR|\fBENGINE\fR" 4
.IX Item "-keyform DER|PEM|P12|ENGINE"
The format of the private key; unspecified by default.
See \fBopenssl\-format\-options\fR\|(1) for details.
.IP "\fB\-keyout\fR \fIfilename\fR" 4
.IX Item "-keyout filename"
This gives the filename to write any private key to that has been newly created
or read from \fB\-key\fR.  If neither the \fB\-keyout\fR option nor the \fB\-key\fR option
are given then the filename specified in the configuration file with the
\&\fBdefault_keyfile\fR option is used, if present.  Thus, if you want to write the
private key and the \fB\-key\fR option is provided, you should provide the
\&\fB\-keyout\fR option explicitly.  If a new key is generated and no filename is
specified the key is written to standard output.
.IP \fB\-noenc\fR 4
.IX Item "-noenc"
If this option is specified then if a private key is created it
will not be encrypted.
.IP \fB\-nodes\fR 4
.IX Item "-nodes"
This option is deprecated since OpenSSL 3.0; use \fB\-noenc\fR instead.
.IP \fB\-\fR\f(BIdigest\fR 4
.IX Item "-digest"
This specifies the message digest to sign the request.
Any digest supported by the OpenSSL \fBdgst\fR command can be used.
This overrides the digest algorithm specified in
the configuration file.
.Sp
Some public key algorithms may override this choice. For instance, DSA
signatures always use SHA1, GOST R 34.10 signatures always use
GOST R 34.11\-94 (\fB\-md_gost94\fR), Ed25519 and Ed448 never use any digest.
.IP "\fB\-config\fR \fIfilename\fR" 4
.IX Item "-config filename"
This allows an alternative configuration file to be specified.
Optional; for a description of the default value,
see "COMMAND SUMMARY" in \fBopenssl\fR\|(1).
.IP "\fB\-section\fR \fIname\fR" 4
.IX Item "-section name"
Specifies the name of the section to use; the default is \fBreq\fR.
.IP "\fB\-subj\fR \fIarg\fR" 4
.IX Item "-subj arg"
Sets subject name for new request or supersedes the subject name
when processing a certificate request.
.Sp
The arg must be formatted as \f(CW\*(C`/type0=value0/type1=value1/type2=...\*(C'\fR.
Special characters may be escaped by \f(CW\*(C`\e\*(C'\fR (backslash), whitespace is retained.
Empty values are permitted, but the corresponding type will not be included
in the request.
Giving a single \f(CW\*(C`/\*(C'\fR will lead to an empty sequence of RDNs (a NULL-DN).
Multi-valued RDNs can be formed by placing a \f(CW\*(C`+\*(C'\fR character instead of a \f(CW\*(C`/\*(C'\fR
between the AttributeValueAssertions (AVAs) that specify the members of the set.
Example:
.Sp
\&\f(CW\*(C`/DC=org/DC=OpenSSL/DC=users/UID=123456+CN=John Doe\*(C'\fR
.IP \fB\-multivalue\-rdn\fR 4
.IX Item "-multivalue-rdn"
This option has been deprecated and has no effect.
.IP \fB\-x509\fR 4
.IX Item "-x509"
This option outputs a certificate instead of a certificate request.
This is typically used to generate test certificates.
It is implied by the \fB\-CA\fR option.
.Sp
This option implies the \fB\-new\fR flag if \fB\-in\fR is not given.
.Sp
If an existing request is specified with the \fB\-in\fR option, it is converted
to a certificate; otherwise a request is created from scratch.
.Sp
Unless specified using the \fB\-set_serial\fR option,
a large random number will be used for the serial number.
.Sp
Unless the \fB\-copy_extensions\fR option is used,
X.509 extensions are not copied from any provided request input file.
.Sp
X.509 extensions to be added can be specified in the configuration file
or using the \fB\-addext\fR option.
.IP "\fB\-CA\fR \fIfilename\fR|\fIuri\fR" 4
.IX Item "-CA filename|uri"
Specifies the "CA" certificate to be used for signing a new certificate
and implies use of \fB\-x509\fR.
When present, this behaves like a "micro CA" as follows:
The subject name of the "CA" certificate is placed as issuer name in the new
certificate, which is then signed using the "CA" key given as specified below.
.IP "\fB\-CAkey\fR \fIfilename\fR|\fIuri\fR" 4
.IX Item "-CAkey filename|uri"
Sets the "CA" private key to sign a certificate with.
The private key must match the public key of the certificate given with \fB\-CA\fR.
If this option is not provided then the key must be present in the \fB\-CA\fR input.
.IP "\fB\-days\fR \fIn\fR" 4
.IX Item "-days n"
When \fB\-x509\fR is in use this specifies the number of
days to certify the certificate for, otherwise it is ignored. \fIn\fR should
be a positive integer. The default is 30 days.
.IP "\fB\-set_serial\fR \fIn\fR" 4
.IX Item "-set_serial n"
Serial number to use when outputting a self-signed certificate.
This may be specified as a decimal value or a hex value if preceded by \f(CW\*(C`0x\*(C'\fR.
If not given, a large random number will be used.
.IP "\fB\-copy_extensions\fR \fIarg\fR" 4
.IX Item "-copy_extensions arg"
Determines how X.509 extensions in certificate requests should be handled
when \fB\-x509\fR is in use.
If \fIarg\fR is \fBnone\fR or this option is not present then extensions are ignored.
If \fIarg\fR is \fBcopy\fR or \fBcopyall\fR then
all extensions in the request are copied to the certificate.
.Sp
The main use of this option is to allow a certificate request to supply
values for certain extensions such as subjectAltName.
.IP "\fB\-addext\fR \fIext\fR" 4
.IX Item "-addext ext"
Add a specific extension to the certificate (if \fB\-x509\fR is in use)
or certificate request.  The argument must have the form of
a key=value pair as it would appear in a config file.
.Sp
This option can be given multiple times.
.IP "\fB\-extensions\fR \fIsection\fR" 4
.IX Item "-extensions section"
.PD 0
.IP "\fB\-reqexts\fR \fIsection\fR" 4
.IX Item "-reqexts section"
.PD
These options specify alternative sections to include certificate
extensions (if \fB\-x509\fR is in use) or certificate request extensions.
This allows several different sections to
be used in the same configuration file to specify requests for
a variety of purposes.
.IP \fB\-precert\fR 4
.IX Item "-precert"
A poison extension will be added to the certificate, making it a
"pre-certificate" (see RFC6962). This can be submitted to Certificate
Transparency logs in order to obtain signed certificate timestamps (SCTs).
These SCTs can then be embedded into the pre-certificate as an extension, before
removing the poison and signing the certificate.
.Sp
This implies the \fB\-new\fR flag.
.IP \fB\-utf8\fR 4
.IX Item "-utf8"
This option causes field values to be interpreted as UTF8 strings, by
default they are interpreted as ASCII. This means that the field
values, whether prompted from a terminal or obtained from a
configuration file, must be valid UTF8 strings.
.IP "\fB\-reqopt\fR \fIoption\fR" 4
.IX Item "-reqopt option"
Customise the printing format used with \fB\-text\fR. The \fIoption\fR argument can be
a single option or multiple options separated by commas.
.Sp
See discussion of the  \fB\-certopt\fR parameter in the \fBopenssl\-x509\fR\|(1)
command.
.IP \fB\-newhdr\fR 4
.IX Item "-newhdr"
Adds the word \fBNEW\fR to the PEM file header and footer lines on the outputted
request. Some software (Netscape certificate server) and some CAs need this.
.IP \fB\-batch\fR 4
.IX Item "-batch"
Non-interactive mode.
.IP \fB\-verbose\fR 4
.IX Item "-verbose"
Print extra details about the operations being performed.
.IP "\fB\-keygen_engine\fR \fIid\fR" 4
.IX Item "-keygen_engine id"
Specifies an engine (by its unique \fIid\fR string) which would be used
for key generation operations.
.IP "\fB\-nameopt\fR \fIoption\fR" 4
.IX Item "-nameopt option"
This specifies how the subject or issuer names are displayed.
See \fBopenssl\-namedisplay\-options\fR\|(1) for details.
.IP "\fB\-rand\fR \fIfiles\fR, \fB\-writerand\fR \fIfile\fR" 4
.IX Item "-rand files, -writerand file"
See "Random State Options" in \fBopenssl\fR\|(1) for details.
.IP "\fB\-engine\fR \fIid\fR" 4
.IX Item "-engine id"
See "Engine Options" in \fBopenssl\fR\|(1).
This option is deprecated.
.IP "\fB\-provider\fR \fIname\fR" 4
.IX Item "-provider name"
.PD 0
.IP "\fB\-provider\-path\fR \fIpath\fR" 4
.IX Item "-provider-path path"
.IP "\fB\-propquery\fR \fIpropq\fR" 4
.IX Item "-propquery propq"
.PD
See "Provider Options" in \fBopenssl\fR\|(1), \fBprovider\fR\|(7), and \fBproperty\fR\|(7).
.SH "CONFIGURATION FILE FORMAT"
.IX Header "CONFIGURATION FILE FORMAT"
The configuration options are specified in the \fBreq\fR section of
the configuration file. An alternate name be specified by using the
\&\fB\-section\fR option.
As with all configuration files, if no
value is specified in the specific section then
the initial unnamed or \fBdefault\fR section is searched too.
.PP
The options available are described in detail below.
.IP "\fBinput_password\fR, \fBoutput_password\fR" 4
.IX Item "input_password, output_password"
The passwords for the input private key file (if present) and
the output private key file (if one will be created). The
command line options \fBpassin\fR and \fBpassout\fR override the
configuration file values.
.IP \fBdefault_bits\fR 4
.IX Item "default_bits"
Specifies the default key size in bits.
.Sp
This option is used in conjunction with the \fB\-new\fR option to generate
a new key. It can be overridden by specifying an explicit key size in
the \fB\-newkey\fR option. The smallest accepted key size is 512 bits. If
no key size is specified then 2048 bits is used.
.IP \fBdefault_keyfile\fR 4
.IX Item "default_keyfile"
This is the default filename to write a private key to. If not
specified the key is written to standard output. This can be
overridden by the \fB\-keyout\fR option.
.IP \fBoid_file\fR 4
.IX Item "oid_file"
This specifies a file containing additional \fBOBJECT IDENTIFIERS\fR.
Each line of the file should consist of the numerical form of the
object identifier followed by whitespace then the short name followed
by whitespace and finally the long name.
.IP \fBoid_section\fR 4
.IX Item "oid_section"
This specifies a section in the configuration file containing extra
object identifiers. Each line should consist of the short name of the
object identifier followed by \fB=\fR and the numerical form. The short
and long names are the same when this option is used.
.IP \fBRANDFILE\fR 4
.IX Item "RANDFILE"
At startup the specified file is loaded into the random number generator,
and at exit 256 bytes will be written to it.
It is used for private key generation.
.IP \fBencrypt_key\fR 4
.IX Item "encrypt_key"
If this is set to \fBno\fR then if a private key is generated it is
\&\fBnot\fR encrypted. This is equivalent to the \fB\-noenc\fR command line
option. For compatibility \fBencrypt_rsa_key\fR is an equivalent option.
.IP \fBdefault_md\fR 4
.IX Item "default_md"
This option specifies the digest algorithm to use. Any digest supported by the
OpenSSL \fBdgst\fR command can be used. This option can be overridden on the
command line. Certain signing algorithms (i.e. Ed25519 and Ed448) will ignore
any digest that has been set.
.IP \fBstring_mask\fR 4
.IX Item "string_mask"
This option masks out the use of certain string types in certain
fields. Most users will not need to change this option.
.Sp
It can be set to several values \fBdefault\fR which is also the default
option uses PrintableStrings, T61Strings and BMPStrings if the
\&\fBpkix\fR value is used then only PrintableStrings and BMPStrings will
be used. This follows the PKIX recommendation in RFC2459. If the
\&\fButf8only\fR option is used then only UTF8Strings will be used: this
is the PKIX recommendation in RFC2459 after 2003. Finally the \fBnombstr\fR
option just uses PrintableStrings and T61Strings: certain software has
problems with BMPStrings and UTF8Strings: in particular Netscape.
.IP \fBreq_extensions\fR 4
.IX Item "req_extensions"
This specifies the configuration file section containing a list of
extensions to add to the certificate request. It can be overridden
by the \fB\-reqexts\fR command line switch. See the
\&\fBx509v3_config\fR\|(5) manual page for details of the
extension section format.
.IP \fBx509_extensions\fR 4
.IX Item "x509_extensions"
This specifies the configuration file section containing a list of
extensions to add to certificate generated when \fB\-x509\fR is in use.
It can be overridden by the \fB\-extensions\fR command line switch.
.IP \fBprompt\fR 4
.IX Item "prompt"
If set to the value \fBno\fR this disables prompting of certificate fields
and just takes values from the config file directly. It also changes the
expected format of the \fBdistinguished_name\fR and \fBattributes\fR sections.
.IP \fButf8\fR 4
.IX Item "utf8"
If set to the value \fByes\fR then field values to be interpreted as UTF8
strings, by default they are interpreted as ASCII. This means that
the field values, whether prompted from a terminal or obtained from a
configuration file, must be valid UTF8 strings.
.IP \fBattributes\fR 4
.IX Item "attributes"
This specifies the section containing any request attributes: its format
is the same as \fBdistinguished_name\fR. Typically these may contain the
challengePassword or unstructuredName types. They are currently ignored
by OpenSSL's request signing utilities but some CAs might want them.
.IP \fBdistinguished_name\fR 4
.IX Item "distinguished_name"
This specifies the section containing the distinguished name fields to
prompt for when generating a certificate or certificate request. The format
is described in the next section.
.SH "DISTINGUISHED NAME AND ATTRIBUTE SECTION FORMAT"
.IX Header "DISTINGUISHED NAME AND ATTRIBUTE SECTION FORMAT"
There are two separate formats for the distinguished name and attribute
sections. If the \fBprompt\fR option is set to \fBno\fR then these sections
just consist of field names and values: for example,
.PP
.Vb 3
\& CN=My Name
\& OU=My Organization
\& emailAddress=<EMAIL>
.Ve
.PP
This allows external programs (e.g. GUI based) to generate a template file with
all the field names and values and just pass it to this command. An example
of this kind of configuration file is contained in the \fBEXAMPLES\fR section.
.PP
Alternatively if the \fBprompt\fR option is absent or not set to \fBno\fR then the
file contains field prompting information. It consists of lines of the form:
.PP
.Vb 4
\& fieldName="prompt"
\& fieldName_default="default field value"
\& fieldName_min= 2
\& fieldName_max= 4
.Ve
.PP
"fieldName" is the field name being used, for example commonName (or CN).
The "prompt" string is used to ask the user to enter the relevant
details. If the user enters nothing then the default value is used if no
default value is present then the field is omitted. A field can
still be omitted if a default value is present if the user just
enters the '.' character.
.PP
The number of characters entered must be between the fieldName_min and
fieldName_max limits: there may be additional restrictions based
on the field being used (for example countryName can only ever be
two characters long and must fit in a PrintableString).
.PP
Some fields (such as organizationName) can be used more than once
in a DN. This presents a problem because configuration files will
not recognize the same name occurring twice. To avoid this problem
if the fieldName contains some characters followed by a full stop
they will be ignored. So for example a second organizationName can
be input by calling it "1.organizationName".
.PP
The actual permitted field names are any object identifier short or
long names. These are compiled into OpenSSL and include the usual
values such as commonName, countryName, localityName, organizationName,
organizationalUnitName, stateOrProvinceName. Additionally emailAddress
is included as well as name, surname, givenName, initials, and dnQualifier.
.PP
Additional object identifiers can be defined with the \fBoid_file\fR or
\&\fBoid_section\fR options in the configuration file. Any additional fields
will be treated as though they were a DirectoryString.
.SH EXAMPLES
.IX Header "EXAMPLES"
Examine and verify certificate request:
.PP
.Vb 1
\& openssl req \-in req.pem \-text \-verify \-noout
.Ve
.PP
Create a private key and then generate a certificate request from it:
.PP
.Vb 2
\& openssl genrsa \-out key.pem 2048
\& openssl req \-new \-key key.pem \-out req.pem
.Ve
.PP
The same but just using req:
.PP
.Vb 1
\& openssl req \-newkey rsa:2048 \-keyout key.pem \-out req.pem
.Ve
.PP
Generate a self-signed root certificate:
.PP
.Vb 1
\& openssl req \-x509 \-newkey rsa:2048 \-keyout key.pem \-out req.pem
.Ve
.PP
Create an SM2 private key and then generate a certificate request from it:
.PP
.Vb 2
\& openssl ecparam \-genkey \-name SM2 \-out sm2.key
\& openssl req \-new \-key sm2.key \-out sm2.csr \-sm3 \-sigopt "distid:1234567812345678"
.Ve
.PP
Examine and verify an SM2 certificate request:
.PP
.Vb 1
\& openssl req \-verify \-in sm2.csr \-sm3 \-vfyopt "distid:1234567812345678"
.Ve
.PP
Example of a file pointed to by the \fBoid_file\fR option:
.PP
.Vb 2
\& *******        shortName       A longer Name
\& 1.2.3.6        otherName       Other longer Name
.Ve
.PP
Example of a section pointed to by \fBoid_section\fR making use of variable
expansion:
.PP
.Vb 2
\& testoid1=1.2.3.5
\& testoid2=${testoid1}.6
.Ve
.PP
Sample configuration file prompting for field values:
.PP
.Vb 6
\& [ req ]
\& default_bits           = 2048
\& default_keyfile        = privkey.pem
\& distinguished_name     = req_distinguished_name
\& attributes             = req_attributes
\& req_extensions         = v3_ca
\&
\& dirstring_type = nobmp
\&
\& [ req_distinguished_name ]
\& countryName                    = Country Name (2 letter code)
\& countryName_default            = AU
\& countryName_min                = 2
\& countryName_max                = 2
\&
\& localityName                   = Locality Name (eg, city)
\&
\& organizationalUnitName         = Organizational Unit Name (eg, section)
\&
\& commonName                     = Common Name (eg, YOUR name)
\& commonName_max                 = 64
\&
\& emailAddress                   = Email Address
\& emailAddress_max               = 40
\&
\& [ req_attributes ]
\& challengePassword              = A challenge password
\& challengePassword_min          = 4
\& challengePassword_max          = 20
\&
\& [ v3_ca ]
\&
\& subjectKeyIdentifier=hash
\& authorityKeyIdentifier=keyid:always,issuer:always
\& basicConstraints = critical, CA:true
.Ve
.PP
Sample configuration containing all field values:
.PP
.Vb 7
\& [ req ]
\& default_bits           = 2048
\& default_keyfile        = keyfile.pem
\& distinguished_name     = req_distinguished_name
\& attributes             = req_attributes
\& prompt                 = no
\& output_password        = mypass
\&
\& [ req_distinguished_name ]
\& C                      = GB
\& ST                     = Test State or Province
\& L                      = Test Locality
\& O                      = Organization Name
\& OU                     = Organizational Unit Name
\& CN                     = Common Name
\& emailAddress           = <EMAIL>
\&
\& [ req_attributes ]
\& challengePassword              = A challenge password
.Ve
.PP
Example of giving the most common attributes (subject and extensions)
on the command line:
.PP
.Vb 4
\& openssl req \-new \-subj "/C=GB/CN=foo" \e
\&                  \-addext "subjectAltName = DNS:foo.co.uk" \e
\&                  \-addext "certificatePolicies = *******" \e
\&                  \-newkey rsa:2048 \-keyout key.pem \-out req.pem
.Ve
.SH NOTES
.IX Header "NOTES"
The certificate requests generated by \fBXenroll\fR with MSIE have extensions
added. It includes the \fBkeyUsage\fR extension which determines the type of
key (signature only or general purpose) and any additional OIDs entered
by the script in an \fBextendedKeyUsage\fR extension.
.SH DIAGNOSTICS
.IX Header "DIAGNOSTICS"
The following messages are frequently asked about:
.PP
.Vb 2
\&        Using configuration from /some/path/openssl.cnf
\&        Unable to load config info
.Ve
.PP
This is followed some time later by:
.PP
.Vb 2
\&        unable to find \*(Aqdistinguished_name\*(Aq in config
\&        problems making Certificate Request
.Ve
.PP
The first error message is the clue: it can't find the configuration
file! Certain operations (like examining a certificate request) don't
need a configuration file so its use isn't enforced. Generation of
certificates or requests however does need a configuration file. This
could be regarded as a bug.
.PP
Another puzzling message is this:
.PP
.Vb 2
\&        Attributes:
\&            a0:00
.Ve
.PP
this is displayed when no attributes are present and the request includes
the correct empty \fBSET OF\fR structure (the DER encoding of which is 0xa0
0x00). If you just see:
.PP
.Vb 1
\&        Attributes:
.Ve
.PP
then the \fBSET OF\fR is missing and the encoding is technically invalid (but
it is tolerated). See the description of the command line option \fB\-asn1\-kludge\fR
for more information.
.SH BUGS
.IX Header "BUGS"
OpenSSL's handling of T61Strings (aka TeletexStrings) is broken: it effectively
treats them as ISO\-8859\-1 (Latin 1), Netscape and MSIE have similar behaviour.
This can cause problems if you need characters that aren't available in
PrintableStrings and you don't want to or can't use BMPStrings.
.PP
As a consequence of the T61String handling the only correct way to represent
accented characters in OpenSSL is to use a BMPString: unfortunately Netscape
currently chokes on these. If you have to use accented characters with Netscape
and MSIE then you currently need to use the invalid T61String form.
.PP
The current prompting is not very friendly. It doesn't allow you to confirm what
you've just entered. Other things like extensions in certificate requests are
statically defined in the configuration file. Some of these: like an email
address in subjectAltName should be input by the user.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBopenssl\fR\|(1),
\&\fBopenssl\-x509\fR\|(1),
\&\fBopenssl\-ca\fR\|(1),
\&\fBopenssl\-genrsa\fR\|(1),
\&\fBopenssl\-gendsa\fR\|(1),
\&\fBconfig\fR\|(5),
\&\fBx509v3_config\fR\|(5)
.SH HISTORY
.IX Header "HISTORY"
The \fB\-section\fR option was added in OpenSSL 3.0.0.
.PP
The \fB\-multivalue\-rdn\fR option has become obsolete in OpenSSL 3.0.0 and
has no effect.
.PP
The \fB\-engine\fR option was deprecated in OpenSSL 3.0.
The <\-nodes> option was deprecated in OpenSSL 3.0, too; use \fB\-noenc\fR instead.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
