.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "OPENSSL-DGST 1ossl"
.TH OPENSSL-DGST 1ossl 2025-08-08 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
openssl\-dgst \- perform digest operations
.SH SYNOPSIS
.IX Header "SYNOPSIS"
\&\fBopenssl\fR \fBdgst\fR|\fIdigest\fR
[\fB\-\fR\f(BIdigest\fR]
[\fB\-list\fR]
[\fB\-help\fR]
[\fB\-c\fR]
[\fB\-d\fR]
[\fB\-debug\fR]
[\fB\-hex\fR]
[\fB\-binary\fR]
[\fB\-xoflen\fR \fIlength\fR]
[\fB\-r\fR]
[\fB\-out\fR \fIfilename\fR]
[\fB\-sign\fR \fIfilename\fR|\fIuri\fR]
[\fB\-keyform\fR \fBDER\fR|\fBPEM\fR|\fBP12\fR|\fBENGINE\fR]
[\fB\-passin\fR \fIarg\fR]
[\fB\-verify\fR \fIfilename\fR]
[\fB\-prverify\fR \fIfilename\fR]
[\fB\-signature\fR \fIfilename\fR]
[\fB\-sigopt\fR \fInm\fR:\fIv\fR]
[\fB\-hmac\fR \fIkey\fR]
[\fB\-mac\fR \fIalg\fR]
[\fB\-macopt\fR \fInm\fR:\fIv\fR]
[\fB\-fips\-fingerprint\fR]
[\fB\-engine\fR \fIid\fR]
[\fB\-engine_impl\fR \fIid\fR]
[\fB\-rand\fR \fIfiles\fR]
[\fB\-writerand\fR \fIfile\fR]
[\fB\-provider\fR \fIname\fR]
[\fB\-provider\-path\fR \fIpath\fR]
[\fB\-propquery\fR \fIpropq\fR]
[\fIfile\fR ...]
.SH DESCRIPTION
.IX Header "DESCRIPTION"
This command output the message digest of a supplied file or files
in hexadecimal, and also generates and verifies digital
signatures using message digests.
.PP
The generic name, \fBopenssl dgst\fR, may be used with an option specifying the
algorithm to be used.
The default digest is \fBsha256\fR.
A supported \fIdigest\fR name may also be used as the sub-command name.
To see the list of supported algorithms, use \f(CW\*(C`openssl list \-digest\-algorithms\*(C'\fR
.SH OPTIONS
.IX Header "OPTIONS"
.IP \fB\-help\fR 4
.IX Item "-help"
Print out a usage message.
.IP \fB\-\fR\f(BIdigest\fR 4
.IX Item "-digest"
Specifies name of a supported digest to be used. See option \fB\-list\fR below :
.IP \fB\-list\fR 4
.IX Item "-list"
Prints out a list of supported message digests.
.IP \fB\-c\fR 4
.IX Item "-c"
Print out the digest in two digit groups separated by colons, only relevant if
the \fB\-hex\fR option is given as well.
.IP "\fB\-d\fR, \fB\-debug\fR" 4
.IX Item "-d, -debug"
Print out BIO debugging information.
.IP \fB\-hex\fR 4
.IX Item "-hex"
Digest is to be output as a hex dump. This is the default case for a "normal"
digest as opposed to a digital signature.  See NOTES below for digital
signatures using \fB\-hex\fR.
.IP \fB\-binary\fR 4
.IX Item "-binary"
Output the digest or signature in binary form.
.IP "\fB\-xoflen\fR \fIlength\fR" 4
.IX Item "-xoflen length"
Set the output length for XOF algorithms, such as \fBshake128\fR and \fBshake256\fR.
This option is not supported for signing operations.
.Sp
For OpenSSL providers it is recommended to set this value for shake algorithms,
since the default values are set to only supply half of the maximum security
strength.
.Sp
For backwards compatibility reasons the default xoflen length for \fBshake128\fR is
16 (bytes) which results in a security strength of only 64 bits. To ensure the
maximum security strength of 128 bits, the xoflen should be set to at least 32.
.Sp
For backwards compatibility reasons the default xoflen length for \fBshake256\fR is
32 (bytes) which results in a security strength of only 128 bits. To ensure the
maximum security strength of 256 bits, the xoflen should be set to at least 64.
.IP \fB\-r\fR 4
.IX Item "-r"
Output the digest in the "coreutils" format, including newlines.
Used by programs like \fBsha1sum\fR\|(1).
.IP "\fB\-out\fR \fIfilename\fR" 4
.IX Item "-out filename"
Filename to output to, or standard output by default.
.IP "\fB\-sign\fR \fIfilename\fR|\fIuri\fR" 4
.IX Item "-sign filename|uri"
Digitally sign the digest using the given private key. Note this option
does not support Ed25519 or Ed448 private keys. Use the \fBopenssl\-pkeyutl\fR\|(1)
command instead for this.
.IP "\fB\-keyform\fR \fBDER\fR|\fBPEM\fR|\fBP12\fR|\fBENGINE\fR" 4
.IX Item "-keyform DER|PEM|P12|ENGINE"
The format of the key to sign with; unspecified by default.
See \fBopenssl\-format\-options\fR\|(1) for details.
.IP "\fB\-sigopt\fR \fInm\fR:\fIv\fR" 4
.IX Item "-sigopt nm:v"
Pass options to the signature algorithm during sign or verify operations.
Names and values of these options are algorithm-specific.
.IP "\fB\-passin\fR \fIarg\fR" 4
.IX Item "-passin arg"
The private key password source. For more information about the format of \fIarg\fR
see \fBopenssl\-passphrase\-options\fR\|(1).
.IP "\fB\-verify\fR \fIfilename\fR" 4
.IX Item "-verify filename"
Verify the signature using the public key in "filename".
The output is either "Verified OK" or "Verification Failure".
.IP "\fB\-prverify\fR \fIfilename\fR" 4
.IX Item "-prverify filename"
Verify the signature using the private key in "filename".
.IP "\fB\-signature\fR \fIfilename\fR" 4
.IX Item "-signature filename"
The actual signature to verify.
.IP "\fB\-hmac\fR \fIkey\fR" 4
.IX Item "-hmac key"
Create a hashed MAC using "key".
.Sp
The \fBopenssl\-mac\fR\|(1) command should be preferred to using this command line
option.
.IP "\fB\-mac\fR \fIalg\fR" 4
.IX Item "-mac alg"
Create MAC (keyed Message Authentication Code). The most popular MAC
algorithm is HMAC (hash-based MAC), but there are other MAC algorithms
which are not based on hash, for instance \fBgost-mac\fR algorithm,
supported by the \fBgost\fR engine. MAC keys and other options should be set
via \fB\-macopt\fR parameter.
.Sp
The \fBopenssl\-mac\fR\|(1) command should be preferred to using this command line
option.
.IP "\fB\-macopt\fR \fInm\fR:\fIv\fR" 4
.IX Item "-macopt nm:v"
Passes options to MAC algorithm, specified by \fB\-mac\fR key.
Following options are supported by both by \fBHMAC\fR and \fBgost-mac\fR:
.RS 4
.IP \fBkey\fR:\fIstring\fR 4
.IX Item "key:string"
Specifies MAC key as alphanumeric string (use if key contain printable
characters only). String length must conform to any restrictions of
the MAC algorithm for example exactly 32 chars for gost-mac.
.IP \fBhexkey\fR:\fIstring\fR 4
.IX Item "hexkey:string"
Specifies MAC key in hexadecimal form (two hex digits per byte).
Key length must conform to any restrictions of the MAC algorithm
for example exactly 32 chars for gost-mac.
.RE
.RS 4
.Sp
The \fBopenssl\-mac\fR\|(1) command should be preferred to using this command line
option.
.RE
.IP \fB\-fips\-fingerprint\fR 4
.IX Item "-fips-fingerprint"
Compute HMAC using a specific key for certain OpenSSL-FIPS operations.
.IP "\fB\-rand\fR \fIfiles\fR, \fB\-writerand\fR \fIfile\fR" 4
.IX Item "-rand files, -writerand file"
See "Random State Options" in \fBopenssl\fR\|(1) for details.
.IP "\fB\-engine\fR \fIid\fR" 4
.IX Item "-engine id"
See "Engine Options" in \fBopenssl\fR\|(1).
This option is deprecated.
.Sp
The engine is not used for digests unless the \fB\-engine_impl\fR option is
used or it is configured to do so, see "Engine Configuration Module" in \fBconfig\fR\|(5).
.IP "\fB\-engine_impl\fR \fIid\fR" 4
.IX Item "-engine_impl id"
When used with the \fB\-engine\fR option, it specifies to also use
engine \fIid\fR for digest operations.
.IP "\fB\-provider\fR \fIname\fR" 4
.IX Item "-provider name"
.PD 0
.IP "\fB\-provider\-path\fR \fIpath\fR" 4
.IX Item "-provider-path path"
.IP "\fB\-propquery\fR \fIpropq\fR" 4
.IX Item "-propquery propq"
.PD
See "Provider Options" in \fBopenssl\fR\|(1), \fBprovider\fR\|(7), and \fBproperty\fR\|(7).
.IP "\fIfile\fR ..." 4
.IX Item "file ..."
File or files to digest. If no files are specified then standard input is
used.
.SH EXAMPLES
.IX Header "EXAMPLES"
To create a hex-encoded message digest of a file:
.PP
.Vb 3
\& openssl dgst \-md5 \-hex file.txt
\& or
\& openssl md5 file.txt
.Ve
.PP
To sign a file using SHA\-256 with binary file output:
.PP
.Vb 3
\& openssl dgst \-sha256 \-sign privatekey.pem \-out signature.sign file.txt
\& or
\& openssl sha256 \-sign privatekey.pem \-out signature.sign file.txt
.Ve
.PP
To verify a signature:
.PP
.Vb 3
\& openssl dgst \-sha256 \-verify publickey.pem \e
\& \-signature signature.sign \e
\& file.txt
.Ve
.SH NOTES
.IX Header "NOTES"
The digest mechanisms that are available will depend on the options
used when building OpenSSL.
The \f(CW\*(C`openssl list \-digest\-algorithms\*(C'\fR command can be used to list them.
.PP
New or agile applications should use probably use SHA\-256. Other digests,
particularly SHA\-1 and MD5, are still widely used for interoperating
with existing formats and protocols.
.PP
When signing a file, this command will automatically determine the algorithm
(RSA, ECC, etc) to use for signing based on the private key's ASN.1 info.
When verifying signatures, it only handles the RSA, DSA, or ECDSA signature
itself, not the related data to identify the signer and algorithm used in
formats such as x.509, CMS, and S/MIME.
.PP
A source of random numbers is required for certain signing algorithms, in
particular ECDSA and DSA.
.PP
The signing and verify options should only be used if a single file is
being signed or verified.
.PP
Hex signatures cannot be verified using \fBopenssl\fR.  Instead, use "xxd \-r"
or similar program to transform the hex signature into a binary signature
prior to verification.
.PP
The \fBopenssl\-mac\fR\|(1) command is preferred over the \fB\-hmac\fR, \fB\-mac\fR and
\&\fB\-macopt\fR command line options.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBopenssl\-mac\fR\|(1)
.SH HISTORY
.IX Header "HISTORY"
The default digest was changed from MD5 to SHA256 in OpenSSL 1.1.0.
The FIPS-related options were removed in OpenSSL 1.1.0.
.PP
The \fB\-engine\fR and \fB\-engine_impl\fR options were deprecated in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2022 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
