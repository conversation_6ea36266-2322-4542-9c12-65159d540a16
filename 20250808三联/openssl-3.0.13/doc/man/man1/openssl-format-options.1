.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "OPENSSL-FORMAT-OPTIONS 1ossl"
.TH OPENSSL-FORMAT-OPTIONS 1ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
openssl\-format\-options \- OpenSSL command input and output format options
.SH SYNOPSIS
.IX Header "SYNOPSIS"
\&\fBopenssl\fR
\&\fIcommand\fR
[ \fIoptions\fR ... ]
[ \fIparameters\fR ... ]
.SH DESCRIPTION
.IX Header "DESCRIPTION"
Several OpenSSL commands can take input or generate output in a variety
of formats.
.PP
Since OpenSSL 3.0 keys, single certificates, and CRLs can be read from
files in any of the \fBDER\fR, \fBPEM\fR or \fBP12\fR formats. Specifying their input
format is no more needed and the openssl commands will automatically try all
the possible formats. However if the \fBDER\fR or \fBPEM\fR input format is specified
it will be enforced.
.PP
In order to access a key via an engine the input format \fBENGINE\fR may be used;
alternatively the key identifier in the <uri> argument of the respective key
option may be preceded by \f(CW\*(C`org.openssl.engine:\*(C'\fR.
See "Engine Options" in \fBopenssl\fR\|(1) for an example usage of the latter.
.SH OPTIONS
.IX Header "OPTIONS"
.SS "Format Options"
.IX Subsection "Format Options"
The options to specify the format are as follows.
Refer to the individual man page to see which options are accepted.
.IP "\fB\-inform\fR \fIformat\fR, \fB\-outform\fR \fIformat\fR" 4
.IX Item "-inform format, -outform format"
The format of the input or output streams.
.IP "\fB\-keyform\fR \fIformat\fR" 4
.IX Item "-keyform format"
Format of a private key input source.
.IP "\fB\-CRLform\fR \fIformat\fR" 4
.IX Item "-CRLform format"
Format of a CRL input source.
.SS "Format Option Arguments"
.IX Subsection "Format Option Arguments"
The possible format arguments are described below.
Both uppercase and lowercase are accepted.
.PP
The list of acceptable format arguments, and the default,
is described in each command documentation.
.IP \fBDER\fR 4
.IX Item "DER"
A binary format, encoded or parsed according to Distinguished Encoding Rules
(DER) of the ASN.1 data language.
.IP \fBENGINE\fR 4
.IX Item "ENGINE"
Used to specify that the cryptographic material is in an OpenSSL \fBengine\fR.
An engine must be configured or specified using the \fB\-engine\fR option.
A password or PIN may be supplied to the engine using the \fB\-passin\fR option.
.IP \fBP12\fR 4
.IX Item "P12"
A DER-encoded file containing a PKCS#12 object.
It might be necessary to provide a decryption password to retrieve
the private key.
.IP \fBPEM\fR 4
.IX Item "PEM"
A text format defined in IETF RFC 1421 and IETF RFC 7468. Briefly, this is
a block of base\-64 encoding (defined in IETF RFC 4648), with specific
lines used to mark the start and end:
.Sp
.Vb 7
\& Text before the BEGIN line is ignored.
\& \-\-\-\-\- BEGIN object\-type \-\-\-\-\-
\& OT43gQKBgQC/2OHZoko6iRlNOAQ/tMVFNq7fL81GivoQ9F1U0Qr+DH3ZfaH8eIkX
\& xT0ToMPJUzWAn8pZv0snA0um6SIgvkCuxO84OkANCVbttzXImIsL7pFzfcwV/ERK
\& UM6j0ZuSMFOCr/lGPAoOQU0fskidGEHi1/kW+suSr28TqsyYZpwBDQ==
\& \-\-\-\-\- END object\-type \-\-\-\-\-
\& Text after the END line is also ignored
.Ve
.Sp
The \fIobject-type\fR must match the type of object that is expected.
For example a \f(CW\*(C`BEGIN X509 CERTIFICATE\*(C'\fR will not match if the command
is trying to read a private key. The types supported include:
.Sp
.Vb 10
\& ANY PRIVATE KEY
\& CERTIFICATE
\& CERTIFICATE REQUEST
\& CMS
\& DH PARAMETERS
\& DSA PARAMETERS
\& DSA PUBLIC KEY
\& EC PARAMETERS
\& EC PRIVATE KEY
\& ECDSA PUBLIC KEY
\& ENCRYPTED PRIVATE KEY
\& PARAMETERS
\& PKCS #7 SIGNED DATA
\& PKCS7
\& PRIVATE KEY
\& PUBLIC KEY
\& RSA PRIVATE KEY
\& SSL SESSION PARAMETERS
\& TRUSTED CERTIFICATE
\& X509 CRL
\& X9.42 DH PARAMETERS
.Ve
.Sp
The following legacy \fIobject-type\fR's are also supported for compatibility
with earlier releases:
.Sp
.Vb 4
\& DSA PRIVATE KEY
\& NEW CERTIFICATE REQUEST
\& RSA PUBLIC KEY
\& X509 CERTIFICATE
.Ve
.IP \fBSMIME\fR 4
.IX Item "SMIME"
An S/MIME object as described in IETF RFC 8551.
Earlier versions were known as CMS and are compatible.
Note that the parsing is simple and might fail to parse some legal data.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
