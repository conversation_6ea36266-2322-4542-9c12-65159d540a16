.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "OPENSSL-EC 1ossl"
.TH OPENSSL-EC 1ossl 2025-08-08 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
openssl\-ec \- EC key processing
.SH SYNOPSIS
.IX Header "SYNOPSIS"
\&\fBopenssl\fR \fBec\fR
[\fB\-help\fR]
[\fB\-inform\fR \fBDER\fR|\fBPEM\fR|\fBP12\fR|\fBENGINE\fR]
[\fB\-outform\fR \fBDER\fR|\fBPEM\fR]
[\fB\-in\fR \fIfilename\fR|\fIuri\fR]
[\fB\-passin\fR \fIarg\fR]
[\fB\-out\fR \fIfilename\fR]
[\fB\-passout\fR \fIarg\fR]
[\fB\-des\fR]
[\fB\-des3\fR]
[\fB\-idea\fR]
[\fB\-text\fR]
[\fB\-noout\fR]
[\fB\-param_out\fR]
[\fB\-pubin\fR]
[\fB\-pubout\fR]
[\fB\-conv_form\fR \fIarg\fR]
[\fB\-param_enc\fR \fIarg\fR]
[\fB\-no_public\fR]
[\fB\-check\fR]
[\fB\-engine\fR \fIid\fR]
[\fB\-provider\fR \fIname\fR]
[\fB\-provider\-path\fR \fIpath\fR]
[\fB\-propquery\fR \fIpropq\fR]
.SH DESCRIPTION
.IX Header "DESCRIPTION"
The \fBopenssl\-ec\fR\|(1) command processes EC keys. They can be converted between
various forms and their components printed out. \fBNote\fR OpenSSL uses the
private key format specified in 'SEC 1: Elliptic Curve Cryptography'
(http://www.secg.org/). To convert an OpenSSL EC private key into the
PKCS#8 private key format use the \fBopenssl\-pkcs8\fR\|(1) command.
.SH OPTIONS
.IX Header "OPTIONS"
.IP \fB\-help\fR 4
.IX Item "-help"
Print out a usage message.
.IP "\fB\-inform\fR \fBDER\fR|\fBPEM\fR|\fBP12\fR|\fBENGINE\fR" 4
.IX Item "-inform DER|PEM|P12|ENGINE"
The key input format; unspecified by default.
See \fBopenssl\-format\-options\fR\|(1) for details.
.IP "\fB\-outform\fR \fBDER\fR|\fBPEM\fR" 4
.IX Item "-outform DER|PEM"
The key output format; the default is \fBPEM\fR.
See \fBopenssl\-format\-options\fR\|(1) for details.
.Sp
Private keys are an SEC1 private key or PKCS#8 format.
Public keys are a \fBSubjectPublicKeyInfo\fR as specified in IETF RFC 3280.
.IP "\fB\-in\fR \fIfilename\fR|\fIuri\fR" 4
.IX Item "-in filename|uri"
This specifies the input to read a key from or standard input if this
option is not specified. If the key is encrypted a pass phrase will be
prompted for.
.IP "\fB\-out\fR \fIfilename\fR" 4
.IX Item "-out filename"
This specifies the output filename to write a key to or standard output by
is not specified. If any encryption options are set then a pass phrase will be
prompted for. The output filename should \fBnot\fR be the same as the input
filename.
.IP "\fB\-passin\fR \fIarg\fR, \fB\-passout\fR \fIarg\fR" 4
.IX Item "-passin arg, -passout arg"
The password source for the input and output file.
For more information about the format of \fBarg\fR
see \fBopenssl\-passphrase\-options\fR\|(1).
.IP \fB\-des\fR|\fB\-des3\fR|\fB\-idea\fR 4
.IX Item "-des|-des3|-idea"
These options encrypt the private key with the DES, triple DES, IDEA or
any other cipher supported by OpenSSL before outputting it. A pass phrase is
prompted for.
If none of these options is specified the key is written in plain text. This
means that using this command to read in an encrypted key with no
encryption option can be used to remove the pass phrase from a key, or by
setting the encryption options it can be use to add or change the pass phrase.
These options can only be used with PEM format output files.
.IP \fB\-text\fR 4
.IX Item "-text"
Prints out the public, private key components and parameters.
.IP \fB\-noout\fR 4
.IX Item "-noout"
This option prevents output of the encoded version of the key.
.IP \fB\-param_out\fR 4
.IX Item "-param_out"
Print the elliptic curve parameters.
.IP \fB\-pubin\fR 4
.IX Item "-pubin"
By default, a private key is read from the input file. With this option a
public key is read instead.
.IP \fB\-pubout\fR 4
.IX Item "-pubout"
By default a private key is output. With this option a public
key will be output instead. This option is automatically set if the input is
a public key.
.IP "\fB\-conv_form\fR \fIarg\fR" 4
.IX Item "-conv_form arg"
This specifies how the points on the elliptic curve are converted
into octet strings. Possible values are: \fBcompressed\fR, \fBuncompressed\fR (the
default value) and \fBhybrid\fR. For more information regarding
the point conversion forms please read the X9.62 standard.
\&\fBNote\fR Due to patent issues the \fBcompressed\fR option is disabled
by default for binary curves and can be enabled by defining
the preprocessor macro \fBOPENSSL_EC_BIN_PT_COMP\fR at compile time.
.IP "\fB\-param_enc\fR \fIarg\fR" 4
.IX Item "-param_enc arg"
This specifies how the elliptic curve parameters are encoded.
Possible value are: \fBnamed_curve\fR, i.e. the ec parameters are
specified by an OID, or \fBexplicit\fR where the ec parameters are
explicitly given (see RFC 3279 for the definition of the
EC parameters structures). The default value is \fBnamed_curve\fR.
\&\fBNote\fR the \fBimplicitlyCA\fR alternative, as specified in RFC 3279,
is currently not implemented in OpenSSL.
.IP \fB\-no_public\fR 4
.IX Item "-no_public"
This option omits the public key components from the private key output.
.IP \fB\-check\fR 4
.IX Item "-check"
This option checks the consistency of an EC private or public key.
.IP "\fB\-engine\fR \fIid\fR" 4
.IX Item "-engine id"
See "Engine Options" in \fBopenssl\fR\|(1).
This option is deprecated.
.IP "\fB\-provider\fR \fIname\fR" 4
.IX Item "-provider name"
.PD 0
.IP "\fB\-provider\-path\fR \fIpath\fR" 4
.IX Item "-provider-path path"
.IP "\fB\-propquery\fR \fIpropq\fR" 4
.IX Item "-propquery propq"
.PD
See "Provider Options" in \fBopenssl\fR\|(1), \fBprovider\fR\|(7), and \fBproperty\fR\|(7).
.PP
The \fBopenssl\-pkey\fR\|(1) command is capable of performing all the operations
this command can, as well as supporting other public key types.
.SH EXAMPLES
.IX Header "EXAMPLES"
The documentation for the \fBopenssl\-pkey\fR\|(1) command contains examples
equivalent to the ones listed here.
.PP
To encrypt a private key using triple DES:
.PP
.Vb 1
\& openssl ec \-in key.pem \-des3 \-out keyout.pem
.Ve
.PP
To convert a private key from PEM to DER format:
.PP
.Vb 1
\& openssl ec \-in key.pem \-outform DER \-out keyout.der
.Ve
.PP
To print out the components of a private key to standard output:
.PP
.Vb 1
\& openssl ec \-in key.pem \-text \-noout
.Ve
.PP
To just output the public part of a private key:
.PP
.Vb 1
\& openssl ec \-in key.pem \-pubout \-out pubkey.pem
.Ve
.PP
To change the parameters encoding to \fBexplicit\fR:
.PP
.Vb 1
\& openssl ec \-in key.pem \-param_enc explicit \-out keyout.pem
.Ve
.PP
To change the point conversion form to \fBcompressed\fR:
.PP
.Vb 1
\& openssl ec \-in key.pem \-conv_form compressed \-out keyout.pem
.Ve
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBopenssl\fR\|(1),
\&\fBopenssl\-pkey\fR\|(1),
\&\fBopenssl\-ecparam\fR\|(1),
\&\fBopenssl\-dsa\fR\|(1),
\&\fBopenssl\-rsa\fR\|(1)
.SH HISTORY
.IX Header "HISTORY"
The \fB\-engine\fR option was deprecated in OpenSSL 3.0.
.PP
The \fB\-conv_form\fR and \fB\-no_public\fR options are no longer supported
with keys loaded from an engine in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2003\-2022 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
