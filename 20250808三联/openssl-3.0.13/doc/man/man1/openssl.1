.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "OPENSSL 1ossl"
.TH OPENSSL 1ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
openssl \- OpenSSL command line program
.SH SYNOPSIS
.IX Header "SYNOPSIS"
\&\fBopenssl\fR
\&\fIcommand\fR
[ \fIoptions\fR ... ]
[ \fIparameters\fR ... ]
.PP
\&\fBopenssl\fR \fBno\-\fR\fIXXX\fR [ \fIoptions\fR ]
.SH DESCRIPTION
.IX Header "DESCRIPTION"
OpenSSL is a cryptography toolkit implementing the Secure Sockets Layer (SSL
v2/v3) and Transport Layer Security (TLS v1) network protocols and related
cryptography standards required by them.
.PP
The \fBopenssl\fR program is a command line program for using the various
cryptography functions of OpenSSL's \fBcrypto\fR library from the shell.
It can be used for
.PP
.Vb 8
\& o  Creation and management of private keys, public keys and parameters
\& o  Public key cryptographic operations
\& o  Creation of X.509 certificates, CSRs and CRLs
\& o  Calculation of Message Digests and Message Authentication Codes
\& o  Encryption and Decryption with Ciphers
\& o  SSL/TLS Client and Server Tests
\& o  Handling of S/MIME signed or encrypted mail
\& o  Timestamp requests, generation and verification
.Ve
.SH "COMMAND SUMMARY"
.IX Header "COMMAND SUMMARY"
The \fBopenssl\fR program provides a rich variety of commands (\fIcommand\fR in
the "SYNOPSIS" above).
Each command can have many options and argument parameters, shown above as
\&\fIoptions\fR and \fIparameters\fR.
.PP
Detailed documentation and use cases for most standard subcommands are available
(e.g., \fBopenssl\-x509\fR\|(1)). The subcommand \fBopenssl\-list\fR\|(1) may be used to list
subcommands.
.PP
The command \fBno\-\fR\fIXXX\fR tests whether a command of the
specified name is available.  If no command named \fIXXX\fR exists, it
returns 0 (success) and prints \fBno\-\fR\fIXXX\fR; otherwise it returns 1
and prints \fIXXX\fR.  In both cases, the output goes to \fBstdout\fR and
nothing is printed to \fBstderr\fR.  Additional command line arguments
are always ignored.  Since for each cipher there is a command of the
same name, this provides an easy way for shell scripts to test for the
availability of ciphers in the \fBopenssl\fR program.  (\fBno\-\fR\fIXXX\fR is
not able to detect pseudo-commands such as \fBquit\fR,
\&\fBlist\fR, or \fBno\-\fR\fIXXX\fR itself.)
.SS "Configuration Option"
.IX Subsection "Configuration Option"
Many commands use an external configuration file for some or all of their
arguments and have a \fB\-config\fR option to specify that file.
The default name of the file is \fIopenssl.cnf\fR in the default certificate
storage area, which can be determined from the \fBopenssl\-version\fR\|(1)
command using the \fB\-d\fR or \fB\-a\fR option.
The environment variable \fBOPENSSL_CONF\fR can be used to specify a different
file location or to disable loading a configuration (using the empty string).
.PP
Among others, the configuration file can be used to load modules
and to specify parameters for generating certificates and random numbers.
See \fBconfig\fR\|(5) for details.
.SS "Standard Commands"
.IX Subsection "Standard Commands"
.IP \fBasn1parse\fR 4
.IX Item "asn1parse"
Parse an ASN.1 sequence.
.IP \fBca\fR 4
.IX Item "ca"
Certificate Authority (CA) Management.
.IP \fBciphers\fR 4
.IX Item "ciphers"
Cipher Suite Description Determination.
.IP \fBcms\fR 4
.IX Item "cms"
CMS (Cryptographic Message Syntax) command.
.IP \fBcrl\fR 4
.IX Item "crl"
Certificate Revocation List (CRL) Management.
.IP \fBcrl2pkcs7\fR 4
.IX Item "crl2pkcs7"
CRL to PKCS#7 Conversion.
.IP \fBdgst\fR 4
.IX Item "dgst"
Message Digest calculation. MAC calculations are superseded by
\&\fBopenssl\-mac\fR\|(1).
.IP \fBdhparam\fR 4
.IX Item "dhparam"
Generation and Management of Diffie-Hellman Parameters. Superseded by
\&\fBopenssl\-genpkey\fR\|(1) and \fBopenssl\-pkeyparam\fR\|(1).
.IP \fBdsa\fR 4
.IX Item "dsa"
DSA Data Management.
.IP \fBdsaparam\fR 4
.IX Item "dsaparam"
DSA Parameter Generation and Management. Superseded by
\&\fBopenssl\-genpkey\fR\|(1) and \fBopenssl\-pkeyparam\fR\|(1).
.IP \fBec\fR 4
.IX Item "ec"
EC (Elliptic curve) key processing.
.IP \fBecparam\fR 4
.IX Item "ecparam"
EC parameter manipulation and generation.
.IP \fBenc\fR 4
.IX Item "enc"
Encryption, decryption, and encoding.
.IP \fBengine\fR 4
.IX Item "engine"
Engine (loadable module) information and manipulation.
.IP \fBerrstr\fR 4
.IX Item "errstr"
Error Number to Error String Conversion.
.IP \fBfipsinstall\fR 4
.IX Item "fipsinstall"
FIPS configuration installation.
.IP \fBgendsa\fR 4
.IX Item "gendsa"
Generation of DSA Private Key from Parameters. Superseded by
\&\fBopenssl\-genpkey\fR\|(1) and \fBopenssl\-pkey\fR\|(1).
.IP \fBgenpkey\fR 4
.IX Item "genpkey"
Generation of Private Key or Parameters.
.IP \fBgenrsa\fR 4
.IX Item "genrsa"
Generation of RSA Private Key. Superseded by \fBopenssl\-genpkey\fR\|(1).
.IP \fBhelp\fR 4
.IX Item "help"
Display information about a command's options.
.IP \fBinfo\fR 4
.IX Item "info"
Display diverse information built into the OpenSSL libraries.
.IP \fBkdf\fR 4
.IX Item "kdf"
Key Derivation Functions.
.IP \fBlist\fR 4
.IX Item "list"
List algorithms and features.
.IP \fBmac\fR 4
.IX Item "mac"
Message Authentication Code Calculation.
.IP \fBnseq\fR 4
.IX Item "nseq"
Create or examine a Netscape certificate sequence.
.IP \fBocsp\fR 4
.IX Item "ocsp"
Online Certificate Status Protocol command.
.IP \fBpasswd\fR 4
.IX Item "passwd"
Generation of hashed passwords.
.IP \fBpkcs12\fR 4
.IX Item "pkcs12"
PKCS#12 Data Management.
.IP \fBpkcs7\fR 4
.IX Item "pkcs7"
PKCS#7 Data Management.
.IP \fBpkcs8\fR 4
.IX Item "pkcs8"
PKCS#8 format private key conversion command.
.IP \fBpkey\fR 4
.IX Item "pkey"
Public and private key management.
.IP \fBpkeyparam\fR 4
.IX Item "pkeyparam"
Public key algorithm parameter management.
.IP \fBpkeyutl\fR 4
.IX Item "pkeyutl"
Public key algorithm cryptographic operation command.
.IP \fBprime\fR 4
.IX Item "prime"
Compute prime numbers.
.IP \fBrand\fR 4
.IX Item "rand"
Generate pseudo-random bytes.
.IP \fBrehash\fR 4
.IX Item "rehash"
Create symbolic links to certificate and CRL files named by the hash values.
.IP \fBreq\fR 4
.IX Item "req"
PKCS#10 X.509 Certificate Signing Request (CSR) Management.
.IP \fBrsa\fR 4
.IX Item "rsa"
RSA key management.
.IP \fBrsautl\fR 4
.IX Item "rsautl"
RSA command for signing, verification, encryption, and decryption. Superseded
by  \fBopenssl\-pkeyutl\fR\|(1).
.IP \fBs_client\fR 4
.IX Item "s_client"
This implements a generic SSL/TLS client which can establish a transparent
connection to a remote server speaking SSL/TLS. It's intended for testing
purposes only and provides only rudimentary interface functionality but
internally uses mostly all functionality of the OpenSSL \fBssl\fR library.
.IP \fBs_server\fR 4
.IX Item "s_server"
This implements a generic SSL/TLS server which accepts connections from remote
clients speaking SSL/TLS. It's intended for testing purposes only and provides
only rudimentary interface functionality but internally uses mostly all
functionality of the OpenSSL \fBssl\fR library.  It provides both an own command
line oriented protocol for testing SSL functions and a simple HTTP response
facility to emulate an SSL/TLS\-aware webserver.
.IP \fBs_time\fR 4
.IX Item "s_time"
SSL Connection Timer.
.IP \fBsess_id\fR 4
.IX Item "sess_id"
SSL Session Data Management.
.IP \fBsmime\fR 4
.IX Item "smime"
S/MIME mail processing.
.IP \fBspeed\fR 4
.IX Item "speed"
Algorithm Speed Measurement.
.IP \fBspkac\fR 4
.IX Item "spkac"
SPKAC printing and generating command.
.IP \fBsrp\fR 4
.IX Item "srp"
Maintain SRP password file. This command is deprecated.
.IP \fBstoreutl\fR 4
.IX Item "storeutl"
Command to list and display certificates, keys, CRLs, etc.
.IP \fBts\fR 4
.IX Item "ts"
Time Stamping Authority command.
.IP \fBverify\fR 4
.IX Item "verify"
X.509 Certificate Verification.
See also the \fBopenssl\-verification\-options\fR\|(1) manual page.
.IP \fBversion\fR 4
.IX Item "version"
OpenSSL Version Information.
.IP \fBx509\fR 4
.IX Item "x509"
X.509 Certificate Data Management.
.SS "Message Digest Commands"
.IX Subsection "Message Digest Commands"
.IP \fBblake2b512\fR 4
.IX Item "blake2b512"
BLAKE2b\-512 Digest
.IP \fBblake2s256\fR 4
.IX Item "blake2s256"
BLAKE2s\-256 Digest
.IP \fBmd2\fR 4
.IX Item "md2"
MD2 Digest
.IP \fBmd4\fR 4
.IX Item "md4"
MD4 Digest
.IP \fBmd5\fR 4
.IX Item "md5"
MD5 Digest
.IP \fBmdc2\fR 4
.IX Item "mdc2"
MDC2 Digest
.IP \fBrmd160\fR 4
.IX Item "rmd160"
RMD\-160 Digest
.IP \fBsha1\fR 4
.IX Item "sha1"
SHA\-1 Digest
.IP \fBsha224\fR 4
.IX Item "sha224"
SHA\-2 224 Digest
.IP \fBsha256\fR 4
.IX Item "sha256"
SHA\-2 256 Digest
.IP \fBsha384\fR 4
.IX Item "sha384"
SHA\-2 384 Digest
.IP \fBsha512\fR 4
.IX Item "sha512"
SHA\-2 512 Digest
.IP \fBsha3\-224\fR 4
.IX Item "sha3-224"
SHA\-3 224 Digest
.IP \fBsha3\-256\fR 4
.IX Item "sha3-256"
SHA\-3 256 Digest
.IP \fBsha3\-384\fR 4
.IX Item "sha3-384"
SHA\-3 384 Digest
.IP \fBsha3\-512\fR 4
.IX Item "sha3-512"
SHA\-3 512 Digest
.IP \fBshake128\fR 4
.IX Item "shake128"
SHA\-3 SHAKE128 Digest
.IP \fBshake256\fR 4
.IX Item "shake256"
SHA\-3 SHAKE256 Digest
.IP \fBsm3\fR 4
.IX Item "sm3"
SM3 Digest
.SS "Encryption, Decryption, and Encoding Commands"
.IX Subsection "Encryption, Decryption, and Encoding Commands"
The following aliases provide convenient access to the most used encodings
and ciphers.
.PP
Depending on how OpenSSL was configured and built, not all ciphers listed
here may be present. See \fBopenssl\-enc\fR\|(1) for more information.
.IP "\fBaes128\fR, \fBaes\-128\-cbc\fR, \fBaes\-128\-cfb\fR, \fBaes\-128\-ctr\fR, \fBaes\-128\-ecb\fR, \fBaes\-128\-ofb\fR" 4
.IX Item "aes128, aes-128-cbc, aes-128-cfb, aes-128-ctr, aes-128-ecb, aes-128-ofb"
AES\-128 Cipher
.IP "\fBaes192\fR, \fBaes\-192\-cbc\fR, \fBaes\-192\-cfb\fR, \fBaes\-192\-ctr\fR, \fBaes\-192\-ecb\fR, \fBaes\-192\-ofb\fR" 4
.IX Item "aes192, aes-192-cbc, aes-192-cfb, aes-192-ctr, aes-192-ecb, aes-192-ofb"
AES\-192 Cipher
.IP "\fBaes256\fR, \fBaes\-256\-cbc\fR, \fBaes\-256\-cfb\fR, \fBaes\-256\-ctr\fR, \fBaes\-256\-ecb\fR, \fBaes\-256\-ofb\fR" 4
.IX Item "aes256, aes-256-cbc, aes-256-cfb, aes-256-ctr, aes-256-ecb, aes-256-ofb"
AES\-256 Cipher
.IP "\fBaria128\fR, \fBaria\-128\-cbc\fR, \fBaria\-128\-cfb\fR, \fBaria\-128\-ctr\fR, \fBaria\-128\-ecb\fR, \fBaria\-128\-ofb\fR" 4
.IX Item "aria128, aria-128-cbc, aria-128-cfb, aria-128-ctr, aria-128-ecb, aria-128-ofb"
Aria\-128 Cipher
.IP "\fBaria192\fR, \fBaria\-192\-cbc\fR, \fBaria\-192\-cfb\fR, \fBaria\-192\-ctr\fR, \fBaria\-192\-ecb\fR, \fBaria\-192\-ofb\fR" 4
.IX Item "aria192, aria-192-cbc, aria-192-cfb, aria-192-ctr, aria-192-ecb, aria-192-ofb"
Aria\-192 Cipher
.IP "\fBaria256\fR, \fBaria\-256\-cbc\fR, \fBaria\-256\-cfb\fR, \fBaria\-256\-ctr\fR, \fBaria\-256\-ecb\fR, \fBaria\-256\-ofb\fR" 4
.IX Item "aria256, aria-256-cbc, aria-256-cfb, aria-256-ctr, aria-256-ecb, aria-256-ofb"
Aria\-256 Cipher
.IP \fBbase64\fR 4
.IX Item "base64"
Base64 Encoding
.IP "\fBbf\fR, \fBbf-cbc\fR, \fBbf-cfb\fR, \fBbf-ecb\fR, \fBbf-ofb\fR" 4
.IX Item "bf, bf-cbc, bf-cfb, bf-ecb, bf-ofb"
Blowfish Cipher
.IP "\fBcamellia128\fR, \fBcamellia\-128\-cbc\fR, \fBcamellia\-128\-cfb\fR, \fBcamellia\-128\-ctr\fR, \fBcamellia\-128\-ecb\fR, \fBcamellia\-128\-ofb\fR" 4
.IX Item "camellia128, camellia-128-cbc, camellia-128-cfb, camellia-128-ctr, camellia-128-ecb, camellia-128-ofb"
Camellia\-128 Cipher
.IP "\fBcamellia192\fR, \fBcamellia\-192\-cbc\fR, \fBcamellia\-192\-cfb\fR, \fBcamellia\-192\-ctr\fR, \fBcamellia\-192\-ecb\fR, \fBcamellia\-192\-ofb\fR" 4
.IX Item "camellia192, camellia-192-cbc, camellia-192-cfb, camellia-192-ctr, camellia-192-ecb, camellia-192-ofb"
Camellia\-192 Cipher
.IP "\fBcamellia256\fR, \fBcamellia\-256\-cbc\fR, \fBcamellia\-256\-cfb\fR, \fBcamellia\-256\-ctr\fR, \fBcamellia\-256\-ecb\fR, \fBcamellia\-256\-ofb\fR" 4
.IX Item "camellia256, camellia-256-cbc, camellia-256-cfb, camellia-256-ctr, camellia-256-ecb, camellia-256-ofb"
Camellia\-256 Cipher
.IP "\fBcast\fR, \fBcast-cbc\fR" 4
.IX Item "cast, cast-cbc"
CAST Cipher
.IP "\fBcast5\-cbc\fR, \fBcast5\-cfb\fR, \fBcast5\-ecb\fR, \fBcast5\-ofb\fR" 4
.IX Item "cast5-cbc, cast5-cfb, cast5-ecb, cast5-ofb"
CAST5 Cipher
.IP \fBchacha20\fR 4
.IX Item "chacha20"
Chacha20 Cipher
.IP "\fBdes\fR, \fBdes-cbc\fR, \fBdes-cfb\fR, \fBdes-ecb\fR, \fBdes-ede\fR, \fBdes-ede-cbc\fR, \fBdes-ede-cfb\fR, \fBdes-ede-ofb\fR, \fBdes-ofb\fR" 4
.IX Item "des, des-cbc, des-cfb, des-ecb, des-ede, des-ede-cbc, des-ede-cfb, des-ede-ofb, des-ofb"
DES Cipher
.IP "\fBdes3\fR, \fBdesx\fR, \fBdes\-ede3\fR, \fBdes\-ede3\-cbc\fR, \fBdes\-ede3\-cfb\fR, \fBdes\-ede3\-ofb\fR" 4
.IX Item "des3, desx, des-ede3, des-ede3-cbc, des-ede3-cfb, des-ede3-ofb"
Triple-DES Cipher
.IP "\fBidea\fR, \fBidea-cbc\fR, \fBidea-cfb\fR, \fBidea-ecb\fR, \fBidea-ofb\fR" 4
.IX Item "idea, idea-cbc, idea-cfb, idea-ecb, idea-ofb"
IDEA Cipher
.IP "\fBrc2\fR, \fBrc2\-cbc\fR, \fBrc2\-cfb\fR, \fBrc2\-ecb\fR, \fBrc2\-ofb\fR" 4
.IX Item "rc2, rc2-cbc, rc2-cfb, rc2-ecb, rc2-ofb"
RC2 Cipher
.IP \fBrc4\fR 4
.IX Item "rc4"
RC4 Cipher
.IP "\fBrc5\fR, \fBrc5\-cbc\fR, \fBrc5\-cfb\fR, \fBrc5\-ecb\fR, \fBrc5\-ofb\fR" 4
.IX Item "rc5, rc5-cbc, rc5-cfb, rc5-ecb, rc5-ofb"
RC5 Cipher
.IP "\fBseed\fR, \fBseed-cbc\fR, \fBseed-cfb\fR, \fBseed-ecb\fR, \fBseed-ofb\fR" 4
.IX Item "seed, seed-cbc, seed-cfb, seed-ecb, seed-ofb"
SEED Cipher
.IP "\fBsm4\fR, \fBsm4\-cbc\fR, \fBsm4\-cfb\fR, \fBsm4\-ctr\fR, \fBsm4\-ecb\fR, \fBsm4\-ofb\fR" 4
.IX Item "sm4, sm4-cbc, sm4-cfb, sm4-ctr, sm4-ecb, sm4-ofb"
SM4 Cipher
.SH OPTIONS
.IX Header "OPTIONS"
Details of which options are available depend on the specific command.
This section describes some common options with common behavior.
.SS "Common Options"
.IX Subsection "Common Options"
.IP \fB\-help\fR 4
.IX Item "-help"
Provides a terse summary of all options.
If an option takes an argument, the "type" of argument is also given.
.IP \fB\-\-\fR 4
.IX Item "--"
This terminates the list of options. It is mostly useful if any filename
parameters start with a minus sign:
.Sp
.Vb 1
\& openssl verify [flags...] \-\- \-cert1.pem...
.Ve
.SS "Format Options"
.IX Subsection "Format Options"
See \fBopenssl\-format\-options\fR\|(1) for manual page.
.SS "Pass Phrase Options"
.IX Subsection "Pass Phrase Options"
See the \fBopenssl\-passphrase\-options\fR\|(1) manual page.
.SS "Random State Options"
.IX Subsection "Random State Options"
Prior to OpenSSL 1.1.1, it was common for applications to store information
about the state of the random-number generator in a file that was loaded
at startup and rewritten upon exit. On modern operating systems, this is
generally no longer necessary as OpenSSL will seed itself from a trusted
entropy source provided by the operating system. These flags are still
supported for special platforms or circumstances that might require them.
.PP
It is generally an error to use the same seed file more than once and
every use of \fB\-rand\fR should be paired with \fB\-writerand\fR.
.IP "\fB\-rand\fR \fIfiles\fR" 4
.IX Item "-rand files"
A file or files containing random data used to seed the random number
generator.
Multiple files can be specified separated by an OS-dependent character.
The separator is \f(CW\*(C`;\*(C'\fR for MS-Windows, \f(CW\*(C`,\*(C'\fR for OpenVMS, and \f(CW\*(C`:\*(C'\fR for
all others. Another way to specify multiple files is to repeat this flag
with different filenames.
.IP "\fB\-writerand\fR \fIfile\fR" 4
.IX Item "-writerand file"
Writes the seed data to the specified \fIfile\fR upon exit.
This file can be used in a subsequent command invocation.
.SS "Certificate Verification Options"
.IX Subsection "Certificate Verification Options"
See the \fBopenssl\-verification\-options\fR\|(1) manual page.
.SS "Name Format Options"
.IX Subsection "Name Format Options"
See the \fBopenssl\-namedisplay\-options\fR\|(1) manual page.
.SS "TLS Version Options"
.IX Subsection "TLS Version Options"
Several commands use SSL, TLS, or DTLS. By default, the commands use TLS and
clients will offer the lowest and highest protocol version they support,
and servers will pick the highest version that the client offers that is also
supported by the server.
.PP
The options below can be used to limit which protocol versions are used,
and whether TCP (SSL and TLS) or UDP (DTLS) is used.
Note that not all protocols and flags may be available, depending on how
OpenSSL was built.
.IP "\fB\-ssl3\fR, \fB\-tls1\fR, \fB\-tls1_1\fR, \fB\-tls1_2\fR, \fB\-tls1_3\fR, \fB\-no_ssl3\fR, \fB\-no_tls1\fR, \fB\-no_tls1_1\fR, \fB\-no_tls1_2\fR, \fB\-no_tls1_3\fR" 4
.IX Item "-ssl3, -tls1, -tls1_1, -tls1_2, -tls1_3, -no_ssl3, -no_tls1, -no_tls1_1, -no_tls1_2, -no_tls1_3"
These options require or disable the use of the specified SSL or TLS protocols.
When a specific TLS version is required, only that version will be offered or
accepted.
Only one specific protocol can be given and it cannot be combined with any of
the \fBno_\fR options.
The \fBno_*\fR options do not work with \fBs_time\fR and \fBciphers\fR commands but work with
\&\fBs_client\fR and \fBs_server\fR commands.
.IP "\fB\-dtls\fR, \fB\-dtls1\fR, \fB\-dtls1_2\fR" 4
.IX Item "-dtls, -dtls1, -dtls1_2"
These options specify to use DTLS instead of TLS.
With \fB\-dtls\fR, clients will negotiate any supported DTLS protocol version.
Use the \fB\-dtls1\fR or \fB\-dtls1_2\fR options to support only DTLS1.0 or DTLS1.2,
respectively.
.SS "Engine Options"
.IX Subsection "Engine Options"
.IP "\fB\-engine\fR \fIid\fR" 4
.IX Item "-engine id"
Load the engine identified by \fIid\fR and use all the methods it implements
(algorithms, key storage, etc.), unless specified otherwise in the
command-specific documentation or it is configured to do so, as described in
"Engine Configuration" in \fBconfig\fR\|(5).
.Sp
The engine will be used for key ids specified with \fB\-key\fR and similar
options when an option like \fB\-keyform engine\fR is given.
.Sp
A special case is the \f(CW\*(C`loader_attic\*(C'\fR engine, which
is meant just for internal OpenSSL testing purposes and
supports loading keys, parameters, certificates, and CRLs from files.
When this engine is used, files with such credentials are read via this engine.
Using the \f(CW\*(C`file:\*(C'\fR schema is optional; a plain file (path) name will do.
.PP
Options specifying keys, like \fB\-key\fR and similar, can use the generic
OpenSSL engine key loading URI scheme \f(CW\*(C`org.openssl.engine:\*(C'\fR to retrieve
private keys and public keys.  The URI syntax is as follows, in simplified
form:
.PP
.Vb 1
\&    org.openssl.engine:{engineid}:{keyid}
.Ve
.PP
Where \f(CW\*(C`{engineid}\*(C'\fR is the identity/name of the engine, and \f(CW\*(C`{keyid}\*(C'\fR is a
key identifier that's acceptable by that engine.  For example, when using an
engine that interfaces against a PKCS#11 implementation, the generic key URI
would be something like this (this happens to be an example for the PKCS#11
engine that's part of OpenSC):
.PP
.Vb 1
\&    \-key org.openssl.engine:pkcs11:label_some\-private\-key
.Ve
.PP
As a third possibility, for engines and providers that have implemented
their own \fBOSSL_STORE_LOADER\fR\|(3), \f(CW\*(C`org.openssl.engine:\*(C'\fR should not be
necessary.  For a PKCS#11 implementation that has implemented such a loader,
the PKCS#11 URI as defined in RFC 7512 should be possible to use directly:
.PP
.Vb 1
\&    \-key pkcs11:object=some\-private\-key;pin\-value=1234
.Ve
.SS "Provider Options"
.IX Subsection "Provider Options"
.IP "\fB\-provider\fR \fIname\fR" 4
.IX Item "-provider name"
Load and initialize the provider identified by \fIname\fR. The \fIname\fR
can be also a path to the provider module. In that case the provider name
will be the specified path and not just the provider module name.
Interpretation of relative paths is platform specific. The configured
"MODULESDIR" path, \fBOPENSSL_MODULES\fR environment variable, or the path
specified by \fB\-provider\-path\fR is prepended to relative paths.
See \fBprovider\fR\|(7) for a more detailed description.
.IP "\fB\-provider\-path\fR \fIpath\fR" 4
.IX Item "-provider-path path"
Specifies the search path that is to be used for looking for providers.
Equivalently, the \fBOPENSSL_MODULES\fR environment variable may be set.
.IP "\fB\-propquery\fR \fIpropq\fR" 4
.IX Item "-propquery propq"
Specifies the \fIproperty query clause\fR to be used when fetching algorithms
from the loaded providers.
See \fBproperty\fR\|(7) for a more detailed description.
.SH ENVIRONMENT
.IX Header "ENVIRONMENT"
The OpenSSL library can be take some configuration parameters from the
environment.  Some of these variables are listed below.  For information
about specific commands, see \fBopenssl\-engine\fR\|(1),
\&\fBopenssl\-rehash\fR\|(1), and \fBtsget\fR\|(1).
.PP
For information about the use of environment variables in configuration,
see "ENVIRONMENT" in \fBconfig\fR\|(5).
.PP
For information about querying or specifying CPU architecture flags, see
\&\fBOPENSSL_ia32cap\fR\|(3), and \fBOPENSSL_s390xcap\fR\|(3).
.PP
For information about all environment variables used by the OpenSSL libraries,
see \fBopenssl\-env\fR\|(7).
.IP \fBOPENSSL_TRACE=\fR\fIname\fR[,...] 4
.IX Item "OPENSSL_TRACE=name[,...]"
Enable tracing output of OpenSSL library, by name.
This output will only make sense if you know OpenSSL internals well.
Also, it might not give you any output at all, depending on how
OpenSSL was built.
.Sp
The value is a comma separated list of names, with the following
available:
.RS 4
.IP \fBTRACE\fR 4
.IX Item "TRACE"
Traces the OpenSSL trace API itself.
.IP \fBINIT\fR 4
.IX Item "INIT"
Traces OpenSSL library initialization and cleanup.
.IP \fBTLS\fR 4
.IX Item "TLS"
Traces the TLS/SSL protocol.
.IP \fBTLS_CIPHER\fR 4
.IX Item "TLS_CIPHER"
Traces the ciphers used by the TLS/SSL protocol.
.IP \fBCONF\fR 4
.IX Item "CONF"
Show details about provider and engine configuration.
.IP \fBENGINE_TABLE\fR 4
.IX Item "ENGINE_TABLE"
The function that is used by RSA, DSA (etc) code to select registered
ENGINEs, cache defaults and functional references (etc), will generate
debugging summaries.
.IP \fBENGINE_REF_COUNT\fR 4
.IX Item "ENGINE_REF_COUNT"
Reference counts in the ENGINE structure will be monitored with a line
of generated for each change.
.IP \fBPKCS5V2\fR 4
.IX Item "PKCS5V2"
Traces PKCS#5 v2 key generation.
.IP \fBPKCS12_KEYGEN\fR 4
.IX Item "PKCS12_KEYGEN"
Traces PKCS#12 key generation.
.IP \fBPKCS12_DECRYPT\fR 4
.IX Item "PKCS12_DECRYPT"
Traces PKCS#12 decryption.
.IP \fBX509V3_POLICY\fR 4
.IX Item "X509V3_POLICY"
Generates the complete policy tree at various points during X.509 v3
policy evaluation.
.IP \fBBN_CTX\fR 4
.IX Item "BN_CTX"
Traces BIGNUM context operations.
.IP \fBCMP\fR 4
.IX Item "CMP"
Traces CMP client and server activity.
.IP \fBSTORE\fR 4
.IX Item "STORE"
Traces STORE operations.
.IP \fBDECODER\fR 4
.IX Item "DECODER"
Traces decoder operations.
.IP \fBENCODER\fR 4
.IX Item "ENCODER"
Traces encoder operations.
.IP \fBREF_COUNT\fR 4
.IX Item "REF_COUNT"
Traces decrementing certain ASN.1 structure references.
.RE
.RS 4
.RE
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBopenssl\-asn1parse\fR\|(1),
\&\fBopenssl\-ca\fR\|(1),
\&\fBopenssl\-ciphers\fR\|(1),
\&\fBopenssl\-cms\fR\|(1),
\&\fBopenssl\-crl\fR\|(1),
\&\fBopenssl\-crl2pkcs7\fR\|(1),
\&\fBopenssl\-dgst\fR\|(1),
\&\fBopenssl\-dhparam\fR\|(1),
\&\fBopenssl\-dsa\fR\|(1),
\&\fBopenssl\-dsaparam\fR\|(1),
\&\fBopenssl\-ec\fR\|(1),
\&\fBopenssl\-ecparam\fR\|(1),
\&\fBopenssl\-enc\fR\|(1),
\&\fBopenssl\-engine\fR\|(1),
\&\fBopenssl\-errstr\fR\|(1),
\&\fBopenssl\-gendsa\fR\|(1),
\&\fBopenssl\-genpkey\fR\|(1),
\&\fBopenssl\-genrsa\fR\|(1),
\&\fBopenssl\-kdf\fR\|(1),
\&\fBopenssl\-list\fR\|(1),
\&\fBopenssl\-mac\fR\|(1),
\&\fBopenssl\-nseq\fR\|(1),
\&\fBopenssl\-ocsp\fR\|(1),
\&\fBopenssl\-passwd\fR\|(1),
\&\fBopenssl\-pkcs12\fR\|(1),
\&\fBopenssl\-pkcs7\fR\|(1),
\&\fBopenssl\-pkcs8\fR\|(1),
\&\fBopenssl\-pkey\fR\|(1),
\&\fBopenssl\-pkeyparam\fR\|(1),
\&\fBopenssl\-pkeyutl\fR\|(1),
\&\fBopenssl\-prime\fR\|(1),
\&\fBopenssl\-rand\fR\|(1),
\&\fBopenssl\-rehash\fR\|(1),
\&\fBopenssl\-req\fR\|(1),
\&\fBopenssl\-rsa\fR\|(1),
\&\fBopenssl\-rsautl\fR\|(1),
\&\fBopenssl\-s_client\fR\|(1),
\&\fBopenssl\-s_server\fR\|(1),
\&\fBopenssl\-s_time\fR\|(1),
\&\fBopenssl\-sess_id\fR\|(1),
\&\fBopenssl\-smime\fR\|(1),
\&\fBopenssl\-speed\fR\|(1),
\&\fBopenssl\-spkac\fR\|(1),
\&\fBopenssl\-srp\fR\|(1),
\&\fBopenssl\-storeutl\fR\|(1),
\&\fBopenssl\-ts\fR\|(1),
\&\fBopenssl\-verify\fR\|(1),
\&\fBopenssl\-version\fR\|(1),
\&\fBopenssl\-x509\fR\|(1),
\&\fBconfig\fR\|(5),
\&\fBcrypto\fR\|(7),
\&\fBopenssl\-env\fR\|(7).
\&\fBssl\fR\|(7),
\&\fBx509v3_config\fR\|(5)
.SH HISTORY
.IX Header "HISTORY"
The \fBlist\fR \-\fIXXX\fR\fB\-algorithms\fR options were added in OpenSSL 1.0.0;
For notes on the availability of other commands, see their individual
manual pages.
.PP
The \fB\-issuer_checks\fR option is deprecated as of OpenSSL 1.1.0 and
is silently ignored.
.PP
The \fB\-xcertform\fR and \fB\-xkeyform\fR options
are obsolete since OpenSSL 3.0 and have no effect.
.PP
The interactive mode, which could be invoked by running \f(CW\*(C`openssl\*(C'\fR
with no further arguments, was removed in OpenSSL 3.0, and running
that program with no arguments is now equivalent to \f(CW\*(C`openssl help\*(C'\fR.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2023 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
