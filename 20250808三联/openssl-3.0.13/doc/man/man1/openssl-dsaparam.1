.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "OPENSSL-DSAPARAM 1ossl"
.TH OPENSSL-DSAPARAM 1ossl 2025-08-08 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
openssl\-dsaparam \- DSA parameter manipulation and generation
.SH SYNOPSIS
.IX Header "SYNOPSIS"
\&\fBopenssl dsaparam\fR
[\fB\-help\fR]
[\fB\-inform\fR \fBDER\fR|\fBPEM\fR]
[\fB\-outform\fR \fBDER\fR|\fBPEM\fR]
[\fB\-in\fR \fIfilename\fR]
[\fB\-out\fR \fIfilename\fR]
[\fB\-noout\fR]
[\fB\-text\fR]
[\fB\-genkey\fR]
[\fB\-verbose\fR]
[\fB\-rand\fR \fIfiles\fR]
[\fB\-writerand\fR \fIfile\fR]
[\fB\-engine\fR \fIid\fR]
[\fB\-provider\fR \fIname\fR]
[\fB\-provider\-path\fR \fIpath\fR]
[\fB\-propquery\fR \fIpropq\fR]
[\fInumbits\fR]
.SH DESCRIPTION
.IX Header "DESCRIPTION"
This command is used to manipulate or generate DSA parameter files.
.PP
DSA parameter generation can be a slow process and as a result the same set of
DSA parameters is often used to generate several distinct keys.
.SH OPTIONS
.IX Header "OPTIONS"
.IP \fB\-help\fR 4
.IX Item "-help"
Print out a usage message.
.IP "\fB\-inform\fR \fBDER\fR|\fBPEM\fR" 4
.IX Item "-inform DER|PEM"
The DSA parameters input format; unspecified by default.
See \fBopenssl\-format\-options\fR\|(1) for details.
.IP "\fB\-outform\fR \fBDER\fR|\fBPEM\fR" 4
.IX Item "-outform DER|PEM"
The DSA parameters output format; the default is \fBPEM\fR.
See \fBopenssl\-format\-options\fR\|(1) for details.
.Sp
Parameters are a sequence of \fBASN.1 INTEGER\fRs: \fBp\fR, \fBq\fR, and \fBg\fR.
This is compatible with RFC 2459 \fBDSS-Parms\fR structure.
.IP "\fB\-in\fR \fIfilename\fR" 4
.IX Item "-in filename"
This specifies the input filename to read parameters from or standard input if
this option is not specified. If the \fInumbits\fR parameter is included then
this option will be ignored.
.IP "\fB\-out\fR \fIfilename\fR" 4
.IX Item "-out filename"
This specifies the output filename parameters to. Standard output is used
if this option is not present. The output filename should \fBnot\fR be the same
as the input filename.
.IP \fB\-noout\fR 4
.IX Item "-noout"
This option inhibits the output of the encoded version of the parameters.
.IP \fB\-text\fR 4
.IX Item "-text"
This option prints out the DSA parameters in human readable form.
.IP \fB\-genkey\fR 4
.IX Item "-genkey"
This option will generate a DSA either using the specified or generated
parameters.
.IP \fB\-verbose\fR 4
.IX Item "-verbose"
Print extra details about the operations being performed.
.IP "\fB\-rand\fR \fIfiles\fR, \fB\-writerand\fR \fIfile\fR" 4
.IX Item "-rand files, -writerand file"
See "Random State Options" in \fBopenssl\fR\|(1) for details.
.IP "\fB\-engine\fR \fIid\fR" 4
.IX Item "-engine id"
See "Engine Options" in \fBopenssl\fR\|(1).
This option is deprecated.
.IP \fInumbits\fR 4
.IX Item "numbits"
This option specifies that a parameter set should be generated of size
\&\fInumbits\fR. It must be the last option. If this option is included then
the input file (if any) is ignored.
.IP "\fB\-provider\fR \fIname\fR" 4
.IX Item "-provider name"
.PD 0
.IP "\fB\-provider\-path\fR \fIpath\fR" 4
.IX Item "-provider-path path"
.IP "\fB\-propquery\fR \fIpropq\fR" 4
.IX Item "-propquery propq"
.PD
See "Provider Options" in \fBopenssl\fR\|(1), \fBprovider\fR\|(7), and \fBproperty\fR\|(7).
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBopenssl\fR\|(1),
\&\fBopenssl\-pkeyparam\fR\|(1),
\&\fBopenssl\-gendsa\fR\|(1),
\&\fBopenssl\-dsa\fR\|(1),
\&\fBopenssl\-genrsa\fR\|(1),
\&\fBopenssl\-rsa\fR\|(1)
.SH HISTORY
.IX Header "HISTORY"
The \fB\-engine\fR option was deprecated in OpenSSL 3.0.
.PP
The \fB\-C\fR option was removed in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
