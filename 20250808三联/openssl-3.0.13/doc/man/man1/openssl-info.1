.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "OPENSSL-INFO 1ossl"
.TH OPENSSL-INFO 1ossl 2025-08-08 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
openssl\-info \- print OpenSSL built\-in information
.SH SYNOPSIS
.IX Header "SYNOPSIS"
\&\fBopenssl info\fR
[\fB\-help\fR]
[\fB\-configdir\fR]
[\fB\-enginesdir\fR]
[\fB\-modulesdir\fR ]
[\fB\-dsoext\fR]
[\fB\-dirnamesep\fR]
[\fB\-listsep\fR]
[\fB\-seeds\fR]
[\fB\-cpusettings\fR]
.SH DESCRIPTION
.IX Header "DESCRIPTION"
This command is used to print out information about OpenSSL.
The information is written exactly as it is with no extra text, which
makes useful for scripts.
.PP
As a consequence, only one item may be chosen for each run of this
command.
.SH OPTIONS
.IX Header "OPTIONS"
.IP \fB\-help\fR 4
.IX Item "-help"
Print out a usage message.
.IP \fB\-configdir\fR 4
.IX Item "-configdir"
Outputs the default directory for OpenSSL configuration files.
.IP \fB\-enginesdir\fR 4
.IX Item "-enginesdir"
Outputs the default directory for OpenSSL engine modules.
.IP \fB\-modulesdir\fR 4
.IX Item "-modulesdir"
Outputs the default directory for OpenSSL dynamically loadable modules
other than engine modules.
.IP \fB\-dsoext\fR 4
.IX Item "-dsoext"
Outputs the DSO extension OpenSSL uses.
.IP \fB\-dirnamesep\fR 4
.IX Item "-dirnamesep"
Outputs the separator character between a directory specification and
a filename.
Note that on some operating systems, this is not the same as the
separator between directory elements.
.IP \fB\-listsep\fR 4
.IX Item "-listsep"
Outputs the OpenSSL list separator character.
This is typically used to construct \f(CW$PATH\fR (\f(CW\*(C`%PATH%\*(C'\fR on Windows)
style lists.
.IP \fB\-seeds\fR 4
.IX Item "-seeds"
Outputs the randomness seed sources.
.IP \fB\-cpusettings\fR 4
.IX Item "-cpusettings"
Outputs the OpenSSL CPU settings info.
.SH HISTORY
.IX Header "HISTORY"
This command was added in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2019\-2020 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
