.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "OPENSSL-CRL2PKCS7 1ossl"
.TH OPENSSL-CRL2PKCS7 1ossl 2025-08-08 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
openssl\-crl2pkcs7 \- Create a PKCS#7 structure from a CRL and certificates
.SH SYNOPSIS
.IX Header "SYNOPSIS"
\&\fBopenssl\fR \fBcrl2pkcs7\fR
[\fB\-help\fR]
[\fB\-inform\fR \fBDER\fR|\fBPEM\fR]
[\fB\-outform\fR \fBDER\fR|\fBPEM\fR]
[\fB\-in\fR \fIfilename\fR]
[\fB\-out\fR \fIfilename\fR]
[\fB\-certfile\fR \fIfilename\fR]
[\fB\-nocrl\fR]
[\fB\-provider\fR \fIname\fR]
[\fB\-provider\-path\fR \fIpath\fR]
[\fB\-propquery\fR \fIpropq\fR]
.SH DESCRIPTION
.IX Header "DESCRIPTION"
This command takes an optional CRL and one or more
certificates and converts them into a PKCS#7 degenerate "certificates
only" structure.
.SH OPTIONS
.IX Header "OPTIONS"
.IP \fB\-help\fR 4
.IX Item "-help"
Print out a usage message.
.IP "\fB\-inform\fR \fBDER\fR|\fBPEM\fR" 4
.IX Item "-inform DER|PEM"
The input format of the CRL; the default is \fBPEM\fR.
See \fBopenssl\-format\-options\fR\|(1) for details.
.IP "\fB\-outform\fR \fBDER\fR|\fBPEM\fR" 4
.IX Item "-outform DER|PEM"
The output format of the PKCS#7 object; the default is \fBPEM\fR.
See \fBopenssl\-format\-options\fR\|(1) for details.
.IP "\fB\-in\fR \fIfilename\fR" 4
.IX Item "-in filename"
This specifies the input filename to read a CRL from or standard input if this
option is not specified.
.IP "\fB\-out\fR \fIfilename\fR" 4
.IX Item "-out filename"
Specifies the output filename to write the PKCS#7 structure to or standard
output by default.
.IP "\fB\-certfile\fR \fIfilename\fR" 4
.IX Item "-certfile filename"
Specifies a filename containing one or more certificates in \fBPEM\fR format.
All certificates in the file will be added to the PKCS#7 structure. This
option can be used more than once to read certificates from multiple
files.
.IP \fB\-nocrl\fR 4
.IX Item "-nocrl"
Normally a CRL is included in the output file. With this option no CRL is
included in the output file and a CRL is not read from the input file.
.IP "\fB\-provider\fR \fIname\fR" 4
.IX Item "-provider name"
.PD 0
.IP "\fB\-provider\-path\fR \fIpath\fR" 4
.IX Item "-provider-path path"
.IP "\fB\-propquery\fR \fIpropq\fR" 4
.IX Item "-propquery propq"
.PD
See "Provider Options" in \fBopenssl\fR\|(1), \fBprovider\fR\|(7), and \fBproperty\fR\|(7).
.SH EXAMPLES
.IX Header "EXAMPLES"
Create a PKCS#7 structure from a certificate and CRL:
.PP
.Vb 1
\& openssl crl2pkcs7 \-in crl.pem \-certfile cert.pem \-out p7.pem
.Ve
.PP
Creates a PKCS#7 structure in DER format with no CRL from several
different certificates:
.PP
.Vb 2
\& openssl crl2pkcs7 \-nocrl \-certfile newcert.pem
\&        \-certfile demoCA/cacert.pem \-outform DER \-out p7.der
.Ve
.SH NOTES
.IX Header "NOTES"
The output file is a PKCS#7 signed data structure containing no signers and
just certificates and an optional CRL.
.PP
This command can be used to send certificates and CAs to Netscape as part of
the certificate enrollment process. This involves sending the DER encoded output
as MIME type application/x\-x509\-user\-cert.
.PP
The \fBPEM\fR encoded form with the header and footer lines removed can be used to
install user certificates and CAs in MSIE using the Xenroll control.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBopenssl\fR\|(1),
\&\fBopenssl\-pkcs7\fR\|(1)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
