.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "OPENSSL-VERIFY 1ossl"
.TH OPENSSL-VERIFY 1ossl 2025-08-08 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
openssl\-verify \- certificate verification command
.SH SYNOPSIS
.IX Header "SYNOPSIS"
\&\fBopenssl\fR \fBverify\fR
[\fB\-help\fR]
[\fB\-CRLfile\fR \fIfilename\fR|\fIuri\fR]
[\fB\-crl_download\fR]
[\fB\-show_chain\fR]
[\fB\-verbose\fR]
[\fB\-trusted\fR \fIfilename\fR|\fIuri\fR]
[\fB\-untrusted\fR \fIfilename\fR|\fIuri\fR]
[\fB\-vfyopt\fR \fInm\fR:\fIv\fR]
[\fB\-nameopt\fR \fIoption\fR]
[\fB\-CAfile\fR \fIfile\fR]
[\fB\-no\-CAfile\fR]
[\fB\-CApath\fR \fIdir\fR]
[\fB\-no\-CApath\fR]
[\fB\-CAstore\fR \fIuri\fR]
[\fB\-no\-CAstore\fR]
[\fB\-engine\fR \fIid\fR]
[\fB\-allow_proxy_certs\fR]
[\fB\-attime\fR \fItimestamp\fR]
[\fB\-no_check_time\fR]
[\fB\-check_ss_sig\fR]
[\fB\-crl_check\fR]
[\fB\-crl_check_all\fR]
[\fB\-explicit_policy\fR]
[\fB\-extended_crl\fR]
[\fB\-ignore_critical\fR]
[\fB\-inhibit_any\fR]
[\fB\-inhibit_map\fR]
[\fB\-partial_chain\fR]
[\fB\-policy\fR \fIarg\fR]
[\fB\-policy_check\fR]
[\fB\-policy_print\fR]
[\fB\-purpose\fR \fIpurpose\fR]
[\fB\-suiteB_128\fR]
[\fB\-suiteB_128_only\fR]
[\fB\-suiteB_192\fR]
[\fB\-trusted_first\fR]
[\fB\-no_alt_chains\fR]
[\fB\-use_deltas\fR]
[\fB\-auth_level\fR \fInum\fR]
[\fB\-verify_depth\fR \fInum\fR]
[\fB\-verify_email\fR \fIemail\fR]
[\fB\-verify_hostname\fR \fIhostname\fR]
[\fB\-verify_ip\fR \fIip\fR]
[\fB\-verify_name\fR \fIname\fR]
[\fB\-x509_strict\fR]
[\fB\-issuer_checks\fR]
[\fB\-provider\fR \fIname\fR]
[\fB\-provider\-path\fR \fIpath\fR]
[\fB\-propquery\fR \fIpropq\fR]
[\fB\-\-\fR]
[\fIcertificate\fR ...]
.SH DESCRIPTION
.IX Header "DESCRIPTION"
This command verifies certificate chains. If a certificate chain has multiple
problems, this program attempts to display all of them.
.SH OPTIONS
.IX Header "OPTIONS"
.IP \fB\-help\fR 4
.IX Item "-help"
Print out a usage message.
.IP "\fB\-CRLfile\fR \fIfilename\fR|\fIuri\fR" 4
.IX Item "-CRLfile filename|uri"
The file or URI should contain one or more CRLs in PEM or DER format.
This option can be specified more than once to include CRLs from multiple
sources.
.IP \fB\-crl_download\fR 4
.IX Item "-crl_download"
Attempt to download CRL information for certificates via their CDP entries.
.IP \fB\-show_chain\fR 4
.IX Item "-show_chain"
Display information about the certificate chain that has been built (if
successful). Certificates in the chain that came from the untrusted list will be
flagged as "untrusted".
.IP \fB\-verbose\fR 4
.IX Item "-verbose"
Print extra information about the operations being performed.
.IP "\fB\-trusted\fR \fIfilename\fR|\fIuri\fR" 4
.IX Item "-trusted filename|uri"
A file or URI of (more or less) trusted certificates.
See \fBopenssl\-verification\-options\fR\|(1) for more information on trust settings.
.Sp
This option can be specified more than once to load certificates from multiple
sources.
.IP "\fB\-untrusted\fR \fIfilename\fR|\fIuri\fR" 4
.IX Item "-untrusted filename|uri"
A file or URI of untrusted certificates to use for chain building.
This option can be specified more than once to load certificates from multiple
sources.
.IP "\fB\-vfyopt\fR \fInm\fR:\fIv\fR" 4
.IX Item "-vfyopt nm:v"
Pass options to the signature algorithm during verify operations.
Names and values of these options are algorithm-specific.
.IP "\fB\-nameopt\fR \fIoption\fR" 4
.IX Item "-nameopt option"
This specifies how the subject or issuer names are displayed.
See \fBopenssl\-namedisplay\-options\fR\|(1) for details.
.IP "\fB\-engine\fR \fIid\fR" 4
.IX Item "-engine id"
See "Engine Options" in \fBopenssl\fR\|(1).
This option is deprecated.
.Sp
To load certificates or CRLs that require engine support, specify the
\&\fB\-engine\fR option before any of the
\&\fB\-trusted\fR, \fB\-untrusted\fR or \fB\-CRLfile\fR options.
.IP "\fB\-CAfile\fR \fIfile\fR, \fB\-no\-CAfile\fR, \fB\-CApath\fR \fIdir\fR, \fB\-no\-CApath\fR, \fB\-CAstore\fR \fIuri\fR, \fB\-no\-CAstore\fR" 4
.IX Item "-CAfile file, -no-CAfile, -CApath dir, -no-CApath, -CAstore uri, -no-CAstore"
See "Trusted Certificate Options" in \fBopenssl\-verification\-options\fR\|(1) for details.
.IP "\fB\-allow_proxy_certs\fR, \fB\-attime\fR, \fB\-no_check_time\fR, \fB\-check_ss_sig\fR, \fB\-crl_check\fR, \fB\-crl_check_all\fR, \fB\-explicit_policy\fR, \fB\-extended_crl\fR, \fB\-ignore_critical\fR, \fB\-inhibit_any\fR, \fB\-inhibit_map\fR, \fB\-no_alt_chains\fR, \fB\-partial_chain\fR, \fB\-policy\fR, \fB\-policy_check\fR, \fB\-policy_print\fR, \fB\-purpose\fR, \fB\-suiteB_128\fR, \fB\-suiteB_128_only\fR, \fB\-suiteB_192\fR, \fB\-trusted_first\fR, \fB\-use_deltas\fR, \fB\-auth_level\fR, \fB\-verify_depth\fR, \fB\-verify_email\fR, \fB\-verify_hostname\fR, \fB\-verify_ip\fR, \fB\-verify_name\fR, \fB\-x509_strict\fR \fB\-issuer_checks\fR" 4
.IX Item "-allow_proxy_certs, -attime, -no_check_time, -check_ss_sig, -crl_check, -crl_check_all, -explicit_policy, -extended_crl, -ignore_critical, -inhibit_any, -inhibit_map, -no_alt_chains, -partial_chain, -policy, -policy_check, -policy_print, -purpose, -suiteB_128, -suiteB_128_only, -suiteB_192, -trusted_first, -use_deltas, -auth_level, -verify_depth, -verify_email, -verify_hostname, -verify_ip, -verify_name, -x509_strict -issuer_checks"
Set various options of certificate chain verification.
See "Verification Options" in \fBopenssl\-verification\-options\fR\|(1) for details.
.IP "\fB\-provider\fR \fIname\fR" 4
.IX Item "-provider name"
.PD 0
.IP "\fB\-provider\-path\fR \fIpath\fR" 4
.IX Item "-provider-path path"
.IP "\fB\-propquery\fR \fIpropq\fR" 4
.IX Item "-propquery propq"
.PD
See "Provider Options" in \fBopenssl\fR\|(1), \fBprovider\fR\|(7), and \fBproperty\fR\|(7).
.IP \fB\-\-\fR 4
.IX Item "--"
Indicates the last option. All arguments following this are assumed to be
certificate files. This is useful if the first certificate filename begins
with a \fB\-\fR.
.IP "\fIcertificate\fR ..." 4
.IX Item "certificate ..."
One or more target certificates to verify, one per file. If no certificates are
given, this command will attempt to read a single certificate from standard
input.
.SH DIAGNOSTICS
.IX Header "DIAGNOSTICS"
When a verify operation fails the output messages can be somewhat cryptic. The
general form of the error message is:
.PP
.Vb 2
\& server.pem: /C=AU/ST=Queensland/O=CryptSoft Pty Ltd/CN=Test CA (1024 bit)
\& error 24 at 1 depth lookup:invalid CA certificate
.Ve
.PP
The first line contains the name of the certificate being verified followed by
the subject name of the certificate. The second line contains the error number
and the depth. The depth is number of the certificate being verified when a
problem was detected starting with zero for the target ("leaf") certificate
itself then 1 for the CA that signed the target certificate and so on.
Finally a textual version of the error number is presented.
.PP
A list of the error codes and messages can be found in
\&\fBX509_STORE_CTX_get_error\fR\|(3); the full list is defined in the header file
\&\fI<openssl/x509_vfy.h>\fR.
.PP
This command ignores many errors, in order to allow all the problems with a
certificate chain to be determined.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBopenssl\-verification\-options\fR\|(1),
\&\fBopenssl\-x509\fR\|(1),
\&\fBossl_store\-file\fR\|(7)
.SH HISTORY
.IX Header "HISTORY"
The \fB\-show_chain\fR option was added in OpenSSL 1.1.0.
.PP
The \fB\-engine option\fR was deprecated in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
