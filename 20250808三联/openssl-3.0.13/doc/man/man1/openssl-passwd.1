.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "OPENSSL-PASSWD 1ossl"
.TH OPENSSL-PASSWD 1ossl 2025-08-08 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
openssl\-passwd \- compute password hashes
.SH SYNOPSIS
.IX Header "SYNOPSIS"
\&\fBopenssl passwd\fR
[\fB\-help\fR]
[\fB\-1\fR]
[\fB\-apr1\fR]
[\fB\-aixmd5\fR]
[\fB\-5\fR]
[\fB\-6\fR]
[\fB\-salt\fR \fIstring\fR]
[\fB\-in\fR \fIfile\fR]
[\fB\-stdin\fR]
[\fB\-noverify\fR]
[\fB\-quiet\fR]
[\fB\-table\fR]
[\fB\-reverse\fR]
[\fB\-rand\fR \fIfiles\fR]
[\fB\-writerand\fR \fIfile\fR]
[\fB\-provider\fR \fIname\fR]
[\fB\-provider\-path\fR \fIpath\fR]
[\fB\-propquery\fR \fIpropq\fR]
[\fIpassword\fR]
.SH DESCRIPTION
.IX Header "DESCRIPTION"
This command computes the hash of a password typed at
run-time or the hash of each password in a list.  The password list is
taken from the named file for option \fB\-in\fR, from stdin for
option \fB\-stdin\fR, or from the command line, or from the terminal otherwise.
.SH OPTIONS
.IX Header "OPTIONS"
.IP \fB\-help\fR 4
.IX Item "-help"
Print out a usage message.
.IP \fB\-1\fR 4
.IX Item "-1"
Use the MD5 based BSD password algorithm \fB1\fR (default).
.IP \fB\-apr1\fR 4
.IX Item "-apr1"
Use the \fBapr1\fR algorithm (Apache variant of the BSD algorithm).
.IP \fB\-aixmd5\fR 4
.IX Item "-aixmd5"
Use the \fBAIX MD5\fR algorithm (AIX variant of the BSD algorithm).
.IP \fB\-5\fR 4
.IX Item "-5"
.PD 0
.IP \fB\-6\fR 4
.IX Item "-6"
.PD
Use the \fBSHA256\fR / \fBSHA512\fR based algorithms defined by Ulrich Drepper.
See <https://www.akkadia.org/drepper/SHA\-crypt.txt>.
.IP "\fB\-salt\fR \fIstring\fR" 4
.IX Item "-salt string"
Use the specified salt.
When reading a password from the terminal, this implies \fB\-noverify\fR.
.IP "\fB\-in\fR \fIfile\fR" 4
.IX Item "-in file"
Read passwords from \fIfile\fR.
.IP \fB\-stdin\fR 4
.IX Item "-stdin"
Read passwords from \fBstdin\fR.
.IP \fB\-noverify\fR 4
.IX Item "-noverify"
Don't verify when reading a password from the terminal.
.IP \fB\-quiet\fR 4
.IX Item "-quiet"
Don't output warnings when passwords given at the command line are truncated.
.IP \fB\-table\fR 4
.IX Item "-table"
In the output list, prepend the cleartext password and a TAB character
to each password hash.
.IP \fB\-reverse\fR 4
.IX Item "-reverse"
When the \fB\-table\fR option is used, reverse the order of cleartext and hash.
.IP "\fB\-rand\fR \fIfiles\fR, \fB\-writerand\fR \fIfile\fR" 4
.IX Item "-rand files, -writerand file"
See "Random State Options" in \fBopenssl\fR\|(1) for details.
.IP "\fB\-provider\fR \fIname\fR" 4
.IX Item "-provider name"
.PD 0
.IP "\fB\-provider\-path\fR \fIpath\fR" 4
.IX Item "-provider-path path"
.IP "\fB\-propquery\fR \fIpropq\fR" 4
.IX Item "-propquery propq"
.PD
See "Provider Options" in \fBopenssl\fR\|(1), \fBprovider\fR\|(7), and \fBproperty\fR\|(7).
.SH EXAMPLES
.IX Header "EXAMPLES"
.Vb 2
\&  % openssl passwd \-1 \-salt xxxxxxxx password
\&  $1$xxxxxxxx$UYCIxa628.9qXjpQCjM4a.
\&
\&  % openssl passwd \-apr1 \-salt xxxxxxxx password
\&  $apr1$xxxxxxxx$dxHfLAsjHkDRmG83UXe8K0
\&
\&  % openssl passwd \-aixmd5 \-salt xxxxxxxx password
\&  xxxxxxxx$8Oaipk/GPKhC64w/YVeFD/
.Ve
.SH HISTORY
.IX Header "HISTORY"
The \fB\-crypt\fR option was removed in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
