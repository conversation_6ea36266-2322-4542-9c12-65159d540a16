.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "OPENSSL-PKEYUTL 1ossl"
.TH OPENSSL-PKEYUTL 1ossl 2025-08-08 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
openssl\-pkeyutl \- public key algorithm command
.SH SYNOPSIS
.IX Header "SYNOPSIS"
\&\fBopenssl\fR \fBpkeyutl\fR
[\fB\-help\fR]
[\fB\-in\fR \fIfile\fR]
[\fB\-rawin\fR]
[\fB\-digest\fR \fIalgorithm\fR]
[\fB\-out\fR \fIfile\fR]
[\fB\-sigfile\fR \fIfile\fR]
[\fB\-inkey\fR \fIfilename\fR|\fIuri\fR]
[\fB\-keyform\fR \fBDER\fR|\fBPEM\fR|\fBP12\fR|\fBENGINE\fR]
[\fB\-passin\fR \fIarg\fR]
[\fB\-peerkey\fR \fIfile\fR]
[\fB\-peerform\fR \fBDER\fR|\fBPEM\fR|\fBP12\fR|\fBENGINE\fR]
[\fB\-pubin\fR]
[\fB\-certin\fR]
[\fB\-rev\fR]
[\fB\-sign\fR]
[\fB\-verify\fR]
[\fB\-verifyrecover\fR]
[\fB\-encrypt\fR]
[\fB\-decrypt\fR]
[\fB\-derive\fR]
[\fB\-kdf\fR \fIalgorithm\fR]
[\fB\-kdflen\fR \fIlength\fR]
[\fB\-pkeyopt\fR \fIopt\fR:\fIvalue\fR]
[\fB\-pkeyopt_passin\fR \fIopt\fR[:\fIpassarg\fR]]
[\fB\-hexdump\fR]
[\fB\-asn1parse\fR]
[\fB\-engine\fR \fIid\fR]
[\fB\-engine_impl\fR]
[\fB\-rand\fR \fIfiles\fR]
[\fB\-writerand\fR \fIfile\fR]
[\fB\-provider\fR \fIname\fR]
[\fB\-provider\-path\fR \fIpath\fR]
[\fB\-propquery\fR \fIpropq\fR]
[\fB\-config\fR \fIconfigfile\fR]
.SH DESCRIPTION
.IX Header "DESCRIPTION"
This command can be used to perform low-level public key
operations using any supported algorithm.
.SH OPTIONS
.IX Header "OPTIONS"
.IP \fB\-help\fR 4
.IX Item "-help"
Print out a usage message.
.IP "\fB\-in\fR \fIfilename\fR" 4
.IX Item "-in filename"
This specifies the input filename to read data from or standard input
if this option is not specified.
.IP \fB\-rawin\fR 4
.IX Item "-rawin"
This indicates that the input data is raw data, which is not hashed by any
message digest algorithm. The user can specify a digest algorithm by using
the \fB\-digest\fR option. This option can only be used with \fB\-sign\fR and
\&\fB\-verify\fR and must be used with the Ed25519 and Ed448 algorithms.
.IP "\fB\-digest\fR \fIalgorithm\fR" 4
.IX Item "-digest algorithm"
This specifies the digest algorithm which is used to hash the input data before
signing or verifying it with the input key. This option could be omitted if the
signature algorithm does not require one (for instance, EdDSA). If this option
is omitted but the signature algorithm requires one, a default value will be
used. For signature algorithms like RSA, DSA and ECDSA, SHA\-256 will be the
default digest algorithm. For SM2, it will be SM3. If this option is present,
then the \fB\-rawin\fR option must be also specified.
.IP "\fB\-out\fR \fIfilename\fR" 4
.IX Item "-out filename"
Specifies the output filename to write to or standard output by
default.
.IP "\fB\-sigfile\fR \fIfile\fR" 4
.IX Item "-sigfile file"
Signature file, required for \fB\-verify\fR operations only
.IP "\fB\-inkey\fR \fIfilename\fR|\fIuri\fR" 4
.IX Item "-inkey filename|uri"
The input key, by default it should be a private key.
.IP "\fB\-keyform\fR \fBDER\fR|\fBPEM\fR|\fBP12\fR|\fBENGINE\fR" 4
.IX Item "-keyform DER|PEM|P12|ENGINE"
The key format; unspecified by default.
See \fBopenssl\-format\-options\fR\|(1) for details.
.IP "\fB\-passin\fR \fIarg\fR" 4
.IX Item "-passin arg"
The input key password source. For more information about the format of \fIarg\fR
see \fBopenssl\-passphrase\-options\fR\|(1).
.IP "\fB\-peerkey\fR \fIfile\fR" 4
.IX Item "-peerkey file"
The peer key file, used by key derivation (agreement) operations.
.IP "\fB\-peerform\fR \fBDER\fR|\fBPEM\fR|\fBP12\fR|\fBENGINE\fR" 4
.IX Item "-peerform DER|PEM|P12|ENGINE"
The peer key format; unspecified by default.
See \fBopenssl\-format\-options\fR\|(1) for details.
.IP \fB\-pubin\fR 4
.IX Item "-pubin"
The input file is a public key.
.IP \fB\-certin\fR 4
.IX Item "-certin"
The input is a certificate containing a public key.
.IP \fB\-rev\fR 4
.IX Item "-rev"
Reverse the order of the input buffer. This is useful for some libraries
(such as CryptoAPI) which represent the buffer in little endian format.
.IP \fB\-sign\fR 4
.IX Item "-sign"
Sign the input data (which must be a hash) and output the signed result. This
requires a private key.
.IP \fB\-verify\fR 4
.IX Item "-verify"
Verify the input data (which must be a hash) against the signature file and
indicate if the verification succeeded or failed.
.IP \fB\-verifyrecover\fR 4
.IX Item "-verifyrecover"
Verify the input data (which must be a hash) and output the recovered data.
.IP \fB\-encrypt\fR 4
.IX Item "-encrypt"
Encrypt the input data using a public key.
.IP \fB\-decrypt\fR 4
.IX Item "-decrypt"
Decrypt the input data using a private key.
.IP \fB\-derive\fR 4
.IX Item "-derive"
Derive a shared secret using the peer key.
.IP "\fB\-kdf\fR \fIalgorithm\fR" 4
.IX Item "-kdf algorithm"
Use key derivation function \fIalgorithm\fR.  The supported algorithms are
at present \fBTLS1\-PRF\fR and \fBHKDF\fR.
Note: additional parameters and the KDF output length will normally have to be
set for this to work.
See \fBEVP_PKEY_CTX_set_hkdf_md\fR\|(3) and \fBEVP_PKEY_CTX_set_tls1_prf_md\fR\|(3)
for the supported string parameters of each algorithm.
.IP "\fB\-kdflen\fR \fIlength\fR" 4
.IX Item "-kdflen length"
Set the output length for KDF.
.IP "\fB\-pkeyopt\fR \fIopt\fR:\fIvalue\fR" 4
.IX Item "-pkeyopt opt:value"
Public key options specified as opt:value. See NOTES below for more details.
.IP "\fB\-pkeyopt_passin\fR \fIopt\fR[:\fIpassarg\fR]" 4
.IX Item "-pkeyopt_passin opt[:passarg]"
Allows reading a public key option \fIopt\fR from stdin or a password source.
If only \fIopt\fR is specified, the user will be prompted to enter a password on
stdin.  Alternatively, \fIpassarg\fR can be specified which can be any value
supported by \fBopenssl\-passphrase\-options\fR\|(1).
.IP \fB\-hexdump\fR 4
.IX Item "-hexdump"
hex dump the output data.
.IP \fB\-asn1parse\fR 4
.IX Item "-asn1parse"
Parse the ASN.1 output data, this is useful when combined with the
\&\fB\-verifyrecover\fR option when an ASN1 structure is signed.
.IP "\fB\-engine\fR \fIid\fR" 4
.IX Item "-engine id"
See "Engine Options" in \fBopenssl\fR\|(1).
This option is deprecated.
.IP \fB\-engine_impl\fR 4
.IX Item "-engine_impl"
When used with the \fB\-engine\fR option, it specifies to also use
engine \fIid\fR for crypto operations.
.IP "\fB\-rand\fR \fIfiles\fR, \fB\-writerand\fR \fIfile\fR" 4
.IX Item "-rand files, -writerand file"
See "Random State Options" in \fBopenssl\fR\|(1) for details.
.IP "\fB\-provider\fR \fIname\fR" 4
.IX Item "-provider name"
.PD 0
.IP "\fB\-provider\-path\fR \fIpath\fR" 4
.IX Item "-provider-path path"
.IP "\fB\-propquery\fR \fIpropq\fR" 4
.IX Item "-propquery propq"
.PD
See "Provider Options" in \fBopenssl\fR\|(1), \fBprovider\fR\|(7), and \fBproperty\fR\|(7).
.IP "\fB\-config\fR \fIconfigfile\fR" 4
.IX Item "-config configfile"
See "Configuration Option" in \fBopenssl\fR\|(1).
.SH NOTES
.IX Header "NOTES"
The operations and options supported vary according to the key algorithm
and its implementation. The OpenSSL operations and options are indicated below.
.PP
Unless otherwise mentioned all algorithms support the \fBdigest:\fR\fIalg\fR option
which specifies the digest in use for sign, verify and verifyrecover operations.
The value \fIalg\fR should represent a digest name as used in the
\&\fBEVP_get_digestbyname()\fR function for example \fBsha1\fR. This value is not used to
hash the input data. It is used (by some algorithms) for sanity-checking the
lengths of data passed in and for creating the structures that make up the
signature (e.g. \fBDigestInfo\fR in RSASSA PKCS#1 v1.5 signatures).
.PP
This command does not hash the input data (except where \-rawin is used) but
rather it will use the data directly as input to the signature algorithm.
Depending on the key type, signature type, and mode of padding, the maximum
acceptable lengths of input data differ. The signed data can't be longer than
the key modulus with RSA. In case of ECDSA and DSA the data shouldn't be longer
than the field size, otherwise it will be silently truncated to the field size.
In any event the input size must not be larger than the largest supported digest
size.
.PP
In other words, if the value of digest is \fBsha1\fR the input should be the 20
bytes long binary encoding of the SHA\-1 hash function output.
.SH "RSA ALGORITHM"
.IX Header "RSA ALGORITHM"
The RSA algorithm generally supports the encrypt, decrypt, sign,
verify and verifyrecover operations. However, some padding modes
support only a subset of these operations. The following additional
\&\fBpkeyopt\fR values are supported:
.IP \fBrsa_padding_mode:\fR\fImode\fR 4
.IX Item "rsa_padding_mode:mode"
This sets the RSA padding mode. Acceptable values for \fImode\fR are \fBpkcs1\fR for
PKCS#1 padding, \fBnone\fR for no padding, \fBoaep\fR
for \fBOAEP\fR mode, \fBx931\fR for X9.31 mode and \fBpss\fR for PSS.
.Sp
In PKCS#1 padding, if the message digest is not set, then the supplied data is
signed or verified directly instead of using a \fBDigestInfo\fR structure. If a
digest is set, then the \fBDigestInfo\fR structure is used and its length
must correspond to the digest type.
.Sp
For \fBoaep\fR mode only encryption and decryption is supported.
.Sp
For \fBx931\fR if the digest type is set it is used to format the block data
otherwise the first byte is used to specify the X9.31 digest ID. Sign,
verify and verifyrecover are can be performed in this mode.
.Sp
For \fBpss\fR mode only sign and verify are supported and the digest type must be
specified.
.IP \fBrsa_pss_saltlen:\fR\fIlen\fR 4
.IX Item "rsa_pss_saltlen:len"
For \fBpss\fR mode only this option specifies the salt length. Three special
values are supported: \fBdigest\fR sets the salt length to the digest length,
\&\fBmax\fR sets the salt length to the maximum permissible value. When verifying
\&\fBauto\fR causes the salt length to be automatically determined based on the
\&\fBPSS\fR block structure.
.IP \fBrsa_mgf1_md:\fR\fIdigest\fR 4
.IX Item "rsa_mgf1_md:digest"
For PSS and OAEP padding sets the MGF1 digest. If the MGF1 digest is not
explicitly set in PSS mode then the signing digest is used.
.IP \fBrsa_oaep_md:\fR\fIdigest\fR 4
.IX Item "rsa_oaep_md:digest"
Sets the digest used for the OAEP hash function. If not explicitly set then
SHA1 is used.
.SH "RSA-PSS ALGORITHM"
.IX Header "RSA-PSS ALGORITHM"
The RSA-PSS algorithm is a restricted version of the RSA algorithm which only
supports the sign and verify operations with PSS padding. The following
additional \fB\-pkeyopt\fR values are supported:
.IP "\fBrsa_padding_mode:\fR\fImode\fR, \fBrsa_pss_saltlen:\fR\fIlen\fR, \fBrsa_mgf1_md:\fR\fIdigest\fR" 4
.IX Item "rsa_padding_mode:mode, rsa_pss_saltlen:len, rsa_mgf1_md:digest"
These have the same meaning as the \fBRSA\fR algorithm with some additional
restrictions. The padding mode can only be set to \fBpss\fR which is the
default value.
.Sp
If the key has parameter restrictions than the digest, MGF1
digest and salt length are set to the values specified in the parameters.
The digest and MG cannot be changed and the salt length cannot be set to a
value less than the minimum restriction.
.SH "DSA ALGORITHM"
.IX Header "DSA ALGORITHM"
The DSA algorithm supports signing and verification operations only. Currently
there are no additional \fB\-pkeyopt\fR options other than \fBdigest\fR. The SHA1
digest is assumed by default.
.SH "DH ALGORITHM"
.IX Header "DH ALGORITHM"
The DH algorithm only supports the derivation operation and no additional
\&\fB\-pkeyopt\fR options.
.SH "EC ALGORITHM"
.IX Header "EC ALGORITHM"
The EC algorithm supports sign, verify and derive operations. The sign and
verify operations use ECDSA and derive uses ECDH. SHA1 is assumed by default for
the \fB\-pkeyopt\fR \fBdigest\fR option.
.SH "X25519 AND X448 ALGORITHMS"
.IX Header "X25519 AND X448 ALGORITHMS"
The X25519 and X448 algorithms support key derivation only. Currently there are
no additional options.
.SH "ED25519 AND ED448 ALGORITHMS"
.IX Header "ED25519 AND ED448 ALGORITHMS"
These algorithms only support signing and verifying. OpenSSL only implements the
"pure" variants of these algorithms so raw data can be passed directly to them
without hashing them first. The option \fB\-rawin\fR must be used with these
algorithms with no \fB\-digest\fR specified. Additionally OpenSSL only supports
"oneshot" operation with these algorithms. This means that the entire file to
be signed/verified must be read into memory before processing it. Signing or
Verifying very large files should be avoided. Additionally the size of the file
must be known for this to work. If the size of the file cannot be determined
(for example if the input is stdin) then the sign or verify operation will fail.
.SH SM2
.IX Header "SM2"
The SM2 algorithm supports sign, verify, encrypt and decrypt operations. For
the sign and verify operations, SM2 requires an Distinguishing ID string to
be passed in. The following \fB\-pkeyopt\fR value is supported:
.IP \fBdistid:\fR\fIstring\fR 4
.IX Item "distid:string"
This sets the ID string used in SM2 sign or verify operations. While verifying
an SM2 signature, the ID string must be the same one used when signing the data.
Otherwise the verification will fail.
.IP \fBhexdistid:\fR\fIhex_string\fR 4
.IX Item "hexdistid:hex_string"
This sets the ID string used in SM2 sign or verify operations. While verifying
an SM2 signature, the ID string must be the same one used when signing the data.
Otherwise the verification will fail. The ID string provided with this option
should be a valid hexadecimal value.
.SH EXAMPLES
.IX Header "EXAMPLES"
Sign some data using a private key:
.PP
.Vb 1
\& openssl pkeyutl \-sign \-in file \-inkey key.pem \-out sig
.Ve
.PP
Recover the signed data (e.g. if an RSA key is used):
.PP
.Vb 1
\& openssl pkeyutl \-verifyrecover \-in sig \-inkey key.pem
.Ve
.PP
Verify the signature (e.g. a DSA key):
.PP
.Vb 1
\& openssl pkeyutl \-verify \-in file \-sigfile sig \-inkey key.pem
.Ve
.PP
Sign data using a message digest value (this is currently only valid for RSA):
.PP
.Vb 1
\& openssl pkeyutl \-sign \-in file \-inkey key.pem \-out sig \-pkeyopt digest:sha256
.Ve
.PP
Derive a shared secret value:
.PP
.Vb 1
\& openssl pkeyutl \-derive \-inkey key.pem \-peerkey pubkey.pem \-out secret
.Ve
.PP
Hexdump 48 bytes of TLS1 PRF using digest \fBSHA256\fR and shared secret and
seed consisting of the single byte 0xFF:
.PP
.Vb 2
\& openssl pkeyutl \-kdf TLS1\-PRF \-kdflen 48 \-pkeyopt md:SHA256 \e
\&    \-pkeyopt hexsecret:ff \-pkeyopt hexseed:ff \-hexdump
.Ve
.PP
Derive a key using \fBscrypt\fR where the password is read from command line:
.PP
.Vb 2
\& openssl pkeyutl \-kdf scrypt \-kdflen 16 \-pkeyopt_passin pass \e
\&    \-pkeyopt hexsalt:aabbcc \-pkeyopt N:16384 \-pkeyopt r:8 \-pkeyopt p:1
.Ve
.PP
Derive using the same algorithm, but read key from environment variable MYPASS:
.PP
.Vb 2
\& openssl pkeyutl \-kdf scrypt \-kdflen 16 \-pkeyopt_passin pass:env:MYPASS \e
\&    \-pkeyopt hexsalt:aabbcc \-pkeyopt N:16384 \-pkeyopt r:8 \-pkeyopt p:1
.Ve
.PP
Sign some data using an \fBSM2\fR\|(7) private key and a specific ID:
.PP
.Vb 2
\& openssl pkeyutl \-sign \-in file \-inkey sm2.key \-out sig \-rawin \-digest sm3 \e
\&    \-pkeyopt distid:someid
.Ve
.PP
Verify some data using an \fBSM2\fR\|(7) certificate and a specific ID:
.PP
.Vb 2
\& openssl pkeyutl \-verify \-certin \-in file \-inkey sm2.cert \-sigfile sig \e
\&    \-rawin \-digest sm3 \-pkeyopt distid:someid
.Ve
.PP
Decrypt some data using a private key with OAEP padding using SHA256:
.PP
.Vb 2
\& openssl pkeyutl \-decrypt \-in file \-inkey key.pem \-out secret \e
\&    \-pkeyopt rsa_padding_mode:oaep \-pkeyopt rsa_oaep_md:sha256
.Ve
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBopenssl\fR\|(1),
\&\fBopenssl\-genpkey\fR\|(1),
\&\fBopenssl\-pkey\fR\|(1),
\&\fBopenssl\-rsautl\fR\|(1)
\&\fBopenssl\-dgst\fR\|(1),
\&\fBopenssl\-rsa\fR\|(1),
\&\fBopenssl\-genrsa\fR\|(1),
\&\fBopenssl\-kdf\fR\|(1)
\&\fBEVP_PKEY_CTX_set_hkdf_md\fR\|(3),
\&\fBEVP_PKEY_CTX_set_tls1_prf_md\fR\|(3),
.SH HISTORY
.IX Header "HISTORY"
The \fB\-engine\fR option was deprecated in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2006\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
