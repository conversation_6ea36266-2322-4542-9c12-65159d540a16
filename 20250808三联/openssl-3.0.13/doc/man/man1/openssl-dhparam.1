.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "OPENSSL-DHPARAM 1ossl"
.TH OPENSSL-DHPARAM 1ossl 2025-08-08 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
openssl\-dhparam \- DH parameter manipulation and generation
.SH SYNOPSIS
.IX Header "SYNOPSIS"
\&\fBopenssl dhparam\fR
[\fB\-help\fR]
[\fB\-inform\fR \fBDER\fR|\fBPEM\fR]
[\fB\-outform\fR \fBDER\fR|\fBPEM\fR]
[\fB\-in\fR \fIfilename\fR]
[\fB\-out\fR \fIfilename\fR]
[\fB\-dsaparam\fR]
[\fB\-check\fR]
[\fB\-noout\fR]
[\fB\-text\fR]
[\fB\-2\fR]
[\fB\-3\fR]
[\fB\-5\fR]
[\fB\-engine\fR \fIid\fR]
[\fB\-rand\fR \fIfiles\fR]
[\fB\-writerand\fR \fIfile\fR]
[\fB\-provider\fR \fIname\fR]
[\fB\-provider\-path\fR \fIpath\fR]
[\fB\-propquery\fR \fIpropq\fR]
[\fInumbits\fR]
.SH DESCRIPTION
.IX Header "DESCRIPTION"
This command is used to manipulate DH parameter files.
.PP
See "EXAMPLES" in \fBopenssl\-genpkey\fR\|(1) for examples on how to generate
a key using a named safe prime group without generating intermediate
parameters.
.SH OPTIONS
.IX Header "OPTIONS"
.IP \fB\-help\fR 4
.IX Item "-help"
Print out a usage message.
.IP "\fB\-inform\fR \fBDER\fR|\fBPEM\fR, \fB\-outform\fR \fBDER\fR|\fBPEM\fR" 4
.IX Item "-inform DER|PEM, -outform DER|PEM"
The input format and output format; the default is \fBPEM\fR.
The object is compatible with the PKCS#3 \fBDHparameter\fR structure.
See \fBopenssl\-format\-options\fR\|(1) for details.
.IP "\fB\-in\fR \fIfilename\fR" 4
.IX Item "-in filename"
This specifies the input filename to read parameters from or standard input if
this option is not specified.
.IP "\fB\-out\fR \fIfilename\fR" 4
.IX Item "-out filename"
This specifies the output filename parameters to. Standard output is used
if this option is not present. The output filename should \fBnot\fR be the same
as the input filename.
.IP \fB\-dsaparam\fR 4
.IX Item "-dsaparam"
If this option is used, DSA rather than DH parameters are read or created;
they are converted to DH format.  Otherwise, "strong" primes (such
that (p\-1)/2 is also prime) will be used for DH parameter generation.
.Sp
DH parameter generation with the \fB\-dsaparam\fR option is much faster,
and the recommended exponent length is shorter, which makes DH key
exchange more efficient.  Beware that with such DSA-style DH
parameters, a fresh DH key should be created for each use to
avoid small-subgroup attacks that may be possible otherwise.
.IP \fB\-check\fR 4
.IX Item "-check"
Performs numerous checks to see if the supplied parameters are valid and
displays a warning if not.
.IP "\fB\-2\fR, \fB\-3\fR, \fB\-5\fR" 4
.IX Item "-2, -3, -5"
The generator to use, either 2, 3 or 5. If present then the
input file is ignored and parameters are generated instead. If not
present but \fInumbits\fR is present, parameters are generated with the
default generator 2.
.IP \fInumbits\fR 4
.IX Item "numbits"
This option specifies that a parameter set should be generated of size
\&\fInumbits\fR. It must be the last option. If this option is present then
the input file is ignored and parameters are generated instead. If
this option is not present but a generator (\fB\-2\fR, \fB\-3\fR or \fB\-5\fR) is
present, parameters are generated with a default length of 2048 bits.
The minimum length is 512 bits. The maximum length is 10000 bits.
.IP \fB\-noout\fR 4
.IX Item "-noout"
This option inhibits the output of the encoded version of the parameters.
.IP \fB\-text\fR 4
.IX Item "-text"
This option prints out the DH parameters in human readable form.
.IP "\fB\-engine\fR \fIid\fR" 4
.IX Item "-engine id"
See "Engine Options" in \fBopenssl\fR\|(1).
This option is deprecated.
.IP "\fB\-rand\fR \fIfiles\fR, \fB\-writerand\fR \fIfile\fR" 4
.IX Item "-rand files, -writerand file"
See "Random State Options" in \fBopenssl\fR\|(1) for details.
.IP "\fB\-provider\fR \fIname\fR" 4
.IX Item "-provider name"
.PD 0
.IP "\fB\-provider\-path\fR \fIpath\fR" 4
.IX Item "-provider-path path"
.IP "\fB\-propquery\fR \fIpropq\fR" 4
.IX Item "-propquery propq"
.PD
See "Provider Options" in \fBopenssl\fR\|(1), \fBprovider\fR\|(7), and \fBproperty\fR\|(7).
.SH NOTES
.IX Header "NOTES"
This command replaces the \fBdh\fR and \fBgendh\fR commands of previous
releases.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBopenssl\fR\|(1),
\&\fBopenssl\-pkeyparam\fR\|(1),
\&\fBopenssl\-dsaparam\fR\|(1),
\&\fBopenssl\-genpkey\fR\|(1).
.SH HISTORY
.IX Header "HISTORY"
The \fB\-engine\fR option was deprecated in OpenSSL 3.0.
.PP
The \fB\-C\fR option was removed in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2023 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
