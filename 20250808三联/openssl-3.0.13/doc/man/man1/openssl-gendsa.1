.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "OPENSSL-GENDSA 1ossl"
.TH OPENSSL-GENDSA 1ossl 2025-08-08 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
openssl\-gendsa \- generate a DSA private key from a set of parameters
.SH SYNOPSIS
.IX Header "SYNOPSIS"
\&\fBopenssl\fR \fBgendsa\fR
[\fB\-help\fR]
[\fB\-out\fR \fIfilename\fR]
[\fB\-passout\fR \fIarg\fR]
[\fB\-aes128\fR]
[\fB\-aes192\fR]
[\fB\-aes256\fR]
[\fB\-aria128\fR]
[\fB\-aria192\fR]
[\fB\-aria256\fR]
[\fB\-camellia128\fR]
[\fB\-camellia192\fR]
[\fB\-camellia256\fR]
[\fB\-des\fR]
[\fB\-des3\fR]
[\fB\-idea\fR]
[\fB\-verbose\fR]
[\fB\-rand\fR \fIfiles\fR]
[\fB\-writerand\fR \fIfile\fR]
[\fB\-engine\fR \fIid\fR]
[\fB\-provider\fR \fIname\fR]
[\fB\-provider\-path\fR \fIpath\fR]
[\fB\-propquery\fR \fIpropq\fR]
[\fIparamfile\fR]
.SH DESCRIPTION
.IX Header "DESCRIPTION"
This command generates a DSA private key from a DSA parameter file
(which will be typically generated by the \fBopenssl\-dsaparam\fR\|(1) command).
.SH OPTIONS
.IX Header "OPTIONS"
.IP \fB\-help\fR 4
.IX Item "-help"
Print out a usage message.
.IP "\fB\-out\fR \fIfilename\fR" 4
.IX Item "-out filename"
Output the key to the specified file. If this argument is not specified then
standard output is used.
.IP "\fB\-passout\fR \fIarg\fR" 4
.IX Item "-passout arg"
The passphrase used for the output file.
See \fBopenssl\-passphrase\-options\fR\|(1).
.IP "\fB\-aes128\fR, \fB\-aes192\fR, \fB\-aes256\fR, \fB\-aria128\fR, \fB\-aria192\fR, \fB\-aria256\fR, \fB\-camellia128\fR, \fB\-camellia192\fR, \fB\-camellia256\fR, \fB\-des\fR, \fB\-des3\fR, \fB\-idea\fR" 4
.IX Item "-aes128, -aes192, -aes256, -aria128, -aria192, -aria256, -camellia128, -camellia192, -camellia256, -des, -des3, -idea"
These options encrypt the private key with specified
cipher before outputting it. A pass phrase is prompted for.
If none of these options is specified no encryption is used.
.Sp
Note that all options must be given before the \fIparamfile\fR argument.
Otherwise they are ignored.
.IP \fB\-verbose\fR 4
.IX Item "-verbose"
Print extra details about the operations being performed.
.IP "\fB\-rand\fR \fIfiles\fR, \fB\-writerand\fR \fIfile\fR" 4
.IX Item "-rand files, -writerand file"
See "Random State Options" in \fBopenssl\fR\|(1) for details.
.IP "\fB\-engine\fR \fIid\fR" 4
.IX Item "-engine id"
See "Engine Options" in \fBopenssl\fR\|(1).
This option is deprecated.
.IP \fIparamfile\fR 4
.IX Item "paramfile"
The DSA parameter file to use. The parameters in this file determine
the size of the private key. DSA parameters can be generated and
examined using the \fBopenssl\-dsaparam\fR\|(1) command.
.IP "\fB\-provider\fR \fIname\fR" 4
.IX Item "-provider name"
.PD 0
.IP "\fB\-provider\-path\fR \fIpath\fR" 4
.IX Item "-provider-path path"
.IP "\fB\-propquery\fR \fIpropq\fR" 4
.IX Item "-propquery propq"
.PD
See "Provider Options" in \fBopenssl\fR\|(1), \fBprovider\fR\|(7), and \fBproperty\fR\|(7).
.SH NOTES
.IX Header "NOTES"
DSA key generation is little more than random number generation so it is
much quicker that RSA key generation for example.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBopenssl\fR\|(1),
\&\fBopenssl\-genpkey\fR\|(1),
\&\fBopenssl\-dsaparam\fR\|(1),
\&\fBopenssl\-dsa\fR\|(1),
\&\fBopenssl\-genrsa\fR\|(1),
\&\fBopenssl\-rsa\fR\|(1)
.SH HISTORY
.IX Header "HISTORY"
The \fB\-engine\fR option was deprecated in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2023 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
