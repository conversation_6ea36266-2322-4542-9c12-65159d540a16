.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "OPENSSL-STOREUTL 1ossl"
.TH OPENSSL-STOREUTL 1ossl 2025-08-08 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
openssl\-storeutl \- STORE command
.SH SYNOPSIS
.IX Header "SYNOPSIS"
\&\fBopenssl\fR \fBstoreutl\fR
[\fB\-help\fR]
[\fB\-out\fR \fIfile\fR]
[\fB\-noout\fR]
[\fB\-passin\fR \fIarg\fR]
[\fB\-text\fR \fIarg\fR]
[\fB\-r\fR]
[\fB\-certs\fR]
[\fB\-keys\fR]
[\fB\-crls\fR]
[\fB\-subject\fR \fIarg\fR]
[\fB\-issuer\fR \fIarg\fR]
[\fB\-serial\fR \fIarg\fR]
[\fB\-alias\fR \fIarg\fR]
[\fB\-fingerprint\fR \fIarg\fR]
[\fB\-\fR\f(BIdigest\fR]
[\fB\-engine\fR \fIid\fR]
[\fB\-provider\fR \fIname\fR]
[\fB\-provider\-path\fR \fIpath\fR]
[\fB\-propquery\fR \fIpropq\fR]
\&\fIuri\fR
.SH DESCRIPTION
.IX Header "DESCRIPTION"
This command can be used to display the contents (after
decryption as the case may be) fetched from the given URI.
.SH OPTIONS
.IX Header "OPTIONS"
.IP \fB\-help\fR 4
.IX Item "-help"
Print out a usage message.
.IP "\fB\-out\fR \fIfilename\fR" 4
.IX Item "-out filename"
specifies the output filename to write to or standard output by
default.
.IP \fB\-noout\fR 4
.IX Item "-noout"
this option prevents output of the PEM data.
.IP "\fB\-passin\fR \fIarg\fR" 4
.IX Item "-passin arg"
the key password source. For more information about the format of \fIarg\fR
see \fBopenssl\-passphrase\-options\fR\|(1).
.IP \fB\-text\fR 4
.IX Item "-text"
Prints out the objects in text form, similarly to the \fB\-text\fR output from
\&\fBopenssl\-x509\fR\|(1), \fBopenssl\-pkey\fR\|(1), etc.
.IP \fB\-r\fR 4
.IX Item "-r"
Fetch objects recursively when possible.
.IP \fB\-certs\fR 4
.IX Item "-certs"
.PD 0
.IP \fB\-keys\fR 4
.IX Item "-keys"
.IP \fB\-crls\fR 4
.IX Item "-crls"
.PD
Only select the certificates, keys or CRLs from the given URI.
However, if this URI would return a set of names (URIs), those are always
returned.
.Sp
Note that all options must be given before the \fIuri\fR argument.
Otherwise they are ignored.
.IP "\fB\-subject\fR \fIarg\fR" 4
.IX Item "-subject arg"
Search for an object having the subject name \fIarg\fR.
.Sp
The arg must be formatted as \f(CW\*(C`/type0=value0/type1=value1/type2=...\*(C'\fR.
Special characters may be escaped by \f(CW\*(C`\e\*(C'\fR (backslash), whitespace is retained.
Empty values are permitted but are ignored for the search.  That is,
a search with an empty value will have the same effect as not specifying
the type at all.
Giving a single \f(CW\*(C`/\*(C'\fR will lead to an empty sequence of RDNs (a NULL-DN).
Multi-valued RDNs can be formed by placing a \f(CW\*(C`+\*(C'\fR character instead of a \f(CW\*(C`/\*(C'\fR
between the AttributeValueAssertions (AVAs) that specify the members of the set.
.Sp
Example:
.Sp
\&\f(CW\*(C`/DC=org/DC=OpenSSL/DC=users/UID=123456+CN=John Doe\*(C'\fR
.IP "\fB\-issuer\fR \fIarg\fR" 4
.IX Item "-issuer arg"
.PD 0
.IP "\fB\-serial\fR \fIarg\fR" 4
.IX Item "-serial arg"
.PD
Search for an object having the given issuer name and serial number.
These two options \fImust\fR be used together.
The issuer arg must be formatted as \f(CW\*(C`/type0=value0/type1=value1/type2=...\*(C'\fR,
characters may be escaped by \e (backslash), no spaces are skipped.
The serial arg may be specified as a decimal value or a hex value if preceded
by \f(CW\*(C`0x\*(C'\fR.
.IP "\fB\-alias\fR \fIarg\fR" 4
.IX Item "-alias arg"
Search for an object having the given alias.
.IP "\fB\-fingerprint\fR \fIarg\fR" 4
.IX Item "-fingerprint arg"
Search for an object having the given fingerprint.
.IP \fB\-\fR\f(BIdigest\fR 4
.IX Item "-digest"
The digest that was used to compute the fingerprint given with \fB\-fingerprint\fR.
.IP "\fB\-engine\fR \fIid\fR" 4
.IX Item "-engine id"
See "Engine Options" in \fBopenssl\fR\|(1).
This option is deprecated.
.IP "\fB\-provider\fR \fIname\fR" 4
.IX Item "-provider name"
.PD 0
.IP "\fB\-provider\-path\fR \fIpath\fR" 4
.IX Item "-provider-path path"
.IP "\fB\-propquery\fR \fIpropq\fR" 4
.IX Item "-propquery propq"
.PD
See "Provider Options" in \fBopenssl\fR\|(1), \fBprovider\fR\|(7), and \fBproperty\fR\|(7).
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBopenssl\fR\|(1)
.SH HISTORY
.IX Header "HISTORY"
This command was added in OpenSSL 1.1.1.
.PP
The \fB\-engine\fR option was deprecated in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2016\-2023 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
