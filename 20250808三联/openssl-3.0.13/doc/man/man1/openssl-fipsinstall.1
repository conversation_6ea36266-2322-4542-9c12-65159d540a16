.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "OPENSSL-FIPSINSTALL 1ossl"
.TH OPENSSL-FIPSINSTALL 1ossl 2025-08-08 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
openssl\-fipsinstall \- perform FIPS configuration installation
.SH SYNOPSIS
.IX Header "SYNOPSIS"
\&\fBopenssl fipsinstall\fR
[\fB\-help\fR]
[\fB\-in\fR \fIconfigfilename\fR]
[\fB\-out\fR \fIconfigfilename\fR]
[\fB\-module\fR \fImodulefilename\fR]
[\fB\-provider_name\fR \fIprovidername\fR]
[\fB\-section_name\fR \fIsectionname\fR]
[\fB\-verify\fR]
[\fB\-mac_name\fR \fImacname\fR]
[\fB\-macopt\fR \fInm\fR:\fIv\fR]
[\fB\-noout\fR]
[\fB\-quiet\fR]
[\fB\-no_conditional_errors\fR]
[\fB\-no_security_checks\fR]
[\fB\-self_test_onload\fR]
[\fB\-corrupt_desc\fR \fIselftest_description\fR]
[\fB\-corrupt_type\fR \fIselftest_type\fR]
[\fB\-config\fR \fIparent_config\fR]
.SH DESCRIPTION
.IX Header "DESCRIPTION"
This command is used to generate a FIPS module configuration file.
This configuration file can be used each time a FIPS module is loaded
in order to pass data to the FIPS module self tests. The FIPS module always
verifies its MAC, but optionally only needs to run the KAT's once,
at installation.
.PP
The generated configuration file consists of:
.IP "\- A MAC of the FIPS module file." 4
.IX Item "- A MAC of the FIPS module file."
.PD 0
.IP "\- A test status indicator." 4
.IX Item "- A test status indicator."
.PD
This indicates if the Known Answer Self Tests (KAT's) have successfully run.
.IP "\- A MAC of the status indicator." 4
.IX Item "- A MAC of the status indicator."
.PD 0
.IP "\- A control for conditional self tests errors." 4
.IX Item "- A control for conditional self tests errors."
.PD
By default if a continuous test (e.g a key pair test) fails then the FIPS module
will enter an error state, and no services or cryptographic algorithms will be
able to be accessed after this point.
The default value of '1' will cause the fips module error state to be entered.
If the value is '0' then the module error state will not be entered.
Regardless of whether the error state is entered or not, the current operation
(e.g. key generation) will return an error. The user is responsible for retrying
the operation if the module error state is not entered.
.IP "\- A control to indicate whether run-time security checks are done." 4
.IX Item "- A control to indicate whether run-time security checks are done."
This indicates if run-time checks related to enforcement of security parameters
such as minimum security strength of keys and approved curve names are used.
The default value of '1' will perform the checks.
If the value is '0' the checks are not performed and FIPS compliance must
be done by procedures documented in the relevant Security Policy.
.PP
This file is described in \fBfips_config\fR\|(5).
.SH OPTIONS
.IX Header "OPTIONS"
.IP \fB\-help\fR 4
.IX Item "-help"
Print a usage message.
.IP "\fB\-module\fR \fIfilename\fR" 4
.IX Item "-module filename"
Filename of the FIPS module to perform an integrity check on.
The path provided in the filename is used to load the module when it is
activated, and this overrides the environment variable \fBOPENSSL_MODULES\fR.
.IP "\fB\-out\fR \fIconfigfilename\fR" 4
.IX Item "-out configfilename"
Filename to output the configuration data to; the default is standard output.
.IP "\fB\-in\fR \fIconfigfilename\fR" 4
.IX Item "-in configfilename"
Input filename to load configuration data from.
Must be used if the \fB\-verify\fR option is specified.
.IP \fB\-verify\fR 4
.IX Item "-verify"
Verify that the input configuration file contains the correct information.
.IP "\fB\-provider_name\fR \fIprovidername\fR" 4
.IX Item "-provider_name providername"
Name of the provider inside the configuration file.
The default value is \f(CW\*(C`fips\*(C'\fR.
.IP "\fB\-section_name\fR \fIsectionname\fR" 4
.IX Item "-section_name sectionname"
Name of the section inside the configuration file.
The default value is \f(CW\*(C`fips_sect\*(C'\fR.
.IP "\fB\-mac_name\fR \fIname\fR" 4
.IX Item "-mac_name name"
Specifies the name of a supported MAC algorithm which will be used.
The MAC mechanisms that are available will depend on the options
used when building OpenSSL.
To see the list of supported MAC's use the command
\&\f(CW\*(C`openssl list \-mac\-algorithms\*(C'\fR.  The default is \fBHMAC\fR.
.IP "\fB\-macopt\fR \fInm\fR:\fIv\fR" 4
.IX Item "-macopt nm:v"
Passes options to the MAC algorithm.
A comprehensive list of controls can be found in the EVP_MAC implementation
documentation.
Common control strings used for this command are:
.RS 4
.IP \fBkey\fR:\fIstring\fR 4
.IX Item "key:string"
Specifies the MAC key as an alphanumeric string (use if the key contains
printable characters only).
The string length must conform to any restrictions of the MAC algorithm.
A key must be specified for every MAC algorithm.
If no key is provided, the default that was specified when OpenSSL was
configured is used.
.IP \fBhexkey\fR:\fIstring\fR 4
.IX Item "hexkey:string"
Specifies the MAC key in hexadecimal form (two hex digits per byte).
The key length must conform to any restrictions of the MAC algorithm.
A key must be specified for every MAC algorithm.
If no key is provided, the default that was specified when OpenSSL was
configured is used.
.IP \fBdigest\fR:\fIstring\fR 4
.IX Item "digest:string"
Used by HMAC as an alphanumeric string (use if the key contains printable
characters only).
The string length must conform to any restrictions of the MAC algorithm.
To see the list of supported digests, use the command
\&\f(CW\*(C`openssl list \-digest\-commands\*(C'\fR.
The default digest is SHA\-256.
.RE
.RS 4
.RE
.IP \fB\-noout\fR 4
.IX Item "-noout"
Disable logging of the self tests.
.IP \fB\-no_conditional_errors\fR 4
.IX Item "-no_conditional_errors"
Configure the module to not enter an error state if a conditional self test
fails as described above.
.IP \fB\-no_security_checks\fR 4
.IX Item "-no_security_checks"
Configure the module to not perform run-time security checks as described above.
.IP \fB\-self_test_onload\fR 4
.IX Item "-self_test_onload"
Do not write the two fields related to the "test status indicator" and
"MAC status indicator" to the output configuration file. Without these fields
the self tests KATS will run each time the module is loaded. This option could be
used for cross compiling, since the self tests need to run at least once on each
target machine. Once the self tests have run on the target machine the user
could possibly then add the 2 fields into the configuration using some other
mechanism.
.IP \fB\-quiet\fR 4
.IX Item "-quiet"
Do not output pass/fail messages. Implies \fB\-noout\fR.
.IP "\fB\-corrupt_desc\fR \fIselftest_description\fR, \fB\-corrupt_type\fR \fIselftest_type\fR" 4
.IX Item "-corrupt_desc selftest_description, -corrupt_type selftest_type"
The corrupt options can be used to test failure of one or more self tests by
name.
Either option or both may be used to select the tests to corrupt.
Refer to the entries for \fBst-desc\fR and \fBst-type\fR in \fBOSSL_PROVIDER\-FIPS\fR\|(7) for
values that can be used.
.IP "\fB\-config\fR \fIparent_config\fR" 4
.IX Item "-config parent_config"
Test that a FIPS provider can be loaded from the specified configuration file.
A previous call to this application needs to generate the extra configuration
data that is included by the base \f(CW\*(C`parent_config\*(C'\fR configuration file.
See \fBconfig\fR\|(5) for further information on how to set up a provider section.
All other options are ignored if '\-config' is used.
.SH NOTES
.IX Header "NOTES"
Self tests results are logged by default if the options \fB\-quiet\fR and \fB\-noout\fR
are not specified, or if either of the options \fB\-corrupt_desc\fR or
\&\fB\-corrupt_type\fR are used.
If the base configuration file is set up to autoload the fips module, then the
fips module will be loaded and self tested BEFORE the fipsinstall application
has a chance to set up its own self test callback. As a result of this the self
test output and the options \fB\-corrupt_desc\fR and \fB\-corrupt_type\fR will be ignored.
For normal usage the base configuration file should use the default provider
when generating the fips configuration file.
.SH EXAMPLES
.IX Header "EXAMPLES"
Calculate the mac of a FIPS module \fIfips.so\fR and run a FIPS self test
for the module, and save the \fIfips.cnf\fR configuration file:
.PP
.Vb 1
\& openssl fipsinstall \-module ./fips.so \-out fips.cnf \-provider_name fips
.Ve
.PP
Verify that the configuration file \fIfips.cnf\fR contains the correct info:
.PP
.Vb 1
\& openssl fipsinstall \-module ./fips.so \-in fips.cnf  \-provider_name fips \-verify
.Ve
.PP
Corrupt any self tests which have the description \f(CW\*(C`SHA1\*(C'\fR:
.PP
.Vb 2
\& openssl fipsinstall \-module ./fips.so \-out fips.cnf \-provider_name fips \e
\&         \-corrupt_desc \*(AqSHA1\*(Aq
.Ve
.PP
Validate that the fips module can be loaded from a base configuration file:
.PP
.Vb 3
\& export OPENSSL_CONF_INCLUDE=<path of configuration files>
\& export OPENSSL_MODULES=<provider\-path>
\& openssl fipsinstall \-config\*(Aq \*(Aqdefault.cnf\*(Aq
.Ve
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBconfig\fR\|(5),
\&\fBfips_config\fR\|(5),
\&\fBOSSL_PROVIDER\-FIPS\fR\|(7),
\&\fBEVP_MAC\fR\|(3)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2019\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
