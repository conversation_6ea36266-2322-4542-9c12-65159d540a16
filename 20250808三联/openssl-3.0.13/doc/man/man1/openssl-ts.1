.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "OPENSSL-TS 1ossl"
.TH OPENSSL-TS 1ossl 2025-08-08 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
openssl\-ts \- Time Stamping Authority command
.SH SYNOPSIS
.IX Header "SYNOPSIS"
\&\fBopenssl\fR \fBts\fR
\&\fB\-help\fR
.PP
\&\fBopenssl\fR \fBts\fR
\&\fB\-query\fR
[\fB\-config\fR \fIconfigfile\fR]
[\fB\-data\fR \fIfile_to_hash\fR]
[\fB\-digest\fR \fIdigest_bytes\fR]
[\fB\-\fR\f(BIdigest\fR]
[\fB\-tspolicy\fR \fIobject_id\fR]
[\fB\-no_nonce\fR]
[\fB\-cert\fR]
[\fB\-in\fR \fIrequest.tsq\fR]
[\fB\-out\fR \fIrequest.tsq\fR]
[\fB\-text\fR]
[\fB\-rand\fR \fIfiles\fR]
[\fB\-writerand\fR \fIfile\fR]
[\fB\-provider\fR \fIname\fR]
[\fB\-provider\-path\fR \fIpath\fR]
[\fB\-propquery\fR \fIpropq\fR]
.PP
\&\fBopenssl\fR \fBts\fR
\&\fB\-reply\fR
[\fB\-config\fR \fIconfigfile\fR]
[\fB\-section\fR \fItsa_section\fR]
[\fB\-queryfile\fR \fIrequest.tsq\fR]
[\fB\-passin\fR \fIpassword_src\fR]
[\fB\-signer\fR \fItsa_cert.pem\fR]
[\fB\-inkey\fR \fIfilename\fR|\fIuri\fR]
[\fB\-\fR\f(BIdigest\fR]
[\fB\-chain\fR \fIcerts_file.pem\fR]
[\fB\-tspolicy\fR \fIobject_id\fR]
[\fB\-in\fR \fIresponse.tsr\fR]
[\fB\-token_in\fR]
[\fB\-out\fR \fIresponse.tsr\fR]
[\fB\-token_out\fR]
[\fB\-text\fR]
[\fB\-engine\fR \fIid\fR]
[\fB\-provider\fR \fIname\fR]
[\fB\-provider\-path\fR \fIpath\fR]
[\fB\-propquery\fR \fIpropq\fR]
.PP
\&\fBopenssl\fR \fBts\fR
\&\fB\-verify\fR
[\fB\-data\fR \fIfile_to_hash\fR]
[\fB\-digest\fR \fIdigest_bytes\fR]
[\fB\-queryfile\fR \fIrequest.tsq\fR]
[\fB\-in\fR \fIresponse.tsr\fR]
[\fB\-token_in\fR]
[\fB\-untrusted\fR \fIfiles\fR|\fIuris\fR]
[\fB\-CAfile\fR \fIfile\fR]
[\fB\-CApath\fR \fIdir\fR]
[\fB\-CAstore\fR \fIuri\fR]
[\fB\-allow_proxy_certs\fR]
[\fB\-attime\fR \fItimestamp\fR]
[\fB\-no_check_time\fR]
[\fB\-check_ss_sig\fR]
[\fB\-crl_check\fR]
[\fB\-crl_check_all\fR]
[\fB\-explicit_policy\fR]
[\fB\-extended_crl\fR]
[\fB\-ignore_critical\fR]
[\fB\-inhibit_any\fR]
[\fB\-inhibit_map\fR]
[\fB\-partial_chain\fR]
[\fB\-policy\fR \fIarg\fR]
[\fB\-policy_check\fR]
[\fB\-policy_print\fR]
[\fB\-purpose\fR \fIpurpose\fR]
[\fB\-suiteB_128\fR]
[\fB\-suiteB_128_only\fR]
[\fB\-suiteB_192\fR]
[\fB\-trusted_first\fR]
[\fB\-no_alt_chains\fR]
[\fB\-use_deltas\fR]
[\fB\-auth_level\fR \fInum\fR]
[\fB\-verify_depth\fR \fInum\fR]
[\fB\-verify_email\fR \fIemail\fR]
[\fB\-verify_hostname\fR \fIhostname\fR]
[\fB\-verify_ip\fR \fIip\fR]
[\fB\-verify_name\fR \fIname\fR]
[\fB\-x509_strict\fR]
[\fB\-issuer_checks\fR]
[\fB\-provider\fR \fIname\fR]
[\fB\-provider\-path\fR \fIpath\fR]
[\fB\-propquery\fR \fIpropq\fR]
.SH DESCRIPTION
.IX Header "DESCRIPTION"
This command is a basic Time Stamping Authority (TSA) client and
server application as specified in RFC 3161 (Time-Stamp Protocol, TSP). A
TSA can be part of a PKI deployment and its role is to provide long
term proof of the existence of a certain datum before a particular
time. Here is a brief description of the protocol:
.IP 1. 4
The TSA client computes a one-way hash value for a data file and sends
the hash to the TSA.
.IP 2. 4
The TSA attaches the current date and time to the received hash value,
signs them and sends the timestamp token back to the client. By
creating this token the TSA certifies the existence of the original
data file at the time of response generation.
.IP 3. 4
The TSA client receives the timestamp token and verifies the
signature on it. It also checks if the token contains the same hash
value that it had sent to the TSA.
.PP
There is one DER encoded protocol data unit defined for transporting a
timestamp request to the TSA and one for sending the timestamp response
back to the client. This command has three main functions:
creating a timestamp request based on a data file,
creating a timestamp response based on a request, verifying if a
response corresponds to a particular request or a data file.
.PP
There is no support for sending the requests/responses automatically
over HTTP or TCP yet as suggested in RFC 3161. The users must send the
requests either by ftp or e\-mail.
.SH OPTIONS
.IX Header "OPTIONS"
.IP \fB\-help\fR 4
.IX Item "-help"
Print out a usage message.
.IP \fB\-query\fR 4
.IX Item "-query"
Generate a TS query. For details see "Timestamp Request generation".
.IP \fB\-reply\fR 4
.IX Item "-reply"
Generate a TS reply. For details see "Timestamp Response generation".
.IP \fB\-verify\fR 4
.IX Item "-verify"
Verify a TS response. For details see "Timestamp Response verification".
.SS "Timestamp Request generation"
.IX Subsection "Timestamp Request generation"
The \fB\-query\fR command can be used for creating and printing a timestamp
request with the following options:
.IP "\fB\-config\fR \fIconfigfile\fR" 4
.IX Item "-config configfile"
The configuration file to use.
Optional; for a description of the default value,
see "COMMAND SUMMARY" in \fBopenssl\fR\|(1).
.IP "\fB\-data\fR \fIfile_to_hash\fR" 4
.IX Item "-data file_to_hash"
The data file for which the timestamp request needs to be
created. stdin is the default if neither the \fB\-data\fR nor the \fB\-digest\fR
parameter is specified. (Optional)
.IP "\fB\-digest\fR \fIdigest_bytes\fR" 4
.IX Item "-digest digest_bytes"
It is possible to specify the message imprint explicitly without the data
file. The imprint must be specified in a hexadecimal format, two characters
per byte, the bytes optionally separated by colons (e.g. 1A:F6:01:... or
1AF601...). The number of bytes must match the message digest algorithm
in use. (Optional)
.IP \fB\-\fR\f(BIdigest\fR 4
.IX Item "-digest"
The message digest to apply to the data file.
Any digest supported by the \fBopenssl\-dgst\fR\|(1) command can be used.
The default is SHA\-256. (Optional)
.IP "\fB\-tspolicy\fR \fIobject_id\fR" 4
.IX Item "-tspolicy object_id"
The policy that the client expects the TSA to use for creating the
timestamp token. Either the dotted OID notation or OID names defined
in the config file can be used. If no policy is requested the TSA will
use its own default policy. (Optional)
.IP \fB\-no_nonce\fR 4
.IX Item "-no_nonce"
No nonce is specified in the request if this option is
given. Otherwise a 64 bit long pseudo-random none is
included in the request. It is recommended to use nonce to
protect against replay-attacks. (Optional)
.IP \fB\-cert\fR 4
.IX Item "-cert"
The TSA is expected to include its signing certificate in the
response. (Optional)
.IP "\fB\-in\fR \fIrequest.tsq\fR" 4
.IX Item "-in request.tsq"
This option specifies a previously created timestamp request in DER
format that will be printed into the output file. Useful when you need
to examine the content of a request in human-readable
format. (Optional)
.IP "\fB\-out\fR \fIrequest.tsq\fR" 4
.IX Item "-out request.tsq"
Name of the output file to which the request will be written. Default
is stdout. (Optional)
.IP \fB\-text\fR 4
.IX Item "-text"
If this option is specified the output is human-readable text format
instead of DER. (Optional)
.IP "\fB\-rand\fR \fIfiles\fR, \fB\-writerand\fR \fIfile\fR" 4
.IX Item "-rand files, -writerand file"
See "Random State Options" in \fBopenssl\fR\|(1) for details.
.SS "Timestamp Response generation"
.IX Subsection "Timestamp Response generation"
A timestamp response (TimeStampResp) consists of a response status
and the timestamp token itself (ContentInfo), if the token generation was
successful. The \fB\-reply\fR command is for creating a timestamp
response or timestamp token based on a request and printing the
response/token in human-readable format. If \fB\-token_out\fR is not
specified the output is always a timestamp response (TimeStampResp),
otherwise it is a timestamp token (ContentInfo).
.IP "\fB\-config\fR \fIconfigfile\fR" 4
.IX Item "-config configfile"
The configuration file to use.
Optional; for a description of the default value,
see "COMMAND SUMMARY" in \fBopenssl\fR\|(1).
See "CONFIGURATION FILE OPTIONS" for configurable variables.
.IP "\fB\-section\fR \fItsa_section\fR" 4
.IX Item "-section tsa_section"
The name of the config file section containing the settings for the
response generation. If not specified the default TSA section is
used, see "CONFIGURATION FILE OPTIONS" for details. (Optional)
.IP "\fB\-queryfile\fR \fIrequest.tsq\fR" 4
.IX Item "-queryfile request.tsq"
The name of the file containing a DER encoded timestamp request. (Optional)
.IP "\fB\-passin\fR \fIpassword_src\fR" 4
.IX Item "-passin password_src"
Specifies the password source for the private key of the TSA. See
description in \fBopenssl\fR\|(1). (Optional)
.IP "\fB\-signer\fR \fItsa_cert.pem\fR" 4
.IX Item "-signer tsa_cert.pem"
The signer certificate of the TSA in PEM format. The TSA signing
certificate must have exactly one extended key usage assigned to it:
timeStamping. The extended key usage must also be critical, otherwise
the certificate is going to be refused. Overrides the \fBsigner_cert\fR
variable of the config file. (Optional)
.IP "\fB\-inkey\fR \fIfilename\fR|\fIuri\fR" 4
.IX Item "-inkey filename|uri"
The signer private key of the TSA in PEM format. Overrides the
\&\fBsigner_key\fR config file option. (Optional)
.IP \fB\-\fR\f(BIdigest\fR 4
.IX Item "-digest"
Signing digest to use. Overrides the \fBsigner_digest\fR config file
option. (Mandatory unless specified in the config file)
.IP "\fB\-chain\fR \fIcerts_file.pem\fR" 4
.IX Item "-chain certs_file.pem"
The collection of certificates in PEM format that will all
be included in the response in addition to the signer certificate if
the \fB\-cert\fR option was used for the request. This file is supposed to
contain the certificate chain for the signer certificate from its
issuer upwards. The \fB\-reply\fR command does not build a certificate
chain automatically. (Optional)
.IP "\fB\-tspolicy\fR \fIobject_id\fR" 4
.IX Item "-tspolicy object_id"
The default policy to use for the response unless the client
explicitly requires a particular TSA policy. The OID can be specified
either in dotted notation or with its name. Overrides the
\&\fBdefault_policy\fR config file option. (Optional)
.IP "\fB\-in\fR \fIresponse.tsr\fR" 4
.IX Item "-in response.tsr"
Specifies a previously created timestamp response or timestamp token
(if \fB\-token_in\fR is also specified) in DER format that will be written
to the output file. This option does not require a request, it is
useful e.g. when you need to examine the content of a response or
token or you want to extract the timestamp token from a response. If
the input is a token and the output is a timestamp response a default
\&'granted' status info is added to the token. (Optional)
.IP \fB\-token_in\fR 4
.IX Item "-token_in"
This flag can be used together with the \fB\-in\fR option and indicates
that the input is a DER encoded timestamp token (ContentInfo) instead
of a timestamp response (TimeStampResp). (Optional)
.IP "\fB\-out\fR \fIresponse.tsr\fR" 4
.IX Item "-out response.tsr"
The response is written to this file. The format and content of the
file depends on other options (see \fB\-text\fR, \fB\-token_out\fR). The default is
stdout. (Optional)
.IP \fB\-token_out\fR 4
.IX Item "-token_out"
The output is a timestamp token (ContentInfo) instead of timestamp
response (TimeStampResp). (Optional)
.IP \fB\-text\fR 4
.IX Item "-text"
If this option is specified the output is human-readable text format
instead of DER. (Optional)
.IP "\fB\-engine\fR \fIid\fR" 4
.IX Item "-engine id"
See "Engine Options" in \fBopenssl\fR\|(1).
This option is deprecated.
.IP "\fB\-provider\fR \fIname\fR" 4
.IX Item "-provider name"
.PD 0
.IP "\fB\-provider\-path\fR \fIpath\fR" 4
.IX Item "-provider-path path"
.IP "\fB\-propquery\fR \fIpropq\fR" 4
.IX Item "-propquery propq"
.PD
See "Provider Options" in \fBopenssl\fR\|(1), \fBprovider\fR\|(7), and \fBproperty\fR\|(7).
.SS "Timestamp Response verification"
.IX Subsection "Timestamp Response verification"
The \fB\-verify\fR command is for verifying if a timestamp response or
timestamp token is valid and matches a particular timestamp request or
data file. The \fB\-verify\fR command does not use the configuration file.
.IP "\fB\-data\fR \fIfile_to_hash\fR" 4
.IX Item "-data file_to_hash"
The response or token must be verified against file_to_hash. The file
is hashed with the message digest algorithm specified in the token.
The \fB\-digest\fR and \fB\-queryfile\fR options must not be specified with this one.
(Optional)
.IP "\fB\-digest\fR \fIdigest_bytes\fR" 4
.IX Item "-digest digest_bytes"
The response or token must be verified against the message digest specified
with this option. The number of bytes must match the message digest algorithm
specified in the token. The \fB\-data\fR and \fB\-queryfile\fR options must not be
specified with this one. (Optional)
.IP "\fB\-queryfile\fR \fIrequest.tsq\fR" 4
.IX Item "-queryfile request.tsq"
The original timestamp request in DER format. The \fB\-data\fR and \fB\-digest\fR
options must not be specified with this one. (Optional)
.IP "\fB\-in\fR \fIresponse.tsr\fR" 4
.IX Item "-in response.tsr"
The timestamp response that needs to be verified in DER format. (Mandatory)
.IP \fB\-token_in\fR 4
.IX Item "-token_in"
This flag can be used together with the \fB\-in\fR option and indicates
that the input is a DER encoded timestamp token (ContentInfo) instead
of a timestamp response (TimeStampResp). (Optional)
.IP "\fB\-untrusted\fR \fIfiles\fR|\fIuris\fR" 4
.IX Item "-untrusted files|uris"
A set of additional untrusted certificates which may be
needed when building the certificate chain for the TSA's signing certificate.
These do not need to contain the TSA signing certificate and intermediate CA
certificates as far as the response already includes them.
(Optional)
.Sp
Multiple sources may be given, separated by commas and/or whitespace.
Each file may contain multiple certificates.
.IP "\fB\-CAfile\fR \fIfile\fR, \fB\-CApath\fR \fIdir\fR, \fB\-CAstore\fR \fIuri\fR" 4
.IX Item "-CAfile file, -CApath dir, -CAstore uri"
See "Trusted Certificate Options" in \fBopenssl\-verification\-options\fR\|(1) for details.
At least one of \fB\-CAfile\fR, \fB\-CApath\fR or \fB\-CAstore\fR must be specified.
.IP "\fB\-allow_proxy_certs\fR, \fB\-attime\fR, \fB\-no_check_time\fR, \fB\-check_ss_sig\fR, \fB\-crl_check\fR, \fB\-crl_check_all\fR, \fB\-explicit_policy\fR, \fB\-extended_crl\fR, \fB\-ignore_critical\fR, \fB\-inhibit_any\fR, \fB\-inhibit_map\fR, \fB\-no_alt_chains\fR, \fB\-partial_chain\fR, \fB\-policy\fR, \fB\-policy_check\fR, \fB\-policy_print\fR, \fB\-purpose\fR, \fB\-suiteB_128\fR, \fB\-suiteB_128_only\fR, \fB\-suiteB_192\fR, \fB\-trusted_first\fR, \fB\-use_deltas\fR, \fB\-auth_level\fR, \fB\-verify_depth\fR, \fB\-verify_email\fR, \fB\-verify_hostname\fR, \fB\-verify_ip\fR, \fB\-verify_name\fR, \fB\-x509_strict\fR \fB\-issuer_checks\fR" 4
.IX Item "-allow_proxy_certs, -attime, -no_check_time, -check_ss_sig, -crl_check, -crl_check_all, -explicit_policy, -extended_crl, -ignore_critical, -inhibit_any, -inhibit_map, -no_alt_chains, -partial_chain, -policy, -policy_check, -policy_print, -purpose, -suiteB_128, -suiteB_128_only, -suiteB_192, -trusted_first, -use_deltas, -auth_level, -verify_depth, -verify_email, -verify_hostname, -verify_ip, -verify_name, -x509_strict -issuer_checks"
Set various options of certificate chain verification.
See "Verification Options" in \fBopenssl\-verification\-options\fR\|(1) for details.
.Sp
Any verification errors cause the command to exit.
.SH "CONFIGURATION FILE OPTIONS"
.IX Header "CONFIGURATION FILE OPTIONS"
The \fB\-query\fR and \fB\-reply\fR commands make use of a configuration file.
See \fBconfig\fR\|(5)
for a general description of the syntax of the config file. The
\&\fB\-query\fR command uses only the symbolic OID names section
and it can work without it. However, the \fB\-reply\fR command needs the
config file for its operation.
.PP
When there is a command line switch equivalent of a variable the
switch always overrides the settings in the config file.
.IP "\fBtsa\fR section, \fBdefault_tsa\fR" 4
.IX Item "tsa section, default_tsa"
This is the main section and it specifies the name of another section
that contains all the options for the \fB\-reply\fR command. This default
section can be overridden with the \fB\-section\fR command line switch. (Optional)
.IP \fBoid_file\fR 4
.IX Item "oid_file"
This specifies a file containing additional \fBOBJECT IDENTIFIERS\fR.
Each line of the file should consist of the numerical form of the
object identifier followed by whitespace then the short name followed
by whitespace and finally the long name. (Optional)
.IP \fBoid_section\fR 4
.IX Item "oid_section"
This specifies a section in the configuration file containing extra
object identifiers. Each line should consist of the short name of the
object identifier followed by \fB=\fR and the numerical form. The short
and long names are the same when this option is used. (Optional)
.IP \fBRANDFILE\fR 4
.IX Item "RANDFILE"
At startup the specified file is loaded into the random number generator,
and at exit 256 bytes will be written to it. (Note: Using a RANDFILE is
not necessary anymore, see the "HISTORY" section.
.IP \fBserial\fR 4
.IX Item "serial"
The name of the file containing the hexadecimal serial number of the
last timestamp response created. This number is incremented by 1 for
each response. If the file does not exist at the time of response
generation a new file is created with serial number 1. (Mandatory)
.IP \fBcrypto_device\fR 4
.IX Item "crypto_device"
Specifies the OpenSSL engine that will be set as the default for
all available algorithms. The default value is built-in, you can specify
any other engines supported by OpenSSL (e.g. use chil for the NCipher HSM).
(Optional)
.IP \fBsigner_cert\fR 4
.IX Item "signer_cert"
TSA signing certificate in PEM format. The same as the \fB\-signer\fR
command line option. (Optional)
.IP \fBcerts\fR 4
.IX Item "certs"
A file containing a set of PEM encoded certificates that need to be
included in the response. The same as the \fB\-chain\fR command line
option. (Optional)
.IP \fBsigner_key\fR 4
.IX Item "signer_key"
The private key of the TSA in PEM format. The same as the \fB\-inkey\fR
command line option. (Optional)
.IP \fBsigner_digest\fR 4
.IX Item "signer_digest"
Signing digest to use. The same as the
\&\fB\-\fR\f(BIdigest\fR command line option. (Mandatory unless specified on the command
line)
.IP \fBdefault_policy\fR 4
.IX Item "default_policy"
The default policy to use when the request does not mandate any
policy. The same as the \fB\-tspolicy\fR command line option. (Optional)
.IP \fBother_policies\fR 4
.IX Item "other_policies"
Comma separated list of policies that are also acceptable by the TSA
and used only if the request explicitly specifies one of them. (Optional)
.IP \fBdigests\fR 4
.IX Item "digests"
The list of message digest algorithms that the TSA accepts. At least
one algorithm must be specified. (Mandatory)
.IP \fBaccuracy\fR 4
.IX Item "accuracy"
The accuracy of the time source of the TSA in seconds, milliseconds
and microseconds. E.g. secs:1, millisecs:500, microsecs:100. If any of
the components is missing zero is assumed for that field. (Optional)
.IP \fBclock_precision_digits\fR 4
.IX Item "clock_precision_digits"
Specifies the maximum number of digits, which represent the fraction of
seconds, that  need to be included in the time field. The trailing zeros
must be removed from the time, so there might actually be fewer digits,
or no fraction of seconds at all. Supported only on UNIX platforms.
The maximum value is 6, default is 0.
(Optional)
.IP \fBordering\fR 4
.IX Item "ordering"
If this option is yes the responses generated by this TSA can always
be ordered, even if the time difference between two responses is less
than the sum of their accuracies. Default is no. (Optional)
.IP \fBtsa_name\fR 4
.IX Item "tsa_name"
Set this option to yes if the subject name of the TSA must be included in
the TSA name field of the response. Default is no. (Optional)
.IP \fBess_cert_id_chain\fR 4
.IX Item "ess_cert_id_chain"
The SignedData objects created by the TSA always contain the
certificate identifier of the signing certificate in a signed
attribute (see RFC 2634, Enhanced Security Services).
If this variable is set to no, only this signing certificate identifier
is included in the SigningCertificate signed attribute.
If this variable is set to yes and the \fBcerts\fR variable or the \fB\-chain\fR option
is specified then the certificate identifiers of the chain will also
be included, where the \fB\-chain\fR option overrides the \fBcerts\fR variable.
Default is no.  (Optional)
.IP \fBess_cert_id_alg\fR 4
.IX Item "ess_cert_id_alg"
This option specifies the hash function to be used to calculate the TSA's
public key certificate identifier. Default is sha1. (Optional)
.SH EXAMPLES
.IX Header "EXAMPLES"
All the examples below presume that \fBOPENSSL_CONF\fR is set to a proper
configuration file, e.g. the example configuration file
\&\fIopenssl/apps/openssl.cnf\fR will do.
.SS "Timestamp Request"
.IX Subsection "Timestamp Request"
To create a timestamp request for \fIdesign1.txt\fR with SHA\-256 digest,
without nonce and policy, and without requirement for a certificate
in the response:
.PP
.Vb 2
\&  openssl ts \-query \-data design1.txt \-no_nonce \e
\&        \-out design1.tsq
.Ve
.PP
To create a similar timestamp request with specifying the message imprint
explicitly:
.PP
.Vb 2
\&  openssl ts \-query \-digest b7e5d3f93198b38379852f2c04e78d73abdd0f4b \e
\&         \-no_nonce \-out design1.tsq
.Ve
.PP
To print the content of the previous request in human readable format:
.PP
.Vb 1
\&  openssl ts \-query \-in design1.tsq \-text
.Ve
.PP
To create a timestamp request which includes the SHA\-512 digest
of \fIdesign2.txt\fR, requests the signer certificate and nonce, and
specifies a policy id (assuming the tsa_policy1 name is defined in the
OID section of the config file):
.PP
.Vb 2
\&  openssl ts \-query \-data design2.txt \-sha512 \e
\&        \-tspolicy tsa_policy1 \-cert \-out design2.tsq
.Ve
.SS "Timestamp Response"
.IX Subsection "Timestamp Response"
Before generating a response a signing certificate must be created for
the TSA that contains the \fBtimeStamping\fR critical extended key usage extension
without any other key usage extensions. You can add this line to the
user certificate section of the config file to generate a proper certificate;
.PP
.Vb 1
\&   extendedKeyUsage = critical,timeStamping
.Ve
.PP
See \fBopenssl\-req\fR\|(1), \fBopenssl\-ca\fR\|(1), and \fBopenssl\-x509\fR\|(1) for
instructions. The examples below assume that \fIcacert.pem\fR contains the
certificate of the CA, \fItsacert.pem\fR is the signing certificate issued
by \fIcacert.pem\fR and \fItsakey.pem\fR is the private key of the TSA.
.PP
To create a timestamp response for a request:
.PP
.Vb 2
\&  openssl ts \-reply \-queryfile design1.tsq \-inkey tsakey.pem \e
\&        \-signer tsacert.pem \-out design1.tsr
.Ve
.PP
If you want to use the settings in the config file you could just write:
.PP
.Vb 1
\&  openssl ts \-reply \-queryfile design1.tsq \-out design1.tsr
.Ve
.PP
To print a timestamp reply to stdout in human readable format:
.PP
.Vb 1
\&  openssl ts \-reply \-in design1.tsr \-text
.Ve
.PP
To create a timestamp token instead of timestamp response:
.PP
.Vb 1
\&  openssl ts \-reply \-queryfile design1.tsq \-out design1_token.der \-token_out
.Ve
.PP
To print a timestamp token to stdout in human readable format:
.PP
.Vb 1
\&  openssl ts \-reply \-in design1_token.der \-token_in \-text \-token_out
.Ve
.PP
To extract the timestamp token from a response:
.PP
.Vb 1
\&  openssl ts \-reply \-in design1.tsr \-out design1_token.der \-token_out
.Ve
.PP
To add 'granted' status info to a timestamp token thereby creating a
valid response:
.PP
.Vb 1
\&  openssl ts \-reply \-in design1_token.der \-token_in \-out design1.tsr
.Ve
.SS "Timestamp Verification"
.IX Subsection "Timestamp Verification"
To verify a timestamp reply against a request:
.PP
.Vb 2
\&  openssl ts \-verify \-queryfile design1.tsq \-in design1.tsr \e
\&        \-CAfile cacert.pem \-untrusted tsacert.pem
.Ve
.PP
To verify a timestamp reply that includes the certificate chain:
.PP
.Vb 2
\&  openssl ts \-verify \-queryfile design2.tsq \-in design2.tsr \e
\&        \-CAfile cacert.pem
.Ve
.PP
To verify a timestamp token against the original data file:
  openssl ts \-verify \-data design2.txt \-in design2.tsr \e
        \-CAfile cacert.pem
.PP
To verify a timestamp token against a message imprint:
  openssl ts \-verify \-digest b7e5d3f93198b38379852f2c04e78d73abdd0f4b \e
         \-in design2.tsr \-CAfile cacert.pem
.PP
You could also look at the 'test' directory for more examples.
.SH BUGS
.IX Header "BUGS"
.IP \(bu 2
No support for timestamps over SMTP, though it is quite easy
to implement an automatic e\-mail based TSA with \fBprocmail\fR\|(1)
and \fBperl\fR\|(1). HTTP server support is provided in the form of
a separate apache module. HTTP client support is provided by
\&\fBtsget\fR\|(1). Pure TCP/IP protocol is not supported.
.IP \(bu 2
The file containing the last serial number of the TSA is not
locked when being read or written. This is a problem if more than one
instance of \fBopenssl\fR\|(1) is trying to create a timestamp
response at the same time. This is not an issue when using the apache
server module, it does proper locking.
.IP \(bu 2
Look for the FIXME word in the source files.
.IP \(bu 2
The source code should really be reviewed by somebody else, too.
.IP \(bu 2
More testing is needed, I have done only some basic tests (see
test/testtsa).
.SH HISTORY
.IX Header "HISTORY"
OpenSSL 1.1.1 introduced a new random generator (CSPRNG) with an improved
seeding mechanism. The new seeding mechanism makes it unnecessary to
define a RANDFILE for saving and restoring randomness. This option is
retained mainly for compatibility reasons.
.PP
The \fB\-engine\fR option was deprecated in OpenSSL 3.0.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBopenssl\fR\|(1),
\&\fBtsget\fR\|(1),
\&\fBopenssl\-req\fR\|(1),
\&\fBopenssl\-x509\fR\|(1),
\&\fBopenssl\-ca\fR\|(1),
\&\fBopenssl\-genrsa\fR\|(1),
\&\fBconfig\fR\|(5),
\&\fBossl_store\-file\fR\|(7)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2006\-2023 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
