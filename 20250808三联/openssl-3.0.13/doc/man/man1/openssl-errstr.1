.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "OPENSSL-ERRSTR 1ossl"
.TH OPENSSL-ERRSTR 1ossl 2025-08-08 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
openssl\-errstr \- lookup error codes
.SH SYNOPSIS
.IX Header "SYNOPSIS"
\&\fBopenssl errstr\fR
[\fB\-help\fR]
\&\fIerror_code...\fR
.SH DESCRIPTION
.IX Header "DESCRIPTION"
Sometimes an application will not load error message texts and only
numerical forms will be available. This command can be
used to display the meaning of the hex code. The hex code is the hex digits
after the second colon.
.SH OPTIONS
.IX Header "OPTIONS"
.IP \fB\-help\fR 4
.IX Item "-help"
Display a usage message.
.SH EXAMPLES
.IX Header "EXAMPLES"
The error code:
.PP
.Vb 1
\& 27594:error:2006D080:lib(32)::reason(128)::107:
.Ve
.PP
can be displayed with:
.PP
.Vb 1
\& openssl errstr 2006D080
.Ve
.PP
to produce the error message:
.PP
.Vb 1
\& error:2006D080:BIO routines::no such file
.Ve
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2004\-2020 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
