.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "OPENSSL-X509 1ossl"
.TH OPENSSL-X509 1ossl 2025-08-08 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
openssl\-x509 \- Certificate display and signing command
.SH SYNOPSIS
.IX Header "SYNOPSIS"
\&\fBopenssl\fR \fBx509\fR
[\fB\-help\fR]
[\fB\-in\fR \fIfilename\fR|\fIuri\fR]
[\fB\-passin\fR \fIarg\fR]
[\fB\-new\fR]
[\fB\-x509toreq\fR]
[\fB\-req\fR]
[\fB\-copy_extensions\fR \fIarg\fR]
[\fB\-inform\fR \fBDER\fR|\fBPEM\fR]
[\fB\-vfyopt\fR \fInm\fR:\fIv\fR]
[\fB\-key\fR \fIfilename\fR|\fIuri\fR]
[\fB\-keyform\fR \fBDER\fR|\fBPEM\fR|\fBP12\fR|\fBENGINE\fR]
[\fB\-signkey\fR \fIfilename\fR|\fIuri\fR]
[\fB\-out\fR \fIfilename\fR]
[\fB\-outform\fR \fBDER\fR|\fBPEM\fR]
[\fB\-nocert\fR]
[\fB\-noout\fR]
[\fB\-dateopt\fR]
[\fB\-text\fR]
[\fB\-certopt\fR \fIoption\fR]
[\fB\-fingerprint\fR]
[\fB\-alias\fR]
[\fB\-serial\fR]
[\fB\-startdate\fR]
[\fB\-enddate\fR]
[\fB\-dates\fR]
[\fB\-subject\fR]
[\fB\-issuer\fR]
[\fB\-nameopt\fR \fIoption\fR]
[\fB\-email\fR]
[\fB\-hash\fR]
[\fB\-subject_hash\fR]
[\fB\-subject_hash_old\fR]
[\fB\-issuer_hash\fR]
[\fB\-issuer_hash_old\fR]
[\fB\-ext\fR \fIextensions\fR]
[\fB\-ocspid\fR]
[\fB\-ocsp_uri\fR]
[\fB\-purpose\fR]
[\fB\-pubkey\fR]
[\fB\-modulus\fR]
[\fB\-checkend\fR \fInum\fR]
[\fB\-checkhost\fR \fIhost\fR]
[\fB\-checkemail\fR \fIhost\fR]
[\fB\-checkip\fR \fIipaddr\fR]
[\fB\-set_serial\fR \fIn\fR]
[\fB\-next_serial\fR]
[\fB\-days\fR \fIarg\fR]
[\fB\-preserve_dates\fR]
[\fB\-subj\fR \fIarg\fR]
[\fB\-force_pubkey\fR \fIfilename\fR]
[\fB\-clrext\fR]
[\fB\-extfile\fR \fIfilename\fR]
[\fB\-extensions\fR \fIsection\fR]
[\fB\-sigopt\fR \fInm\fR:\fIv\fR]
[\fB\-badsig\fR]
[\fB\-\fR\f(BIdigest\fR]
[\fB\-CA\fR \fIfilename\fR|\fIuri\fR]
[\fB\-CAform\fR \fBDER\fR|\fBPEM\fR|\fBP12\fR]
[\fB\-CAkey\fR \fIfilename\fR|\fIuri\fR]
[\fB\-CAkeyform\fR \fBDER\fR|\fBPEM\fR|\fBP12\fR|\fBENGINE\fR]
[\fB\-CAserial\fR \fIfilename\fR]
[\fB\-CAcreateserial\fR]
[\fB\-trustout\fR]
[\fB\-setalias\fR \fIarg\fR]
[\fB\-clrtrust\fR]
[\fB\-addtrust\fR \fIarg\fR]
[\fB\-clrreject\fR]
[\fB\-addreject\fR \fIarg\fR]
[\fB\-rand\fR \fIfiles\fR]
[\fB\-writerand\fR \fIfile\fR]
[\fB\-engine\fR \fIid\fR]
[\fB\-provider\fR \fIname\fR]
[\fB\-provider\-path\fR \fIpath\fR]
[\fB\-propquery\fR \fIpropq\fR]
.SH DESCRIPTION
.IX Header "DESCRIPTION"
This command is a multi-purposes certificate handling command.
It can be used to print certificate information,
convert certificates to various forms, edit certificate trust settings,
generate certificates from scratch or from certificating requests
and then self-signing them or signing them like a "micro CA".
.PP
Since there are a large number of options they will split up into
various sections.
.SH OPTIONS
.IX Header "OPTIONS"
.SS "Input, Output, and General Purpose Options"
.IX Subsection "Input, Output, and General Purpose Options"
.IP \fB\-help\fR 4
.IX Item "-help"
Print out a usage message.
.IP "\fB\-in\fR \fIfilename\fR|\fIuri\fR" 4
.IX Item "-in filename|uri"
This specifies the input to read a certificate from
or the input file for reading a certificate request if the \fB\-req\fR flag is used.
In both cases this defaults to standard input.
.Sp
This option cannot be combined with the \fB\-new\fR flag.
.IP "\fB\-passin\fR \fIarg\fR" 4
.IX Item "-passin arg"
The key and certificate file password source.
For more information about the format of \fIarg\fR
see \fBopenssl\-passphrase\-options\fR\|(1).
.IP \fB\-new\fR 4
.IX Item "-new"
Generate a certificate from scratch, not using an input certificate
or certificate request. So the \fB\-in\fR option must not be used in this case.
Instead, the \fB\-subj\fR option needs to be given.
The public key to include can be given with the \fB\-force_pubkey\fR option
and defaults to the key given with the \fB\-key\fR (or \fB\-signkey\fR) option,
which implies self-signature.
.IP \fB\-x509toreq\fR 4
.IX Item "-x509toreq"
Output a PKCS#10 certificate request (rather than a certificate).
The \fB\-key\fR (or \fB\-signkey\fR) option must be used to provide the private key for
self-signing; the corresponding public key is placed in the subjectPKInfo field.
.Sp
X.509 extensions included in a certificate input are not copied by default.
X.509 extensions to be added can be specified using the \fB\-extfile\fR option.
.IP \fB\-req\fR 4
.IX Item "-req"
By default a certificate is expected on input.
With this option a PKCS#10 certificate request is expected instead,
which must be correctly self-signed.
.Sp
X.509 extensions included in the request are not copied by default.
X.509 extensions to be added can be specified using the \fB\-extfile\fR option.
.IP "\fB\-copy_extensions\fR \fIarg\fR" 4
.IX Item "-copy_extensions arg"
Determines how to handle X.509 extensions
when converting from a certificate to a request using the \fB\-x509toreq\fR option
or converting from a request to a certificate using the \fB\-req\fR option.
If \fIarg\fR is \fBnone\fR or this option is not present then extensions are ignored.
If \fIarg\fR is \fBcopy\fR or \fBcopyall\fR then all extensions are copied,
except that subject identifier and authority key identifier extensions
are not taken over when producing a certificate request.
.Sp
The \fB\-ext\fR option can be used to further restrict which extensions to copy.
.IP "\fB\-inform\fR \fBDER\fR|\fBPEM\fR" 4
.IX Item "-inform DER|PEM"
The input file format; unspecified by default.
See \fBopenssl\-format\-options\fR\|(1) for details.
.IP "\fB\-vfyopt\fR \fInm\fR:\fIv\fR" 4
.IX Item "-vfyopt nm:v"
Pass options to the signature algorithm during verify operations.
Names and values of these options are algorithm-specific.
.IP "\fB\-key\fR \fIfilename\fR|\fIuri\fR" 4
.IX Item "-key filename|uri"
This option provides the private key for signing a new certificate or
certificate request.
Unless \fB\-force_pubkey\fR is given, the corresponding public key is placed in
the new certificate or certificate request, resulting in a self-signature.
.Sp
This option cannot be used in conjunction with the \fB\-CA\fR option.
.Sp
It sets the issuer name to the subject name (i.e., makes it self-issued)
and changes the public key to the supplied value (unless overridden
by \fB\-force_pubkey\fR).
Unless the \fB\-preserve_dates\fR option is supplied,
it sets the validity start date to the current time
and the end date to a value determined by the \fB\-days\fR option.
.IP "\fB\-signkey\fR \fIfilename\fR|\fIuri\fR" 4
.IX Item "-signkey filename|uri"
This option is an alias of \fB\-key\fR.
.IP "\fB\-keyform\fR \fBDER\fR|\fBPEM\fR|\fBP12\fR|\fBENGINE\fR" 4
.IX Item "-keyform DER|PEM|P12|ENGINE"
The key input format; unspecified by default.
See \fBopenssl\-format\-options\fR\|(1) for details.
.IP "\fB\-out\fR \fIfilename\fR" 4
.IX Item "-out filename"
This specifies the output filename to write to or standard output by default.
.IP "\fB\-outform\fR \fBDER\fR|\fBPEM\fR" 4
.IX Item "-outform DER|PEM"
The output format; the default is \fBPEM\fR.
See \fBopenssl\-format\-options\fR\|(1) for details.
.IP \fB\-nocert\fR 4
.IX Item "-nocert"
Do not output a certificate (except for printing as requested by below options).
.IP \fB\-noout\fR 4
.IX Item "-noout"
This option prevents output except for printing as requested by below options.
.SS "Certificate Printing Options"
.IX Subsection "Certificate Printing Options"
Note: the \fB\-alias\fR and \fB\-purpose\fR options are also printing options
but are described in the "Trust Settings" section.
.IP \fB\-dateopt\fR 4
.IX Item "-dateopt"
Specify the date output format. Values are: rfc_822 and iso_8601.
Defaults to rfc_822.
.IP \fB\-text\fR 4
.IX Item "-text"
Prints out the certificate in text form. Full details are printed including the
public key, signature algorithms, issuer and subject names, serial number
any extensions present and any trust settings.
.IP "\fB\-certopt\fR \fIoption\fR" 4
.IX Item "-certopt option"
Customise the print format used with \fB\-text\fR. The \fIoption\fR argument
can be a single option or multiple options separated by commas.
The \fB\-certopt\fR switch may be also be used more than once to set multiple
options. See the "Text Printing Flags" section for more information.
.IP \fB\-fingerprint\fR 4
.IX Item "-fingerprint"
Calculates and prints the digest of the DER encoded version of the entire
certificate (see digest options).
This is commonly called a "fingerprint". Because of the nature of message
digests, the fingerprint of a certificate is unique to that certificate and
two certificates with the same fingerprint can be considered to be the same.
.IP \fB\-alias\fR 4
.IX Item "-alias"
Prints the certificate "alias" (nickname), if any.
.IP \fB\-serial\fR 4
.IX Item "-serial"
Prints the certificate serial number.
.IP \fB\-startdate\fR 4
.IX Item "-startdate"
Prints out the start date of the certificate, that is the notBefore date.
.IP \fB\-enddate\fR 4
.IX Item "-enddate"
Prints out the expiry date of the certificate, that is the notAfter date.
.IP \fB\-dates\fR 4
.IX Item "-dates"
Prints out the start and expiry dates of a certificate.
.IP \fB\-subject\fR 4
.IX Item "-subject"
Prints the subject name.
.IP \fB\-issuer\fR 4
.IX Item "-issuer"
Prints the issuer name.
.IP "\fB\-nameopt\fR \fIoption\fR" 4
.IX Item "-nameopt option"
This specifies how the subject or issuer names are displayed.
See \fBopenssl\-namedisplay\-options\fR\|(1) for details.
.IP \fB\-email\fR 4
.IX Item "-email"
Prints the email address(es) if any.
.IP \fB\-hash\fR 4
.IX Item "-hash"
Synonym for "\-subject_hash" for backward compatibility reasons.
.IP \fB\-subject_hash\fR 4
.IX Item "-subject_hash"
Prints the "hash" of the certificate subject name. This is used in OpenSSL to
form an index to allow certificates in a directory to be looked up by subject
name.
.IP \fB\-subject_hash_old\fR 4
.IX Item "-subject_hash_old"
Prints the "hash" of the certificate subject name using the older algorithm
as used by OpenSSL before version 1.0.0.
.IP \fB\-issuer_hash\fR 4
.IX Item "-issuer_hash"
Prints the "hash" of the certificate issuer name.
.IP \fB\-issuer_hash_old\fR 4
.IX Item "-issuer_hash_old"
Prints the "hash" of the certificate issuer name using the older algorithm
as used by OpenSSL before version 1.0.0.
.IP "\fB\-ext\fR \fIextensions\fR" 4
.IX Item "-ext extensions"
Prints out the certificate extensions in text form.
Can also be used to restrict which extensions to copy.
Extensions are specified
with a comma separated string, e.g., "subjectAltName,subjectKeyIdentifier".
See the \fBx509v3_config\fR\|(5) manual page for the extension names.
.IP \fB\-ocspid\fR 4
.IX Item "-ocspid"
Prints the OCSP hash values for the subject name and public key.
.IP \fB\-ocsp_uri\fR 4
.IX Item "-ocsp_uri"
Prints the OCSP responder address(es) if any.
.IP \fB\-purpose\fR 4
.IX Item "-purpose"
This option performs tests on the certificate extensions and outputs
the results. For a more complete description see
"Certificate Extensions" in \fBopenssl\-verification\-options\fR\|(1).
.IP \fB\-pubkey\fR 4
.IX Item "-pubkey"
Prints the certificate's SubjectPublicKeyInfo block in PEM format.
.IP \fB\-modulus\fR 4
.IX Item "-modulus"
This option prints out the value of the modulus of the public key
contained in the certificate.
.SS "Certificate Checking Options"
.IX Subsection "Certificate Checking Options"
.IP "\fB\-checkend\fR \fIarg\fR" 4
.IX Item "-checkend arg"
Checks if the certificate expires within the next \fIarg\fR seconds and exits
nonzero if yes it will expire or zero if not.
.IP "\fB\-checkhost\fR \fIhost\fR" 4
.IX Item "-checkhost host"
Check that the certificate matches the specified host.
.IP "\fB\-checkemail\fR \fIemail\fR" 4
.IX Item "-checkemail email"
Check that the certificate matches the specified email address.
.IP "\fB\-checkip\fR \fIipaddr\fR" 4
.IX Item "-checkip ipaddr"
Check that the certificate matches the specified IP address.
.SS "Certificate Output Options"
.IX Subsection "Certificate Output Options"
.IP "\fB\-set_serial\fR \fIn\fR" 4
.IX Item "-set_serial n"
Specifies the serial number to use.
This option can be used with the \fB\-key\fR, \fB\-signkey\fR, or \fB\-CA\fR options.
If used in conjunction with the \fB\-CA\fR option
the serial number file (as specified by the \fB\-CAserial\fR option) is not used.
.Sp
The serial number can be decimal or hex (if preceded by \f(CW\*(C`0x\*(C'\fR).
.IP \fB\-next_serial\fR 4
.IX Item "-next_serial"
Set the serial to be one more than the number in the certificate.
.IP "\fB\-days\fR \fIarg\fR" 4
.IX Item "-days arg"
Specifies the number of days until a newly generated certificate expires.
The default is 30.
Cannot be used together with the \fB\-preserve_dates\fR option.
.IP \fB\-preserve_dates\fR 4
.IX Item "-preserve_dates"
When signing a certificate, preserve "notBefore" and "notAfter" dates of any
input certificate instead of adjusting them to current time and duration.
Cannot be used together with the \fB\-days\fR option.
.IP "\fB\-subj\fR \fIarg\fR" 4
.IX Item "-subj arg"
When a certificate is created set its subject name to the given value.
When the certificate is self-signed the issuer name is set to the same value.
.Sp
The arg must be formatted as \f(CW\*(C`/type0=value0/type1=value1/type2=...\*(C'\fR.
Special characters may be escaped by \f(CW\*(C`\e\*(C'\fR (backslash), whitespace is retained.
Empty values are permitted, but the corresponding type will not be included
in the certificate.
Giving a single \f(CW\*(C`/\*(C'\fR will lead to an empty sequence of RDNs (a NULL-DN).
Multi-valued RDNs can be formed by placing a \f(CW\*(C`+\*(C'\fR character instead of a \f(CW\*(C`/\*(C'\fR
between the AttributeValueAssertions (AVAs) that specify the members of the set.
Example:
.Sp
\&\f(CW\*(C`/DC=org/DC=OpenSSL/DC=users/UID=123456+CN=John Doe\*(C'\fR
.Sp
This option can be used in conjunction with the \fB\-force_pubkey\fR option
to create a certificate even without providing an input certificate
or certificate request.
.IP "\fB\-force_pubkey\fR \fIfilename\fR" 4
.IX Item "-force_pubkey filename"
When a certificate is created set its public key to the key in \fIfilename\fR
instead of the key contained in the input
or given with the \fB\-key\fR (or \fB\-signkey\fR) option.
.Sp
This option is useful for creating self-issued certificates that are not
self-signed, for instance when the key cannot be used for signing, such as DH.
It can also be used in conjunction with \fB\-new\fR and \fB\-subj\fR to directly
generate a certificate containing any desired public key.
.IP \fB\-clrext\fR 4
.IX Item "-clrext"
When transforming a certificate to a new certificate
by default all certificate extensions are retained.
.Sp
When transforming a certificate or certificate request,
the \fB\-clrext\fR option prevents taking over any extensions from the source.
In any case, when producing a certificate request,
neither subject identifier nor authority key identifier extensions are included.
.IP "\fB\-extfile\fR \fIfilename\fR" 4
.IX Item "-extfile filename"
Configuration file containing certificate and request X.509 extensions to add.
.IP "\fB\-extensions\fR \fIsection\fR" 4
.IX Item "-extensions section"
The section in the extfile to add X.509 extensions from.
If this option is not
specified then the extensions should either be contained in the unnamed
(default) section or the default section should contain a variable called
"extensions" which contains the section to use.
See the \fBx509v3_config\fR\|(5) manual page for details of the
extension section format.
.IP "\fB\-sigopt\fR \fInm\fR:\fIv\fR" 4
.IX Item "-sigopt nm:v"
Pass options to the signature algorithm during sign operations.
This option may be given multiple times.
Names and values provided using this option are algorithm-specific.
.IP \fB\-badsig\fR 4
.IX Item "-badsig"
Corrupt the signature before writing it; this can be useful
for testing.
.IP \fB\-\fR\f(BIdigest\fR 4
.IX Item "-digest"
The digest to use.
This affects any signing or printing option that uses a message
digest, such as the \fB\-fingerprint\fR, \fB\-key\fR, and \fB\-CA\fR options.
Any digest supported by the \fBopenssl\-dgst\fR\|(1) command can be used.
If not specified then SHA1 is used with \fB\-fingerprint\fR or
the default digest for the signing algorithm is used, typically SHA256.
.SS "Micro-CA Options"
.IX Subsection "Micro-CA Options"
.IP "\fB\-CA\fR \fIfilename\fR|\fIuri\fR" 4
.IX Item "-CA filename|uri"
Specifies the "CA" certificate to be used for signing.
When present, this behaves like a "micro CA" as follows:
The subject name of the "CA" certificate is placed as issuer name in the new
certificate, which is then signed using the "CA" key given as detailed below.
.Sp
This option cannot be used in conjunction with \fB\-key\fR (or \fB\-signkey\fR).
This option is normally combined with the \fB\-req\fR option referencing a CSR.
Without the \fB\-req\fR option the input must be an existing certificate
unless the \fB\-new\fR option is given, which generates a certificate from scratch.
.IP "\fB\-CAform\fR \fBDER\fR|\fBPEM\fR|\fBP12\fR," 4
.IX Item "-CAform DER|PEM|P12,"
The format for the CA certificate; unspecified by default.
See \fBopenssl\-format\-options\fR\|(1) for details.
.IP "\fB\-CAkey\fR \fIfilename\fR|\fIuri\fR" 4
.IX Item "-CAkey filename|uri"
Sets the CA private key to sign a certificate with.
The private key must match the public key of the certificate given with \fB\-CA\fR.
If this option is not provided then the key must be present in the \fB\-CA\fR input.
.IP "\fB\-CAkeyform\fR \fBDER\fR|\fBPEM\fR|\fBP12\fR|\fBENGINE\fR" 4
.IX Item "-CAkeyform DER|PEM|P12|ENGINE"
The format for the CA key; unspecified by default.
See \fBopenssl\-format\-options\fR\|(1) for details.
.IP "\fB\-CAserial\fR \fIfilename\fR" 4
.IX Item "-CAserial filename"
Sets the CA serial number file to use.
.Sp
When creating a certificate with this option and with the \fB\-CA\fR option,
the certificate serial number is stored in the given file.
This file consists of one line containing
an even number of hex digits with the serial number used last time.
After reading this number, it is incremented and used, and the file is updated.
.Sp
The default filename consists of the CA certificate file base name with
\&\fI.srl\fR appended. For example if the CA certificate file is called
\&\fImycacert.pem\fR it expects to find a serial number file called
\&\fImycacert.srl\fR.
.Sp
If the \fB\-CA\fR option is specified and neither <\-CAserial> or <\-CAcreateserial>
is given and the default serial number file does not exist,
a random number is generated; this is the recommended practice.
.IP \fB\-CAcreateserial\fR 4
.IX Item "-CAcreateserial"
With this option and the \fB\-CA\fR option
the CA serial number file is created if it does not exist.
A random number is generated, used for the certificate,
and saved into the serial number file determined as described above.
.SS "Trust Settings"
.IX Subsection "Trust Settings"
A \fBtrusted certificate\fR is an ordinary certificate which has several
additional pieces of information attached to it such as the permitted
and prohibited uses of the certificate and possibly an "alias" (nickname).
.PP
Normally when a certificate is being verified at least one certificate
must be "trusted". By default a trusted certificate must be stored
locally and must be a root CA: any certificate chain ending in this CA
is then usable for any purpose.
.PP
Trust settings currently are only used with a root CA.
They allow a finer control over the purposes the root CA can be used for.
For example, a CA may be trusted for SSL client but not SSL server use.
.PP
See \fBopenssl\-verification\-options\fR\|(1) for more information
on the meaning of trust settings.
.PP
Future versions of OpenSSL will recognize trust settings on any
certificate: not just root CAs.
.IP \fB\-trustout\fR 4
.IX Item "-trustout"
Mark any certificate PEM output as <trusted> certificate rather than ordinary.
An ordinary or trusted certificate can be input but by default an ordinary
certificate is output and any trust settings are discarded.
With the \fB\-trustout\fR option a trusted certificate is output. A trusted
certificate is automatically output if any trust settings are modified.
.IP "\fB\-setalias\fR \fIarg\fR" 4
.IX Item "-setalias arg"
Sets the "alias" of the certificate. This will allow the certificate
to be referred to using a nickname for example "Steve's Certificate".
.IP \fB\-clrtrust\fR 4
.IX Item "-clrtrust"
Clears all the permitted or trusted uses of the certificate.
.IP "\fB\-addtrust\fR \fIarg\fR" 4
.IX Item "-addtrust arg"
Adds a trusted certificate use.
Any object name can be used here but currently only \fBclientAuth\fR,
\&\fBserverAuth\fR, \fBemailProtection\fR, and \fBanyExtendedKeyUsage\fR are defined.
As of OpenSSL 1.1.0, the last of these blocks all purposes when rejected or
enables all purposes when trusted.
Other OpenSSL applications may define additional uses.
.IP \fB\-clrreject\fR 4
.IX Item "-clrreject"
Clears all the prohibited or rejected uses of the certificate.
.IP "\fB\-addreject\fR \fIarg\fR" 4
.IX Item "-addreject arg"
Adds a prohibited trust anchor purpose.
It accepts the same values as the \fB\-addtrust\fR option.
.SS "Generic options"
.IX Subsection "Generic options"
.IP "\fB\-rand\fR \fIfiles\fR, \fB\-writerand\fR \fIfile\fR" 4
.IX Item "-rand files, -writerand file"
See "Random State Options" in \fBopenssl\fR\|(1) for details.
.IP "\fB\-engine\fR \fIid\fR" 4
.IX Item "-engine id"
See "Engine Options" in \fBopenssl\fR\|(1).
This option is deprecated.
.IP "\fB\-provider\fR \fIname\fR" 4
.IX Item "-provider name"
.PD 0
.IP "\fB\-provider\-path\fR \fIpath\fR" 4
.IX Item "-provider-path path"
.IP "\fB\-propquery\fR \fIpropq\fR" 4
.IX Item "-propquery propq"
.PD
See "Provider Options" in \fBopenssl\fR\|(1), \fBprovider\fR\|(7), and \fBproperty\fR\|(7).
.SS "Text Printing Flags"
.IX Subsection "Text Printing Flags"
As well as customising the name printing format, it is also possible to
customise the actual fields printed using the \fBcertopt\fR option when
the \fBtext\fR option is present. The default behaviour is to print all fields.
.IP \fBcompatible\fR 4
.IX Item "compatible"
Use the old format. This is equivalent to specifying no printing options at all.
.IP \fBno_header\fR 4
.IX Item "no_header"
Don't print header information: that is the lines saying "Certificate"
and "Data".
.IP \fBno_version\fR 4
.IX Item "no_version"
Don't print out the version number.
.IP \fBno_serial\fR 4
.IX Item "no_serial"
Don't print out the serial number.
.IP \fBno_signame\fR 4
.IX Item "no_signame"
Don't print out the signature algorithm used.
.IP \fBno_validity\fR 4
.IX Item "no_validity"
Don't print the validity, that is the \fBnotBefore\fR and \fBnotAfter\fR fields.
.IP \fBno_subject\fR 4
.IX Item "no_subject"
Don't print out the subject name.
.IP \fBno_issuer\fR 4
.IX Item "no_issuer"
Don't print out the issuer name.
.IP \fBno_pubkey\fR 4
.IX Item "no_pubkey"
Don't print out the public key.
.IP \fBno_sigdump\fR 4
.IX Item "no_sigdump"
Don't give a hexadecimal dump of the certificate signature.
.IP \fBno_aux\fR 4
.IX Item "no_aux"
Don't print out certificate trust information.
.IP \fBno_extensions\fR 4
.IX Item "no_extensions"
Don't print out any X509V3 extensions.
.IP \fBext_default\fR 4
.IX Item "ext_default"
Retain default extension behaviour: attempt to print out unsupported
certificate extensions.
.IP \fBext_error\fR 4
.IX Item "ext_error"
Print an error message for unsupported certificate extensions.
.IP \fBext_parse\fR 4
.IX Item "ext_parse"
ASN1 parse unsupported extensions.
.IP \fBext_dump\fR 4
.IX Item "ext_dump"
Hex dump unsupported extensions.
.IP \fBca_default\fR 4
.IX Item "ca_default"
The value used by \fBopenssl\-ca\fR\|(1), equivalent to \fBno_issuer\fR, \fBno_pubkey\fR,
\&\fBno_header\fR, and \fBno_version\fR.
.SH EXAMPLES
.IX Header "EXAMPLES"
Note: in these examples the '\e' means the example should be all on one
line.
.PP
Print the contents of a certificate:
.PP
.Vb 1
\& openssl x509 \-in cert.pem \-noout \-text
.Ve
.PP
Print the "Subject Alternative Name" extension of a certificate:
.PP
.Vb 1
\& openssl x509 \-in cert.pem \-noout \-ext subjectAltName
.Ve
.PP
Print more extensions of a certificate:
.PP
.Vb 1
\& openssl x509 \-in cert.pem \-noout \-ext subjectAltName,nsCertType
.Ve
.PP
Print the certificate serial number:
.PP
.Vb 1
\& openssl x509 \-in cert.pem \-noout \-serial
.Ve
.PP
Print the certificate subject name:
.PP
.Vb 1
\& openssl x509 \-in cert.pem \-noout \-subject
.Ve
.PP
Print the certificate subject name in RFC2253 form:
.PP
.Vb 1
\& openssl x509 \-in cert.pem \-noout \-subject \-nameopt RFC2253
.Ve
.PP
Print the certificate subject name in oneline form on a terminal
supporting UTF8:
.PP
.Vb 1
\& openssl x509 \-in cert.pem \-noout \-subject \-nameopt oneline,\-esc_msb
.Ve
.PP
Print the certificate SHA1 fingerprint:
.PP
.Vb 1
\& openssl x509 \-sha1 \-in cert.pem \-noout \-fingerprint
.Ve
.PP
Convert a certificate from PEM to DER format:
.PP
.Vb 1
\& openssl x509 \-in cert.pem \-inform PEM \-out cert.der \-outform DER
.Ve
.PP
Convert a certificate to a certificate request:
.PP
.Vb 1
\& openssl x509 \-x509toreq \-in cert.pem \-out req.pem \-key key.pem
.Ve
.PP
Convert a certificate request into a self-signed certificate using
extensions for a CA:
.PP
.Vb 2
\& openssl x509 \-req \-in careq.pem \-extfile openssl.cnf \-extensions v3_ca \e
\&        \-key key.pem \-out cacert.pem
.Ve
.PP
Sign a certificate request using the CA certificate above and add user
certificate extensions:
.PP
.Vb 2
\& openssl x509 \-req \-in req.pem \-extfile openssl.cnf \-extensions v3_usr \e
\&        \-CA cacert.pem \-CAkey key.pem \-CAcreateserial
.Ve
.PP
Set a certificate to be trusted for SSL client use and change set its alias to
"Steve's Class 1 CA"
.PP
.Vb 2
\& openssl x509 \-in cert.pem \-addtrust clientAuth \e
\&        \-setalias "Steve\*(Aqs Class 1 CA" \-out trust.pem
.Ve
.SH NOTES
.IX Header "NOTES"
The conversion to UTF8 format used with the name options assumes that
T61Strings use the ISO8859\-1 character set. This is wrong but Netscape
and MSIE do this as do many certificates. So although this is incorrect
it is more likely to print the majority of certificates correctly.
.PP
The \fB\-email\fR option searches the subject name and the subject alternative
name extension. Only unique email addresses will be printed out: it will
not print the same address more than once.
.SH BUGS
.IX Header "BUGS"
It is possible to produce invalid certificates or requests by specifying the
wrong private key, using unsuitable X.509 extensions,
or using inconsistent options in some cases: these should be checked.
.PP
There should be options to explicitly set such things as start and end
dates rather than an offset from the current time.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBopenssl\fR\|(1),
\&\fBopenssl\-req\fR\|(1),
\&\fBopenssl\-ca\fR\|(1),
\&\fBopenssl\-genrsa\fR\|(1),
\&\fBopenssl\-gendsa\fR\|(1),
\&\fBopenssl\-verify\fR\|(1),
\&\fBx509v3_config\fR\|(5)
.SH HISTORY
.IX Header "HISTORY"
The hash algorithm used in the \fB\-subject_hash\fR and \fB\-issuer_hash\fR options
before OpenSSL 1.0.0 was based on the deprecated MD5 algorithm and the encoding
of the distinguished name. In OpenSSL 1.0.0 and later it is based on a canonical
version of the DN using SHA1. This means that any directories using the old
form must have their links rebuilt using \fBopenssl\-rehash\fR\|(1) or similar.
.PP
The \fB\-signkey\fR option has been renamed to \fB\-key\fR in OpenSSL 3.0,
keeping the old name as an alias.
.PP
The \fB\-engine\fR option was deprecated in OpenSSL 3.0.
.PP
The \fB\-C\fR option was removed in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2023 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
