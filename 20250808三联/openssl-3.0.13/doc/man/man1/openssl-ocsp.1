.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "OPENSSL-OCSP 1ossl"
.TH OPENSSL-OCSP 1ossl 2025-08-08 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
openssl\-ocsp \- Online Certificate Status Protocol command
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.SS "OCSP Client"
.IX Subsection "OCSP Client"
\&\fBopenssl\fR \fBocsp\fR
[\fB\-help\fR]
[\fB\-out\fR \fIfile\fR]
[\fB\-issuer\fR \fIfile\fR]
[\fB\-cert\fR \fIfile\fR]
[\fB\-no_certs\fR]
[\fB\-serial\fR \fIn\fR]
[\fB\-signer\fR \fIfile\fR]
[\fB\-signkey\fR \fIfile\fR]
[\fB\-sign_other\fR \fIfile\fR]
[\fB\-nonce\fR]
[\fB\-no_nonce\fR]
[\fB\-req_text\fR]
[\fB\-resp_text\fR]
[\fB\-text\fR]
[\fB\-reqout\fR \fIfile\fR]
[\fB\-respout\fR \fIfile\fR]
[\fB\-reqin\fR \fIfile\fR]
[\fB\-respin\fR \fIfile\fR]
[\fB\-url\fR \fIURL\fR]
[\fB\-host\fR \fIhost\fR:\fIport\fR]
[\fB\-path\fR]
[\fB\-proxy\fR \fI[http[s]://][userinfo@]host[:port][/path]\fR]
[\fB\-no_proxy\fR \fIaddresses\fR]
[\fB\-header\fR]
[\fB\-timeout\fR \fIseconds\fR]
[\fB\-VAfile\fR \fIfile\fR]
[\fB\-validity_period\fR \fIn\fR]
[\fB\-status_age\fR \fIn\fR]
[\fB\-noverify\fR]
[\fB\-verify_other\fR \fIfile\fR]
[\fB\-trust_other\fR]
[\fB\-no_intern\fR]
[\fB\-no_signature_verify\fR]
[\fB\-no_cert_verify\fR]
[\fB\-no_chain\fR]
[\fB\-no_cert_checks\fR]
[\fB\-no_explicit\fR]
[\fB\-port\fR \fInum\fR]
[\fB\-ignore_err\fR]
.SS "OCSP Server"
.IX Subsection "OCSP Server"
\&\fBopenssl\fR \fBocsp\fR
[\fB\-index\fR \fIfile\fR]
[\fB\-CA\fR \fIfile\fR]
[\fB\-rsigner\fR \fIfile\fR]
[\fB\-rkey\fR \fIfile\fR]
[\fB\-passin\fR \fIarg\fR]
[\fB\-rother\fR \fIfile\fR]
[\fB\-rsigopt\fR \fInm\fR:\fIv\fR]
[\fB\-rmd\fR \fIdigest\fR]
[\fB\-badsig\fR]
[\fB\-resp_no_certs\fR]
[\fB\-nmin\fR \fIn\fR]
[\fB\-ndays\fR \fIn\fR]
[\fB\-resp_key_id\fR]
[\fB\-nrequest\fR \fIn\fR]
[\fB\-multi\fR \fIprocess-count\fR]
[\fB\-rcid\fR \fIdigest\fR]
[\fB\-\fR\f(BIdigest\fR]
[\fB\-CAfile\fR \fIfile\fR]
[\fB\-no\-CAfile\fR]
[\fB\-CApath\fR \fIdir\fR]
[\fB\-no\-CApath\fR]
[\fB\-CAstore\fR \fIuri\fR]
[\fB\-no\-CAstore\fR]
[\fB\-allow_proxy_certs\fR]
[\fB\-attime\fR \fItimestamp\fR]
[\fB\-no_check_time\fR]
[\fB\-check_ss_sig\fR]
[\fB\-crl_check\fR]
[\fB\-crl_check_all\fR]
[\fB\-explicit_policy\fR]
[\fB\-extended_crl\fR]
[\fB\-ignore_critical\fR]
[\fB\-inhibit_any\fR]
[\fB\-inhibit_map\fR]
[\fB\-partial_chain\fR]
[\fB\-policy\fR \fIarg\fR]
[\fB\-policy_check\fR]
[\fB\-policy_print\fR]
[\fB\-purpose\fR \fIpurpose\fR]
[\fB\-suiteB_128\fR]
[\fB\-suiteB_128_only\fR]
[\fB\-suiteB_192\fR]
[\fB\-trusted_first\fR]
[\fB\-no_alt_chains\fR]
[\fB\-use_deltas\fR]
[\fB\-auth_level\fR \fInum\fR]
[\fB\-verify_depth\fR \fInum\fR]
[\fB\-verify_email\fR \fIemail\fR]
[\fB\-verify_hostname\fR \fIhostname\fR]
[\fB\-verify_ip\fR \fIip\fR]
[\fB\-verify_name\fR \fIname\fR]
[\fB\-x509_strict\fR]
[\fB\-issuer_checks\fR]
[\fB\-provider\fR \fIname\fR]
[\fB\-provider\-path\fR \fIpath\fR]
[\fB\-propquery\fR \fIpropq\fR]
.SH DESCRIPTION
.IX Header "DESCRIPTION"
The Online Certificate Status Protocol (OCSP) enables applications to
determine the (revocation) state of an identified certificate (RFC 2560).
.PP
This command performs many common OCSP tasks. It can be used
to print out requests and responses, create requests and send queries
to an OCSP responder and behave like a mini OCSP server itself.
.SH OPTIONS
.IX Header "OPTIONS"
This command operates as either a client or a server.
The options are described below, divided into those two modes.
.SS "OCSP Client Options"
.IX Subsection "OCSP Client Options"
.IP \fB\-help\fR 4
.IX Item "-help"
Print out a usage message.
.IP "\fB\-out\fR \fIfilename\fR" 4
.IX Item "-out filename"
specify output filename, default is standard output.
.IP "\fB\-issuer\fR \fIfilename\fR" 4
.IX Item "-issuer filename"
This specifies the current issuer certificate. This option can be used
multiple times.
This option \fBMUST\fR come before any \fB\-cert\fR options.
.IP "\fB\-cert\fR \fIfilename\fR" 4
.IX Item "-cert filename"
Add the certificate \fIfilename\fR to the request. The issuer certificate
is taken from the previous \fB\-issuer\fR option, or an error occurs if no
issuer certificate is specified.
.IP \fB\-no_certs\fR 4
.IX Item "-no_certs"
Don't include any certificates in signed request.
.IP "\fB\-serial\fR \fInum\fR" 4
.IX Item "-serial num"
Same as the \fB\-cert\fR option except the certificate with serial number
\&\fBnum\fR is added to the request. The serial number is interpreted as a
decimal integer unless preceded by \f(CW\*(C`0x\*(C'\fR. Negative integers can also
be specified by preceding the value by a \f(CW\*(C`\-\*(C'\fR sign.
.IP "\fB\-signer\fR \fIfilename\fR, \fB\-signkey\fR \fIfilename\fR" 4
.IX Item "-signer filename, -signkey filename"
Sign the OCSP request using the certificate specified in the \fB\-signer\fR
option and the private key specified by the \fB\-signkey\fR option. If
the \fB\-signkey\fR option is not present then the private key is read
from the same file as the certificate. If neither option is specified then
the OCSP request is not signed.
.IP "\fB\-sign_other\fR \fIfilename\fR" 4
.IX Item "-sign_other filename"
Additional certificates to include in the signed request.
The input can be in PEM, DER, or PKCS#12 format.
.IP "\fB\-nonce\fR, \fB\-no_nonce\fR" 4
.IX Item "-nonce, -no_nonce"
Add an OCSP nonce extension to a request or disable OCSP nonce addition.
Normally if an OCSP request is input using the \fB\-reqin\fR option no
nonce is added: using the \fB\-nonce\fR option will force addition of a nonce.
If an OCSP request is being created (using \fB\-cert\fR and \fB\-serial\fR options)
a nonce is automatically added specifying \fB\-no_nonce\fR overrides this.
.IP "\fB\-req_text\fR, \fB\-resp_text\fR, \fB\-text\fR" 4
.IX Item "-req_text, -resp_text, -text"
Print out the text form of the OCSP request, response or both respectively.
.IP "\fB\-reqout\fR \fIfile\fR, \fB\-respout\fR \fIfile\fR" 4
.IX Item "-reqout file, -respout file"
Write out the DER encoded certificate request or response to \fIfile\fR.
.IP "\fB\-reqin\fR \fIfile\fR, \fB\-respin\fR \fIfile\fR" 4
.IX Item "-reqin file, -respin file"
Read OCSP request or response file from \fIfile\fR. These option are ignored
if OCSP request or response creation is implied by other options (for example
with \fB\-serial\fR, \fB\-cert\fR and \fB\-host\fR options).
.IP "\fB\-url\fR \fIresponder_url\fR" 4
.IX Item "-url responder_url"
Specify the responder URL. Both HTTP and HTTPS (SSL/TLS) URLs can be specified.
The optional userinfo and fragment components are ignored.
Any given query component is handled as part of the path component.
.IP "\fB\-host\fR \fIhostname\fR:\fIport\fR, \fB\-path\fR \fIpathname\fR" 4
.IX Item "-host hostname:port, -path pathname"
If the \fB\-host\fR option is present then the OCSP request is sent to the host
\&\fIhostname\fR on port \fIport\fR. The \fB\-path\fR option specifies the HTTP pathname
to use or "/" by default.  This is equivalent to specifying \fB\-url\fR with scheme
http:// and the given hostname, port, and pathname.
.IP "\fB\-proxy\fR \fI[http[s]://][userinfo@]host[:port][/path]\fR" 4
.IX Item "-proxy [http[s]://][userinfo@]host[:port][/path]"
The HTTP(S) proxy server to use for reaching the OCSP server unless \fB\-no_proxy\fR
applies, see below.
The proxy port defaults to 80 or 443 if the scheme is \f(CW\*(C`https\*(C'\fR; apart from that
the optional \f(CW\*(C`http://\*(C'\fR or \f(CW\*(C`https://\*(C'\fR prefix is ignored,
as well as any userinfo and path components.
Defaults to the environment variable \f(CW\*(C`http_proxy\*(C'\fR if set, else \f(CW\*(C`HTTP_PROXY\*(C'\fR
in case no TLS is used, otherwise \f(CW\*(C`https_proxy\*(C'\fR if set, else \f(CW\*(C`HTTPS_PROXY\*(C'\fR.
.IP "\fB\-no_proxy\fR \fIaddresses\fR" 4
.IX Item "-no_proxy addresses"
List of IP addresses and/or DNS names of servers
not to use an HTTP(S) proxy for, separated by commas and/or whitespace
(where in the latter case the whole argument must be enclosed in "...").
Default is from the environment variable \f(CW\*(C`no_proxy\*(C'\fR if set, else \f(CW\*(C`NO_PROXY\*(C'\fR.
.IP "\fB\-header\fR \fIname\fR=\fIvalue\fR" 4
.IX Item "-header name=value"
Adds the header \fIname\fR with the specified \fIvalue\fR to the OCSP request
that is sent to the responder.
This may be repeated.
.IP "\fB\-timeout\fR \fIseconds\fR" 4
.IX Item "-timeout seconds"
Connection timeout to the OCSP responder in seconds.
On POSIX systems, when running as an OCSP responder, this option also limits
the time that the responder is willing to wait for the client request.
This time is measured from the time the responder accepts the connection until
the complete request is received.
.IP "\fB\-verify_other\fR \fIfile\fR" 4
.IX Item "-verify_other file"
File or URI containing additional certificates to search
when attempting to locate
the OCSP response signing certificate. Some responders omit the actual signer's
certificate from the response: this option can be used to supply the necessary
certificate in such cases.
The input can be in PEM, DER, or PKCS#12 format.
.IP \fB\-trust_other\fR 4
.IX Item "-trust_other"
The certificates specified by the \fB\-verify_other\fR option should be explicitly
trusted and no additional checks will be performed on them. This is useful
when the complete responder certificate chain is not available or trusting a
root CA is not appropriate.
.IP "\fB\-VAfile\fR \fIfile\fR" 4
.IX Item "-VAfile file"
File or URI containing explicitly trusted responder certificates.
Equivalent to the \fB\-verify_other\fR and \fB\-trust_other\fR options.
The input can be in PEM, DER, or PKCS#12 format.
.IP \fB\-noverify\fR 4
.IX Item "-noverify"
Don't attempt to verify the OCSP response signature or the nonce
values. This option will normally only be used for debugging since it
disables all verification of the responders certificate.
.IP \fB\-no_intern\fR 4
.IX Item "-no_intern"
Ignore certificates contained in the OCSP response when searching for the
signers certificate. With this option the signers certificate must be specified
with either the \fB\-verify_other\fR or \fB\-VAfile\fR options.
.IP \fB\-no_signature_verify\fR 4
.IX Item "-no_signature_verify"
Don't check the signature on the OCSP response. Since this option
tolerates invalid signatures on OCSP responses it will normally only be
used for testing purposes.
.IP \fB\-no_cert_verify\fR 4
.IX Item "-no_cert_verify"
Don't verify the OCSP response signers certificate at all. Since this
option allows the OCSP response to be signed by any certificate it should
only be used for testing purposes.
.IP \fB\-no_chain\fR 4
.IX Item "-no_chain"
Do not use certificates in the response as additional untrusted CA
certificates.
.IP \fB\-no_explicit\fR 4
.IX Item "-no_explicit"
Do not explicitly trust the root CA if it is set to be trusted for OCSP signing.
.IP \fB\-no_cert_checks\fR 4
.IX Item "-no_cert_checks"
Don't perform any additional checks on the OCSP response signers certificate.
That is do not make any checks to see if the signers certificate is authorised
to provide the necessary status information: as a result this option should
only be used for testing purposes.
.IP "\fB\-validity_period\fR \fInsec\fR, \fB\-status_age\fR \fIage\fR" 4
.IX Item "-validity_period nsec, -status_age age"
These options specify the range of times, in seconds, which will be tolerated
in an OCSP response. Each certificate status response includes a \fBnotBefore\fR
time and an optional \fBnotAfter\fR time. The current time should fall between
these two values, but the interval between the two times may be only a few
seconds. In practice the OCSP responder and clients clocks may not be precisely
synchronised and so such a check may fail. To avoid this the
\&\fB\-validity_period\fR option can be used to specify an acceptable error range in
seconds, the default value is 5 minutes.
.Sp
If the \fBnotAfter\fR time is omitted from a response then this means that new
status information is immediately available. In this case the age of the
\&\fBnotBefore\fR field is checked to see it is not older than \fIage\fR seconds old.
By default this additional check is not performed.
.IP "\fB\-rcid\fR \fIdigest\fR" 4
.IX Item "-rcid digest"
This option sets the digest algorithm to use for certificate identification
in the OCSP response. Any digest supported by the \fBopenssl\-dgst\fR\|(1) command can
be used. The default is the same digest algorithm used in the request.
.IP \fB\-\fR\f(BIdigest\fR 4
.IX Item "-digest"
This option sets digest algorithm to use for certificate identification in the
OCSP request. Any digest supported by the OpenSSL \fBdgst\fR command can be used.
The default is SHA\-1. This option may be used multiple times to specify the
digest used by subsequent certificate identifiers.
.IP "\fB\-CAfile\fR \fIfile\fR, \fB\-no\-CAfile\fR, \fB\-CApath\fR \fIdir\fR, \fB\-no\-CApath\fR, \fB\-CAstore\fR \fIuri\fR, \fB\-no\-CAstore\fR" 4
.IX Item "-CAfile file, -no-CAfile, -CApath dir, -no-CApath, -CAstore uri, -no-CAstore"
See "Trusted Certificate Options" in \fBopenssl\-verification\-options\fR\|(1) for details.
.IP "\fB\-allow_proxy_certs\fR, \fB\-attime\fR, \fB\-no_check_time\fR, \fB\-check_ss_sig\fR, \fB\-crl_check\fR, \fB\-crl_check_all\fR, \fB\-explicit_policy\fR, \fB\-extended_crl\fR, \fB\-ignore_critical\fR, \fB\-inhibit_any\fR, \fB\-inhibit_map\fR, \fB\-no_alt_chains\fR, \fB\-partial_chain\fR, \fB\-policy\fR, \fB\-policy_check\fR, \fB\-policy_print\fR, \fB\-purpose\fR, \fB\-suiteB_128\fR, \fB\-suiteB_128_only\fR, \fB\-suiteB_192\fR, \fB\-trusted_first\fR, \fB\-use_deltas\fR, \fB\-auth_level\fR, \fB\-verify_depth\fR, \fB\-verify_email\fR, \fB\-verify_hostname\fR, \fB\-verify_ip\fR, \fB\-verify_name\fR, \fB\-x509_strict\fR \fB\-issuer_checks\fR" 4
.IX Item "-allow_proxy_certs, -attime, -no_check_time, -check_ss_sig, -crl_check, -crl_check_all, -explicit_policy, -extended_crl, -ignore_critical, -inhibit_any, -inhibit_map, -no_alt_chains, -partial_chain, -policy, -policy_check, -policy_print, -purpose, -suiteB_128, -suiteB_128_only, -suiteB_192, -trusted_first, -use_deltas, -auth_level, -verify_depth, -verify_email, -verify_hostname, -verify_ip, -verify_name, -x509_strict -issuer_checks"
Set various options of certificate chain verification.
See "Verification Options" in \fBopenssl\-verification\-options\fR\|(1) for details.
.IP "\fB\-provider\fR \fIname\fR" 4
.IX Item "-provider name"
.PD 0
.IP "\fB\-provider\-path\fR \fIpath\fR" 4
.IX Item "-provider-path path"
.IP "\fB\-propquery\fR \fIpropq\fR" 4
.IX Item "-propquery propq"
.PD
See "Provider Options" in \fBopenssl\fR\|(1), \fBprovider\fR\|(7), and \fBproperty\fR\|(7).
.SS "OCSP Server Options"
.IX Subsection "OCSP Server Options"
.IP "\fB\-index\fR \fIindexfile\fR" 4
.IX Item "-index indexfile"
The \fIindexfile\fR parameter is the name of a text index file in \fBca\fR
format containing certificate revocation information.
.Sp
If the \fB\-index\fR option is specified then this command switches to
responder mode, otherwise it is in client mode. The request(s) the responder
processes can be either specified on the command line (using \fB\-issuer\fR
and \fB\-serial\fR options), supplied in a file (using the \fB\-reqin\fR option)
or via external OCSP clients (if \fB\-port\fR or \fB\-url\fR is specified).
.Sp
If the \fB\-index\fR option is present then the \fB\-CA\fR and \fB\-rsigner\fR options
must also be present.
.IP "\fB\-CA\fR \fIfile\fR" 4
.IX Item "-CA file"
CA certificate corresponding to the revocation information in the index
file given with \fB\-index\fR.
The input can be in PEM, DER, or PKCS#12 format.
.IP "\fB\-rsigner\fR \fIfile\fR" 4
.IX Item "-rsigner file"
The certificate to sign OCSP responses with.
.IP "\fB\-rkey\fR \fIfile\fR" 4
.IX Item "-rkey file"
The private key to sign OCSP responses with: if not present the file
specified in the \fB\-rsigner\fR option is used.
.IP "\fB\-passin\fR \fIarg\fR" 4
.IX Item "-passin arg"
The private key password source. For more information about the format of \fIarg\fR
see \fBopenssl\-passphrase\-options\fR\|(1).
.IP "\fB\-rother\fR \fIfile\fR" 4
.IX Item "-rother file"
Additional certificates to include in the OCSP response.
The input can be in PEM, DER, or PKCS#12 format.
.IP "\fB\-rsigopt\fR \fInm\fR:\fIv\fR" 4
.IX Item "-rsigopt nm:v"
Pass options to the signature algorithm when signing OCSP responses.
Names and values of these options are algorithm-specific.
.IP "\fB\-rmd\fR \fIdigest\fR" 4
.IX Item "-rmd digest"
The digest to use when signing the response.
.IP \fB\-badsig\fR 4
.IX Item "-badsig"
Corrupt the response signature before writing it; this can be useful
for testing.
.IP \fB\-resp_no_certs\fR 4
.IX Item "-resp_no_certs"
Don't include any certificates in the OCSP response.
.IP \fB\-resp_key_id\fR 4
.IX Item "-resp_key_id"
Identify the signer certificate using the key ID, default is to use the
subject name.
.IP "\fB\-port\fR \fIportnum\fR" 4
.IX Item "-port portnum"
Port to listen for OCSP requests on. The port may also be specified
using the \fBurl\fR option.
A \f(CW0\fR argument indicates that any available port shall be chosen automatically.
.IP \fB\-ignore_err\fR 4
.IX Item "-ignore_err"
Ignore malformed requests or responses: When acting as an OCSP client, retry if
a malformed response is received. When acting as an OCSP responder, continue
running instead of terminating upon receiving a malformed request.
.IP "\fB\-nrequest\fR \fInumber\fR" 4
.IX Item "-nrequest number"
The OCSP server will exit after receiving \fInumber\fR requests, default unlimited.
.IP "\fB\-multi\fR \fIprocess-count\fR" 4
.IX Item "-multi process-count"
Run the specified number of OCSP responder child processes, with the parent
process respawning child processes as needed.
Child processes will detect changes in the CA index file and automatically
reload it.
When running as a responder \fB\-timeout\fR option is recommended to limit the time
each child is willing to wait for the client's OCSP response.
This option is available on POSIX systems (that support the \fBfork()\fR and other
required unix system-calls).
.IP "\fB\-nmin\fR \fIminutes\fR, \fB\-ndays\fR \fIdays\fR" 4
.IX Item "-nmin minutes, -ndays days"
Number of minutes or days when fresh revocation information is available:
used in the \fBnextUpdate\fR field. If neither option is present then the
\&\fBnextUpdate\fR field is omitted meaning fresh revocation information is
immediately available.
.SH "OCSP RESPONSE VERIFICATION"
.IX Header "OCSP RESPONSE VERIFICATION"
OCSP Response follows the rules specified in RFC2560.
.PP
Initially the OCSP responder certificate is located and the signature on
the OCSP request checked using the responder certificate's public key.
.PP
Then a normal certificate verify is performed on the OCSP responder certificate
building up a certificate chain in the process. The locations of the trusted
certificates used to build the chain can be specified by the \fB\-CAfile\fR,
\&\fB\-CApath\fR or \fB\-CAstore\fR options or they will be looked for in the
standard OpenSSL certificates directory.
.PP
If the initial verify fails then the OCSP verify process halts with an
error.
.PP
Otherwise the issuing CA certificate in the request is compared to the OCSP
responder certificate: if there is a match then the OCSP verify succeeds.
.PP
Otherwise the OCSP responder certificate's CA is checked against the issuing
CA certificate in the request. If there is a match and the OCSPSigning
extended key usage is present in the OCSP responder certificate then the
OCSP verify succeeds.
.PP
Otherwise, if \fB\-no_explicit\fR is \fBnot\fR set the root CA of the OCSP responders
CA is checked to see if it is trusted for OCSP signing. If it is the OCSP
verify succeeds.
.PP
If none of these checks is successful then the OCSP verify fails.
.PP
What this effectively means if that if the OCSP responder certificate is
authorised directly by the CA it is issuing revocation information about
(and it is correctly configured) then verification will succeed.
.PP
If the OCSP responder is a "global responder" which can give details about
multiple CAs and has its own separate certificate chain then its root
CA can be trusted for OCSP signing. For example:
.PP
.Vb 1
\& openssl x509 \-in ocspCA.pem \-addtrust OCSPSigning \-out trustedCA.pem
.Ve
.PP
Alternatively the responder certificate itself can be explicitly trusted
with the \fB\-VAfile\fR option.
.SH NOTES
.IX Header "NOTES"
As noted, most of the verify options are for testing or debugging purposes.
Normally only the \fB\-CApath\fR, \fB\-CAfile\fR, \fB\-CAstore\fR and (if the responder
is a 'global VA') \fB\-VAfile\fR options need to be used.
.PP
The OCSP server is only useful for test and demonstration purposes: it is
not really usable as a full OCSP responder. It contains only a very
simple HTTP request handling and can only handle the POST form of OCSP
queries. It also handles requests serially meaning it cannot respond to
new requests until it has processed the current one. The text index file
format of revocation is also inefficient for large quantities of revocation
data.
.PP
It is possible to run this command in responder mode via a CGI
script using the \fB\-reqin\fR and \fB\-respout\fR options.
.SH EXAMPLES
.IX Header "EXAMPLES"
Create an OCSP request and write it to a file:
.PP
.Vb 1
\& openssl ocsp \-issuer issuer.pem \-cert c1.pem \-cert c2.pem \-reqout req.der
.Ve
.PP
Send a query to an OCSP responder with URL http://ocsp.myhost.com/ save the
response to a file, print it out in text form, and verify the response:
.PP
.Vb 2
\& openssl ocsp \-issuer issuer.pem \-cert c1.pem \-cert c2.pem \e
\&     \-url http://ocsp.myhost.com/ \-resp_text \-respout resp.der
.Ve
.PP
Read in an OCSP response and print out text form:
.PP
.Vb 1
\& openssl ocsp \-respin resp.der \-text \-noverify
.Ve
.PP
OCSP server on port 8888 using a standard \fBca\fR configuration, and a separate
responder certificate. All requests and responses are printed to a file.
.PP
.Vb 2
\& openssl ocsp \-index demoCA/index.txt \-port 8888 \-rsigner rcert.pem \-CA demoCA/cacert.pem
\&        \-text \-out log.txt
.Ve
.PP
As above but exit after processing one request:
.PP
.Vb 2
\& openssl ocsp \-index demoCA/index.txt \-port 8888 \-rsigner rcert.pem \-CA demoCA/cacert.pem
\&     \-nrequest 1
.Ve
.PP
Query status information using an internally generated request:
.PP
.Vb 2
\& openssl ocsp \-index demoCA/index.txt \-rsigner rcert.pem \-CA demoCA/cacert.pem
\&     \-issuer demoCA/cacert.pem \-serial 1
.Ve
.PP
Query status information using request read from a file, and write the response
to a second file.
.PP
.Vb 2
\& openssl ocsp \-index demoCA/index.txt \-rsigner rcert.pem \-CA demoCA/cacert.pem
\&     \-reqin req.der \-respout resp.der
.Ve
.SH HISTORY
.IX Header "HISTORY"
The \-no_alt_chains option was added in OpenSSL 1.1.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2001\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
