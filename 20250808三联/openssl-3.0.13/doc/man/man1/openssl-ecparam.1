.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "OPENSSL-ECPARAM 1ossl"
.TH OPENSSL-ECPARAM 1ossl 2025-08-08 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
openssl\-ecparam \- EC parameter manipulation and generation
.SH SYNOPSIS
.IX Header "SYNOPSIS"
\&\fBopenssl ecparam\fR
[\fB\-help\fR]
[\fB\-inform\fR \fBDER\fR|\fBPEM\fR]
[\fB\-outform\fR \fBDER\fR|\fBPEM\fR]
[\fB\-in\fR \fIfilename\fR]
[\fB\-out\fR \fIfilename\fR]
[\fB\-noout\fR]
[\fB\-text\fR]
[\fB\-check\fR]
[\fB\-check_named\fR]
[\fB\-name\fR \fIarg\fR]
[\fB\-list_curves\fR]
[\fB\-conv_form\fR \fIarg\fR]
[\fB\-param_enc\fR \fIarg\fR]
[\fB\-no_seed\fR]
[\fB\-genkey\fR]
[\fB\-engine\fR \fIid\fR]
[\fB\-rand\fR \fIfiles\fR]
[\fB\-writerand\fR \fIfile\fR]
[\fB\-provider\fR \fIname\fR]
[\fB\-provider\-path\fR \fIpath\fR]
[\fB\-propquery\fR \fIpropq\fR]
.SH DESCRIPTION
.IX Header "DESCRIPTION"
This command is used to manipulate or generate EC parameter files.
.PP
OpenSSL is currently not able to generate new groups and therefore
this command can only create EC parameters from known (named) curves.
.SH OPTIONS
.IX Header "OPTIONS"
.IP \fB\-help\fR 4
.IX Item "-help"
Print out a usage message.
.IP "\fB\-inform\fR \fBDER\fR|\fBPEM\fR" 4
.IX Item "-inform DER|PEM"
The EC parameters input format; unspecified by default.
See \fBopenssl\-format\-options\fR\|(1) for details.
.IP "\fB\-outform\fR \fBDER\fR|\fBPEM\fR" 4
.IX Item "-outform DER|PEM"
The EC parameters output format; the default is \fBPEM\fR.
See \fBopenssl\-format\-options\fR\|(1) for details.
.Sp
Parameters are encoded as \fBEcpkParameters\fR as specified in IETF RFC 3279.
.IP "\fB\-in\fR \fIfilename\fR" 4
.IX Item "-in filename"
This specifies the input filename to read parameters from or standard input if
this option is not specified.
.IP "\fB\-out\fR \fIfilename\fR" 4
.IX Item "-out filename"
This specifies the output filename parameters to. Standard output is used
if this option is not present. The output filename should \fBnot\fR be the same
as the input filename.
.IP \fB\-noout\fR 4
.IX Item "-noout"
This option inhibits the output of the encoded version of the parameters.
.IP \fB\-text\fR 4
.IX Item "-text"
This option prints out the EC parameters in human readable form.
.IP \fB\-check\fR 4
.IX Item "-check"
Validate the elliptic curve parameters.
.IP \fB\-check_named\fR 4
.IX Item "-check_named"
Validate the elliptic name curve parameters by checking if the curve parameters
match any built-in curves.
.IP "\fB\-name\fR \fIarg\fR" 4
.IX Item "-name arg"
Use the EC parameters with the specified 'short' name. Use \fB\-list_curves\fR
to get a list of all currently implemented EC parameters.
.IP \fB\-list_curves\fR 4
.IX Item "-list_curves"
Print out a list of all currently implemented EC parameters names and exit.
.IP "\fB\-conv_form\fR \fIarg\fR" 4
.IX Item "-conv_form arg"
This specifies how the points on the elliptic curve are converted
into octet strings. Possible values are: \fBcompressed\fR, \fBuncompressed\fR (the
default value) and \fBhybrid\fR. For more information regarding
the point conversion forms please read the X9.62 standard.
\&\fBNote\fR Due to patent issues the \fBcompressed\fR option is disabled
by default for binary curves and can be enabled by defining
the preprocessor macro \fBOPENSSL_EC_BIN_PT_COMP\fR at compile time.
.IP "\fB\-param_enc\fR \fIarg\fR" 4
.IX Item "-param_enc arg"
This specifies how the elliptic curve parameters are encoded.
Possible value are: \fBnamed_curve\fR, i.e. the ec parameters are
specified by an OID, or \fBexplicit\fR where the ec parameters are
explicitly given (see RFC 3279 for the definition of the
EC parameters structures). The default value is \fBnamed_curve\fR.
\&\fBNote\fR the \fBimplicitlyCA\fR alternative, as specified in RFC 3279,
is currently not implemented in OpenSSL.
.IP \fB\-no_seed\fR 4
.IX Item "-no_seed"
This option inhibits that the 'seed' for the parameter generation
is included in the ECParameters structure (see RFC 3279).
.IP \fB\-genkey\fR 4
.IX Item "-genkey"
This option will generate an EC private key using the specified parameters.
.IP "\fB\-engine\fR \fIid\fR" 4
.IX Item "-engine id"
See "Engine Options" in \fBopenssl\fR\|(1).
This option is deprecated.
.IP "\fB\-rand\fR \fIfiles\fR, \fB\-writerand\fR \fIfile\fR" 4
.IX Item "-rand files, -writerand file"
See "Random State Options" in \fBopenssl\fR\|(1) for details.
.IP "\fB\-provider\fR \fIname\fR" 4
.IX Item "-provider name"
.PD 0
.IP "\fB\-provider\-path\fR \fIpath\fR" 4
.IX Item "-provider-path path"
.IP "\fB\-propquery\fR \fIpropq\fR" 4
.IX Item "-propquery propq"
.PD
See "Provider Options" in \fBopenssl\fR\|(1), \fBprovider\fR\|(7), and \fBproperty\fR\|(7).
.PP
The \fBopenssl\-genpkey\fR\|(1) and \fBopenssl\-pkeyparam\fR\|(1) commands are capable
of performing all the operations this command can, as well as supporting
other public key types.
.SH EXAMPLES
.IX Header "EXAMPLES"
The documentation for the \fBopenssl\-genpkey\fR\|(1) and \fBopenssl\-pkeyparam\fR\|(1)
commands contains examples equivalent to the ones listed here.
.PP
To create EC parameters with the group 'prime192v1':
.PP
.Vb 1
\&  openssl ecparam \-out ec_param.pem \-name prime192v1
.Ve
.PP
To create EC parameters with explicit parameters:
.PP
.Vb 1
\&  openssl ecparam \-out ec_param.pem \-name prime192v1 \-param_enc explicit
.Ve
.PP
To validate given EC parameters:
.PP
.Vb 1
\&  openssl ecparam \-in ec_param.pem \-check
.Ve
.PP
To create EC parameters and a private key:
.PP
.Vb 1
\&  openssl ecparam \-out ec_key.pem \-name prime192v1 \-genkey
.Ve
.PP
To change the point encoding to 'compressed':
.PP
.Vb 1
\&  openssl ecparam \-in ec_in.pem \-out ec_out.pem \-conv_form compressed
.Ve
.PP
To print out the EC parameters to standard output:
.PP
.Vb 1
\&  openssl ecparam \-in ec_param.pem \-noout \-text
.Ve
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBopenssl\fR\|(1),
\&\fBopenssl\-pkeyparam\fR\|(1),
\&\fBopenssl\-genpkey\fR\|(1),
\&\fBopenssl\-ec\fR\|(1),
\&\fBopenssl\-dsaparam\fR\|(1)
.SH HISTORY
.IX Header "HISTORY"
The \fB\-engine\fR option was deprecated in OpenSSL 3.0.
.PP
The \fB\-C\fR option was removed in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2003\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
