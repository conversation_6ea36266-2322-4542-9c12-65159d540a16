.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "OPENSSL-CMDS 1ossl"
.TH OPENSSL-CMDS 1ossl 2025-08-08 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
asn1parse,
ca,
ciphers,
cmp,
cms,
crl,
crl2pkcs7,
dgst,
dhparam,
dsa,
dsaparam,
ec,
ecparam,
enc,
engine,
errstr,
gendsa,
genpkey,
genrsa,
info,
kdf,
mac,
nseq,
ocsp,
passwd,
pkcs12,
pkcs7,
pkcs8,
pkey,
pkeyparam,
pkeyutl,
prime,
rand,
rehash,
req,
rsa,
rsautl,
s_client,
s_server,
s_time,
sess_id,
smime,
speed,
spkac,
srp,
storeutl,
ts,
verify,
version,
x509
\&\- OpenSSL application commands
.SH SYNOPSIS
.IX Header "SYNOPSIS"
\&\fBopenssl\fR \fIcmd\fR \fB\-help\fR | [\fI\-option\fR | \fI\-option\fR \fIarg\fR] ... [\fIarg\fR] ...
.SH DESCRIPTION
.IX Header "DESCRIPTION"
Every \fIcmd\fR listed above is a (sub\-)command of the \fBopenssl\fR\|(1) application.
It has its own detailed manual page at \fBopenssl\-\fR\f(BIcmd\fR(1). For example, to
view the manual page for the \fBopenssl dgst\fR command, type \f(CW\*(C`man openssl\-dgst\*(C'\fR.
.SH OPTIONS
.IX Header "OPTIONS"
Among others, every subcommand has a help option.
.IP \fB\-help\fR 4
.IX Item "-help"
Print out a usage message for the subcommand.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBopenssl\fR\|(1),
\&\fBopenssl\-asn1parse\fR\|(1),
\&\fBopenssl\-ca\fR\|(1),
\&\fBopenssl\-ciphers\fR\|(1),
\&\fBopenssl\-cmp\fR\|(1),
\&\fBopenssl\-cms\fR\|(1),
\&\fBopenssl\-crl\fR\|(1),
\&\fBopenssl\-crl2pkcs7\fR\|(1),
\&\fBopenssl\-dgst\fR\|(1),
\&\fBopenssl\-dhparam\fR\|(1),
\&\fBopenssl\-dsa\fR\|(1),
\&\fBopenssl\-dsaparam\fR\|(1),
\&\fBopenssl\-ec\fR\|(1),
\&\fBopenssl\-ecparam\fR\|(1),
\&\fBopenssl\-enc\fR\|(1),
\&\fBopenssl\-engine\fR\|(1),
\&\fBopenssl\-errstr\fR\|(1),
\&\fBopenssl\-gendsa\fR\|(1),
\&\fBopenssl\-genpkey\fR\|(1),
\&\fBopenssl\-genrsa\fR\|(1),
\&\fBopenssl\-info\fR\|(1),
\&\fBopenssl\-kdf\fR\|(1),
\&\fBopenssl\-mac\fR\|(1),
\&\fBopenssl\-nseq\fR\|(1),
\&\fBopenssl\-ocsp\fR\|(1),
\&\fBopenssl\-passwd\fR\|(1),
\&\fBopenssl\-pkcs12\fR\|(1),
\&\fBopenssl\-pkcs7\fR\|(1),
\&\fBopenssl\-pkcs8\fR\|(1),
\&\fBopenssl\-pkey\fR\|(1),
\&\fBopenssl\-pkeyparam\fR\|(1),
\&\fBopenssl\-pkeyutl\fR\|(1),
\&\fBopenssl\-prime\fR\|(1),
\&\fBopenssl\-rand\fR\|(1),
\&\fBopenssl\-rehash\fR\|(1),
\&\fBopenssl\-req\fR\|(1),
\&\fBopenssl\-rsa\fR\|(1),
\&\fBopenssl\-rsautl\fR\|(1),
\&\fBopenssl\-s_client\fR\|(1),
\&\fBopenssl\-s_server\fR\|(1),
\&\fBopenssl\-s_time\fR\|(1),
\&\fBopenssl\-sess_id\fR\|(1),
\&\fBopenssl\-smime\fR\|(1),
\&\fBopenssl\-speed\fR\|(1),
\&\fBopenssl\-spkac\fR\|(1),
\&\fBopenssl\-srp\fR\|(1),
\&\fBopenssl\-storeutl\fR\|(1),
\&\fBopenssl\-ts\fR\|(1),
\&\fBopenssl\-verify\fR\|(1),
\&\fBopenssl\-version\fR\|(1),
\&\fBopenssl\-x509\fR\|(1),
.SH HISTORY
.IX Header "HISTORY"
Initially, the manual page entry for the \f(CW\*(C`openssl \fR\f(CIcmd\fR\f(CW\*(C'\fR command used
to be available at \fIcmd\fR(1). Later, the alias \fBopenssl\-\fR\f(BIcmd\fR(1) was
introduced, which made it easier to group the openssl commands using
the \fBapropos\fR\|(1) command or the shell's tab completion.
.PP
In order to reduce cluttering of the global manual page namespace,
the manual page entries without the 'openssl\-' prefix have been
deprecated in OpenSSL 3.0 and will be removed in OpenSSL 4.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2019\-2022 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
