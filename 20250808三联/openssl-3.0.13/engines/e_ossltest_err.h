/*
 * Generated by util/mkerr.pl DO NOT EDIT
 * Copyright 1995-2021 The OpenSSL Project Authors. All Rights Reserved.
 *
 * Licensed under the Apache License 2.0 (the "License").  You may not use
 * this file except in compliance with the License.  You can obtain a copy
 * in the file LICENSE in the source distribution or at
 * https://www.openssl.org/source/license.html
 */

#ifndef OSSL_E_OSSLTEST_ERR_H
# define OSSL_E_OSSLTEST_ERR_H
# pragma once

# include <openssl/opensslconf.h>
# include <openssl/symhacks.h>


# define OSSLTESTerr(f, r) ERR_OSSLTEST_error(0, (r), OPENSSL_FILE, OPENSSL_LINE)


/*
 * OSSLTEST reason codes.
 */
# define OSSLTEST_R_INIT_FAILED                           100

#endif
