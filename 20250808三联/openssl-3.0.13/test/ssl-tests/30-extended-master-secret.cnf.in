# -*- mode: perl; -*-
# Copyright 2016-2020 The OpenSSL Project Authors. All Rights Reserved.
#
# Licensed under the Apache License 2.0 (the "License").  You may not use
# this file except in compliance with the License.  You can obtain a copy
# in the file LICENSE in the source distribution or at
# https://www.openssl.org/source/license.html


## SSL test configurations

package ssltests;

use OpenSSL::Test::Utils;

our @tests = ();

my @tests_tls1_2 = (
    {
        name => "disable-extended-master-secret-server-sha",
        server => {
          "Options" => "-ExtendedMasterSecret",
        },
        client => {
          "CipherString" => "AES128-SHA",
          "MaxProtocol" => "TLSv1.2"
        },
        test   => {
          "ExpectedResult" => "Success",
        },
    },
    {
        name => "disable-extended-master-secret-client-sha",
        server => {
        },
        client => {
          "CipherString" => "AES128-SHA",
          "Options" => "-ExtendedMasterSecret",
          "MaxProtocol" => "TLSv1.2"
        },
        test   => {
          "ExpectedResult" => "Success",
        },
    },
    {
        name => "disable-extended-master-secret-both-sha",
        server => {
          "Options" => "-ExtendedMasterSecret",
        },
        client => {
          "CipherString" => "AES128-SHA",
          "Options" => "-ExtendedMasterSecret",
          "MaxProtocol" => "TLSv1.2"
        },
        test   => {
          "ExpectedResult" => "Success",
        },
    },
    {
        name => "disable-extended-master-secret-both-resume",
        server => {
          "Options" => "-ExtendedMasterSecret",
        },
        resume_server => {
        },
        client => {
          "CipherString" => "AES128-SHA",
          "Options" => "-ExtendedMasterSecret",
          "MaxProtocol" => "TLSv1.2"
        },
        resume_client => {
          "CipherString" => "AES128-SHA",
          "MaxProtocol" => "TLSv1.2"
        },
        test   => {
	  "HandshakeMode" => "Resume",
          "ExpectedResult" => "Success",
        },
    },
    {
        name => "disable-extended-master-secret-server-sha2",
        server => {
          "Options" => "-ExtendedMasterSecret",
        },
        client => {
          "CipherString" => "AES128-SHA256",
          "MaxProtocol" => "TLSv1.2"
        },
        test   => {
          "ExpectedResult" => "Success",
        },
    },
    {
        name => "disable-extended-master-secret-client-sha2",
        server => {
        },
        client => {
          "CipherString" => "AES128-SHA256",
          "Options" => "-ExtendedMasterSecret",
          "MaxProtocol" => "TLSv1.2"
        },
        test   => {
          "ExpectedResult" => "Success",
        },
    },
    {
        name => "disable-extended-master-secret-both-sha2",
        server => {
          "Options" => "-ExtendedMasterSecret",
        },
        client => {
          "CipherString" => "AES128-SHA256",
          "Options" => "-ExtendedMasterSecret",
          "MaxProtocol" => "TLSv1.2"
        },
        test   => {
          "ExpectedResult" => "Success",
        },
    },
);

push @tests, @tests_tls1_2 unless disabled("tls1_2");
