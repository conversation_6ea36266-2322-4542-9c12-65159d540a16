# Generated with generate_ssl_tests.pl

num_tests = 65

test-0 = 0-resumption
test-1 = 1-resumption
test-2 = 2-resumption
test-3 = 3-resumption
test-4 = 4-resumption
test-5 = 5-resumption
test-6 = 6-resumption
test-7 = 7-resumption
test-8 = 8-resumption
test-9 = 9-resumption
test-10 = 10-resumption
test-11 = 11-resumption
test-12 = 12-resumption
test-13 = 13-resumption
test-14 = 14-resumption
test-15 = 15-resumption
test-16 = 16-resumption
test-17 = 17-resumption
test-18 = 18-resumption
test-19 = 19-resumption
test-20 = 20-resumption
test-21 = 21-resumption
test-22 = 22-resumption
test-23 = 23-resumption
test-24 = 24-resumption
test-25 = 25-resumption
test-26 = 26-resumption
test-27 = 27-resumption
test-28 = 28-resumption
test-29 = 29-resumption
test-30 = 30-resumption
test-31 = 31-resumption
test-32 = 32-resumption
test-33 = 33-resumption
test-34 = 34-resumption
test-35 = 35-resumption
test-36 = 36-resumption
test-37 = 37-resumption
test-38 = 38-resumption
test-39 = 39-resumption
test-40 = 40-resumption
test-41 = 41-resumption
test-42 = 42-resumption
test-43 = 43-resumption
test-44 = 44-resumption
test-45 = 45-resumption
test-46 = 46-resumption
test-47 = 47-resumption
test-48 = 48-resumption
test-49 = 49-resumption
test-50 = 50-resumption
test-51 = 51-resumption
test-52 = 52-resumption
test-53 = 53-resumption
test-54 = 54-resumption
test-55 = 55-resumption
test-56 = 56-resumption
test-57 = 57-resumption
test-58 = 58-resumption
test-59 = 59-resumption
test-60 = 60-resumption
test-61 = 61-resumption
test-62 = 62-resumption
test-63 = 63-resumption
test-64 = 64-resumption-with-hrr
# ===========================================================

[0-resumption]
ssl_conf = 0-resumption-ssl

[0-resumption-ssl]
server = 0-resumption-server
client = 0-resumption-client
resume-server = 0-resumption-resume-server
resume-client = 0-resumption-client

[0-resumption-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
MinProtocol = TLSv1
Options = SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[0-resumption-resume-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
Options = SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[0-resumption-client]
CipherString = DEFAULT:@SECLEVEL=0
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-0]
ExpectedProtocol = TLSv1
HandshakeMode = Resume
ResumptionExpected = Yes


# ===========================================================

[1-resumption]
ssl_conf = 1-resumption-ssl

[1-resumption-ssl]
server = 1-resumption-server
client = 1-resumption-client
resume-server = 1-resumption-resume-server
resume-client = 1-resumption-client

[1-resumption-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
MinProtocol = TLSv1
Options = -SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[1-resumption-resume-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
Options = -SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[1-resumption-client]
CipherString = DEFAULT:@SECLEVEL=0
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-1]
ExpectedProtocol = TLSv1
HandshakeMode = Resume
ResumptionExpected = Yes


# ===========================================================

[2-resumption]
ssl_conf = 2-resumption-ssl

[2-resumption-ssl]
server = 2-resumption-server
client = 2-resumption-client
resume-server = 2-resumption-resume-server
resume-client = 2-resumption-client

[2-resumption-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
MinProtocol = TLSv1
Options = SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[2-resumption-resume-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
Options = SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[2-resumption-client]
CipherString = DEFAULT:@SECLEVEL=0
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-2]
ExpectedProtocol = TLSv1.1
HandshakeMode = Resume
ResumptionExpected = No


# ===========================================================

[3-resumption]
ssl_conf = 3-resumption-ssl

[3-resumption-ssl]
server = 3-resumption-server
client = 3-resumption-client
resume-server = 3-resumption-resume-server
resume-client = 3-resumption-client

[3-resumption-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
MinProtocol = TLSv1
Options = -SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[3-resumption-resume-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
Options = -SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[3-resumption-client]
CipherString = DEFAULT:@SECLEVEL=0
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-3]
ExpectedProtocol = TLSv1.1
HandshakeMode = Resume
ResumptionExpected = No


# ===========================================================

[4-resumption]
ssl_conf = 4-resumption-ssl

[4-resumption-ssl]
server = 4-resumption-server
client = 4-resumption-client
resume-server = 4-resumption-resume-server
resume-client = 4-resumption-client

[4-resumption-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
MinProtocol = TLSv1
Options = SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[4-resumption-resume-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
Options = SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[4-resumption-client]
CipherString = DEFAULT:@SECLEVEL=0
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-4]
ExpectedProtocol = TLSv1.2
HandshakeMode = Resume
ResumptionExpected = No


# ===========================================================

[5-resumption]
ssl_conf = 5-resumption-ssl

[5-resumption-ssl]
server = 5-resumption-server
client = 5-resumption-client
resume-server = 5-resumption-resume-server
resume-client = 5-resumption-client

[5-resumption-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
MinProtocol = TLSv1
Options = -SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[5-resumption-resume-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
Options = -SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[5-resumption-client]
CipherString = DEFAULT:@SECLEVEL=0
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-5]
ExpectedProtocol = TLSv1.2
HandshakeMode = Resume
ResumptionExpected = No


# ===========================================================

[6-resumption]
ssl_conf = 6-resumption-ssl

[6-resumption-ssl]
server = 6-resumption-server
client = 6-resumption-client
resume-server = 6-resumption-resume-server
resume-client = 6-resumption-client

[6-resumption-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
MinProtocol = TLSv1
Options = SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[6-resumption-resume-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
Options = SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[6-resumption-client]
CipherString = DEFAULT:@SECLEVEL=0
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-6]
ExpectedProtocol = TLSv1.3
HandshakeMode = Resume
ResumptionExpected = No


# ===========================================================

[7-resumption]
ssl_conf = 7-resumption-ssl

[7-resumption-ssl]
server = 7-resumption-server
client = 7-resumption-client
resume-server = 7-resumption-resume-server
resume-client = 7-resumption-client

[7-resumption-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
MinProtocol = TLSv1
Options = -SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[7-resumption-resume-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
Options = -SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[7-resumption-client]
CipherString = DEFAULT:@SECLEVEL=0
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-7]
ExpectedProtocol = TLSv1.3
HandshakeMode = Resume
ResumptionExpected = No


# ===========================================================

[8-resumption]
ssl_conf = 8-resumption-ssl

[8-resumption-ssl]
server = 8-resumption-server
client = 8-resumption-client
resume-server = 8-resumption-resume-server
resume-client = 8-resumption-client

[8-resumption-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = TLSv1.1
Options = SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[8-resumption-resume-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
Options = SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[8-resumption-client]
CipherString = DEFAULT:@SECLEVEL=0
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-8]
ExpectedProtocol = TLSv1
HandshakeMode = Resume
ResumptionExpected = No


# ===========================================================

[9-resumption]
ssl_conf = 9-resumption-ssl

[9-resumption-ssl]
server = 9-resumption-server
client = 9-resumption-client
resume-server = 9-resumption-resume-server
resume-client = 9-resumption-client

[9-resumption-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = TLSv1.1
Options = -SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[9-resumption-resume-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
Options = -SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[9-resumption-client]
CipherString = DEFAULT:@SECLEVEL=0
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-9]
ExpectedProtocol = TLSv1
HandshakeMode = Resume
ResumptionExpected = No


# ===========================================================

[10-resumption]
ssl_conf = 10-resumption-ssl

[10-resumption-ssl]
server = 10-resumption-server
client = 10-resumption-client
resume-server = 10-resumption-resume-server
resume-client = 10-resumption-client

[10-resumption-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = TLSv1.1
Options = SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[10-resumption-resume-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
Options = SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[10-resumption-client]
CipherString = DEFAULT:@SECLEVEL=0
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-10]
ExpectedProtocol = TLSv1.1
HandshakeMode = Resume
ResumptionExpected = Yes


# ===========================================================

[11-resumption]
ssl_conf = 11-resumption-ssl

[11-resumption-ssl]
server = 11-resumption-server
client = 11-resumption-client
resume-server = 11-resumption-resume-server
resume-client = 11-resumption-client

[11-resumption-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = TLSv1.1
Options = -SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[11-resumption-resume-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
Options = -SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[11-resumption-client]
CipherString = DEFAULT:@SECLEVEL=0
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-11]
ExpectedProtocol = TLSv1.1
HandshakeMode = Resume
ResumptionExpected = Yes


# ===========================================================

[12-resumption]
ssl_conf = 12-resumption-ssl

[12-resumption-ssl]
server = 12-resumption-server
client = 12-resumption-client
resume-server = 12-resumption-resume-server
resume-client = 12-resumption-client

[12-resumption-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = TLSv1.1
Options = SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[12-resumption-resume-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
Options = SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[12-resumption-client]
CipherString = DEFAULT:@SECLEVEL=0
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-12]
ExpectedProtocol = TLSv1.2
HandshakeMode = Resume
ResumptionExpected = No


# ===========================================================

[13-resumption]
ssl_conf = 13-resumption-ssl

[13-resumption-ssl]
server = 13-resumption-server
client = 13-resumption-client
resume-server = 13-resumption-resume-server
resume-client = 13-resumption-client

[13-resumption-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = TLSv1.1
Options = -SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[13-resumption-resume-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
Options = -SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[13-resumption-client]
CipherString = DEFAULT:@SECLEVEL=0
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-13]
ExpectedProtocol = TLSv1.2
HandshakeMode = Resume
ResumptionExpected = No


# ===========================================================

[14-resumption]
ssl_conf = 14-resumption-ssl

[14-resumption-ssl]
server = 14-resumption-server
client = 14-resumption-client
resume-server = 14-resumption-resume-server
resume-client = 14-resumption-client

[14-resumption-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = TLSv1.1
Options = SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[14-resumption-resume-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
Options = SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[14-resumption-client]
CipherString = DEFAULT:@SECLEVEL=0
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-14]
ExpectedProtocol = TLSv1.3
HandshakeMode = Resume
ResumptionExpected = No


# ===========================================================

[15-resumption]
ssl_conf = 15-resumption-ssl

[15-resumption-ssl]
server = 15-resumption-server
client = 15-resumption-client
resume-server = 15-resumption-resume-server
resume-client = 15-resumption-client

[15-resumption-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = TLSv1.1
Options = -SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[15-resumption-resume-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
Options = -SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[15-resumption-client]
CipherString = DEFAULT:@SECLEVEL=0
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-15]
ExpectedProtocol = TLSv1.3
HandshakeMode = Resume
ResumptionExpected = No


# ===========================================================

[16-resumption]
ssl_conf = 16-resumption-ssl

[16-resumption-ssl]
server = 16-resumption-server
client = 16-resumption-client
resume-server = 16-resumption-resume-server
resume-client = 16-resumption-client

[16-resumption-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1.2
Options = SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[16-resumption-resume-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
Options = SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[16-resumption-client]
CipherString = DEFAULT:@SECLEVEL=0
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-16]
ExpectedProtocol = TLSv1
HandshakeMode = Resume
ResumptionExpected = No


# ===========================================================

[17-resumption]
ssl_conf = 17-resumption-ssl

[17-resumption-ssl]
server = 17-resumption-server
client = 17-resumption-client
resume-server = 17-resumption-resume-server
resume-client = 17-resumption-client

[17-resumption-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1.2
Options = -SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[17-resumption-resume-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
Options = -SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[17-resumption-client]
CipherString = DEFAULT:@SECLEVEL=0
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-17]
ExpectedProtocol = TLSv1
HandshakeMode = Resume
ResumptionExpected = No


# ===========================================================

[18-resumption]
ssl_conf = 18-resumption-ssl

[18-resumption-ssl]
server = 18-resumption-server
client = 18-resumption-client
resume-server = 18-resumption-resume-server
resume-client = 18-resumption-client

[18-resumption-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1.2
Options = SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[18-resumption-resume-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
Options = SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[18-resumption-client]
CipherString = DEFAULT:@SECLEVEL=0
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-18]
ExpectedProtocol = TLSv1.1
HandshakeMode = Resume
ResumptionExpected = No


# ===========================================================

[19-resumption]
ssl_conf = 19-resumption-ssl

[19-resumption-ssl]
server = 19-resumption-server
client = 19-resumption-client
resume-server = 19-resumption-resume-server
resume-client = 19-resumption-client

[19-resumption-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1.2
Options = -SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[19-resumption-resume-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
Options = -SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[19-resumption-client]
CipherString = DEFAULT:@SECLEVEL=0
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-19]
ExpectedProtocol = TLSv1.1
HandshakeMode = Resume
ResumptionExpected = No


# ===========================================================

[20-resumption]
ssl_conf = 20-resumption-ssl

[20-resumption-ssl]
server = 20-resumption-server
client = 20-resumption-client
resume-server = 20-resumption-resume-server
resume-client = 20-resumption-client

[20-resumption-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1.2
Options = SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[20-resumption-resume-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
Options = SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[20-resumption-client]
CipherString = DEFAULT:@SECLEVEL=0
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-20]
ExpectedProtocol = TLSv1.2
HandshakeMode = Resume
ResumptionExpected = Yes


# ===========================================================

[21-resumption]
ssl_conf = 21-resumption-ssl

[21-resumption-ssl]
server = 21-resumption-server
client = 21-resumption-client
resume-server = 21-resumption-resume-server
resume-client = 21-resumption-client

[21-resumption-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1.2
Options = -SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[21-resumption-resume-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
Options = -SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[21-resumption-client]
CipherString = DEFAULT:@SECLEVEL=0
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-21]
ExpectedProtocol = TLSv1.2
HandshakeMode = Resume
ResumptionExpected = Yes


# ===========================================================

[22-resumption]
ssl_conf = 22-resumption-ssl

[22-resumption-ssl]
server = 22-resumption-server
client = 22-resumption-client
resume-server = 22-resumption-resume-server
resume-client = 22-resumption-client

[22-resumption-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1.2
Options = SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[22-resumption-resume-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
Options = SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[22-resumption-client]
CipherString = DEFAULT:@SECLEVEL=0
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-22]
ExpectedProtocol = TLSv1.3
HandshakeMode = Resume
ResumptionExpected = No


# ===========================================================

[23-resumption]
ssl_conf = 23-resumption-ssl

[23-resumption-ssl]
server = 23-resumption-server
client = 23-resumption-client
resume-server = 23-resumption-resume-server
resume-client = 23-resumption-client

[23-resumption-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1.2
Options = -SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[23-resumption-resume-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
Options = -SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[23-resumption-client]
CipherString = DEFAULT:@SECLEVEL=0
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-23]
ExpectedProtocol = TLSv1.3
HandshakeMode = Resume
ResumptionExpected = No


# ===========================================================

[24-resumption]
ssl_conf = 24-resumption-ssl

[24-resumption-ssl]
server = 24-resumption-server
client = 24-resumption-client
resume-server = 24-resumption-resume-server
resume-client = 24-resumption-client

[24-resumption-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.3
Options = SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[24-resumption-resume-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
Options = SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[24-resumption-client]
CipherString = DEFAULT:@SECLEVEL=0
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-24]
ExpectedProtocol = TLSv1
HandshakeMode = Resume
ResumptionExpected = No


# ===========================================================

[25-resumption]
ssl_conf = 25-resumption-ssl

[25-resumption-ssl]
server = 25-resumption-server
client = 25-resumption-client
resume-server = 25-resumption-resume-server
resume-client = 25-resumption-client

[25-resumption-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.3
Options = -SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[25-resumption-resume-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
Options = -SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[25-resumption-client]
CipherString = DEFAULT:@SECLEVEL=0
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-25]
ExpectedProtocol = TLSv1
HandshakeMode = Resume
ResumptionExpected = No


# ===========================================================

[26-resumption]
ssl_conf = 26-resumption-ssl

[26-resumption-ssl]
server = 26-resumption-server
client = 26-resumption-client
resume-server = 26-resumption-resume-server
resume-client = 26-resumption-client

[26-resumption-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.3
Options = SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[26-resumption-resume-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
Options = SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[26-resumption-client]
CipherString = DEFAULT:@SECLEVEL=0
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-26]
ExpectedProtocol = TLSv1.1
HandshakeMode = Resume
ResumptionExpected = No


# ===========================================================

[27-resumption]
ssl_conf = 27-resumption-ssl

[27-resumption-ssl]
server = 27-resumption-server
client = 27-resumption-client
resume-server = 27-resumption-resume-server
resume-client = 27-resumption-client

[27-resumption-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.3
Options = -SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[27-resumption-resume-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
Options = -SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[27-resumption-client]
CipherString = DEFAULT:@SECLEVEL=0
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-27]
ExpectedProtocol = TLSv1.1
HandshakeMode = Resume
ResumptionExpected = No


# ===========================================================

[28-resumption]
ssl_conf = 28-resumption-ssl

[28-resumption-ssl]
server = 28-resumption-server
client = 28-resumption-client
resume-server = 28-resumption-resume-server
resume-client = 28-resumption-client

[28-resumption-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.3
Options = SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[28-resumption-resume-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
Options = SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[28-resumption-client]
CipherString = DEFAULT:@SECLEVEL=0
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-28]
ExpectedProtocol = TLSv1.2
HandshakeMode = Resume
ResumptionExpected = No


# ===========================================================

[29-resumption]
ssl_conf = 29-resumption-ssl

[29-resumption-ssl]
server = 29-resumption-server
client = 29-resumption-client
resume-server = 29-resumption-resume-server
resume-client = 29-resumption-client

[29-resumption-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.3
Options = -SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[29-resumption-resume-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
Options = -SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[29-resumption-client]
CipherString = DEFAULT:@SECLEVEL=0
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-29]
ExpectedProtocol = TLSv1.2
HandshakeMode = Resume
ResumptionExpected = No


# ===========================================================

[30-resumption]
ssl_conf = 30-resumption-ssl

[30-resumption-ssl]
server = 30-resumption-server
client = 30-resumption-client
resume-server = 30-resumption-resume-server
resume-client = 30-resumption-client

[30-resumption-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.3
Options = SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[30-resumption-resume-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
Options = SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[30-resumption-client]
CipherString = DEFAULT:@SECLEVEL=0
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-30]
ExpectedProtocol = TLSv1.3
HandshakeMode = Resume
ResumptionExpected = Yes


# ===========================================================

[31-resumption]
ssl_conf = 31-resumption-ssl

[31-resumption-ssl]
server = 31-resumption-server
client = 31-resumption-client
resume-server = 31-resumption-resume-server
resume-client = 31-resumption-client

[31-resumption-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.3
Options = -SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[31-resumption-resume-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
Options = -SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[31-resumption-client]
CipherString = DEFAULT:@SECLEVEL=0
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-31]
ExpectedProtocol = TLSv1.3
HandshakeMode = Resume
ResumptionExpected = Yes


# ===========================================================

[32-resumption]
ssl_conf = 32-resumption-ssl

[32-resumption-ssl]
server = 32-resumption-server
client = 32-resumption-client
resume-server = 32-resumption-server
resume-client = 32-resumption-resume-client

[32-resumption-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
Options = SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[32-resumption-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
MinProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[32-resumption-resume-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-32]
ExpectedProtocol = TLSv1
HandshakeMode = Resume
ResumptionExpected = Yes


# ===========================================================

[33-resumption]
ssl_conf = 33-resumption-ssl

[33-resumption-ssl]
server = 33-resumption-server
client = 33-resumption-client
resume-server = 33-resumption-server
resume-client = 33-resumption-resume-client

[33-resumption-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
Options = -SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[33-resumption-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
MinProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[33-resumption-resume-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-33]
ExpectedProtocol = TLSv1
HandshakeMode = Resume
ResumptionExpected = Yes


# ===========================================================

[34-resumption]
ssl_conf = 34-resumption-ssl

[34-resumption-ssl]
server = 34-resumption-server
client = 34-resumption-client
resume-server = 34-resumption-server
resume-client = 34-resumption-resume-client

[34-resumption-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
Options = SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[34-resumption-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
MinProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[34-resumption-resume-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-34]
ExpectedProtocol = TLSv1.1
HandshakeMode = Resume
ResumptionExpected = No


# ===========================================================

[35-resumption]
ssl_conf = 35-resumption-ssl

[35-resumption-ssl]
server = 35-resumption-server
client = 35-resumption-client
resume-server = 35-resumption-server
resume-client = 35-resumption-resume-client

[35-resumption-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
Options = -SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[35-resumption-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
MinProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[35-resumption-resume-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-35]
ExpectedProtocol = TLSv1.1
HandshakeMode = Resume
ResumptionExpected = No


# ===========================================================

[36-resumption]
ssl_conf = 36-resumption-ssl

[36-resumption-ssl]
server = 36-resumption-server
client = 36-resumption-client
resume-server = 36-resumption-server
resume-client = 36-resumption-resume-client

[36-resumption-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
Options = SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[36-resumption-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
MinProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[36-resumption-resume-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-36]
ExpectedProtocol = TLSv1.2
HandshakeMode = Resume
ResumptionExpected = No


# ===========================================================

[37-resumption]
ssl_conf = 37-resumption-ssl

[37-resumption-ssl]
server = 37-resumption-server
client = 37-resumption-client
resume-server = 37-resumption-server
resume-client = 37-resumption-resume-client

[37-resumption-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
Options = -SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[37-resumption-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
MinProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[37-resumption-resume-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-37]
ExpectedProtocol = TLSv1.2
HandshakeMode = Resume
ResumptionExpected = No


# ===========================================================

[38-resumption]
ssl_conf = 38-resumption-ssl

[38-resumption-ssl]
server = 38-resumption-server
client = 38-resumption-client
resume-server = 38-resumption-server
resume-client = 38-resumption-resume-client

[38-resumption-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
Options = SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[38-resumption-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
MinProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[38-resumption-resume-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-38]
ExpectedProtocol = TLSv1.3
HandshakeMode = Resume
ResumptionExpected = No


# ===========================================================

[39-resumption]
ssl_conf = 39-resumption-ssl

[39-resumption-ssl]
server = 39-resumption-server
client = 39-resumption-client
resume-server = 39-resumption-server
resume-client = 39-resumption-resume-client

[39-resumption-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
Options = -SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[39-resumption-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
MinProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[39-resumption-resume-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-39]
ExpectedProtocol = TLSv1.3
HandshakeMode = Resume
ResumptionExpected = No


# ===========================================================

[40-resumption]
ssl_conf = 40-resumption-ssl

[40-resumption-ssl]
server = 40-resumption-server
client = 40-resumption-client
resume-server = 40-resumption-server
resume-client = 40-resumption-resume-client

[40-resumption-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
Options = SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[40-resumption-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = TLSv1.1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[40-resumption-resume-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-40]
ExpectedProtocol = TLSv1
HandshakeMode = Resume
ResumptionExpected = No


# ===========================================================

[41-resumption]
ssl_conf = 41-resumption-ssl

[41-resumption-ssl]
server = 41-resumption-server
client = 41-resumption-client
resume-server = 41-resumption-server
resume-client = 41-resumption-resume-client

[41-resumption-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
Options = -SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[41-resumption-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = TLSv1.1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[41-resumption-resume-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-41]
ExpectedProtocol = TLSv1
HandshakeMode = Resume
ResumptionExpected = No


# ===========================================================

[42-resumption]
ssl_conf = 42-resumption-ssl

[42-resumption-ssl]
server = 42-resumption-server
client = 42-resumption-client
resume-server = 42-resumption-server
resume-client = 42-resumption-resume-client

[42-resumption-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
Options = SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[42-resumption-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = TLSv1.1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[42-resumption-resume-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-42]
ExpectedProtocol = TLSv1.1
HandshakeMode = Resume
ResumptionExpected = Yes


# ===========================================================

[43-resumption]
ssl_conf = 43-resumption-ssl

[43-resumption-ssl]
server = 43-resumption-server
client = 43-resumption-client
resume-server = 43-resumption-server
resume-client = 43-resumption-resume-client

[43-resumption-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
Options = -SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[43-resumption-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = TLSv1.1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[43-resumption-resume-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-43]
ExpectedProtocol = TLSv1.1
HandshakeMode = Resume
ResumptionExpected = Yes


# ===========================================================

[44-resumption]
ssl_conf = 44-resumption-ssl

[44-resumption-ssl]
server = 44-resumption-server
client = 44-resumption-client
resume-server = 44-resumption-server
resume-client = 44-resumption-resume-client

[44-resumption-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
Options = SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[44-resumption-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = TLSv1.1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[44-resumption-resume-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-44]
ExpectedProtocol = TLSv1.2
HandshakeMode = Resume
ResumptionExpected = No


# ===========================================================

[45-resumption]
ssl_conf = 45-resumption-ssl

[45-resumption-ssl]
server = 45-resumption-server
client = 45-resumption-client
resume-server = 45-resumption-server
resume-client = 45-resumption-resume-client

[45-resumption-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
Options = -SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[45-resumption-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = TLSv1.1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[45-resumption-resume-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-45]
ExpectedProtocol = TLSv1.2
HandshakeMode = Resume
ResumptionExpected = No


# ===========================================================

[46-resumption]
ssl_conf = 46-resumption-ssl

[46-resumption-ssl]
server = 46-resumption-server
client = 46-resumption-client
resume-server = 46-resumption-server
resume-client = 46-resumption-resume-client

[46-resumption-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
Options = SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[46-resumption-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = TLSv1.1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[46-resumption-resume-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-46]
ExpectedProtocol = TLSv1.3
HandshakeMode = Resume
ResumptionExpected = No


# ===========================================================

[47-resumption]
ssl_conf = 47-resumption-ssl

[47-resumption-ssl]
server = 47-resumption-server
client = 47-resumption-client
resume-server = 47-resumption-server
resume-client = 47-resumption-resume-client

[47-resumption-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
Options = -SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[47-resumption-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = TLSv1.1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[47-resumption-resume-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-47]
ExpectedProtocol = TLSv1.3
HandshakeMode = Resume
ResumptionExpected = No


# ===========================================================

[48-resumption]
ssl_conf = 48-resumption-ssl

[48-resumption-ssl]
server = 48-resumption-server
client = 48-resumption-client
resume-server = 48-resumption-server
resume-client = 48-resumption-resume-client

[48-resumption-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
Options = SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[48-resumption-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[48-resumption-resume-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-48]
ExpectedProtocol = TLSv1
HandshakeMode = Resume
ResumptionExpected = No


# ===========================================================

[49-resumption]
ssl_conf = 49-resumption-ssl

[49-resumption-ssl]
server = 49-resumption-server
client = 49-resumption-client
resume-server = 49-resumption-server
resume-client = 49-resumption-resume-client

[49-resumption-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
Options = -SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[49-resumption-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[49-resumption-resume-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-49]
ExpectedProtocol = TLSv1
HandshakeMode = Resume
ResumptionExpected = No


# ===========================================================

[50-resumption]
ssl_conf = 50-resumption-ssl

[50-resumption-ssl]
server = 50-resumption-server
client = 50-resumption-client
resume-server = 50-resumption-server
resume-client = 50-resumption-resume-client

[50-resumption-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
Options = SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[50-resumption-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[50-resumption-resume-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-50]
ExpectedProtocol = TLSv1.1
HandshakeMode = Resume
ResumptionExpected = No


# ===========================================================

[51-resumption]
ssl_conf = 51-resumption-ssl

[51-resumption-ssl]
server = 51-resumption-server
client = 51-resumption-client
resume-server = 51-resumption-server
resume-client = 51-resumption-resume-client

[51-resumption-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
Options = -SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[51-resumption-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[51-resumption-resume-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-51]
ExpectedProtocol = TLSv1.1
HandshakeMode = Resume
ResumptionExpected = No


# ===========================================================

[52-resumption]
ssl_conf = 52-resumption-ssl

[52-resumption-ssl]
server = 52-resumption-server
client = 52-resumption-client
resume-server = 52-resumption-server
resume-client = 52-resumption-resume-client

[52-resumption-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
Options = SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[52-resumption-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[52-resumption-resume-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-52]
ExpectedProtocol = TLSv1.2
HandshakeMode = Resume
ResumptionExpected = Yes


# ===========================================================

[53-resumption]
ssl_conf = 53-resumption-ssl

[53-resumption-ssl]
server = 53-resumption-server
client = 53-resumption-client
resume-server = 53-resumption-server
resume-client = 53-resumption-resume-client

[53-resumption-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
Options = -SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[53-resumption-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[53-resumption-resume-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-53]
ExpectedProtocol = TLSv1.2
HandshakeMode = Resume
ResumptionExpected = Yes


# ===========================================================

[54-resumption]
ssl_conf = 54-resumption-ssl

[54-resumption-ssl]
server = 54-resumption-server
client = 54-resumption-client
resume-server = 54-resumption-server
resume-client = 54-resumption-resume-client

[54-resumption-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
Options = SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[54-resumption-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[54-resumption-resume-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-54]
ExpectedProtocol = TLSv1.3
HandshakeMode = Resume
ResumptionExpected = No


# ===========================================================

[55-resumption]
ssl_conf = 55-resumption-ssl

[55-resumption-ssl]
server = 55-resumption-server
client = 55-resumption-client
resume-server = 55-resumption-server
resume-client = 55-resumption-resume-client

[55-resumption-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
Options = -SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[55-resumption-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[55-resumption-resume-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-55]
ExpectedProtocol = TLSv1.3
HandshakeMode = Resume
ResumptionExpected = No


# ===========================================================

[56-resumption]
ssl_conf = 56-resumption-ssl

[56-resumption-ssl]
server = 56-resumption-server
client = 56-resumption-client
resume-server = 56-resumption-server
resume-client = 56-resumption-resume-client

[56-resumption-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
Options = SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[56-resumption-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[56-resumption-resume-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-56]
ExpectedProtocol = TLSv1
HandshakeMode = Resume
ResumptionExpected = No


# ===========================================================

[57-resumption]
ssl_conf = 57-resumption-ssl

[57-resumption-ssl]
server = 57-resumption-server
client = 57-resumption-client
resume-server = 57-resumption-server
resume-client = 57-resumption-resume-client

[57-resumption-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
Options = -SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[57-resumption-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[57-resumption-resume-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-57]
ExpectedProtocol = TLSv1
HandshakeMode = Resume
ResumptionExpected = No


# ===========================================================

[58-resumption]
ssl_conf = 58-resumption-ssl

[58-resumption-ssl]
server = 58-resumption-server
client = 58-resumption-client
resume-server = 58-resumption-server
resume-client = 58-resumption-resume-client

[58-resumption-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
Options = SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[58-resumption-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[58-resumption-resume-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-58]
ExpectedProtocol = TLSv1.1
HandshakeMode = Resume
ResumptionExpected = No


# ===========================================================

[59-resumption]
ssl_conf = 59-resumption-ssl

[59-resumption-ssl]
server = 59-resumption-server
client = 59-resumption-client
resume-server = 59-resumption-server
resume-client = 59-resumption-resume-client

[59-resumption-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
Options = -SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[59-resumption-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[59-resumption-resume-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-59]
ExpectedProtocol = TLSv1.1
HandshakeMode = Resume
ResumptionExpected = No


# ===========================================================

[60-resumption]
ssl_conf = 60-resumption-ssl

[60-resumption-ssl]
server = 60-resumption-server
client = 60-resumption-client
resume-server = 60-resumption-server
resume-client = 60-resumption-resume-client

[60-resumption-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
Options = SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[60-resumption-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[60-resumption-resume-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-60]
ExpectedProtocol = TLSv1.2
HandshakeMode = Resume
ResumptionExpected = No


# ===========================================================

[61-resumption]
ssl_conf = 61-resumption-ssl

[61-resumption-ssl]
server = 61-resumption-server
client = 61-resumption-client
resume-server = 61-resumption-server
resume-client = 61-resumption-resume-client

[61-resumption-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
Options = -SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[61-resumption-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[61-resumption-resume-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-61]
ExpectedProtocol = TLSv1.2
HandshakeMode = Resume
ResumptionExpected = No


# ===========================================================

[62-resumption]
ssl_conf = 62-resumption-ssl

[62-resumption-ssl]
server = 62-resumption-server
client = 62-resumption-client
resume-server = 62-resumption-server
resume-client = 62-resumption-resume-client

[62-resumption-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
Options = SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[62-resumption-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[62-resumption-resume-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-62]
ExpectedProtocol = TLSv1.3
HandshakeMode = Resume
ResumptionExpected = Yes


# ===========================================================

[63-resumption]
ssl_conf = 63-resumption-ssl

[63-resumption-ssl]
server = 63-resumption-server
client = 63-resumption-client
resume-server = 63-resumption-server
resume-client = 63-resumption-resume-client

[63-resumption-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
Options = -SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[63-resumption-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[63-resumption-resume-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-63]
ExpectedProtocol = TLSv1.3
HandshakeMode = Resume
ResumptionExpected = Yes


# ===========================================================

[64-resumption-with-hrr]
ssl_conf = 64-resumption-with-hrr-ssl

[64-resumption-with-hrr-ssl]
server = 64-resumption-with-hrr-server
client = 64-resumption-with-hrr-client
resume-server = 64-resumption-with-hrr-server
resume-client = 64-resumption-with-hrr-resume-client

[64-resumption-with-hrr-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
Curves = P-256
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[64-resumption-with-hrr-client]
CipherString = DEFAULT
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[64-resumption-with-hrr-resume-client]
CipherString = DEFAULT
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-64]
ExpectedProtocol = TLSv1.3
HandshakeMode = Resume
Method = TLS
ResumptionExpected = Yes


