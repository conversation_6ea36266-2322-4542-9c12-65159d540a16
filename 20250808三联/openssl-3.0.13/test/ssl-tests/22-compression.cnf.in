# -*- mode: perl; -*-
# Copyright 2016-2020 The OpenSSL Project Authors. All Rights Reserved.
#
# Licensed under the Apache License 2.0 (the "License").  You may not use
# this file except in compliance with the License.  You can obtain a copy
# in the file LICENSE in the source distribution or at
# https://www.openssl.org/source/license.html


## Test Compression

use strict;
use warnings;

package ssltests;
use OpenSSL::Test::Utils;

our @tests = ();

our @tests_tls1_3 = (
    {
        name => "tlsv1_3-both-compress",
        server => {
            "Options" => "Compression"
        },
        client => {
            "Options" => "Compression"
        },
        test => {
            "CompressionExpected" => "No",
            "ExpectedResult" => "Success"
        }
    },
    {
        name => "tlsv1_3-client-compress",
        server => {
        },
        client => {
            "Options" => "Compression"
        },
        test => {
            "CompressionExpected" => "No",
            "ExpectedResult" => "Success"
        }
    },
    {
        name => "tlsv1_3-server-compress",
        server => {
            "Options" => "Compression"
        },
        client => {
        },
        test => {
            "CompressionExpected" => "No",
            "ExpectedResult" => "Success"
        }
    },
    {
        name => "tlsv1_3-neither-compress",
        server => {
        },
        client => {
        },
        test => {
            "CompressionExpected" => "No",
            "ExpectedResult" => "Success"
        }
    },
);
our @tests_tls1_2 = (
    {
        name => "tlsv1_2-both-compress",
        server => {
            "Options" => "Compression"
        },
        client => {
            "Options" => "Compression",
            "MaxProtocol" => "TLSv1.2"
        },
        test => {
            "CompressionExpected" => "Yes",
            "ExpectedResult" => "Success"
        }
    },
    {
        name => "tlsv1_2-client-compress",
        server => {
        },
        client => {
            "Options" => "Compression",
            "MaxProtocol" => "TLSv1.2"
        },
        test => {
            "CompressionExpected" => "No",
            "ExpectedResult" => "Success"
        }
    },
    {
        name => "tlsv1_2-server-compress",
        server => {
            "Options" => "Compression"
        },
        client => {
            "MaxProtocol" => "TLSv1.2"
        },
        test => {
            "CompressionExpected" => "No",
            "ExpectedResult" => "Success"
        }
    },
    {
        name => "tlsv1_2-neither-compress",
        server => {
        },
        client => {
            "MaxProtocol" => "TLSv1.2"
        },
        test => {
            "CompressionExpected" => "No",
            "ExpectedResult" => "Success"
        }
    },
);

push @tests, @tests_tls1_3 unless disabled("tls1_3");
push @tests, @tests_tls1_2 unless alldisabled(("tls1_2", "tls1_1", "tls1",
                                               "ssl3"));
