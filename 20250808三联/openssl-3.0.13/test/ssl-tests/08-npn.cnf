# Generated with generate_ssl_tests.pl

num_tests = 20

test-0 = 0-npn-simple
test-1 = 1-npn-client-finds-match
test-2 = 2-npn-client-honours-server-pref
test-3 = 3-npn-client-first-pref-on-mismatch
test-4 = 4-npn-no-server-support
test-5 = 5-npn-no-client-support
test-6 = 6-npn-with-sni-no-context-switch
test-7 = 7-npn-with-sni-context-switch
test-8 = 8-npn-selected-sni-server-supports-npn
test-9 = 9-npn-selected-sni-server-does-not-support-npn
test-10 = 10-alpn-preferred-over-npn
test-11 = 11-sni-npn-preferred-over-alpn
test-12 = 12-npn-simple-resumption
test-13 = 13-npn-server-switch-resumption
test-14 = 14-npn-client-switch-resumption
test-15 = 15-npn-client-first-pref-on-mismatch-resumption
test-16 = 16-npn-no-server-support-resumption
test-17 = 17-npn-no-client-support-resumption
test-18 = 18-alpn-preferred-over-npn-resumption
test-19 = 19-npn-used-if-alpn-not-supported-resumption
# ===========================================================

[0-npn-simple]
ssl_conf = 0-npn-simple-ssl

[0-npn-simple-ssl]
server = 0-npn-simple-server
client = 0-npn-simple-client

[0-npn-simple-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[0-npn-simple-client]
CipherString = DEFAULT
MaxProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-0]
ExpectedNPNProtocol = foo
server = 0-npn-simple-server-extra
client = 0-npn-simple-client-extra

[0-npn-simple-server-extra]
NPNProtocols = foo

[0-npn-simple-client-extra]
NPNProtocols = foo


# ===========================================================

[1-npn-client-finds-match]
ssl_conf = 1-npn-client-finds-match-ssl

[1-npn-client-finds-match-ssl]
server = 1-npn-client-finds-match-server
client = 1-npn-client-finds-match-client

[1-npn-client-finds-match-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[1-npn-client-finds-match-client]
CipherString = DEFAULT
MaxProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-1]
ExpectedNPNProtocol = bar
server = 1-npn-client-finds-match-server-extra
client = 1-npn-client-finds-match-client-extra

[1-npn-client-finds-match-server-extra]
NPNProtocols = baz,bar

[1-npn-client-finds-match-client-extra]
NPNProtocols = foo,bar


# ===========================================================

[2-npn-client-honours-server-pref]
ssl_conf = 2-npn-client-honours-server-pref-ssl

[2-npn-client-honours-server-pref-ssl]
server = 2-npn-client-honours-server-pref-server
client = 2-npn-client-honours-server-pref-client

[2-npn-client-honours-server-pref-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[2-npn-client-honours-server-pref-client]
CipherString = DEFAULT
MaxProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-2]
ExpectedNPNProtocol = bar
server = 2-npn-client-honours-server-pref-server-extra
client = 2-npn-client-honours-server-pref-client-extra

[2-npn-client-honours-server-pref-server-extra]
NPNProtocols = bar,foo

[2-npn-client-honours-server-pref-client-extra]
NPNProtocols = foo,bar


# ===========================================================

[3-npn-client-first-pref-on-mismatch]
ssl_conf = 3-npn-client-first-pref-on-mismatch-ssl

[3-npn-client-first-pref-on-mismatch-ssl]
server = 3-npn-client-first-pref-on-mismatch-server
client = 3-npn-client-first-pref-on-mismatch-client

[3-npn-client-first-pref-on-mismatch-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[3-npn-client-first-pref-on-mismatch-client]
CipherString = DEFAULT
MaxProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-3]
ExpectedNPNProtocol = foo
server = 3-npn-client-first-pref-on-mismatch-server-extra
client = 3-npn-client-first-pref-on-mismatch-client-extra

[3-npn-client-first-pref-on-mismatch-server-extra]
NPNProtocols = baz

[3-npn-client-first-pref-on-mismatch-client-extra]
NPNProtocols = foo,bar


# ===========================================================

[4-npn-no-server-support]
ssl_conf = 4-npn-no-server-support-ssl

[4-npn-no-server-support-ssl]
server = 4-npn-no-server-support-server
client = 4-npn-no-server-support-client

[4-npn-no-server-support-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[4-npn-no-server-support-client]
CipherString = DEFAULT
MaxProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-4]
client = 4-npn-no-server-support-client-extra

[4-npn-no-server-support-client-extra]
NPNProtocols = foo


# ===========================================================

[5-npn-no-client-support]
ssl_conf = 5-npn-no-client-support-ssl

[5-npn-no-client-support-ssl]
server = 5-npn-no-client-support-server
client = 5-npn-no-client-support-client

[5-npn-no-client-support-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[5-npn-no-client-support-client]
CipherString = DEFAULT
MaxProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-5]
server = 5-npn-no-client-support-server-extra

[5-npn-no-client-support-server-extra]
NPNProtocols = foo


# ===========================================================

[6-npn-with-sni-no-context-switch]
ssl_conf = 6-npn-with-sni-no-context-switch-ssl

[6-npn-with-sni-no-context-switch-ssl]
server = 6-npn-with-sni-no-context-switch-server
client = 6-npn-with-sni-no-context-switch-client
server2 = 6-npn-with-sni-no-context-switch-server2

[6-npn-with-sni-no-context-switch-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[6-npn-with-sni-no-context-switch-server2]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[6-npn-with-sni-no-context-switch-client]
CipherString = DEFAULT
MaxProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-6]
ExpectedNPNProtocol = foo
ExpectedServerName = server1
server = 6-npn-with-sni-no-context-switch-server-extra
server2 = 6-npn-with-sni-no-context-switch-server2-extra
client = 6-npn-with-sni-no-context-switch-client-extra

[6-npn-with-sni-no-context-switch-server-extra]
NPNProtocols = foo
ServerNameCallback = IgnoreMismatch

[6-npn-with-sni-no-context-switch-server2-extra]
NPNProtocols = bar

[6-npn-with-sni-no-context-switch-client-extra]
NPNProtocols = foo,bar
ServerName = server1


# ===========================================================

[7-npn-with-sni-context-switch]
ssl_conf = 7-npn-with-sni-context-switch-ssl

[7-npn-with-sni-context-switch-ssl]
server = 7-npn-with-sni-context-switch-server
client = 7-npn-with-sni-context-switch-client
server2 = 7-npn-with-sni-context-switch-server2

[7-npn-with-sni-context-switch-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[7-npn-with-sni-context-switch-server2]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[7-npn-with-sni-context-switch-client]
CipherString = DEFAULT
MaxProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-7]
ExpectedNPNProtocol = bar
ExpectedServerName = server2
server = 7-npn-with-sni-context-switch-server-extra
server2 = 7-npn-with-sni-context-switch-server2-extra
client = 7-npn-with-sni-context-switch-client-extra

[7-npn-with-sni-context-switch-server-extra]
NPNProtocols = foo
ServerNameCallback = IgnoreMismatch

[7-npn-with-sni-context-switch-server2-extra]
NPNProtocols = bar

[7-npn-with-sni-context-switch-client-extra]
NPNProtocols = foo,bar
ServerName = server2


# ===========================================================

[8-npn-selected-sni-server-supports-npn]
ssl_conf = 8-npn-selected-sni-server-supports-npn-ssl

[8-npn-selected-sni-server-supports-npn-ssl]
server = 8-npn-selected-sni-server-supports-npn-server
client = 8-npn-selected-sni-server-supports-npn-client
server2 = 8-npn-selected-sni-server-supports-npn-server2

[8-npn-selected-sni-server-supports-npn-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[8-npn-selected-sni-server-supports-npn-server2]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[8-npn-selected-sni-server-supports-npn-client]
CipherString = DEFAULT
MaxProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-8]
ExpectedNPNProtocol = bar
ExpectedServerName = server2
server = 8-npn-selected-sni-server-supports-npn-server-extra
server2 = 8-npn-selected-sni-server-supports-npn-server2-extra
client = 8-npn-selected-sni-server-supports-npn-client-extra

[8-npn-selected-sni-server-supports-npn-server-extra]
ServerNameCallback = IgnoreMismatch

[8-npn-selected-sni-server-supports-npn-server2-extra]
NPNProtocols = bar

[8-npn-selected-sni-server-supports-npn-client-extra]
NPNProtocols = foo,bar
ServerName = server2


# ===========================================================

[9-npn-selected-sni-server-does-not-support-npn]
ssl_conf = 9-npn-selected-sni-server-does-not-support-npn-ssl

[9-npn-selected-sni-server-does-not-support-npn-ssl]
server = 9-npn-selected-sni-server-does-not-support-npn-server
client = 9-npn-selected-sni-server-does-not-support-npn-client
server2 = 9-npn-selected-sni-server-does-not-support-npn-server2

[9-npn-selected-sni-server-does-not-support-npn-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[9-npn-selected-sni-server-does-not-support-npn-server2]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[9-npn-selected-sni-server-does-not-support-npn-client]
CipherString = DEFAULT
MaxProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-9]
ExpectedServerName = server2
server = 9-npn-selected-sni-server-does-not-support-npn-server-extra
client = 9-npn-selected-sni-server-does-not-support-npn-client-extra

[9-npn-selected-sni-server-does-not-support-npn-server-extra]
NPNProtocols = bar
ServerNameCallback = IgnoreMismatch

[9-npn-selected-sni-server-does-not-support-npn-client-extra]
NPNProtocols = foo,bar
ServerName = server2


# ===========================================================

[10-alpn-preferred-over-npn]
ssl_conf = 10-alpn-preferred-over-npn-ssl

[10-alpn-preferred-over-npn-ssl]
server = 10-alpn-preferred-over-npn-server
client = 10-alpn-preferred-over-npn-client

[10-alpn-preferred-over-npn-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[10-alpn-preferred-over-npn-client]
CipherString = DEFAULT
MaxProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-10]
ExpectedALPNProtocol = foo
server = 10-alpn-preferred-over-npn-server-extra
client = 10-alpn-preferred-over-npn-client-extra

[10-alpn-preferred-over-npn-server-extra]
ALPNProtocols = foo
NPNProtocols = bar

[10-alpn-preferred-over-npn-client-extra]
ALPNProtocols = foo
NPNProtocols = bar


# ===========================================================

[11-sni-npn-preferred-over-alpn]
ssl_conf = 11-sni-npn-preferred-over-alpn-ssl

[11-sni-npn-preferred-over-alpn-ssl]
server = 11-sni-npn-preferred-over-alpn-server
client = 11-sni-npn-preferred-over-alpn-client
server2 = 11-sni-npn-preferred-over-alpn-server2

[11-sni-npn-preferred-over-alpn-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[11-sni-npn-preferred-over-alpn-server2]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[11-sni-npn-preferred-over-alpn-client]
CipherString = DEFAULT
MaxProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-11]
ExpectedNPNProtocol = bar
ExpectedServerName = server2
server = 11-sni-npn-preferred-over-alpn-server-extra
server2 = 11-sni-npn-preferred-over-alpn-server2-extra
client = 11-sni-npn-preferred-over-alpn-client-extra

[11-sni-npn-preferred-over-alpn-server-extra]
ALPNProtocols = foo
ServerNameCallback = IgnoreMismatch

[11-sni-npn-preferred-over-alpn-server2-extra]
NPNProtocols = bar

[11-sni-npn-preferred-over-alpn-client-extra]
ALPNProtocols = foo
NPNProtocols = bar
ServerName = server2


# ===========================================================

[12-npn-simple-resumption]
ssl_conf = 12-npn-simple-resumption-ssl

[12-npn-simple-resumption-ssl]
server = 12-npn-simple-resumption-server
client = 12-npn-simple-resumption-client
resume-server = 12-npn-simple-resumption-server
resume-client = 12-npn-simple-resumption-client

[12-npn-simple-resumption-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[12-npn-simple-resumption-client]
CipherString = DEFAULT
MaxProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-12]
ExpectedNPNProtocol = foo
HandshakeMode = Resume
ResumptionExpected = Yes
server = 12-npn-simple-resumption-server-extra
resume-server = 12-npn-simple-resumption-server-extra
client = 12-npn-simple-resumption-client-extra
resume-client = 12-npn-simple-resumption-client-extra

[12-npn-simple-resumption-server-extra]
NPNProtocols = foo

[12-npn-simple-resumption-client-extra]
NPNProtocols = foo


# ===========================================================

[13-npn-server-switch-resumption]
ssl_conf = 13-npn-server-switch-resumption-ssl

[13-npn-server-switch-resumption-ssl]
server = 13-npn-server-switch-resumption-server
client = 13-npn-server-switch-resumption-client
resume-server = 13-npn-server-switch-resumption-resume-server
resume-client = 13-npn-server-switch-resumption-client

[13-npn-server-switch-resumption-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[13-npn-server-switch-resumption-resume-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[13-npn-server-switch-resumption-client]
CipherString = DEFAULT
MaxProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-13]
ExpectedNPNProtocol = baz
HandshakeMode = Resume
ResumptionExpected = Yes
server = 13-npn-server-switch-resumption-server-extra
resume-server = 13-npn-server-switch-resumption-resume-server-extra
client = 13-npn-server-switch-resumption-client-extra
resume-client = 13-npn-server-switch-resumption-client-extra

[13-npn-server-switch-resumption-server-extra]
NPNProtocols = bar,foo

[13-npn-server-switch-resumption-resume-server-extra]
NPNProtocols = baz,foo

[13-npn-server-switch-resumption-client-extra]
NPNProtocols = foo,bar,baz


# ===========================================================

[14-npn-client-switch-resumption]
ssl_conf = 14-npn-client-switch-resumption-ssl

[14-npn-client-switch-resumption-ssl]
server = 14-npn-client-switch-resumption-server
client = 14-npn-client-switch-resumption-client
resume-server = 14-npn-client-switch-resumption-server
resume-client = 14-npn-client-switch-resumption-resume-client

[14-npn-client-switch-resumption-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[14-npn-client-switch-resumption-client]
CipherString = DEFAULT
MaxProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[14-npn-client-switch-resumption-resume-client]
CipherString = DEFAULT
MaxProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-14]
ExpectedNPNProtocol = bar
HandshakeMode = Resume
ResumptionExpected = Yes
server = 14-npn-client-switch-resumption-server-extra
resume-server = 14-npn-client-switch-resumption-server-extra
client = 14-npn-client-switch-resumption-client-extra
resume-client = 14-npn-client-switch-resumption-resume-client-extra

[14-npn-client-switch-resumption-server-extra]
NPNProtocols = foo,bar,baz

[14-npn-client-switch-resumption-client-extra]
NPNProtocols = foo,baz

[14-npn-client-switch-resumption-resume-client-extra]
NPNProtocols = bar,baz


# ===========================================================

[15-npn-client-first-pref-on-mismatch-resumption]
ssl_conf = 15-npn-client-first-pref-on-mismatch-resumption-ssl

[15-npn-client-first-pref-on-mismatch-resumption-ssl]
server = 15-npn-client-first-pref-on-mismatch-resumption-server
client = 15-npn-client-first-pref-on-mismatch-resumption-client
resume-server = 15-npn-client-first-pref-on-mismatch-resumption-resume-server
resume-client = 15-npn-client-first-pref-on-mismatch-resumption-client

[15-npn-client-first-pref-on-mismatch-resumption-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[15-npn-client-first-pref-on-mismatch-resumption-resume-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[15-npn-client-first-pref-on-mismatch-resumption-client]
CipherString = DEFAULT
MaxProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-15]
ExpectedNPNProtocol = foo
HandshakeMode = Resume
ResumptionExpected = Yes
server = 15-npn-client-first-pref-on-mismatch-resumption-server-extra
resume-server = 15-npn-client-first-pref-on-mismatch-resumption-resume-server-extra
client = 15-npn-client-first-pref-on-mismatch-resumption-client-extra
resume-client = 15-npn-client-first-pref-on-mismatch-resumption-client-extra

[15-npn-client-first-pref-on-mismatch-resumption-server-extra]
NPNProtocols = bar

[15-npn-client-first-pref-on-mismatch-resumption-resume-server-extra]
NPNProtocols = baz

[15-npn-client-first-pref-on-mismatch-resumption-client-extra]
NPNProtocols = foo,bar


# ===========================================================

[16-npn-no-server-support-resumption]
ssl_conf = 16-npn-no-server-support-resumption-ssl

[16-npn-no-server-support-resumption-ssl]
server = 16-npn-no-server-support-resumption-server
client = 16-npn-no-server-support-resumption-client
resume-server = 16-npn-no-server-support-resumption-resume-server
resume-client = 16-npn-no-server-support-resumption-client

[16-npn-no-server-support-resumption-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[16-npn-no-server-support-resumption-resume-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[16-npn-no-server-support-resumption-client]
CipherString = DEFAULT
MaxProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-16]
HandshakeMode = Resume
ResumptionExpected = Yes
server = 16-npn-no-server-support-resumption-server-extra
client = 16-npn-no-server-support-resumption-client-extra
resume-client = 16-npn-no-server-support-resumption-client-extra

[16-npn-no-server-support-resumption-server-extra]
NPNProtocols = foo

[16-npn-no-server-support-resumption-client-extra]
NPNProtocols = foo


# ===========================================================

[17-npn-no-client-support-resumption]
ssl_conf = 17-npn-no-client-support-resumption-ssl

[17-npn-no-client-support-resumption-ssl]
server = 17-npn-no-client-support-resumption-server
client = 17-npn-no-client-support-resumption-client
resume-server = 17-npn-no-client-support-resumption-server
resume-client = 17-npn-no-client-support-resumption-resume-client

[17-npn-no-client-support-resumption-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[17-npn-no-client-support-resumption-client]
CipherString = DEFAULT
MaxProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[17-npn-no-client-support-resumption-resume-client]
CipherString = DEFAULT
MaxProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-17]
HandshakeMode = Resume
ResumptionExpected = Yes
server = 17-npn-no-client-support-resumption-server-extra
resume-server = 17-npn-no-client-support-resumption-server-extra
client = 17-npn-no-client-support-resumption-client-extra

[17-npn-no-client-support-resumption-server-extra]
NPNProtocols = foo

[17-npn-no-client-support-resumption-client-extra]
NPNProtocols = foo


# ===========================================================

[18-alpn-preferred-over-npn-resumption]
ssl_conf = 18-alpn-preferred-over-npn-resumption-ssl

[18-alpn-preferred-over-npn-resumption-ssl]
server = 18-alpn-preferred-over-npn-resumption-server
client = 18-alpn-preferred-over-npn-resumption-client
resume-server = 18-alpn-preferred-over-npn-resumption-resume-server
resume-client = 18-alpn-preferred-over-npn-resumption-client

[18-alpn-preferred-over-npn-resumption-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[18-alpn-preferred-over-npn-resumption-resume-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[18-alpn-preferred-over-npn-resumption-client]
CipherString = DEFAULT
MaxProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-18]
ExpectedALPNProtocol = foo
HandshakeMode = Resume
ResumptionExpected = Yes
server = 18-alpn-preferred-over-npn-resumption-server-extra
resume-server = 18-alpn-preferred-over-npn-resumption-resume-server-extra
client = 18-alpn-preferred-over-npn-resumption-client-extra
resume-client = 18-alpn-preferred-over-npn-resumption-client-extra

[18-alpn-preferred-over-npn-resumption-server-extra]
NPNProtocols = bar

[18-alpn-preferred-over-npn-resumption-resume-server-extra]
ALPNProtocols = foo
NPNProtocols = baz

[18-alpn-preferred-over-npn-resumption-client-extra]
ALPNProtocols = foo
NPNProtocols = bar,baz


# ===========================================================

[19-npn-used-if-alpn-not-supported-resumption]
ssl_conf = 19-npn-used-if-alpn-not-supported-resumption-ssl

[19-npn-used-if-alpn-not-supported-resumption-ssl]
server = 19-npn-used-if-alpn-not-supported-resumption-server
client = 19-npn-used-if-alpn-not-supported-resumption-client
resume-server = 19-npn-used-if-alpn-not-supported-resumption-resume-server
resume-client = 19-npn-used-if-alpn-not-supported-resumption-client

[19-npn-used-if-alpn-not-supported-resumption-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[19-npn-used-if-alpn-not-supported-resumption-resume-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[19-npn-used-if-alpn-not-supported-resumption-client]
CipherString = DEFAULT
MaxProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-19]
ExpectedNPNProtocol = baz
HandshakeMode = Resume
ResumptionExpected = Yes
server = 19-npn-used-if-alpn-not-supported-resumption-server-extra
resume-server = 19-npn-used-if-alpn-not-supported-resumption-resume-server-extra
client = 19-npn-used-if-alpn-not-supported-resumption-client-extra
resume-client = 19-npn-used-if-alpn-not-supported-resumption-client-extra

[19-npn-used-if-alpn-not-supported-resumption-server-extra]
ALPNProtocols = foo
NPNProtocols = bar

[19-npn-used-if-alpn-not-supported-resumption-resume-server-extra]
NPNProtocols = baz

[19-npn-used-if-alpn-not-supported-resumption-client-extra]
ALPNProtocols = foo
NPNProtocols = bar,baz


