# -*- mode: perl; -*-
# Copyright 2016-2020 The OpenSSL Project Authors. All Rights Reserved.
#
# Licensed under the Apache License 2.0 (the "License").  You may not use
# this file except in compliance with the License.  You can obtain a copy
# in the file LICENSE in the source distribution or at
# https://www.openssl.org/source/license.html


## Test version negotiation

use strict;
use warnings;

package ssltests;


our @tests = (
    {
        name => "alpn-simple",
        server => {
            extra => {
                "ALPNProtocols" => "foo",
            },
        },
        client => {
            extra => {
                "ALPNProtocols" => "foo",
            },
        },
        test => {
            "ExpectedALPNProtocol" => "foo",
        },
    },
    {
        name => "alpn-server-finds-match",
        server => {
            extra => {
                "ALPNProtocols" => "baz,bar",
            },
        },
        client => {
            extra => {
                "ALPNProtocols" => "foo,bar",
            },
        },
        test => {
            "ExpectedALPNProtocol" => "bar",
        },
    },
    {
        name => "alpn-server-honours-server-pref",
        server => {
            extra => {
                "ALPNProtocols" => "bar,foo",
            },
        },
        client => {
            extra => {
                "ALPNProtocols" => "foo,bar",
            },
        },
        test => {
            "ExpectedALPNProtocol" => "bar",
        },
    },
    {
        name => "alpn-alert-on-mismatch",
        server => {
            extra => {
                "ALPNProtocols" => "baz",
            },
        },
        client => {
            extra => {
                "ALPNProtocols" => "foo,bar",
            },
        },
        test => {
            "ExpectedResult" => "ServerFail",
            "ExpectedServerAlert" => "NoApplicationProtocol",
        },
    },
    {
        name => "alpn-no-server-support",
        server => { },
        client => {
            extra => {
                "ALPNProtocols" => "foo",
            },
        },
        test => {
            "ExpectedALPNProtocol" => undef,
        },
    },
    {
        name => "alpn-no-client-support",
        server => {
            extra => {
                "ALPNProtocols" => "foo",
            },
        },
        client => { },
        test => {
            "ExpectedALPNProtocol" => undef,
        },
    },
    {
        name => "alpn-with-sni-no-context-switch",
        server => {
            extra => {
                "ALPNProtocols" => "foo",
                "ServerNameCallback" => "IgnoreMismatch",
            },
        },
        server2 => {
            extra => {
                "ALPNProtocols" => "bar",
            },
        },
        client => {
            extra => {
                "ALPNProtocols" => "foo,bar",
                "ServerName" => "server1",
            },
        },
        test => {
            "ExpectedServerName" => "server1",
            "ExpectedALPNProtocol" => "foo",
        },
    },
    {
        name => "alpn-with-sni-context-switch",
        server => {
            extra => {
                "ALPNProtocols" => "foo",
                "ServerNameCallback" => "IgnoreMismatch",
            },
        },
        server2 => {
            extra => {
                "ALPNProtocols" => "bar",
            },
        },
        client => {
            extra => {
                "ALPNProtocols" => "foo,bar",
                "ServerName" => "server2",
            },
        },
        test => {
            "ExpectedServerName" => "server2",
            "ExpectedALPNProtocol" => "bar",
        },
    },
    {
        name => "alpn-selected-sni-server-supports-alpn",
        server => {
            extra => {
               "ServerNameCallback" => "IgnoreMismatch",
            },
        },
        server2 => {
            extra => {
                "ALPNProtocols" => "bar",
            },
        },
        client => {
            extra => {
                "ALPNProtocols" => "foo,bar",
                "ServerName" => "server2",
            },
        },
        test => {
            "ExpectedServerName" => "server2",
            "ExpectedALPNProtocol" => "bar",
        },
    },
    {
        name => "alpn-selected-sni-server-does-not-support-alpn",
        server => {
            extra => {
                "ALPNProtocols" => "bar", 
                "ServerNameCallback" => "IgnoreMismatch",
            },
        },
        server2 => { },
        client => {
            extra => {
                "ALPNProtocols" => "foo,bar",
                "ServerName" => "server2",
            },
        },
        test => {
            "ExpectedServerName" => "server2",
            "ExpectedALPNProtocol" => undef,
        },
    },
    {
        name => "alpn-simple-resumption",
        server => {
            extra => {
                "ALPNProtocols" => "foo",
            },
        },
        client => {
            extra => {
                "ALPNProtocols" => "foo",
            },
        },
        test => {
            "HandshakeMode" => "Resume",
            "ResumptionExpected" => "Yes",
            "ExpectedALPNProtocol" => "foo",
        },
    },
    {
        name => "alpn-server-switch-resumption",
        server => {
            extra => {
                "ALPNProtocols" => "bar,foo",
            },
        },
        resume_server => {
            extra => {
                "ALPNProtocols" => "baz,foo",
            },
        },
        client => {
            extra => {
                "ALPNProtocols" => "foo,bar,baz",
            },
        },
        test => {
            "HandshakeMode" => "Resume",
            "ResumptionExpected" => "Yes",
            "ExpectedALPNProtocol" => "baz",
        },
    },
    {
        name => "alpn-client-switch-resumption",
        server => {
            extra => {
                "ALPNProtocols" => "foo,bar,baz",
            },
        },
        client => {
            extra => {
                "ALPNProtocols" => "foo,baz",
            },
        },
        resume_client => {
            extra => {
                "ALPNProtocols" => "bar,baz",
            },
        },
        test => {
            "HandshakeMode" => "Resume",
            "ResumptionExpected" => "Yes",
            "ExpectedALPNProtocol" => "bar",
        },
    },
    {
        name => "alpn-alert-on-mismatch-resumption",
        server => {
            extra => {
                "ALPNProtocols" => "bar",
            },
        },
        resume_server => {
            extra => {
                "ALPNProtocols" => "baz",
            },
        },
        client => {
            extra => {
                "ALPNProtocols" => "foo,bar",
            },
        },
        test => {
            "HandshakeMode" => "Resume",
            "ExpectedResult" => "ServerFail",
            "ExpectedServerAlert" => "NoApplicationProtocol",
        },
    },
    {
        name => "alpn-no-server-support-resumption",
        server => {
            extra => {
                "ALPNProtocols" => "foo",
            },
        },
        resume_server => { },
        client => {
            extra => {
                "ALPNProtocols" => "foo",
            },
        },
        test => {
            "HandshakeMode" => "Resume",
            "ResumptionExpected" => "Yes",
            "ExpectedALPNProtocol" => undef,
        },
    },
    {
        name => "alpn-no-client-support-resumption",
        server => {
            extra => {
                "ALPNProtocols" => "foo",
            },
        },
        client => {
            extra => {
                "ALPNProtocols" => "foo",
            },
        },
        resume_client => {
        },
        test => {
            "HandshakeMode" => "Resume",
            "ResumptionExpected" => "Yes",
            "ExpectedALPNProtocol" => undef,
        },
    },
);
