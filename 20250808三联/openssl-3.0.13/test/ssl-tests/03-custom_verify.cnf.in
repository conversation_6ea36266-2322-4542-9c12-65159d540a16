# -*- mode: perl; -*-
# Copyright 2016-2021 The OpenSSL Project Authors. All Rights Reserved.
#
# Licensed under the Apache License 2.0 (the "License").  You may not use
# this file except in compliance with the License.  You can obtain a copy
# in the file LICENSE in the source distribution or at
# https://www.openssl.org/source/license.html


## SSL test configurations

package ssltests;

our @tests = (

    # Sanity-check that verification indeed succeeds without the
    # restrictive callback.
    {
        name => "verify-success",
        server => { },
        client => { },
        test   => { "ExpectedResult" => "Success" },
    },

    # Same test as above but with a custom callback that always fails.
    {
        name => "verify-custom-reject",
        server => { },
        client => {
            extra => {
                "VerifyCallback" => "RejectAll",
            },
        },
        test   => {
            "ExpectedResult" => "ClientFail",
            "ExpectedClientAlert" => "HandshakeFailure",
        },
    },

    # Same test as above but with a custom callback that always succeeds.
    {
        name => "verify-custom-allow",
        server => { },
        client => {
            extra => {
                "VerifyCallback" => "AcceptAll",
            },
        },
        test   => {
            "ExpectedResult" => "Success",
        },
    },

    # Same test as above but with a custom callback that requests retry once.
    {
        name => "verify-custom-retry",
        server => { },
        client => {
            extra => {
                "VerifyCallback" => "RetryOnce",
            },
        },
        test   => {
            "ExpectedResult" => "Success",
        },
    },

    # Sanity-check that verification indeed succeeds if peer verification
    # is not requested.
    {
        name => "noverify-success",
        server => { },
        client => {
            "VerifyMode" => undef,
            "VerifyCAFile" => undef,
        },
        test   => { "ExpectedResult" => "Success" },
    },

    # Same test as above but with a custom callback that always fails.
    # The callback return has no impact on handshake success in this mode.
    {
        name => "noverify-ignore-custom-reject",
        server => { },
        client => {
            "VerifyMode" => undef,
            "VerifyCAFile" => undef,
            extra => {
                "VerifyCallback" => "RejectAll",
            },
        },
        test   => {
            "ExpectedResult" => "Success",
        },
    },

    # Same test as above but with a custom callback that always succeeds.
    # The callback return has no impact on handshake success in this mode.
    {
        name => "noverify-accept-custom-allow",
        server => { },
        client => {
            "VerifyMode" => undef,
            "VerifyCAFile" => undef,
            extra => {
                "VerifyCallback" => "AcceptAll",
            },
        },
        test   => {
            "ExpectedResult" => "Success",
        },
    },

    # Sanity-check that verification indeed fails without the
    # permissive callback.
    {
        name => "verify-fail-no-root",
        server => { },
        client => {
            # Don't set up the client root file.
            "VerifyCAFile" => undef,
        },
        test   => {
          "ExpectedResult" => "ClientFail",
          "ExpectedClientAlert" => "UnknownCA",
        },
    },

    # Same test as above but with a custom callback that always succeeds.
    {
        name => "verify-custom-success-no-root",
        server => { },
        client => {
            "VerifyCAFile" => undef,
            extra => {
                "VerifyCallback" => "AcceptAll",
            },
        },
        test   => {
            "ExpectedResult" => "Success"
        },
    },

    # Same test as above but with a custom callback that always fails.
    {
        name => "verify-custom-fail-no-root",
        server => { },
        client => {
            "VerifyCAFile" => undef,
            extra => {
                "VerifyCallback" => "RejectAll",
            },
        },
        test   => {
            "ExpectedResult" => "ClientFail",
            "ExpectedClientAlert" => "HandshakeFailure",
        },
    },
);
