# -*- mode: perl; -*-
# Copyright 2018-2021 The OpenSSL Project Authors. All Rights Reserved.
#
# Licensed under the Apache License 2.0 (the "License").  You may not use
# this file except in compliance with the License.  You can obtain a copy
# in the file LICENSE in the source distribution or at
# https://www.openssl.org/source/license.html


## Test session ticket app data

use strict;
use warnings;

package ssltests;
use OpenSSL::Test::Utils;

our @tests12 = (
    {
	"name" => "session-ticket-app-data12",
	"client" => {
	    "MaxProtocol" => "TLSv1.2",
	    "Options" => "SessionTicket",
	},
	"server" => {
	    "Options" => "SessionTicket",
	    "extra" => {
		"SessionTicketAppData" => "HelloWorld",
	    },
	},
	"test" => {
	    "HandshakeMode" => "Resume",
	    "ExpectedResult" => "Success",
	    "SessionTicketExpected" => "Yes",
	    "ResumptionExpected" => "Yes",
	    "ExpectedSessionTicketAppData" => "HelloWorld",
	}
    },
    {
	"name" => "session-ticket-app-data12",
	"client" => {
	    "MaxProtocol" => "TLSv1.2",
	    "Options" => "SessionTicket",
	},
	"server" => {
	    "Options" => "SessionTicket",
	},
	"test" => {
	    "HandshakeMode" => "Resume",
	    "ExpectedResult" => "Success",
	    "SessionTicketExpected" => "Yes",
	    "ResumptionExpected" => "Yes",
	    "ExpectedSessionTicketAppData" => "",
	}
    }
);
our @tests13 = (
    {
	"name" => "session-ticket-app-data13",
	"client" => {
	    "MaxProtocol" => "TLSv1.3",
	    "Options" => "SessionTicket",
	},
	"server" => {
	    "Options" => "SessionTicket",
	    "extra" => {
		"SessionTicketAppData" => "HelloWorld",
	    },
	},
	"test" => {
	    "HandshakeMode" => "Resume",
	    "ExpectedResult" => "Success",
	    "SessionTicketExpected" => "Yes",
	    "ResumptionExpected" => "Yes",
	    "ExpectedSessionTicketAppData" => "HelloWorld",
	}
    },
    {
	"name" => "session-ticket-app-data13",
	"client" => {
	    "MaxProtocol" => "TLSv1.3",
	    "Options" => "SessionTicket",
	},
	"server" => {
	    "Options" => "SessionTicket",
	},
	"test" => {
	    "HandshakeMode" => "Resume",
	    "ExpectedResult" => "Success",
	    "SessionTicketExpected" => "Yes",
	    "ResumptionExpected" => "Yes",
	    "ExpectedSessionTicketAppData" => "",
	}
    }
);

our @tests = ();
push @tests, @tests12 unless disabled("tls1_2");
push @tests, @tests13 unless disabled("tls1_3")
                             || (disabled("ec") && disabled("dh"));
