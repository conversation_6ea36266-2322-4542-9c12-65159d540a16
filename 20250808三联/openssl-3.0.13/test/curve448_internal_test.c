/*
 * Copyright 2017-2021 The OpenSSL Project Authors. All Rights Reserved.
 *
 * Licensed under the Apache License 2.0 (the "License").  You may not use
 * this file except in compliance with the License.  You can obtain a copy
 * in the file LICENSE in the source distribution or at
 * https://www.openssl.org/source/license.html
 */
#include <stdio.h>
#include <string.h>
#include <openssl/e_os2.h>
#include <openssl/evp.h>
#include "crypto/ecx.h"
#include "curve448_local.h"
#include "testutil.h"

static unsigned int max = 1000;
static unsigned int verbose = 0;

/* Test vectors from RFC7748 for X448 */

static const uint8_t in_scalar1[56] = {
    0x3d, 0x26, 0x2f, 0xdd, 0xf9, 0xec, 0x8e, 0x88, 0x49, 0x52, 0x66, 0xfe,
    0xa1, 0x9a, 0x34, 0xd2, 0x88, 0x82, 0xac, 0xef, 0x04, 0x51, 0x04, 0xd0,
    0xd1, 0xaa, 0xe1, 0x21, 0x70, 0x0a, 0x77, 0x9c, 0x98, 0x4c, 0x24, 0xf8,
    0xcd, 0xd7, 0x8f, 0xbf, 0xf4, 0x49, 0x43, 0xeb, 0xa3, 0x68, 0xf5, 0x4b,
    0x29, 0x25, 0x9a, 0x4f, 0x1c, 0x60, 0x0a, 0xd3
};

static const uint8_t in_u1[56] = {
    0x06, 0xfc, 0xe6, 0x40, 0xfa, 0x34, 0x87, 0xbf, 0xda, 0x5f, 0x6c, 0xf2,
    0xd5, 0x26, 0x3f, 0x8a, 0xad, 0x88, 0x33, 0x4c, 0xbd, 0x07, 0x43, 0x7f,
    0x02, 0x0f, 0x08, 0xf9, 0x81, 0x4d, 0xc0, 0x31, 0xdd, 0xbd, 0xc3, 0x8c,
    0x19, 0xc6, 0xda, 0x25, 0x83, 0xfa, 0x54, 0x29, 0xdb, 0x94, 0xad, 0xa1,
    0x8a, 0xa7, 0xa7, 0xfb, 0x4e, 0xf8, 0xa0, 0x86
};

static const uint8_t out_u1[56] = {
    0xce, 0x3e, 0x4f, 0xf9, 0x5a, 0x60, 0xdc, 0x66, 0x97, 0xda, 0x1d, 0xb1,
    0xd8, 0x5e, 0x6a, 0xfb, 0xdf, 0x79, 0xb5, 0x0a, 0x24, 0x12, 0xd7, 0x54,
    0x6d, 0x5f, 0x23, 0x9f, 0xe1, 0x4f, 0xba, 0xad, 0xeb, 0x44, 0x5f, 0xc6,
    0x6a, 0x01, 0xb0, 0x77, 0x9d, 0x98, 0x22, 0x39, 0x61, 0x11, 0x1e, 0x21,
    0x76, 0x62, 0x82, 0xf7, 0x3d, 0xd9, 0x6b, 0x6f
};

static const uint8_t in_scalar2[56] = {
    0x20, 0x3d, 0x49, 0x44, 0x28, 0xb8, 0x39, 0x93, 0x52, 0x66, 0x5d, 0xdc,
    0xa4, 0x2f, 0x9d, 0xe8, 0xfe, 0xf6, 0x00, 0x90, 0x8e, 0x0d, 0x46, 0x1c,
    0xb0, 0x21, 0xf8, 0xc5, 0x38, 0x34, 0x5d, 0xd7, 0x7c, 0x3e, 0x48, 0x06,
    0xe2, 0x5f, 0x46, 0xd3, 0x31, 0x5c, 0x44, 0xe0, 0xa5, 0xb4, 0x37, 0x12,
    0x82, 0xdd, 0x2c, 0x8d, 0x5b, 0xe3, 0x09, 0x5f
};

static const uint8_t in_u2[56] = {
    0x0f, 0xbc, 0xc2, 0xf9, 0x93, 0xcd, 0x56, 0xd3, 0x30, 0x5b, 0x0b, 0x7d,
    0x9e, 0x55, 0xd4, 0xc1, 0xa8, 0xfb, 0x5d, 0xbb, 0x52, 0xf8, 0xe9, 0xa1,
    0xe9, 0xb6, 0x20, 0x1b, 0x16, 0x5d, 0x01, 0x58, 0x94, 0xe5, 0x6c, 0x4d,
    0x35, 0x70, 0xbe, 0xe5, 0x2f, 0xe2, 0x05, 0xe2, 0x8a, 0x78, 0xb9, 0x1c,
    0xdf, 0xbd, 0xe7, 0x1c, 0xe8, 0xd1, 0x57, 0xdb
};

static const uint8_t out_u2[56] = {
    0x88, 0x4a, 0x02, 0x57, 0x62, 0x39, 0xff, 0x7a, 0x2f, 0x2f, 0x63, 0xb2,
    0xdb, 0x6a, 0x9f, 0xf3, 0x70, 0x47, 0xac, 0x13, 0x56, 0x8e, 0x1e, 0x30,
    0xfe, 0x63, 0xc4, 0xa7, 0xad, 0x1b, 0x3e, 0xe3, 0xa5, 0x70, 0x0d, 0xf3,
    0x43, 0x21, 0xd6, 0x20, 0x77, 0xe6, 0x36, 0x33, 0xc5, 0x75, 0xc1, 0xc9,
    0x54, 0x51, 0x4e, 0x99, 0xda, 0x7c, 0x17, 0x9d
};

static const uint8_t in_u3[56] = {
    0x05, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
};

static const uint8_t out_u3[3][56] = {
    {
        0x3f, 0x48, 0x2c, 0x8a, 0x9f, 0x19, 0xb0, 0x1e, 0x6c, 0x46, 0xee, 0x97,
        0x11, 0xd9, 0xdc, 0x14, 0xfd, 0x4b, 0xf6, 0x7a, 0xf3, 0x07, 0x65, 0xc2,
        0xae, 0x2b, 0x84, 0x6a, 0x4d, 0x23, 0xa8, 0xcd, 0x0d, 0xb8, 0x97, 0x08,
        0x62, 0x39, 0x49, 0x2c, 0xaf, 0x35, 0x0b, 0x51, 0xf8, 0x33, 0x86, 0x8b,
        0x9b, 0xc2, 0xb3, 0xbc, 0xa9, 0xcf, 0x41, 0x13
    }, {
        0xaa, 0x3b, 0x47, 0x49, 0xd5, 0x5b, 0x9d, 0xaf, 0x1e, 0x5b, 0x00, 0x28,
        0x88, 0x26, 0xc4, 0x67, 0x27, 0x4c, 0xe3, 0xeb, 0xbd, 0xd5, 0xc1, 0x7b,
        0x97, 0x5e, 0x09, 0xd4, 0xaf, 0x6c, 0x67, 0xcf, 0x10, 0xd0, 0x87, 0x20,
        0x2d, 0xb8, 0x82, 0x86, 0xe2, 0xb7, 0x9f, 0xce, 0xea, 0x3e, 0xc3, 0x53,
        0xef, 0x54, 0xfa, 0xa2, 0x6e, 0x21, 0x9f, 0x38
    }, {
        0x07, 0x7f, 0x45, 0x36, 0x81, 0xca, 0xca, 0x36, 0x93, 0x19, 0x84, 0x20,
        0xbb, 0xe5, 0x15, 0xca, 0xe0, 0x00, 0x24, 0x72, 0x51, 0x9b, 0x3e, 0x67,
        0x66, 0x1a, 0x7e, 0x89, 0xca, 0xb9, 0x46, 0x95, 0xc8, 0xf4, 0xbc, 0xd6,
        0x6e, 0x61, 0xb9, 0xb9, 0xc9, 0x46, 0xda, 0x8d, 0x52, 0x4d, 0xe3, 0xd6,
        0x9b, 0xd9, 0xd9, 0xd6, 0x6b, 0x99, 0x7e, 0x37
    }
};

/* Test vectors from RFC8032 for Ed448 */

/* Pure Ed448 */

static const uint8_t privkey1[57] = {
    0x6c, 0x82, 0xa5, 0x62, 0xcb, 0x80, 0x8d, 0x10, 0xd6, 0x32, 0xbe, 0x89,
    0xc8, 0x51, 0x3e, 0xbf, 0x6c, 0x92, 0x9f, 0x34, 0xdd, 0xfa, 0x8c, 0x9f,
    0x63, 0xc9, 0x96, 0x0e, 0xf6, 0xe3, 0x48, 0xa3, 0x52, 0x8c, 0x8a, 0x3f,
    0xcc, 0x2f, 0x04, 0x4e, 0x39, 0xa3, 0xfc, 0x5b, 0x94, 0x49, 0x2f, 0x8f,
    0x03, 0x2e, 0x75, 0x49, 0xa2, 0x00, 0x98, 0xf9, 0x5b
};

static const uint8_t pubkey1[57] = {
    0x5f, 0xd7, 0x44, 0x9b, 0x59, 0xb4, 0x61, 0xfd, 0x2c, 0xe7, 0x87, 0xec,
    0x61, 0x6a, 0xd4, 0x6a, 0x1d, 0xa1, 0x34, 0x24, 0x85, 0xa7, 0x0e, 0x1f,
    0x8a, 0x0e, 0xa7, 0x5d, 0x80, 0xe9, 0x67, 0x78, 0xed, 0xf1, 0x24, 0x76,
    0x9b, 0x46, 0xc7, 0x06, 0x1b, 0xd6, 0x78, 0x3d, 0xf1, 0xe5, 0x0f, 0x6c,
    0xd1, 0xfa, 0x1a, 0xbe, 0xaf, 0xe8, 0x25, 0x61, 0x80
};

static const uint8_t sig1[114] = {
    0x53, 0x3a, 0x37, 0xf6, 0xbb, 0xe4, 0x57, 0x25, 0x1f, 0x02, 0x3c, 0x0d,
    0x88, 0xf9, 0x76, 0xae, 0x2d, 0xfb, 0x50, 0x4a, 0x84, 0x3e, 0x34, 0xd2,
    0x07, 0x4f, 0xd8, 0x23, 0xd4, 0x1a, 0x59, 0x1f, 0x2b, 0x23, 0x3f, 0x03,
    0x4f, 0x62, 0x82, 0x81, 0xf2, 0xfd, 0x7a, 0x22, 0xdd, 0xd4, 0x7d, 0x78,
    0x28, 0xc5, 0x9b, 0xd0, 0xa2, 0x1b, 0xfd, 0x39, 0x80, 0xff, 0x0d, 0x20,
    0x28, 0xd4, 0xb1, 0x8a, 0x9d, 0xf6, 0x3e, 0x00, 0x6c, 0x5d, 0x1c, 0x2d,
    0x34, 0x5b, 0x92, 0x5d, 0x8d, 0xc0, 0x0b, 0x41, 0x04, 0x85, 0x2d, 0xb9,
    0x9a, 0xc5, 0xc7, 0xcd, 0xda, 0x85, 0x30, 0xa1, 0x13, 0xa0, 0xf4, 0xdb,
    0xb6, 0x11, 0x49, 0xf0, 0x5a, 0x73, 0x63, 0x26, 0x8c, 0x71, 0xd9, 0x58,
    0x08, 0xff, 0x2e, 0x65, 0x26, 0x00
};

static const uint8_t privkey2[57] = {
    0xc4, 0xea, 0xb0, 0x5d, 0x35, 0x70, 0x07, 0xc6, 0x32, 0xf3, 0xdb, 0xb4,
    0x84, 0x89, 0x92, 0x4d, 0x55, 0x2b, 0x08, 0xfe, 0x0c, 0x35, 0x3a, 0x0d,
    0x4a, 0x1f, 0x00, 0xac, 0xda, 0x2c, 0x46, 0x3a, 0xfb, 0xea, 0x67, 0xc5,
    0xe8, 0xd2, 0x87, 0x7c, 0x5e, 0x3b, 0xc3, 0x97, 0xa6, 0x59, 0x94, 0x9e,
    0xf8, 0x02, 0x1e, 0x95, 0x4e, 0x0a, 0x12, 0x27, 0x4e
};

static const uint8_t pubkey2[57] = {
    0x43, 0xba, 0x28, 0xf4, 0x30, 0xcd, 0xff, 0x45, 0x6a, 0xe5, 0x31, 0x54,
    0x5f, 0x7e, 0xcd, 0x0a, 0xc8, 0x34, 0xa5, 0x5d, 0x93, 0x58, 0xc0, 0x37,
    0x2b, 0xfa, 0x0c, 0x6c, 0x67, 0x98, 0xc0, 0x86, 0x6a, 0xea, 0x01, 0xeb,
    0x00, 0x74, 0x28, 0x02, 0xb8, 0x43, 0x8e, 0xa4, 0xcb, 0x82, 0x16, 0x9c,
    0x23, 0x51, 0x60, 0x62, 0x7b, 0x4c, 0x3a, 0x94, 0x80
};

static const uint8_t msg2[1] = {
    0x03
};

static const uint8_t sig2[114] = {
    0x26, 0xb8, 0xf9, 0x17, 0x27, 0xbd, 0x62, 0x89, 0x7a, 0xf1, 0x5e, 0x41,
    0xeb, 0x43, 0xc3, 0x77, 0xef, 0xb9, 0xc6, 0x10, 0xd4, 0x8f, 0x23, 0x35,
    0xcb, 0x0b, 0xd0, 0x08, 0x78, 0x10, 0xf4, 0x35, 0x25, 0x41, 0xb1, 0x43,
    0xc4, 0xb9, 0x81, 0xb7, 0xe1, 0x8f, 0x62, 0xde, 0x8c, 0xcd, 0xf6, 0x33,
    0xfc, 0x1b, 0xf0, 0x37, 0xab, 0x7c, 0xd7, 0x79, 0x80, 0x5e, 0x0d, 0xbc,
    0xc0, 0xaa, 0xe1, 0xcb, 0xce, 0xe1, 0xaf, 0xb2, 0xe0, 0x27, 0xdf, 0x36,
    0xbc, 0x04, 0xdc, 0xec, 0xbf, 0x15, 0x43, 0x36, 0xc1, 0x9f, 0x0a, 0xf7,
    0xe0, 0xa6, 0x47, 0x29, 0x05, 0xe7, 0x99, 0xf1, 0x95, 0x3d, 0x2a, 0x0f,
    0xf3, 0x34, 0x8a, 0xb2, 0x1a, 0xa4, 0xad, 0xaf, 0xd1, 0xd2, 0x34, 0x44,
    0x1c, 0xf8, 0x07, 0xc0, 0x3a, 0x00
};

static const uint8_t privkey3[57] = {
    0xc4, 0xea, 0xb0, 0x5d, 0x35, 0x70, 0x07, 0xc6, 0x32, 0xf3, 0xdb, 0xb4,
    0x84, 0x89, 0x92, 0x4d, 0x55, 0x2b, 0x08, 0xfe, 0x0c, 0x35, 0x3a, 0x0d,
    0x4a, 0x1f, 0x00, 0xac, 0xda, 0x2c, 0x46, 0x3a, 0xfb, 0xea, 0x67, 0xc5,
    0xe8, 0xd2, 0x87, 0x7c, 0x5e, 0x3b, 0xc3, 0x97, 0xa6, 0x59, 0x94, 0x9e,
    0xf8, 0x02, 0x1e, 0x95, 0x4e, 0x0a, 0x12, 0x27, 0x4e
};

static const uint8_t pubkey3[57] = {
    0x43, 0xba, 0x28, 0xf4, 0x30, 0xcd, 0xff, 0x45, 0x6a, 0xe5, 0x31, 0x54,
    0x5f, 0x7e, 0xcd, 0x0a, 0xc8, 0x34, 0xa5, 0x5d, 0x93, 0x58, 0xc0, 0x37,
    0x2b, 0xfa, 0x0c, 0x6c, 0x67, 0x98, 0xc0, 0x86, 0x6a, 0xea, 0x01, 0xeb,
    0x00, 0x74, 0x28, 0x02, 0xb8, 0x43, 0x8e, 0xa4, 0xcb, 0x82, 0x16, 0x9c,
    0x23, 0x51, 0x60, 0x62, 0x7b, 0x4c, 0x3a, 0x94, 0x80
};

static const uint8_t msg3[1] = {
    0x03
};

static const uint8_t context3[3] = {
    0x66, 0x6f, 0x6f
};

static const uint8_t sig3[114] = {
    0xd4, 0xf8, 0xf6, 0x13, 0x17, 0x70, 0xdd, 0x46, 0xf4, 0x08, 0x67, 0xd6,
    0xfd, 0x5d, 0x50, 0x55, 0xde, 0x43, 0x54, 0x1f, 0x8c, 0x5e, 0x35, 0xab,
    0xbc, 0xd0, 0x01, 0xb3, 0x2a, 0x89, 0xf7, 0xd2, 0x15, 0x1f, 0x76, 0x47,
    0xf1, 0x1d, 0x8c, 0xa2, 0xae, 0x27, 0x9f, 0xb8, 0x42, 0xd6, 0x07, 0x21,
    0x7f, 0xce, 0x6e, 0x04, 0x2f, 0x68, 0x15, 0xea, 0x00, 0x0c, 0x85, 0x74,
    0x1d, 0xe5, 0xc8, 0xda, 0x11, 0x44, 0xa6, 0xa1, 0xab, 0xa7, 0xf9, 0x6d,
    0xe4, 0x25, 0x05, 0xd7, 0xa7, 0x29, 0x85, 0x24, 0xfd, 0xa5, 0x38, 0xfc,
    0xcb, 0xbb, 0x75, 0x4f, 0x57, 0x8c, 0x1c, 0xad, 0x10, 0xd5, 0x4d, 0x0d,
    0x54, 0x28, 0x40, 0x7e, 0x85, 0xdc, 0xbc, 0x98, 0xa4, 0x91, 0x55, 0xc1,
    0x37, 0x64, 0xe6, 0x6c, 0x3c, 0x00
};

static const uint8_t privkey4[57] = {
    0xcd, 0x23, 0xd2, 0x4f, 0x71, 0x42, 0x74, 0xe7, 0x44, 0x34, 0x32, 0x37,
    0xb9, 0x32, 0x90, 0xf5, 0x11, 0xf6, 0x42, 0x5f, 0x98, 0xe6, 0x44, 0x59,
    0xff, 0x20, 0x3e, 0x89, 0x85, 0x08, 0x3f, 0xfd, 0xf6, 0x05, 0x00, 0x55,
    0x3a, 0xbc, 0x0e, 0x05, 0xcd, 0x02, 0x18, 0x4b, 0xdb, 0x89, 0xc4, 0xcc,
    0xd6, 0x7e, 0x18, 0x79, 0x51, 0x26, 0x7e, 0xb3, 0x28
};

static const uint8_t pubkey4[57] = {
    0xdc, 0xea, 0x9e, 0x78, 0xf3, 0x5a, 0x1b, 0xf3, 0x49, 0x9a, 0x83, 0x1b,
    0x10, 0xb8, 0x6c, 0x90, 0xaa, 0xc0, 0x1c, 0xd8, 0x4b, 0x67, 0xa0, 0x10,
    0x9b, 0x55, 0xa3, 0x6e, 0x93, 0x28, 0xb1, 0xe3, 0x65, 0xfc, 0xe1, 0x61,
    0xd7, 0x1c, 0xe7, 0x13, 0x1a, 0x54, 0x3e, 0xa4, 0xcb, 0x5f, 0x7e, 0x9f,
    0x1d, 0x8b, 0x00, 0x69, 0x64, 0x47, 0x00, 0x14, 0x00
};

static const uint8_t msg4[11] = {
    0x0c, 0x3e, 0x54, 0x40, 0x74, 0xec, 0x63, 0xb0, 0x26, 0x5e, 0x0c
};

static const uint8_t sig4[114] = {
    0x1f, 0x0a, 0x88, 0x88, 0xce, 0x25, 0xe8, 0xd4, 0x58, 0xa2, 0x11, 0x30,
    0x87, 0x9b, 0x84, 0x0a, 0x90, 0x89, 0xd9, 0x99, 0xaa, 0xba, 0x03, 0x9e,
    0xaf, 0x3e, 0x3a, 0xfa, 0x09, 0x0a, 0x09, 0xd3, 0x89, 0xdb, 0xa8, 0x2c,
    0x4f, 0xf2, 0xae, 0x8a, 0xc5, 0xcd, 0xfb, 0x7c, 0x55, 0xe9, 0x4d, 0x5d,
    0x96, 0x1a, 0x29, 0xfe, 0x01, 0x09, 0x94, 0x1e, 0x00, 0xb8, 0xdb, 0xde,
    0xea, 0x6d, 0x3b, 0x05, 0x10, 0x68, 0xdf, 0x72, 0x54, 0xc0, 0xcd, 0xc1,
    0x29, 0xcb, 0xe6, 0x2d, 0xb2, 0xdc, 0x95, 0x7d, 0xbb, 0x47, 0xb5, 0x1f,
    0xd3, 0xf2, 0x13, 0xfb, 0x86, 0x98, 0xf0, 0x64, 0x77, 0x42, 0x50, 0xa5,
    0x02, 0x89, 0x61, 0xc9, 0xbf, 0x8f, 0xfd, 0x97, 0x3f, 0xe5, 0xd5, 0xc2,
    0x06, 0x49, 0x2b, 0x14, 0x0e, 0x00
};

static const uint8_t privkey5[57] = {
    0x25, 0x8c, 0xdd, 0x4a, 0xda, 0x32, 0xed, 0x9c, 0x9f, 0xf5, 0x4e, 0x63,
    0x75, 0x6a, 0xe5, 0x82, 0xfb, 0x8f, 0xab, 0x2a, 0xc7, 0x21, 0xf2, 0xc8,
    0xe6, 0x76, 0xa7, 0x27, 0x68, 0x51, 0x3d, 0x93, 0x9f, 0x63, 0xdd, 0xdb,
    0x55, 0x60, 0x91, 0x33, 0xf2, 0x9a, 0xdf, 0x86, 0xec, 0x99, 0x29, 0xdc,
    0xcb, 0x52, 0xc1, 0xc5, 0xfd, 0x2f, 0xf7, 0xe2, 0x1b
};

static const uint8_t pubkey5[57] = {
    0x3b, 0xa1, 0x6d, 0xa0, 0xc6, 0xf2, 0xcc, 0x1f, 0x30, 0x18, 0x77, 0x40,
    0x75, 0x6f, 0x5e, 0x79, 0x8d, 0x6b, 0xc5, 0xfc, 0x01, 0x5d, 0x7c, 0x63,
    0xcc, 0x95, 0x10, 0xee, 0x3f, 0xd4, 0x4a, 0xdc, 0x24, 0xd8, 0xe9, 0x68,
    0xb6, 0xe4, 0x6e, 0x6f, 0x94, 0xd1, 0x9b, 0x94, 0x53, 0x61, 0x72, 0x6b,
    0xd7, 0x5e, 0x14, 0x9e, 0xf0, 0x98, 0x17, 0xf5, 0x80
};

static const uint8_t msg5[12] = {
    0x64, 0xa6, 0x5f, 0x3c, 0xde, 0xdc, 0xdd, 0x66, 0x81, 0x1e, 0x29, 0x15
};

static const uint8_t sig5[114] = {
    0x7e, 0xee, 0xab, 0x7c, 0x4e, 0x50, 0xfb, 0x79, 0x9b, 0x41, 0x8e, 0xe5,
    0xe3, 0x19, 0x7f, 0xf6, 0xbf, 0x15, 0xd4, 0x3a, 0x14, 0xc3, 0x43, 0x89,
    0xb5, 0x9d, 0xd1, 0xa7, 0xb1, 0xb8, 0x5b, 0x4a, 0xe9, 0x04, 0x38, 0xac,
    0xa6, 0x34, 0xbe, 0xa4, 0x5e, 0x3a, 0x26, 0x95, 0xf1, 0x27, 0x0f, 0x07,
    0xfd, 0xcd, 0xf7, 0xc6, 0x2b, 0x8e, 0xfe, 0xaf, 0x00, 0xb4, 0x5c, 0x2c,
    0x96, 0xba, 0x45, 0x7e, 0xb1, 0xa8, 0xbf, 0x07, 0x5a, 0x3d, 0xb2, 0x8e,
    0x5c, 0x24, 0xf6, 0xb9, 0x23, 0xed, 0x4a, 0xd7, 0x47, 0xc3, 0xc9, 0xe0,
    0x3c, 0x70, 0x79, 0xef, 0xb8, 0x7c, 0xb1, 0x10, 0xd3, 0xa9, 0x98, 0x61,
    0xe7, 0x20, 0x03, 0xcb, 0xae, 0x6d, 0x6b, 0x8b, 0x82, 0x7e, 0x4e, 0x6c,
    0x14, 0x30, 0x64, 0xff, 0x3c, 0x00
};

static const uint8_t privkey6[57] = {
    0x7e, 0xf4, 0xe8, 0x45, 0x44, 0x23, 0x67, 0x52, 0xfb, 0xb5, 0x6b, 0x8f,
    0x31, 0xa2, 0x3a, 0x10, 0xe4, 0x28, 0x14, 0xf5, 0xf5, 0x5c, 0xa0, 0x37,
    0xcd, 0xcc, 0x11, 0xc6, 0x4c, 0x9a, 0x3b, 0x29, 0x49, 0xc1, 0xbb, 0x60,
    0x70, 0x03, 0x14, 0x61, 0x17, 0x32, 0xa6, 0xc2, 0xfe, 0xa9, 0x8e, 0xeb,
    0xc0, 0x26, 0x6a, 0x11, 0xa9, 0x39, 0x70, 0x10, 0x0e
};

static const uint8_t pubkey6[57] = {
    0xb3, 0xda, 0x07, 0x9b, 0x0a, 0xa4, 0x93, 0xa5, 0x77, 0x20, 0x29, 0xf0,
    0x46, 0x7b, 0xae, 0xbe, 0xe5, 0xa8, 0x11, 0x2d, 0x9d, 0x3a, 0x22, 0x53,
    0x23, 0x61, 0xda, 0x29, 0x4f, 0x7b, 0xb3, 0x81, 0x5c, 0x5d, 0xc5, 0x9e,
    0x17, 0x6b, 0x4d, 0x9f, 0x38, 0x1c, 0xa0, 0x93, 0x8e, 0x13, 0xc6, 0xc0,
    0x7b, 0x17, 0x4b, 0xe6, 0x5d, 0xfa, 0x57, 0x8e, 0x80
};

static const uint8_t msg6[13] = {
    0x64, 0xa6, 0x5f, 0x3c, 0xde, 0xdc, 0xdd, 0x66, 0x81, 0x1e, 0x29, 0x15,
    0xe7
};

static const uint8_t sig6[114] = {
    0x6a, 0x12, 0x06, 0x6f, 0x55, 0x33, 0x1b, 0x6c, 0x22, 0xac, 0xd5, 0xd5,
    0xbf, 0xc5, 0xd7, 0x12, 0x28, 0xfb, 0xda, 0x80, 0xae, 0x8d, 0xec, 0x26,
    0xbd, 0xd3, 0x06, 0x74, 0x3c, 0x50, 0x27, 0xcb, 0x48, 0x90, 0x81, 0x0c,
    0x16, 0x2c, 0x02, 0x74, 0x68, 0x67, 0x5e, 0xcf, 0x64, 0x5a, 0x83, 0x17,
    0x6c, 0x0d, 0x73, 0x23, 0xa2, 0xcc, 0xde, 0x2d, 0x80, 0xef, 0xe5, 0xa1,
    0x26, 0x8e, 0x8a, 0xca, 0x1d, 0x6f, 0xbc, 0x19, 0x4d, 0x3f, 0x77, 0xc4,
    0x49, 0x86, 0xeb, 0x4a, 0xb4, 0x17, 0x79, 0x19, 0xad, 0x8b, 0xec, 0x33,
    0xeb, 0x47, 0xbb, 0xb5, 0xfc, 0x6e, 0x28, 0x19, 0x6f, 0xd1, 0xca, 0xf5,
    0x6b, 0x4e, 0x7e, 0x0b, 0xa5, 0x51, 0x92, 0x34, 0xd0, 0x47, 0x15, 0x5a,
    0xc7, 0x27, 0xa1, 0x05, 0x31, 0x00
};

static const uint8_t privkey7[57] = {
    0xd6, 0x5d, 0xf3, 0x41, 0xad, 0x13, 0xe0, 0x08, 0x56, 0x76, 0x88, 0xba,
    0xed, 0xda, 0x8e, 0x9d, 0xcd, 0xc1, 0x7d, 0xc0, 0x24, 0x97, 0x4e, 0xa5,
    0xb4, 0x22, 0x7b, 0x65, 0x30, 0xe3, 0x39, 0xbf, 0xf2, 0x1f, 0x99, 0xe6,
    0x8c, 0xa6, 0x96, 0x8f, 0x3c, 0xca, 0x6d, 0xfe, 0x0f, 0xb9, 0xf4, 0xfa,
    0xb4, 0xfa, 0x13, 0x5d, 0x55, 0x42, 0xea, 0x3f, 0x01
};

static const uint8_t pubkey7[57] = {
    0xdf, 0x97, 0x05, 0xf5, 0x8e, 0xdb, 0xab, 0x80, 0x2c, 0x7f, 0x83, 0x63,
    0xcf, 0xe5, 0x56, 0x0a, 0xb1, 0xc6, 0x13, 0x2c, 0x20, 0xa9, 0xf1, 0xdd,
    0x16, 0x34, 0x83, 0xa2, 0x6f, 0x8a, 0xc5, 0x3a, 0x39, 0xd6, 0x80, 0x8b,
    0xf4, 0xa1, 0xdf, 0xbd, 0x26, 0x1b, 0x09, 0x9b, 0xb0, 0x3b, 0x3f, 0xb5,
    0x09, 0x06, 0xcb, 0x28, 0xbd, 0x8a, 0x08, 0x1f, 0x00
};

static const uint8_t msg7[64] = {
    0xbd, 0x0f, 0x6a, 0x37, 0x47, 0xcd, 0x56, 0x1b, 0xdd, 0xdf, 0x46, 0x40,
    0xa3, 0x32, 0x46, 0x1a, 0x4a, 0x30, 0xa1, 0x2a, 0x43, 0x4c, 0xd0, 0xbf,
    0x40, 0xd7, 0x66, 0xd9, 0xc6, 0xd4, 0x58, 0xe5, 0x51, 0x22, 0x04, 0xa3,
    0x0c, 0x17, 0xd1, 0xf5, 0x0b, 0x50, 0x79, 0x63, 0x1f, 0x64, 0xeb, 0x31,
    0x12, 0x18, 0x2d, 0xa3, 0x00, 0x58, 0x35, 0x46, 0x11, 0x13, 0x71, 0x8d,
    0x1a, 0x5e, 0xf9, 0x44
};

static const uint8_t sig7[114] = {
    0x55, 0x4b, 0xc2, 0x48, 0x08, 0x60, 0xb4, 0x9e, 0xab, 0x85, 0x32, 0xd2,
    0xa5, 0x33, 0xb7, 0xd5, 0x78, 0xef, 0x47, 0x3e, 0xeb, 0x58, 0xc9, 0x8b,
    0xb2, 0xd0, 0xe1, 0xce, 0x48, 0x8a, 0x98, 0xb1, 0x8d, 0xfd, 0xe9, 0xb9,
    0xb9, 0x07, 0x75, 0xe6, 0x7f, 0x47, 0xd4, 0xa1, 0xc3, 0x48, 0x20, 0x58,
    0xef, 0xc9, 0xf4, 0x0d, 0x2c, 0xa0, 0x33, 0xa0, 0x80, 0x1b, 0x63, 0xd4,
    0x5b, 0x3b, 0x72, 0x2e, 0xf5, 0x52, 0xba, 0xd3, 0xb4, 0xcc, 0xb6, 0x67,
    0xda, 0x35, 0x01, 0x92, 0xb6, 0x1c, 0x50, 0x8c, 0xf7, 0xb6, 0xb5, 0xad,
    0xad, 0xc2, 0xc8, 0xd9, 0xa4, 0x46, 0xef, 0x00, 0x3f, 0xb0, 0x5c, 0xba,
    0x5f, 0x30, 0xe8, 0x8e, 0x36, 0xec, 0x27, 0x03, 0xb3, 0x49, 0xca, 0x22,
    0x9c, 0x26, 0x70, 0x83, 0x39, 0x00
};

static const uint8_t privkey8[57] = {
    0x2e, 0xc5, 0xfe, 0x3c, 0x17, 0x04, 0x5a, 0xbd, 0xb1, 0x36, 0xa5, 0xe6,
    0xa9, 0x13, 0xe3, 0x2a, 0xb7, 0x5a, 0xe6, 0x8b, 0x53, 0xd2, 0xfc, 0x14,
    0x9b, 0x77, 0xe5, 0x04, 0x13, 0x2d, 0x37, 0x56, 0x9b, 0x7e, 0x76, 0x6b,
    0xa7, 0x4a, 0x19, 0xbd, 0x61, 0x62, 0x34, 0x3a, 0x21, 0xc8, 0x59, 0x0a,
    0xa9, 0xce, 0xbc, 0xa9, 0x01, 0x4c, 0x63, 0x6d, 0xf5
};

static const uint8_t pubkey8[57] = {
    0x79, 0x75, 0x6f, 0x01, 0x4d, 0xcf, 0xe2, 0x07, 0x9f, 0x5d, 0xd9, 0xe7,
    0x18, 0xbe, 0x41, 0x71, 0xe2, 0xef, 0x24, 0x86, 0xa0, 0x8f, 0x25, 0x18,
    0x6f, 0x6b, 0xff, 0x43, 0xa9, 0x93, 0x6b, 0x9b, 0xfe, 0x12, 0x40, 0x2b,
    0x08, 0xae, 0x65, 0x79, 0x8a, 0x3d, 0x81, 0xe2, 0x2e, 0x9e, 0xc8, 0x0e,
    0x76, 0x90, 0x86, 0x2e, 0xf3, 0xd4, 0xed, 0x3a, 0x00
};

static const uint8_t msg8[256] = {
    0x15, 0x77, 0x75, 0x32, 0xb0, 0xbd, 0xd0, 0xd1, 0x38, 0x9f, 0x63, 0x6c,
    0x5f, 0x6b, 0x9b, 0xa7, 0x34, 0xc9, 0x0a, 0xf5, 0x72, 0x87, 0x7e, 0x2d,
    0x27, 0x2d, 0xd0, 0x78, 0xaa, 0x1e, 0x56, 0x7c, 0xfa, 0x80, 0xe1, 0x29,
    0x28, 0xbb, 0x54, 0x23, 0x30, 0xe8, 0x40, 0x9f, 0x31, 0x74, 0x50, 0x41,
    0x07, 0xec, 0xd5, 0xef, 0xac, 0x61, 0xae, 0x75, 0x04, 0xda, 0xbe, 0x2a,
    0x60, 0x2e, 0xde, 0x89, 0xe5, 0xcc, 0xa6, 0x25, 0x7a, 0x7c, 0x77, 0xe2,
    0x7a, 0x70, 0x2b, 0x3a, 0xe3, 0x9f, 0xc7, 0x69, 0xfc, 0x54, 0xf2, 0x39,
    0x5a, 0xe6, 0xa1, 0x17, 0x8c, 0xab, 0x47, 0x38, 0xe5, 0x43, 0x07, 0x2f,
    0xc1, 0xc1, 0x77, 0xfe, 0x71, 0xe9, 0x2e, 0x25, 0xbf, 0x03, 0xe4, 0xec,
    0xb7, 0x2f, 0x47, 0xb6, 0x4d, 0x04, 0x65, 0xaa, 0xea, 0x4c, 0x7f, 0xad,
    0x37, 0x25, 0x36, 0xc8, 0xba, 0x51, 0x6a, 0x60, 0x39, 0xc3, 0xc2, 0xa3,
    0x9f, 0x0e, 0x4d, 0x83, 0x2b, 0xe4, 0x32, 0xdf, 0xa9, 0xa7, 0x06, 0xa6,
    0xe5, 0xc7, 0xe1, 0x9f, 0x39, 0x79, 0x64, 0xca, 0x42, 0x58, 0x00, 0x2f,
    0x7c, 0x05, 0x41, 0xb5, 0x90, 0x31, 0x6d, 0xbc, 0x56, 0x22, 0xb6, 0xb2,
    0xa6, 0xfe, 0x7a, 0x4a, 0xbf, 0xfd, 0x96, 0x10, 0x5e, 0xca, 0x76, 0xea,
    0x7b, 0x98, 0x81, 0x6a, 0xf0, 0x74, 0x8c, 0x10, 0xdf, 0x04, 0x8c, 0xe0,
    0x12, 0xd9, 0x01, 0x01, 0x5a, 0x51, 0xf1, 0x89, 0xf3, 0x88, 0x81, 0x45,
    0xc0, 0x36, 0x50, 0xaa, 0x23, 0xce, 0x89, 0x4c, 0x3b, 0xd8, 0x89, 0xe0,
    0x30, 0xd5, 0x65, 0x07, 0x1c, 0x59, 0xf4, 0x09, 0xa9, 0x98, 0x1b, 0x51,
    0x87, 0x8f, 0xd6, 0xfc, 0x11, 0x06, 0x24, 0xdc, 0xbc, 0xde, 0x0b, 0xf7,
    0xa6, 0x9c, 0xcc, 0xe3, 0x8f, 0xab, 0xdf, 0x86, 0xf3, 0xbe, 0xf6, 0x04,
    0x48, 0x19, 0xde, 0x11
};

static const uint8_t sig8[114] = {
    0xc6, 0x50, 0xdd, 0xbb, 0x06, 0x01, 0xc1, 0x9c, 0xa1, 0x14, 0x39, 0xe1,
    0x64, 0x0d, 0xd9, 0x31, 0xf4, 0x3c, 0x51, 0x8e, 0xa5, 0xbe, 0xa7, 0x0d,
    0x3d, 0xcd, 0xe5, 0xf4, 0x19, 0x1f, 0xe5, 0x3f, 0x00, 0xcf, 0x96, 0x65,
    0x46, 0xb7, 0x2b, 0xcc, 0x7d, 0x58, 0xbe, 0x2b, 0x9b, 0xad, 0xef, 0x28,
    0x74, 0x39, 0x54, 0xe3, 0xa4, 0x4a, 0x23, 0xf8, 0x80, 0xe8, 0xd4, 0xf1,
    0xcf, 0xce, 0x2d, 0x7a, 0x61, 0x45, 0x2d, 0x26, 0xda, 0x05, 0x89, 0x6f,
    0x0a, 0x50, 0xda, 0x66, 0xa2, 0x39, 0xa8, 0xa1, 0x88, 0xb6, 0xd8, 0x25,
    0xb3, 0x30, 0x5a, 0xd7, 0x7b, 0x73, 0xfb, 0xac, 0x08, 0x36, 0xec, 0xc6,
    0x09, 0x87, 0xfd, 0x08, 0x52, 0x7c, 0x1a, 0x8e, 0x80, 0xd5, 0x82, 0x3e,
    0x65, 0xca, 0xfe, 0x2a, 0x3d, 0x00
};

static const uint8_t privkey9[57] = {
    0x87, 0x2d, 0x09, 0x37, 0x80, 0xf5, 0xd3, 0x73, 0x0d, 0xf7, 0xc2, 0x12,
    0x66, 0x4b, 0x37, 0xb8, 0xa0, 0xf2, 0x4f, 0x56, 0x81, 0x0d, 0xaa, 0x83,
    0x82, 0xcd, 0x4f, 0xa3, 0xf7, 0x76, 0x34, 0xec, 0x44, 0xdc, 0x54, 0xf1,
    0xc2, 0xed, 0x9b, 0xea, 0x86, 0xfa, 0xfb, 0x76, 0x32, 0xd8, 0xbe, 0x19,
    0x9e, 0xa1, 0x65, 0xf5, 0xad, 0x55, 0xdd, 0x9c, 0xe8
};

static const uint8_t pubkey9[57] = {
    0xa8, 0x1b, 0x2e, 0x8a, 0x70, 0xa5, 0xac, 0x94, 0xff, 0xdb, 0xcc, 0x9b,
    0xad, 0xfc, 0x3f, 0xeb, 0x08, 0x01, 0xf2, 0x58, 0x57, 0x8b, 0xb1, 0x14,
    0xad, 0x44, 0xec, 0xe1, 0xec, 0x0e, 0x79, 0x9d, 0xa0, 0x8e, 0xff, 0xb8,
    0x1c, 0x5d, 0x68, 0x5c, 0x0c, 0x56, 0xf6, 0x4e, 0xec, 0xae, 0xf8, 0xcd,
    0xf1, 0x1c, 0xc3, 0x87, 0x37, 0x83, 0x8c, 0xf4, 0x00
};

static const uint8_t msg9[1023] = {
    0x6d, 0xdf, 0x80, 0x2e, 0x1a, 0xae, 0x49, 0x86, 0x93, 0x5f, 0x7f, 0x98,
    0x1b, 0xa3, 0xf0, 0x35, 0x1d, 0x62, 0x73, 0xc0, 0xa0, 0xc2, 0x2c, 0x9c,
    0x0e, 0x83, 0x39, 0x16, 0x8e, 0x67, 0x54, 0x12, 0xa3, 0xde, 0xbf, 0xaf,
    0x43, 0x5e, 0xd6, 0x51, 0x55, 0x80, 0x07, 0xdb, 0x43, 0x84, 0xb6, 0x50,
    0xfc, 0xc0, 0x7e, 0x3b, 0x58, 0x6a, 0x27, 0xa4, 0xf7, 0xa0, 0x0a, 0xc8,
    0xa6, 0xfe, 0xc2, 0xcd, 0x86, 0xae, 0x4b, 0xf1, 0x57, 0x0c, 0x41, 0xe6,
    0xa4, 0x0c, 0x93, 0x1d, 0xb2, 0x7b, 0x2f, 0xaa, 0x15, 0xa8, 0xce, 0xdd,
    0x52, 0xcf, 0xf7, 0x36, 0x2c, 0x4e, 0x6e, 0x23, 0xda, 0xec, 0x0f, 0xbc,
    0x3a, 0x79, 0xb6, 0x80, 0x6e, 0x31, 0x6e, 0xfc, 0xc7, 0xb6, 0x81, 0x19,
    0xbf, 0x46, 0xbc, 0x76, 0xa2, 0x60, 0x67, 0xa5, 0x3f, 0x29, 0x6d, 0xaf,
    0xdb, 0xdc, 0x11, 0xc7, 0x7f, 0x77, 0x77, 0xe9, 0x72, 0x66, 0x0c, 0xf4,
    0xb6, 0xa9, 0xb3, 0x69, 0xa6, 0x66, 0x5f, 0x02, 0xe0, 0xcc, 0x9b, 0x6e,
    0xdf, 0xad, 0x13, 0x6b, 0x4f, 0xab, 0xe7, 0x23, 0xd2, 0x81, 0x3d, 0xb3,
    0x13, 0x6c, 0xfd, 0xe9, 0xb6, 0xd0, 0x44, 0x32, 0x2f, 0xee, 0x29, 0x47,
    0x95, 0x2e, 0x03, 0x1b, 0x73, 0xab, 0x5c, 0x60, 0x33, 0x49, 0xb3, 0x07,
    0xbd, 0xc2, 0x7b, 0xc6, 0xcb, 0x8b, 0x8b, 0xbd, 0x7b, 0xd3, 0x23, 0x21,
    0x9b, 0x80, 0x33, 0xa5, 0x81, 0xb5, 0x9e, 0xad, 0xeb, 0xb0, 0x9b, 0x3c,
    0x4f, 0x3d, 0x22, 0x77, 0xd4, 0xf0, 0x34, 0x36, 0x24, 0xac, 0xc8, 0x17,
    0x80, 0x47, 0x28, 0xb2, 0x5a, 0xb7, 0x97, 0x17, 0x2b, 0x4c, 0x5c, 0x21,
    0xa2, 0x2f, 0x9c, 0x78, 0x39, 0xd6, 0x43, 0x00, 0x23, 0x2e, 0xb6, 0x6e,
    0x53, 0xf3, 0x1c, 0x72, 0x3f, 0xa3, 0x7f, 0xe3, 0x87, 0xc7, 0xd3, 0xe5,
    0x0b, 0xdf, 0x98, 0x13, 0xa3, 0x0e, 0x5b, 0xb1, 0x2c, 0xf4, 0xcd, 0x93,
    0x0c, 0x40, 0xcf, 0xb4, 0xe1, 0xfc, 0x62, 0x25, 0x92, 0xa4, 0x95, 0x88,
    0x79, 0x44, 0x94, 0xd5, 0x6d, 0x24, 0xea, 0x4b, 0x40, 0xc8, 0x9f, 0xc0,
    0x59, 0x6c, 0xc9, 0xeb, 0xb9, 0x61, 0xc8, 0xcb, 0x10, 0xad, 0xde, 0x97,
    0x6a, 0x5d, 0x60, 0x2b, 0x1c, 0x3f, 0x85, 0xb9, 0xb9, 0xa0, 0x01, 0xed,
    0x3c, 0x6a, 0x4d, 0x3b, 0x14, 0x37, 0xf5, 0x20, 0x96, 0xcd, 0x19, 0x56,
    0xd0, 0x42, 0xa5, 0x97, 0xd5, 0x61, 0xa5, 0x96, 0xec, 0xd3, 0xd1, 0x73,
    0x5a, 0x8d, 0x57, 0x0e, 0xa0, 0xec, 0x27, 0x22, 0x5a, 0x2c, 0x4a, 0xaf,
    0xf2, 0x63, 0x06, 0xd1, 0x52, 0x6c, 0x1a, 0xf3, 0xca, 0x6d, 0x9c, 0xf5,
    0xa2, 0xc9, 0x8f, 0x47, 0xe1, 0xc4, 0x6d, 0xb9, 0xa3, 0x32, 0x34, 0xcf,
    0xd4, 0xd8, 0x1f, 0x2c, 0x98, 0x53, 0x8a, 0x09, 0xeb, 0xe7, 0x69, 0x98,
    0xd0, 0xd8, 0xfd, 0x25, 0x99, 0x7c, 0x7d, 0x25, 0x5c, 0x6d, 0x66, 0xec,
    0xe6, 0xfa, 0x56, 0xf1, 0x11, 0x44, 0x95, 0x0f, 0x02, 0x77, 0x95, 0xe6,
    0x53, 0x00, 0x8f, 0x4b, 0xd7, 0xca, 0x2d, 0xee, 0x85, 0xd8, 0xe9, 0x0f,
    0x3d, 0xc3, 0x15, 0x13, 0x0c, 0xe2, 0xa0, 0x03, 0x75, 0xa3, 0x18, 0xc7,
    0xc3, 0xd9, 0x7b, 0xe2, 0xc8, 0xce, 0x5b, 0x6d, 0xb4, 0x1a, 0x62, 0x54,
    0xff, 0x26, 0x4f, 0xa6, 0x15, 0x5b, 0xae, 0xe3, 0xb0, 0x77, 0x3c, 0x0f,
    0x49, 0x7c, 0x57, 0x3f, 0x19, 0xbb, 0x4f, 0x42, 0x40, 0x28, 0x1f, 0x0b,
    0x1f, 0x4f, 0x7b, 0xe8, 0x57, 0xa4, 0xe5, 0x9d, 0x41, 0x6c, 0x06, 0xb4,
    0xc5, 0x0f, 0xa0, 0x9e, 0x18, 0x10, 0xdd, 0xc6, 0xb1, 0x46, 0x7b, 0xae,
    0xac, 0x5a, 0x36, 0x68, 0xd1, 0x1b, 0x6e, 0xca, 0xa9, 0x01, 0x44, 0x00,
    0x16, 0xf3, 0x89, 0xf8, 0x0a, 0xcc, 0x4d, 0xb9, 0x77, 0x02, 0x5e, 0x7f,
    0x59, 0x24, 0x38, 0x8c, 0x7e, 0x34, 0x0a, 0x73, 0x2e, 0x55, 0x44, 0x40,
    0xe7, 0x65, 0x70, 0xf8, 0xdd, 0x71, 0xb7, 0xd6, 0x40, 0xb3, 0x45, 0x0d,
    0x1f, 0xd5, 0xf0, 0x41, 0x0a, 0x18, 0xf9, 0xa3, 0x49, 0x4f, 0x70, 0x7c,
    0x71, 0x7b, 0x79, 0xb4, 0xbf, 0x75, 0xc9, 0x84, 0x00, 0xb0, 0x96, 0xb2,
    0x16, 0x53, 0xb5, 0xd2, 0x17, 0xcf, 0x35, 0x65, 0xc9, 0x59, 0x74, 0x56,
    0xf7, 0x07, 0x03, 0x49, 0x7a, 0x07, 0x87, 0x63, 0x82, 0x9b, 0xc0, 0x1b,
    0xb1, 0xcb, 0xc8, 0xfa, 0x04, 0xea, 0xdc, 0x9a, 0x6e, 0x3f, 0x66, 0x99,
    0x58, 0x7a, 0x9e, 0x75, 0xc9, 0x4e, 0x5b, 0xab, 0x00, 0x36, 0xe0, 0xb2,
    0xe7, 0x11, 0x39, 0x2c, 0xff, 0x00, 0x47, 0xd0, 0xd6, 0xb0, 0x5b, 0xd2,
    0xa5, 0x88, 0xbc, 0x10, 0x97, 0x18, 0x95, 0x42, 0x59, 0xf1, 0xd8, 0x66,
    0x78, 0xa5, 0x79, 0xa3, 0x12, 0x0f, 0x19, 0xcf, 0xb2, 0x96, 0x3f, 0x17,
    0x7a, 0xeb, 0x70, 0xf2, 0xd4, 0x84, 0x48, 0x26, 0x26, 0x2e, 0x51, 0xb8,
    0x02, 0x71, 0x27, 0x20, 0x68, 0xef, 0x5b, 0x38, 0x56, 0xfa, 0x85, 0x35,
    0xaa, 0x2a, 0x88, 0xb2, 0xd4, 0x1f, 0x2a, 0x0e, 0x2f, 0xda, 0x76, 0x24,
    0xc2, 0x85, 0x02, 0x72, 0xac, 0x4a, 0x2f, 0x56, 0x1f, 0x8f, 0x2f, 0x7a,
    0x31, 0x8b, 0xfd, 0x5c, 0xaf, 0x96, 0x96, 0x14, 0x9e, 0x4a, 0xc8, 0x24,
    0xad, 0x34, 0x60, 0x53, 0x8f, 0xdc, 0x25, 0x42, 0x1b, 0xee, 0xc2, 0xcc,
    0x68, 0x18, 0x16, 0x2d, 0x06, 0xbb, 0xed, 0x0c, 0x40, 0xa3, 0x87, 0x19,
    0x23, 0x49, 0xdb, 0x67, 0xa1, 0x18, 0xba, 0xda, 0x6c, 0xd5, 0xab, 0x01,
    0x40, 0xee, 0x27, 0x32, 0x04, 0xf6, 0x28, 0xaa, 0xd1, 0xc1, 0x35, 0xf7,
    0x70, 0x27, 0x9a, 0x65, 0x1e, 0x24, 0xd8, 0xc1, 0x4d, 0x75, 0xa6, 0x05,
    0x9d, 0x76, 0xb9, 0x6a, 0x6f, 0xd8, 0x57, 0xde, 0xf5, 0xe0, 0xb3, 0x54,
    0xb2, 0x7a, 0xb9, 0x37, 0xa5, 0x81, 0x5d, 0x16, 0xb5, 0xfa, 0xe4, 0x07,
    0xff, 0x18, 0x22, 0x2c, 0x6d, 0x1e, 0xd2, 0x63, 0xbe, 0x68, 0xc9, 0x5f,
    0x32, 0xd9, 0x08, 0xbd, 0x89, 0x5c, 0xd7, 0x62, 0x07, 0xae, 0x72, 0x64,
    0x87, 0x56, 0x7f, 0x9a, 0x67, 0xda, 0xd7, 0x9a, 0xbe, 0xc3, 0x16, 0xf6,
    0x83, 0xb1, 0x7f, 0x2d, 0x02, 0xbf, 0x07, 0xe0, 0xac, 0x8b, 0x5b, 0xc6,
    0x16, 0x2c, 0xf9, 0x46, 0x97, 0xb3, 0xc2, 0x7c, 0xd1, 0xfe, 0xa4, 0x9b,
    0x27, 0xf2, 0x3b, 0xa2, 0x90, 0x18, 0x71, 0x96, 0x25, 0x06, 0x52, 0x0c,
    0x39, 0x2d, 0xa8, 0xb6, 0xad, 0x0d, 0x99, 0xf7, 0x01, 0x3f, 0xbc, 0x06,
    0xc2, 0xc1, 0x7a, 0x56, 0x95, 0x00, 0xc8, 0xa7, 0x69, 0x64, 0x81, 0xc1,
    0xcd, 0x33, 0xe9, 0xb1, 0x4e, 0x40, 0xb8, 0x2e, 0x79, 0xa5, 0xf5, 0xdb,
    0x82, 0x57, 0x1b, 0xa9, 0x7b, 0xae, 0x3a, 0xd3, 0xe0, 0x47, 0x95, 0x15,
    0xbb, 0x0e, 0x2b, 0x0f, 0x3b, 0xfc, 0xd1, 0xfd, 0x33, 0x03, 0x4e, 0xfc,
    0x62, 0x45, 0xed, 0xdd, 0x7e, 0xe2, 0x08, 0x6d, 0xda, 0xe2, 0x60, 0x0d,
    0x8c, 0xa7, 0x3e, 0x21, 0x4e, 0x8c, 0x2b, 0x0b, 0xdb, 0x2b, 0x04, 0x7c,
    0x6a, 0x46, 0x4a, 0x56, 0x2e, 0xd7, 0x7b, 0x73, 0xd2, 0xd8, 0x41, 0xc4,
    0xb3, 0x49, 0x73, 0x55, 0x12, 0x57, 0x71, 0x3b, 0x75, 0x36, 0x32, 0xef,
    0xba, 0x34, 0x81, 0x69, 0xab, 0xc9, 0x0a, 0x68, 0xf4, 0x26, 0x11, 0xa4,
    0x01, 0x26, 0xd7, 0xcb, 0x21, 0xb5, 0x86, 0x95, 0x56, 0x81, 0x86, 0xf7,
    0xe5, 0x69, 0xd2, 0xff, 0x0f, 0x9e, 0x74, 0x5d, 0x04, 0x87, 0xdd, 0x2e,
    0xb9, 0x97, 0xca, 0xfc, 0x5a, 0xbf, 0x9d, 0xd1, 0x02, 0xe6, 0x2f, 0xf6,
    0x6c, 0xba, 0x87
};

static const uint8_t sig9[114] = {
    0xe3, 0x01, 0x34, 0x5a, 0x41, 0xa3, 0x9a, 0x4d, 0x72, 0xff, 0xf8, 0xdf,
    0x69, 0xc9, 0x80, 0x75, 0xa0, 0xcc, 0x08, 0x2b, 0x80, 0x2f, 0xc9, 0xb2,
    0xb6, 0xbc, 0x50, 0x3f, 0x92, 0x6b, 0x65, 0xbd, 0xdf, 0x7f, 0x4c, 0x8f,
    0x1c, 0xb4, 0x9f, 0x63, 0x96, 0xaf, 0xc8, 0xa7, 0x0a, 0xbe, 0x6d, 0x8a,
    0xef, 0x0d, 0xb4, 0x78, 0xd4, 0xc6, 0xb2, 0x97, 0x00, 0x76, 0xc6, 0xa0,
    0x48, 0x4f, 0xe7, 0x6d, 0x76, 0xb3, 0xa9, 0x76, 0x25, 0xd7, 0x9f, 0x1c,
    0xe2, 0x40, 0xe7, 0xc5, 0x76, 0x75, 0x0d, 0x29, 0x55, 0x28, 0x28, 0x6f,
    0x71, 0x9b, 0x41, 0x3d, 0xe9, 0xad, 0xa3, 0xe8, 0xeb, 0x78, 0xed, 0x57,
    0x36, 0x03, 0xce, 0x30, 0xd8, 0xbb, 0x76, 0x17, 0x85, 0xdc, 0x30, 0xdb,
    0xc3, 0x20, 0x86, 0x9e, 0x1a, 0x00
};

/* Prehash Ed448 */

static const uint8_t phprivkey1[57] = {
    0x83, 0x3f, 0xe6, 0x24, 0x09, 0x23, 0x7b, 0x9d, 0x62, 0xec, 0x77, 0x58,
    0x75, 0x20, 0x91, 0x1e, 0x9a, 0x75, 0x9c, 0xec, 0x1d, 0x19, 0x75, 0x5b,
    0x7d, 0xa9, 0x01, 0xb9, 0x6d, 0xca, 0x3d, 0x42, 0xef, 0x78, 0x22, 0xe0,
    0xd5, 0x10, 0x41, 0x27, 0xdc, 0x05, 0xd6, 0xdb, 0xef, 0xde, 0x69, 0xe3,
    0xab, 0x2c, 0xec, 0x7c, 0x86, 0x7c, 0x6e, 0x2c, 0x49
};

static const uint8_t phpubkey1[57] = {
    0x25, 0x9b, 0x71, 0xc1, 0x9f, 0x83, 0xef, 0x77, 0xa7, 0xab, 0xd2, 0x65,
    0x24, 0xcb, 0xdb, 0x31, 0x61, 0xb5, 0x90, 0xa4, 0x8f, 0x7d, 0x17, 0xde,
    0x3e, 0xe0, 0xba, 0x9c, 0x52, 0xbe, 0xb7, 0x43, 0xc0, 0x94, 0x28, 0xa1,
    0x31, 0xd6, 0xb1, 0xb5, 0x73, 0x03, 0xd9, 0x0d, 0x81, 0x32, 0xc2, 0x76,
    0xd5, 0xed, 0x3d, 0x5d, 0x01, 0xc0, 0xf5, 0x38, 0x80
};

static const uint8_t phmsg1[3] = {
    0x61, 0x62, 0x63
};

static const uint8_t phsig1[114] = {
    0x82, 0x2f, 0x69, 0x01, 0xf7, 0x48, 0x0f, 0x3d, 0x5f, 0x56, 0x2c, 0x59,
    0x29, 0x94, 0xd9, 0x69, 0x36, 0x02, 0x87, 0x56, 0x14, 0x48, 0x32, 0x56,
    0x50, 0x56, 0x00, 0xbb, 0xc2, 0x81, 0xae, 0x38, 0x1f, 0x54, 0xd6, 0xbc,
    0xe2, 0xea, 0x91, 0x15, 0x74, 0x93, 0x2f, 0x52, 0xa4, 0xe6, 0xca, 0xdd,
    0x78, 0x76, 0x93, 0x75, 0xec, 0x3f, 0xfd, 0x1b, 0x80, 0x1a, 0x0d, 0x9b,
    0x3f, 0x40, 0x30, 0xcd, 0x43, 0x39, 0x64, 0xb6, 0x45, 0x7e, 0xa3, 0x94,
    0x76, 0x51, 0x12, 0x14, 0xf9, 0x74, 0x69, 0xb5, 0x7d, 0xd3, 0x2d, 0xbc,
    0x56, 0x0a, 0x9a, 0x94, 0xd0, 0x0b, 0xff, 0x07, 0x62, 0x04, 0x64, 0xa3,
    0xad, 0x20, 0x3d, 0xf7, 0xdc, 0x7c, 0xe3, 0x60, 0xc3, 0xcd, 0x36, 0x96,
    0xd9, 0xd9, 0xfa, 0xb9, 0x0f, 0x00
};

static const uint8_t phprivkey2[57] = {
    0x83, 0x3f, 0xe6, 0x24, 0x09, 0x23, 0x7b, 0x9d, 0x62, 0xec, 0x77, 0x58,
    0x75, 0x20, 0x91, 0x1e, 0x9a, 0x75, 0x9c, 0xec, 0x1d, 0x19, 0x75, 0x5b,
    0x7d, 0xa9, 0x01, 0xb9, 0x6d, 0xca, 0x3d, 0x42, 0xef, 0x78, 0x22, 0xe0,
    0xd5, 0x10, 0x41, 0x27, 0xdc, 0x05, 0xd6, 0xdb, 0xef, 0xde, 0x69, 0xe3,
    0xab, 0x2c, 0xec, 0x7c, 0x86, 0x7c, 0x6e, 0x2c, 0x49
};

static const uint8_t phpubkey2[57] = {
    0x25, 0x9b, 0x71, 0xc1, 0x9f, 0x83, 0xef, 0x77, 0xa7, 0xab, 0xd2, 0x65,
    0x24, 0xcb, 0xdb, 0x31, 0x61, 0xb5, 0x90, 0xa4, 0x8f, 0x7d, 0x17, 0xde,
    0x3e, 0xe0, 0xba, 0x9c, 0x52, 0xbe, 0xb7, 0x43, 0xc0, 0x94, 0x28, 0xa1,
    0x31, 0xd6, 0xb1, 0xb5, 0x73, 0x03, 0xd9, 0x0d, 0x81, 0x32, 0xc2, 0x76,
    0xd5, 0xed, 0x3d, 0x5d, 0x01, 0xc0, 0xf5, 0x38, 0x80
};

static const uint8_t phmsg2[3] = {
    0x61, 0x62, 0x63
};

static const uint8_t phcontext2[3] = {
    0x66, 0x6f, 0x6f
};

static const uint8_t phsig2[114] = {
    0xc3, 0x22, 0x99, 0xd4, 0x6e, 0xc8, 0xff, 0x02, 0xb5, 0x45, 0x40, 0x98,
    0x28, 0x14, 0xdc, 0xe9, 0xa0, 0x58, 0x12, 0xf8, 0x19, 0x62, 0xb6, 0x49,
    0xd5, 0x28, 0x09, 0x59, 0x16, 0xa2, 0xaa, 0x48, 0x10, 0x65, 0xb1, 0x58,
    0x04, 0x23, 0xef, 0x92, 0x7e, 0xcf, 0x0a, 0xf5, 0x88, 0x8f, 0x90, 0xda,
    0x0f, 0x6a, 0x9a, 0x85, 0xad, 0x5d, 0xc3, 0xf2, 0x80, 0xd9, 0x12, 0x24,
    0xba, 0x99, 0x11, 0xa3, 0x65, 0x3d, 0x00, 0xe4, 0x84, 0xe2, 0xce, 0x23,
    0x25, 0x21, 0x48, 0x1c, 0x86, 0x58, 0xdf, 0x30, 0x4b, 0xb7, 0x74, 0x5a,
    0x73, 0x51, 0x4c, 0xdb, 0x9b, 0xf3, 0xe1, 0x57, 0x84, 0xab, 0x71, 0x28,
    0x4f, 0x8d, 0x07, 0x04, 0xa6, 0x08, 0xc5, 0x4a, 0x6b, 0x62, 0xd9, 0x7b,
    0xeb, 0x51, 0x1d, 0x13, 0x21, 0x00
};

static const uint8_t *dohash(EVP_MD_CTX *hashctx, const uint8_t *msg,
                             size_t msglen)
{
    static uint8_t hashout[64];

    if (!EVP_DigestInit_ex(hashctx, EVP_shake256(), NULL)
            || !EVP_DigestUpdate(hashctx, msg, msglen)
            || !EVP_DigestFinalXOF(hashctx, hashout, sizeof(hashout)))
        return NULL;

    return hashout;
}

static int test_ed448(void)
{
    uint8_t outsig[114];
    EVP_MD_CTX *hashctx = EVP_MD_CTX_new();

    if (!TEST_ptr(hashctx)
            || !TEST_true(ossl_ed448_sign(NULL, outsig, NULL, 0, pubkey1,
                                          privkey1, NULL, 0, NULL))
            || !TEST_int_eq(memcmp(sig1, outsig, sizeof(sig1)), 0)
            || !TEST_true(ossl_ed448_sign(NULL, outsig, msg2, sizeof(msg2),
                                          pubkey2, privkey2, NULL, 0, NULL))
            || !TEST_int_eq(memcmp(sig2, outsig, sizeof(sig2)), 0)
            || !TEST_true(ossl_ed448_sign(NULL, outsig, msg3, sizeof(msg3),
                                          pubkey3, privkey3, context3,
                                          sizeof(context3), NULL))
            || !TEST_int_eq(memcmp(sig3, outsig, sizeof(sig3)), 0)
            || !TEST_true(ossl_ed448_sign(NULL, outsig, msg4, sizeof(msg4),
                                          pubkey4, privkey4, NULL, 0, NULL))
            || !TEST_int_eq(memcmp(sig4, outsig, sizeof(sig4)), 0)
            || !TEST_true(ossl_ed448_sign(NULL, outsig, msg5, sizeof(msg5),
                                          pubkey5, privkey5, NULL, 0, NULL))
            || !TEST_int_eq(memcmp(sig5, outsig, sizeof(sig5)), 0)
            || !TEST_true(ossl_ed448_sign(NULL, outsig, msg6, sizeof(msg6),
                                          pubkey6, privkey6, NULL, 0, NULL))
            || !TEST_int_eq(memcmp(sig6, outsig, sizeof(sig6)), 0)
            || !TEST_true(ossl_ed448_sign(NULL, outsig, msg7, sizeof(msg7),
                                          pubkey7, privkey7, NULL, 0, NULL))
            || !TEST_int_eq(memcmp(sig7, outsig, sizeof(sig7)), 0)
            || !TEST_true(ossl_ed448_sign(NULL, outsig, msg8, sizeof(msg8),
                                          pubkey8, privkey8, NULL, 0, NULL))
            || !TEST_int_eq(memcmp(sig8, outsig, sizeof(sig8)), 0)
            || !TEST_true(ossl_ed448_sign(NULL, outsig, msg9, sizeof(msg9),
                                          pubkey9, privkey9, NULL, 0, NULL))
            || !TEST_int_eq(memcmp(sig9, outsig, sizeof(sig9)), 0)
            || !TEST_true(ossl_ed448ph_sign(NULL, outsig,
                                            dohash(hashctx, phmsg1,
                                                   sizeof(phmsg1)), phpubkey1,
                                                   phprivkey1, NULL, 0, NULL))
            || !TEST_int_eq(memcmp(phsig1, outsig, sizeof(phsig1)), 0)
            || !TEST_true(ossl_ed448ph_sign(NULL, outsig,
                                            dohash(hashctx, phmsg2,
                                                   sizeof(phmsg2)), phpubkey2,
                                                   phprivkey2, phcontext2,
                                                   sizeof(phcontext2), NULL))
            || !TEST_int_eq(memcmp(phsig2, outsig, sizeof(phsig2)), 0)) {
        EVP_MD_CTX_free(hashctx);
        return 0;
    }

    EVP_MD_CTX_free(hashctx);
    return 1;
}

static int test_x448(void)
{
    uint8_t u[56], k[56], out[56];
    unsigned int i;
    int j = -1;

    /* Curve448 tests */

    if (!TEST_true(ossl_x448(out, in_scalar1, in_u1))
          || !TEST_int_eq(memcmp(out, out_u1, sizeof(out)), 0)
          || !TEST_true(ossl_x448(out, in_scalar2, in_u2))
          || !TEST_int_eq(memcmp(out, out_u2, sizeof(out)), 0))
        return 0;

    memcpy(u, in_u3, sizeof(u));
    memcpy(k, in_u3, sizeof(k));
    for (i = 1; i <= max; i++) {
        if (verbose && i % 10000 == 0) {
            printf(".");
            fflush(stdout);
        }

        if (!TEST_true(ossl_x448(out, k, u)))
            return 0;

        if (i == 1 || i == 1000 || i == 1000000) {
            j++;
            if (!TEST_int_eq(memcmp(out, out_u3[j], sizeof(out)), 0)) {
                TEST_info("Failed at iteration %d", i);
                return 0;
            }
        }
        memcpy(u, k, sizeof(u));
        memcpy(k, out, sizeof(k));
    }

    return 1;
}

typedef enum OPTION_choice {
    OPT_ERR = -1,
    OPT_EOF = 0,
    OPT_PROGRESS,
    OPT_SLOW,
    OPT_TEST_ENUM
} OPTION_CHOICE;

const OPTIONS *test_get_options(void)
{
    static const OPTIONS test_options[] = {
        OPT_TEST_OPTIONS_WITH_EXTRA_USAGE("conf_file\n"),
        { "f", OPT_SLOW, '-', "Enables a slow test" },
        { "v", OPT_PROGRESS, '-',
              "Enables verbose mode (prints progress dots)" },
        { NULL }
    };
    return test_options;
}

int setup_tests(void)
{
    OPTION_CHOICE o;

    while ((o = opt_next()) != OPT_EOF) {
        switch (o) {
        case OPT_TEST_CASES:
            break;
        default:
            return 0;
        /*
         * The test vectors contain one test which takes a very long time to run
         * so we don't do that be default. Using the -f option will cause it to
         * be run.
         */
        case OPT_SLOW:
            max = 1000000;
            break;
        case OPT_PROGRESS:
            verbose = 1; /* Print progress dots */
            break;
        }
    }

    ADD_TEST(test_x448);
    ADD_TEST(test_ed448);
    return 1;
}
