/*
 * Copyright 2018-2020 The OpenSSL Project Authors. All Rights Reserved.
 *
 * Licensed under the Apache License 2.0 (the "License").  You may not use
 * this file except in compliance with the License.  You can obtain a copy
 * in the file LICENSE in the source distribution or at
 * https://www.openssl.org/source/license.html
 */

/*
 * DSA low level APIs are deprecated for public use, but still ok for
 * internal use.
 */
#include "internal/deprecated.h"

#include <stdlib.h>
#include <string.h>

#include "testutil.h"

#include <openssl/evp.h>
#include <openssl/err.h>
#include <openssl/rand.h>

#ifndef OPENSSL_NO_DSA
#include <openssl/dsa.h>

static DSA *dsakey;

/*
 * These parameters are from test/recipes/04-test_pem_data/dsaparam.pem,
 * converted using dsaparam -C
 */
static DSA *load_dsa_params(void)
{
    static unsigned char dsap_2048[] = {
        0xAE, 0x35, 0x7D, 0x4E, 0x1D, 0x96, 0xE2, 0x9F, 0x00, 0x96,
        0x60, 0x5A, 0x6E, 0x4D, 0x07, 0x8D, 0xA5, 0x7C, 0xBC, 0xF9,
        0xAD, 0xD7, 0x9F, 0xD5, 0xE9, 0xEE, 0xA6, 0x33, 0x51, 0xDE,
        0x7B, 0x72, 0xD2, 0x75, 0xAA, 0x71, 0x77, 0xF1, 0x63, 0xFB,
        0xB6, 0xEC, 0x5A, 0xBA, 0x0D, 0x72, 0xA2, 0x1A, 0x1C, 0x64,
        0xB8, 0xE5, 0x89, 0x09, 0x6D, 0xC9, 0x6F, 0x0B, 0x7F, 0xD2,
        0xCE, 0x9F, 0xEF, 0x87, 0x5A, 0xB6, 0x67, 0x2F, 0xEF, 0xEE,
        0xEB, 0x59, 0xF5, 0x5E, 0xFF, 0xA8, 0x28, 0x84, 0x9E, 0x5B,
        0x37, 0x09, 0x11, 0x80, 0x7C, 0x08, 0x5C, 0xD5, 0xE1, 0x48,
        0x4B, 0xD2, 0x68, 0xFB, 0x3F, 0x9F, 0x2B, 0x6B, 0x6C, 0x0D,
        0x48, 0x1B, 0x1A, 0x80, 0xC2, 0xEB, 0x11, 0x1B, 0x37, 0x79,
        0xD6, 0x8C, 0x8B, 0x72, 0x3E, 0x67, 0xA5, 0x05, 0x0E, 0x41,
        0x8A, 0x9E, 0x35, 0x50, 0xB4, 0xD2, 0x40, 0x27, 0x6B, 0xFD,
        0xE0, 0x64, 0x6B, 0x5B, 0x38, 0x42, 0x94, 0xB5, 0x49, 0xDA,
        0xEF, 0x6E, 0x78, 0x37, 0xCD, 0x30, 0x89, 0xC3, 0x45, 0x50,
        0x7B, 0x9C, 0x8C, 0xE7, 0x1C, 0x98, 0x70, 0x71, 0x5D, 0x79,
        0x5F, 0xEF, 0xE8, 0x94, 0x85, 0x53, 0x3E, 0xEF, 0xA3, 0x2C,
        0xCE, 0x1A, 0xAB, 0x7D, 0xD6, 0x5E, 0x14, 0xCD, 0x51, 0x54,
        0x89, 0x9D, 0x77, 0xE4, 0xF8, 0x22, 0xF0, 0x35, 0x10, 0x75,
        0x05, 0x71, 0x51, 0x4F, 0x8C, 0x4C, 0x5C, 0x0D, 0x2C, 0x2C,
        0xBE, 0x6C, 0x34, 0xEE, 0x12, 0x82, 0x87, 0x03, 0x19, 0x06,
        0x12, 0xA8, 0xAA, 0xF4, 0x0D, 0x3C, 0x49, 0xCC, 0x70, 0x5A,
        0xD8, 0x32, 0xEE, 0x32, 0x50, 0x85, 0x70, 0xE8, 0x18, 0xFD,
        0x74, 0x80, 0x53, 0x32, 0x57, 0xEE, 0x50, 0xC9, 0xAE, 0xEB,
        0xAE, 0xB6, 0x22, 0x32, 0x16, 0x6B, 0x8C, 0x59, 0xDA, 0xEE,
        0x1D, 0x33, 0xDF, 0x4C, 0xA2, 0x3D
    };
    static unsigned char dsaq_2048[] = {
        0xAD, 0x2D, 0x6E, 0x17, 0xB0, 0xF3, 0xEB, 0xC7, 0xB8, 0xEE,
        0x95, 0x78, 0xF2, 0x17, 0xF5, 0x33, 0x01, 0x67, 0xBC, 0xDE,
        0x93, 0xFF, 0xEE, 0x40, 0xE8, 0x7F, 0xF1, 0x93, 0x6D, 0x4B,
        0x87, 0x13
    };
    static unsigned char dsag_2048[] = {
        0x66, 0x6F, 0xDA, 0x63, 0xA5, 0x8E, 0xD2, 0x4C, 0xD5, 0x45,
        0x2D, 0x76, 0x5D, 0x5F, 0xCD, 0x4A, 0xB4, 0x1A, 0x42, 0x35,
        0x86, 0x3A, 0x6F, 0xA9, 0xFA, 0x27, 0xAB, 0xDE, 0x03, 0x21,
        0x36, 0x0A, 0x07, 0x29, 0xC9, 0x2F, 0x6D, 0x49, 0xA8, 0xF7,
        0xC6, 0xF4, 0x92, 0xD7, 0x73, 0xC1, 0xD8, 0x76, 0x0E, 0x61,
        0xA7, 0x0B, 0x6E, 0x96, 0xB8, 0xC8, 0xCB, 0x38, 0x35, 0x12,
        0x20, 0x79, 0xA5, 0x08, 0x28, 0x35, 0x5C, 0xBC, 0x52, 0x16,
        0xAF, 0x52, 0xBA, 0x0F, 0xC3, 0xB1, 0x63, 0x12, 0x27, 0x0B,
        0x74, 0xA4, 0x47, 0x43, 0xD6, 0x30, 0xB8, 0x9C, 0x2E, 0x40,
        0x14, 0xCD, 0x99, 0x7F, 0xE8, 0x8E, 0x37, 0xB0, 0xA9, 0x3F,
        0x54, 0xE9, 0x66, 0x22, 0x61, 0x4C, 0xF8, 0x49, 0x03, 0x57,
        0x14, 0x32, 0x1D, 0x37, 0x3D, 0xE2, 0x92, 0xF8, 0x8E, 0xA0,
        0x6A, 0x66, 0x63, 0xF0, 0xB0, 0x6E, 0x07, 0x2B, 0x3D, 0xBF,
        0xD0, 0x84, 0x6A, 0xAA, 0x1F, 0x30, 0x77, 0x65, 0xE5, 0xFC,
        0xF5, 0xEC, 0x55, 0xCE, 0x73, 0xDB, 0xBE, 0xA7, 0x8D, 0x3A,
        0x9F, 0x7A, 0xED, 0x4F, 0xAF, 0xA2, 0x80, 0x4C, 0x30, 0x9E,
        0x28, 0x49, 0x65, 0x40, 0xF0, 0x03, 0x45, 0x56, 0x99, 0xA2,
        0x93, 0x1B, 0x9C, 0x46, 0xDE, 0xBD, 0xA8, 0xAB, 0x5F, 0x90,
        0x3F, 0xB7, 0x3F, 0xD4, 0x6F, 0x8D, 0x5A, 0x30, 0xE1, 0xD4,
        0x63, 0x3A, 0x6A, 0x7C, 0x8F, 0x24, 0xFC, 0xD9, 0x14, 0x28,
        0x09, 0xE4, 0x84, 0x4E, 0x17, 0x43, 0x56, 0xB8, 0xD4, 0x4B,
        0xA2, 0x29, 0x45, 0xD3, 0x13, 0xF0, 0xC2, 0x76, 0x9B, 0x01,
        0xA0, 0x80, 0x6E, 0x93, 0x63, 0x5E, 0x87, 0x24, 0x20, 0x2A,
        0xFF, 0xBB, 0x9F, 0xA8, 0x99, 0x6C, 0xA7, 0x9A, 0x00, 0xB9,
        0x7D, 0xDA, 0x66, 0xC9, 0xC0, 0x72, 0x72, 0x22, 0x0F, 0x1A,
        0xCC, 0x23, 0xD9, 0xB7, 0x5F, 0x1B
    };
    DSA *dsa = DSA_new();
    BIGNUM *p, *q, *g;

    if (dsa == NULL)
        return NULL;
    if (!DSA_set0_pqg(dsa, p = BN_bin2bn(dsap_2048, sizeof(dsap_2048), NULL),
                           q = BN_bin2bn(dsaq_2048, sizeof(dsaq_2048), NULL),
                           g = BN_bin2bn(dsag_2048, sizeof(dsag_2048), NULL))) {
        DSA_free(dsa);
        BN_free(p);
        BN_free(q);
        BN_free(g);
        return NULL;
    }
    return dsa;
}

static int genkeys(void)
{
    if (!TEST_ptr(dsakey = load_dsa_params()))
        return 0;

    if (!TEST_int_eq(DSA_generate_key(dsakey), 1))
        return 0;

    return 1;
}

static int sign_and_verify(int len)
{
    /*
     * Per FIPS 186-4, the hash is recommended to be the same length as q.
     * If the hash is longer than q, the leftmost N bits are used; if the hash
     * is shorter, then we left-pad (see appendix C.2.1).
     */
    size_t sigLength;
    int digestlen = BN_num_bytes(DSA_get0_q(dsakey));
    int ok = 0;

    unsigned char *dataToSign = OPENSSL_malloc(len);
    unsigned char *paddedData = OPENSSL_malloc(digestlen);
    unsigned char *signature = NULL;
    EVP_PKEY_CTX *ctx = NULL;
    EVP_PKEY *pkey = NULL;

    if (!TEST_ptr(dataToSign) ||
        !TEST_ptr(paddedData) ||
        !TEST_int_eq(RAND_bytes(dataToSign, len), 1))
        goto end;

    memset(paddedData, 0, digestlen);
    if (len > digestlen)
        memcpy(paddedData, dataToSign, digestlen);
    else
        memcpy(paddedData + digestlen - len, dataToSign, len);

    if (!TEST_ptr(pkey = EVP_PKEY_new()))
        goto end;
    EVP_PKEY_set1_DSA(pkey, dsakey);

    if (!TEST_ptr(ctx = EVP_PKEY_CTX_new(pkey, NULL)))
        goto end;
    if (!TEST_int_eq(EVP_PKEY_sign_init(ctx), 1))
        goto end;

    if (EVP_PKEY_sign(ctx, NULL, &sigLength, dataToSign, len) != 1) {
        TEST_error("Failed to get signature length, len=%d", len);
        goto end;
    }

    if (!TEST_ptr(signature = OPENSSL_malloc(sigLength)))
        goto end;

    if (EVP_PKEY_sign(ctx, signature, &sigLength, dataToSign, len) != 1) {
        TEST_error("Failed to sign, len=%d", len);
        goto end;
    }

    /* Check that the signature is okay via the EVP interface */
    if (!TEST_int_eq(EVP_PKEY_verify_init(ctx), 1))
        goto end;

    /* ... using the same data we just signed */
    if (EVP_PKEY_verify(ctx, signature, sigLength, dataToSign, len) != 1) {
        TEST_error("EVP verify with unpadded length %d failed\n", len);
        goto end;
    }

    /* ... padding/truncating the data to the appropriate digest size */
    if (EVP_PKEY_verify(ctx, signature, sigLength, paddedData, digestlen) != 1) {
        TEST_error("EVP verify with length %d failed\n", len);
        goto end;
    }

    /* Verify again using the raw DSA interface */
    if (DSA_verify(0, dataToSign, len, signature, sigLength, dsakey) != 1) {
        TEST_error("Verification with unpadded data failed, len=%d", len);
        goto end;
    }

    if (DSA_verify(0, paddedData, digestlen, signature, sigLength, dsakey) != 1) {
        TEST_error("verify with length %d failed\n", len);
        goto end;
    }

    ok = 1;
end:
    EVP_PKEY_CTX_free(ctx);
    EVP_PKEY_free(pkey);

    OPENSSL_free(signature);
    OPENSSL_free(paddedData);
    OPENSSL_free(dataToSign);

    return ok;
}

static int dsa_exact_size_test(void) {
    /*
     * For a 2048-bit p, q should be either 224 or 256 bits per the table in
     * FIPS 186-4 4.2.
     */

    return sign_and_verify(224 / 8) && sign_and_verify(256 / 8);
}

static int dsa_small_digest_test(void) {
    return sign_and_verify(16) && sign_and_verify(1);
}

static int dsa_large_digest_test(void) {
    return sign_and_verify(33) && sign_and_verify(64);
}

void cleanup_tests(void)
{
    DSA_free(dsakey);
}

#endif /* OPENSSL_NO_DSA */

int setup_tests(void)
{
#ifndef OPENSSL_NO_DSA
    if (!genkeys())
        return 0;

    ADD_TEST(dsa_exact_size_test);
    ADD_TEST(dsa_small_digest_test);
    ADD_TEST(dsa_large_digest_test);
#endif
    return 1;
}

