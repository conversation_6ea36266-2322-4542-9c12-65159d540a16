# TODO: use ../apps/libapps.a instead of direct ../apps/lib source.
# This can't currently be done, because some of its units drag in too many
# unresolved references that don't apply here.
# Most of all, ../apps/lib/apps.c needs to be divided in smaller pieces to
# be useful here.
#
# Auxiliary program source (copied from ../apps/build.info)
IF[{- $config{target} =~ /^(?:VC-|mingw|BC-)/ -}]
  # It's called 'init', but doesn't have much 'init' in it...
  $AUXLIBAPPSSRC=../apps/lib/win32_init.c
ENDIF
IF[{- $config{target} =~ /^vms-/ -}]
  $AUXLIBAPPSSRC=../apps/lib/vms_term_sock.c ../apps/lib/vms_decc_argv.c
ENDIF
# Program init source, that don't have direct linkage with the rest of the
# source, and can therefore not be part of a library.
IF[{- !$disabled{uplink} -}]
  $INITSRC=../ms/applink.c
ENDIF
$LIBAPPSSRC=../apps/lib/opt.c $AUXLIBAPPSSRC

IF[{- !$disabled{tests} -}]
  LIBS{noinst,has_main}=libtestutil.a
  SOURCE[libtestutil.a]=testutil/basic_output.c testutil/output.c \
          testutil/driver.c testutil/tests.c testutil/cb.c testutil/stanza.c \
          testutil/format_output.c testutil/load.c testutil/fake_random.c \
          testutil/test_cleanup.c testutil/main.c testutil/testutil_init.c \
          testutil/options.c testutil/test_options.c testutil/provider.c \
          testutil/apps_shims.c testutil/random.c $LIBAPPSSRC
  INCLUDE[libtestutil.a]=../include ../apps/include ..
  DEPEND[libtestutil.a]=../libcrypto

  PROGRAMS{noinst}= \
          confdump \
          versions \
          aborttest test_test pkcs12_format_test \
          sanitytest rsa_complex exdatatest bntest \
          ecstresstest gmdifftest pbelutest \
          destest mdc2test sha_test \
          exptest pbetest localetest evp_pkey_ctx_new_from_name\
          evp_pkey_provided_test evp_test evp_extra_test evp_extra_test2 \
          evp_fetch_prov_test evp_libctx_test ossl_store_test \
          v3nametest v3ext punycode_test \
          crltest danetest bad_dtls_test lhash_test sparse_array_test \
          conf_include_test params_api_test params_conversion_test \
          constant_time_test verify_extra_test clienthellotest \
          packettest asynctest secmemtest srptest memleaktest stack_test \
          dtlsv1listentest ct_test threadstest afalgtest d2i_test \
          ssl_test_ctx_test ssl_test x509aux cipherlist_test asynciotest \
          bio_callback_test bio_memleak_test bio_core_test param_build_test \
          bioprinttest sslapitest dtlstest sslcorrupttest \
          bio_enc_test pkey_meth_test pkey_meth_kdf_test evp_kdf_test uitest \
          cipherbytes_test threadstest_fips \
          asn1_encode_test asn1_decode_test asn1_string_table_test asn1_stable_parse_test \
          x509_time_test x509_dup_cert_test x509_check_cert_pkey_test \
          recordlentest drbgtest rand_status_test sslbuffertest \
          time_offset_test pemtest ssl_cert_table_internal_test ciphername_test \
          http_test servername_test ocspapitest fatalerrtest tls13ccstest \
          sysdefaulttest errtest ssl_ctx_test \
          context_internal_test aesgcmtest params_test evp_pkey_dparams_test \
          keymgmt_internal_test hexstr_test provider_status_test defltfips_test \
          bio_readbuffer_test user_property_test pkcs7_test upcallstest \
          provfetchtest prov_config_test rand_test fips_version_test \
          nodefltctxtest

  IF[{- !$disabled{'deprecated-3.0'} -}]
    PROGRAMS{noinst}=enginetest
  ENDIF

  SOURCE[confdump]=confdump.c
  INCLUDE[confdump]=../include ../apps/include
  DEPEND[confdump]=../libcrypto

  SOURCE[versions]=versions.c
  INCLUDE[versions]=../include ../apps/include
  DEPEND[versions]=../libcrypto

  SOURCE[aborttest]=aborttest.c
  INCLUDE[aborttest]=../include ../apps/include
  DEPEND[aborttest]=../libcrypto

  SOURCE[sanitytest]=sanitytest.c
  INCLUDE[sanitytest]=../include ../apps/include
  DEPEND[sanitytest]=../libcrypto libtestutil.a

  SOURCE[rand_test]=rand_test.c
  INCLUDE[rand_test]=../include ../apps/include
  DEPEND[rand_test]=../libcrypto libtestutil.a

  SOURCE[rsa_complex]=rsa_complex.c
  INCLUDE[rsa_complex]=../include ../apps/include

  SOURCE[test_test]=test_test.c
  INCLUDE[test_test]=../include ../apps/include
  DEPEND[test_test]=../libcrypto libtestutil.a

  SOURCE[exdatatest]=exdatatest.c
  INCLUDE[exdatatest]=../include ../apps/include
  DEPEND[exdatatest]=../libcrypto libtestutil.a

  SOURCE[bntest]=bntest.c
  INCLUDE[bntest]=../include ../apps/include
  DEPEND[bntest]=../libcrypto libtestutil.a

  SOURCE[ectest]=ectest.c
  INCLUDE[ectest]=../include ../apps/include
  DEPEND[ectest]=../libcrypto.a libtestutil.a

  SOURCE[ecstresstest]=ecstresstest.c
  INCLUDE[ecstresstest]=../include ../apps/include
  DEPEND[ecstresstest]=../libcrypto libtestutil.a

  SOURCE[gmdifftest]=gmdifftest.c
  INCLUDE[gmdifftest]=../include ../apps/include
  DEPEND[gmdifftest]=../libcrypto libtestutil.a

  SOURCE[pbelutest]=pbelutest.c
  INCLUDE[pbelutest]=../include ../apps/include
  DEPEND[pbelutest]=../libcrypto libtestutil.a

  SOURCE[mdc2test]=mdc2test.c
  INCLUDE[mdc2test]=../include ../apps/include
  DEPEND[mdc2test]=../libcrypto libtestutil.a

  SOURCE[sha_test]=sha_test.c
  INCLUDE[sha_test]=../include ../apps/include
  DEPEND[sha_test]=../libcrypto libtestutil.a

  SOURCE[enginetest]=enginetest.c
  INCLUDE[enginetest]=../include ../apps/include
  DEPEND[enginetest]=../libcrypto libtestutil.a

  SOURCE[exptest]=exptest.c
  INCLUDE[exptest]=../include ../apps/include
  DEPEND[exptest]=../libcrypto libtestutil.a

  SOURCE[localetest]=localetest.c
  INCLUDE[localetest]=../include ../apps/include
  DEPEND[localetest]=../libcrypto libtestutil.a

  SOURCE[evp_pkey_ctx_new_from_name]=evp_pkey_ctx_new_from_name.c
  INCLUDE[evp_pkey_ctx_new_from_name]=../include ../apps/include
  DEPEND[evp_pkey_ctx_new_from_name]=../libcrypto

  SOURCE[pbetest]=pbetest.c
  INCLUDE[pbetest]=../include ../apps/include
  DEPEND[pbetest]=../libcrypto libtestutil.a

  SOURCE[fatalerrtest]=fatalerrtest.c helpers/ssltestlib.c
  INCLUDE[fatalerrtest]=../include ../apps/include
  DEPEND[fatalerrtest]=../libcrypto ../libssl libtestutil.a

  SOURCE[tls13ccstest]=tls13ccstest.c helpers/ssltestlib.c
  INCLUDE[tls13ccstest]=../include ../apps/include
  DEPEND[tls13ccstest]=../libcrypto ../libssl libtestutil.a

  SOURCE[upcallstest]=upcallstest.c
  INCLUDE[upcallstest]=../include ../apps/include
  DEPEND[upcallstest]=../libcrypto libtestutil.a

  SOURCE[user_property_test]=user_property_test.c
  INCLUDE[user_property_test]=../include ../apps/include
  DEPEND[user_property_test]=../libcrypto libtestutil.a

  SOURCE[evp_test]=evp_test.c
  INCLUDE[evp_test]=../include ../apps/include
  DEPEND[evp_test]=../libcrypto libtestutil.a
  IF[{- $disabled{legacy} || !$target{dso_scheme} -}]
    DEFINE[evp_test]=NO_LEGACY_MODULE
  ENDIF

  SOURCE[evp_extra_test]=evp_extra_test.c
  INCLUDE[evp_extra_test]=../include ../apps/include
  DEPEND[evp_extra_test]=../libcrypto.a libtestutil.a
  IF[{- !$disabled{module} && !$disabled{legacy} -}]
    DEFINE[evp_extra_test]=STATIC_LEGACY
    SOURCE[evp_extra_test]=../providers/legacyprov.c
    INCLUDE[evp_extra_test]=../providers/common/include \
                            ../providers/implementations/include
    DEPEND[evp_extra_test]=../providers/liblegacy.a \
                           ../providers/libcommon.a
  ENDIF

  SOURCE[evp_extra_test2]=evp_extra_test2.c
  INCLUDE[evp_extra_test2]=../include ../apps/include
  DEPEND[evp_extra_test2]=../libcrypto libtestutil.a

  SOURCE[evp_libctx_test]=evp_libctx_test.c
  INCLUDE[evp_libctx_test]=../include ../apps/include
  DEPEND[evp_libctx_test]=../libcrypto.a libtestutil.a

  SOURCE[evp_fetch_prov_test]=evp_fetch_prov_test.c
  INCLUDE[evp_fetch_prov_test]=../include ../apps/include
  DEPEND[evp_fetch_prov_test]=../libcrypto libtestutil.a

  SOURCE[provfetchtest]=provfetchtest.c
  INCLUDE[provfetchtest]=../include ../apps/include
  DEPEND[provfetchtest]=../libcrypto.a libtestutil.a

  SOURCE[prov_config_test]=prov_config_test.c
  INCLUDE[prov_config_test]=../include ../apps/include
  DEPEND[prov_config_test]=../libcrypto.a libtestutil.a

  SOURCE[evp_pkey_provided_test]=evp_pkey_provided_test.c
  INCLUDE[evp_pkey_provided_test]=../include ../apps/include
  DEPEND[evp_pkey_provided_test]=../libcrypto.a libtestutil.a

  IF[{- !$disabled{'acvp-tests'} -}]
    PROGRAMS{noinst}=acvp_test

    SOURCE[acvp_test]=acvp_test.c
    INCLUDE[acvp_test]=../include ../apps/include
    DEPEND[acvp_test]=../libcrypto.a libtestutil.a
  ENDIF

  SOURCE[ossl_store_test]=ossl_store_test.c
  INCLUDE[ossl_store_test]=../include ../apps/include
  DEPEND[ossl_store_test]=../libcrypto.a libtestutil.a

  SOURCE[provider_status_test]=provider_status_test.c
  INCLUDE[provider_status_test]=../include ../apps/include
  DEPEND[provider_status_test]=../libcrypto.a libtestutil.a

  SOURCE[nodefltctxtest]=nodefltctxtest.c
  INCLUDE[nodefltctxtest]=../include ../apps/include
  DEPEND[nodefltctxtest]=../libcrypto.a libtestutil.a

  IF[{- !$disabled{'deprecated-3.0'} -}]
    PROGRAMS{noinst}=igetest bftest casttest

    SOURCE[igetest]=igetest.c
    INCLUDE[igetest]=../include ../apps/include
    DEPEND[igetest]=../libcrypto libtestutil.a

    SOURCE[bftest]=bftest.c
    INCLUDE[bftest]=../include ../apps/include
    DEPEND[bftest]=../libcrypto libtestutil.a

    SOURCE[casttest]=casttest.c
    INCLUDE[casttest]=../include ../apps/include
    DEPEND[casttest]=../libcrypto libtestutil.a
  ENDIF

  SOURCE[v3nametest]=v3nametest.c
  INCLUDE[v3nametest]=../include ../apps/include
  DEPEND[v3nametest]=../libcrypto libtestutil.a

  SOURCE[crltest]=crltest.c
  INCLUDE[crltest]=../include ../apps/include
  DEPEND[crltest]=../libcrypto libtestutil.a

  SOURCE[v3ext]=v3ext.c
  INCLUDE[v3ext]=../include ../apps/include
  DEPEND[v3ext]=../libcrypto libtestutil.a

  SOURCE[danetest]=danetest.c
  INCLUDE[danetest]=../include ../apps/include
  DEPEND[danetest]=../libcrypto ../libssl libtestutil.a

  SOURCE[constant_time_test]=constant_time_test.c
  INCLUDE[constant_time_test]=../include ../apps/include
  DEPEND[constant_time_test]=../libcrypto libtestutil.a

  SOURCE[verify_extra_test]=verify_extra_test.c
  INCLUDE[verify_extra_test]=../include ../apps/include
  DEPEND[verify_extra_test]=../libcrypto libtestutil.a

  SOURCE[clienthellotest]=clienthellotest.c
  INCLUDE[clienthellotest]=../include ../apps/include
  DEPEND[clienthellotest]=../libcrypto ../libssl libtestutil.a

  SOURCE[bad_dtls_test]=bad_dtls_test.c
  INCLUDE[bad_dtls_test]=../include ../apps/include
  DEPEND[bad_dtls_test]=../libcrypto ../libssl libtestutil.a

  SOURCE[packettest]=packettest.c
  INCLUDE[packettest]=../include ../apps/include
  DEPEND[packettest]=../libcrypto libtestutil.a

  SOURCE[asynctest]=asynctest.c
  INCLUDE[asynctest]=../include ../apps/include
  DEPEND[asynctest]=../libcrypto

  SOURCE[secmemtest]=secmemtest.c
  INCLUDE[secmemtest]=../include ../apps/include
  DEPEND[secmemtest]=../libcrypto libtestutil.a

  SOURCE[srptest]=srptest.c
  INCLUDE[srptest]=../include ../apps/include
  DEPEND[srptest]=../libcrypto libtestutil.a

  SOURCE[memleaktest]=memleaktest.c
  INCLUDE[memleaktest]=../include ../apps/include
  DEPEND[memleaktest]=../libcrypto libtestutil.a

  SOURCE[pkcs12_format_test]=pkcs12_format_test.c helpers/pkcs12.c
  INCLUDE[pkcs12_format_test]=../include ../apps/include
  DEPEND[pkcs12_format_test]=../libcrypto libtestutil.a

  SOURCE[pkcs7_test]=pkcs7_test.c
  INCLUDE[pkcs7_test]=../include ../apps/include
  DEPEND[pkcs7_test]=../libcrypto libtestutil.a

  SOURCE[punycode_test]=punycode_test.c
  INCLUDE[punycode_test]=../include ../apps/include
  DEPEND[punycode_test]=../libcrypto.a libtestutil.a

  SOURCE[stack_test]=stack_test.c
  INCLUDE[stack_test]=../include ../apps/include
  DEPEND[stack_test]=../libcrypto libtestutil.a

  SOURCE[lhash_test]=lhash_test.c
  INCLUDE[lhash_test]=../include ../apps/include
  DEPEND[lhash_test]=../libcrypto libtestutil.a

  SOURCE[dtlsv1listentest]=dtlsv1listentest.c
  INCLUDE[dtlsv1listentest]=../include ../apps/include
  DEPEND[dtlsv1listentest]=../libssl libtestutil.a

  SOURCE[ct_test]=ct_test.c
  INCLUDE[ct_test]=../include ../apps/include
  DEPEND[ct_test]=../libcrypto libtestutil.a

  SOURCE[threadstest]=threadstest.c
  INCLUDE[threadstest]=../include ../apps/include
  DEPEND[threadstest]=../libcrypto libtestutil.a

  SOURCE[threadstest_fips]=threadstest_fips.c
  INCLUDE[threadstest_fips]=../include ../apps/include
  DEPEND[threadstest_fips]=../libcrypto libtestutil.a

  SOURCE[afalgtest]=afalgtest.c
  INCLUDE[afalgtest]=../include ../apps/include
  DEPEND[afalgtest]=../libcrypto libtestutil.a

  SOURCE[d2i_test]=d2i_test.c
  INCLUDE[d2i_test]=../include ../apps/include
  DEPEND[d2i_test]=../libcrypto libtestutil.a

  SOURCE[ssl_test_ctx_test]=ssl_test_ctx_test.c helpers/ssl_test_ctx.c
  INCLUDE[ssl_test_ctx_test]=../include ../apps/include
  DEPEND[ssl_test_ctx_test]=../libcrypto ../libssl libtestutil.a

  SOURCE[ssl_test]=ssl_test.c helpers/ssl_test_ctx.c helpers/handshake.c
  IF[{- !$disabled{'srp'} -}]
    SOURCE[ssl_test]=helpers/handshake_srp.c
  ENDIF
  INCLUDE[ssl_test]=../include ../apps/include
  DEPEND[ssl_test]=../libcrypto ../libssl libtestutil.a

  SOURCE[cipherlist_test]=cipherlist_test.c
  INCLUDE[cipherlist_test]=../include ../apps/include
  DEPEND[cipherlist_test]=../libcrypto ../libssl libtestutil.a

  INCLUDE[helpers/ssl_test_ctx.o]=../include
  INCLUDE[helpers/handshake.o]=.. ../include
  INCLUDE[helpers/pkcs12.o]=.. ../include
  INCLUDE[helpers/ssltestlib.o]=.. ../include
  INCLUDE[helpers/cmp_testlib.o]=.. ../include ../apps/include

  SOURCE[x509aux]=x509aux.c
  INCLUDE[x509aux]=../include ../apps/include
  DEPEND[x509aux]=../libcrypto libtestutil.a

  SOURCE[asynciotest]=asynciotest.c helpers/ssltestlib.c
  INCLUDE[asynciotest]=../include ../apps/include
  DEPEND[asynciotest]=../libcrypto ../libssl libtestutil.a

  SOURCE[bio_callback_test]=bio_callback_test.c
  INCLUDE[bio_callback_test]=../include ../apps/include
  DEPEND[bio_callback_test]=../libcrypto libtestutil.a

  SOURCE[bio_readbuffer_test]=bio_readbuffer_test.c
  INCLUDE[bio_readbuffer_test]=../include ../apps/include
  DEPEND[bio_readbuffer_test]=../libcrypto libtestutil.a

  SOURCE[bio_memleak_test]=bio_memleak_test.c
  INCLUDE[bio_memleak_test]=../include ../apps/include
  DEPEND[bio_memleak_test]=../libcrypto libtestutil.a

  SOURCE[bioprinttest]=bioprinttest.c
  INCLUDE[bioprinttest]=../include ../apps/include
  DEPEND[bioprinttest]=../libcrypto libtestutil.a

  SOURCE[bio_core_test]=bio_core_test.c
  INCLUDE[bio_core_test]=../include ../apps/include
  DEPEND[bio_core_test]=../libcrypto libtestutil.a

  SOURCE[params_api_test]=params_api_test.c
  INCLUDE[params_api_test]=../include ../apps/include
  DEPEND[params_api_test]=../libcrypto libtestutil.a

  SOURCE[params_conversion_test]=params_conversion_test.c
  INCLUDE[params_conversion_test]=../include ../apps/include
  DEPEND[params_conversion_test]=../libcrypto libtestutil.a

  SOURCE[param_build_test]=param_build_test.c
  INCLUDE[param_build_test]=../include ../apps/include
  DEPEND[param_build_test]=../libcrypto.a libtestutil.a

  SOURCE[sslapitest]=sslapitest.c helpers/ssltestlib.c filterprov.c tls-provider.c
  INCLUDE[sslapitest]=../include ../apps/include ..
  DEPEND[sslapitest]=../libcrypto ../libssl libtestutil.a

  SOURCE[defltfips_test]=defltfips_test.c
  INCLUDE[defltfips_test]=../include  ../apps/include
  DEPEND[defltfips_test]=../libcrypto libtestutil.a

  SOURCE[fips_version_test]=fips_version_test.c
  INCLUDE[fips_version_test]=../include  ../apps/include
  DEPEND[fips_version_test]=../libcrypto libtestutil.a

  SOURCE[ocspapitest]=ocspapitest.c
  INCLUDE[ocspapitest]=../include ../apps/include
  DEPEND[ocspapitest]=../libcrypto libtestutil.a

  IF[{- !$disabled{sock} -}]
    PROGRAMS{noinst}=http_test
  ENDIF

  SOURCE[http_test]=http_test.c
  INCLUDE[http_test]=../include ../apps/include
  DEPEND[http_test]=../libcrypto libtestutil.a

  SOURCE[dtlstest]=dtlstest.c helpers/ssltestlib.c
  INCLUDE[dtlstest]=../include ../apps/include
  DEPEND[dtlstest]=../libcrypto ../libssl libtestutil.a

  SOURCE[sslcorrupttest]=sslcorrupttest.c helpers/ssltestlib.c
  INCLUDE[sslcorrupttest]=../include ../apps/include
  DEPEND[sslcorrupttest]=../libcrypto ../libssl libtestutil.a

  SOURCE[bio_enc_test]=bio_enc_test.c
  INCLUDE[bio_enc_test]=../include ../apps/include
  DEPEND[bio_enc_test]=../libcrypto libtestutil.a

  SOURCE[pkey_meth_test]=pkey_meth_test.c
  INCLUDE[pkey_meth_test]=../include ../apps/include
  DEPEND[pkey_meth_test]=../libcrypto libtestutil.a

  SOURCE[pkey_meth_kdf_test]=pkey_meth_kdf_test.c
  INCLUDE[pkey_meth_kdf_test]=../include ../apps/include
  DEPEND[pkey_meth_kdf_test]=../libcrypto libtestutil.a

  SOURCE[evp_kdf_test]=evp_kdf_test.c
  INCLUDE[evp_kdf_test]=../include ../apps/include
  DEPEND[evp_kdf_test]=../libcrypto libtestutil.a

  SOURCE[evp_pkey_dparams_test]=evp_pkey_dparams_test.c
  INCLUDE[evp_pkey_dparams_test]=../include ../apps/include
  DEPEND[evp_pkey_dparams_test]=../libcrypto libtestutil.a

  SOURCE[x509_time_test]=x509_time_test.c
  INCLUDE[x509_time_test]=../include ../apps/include
  DEPEND[x509_time_test]=../libcrypto libtestutil.a

  SOURCE[recordlentest]=recordlentest.c helpers/ssltestlib.c
  INCLUDE[recordlentest]=../include ../apps/include
  DEPEND[recordlentest]=../libcrypto ../libssl libtestutil.a

  SOURCE[drbgtest]=drbgtest.c
  INCLUDE[drbgtest]=../include ../apps/include ../providers/common/include
  DEPEND[drbgtest]=../libcrypto.a libtestutil.a

  SOURCE[rand_status_test]=rand_status_test.c
  INCLUDE[rand_status_test]=../include ../apps/include
  DEPEND[rand_status_test]=../libcrypto libtestutil.a

  SOURCE[x509_dup_cert_test]=x509_dup_cert_test.c
  INCLUDE[x509_dup_cert_test]=../include ../apps/include
  DEPEND[x509_dup_cert_test]=../libcrypto libtestutil.a

  SOURCE[x509_check_cert_pkey_test]=x509_check_cert_pkey_test.c
  INCLUDE[x509_check_cert_pkey_test]=../include ../apps/include
  DEPEND[x509_check_cert_pkey_test]=../libcrypto libtestutil.a

  SOURCE[pemtest]=pemtest.c
  INCLUDE[pemtest]=../include ../apps/include
  DEPEND[pemtest]=../libcrypto libtestutil.a

  SOURCE[ssl_cert_table_internal_test]=ssl_cert_table_internal_test.c
  INCLUDE[ssl_cert_table_internal_test]=.. ../include ../apps/include
  DEPEND[ssl_cert_table_internal_test]=../libcrypto libtestutil.a

  SOURCE[ciphername_test]=ciphername_test.c
  INCLUDE[ciphername_test]=../include ../apps/include
  DEPEND[ciphername_test]=../libcrypto ../libssl libtestutil.a

  SOURCE[http_test]=http_test.c
  INCLUDE[http_test]=../include ../apps/include
  DEPEND[http_test]=../libcrypto libtestutil.a

  SOURCE[servername_test]=servername_test.c helpers/ssltestlib.c
  INCLUDE[servername_test]=../include ../apps/include
  DEPEND[servername_test]=../libcrypto ../libssl libtestutil.a

  IF[{- !$disabled{cms} -}]
    PROGRAMS{noinst}=cmsapitest
    SOURCE[cmsapitest]=cmsapitest.c
    INCLUDE[cmsapitest]=../include ../apps/include
    DEPEND[cmsapitest]=../libcrypto libtestutil.a
  ENDIF

  IF[{- !$disabled{psk} -}]
    PROGRAMS{noinst}=dtls_mtu_test
    SOURCE[dtls_mtu_test]=dtls_mtu_test.c helpers/ssltestlib.c
    INCLUDE[dtls_mtu_test]=.. ../include ../apps/include
    DEPEND[dtls_mtu_test]=../libcrypto ../libssl libtestutil.a
  ENDIF

  IF[{- !$disabled{shared} -}]
    PROGRAMS{noinst}=shlibloadtest
    SOURCE[shlibloadtest]=shlibloadtest.c simpledynamic.c
    INCLUDE[shlibloadtest]=../include ../apps/include

    PROGRAMS{noinst}=moduleloadtest
    SOURCE[moduleloadtest]=moduleloadtest.c simpledynamic.c
    INCLUDE[moduleloadtest]=../include ../apps/include
  ENDIF

  # cipher_overhead_test uses internal symbols, so it must be linked with
  # the static libraries
  PROGRAMS{noinst}=cipher_overhead_test
  SOURCE[cipher_overhead_test]=cipher_overhead_test.c
  INCLUDE[cipher_overhead_test]=.. ../include ../apps/include
  DEPEND[cipher_overhead_test]=../libcrypto.a ../libssl.a libtestutil.a

  SOURCE[uitest]=uitest.c ../apps/lib/apps_ui.c
  INCLUDE[uitest]=.. ../include ../apps/include
  DEPEND[uitest]=../libcrypto ../libssl libtestutil.a

  SOURCE[cipherbytes_test]=cipherbytes_test.c
  INCLUDE[cipherbytes_test]=../include ../apps/include
  DEPEND[cipherbytes_test]=../libcrypto ../libssl libtestutil.a

  SOURCE[asn1_encode_test]=asn1_encode_test.c
  INCLUDE[asn1_encode_test]=../include ../apps/include
  DEPEND[asn1_encode_test]=../libcrypto libtestutil.a

  SOURCE[asn1_decode_test]=asn1_decode_test.c
  INCLUDE[asn1_decode_test]=../include ../apps/include
  DEPEND[asn1_decode_test]=../libcrypto libtestutil.a

  SOURCE[asn1_string_table_test]=asn1_string_table_test.c
  INCLUDE[asn1_string_table_test]=../include ../apps/include
  DEPEND[asn1_string_table_test]=../libcrypto libtestutil.a

  SOURCE[asn1_stable_parse_test]=asn1_stable_parse_test.c
  INCLUDE[asn1_stable_parse_test]=../include ../apps/include
  DEPEND[asn1_stable_parse_test]=../libcrypto libtestutil.a

  SOURCE[time_offset_test]=time_offset_test.c
  INCLUDE[time_offset_test]=../include ../apps/include
  DEPEND[time_offset_test]=../libcrypto libtestutil.a

  SOURCE[conf_include_test]=conf_include_test.c
  INCLUDE[conf_include_test]=../include ../apps/include
  DEPEND[conf_include_test]=../libcrypto libtestutil.a

  IF[{- !$disabled{cmp} -}]
    PROGRAMS{noinst}=cmp_asn_test cmp_ctx_test cmp_status_test cmp_hdr_test \
                     cmp_protect_test cmp_msg_test cmp_vfy_test \
                     cmp_server_test cmp_client_test
  ENDIF

  SOURCE[cmp_asn_test]=cmp_asn_test.c helpers/cmp_testlib.c
  INCLUDE[cmp_asn_test]=.. ../include ../apps/include
  DEPEND[cmp_asn_test]=../libcrypto.a libtestutil.a

  SOURCE[cmp_ctx_test]=cmp_ctx_test.c helpers/cmp_testlib.c
  INCLUDE[cmp_ctx_test]=.. ../include ../apps/include
  DEPEND[cmp_ctx_test]=../libcrypto.a libtestutil.a

  SOURCE[cmp_hdr_test]=cmp_hdr_test.c helpers/cmp_testlib.c
  INCLUDE[cmp_hdr_test]=.. ../include ../apps/include
  DEPEND[cmp_hdr_test]=../libcrypto.a libtestutil.a

  SOURCE[cmp_status_test]=cmp_status_test.c helpers/cmp_testlib.c
  INCLUDE[cmp_status_test]=.. ../include ../apps/include
  DEPEND[cmp_status_test]=../libcrypto.a libtestutil.a

  SOURCE[cmp_protect_test]=cmp_protect_test.c helpers/cmp_testlib.c
  INCLUDE[cmp_protect_test]=.. ../include ../apps/include
  DEPEND[cmp_protect_test]=../libcrypto.a libtestutil.a

  SOURCE[cmp_msg_test]=cmp_msg_test.c helpers/cmp_testlib.c
  INCLUDE[cmp_msg_test]=.. ../include ../apps/include
  DEPEND[cmp_msg_test]=../libcrypto.a libtestutil.a

  SOURCE[cmp_vfy_test]=cmp_vfy_test.c helpers/cmp_testlib.c
  INCLUDE[cmp_vfy_test]=.. ../include ../apps/include
  DEPEND[cmp_vfy_test]=../libcrypto.a libtestutil.a

  SOURCE[cmp_server_test]=cmp_server_test.c helpers/cmp_testlib.c
  INCLUDE[cmp_server_test]=.. ../include ../apps/include
  DEPEND[cmp_server_test]=../libcrypto.a libtestutil.a

  SOURCE[cmp_client_test]=cmp_client_test.c helpers/cmp_testlib.c ../apps/lib/cmp_mock_srv.c
  INCLUDE[cmp_client_test]=.. ../include ../apps/include
  DEPEND[cmp_client_test]=../libcrypto.a libtestutil.a

  # Internal test programs.  These are essentially a collection of internal
  # test routines.  Some of them need to reach internal symbols that aren't
  # available through the shared library (at least on Linux, Solaris, Windows
  # and VMS, where the exported symbols are those listed in util/*.num), these
  # programs are forcibly linked with the static libraries, where all symbols
  # are always available.
  IF[1]
    PROGRAMS{noinst}=asn1_internal_test modes_internal_test x509_internal_test \
                     tls13encryptiontest wpackettest ctype_internal_test \
                     rdrand_sanitytest property_test ideatest rsa_mp_test \
                     rsa_sp800_56b_test bn_internal_test ecdsatest rsa_test \
                     rc2test rc4test rc5test hmactest ffc_internal_test \
                     asn1_dsa_internal_test dsatest dsa_no_digest_size_test \
                     dhtest ssl_old_test

    IF[{- !$disabled{poly1305} -}]
      PROGRAMS{noinst}=poly1305_internal_test
    ENDIF
    IF[{- !$disabled{chacha} -}]
      PROGRAMS{noinst}=chacha_internal_test
    ENDIF
    IF[{- !$disabled{siphash} -}]
      PROGRAMS{noinst}=siphash_internal_test
    ENDIF
    IF[{- !$disabled{sm2} -}]
      PROGRAMS{noinst}=sm2_internal_test
    ENDIF
    IF[{- !$disabled{sm3} -}]
      PROGRAMS{noinst}=sm3_internal_test
    ENDIF
    IF[{- !$disabled{sm4} -}]
      PROGRAMS{noinst}=sm4_internal_test
    ENDIF
    IF[{- !$disabled{ec} -}]
      PROGRAMS{noinst}=ectest ec_internal_test curve448_internal_test
    ENDIF
    IF[{- !$disabled{cmac} -}]
      PROGRAMS{noinst}=cmactest
    ENDIF

    SOURCE[poly1305_internal_test]=poly1305_internal_test.c
    INCLUDE[poly1305_internal_test]=.. ../include ../apps/include
    DEPEND[poly1305_internal_test]=../libcrypto.a libtestutil.a

    SOURCE[chacha_internal_test]=chacha_internal_test.c
    INCLUDE[chacha_internal_test]=.. ../include ../apps/include
    DEPEND[chacha_internal_test]=../libcrypto.a libtestutil.a

    SOURCE[asn1_internal_test]=asn1_internal_test.c
    INCLUDE[asn1_internal_test]=.. ../include ../apps/include
    DEPEND[asn1_internal_test]=../libcrypto.a libtestutil.a

    SOURCE[modes_internal_test]=modes_internal_test.c
    INCLUDE[modes_internal_test]=.. ../include ../apps/include
    DEPEND[modes_internal_test]=../libcrypto.a libtestutil.a

    SOURCE[x509_internal_test]=x509_internal_test.c
    INCLUDE[x509_internal_test]=.. ../include ../apps/include
    DEPEND[x509_internal_test]=../libcrypto.a libtestutil.a

    SOURCE[rsa_test]=rsa_test.c
    INCLUDE[rsa_test]=../include ../apps/include
    DEPEND[rsa_test]=../libcrypto.a libtestutil.a

    SOURCE[rsa_mp_test]=rsa_mp_test.c
    INCLUDE[rsa_mp_test]=../include ../apps/include
    DEPEND[rsa_mp_test]=../libcrypto.a libtestutil.a

    SOURCE[ecdsatest]=ecdsatest.c
    INCLUDE[ecdsatest]=../include ../apps/include
    DEPEND[ecdsatest]=../libcrypto.a libtestutil.a

    SOURCE[dsatest]=dsatest.c
    INCLUDE[dsatest]=../include ../apps/include
    DEPEND[dsatest]=../libcrypto.a libtestutil.a

    SOURCE[dsa_no_digest_size_test]=dsa_no_digest_size_test.c
    INCLUDE[dsa_no_digest_size_test]=../include ../apps/include
    DEPEND[dsa_no_digest_size_test]=../libcrypto.a libtestutil.a

    SOURCE[tls13encryptiontest]=tls13encryptiontest.c
    INCLUDE[tls13encryptiontest]=.. ../include ../apps/include
    DEPEND[tls13encryptiontest]=../libcrypto.a ../libssl.a libtestutil.a

    SOURCE[ideatest]=ideatest.c
    INCLUDE[ideatest]=../include ../apps/include
    DEPEND[ideatest]=../libcrypto.a libtestutil.a

    SOURCE[wpackettest]=wpackettest.c
    INCLUDE[wpackettest]=../include ../apps/include
    DEPEND[wpackettest]=../libcrypto.a ../libssl.a libtestutil.a

    SOURCE[property_test]=property_test.c
    INCLUDE[property_test]=.. ../include ../apps/include
    DEPEND[property_test]=../libcrypto.a libtestutil.a

    SOURCE[ctype_internal_test]=ctype_internal_test.c
    INCLUDE[ctype_internal_test]=.. ../include ../apps/include
    DEPEND[ctype_internal_test]=../libcrypto.a libtestutil.a

    SOURCE[sparse_array_test]=sparse_array_test.c
    INCLUDE[sparse_array_test]=../include ../apps/include
    DEPEND[sparse_array_test]=../libcrypto.a libtestutil.a

    SOURCE[dhtest]=dhtest.c
    INCLUDE[dhtest]=../include ../apps/include
    DEPEND[dhtest]=../libcrypto.a libtestutil.a

    SOURCE[hmactest]=hmactest.c
    INCLUDE[hmactest]=../include ../apps/include
    DEPEND[hmactest]=../libcrypto.a libtestutil.a

    IF[{- !$disabled{cmac} -}]
      SOURCE[cmactest]=cmactest.c
      INCLUDE[cmactest]=../include ../apps/include
      DEPEND[cmactest]=../libcrypto.a libtestutil.a
    ENDIF

    SOURCE[siphash_internal_test]=siphash_internal_test.c
    INCLUDE[siphash_internal_test]=.. ../include ../apps/include
    DEPEND[siphash_internal_test]=../libcrypto.a libtestutil.a

    SOURCE[sm2_internal_test]=sm2_internal_test.c
    INCLUDE[sm2_internal_test]=../include ../apps/include
    DEPEND[sm2_internal_test]=../libcrypto.a libtestutil.a

    SOURCE[sm3_internal_test]=sm3_internal_test.c
    INCLUDE[sm3_internal_test]=../include ../apps/include
    DEPEND[sm3_internal_test]=../libcrypto.a libtestutil.a

    SOURCE[sm4_internal_test]=sm4_internal_test.c
    INCLUDE[sm4_internal_test]=.. ../include ../apps/include
    DEPEND[sm4_internal_test]=../libcrypto.a libtestutil.a

    SOURCE[destest]=destest.c
    INCLUDE[destest]=../include ../apps/include
    DEPEND[destest]=../libcrypto.a libtestutil.a

    SOURCE[rc2test]=rc2test.c
    INCLUDE[rc2test]=../include ../apps/include
    DEPEND[rc2test]=../libcrypto.a libtestutil.a

    SOURCE[rc4test]=rc4test.c
    INCLUDE[rc4test]=../include ../apps/include
    DEPEND[rc4test]=../libcrypto.a libtestutil.a

    SOURCE[rc5test]=rc5test.c
    INCLUDE[rc5test]=../include ../apps/include
    DEPEND[rc5test]=../libcrypto.a libtestutil.a

    SOURCE[ec_internal_test]=ec_internal_test.c $INITSRC
    INCLUDE[ec_internal_test]=../include ../crypto/ec ../apps/include
    DEPEND[ec_internal_test]=../libcrypto.a libtestutil.a

    SOURCE[curve448_internal_test]=curve448_internal_test.c
    INCLUDE[curve448_internal_test]=.. ../include ../apps/include ../crypto/ec/curve448
    DEPEND[curve448_internal_test]=../libcrypto.a libtestutil.a

    SOURCE[rc4test]=rc4test.c
    INCLUDE[rc4test]=../include ../apps/include
    DEPEND[rc4test]=../libcrypto.a libtestutil.a

    SOURCE[rdrand_sanitytest]=rdrand_sanitytest.c
    INCLUDE[rdrand_sanitytest]=../include ../apps/include
    DEPEND[rdrand_sanitytest]=../libcrypto.a libtestutil.a

    SOURCE[rsa_sp800_56b_test]=rsa_sp800_56b_test.c
    INCLUDE[rsa_sp800_56b_test]=.. ../include ../crypto/rsa ../apps/include
    DEPEND[rsa_sp800_56b_test]=../libcrypto.a libtestutil.a

    SOURCE[bn_internal_test]=bn_internal_test.c
    INCLUDE[bn_internal_test]=.. ../include ../crypto/bn ../apps/include
    DEPEND[bn_internal_test]=../libcrypto.a libtestutil.a

    SOURCE[asn1_dsa_internal_test]=asn1_dsa_internal_test.c
    INCLUDE[asn1_dsa_internal_test]=.. ../include ../apps/include
    DEPEND[asn1_dsa_internal_test]=../libcrypto.a libtestutil.a

    SOURCE[keymgmt_internal_test]=keymgmt_internal_test.c
    INCLUDE[keymgmt_internal_test]=.. ../include ../apps/include
    DEPEND[keymgmt_internal_test]=../libcrypto.a libtestutil.a

    SOURCE[ffc_internal_test]=ffc_internal_test.c
    INCLUDE[ffc_internal_test]=.. ../include ../apps/include
    DEPEND[ffc_internal_test]=../libcrypto.a libtestutil.a

    IF[{- !$disabled{mdc2} -}]
      PROGRAMS{noinst}=mdc2_internal_test
    ENDIF

    SOURCE[mdc2_internal_test]=mdc2_internal_test.c
    INCLUDE[mdc2_internal_test]=.. ../include ../apps/include
    DEPEND[mdc2_internal_test]=../libcrypto.a libtestutil.a

    SOURCE[ssl_old_test]=ssl_old_test.c helpers/predefined_dhparams.c
    INCLUDE[ssl_old_test]=.. ../include ../apps/include
    DEPEND[ssl_old_test]=../libcrypto.a ../libssl.a libtestutil.a

    PROGRAMS{noinst}=ext_internal_test
    SOURCE[ext_internal_test]=ext_internal_test.c
    INCLUDE[ext_internal_test]=.. ../include ../apps/include
    DEPEND[ext_internal_test]=../libcrypto.a ../libssl.a libtestutil.a

    PROGRAMS{noinst}=algorithmid_test
    SOURCE[algorithmid_test]=algorithmid_test.c
    INCLUDE[algorithmid_test]=../include ../apps/include
    DEPEND[algorithmid_test]=../libcrypto.a libtestutil.a
  ENDIF

  PROGRAMS{noinst}=asn1_time_test
  SOURCE[asn1_time_test]=asn1_time_test.c
  INCLUDE[asn1_time_test]=../include ../apps/include
  DEPEND[asn1_time_test]=../libcrypto libtestutil.a

  # We disable this test completely in a shared build because it deliberately
  # redefines some internal libssl symbols. This doesn't work in a non-shared
  # build
  IF[{- !$disabled{shared} -}]
    PROGRAMS{noinst}=tls13secretstest
    SOURCE[tls13secretstest]=tls13secretstest.c
    DEFINE[tls13secretstest]=OPENSSL_NO_KTLS
    SOURCE[tls13secretstest]= ../ssl/tls13_enc.c ../crypto/packet.c
    INCLUDE[tls13secretstest]=.. ../include ../apps/include
    DEPEND[tls13secretstest]=../libcrypto ../libssl libtestutil.a
  ENDIF

  SOURCE[sslbuffertest]=sslbuffertest.c helpers/ssltestlib.c
  INCLUDE[sslbuffertest]=../include ../apps/include
  DEPEND[sslbuffertest]=../libcrypto ../libssl libtestutil.a

  SOURCE[sysdefaulttest]=sysdefaulttest.c
  INCLUDE[sysdefaulttest]=../include ../apps/include
  DEPEND[sysdefaulttest]=../libcrypto ../libssl libtestutil.a

  SOURCE[errtest]=errtest.c
  INCLUDE[errtest]=../include ../apps/include
  DEPEND[errtest]=../libcrypto libtestutil.a

  SOURCE[aesgcmtest]=aesgcmtest.c
  INCLUDE[aesgcmtest]=../include ../apps/include ..
  DEPEND[aesgcmtest]=../libcrypto libtestutil.a

  PROGRAMS{noinst}=context_internal_test
  SOURCE[context_internal_test]=context_internal_test.c
  INCLUDE[context_internal_test]=.. ../include ../apps/include
  DEPEND[context_internal_test]=../libcrypto.a libtestutil.a

  PROGRAMS{noinst}=provider_internal_test
  DEFINE[provider_internal_test]=PROVIDER_INIT_FUNCTION_NAME=p_test_init
  SOURCE[provider_internal_test]=provider_internal_test.c p_test.c
  INCLUDE[provider_internal_test]=../include ../apps/include ..
  DEPEND[provider_internal_test]=../libcrypto.a libtestutil.a
  PROGRAMS{noinst}=provider_test
  DEFINE[provider_test]=PROVIDER_INIT_FUNCTION_NAME=p_test_init
  SOURCE[provider_test]=provider_test.c p_test.c
  INCLUDE[provider_test]=../include ../apps/include ..
  DEPEND[provider_test]=../libcrypto.a libtestutil.a
  IF[{- !$disabled{module} -}]
    MODULES{noinst}=p_test
    SOURCE[p_test]=p_test.c
    INCLUDE[p_test]=../include ..
    IF[{- defined $target{shared_defflag} -}]
      SOURCE[p_test]=p_test.ld
      GENERATE[p_test.ld]=../util/providers.num
    ENDIF
    MODULES{noinst}=p_minimal
    SOURCE[p_minimal]=p_minimal.c
    INCLUDE[p_minimal]=../include ..
    IF[{- defined $target{shared_defflag} -}]
      SOURCE[p_minimal]=p_minimal.ld
      GENERATE[p_minimal.ld]=../util/providers.num
    ENDIF
  ENDIF
  IF[{- $disabled{module} || !$target{dso_scheme} -}]
    DEFINE[provider_test]=NO_PROVIDER_MODULE
    DEFINE[provider_internal_test]=NO_PROVIDER_MODULE
  ENDIF
  DEPEND[]=provider_internal_test.cnf
  GENERATE[provider_internal_test.cnf]=provider_internal_test.cnf.in

  PROGRAMS{noinst}=provider_fallback_test
  SOURCE[provider_fallback_test]=provider_fallback_test.c
  INCLUDE[provider_fallback_test]=../include ../apps/include
  DEPEND[provider_fallback_test]=../libcrypto libtestutil.a

  PROGRAMS{noinst}=provider_pkey_test
  SOURCE[provider_pkey_test]=provider_pkey_test.c fake_rsaprov.c
  INCLUDE[provider_pkey_test]=../include ../apps/include
  DEPEND[provider_pkey_test]=../libcrypto libtestutil.a

  PROGRAMS{noinst}=params_test
  SOURCE[params_test]=params_test.c
  INCLUDE[params_test]=.. ../include ../apps/include
  DEPEND[params_test]=../libcrypto.a libtestutil.a

  PROGRAMS{noinst}=hexstr_test
  SOURCE[hexstr_test]=hexstr_test.c
  INCLUDE[hexstr_test]=.. ../include ../apps/include
  DEPEND[hexstr_test]=../libcrypto.a libtestutil.a

  PROGRAMS{noinst}=trace_api_test
  SOURCE[trace_api_test]=trace_api_test.c
  INCLUDE[trace_api_test]=.. ../include ../apps/include
  DEPEND[trace_api_test]=../libcrypto.a libtestutil.a

  PROGRAMS{noinst}=endecode_test
  SOURCE[endecode_test]=endecode_test.c helpers/predefined_dhparams.c
  INCLUDE[endecode_test]=.. ../include ../apps/include
  DEPEND[endecode_test]=../libcrypto.a libtestutil.a

  IF[{- !$disabled{'deprecated-3.0'} -}]
    PROGRAMS{noinst}=endecoder_legacy_test
    SOURCE[endecoder_legacy_test]=endecoder_legacy_test.c
    INCLUDE[endecoder_legacy_test]=.. ../include ../apps/include
    DEPEND[endecoder_legacy_test]=../libcrypto.a libtestutil.a
  ENDIF

  PROGRAMS{noinst}=namemap_internal_test
  SOURCE[namemap_internal_test]=namemap_internal_test.c
  INCLUDE[namemap_internal_test]=.. ../include ../apps/include
  DEPEND[namemap_internal_test]=../libcrypto.a libtestutil.a

  PROGRAMS{noinst}=bio_prefix_text
  SOURCE[bio_prefix_text]=bio_prefix_text.c
  INCLUDE[bio_prefix_text]=.. ../include ../apps/include
  DEPEND[bio_prefix_text]=../libcrypto libtestutil.a

  IF[{- !$disabled{'deprecated-3.0'} -}]
    PROGRAMS{noinst}=pem_read_depr_test
    SOURCE[pem_read_depr_test]=pem_read_depr_test.c
    INCLUDE[pem_read_depr_test]=../include ../apps/include
    DEPEND[pem_read_depr_test]=../libcrypto libtestutil.a
  ENDIF
ENDIF

  SOURCE[ssl_ctx_test]=ssl_ctx_test.c
  INCLUDE[ssl_ctx_test]=../include ../apps/include
  DEPEND[ssl_ctx_test]=../libcrypto ../libssl libtestutil.a

{-
   use File::Spec::Functions;
   use File::Basename;
   use OpenSSL::Glob;

   my @nogo_headers = ( "asn1_mac.h",
                        "opensslconf.h",
                        "__decc_include_prologue.h",
                        "__decc_include_epilogue.h" );
   my @nogo_headers_re = ( qr/.*err\.h/ );
   my @headerfiles = glob catfile($sourcedir,
                                  updir(), "include", "openssl", "*.h");

   foreach my $headerfile (@headerfiles) {
       my $name = basename($headerfile, ".h");
       next if $disabled{$name};
       next if grep { $_ eq lc("$name.h") } @nogo_headers;
       next if grep { lc("$name.h") =~ m/$_/i } @nogo_headers_re;
       $OUT .= <<"_____";

  PROGRAMS{noinst}=buildtest_c_$name
  SOURCE[buildtest_c_$name]=buildtest_$name.c
  GENERATE[buildtest_$name.c]=generate_buildtest.pl $name
  INCLUDE[buildtest_c_$name]=../include
  DEPEND[buildtest_c_$name]=../libssl ../libcrypto
_____
       $OUT .= <<"_____" if $config{CXX} && !$disabled{"buildtest-c++"};

  PROGRAMS{noinst}=buildtest_cc_$name
  SOURCE[buildtest_cc_$name]=buildtest_$name.cc
  GENERATE[buildtest_$name.cc]=generate_buildtest.pl $name
  INCLUDE[buildtest_cc_$name]=../include
  DEPEND[buildtest_cc_$name]=../libssl ../libcrypto
_____
   }
-}
