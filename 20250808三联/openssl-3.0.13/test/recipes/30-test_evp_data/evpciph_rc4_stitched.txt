Title = RC4-HMAC-MD5 test vectors

Availablein = legacy
Cipher = RC4-HMAC-MD5
Key = d48ecc0a163a06626bd1b7e172dfb5b3
MACKey = 5973581f63768353af37d3f51ec9f6ef
TLSAAD = 90a1b2c3e4f506172803010050
TLSVersion = 0x0301
Plaintext = 000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f202122232425262728292a2b2c2d2e2f303132333435363738393a3b3c3d3e3f000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f
Ciphertext = eea8eba927d9b16c640958f922b3ca43b197eea520674aa1d059156dfd4c12249e2890e8f3c72676e20fe4a30848c1cc6c12f4596d6e290b5f84745ac36959645ea4acabc84e748b2fd5e4228a2fe4f8c5792501fca9d8455160d626dc1a9716
# DECRYPT must be a separate entry due to change in TLSAAD value
Operation = ENCRYPT

Availablein = legacy
Cipher = RC4-HMAC-MD5
Key = d48ecc0a163a06626bd1b7e172dfb5b3
MACKey = 5973581f63768353af37d3f51ec9f6ef
TLSAAD = 90a1b2c3e4f506172803010060
TLSVersion = 0x0301
Plaintext = 000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f202122232425262728292a2b2c2d2e2f303132333435363738393a3b3c3d3e3f000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f
Ciphertext = eea8eba927d9b16c640958f922b3ca43b197eea520674aa1d059156dfd4c12249e2890e8f3c72676e20fe4a30848c1cc6c12f4596d6e290b5f84745ac36959645ea4acabc84e748b2fd5e4228a2fe4f8c5792501fca9d8455160d626dc1a9716
Operation = DECRYPT
