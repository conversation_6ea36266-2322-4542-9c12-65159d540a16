#
# Copyright 2001-2022 The OpenSSL Project Authors. All Rights Reserved.
#
# Licensed under the Apache License 2.0 (the "License").  You may not use
# this file except in compliance with the License.  You can obtain a copy
# in the file LICENSE in the source distribution or at
# https://www.openssl.org/source/license.html

# Tests start with one of these keywords
#       Cipher Decrypt Derive Digest Encoding KDF MAC PBE
#       PrivPubKeyPair Sign Verify VerifyRecover
# and continue until a blank line. Lines starting with a pound sign are ignored.

Title = Poly1305 Tests (from RFC 7539 and others)

MAC = Poly1305
Key = 0000000000000000000000000000000000000000000000000000000000000000
Input = 00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000
Output = 00000000000000000000000000000000
NoReinit = 1

MAC = Poly1305
Key = 0000000000000000000000000000000036e5f6b5c5e06070f0efca96227a863e
Input = 416e79207375626d697373696f6e20746f20746865204945544620696e74656e6465642062792074686520436f6e7472696275746f7220666f72207075626c69636174696f6e20617320616c6c206f722070617274206f6620616e204945544620496e7465726e65742d4472616674206f722052464320616e6420616e792073746174656d656e74206d6164652077697468696e2074686520636f6e74657874206f6620616e204945544620616374697669747920697320636f6e7369646572656420616e20224945544620436f6e747269627574696f6e222e20537563682073746174656d656e747320696e636c756465206f72616c2073746174656d656e747320696e20494554462073657373696f6e732c2061732077656c6c206173207772697474656e20616e6420656c656374726f6e696320636f6d6d756e69636174696f6e73206d61646520617420616e792074696d65206f7220706c6163652c207768696368206172652061646472657373656420746f
Output = 36e5f6b5c5e06070f0efca96227a863e
NoReinit = 1

MAC = Poly1305
Key = 36e5f6b5c5e06070f0efca96227a863e00000000000000000000000000000000
Input = 416e79207375626d697373696f6e20746f20746865204945544620696e74656e6465642062792074686520436f6e7472696275746f7220666f72207075626c69636174696f6e20617320616c6c206f722070617274206f6620616e204945544620496e7465726e65742d4472616674206f722052464320616e6420616e792073746174656d656e74206d6164652077697468696e2074686520636f6e74657874206f6620616e204945544620616374697669747920697320636f6e7369646572656420616e20224945544620436f6e747269627574696f6e222e20537563682073746174656d656e747320696e636c756465206f72616c2073746174656d656e747320696e20494554462073657373696f6e732c2061732077656c6c206173207772697474656e20616e6420656c656374726f6e696320636f6d6d756e69636174696f6e73206d61646520617420616e792074696d65206f7220706c6163652c207768696368206172652061646472657373656420746f
Output = f3477e7cd95417af89a6b8794c310cf0
NoReinit = 1

MAC = Poly1305
Key = 1c9240a5eb55d38af333888604f6b5f0473917c1402b80099dca5cbc207075c0
Input = 2754776173206272696c6c69672c20616e642074686520736c6974687920746f7665730a446964206779726520616e642067696d626c6520696e2074686520776162653a0a416c6c206d696d737920776572652074686520626f726f676f7665732c0a416e6420746865206d6f6d65207261746873206f757467726162652e
Output = 4541669a7eaaee61e708dc7cbcc5eb62
NoReinit = 1

# If one uses 130-bit partial reduction, does the code handle the case where partially reduced final result is not fully reduced?
MAC = Poly1305
Key = 0200000000000000000000000000000000000000000000000000000000000000
Input = FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF
Output = 03000000000000000000000000000000
NoReinit = 1

# What happens if addition of s overflows modulo 2^128?
MAC = Poly1305
Key = 02000000000000000000000000000000FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF
Input = 02000000000000000000000000000000
Output = 03000000000000000000000000000000
NoReinit = 1

# What happens if data limb is all ones and there is carry from lower limb?
MAC = Poly1305
Key = 0100000000000000000000000000000000000000000000000000000000000000
Input = FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF0FFFFFFFFFFFFFFFFFFFFFFFFFFFFFF11000000000000000000000000000000
Output = 05000000000000000000000000000000
NoReinit = 1

# What happens if final result from polynomial part is exactly 2^130-5?
MAC = Poly1305
Key = 0100000000000000000000000000000000000000000000000000000000000000
Input = FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFBFEFEFEFEFEFEFEFEFEFEFEFEFEFEFE01010101010101010101010101010101
Output = 00000000000000000000000000000000
NoReinit = 1

# What happens if final result from polynomial part is exactly 2^130-6?
MAC = Poly1305
Key = 0200000000000000000000000000000000000000000000000000000000000000
Input = FDFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF
Output = FAFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF
NoReinit = 1

# Taken from poly1305_internal_test.c
# More RFC7539

MAC = Poly1305
Input = 43727970746f6772617068696320466f72756d2052657365617263682047726f7570
Key = 85d6be7857556d337f4452fe42d506a80103808afb0db2fd4abff6af4149f51b
Output = a8061dc1305136c6c22b8baf0c0127a9
NoReinit = 1

# test vectors from "The Poly1305-AES message-authentication code"

MAC = Poly1305
Input = f3f6
Key = 851fc40c3467ac0be05cc20404f3f700580b3b0f9447bb1e69d095b5928b6dbc
Output = f4c633c3044fc145f84f335cb81953de
NoReinit = 1

# No input?
# # MAC = Poly1305
# Input =
# Key = a0f3080000f46400d0c7e9076c834403dd3fab2251f11ac759f0887129cc2ee7
# Output = dd3fab2251f11ac759f0887129cc2ee7
# NoReinit = 1

MAC = Poly1305
Input = 663cea190ffb83d89593f3f476b6bc24d7e679107ea26adb8caf6652d0656136
Key = 48443d0bb0d21109c89a100b5ce2c20883149c69b561dd88298a1798b10716ef
Output = 0ee1c16bb73f0f4fd19881753c01cdbe
NoReinit = 1

MAC = Poly1305
Input = ab0812724a7f1e342742cbed374d94d136c6b8795d45b3819830f2c04491faf0990c62e48b8018b2c3e4a0fa3134cb67fa83e158c994d961c4cb21095c1bf9
Key = 12976a08c4426d0ce8a82407c4f4820780f8c20aa71202d1e29179cbcb555a57
Output = 5154ad0d2cb26e01274fc51148491f1b
NoReinit = 1

# self-generated vectors exercise "significant" length such that* are handled by different code paths

MAC = Poly1305
Input = ab0812724a7f1e342742cbed374d94d136c6b8795d45b3819830f2c04491faf0990c62e48b8018b2c3e4a0fa3134cb67fa83e158c994d961c4cb21095c1bf9af
Key = 12976a08c4426d0ce8a82407c4f4820780f8c20aa71202d1e29179cbcb555a57
Output = 812059a5da198637cac7c4a631bee466
NoReinit = 1

MAC = Poly1305
Input = ab0812724a7f1e342742cbed374d94d136c6b8795d45b3819830f2c04491faf0990c62e48b8018b2c3e4a0fa3134cb67
Key = 12976a08c4426d0ce8a82407c4f4820780f8c20aa71202d1e29179cbcb555a57
Output = 5b88d7f6228b11e2e28579a5c0c1f761
NoReinit = 1

MAC = Poly1305
Input = ab0812724a7f1e342742cbed374d94d136c6b8795d45b3819830f2c04491faf0990c62e48b8018b2c3e4a0fa3134cb67fa83e158c994d961c4cb21095c1bf9af663cea190ffb83d89593f3f476b6bc24d7e679107ea26adb8caf6652d0656136
Key = 12976a08c4426d0ce8a82407c4f4820780f8c20aa71202d1e29179cbcb555a57
Output = bbb613b2b6d753ba07395b916aaece15
NoReinit = 1

MAC = Poly1305
Input = ab0812724a7f1e342742cbed374d94d136c6b8795d45b3819830f2c04491faf0990c62e48b8018b2c3e4a0fa3134cb67fa83e158c994d961c4cb21095c1bf9af48443d0bb0d21109c89a100b5ce2c20883149c69b561dd88298a1798b10716ef663cea190ffb83d89593f3f476b6bc24
Key = 12976a08c4426d0ce8a82407c4f4820780f8c20aa71202d1e29179cbcb555a57
Output = c794d7057d1778c4bbee0a39b3d97342
NoReinit = 1

MAC = Poly1305
Input = ab0812724a7f1e342742cbed374d94d136c6b8795d45b3819830f2c04491faf0990c62e48b8018b2c3e4a0fa3134cb67fa83e158c994d961c4cb21095c1bf9af48443d0bb0d21109c89a100b5ce2c20883149c69b561dd88298a1798b10716ef663cea190ffb83d89593f3f476b6bc24d7e679107ea26adb8caf6652d0656136
Key = 12976a08c4426d0ce8a82407c4f4820780f8c20aa71202d1e29179cbcb555a57
Output = ffbcb9b371423152d7fca5ad042fbaa9
NoReinit = 1

MAC = Poly1305
Input = ab0812724a7f1e342742cbed374d94d136c6b8795d45b3819830f2c04491faf0990c62e48b8018b2c3e4a0fa3134cb67fa83e158c994d961c4cb21095c1bf9af48443d0bb0d21109c89a100b5ce2c20883149c69b561dd88298a1798b10716ef663cea190ffb83d89593f3f476b6bc24d7e679107ea26adb8caf6652d0656136812059a5da198637cac7c4a631bee466
Key = 12976a08c4426d0ce8a82407c4f4820780f8c20aa71202d1e29179cbcb555a57
Output = 069ed6b8ef0f207b3e243bb1019fe632
NoReinit = 1

MAC = Poly1305
Input = ab0812724a7f1e342742cbed374d94d136c6b8795d45b3819830f2c04491faf0990c62e48b8018b2c3e4a0fa3134cb67fa83e158c994d961c4cb21095c1bf9af48443d0bb0d21109c89a100b5ce2c20883149c69b561dd88298a1798b10716ef663cea190ffb83d89593f3f476b6bc24d7e679107ea26adb8caf6652d0656136812059a5da198637cac7c4a631bee4665b88d7f6228b11e2e28579a5c0c1f761
Key = 12976a08c4426d0ce8a82407c4f4820780f8c20aa71202d1e29179cbcb555a57
Output = cca339d9a45fa2368c2c68b3a4179133
NoReinit = 1

MAC = Poly1305
Input = ab0812724a7f1e342742cbed374d94d136c6b8795d45b3819830f2c04491faf0990c62e48b8018b2c3e4a0fa3134cb67fa83e158c994d961c4cb21095c1bf9af48443d0bb0d21109c89a100b5ce2c20883149c69b561dd88298a1798b10716ef663cea190ffb83d89593f3f476b6bc24d7e679107ea26adb8caf6652d0656136812059a5da198637cac7c4a631bee4665b88d7f6228b11e2e28579a5c0c1f761ab0812724a7f1e342742cbed374d94d136c6b8795d45b3819830f2c04491faf0990c62e48b8018b2c3e4a0fa3134cb67fa83e158c994d961c4cb21095c1bf9af48443d0bb0d21109c89a100b5ce2c20883149c69b561dd88298a1798b10716ef663cea190ffb83d89593f3f476b6bc24d7e679107ea26adb8caf6652d0656136
Key = 12976a08c4426d0ce8a82407c4f4820780f8c20aa71202d1e29179cbcb555a57
Output = 53f6e828a2f0fe0ee815bf0bd5841a34
NoReinit = 1

MAC = Poly1305
Input = ab0812724a7f1e342742cbed374d94d136c6b8795d45b3819830f2c04491faf0990c62e48b8018b2c3e4a0fa3134cb67fa83e158c994d961c4cb21095c1bf9af48443d0bb0d21109c89a100b5ce2c20883149c69b561dd88298a1798b10716ef663cea190ffb83d89593f3f476b6bc24d7e679107ea26adb8caf6652d0656136812059a5da198637cac7c4a631bee4665b88d7f6228b11e2e28579a5c0c1f761ab0812724a7f1e342742cbed374d94d136c6b8795d45b3819830f2c04491faf0990c62e48b8018b2c3e4a0fa3134cb67fa83e158c994d961c4cb21095c1bf9af48443d0bb0d21109c89a100b5ce2c20883149c69b561dd88298a1798b10716ef663cea190ffb83d89593f3f476b6bc24d7e679107ea26adb8caf6652d0656136812059a5da198637cac7c4a631bee4665b88d7f6228b11e2e28579a5c0c1f761
Key = 12976a08c4426d0ce8a82407c4f4820780f8c20aa71202d1e29179cbcb555a57
Output = b846d44e9bbd53cedffbfbb6b7fa4933
NoReinit = 1

# 4th power of the key spills to 131th bit in SIMD key setup

MAC = Poly1305
Input = ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff
Key = ad628107e8351d0f2c231a05dc4a410600000000000000000000000000000000
Output = 07145a4c02fe5fa32036de68fabe9066
NoReinit = 1

# poly1305_ieee754.c failed this in final stage

MAC = Poly1305
Input = 842364e156336c0998b933a6237726180d9e3fdcbde4cd5d17080fc3beb49614d7122c037463ff104d73f19c12704628d417c4c54a3fe30d3c3d7714382d43b0382a50a5dee54be844b076e8df88201a1cd43b90eb21643fa96f39b518aa8340c942ff3c31baf7c9bdbf0f31ae3fa096bf8c63030609829fe72e179824890bc8e08c315c1cce2a83144dbbff09f74e3efc770b54d0984a8f19b14719e63635641d6b1eedf63efbf080e1783d32445412114c20de0b837a0dfa33d6b82825fff44c9a70ea54ce47f07df698e6b03323b53079364a5fc3e9dd034392bdde86dccdda94321c5e44060489336cb65bf3989c36f7282c2f5d2b882c171e74
Key = 95d5c005503e510d8cd0aa072c4a4d066eabc52d11653df47fbf63ab198bcc26
Output = f248312e578d9d58f8b7bb4d19105431
NoReinit = 1

# AVX2 in poly1305-x86.pl failed this with 176+32 split

MAC = Poly1305
Input = 248ac31085b6c2adaaa38259a0d7192c5c35d1bb4ef39ad94c38d1c82479e2dd2159a077024b0589bc8a20101b506f0a1ad0bbab76e83a83f1b94be6beae74e874cab692c5963a75436b776121ec9f62399a3e66b2d22707dae81933b6277f3c8516bcbe26dbbd86f373103d7cf4cad1888c952118fbfbd0d7b4bedc4ae4936aff91157e7aa47c54442ea78d6ac251d324a0fbe49d89cc3521b66d16e9c66a3709894e4eb0a4eedc4ae19468e66b81f271351b1d921ea551047abcc6b87a901fde7db79fa1818c11336dbc07244a40eb
Key = 000102030405060708090a0b0c0d0e0f00000000000000000000000000000000
Output = bc939bc5281480fa99c6d68c258ec42f
NoReinit = 1

# test vectors from Google

# No input?
# # MAC = Poly1305
# Input =
# Key = c8afaac331ee372cd6082de134943b174710130e9f6fea8d72293850a667d86c
# Output = 4710130e9f6fea8d72293850a667d86c
# NoReinit = 1

MAC = Poly1305
Input = 48656c6c6f20776f726c6421
Key = 746869732069732033322d62797465206b657920666f7220506f6c7931333035
Output = a6f745008f81c916a20dcc74eef2b2f0
NoReinit = 1

MAC = Poly1305
Input = 0000000000000000000000000000000000000000000000000000000000000000
Key = 746869732069732033322d62797465206b657920666f7220506f6c7931333035
Output = 49ec78090e481ec6c26b33b91ccc0307
NoReinit = 1

MAC = Poly1305
Input = 89dab80b7717c1db5db437860a3f70218e93e1b8f461fb677f16f35f6f87e2a91c99bc3a47ace47640cc95c345be5ecca5a3523c35cc01893af0b64a620334270372ec12482d1b1e363561698a578b359803495bb4e2ef1930b17a5190b580f141300df30adbeca28f6427a8bc1a999fd51c554a017d095d8c3e3127daf9f595
Key = 2d773be37adb1e4d683bf0075e79c4ee037918535a7f99ccb7040fb5f5f43aea
Output = c85d15ed44c378d6b00e23064c7bcd51
NoReinit = 1

MAC = Poly1305
Input = 000000000000000b170303020000000006db1f1f368d696a810a349c0c714c9a5e7850c2407d721acded95e018d7a85266a6e1289cdb4aeb18da5ac8a2b0026d24a59ad485227f3eaedbb2e7e35e1c66cd60f9abf716dcc9ac42682dd7dab287a7024c4eefc321cc0574e16793e37cec03c5bda42b54c114a80b57af26416c7be742005e20855c73e21dc8e2edc9d435cb6f6059280011c270b71570051c1c9b3052126620bc1e2730fa066c7a509d53c60e5ae1b40aa6e39e49669228c90eecb4a50db32a50bc49e90b4f4b359a1dfd11749cd3867fcf2fb7bb6cd4738f6a4ad6f7ca5058f7618845af9f020f6c3b967b8f4cd4a91e2813b507ae66f2d35c18284f7292186062e10fd5510d18775351ef334e7634ab4743f5b68f49adcab384d3fd75f7390f4006ef2a295c8c7a076ad54546cd25d2107fbe1436c840924aaebe5b370893cd63d1325b8616fc4810886bc152c53221b6df373119393255ee72bcaa880174f1717f9184fa91646f17a24ac55d16bfddca9581a92eda479201f0edbf633600d6066d1ab36d5d2415d71351bbcd608a25108d25641992c1f26c531cf9f90203bc4cc19f5927d834b0a47116d3884bbb164b8ec883d1ac832e56b3918a98601a08d171881541d594db399c6ae6151221745aec814c45b0b05b565436fd6f137aa10a0c0b643761dbd6f9a9dcb99b1a6e690854ce0769cde39761d82fcdec15f0d92d7d8e94ade8eb83fbe0
Key = 99e5822dd4173c995e3dae0ddefb97743fde3b080134b39f76e9bf8d0e88d546
Output = 2637408fe13086ea73f971e3425e2820
NoReinit = 1

# test vectors from Hanno Bock

MAC = Poly1305
Input = cccccccccccccccccccccccccccccccccccccccccccccccccc80ccccccccccccccccccccccccccccccccccccccccccccccccccccccccccccccccccccceccccccccccccccccccccccccccccccccccccc5cccccccccccccccccccccccccccccccccccccccccce3ccccccccccccccccccccccccccccccccccccccccccccccccccccccccccccaccccccccccccccccccccce6cccccccccc000000afccccccccccccccccccfffffff5000000000000000000000000000000000000000000000000000000ffffffe70000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000719205a8521dfc
Key = 7f1b02640000000000000000000000000000000000000000cccccccccccccccc
Output = 8559b876eceed66eb37798c0457baff9
NoReinit = 1

MAC = Poly1305
Input = aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa000000000000000000800264
Key = e00016000000000000000000000000000000aaaaaaaaaaaaaaaaaaaaaaaaaaaa
Output = 00bd1258978e205444c9aaaa82006fed
NoReinit = 1

MAC = Poly1305
Input = 02fc
Key = 0c0c0c0c0c0c0c0c0c0c0c0c0c0c0c0c0c0c0c0c0c0c0c0c0c0c0c0c0c0c0c0c
Output = 06120c0c0c0c0c0c0c0c0c0c0c0c0c0c
NoReinit = 1

MAC = Poly1305
Input = 7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7a7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b5c7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b6e7b007b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7a7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b5c7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b7b6e7b001300000000b300000000000000000000000000000000000000000000f20000000000000000000000000000000000002000efff0009000000000000000000000000100000000009000000640000000000000000000000001300000000b300000000000000000000000000000000000000000000f20000000000000000000000000000000000002000efff00090000000000000000007a000010000000000900000064000000000000000000000000000000000000000000000000fc
Key = 00ff000000000000000000000000000000000000001e00000000000000007b7b
Output = 33205bbf9e9f8f7212ab9e2ab9b7e4a5
NoReinit = 1

MAC = Poly1305
Input = 77777777777777777777777777777777777777777777777777777777777777777777777777777777777777777777777777777777777777777777777777777777777777777777777777777777ffffffe9e9acacacacacacacacacacac0000acacec0100acacac2caca2acacacacacacacacacacac64f2
Key = 0000007f0000007f01000020000000000000cf77777777777777777777777777
Output = 02ee7c8c546ddeb1a467e4c3981158b9
NoReinit = 1

# test vectors from Andrew Moon - nacl

MAC = Poly1305
Input = 8e993b9f48681273c29650ba32fc76ce48332ea7164d96a4476fb8c531a1186ac0dfc17c98dce87b4da7f011ec48c97271d2c20f9b928fe2270d6fb863d51738b48eeee314a7cc8ab932164548e526ae90224368517acfeabd6bb3732bc0e9da99832b61ca01b6de56244a9e88d5f9b37973f622a43d14a6599b1f654cb45a74e355a5
Key = eea6a7251c1e72916d11c2cb214d3c252539121d8e234e652d651fa4c8cff880
Output = f3ffc7703f9400e52a7dfb4b3d3305d9
NoReinit = 1

# wrap 2^130-5
MAC = Poly1305
Input = ffffffffffffffffffffffffffffffff
Key = 0200000000000000000000000000000000000000000000000000000000000000
Output = 03000000000000000000000000000000
NoReinit = 1

# wrap 2^128
MAC = Poly1305
Input = 02000000000000000000000000000000
Key = 02000000000000000000000000000000ffffffffffffffffffffffffffffffff
Output = 03000000000000000000000000000000
NoReinit = 1

# limb carry
MAC = Poly1305
Input = fffffffffffffffffffffffffffffffff0ffffffffffffffffffffffffffffff11000000000000000000000000000000
Key = 0100000000000000000000000000000000000000000000000000000000000000
Output = 05000000000000000000000000000000
NoReinit = 1

# 2^130-5
MAC = Poly1305
Input = fffffffffffffffffffffffffffffffffbfefefefefefefefefefefefefefefe01010101010101010101010101010101
Key = 0100000000000000000000000000000000000000000000000000000000000000
Output = 00000000000000000000000000000000
NoReinit = 1

# 2^130-6
MAC = Poly1305
Input = fdffffffffffffffffffffffffffffff
Key = 0200000000000000000000000000000000000000000000000000000000000000
Output = faffffffffffffffffffffffffffffff
NoReinit = 1

# 5*H+L reduction intermediate
MAC = Poly1305
Input = e33594d7505e43b900000000000000003394d7505e4379cd01000000000000000000000000000000000000000000000001000000000000000000000000000000
Key = 0100000000000000040000000000000000000000000000000000000000000000
Output = 14000000000000005500000000000000
NoReinit = 1

# 5*H+L reduction final
MAC = Poly1305
Input = e33594d7505e43b900000000000000003394d7505e4379cd010000000000000000000000000000000000000000000000
Key = 0100000000000000040000000000000000000000000000000000000000000000
Output = 13000000000000000000000000000000
NoReinit = 1

# Here are 4 duplicated cases for Poly1305 by EVP_PKEY
MAC = Poly1305 by EVP_PKEY
Key = 0000000000000000000000000000000000000000000000000000000000000000
Input = 00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000
Output = 00000000000000000000000000000000

MAC = Poly1305 by EVP_PKEY
Key = 0000000000000000000000000000000036e5f6b5c5e06070f0efca96227a863e
Input = 416e79207375626d697373696f6e20746f20746865204945544620696e74656e6465642062792074686520436f6e7472696275746f7220666f72207075626c69636174696f6e20617320616c6c206f722070617274206f6620616e204945544620496e7465726e65742d4472616674206f722052464320616e6420616e792073746174656d656e74206d6164652077697468696e2074686520636f6e74657874206f6620616e204945544620616374697669747920697320636f6e7369646572656420616e20224945544620436f6e747269627574696f6e222e20537563682073746174656d656e747320696e636c756465206f72616c2073746174656d656e747320696e20494554462073657373696f6e732c2061732077656c6c206173207772697474656e20616e6420656c656374726f6e696320636f6d6d756e69636174696f6e73206d61646520617420616e792074696d65206f7220706c6163652c207768696368206172652061646472657373656420746f
Output = 36e5f6b5c5e06070f0efca96227a863e

MAC = Poly1305 by EVP_PKEY
Key = 36e5f6b5c5e06070f0efca96227a863e00000000000000000000000000000000
Input = 416e79207375626d697373696f6e20746f20746865204945544620696e74656e6465642062792074686520436f6e7472696275746f7220666f72207075626c69636174696f6e20617320616c6c206f722070617274206f6620616e204945544620496e7465726e65742d4472616674206f722052464320616e6420616e792073746174656d656e74206d6164652077697468696e2074686520636f6e74657874206f6620616e204945544620616374697669747920697320636f6e7369646572656420616e20224945544620436f6e747269627574696f6e222e20537563682073746174656d656e747320696e636c756465206f72616c2073746174656d656e747320696e20494554462073657373696f6e732c2061732077656c6c206173207772697474656e20616e6420656c656374726f6e696320636f6d6d756e69636174696f6e73206d61646520617420616e792074696d65206f7220706c6163652c207768696368206172652061646472657373656420746f
Output = f3477e7cd95417af89a6b8794c310cf0

MAC = Poly1305 by EVP_PKEY
Key = 1c9240a5eb55d38af333888604f6b5f0473917c1402b80099dca5cbc207075c0
Input = 2754776173206272696c6c69672c20616e642074686520736c6974687920746f7665730a446964206779726520616e642067696d626c6520696e2074686520776162653a0a416c6c206d696d737920776572652074686520626f726f676f7665732c0a416e6420746865206d6f6d65207261746873206f757467726162652e
Output = 4541669a7eaaee61e708dc7cbcc5eb62
