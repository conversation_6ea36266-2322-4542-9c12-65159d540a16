#
# Copyright 2001-2021 The OpenSSL Project Authors. All Rights Reserved.
#
# Licensed under the Apache License 2.0 (the "License").  You may not use
# this file except in compliance with the License.  You can obtain a copy
# in the file LICENSE in the source distribution or at
# https://www.openssl.org/source/license.html

# Tests start with one of these keywords
#       Cipher Decrypt Derive Digest Encoding KDF MAC PBE
#       PrivPubKeyPair Sign Verify VerifyRecover
# and continue until a blank line. Lines starting with a pound sign are ignored.
# The keyword Availablein must appear before the test name if needed.

Title = CMAC tests (from FIPS module)

MAC = CMAC
Algorithm = DES-EDE3-CBC
Key = 89BCD952A8C8AB371AF48AC7D07085D5EFF702E6D62CDC23
Input = FA620C1BBE97319E9A0CF0492121F7A20EB08A6A709DCBD00AAF38E4F99E754E
Output = 8F49A1B7D6AA2258

MAC = CMAC by EVP_PKEY
Algorithm = DES-EDE3-CBC
Key = 89BCD952A8C8AB371AF48AC7D07085D5EFF702E6D62CDC23
Input = FA620C1BBE97319E9A0CF0492121F7A20EB08A6A709DCBD00AAF38E4F99E754E
Output = 8F49A1B7D6AA2258

