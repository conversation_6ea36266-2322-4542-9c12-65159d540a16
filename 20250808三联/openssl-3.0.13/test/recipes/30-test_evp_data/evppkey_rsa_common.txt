#
# Copyright 2001-2021 The OpenSSL Project Authors. All Rights Reserved.
#
# Licensed under the Apache License 2.0 (the "License").  You may not use
# this file except in compliance with the License.  You can obtain a copy
# in the file LICENSE in the source distribution or at
# https://www.openssl.org/source/license.html

# Tests start with one of these keywords
#       Cipher Decrypt Derive Digest Encoding KDF MAC PBE
#       PrivPubKeyPair Sign Verify VerifyRecover
# and continue until a blank line. Lines starting with a pound sign are ignored.
# The keyword Availablein must appear before the test name if needed.

# Private keys used for PKEY operations.

# RSA 2048 bit key.

PrivateKey = RSA-2048
-----B<PERSON>IN PRIVATE KEY-----
MIIEvAIBADANBgkqhkiG9w0BAQEFAASCBKYwggSiAgEAAoIBAQDNAIHqeyrh6gbV
n3xz2f+5SglhXC5Lp8Y2zvCN01M+wxhVJbAVx2m5mnfWclv5w1Mqm25fZifV+4UW
B2jT3anL01l0URcX3D0wnS/EfuQfl+Mq23+d2GShxHZ6Zm7NcbwarPXnUX9LOFlP
6psF5C1a2pkSAIAT5FMWpNm7jtCGuI0odYusr5ItRqhotIXSOcm66w4rZFknEPQr
LR6gpLSALAvsqzKPimiwBzvbVG/uqYCdKEmRKzkMFTK8finHZY+BdfrkbzQzL/h7
yrPkBkm5hXeGnaDqcYNT8HInVIhpE2SHYNEivmduD8SD3SD/wxvalqMZZsmqLnWt
A95H4cRPAgMBAAECggEAYCl6x5kbFnoG1rJHWLjL4gi+ubLZ7Jc4vYD5Ci41AF3X
ziktnim6iFvTFv7x8gkTvArJDWsICLJBTYIQREHYYkozzgIzyPeApIs3Wv8C12cS
IopwJITbP56+zM+77hcJ26GCgA2Unp5CFuC/81WDiPi9kNo3Oh2CdD7D+90UJ/0W
glplejFpEuhpU2URfKL4RckJQF/KxV+JX8FdIDhsJu54yemQdQKaF4psHkzwwgDo
qc+yfp0Vb4bmwq3CKxqEoc1cpbJ5CHXXlAfISzUjlcuBzD/tW7BDtp7eDAcgRVAC
XO6MX0QBcLYSC7SOD3R7zY9SIRCFDfBDxCjf0YcFMQKBgQD2+WG0fLwDXTrt68fe
hQqVa2Xs25z2B2QGPxWqSFU8WNly/mZ1BW413f3De/O58vYi7icTNyVoScm+8hdv
6PfD+LuRujdN1TuvPeyBTSvewQwf3IjN0Wh28mse36PwlBl+301C/x+ylxEDuJjK
hZxCcocIaoQqtBC7ac8tNa9r4wKBgQDUfnJKf/QQSLJwwlJKQQGHi3MVm7c9PbwY
eyIOY1s1NPluJDoYTZP4YLa/u2txwe2aHh9FhYMCPDAelqaSwaCLU9DsnKkQEA2A
RR47fcagG6xK7O+N95iEa8I1oIy7os9MBoBMwRIZ6VYIxxTj8UMNSR+tu6MqV1Gg
T5d0WDTJpQKBgCHyRSu5uV39AoyRS/eZ8cp36JqV1Q08FtOE+EVfi9evnrPfo9WR
2YQt7yNfdjCo5IwIj/ZkLhAXlFNakz4el2+oUJ/HKLLaDEoaCNf883q6rh/zABrK
HcG7sF2d/7qhoJ9/se7zgjfZ68zHIrkzhDbd5xGREnmMJoCcGo3sQyBhAoGAH3UQ
qmLC2N5KPFMoJ4H0HgLQ6LQCrnhDLkScSBEBYaEUA/AtAYgKjcyTgVLXlyGkcRpg
esRHHr+WSBD5W+R6ReYEmeKfTJdzyDdzQE9gZjdyjC0DUbsDwybIu3OnIef6VEDq
IXK7oUZfzDDcsNn4mTDoFaoff5cpqFfgDgM43VkCgYBNHw11b+d+AQmaZS9QqIt7
aF3FvwCYHV0jdv0Mb+Kc1bY4c0R5MFpzrTwVmdOerjuuA1+9b+0Hwo3nBZM4eaBu
SOamA2hu2OJWCl9q8fLCT69KqWDjghhvFe7c6aJJGucwaA3Uz3eLcPqoaCarMiNH
fMkTd7GabVourqIZdgvu1Q==
-----END PRIVATE KEY-----

# Corresponding public key

PublicKey = RSA-2048-PUBLIC
-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAzQCB6nsq4eoG1Z98c9n/
uUoJYVwuS6fGNs7wjdNTPsMYVSWwFcdpuZp31nJb+cNTKptuX2Yn1fuFFgdo092p
y9NZdFEXF9w9MJ0vxH7kH5fjKtt/ndhkocR2emZuzXG8Gqz151F/SzhZT+qbBeQt
WtqZEgCAE+RTFqTZu47QhriNKHWLrK+SLUaoaLSF0jnJuusOK2RZJxD0Ky0eoKS0
gCwL7Ksyj4posAc721Rv7qmAnShJkSs5DBUyvH4px2WPgXX65G80My/4e8qz5AZJ
uYV3hp2g6nGDU/ByJ1SIaRNkh2DRIr5nbg/Eg90g/8Mb2pajGWbJqi51rQPeR+HE
TwIDAQAB
-----END PUBLIC KEY-----

PrivPubKeyPair = RSA-2048:RSA-2048-PUBLIC


# 1024 bit key
# generated using (openssl genpkey -algorithm RSA -pkeyopt bits:1024)
PrivateKey = RSA-1024
****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
PrivateKey = RSA-512
******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
Title = RSA tests

Verify = RSA-2048
Ctrl = digest:SHA1
Input = "0123456789ABCDEF1234"
Output = c09d402423cbf233d26cae21f954547bc43fe80fd41360a0336cfdbe9aedad05bef6fd2eaee6cd60089a52482d4809a238149520df3bdde4cb9e23d9307b05c0a6f327052325a29adf2cc95b66523be7024e2a585c3d4db15dfbe146efe0ecdc0402e33fe5d40324ee96c5c3edd374a15cdc0f5d84aa243c0f07e188c6518fbfceae158a9943be398e31097da81b62074f626eff738be6160741d5a26957a482b3251fd85d8df78b98148459de10aa93305dbb4a5230aa1da291a9b0e481918f99b7638d72bb687f97661d304ae145d64a474437a4ef39d7b8059332ddeb07e92bf6e0e3acaf8afedc93795e4511737ec1e7aab6d5bc9466afc950c1c17b48ad

# Truncated digest
Sign = RSA-2048
Ctrl = digest:SHA512-224
Input = "0123456789ABCDEF123456789ABC"
Output = 5f720e9488139bb21e1c2f027fd5ce5993e6d31c5a8faaee833487b3a944d66891178868ace8070cad3ee2ffbe54aa4885a15fd1a7cc5166970fe1fd8c0423e72bd3e3b56fc4a53ed80aaaeca42497f0ec3c62113edc05cd006608f5eef7ce3ad4cba1069f68731dd28a524a1f93fcdc5547112d48d45586dd943ba0d443be9635720d8a61697c54c96627f0d85c5fbeaa3b4af86a65cf2fc3800dd5de34c046985f25d0efc0bb6edccc1d08b3a4fb9c8faffe181c7e68b31e374ad1440a4a664eec9ca0dc53a9d2f5bc7d9940d866f64201bcbc63612754df45727ea24b531d7de83d1bb707444859fa35521320c33bf6f4dbeb6fb56e653adbf7af15843f17

Verify = RSA-2048
Ctrl = digest:SHA512-224
Input = "0123456789ABCDEF123456789ABC"
Output = 5f720e9488139bb21e1c2f027fd5ce5993e6d31c5a8faaee833487b3a944d66891178868ace8070cad3ee2ffbe54aa4885a15fd1a7cc5166970fe1fd8c0423e72bd3e3b56fc4a53ed80aaaeca42497f0ec3c62113edc05cd006608f5eef7ce3ad4cba1069f68731dd28a524a1f93fcdc5547112d48d45586dd943ba0d443be9635720d8a61697c54c96627f0d85c5fbeaa3b4af86a65cf2fc3800dd5de34c046985f25d0efc0bb6edccc1d08b3a4fb9c8faffe181c7e68b31e374ad1440a4a664eec9ca0dc53a9d2f5bc7d9940d866f64201bcbc63612754df45727ea24b531d7de83d1bb707444859fa35521320c33bf6f4dbeb6fb56e653adbf7af15843f17

VerifyRecover = RSA-2048
Ctrl = digest:SHA1
Input = c09d402423cbf233d26cae21f954547bc43fe80fd41360a0336cfdbe9aedad05bef6fd2eaee6cd60089a52482d4809a238149520df3bdde4cb9e23d9307b05c0a6f327052325a29adf2cc95b66523be7024e2a585c3d4db15dfbe146efe0ecdc0402e33fe5d40324ee96c5c3edd374a15cdc0f5d84aa243c0f07e188c6518fbfceae158a9943be398e31097da81b62074f626eff738be6160741d5a26957a482b3251fd85d8df78b98148459de10aa93305dbb4a5230aa1da291a9b0e481918f99b7638d72bb687f97661d304ae145d64a474437a4ef39d7b8059332ddeb07e92bf6e0e3acaf8afedc93795e4511737ec1e7aab6d5bc9466afc950c1c17b48ad
Output = "0123456789ABCDEF1234"

# Leading zero in the signature
Verify = RSA-2048
Ctrl = digest:SHA1
Input = "0123456789ABCDEF1234"
Output = 00c09d402423cbf233d26cae21f954547bc43fe80fd41360a0336cfdbe9aedad05bef6fd2eaee6cd60089a52482d4809a238149520df3bdde4cb9e23d9307b05c0a6f327052325a29adf2cc95b66523be7024e2a585c3d4db15dfbe146efe0ecdc0402e33fe5d40324ee96c5c3edd374a15cdc0f5d84aa243c0f07e188c6518fbfceae158a9943be398e31097da81b62074f626eff738be6160741d5a26957a482b3251fd85d8df78b98148459de10aa93305dbb4a5230aa1da291a9b0e481918f99b7638d72bb687f97661d304ae145d64a474437a4ef39d7b8059332ddeb07e92bf6e0e3acaf8afedc93795e4511737ec1e7aab6d5bc9466afc950c1c17b48ad
Result = VERIFY_ERROR

VerifyRecover = RSA-2048
Ctrl = digest:SHA1
Input = 00c09d402423cbf233d26cae21f954547bc43fe80fd41360a0336cfdbe9aedad05bef6fd2eaee6cd60089a52482d4809a238149520df3bdde4cb9e23d9307b05c0a6f327052325a29adf2cc95b66523be7024e2a585c3d4db15dfbe146efe0ecdc0402e33fe5d40324ee96c5c3edd374a15cdc0f5d84aa243c0f07e188c6518fbfceae158a9943be398e31097da81b62074f626eff738be6160741d5a26957a482b3251fd85d8df78b98148459de10aa93305dbb4a5230aa1da291a9b0e481918f99b7638d72bb687f97661d304ae145d64a474437a4ef39d7b8059332ddeb07e92bf6e0e3acaf8afedc93795e4511737ec1e7aab6d5bc9466afc950c1c17b48ad
Result = KEYOP_ERROR

# Mismatched digest
Verify = RSA-2048
Ctrl = digest:SHA1
Input = "0123456789ABCDEF1233"
Output = c09d402423cbf233d26cae21f954547bc43fe80fd41360a0336cfdbe9aedad05bef6fd2eaee6cd60089a52482d4809a238149520df3bdde4cb9e23d9307b05c0a6f327052325a29adf2cc95b66523be7024e2a585c3d4db15dfbe146efe0ecdc0402e33fe5d40324ee96c5c3edd374a15cdc0f5d84aa243c0f07e188c6518fbfceae158a9943be398e31097da81b62074f626eff738be6160741d5a26957a482b3251fd85d8df78b98148459de10aa93305dbb4a5230aa1da291a9b0e481918f99b7638d72bb687f97661d304ae145d64a474437a4ef39d7b8059332ddeb07e92bf6e0e3acaf8afedc93795e4511737ec1e7aab6d5bc9466afc950c1c17b48ad
Result = VERIFY_ERROR

# Corrupted signature
Verify = RSA-2048
Ctrl = digest:SHA1
Input = "0123456789ABCDEF1233"
Output = c09d402423cbf233d26cae21f954547bc43fe80fd41360a0336cfdbe9aedad05bef6fd2eaee6cd60089a52482d4809a238149520df3bdde4cb9e23d9307b05c0a6f327052325a29adf2cc95b66523be7024e2a585c3d4db15dfbe146efe0ecdc0402e33fe5d40324ee96c5c3edd374a15cdc0f5d84aa243c0f07e188c6518fbfceae158a9943be398e31097da81b62074f626eff738be6160741d5a26957a482b3251fd85d8df78b98148459de10aa93305dbb4a5230aa1da291a9b0e481918f99b7638d72bb687f97661d304ae145d64a474437a4ef39d7b8059332ddeb07e92bf6e0e3acaf8afedc93795e4511737ec1e7aab6d5bc9466afc950c1c17b48ae
Result = VERIFY_ERROR

# parameter is not NULLt
Verify = RSA-2048
Ctrl = digest:sha1
Input = "0123456789ABCDEF1234"
Output = 3ec3fc29eb6e122bd7aa361cd09fe1bcbe85311096a7b9e4799cedfb2351ce0ab7fe4e75b4f6b37f67edd9c60c800f9ab941c0c157d7d880ca9de40c951d60fd293ae220d4bc510b1572d6e85a1bbbd8605b52e05f1c64fafdae59a1c2fbed214b7844d0134619de62851d5a0522e32e556e5950f3f97b8150e3f0dffee612c924201c27cd9bc8b423a71533380c276d3d59fcba35a2e80a1a192ec266a6c2255012cd86a349fe90a542b355fa3355b04da6cdf1df77f0e7bd44a90e880e1760266d233e465226f5db1c68857847d82072861ee266ddfc2e596845b77e1803274a579835ab5e4975d81d20b7df9cec7795489e4a2bdb8c1cf6a6b359945ac92c
Result = VERIFY_ERROR

# embedded digest too long
Verify = RSA-2048
Ctrl = digest:sha1
Input = "0123456789ABCDEF1234"
Output = afec9a0d5330a08f54283bb4a9d4e7e7e70fc1342336c4c766fba713f66970151c6e27413c48c33864ea45a0238787004f338ed3e21b53b0fe9c1151c42c388cbc7cba5a06b706c407a5b48324fbe994dc7afc3a19fb3d2841e66222596c14cd72a0f0a7455a019d8eb554f59c0183f9552b75aa96fee8bf935945e079ca283d2bd3534a86f11351f6d6181fbf433e5b01a6d1422145c7a72214d3aacdd5d3af12b2d6bf6438f9f9a64010d8aeed801c87f0859412b236150b86a545f7239be022f4a7ad246b59df87514294cb4a4c7c5a997ee53c66054d9f38ca4e76c1f7af83c30f737ef70f83a45aebe18238ddb95e1998814ca4fc72388f1533147c169d
Result = VERIFY_ERROR

VerifyRecover = RSA-2048
Ctrl = digest:sha1
Input = afec9a0d5330a08f54283bb4a9d4e7e7e70fc1342336c4c766fba713f66970151c6e27413c48c33864ea45a0238787004f338ed3e21b53b0fe9c1151c42c388cbc7cba5a06b706c407a5b48324fbe994dc7afc3a19fb3d2841e66222596c14cd72a0f0a7455a019d8eb554f59c0183f9552b75aa96fee8bf935945e079ca283d2bd3534a86f11351f6d6181fbf433e5b01a6d1422145c7a72214d3aacdd5d3af12b2d6bf6438f9f9a64010d8aeed801c87f0859412b236150b86a545f7239be022f4a7ad246b59df87514294cb4a4c7c5a997ee53c66054d9f38ca4e76c1f7af83c30f737ef70f83a45aebe18238ddb95e1998814ca4fc72388f1533147c169d
Result = KEYOP_ERROR

# embedded digest too short
Verify = RSA-2048
Ctrl = digest:sha1
Input = "0123456789ABCDEF1234"
Output = afec9a0d5330a08f54283bb4a9d4e7e7e70fc1342336c4c766fba713f66970151c6e27413c48c33864ea45a0238787004f338ed3e21b53b0fe9c1151c42c388cbc7cba5a06b706c407a5b48324fbe994dc7afc3a19fb3d2841e66222596c14cd72a0f0a7455a019d8eb554f59c0183f9552b75aa96fee8bf935945e079ca283d2bd3534a86f11351f6d6181fbf433e5b01a6d1422145c7a72214d3aacdd5d3af12b2d6bf6438f9f9a64010d8aeed801c87f0859412b236150b86a545f7239be022f4a7ad246b59df87514294cb4a4c7c5a997ee53c66054d9f38ca4e76c1f7af83c30f737ef70f83a45aebe18238ddb95e1998814ca4fc72388f1533147c169d
Result = VERIFY_ERROR

VerifyRecover = RSA-2048
Ctrl = digest:sha1
Input = afec9a0d5330a08f54283bb4a9d4e7e7e70fc1342336c4c766fba713f66970151c6e27413c48c33864ea45a0238787004f338ed3e21b53b0fe9c1151c42c388cbc7cba5a06b706c407a5b48324fbe994dc7afc3a19fb3d2841e66222596c14cd72a0f0a7455a019d8eb554f59c0183f9552b75aa96fee8bf935945e079ca283d2bd3534a86f11351f6d6181fbf433e5b01a6d1422145c7a72214d3aacdd5d3af12b2d6bf6438f9f9a64010d8aeed801c87f0859412b236150b86a545f7239be022f4a7ad246b59df87514294cb4a4c7c5a997ee53c66054d9f38ca4e76c1f7af83c30f737ef70f83a45aebe18238ddb95e1998814ca4fc72388f1533147c169d
Result = KEYOP_ERROR

# Garbage after DigestInfo
Verify = RSA-2048
Ctrl = digest:sha1
Input = "0123456789ABCDEF1234"
Output = 9ee34872d4271a7d8808af0a4052a145a6d6a8437d00da3ed14428c7f087cd39f4d43334c41af63e7fa1ba363fee7bcef401d9d36a662abbab55ce89a696e1be0dfa19a5d09ca617dd488787b6048baaefeb29bc8688b2fe3882de2b77c905b5a8b56cf9616041e5ec934ba6de863efe93acc4eef783fe7f72a00fa65d6093ed32bf98ce527e62ccb1d56317f4be18b7e0f55d7c36617d2d0678a306e3350956b662ac15df45215dd8f6b314babb9788e6c272fa461e4c9b512a11a4b92bc77c3a4c95c903fccb238794eca5c750477bf56ea6ee6a167367d881b485ae3889e7c489af8fdf38e0c0f2aed780831182e34abedd43c39281b290774bf35cc25274
Result = VERIFY_ERROR

VerifyRecover = RSA-2048
Ctrl = digest:sha1
Input = 9ee34872d4271a7d8808af0a4052a145a6d6a8437d00da3ed14428c7f087cd39f4d43334c41af63e7fa1ba363fee7bcef401d9d36a662abbab55ce89a696e1be0dfa19a5d09ca617dd488787b6048baaefeb29bc8688b2fe3882de2b77c905b5a8b56cf9616041e5ec934ba6de863efe93acc4eef783fe7f72a00fa65d6093ed32bf98ce527e62ccb1d56317f4be18b7e0f55d7c36617d2d0678a306e3350956b662ac15df45215dd8f6b314babb9788e6c272fa461e4c9b512a11a4b92bc77c3a4c95c903fccb238794eca5c750477bf56ea6ee6a167367d881b485ae3889e7c489af8fdf38e0c0f2aed780831182e34abedd43c39281b290774bf35cc25274
Result = KEYOP_ERROR

# invalid tag for parameter
Verify = RSA-2048
Ctrl = digest:sha1
Input = "0123456789ABCDEF1234"
Output = 49525db4d44c755e560cba980b1d85ea604b0e077fcadd4ba44072a3487bbddb835016200a7d8739cce2dc3223d9c20cbdd25059ab02277f1f21318efd18e21038ec89aa9d40680987129e8b41ba33bceb86518bdf47268b921cce2037acabca6575d832499538d6f40cdba0d40bd7f4d8ea6ca6e2eec87f294efc971407857f5d7db09f6a7b31e301f571c6d82a5e3d08d2bb3a36e673d28b910f5bec57f0fcc4d968fd7c94d0b9226dec17f5192ad8b42bcab6f26e1bea1fdc3b958199acb00f14ebcb2a352f3afcedd4c09000128a603bbeb9696dea13040445253972d46237a25c7845e3b464e6984c2348ea1f1210a9ff0b00d2d72b50db00c009bb39f9
Result = VERIFY_ERROR

# Verify using public key

Verify = RSA-2048-PUBLIC
Ctrl = digest:SHA1
Input = "0123456789ABCDEF1234"
Output = c09d402423cbf233d26cae21f954547bc43fe80fd41360a0336cfdbe9aedad05bef6fd2eaee6cd60089a52482d4809a238149520df3bdde4cb9e23d9307b05c0a6f327052325a29adf2cc95b66523be7024e2a585c3d4db15dfbe146efe0ecdc0402e33fe5d40324ee96c5c3edd374a15cdc0f5d84aa243c0f07e188c6518fbfceae158a9943be398e31097da81b62074f626eff738be6160741d5a26957a482b3251fd85d8df78b98148459de10aa93305dbb4a5230aa1da291a9b0e481918f99b7638d72bb687f97661d304ae145d64a474437a4ef39d7b8059332ddeb07e92bf6e0e3acaf8afedc93795e4511737ec1e7aab6d5bc9466afc950c1c17b48ad

# no padding

# Too small input
Sign = RSA-2048
Ctrl = rsa_padding_mode:none
Input = "0123456789ABCDEF123456789ABC"
Output = c09d402423cbf233d26cae21f954547bc43fe80fd41360a0336cfdbe9aedad05bef6fd2eaee6cd60089a52482d4809a238149520df3bdde4cb9e23d9307b05c0a6f327052325a29adf2cc95b66523be7024e2a585c3d4db15dfbe146efe0ecdc0402e33fe5d40324ee96c5c3edd374a15cdc0f5d84aa243c0f07e188c6518fbfceae158a9943be398e31097da81b62074f626eff738be6160741d5a26957a482b3251fd85d8df78b98148459de10aa93305dbb4a5230aa1da291a9b0e481918f99b7638d72bb687f97661d304ae145d64a474437a4ef39d7b8059332ddeb07e92bf6e0e3acaf8afedc93795e4511737ec1e7aab6d5bc9466afc950c1c17b48ad
Result = KEYOP_ERROR

# Digest set before padding
Sign = RSA-2048
Ctrl = digest:sha256
Ctrl = rsa_padding_mode:none
Input = 0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef
Output = 64b0e9f9892371110c40ba5739dc0974002aa6e6160b481447c6819947c2d3b537a6e3775a85ae8ef75e000ca5498d772e3e797012ac8e462d72e567eb4afae0d1df72ffc84b3117045c58eb13aabb427fd6591577089dfa36d8d07ebd0670e4473683659b53b050c32397752cdee7c08de667f8de0ec01db01d440e433986e57ead2f877356b7d4985daf6c7ba09e46c061fe2372baa90cbd77557ef1143f46e27abf65c276f165a753e1f09e3719d1bfd8b32efe4aed2e97b502aa96ce472d3d91a09fae47b1a5103c448039ada73a57d7a001542bfb0b58c8b4bcb705a108a643434bb7ff997b58ba8b76425d7510aeff3e60f17af82191500517653fa5f3
Result = PKEY_CTRL_ERROR

# Digest set after padding
Sign = RSA-2048
Ctrl = rsa_padding_mode:none
Ctrl = digest:sha256
Input = 0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef
Output = 64b0e9f9892371110c40ba5739dc0974002aa6e6160b481447c6819947c2d3b537a6e3775a85ae8ef75e000ca5498d772e3e797012ac8e462d72e567eb4afae0d1df72ffc84b3117045c58eb13aabb427fd6591577089dfa36d8d07ebd0670e4473683659b53b050c32397752cdee7c08de667f8de0ec01db01d440e433986e57ead2f877356b7d4985daf6c7ba09e46c061fe2372baa90cbd77557ef1143f46e27abf65c276f165a753e1f09e3719d1bfd8b32efe4aed2e97b502aa96ce472d3d91a09fae47b1a5103c448039ada73a57d7a001542bfb0b58c8b4bcb705a108a643434bb7ff997b58ba8b76425d7510aeff3e60f17af82191500517653fa5f3
Result = PKEY_CTRL_ERROR

Sign = RSA-2048
Ctrl = rsa_padding_mode:none
Input = 0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef
Output = 64b0e9f9892371110c40ba5739dc0974002aa6e6160b481447c6819947c2d3b537a6e3775a85ae8ef75e000ca5498d772e3e797012ac8e462d72e567eb4afae0d1df72ffc84b3117045c58eb13aabb427fd6591577089dfa36d8d07ebd0670e4473683659b53b050c32397752cdee7c08de667f8de0ec01db01d440e433986e57ead2f877356b7d4985daf6c7ba09e46c061fe2372baa90cbd77557ef1143f46e27abf65c276f165a753e1f09e3719d1bfd8b32efe4aed2e97b502aa96ce472d3d91a09fae47b1a5103c448039ada73a57d7a001542bfb0b58c8b4bcb705a108a643434bb7ff997b58ba8b76425d7510aeff3e60f17af82191500517653fa5f3

Verify = RSA-2048-PUBLIC
Ctrl = rsa_padding_mode:none
Input = 0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef
Output = 64b0e9f9892371110c40ba5739dc0974002aa6e6160b481447c6819947c2d3b537a6e3775a85ae8ef75e000ca5498d772e3e797012ac8e462d72e567eb4afae0d1df72ffc84b3117045c58eb13aabb427fd6591577089dfa36d8d07ebd0670e4473683659b53b050c32397752cdee7c08de667f8de0ec01db01d440e433986e57ead2f877356b7d4985daf6c7ba09e46c061fe2372baa90cbd77557ef1143f46e27abf65c276f165a753e1f09e3719d1bfd8b32efe4aed2e97b502aa96ce472d3d91a09fae47b1a5103c448039ada73a57d7a001542bfb0b58c8b4bcb705a108a643434bb7ff997b58ba8b76425d7510aeff3e60f17af82191500517653fa5f3

# Plaintext modified
Verify = RSA-2048-PUBLIC
Ctrl = rsa_padding_mode:none
Input = 0223456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef
Output = 64b0e9f9892371110c40ba5739dc0974002aa6e6160b481447c6819947c2d3b537a6e3775a85ae8ef75e000ca5498d772e3e797012ac8e462d72e567eb4afae0d1df72ffc84b3117045c58eb13aabb427fd6591577089dfa36d8d07ebd0670e4473683659b53b050c32397752cdee7c08de667f8de0ec01db01d440e433986e57ead2f877356b7d4985daf6c7ba09e46c061fe2372baa90cbd77557ef1143f46e27abf65c276f165a753e1f09e3719d1bfd8b32efe4aed2e97b502aa96ce472d3d91a09fae47b1a5103c448039ada73a57d7a001542bfb0b58c8b4bcb705a108a643434bb7ff997b58ba8b76425d7510aeff3e60f17af82191500517653fa5f3
Result = VERIFY_ERROR

VerifyRecover = RSA-2048-PUBLIC
Ctrl = rsa_padding_mode:none
Input = 64b0e9f9892371110c40ba5739dc0974002aa6e6160b481447c6819947c2d3b537a6e3775a85ae8ef75e000ca5498d772e3e797012ac8e462d72e567eb4afae0d1df72ffc84b3117045c58eb13aabb427fd6591577089dfa36d8d07ebd0670e4473683659b53b050c32397752cdee7c08de667f8de0ec01db01d440e433986e57ead2f877356b7d4985daf6c7ba09e46c061fe2372baa90cbd77557ef1143f46e27abf65c276f165a753e1f09e3719d1bfd8b32efe4aed2e97b502aa96ce472d3d91a09fae47b1a5103c448039ada73a57d7a001542bfb0b58c8b4bcb705a108a643434bb7ff997b58ba8b76425d7510aeff3e60f17af82191500517653fa5f3
Output = 0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef

# RSA decrypt

Decrypt = RSA-2048
Input = 550AF55A2904E7B9762352F8FB7FA235A9CB053AACB2D5FCB8CA48453CB2EE3619746C701ABF2D4CC67003471A187900B05AA812BD25ED05C675DFC8C97A24A7BF49BD6214992CAD766D05A9A2B57B74F26A737E0237B8B76C45F1F226A836D7CFBC75BA999BDBE48DBC09227AA46C88F21DCCBA7840141AD5A5D71FD122E6BD6AC3E564780DFE623FC1CA9B995A6037BF0BBD43B205A84AC5444F34202C05CE9113087176432476576DE6FFFF9A52EA57C08BE3EC2F49676CB8E12F762AC71FA3C321E00AC988910C85FF52F93825666CE0D40FFAA0592078919D4493F46D95CCF76364C6D57760DD0B64805F9AFC76A2365A5575CA301D5103F0EA76CB9A78
Output = "Hello World"

# Corrupted ciphertext
FIPSversion = <3.2.0
Decrypt = RSA-2048
Input = 550AF55A2904E7B9762352F8FB7FA235A9CB053AACB2D5FCB8CA48453CB2EE3619746C701ABF2D4CC67003471A187900B05AA812BD25ED05C675DFC8C97A24A7BF49BD6214992CAD766D05A9A2B57B74F26A737E0237B8B76C45F1F226A836D7CFBC75BA999BDBE48DBC09227AA46C88F21DCCBA7840141AD5A5D71FD122E6BD6AC3E564780DFE623FC1CA9B995A6037BF0BBD43B205A84AC5444F34202C05CE9113087176432476576DE6FFFF9A52EA57C08BE3EC2F49676CB8E12F762AC71FA3C321E00AC988910C85FF52F93825666CE0D40FFAA0592078919D4493F46D95CCF76364C6D57760DD0B64805F9AFC76A2365A5575CA301D5103F0EA76CB9A79
Output = "Hello World"
Result = KEYOP_ERROR

# OAEP padding
Decrypt = RSA-2048
Ctrl = rsa_padding_mode:oaep
Input = 458708DFBD42A1297CE7A9C86C7087AB80B1754810929B89C5107CA55368587686986FCE94D86CC1595B3FB736223A656EC0F34D18BA1CC5665593610F56C58E26B272D584F3D983A5C91085700755AEBD921FB280BBA3EDA7046EC07B43E7298E52D59EDC92BE4639A8CE08B2F85976ECF6D98CC469EEB9D5D8E2A32EA8A6626EDAFE1038B3DF455668A9F3C77CAD8B92FB872E00058C3D2A7EDE1A1F03FC5622084AE04D9D24F6BF0995C58D35B93B699B9763595E123F2AB0863CC9229EB290E2EDE7715C7A8F39E0B9A3E2E1B56EBB62F1CBFBB5986FB212EBD785B83D01D968B11D1756C7337F70C1F1A63BFF03608E24F3A2FD44E67F832A8701C5D5AF
Output = "Hello World"

# OAEP padding, corrupted ciphertext
Decrypt = RSA-2048
Ctrl = rsa_padding_mode:oaep
Input = 458708DFBD42A1297CE7A9C86C7087AB80B1754810929B89C5107CA55368587686986FCE94D86CC1595B3FB736223A656EC0F34D18BA1CC5665593610F56C58E26B272D584F3D983A5C91085700755AEBD921FB280BBA3EDA7046EC07B43E7298E52D59EDC92BE4639A8CE08B2F85976ECF6D98CC469EEB9D5D8E2A32EA8A6626EDAFE1038B3DF455668A9F3C77CAD8B92FB872E00058C3D2A7EDE1A1F03FC5622084AE04D9D24F6BF0995C58D35B93B699B9763595E123F2AB0863CC9229EB290E2EDE7715C7A8F39E0B9A3E2E1B56EBB62F1CBFBB5986FB212EBD785B83D01D968B11D1756C7337F70C1F1A63BFF03608E24F3A2FD44E67F832A8701C5D5AC
Output = "Hello World"
Result = KEYOP_ERROR

# Illegal RSA key derivation
Derive = RSA-2048
Result = KEYOP_INIT_ERROR
Reason = operation not supported for this keytype

# RSA PSS key tests

# PSS only key, no parameter restrictions
PrivateKey = RSA-PSS
-----BEGIN PRIVATE KEY-----
MIIEugIBADALBgkqhkiG9w0BAQoEggSmMIIEogIBAAKCAQEAzQCB6nsq4eoG1Z98
c9n/uUoJYVwuS6fGNs7wjdNTPsMYVSWwFcdpuZp31nJb+cNTKptuX2Yn1fuFFgdo
092py9NZdFEXF9w9MJ0vxH7kH5fjKtt/ndhkocR2emZuzXG8Gqz151F/SzhZT+qb
BeQtWtqZEgCAE+RTFqTZu47QhriNKHWLrK+SLUaoaLSF0jnJuusOK2RZJxD0Ky0e
oKS0gCwL7Ksyj4posAc721Rv7qmAnShJkSs5DBUyvH4px2WPgXX65G80My/4e8qz
5AZJuYV3hp2g6nGDU/ByJ1SIaRNkh2DRIr5nbg/Eg90g/8Mb2pajGWbJqi51rQPe
R+HETwIDAQABAoIBAGApeseZGxZ6BtayR1i4y+IIvrmy2eyXOL2A+QouNQBd184p
LZ4puohb0xb+8fIJE7wKyQ1rCAiyQU2CEERB2GJKM84CM8j3gKSLN1r/AtdnEiKK
cCSE2z+evszPu+4XCduhgoANlJ6eQhbgv/NVg4j4vZDaNzodgnQ+w/vdFCf9FoJa
ZXoxaRLoaVNlEXyi+EXJCUBfysVfiV/BXSA4bCbueMnpkHUCmheKbB5M8MIA6KnP
sn6dFW+G5sKtwisahKHNXKWyeQh115QHyEs1I5XLgcw/7VuwQ7ae3gwHIEVQAlzu
jF9EAXC2Egu0jg90e82PUiEQhQ3wQ8Qo39GHBTECgYEA9vlhtHy8A1067evH3oUK
lWtl7Nuc9gdkBj8VqkhVPFjZcv5mdQVuNd39w3vzufL2Iu4nEzclaEnJvvIXb+j3
w/i7kbo3TdU7rz3sgU0r3sEMH9yIzdFodvJrHt+j8JQZft9NQv8fspcRA7iYyoWc
QnKHCGqEKrQQu2nPLTWva+MCgYEA1H5ySn/0EEiycMJSSkEBh4tzFZu3PT28GHsi
DmNbNTT5biQ6GE2T+GC2v7trccHtmh4fRYWDAjwwHpamksGgi1PQ7JypEBANgEUe
O33GoBusSuzvjfeYhGvCNaCMu6LPTAaATMESGelWCMcU4/FDDUkfrbujKldRoE+X
dFg0yaUCgYAh8kUrubld/QKMkUv3mfHKd+ialdUNPBbThPhFX4vXr56z36PVkdmE
Le8jX3YwqOSMCI/2ZC4QF5RTWpM+HpdvqFCfxyiy2gxKGgjX/PN6uq4f8wAayh3B
u7Bdnf+6oaCff7Hu84I32evMxyK5M4Q23ecRkRJ5jCaAnBqN7EMgYQKBgB91EKpi
wtjeSjxTKCeB9B4C0Oi0Aq54Qy5EnEgRAWGhFAPwLQGICo3Mk4FS15chpHEaYHrE
Rx6/lkgQ+VvkekXmBJnin0yXc8g3c0BPYGY3cowtA1G7A8MmyLtzpyHn+lRA6iFy
u6FGX8ww3LDZ+Jkw6BWqH3+XKahX4A4DON1ZAoGATR8NdW/nfgEJmmUvUKiLe2hd
xb8AmB1dI3b9DG/inNW2OHNEeTBac608FZnTnq47rgNfvW/tB8KN5wWTOHmgbkjm
pgNobtjiVgpfavHywk+vSqlg44IYbxXu3OmiSRrnMGgN1M93i3D6qGgmqzIjR3zJ
E3exmm1aLq6iGXYL7tU=
-----END PRIVATE KEY-----

# PSS public key default parameters
PublicKey = RSA-PSS-DEFAULT
-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQowAAOCAQ8AMIIBCgKCAQEAzQCB6nsq4eoG1Z98c9n/
uUoJYVwuS6fGNs7wjdNTPsMYVSWwFcdpuZp31nJb+cNTKptuX2Yn1fuFFgdo092p
y9NZdFEXF9w9MJ0vxH7kH5fjKtt/ndhkocR2emZuzXG8Gqz151F/SzhZT+qbBeQt
WtqZEgCAE+RTFqTZu47QhriNKHWLrK+SLUaoaLSF0jnJuusOK2RZJxD0Ky0eoKS0
gCwL7Ksyj4posAc721Rv7qmAnShJkSs5DBUyvH4px2WPgXX65G80My/4e8qz5AZJ
uYV3hp2g6nGDU/ByJ1SIaRNkh2DRIr5nbg/Eg90g/8Mb2pajGWbJqi51rQPeR+HE
TwIDAQAB
-----END PUBLIC KEY-----

PrivPubKeyPair = RSA-PSS:RSA-PSS-DEFAULT

# Key with invalid negative minimum salt length
PublicKey = RSA-PSS-BAD
-----BEGIN PUBLIC KEY-----
MIIBJzASBgkqhkiG9w0BAQowBaIDAgH/A4IBDwAwggEKAoIBAQDNAIHqeyrh6gbV
n3xz2f+5SglhXC5Lp8Y2zvCN01M+wxhVJbAVx2m5mnfWclv5w1Mqm25fZifV+4UW
B2jT3anL01l0URcX3D0wnS/EfuQfl+Mq23+d2GShxHZ6Zm7NcbwarPXnUX9LOFlP
6psF5C1a2pkSAIAT5FMWpNm7jtCGuI0odYusr5ItRqhotIXSOcm66w4rZFknEPQr
LR6gpLSALAvsqzKPimiwBzvbVG/uqYCdKEmRKzkMFTK8finHZY+BdfrkbzQzL/h7
yrPkBkm5hXeGnaDqcYNT8HInVIhpE2SHYNEivmduD8SD3SD/wxvalqMZZsmqLnWt
A95H4cRPAgMBAAE=
-----END PUBLIC KEY-----

PrivPubKeyPair = RSA-PSS:RSA-PSS-BAD


# Key with minimum salt length exceeding maximum permitted value
PublicKey = RSA-PSS-BAD2
-----BEGIN PUBLIC KEY-----
MIIBKDATBgkqhkiG9w0BAQowBqIEAgIBAAOCAQ8AMIIBCgKCAQEAzQCB6nsq4eoG
1Z98c9n/uUoJYVwuS6fGNs7wjdNTPsMYVSWwFcdpuZp31nJb+cNTKptuX2Yn1fuF
Fgdo092py9NZdFEXF9w9MJ0vxH7kH5fjKtt/ndhkocR2emZuzXG8Gqz151F/SzhZ
T+qbBeQtWtqZEgCAE+RTFqTZu47QhriNKHWLrK+SLUaoaLSF0jnJuusOK2RZJxD0
Ky0eoKS0gCwL7Ksyj4posAc721Rv7qmAnShJkSs5DBUyvH4px2WPgXX65G80My/4
e8qz5AZJuYV3hp2g6nGDU/ByJ1SIaRNkh2DRIr5nbg/Eg90g/8Mb2pajGWbJqi51
rQPeR+HETwIDAQAB
-----END PUBLIC KEY-----

PrivPubKeyPair = RSA-PSS:RSA-PSS-BAD2

# Zero salt length makes output deterministic
Sign = RSA-2048
Ctrl = digest:sha256
Ctrl = rsa_padding_mode:pss
Ctrl = rsa_pss_saltlen:0
Input="0123456789ABCDEF0123456789ABCDEF"
Output=4DE433D5844043EF08D354DA03CB29068780D52706D7D1E4D50EFB7D58C9D547D83A747DDD0635A96B28F854E50145518482CB49E963054621B53C60C498D07C16E9C2789C893CF38D4D86900DE71BDE463BD2761D1271E358C7480A1AC0BAB930DDF39602AD1BC165B5D7436B516B7A7858E8EB7AB1C420EEB482F4D207F0E462B1724959320A084E13848D11D10FB593E66BF680BF6D3F345FC3E9C3DE60ABBAC37E1C6EC80A268C8D9FC49626C679097AA690BC1AA662B95EB8DB70390861AA0898229F9349B4B5FDD030D4928C47084708A933144BE23BD3C6E661B85B2C0EF9ED36D498D5B7320E8194D363D4AD478C059BAE804181965E0B81B663158A

# Verify of above signature
Verify = RSA-2048-PUBLIC
Ctrl = rsa_padding_mode:pss
Ctrl = rsa_pss_saltlen:0
Ctrl = digest:sha256
Input="0123456789ABCDEF0123456789ABCDEF"
Output=4DE433D5844043EF08D354DA03CB29068780D52706D7D1E4D50EFB7D58C9D547D83A747DDD0635A96B28F854E50145518482CB49E963054621B53C60C498D07C16E9C2789C893CF38D4D86900DE71BDE463BD2761D1271E358C7480A1AC0BAB930DDF39602AD1BC165B5D7436B516B7A7858E8EB7AB1C420EEB482F4D207F0E462B1724959320A084E13848D11D10FB593E66BF680BF6D3F345FC3E9C3DE60ABBAC37E1C6EC80A268C8D9FC49626C679097AA690BC1AA662B95EB8DB70390861AA0898229F9349B4B5FDD030D4928C47084708A933144BE23BD3C6E661B85B2C0EF9ED36D498D5B7320E8194D363D4AD478C059BAE804181965E0B81B663158A

# Verify using salt length auto detect
Verify = RSA-2048-PUBLIC
Ctrl = rsa_padding_mode:pss
Ctrl = rsa_pss_saltlen:auto
Input="0123456789ABCDEF0123"
Output = 6BF7EDC63A0BA184EEEC7F3020FEC8F5EBF38C2B76481881F48BCCE5796E7AB294548BA9AE810457C7723CABD1BDE94CF59CF7C0FC7461B22760C8ED703DD98E97BFDD61FA8D1181C411F6DEE5FF159F4850746D78EDEE385A363DC28E2CB373D5CAD7953F3BD5E639BE345732C03A1BDEA268814DA036EB1891C82D4012F3B903D86636055F87B96FC98806AD1B217685A4D754046A5DE0B0D7870664BE07902153EC85BA457BE7D7F89D7FE0F626D02A9CBBB2BB479DDA1A5CAE75247FB7BF6BFB15C1D3FD9E6B1573CCDBC72011C3B97716058BB11C7EA2E4E56ADAFE1F5DE6A7FD405AC5890100F9C3408EFFB5C73BF73F48177FF743B4B819D0699D507B

# Digest too short
Verify = RSA-2048-PUBLIC
Ctrl = rsa_padding_mode:pss
Ctrl = rsa_pss_saltlen:0
Ctrl = digest:sha256
Input="0123456789ABCDEF0123456789ABCDE"
Output=4DE433D5844043EF08D354DA03CB29068780D52706D7D1E4D50EFB7D58C9D547D83A747DDD0635A96B28F854E50145518482CB49E963054621B53C60C498D07C16E9C2789C893CF38D4D86900DE71BDE463BD2761D1271E358C7480A1AC0BAB930DDF39602AD1BC165B5D7436B516B7A7858E8EB7AB1C420EEB482F4D207F0E462B1724959320A084E13848D11D10FB593E66BF680BF6D3F345FC3E9C3DE60ABBAC37E1C6EC80A268C8D9FC49626C679097AA690BC1AA662B95EB8DB70390861AA0898229F9349B4B5FDD030D4928C47084708A933144BE23BD3C6E661B85B2C0EF9ED36D498D5B7320E8194D363D4AD478C059BAE804181965E0B81B663158A
Result = VERIFY_ERROR

# Digest too long
Verify = RSA-2048-PUBLIC
Ctrl = rsa_padding_mode:pss
Ctrl = rsa_pss_saltlen:0
Ctrl = digest:sha256
Input="0123456789ABCDEF0123456789ABCDEF0"
Output=4DE433D5844043EF08D354DA03CB29068780D52706D7D1E4D50EFB7D58C9D547D83A747DDD0635A96B28F854E50145518482CB49E963054621B53C60C498D07C16E9C2789C893CF38D4D86900DE71BDE463BD2761D1271E358C7480A1AC0BAB930DDF39602AD1BC165B5D7436B516B7A7858E8EB7AB1C420EEB482F4D207F0E462B1724959320A084E13848D11D10FB593E66BF680BF6D3F345FC3E9C3DE60ABBAC37E1C6EC80A268C8D9FC49626C679097AA690BC1AA662B95EB8DB70390861AA0898229F9349B4B5FDD030D4928C47084708A933144BE23BD3C6E661B85B2C0EF9ED36D498D5B7320E8194D363D4AD478C059BAE804181965E0B81B663158A
Result = VERIFY_ERROR

# Wrong salt length
Verify = RSA-2048
Ctrl = rsa_padding_mode:pss
Ctrl = rsa_pss_saltlen:2
Ctrl = digest:sha256
Input="0123456789ABCDEF0123456789ABCDEF"
Output=4DE433D5844043EF08D354DA03CB29068780D52706D7D1E4D50EFB7D58C9D547D83A747DDD0635A96B28F854E50145518482CB49E963054621B53C60C498D07C16E9C2789C893CF38D4D86900DE71BDE463BD2761D1271E358C7480A1AC0BAB930DDF39602AD1BC165B5D7436B516B7A7858E8EB7AB1C420EEB482F4D207F0E462B1724959320A084E13848D11D10FB593E66BF680BF6D3F345FC3E9C3DE60ABBAC37E1C6EC80A268C8D9FC49626C679097AA690BC1AA662B95EB8DB70390861AA0898229F9349B4B5FDD030D4928C47084708A933144BE23BD3C6E661B85B2C0EF9ED36D498D5B7320E8194D363D4AD478C059BAE804181965E0B81B663158A
Result = VERIFY_ERROR

# Verify using default parameters, explicitly setting parameters
Verify = RSA-PSS-DEFAULT
Ctrl = rsa_padding_mode:pss
Ctrl = rsa_pss_saltlen:20
Ctrl = digest:sha1
Input="0123456789ABCDEF0123"
Output = 3EFE09D88509027D837BFA5F8471CF7B69E6DF395DD999BB9CA42021F15722D9AC76670507C6BCFB73F64FB2211B611B8F140E76EBDB064BD762FDBA89D019E304A0D6B274E1C2FE1DF50005598A0306AF805416094E2A5BA60BC72BDE38CE061E853ED40F14967A8B9CA4DC739B462F89558F12FDF2D8D19FBEF16AD66FE2DDDA8BEE983ECBD873064244849D8D94B5B33F45E076871A47ED653E73257A2BE2DB3C0878094B0D2B6B682C8007DFD989425FB39A1FEEC9EED5876414601A49176EC344F5E3EDEE81CA2DDD29B7364F4638112CB3A547E2BC170E28CB66BDABE863754BE8AD5BA230567B575266F4B6B4CF81F28310ABF05351CC9E2DB85D00BF

# Verify explicitly setting parameters "digest" salt length
Verify = RSA-PSS-DEFAULT
Ctrl = rsa_padding_mode:pss
Ctrl = rsa_pss_saltlen:digest
Ctrl = digest:sha1
Input="0123456789ABCDEF0123"
Output = 3EFE09D88509027D837BFA5F8471CF7B69E6DF395DD999BB9CA42021F15722D9AC76670507C6BCFB73F64FB2211B611B8F140E76EBDB064BD762FDBA89D019E304A0D6B274E1C2FE1DF50005598A0306AF805416094E2A5BA60BC72BDE38CE061E853ED40F14967A8B9CA4DC739B462F89558F12FDF2D8D19FBEF16AD66FE2DDDA8BEE983ECBD873064244849D8D94B5B33F45E076871A47ED653E73257A2BE2DB3C0878094B0D2B6B682C8007DFD989425FB39A1FEEC9EED5876414601A49176EC344F5E3EDEE81CA2DDD29B7364F4638112CB3A547E2BC170E28CB66BDABE863754BE8AD5BA230567B575266F4B6B4CF81F28310ABF05351CC9E2DB85D00BF

# Verify using salt length larger than minimum
Verify = RSA-PSS-DEFAULT
Ctrl = rsa_pss_saltlen:30
Input="0123456789ABCDEF0123"
Output = 6BF7EDC63A0BA184EEEC7F3020FEC8F5EBF38C2B76481881F48BCCE5796E7AB294548BA9AE810457C7723CABD1BDE94CF59CF7C0FC7461B22760C8ED703DD98E97BFDD61FA8D1181C411F6DEE5FF159F4850746D78EDEE385A363DC28E2CB373D5CAD7953F3BD5E639BE345732C03A1BDEA268814DA036EB1891C82D4012F3B903D86636055F87B96FC98806AD1B217685A4D754046A5DE0B0D7870664BE07902153EC85BA457BE7D7F89D7FE0F626D02A9CBBB2BB479DDA1A5CAE75247FB7BF6BFB15C1D3FD9E6B1573CCDBC72011C3B97716058BB11C7EA2E4E56ADAFE1F5DE6A7FD405AC5890100F9C3408EFFB5C73BF73F48177FF743B4B819D0699D507B

# Verify using maximum salt length
Verify = RSA-PSS-DEFAULT
Ctrl = rsa_pss_saltlen:max
Input="0123456789ABCDEF0123"
Output = 4470DCFE812DEE2E58E4301D4ED274AB348FE040B724B2CD1D8CD0914BFF375F0B86FCB32BFA8AEA9BD22BD7C4F1ADD4F3D215A5CFCC99055BAFECFC23800E9BECE19A08C66BEBC5802122D13A732E5958FC228DCC0B49B5B4B1154F032D8FA2F3564AA949C1310CC9266B0C47F86D449AC9D2E7678347E7266E2D7C888CCE1ADF44A109A293F8516AE2BD94CE220F26E137DB8E7A66BB9FCE052CDC1D0BE24D8CEBB20D10125F26B069F117044B9E1D16FDDAABCA5340AE1702F37D0E1C08A2E93801C0A41035C6C73DA02A0E32227EAFB0B85E79107B59650D0EE7DC32A6772CCCE90F06369B2880FE87ED76997BA61F5EA818091EE88F8B0D6F24D02A3FC6

# Attempt to change salt length below minimum
Verify = RSA-PSS-DEFAULT
Ctrl = rsa_pss_saltlen:0
Result = PKEY_CTRL_ERROR

# Attempt to change padding mode
# Note this used to return PKEY_CTRL_INVALID
# but it is limited because setparams only returns 0 or 1.
Verify = RSA-PSS-DEFAULT
Ctrl = rsa_padding_mode:pkcs1
Result = PKEY_CTRL_ERROR

# Attempt to change digest
Verify = RSA-PSS-DEFAULT
Ctrl = digest:sha256
Result = PKEY_CTRL_ERROR

# Invalid key: rejected when we try to init
Verify = RSA-PSS-BAD
Result = KEYOP_INIT_ERROR
Reason = invalid salt length

# Invalid key: rejected when we try to init
Verify = RSA-PSS-BAD2
Result = KEYOP_INIT_ERROR
Reason = invalid salt length


# Additional RSA-PSS and RSA-OAEP tests converted from
# ftp://ftp.rsasecurity.com/pub/pkcs/pkcs-1/pkcs-1v2-1-vec.zip
Title = RSA PSS/OAEP (from RSASecurity FTP)

# 1024 bit key
PublicKey=RSA-PSS-1
-----BEGIN PUBLIC KEY-----
MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQClbkoOcBAXWJpRh9x+qEHRVvLs
DjatUqRN/rHmH3rZkdjFEFb/7bFitMDyg6EqiKOU3/Umq3KRy7MHzqv84LHf1c2V
CAltWyuLbfXWce9jd8CSHLI8Jwpw4lmOb/idGfEFrMLT8Ms18pKA4Thrb2TE7yLh
4fINDOjP+yJJvZohNwIDAQAB
-----END PUBLIC KEY-----

Verify=RSA-PSS-1
Ctrl = rsa_padding_mode:pss
Ctrl = rsa_mgf1_md:sha1
Input=cd8b6538cb8e8de566b68bd067569dbf1ee2718e
Output=9074308fb598e9701b2294388e52f971faac2b60a5145af185df5287b5ed2887e57ce7fd44dc8634e407c8e0e4360bc226f3ec227f9d9e54638e8d31f5051215df6ebb9c2f9579aa77598a38f914b5b9c1bd83c4e2f9f382a0d0aa3542ffee65984a601bc69eb28deb27dca12c82c2d4c3f66cd500f1ff2b994d8a4e30cbb33c

Verify=RSA-PSS-1
Ctrl = rsa_padding_mode:pss
Ctrl = rsa_mgf1_md:sha1
Input=e35befc17a1d160b9ce35fbd8eb16e7ee491d3fd
Output=3ef7f46e831bf92b32274142a585ffcefbdca7b32ae90d10fb0f0c729984f04ef29a9df0780775ce43739b97838390db0a5505e63de927028d9d29b219ca2c4517832558a55d694a6d25b9dab66003c4cccd907802193be5170d26147d37b93590241be51c25055f47ef62752cfbe21418fafe98c22c4d4d47724fdb5669e843

Verify=RSA-PSS-1
Ctrl = rsa_padding_mode:pss
Ctrl = rsa_mgf1_md:sha1
Input=0652ec67bcee30f9d2699122b91c19abdba89f91
Output=666026fba71bd3e7cf13157cc2c51a8e4aa684af9778f91849f34335d141c00154c4197621f9624a675b5abc22ee7d5baaffaae1c9baca2cc373b3f33e78e6143c395a91aa7faca664eb733afd14d8827259d99a7550faca501ef2b04e33c23aa51f4b9e8282efdb728cc0ab09405a91607c6369961bc8270d2d4f39fce612b1

Verify=RSA-PSS-1
Ctrl = rsa_padding_mode:pss
Ctrl = rsa_mgf1_md:sha1
Input=39c21c4cceda9c1adf839c744e1212a6437575ec
Output=4609793b23e9d09362dc21bb47da0b4f3a7622649a47d464019b9aeafe53359c178c91cd58ba6bcb78be0346a7bc637f4b873d4bab38ee661f199634c547a1ad8442e03da015b136e543f7ab07c0c13e4225b8de8cce25d4f6eb8400f81f7e1833b7ee6e334d370964ca79fdb872b4d75223b5eeb08101591fb532d155a6de87

Verify=RSA-PSS-1
Ctrl = rsa_padding_mode:pss
Ctrl = rsa_mgf1_md:sha1
Input=36dae913b77bd17cae6e7b09453d24544cebb33c
Output=1d2aad221ca4d31ddf13509239019398e3d14b32dc34dc5af4aeaea3c095af73479cf0a45e5629635a53a018377615b16cb9b13b3e09d671eb71e387b8545c5960da5a64776e768e82b2c93583bf104c3fdb23512b7b4e89f633dd0063a530db4524b01c3f384c09310e315a79dcd3d684022a7f31c865a664e316978b759fad

Verify=RSA-PSS-1
Ctrl = rsa_padding_mode:pss
Ctrl = rsa_mgf1_md:sha1
Input=45eef191f4f79c31fe5d2ede7e5098994e929d2d
Output=2a34f6125e1f6b0bf971e84fbd41c632be8f2c2ace7de8b6926e31ff93e9af987fbc06e51e9be14f5198f91f3f953bd67da60a9df59764c3dc0fe08e1cbef0b75f868d10ad3fba749fef59fb6dac46a0d6e504369331586f58e4628f39aa278982543bc0eeb537dc61958019b394fb273f215858a0a01ac4d650b955c67f4c58

PublicKey=RSA-PSS-9
-----BEGIN PUBLIC KEY-----
MIHfMA0GCSqGSIb3DQEBAQUAA4HNADCByQKBwQDmvWkqyWZFeQQD/dD1vri5v5Lt
EAB/w2UEZBndBsBcW1svSOz5ieTOJpEJl5y7QLSgrSTSJIPR7jFa1MyxU0JoNSaR
xST23Y5sKdIkzyRpc67IbFv2sUAahQ0bmtG7jLzsR7BvD4x/RdP8jzGSmcVDPdvC
swU7R97S7NSkyu/WFIM9yLtiLzF+0Ha4BX/o3j+ESArV6D5KYZBKTySPs5cCc1fh
0w5GMTmBXG/U/VrFuBcqRSMOy2MYoE8UVdhOWosCAwEAAQ==
-----END PUBLIC KEY-----

Verify=RSA-PSS-9
Ctrl = rsa_padding_mode:pss
Ctrl = rsa_mgf1_md:sha1
Input=2715a49b8b0012cd7aee84c116446e6dfe3faec0
Output=586107226c3ce013a7c8f04d1a6a2959bb4b8e205ba43a27b50f124111bc35ef589b039f5932187cb696d7d9a32c0c38300a5cdda4834b62d2eb240af33f79d13dfbf095bf599e0d9686948c1964747b67e89c9aba5cd85016236f566cc5802cb13ead51bc7ca6bef3b94dcbdbb1d570469771df0e00b1a8a06777472d2316279edae86474668d4e1efff95f1de61c6020da32ae92bbf16520fef3cf4d88f61121f24bbd9fe91b59caf1235b2a93ff81fc403addf4ebdea84934a9cdaf8e1a9e

Verify=RSA-PSS-9
Ctrl = rsa_padding_mode:pss
Ctrl = rsa_mgf1_md:sha1
Input=2dac956d53964748ac364d06595827c6b4f143cd
Output=80b6d643255209f0a456763897ac9ed259d459b49c2887e5882ecb4434cfd66dd7e1699375381e51cd7f554f2c271704b399d42b4be2540a0eca61951f55267f7c2878c122842dadb28b01bd5f8c025f7e228418a673c03d6bc0c736d0a29546bd67f786d9d692ccea778d71d98c2063b7a71092187a4d35af108111d83e83eae46c46aa34277e06044589903788f1d5e7cee25fb485e92949118814d6f2c3ee361489016f327fb5bc517eb50470bffa1afa5f4ce9aa0ce5b8ee19bf5501b958

Verify=RSA-PSS-9
Ctrl = rsa_padding_mode:pss
Ctrl = rsa_mgf1_md:sha1
Input=28d98c46cccafbd3bc04e72f967a54bd3ea12298
Output=484408f3898cd5f53483f80819efbf2708c34d27a8b2a6fae8b322f9240237f981817aca1846f1084daa6d7c0795f6e5bf1af59c38e1858437ce1f7ec419b98c8736adf6dd9a00b1806d2bd3ad0a73775e05f52dfef3a59ab4b08143f0df05cd1ad9d04bececa6daa4a2129803e200cbc77787caf4c1d0663a6c5987b605952019782caf2ec1426d68fb94ed1d4be816a7ed081b77e6ab330b3ffc073820fecde3727fcbe295ee61a050a343658637c3fd659cfb63736de32d9f90d3c2f63eca

Verify=RSA-PSS-9
Ctrl = rsa_padding_mode:pss
Ctrl = rsa_mgf1_md:sha1
Input=0866d2ff5a79f25ef668cd6f31b42dee421e4c0e
Output=84ebeb481be59845b46468bafb471c0112e02b235d84b5d911cbd1926ee5074ae0424495cb20e82308b8ebb65f419a03fb40e72b78981d88aad143053685172c97b29c8b7bf0ae73b5b2263c403da0ed2f80ff7450af7828eb8b86f0028bd2a8b176a4d228cccea18394f238b09ff758cc00bc04301152355742f282b54e663a919e709d8da24ade5500a7b9aa50226e0ca52923e6c2d860ec50ff480fa57477e82b0565f4379f79c772d5c2da80af9fbf325ece6fc20b00961614bee89a183e

Verify=RSA-PSS-9
Ctrl = rsa_padding_mode:pss
Ctrl = rsa_mgf1_md:sha1
Input=6a5b4be4cd36cc97dfde9995efbf8f097a4a991a
Output=82102df8cb91e7179919a04d26d335d64fbc2f872c44833943241de8454810274cdf3db5f42d423db152af7135f701420e39b494a67cbfd19f9119da233a23da5c6439b5ba0d2bc373eee3507001378d4a4073856b7fe2aba0b5ee93b27f4afec7d4d120921c83f606765b02c19e4d6a1a3b95fa4c422951be4f52131077ef17179729cddfbdb56950dbaceefe78cb16640a099ea56d24389eef10f8fecb31ba3ea3b227c0a86698bb89e3e9363905bf22777b2a3aa521b65b4cef76d83bde4c

Verify=RSA-PSS-9
Ctrl = rsa_padding_mode:pss
Ctrl = rsa_mgf1_md:sha1
Input=b9dfd1df76a461c51e6576c6c8ed0a923d1c50e7
Output=a7fdb0d259165ca2c88d00bbf1028a867d337699d061193b17a9648e14ccbbaadeacaacdec815e7571294ebb8a117af205fa078b47b0712c199e3ad05135c504c24b81705115740802487992ffd511d4afc6b854491eb3f0dd523139542ff15c3101ee85543517c6a3c79417c67e2dd9aa741e9a29b06dcb593c2336b3670ae3afbac7c3e76e215473e866e338ca244de00b62624d6b9426822ceae9f8cc460895f41250073fd45c5a1e7b425c204a423a699159f6903e710b37a7bb2bc8049f

PublicKey=RSA-PSS-10
-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEApd2GesTLAvkLlFfUjBSn
cO+ZHFbDnA7GX9Ea+ok3zqV7m+esc7RcABdhW4LWIuMYdTtgJ8D9FXvhL4CQ/uKn
rc0O73WfiLpJl8ekLVjJqhLLma4AH+UhwTu1QxRFqNWuT15MfpSKwifTYEBx8g5X
fpBfvrFd+vBtHeWuYlPWOmohILMaXaXavJVQYA4g8n03OeJieSX+o8xQnyHf8E5u
6kVJxUDWgJ/5MH7t6R//WHM9g4WiN9bTcFoz45GQCZIHDfet8TV89+NwDONmfeg/
F7jfF3jbOB3OCctK0FilEQAac4GY7ifPVaE7dUU5kGWC7IsXS9WNXR89dnxhNyGu
BQIDAQAB
-----END PUBLIC KEY-----

Verify=RSA-PSS-10
Ctrl = rsa_padding_mode:pss
Ctrl = rsa_mgf1_md:sha1
Input=9596bb630cf6a8d4ea4600422b9eba8b13675dd4
Output=82c2b160093b8aa3c0f7522b19f87354066c77847abf2a9fce542d0e84e920c5afb49ffdfdace16560ee94a1369601148ebad7a0e151cf16331791a5727d05f21e74e7eb811440206935d744765a15e79f015cb66c532c87a6a05961c8bfad741a9a6657022894393e7223739796c02a77455d0f555b0ec01ddf259b6207fd0fd57614cef1a5573baaff4ec00069951659b85f24300a25160ca8522dc6e6727e57d019d7e63629b8fe5e89e25cc15beb3a647577559299280b9b28f79b0409000be25bbd96408ba3b43cc486184dd1c8e62553fa1af4040f60663de7f5e49c04388e257f1ce89c95dab48a315d9b66b1b7628233876ff2385230d070d07e1666

Verify=RSA-PSS-10
Ctrl = rsa_padding_mode:pss
Ctrl = rsa_mgf1_md:sha1
Input=b503319399277fd6c1c8f1033cbf04199ea21716
Output=14ae35d9dd06ba92f7f3b897978aed7cd4bf5ff0b585a40bd46ce1b42cd2703053bb9044d64e813d8f96db2dd7007d10118f6f8f8496097ad75e1ff692341b2892ad55a633a1c55e7f0a0ad59a0e203a5b8278aec54dd8622e2831d87174f8caff43ee6c46445345d84a59659bfb92ecd4c818668695f34706f66828a89959637f2bf3e3251c24bdba4d4b7649da0022218b119c84e79a6527ec5b8a5f861c159952e23ec05e1e717346faefe8b1686825bd2b262fb2531066c0de09acde2e4231690728b5d85e115a2f6b92b79c25abc9bd9399ff8bcf825a52ea1f56ea76dd26f43baafa18bfa92a504cbd35699e26d1dcc5a2887385f3c63232f06f3244c3

Verify=RSA-PSS-10
Ctrl = rsa_padding_mode:pss
Ctrl = rsa_mgf1_md:sha1
Input=50aaede8536b2c307208b275a67ae2df196c7628
Output=6e3e4d7b6b15d2fb46013b8900aa5bbb3939cf2c095717987042026ee62c74c54cffd5d7d57efbbf950a0f5c574fa09d3fc1c9f513b05b4ff50dd8df7edfa20102854c35e592180119a70ce5b085182aa02d9ea2aa90d1df03f2daae885ba2f5d05afdac97476f06b93b5bc94a1a80aa9116c4d615f333b098892b25fface266f5db5a5a3bcc10a824ed55aad35b727834fb8c07da28fcf416a5d9b2224f1f8b442b36f91e456fdea2d7cfe3367268de0307a4c74e924159ed33393d5e0655531c77327b89821bdedf880161c78cd4196b5419f7acc3f13e5ebf161b6e7c6724716ca33b85c2e25640192ac2859651d50bde7eb976e51cec828b98b6563b86bb

Verify=RSA-PSS-10
Ctrl = rsa_padding_mode:pss
Ctrl = rsa_mgf1_md:sha1
Input=aa0b72b8b371ddd10c8ae474425ccccf8842a294
Output=34047ff96c4dc0dc90b2d4ff59a1a361a4754b255d2ee0af7d8bf87c9bc9e7ddeede33934c63ca1c0e3d262cb145ef932a1f2c0a997aa6a34f8eaee7477d82ccf09095a6b8acad38d4eec9fb7eab7ad02da1d11d8e54c1825e55bf58c2a23234b902be124f9e9038a8f68fa45dab72f66e0945bf1d8bacc9044c6f07098c9fcec58a3aab100c805178155f030a124c450e5acbda47d0e4f10b80a23f803e774d023b0015c20b9f9bbe7c91296338d5ecb471cafb032007b67a60be5f69504a9f01abb3cb467b260e2bce860be8d95bf92c0c8e1496ed1e528593a4abb6df462dde8a0968dffe4683116857a232f5ebf6c85be238745ad0f38f767a5fdbf486fb

Verify=RSA-PSS-10
Ctrl = rsa_padding_mode:pss
Ctrl = rsa_mgf1_md:sha1
Input=fad3902c9750622a2bc672622c48270cc57d3ea8
Output=7e0935ea18f4d6c1d17ce82eb2b3836c55b384589ce19dfe743363ac9948d1f346b7bfddfe92efd78adb21faefc89ade42b10f374003fe122e67429a1cb8cbd1f8d9014564c44d120116f4990f1a6e38774c194bd1b8213286b077b0499d2e7b3f434ab12289c556684deed78131934bb3dd6537236f7c6f3dcb09d476be07721e37e1ceed9b2f7b406887bd53157305e1c8b4f84d733bc1e186fe06cc59b6edb8f4bd7ffefdf4f7ba9cfb9d570689b5a1a4109a746a690893db3799255a0cb9215d2d1cd490590e952e8c8786aa0011265252470c041dfbc3eec7c3cbf71c24869d115c0cb4a956f56d530b80ab589acfefc690751ddf36e8d383f83cedd2cc

Verify=RSA-PSS-10
Ctrl = rsa_padding_mode:pss
Ctrl = rsa_mgf1_md:sha1
Input=122196deb5d122bd8c6fc781ff6924d7c695aade
Output=6d3b5b87f67ea657af21f75441977d2180f91b2c5f692de82955696a686730d9b9778d970758ccb26071c2209ffbd6125be2e96ea81b67cb9b9308239fda17f7b2b64ecda096b6b935640a5a1cb42a9155b1c9ef7a633a02c59f0d6ee59b852c43b35029e73c940ff0410e8f114eed46bbd0fae165e42be2528a401c3b28fd818ef3232dca9f4d2a0f5166ec59c42396d6c11dbc1215a56fa17169db9575343ef34f9de32a49cdc3174922f229c23e18e45df9353119ec4319cedce7a17c64088c1f6f52be29634100b3919d38f3d1ed94e6891e66a73b8fb849f5874df59459e298c7bbce2eee782a195aa66fe2d0732b25e595f57d3e061b1fc3e4063bf98f

PrivateKey=RSA-OAEP-1
****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
Decrypt=RSA-OAEP-1
Ctrl = rsa_padding_mode:oaep
Ctrl = rsa_mgf1_md:sha1
Input=354fe67b4a126d5d35fe36c777791a3f7ba13def484e2d3908aff722fad468fb21696de95d0be911c2d3174f8afcc201035f7b6d8e69402de5451618c21a535fa9d7bfc5b8dd9fc243f8cf927db31322d6e881eaa91a996170e657a05a266426d98c88003f8477c1227094a0d9fa1e8c4024309ce1ecccb5210035d47ac72e8a
Output=6628194e12073db03ba94cda9ef9532397d50dba79b987004afefe34

Decrypt=RSA-OAEP-1
Ctrl = rsa_padding_mode:oaep
Ctrl = rsa_mgf1_md:sha1
Input=640db1acc58e0568fe5407e5f9b701dff8c3c91e716c536fc7fcec6cb5b71c1165988d4a279e1577d730fc7a29932e3f00c81515236d8d8e31017a7a09df4352d904cdeb79aa583adcc31ea698a4c05283daba9089be5491f67c1a4ee48dc74bbbe6643aef846679b4cb395a352d5ed115912df696ffe0702932946d71492b44
Output=750c4047f547e8e41411856523298ac9bae245efaf1397fbe56f9dd5

Decrypt=RSA-OAEP-1
Ctrl = rsa_padding_mode:oaep
Ctrl = rsa_mgf1_md:sha1
Input=423736ed035f6026af276c35c0b3741b365e5f76ca091b4e8c29e2f0befee603595aa8322d602d2e625e95eb81b2f1c9724e822eca76db8618cf09c5343503a4360835b5903bc637e3879fb05e0ef32685d5aec5067cd7cc96fe4b2670b6eac3066b1fcf5686b68589aafb7d629b02d8f8625ca3833624d4800fb081b1cf94eb
Output=d94ae0832e6445ce42331cb06d531a82b1db4baad30f746dc916df24d4e3c2451fff59a6423eb0e1d02d4fe646cf699dfd818c6e97b051

Decrypt=RSA-OAEP-1
Ctrl = rsa_padding_mode:oaep
Ctrl = rsa_mgf1_md:sha1
Input=45ead4ca551e662c9800f1aca8283b0525e6abae30be4b4aba762fa40fd3d38e22abefc69794f6ebbbc05ddbb11216247d2f412fd0fba87c6e3acd888813646fd0e48e785204f9c3f73d6d8239562722dddd8771fec48b83a31ee6f592c4cfd4bc88174f3b13a112aae3b9f7b80e0fc6f7255ba880dc7d8021e22ad6a85f0755
Output=52e650d98e7f2a048b4f86852153b97e01dd316f346a19f67a85

Decrypt=RSA-OAEP-1
Ctrl = rsa_padding_mode:oaep
Ctrl = rsa_mgf1_md:sha1
Input=36f6e34d94a8d34daacba33a2139d00ad85a9345a86051e73071620056b920e219005855a213a0f23897cdcd731b45257c777fe908202befdd0b58386b1244ea0cf539a05d5d10329da44e13030fd760dcd644cfef2094d1910d3f433e1c7c6dd18bc1f2df7f643d662fb9dd37ead9059190f4fa66ca39e869c4eb449cbdc439
Output=8da89fd9e5f974a29feffb462b49180f6cf9e802

Decrypt=RSA-OAEP-1
Ctrl = rsa_padding_mode:oaep
Ctrl = rsa_mgf1_md:sha1
Input=42cee2617b1ecea4db3f4829386fbd61dafbf038e180d837c96366df24c097b4ab0fac6bdf590d821c9f10642e681ad05b8d78b378c0f46ce2fad63f74e0ad3df06b075d7eb5f5636f8d403b9059ca761b5c62bb52aa45002ea70baace08ded243b9d8cbd62a68ade265832b56564e43a6fa42ed199a099769742df1539e8255
Output=26521050844271

PrivateKey=RSA-OAEP-2
****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
Decrypt=RSA-OAEP-2
Ctrl = rsa_padding_mode:oaep
Ctrl = rsa_mgf1_md:sha1
Input=0181af8922b9fcb4d79d92ebe19815992fc0c1439d8bcd491398a0f4ad3a329a5bd9385560db532683c8b7da04e4b12aed6aacdf471c34c9cda891addcc2df3456653aa6382e9ae59b54455257eb099d562bbe10453f2b6d13c59c02e10f1f8abb5da0d0570932dacf2d0901db729d0fefcc054e70968ea540c81b04bcaefe720e
Output=8ff00caa605c702830634d9a6c3d42c652b58cf1d92fec570beee7

Decrypt=RSA-OAEP-2
Ctrl = rsa_padding_mode:oaep
Ctrl = rsa_mgf1_md:sha1
Input=018759ff1df63b2792410562314416a8aeaf2ac634b46f940ab82d64dbf165eee33011da749d4bab6e2fcd18129c9e49277d8453112b429a222a8471b070993998e758861c4d3f6d749d91c4290d332c7a4ab3f7ea35ff3a07d497c955ff0ffc95006b62c6d296810d9bfab024196c7934012c2df978ef299aba239940cba10245
Output=2d

Decrypt=RSA-OAEP-2
Ctrl = rsa_padding_mode:oaep
Ctrl = rsa_mgf1_md:sha1
Input=018802bab04c60325e81c4962311f2be7c2adce93041a00719c88f957575f2c79f1b7bc8ced115c706b311c08a2d986ca3b6a9336b147c29c6f229409ddec651bd1fdd5a0b7f610c9937fdb4a3a762364b8b3206b4ea485fd098d08f63d4aa8bb2697d027b750c32d7f74eaf5180d2e9b66b17cb2fa55523bc280da10d14be2053
Output=74fc88c51bc90f77af9d5e9a4a70133d4b4e0b34da3c37c7ef8e

Decrypt=RSA-OAEP-2
Ctrl = rsa_padding_mode:oaep
Ctrl = rsa_mgf1_md:sha1
Input=00a4578cbc176318a638fba7d01df15746af44d4f6cd96d7e7c495cbf425b09c649d32bf886da48fbaf989a2117187cafb1fb580317690e3ccd446920b7af82b31db5804d87d01514acbfa9156e782f867f6bed9449e0e9a2c09bcecc6aa087636965e34b3ec766f2fe2e43018a2fddeb140616a0e9d82e5331024ee0652fc7641
Output=a7eb2a5036931d27d4e891326d99692ffadda9bf7efd3e34e622c4adc085f721dfe885072c78a203b151739be540fa8c153a10f00a

Decrypt=RSA-OAEP-2
Ctrl = rsa_padding_mode:oaep
Ctrl = rsa_mgf1_md:sha1
Input=00ebc5f5fda77cfdad3c83641a9025e77d72d8a6fb33a810f5950f8d74c73e8d931e8634d86ab1246256ae07b6005b71b7f2fb98351218331ce69b8ffbdc9da08bbc9c704f876deb9df9fc2ec065cad87f9090b07acc17aa7f997b27aca48806e897f771d95141fe4526d8a5301b678627efab707fd40fbebd6e792a25613e7aec
Output=2ef2b066f854c33f3bdcbb5994a435e73d6c6c

Decrypt=RSA-OAEP-2
Ctrl = rsa_padding_mode:oaep
Ctrl = rsa_mgf1_md:sha1
Input=010839ec20c27b9052e55befb9b77e6fc26e9075d7a54378c646abdf51e445bd5715de81789f56f1803d9170764a9e93cb78798694023ee7393ce04bc5d8f8c5a52c171d43837e3aca62f609eb0aa5ffb0960ef04198dd754f57f7fbe6abf765cf118b4ca443b23b5aab266f952326ac4581100644325f8b721acd5d04ff14ef3a
Output=8a7fb344c8b6cb2cf2ef1f643f9a3218f6e19bba89c0

PrivateKey=RSA-OAEP-3
****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
Decrypt=RSA-OAEP-3
Ctrl = rsa_padding_mode:oaep
Ctrl = rsa_mgf1_md:sha1
Input=026a0485d96aebd96b4382085099b962e6a2bdec3d90c8db625e14372de85e2d5b7baab65c8faf91bb5504fb495afce5c988b3f6a52e20e1d6cbd3566c5cd1f2b8318bb542cc0ea25c4aab9932afa20760eaddec784396a07ea0ef24d4e6f4d37e5052a7a31e146aa480a111bbe926401307e00f410033842b6d82fe5ce4dfae80
Output=087820b569e8fa8d

Decrypt=RSA-OAEP-3
Ctrl = rsa_padding_mode:oaep
Ctrl = rsa_mgf1_md:sha1
Input=024db89c7802989be0783847863084941bf209d761987e38f97cb5f6f1bc88da72a50b73ebaf11c879c4f95df37b850b8f65d7622e25b1b889e80fe80baca2069d6e0e1d829953fc459069de98ea9798b451e557e99abf8fe3d9ccf9096ebbf3e5255d3b4e1c6d2ecadf067a359eea86405acd47d5e165517ccafd47d6dbee4bf5
Output=4653acaf171960b01f52a7be63a3ab21dc368ec43b50d82ec3781e04

Decrypt=RSA-OAEP-3
Ctrl = rsa_padding_mode:oaep
Ctrl = rsa_mgf1_md:sha1
Input=0239bce681032441528877d6d1c8bb28aa3bc97f1df584563618995797683844ca86664732f4bed7a0aab083aaabfb7238f582e30958c2024e44e57043b97950fd543da977c90cdde5337d618442f99e60d7783ab59ce6dd9d69c47ad1e962bec22d05895cff8d3f64ed5261d92b2678510393484990ba3f7f06818ae6ffce8a3a
Output=d94cd0e08fa404ed89

Decrypt=RSA-OAEP-3
Ctrl = rsa_padding_mode:oaep
Ctrl = rsa_mgf1_md:sha1
Input=02994c62afd76f498ba1fd2cf642857fca81f4373cb08f1cbaee6f025c3b512b42c3e8779113476648039dbe0493f9246292fac28950600e7c0f32edf9c81b9dec45c3bde0cc8d8847590169907b7dc5991ceb29bb0714d613d96df0f12ec5d8d3507c8ee7ae78dd83f216fa61de100363aca48a7e914ae9f42ddfbe943b09d9a0
Output=6cc641b6b61e6f963974dad23a9013284ef1

Decrypt=RSA-OAEP-3
Ctrl = rsa_padding_mode:oaep
Ctrl = rsa_mgf1_md:sha1
Input=0162042ff6969592a6167031811a239834ce638abf54fec8b99478122afe2ee67f8c5b18b0339805bfdbc5a4e6720b37c59cfba942464c597ff532a119821545fd2e59b114e61daf71820529f5029cf524954327c34ec5e6f5ba7efcc4de943ab8ad4ed787b1454329f70db798a3a8f4d92f8274e2b2948ade627ce8ee33e43c60
Output=df5151832b61f4f25891fb4172f328d2eddf8371ffcfdbe997939295f30eca6918017cfda1153bf7a6af87593223

Decrypt=RSA-OAEP-3
Ctrl = rsa_padding_mode:oaep
Ctrl = rsa_mgf1_md:sha1
Input=00112051e75d064943bc4478075e43482fd59cee0679de6893eec3a943daa490b9691c93dfc0464b6623b9f3dbd3e70083264f034b374f74164e1a00763725e574744ba0b9db83434f31df96f6e2a26f6d8eba348bd4686c2238ac07c37aac3785d1c7eea2f819fd91491798ed8e9cef5e43b781b0e0276e37c43ff9492d005730
Output=3c3bad893c544a6d520ab022319188c8d504b7a788b850903b85972eaa18552e1134a7ad6098826254ff7ab672b3d8eb3158fac6d4cbaef1

PrivateKey=RSA-OAEP-4
****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
Decrypt=RSA-OAEP-4
Ctrl = rsa_padding_mode:oaep
Ctrl = rsa_mgf1_md:sha1
Input=04cce19614845e094152a3fe18e54e3330c44e5efbc64ae16886cb1869014cc5781b1f8f9e045384d0112a135ca0d12e9c88a8e4063416deaae3844f60d6e96fe155145f4525b9a34431ca3766180f70e15a5e5d8e8b1a516ff870609f13f896935ced188279a58ed13d07114277d75c6568607e0ab092fd803a223e4a8ee0b1a8
Output=4a86609534ee434a6cbca3f7e962e76d455e3264c19f605f6e5ff6137c65c56d7fb344cd52bc93374f3d166c9f0c6f9c506bad19330972d2

Decrypt=RSA-OAEP-4
Ctrl = rsa_padding_mode:oaep
Ctrl = rsa_mgf1_md:sha1
Input=0097b698c6165645b303486fbf5a2a4479c0ee85889b541a6f0b858d6b6597b13b854eb4f839af03399a80d79bda6578c841f90d645715b280d37143992dd186c80b949b775cae97370e4ec97443136c6da484e970ffdb1323a20847821d3b18381de13bb49aaea66530c4a4b8271f3eae172cd366e07e6636f1019d2a28aed15e
Output=b0adc4f3fe11da59ce992773d9059943c03046497ee9d9f9a06df1166db46d98f58d27ec074c02eee6cbe2449c8b9fc5080c5c3f4433092512ec46aa793743c8

Decrypt=RSA-OAEP-4
Ctrl = rsa_padding_mode:oaep
Ctrl = rsa_mgf1_md:sha1
Input=0301f935e9c47abcb48acbbe09895d9f5971af14839da4ff95417ee453d1fd77319072bb7297e1b55d7561cd9d1bb24c1a9a37c619864308242804879d86ebd001dce5183975e1506989b70e5a83434154d5cbfd6a24787e60eb0c658d2ac193302d1192c6e622d4a12ad4b53923bca246df31c6395e37702c6a78ae081fb9d065
Output=bf6d42e701707b1d0206b0c8b45a1c72641ff12889219a82bdea965b5e79a96b0d0163ed9d578ec9ada20f2fbcf1ea3c4089d83419ba81b0c60f3606da99

Decrypt=RSA-OAEP-4
Ctrl = rsa_padding_mode:oaep
Ctrl = rsa_mgf1_md:sha1
Input=02d110ad30afb727beb691dd0cf17d0af1a1e7fa0cc040ec1a4ba26a42c59d0a796a2e22c8f357ccc98b6519aceb682e945e62cb734614a529407cd452bee3e44fece8423cc19e55548b8b994b849c7ecde4933e76037e1d0ce44275b08710c68e430130b929730ed77e09b015642c5593f04e4ffb9410798102a8e96ffdfe11e4
Output=fb2ef112f5e766eb94019297934794f7be2f6fc1c58e

Decrypt=RSA-OAEP-4
Ctrl = rsa_padding_mode:oaep
Ctrl = rsa_mgf1_md:sha1
Input=00dbb8a7439d90efd919a377c54fae8fe11ec58c3b858362e23ad1b8a44310799066b99347aa525691d2adc58d9b06e34f288c170390c5f0e11c0aa3645959f18ee79e8f2be8d7ac5c23d061f18dd74b8c5f2a58fcb5eb0c54f99f01a83247568292536583340948d7a8c97c4acd1e98d1e29dc320e97a260532a8aa7a758a1ec2
Output=28ccd447bb9e85166dabb9e5b7d1adadc4b9d39f204e96d5e440ce9ad928bc1c2284

Decrypt=RSA-OAEP-4
Ctrl = rsa_padding_mode:oaep
Ctrl = rsa_mgf1_md:sha1
Input=00a5ffa4768c8bbecaee2db77e8f2eec99595933545520835e5ba7db9493d3e17cddefe6a5f567624471908db4e2d83a0fbee60608fc84049503b2234a07dc83b27b22847ad8920ff42f674ef79b76280b00233d2b51b8cb2703a9d42bfbc8250c96ec32c051e57f1b4ba528db89c37e4c54e27e6e64ac69635ae887d9541619a9
Output=f22242751ec6b1

PrivateKey=RSA-OAEP-5
********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
Decrypt=RSA-OAEP-5
Ctrl = rsa_padding_mode:oaep
Ctrl = rsa_mgf1_md:sha1
Input=036046a4a47d9ed3ba9a89139c105038eb7492b05a5d68bfd53accff4597f7a68651b47b4a4627d927e485eed7b4566420e8b409879e5d606eae251d22a5df799f7920bfc117b992572a53b1263146bcea03385cc5e853c9a101c8c3e1bda31a519807496c6cb5e5efb408823a352b8fa0661fb664efadd593deb99fff5ed000e5
Output=af71a901e3a61d3132f0fc1fdb474f9ea6579257ffc24d164170145b3dbde8

Decrypt=RSA-OAEP-5
Ctrl = rsa_padding_mode:oaep
Ctrl = rsa_mgf1_md:sha1
Input=03d6eb654edce615bc59f455265ed4e5a18223cbb9be4e4069b473804d5de96f54dcaaa603d049c5d94aa1470dfcd2254066b7c7b61ff1f6f6770e3215c51399fd4e34ec5082bc48f089840ad04354ae66dc0f1bd18e461a33cc1258b443a2837a6df26759aa2302334986f87380c9cc9d53be9f99605d2c9a97da7b0915a4a7ad
Output=a3b844a08239a8ac41605af17a6cfda4d350136585903a417a79268760519a4b4ac3303ec73f0f87cfb32399

Decrypt=RSA-OAEP-5
Ctrl = rsa_padding_mode:oaep
Ctrl = rsa_mgf1_md:sha1
Input=0770952181649f9f9f07ff626ff3a22c35c462443d905d456a9fd0bff43cac2ca7a9f554e9478b9acc3ac838b02040ffd3e1847de2e4253929f9dd9ee4044325a9b05cabb808b2ee840d34e15d105a3f1f7b27695a1a07a2d73fe08ecaaa3c9c9d4d5a89ff890d54727d7ae40c0ec1a8dd86165d8ee2c6368141016a48b55b6967
Output=308b0ecbd2c76cb77fc6f70c5edd233fd2f20929d629f026953bb62a8f4a3a314bde195de85b5f816da2aab074d26cb6acddf323ae3b9c678ac3cf12fbdde7

Decrypt=RSA-OAEP-5
Ctrl = rsa_padding_mode:oaep
Ctrl = rsa_mgf1_md:sha1
Input=0812b76768ebcb642d040258e5f4441a018521bd96687e6c5e899fcd6c17588ff59a82cc8ae03a4b45b31299af1788c329f7dcd285f8cf4ced82606b97612671a45bedca133442144d1617d114f802857f0f9d739751c57a3f9ee400912c61e2e6992be031a43dd48fa6ba14eef7c422b5edc4e7afa04fdd38f402d1c8bb719abf
Output=15c5b9ee1185

Decrypt=RSA-OAEP-5
Ctrl = rsa_padding_mode:oaep
Ctrl = rsa_mgf1_md:sha1
Input=07b60e14ec954bfd29e60d0047e789f51d57186c63589903306793ced3f68241c743529aba6a6374f92e19e0163efa33697e196f7661dfaaa47aac6bde5e51deb507c72c589a2ca1693d96b1460381249b2cdb9eac44769f2489c5d3d2f99f0ee3c7ee5bf64a5ac79c42bd433f149be8cb59548361640595513c97af7bc2509723
Output=21026e6800c7fa728fcaaba0d196ae28d7a2ac4ffd8abce794f0985f60c8a6737277365d3fea11db8923a2029a

Decrypt=RSA-OAEP-5
Ctrl = rsa_padding_mode:oaep
Ctrl = rsa_mgf1_md:sha1
Input=08c36d4dda33423b2ed6830d85f6411ba1dcf470a1fae0ebefee7c089f256cef74cb96ea69c38f60f39abee44129bcb4c92de7f797623b20074e3d9c2899701ed9071e1efa0bdd84d4c3e5130302d8f0240baba4b84a71cc032f2235a5ff0fae277c3e8f9112bef44c9ae20d175fc9a4058bfc930ba31b02e2e4f444483710f24a
Output=541e37b68b6c8872b84c02

PrivateKey=RSA-OAEP-6
********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
Decrypt=RSA-OAEP-6
Ctrl = rsa_padding_mode:oaep
Ctrl = rsa_mgf1_md:sha1
Input=0630eebcd2856c24f798806e41f9e67345eda9ceda386acc9facaea1eeed06ace583709718d9d169fadf414d5c76f92996833ef305b75b1e4b95f662a20faedc3bae0c4827a8bf8a88edbd57ec203a27a841f02e43a615bab1a8cac0701de34debdef62a088089b55ec36ea7522fd3ec8d06b6a073e6df833153bc0aefd93bd1a3
Output=4046ca8baa3347ca27f49e0d81f9cc1d71be9ba517d4

Decrypt=RSA-OAEP-6
Ctrl = rsa_padding_mode:oaep
Ctrl = rsa_mgf1_md:sha1
Input=0ebc37376173a4fd2f89cc55c2ca62b26b11d51c3c7ce49e8845f74e7607317c436bc8d23b9667dfeb9d087234b47bc6837175ae5c0559f6b81d7d22416d3e50f4ac533d8f0812f2db9e791fe9c775ac8b6ad0f535ad9ceb23a4a02014c58ab3f8d3161499a260f39348e714ae2a1d3443208fd8b722ccfdfb393e98011f99e63f
Output=5cc72c60231df03b3d40f9b57931bc31109f972527f28b19e7480c7288cb3c92b22512214e4be6c914792ddabdf57faa8aa7

Decrypt=RSA-OAEP-6
Ctrl = rsa_padding_mode:oaep
Ctrl = rsa_mgf1_md:sha1
Input=0a98bf1093619394436cf68d8f38e2f158fde8ea54f3435f239b8d06b8321844202476aeed96009492480ce3a8d705498c4c8c68f01501dc81db608f60087350c8c3b0bd2e9ef6a81458b7c801b89f2e4fe99d4900ba6a4b5e5a96d865dc676c7755928794130d6280a8160a190f2df3ea7cf9aa0271d88e9e6905ecf1c5152d65
Output=b20e651303092f4bccb43070c0f86d23049362ed96642fc5632c27db4a52e3d831f2ab068b23b149879c002f6bf3feee97591112562c

Decrypt=RSA-OAEP-6
Ctrl = rsa_padding_mode:oaep
Ctrl = rsa_mgf1_md:sha1
Input=008e7a67cacfb5c4e24bec7dee149117f19598ce8c45808fef88c608ff9cd6e695263b9a3c0ad4b8ba4c95238e96a8422b8535629c8d5382374479ad13fa39974b242f9a759eeaf9c83ad5a8ca18940a0162ba755876df263f4bd50c6525c56090267c1f0e09ce0899a0cf359e88120abd9bf893445b3cae77d3607359ae9a52f8
Output=684e3038c5c041f7

Decrypt=RSA-OAEP-6
Ctrl = rsa_padding_mode:oaep
Ctrl = rsa_mgf1_md:sha1
Input=00003474416c7b68bdf961c385737944d7f1f40cb395343c693cc0b4fe63b31fedf1eaeeac9ccc0678b31dc32e0977489514c4f09085f6298a9653f01aea4045ff582ee887be26ae575b73eef7f3774921e375a3d19adda0ca31aa1849887c1f42cac9677f7a2f4e923f6e5a868b38c084ef187594dc9f7f048fea2e02955384ab
Output=32488cb262d041d6e4dd35f987bf3ca696db1f06ac29a44693

Decrypt=RSA-OAEP-6
Ctrl = rsa_padding_mode:oaep
Ctrl = rsa_mgf1_md:sha1
Input=0a026dda5fc8785f7bd9bf75327b63e85e2c0fdee5dadb65ebdcac9ae1de95c92c672ab433aa7a8e69ce6a6d8897fac4ac4a54de841ae5e5bbce7687879d79634cea7a30684065c714d52409b928256bbf53eabcd5231eb7259504537399bd29164b726d33a46da701360a4168a091ccab72d44a62fed246c0ffea5b1348ab5470
Output=50ba14be8462720279c306ba

PrivateKey=RSA-OAEP-7
****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
Decrypt=RSA-OAEP-7
Ctrl = rsa_padding_mode:oaep
Ctrl = rsa_mgf1_md:sha1
Input=1688e4ce7794bba6cb7014169ecd559cede2a30b56a52b68d9fe18cf1973ef97b2a03153951c755f6294aa49adbdb55845ab6875fb3986c93ecf927962840d282f9e54ce8b690f7c0cb8bbd73440d9571d1b16cd9260f9eab4783cc482e5223dc60973871783ec27b0ae0fd47732cbc286a173fc92b00fb4ba6824647cd93c85c1
Output=47aae909

Decrypt=RSA-OAEP-7
Ctrl = rsa_padding_mode:oaep
Ctrl = rsa_mgf1_md:sha1
Input=1052ed397b2e01e1d0ee1c50bf24363f95e504f4a03434a08fd822574ed6b9736edbb5f390db10321479a8a139350e2bd4977c3778ef331f3e78ae118b268451f20a2f01d471f5d53c566937171b2dbc2d4bde459a5799f0372d6574239b2323d245d0bb81c286b63c89a361017337e4902f88a467f4c7f244bfd5ab46437ff3b6
Output=1d9b2e2223d9bc13bfb9f162ce735db48ba7c68f6822a0a1a7b6ae165834e7

Decrypt=RSA-OAEP-7
Ctrl = rsa_padding_mode:oaep
Ctrl = rsa_mgf1_md:sha1
Input=2155cd843ff24a4ee8badb7694260028a490813ba8b369a4cbf106ec148e5298707f5965be7d101c1049ea8584c24cd63455ad9c104d686282d3fb803a4c11c1c2e9b91c7178801d1b6640f003f5728df007b8a4ccc92bce05e41a27278d7c85018c52414313a5077789001d4f01910b72aad05d220aa14a58733a7489bc54556b
Output=d976fc

Decrypt=RSA-OAEP-7
Ctrl = rsa_padding_mode:oaep
Ctrl = rsa_mgf1_md:sha1
Input=0ab14c373aeb7d4328d0aaad8c094d88b9eb098b95f21054a29082522be7c27a312878b637917e3d819e6c3c568db5d843802b06d51d9e98a2be0bf40c031423b00edfbff8320efb9171bd2044653a4cb9c5122f6c65e83cda2ec3c126027a9c1a56ba874d0fea23f380b82cf240b8cf540004758c4c77d934157a74f3fc12bfac
Output=d4738623df223aa43843df8467534c41d013e0c803c624e263666b239bde40a5f29aeb8de79e3daa61dd0370f49bd4b013834b98212aef6b1c5ee373b3cb

Decrypt=RSA-OAEP-7
Ctrl = rsa_padding_mode:oaep
Ctrl = rsa_mgf1_md:sha1
Input=028387a318277434798b4d97f460068df5298faba5041ba11761a1cb7316b24184114ec500257e2589ed3b607a1ebbe97a6cc2e02bf1b681f42312a33b7a77d8e7855c4a6de03e3c04643f786b91a264a0d6805e2cea91e68177eb7a64d9255e4f27e713b7ccec00dc200ebd21c2ea2bb890feae4942df941dc3f97890ed347478
Output=bb47231ca5ea1d3ad46c99345d9a8a61

Decrypt=RSA-OAEP-7
Ctrl = rsa_padding_mode:oaep
Ctrl = rsa_mgf1_md:sha1
Input=14c678a94ad60525ef39e959b2f3ba5c097a94ff912b67dbace80535c187abd47d075420b1872152bba08f7fc31f313bbf9273c912fc4c0149a9b0cfb79807e346eb332069611bec0ff9bcd168f1f7c33e77313cea454b94e2549eecf002e2acf7f6f2d2845d4fe0aab2e5a92ddf68c480ae11247935d1f62574842216ae674115
Output=2184827095d35c3f86f600e8e59754013296

PrivateKey=RSA-OAEP-8
********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
Decrypt=RSA-OAEP-8
Ctrl = rsa_padding_mode:oaep
Ctrl = rsa_mgf1_md:sha1
Input=09b3683d8a2eb0fb295b62ed1fb9290b714457b7825319f4647872af889b30409472020ad12912bf19b11d4819f49614824ffd84d09c0a17e7d17309d12919790410aa2995699f6a86dbe3242b5acc23af45691080d6b1ae810fb3e3057087f0970092ce00be9562ff4053b6262ce0caa93e13723d2e3a5ba075d45f0d61b54b61
Output=050b755e5e6880f7b9e9d692a74c37aae449b31bfea6deff83747a897f6c2c825bb1adbf850a3c96994b5de5b33cbc7d4a17913a7967

Decrypt=RSA-OAEP-8
Ctrl = rsa_padding_mode:oaep
Ctrl = rsa_mgf1_md:sha1
Input=2ecf15c97c5a15b1476ae986b371b57a24284f4a162a8d0c8182e7905e792256f1812ba5f83f1f7a130e42dcc02232844edc14a31a68ee97ae564a383a3411656424c5f62ddb646093c367be1fcda426cf00a06d8acb7e57776fbbd855ac3df506fc16b1d7c3f2110f3d8068e91e186363831c8409680d8da9ecd8cf1fa20ee39d
Output=4eb68dcd93ca9b19df111bd43608f557026fe4aa1d5cfac227a3eb5ab9548c18a06dded23f81825986b2fcd71109ecef7eff88873f075c2aa0c469f69c92bc

Decrypt=RSA-OAEP-8
Ctrl = rsa_padding_mode:oaep
Ctrl = rsa_mgf1_md:sha1
Input=4bc89130a5b2dabb7c2fcf90eb5d0eaf9e681b7146a38f3173a3d9cfec52ea9e0a41932e648a9d69344c50da763f51a03c95762131e8052254dcd2248cba40fd31667786ce05a2b7b531ac9dac9ed584a59b677c1a8aed8c5d15d68c05569e2be780bf7db638fd2bfd2a85ab276860f3777338fca989ffd743d13ee08e0ca9893f
Output=8604ac56328c1ab5ad917861

Decrypt=RSA-OAEP-8
Ctrl = rsa_padding_mode:oaep
Ctrl = rsa_mgf1_md:sha1
Input=2e456847d8fc36ff0147d6993594b9397227d577752c79d0f904fcb039d4d812fea605a7b574dd82ca786f93752348438ee9f5b5454985d5f0e1699e3e7ad175a32e15f03deb042ab9fe1dd9db1bb86f8c089ccb45e7ef0c5ee7ca9b7290ca6b15bed47039788a8a93ff83e0e8d6244c71006362deef69b6f416fb3c684383fbd0
Output=fdda5fbf6ec361a9d9a4ac68af216a0686f438b1e0e5c36b955f74e107f39c0dddcc

Decrypt=RSA-OAEP-8
Ctrl = rsa_padding_mode:oaep
Ctrl = rsa_mgf1_md:sha1
Input=1fb9356fd5c4b1796db2ebf7d0d393cc810adf6145defc2fce714f79d93800d5e2ac211ea8bbecca4b654b94c3b18b30dd576ce34dc95436ef57a09415645923359a5d7b4171ef22c24670f1b229d3603e91f76671b7df97e7317c97734476d5f3d17d21cf82b5ba9f83df2e588d36984fd1b584468bd23b2e875f32f68953f7b2
Output=4a5f4914bee25de3c69341de07

Decrypt=RSA-OAEP-8
Ctrl = rsa_padding_mode:oaep
Ctrl = rsa_mgf1_md:sha1
Input=3afd9c6600147b21798d818c655a0f4c9212db26d0b0dfdc2a7594ccb3d22f5bf1d7c3e112cd73fc7d509c7a8bafdd3c274d1399009f9609ec4be6477e453f075aa33db382870c1c3409aef392d7386ae3a696b99a94b4da0589447e955d16c98b17602a59bd736279fcd8fb280c4462d590bfa9bf13fed570eafde97330a2c210
Output=8e07d66f7b880a72563abcd3f35092bc33409fb7f88f2472be

PrivateKey=RSA-OAEP-9
**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
Decrypt=RSA-OAEP-9
Ctrl = rsa_padding_mode:oaep
Ctrl = rsa_mgf1_md:sha1
Input=267bcd118acab1fc8ba81c85d73003cb8610fa55c1d97da8d48a7c7f06896a4db751aa284255b9d36ad65f37653d829f1b37f97b8001942545b2fc2c55a7376ca7a1be4b1760c8e05a33e5aa2526b8d98e317088e7834c755b2a59b12631a182c05d5d43ab1779264f8456f515ce57dfdf512d5493dab7b7338dc4b7d78db9c091ac3baf537a69fc7f549d979f0eff9a94fda4169bd4d1d19a69c99e33c3b55490d501b39b1edae118ff6793a153261584d3a5f39f6e682e3d17c8cd1261fa72
Output=f735fd55ba92592c3b52b8f9c4f69aaa1cbef8fe88add095595412467f9cf4ec0b896c59eda16210e7549c8abb10cdbc21a12ec9b6b5b8fd2f10399eb6

Decrypt=RSA-OAEP-9
Ctrl = rsa_padding_mode:oaep
Ctrl = rsa_mgf1_md:sha1
Input=93ac9f0671ec29acbb444effc1a5741351d60fdb0e393fbf754acf0de49761a14841df7772e9bc82773966a1584c4d72baea00118f83f35cca6e537cbd4d811f5583b29783d8a6d94cd31be70d6f526c10ff09c6fa7ce069795a3fcd0511fd5fcb564bcc80ea9c78f38b80012539d8a4ddf6fe81e9cddb7f50dbbbbcc7e5d86097ccf4ec49189fb8bf318be6d5a0715d516b49af191258cd32dc833ce6eb4673c03a19bbace88cc54895f636cc0c1ec89096d11ce235a265ca1764232a689ae8
Output=81b906605015a63aabe42ddf11e1978912f5404c7474b26dce3ed482bf961ecc818bf420c54659

Decrypt=RSA-OAEP-9
Ctrl = rsa_padding_mode:oaep
Ctrl = rsa_mgf1_md:sha1
Input=81ebdd95054b0c822ef9ad7693f5a87adfb4b4c4ce70df2df84ed49c04da58ba5fc20a19e1a6e8b7a3900b22796dc4e869ee6b42792d15a8eceb56c09c69914e813cea8f6931e4b8ed6f421af298d595c97f4789c7caa612c7ef360984c21b93edc5401068b5af4c78a8771b984d53b8ea8adf2f6a7d4a0ba76c75e1dd9f658f20ded4a46071d46d7791b56803d8fea7f0b0f8e41ae3f09383a6f9585fe7753eaaffd2bf94563108beecc207bbb535f5fcc705f0dde9f708c62f49a9c90371d3
Output=fd326429df9b890e09b54b18b8f34f1e24

Decrypt=RSA-OAEP-9
Ctrl = rsa_padding_mode:oaep
Ctrl = rsa_mgf1_md:sha1
Input=bcc35f94cde66cb1136625d625b94432a35b22f3d2fa11a613ff0fca5bd57f87b902ccdc1cd0aebcb0715ee869d1d1fe395f6793003f5eca465059c88660d446ff5f0818552022557e38c08a67ead991262254f10682975ec56397768537f4977af6d5f6aaceb7fb25dec5937230231fd8978af49119a29f29e424ab8272b47562792d5c94f774b8829d0b0d9f1a8c9eddf37574d5fa248eefa9c5271fc5ec2579c81bdd61b410fa61fe36e424221c113addb275664c801d34ca8c6351e4a858
Output=f1459b5f0c92f01a0f723a2e5662484d8f8c0a20fc29dad6acd43bb5f3effdf4e1b63e07fdfe6628d0d74ca19bf2d69e4a0abf86d293925a796772f8088e

Decrypt=RSA-OAEP-9
Ctrl = rsa_padding_mode:oaep
Ctrl = rsa_mgf1_md:sha1
Input=232afbc927fa08c2f6a27b87d4a5cb09c07dc26fae73d73a90558839f4fd66d281b87ec734bce237ba166698ed829106a7de6942cd6cdce78fed8d2e4d81428e66490d036264cef92af941d3e35055fe3981e14d29cbb9a4f67473063baec79a1179f5a17c9c1832f2838fd7d5e59bb9659d56dce8a019edef1bb3accc697cc6cc7a778f60a064c7f6f5d529c6210262e003de583e81e3167b89971fb8c0e15d44fffef89b53d8d64dd797d159b56d2b08ea5307ea12c241bd58d4ee278a1f2e
Output=53e6e8c729d6f9c319dd317e74b0db8e4ccca25f3c8305746e137ac63a63ef3739e7b595abb96e8d55e54f7bd41ab433378ffb911d

Decrypt=RSA-OAEP-9
Ctrl = rsa_padding_mode:oaep
Ctrl = rsa_mgf1_md:sha1
Input=438cc7dc08a68da249e42505f8573ba60e2c2773d5b290f4cf9dff718e842081c383e67024a0f29594ea987b9d25e4b738f285970d195abb3a8c8054e3d79d6b9c9a8327ba596f1259e27126674766907d8d582ff3a8476154929adb1e6d1235b2ccb4ec8f663ba9cc670a92bebd853c8dbf69c6436d016f61add836e94732450434207f9fd4c43dec2a12a958efa01efe2669899b5e604c255c55fb7166de5589e369597bb09168c06dd5db177e06a1740eb2d5c82faeca6d92fcee9931ba9f
Output=b6b28ea2198d0c1008bc64

PrivateKey=RSA-OAEP-10
************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
Decrypt=RSA-OAEP-10
Ctrl = rsa_padding_mode:oaep
Ctrl = rsa_mgf1_md:sha1
Input=53ea5dc08cd260fb3b858567287fa91552c30b2febfba213f0ae87702d068d19bab07fe574523dfb42139d68c3c5afeee0bfe4cb7969cbf382b804d6e61396144e2d0e60741f8993c3014b58b9b1957a8babcd23af854f4c356fb1662aa72bfcc7e586559dc4280d160c126785a723ebeebeff71f11594440aaef87d10793a8774a239d4a04c87fe1467b9daf85208ec6c7255794a96cc29142f9a8bd418e3c1fd67344b0cd0829df3b2bec60253196293c6b34d3f75d32f213dd45c6273d505adf4cced1057cb758fc26aeefa441255ed4e64c199ee075e7f16646182fdb464739b68ab5daff0e63e9552016824f054bf4d3c8c90a97bb6b6553284eb429fcc
Output=8bba6bf82a6c0f86d5f1756e97956870b08953b06b4eb205bc1694ee

Decrypt=RSA-OAEP-10
Ctrl = rsa_padding_mode:oaep
Ctrl = rsa_mgf1_md:sha1
Input=a2b1a430a9d657e2fa1c2bb5ed43ffb25c05a308fe9093c01031795f5874400110828ae58fb9b581ce9dddd3e549ae04a0985459bde6c626594e7b05dc4278b2a1465c1368408823c85e96dc66c3a30983c639664fc4569a37fe21e5a195b5776eed2df8d8d361af686e750229bbd663f161868a50615e0c337bec0ca35fec0bb19c36eb2e0bbcc0582fa1d93aacdb061063f59f2ce1ee43605e5d89eca183d2acdfe9f81011022ad3b43a3dd417dac94b4e11ea81b192966e966b182082e71964607b4f8002f36299844a11f2ae0faeac2eae70f8f4f98088acdcd0ac556e9fccc511521908fad26f04c64201450305778758b0538bf8b5bb144a828e629795
Output=e6ad181f053b58a904f2457510373e57

Decrypt=RSA-OAEP-10
Ctrl = rsa_padding_mode:oaep
Ctrl = rsa_mgf1_md:sha1
Input=9886c3e6764a8b9a84e84148ebd8c3b1aa8050381a78f668714c16d9cfd2a6edc56979c535d9dee3b44b85c18be8928992371711472216d95dda98d2ee8347c9b14dffdff84aa48d25ac06f7d7e65398ac967b1ce90925f67dce049b7f812db0742997a74d44fe81dbe0e7a3feaf2e5c40af888d550ddbbe3bc20657a29543f8fc2913b9bd1a61b2ab2256ec409bbd7dc0d17717ea25c43f42ed27df8738bf4afc6766ff7aff0859555ee283920f4c8a63c4a7340cbafddc339ecdb4b0515002f96c932b5b79167af699c0ad3fccfdf0f44e85a70262bf2e18fe34b850589975e867ff969d48eabf212271546cdc05a69ecb526e52870c836f307bd798780ede
Output=510a2cf60e866fa2340553c94ea39fbc256311e83e94454b4124

Decrypt=RSA-OAEP-10
Ctrl = rsa_padding_mode:oaep
Ctrl = rsa_mgf1_md:sha1
Input=6318e9fb5c0d05e5307e1683436e903293ac4642358aaa223d7163013aba87e2dfda8e60c6860e29a1e92686163ea0b9175f329ca3b131a1edd3a77759a8b97bad6a4f8f4396f28cf6f39ca58112e48160d6e203daa5856f3aca5ffed577af499408e3dfd233e3e604dbe34a9c4c9082de65527cac6331d29dc80e0508a0fa7122e7f329f6cca5cfa34d4d1da417805457e008bec549e478ff9e12a763c477d15bbb78f5b69bd57830fc2c4ed686d79bc72a95d85f88134c6b0afe56a8ccfbc855828bb339bd17909cf1d70de3335ae07039093e606d655365de6550b872cd6de1d440ee031b61945f629ad8a353b0d40939e96a3c450d2a8d5eee9f678093c8
Output=bcdd190da3b7d300df9a06e22caae2a75f10c91ff667b7c16bde8b53064a2649a94045c9

Decrypt=RSA-OAEP-10
Ctrl = rsa_padding_mode:oaep
Ctrl = rsa_mgf1_md:sha1
Input=75290872ccfd4a4505660d651f56da6daa09ca1301d890632f6a992f3d565cee464afded40ed3b5be9356714ea5aa7655f4a1366c2f17c728f6f2c5a5d1f8e28429bc4e6f8f2cff8da8dc0e0a9808e45fd09ea2fa40cb2b6ce6ffff5c0e159d11b68d90a85f7b84e103b09e682666480c657505c0929259468a314786d74eab131573cf234bf57db7d9e66cc6748192e002dc0deea930585f0831fdcd9bc33d51f79ed2ffc16bcf4d59812fcebcaa3f9069b0e445686d644c25ccf63b456ee5fa6ffe96f19cdf751fed9eaf35957754dbf4bfea5216aa1844dc507cb2d080e722eba150308c2b5ff1193620f1766ecf4481bafb943bd292877f2136ca494aba0
Output=a7dd6c7dc24b46f9dd5f1e91ada4c3b3df947e877232a9

Decrypt=RSA-OAEP-10
Ctrl = rsa_padding_mode:oaep
Ctrl = rsa_mgf1_md:sha1
Input=2d207a73432a8fb4c03051b3f73b28a61764098dfa34c47a20995f8115aa6816679b557e82dbee584908c6e69782d7deb34dbd65af063d57fca76a5fd069492fd6068d9984d209350565a62e5c77f23038c12cb10c6634709b547c46f6b4a709bd85ca122d74465ef97762c29763e06dbc7a9e738c78bfca0102dc5e79d65b973f28240caab2e161a78b57d262457ed8195d53e3c7ae9da021883c6db7c24afdd2322eac972ad3c354c5fcef1e146c3a0290fb67adf007066e00428d2cec18ce58f9328698defef4b2eb5ec76918fde1c198cbb38b7afc67626a9aefec4322bfd90d2563481c9a221f78c8272c82d1b62ab914e1c69f6af6ef30ca5260db4a46
Output=eaf1a73a1b0c4609537de69cd9228bbcfb9a8ca8c6c3efaf056fe4a7f4634ed00b7c39ec6922d7b8ea2c04ebac

Title = RSA DigestSign and DigestVerify

DigestSign = SHA256
Key = RSA-2048
Input = "Hello World"
Output = ba8c24b86f18633767ed1778ef12d283a508d0bef32dd50b4a67cbd6b75df0f4ef6e69bfafbc809b01b93ab34aad9a33908644efca6eca04db1afda1016d1c1603183d2263597cf85ce5b7acd6a4872cbcc401b90b221d85aa0a2d0e1f159fc0843e0a55c47dc108c3f207d000e954605fabbb8c938050f280e29653aa1438109d02e53dfbdcb8cb9b46d372dd39ba7317a3f4c0020dba1ddd247b3d58addb1df7208785a62a8e3e4372c1fa6d24a17cd6413f7f5c046ba40a881c21875fde848b3b56fea7264430eca15b27c5c3b72fedcbcc124f8d939ffc11e6d3172c7eb491d378902093fcc3bf3a2835a1fcfabf457c13abf7b37f08595ed72332e27034

DigestSign = SHA256
Key = RSA-2048
Input = "Hello "
Input = "World"
Output = ba8c24b86f18633767ed1778ef12d283a508d0bef32dd50b4a67cbd6b75df0f4ef6e69bfafbc809b01b93ab34aad9a33908644efca6eca04db1afda1016d1c1603183d2263597cf85ce5b7acd6a4872cbcc401b90b221d85aa0a2d0e1f159fc0843e0a55c47dc108c3f207d000e954605fabbb8c938050f280e29653aa1438109d02e53dfbdcb8cb9b46d372dd39ba7317a3f4c0020dba1ddd247b3d58addb1df7208785a62a8e3e4372c1fa6d24a17cd6413f7f5c046ba40a881c21875fde848b3b56fea7264430eca15b27c5c3b72fedcbcc124f8d939ffc11e6d3172c7eb491d378902093fcc3bf3a2835a1fcfabf457c13abf7b37f08595ed72332e27034

DigestSign = SHA256
Key = RSA-2048
Input = "Hello "
Input = "World"
Ctrl = rsa_padding_mode:pss
Ctrl = rsa_pss_saltlen:0
Output = 4a35cc7623f176c997696213045024f1b1121a6ec4a5755d206c20fc4a7c5259566d19730f6f1a75ac00878c6290e6757510588d740da3633b09a1d899c7dfba2031cfcae6a490e995c87f4750ea88948009cbed6c80cebb9ebfab7d04805e7a2140373fb888b5e6151d1c4eb7f505c4e0a584c17c6ca71e552ba13e1f20101796fe0d1af0cde661fc47d904b5d3f127073471fe6dc7e78f5cd2a049d67e0c7c92184e2bf97f8e16b50b8385daa1f8882e8f6c8683720903454b35356058f2f0136cad7689105167bacbe0dbad466ff2a298e41e5a65caecac4cde08529b7ea8717258e19b0732c966b34f5d52e4ec3073da78757471086553a3ff6c5460bcda

DigestVerify = SHA256
Key = RSA-2048-PUBLIC
Input = "Hello "
Input = "World"
Output = ba8c24b86f18633767ed1778ef12d283a508d0bef32dd50b4a67cbd6b75df0f4ef6e69bfafbc809b01b93ab34aad9a33908644efca6eca04db1afda1016d1c1603183d2263597cf85ce5b7acd6a4872cbcc401b90b221d85aa0a2d0e1f159fc0843e0a55c47dc108c3f207d000e954605fabbb8c938050f280e29653aa1438109d02e53dfbdcb8cb9b46d372dd39ba7317a3f4c0020dba1ddd247b3d58addb1df7208785a62a8e3e4372c1fa6d24a17cd6413f7f5c046ba40a881c21875fde848b3b56fea7264430eca15b27c5c3b72fedcbcc124f8d939ffc11e6d3172c7eb491d378902093fcc3bf3a2835a1fcfabf457c13abf7b37f08595ed72332e27034

DigestVerify = SHA256
Key = RSA-2048-PUBLIC
Input = "Hello"
Input = "World"
Output = ba8c24b86f18633767ed1778ef12d283a508d0bef32dd50b4a67cbd6b75df0f4ef6e69bfafbc809b01b93ab34aad9a33908644efca6eca04db1afda1016d1c1603183d2263597cf85ce5b7acd6a4872cbcc401b90b221d85aa0a2d0e1f159fc0843e0a55c47dc108c3f207d000e954605fabbb8c938050f280e29653aa1438109d02e53dfbdcb8cb9b46d372dd39ba7317a3f4c0020dba1ddd247b3d58addb1df7208785a62a8e3e4372c1fa6d24a17cd6413f7f5c046ba40a881c21875fde848b3b56fea7264430eca15b27c5c3b72fedcbcc124f8d939ffc11e6d3172c7eb491d378902093fcc3bf3a2835a1fcfabf457c13abf7b37f08595ed72332e27034
Result = VERIFY_ERROR

DigestSign = SHA256
Key = RSA-2048
Input = "Hello "
Input = "World"
Output = ba8c24b86f18633767ed1778ef12d283a508d0bef32dd50b4a67cbd6b75df0f4ef6e69bfafbc809b01b93ab34aad9a33908644efca6eca04db1afda1016d1c1603183d2263597cf85ce5b7acd6a4872cbcc401b90b221d85aa0a2d0e1f159fc0843e0a55c47dc108c3f207d000e954605fabbb8c938050f280e29653aa1438109d02e53dfbdcb8cb9b46d372dd39ba7317a3f4c0020dba1ddd247b3d58addb1df7208785a62a8e3e4372c1fa6d24a17cd6413f7f5c046ba40a881c21875fde848b3b56fea7264430eca15b27c5c3b72fedcbcc124f8d939ffc11e6d3172c7eb491d378902093fcc3bf3a2835a1fcfabf457c13abf7b37f08595ed72332e27034

# As there dont seem to be test vectors for these - they were just generated
# to verify that all approved digests are supported.
Title = Test RSA with different digests

DigestSign = SHA224
Key = RSA-2048
Input = "Hello World"
Output = 4bae2cf892733233985102eb7117303e49994a8a97b3de64597c33f15a689502ba4dcea20f0ada460b262bd0c92db23dd090453f95debbfd7a835b51d1923ede76f4f66ba04d9ba0715b333a747c8d469283af653f286a307a3c46cd91f51ca2d597f9d3951fc068ff3ed45ba6b3540801159c7c890a252183de9e4aeb0512eb9ae681ac1d436c9efef9f265dcdf64ce67dce73d8a88bcb0008211bc6cd5493822818bfc208b8737ae38874184f834301de26e537694cfdc0abaccc2702820ee58cbba243aac685681329705999f4d77e596615293eb642ec450f149caabfab55092fc0315ec4157d322813ba9790c0f46c6805b8e1c0f21b6211c3529e6d7cc

DigestSign = SHA384
Key = RSA-2048
Input = "Hello World"
Output = 041348caa7ffaa7dce1ac67567c408967a1736dd44ff73076570a63c4c7128ff09f716918b81fdfaa160368bc8b85c23466f8c0d4172da880386b75065f541c0fb6c9355a1731532ebab9cf5026cafe03a790bc1698586fbdff5f6e8f1a7148b07f1f895a99d0f041b8b36d8ffdd5e77ba36e5177610e0825ba78594f95826dabdc95f682da04edf0d133c55e78fa386dea9dbc1cf748e768e9116976476e75a84b5510d869beac9a1a9ffc579a28629154b24ee9df8d51fee479f4dca5646a2032c27206ad30f4937fc096c41362562bee74562132a4f471bb4b4c76e774be0e9eca9412809665ab13951d3699b3a7ccb6714a0f070345f6f3e037bd0f8ae52

DigestSign = SHA512
Key = RSA-2048
Input = "Hello World"
Output = 32e1f87fcd81a9d5a9f988087cdb3931fe0b12197d501b9c958cc1614a236e71a00ee7c96f7d5b3ae5ae3f11e40df5d06b0a818ee2a573aa1459f7f14dc22ca0b0b0ad2d47f4ab9e479baa084973b69f74f691ee700af102aceaf788e513f0e942a862bc6884ef1efb993b11d2ebd95139c61c37f8eac3edd94a4d8a358842f17da6d2971b8141a70097a1de569751eb4533e27880206fb8f8e1ac995688ca6facc85ed116becea9d3c4d0e96b2cb4b422b21c81328a335441d897c7deebf0f605ef086cfc5d81d1ab30ac04f674993511b142ea7ad43a8d3b7b1d2d26ce4a93413b690afdb1718408379fbd002a3c569bf11f0c10ce7cb46e257ce6b62ef62b

DigestSign = SHA512-224
Key = RSA-2048
Input = "Hello World"
Output = 8f20f0f2389408cfb7f4b47c299ca610c34a2ad20664378530ecf977c3243ce5f01c71296d2101dc962853a27f64a228eea92fee05780be31cdd7fe648d39f136de1dd857609d0d54d0de52c5cdf09d95986d808d1c92dcb3e8e96848af788c959927196316fcc56b884d34711a2a947b7e806caf7a2b05314f0c726b5e083baa0b341ac6515529bf6b3a7098a635c865e8d4b05af68fb3e893feb12ac7c4d667e1233f2e39449c285d4ad2a809e687f68ef66daf710e7d21e97833372c251bc0b45bc79c8bfe4087f16662875c2fc91a02d11078556a2b3232617406fbd947b9d95c193742ac4bb1858934ed7288cd0327c678b5295c682a4d2ba91d8fafae7

DigestSign = SHA512-256
Key = RSA-2048
Input = "Hello World"
Output = 00904130234e4e1a51060f7747ab26ca766c64cff1df97e4d0f432169046da422305ddea16b96e0d8fcc5ab651ac247d1b65283f2f27e7e97538e9f6b77f9558c9885d5ddacaf4d6ca600ad61693766498fa815361cd1debe2d71a5af77da71f8bd72d2f5cf741aa177a2dc44107f5887d95b217c67dbc5a4874f9aa8de1fc8a5645815c561edcb348db6458018096a99a98f5d2e677f068c9093618e28f532a97e8d6161aec002d5273884ce8b46f077d37b1e158de682a927126952bdbb6b6dfdf47d7e34a7d9c9b1395315586a11ceadfb72c021b28e28b8397bc9086418a45cb8c1ad46ef47f1c2bedb14b50e50b58bd607992e1d19b895119c6a62471b4

DigestSign = SHA3-224
Key = RSA-2048
Input = "Hello World"
Output = 5b6f6834356ba08ef649d822592d7a3d889995283e3752760b0728ed99aa05af901298159485a690f8dab21c850946ef2376bedcc87f403288039e1eb18dfdf4ac1075753528c94b5a8b11f74513ef64eddc215f7df97d977e788fd93db4063529fd87f583c1c98d39cd79178330bae7980d9a846acdf3bdd77a864f85b77941b375c076ff86def44b8da19b5de6131bb8894c870d4deeb62361bd429faad97530abab33795912d63f2fb886b25f1a1d9583fae3b7cf6dd6a5addd1b28320ab88a4dac2d938fb50b6c9115c7a0a4edd6b7b3453e435605839703f0b0c6e58f7bb73ef0fb38cab4f2d9dc1fd8b8b77b36ae945dea7563eaa33608781ce5ac181b

DigestSign = SHA3-256
Key = RSA-2048
Input = "Hello World"
Output = c531d0db6635b7c58c7118641565796c3d61323e303246cfc39e4e697727fede52b029407f82aa07e25c4833d18ec6ef6b585f3f078ca1494ae80a696fec701e1c2386196100cc5879fb8ea77f07b7019185258e1ad632c28cc672a61296787bb16a0e4f259d6eac1960f7eaaee1f7ab7eea3cf2d9e11435859630f0c71c48c8655b1252e072f18831731b2bc8e788a722c77db57583c7456e9b7fd5273e6781e214616620084dfefffe506910a39087912c3a8586fce65d8a0b1e5803ffe59b53537119653d708a55ef17d2721b9c8c183fe6750991b0e77b79d8d208ad750b2bc3426fbeeebf9b090bfc19f6534e44bf6d7d2f2e1dc2594b38ceda420553c9

DigestSign = SHA3-384
Key = RSA-2048
Input = "Hello World"
Output = 78bc11342fd44b4c047a365cc6ef78476bf28547da4eafec09f6bdf68a5593e993716662801b313515d80ddd232dfcc414cf896349a0237976dd89a97a9c88a92cd86992996d9c2747bf4e5dff5acb2fc67aa51ceb27ed1b95edd952d8eab3055cdc576a860593308c426f7ea73e01bd05fb20a4fcb64b10945e1684da67a8367826535344ad25057b4840e138909668c2423669b51f2fec631fd7e919f6e8b82ee293878894cac6a58b21f2168ba6335197e39e587fd60076a369f0b8e779c045c507654fbb98f5ffc998f720ab48bdbfe6d397aa63c7209b45068e24b65371180ff6d775851b2aaceac393082920fffb9e8b6999c682875bd0eff231dfaadf

DigestSign = SHA3-512
Key = RSA-2048
Input = "Hello World"
Output = 4681312b0e27e4e5f2dd3165ea5bdc98210d4883709a842239cef72099d71855fc322c3d2f74a7e7357647de153c22e4c4f9a0e4f1ce9b7f993242df5ee778ca949e6e184551e87e0aac8440ae990ef76a14f6056a16181390ad3a150bfdbb985cfe4cca3255b5eb0871116654d59fc89f69adb46c307b927c51a5f34069a2f43c854fde8e2df0ded10f8e257d1d800aa44bf31981f93c062128cc551ad7f5f7ba68e9d7c917c8fc51bcda3ce9192680c1e5308bd5c571ac0a5ae7347a897e8698ea94059562cd3035e3ed5fcf6c4ca8c20f58a079dfcdb175242a5726237d1b92378ec7dc4f2db09a0fbd62d1ab6d1a3d0be7afd34ed9c3ecd1bc5a3d2c448c

Title = Test RSA keypair mismatches

PrivateKey = RSA-2048-BIS
************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
PublicKey = RSA-2048-BIS-PUBLIC
-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAsrRB4OKs3ga6rtYWkn9M
TrbUHsm7q0SguDeCiSG7UG6KGEujOwvZr13c+LwOz7YF0ui+IA0x9gaWuar2wGEO
wm5rLJpHIjf26sTKnnLLjePbjERdmMuNFGAir/sL+nxmwrK3Mw3OZH0btl8yh3sA
ChfRGWJCizXy40/o0AKIHX5p0oQ0D1rrO7fvUW7mT64P4s/7q0Bn0xGq1vMhZeoy
XfPHIwEqxggsUwi9gS7opi60mrhfzWvStSZsbLfpalyoX+GGLX+QEYAwZ6Dn2Zdu
LbPbcAVw+oY34VYd8DjyS9/hCjjkQXJR4sUOOysfXq5m+oUA79AMkaF8qeicu4JC
8QIDAQAB
-----END PUBLIC KEY-----

PrivPubKeyPair = RSA-2048-BIS:RSA-2048-BIS-PUBLIC

PrivPubKeyPair = RSA-2048-BIS:RSA-2048-PUBLIC
Result = KEYPAIR_MISMATCH

Title = Test RSA keygen

# Key generation tests

# RSA-PSS with restrictions, should succeed.
KeyGen = RSASSA-PSS
KeyName = tmppss
Ctrl = rsa_pss_keygen_md:sha256
Ctrl = rsa_pss_keygen_mgf1_md:sha512

# Check MGF1 restrictions
DigestVerify = SHA256
Key = tmppss
Ctrl = rsa_mgf1_md:sha256
Result = PKEY_CTRL_ERROR

# Test valid digest and MGF1 parameters. Verify will fail
DigestVerify = SHA256
Key = tmppss
Ctrl = rsa_mgf1_md:sha512
Input = ""
Output = ""
Result = VERIFY_ERROR

# Check caching of key MGF1 digest restriction
DigestVerify = SHA256
Key = tmppss
Ctrl = rsa_mgf1_md:sha1
Result = PKEY_CTRL_ERROR

Title = RSA FIPS tests

# FIPS tests

# Verifying with SHA1 is permitted in fips mode for older applications
DigestVerify = SHA1
Key = RSA-2048
Input = "Hello "
Output = 87ea0e2226ef35e5a2aec9ca1222fcbe39ba723f05b3203564f671dd3601271806ead3240e61d424359ee3b17bd3e32f54b82df83998a8ac4148410710361de0400f9ddf98278618fbc87747a0531972543e6e5f18ab2fdfbfda02952f6ac69690e43864690af271bf43d4be9705b303d4ff994ab3abd4d5851562b73e59be3edc01cec41a4cc13b68206329bad1a46c6608d3609e951faa321d0fdbc765d54e9a7c59248d2f67913c9903e932b769c9c8a45520cabea06e8c0b231dd3bcc7f7ec55b46b0157ccb5fc5011fa57353cd3df32edcbadcb8d168133cbd0acfb64444cb040e1298f621508a38f79e14ae8c2c5c857f90aa9d24ef5fc07d34bf23859

# Verifying with a 1024 bit key is permitted in fips mode for older applications
DigestVerify = SHA256
Key = RSA-1024
Input = "Hello"
Output = 80382819f51b197c42f9fc02a85198683d918059afc013ae155992442563dd2897008297fecb3a8d8cf9421d493a99bd427a628f17cc4a7c76d23dfad0619f4068403fa7351f6d5a92a631d670c04407f305a4b5cb492295754e73e9b7ad41459826d3619a61e90d4744bdaf0f24f2393ea9241e973600c2ed62b1a0a37c504e

# Signing with SHA1 is not allowed in fips mode
Availablein = fips
DigestSign = SHA1
Securitycheck = 1
Key = RSA-2048
Input = "Hello"
Result = DIGESTSIGNINIT_ERROR

# Signing with a 1024 bit key is not allowed in fips mode
Availablein = fips
DigestSign = SHA256
Securitycheck = 1
Key = RSA-1024
Input = "Hello"
Result = DIGESTSIGNINIT_ERROR

# Verifying with a legacy digest in fips mode is not allowed
Availablein = fips
DigestVerify = MD5
Securitycheck = 1
Key = RSA-2048
Input = "Hello"
Result = DIGESTVERIFYINIT_ERROR

# Verifying with a key smaller than 1024 bits in fips mode is not allowed
Availablein = fips
DigestVerify = SHA256
Securitycheck = 1
Key = RSA-512
Input = "Hello"
Result = DIGESTVERIFYINIT_ERROR
