#
# Copyright 2001-2021 The OpenSSL Project Authors. All Rights Reserved.
#
# Licensed under the Apache License 2.0 (the "License").  You may not use
# this file except in compliance with the License.  You can obtain a copy
# in the file LICENSE in the source distribution or at
# https://www.openssl.org/source/license.html

# Tests start with one of these keywords
#       Cipher Decrypt Derive Digest Encoding KDF MAC PBE
#       PrivPubKeyPair Sign Verify VerifyRecover
# and continue until a blank line. Lines starting with a pound sign are ignored.
# The keyword Availablein must appear before the test name if needed.

# Private keys used for PKEY operations.

# Any Tests that have keys < 2048 bits OR sign with SHA1 are in this file.

# RSA 2048 bit key.

PrivateKey = RSA-2048
************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
# Corresponding public key

PublicKey = RSA-2048-PUBLIC
-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAzQCB6nsq4eoG1Z98c9n/
uUoJYVwuS6fGNs7wjdNTPsMYVSWwFcdpuZp31nJb+cNTKptuX2Yn1fuFFgdo092p
y9NZdFEXF9w9MJ0vxH7kH5fjKtt/ndhkocR2emZuzXG8Gqz151F/SzhZT+qbBeQt
WtqZEgCAE+RTFqTZu47QhriNKHWLrK+SLUaoaLSF0jnJuusOK2RZJxD0Ky0eoKS0
gCwL7Ksyj4posAc721Rv7qmAnShJkSs5DBUyvH4px2WPgXX65G80My/4e8qz5AZJ
uYV3hp2g6nGDU/ByJ1SIaRNkh2DRIr5nbg/Eg90g/8Mb2pajGWbJqi51rQPeR+HE
TwIDAQAB
-----END PUBLIC KEY-----

PrivPubKeyPair = RSA-2048:RSA-2048-PUBLIC

Title = RSA tests

Sign = RSA-2048
Ctrl = digest:SHA1
Input = "0123456789ABCDEF1234"
Output = c09d402423cbf233d26cae21f954547bc43fe80fd41360a0336cfdbe9aedad05bef6fd2eaee6cd60089a52482d4809a238149520df3bdde4cb9e23d9307b05c0a6f327052325a29adf2cc95b66523be7024e2a585c3d4db15dfbe146efe0ecdc0402e33fe5d40324ee96c5c3edd374a15cdc0f5d84aa243c0f07e188c6518fbfceae158a9943be398e31097da81b62074f626eff738be6160741d5a26957a482b3251fd85d8df78b98148459de10aa93305dbb4a5230aa1da291a9b0e481918f99b7638d72bb687f97661d304ae145d64a474437a4ef39d7b8059332ddeb07e92bf6e0e3acaf8afedc93795e4511737ec1e7aab6d5bc9466afc950c1c17b48ad

# Digest too long
Sign = RSA-2048
Ctrl = digest:SHA1
Input = "0123456789ABCDEF12345"
Output = 00
Result = KEYOP_ERROR

# Digest too short
Sign = RSA-2048
Ctrl = digest:SHA1
Input = "0123456789ABCDEF12345"
Output = 00
Result = KEYOP_ERROR

VerifyRecover = RSA-2048
Ctrl = digest:sha1
Input = 49525db4d44c755e560cba980b1d85ea604b0e077fcadd4ba44072a3487bbddb835016200a7d8739cce2dc3223d9c20cbdd25059ab02277f1f21318efd18e21038ec89aa9d40680987129e8b41ba33bceb86518bdf47268b921cce2037acabca6575d832499538d6f40cdba0d40bd7f4d8ea6ca6e2eec87f294efc971407857f5d7db09f6a7b31e301f571c6d82a5e3d08d2bb3a36e673d28b910f5bec57f0fcc4d968fd7c94d0b9226dec17f5192ad8b42bcab6f26e1bea1fdc3b958199acb00f14ebcb2a352f3afcedd4c09000128a603bbeb9696dea13040445253972d46237a25c7845e3b464e6984c2348ea1f1210a9ff0b00d2d72b50db00c009bb39f9
Result = KEYOP_ERROR

# MD5/SHA-1 combination
Verify = RSA-2048
Ctrl = digest:MD5-SHA1
Input = "0123456789ABCDEF0123456789ABCDEF0123"
Output = 7b80e0d4d2a6b7f4b018ce164bc0be21a0604b1b05e91c6204372458b05a0e4dbf0b36b3f80dbf04b278ad1fcf7ff6d982d5ca5d98b13b68240d846d400b8db6675b1a5fcbe2256322c5f691378bc941785326030fa835d240e334e2a4d35b17c1149b59dbb6e6d53b44326ebfc371f754449d36bad3722c1878af1699bb0a00c28e37162f99aba550b7c333228a70c906e3701c519a460a14fac29ff164ca9413efd19b431b31a9ad2988662cdbda9cdcff85f294b4be2cf072caceb1d3f52642edafea2e1d1e495061f18b5b3a130d2242cec830e44d506590e5df69bb974879a35e6bdc1ad00e3e31b362f2f5cdeabd8a0dfddfdb66a7c43993a3e189b80d

VerifyRecover = RSA-2048
Ctrl = digest:MD5-SHA1
Input = 7b80e0d4d2a6b7f4b018ce164bc0be21a0604b1b05e91c6204372458b05a0e4dbf0b36b3f80dbf04b278ad1fcf7ff6d982d5ca5d98b13b68240d846d400b8db6675b1a5fcbe2256322c5f691378bc941785326030fa835d240e334e2a4d35b17c1149b59dbb6e6d53b44326ebfc371f754449d36bad3722c1878af1699bb0a00c28e37162f99aba550b7c333228a70c906e3701c519a460a14fac29ff164ca9413efd19b431b31a9ad2988662cdbda9cdcff85f294b4be2cf072caceb1d3f52642edafea2e1d1e495061f18b5b3a130d2242cec830e44d506590e5df69bb974879a35e6bdc1ad00e3e31b362f2f5cdeabd8a0dfddfdb66a7c43993a3e189b80d
Output = "0123456789ABCDEF0123456789ABCDEF0123"

# MD5/SHA-1 combination, digest mismatch
Verify = RSA-2048
Ctrl = digest:MD5-SHA1
Input = "000000000000000000000000000000000000"
Output = 7b80e0d4d2a6b7f4b018ce164bc0be21a0604b1b05e91c6204372458b05a0e4dbf0b36b3f80dbf04b278ad1fcf7ff6d982d5ca5d98b13b68240d846d400b8db6675b1a5fcbe2256322c5f691378bc941785326030fa835d240e334e2a4d35b17c1149b59dbb6e6d53b44326ebfc371f754449d36bad3722c1878af1699bb0a00c28e37162f99aba550b7c333228a70c906e3701c519a460a14fac29ff164ca9413efd19b431b31a9ad2988662cdbda9cdcff85f294b4be2cf072caceb1d3f52642edafea2e1d1e495061f18b5b3a130d2242cec830e44d506590e5df69bb974879a35e6bdc1ad00e3e31b362f2f5cdeabd8a0dfddfdb66a7c43993a3e189b80d
Result = VERIFY_ERROR

# MD5/SHA-1 combination, wrong signature digest length
Verify = RSA-2048
Ctrl = digest:MD5-SHA1
Input = "0123456789ABCDEF0123456789ABCDEF0123"
Output = 6c13511f97ffb8137545fce551a43cf2b5b3dbdd5c3ceaaccd4620a6a373f3c38cc523d95bbdd810c852743b981bc4393c6b0cdfb0da5e77a8cc0108b05ff95e0f4dd7a0125b7390af1408dca6ddefac3b05b768de7b0c3df3c74e5f102f62743d67813beee1777036078da4cff5b29f49f01a6df3a2e709c37a83737108517687fe754d9ee908cb36c55e88f67c0b537108707347d16049f5dfac3d400ea367222d36627937a7f822f451c3d2c2dbc9e2202bffd3dc1b22213e17270a6b657619c6f44cbf66b077d548cfc9e1a114f8b853412470f2bf8d828f04d0d9f1aef260d216acb0911329fb5bdc48c2be3b198bf6f96e1c3fb116ad4430140d0640d4
Result = VERIFY_ERROR

VerifyRecover = RSA-2048
Ctrl = digest:MD5-SHA1
Input = 6c13511f97ffb8137545fce551a43cf2b5b3dbdd5c3ceaaccd4620a6a373f3c38cc523d95bbdd810c852743b981bc4393c6b0cdfb0da5e77a8cc0108b05ff95e0f4dd7a0125b7390af1408dca6ddefac3b05b768de7b0c3df3c74e5f102f62743d67813beee1777036078da4cff5b29f49f01a6df3a2e709c37a83737108517687fe754d9ee908cb36c55e88f67c0b537108707347d16049f5dfac3d400ea367222d36627937a7f822f451c3d2c2dbc9e2202bffd3dc1b22213e17270a6b657619c6f44cbf66b077d548cfc9e1a114f8b853412470f2bf8d828f04d0d9f1aef260d216acb0911329fb5bdc48c2be3b198bf6f96e1c3fb116ad4430140d0640d4
Result = KEYOP_ERROR

# MD5/SHA-1 combination, wrong input digest length
Verify = RSA-2048
Ctrl = digest:MD5-SHA1
Input = "0123456789ABCDEF0123456789ABCDEF012"
Output = 7b80e0d4d2a6b7f4b018ce164bc0be21a0604b1b05e91c6204372458b05a0e4dbf0b36b3f80dbf04b278ad1fcf7ff6d982d5ca5d98b13b68240d846d400b8db6675b1a5fcbe2256322c5f691378bc941785326030fa835d240e334e2a4d35b17c1149b59dbb6e6d53b44326ebfc371f754449d36bad3722c1878af1699bb0a00c28e37162f99aba550b7c333228a70c906e3701c519a460a14fac29ff164ca9413efd19b431b31a9ad2988662cdbda9cdcff85f294b4be2cf072caceb1d3f52642edafea2e1d1e495061f18b5b3a130d2242cec830e44d506590e5df69bb974879a35e6bdc1ad00e3e31b362f2f5cdeabd8a0dfddfdb66a7c43993a3e189b80d
Result = VERIFY_ERROR

# MD5/SHA-1 combination, wrong input and signature digest length
Verify = RSA-2048
Ctrl = digest:MD5-SHA1
Input = "0123456789ABCDEF0123456789ABCDEF012"
Output = 6c13511f97ffb8137545fce551a43cf2b5b3dbdd5c3ceaaccd4620a6a373f3c38cc523d95bbdd810c852743b981bc4393c6b0cdfb0da5e77a8cc0108b05ff95e0f4dd7a0125b7390af1408dca6ddefac3b05b768de7b0c3df3c74e5f102f62743d67813beee1777036078da4cff5b29f49f01a6df3a2e709c37a83737108517687fe754d9ee908cb36c55e88f67c0b537108707347d16049f5dfac3d400ea367222d36627937a7f822f451c3d2c2dbc9e2202bffd3dc1b22213e17270a6b657619c6f44cbf66b077d548cfc9e1a114f8b853412470f2bf8d828f04d0d9f1aef260d216acb0911329fb5bdc48c2be3b198bf6f96e1c3fb116ad4430140d0640d4
Result = VERIFY_ERROR

# DigestInfo-wrapped MDC-2 signature
Availablein = legacy
Verify = RSA-2048
Ctrl = digest:MDC2
Input = "0123456789ABCDEF"
Output = 3a46e5e80635d3b5586187b44b08fd02ca0bd36a637a8afeb46a1c1eb18d05b3196e00edf85378109015bcd3d0cfcefc2919c5b8e3ac42884b360188b1395ed34df7d2749f36b91c320d290311d78b36f390481eff42ace0275385c05176d022e4b625cf0ed85082d4b25da9e8a86011f6ac1cb8d8b812cc2bbd6c240caa8445aa74f8e971c935dbf3447df0411eb9e5cdee0851d1e0fea7041916c77efc09dc54e8dd4b7ba8f8d85ef43d4f12abde99886f4ebd5f021fc1b476cc23dc6a94fbbe77c954eee496fb6b4b5c534daa4e819143ce8de511a8bcb65759750c17edaca6fb31ac271c1ca3a27705f780ae86c67009e76fcba9067dde3556ff59c44111

Availablein = legacy
VerifyRecover = RSA-2048
Ctrl = digest:MDC2
Input = 3a46e5e80635d3b5586187b44b08fd02ca0bd36a637a8afeb46a1c1eb18d05b3196e00edf85378109015bcd3d0cfcefc2919c5b8e3ac42884b360188b1395ed34df7d2749f36b91c320d290311d78b36f390481eff42ace0275385c05176d022e4b625cf0ed85082d4b25da9e8a86011f6ac1cb8d8b812cc2bbd6c240caa8445aa74f8e971c935dbf3447df0411eb9e5cdee0851d1e0fea7041916c77efc09dc54e8dd4b7ba8f8d85ef43d4f12abde99886f4ebd5f021fc1b476cc23dc6a94fbbe77c954eee496fb6b4b5c534daa4e819143ce8de511a8bcb65759750c17edaca6fb31ac271c1ca3a27705f780ae86c67009e76fcba9067dde3556ff59c44111
Output = "0123456789ABCDEF"

# Legacy OCTET STRING MDC-2 signature
Availablein = legacy
Verify = RSA-2048
Ctrl = digest:MDC2
Input = "0123456789ABCDEF"
Output = 6cde46bbfc6a3b772c3d884640709be9f2fb70fcf199c14eaff7811369ea99733f984a9c48cd372578fa37cedeef24c93286d6d64f438df051e625ab2e125a7d9974a76240873e43efc3acbcbdccc2ee63769cdbf983b334ccb982273315c222b3bbdc3e928ac8a141a7412f1f794cfcabcc069a2ae4975d7bb68bea145d789634c9e0b02d324b5efd599c9bf2b1d32d077aba59aa0ad4a82cbbb90eaa9214e4f57104cf049c4139e2ddecf6edf219cd986f4d79cf25128c58667562c9d22be0291430d6cc7dad977d56e08315fcec133ea95d8db550f89735b4d5f233eaff0c86fce2b99f3f508e920f882c31f3e13f8775a3c8fa585c4f4c69eca89f648b7e

Availablein = legacy
VerifyRecover = RSA-2048
Ctrl = digest:MDC2
Input = 6cde46bbfc6a3b772c3d884640709be9f2fb70fcf199c14eaff7811369ea99733f984a9c48cd372578fa37cedeef24c93286d6d64f438df051e625ab2e125a7d9974a76240873e43efc3acbcbdccc2ee63769cdbf983b334ccb982273315c222b3bbdc3e928ac8a141a7412f1f794cfcabcc069a2ae4975d7bb68bea145d789634c9e0b02d324b5efd599c9bf2b1d32d077aba59aa0ad4a82cbbb90eaa9214e4f57104cf049c4139e2ddecf6edf219cd986f4d79cf25128c58667562c9d22be0291430d6cc7dad977d56e08315fcec133ea95d8db550f89735b4d5f233eaff0c86fce2b99f3f508e920f882c31f3e13f8775a3c8fa585c4f4c69eca89f648b7e
Output = "0123456789ABCDEF"

# Legacy OCTET STRING MDC-2 signature, digest mismatch
Availablein = legacy
Verify = RSA-2048
Ctrl = digest:MDC2
Input = "0000000000000000"
Output = 6cde46bbfc6a3b772c3d884640709be9f2fb70fcf199c14eaff7811369ea99733f984a9c48cd372578fa37cedeef24c93286d6d64f438df051e625ab2e125a7d9974a76240873e43efc3acbcbdccc2ee63769cdbf983b334ccb982273315c222b3bbdc3e928ac8a141a7412f1f794cfcabcc069a2ae4975d7bb68bea145d789634c9e0b02d324b5efd599c9bf2b1d32d077aba59aa0ad4a82cbbb90eaa9214e4f57104cf049c4139e2ddecf6edf219cd986f4d79cf25128c58667562c9d22be0291430d6cc7dad977d56e08315fcec133ea95d8db550f89735b4d5f233eaff0c86fce2b99f3f508e920f882c31f3e13f8775a3c8fa585c4f4c69eca89f648b7e
Result = VERIFY_ERROR

# Legacy OCTET STRING MDC-2 signature, wrong input digest length
Availablein = legacy
Verify = RSA-2048
Ctrl = digest:MDC2
Input = "0123456789ABCDE"
Output = 6cde46bbfc6a3b772c3d884640709be9f2fb70fcf199c14eaff7811369ea99733f984a9c48cd372578fa37cedeef24c93286d6d64f438df051e625ab2e125a7d9974a76240873e43efc3acbcbdccc2ee63769cdbf983b334ccb982273315c222b3bbdc3e928ac8a141a7412f1f794cfcabcc069a2ae4975d7bb68bea145d789634c9e0b02d324b5efd599c9bf2b1d32d077aba59aa0ad4a82cbbb90eaa9214e4f57104cf049c4139e2ddecf6edf219cd986f4d79cf25128c58667562c9d22be0291430d6cc7dad977d56e08315fcec133ea95d8db550f89735b4d5f233eaff0c86fce2b99f3f508e920f882c31f3e13f8775a3c8fa585c4f4c69eca89f648b7e
Result = VERIFY_ERROR

# Legacy OCTET STRING MDC-2 signature, wrong signature digest length
Availablein = legacy
Verify = RSA-2048
Ctrl = digest:MDC2
Input = "0123456789ABCDEF"
Output = 08da512483ece70be57f28a75271612800ae30ffbadc62609bc88b80d497a1fc13c300fdfcab6dc80cf55373c10adcc249ae80479b87fa3e391a2cd4a74babd1c22a4976812d544dcd6729b161bbc48fd067cf635b05f9edaddaeb6f67f2117d6b54a23c5e6f08a246abfe0356a67d7f3929306515e6d9962f8ce205120ecdcd2d4e3783cd0b4a1f0196a1b13924d0d3649233312695c3c336ae04e0b1efddabcc878b57622db60f6f747a1124c38426dacf1425c92d304c2bb1052f987c1dd73e4cc4b20d23396d4f05f52f98cf5065c3fb7dc319425f1f6f1878b87f57afbd24fbff98909494581aadd04d80a639b85ce8684ea58409d8dbbbaacf256bb5c4
Result = VERIFY_ERROR

Availablein = default
Availablein = legacy
VerifyRecover = RSA-2048
Ctrl = digest:MDC2
Input = 08da512483ece70be57f28a75271612800ae30ffbadc62609bc88b80d497a1fc13c300fdfcab6dc80cf55373c10adcc249ae80479b87fa3e391a2cd4a74babd1c22a4976812d544dcd6729b161bbc48fd067cf635b05f9edaddaeb6f67f2117d6b54a23c5e6f08a246abfe0356a67d7f3929306515e6d9962f8ce205120ecdcd2d4e3783cd0b4a1f0196a1b13924d0d3649233312695c3c336ae04e0b1efddabcc878b57622db60f6f747a1124c38426dacf1425c92d304c2bb1052f987c1dd73e4cc4b20d23396d4f05f52f98cf5065c3fb7dc319425f1f6f1878b87f57afbd24fbff98909494581aadd04d80a639b85ce8684ea58409d8dbbbaacf256bb5c4
Result = KEYOP_ERROR

# Legacy OCTET STRING MDC-2 signature, wrong input and signature digest length
Availablein = legacy
Verify = RSA-2048
Ctrl = digest:MDC2
Input = "0123456789ABCDE"
Output = 08da512483ece70be57f28a75271612800ae30ffbadc62609bc88b80d497a1fc13c300fdfcab6dc80cf55373c10adcc249ae80479b87fa3e391a2cd4a74babd1c22a4976812d544dcd6729b161bbc48fd067cf635b05f9edaddaeb6f67f2117d6b54a23c5e6f08a246abfe0356a67d7f3929306515e6d9962f8ce205120ecdcd2d4e3783cd0b4a1f0196a1b13924d0d3649233312695c3c336ae04e0b1efddabcc878b57622db60f6f747a1124c38426dacf1425c92d304c2bb1052f987c1dd73e4cc4b20d23396d4f05f52f98cf5065c3fb7dc319425f1f6f1878b87f57afbd24fbff98909494581aadd04d80a639b85ce8684ea58409d8dbbbaacf256bb5c4
Result = VERIFY_ERROR

Sign = RSA-2048
Ctrl = rsa_mgf1_md:sha1
Result = PKEY_CTRL_ERROR
Reason = invalid mgf1 md

# RSA PSS key tests

# PSS only key, no parameter restrictions
PrivateKey = RSA-PSS
-----BEGIN PRIVATE KEY-----
MIIEugIBADALBgkqhkiG9w0BAQoEggSmMIIEogIBAAKCAQEAzQCB6nsq4eoG1Z98
c9n/uUoJYVwuS6fGNs7wjdNTPsMYVSWwFcdpuZp31nJb+cNTKptuX2Yn1fuFFgdo
092py9NZdFEXF9w9MJ0vxH7kH5fjKtt/ndhkocR2emZuzXG8Gqz151F/SzhZT+qb
BeQtWtqZEgCAE+RTFqTZu47QhriNKHWLrK+SLUaoaLSF0jnJuusOK2RZJxD0Ky0e
oKS0gCwL7Ksyj4posAc721Rv7qmAnShJkSs5DBUyvH4px2WPgXX65G80My/4e8qz
5AZJuYV3hp2g6nGDU/ByJ1SIaRNkh2DRIr5nbg/Eg90g/8Mb2pajGWbJqi51rQPe
R+HETwIDAQABAoIBAGApeseZGxZ6BtayR1i4y+IIvrmy2eyXOL2A+QouNQBd184p
LZ4puohb0xb+8fIJE7wKyQ1rCAiyQU2CEERB2GJKM84CM8j3gKSLN1r/AtdnEiKK
cCSE2z+evszPu+4XCduhgoANlJ6eQhbgv/NVg4j4vZDaNzodgnQ+w/vdFCf9FoJa
ZXoxaRLoaVNlEXyi+EXJCUBfysVfiV/BXSA4bCbueMnpkHUCmheKbB5M8MIA6KnP
sn6dFW+G5sKtwisahKHNXKWyeQh115QHyEs1I5XLgcw/7VuwQ7ae3gwHIEVQAlzu
jF9EAXC2Egu0jg90e82PUiEQhQ3wQ8Qo39GHBTECgYEA9vlhtHy8A1067evH3oUK
lWtl7Nuc9gdkBj8VqkhVPFjZcv5mdQVuNd39w3vzufL2Iu4nEzclaEnJvvIXb+j3
w/i7kbo3TdU7rz3sgU0r3sEMH9yIzdFodvJrHt+j8JQZft9NQv8fspcRA7iYyoWc
QnKHCGqEKrQQu2nPLTWva+MCgYEA1H5ySn/0EEiycMJSSkEBh4tzFZu3PT28GHsi
DmNbNTT5biQ6GE2T+GC2v7trccHtmh4fRYWDAjwwHpamksGgi1PQ7JypEBANgEUe
O33GoBusSuzvjfeYhGvCNaCMu6LPTAaATMESGelWCMcU4/FDDUkfrbujKldRoE+X
dFg0yaUCgYAh8kUrubld/QKMkUv3mfHKd+ialdUNPBbThPhFX4vXr56z36PVkdmE
Le8jX3YwqOSMCI/2ZC4QF5RTWpM+HpdvqFCfxyiy2gxKGgjX/PN6uq4f8wAayh3B
u7Bdnf+6oaCff7Hu84I32evMxyK5M4Q23ecRkRJ5jCaAnBqN7EMgYQKBgB91EKpi
wtjeSjxTKCeB9B4C0Oi0Aq54Qy5EnEgRAWGhFAPwLQGICo3Mk4FS15chpHEaYHrE
Rx6/lkgQ+VvkekXmBJnin0yXc8g3c0BPYGY3cowtA1G7A8MmyLtzpyHn+lRA6iFy
u6FGX8ww3LDZ+Jkw6BWqH3+XKahX4A4DON1ZAoGATR8NdW/nfgEJmmUvUKiLe2hd
xb8AmB1dI3b9DG/inNW2OHNEeTBac608FZnTnq47rgNfvW/tB8KN5wWTOHmgbkjm
pgNobtjiVgpfavHywk+vSqlg44IYbxXu3OmiSRrnMGgN1M93i3D6qGgmqzIjR3zJ
E3exmm1aLq6iGXYL7tU=
-----END PRIVATE KEY-----

# PSS public key default parameters
PublicKey = RSA-PSS-DEFAULT
-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQowAAOCAQ8AMIIBCgKCAQEAzQCB6nsq4eoG1Z98c9n/
uUoJYVwuS6fGNs7wjdNTPsMYVSWwFcdpuZp31nJb+cNTKptuX2Yn1fuFFgdo092p
y9NZdFEXF9w9MJ0vxH7kH5fjKtt/ndhkocR2emZuzXG8Gqz151F/SzhZT+qbBeQt
WtqZEgCAE+RTFqTZu47QhriNKHWLrK+SLUaoaLSF0jnJuusOK2RZJxD0Ky0eoKS0
gCwL7Ksyj4posAc721Rv7qmAnShJkSs5DBUyvH4px2WPgXX65G80My/4e8qz5AZJ
uYV3hp2g6nGDU/ByJ1SIaRNkh2DRIr5nbg/Eg90g/8Mb2pajGWbJqi51rQPeR+HE
TwIDAQAB
-----END PUBLIC KEY-----

PrivPubKeyPair = RSA-PSS:RSA-PSS-DEFAULT


# Wrong MGF1 digest
Verify = RSA-2048
Ctrl = rsa_padding_mode:pss
Ctrl = rsa_pss_saltlen:0
Ctrl = digest:sha256
Ctrl = rsa_mgf1_md:sha1
Input="0123456789ABCDEF0123456789ABCDEF"
Output=4DE433D5844043EF08D354DA03CB29068780D52706D7D1E4D50EFB7D58C9D547D83A747DDD0635A96B28F854E50145518482CB49E963054621B53C60C498D07C16E9C2789C893CF38D4D86900DE71BDE463BD2761D1271E358C7480A1AC0BAB930DDF39602AD1BC165B5D7436B516B7A7858E8EB7AB1C420EEB482F4D207F0E462B1724959320A084E13848D11D10FB593E66BF680BF6D3F345FC3E9C3DE60ABBAC37E1C6EC80A268C8D9FC49626C679097AA690BC1AA662B95EB8DB70390861AA0898229F9349B4B5FDD030D4928C47084708A933144BE23BD3C6E661B85B2C0EF9ED36D498D5B7320E8194D363D4AD478C059BAE804181965E0B81B663158A
Result = VERIFY_ERROR

# Verify using default parameters
Verify = RSA-PSS-DEFAULT
Input="0123456789ABCDEF0123"
Output = 3EFE09D88509027D837BFA5F8471CF7B69E6DF395DD999BB9CA42021F15722D9AC76670507C6BCFB73F64FB2211B611B8F140E76EBDB064BD762FDBA89D019E304A0D6B274E1C2FE1DF50005598A0306AF805416094E2A5BA60BC72BDE38CE061E853ED40F14967A8B9CA4DC739B462F89558F12FDF2D8D19FBEF16AD66FE2DDDA8BEE983ECBD873064244849D8D94B5B33F45E076871A47ED653E73257A2BE2DB3C0878094B0D2B6B682C8007DFD989425FB39A1FEEC9EED5876414601A49176EC344F5E3EDEE81CA2DDD29B7364F4638112CB3A547E2BC170E28CB66BDABE863754BE8AD5BA230567B575266F4B6B4CF81F28310ABF05351CC9E2DB85D00BF

# Illegal decrypt
Decrypt = RSA-PSS
Result = KEYOP_INIT_ERROR
Reason = operation not supported for this keytype


# Additional RSA-PSS and RSA-OAEP tests converted from
# ftp://ftp.rsasecurity.com/pub/pkcs/pkcs-1/pkcs-1v2-1-vec.zip
Title = RSA PSS/OAEP (from RSASecurity FTP)

# 1025 bit key
PublicKey=RSA-PSS-2
-----BEGIN PUBLIC KEY-----
MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQHUDBvPl6aK5829invz40+hncyk
73WkdFQ3X5RRTYj+0Ab7gp+EGf+H1jFdpoof86CTjpq7NGQBHDA62ZGZzwx8eotH
fc6CnohE9iWxFeXpxKWc+PgRO2g0M2ov0mibRyy7Xlyr5nQ1DFm2wX4XaHT7Qvj8
PRdqAX7cYf0ybEszyQIDAQAB
-----END PUBLIC KEY-----

Verify=RSA-PSS-2
Ctrl = rsa_padding_mode:pss
Ctrl = rsa_mgf1_md:sha1
Input=5c81a3e2a658246628cd0ee8b00bb4c012bc9739
Output=014c5ba5338328ccc6e7a90bf1c0ab3fd606ff4796d3c12e4b639ed9136a5fec6c16d8884bdd99cfdc521456b0742b736868cf90de099adb8d5ffd1deff39ba4007ab746cefdb22d7df0e225f54627dc65466131721b90af445363a8358b9f607642f78fab0ab0f43b7168d64bae70d8827848d8ef1e421c5754ddf42c2589b5b3

Verify=RSA-PSS-2
Ctrl = rsa_padding_mode:pss
Ctrl = rsa_mgf1_md:sha1
Input=27f71611446aa6eabf037f7dedeede3203244991
Output=010991656cca182b7f29d2dbc007e7ae0fec158eb6759cb9c45c5ff87c7635dd46d150882f4de1e9ae65e7f7d9018f6836954a47c0a81a8a6b6f83f2944d6081b1aa7c759b254b2c34b691da67cc0226e20b2f18b42212761dcd4b908a62b371b5918c5742af4b537e296917674fb914194761621cc19a41f6fb953fbcbb649dea

Verify=RSA-PSS-2
Ctrl = rsa_padding_mode:pss
Ctrl = rsa_mgf1_md:sha1
Input=03ecc2c33e93f05fc7224fcc0d461356cb897217
Output=007f0030018f53cdc71f23d03659fde54d4241f758a750b42f185f87578520c30742afd84359b6e6e8d3ed959dc6fe486bedc8e2cf001f63a7abe16256a1b84df0d249fc05d3194ce5f0912742dbbf80dd174f6c51f6bad7f16cf3364eba095a06267dc3793803ac7526aebe0a475d38b8c2247ab51c4898df7047dc6adf52c6c4

Verify=RSA-PSS-2
Ctrl = rsa_padding_mode:pss
Ctrl = rsa_mgf1_md:sha1
Input=246c727b4b9494849dddb068d582e179ac20999c
Output=009cd2f4edbe23e12346ae8c76dd9ad3230a62076141f16c152ba18513a48ef6f010e0e37fd3df10a1ec629a0cb5a3b5d2893007298c30936a95903b6ba85555d9ec3673a06108fd62a2fda56d1ce2e85c4db6b24a81ca3b496c36d4fd06eb7c9166d8e94877c42bea622b3bfe9251fdc21d8d5371badad78a488214796335b40b

Verify=RSA-PSS-2
Ctrl = rsa_padding_mode:pss
Ctrl = rsa_mgf1_md:sha1
Input=e8617ca3ea66ce6a58ede2d11af8c3ba8a6ba912
Output=00ec430824931ebd3baa43034dae98ba646b8c36013d1671c3cf1cf8260c374b19f8e1cc8d965012405e7e9bf7378612dfcc85fce12cda11f950bd0ba8876740436c1d2595a64a1b32efcfb74a21c873b3cc33aaf4e3dc3953de67f0674c0453b4fd9f604406d441b816098cb106fe3472bc251f815f59db2e4378a3addc181ecf

Verify=RSA-PSS-2
Ctrl = rsa_padding_mode:pss
Ctrl = rsa_mgf1_md:sha1
Input=7a6fdc1a4e434ecbc35d657ad49a2f4fafd43bc8
Output=00475b1648f814a8dc0abdc37b5527f543b666bb6e39d30e5b49d3b876dccc58eac14e32a2d55c2616014456ad2f246fc8e3d560da3ddf379a1c0bd200f10221df078c219a151bc8d4ec9d2fc2564467811014ef15d8ea01c2ebbff8c2c8efab38096e55fcbe3285c7aa558851254faffa92c1c72b78758663ef4582843139d7a6

# 1026 bit key
PublicKey=RSA-PSS-3
-----BEGIN PUBLIC KEY-----
MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQLyRu9FHtPuu5oxAgDMJYWcBI5L
55gwKZERLraM5ttnTigNoh/t7RrnSIDKUisY2ySThQEoJ8UV8ORmof+mkdmBcFdO
nQ6tsIdYbKSJM9o8yVPZW9DtUN4Q3ctnNhB9bIMcf2Y+gzykwJfnAM4PuUX4j7hf
6OWncxclZbkUpHGkQwIDAQAB
-----END PUBLIC KEY-----

Verify=RSA-PSS-3
Ctrl = rsa_padding_mode:pss
Ctrl = rsa_mgf1_md:sha1
Input=3552be69dd74bdc56d2cf8c38ef7bafe269040fe
Output=0088b135fb1794b6b96c4a3e678197f8cac52b64b2fe907d6f27de761124964a99a01a882740ecfaed6c01a47464bb05182313c01338a8cd097214cd68ca103bd57d3bc9e816213e61d784f182467abf8a01cf253e99a156eaa8e3e1f90e3c6e4e3aa2d83ed0345b89fafc9c26077c14b6ac51454fa26e446e3a2f153b2b16797f

Verify=RSA-PSS-3
Ctrl = rsa_padding_mode:pss
Ctrl = rsa_mgf1_md:sha1
Input=609143ff7240e55c062aba8b9e4426a781919bc9
Output=02a5f0a858a0864a4f65017a7d69454f3f973a2999839b7bbc48bf78641169179556f595fa41f6ff18e286c2783079bc0910ee9cc34f49ba681124f923dfa88f426141a368a5f5a930c628c2c3c200e18a7644721a0cbec6dd3f6279bde3e8f2be5e2d4ee56f97e7ceaf33054be7042bd91a63bb09f897bd41e81197dee99b11af

Verify=RSA-PSS-3
Ctrl = rsa_padding_mode:pss
Ctrl = rsa_mgf1_md:sha1
Input=0afd22f879a9cda7c584f4135f8f1c961db114c0
Output=0244bcd1c8c16955736c803be401272e18cb990811b14f72db964124d5fa760649cbb57afb8755dbb62bf51f466cf23a0a1607576e983d778fceffa92df7548aea8ea4ecad2c29dd9f95bc07fe91ecf8bee255bfe8762fd7690aa9bfa4fa0849ef728c2c42c4532364522df2ab7f9f8a03b63f7a499175828668f5ef5a29e3802c

Verify=RSA-PSS-3
Ctrl = rsa_padding_mode:pss
Ctrl = rsa_mgf1_md:sha1
Input=405dd56d395ef0f01b555c48f748cc32b210650b
Output=0196f12a005b98129c8df13c4cb16f8aa887d3c40d96df3a88e7532ef39cd992f273abc370bc1be6f097cfebbf0118fd9ef4b927155f3df22b904d90702d1f7ba7a52bed8b8942f412cd7bd676c9d18e170391dcd345c06a730964b3f30bcce0bb20ba106f9ab0eeb39cf8a6607f75c0347f0af79f16afa081d2c92d1ee6f836b8

Verify=RSA-PSS-3
Ctrl = rsa_padding_mode:pss
Ctrl = rsa_mgf1_md:sha1
Input=a2c313b0440c8a0c47233b87f0a160c61af3eae7
Output=021eca3ab4892264ec22411a752d92221076d4e01c0e6f0dde9afd26ba5acf6d739ef987545d16683e5674c9e70f1de649d7e61d48d0caeb4fb4d8b24fba84a6e3108fee7d0705973266ac524b4ad280f7ae17dc59d96d3351586b5a3bdb895d1e1f7820ac6135d8753480998382ba32b7349559608c38745290a85ef4e9f9bd83

Verify=RSA-PSS-3
Ctrl = rsa_padding_mode:pss
Ctrl = rsa_mgf1_md:sha1
Input=f1bf6ca7b4bbdbb6bf20a4bf55728725d177154a
Output=012fafec862f56e9e92f60ab0c77824f4299a0ca734ed26e0644d5d222c7f0bde03964f8e70a5cb65ed44e44d56ae0edf1ff86ca032cc5dd4404dbb76ab854586c44eed8336d08d457ce6c03693b45c0f1efef93624b95b8ec169c616d20e5538ebc0b6737a6f82b4bc0570924fc6b35759a3348426279f8b3d7744e2d222426ce

# 1027 bit key
PublicKey=RSA-PSS-4
-----BEGIN PUBLIC KEY-----
MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQVK23iGRH7+b1fgNo8Gz1KwozcH
YNFhzvEmuRvn+JxCG2Km7B2jwxHXXtUOCrX/8/0zisw6qKTnfuJjaay4G6kA+oP1
MAz5u2xTrR3IoXi4FdtCNamp2gwG3k5hXqEnfOVZ6cEI3ljBSoGqd/Wm+NEzVJRJ
iEjIuVlAdAvnv3w3BQIDAQAB
-----END PUBLIC KEY-----

Verify=RSA-PSS-4
Ctrl = rsa_padding_mode:pss
Ctrl = rsa_mgf1_md:sha1
Input=f8b0abf70fec0bca74f0accbc24f75e6e90d3bfd
Output=0323d5b7bf20ba4539289ae452ae4297080feff4518423ff4811a817837e7d82f1836cdfab54514ff0887bddeebf40bf99b047abc3ecfa6a37a3ef00f4a0c4a88aae0904b745c846c4107e8797723e8ac810d9e3d95dfa30ff4966f4d75d13768d20857f2b1406f264cfe75e27d7652f4b5ed3575f28a702f8c4ed9cf9b2d44948

Verify=RSA-PSS-4
Ctrl = rsa_padding_mode:pss
Ctrl = rsa_mgf1_md:sha1
Input=04a10944bfe11ab801e77889f3fd3d7f4ff0b629
Output=049d0185845a264d28feb1e69edaec090609e8e46d93abb38371ce51f4aa65a599bdaaa81d24fba66a08a116cb644f3f1e653d95c89db8bbd5daac2709c8984000178410a7c6aa8667ddc38c741f710ec8665aa9052be929d4e3b16782c1662114c5414bb0353455c392fc28f3db59054b5f365c49e1d156f876ee10cb4fd70598

Verify=RSA-PSS-4
Ctrl = rsa_padding_mode:pss
Ctrl = rsa_mgf1_md:sha1
Input=ba01243db223eb97fb86d746c3148adaaa0ca344
Output=03fbc410a2ced59500fb99f9e2af2781ada74e13145624602782e2994813eefca0519ecd253b855fb626a90d771eae028b0c47a199cbd9f8e3269734af4163599090713a3fa910fa0960652721432b971036a7181a2bc0cab43b0b598bc6217461d7db305ff7e954c5b5bb231c39e791af6bcfa76b147b081321f72641482a2aad

Verify=RSA-PSS-4
Ctrl = rsa_padding_mode:pss
Ctrl = rsa_mgf1_md:sha1
Input=934bb0d38d6836daec9de82a9648d4593da67cd2
Output=0486644bc66bf75d28335a6179b10851f43f09bded9fac1af33252bb9953ba4298cd6466b27539a70adaa3f89b3db3c74ab635d122f4ee7ce557a61e59b82ffb786630e5f9db53c77d9a0c12fab5958d4c2ce7daa807cd89ba2cc7fcd02ff470ca67b229fcce814c852c73cc93bea35be68459ce478e9d4655d121c8472f371d4f

Verify=RSA-PSS-4
Ctrl = rsa_padding_mode:pss
Ctrl = rsa_mgf1_md:sha1
Input=ec35d81abd1cceac425a935758b683465c8bd879
Output=022a80045353904cb30cbb542d7d4990421a6eec16a8029a8422adfd22d6aff8c4cc0294af110a0c067ec86a7d364134459bb1ae8ff836d5a8a2579840996b320b19f13a13fad378d931a65625dae2739f0c53670b35d9d3cbac08e733e4ec2b83af4b9196d63e7c4ff1ddeae2a122791a125bfea8deb0de8ccf1f4ffaf6e6fb0a

Verify=RSA-PSS-4
Ctrl = rsa_padding_mode:pss
Ctrl = rsa_mgf1_md:sha1
Input=72ce251d17b04dd3970d6ff1fbe3624899e9e941
Output=00938dcb6d583046065f69c78da7a1f1757066a7fa75125a9d2929f0b79a60b627b082f11f5b196f28eb9daa6f21c05e5140f6aef1737d2023075c05ecf04a028c686a2ab3e7d5a0664f295ce12995e890908b6ad21f0839eb65b70393a7b5afd9871de0caa0cedec5b819626756209d13ab1e7bb9546a26ff37e9a51af9fd562e

# 1028 bit key
PublicKey=RSA-PSS-5
-----BEGIN PUBLIC KEY-----
MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQ0Q9mHymUD17TmqJglm3rR4Q2ed
K2+yWz3jcPOsfBmRY5H9JftSfr+mpLTfRaF1nZlsS7Tr0YgoxE/FLQGRhxdAUl9H
pLDMjaMl7YqmdrDQ9ibgp38HaSFwrKyAgvQvqn3HzRI+cw4xqHmFIEyry+ZnDUOi
3Sst3vXgU5L8ITvFBwIDAQAB
-----END PUBLIC KEY-----

Verify=RSA-PSS-5
Ctrl = rsa_padding_mode:pss
Ctrl = rsa_mgf1_md:sha1
Input=d98b7061943510bc3dd9162f7169aabdbdcd0222
Output=0ba373f76e0921b70a8fbfe622f0bf77b28a3db98e361051c3d7cb92ad0452915a4de9c01722f6823eeb6adf7e0ca8290f5de3e549890ac2a3c5950ab217ba58590894952de96f8df111b2575215da6c161590c745be612476ee578ed384ab33e3ece97481a252f5c79a98b5532ae00cdd62f2ecc0cd1baefe80d80b962193ec1d

Verify=RSA-PSS-5
Ctrl = rsa_padding_mode:pss
Ctrl = rsa_mgf1_md:sha1
Input=7ae8e699f754988f4fd645e463302e49a2552072
Output=08180de825e4b8b014a32da8ba761555921204f2f90d5f24b712908ff84f3e220ad17997c0dd6e706630ba3e84add4d5e7ab004e58074b549709565d43ad9e97b5a7a1a29e85b9f90f4aafcdf58321de8c5974ef9abf2d526f33c0f2f82e95d158ea6b81f1736db8d1af3d6ac6a83b32d18bae0ff1b2fe27de4c76ed8c7980a34e

Verify=RSA-PSS-5
Ctrl = rsa_padding_mode:pss
Ctrl = rsa_mgf1_md:sha1
Input=8d46c7c05534c1ba2cc7624500d48a4531604bff
Output=05e0fdbdf6f756ef733185ccfa8ced2eb6d029d9d56e35561b5db8e70257ee6fd019d2f0bbf669fe9b9821e78df6d41e31608d58280f318ee34f559941c8df13287574bac000b7e58dc4f414ba49fb127f9d0f8936638c76e85356c994f79750f7fa3cf4fd482df75e3fb9978cd061f7abb17572e6e63e0bde12cbdcf18c68b979

Availablein = default
Verify=RSA-PSS-5
Ctrl = rsa_padding_mode:pss
Ctrl = rsa_mgf1_md:sha1
Input=ee3de96783fd0a157c8b20bf5566124124dcfe65
Output=0bc989853bc2ea86873271ce183a923ab65e8a53100e6df5d87a24c4194eb797813ee2a187c097dd872d591da60c568605dd7e742d5af4e33b11678ccb63903204a3d080b0902c89aba8868f009c0f1c0cb85810bbdd29121abb8471ff2d39e49fd92d56c655c8e037ad18fafbdc92c95863f7f61ea9efa28fea401369d19daea1

Verify=RSA-PSS-5
Ctrl = rsa_padding_mode:pss
Ctrl = rsa_mgf1_md:sha1
Input=1204df0b03c2724e2709c23fc71789a21b00ae4c
Output=0aefa943b698b9609edf898ad22744ac28dc239497cea369cbbd84f65c95c0ad776b594740164b59a739c6ff7c2f07c7c077a86d95238fe51e1fcf33574a4ae0684b42a3f6bf677d91820ca89874467b2c23add77969c80717430d0efc1d3695892ce855cb7f7011630f4df26def8ddf36fc23905f57fa6243a485c770d5681fcd

Verify=RSA-PSS-5
Ctrl = rsa_padding_mode:pss
Ctrl = rsa_mgf1_md:sha1
Input=29926bc3280c841f601acd0d6f17ea38023eddbc
Output=02802dccfa8dfaf5279bf0b4a29ba1b157611faeaaf419b8919d15941900c1339e7e92e6fae562c53e6cc8e84104b110bce03ad18525e3c49a0eadad5d3f28f244a8ed89edbafbb686277cfa8ae909714d6b28f4bf8e293aa04c41efe7c0a81266d5c061e2575be032aa464674ff71626219bd74cc45f0e7ed4e3ff96eee758e8f

# 1029 bit key
PublicKey=RSA-PSS-6
-----BEGIN PUBLIC KEY-----
MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgRZMoxz/YJ86DnEBsDny5P5t03UZ
q5hZjReeF0mWWYBx9H06BFWRWNe+NzzxqlPwqm7wkDnlZ4wqTGOQBRTIxPiq7V3h
Kl8QsJwxGvjA/7W3opfy78Y7jWsFEJMfC5jki/X8bsTnuNsf+usIw44CrbjwOkgi
nJnpaUMfYcuMTcaY0QIDAQAB
-----END PUBLIC KEY-----

Verify=RSA-PSS-6
Ctrl = rsa_padding_mode:pss
Ctrl = rsa_mgf1_md:sha1
Input=ab464e8cb65ae5fdea47a53fa84b234d6bfd52f6
Output=04c0cfacec04e5badbece159a5a1103f69b3f32ba593cb4cc4b1b7ab455916a96a27cd2678ea0f46ba37f7fc9c86325f29733b389f1d97f43e7201c0f348fc45fe42892335362eee018b5b161f2f9393031225c713012a576bc88e23052489868d9010cbf033ecc568e8bc152bdc59d560e41291915d28565208e22aeec9ef85d1

Verify=RSA-PSS-6
Ctrl = rsa_padding_mode:pss
Ctrl = rsa_mgf1_md:sha1
Input=92d0bcae82b641f578f040f5151be8eda6d42299
Output=0a2314250cf52b6e4e908de5b35646bcaa24361da8160fb0f9257590ab3ace42b0dc3e77ad2db7c203a20bd952fbb56b1567046ecfaa933d7b1000c3de9ff05b7d989ba46fd43bc4c2d0a3986b7ffa13471d37eb5b47d64707bd290cfd6a9f393ad08ec1e3bd71bb5792615035cdaf2d8929aed3be098379377e777ce79aaa4773

Verify=RSA-PSS-6
Ctrl = rsa_padding_mode:pss
Ctrl = rsa_mgf1_md:sha1
Input=3569bd8fd2e28f2443375efa94f186f6911ffc2b
Output=086df6b500098c120f24ff8423f727d9c61a5c9007d3b6a31ce7cf8f3cbec1a26bb20e2bd4a046793299e03e37a21b40194fb045f90b18bf20a47992ccd799cf9c059c299c0526854954aade8a6ad9d97ec91a1145383f42468b231f4d72f23706d9853c3fa43ce8ace8bfe7484987a1ec6a16c8daf81f7c8bf42774707a9df456

Verify=RSA-PSS-6
Ctrl = rsa_padding_mode:pss
Ctrl = rsa_mgf1_md:sha1
Input=7abbb7b42de335730a0b641f1e314b6950b84f98
Output=0b5b11ad549863ffa9c51a14a1106c2a72cc8b646e5c7262509786105a984776534ca9b54c1cc64bf2d5a44fd7e8a69db699d5ea52087a4748fd2abc1afed1e5d6f7c89025530bdaa2213d7e030fa55df6f34bcf1ce46d2edf4e3ae4f3b01891a068c9e3a44bbc43133edad6ecb9f35400c4252a5762d65744b99cb9f4c559329f

Verify=RSA-PSS-6
Ctrl = rsa_padding_mode:pss
Ctrl = rsa_mgf1_md:sha1
Input=55b7eb27be7a787a59eb7e5fac468db8917a7725
Output=02d71fa9b53e4654fefb7f08385cf6b0ae3a817942ebf66c35ac67f0b069952a3ce9c7e1f1b02e480a9500836de5d64cdb7ecde04542f7a79988787e24c2ba05f5fd482c023ed5c30e04839dc44bed2a3a3a4fee01113c891a47d32eb8025c28cb050b5cdb576c70fe76ef523405c08417faf350b037a43c379339fcb18d3a356b

Verify=RSA-PSS-6
Ctrl = rsa_padding_mode:pss
Ctrl = rsa_mgf1_md:sha1
Input=de2fa0367ef49083ff89b9905d3fd646fcc12c38
Output=0a40a16e2fe2b38d1df90546167cf9469c9e3c3681a3442b4b2c2f581deb385ce99fc6188bb02a841d56e76d301891e24560550fcc2a26b55f4ccb26d837d350a154bcaca8392d98fa67959e9727b78cad03269f56968fc56b68bd679926d83cc9cb215550645ccda31c760ff35888943d2d8a1d351e81e5d07b86182e751081ef

# 1030 bit key
PublicKey=RSA-PSS-7
-----BEGIN PUBLIC KEY-----
MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgTfJ2kpmyMQIuNon0MnXn4zLHq/B
0v5IdG2UC3xO9d7hitEmR876oMSzGIsiHFFThnWbk/AgJLJauSQvg1fY8/1JZA7l
5kPq9sZN7vpwiXJ8j/A5kzM5FcbvIb9ZdbblDRGLUQCOwz6fAaClRaEKg2pD3byp
2LXF01SAItcGTqKaswIDAQAB
-----END PUBLIC KEY-----

Verify=RSA-PSS-7
Ctrl = rsa_padding_mode:pss
Ctrl = rsa_mgf1_md:sha1
Input=8be4afbdd76bd8d142c5f4f46dba771ee5d6d29d
Output=187f390723c8902591f0154bae6d4ecbffe067f0e8b795476ea4f4d51ccc810520bb3ca9bca7d0b1f2ea8a17d873fa27570acd642e3808561cb9e975ccfd80b23dc5771cdb3306a5f23159dacbd3aa2db93d46d766e09ed15d900ad897a8d274dc26b47e994a27e97e2268a766533ae4b5e42a2fcaf755c1c4794b294c60555823

Verify=RSA-PSS-7
Ctrl = rsa_padding_mode:pss
Ctrl = rsa_mgf1_md:sha1
Input=402140dc605b2f5c5ec0d15bce9f9ba8857fe117
Output=10fd89768a60a67788abb5856a787c8561f3edcf9a83e898f7dc87ab8cce79429b43e56906941a886194f137e591fe7c339555361fbbe1f24feb2d4bcdb80601f3096bc9132deea60ae13082f44f9ad41cd628936a4d51176e42fc59cb76db815ce5ab4db99a104aafea68f5d330329ebf258d4ede16064bd1d00393d5e1570eb8

Verify=RSA-PSS-7
Ctrl = rsa_padding_mode:pss
Ctrl = rsa_mgf1_md:sha1
Input=3e885205892ff2b6b37c2c4eb486c4bf2f9e7f20
Output=2b31fde99859b977aa09586d8e274662b25a2a640640b457f594051cb1e7f7a911865455242926cf88fe80dfa3a75ba9689844a11e634a82b075afbd69c12a0df9d25f84ad4945df3dc8fe90c3cefdf26e95f0534304b5bdba20d3e5640a2ebfb898aac35ae40f26fce5563c2f9f24f3042af76f3c7072d687bbfb959a88460af1

Verify=RSA-PSS-7
Ctrl = rsa_padding_mode:pss
Ctrl = rsa_mgf1_md:sha1
Input=1fc2201d0c442a4736cd8b2cd00c959c47a3bf42
Output=32c7ca38ff26949a15000c4ba04b2b13b35a3810e568184d7ecabaa166b7ffabddf2b6cf4ba07124923790f2e5b1a5be040aea36fe132ec130e1f10567982d17ac3e89b8d26c3094034e762d2e031264f01170beecb3d1439e05846f25458367a7d9c02060444672671e64e877864559ca19b2074d588a281b5804d23772fbbe19

Verify=RSA-PSS-7
Ctrl = rsa_padding_mode:pss
Ctrl = rsa_mgf1_md:sha1
Input=e4351b66819e5a31501f89acc7faf57030e9aac5
Output=07eb651d75f1b52bc263b2e198336e99fbebc4f332049a922a10815607ee2d989db3a4495b7dccd38f58a211fb7e193171a3d891132437ebca44f318b280509e52b5fa98fcce8205d9697c8ee4b7ff59d4c59c79038a1970bd2a0d451ecdc5ef11d9979c9d35f8c70a6163717607890d586a7c6dc01c79f86a8f28e85235f8c2f1

Verify=RSA-PSS-7
Ctrl = rsa_padding_mode:pss
Ctrl = rsa_mgf1_md:sha1
Input=49f6cc58365e514e1a3f301f4de16f9fb5347ff2
Output=18da3cdcfe79bfb77fd9c32f377ad399146f0a8e810620233271a6e3ed3248903f5cdc92dc79b55d3e11615aa056a795853792a3998c349ca5c457e8ca7d29d796aa24f83491709befcfb1510ea513c92829a3f00b104f655634f320752e130ec0ccf6754ff893db302932bb025eb60e87822598fc619e0e981737a9a4c4152d33

# 1031 bit key
PublicKey=RSA-PSS-8
-----BEGIN PUBLIC KEY-----
MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgUlTcKH7GFQ8FtNjHjFjJV32K+bu
6JDV8lUJ5Pd4qOpvu7zfhd/2Tg2XIAOrNoH7um3UH9VBgpsuWC3p8qSk4KLQkAvv
R1PbPO4O4Gx9+uix1TtZUyGPnM7qaVsIZo7eqtztlGOx15DV6/J+kRW0bK1NmiuO
+rBWGwgQNEc5raBzPwIDAQAB
-----END PUBLIC KEY-----

Verify=RSA-PSS-8
Ctrl = rsa_padding_mode:pss
Ctrl = rsa_mgf1_md:sha1
Input=a1dd230d8ead860199b6277c2ecfe3d95f6d9160
Output=0262ac254bfa77f3c1aca22c5179f8f040422b3c5bafd40a8f21cf0fa5a667ccd5993d42dbafb409c520e25fce2b1ee1e716577f1efa17f3da28052f40f0419b23106d7845aaf01125b698e7a4dfe92d3967bb00c4d0d35ba3552ab9a8b3eef07c7fecdbc5424ac4db1e20cb37d0b2744769940ea907e17fbbca673b20522380c5

Verify=RSA-PSS-8
Ctrl = rsa_padding_mode:pss
Ctrl = rsa_mgf1_md:sha1
Input=f6e68e53c602c5c65fa67b5aa6d786e5524b12ab
Output=2707b9ad5115c58c94e932e8ec0a280f56339e44a1b58d4ddcff2f312e5f34dcfe39e89c6a94dcee86dbbdae5b79ba4e0819a9e7bfd9d982e7ee6c86ee68396e8b3a14c9c8f34b178eb741f9d3f121109bf5c8172fada2e768f9ea1433032c004a8aa07eb990000a48dc94c8bac8aabe2b09b1aa46c0a2aa0e12f63fbba775ba7e

Verify=RSA-PSS-8
Ctrl = rsa_padding_mode:pss
Ctrl = rsa_mgf1_md:sha1
Input=d6f9fcd3ae27f32bb2c7c93536782eba52af1f76
Output=2ad20509d78cf26d1b6c406146086e4b0c91a91c2bd164c87b966b8faa42aa0ca446022323ba4b1a1b89706d7f4c3be57d7b69702d168ab5955ee290356b8c4a29ed467d547ec23cbadf286ccb5863c6679da467fc9324a151c7ec55aac6db4084f82726825cfe1aa421bc64049fb42f23148f9c25b2dc300437c38d428aa75f96

Verify=RSA-PSS-8
Ctrl = rsa_padding_mode:pss
Ctrl = rsa_mgf1_md:sha1
Input=7ff2a53ce2e2d900d468e498f230a5f5dd0020de
Output=1e24e6e58628e5175044a9eb6d837d48af1260b0520e87327de7897ee4d5b9f0df0be3e09ed4dea8c1454ff3423bb08e1793245a9df8bf6ab3968c8eddc3b5328571c77f091cc578576912dfebd164b9de5454fe0be1c1f6385b328360ce67ec7a05f6e30eb45c17c48ac70041d2cab67f0a2ae7aafdcc8d245ea3442a6300ccc7

Verify=RSA-PSS-8
Ctrl = rsa_padding_mode:pss
Ctrl = rsa_mgf1_md:sha1
Input=4eb309f7022ba0b03bb78601b12931ec7c1be8d3
Output=33341ba3576a130a50e2a5cf8679224388d5693f5accc235ac95add68e5eb1eec31666d0ca7a1cda6f70a1aa762c05752a51950cdb8af3c5379f18cfe6b5bc55a4648226a15e912ef19ad77adeea911d67cfefd69ba43fa4119135ff642117ba985a7e0100325e9519f1ca6a9216bda055b5785015291125e90dcd07a2ca9673ee

Verify=RSA-PSS-8
Ctrl = rsa_padding_mode:pss
Ctrl = rsa_mgf1_md:sha1
Input=65033bc2f67d6aba7d526acb873b8d9241e5e4d9
Output=1ed1d848fb1edb44129bd9b354795af97a069a7a00d0151048593e0c72c3517ff9ff2a41d0cb5a0ac860d736a199704f7cb6a53986a88bbd8abcc0076a2ce847880031525d449da2ac78356374c536e343faa7cba42a5aaa6506087791c06a8e989335aed19bfab2d5e67e27fb0c2875af896c21b6e8e7309d04e4f6727e69463e


Title = RSA DigestSign and DigestVerify

DigestSign = SHA1
Key = RSA-2048
Input = "Hello World"
Output = 3da3ca2bdd1b23a231b0e3c49d95d5959f9398c27a1e534c7e6baf1d2682304d3b6b229385b1edf483f5ef6f9b35bf10c519a302bb2f79c564e1a59ba71aa2fa36df96c942c43e8d9bd4702b5f61c12a078ae2b34d0de221fc8f9f936b79a67c89d11ba5da8c63a1370d0e824c6b661123e9b58b143ff533cf362cbdad70e65b419a6d45723bf22db3c76bb8f5337c5c5c93cb6f38b30d0c835b54c23405ca4217dd0b755f3712ebad285d9e0c02655f6ce5ce6fed78f3c81843de325f628055eef57f280dee0c3170050137ee599b9ab7f2b5d3c5f831777ea05a5eb097c70bad1a7214dadae12d7960bb9425390c7d25a79985e1e3c28ad422ff93c808f4b5

# Oneshot test
OneShotDigestSign = SHA1
Key = RSA-2048
Input = "Hello World"
Output = 3da3ca2bdd1b23a231b0e3c49d95d5959f9398c27a1e534c7e6baf1d2682304d3b6b229385b1edf483f5ef6f9b35bf10c519a302bb2f79c564e1a59ba71aa2fa36df96c942c43e8d9bd4702b5f61c12a078ae2b34d0de221fc8f9f936b79a67c89d11ba5da8c63a1370d0e824c6b661123e9b58b143ff533cf362cbdad70e65b419a6d45723bf22db3c76bb8f5337c5c5c93cb6f38b30d0c835b54c23405ca4217dd0b755f3712ebad285d9e0c02655f6ce5ce6fed78f3c81843de325f628055eef57f280dee0c3170050137ee599b9ab7f2b5d3c5f831777ea05a5eb097c70bad1a7214dadae12d7960bb9425390c7d25a79985e1e3c28ad422ff93c808f4b5


Title = Test RSA keygen

# Key generation tests

KeyGen = rsaEncryption
Ctrl = rsa_keygen_bits:128
KeyName = tmprsa
Result = PKEY_CTRL_ERROR
Reason = key size too small
