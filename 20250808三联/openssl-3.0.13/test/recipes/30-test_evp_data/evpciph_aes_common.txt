#
# Copyright 2001-2023 The OpenSSL Project Authors. All Rights Reserved.
#
# Licensed under the Apache License 2.0 (the "License").  You may not use
# this file except in compliance with the License.  You can obtain a copy
# in the file LICENSE in the source distribution or at
# https://www.openssl.org/source/license.html

# Tests start with one of these keywords
#       Cipher Decrypt Derive Digest Encoding KDF MAC PBE
#       PrivPubKeyPair Sign Verify VerifyRecover
# and continue until a blank line. Lines starting with a pound sign are ignored.
# The keyword Availablein must appear before the test name if needed.


Title = AES (from FIPS-197 test vectors)

Cipher = AES-128-ECB
Key = 000102030405060708090A0B0C0D0E0F
Operation = ENCRYPT
Plaintext = 00112233445566778899AABBCCDDEEFF
Ciphertext = 69C4E0D86A7B0430D8CDB78070B4C55A

# AES 192 ECB tests (from FIPS-197 test vectors, encrypt)

Cipher = AES-192-ECB
Key = 000102030405060708090A0B0C0D0E0F1011121314151617
Operation = ENCRYPT
Plaintext = 00112233445566778899AABBCCDDEEFF
Ciphertext = DDA97CA4864CDFE06EAF70A0EC0D7191


# AES 256 ECB tests (from FIPS-197 test vectors, encrypt)

Cipher = AES-256-ECB
Key = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F
Operation = ENCRYPT
Plaintext = 00112233445566778899AABBCCDDEEFF
Ciphertext = 8EA2B7CA516745BFEAFC49904B496089


# AES 128 ECB tests (from NIST test vectors, encrypt)

#AES-128-ECB:00000000000000000000000000000000::00000000000000000000000000000000:C34C052CC0DA8D73451AFE5F03BE297F:1

# AES 128 ECB tests (from NIST test vectors, decrypt)

#AES-128-ECB:00000000000000000000000000000000::44416AC2D1F53C583303917E6BE9EBE0:00000000000000000000000000000000:0

# AES 192 ECB tests (from NIST test vectors, decrypt)

#AES-192-ECB:000000000000000000000000000000000000000000000000::48E31E9E256718F29229319C19F15BA4:00000000000000000000000000000000:0

# AES 256 ECB tests (from NIST test vectors, decrypt)

#AES-256-ECB:0000000000000000000000000000000000000000000000000000000000000000::058CCFFDBBCB382D1F6F56585D8A4ADE:00000000000000000000000000000000:0

# AES 128 CBC tests (from NIST test vectors, encrypt)

#AES-128-CBC:00000000000000000000000000000000:00000000000000000000000000000000:00000000000000000000000000000000:8A05FC5E095AF4848A08D328D3688E3D:1

# AES 192 CBC tests (from NIST test vectors, encrypt)

#AES-192-CBC:000000000000000000000000000000000000000000000000:00000000000000000000000000000000:00000000000000000000000000000000:7BD966D53AD8C1BB85D2ADFAE87BB104:1

# AES 256 CBC tests (from NIST test vectors, encrypt)

#AES-256-CBC:0000000000000000000000000000000000000000000000000000000000000000:00000000000000000000000000000000:00000000000000000000000000000000:FE3C53653E2F45B56FCD88B2CC898FF0:1

# AES 128 CBC tests (from NIST test vectors, decrypt)

#AES-128-CBC:00000000000000000000000000000000:00000000000000000000000000000000:FACA37E0B0C85373DF706E73F7C9AF86:00000000000000000000000000000000:0

# AES tests from NIST document SP800-38A
# For all ECB encrypts and decrypts, the transformed sequence is
#   AES-bits-ECB:key::plaintext:ciphertext:encdec
# ECB-AES128.Encrypt and ECB-AES128.Decrypt

Title = AES tests from NIST document SP800-38A

Cipher = AES-128-ECB
Key = 2B7E151628AED2A6ABF7158809CF4F3C
Plaintext = 6BC1BEE22E409F96E93D7E117393172A
Ciphertext = 3AD77BB40D7A3660A89ECAF32466EF97

Cipher = AES-128-ECB
Key = 2B7E151628AED2A6ABF7158809CF4F3C
Plaintext = AE2D8A571E03AC9C9EB76FAC45AF8E51
Ciphertext = F5D3D58503B9699DE785895A96FDBAAF

Cipher = AES-128-ECB
Key = 2B7E151628AED2A6ABF7158809CF4F3C
Plaintext = 30C81C46A35CE411E5FBC1191A0A52EF
Ciphertext = 43B1CD7F598ECE23881B00E3ED030688

Cipher = AES-128-ECB
Key = 2B7E151628AED2A6ABF7158809CF4F3C
Plaintext = F69F2445DF4F9B17AD2B417BE66C3710
Ciphertext = 7B0C785E27E8AD3F8223207104725DD4

# ECB-AES192.Encrypt and ECB-AES192.Decrypt
Cipher = AES-192-ECB
Key = 8E73B0F7DA0E6452C810F32B809079E562F8EAD2522C6B7B
Plaintext = 6BC1BEE22E409F96E93D7E117393172A
Ciphertext = BD334F1D6E45F25FF712A214571FA5CC

Cipher = AES-192-ECB
Key = 8E73B0F7DA0E6452C810F32B809079E562F8EAD2522C6B7B
Plaintext = AE2D8A571E03AC9C9EB76FAC45AF8E51
Ciphertext = 974104846D0AD3AD7734ECB3ECEE4EEF

Cipher = AES-192-ECB
Key = 8E73B0F7DA0E6452C810F32B809079E562F8EAD2522C6B7B
Plaintext = 30C81C46A35CE411E5FBC1191A0A52EF
Ciphertext = EF7AFD2270E2E60ADCE0BA2FACE6444E

Cipher = AES-192-ECB
Key = 8E73B0F7DA0E6452C810F32B809079E562F8EAD2522C6B7B
Plaintext = F69F2445DF4F9B17AD2B417BE66C3710
Ciphertext = 9A4B41BA738D6C72FB16691603C18E0E

# ECB-AES256.Encrypt and ECB-AES256.Decrypt
Cipher = AES-256-ECB
Key = 603DEB1015CA71BE2B73AEF0857D77811F352C073B6108D72D9810A30914DFF4
Plaintext = 6BC1BEE22E409F96E93D7E117393172A
Ciphertext = F3EED1BDB5D2A03C064B5A7E3DB181F8

Cipher = AES-256-ECB
Key = 603DEB1015CA71BE2B73AEF0857D77811F352C073B6108D72D9810A30914DFF4
Plaintext = AE2D8A571E03AC9C9EB76FAC45AF8E51
Ciphertext = 591CCB10D410ED26DC5BA74A31362870

Cipher = AES-256-ECB
Key = 603DEB1015CA71BE2B73AEF0857D77811F352C073B6108D72D9810A30914DFF4
Plaintext = 30C81C46A35CE411E5FBC1191A0A52EF
Ciphertext = B6ED21B99CA6F4F9F153E7B1BEAFED1D

Cipher = AES-256-ECB
Key = 603DEB1015CA71BE2B73AEF0857D77811F352C073B6108D72D9810A30914DFF4
Plaintext = F69F2445DF4F9B17AD2B417BE66C3710
Ciphertext = 23304B7A39F9F3FF067D8D8F9E24ECC7

# For all CBC encrypts and decrypts, the transformed sequence is
#   AES-bits-CBC:key:IV/ciphertext':plaintext:ciphertext:encdec
# CBC-AES128.Encrypt and CBC-AES128.Decrypt
Cipher = AES-128-CBC
Key = 2B7E151628AED2A6ABF7158809CF4F3C
IV = 000102030405060708090A0B0C0D0E0F
Plaintext = 6BC1BEE22E409F96E93D7E117393172A
Ciphertext = 7649ABAC8119B246CEE98E9B12E9197D
NextIV = 7649abac8119b246cee98e9b12e9197d

Cipher = AES-128-CBC
Key = 2B7E151628AED2A6ABF7158809CF4F3C
IV = 7649ABAC8119B246CEE98E9B12E9197D
Plaintext = AE2D8A571E03AC9C9EB76FAC45AF8E51
Ciphertext = 5086CB9B507219EE95DB113A917678B2
NextIV = 5086cb9b507219ee95db113a917678b2

Cipher = AES-128-CBC
Key = 2B7E151628AED2A6ABF7158809CF4F3C
IV = 5086CB9B507219EE95DB113A917678B2
Plaintext = 30C81C46A35CE411E5FBC1191A0A52EF
Ciphertext = 73BED6B8E3C1743B7116E69E22229516
NextIV = 73bed6b8e3c1743b7116e69e22229516

Cipher = AES-128-CBC
Key = 2B7E151628AED2A6ABF7158809CF4F3C
IV = 73BED6B8E3C1743B7116E69E22229516
Plaintext = F69F2445DF4F9B17AD2B417BE66C3710
Ciphertext = 3FF1CAA1681FAC09120ECA307586E1A7
NextIV = 3ff1caa1681fac09120eca307586e1a7

# CBC-AES192.Encrypt and CBC-AES192.Decrypt
Cipher = AES-192-CBC
Key = 8E73B0F7DA0E6452C810F32B809079E562F8EAD2522C6B7B
IV = 000102030405060708090A0B0C0D0E0F
Plaintext = 6BC1BEE22E409F96E93D7E117393172A
Ciphertext = 4F021DB243BC633D7178183A9FA071E8
NextIV = 4f021db243bc633d7178183a9fa071e8

Cipher = AES-192-CBC
Key = 8E73B0F7DA0E6452C810F32B809079E562F8EAD2522C6B7B
IV = 4F021DB243BC633D7178183A9FA071E8
Plaintext = AE2D8A571E03AC9C9EB76FAC45AF8E51
Ciphertext = B4D9ADA9AD7DEDF4E5E738763F69145A
NextIV = b4d9ada9ad7dedf4e5e738763f69145a

Cipher = AES-192-CBC
Key = 8E73B0F7DA0E6452C810F32B809079E562F8EAD2522C6B7B
IV = B4D9ADA9AD7DEDF4E5E738763F69145A
Plaintext = 30C81C46A35CE411E5FBC1191A0A52EF
Ciphertext = 571B242012FB7AE07FA9BAAC3DF102E0
NextIV = 571b242012fb7ae07fa9baac3df102e0

Cipher = AES-192-CBC
Key = 8E73B0F7DA0E6452C810F32B809079E562F8EAD2522C6B7B
IV = 571B242012FB7AE07FA9BAAC3DF102E0
Plaintext = F69F2445DF4F9B17AD2B417BE66C3710
Ciphertext = 08B0E27988598881D920A9E64F5615CD
NextIV = 08b0e27988598881d920a9e64f5615cd

# CBC-AES256.Encrypt and CBC-AES256.Decrypt
Cipher = AES-256-CBC
Key = 603DEB1015CA71BE2B73AEF0857D77811F352C073B6108D72D9810A30914DFF4
IV = 000102030405060708090A0B0C0D0E0F
Plaintext = 6BC1BEE22E409F96E93D7E117393172A
Ciphertext = F58C4C04D6E5F1BA779EABFB5F7BFBD6
NextIV = f58c4c04d6e5f1ba779eabfb5f7bfbd6

Cipher = AES-256-CBC
Key = 603DEB1015CA71BE2B73AEF0857D77811F352C073B6108D72D9810A30914DFF4
IV = F58C4C04D6E5F1BA779EABFB5F7BFBD6
Plaintext = AE2D8A571E03AC9C9EB76FAC45AF8E51
Ciphertext = 9CFC4E967EDB808D679F777BC6702C7D
NextIV = 9cfc4e967edb808d679f777bc6702c7d

Cipher = AES-256-CBC
Key = 603DEB1015CA71BE2B73AEF0857D77811F352C073B6108D72D9810A30914DFF4
IV = 9CFC4E967EDB808D679F777BC6702C7D
Plaintext = 30C81C46A35CE411E5FBC1191A0A52EF
Ciphertext = 39F23369A9D9BACFA530E26304231461
NextIV = 39f23369a9d9bacfa530e26304231461

Cipher = AES-256-CBC
Key = 603DEB1015CA71BE2B73AEF0857D77811F352C073B6108D72D9810A30914DFF4
IV = 39F23369A9D9BACFA530E26304231461
Plaintext = F69F2445DF4F9B17AD2B417BE66C3710
Ciphertext = B2EB05E2C39BE9FCDA6C19078C6A9D1B
NextIV = b2eb05e2c39be9fcda6c19078c6a9d1b

# We don't support CFB{1,8}-AESxxx.{En,De}crypt
# For all CFB128 encrypts and decrypts, the transformed sequence is
#   AES-bits-CFB:key:IV/ciphertext':plaintext:ciphertext:encdec
# CFB128-AES128.Encrypt
Cipher = AES-128-CFB
Key = 2B7E151628AED2A6ABF7158809CF4F3C
IV = 000102030405060708090A0B0C0D0E0F
Operation = ENCRYPT
Plaintext = 6BC1BEE22E409F96E93D7E117393172A
Ciphertext = 3B3FD92EB72DAD20333449F8E83CFB4A
NextIV = 3b3fd92eb72dad20333449f8e83cfb4a

Cipher = AES-128-CFB
Key = 2B7E151628AED2A6ABF7158809CF4F3C
IV = 3B3FD92EB72DAD20333449F8E83CFB4A
Operation = ENCRYPT
Plaintext = AE2D8A571E03AC9C9EB76FAC45AF8E51
Ciphertext = C8A64537A0B3A93FCDE3CDAD9F1CE58B
NextIV = c8a64537a0b3a93fcde3cdad9f1ce58b

Cipher = AES-128-CFB
Key = 2B7E151628AED2A6ABF7158809CF4F3C
IV = C8A64537A0B3A93FCDE3CDAD9F1CE58B
Operation = ENCRYPT
Plaintext = 30C81C46A35CE411E5FBC1191A0A52EF
Ciphertext = 26751F67A3CBB140B1808CF187A4F4DF
NextIV = 26751f67a3cbb140b1808cf187a4f4df

Cipher = AES-128-CFB
Key = 2B7E151628AED2A6ABF7158809CF4F3C
IV = 26751F67A3CBB140B1808CF187A4F4DF
Operation = ENCRYPT
Plaintext = F69F2445DF4F9B17AD2B417BE66C3710
Ciphertext = C04B05357C5D1C0EEAC4C66F9FF7F2E6
NextIV = c04b05357c5d1c0eeac4c66f9ff7f2e6

# CFB128-AES128.Decrypt
Cipher = AES-128-CFB
Key = 2B7E151628AED2A6ABF7158809CF4F3C
IV = 000102030405060708090A0B0C0D0E0F
Operation = DECRYPT
Plaintext = 6BC1BEE22E409F96E93D7E117393172A
Ciphertext = 3B3FD92EB72DAD20333449F8E83CFB4A
NextIV = 3b3fd92eb72dad20333449f8e83cfb4a

Cipher = AES-128-CFB
Key = 2B7E151628AED2A6ABF7158809CF4F3C
IV = 3B3FD92EB72DAD20333449F8E83CFB4A
Operation = DECRYPT
Plaintext = AE2D8A571E03AC9C9EB76FAC45AF8E51
Ciphertext = C8A64537A0B3A93FCDE3CDAD9F1CE58B
NextIV = c8a64537a0b3a93fcde3cdad9f1ce58b

Cipher = AES-128-CFB
Key = 2B7E151628AED2A6ABF7158809CF4F3C
IV = C8A64537A0B3A93FCDE3CDAD9F1CE58B
Operation = DECRYPT
Plaintext = 30C81C46A35CE411E5FBC1191A0A52EF
Ciphertext = 26751F67A3CBB140B1808CF187A4F4DF
NextIV = 26751f67a3cbb140b1808cf187a4f4df

Cipher = AES-128-CFB
Key = 2B7E151628AED2A6ABF7158809CF4F3C
IV = 26751F67A3CBB140B1808CF187A4F4DF
Operation = DECRYPT
Plaintext = F69F2445DF4F9B17AD2B417BE66C3710
Ciphertext = C04B05357C5D1C0EEAC4C66F9FF7F2E6
NextIV = c04b05357c5d1c0eeac4c66f9ff7f2e6

# CFB128-AES192.Encrypt
Cipher = AES-192-CFB
Key = 8E73B0F7DA0E6452C810F32B809079E562F8EAD2522C6B7B
IV = 000102030405060708090A0B0C0D0E0F
Operation = ENCRYPT
Plaintext = 6BC1BEE22E409F96E93D7E117393172A
Ciphertext = CDC80D6FDDF18CAB34C25909C99A4174
NextIV = cdc80d6fddf18cab34c25909c99a4174

Cipher = AES-192-CFB
Key = 8E73B0F7DA0E6452C810F32B809079E562F8EAD2522C6B7B
IV = CDC80D6FDDF18CAB34C25909C99A4174
Operation = ENCRYPT
Plaintext = AE2D8A571E03AC9C9EB76FAC45AF8E51
Ciphertext = 67CE7F7F81173621961A2B70171D3D7A
NextIV = 67ce7f7f81173621961a2b70171d3d7a

Cipher = AES-192-CFB
Key = 8E73B0F7DA0E6452C810F32B809079E562F8EAD2522C6B7B
IV = 67CE7F7F81173621961A2B70171D3D7A
Operation = ENCRYPT
Plaintext = 30C81C46A35CE411E5FBC1191A0A52EF
Ciphertext = 2E1E8A1DD59B88B1C8E60FED1EFAC4C9
NextIV = 2e1e8a1dd59b88b1c8e60fed1efac4c9

Cipher = AES-192-CFB
Key = 8E73B0F7DA0E6452C810F32B809079E562F8EAD2522C6B7B
IV = 2E1E8A1DD59B88B1C8E60FED1EFAC4C9
Operation = ENCRYPT
Plaintext = F69F2445DF4F9B17AD2B417BE66C3710
Ciphertext = C05F9F9CA9834FA042AE8FBA584B09FF
NextIV = c05f9f9ca9834fa042ae8fba584b09ff

# CFB128-AES192.Decrypt
Cipher = AES-192-CFB
Key = 8E73B0F7DA0E6452C810F32B809079E562F8EAD2522C6B7B
IV = 000102030405060708090A0B0C0D0E0F
Operation = DECRYPT
Plaintext = 6BC1BEE22E409F96E93D7E117393172A
Ciphertext = CDC80D6FDDF18CAB34C25909C99A4174
NextIV = cdc80d6fddf18cab34c25909c99a4174

Cipher = AES-192-CFB
Key = 8E73B0F7DA0E6452C810F32B809079E562F8EAD2522C6B7B
IV = CDC80D6FDDF18CAB34C25909C99A4174
Operation = DECRYPT
Plaintext = AE2D8A571E03AC9C9EB76FAC45AF8E51
Ciphertext = 67CE7F7F81173621961A2B70171D3D7A
NextIV = 67ce7f7f81173621961a2b70171d3d7a

Cipher = AES-192-CFB
Key = 8E73B0F7DA0E6452C810F32B809079E562F8EAD2522C6B7B
IV = 67CE7F7F81173621961A2B70171D3D7A
Operation = DECRYPT
Plaintext = 30C81C46A35CE411E5FBC1191A0A52EF
Ciphertext = 2E1E8A1DD59B88B1C8E60FED1EFAC4C9
NextIV = 2e1e8a1dd59b88b1c8e60fed1efac4c9

Cipher = AES-192-CFB
Key = 8E73B0F7DA0E6452C810F32B809079E562F8EAD2522C6B7B
IV = 2E1E8A1DD59B88B1C8E60FED1EFAC4C9
Operation = DECRYPT
Plaintext = F69F2445DF4F9B17AD2B417BE66C3710
Ciphertext = C05F9F9CA9834FA042AE8FBA584B09FF
NextIV = c05f9f9ca9834fa042ae8fba584b09ff

# CFB128-AES256.Encrypt
Cipher = AES-256-CFB
Key = 603DEB1015CA71BE2B73AEF0857D77811F352C073B6108D72D9810A30914DFF4
IV = 000102030405060708090A0B0C0D0E0F
Operation = ENCRYPT
Plaintext = 6BC1BEE22E409F96E93D7E117393172A
Ciphertext = DC7E84BFDA79164B7ECD8486985D3860
NextIV = dc7e84bfda79164b7ecd8486985d3860

Cipher = AES-256-CFB
Key = 603DEB1015CA71BE2B73AEF0857D77811F352C073B6108D72D9810A30914DFF4
IV = DC7E84BFDA79164B7ECD8486985D3860
Operation = ENCRYPT
Plaintext = AE2D8A571E03AC9C9EB76FAC45AF8E51
Ciphertext = 39FFED143B28B1C832113C6331E5407B
NextIV = 39ffed143b28b1c832113c6331e5407b

Cipher = AES-256-CFB
Key = 603DEB1015CA71BE2B73AEF0857D77811F352C073B6108D72D9810A30914DFF4
IV = 39FFED143B28B1C832113C6331E5407B
Operation = ENCRYPT
Plaintext = 30C81C46A35CE411E5FBC1191A0A52EF
Ciphertext = DF10132415E54B92A13ED0A8267AE2F9
NextIV = df10132415e54b92a13ed0a8267ae2f9

Cipher = AES-256-CFB
Key = 603DEB1015CA71BE2B73AEF0857D77811F352C073B6108D72D9810A30914DFF4
IV = DF10132415E54B92A13ED0A8267AE2F9
Operation = ENCRYPT
Plaintext = F69F2445DF4F9B17AD2B417BE66C3710
Ciphertext = 75A385741AB9CEF82031623D55B1E471
NextIV = 75a385741ab9cef82031623d55b1e471

# CFB128-AES256.Decrypt
Cipher = AES-256-CFB
Key = 603DEB1015CA71BE2B73AEF0857D77811F352C073B6108D72D9810A30914DFF4
IV = 000102030405060708090A0B0C0D0E0F
Operation = DECRYPT
Plaintext = 6BC1BEE22E409F96E93D7E117393172A
Ciphertext = DC7E84BFDA79164B7ECD8486985D3860
NextIV = dc7e84bfda79164b7ecd8486985d3860

Cipher = AES-256-CFB
Key = 603DEB1015CA71BE2B73AEF0857D77811F352C073B6108D72D9810A30914DFF4
IV = DC7E84BFDA79164B7ECD8486985D3860
Operation = DECRYPT
Plaintext = AE2D8A571E03AC9C9EB76FAC45AF8E51
Ciphertext = 39FFED143B28B1C832113C6331E5407B
NextIV = 39ffed143b28b1c832113c6331e5407b

Cipher = AES-256-CFB
Key = 603DEB1015CA71BE2B73AEF0857D77811F352C073B6108D72D9810A30914DFF4
IV = 39FFED143B28B1C832113C6331E5407B
Operation = DECRYPT
Plaintext = 30C81C46A35CE411E5FBC1191A0A52EF
Ciphertext = DF10132415E54B92A13ED0A8267AE2F9
NextIV = df10132415e54b92a13ed0a8267ae2f9

Cipher = AES-256-CFB
Key = 603DEB1015CA71BE2B73AEF0857D77811F352C073B6108D72D9810A30914DFF4
IV = DF10132415E54B92A13ED0A8267AE2F9
Operation = DECRYPT
Plaintext = F69F2445DF4F9B17AD2B417BE66C3710
Ciphertext = 75A385741AB9CEF82031623D55B1E471
NextIV = 75a385741ab9cef82031623d55b1e471

# For all OFB encrypts and decrypts, the transformed sequence is
#   AES-bits-CFB:key:IV/output':plaintext:ciphertext:encdec
# OFB-AES128
Cipher = AES-128-OFB
Key = 2B7E151628AED2A6ABF7158809CF4F3C
IV = 000102030405060708090A0B0C0D0E0F
Plaintext = 6BC1BEE22E409F96E93D7E117393172A
Ciphertext = 3B3FD92EB72DAD20333449F8E83CFB4A
NextIV = 50fe67cc996d32b6da0937e99bafec60

Cipher = AES-128-OFB
Key = 2B7E151628AED2A6ABF7158809CF4F3C
IV = 50FE67CC996D32B6DA0937E99BAFEC60
Plaintext = AE2D8A571E03AC9C9EB76FAC45AF8E51
Ciphertext = 7789508D16918F03F53C52DAC54ED825
NextIV = d9a4dada0892239f6b8b3d7680e15674

Cipher = AES-128-OFB
Key = 2B7E151628AED2A6ABF7158809CF4F3C
IV = D9A4DADA0892239F6B8B3D7680E15674
Plaintext = 30C81C46A35CE411E5FBC1191A0A52EF
Ciphertext = 9740051E9C5FECF64344F7A82260EDCC
NextIV = a78819583f0308e7a6bf36b1386abf23

Cipher = AES-128-OFB
Key = 2B7E151628AED2A6ABF7158809CF4F3C
IV = A78819583F0308E7A6BF36B1386ABF23
Plaintext = F69F2445DF4F9B17AD2B417BE66C3710
Ciphertext = 304C6528F659C77866A510D9C1D6AE5E
NextIV = c6d3416d29165c6fcb8e51a227ba994e

# OFB-AES192
Cipher = AES-192-OFB
Key = 8E73B0F7DA0E6452C810F32B809079E562F8EAD2522C6B7B
IV = 000102030405060708090A0B0C0D0E0F
Plaintext = 6BC1BEE22E409F96E93D7E117393172A
Ciphertext = CDC80D6FDDF18CAB34C25909C99A4174
NextIV = a609b38df3b1133dddff2718ba09565e

Cipher = AES-192-OFB
Key = 8E73B0F7DA0E6452C810F32B809079E562F8EAD2522C6B7B
IV = A609B38DF3B1133DDDFF2718BA09565E
Plaintext = AE2D8A571E03AC9C9EB76FAC45AF8E51
Ciphertext = FCC28B8D4C63837C09E81700C1100401
NextIV = 52ef01da52602fe0975f78ac84bf8a50

Cipher = AES-192-OFB
Key = 8E73B0F7DA0E6452C810F32B809079E562F8EAD2522C6B7B
IV = 52EF01DA52602FE0975F78AC84BF8A50
Plaintext = 30C81C46A35CE411E5FBC1191A0A52EF
Ciphertext = 8D9A9AEAC0F6596F559C6D4DAF59A5F2
NextIV = bd5286ac63aabd7eb067ac54b553f71d

Cipher = AES-192-OFB
Key = 8E73B0F7DA0E6452C810F32B809079E562F8EAD2522C6B7B
IV = BD5286AC63AABD7EB067AC54B553F71D
Plaintext = F69F2445DF4F9B17AD2B417BE66C3710
Ciphertext = 6D9F200857CA6C3E9CAC524BD9ACC92A
NextIV = 9b00044d8885f729318713303fc0fe3a

# OFB-AES256
Cipher = AES-256-OFB
Key = 603DEB1015CA71BE2B73AEF0857D77811F352C073B6108D72D9810A30914DFF4
IV = 000102030405060708090A0B0C0D0E0F
Plaintext = 6BC1BEE22E409F96E93D7E117393172A
Ciphertext = DC7E84BFDA79164B7ECD8486985D3860
NextIV = b7bf3a5df43989dd97f0fa97ebce2f4a

Cipher = AES-256-OFB
Key = 603DEB1015CA71BE2B73AEF0857D77811F352C073B6108D72D9810A30914DFF4
IV = B7BF3A5DF43989DD97F0FA97EBCE2F4A
Plaintext = AE2D8A571E03AC9C9EB76FAC45AF8E51
Ciphertext = 4FEBDC6740D20B3AC88F6AD82A4FB08D
NextIV = e1c656305ed1a7a6563805746fe03edc

Cipher = AES-256-OFB
Key = 603DEB1015CA71BE2B73AEF0857D77811F352C073B6108D72D9810A30914DFF4
IV = E1C656305ED1A7A6563805746FE03EDC
Plaintext = 30C81C46A35CE411E5FBC1191A0A52EF
Ciphertext = 71AB47A086E86EEDF39D1C5BBA97C408
NextIV = 41635be625b48afc1666dd42a09d96e7

Cipher = AES-256-OFB
Key = 603DEB1015CA71BE2B73AEF0857D77811F352C073B6108D72D9810A30914DFF4
IV = 41635BE625B48AFC1666DD42A09D96E7
Plaintext = F69F2445DF4F9B17AD2B417BE66C3710
Ciphertext = 0126141D67F37BE8538F5A8BE740E484
NextIV = f7b93058b8bce0fffea41bf0012cd394

Title = AES Counter test vectors from RFC3686

Cipher = aes-128-ctr
Key = AE6852F8121067CC4BF7A5765577F39E
IV = 00000030000000000000000000000001
Operation = ENCRYPT
Plaintext = 53696E676C6520626C6F636B206D7367
Ciphertext = E4095D4FB7A7B3792D6175A3261311B8
NextIV = 00000030000000000000000000000002

Cipher = aes-128-ctr
Key = 7E24067817FAE0D743D6CE1F32539163
IV = 006CB6DBC0543B59DA48D90B00000001
Operation = ENCRYPT
Plaintext = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F
Ciphertext = 5104A106168A72D9790D41EE8EDAD388EB2E1EFC46DA57C8FCE630DF9141BE28
NextIV = 006cb6dbc0543b59da48d90b00000003

Cipher = aes-128-ctr
Key = 7691BE035E5020A8AC6E618529F9A0DC
IV = 00E0017B27777F3F4A1786F000000001
Operation = ENCRYPT
Plaintext = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F20212223
Ciphertext = C1CF48A89F2FFDD9CF4652E9EFDB72D74540A42BDE6D7836D59A5CEAAEF3105325B2072F
NextIV = 00e0017b27777f3f4a1786f000000004

Cipher = aes-192-ctr
Key = 16AF5B145FC9F579C175F93E3BFB0EED863D06CCFDB78515
IV = 0000004836733C147D6D93CB00000001
Operation = ENCRYPT
Plaintext = 53696E676C6520626C6F636B206D7367
Ciphertext = 4B55384FE259C9C84E7935A003CBE928
NextIV = 0000004836733c147d6d93cb00000002

Cipher = aes-192-ctr
Key = 7C5CB2401B3DC33C19E7340819E0F69C678C3DB8E6F6A91A
IV = 0096B03B020C6EADC2CB500D00000001
Operation = ENCRYPT
Plaintext = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F
Ciphertext = 453243FC609B23327EDFAAFA7131CD9F8490701C5AD4A79CFC1FE0FF42F4FB00
NextIV = 0096b03b020c6eadc2cb500d00000003

Cipher = aes-192-ctr
Key = 02BF391EE8ECB159B959617B0965279BF59B60A786D3E0FE
IV = 0007BDFD5CBD60278DCC091200000001
Operation = ENCRYPT
Plaintext = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F20212223
Ciphertext = 96893FC55E5C722F540B7DD1DDF7E758D288BC95C69165884536C811662F2188ABEE0935
NextIV = 0007bdfd5cbd60278dcc091200000004

Cipher = aes-256-ctr
Key = 776BEFF2851DB06F4C8A0542C8696F6C6A81AF1EEC96B4D37FC1D689E6C1C104
IV = 00000060DB5672C97AA8F0B200000001
Operation = ENCRYPT
Plaintext = 53696E676C6520626C6F636B206D7367
Ciphertext = 145AD01DBF824EC7560863DC71E3E0C0
NextIV = 00000060db5672c97aa8f0b200000002

Cipher = aes-256-ctr
Key = F6D66D6BD52D59BB0796365879EFF886C66DD51A5B6A99744B50590C87A23884
IV = 00FAAC24C1585EF15A43D87500000001
Operation = ENCRYPT
Plaintext = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F
Ciphertext = F05E231B3894612C49EE000B804EB2A9B8306B508F839D6A5530831D9344AF1C
NextIV = 00faac24c1585ef15a43d87500000003

Cipher = aes-256-ctr
Key = FF7A617CE69148E4F1726E2F43581DE2AA62D9F805532EDFF1EED687FB54153D
IV = 001CC5B751A51D70A1C1114800000001
Operation = ENCRYPT
Plaintext = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F20212223
Ciphertext = EB6C52821D0BBBF7CE7594462ACA4FAAB407DF866569FD07F48CC0B583D6071F1EC0E6B8
NextIV = 001cc5b751a51d70a1c1114800000004

# Self-generated vector to trigger false carry on big-endian platforms
Cipher = aes-128-ctr
Key = 7E24067817FAE0D743D6CE1F32539163
IV = 00000000000000007FFFFFFFFFFFFFFF
Operation = ENCRYPT
Plaintext = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F
Ciphertext = A2D459477E6432BD74184B1B5370D2243CDC202BC43583B2A55D288CDBBD1E03
NextIV = 00000000000000008000000000000001

# AES CCM 256 bit key
Cipher = aes-256-ccm
Key = 1bde3251d41a8b5ea013c195ae128b218b3e0306376357077ef1c1c78548b92e
IV = 5b8e40746f6b98e00f1d13ff41
AAD = c17a32514eb6103f3249e076d4c871dc97e04b286699e54491dc18f6d734d4c0
Tag = 2024931d73bca480c24a24ece6b6c2bf
Plaintext = 53bd72a97089e312422bf72e242377b3c6ee3e2075389b999c4ef7f28bd2b80a
Ciphertext = 9a5fcccdb4cf04e7293d2775cc76a488f042382d949b43b7d6bb2b9864786726

Cipher = aes-256-ccm
Key = 1bde3251d41a8b5ea013c195ae128b218b3e0306376357077ef1c1c78548b92e
IV = 5b8e40746f6b98e00f1d13ff41
AAD = c17a32514eb6103f3249e076d4c871dc97e04b286699e54491dc18f6d734d4c0
Tag = 2024931d73bca480c24a24ece6b6c2be
Plaintext = 53bd72a97089e312422bf72e242377b3c6ee3e2075389b999c4ef7f28bd2b80a
Ciphertext = 9a5fcccdb4cf04e7293d2775cc76a488f042382d949b43b7d6bb2b9864786726
Operation = DECRYPT
Result = CIPHERUPDATE_ERROR

# Test that the tag can be set after specifying AAD.
Cipher = aes-256-ccm
Key = 1bde3251d41a8b5ea013c195ae128b218b3e0306376357077ef1c1c78548b92e
IV = 5b8e40746f6b98e00f1d13ff41
AAD = c17a32514eb6103f3249e076d4c871dc97e04b286699e54491dc18f6d734d4c0
Tag = 2024931d73bca480c24a24ece6b6c2bf
SetTagLate = TRUE
Operation = DECRYPT
Plaintext = 53bd72a97089e312422bf72e242377b3c6ee3e2075389b999c4ef7f28bd2b80a
Ciphertext = 9a5fcccdb4cf04e7293d2775cc76a488f042382d949b43b7d6bb2b9864786726

# AES GCM test vectors from http://csrc.nist.gov/groups/ST/toolkit/BCM/documents/proposedmodes/gcm/gcm-spec.pdf
Cipher = aes-128-gcm
Key = 00000000000000000000000000000000
IV = 000000000000000000000000
AAD =
Tag = 58e2fccefa7e3061367f1d57a4e7455a
Plaintext =
Ciphertext =

Cipher = aes-128-gcm
Key = 00000000000000000000000000000000
IV = 000000000000000000000000
AAD =
Tag = ab6e47d42cec13bdf53a67b21257bddf
Plaintext = 00000000000000000000000000000000
Ciphertext = 0388dace60b6a392f328c2b971b2fe78
NextIV = 000000000000000000000000

Cipher = aes-128-gcm
Key = feffe9928665731c6d6a8f9467308308
IV = cafebabefacedbaddecaf888
AAD =
Tag = 4d5c2af327cd64a62cf35abd2ba6fab4
Plaintext = d9313225f88406e5a55909c5aff5269a86a7a9531534f7da2e4c303d8a318a721c3c0c95956809532fcf0e2449a6b525b16aedf5aa0de657ba637b391aafd255
Ciphertext = 42831ec2217774244b7221b784d0d49ce3aa212f2c02a4e035c17e2329aca12e21d514b25466931c7d8f6a5aac84aa051ba30b396a0aac973d58e091473f5985
NextIV = cafebabefacedbaddecaf888

Cipher = aes-128-gcm
Key = feffe9928665731c6d6a8f9467308308
IV = cafebabefacedbaddecaf888
AAD = feedfacedeadbeeffeedfacedeadbeefabaddad2
Tag = 5bc94fbc3221a5db94fae95ae7121a47
Plaintext = d9313225f88406e5a55909c5aff5269a86a7a9531534f7da2e4c303d8a318a721c3c0c95956809532fcf0e2449a6b525b16aedf5aa0de657ba637b39
Ciphertext = 42831ec2217774244b7221b784d0d49ce3aa212f2c02a4e035c17e2329aca12e21d514b25466931c7d8f6a5aac84aa051ba30b396a0aac973d58e091
NextIV = cafebabefacedbaddecaf888

Cipher = aes-128-gcm
Key = feffe9928665731c6d6a8f9467308308
IV = cafebabefacedbad
AAD = feedfacedeadbeeffeedfacedeadbeefabaddad2
Tag = 3612d2e79e3b0785561be14aaca2fccb
Plaintext = d9313225f88406e5a55909c5aff5269a86a7a9531534f7da2e4c303d8a318a721c3c0c95956809532fcf0e2449a6b525b16aedf5aa0de657ba637b39
Ciphertext = 61353b4c2806934a777ff51fa22a4755699b2a714fcdc6f83766e5f97b6c742373806900e49f24b22b097544d4896b424989b5e1ebac0f07c23f4598

Cipher = aes-128-gcm
Key = feffe9928665731c6d6a8f9467308308
IV = 9313225df88406e555909c5aff5269aa6a7a9538534f7da1e4c303d2a318a728c3c0c95156809539fcf0e2429a6b525416aedbf5a0de6a57a637b39b
AAD = feedfacedeadbeeffeedfacedeadbeefabaddad2
Tag = 619cc5aefffe0bfa462af43c1699d050
Plaintext = d9313225f88406e5a55909c5aff5269a86a7a9531534f7da2e4c303d8a318a721c3c0c95956809532fcf0e2449a6b525b16aedf5aa0de657ba637b39
Ciphertext = 8ce24998625615b603a033aca13fb894be9112a5c3a211a8ba262a3cca7e2ca701e4a9a4fba43c90ccdcb281d48c7c6fd62875d2aca417034c34aee5

Cipher = aes-128-gcm
Key = feffe9928665731c6d6a8f9467308308
IV = 9313225df88406e555909c5aff5269aa6a7a9538534f7da1e4c303d2a318a728c3c0c95156809539fcf0e2429a6b525416aedbf5a0de6a57a637b39b
AAD = feedfacedeadbeeffeedfacedeadbeefabaddad2
Tag = 619cc5aefffe0bfa462af43c1699d051
Plaintext = d9313225f88406e5a55909c5aff5269a86a7a9531534f7da2e4c303d8a318a721c3c0c95956809532fcf0e2449a6b525b16aedf5aa0de657ba637b39
Ciphertext = 8ce24998625615b603a033aca13fb894be9112a5c3a211a8ba262a3cca7e2ca701e4a9a4fba43c90ccdcb281d48c7c6fd62875d2aca417034c34aee5
Operation = DECRYPT
Result = CIPHERFINAL_ERROR

Cipher = aes-192-gcm
Key = 000000000000000000000000000000000000000000000000
IV = 000000000000000000000000
AAD =
Tag = cd33b28ac773f74ba00ed1f312572435
Plaintext =
Ciphertext =

Cipher = aes-192-gcm
Key = 000000000000000000000000000000000000000000000000
IV = 000000000000000000000000
AAD =
Tag = 2ff58d80033927ab8ef4d4587514f0fb
Plaintext = 00000000000000000000000000000000
Ciphertext = 98e7247c07f0fe411c267e4384b0f600
NextIV = 000000000000000000000000

Cipher = aes-192-gcm
Key = feffe9928665731c6d6a8f9467308308feffe9928665731c
IV = cafebabefacedbaddecaf888
AAD =
Tag = 9924a7c8587336bfb118024db8674a14
Plaintext = d9313225f88406e5a55909c5aff5269a86a7a9531534f7da2e4c303d8a318a721c3c0c95956809532fcf0e2449a6b525b16aedf5aa0de657ba637b391aafd255
Ciphertext = 3980ca0b3c00e841eb06fac4872a2757859e1ceaa6efd984628593b40ca1e19c7d773d00c144c525ac619d18c84a3f4718e2448b2fe324d9ccda2710acade256
NextIV = cafebabefacedbaddecaf888

Cipher = aes-192-gcm
Key = feffe9928665731c6d6a8f9467308308feffe9928665731c
IV = cafebabefacedbaddecaf888
AAD = feedfacedeadbeeffeedfacedeadbeefabaddad2
Tag = 2519498e80f1478f37ba55bd6d27618c
Plaintext = d9313225f88406e5a55909c5aff5269a86a7a9531534f7da2e4c303d8a318a721c3c0c95956809532fcf0e2449a6b525b16aedf5aa0de657ba637b39
Ciphertext = 3980ca0b3c00e841eb06fac4872a2757859e1ceaa6efd984628593b40ca1e19c7d773d00c144c525ac619d18c84a3f4718e2448b2fe324d9ccda2710
NextIV = cafebabefacedbaddecaf888

Cipher = aes-192-gcm
Key = feffe9928665731c6d6a8f9467308308feffe9928665731c
IV = cafebabefacedbad
AAD = feedfacedeadbeeffeedfacedeadbeefabaddad2
Tag = 65dcc57fcf623a24094fcca40d3533f8
Plaintext = d9313225f88406e5a55909c5aff5269a86a7a9531534f7da2e4c303d8a318a721c3c0c95956809532fcf0e2449a6b525b16aedf5aa0de657ba637b39
Ciphertext = 0f10f599ae14a154ed24b36e25324db8c566632ef2bbb34f8347280fc4507057fddc29df9a471f75c66541d4d4dad1c9e93a19a58e8b473fa0f062f7

Cipher = aes-192-gcm
Key = feffe9928665731c6d6a8f9467308308feffe9928665731c
IV = 9313225df88406e555909c5aff5269aa6a7a9538534f7da1e4c303d2a318a728c3c0c95156809539fcf0e2429a6b525416aedbf5a0de6a57a637b39b
AAD = feedfacedeadbeeffeedfacedeadbeefabaddad2
Tag = dcf566ff291c25bbb8568fc3d376a6d9
Plaintext = d9313225f88406e5a55909c5aff5269a86a7a9531534f7da2e4c303d8a318a721c3c0c95956809532fcf0e2449a6b525b16aedf5aa0de657ba637b39
Ciphertext = d27e88681ce3243c4830165a8fdcf9ff1de9a1d8e6b447ef6ef7b79828666e4581e79012af34ddd9e2f037589b292db3e67c036745fa22e7e9b7373b

Cipher = aes-192-gcm
Key = feffe9928665731c6d6a8f9467308308feffe9928665731c
IV = 9313225df88406e555909c5aff5269aa6a7a9538534f7da1e4c303d2a318a728c3c0c95156809539fcf0e2429a6b525416aedbf5a0de6a57a637b39b
AAD = feedfacedeadbeeffeedfacedeadbeefabaddad2
Tag = dcf566ff291c25bbb8568fc3d376a6d8
Plaintext = d9313225f88406e5a55909c5aff5269a86a7a9531534f7da2e4c303d8a318a721c3c0c95956809532fcf0e2449a6b525b16aedf5aa0de657ba637b39
Ciphertext = d27e88681ce3243c4830165a8fdcf9ff1de9a1d8e6b447ef6ef7b79828666e4581e79012af34ddd9e2f037589b292db3e67c036745fa22e7e9b7373b
Operation = DECRYPT
Result = CIPHERFINAL_ERROR

Cipher = aes-256-gcm
Key = 0000000000000000000000000000000000000000000000000000000000000000
IV = 000000000000000000000000
AAD =
Tag = 530f8afbc74536b9a963b4f1c4cb738b
Plaintext =
Ciphertext =

Cipher = aes-256-gcm
Key = 0000000000000000000000000000000000000000000000000000000000000000
IV = 000000000000000000000000
AAD =
Tag = d0d1c8a799996bf0265b98b5d48ab919
Plaintext = 00000000000000000000000000000000
Ciphertext = cea7403d4d606b6e074ec5d3baf39d18
NextIV = 000000000000000000000000

Cipher = aes-256-gcm
Key = feffe9928665731c6d6a8f9467308308feffe9928665731c6d6a8f9467308308
IV = cafebabefacedbaddecaf888
AAD =
Tag = b094dac5d93471bdec1a502270e3cc6c
Plaintext = d9313225f88406e5a55909c5aff5269a86a7a9531534f7da2e4c303d8a318a721c3c0c95956809532fcf0e2449a6b525b16aedf5aa0de657ba637b391aafd255
Ciphertext = 522dc1f099567d07f47f37a32a84427d643a8cdcbfe5c0c97598a2bd2555d1aa8cb08e48590dbb3da7b08b1056828838c5f61e6393ba7a0abcc9f662898015ad
NextIV = cafebabefacedbaddecaf888

Cipher = aes-256-gcm
Key = feffe9928665731c6d6a8f9467308308feffe9928665731c6d6a8f9467308308
IV = cafebabefacedbaddecaf888
AAD = feedfacedeadbeeffeedfacedeadbeefabaddad2
Tag = 76fc6ece0f4e1768cddf8853bb2d551b
Plaintext = d9313225f88406e5a55909c5aff5269a86a7a9531534f7da2e4c303d8a318a721c3c0c95956809532fcf0e2449a6b525b16aedf5aa0de657ba637b39
Ciphertext = 522dc1f099567d07f47f37a32a84427d643a8cdcbfe5c0c97598a2bd2555d1aa8cb08e48590dbb3da7b08b1056828838c5f61e6393ba7a0abcc9f662
NextIV = cafebabefacedbaddecaf888

Cipher = aes-256-gcm
Key = feffe9928665731c6d6a8f9467308308feffe9928665731c6d6a8f9467308308
IV = cafebabefacedbad
AAD = feedfacedeadbeeffeedfacedeadbeefabaddad2
Tag = 3a337dbf46a792c45e454913fe2ea8f2
Plaintext = d9313225f88406e5a55909c5aff5269a86a7a9531534f7da2e4c303d8a318a721c3c0c95956809532fcf0e2449a6b525b16aedf5aa0de657ba637b39
Ciphertext = c3762df1ca787d32ae47c13bf19844cbaf1ae14d0b976afac52ff7d79bba9de0feb582d33934a4f0954cc2363bc73f7862ac430e64abe499f47c9b1f

Cipher = aes-256-gcm
Key = feffe9928665731c6d6a8f9467308308feffe9928665731c6d6a8f9467308308
IV = 9313225df88406e555909c5aff5269aa6a7a9538534f7da1e4c303d2a318a728c3c0c95156809539fcf0e2429a6b525416aedbf5a0de6a57a637b39b
AAD = feedfacedeadbeeffeedfacedeadbeefabaddad2
Tag = a44a8266ee1c8eb0c8b5d4cf5ae9f19a
Plaintext = d9313225f88406e5a55909c5aff5269a86a7a9531534f7da2e4c303d8a318a721c3c0c95956809532fcf0e2449a6b525b16aedf5aa0de657ba637b39
Ciphertext = 5a8def2f0c9e53f1f75d7853659e2a20eeb2b22aafde6419a058ab4f6f746bf40fc0c3b780f244452da3ebf1c5d82cdea2418997200ef82e44ae7e3f

Cipher = aes-256-gcm
Key = feffe9928665731c6d6a8f9467308308feffe9928665731c6d6a8f9467308308
IV = 9313225df88406e555909c5aff5269aa6a7a9538534f7da1e4c303d2a318a728c3c0c95156809539fcf0e2429a6b525416aedbf5a0de6a57a637b39b
AAD = feedfacedeadbeeffeedfacedeadbeefabaddad2
Tag = a44a8266ee1c8eb0c8b5d4cf5ae9f19b
Plaintext = d9313225f88406e5a55909c5aff5269a86a7a9531534f7da2e4c303d8a318a721c3c0c95956809532fcf0e2449a6b525b16aedf5aa0de657ba637b39
Ciphertext = 5a8def2f0c9e53f1f75d7853659e2a20eeb2b22aafde6419a058ab4f6f746bf40fc0c3b780f244452da3ebf1c5d82cdea2418997200ef82e44ae7e3f
Operation = DECRYPT
Result = CIPHERFINAL_ERROR

# local add-ons, primarily streaming ghash tests
# 128 bytes aad
Cipher = aes-128-gcm
Key = 00000000000000000000000000000000
IV = 000000000000000000000000
AAD = d9313225f88406e5a55909c5aff5269a86a7a9531534f7da2e4c303d8a318a721c3c0c95956809532fcf0e2449a6b525b16aedf5aa0de657ba637b391aafd255522dc1f099567d07f47f37a32a84427d643a8cdcbfe5c0c97598a2bd2555d1aa8cb08e48590dbb3da7b08b1056828838c5f61e6393ba7a0abcc9f662898015ad
Tag = 5fea793a2d6f974d37e68e0cb8ff9492
Plaintext =
Ciphertext =

# 48 bytes plaintext
Cipher = aes-128-gcm
Key = 00000000000000000000000000000000
IV = 000000000000000000000000
AAD =
Tag = 9dd0a376b08e40eb00c35f29f9ea61a4
Plaintext = 000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000
Ciphertext = 0388dace60b6a392f328c2b971b2fe78f795aaab494b5923f7fd89ff948bc1e0200211214e7394da2089b6acd093abe0
NextIV = 000000000000000000000000

# 80 bytes plaintext
Cipher = aes-128-gcm
Key = 00000000000000000000000000000000
IV = 000000000000000000000000
AAD =
Tag = 98885a3a22bd4742fe7b72172193b163
Plaintext = 0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000
Ciphertext = 0388dace60b6a392f328c2b971b2fe78f795aaab494b5923f7fd89ff948bc1e0200211214e7394da2089b6acd093abe0c94da219118e297d7b7ebcbcc9c388f28ade7d85a8ee35616f7124a9d5270291
NextIV = 000000000000000000000000

# 128 bytes plaintext
Cipher = aes-128-gcm
Key = 00000000000000000000000000000000
IV = 000000000000000000000000
AAD =
Tag = cac45f60e31efd3b5a43b98a22ce1aa1
Plaintext = 0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000
Ciphertext = 0388dace60b6a392f328c2b971b2fe78f795aaab494b5923f7fd89ff948bc1e0200211214e7394da2089b6acd093abe0c94da219118e297d7b7ebcbcc9c388f28ade7d85a8ee35616f7124a9d527029195b84d1b96c690ff2f2de30bf2ec89e00253786e126504f0dab90c48a30321de3345e6b0461e7c9e6c6b7afedde83f40
NextIV = 000000000000000000000000

# 192 bytes plaintext, iv is chosen so that initial counter LSB is 0xFF
Cipher = aes-128-gcm
Key = 00000000000000000000000000000000
IV = ffffffff000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000
AAD =
Tag = 566f8ef683078bfdeeffa869d751a017
Plaintext = 000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000
Ciphertext = 56b3373ca9ef6e4a2b64fe1e9a17b61425f10d47a75a5fce13efc6bc784af24f4141bdd48cf7c770887afd573cca5418a9aeffcd7c5ceddfc6a78397b9a85b499da558257267caab2ad0b23ca476a53cb17fb41c4b8b475cb4f3f7165094c229c9e8c4dc0a2a5ff1903e501511221376a1cdb8364c5061a20cae74bc4acd76ceb0abc9fd3217ef9f8c90be402ddf6d8697f4f880dff15bfb7a6b28241ec8fe183c2d59e3f9dfff653c7126f0acb9e64211f42bae12af462b1070bef1ab5e3606

# 240 bytes plaintext, iv is chosen so that initial counter LSB is 0xFF
Cipher = aes-128-gcm
Key = 00000000000000000000000000000000
IV = ffffffff000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000
AAD =
Tag = fd0c7011ff07f0071324bdfb2d0f3a29
Plaintext = 000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000
Ciphertext = 56b3373ca9ef6e4a2b64fe1e9a17b61425f10d47a75a5fce13efc6bc784af24f4141bdd48cf7c770887afd573cca5418a9aeffcd7c5ceddfc6a78397b9a85b499da558257267caab2ad0b23ca476a53cb17fb41c4b8b475cb4f3f7165094c229c9e8c4dc0a2a5ff1903e501511221376a1cdb8364c5061a20cae74bc4acd76ceb0abc9fd3217ef9f8c90be402ddf6d8697f4f880dff15bfb7a6b28241ec8fe183c2d59e3f9dfff653c7126f0acb9e64211f42bae12af462b1070bef1ab5e3606872ca10dee15b3249b1a1b958f23134c4bccb7d03200bce420a2f8eb66dcf3644d1423c1b5699003c13ecef4bf38a3b6

# 288 bytes plaintext, iv is chosen so that initial counter LSB is 0xFF
Cipher = aes-128-gcm
Key = 00000000000000000000000000000000
IV = ffffffff000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000
AAD =
Tag = 8b307f6b33286d0ab026a9ed3fe1e85f
Plaintext = 000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000
Ciphertext = 56b3373ca9ef6e4a2b64fe1e9a17b61425f10d47a75a5fce13efc6bc784af24f4141bdd48cf7c770887afd573cca5418a9aeffcd7c5ceddfc6a78397b9a85b499da558257267caab2ad0b23ca476a53cb17fb41c4b8b475cb4f3f7165094c229c9e8c4dc0a2a5ff1903e501511221376a1cdb8364c5061a20cae74bc4acd76ceb0abc9fd3217ef9f8c90be402ddf6d8697f4f880dff15bfb7a6b28241ec8fe183c2d59e3f9dfff653c7126f0acb9e64211f42bae12af462b1070bef1ab5e3606872ca10dee15b3249b1a1b958f23134c4bccb7d03200bce420a2f8eb66dcf3644d1423c1b5699003c13ecef4bf38a3b60eedc34033bac1902783dc6d89e2e774188a439c7ebcc0672dbda4ddcfb2794613b0be41315ef778708a70ee7d75165c

# 80 bytes plaintext, submitted by Intel
Cipher = aes-128-gcm
Key = 843ffcf5d2b72694d19ed01d01249412
IV = dbcca32ebf9b804617c3aa9e
AAD = 00000000000000000000000000000000101112131415161718191a1b1c1d1e1f
Tag = ********************************
Plaintext = 000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f202122232425262728292a2b2c2d2e2f303132333435363738393a3b3c3d3e3f404142434445464748494a4b4c4d4e4f
Ciphertext = 6268c6fa2a80b2d137467f092f657ac04d89be2beaa623d61b5a868c8f03ff95d3dcee23ad2f1ab3a6c80eaf4b140eb05de3457f0fbc111a6b43d0763aa422a3013cf1dc37fe417d1fbfc449b75d4cc5
NextIV = dbcca32ebf9b804617c3aa9e

# Single byte IV test cases from
# https://csrc.nist.gov/Projects/Cryptographic-Algorithm-Validation-Program/CAVP-TESTING-BLOCK-CIPHER-MODES#GCMVS

Title = AES GCM single byte IV tests

Cipher = aes-128-gcm
Key = 1672c3537afa82004c6b8a46f6f0d026
IV = 05
Tag = 8e2ad721f9455f74d8b53d3141f27e8e
Plaintext = 
Ciphertext = 

Cipher = aes-128-gcm
Key = 6471e11b5a559f84d196160c64ced95a
IV = 1a
AAD = 147c70bd944ae51289717bdbdac86511fa3a43a2
Tag = b7b80d314024261bafd7d218
Plaintext = 
Ciphertext = 

Cipher = aes-128-gcm
Key = f0d44d3c8c8ff4d2aab5c315e77a5cff
IV = 3e
Tag = fe0c50de4c5443e4c9380a7df0
Plaintext = ecb7e9263c3080cb8861ffc5afdf3fe8
Ciphertext = b5cfd9141ea43d5c16e28666c3840805

Cipher = aes-128-gcm
Key = c422ac0266dc9b5ddc391d9cdb72257e
IV = c7
Tag = 95c1e410d4ea59dda50d84162b49
Plaintext = 6149277175c02a462dab219b80d15641a4c033dfa4c9a81de1765f0276008fa2
AAD = aa56b160c5d51a4aa400e798c825aaa27d6693de
Ciphertext = 39d9f9b2348214270f1ca18b521f7485c5390c8e993eb7ff79a5be99c7d523f1

Cipher = aes-128-gcm
Key = 72c5683d1e0173afcd92002c26ae3ea5
IV = 4c
Plaintext = f69623243c6bb924a5502dd270f730baf3fd4a0c10b889fb42a12b086d427786
AAD = f6cecdc9118777b875ef256cf92a3dc0cf208149
Ciphertext = 188e68729648fa9b4a202ed2313be860c593600ac8419c75c55859faa585bc0e
Tag = bb4e889b58b9716f6556c676bd59

Cipher = aes-192-gcm
Key = d49dfb35287db0b4bce518412c4e84229a9bf8461e11e8a9
IV = 0b
Tag = b1ecd3e6f27346c47e7fff898a418a0f
Plaintext = 
Ciphertext = 

Cipher = aes-192-gcm
Key = 842c899c5d7c3598676081e9e25fdd030d3e4490a3268fd0
IV = 9e
AAD = ed1162a2d95e0c248ebf9197cb03ad2d
Tag = c81a9ff65112b75911b22523926bf39f
Plaintext = 
Ciphertext = 

Cipher = aes-192-gcm
Key = d273319d24f356bf77cb6a56450bd0d464f476a18863840a
IV = 13
Tag = 729b5b765d008e982d9e5fae7c998e
Plaintext = c07e127ceb93a3d8d166d1e3fa2565e4
Ciphertext = 0c44c7b5fb1520bdb493bec38e7846e6

Cipher = aes-192-gcm
Key = 1120b14c39f4240e2cc63285d8b7d59d44c993fddc77d456
IV = 66
AAD = c25f9b36d06547a64442e534b1fe3bb120a55292060a3c8611b75313fb5475333eeedac642ee2eed1dd110643ce8aff8
Tag = ebf90469dae01e4be5b0ce86
Plaintext = 8eb78ac034ce4f182fb9ee68d71ed3f7
Ciphertext = ec13d51a2c37ea48beb32f766e1e42a1

Cipher = aes-256-gcm
Key = a70f2f3c96b952b2d177fce5d5edac7c939259ebd3ff7354df3d86100f0be5ac
IV = 69
Tag = 2d484f834a313bf3f9a25f0a7604a869
Plaintext = 
Ciphertext = 

Cipher = aes-256-gcm
Key = c639f716597a86afd12319199e21a62b1fc0277a70e3ca120bd3ff745be88604
IV = 29
AAD = 20fda1db6911d160121dc3c48e5f19b2
Tag = 221a3398f20d0d9fe913f33a6cd413d3
Plaintext = 
Ciphertext = 

Cipher = aes-256-gcm
Key = 9473c28f6e978eb15e1967b888282aa6b078d320034fe5f40f8bb68674f1ecda
IV = 0a
Tag = 03337df7e1e68d77706abef9edaf5e07
Plaintext = 2d2e2798c10bcfcce742e92d3c390fef
Ciphertext = c4e5ab2c6a4316e57c6c37d2c2acb42c

Cipher = aes-256-gcm
Key = bb4635d766dd0e4a7019d1724c736e1f2c016af9e29e7d3aa2c0de23e780af26
IV = ab
AAD = 0f85c7dbeb674b7a70c35125d3619350
Tag = 6bd54e5184eb300934b392c32b7c1a6e
Plaintext = d05ce878d94662d1520b184b4bef3c45
Ciphertext = 51baa26a6a719c1600645ff3bfdfa53b

Title = AES XTS test vectors from IEEE Std 1619-2007

# Using the same key twice for encryption is always banned.
Cipher = aes-128-xts
Operation = ENCRYPT
Key = 0000000000000000000000000000000000000000000000000000000000000000
IV = 00000000000000000000000000000000
Plaintext = 0000000000000000000000000000000000000000000000000000000000000000
Ciphertext = 917cf69ebd68b2ec9b9fe9a3eadda692cd43d2f59598ed858c02c2652fbf922e
Result = KEY_SET_ERROR

# Using the same key twice for decryption is banned in FIPS mode.
Availablein = fips
Cipher = aes-128-xts
Operation = DECRYPT
Key = 0000000000000000000000000000000000000000000000000000000000000000
IV = 00000000000000000000000000000000
Plaintext = 0000000000000000000000000000000000000000000000000000000000000000
Ciphertext = 917cf69ebd68b2ec9b9fe9a3eadda692cd43d2f59598ed858c02c2652fbf922e
Result = KEY_SET_ERROR

# Using the same key twice for decryption is allowed outside of FIPS mode.
Availablein = default
Cipher = aes-128-xts
Operation = DECRYPT
Key = 0000000000000000000000000000000000000000000000000000000000000000
IV = 00000000000000000000000000000000
Plaintext = 0000000000000000000000000000000000000000000000000000000000000000
Ciphertext = 917cf69ebd68b2ec9b9fe9a3eadda692cd43d2f59598ed858c02c2652fbf922e

Cipher = aes-128-xts
Key = 1111111111111111111111111111111122222222222222222222222222222222
IV = 33333333330000000000000000000000
Plaintext = 4444444444444444444444444444444444444444444444444444444444444444
Ciphertext = c454185e6a16936e39334038acef838bfb186fff7480adc4289382ecd6d394f0

Cipher = aes-128-xts
Key = fffefdfcfbfaf9f8f7f6f5f4f3f2f1f022222222222222222222222222222222
IV = 33333333330000000000000000000000
Plaintext = 4444444444444444444444444444444444444444444444444444444444444444
Ciphertext = af85336b597afc1a900b2eb21ec949d292df4c047e0b21532186a5971a227a89

Cipher = aes-128-xts
Key = 2718281828459045235360287471352631415926535897932384626433832795
IV = 00000000000000000000000000000000
Plaintext = 000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f202122232425262728292a2b2c2d2e2f303132333435363738393a3b3c3d3e3f404142434445464748494a4b4c4d4e4f505152535455565758595a5b5c5d5e5f606162636465666768696a6b6c6d6e6f707172737475767778797a7b7c7d7e7f808182838485868788898a8b8c8d8e8f909192939495969798999a9b9c9d9e9fa0a1a2a3a4a5a6a7a8a9aaabacadaeafb0b1b2b3b4b5b6b7b8b9babbbcbdbebfc0c1c2c3c4c5c6c7c8c9cacbcccdcecfd0d1d2d3d4d5d6d7d8d9dadbdcdddedfe0e1e2e3e4e5e6e7e8e9eaebecedeeeff0f1f2f3f4f5f6f7f8f9fafbfcfdfeff000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f202122232425262728292a2b2c2d2e2f303132333435363738393a3b3c3d3e3f404142434445464748494a4b4c4d4e4f505152535455565758595a5b5c5d5e5f606162636465666768696a6b6c6d6e6f707172737475767778797a7b7c7d7e7f808182838485868788898a8b8c8d8e8f909192939495969798999a9b9c9d9e9fa0a1a2a3a4a5a6a7a8a9aaabacadaeafb0b1b2b3b4b5b6b7b8b9babbbcbdbebfc0c1c2c3c4c5c6c7c8c9cacbcccdcecfd0d1d2d3d4d5d6d7d8d9dadbdcdddedfe0e1e2e3e4e5e6e7e8e9eaebecedeeeff0f1f2f3f4f5f6f7f8f9fafbfcfdfeff
Ciphertext = 27a7479befa1d476489f308cd4cfa6e2a96e4bbe3208ff25287dd3819616e89cc78cf7f5e543445f8333d8fa7f56000005279fa5d8b5e4ad40e736ddb4d35412328063fd2aab53e5ea1e0a9f332500a5df9487d07a5c92cc512c8866c7e860ce93fdf166a24912b422976146ae20ce846bb7dc9ba94a767aaef20c0d61ad02655ea92dc4c4e41a8952c651d33174be51a10c421110e6d81588ede82103a252d8a750e8768defffed9122810aaeb99f9172af82b604dc4b8e51bcb08235a6f4341332e4ca60482a4ba1a03b3e65008fc5da76b70bf1690db4eae29c5f1badd03c5ccf2a55d705ddcd86d449511ceb7ec30bf12b1fa35b913f9f747a8afd1b130e94bff94effd01a91735ca1726acd0b197c4e5b03393697e126826fb6bbde8ecc1e08298516e2c9ed03ff3c1b7860f6de76d4cecd94c8119855ef5297ca67e9f3e7ff72b1e99785ca0a7e7720c5b36dc6d72cac9574c8cbbc2f801e23e56fd344b07f22154beba0f08ce8891e643ed995c94d9a69c9f1b5f499027a78572aeebd74d20cc39881c213ee770b1010e4bea718846977ae119f7a023ab58cca0ad752afe656bb3c17256a9f6e9bf19fdd5a38fc82bbe872c5539edb609ef4f79c203ebb140f2e583cb2ad15b4aa5b655016a8449277dbd477ef2c8d6c017db738b18deb4a427d1923ce3ff262735779a418f20a282df920147beabe421ee5319d0568

Cipher = aes-128-xts
Key = 2718281828459045235360287471352631415926535897932384626433832795
IV = 01000000000000000000000000000000
Plaintext = 27a7479befa1d476489f308cd4cfa6e2a96e4bbe3208ff25287dd3819616e89cc78cf7f5e543445f8333d8fa7f56000005279fa5d8b5e4ad40e736ddb4d35412328063fd2aab53e5ea1e0a9f332500a5df9487d07a5c92cc512c8866c7e860ce93fdf166a24912b422976146ae20ce846bb7dc9ba94a767aaef20c0d61ad02655ea92dc4c4e41a8952c651d33174be51a10c421110e6d81588ede82103a252d8a750e8768defffed9122810aaeb99f9172af82b604dc4b8e51bcb08235a6f4341332e4ca60482a4ba1a03b3e65008fc5da76b70bf1690db4eae29c5f1badd03c5ccf2a55d705ddcd86d449511ceb7ec30bf12b1fa35b913f9f747a8afd1b130e94bff94effd01a91735ca1726acd0b197c4e5b03393697e126826fb6bbde8ecc1e08298516e2c9ed03ff3c1b7860f6de76d4cecd94c8119855ef5297ca67e9f3e7ff72b1e99785ca0a7e7720c5b36dc6d72cac9574c8cbbc2f801e23e56fd344b07f22154beba0f08ce8891e643ed995c94d9a69c9f1b5f499027a78572aeebd74d20cc39881c213ee770b1010e4bea718846977ae119f7a023ab58cca0ad752afe656bb3c17256a9f6e9bf19fdd5a38fc82bbe872c5539edb609ef4f79c203ebb140f2e583cb2ad15b4aa5b655016a8449277dbd477ef2c8d6c017db738b18deb4a427d1923ce3ff262735779a418f20a282df920147beabe421ee5319d0568
Ciphertext = 264d3ca8512194fec312c8c9891f279fefdd608d0c027b60483a3fa811d65ee59d52d9e40ec5672d81532b38b6b089ce951f0f9c35590b8b978d175213f329bb1c2fd30f2f7f30492a61a532a79f51d36f5e31a7c9a12c286082ff7d2394d18f783e1a8e72c722caaaa52d8f065657d2631fd25bfd8e5baad6e527d763517501c68c5edc3cdd55435c532d7125c8614deed9adaa3acade5888b87bef641c4c994c8091b5bcd387f3963fb5bc37aa922fbfe3df4e5b915e6eb514717bdd2a74079a5073f5c4bfd46adf7d282e7a393a52579d11a028da4d9cd9c77124f9648ee383b1ac763930e7162a8d37f350b2f74b8472cf09902063c6b32e8c2d9290cefbd7346d1c779a0df50edcde4531da07b099c638e83a755944df2aef1aa31752fd323dcb710fb4bfbb9d22b925bc3577e1b8949e729a90bbafeacf7f7879e7b1147e28ba0bae940db795a61b15ecf4df8db07b824bb062802cc98a9545bb2aaeed77cb3fc6db15dcd7d80d7d5bc406c4970a3478ada8899b329198eb61c193fb6275aa8ca340344a75a862aebe92eee1ce032fd950b47d7704a3876923b4ad62844bf4a09c4dbe8b4397184b7471360c9564880aedddb9baa4af2e75394b08cd32ff479c57a07d3eab5d54de5f9738b8d27f27a9f0ab11799d7b7ffefb2704c95c6ad12c39f1e867a4b7b1d7818a4b753dfd2a89ccb45e001a03a867b187f225dd

Cipher = aes-128-xts
Key = 2718281828459045235360287471352631415926535897932384626433832795
IV = 02000000000000000000000000000000
Plaintext = 264d3ca8512194fec312c8c9891f279fefdd608d0c027b60483a3fa811d65ee59d52d9e40ec5672d81532b38b6b089ce951f0f9c35590b8b978d175213f329bb1c2fd30f2f7f30492a61a532a79f51d36f5e31a7c9a12c286082ff7d2394d18f783e1a8e72c722caaaa52d8f065657d2631fd25bfd8e5baad6e527d763517501c68c5edc3cdd55435c532d7125c8614deed9adaa3acade5888b87bef641c4c994c8091b5bcd387f3963fb5bc37aa922fbfe3df4e5b915e6eb514717bdd2a74079a5073f5c4bfd46adf7d282e7a393a52579d11a028da4d9cd9c77124f9648ee383b1ac763930e7162a8d37f350b2f74b8472cf09902063c6b32e8c2d9290cefbd7346d1c779a0df50edcde4531da07b099c638e83a755944df2aef1aa31752fd323dcb710fb4bfbb9d22b925bc3577e1b8949e729a90bbafeacf7f7879e7b1147e28ba0bae940db795a61b15ecf4df8db07b824bb062802cc98a9545bb2aaeed77cb3fc6db15dcd7d80d7d5bc406c4970a3478ada8899b329198eb61c193fb6275aa8ca340344a75a862aebe92eee1ce032fd950b47d7704a3876923b4ad62844bf4a09c4dbe8b4397184b7471360c9564880aedddb9baa4af2e75394b08cd32ff479c57a07d3eab5d54de5f9738b8d27f27a9f0ab11799d7b7ffefb2704c95c6ad12c39f1e867a4b7b1d7818a4b753dfd2a89ccb45e001a03a867b187f225dd
Ciphertext = fa762a3680b76007928ed4a4f49a9456031b704782e65e16cecb54ed7d017b5e18abd67b338e81078f21edb7868d901ebe9c731a7c18b5e6dec1d6a72e078ac9a4262f860beefa14f4e821018272e411a951502b6e79066e84252c3346f3aa62344351a291d4bedc7a07618bdea2af63145cc7a4b8d4070691ae890cd65733e7946e9021a1dffc4c59f159425ee6d50ca9b135fa6162cea18a939838dc000fb386fad086acce5ac07cb2ece7fd580b00cfa5e98589631dc25e8e2a3daf2ffdec26531659912c9d8f7a15e5865ea8fb5816d6207052bd7128cd743c12c8118791a4736811935eb982a532349e31dd401e0b660a568cb1a4711f552f55ded59f1f15bf7196b3ca12a91e488ef59d64f3a02bf45239499ac6176ae321c4a211ec545365971c5d3f4f09d4eb139bfdf2073d33180b21002b65cc9865e76cb24cd92c874c24c18350399a936ab3637079295d76c417776b94efce3a0ef7206b15110519655c956cbd8b2489405ee2b09a6b6eebe0c53790a12a8998378b33a5b71159625f4ba49d2a2fdba59fbf0897bc7aabd8d707dc140a80f0f309f835d3da54ab584e501dfa0ee977fec543f74186a802b9a37adb3e8291eca04d66520d229e60401e7282bef486ae059aa70696e0e305d777140a7a883ecdcb69b9ff938e8a4231864c69ca2c2043bed007ff3e605e014bcf518138dc3a25c5e236171a2d01d6

Cipher = aes-128-xts
Key = 2718281828459045235360287471352631415926535897932384626433832795
IV = fd000000000000000000000000000000
Plaintext = 8e41b78c390b5af9d758bb214a67e9f6bf7727b09ac6124084c37611398fa45daad94868600ed391fb1acd4857a95b466e62ef9f4b377244d1c152e7b30d731aad30c716d214b707aed99eb5b5e580b3e887cf7497465651d4b60e6042051da3693c3b78c14489543be8b6ad0ba629565bba202313ba7b0d0c94a3252b676f46cc02ce0f8a7d34c0ed229129673c1f61aed579d08a9203a25aac3a77e9db60267996db38df637356d9dcd1632e369939f2a29d89345c66e05066f1a3677aef18dea4113faeb629e46721a66d0a7e785d3e29af2594eb67dfa982affe0aac058f6e15864269b135418261fc3afb089472cf68c45dd7f231c6249ba0255e1e033833fc4d00a3fe02132d7bc3873614b8aee34273581ea0325c81f0270affa13641d052d36f0757d484014354d02d6883ca15c24d8c3956b1bd027bcf41f151fd8023c5340e5606f37e90fdb87c86fb4fa634b3718a30bace06a66eaf8f63c4aa3b637826a87fe8cfa44282e92cb1615af3a28e53bc74c7cba1a0977be9065d0c1a5dec6c54ae38d37f37aa35283e048e5530a85c4e7a29d7b92ec0c3169cdf2a805c7604bce60049b9fb7b8eaac10f51ae23794ceba68bb58112e293b9b692ca721b37c662f8574ed4dba6f88e170881c82cddc1034a0ca7e284bf0962b6b26292d836fa9f73c1ac770eef0f2d3a1eaf61d3e03555fd424eedd67e18a18094f888
Ciphertext = d55f684f81f4426e9fde92a5ff02df2ac896af63962888a97910c1379e20b0a3b1db613fb7fe2e07004329ea5c22bfd33e3dbe4cf58cc608c2c26c19a2e2fe22f98732c2b5cb844cc6c0702d91e1d50fc4382a7eba5635cd602432a2306ac4ce82f8d70c8d9bc15f918fe71e74c622d5cf71178bf6e0b9cc9f2b41dd8dbe441c41cd0c73a6dc47a348f6702f9d0e9b1b1431e948e299b9ec2272ab2c5f0c7be86affa5dec87a0bee81d3d50007edaa2bcfccb35605155ff36ed8edd4a40dcd4b243acd11b2b987bdbfaf91a7cac27e9c5aea525ee53de7b2d3332c8644402b823e94a7db26276d2d23aa07180f76b4fd29b9c0823099c9d62c519880aee7e9697617c1497d47bf3e571950311421b6b734d38b0db91eb85331b91ea9f61530f54512a5a52a4bad589eb69781d537f23297bb459bdad2948a29e1550bf4787e0be95bb173cf5fab17dab7a13a052a63453d97ccec1a321954886b7a1299faaeecae35c6eaaca753b041b5e5f093bf83397fd21dd6b3012066fcc058cc32c3b09d7562dee29509b5839392c9ff05f51f3166aaac4ac5f238038a3045e6f72e48ef0fe8bc675e82c318a268e43970271bf119b81bf6a982746554f84e72b9f00280a320a08142923c23c883423ff949827f29bbacdc1ccdb04938ce6098c95ba6b32528f4ef78eed778b2e122ddfd1cbdd11d1c0a6783e011fc536d63d053260637

Cipher = aes-128-xts
Key = 2718281828459045235360287471352631415926535897932384626433832795
IV = fe000000000000000000000000000000
Plaintext = d55f684f81f4426e9fde92a5ff02df2ac896af63962888a97910c1379e20b0a3b1db613fb7fe2e07004329ea5c22bfd33e3dbe4cf58cc608c2c26c19a2e2fe22f98732c2b5cb844cc6c0702d91e1d50fc4382a7eba5635cd602432a2306ac4ce82f8d70c8d9bc15f918fe71e74c622d5cf71178bf6e0b9cc9f2b41dd8dbe441c41cd0c73a6dc47a348f6702f9d0e9b1b1431e948e299b9ec2272ab2c5f0c7be86affa5dec87a0bee81d3d50007edaa2bcfccb35605155ff36ed8edd4a40dcd4b243acd11b2b987bdbfaf91a7cac27e9c5aea525ee53de7b2d3332c8644402b823e94a7db26276d2d23aa07180f76b4fd29b9c0823099c9d62c519880aee7e9697617c1497d47bf3e571950311421b6b734d38b0db91eb85331b91ea9f61530f54512a5a52a4bad589eb69781d537f23297bb459bdad2948a29e1550bf4787e0be95bb173cf5fab17dab7a13a052a63453d97ccec1a321954886b7a1299faaeecae35c6eaaca753b041b5e5f093bf83397fd21dd6b3012066fcc058cc32c3b09d7562dee29509b5839392c9ff05f51f3166aaac4ac5f238038a3045e6f72e48ef0fe8bc675e82c318a268e43970271bf119b81bf6a982746554f84e72b9f00280a320a08142923c23c883423ff949827f29bbacdc1ccdb04938ce6098c95ba6b32528f4ef78eed778b2e122ddfd1cbdd11d1c0a6783e011fc536d63d053260637
Ciphertext = 72efc1ebfe1ee25975a6eb3aa8589dda2b261f1c85bdab442a9e5b2dd1d7c3957a16fc08e526d4b1223f1b1232a11af274c3d70dac57f83e0983c498f1a6f1aecb021c3e70085a1e527f1ce41ee5911a82020161529cd82773762daf5459de94a0a82adae7e1703c808543c29ed6fb32d9e004327c1355180c995a07741493a09c21ba01a387882da4f62534b87bb15d60d197201c0fd3bf30c1500a3ecfecdd66d8721f90bcc4c17ee925c61b0a03727a9c0d5f5ca462fbfa0af1c2513a9d9d4b5345bd27a5f6e653f751693e6b6a2b8ead57d511e00e58c45b7b8d005af79288f5c7c22fd4f1bf7a898b03a5634c6a1ae3f9fae5de4f296a2896b23e7ed43ed14fa5a2803f4d28f0d3ffcf24757677aebdb47bb388378708948a8d4126ed1839e0da29a537a8c198b3c66ab00712dd261674bf45a73d67f76914f830ca014b65596f27e4cf62de66125a5566df9975155628b400fbfb3a29040ed50faffdbb18aece7c5c44693260aab386c0a37b11b114f1c415aebb653be468179428d43a4d8bc3ec38813eca30a13cf1bb18d524f1992d44d8b1a42ea30b22e6c95b199d8d182f8840b09d059585c31ad691fa0619ff038aca2c39a943421157361717c49d322028a74648113bd8c9d7ec77cf3c89c1ec8718ceff8516d96b34c3c614f10699c9abc4ed0411506223bea16af35c883accdbe1104eef0cfdb54e12fb230a

Cipher = aes-128-xts
Key = 2718281828459045235360287471352631415926535897932384626433832795
IV = ff000000000000000000000000000000
Plaintext = 72efc1ebfe1ee25975a6eb3aa8589dda2b261f1c85bdab442a9e5b2dd1d7c3957a16fc08e526d4b1223f1b1232a11af274c3d70dac57f83e0983c498f1a6f1aecb021c3e70085a1e527f1ce41ee5911a82020161529cd82773762daf5459de94a0a82adae7e1703c808543c29ed6fb32d9e004327c1355180c995a07741493a09c21ba01a387882da4f62534b87bb15d60d197201c0fd3bf30c1500a3ecfecdd66d8721f90bcc4c17ee925c61b0a03727a9c0d5f5ca462fbfa0af1c2513a9d9d4b5345bd27a5f6e653f751693e6b6a2b8ead57d511e00e58c45b7b8d005af79288f5c7c22fd4f1bf7a898b03a5634c6a1ae3f9fae5de4f296a2896b23e7ed43ed14fa5a2803f4d28f0d3ffcf24757677aebdb47bb388378708948a8d4126ed1839e0da29a537a8c198b3c66ab00712dd261674bf45a73d67f76914f830ca014b65596f27e4cf62de66125a5566df9975155628b400fbfb3a29040ed50faffdbb18aece7c5c44693260aab386c0a37b11b114f1c415aebb653be468179428d43a4d8bc3ec38813eca30a13cf1bb18d524f1992d44d8b1a42ea30b22e6c95b199d8d182f8840b09d059585c31ad691fa0619ff038aca2c39a943421157361717c49d322028a74648113bd8c9d7ec77cf3c89c1ec8718ceff8516d96b34c3c614f10699c9abc4ed0411506223bea16af35c883accdbe1104eef0cfdb54e12fb230a
Ciphertext = 3260ae8dad1f4a32c5cafe3ab0eb95549d461a67ceb9e5aa2d3afb62dece0553193ba50c75be251e08d1d08f1088576c7efdfaaf3f459559571e12511753b07af073f35da06af0ce0bbf6b8f5ccc5cea500ec1b211bd51f63b606bf6528796ca12173ba39b8935ee44ccce646f90a45bf9ccc567f0ace13dc2d53ebeedc81f58b2e41179dddf0d5a5c42f5d8506c1a5d2f8f59f3ea873cbcd0eec19acbf325423bd3dcb8c2b1bf1d1eaed0eba7f0698e4314fbeb2f1566d1b9253008cbccf45a2b0d9c5c9c21474f4076e02be26050b99dee4fd68a4cf890e496e4fcae7b70f94ea5a9062da0daeba1993d2ccd1dd3c244b8428801495a58b216547e7e847c46d1d756377b6242d2e5fb83bf752b54e0df71e889f3a2bb0f4c10805bf3c590376e3c24e22ff57f7fa965577375325cea5d920db94b9c336b455f6e894c01866fe9fbb8c8d3f70a2957285f6dfb5dcd8cbf54782f8fe7766d4723819913ac773421e3a31095866bad22c86a6036b2518b2059b4229d18c8c2ccbdf906c6cc6e82464ee57bddb0bebcb1dc645325bfb3e665ef7251082c88ebb1cf203bd779fdd38675713c8daadd17e1cabee432b09787b6ddf3304e38b731b45df5df51b78fcfb3d32466028d0ba36555e7e11ab0ee0666061d1645d962444bc47a38188930a84b4d561395c73c087021927ca638b7afc8a8679ccb84c26555440ec7f10445cd


Cipher = aes-256-xts
Key = 27182818284590452353602874713526624977572470936999595749669676273141592653589793238462643383279502884197169399375105820974944592
IV = ff000000000000000000000000000000
Plaintext = 000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f202122232425262728292a2b2c2d2e2f303132333435363738393a3b3c3d3e3f404142434445464748494a4b4c4d4e4f505152535455565758595a5b5c5d5e5f606162636465666768696a6b6c6d6e6f707172737475767778797a7b7c7d7e7f808182838485868788898a8b8c8d8e8f909192939495969798999a9b9c9d9e9fa0a1a2a3a4a5a6a7a8a9aaabacadaeafb0b1b2b3b4b5b6b7b8b9babbbcbdbebfc0c1c2c3c4c5c6c7c8c9cacbcccdcecfd0d1d2d3d4d5d6d7d8d9dadbdcdddedfe0e1e2e3e4e5e6e7e8e9eaebecedeeeff0f1f2f3f4f5f6f7f8f9fafbfcfdfeff000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f202122232425262728292a2b2c2d2e2f303132333435363738393a3b3c3d3e3f404142434445464748494a4b4c4d4e4f505152535455565758595a5b5c5d5e5f606162636465666768696a6b6c6d6e6f707172737475767778797a7b7c7d7e7f808182838485868788898a8b8c8d8e8f909192939495969798999a9b9c9d9e9fa0a1a2a3a4a5a6a7a8a9aaabacadaeafb0b1b2b3b4b5b6b7b8b9babbbcbdbebfc0c1c2c3c4c5c6c7c8c9cacbcccdcecfd0d1d2d3d4d5d6d7d8d9dadbdcdddedfe0e1e2e3e4e5e6e7e8e9eaebecedeeeff0f1f2f3f4f5f6f7f8f9fafbfcfdfeff
Ciphertext = 1c3b3a102f770386e4836c99e370cf9bea00803f5e482357a4ae12d414a3e63b5d31e276f8fe4a8d66b317f9ac683f44680a86ac35adfc3345befecb4bb188fd5776926c49a3095eb108fd1098baec70aaa66999a72a82f27d848b21d4a741b0c5cd4d5fff9dac89aeba122961d03a757123e9870f8acf1000020887891429ca2a3e7a7d7df7b10355165c8b9a6d0a7de8b062c4500dc4cd120c0f7418dae3d0b5781c34803fa75421c790dfe1de1834f280d7667b327f6c8cd7557e12ac3a0f93ec05c52e0493ef31a12d3d9260f79a289d6a379bc70c50841473d1a8cc81ec583e9645e07b8d9670655ba5bbcfecc6dc3966380ad8fecb17b6ba02469a020a84e18e8f84252070c13e9f1f289be54fbc481457778f616015e1327a02b140f1505eb309326d68378f8374595c849d84f4c333ec4423885143cb47bd71c5edae9be69a2ffeceb1bec9de244fbe15992b11b77c040f12bd8f6a975a44a0f90c29a9abc3d4d893927284c58754cce294529f8614dcd2aba991925fedc4ae74ffac6e333b93eb4aff0479da9a410e4450e0dd7ae4c6e2910900575da401fc07059f645e8b7e9bfdef33943054ff84011493c27b3429eaedb4ed5376441a77ed43851ad77f16f541dfd269d50d6a5f14fb0aab1cbb4c1550be97f7ab4066193c4caa773dad38014bd2092fa755c824bb5e54c4f36ffda9fcea70b9c6e693e148c151

Cipher = aes-256-xts
Key = 27182818284590452353602874713526624977572470936999595749669676273141592653589793238462643383279502884197169399375105820974944592
IV = ffff0000000000000000000000000000
Plaintext = 000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f202122232425262728292a2b2c2d2e2f303132333435363738393a3b3c3d3e3f404142434445464748494a4b4c4d4e4f505152535455565758595a5b5c5d5e5f606162636465666768696a6b6c6d6e6f707172737475767778797a7b7c7d7e7f808182838485868788898a8b8c8d8e8f909192939495969798999a9b9c9d9e9fa0a1a2a3a4a5a6a7a8a9aaabacadaeafb0b1b2b3b4b5b6b7b8b9babbbcbdbebfc0c1c2c3c4c5c6c7c8c9cacbcccdcecfd0d1d2d3d4d5d6d7d8d9dadbdcdddedfe0e1e2e3e4e5e6e7e8e9eaebecedeeeff0f1f2f3f4f5f6f7f8f9fafbfcfdfeff000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f202122232425262728292a2b2c2d2e2f303132333435363738393a3b3c3d3e3f404142434445464748494a4b4c4d4e4f505152535455565758595a5b5c5d5e5f606162636465666768696a6b6c6d6e6f707172737475767778797a7b7c7d7e7f808182838485868788898a8b8c8d8e8f909192939495969798999a9b9c9d9e9fa0a1a2a3a4a5a6a7a8a9aaabacadaeafb0b1b2b3b4b5b6b7b8b9babbbcbdbebfc0c1c2c3c4c5c6c7c8c9cacbcccdcecfd0d1d2d3d4d5d6d7d8d9dadbdcdddedfe0e1e2e3e4e5e6e7e8e9eaebecedeeeff0f1f2f3f4f5f6f7f8f9fafbfcfdfeff
Ciphertext = 77a31251618a15e6b92d1d66dffe7b50b50bad552305ba0217a610688eff7e11e1d0225438e093242d6db274fde801d4cae06f2092c728b2478559df58e837c2469ee4a4fa794e4bbc7f39bc026e3cb72c33b0888f25b4acf56a2a9804f1ce6d3d6e1dc6ca181d4b546179d55544aa7760c40d06741539c7e3cd9d2f6650b2013fd0eeb8c2b8e3d8d240ccae2d4c98320a7442e1c8d75a42d6e6cfa4c2eca1798d158c7aecdf82490f24bb9b38e108bcda12c3faf9a21141c3613b58367f922aaa26cd22f23d708dae699ad7cb40a8ad0b6e2784973dcb605684c08b8d6998c69aac049921871ebb65301a4619ca80ecb485a31d744223ce8ddc2394828d6a80470c092f5ba413c3378fa6054255c6f9df4495862bbb3287681f931b687c888abf844dfc8fc28331e579928cd12bd2390ae123cf03818d14dedde5c0c24c8ab018bfca75ca096f2d531f3d1619e785f1ada437cab92e980558b3dce1474afb75bfedbf8ff54cb2618e0244c9ac0d3c66fb51598cd2db11f9be39791abe447c63094f7c453b7ff87cb5bb36b7c79efb0872d17058b83b15ab0866ad8a58656c5a7e20dbdf308b2461d97c0ec0024a2715055249cf3b478ddd4740de654f75ca686e0d7345c69ed50cdc2a8b332b1f8824108ac937eb050585608ee734097fc09054fbff89eeaeea791f4a7ab1f9868294a4f9e27b42af8100cb9d59cef9645803

Cipher = aes-256-xts
Key = 27182818284590452353602874713526624977572470936999595749669676273141592653589793238462643383279502884197169399375105820974944592
IV = ffffff00000000000000000000000000
Plaintext = 000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f202122232425262728292a2b2c2d2e2f303132333435363738393a3b3c3d3e3f404142434445464748494a4b4c4d4e4f505152535455565758595a5b5c5d5e5f606162636465666768696a6b6c6d6e6f707172737475767778797a7b7c7d7e7f808182838485868788898a8b8c8d8e8f909192939495969798999a9b9c9d9e9fa0a1a2a3a4a5a6a7a8a9aaabacadaeafb0b1b2b3b4b5b6b7b8b9babbbcbdbebfc0c1c2c3c4c5c6c7c8c9cacbcccdcecfd0d1d2d3d4d5d6d7d8d9dadbdcdddedfe0e1e2e3e4e5e6e7e8e9eaebecedeeeff0f1f2f3f4f5f6f7f8f9fafbfcfdfeff000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f202122232425262728292a2b2c2d2e2f303132333435363738393a3b3c3d3e3f404142434445464748494a4b4c4d4e4f505152535455565758595a5b5c5d5e5f606162636465666768696a6b6c6d6e6f707172737475767778797a7b7c7d7e7f808182838485868788898a8b8c8d8e8f909192939495969798999a9b9c9d9e9fa0a1a2a3a4a5a6a7a8a9aaabacadaeafb0b1b2b3b4b5b6b7b8b9babbbcbdbebfc0c1c2c3c4c5c6c7c8c9cacbcccdcecfd0d1d2d3d4d5d6d7d8d9dadbdcdddedfe0e1e2e3e4e5e6e7e8e9eaebecedeeeff0f1f2f3f4f5f6f7f8f9fafbfcfdfeff
Ciphertext = e387aaa58ba483afa7e8eb469778317ecf4cf573aa9d4eac23f2cdf914e4e200a8b490e42ee646802dc6ee2b471b278195d60918ececb44bf79966f83faba0499298ebc699c0c8634715a320bb4f075d622e74c8c932004f25b41e361025b5a87815391f6108fc4afa6a05d9303c6ba68a128a55705d415985832fdeaae6c8e19110e84d1b1f199a2692119edc96132658f09da7c623efcec712537a3d94c0bf5d7e352ec94ae5797fdb377dc1551150721adf15bd26a8efc2fcaad56881fa9e62462c28f30ae1ceaca93c345cf243b73f542e2074a705bd2643bb9f7cc79bb6e7091ea6e232df0f9ad0d6cf502327876d82207abf2115cdacf6d5a48f6c1879a65b115f0f8b3cb3c59d15dd8c769bc014795a1837f3901b5845eb491adfefe097b1fa30a12fc1f65ba22905031539971a10f2f36c321bb51331cdefb39e3964c7ef079994f5b69b2edd83a71ef549971ee93f44eac3938fcdd61d01fa71799da3a8091c4c48aa9ed263ff0749df95d44fef6a0bb578ec69456aa5408ae32c7af08ad7ba8921287e3bbee31b767be06a0e705c864a769137df28292283ea81a2480241b44d9921cdbec1bc28dc1fda114bd8e5217ac9d8ebafa720e9da4f9ace231cc949e5b96fe76ffc21063fddc83a6b8679c00d35e09576a875305bed5f36ed242c8900dd1fa965bc950dfce09b132263a1eef52dd6888c309f5a7d712826

Cipher = aes-256-xts
Key = 27182818284590452353602874713526624977572470936999595749669676273141592653589793238462643383279502884197169399375105820974944592
IV = ffffffff000000000000000000000000
Plaintext = 000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f202122232425262728292a2b2c2d2e2f303132333435363738393a3b3c3d3e3f404142434445464748494a4b4c4d4e4f505152535455565758595a5b5c5d5e5f606162636465666768696a6b6c6d6e6f707172737475767778797a7b7c7d7e7f808182838485868788898a8b8c8d8e8f909192939495969798999a9b9c9d9e9fa0a1a2a3a4a5a6a7a8a9aaabacadaeafb0b1b2b3b4b5b6b7b8b9babbbcbdbebfc0c1c2c3c4c5c6c7c8c9cacbcccdcecfd0d1d2d3d4d5d6d7d8d9dadbdcdddedfe0e1e2e3e4e5e6e7e8e9eaebecedeeeff0f1f2f3f4f5f6f7f8f9fafbfcfdfeff000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f202122232425262728292a2b2c2d2e2f303132333435363738393a3b3c3d3e3f404142434445464748494a4b4c4d4e4f505152535455565758595a5b5c5d5e5f606162636465666768696a6b6c6d6e6f707172737475767778797a7b7c7d7e7f808182838485868788898a8b8c8d8e8f909192939495969798999a9b9c9d9e9fa0a1a2a3a4a5a6a7a8a9aaabacadaeafb0b1b2b3b4b5b6b7b8b9babbbcbdbebfc0c1c2c3c4c5c6c7c8c9cacbcccdcecfd0d1d2d3d4d5d6d7d8d9dadbdcdddedfe0e1e2e3e4e5e6e7e8e9eaebecedeeeff0f1f2f3f4f5f6f7f8f9fafbfcfdfeff
Ciphertext = bf53d2dade78e822a4d949a9bc6766b01b06a8ef70d26748c6a7fc36d80ae4c5520f7c4ab0ac8544424fa405162fef5a6b7f229498063618d39f0003cb5fb8d1c86b643497da1ff945c8d3bedeca4f479702a7a735f043ddb1d6aaade3c4a0ac7ca7f3fa5279bef56f82cd7a2f38672e824814e10700300a055e1630b8f1cb0e919f5e942010a416e2bf48cb46993d3cb6a51c19bacf864785a00bc2ecff15d350875b246ed53e68be6f55bd7e05cfc2b2ed6432198a6444b6d8c247fab941f569768b5c429366f1d3f00f0345b96123d56204c01c63b22ce78baf116e525ed90fdea39fa469494d3866c31e05f295ff21fea8d4e6e13d67e47ce722e9698a1c1048d68ebcde76b86fcf976eab8aa9790268b7068e017a8b9b749409514f1053027fd16c3786ea1bac5f15cb79711ee2abe82f5cf8b13ae73030ef5b9e4457e75d1304f988d62dd6fc4b94ed38ba831da4b7634971b6cd8ec325d9c61c00f1df73627ed3745a5e8489f3a95c69639c32cd6e1d537a85f75cc844726e8a72fc0077ad22000f1d5078f6b866318c668f1ad03d5a5fced5219f2eabbd0aa5c0f460d183f04404a0d6f469558e81fab24a167905ab4c7878502ad3e38fdbe62a41556cec37325759533ce8f25f367c87bb5578d667ae93f9e2fd99bcbc5f2fbba88cf6516139420fcff3b7361d86322c4bd84c82f335abb152c4a93411373aaa8220

Cipher = aes-256-xts
Key = 27182818284590452353602874713526624977572470936999595749669676273141592653589793238462643383279502884197169399375105820974944592
IV = ffffffffff0000000000000000000000
Plaintext = 000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f202122232425262728292a2b2c2d2e2f303132333435363738393a3b3c3d3e3f404142434445464748494a4b4c4d4e4f505152535455565758595a5b5c5d5e5f606162636465666768696a6b6c6d6e6f707172737475767778797a7b7c7d7e7f808182838485868788898a8b8c8d8e8f909192939495969798999a9b9c9d9e9fa0a1a2a3a4a5a6a7a8a9aaabacadaeafb0b1b2b3b4b5b6b7b8b9babbbcbdbebfc0c1c2c3c4c5c6c7c8c9cacbcccdcecfd0d1d2d3d4d5d6d7d8d9dadbdcdddedfe0e1e2e3e4e5e6e7e8e9eaebecedeeeff0f1f2f3f4f5f6f7f8f9fafbfcfdfeff000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f202122232425262728292a2b2c2d2e2f303132333435363738393a3b3c3d3e3f404142434445464748494a4b4c4d4e4f505152535455565758595a5b5c5d5e5f606162636465666768696a6b6c6d6e6f707172737475767778797a7b7c7d7e7f808182838485868788898a8b8c8d8e8f909192939495969798999a9b9c9d9e9fa0a1a2a3a4a5a6a7a8a9aaabacadaeafb0b1b2b3b4b5b6b7b8b9babbbcbdbebfc0c1c2c3c4c5c6c7c8c9cacbcccdcecfd0d1d2d3d4d5d6d7d8d9dadbdcdddedfe0e1e2e3e4e5e6e7e8e9eaebecedeeeff0f1f2f3f4f5f6f7f8f9fafbfcfdfeff
Ciphertext = 64497e5a831e4a932c09be3e5393376daa599548b816031d224bbf50a818ed2350eae7e96087c8a0db51ad290bd00c1ac1620857635bf246c176ab463be30b808da548081ac847b158e1264be25bb0910bbc92647108089415d45fab1b3d2604e8a8eff1ae4020cfa39936b66827b23f371b92200be90251e6d73c5f86de5fd4a950781933d79a28272b782a2ec313efdfcc0628f43d744c2dc2ff3dcb66999b50c7ca895b0c64791eeaa5f29499fb1c026f84ce5b5c72ba1083cddb5ce45434631665c333b60b11593fb253c5179a2c8db813782a004856a1653011e93fb6d876c18366dd8683f53412c0c180f9c848592d593f8609ca736317d356e13e2bff3a9f59cd9aeb19cd482593d8c46128bb32423b37a9adfb482b99453fbe25a41bf6feb4aa0bef5ed24bf73c762978025482c13115e4015aac992e5613a3b5c2f685b84795cb6e9b2656d8c88157e52c42f978d8634c43d06fea928f2822e465aa6576e9bf419384506cc3ce3c54ac1a6f67dc66f3b30191e698380bc999b05abce19dc0c6dcc2dd001ec535ba18deb2df1a101023108318c75dc98611a09dc48a0acdec676fabdf222f07e026f059b672b56e5cbc8e1d21bbd867dd927212054681d70ea737134cdfce93b6f82ae22423274e58a0821cc5502e2d0ab4585e94de6975be5e0b4efce51cd3e70c25a1fbbbd609d273ad5b0d59631c531f6a0a57b9


Cipher = aes-128-xts
Key = fffefdfcfbfaf9f8f7f6f5f4f3f2f1f0bfbebdbcbbbab9b8b7b6b5b4b3b2b1b0
IV = 9a785634120000000000000000000000
Plaintext = 000102030405060708090a0b0c0d0e0f10
Ciphertext = 6c1625db4671522d3d7599601de7ca09ed

Cipher = aes-128-xts
Key = fffefdfcfbfaf9f8f7f6f5f4f3f2f1f0bfbebdbcbbbab9b8b7b6b5b4b3b2b1b0
IV = 9a785634120000000000000000000000
Plaintext = 000102030405060708090a0b0c0d0e0f1011
Ciphertext = d069444b7a7e0cab09e24447d24deb1fedbf

Cipher = aes-128-xts
Key = fffefdfcfbfaf9f8f7f6f5f4f3f2f1f0bfbebdbcbbbab9b8b7b6b5b4b3b2b1b0
IV = 9a785634120000000000000000000000
Plaintext = 000102030405060708090a0b0c0d0e0f101112
Ciphertext = e5df1351c0544ba1350b3363cd8ef4beedbf9d

Cipher = aes-128-xts
Key = fffefdfcfbfaf9f8f7f6f5f4f3f2f1f0bfbebdbcbbbab9b8b7b6b5b4b3b2b1b0
IV = 9a785634120000000000000000000000
Plaintext = 000102030405060708090a0b0c0d0e0f10111213
Ciphertext = 9d84c813f719aa2c7be3f66171c7c5c2edbf9dac

Cipher = aes-128-xts
Key = e0e1e2e3e4e5e6e7e8e9eaebecedeeefc0c1c2c3c4c5c6c7c8c9cacbcccdcecf
IV = 21436587a90000000000000000000000
Plaintext = 000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f202122232425262728292a2b2c2d2e2f303132333435363738393a3b3c3d3e3f404142434445464748494a4b4c4d4e4f505152535455565758595a5b5c5d5e5f606162636465666768696a6b6c6d6e6f707172737475767778797a7b7c7d7e7f808182838485868788898a8b8c8d8e8f909192939495969798999a9b9c9d9e9fa0a1a2a3a4a5a6a7a8a9aaabacadaeafb0b1b2b3b4b5b6b7b8b9babbbcbdbebfc0c1c2c3c4c5c6c7c8c9cacbcccdcecfd0d1d2d3d4d5d6d7d8d9dadbdcdddedfe0e1e2e3e4e5e6e7e8e9eaebecedeeeff0f1f2f3f4f5f6f7f8f9fafbfcfdfeff000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f202122232425262728292a2b2c2d2e2f303132333435363738393a3b3c3d3e3f404142434445464748494a4b4c4d4e4f505152535455565758595a5b5c5d5e5f606162636465666768696a6b6c6d6e6f707172737475767778797a7b7c7d7e7f808182838485868788898a8b8c8d8e8f909192939495969798999a9b9c9d9e9fa0a1a2a3a4a5a6a7a8a9aaabacadaeafb0b1b2b3b4b5b6b7b8b9babbbcbdbebfc0c1c2c3c4c5c6c7c8c9cacbcccdcecfd0d1d2d3d4d5d6d7d8d9dadbdcdddedfe0e1e2e3e4e5e6e7e8e9eaebecedeeeff0f1f2f3f4f5f6f7f8f9fafbfcfdfeff
Ciphertext = 38b45812ef43a05bd957e545907e223b954ab4aaf088303ad910eadf14b42be68b2461149d8c8ba85f992be970bc621f1b06573f63e867bf5875acafa04e42ccbd7bd3c2a0fb1fff791ec5ec36c66ae4ac1e806d81fbf709dbe29e471fad38549c8e66f5345d7c1eb94f405d1ec785cc6f6a68f6254dd8339f9d84057e01a17741990482999516b5611a38f41bb6478e6f173f320805dd71b1932fc333cb9ee39936beea9ad96fa10fb4112b901734ddad40bc1878995f8e11aee7d141a2f5d48b7a4e1e7f0b2c04830e69a4fd1378411c2f287edf48c6c4e5c247a19680f7fe41cefbd49b582106e3616cbbe4dfb2344b2ae9519391f3e0fb4922254b1d6d2d19c6d4d537b3a26f3bcc51588b32f3eca0829b6a5ac72578fb814fb43cf80d64a233e3f997a3f02683342f2b33d25b492536b93becb2f5e1a8b82f5b883342729e8ae09d16938841a21a97fb543eea3bbff59f13c1a18449e398701c1ad51648346cbc04c27bb2da3b93a1372ccae548fb53bee476f9e9c91773b1bb19828394d55d3e1a20ed69113a860b6829ffa847224604435070221b257e8dff783615d2cae4803a93aa4334ab482a0afac9c0aeda70b45a481df5dec5df8cc0f423c77a5fd46cd312021d4b438862419a791be03bb4d97c0e59578542531ba466a83baf92cefc151b5cc1611a167893819b63fb8a6b18e86de60290fa72b797b0ce59f3

# Exercise different lengths covering even ciphertext stealing cases
Cipher = aes-128-xts
Key = 2718281828459045235360287471352631415926535897932384626433832795
IV = 00000000000000000000000000000000
Plaintext = 000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f202122232425262728292a2b2c2d2e2f303132333435363738393a3b3c3d3e3f404142434445464748494a4b4c4d4e4f505152535455565758595a5b5c5d5e5f
Ciphertext = 27a7479befa1d476489f308cd4cfa6e2a96e4bbe3208ff25287dd3819616e89cc78cf7f5e543445f8333d8fa7f56000005279fa5d8b5e4ad40e736ddb4d35412328063fd2aab53e5ea1e0a9f332500a5df9487d07a5c92cc512c8866c7e860ce

Cipher = aes-128-xts
Key = 2718281828459045235360287471352631415926535897932384626433832795
IV = 00000000000000000000000000000000
Plaintext = 000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f202122232425262728292a2b2c2d2e2f303132333435363738393a3b3c3d3e3f404142434445464748494a4b4c4d4e4f505152535455565758595a5b5c5d5e5f6061
Ciphertext = 27A7479BEFA1D476489F308CD4CFA6E2A96E4BBE3208FF25287DD3819616E89CC78CF7F5E543445F8333D8FA7F56000005279FA5D8B5E4AD40E736DDB4D35412328063FD2AAB53E5EA1E0A9F332500A5B079C6307EA0914559C6D2FB6384F8AADF94

Cipher = aes-128-xts
Key = 2718281828459045235360287471352631415926535897932384626433832795
IV = 00000000000000000000000000000000
Plaintext = 000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f202122232425262728292a2b2c2d2e2f303132333435363738393a3b3c3d3e3f404142434445464748494a4b4c4d4e4f505152535455565758595a5b5c5d5e5f606162636465666768696a6b6c6d6e6f
Ciphertext = 27a7479befa1d476489f308cd4cfa6e2a96e4bbe3208ff25287dd3819616e89cc78cf7f5e543445f8333d8fa7f56000005279fa5d8b5e4ad40e736ddb4d35412328063fd2aab53e5ea1e0a9f332500a5df9487d07a5c92cc512c8866c7e860ce93fdf166a24912b422976146ae20ce84

Cipher = aes-128-xts
Key = 2718281828459045235360287471352631415926535897932384626433832795
IV = 00000000000000000000000000000000
Plaintext = 000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f202122232425262728292a2b2c2d2e2f303132333435363738393a3b3c3d3e3f404142434445464748494a4b4c4d4e4f505152535455565758595a5b5c5d5e5f606162636465666768696a6b6c6d6e6f7071
Ciphertext = 27A7479BEFA1D476489F308CD4CFA6E2A96E4BBE3208FF25287DD3819616E89CC78CF7F5E543445F8333D8FA7F56000005279FA5D8B5E4AD40E736DDB4D35412328063FD2AAB53E5EA1E0A9F332500A5DF9487D07A5C92CC512C8866C7E860CEF4F253466EF4953ADC8FE2F5BC1FF57593FD

Cipher = aes-128-xts
Key = 2718281828459045235360287471352631415926535897932384626433832795
IV = 00000000000000000000000000000000
Plaintext = 000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f202122232425262728292a2b2c2d2e2f303132333435363738393a3b3c3d3e3f404142434445464748494a4b4c4d4e4f505152535455565758595a5b5c5d5e5f606162636465666768696a6b6c6d6e6f707172737475767778797a7b7c7d7e7f
Ciphertext = 27a7479befa1d476489f308cd4cfa6e2a96e4bbe3208ff25287dd3819616e89cc78cf7f5e543445f8333d8fa7f56000005279fa5d8b5e4ad40e736ddb4d35412328063fd2aab53e5ea1e0a9f332500a5df9487d07a5c92cc512c8866c7e860ce93fdf166a24912b422976146ae20ce846bb7dc9ba94a767aaef20c0d61ad0265

Cipher = aes-128-xts
Key = 2718281828459045235360287471352631415926535897932384626433832795
IV = 00000000000000000000000000000000
Plaintext = 000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f202122232425262728292a2b2c2d2e2f303132333435363738393a3b3c3d3e3f404142434445464748494a4b4c4d4e4f505152535455565758595a5b5c5d5e5f606162636465666768696a6b6c6d6e6f707172737475767778797a7b7c7d7e7f8081
Ciphertext = 27A7479BEFA1D476489F308CD4CFA6E2A96E4BBE3208FF25287DD3819616E89CC78CF7F5E543445F8333D8FA7F56000005279FA5D8B5E4AD40E736DDB4D35412328063FD2AAB53E5EA1E0A9F332500A5DF9487D07A5C92CC512C8866C7E860CE93FDF166A24912B422976146AE20CE842973C68248EDDFE26FB9B096659C8A5D6BB7

Cipher = aes-128-xts
Key = 2718281828459045235360287471352631415926535897932384626433832795
IV = 00000000000000000000000000000000
Plaintext = 000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f202122232425262728292a2b2c2d2e2f303132333435363738393a3b3c3d3e3f404142434445464748494a4b4c4d4e4f505152535455565758595a5b5c5d5e5f606162636465666768696a6b6c6d6e6f707172737475767778797a7b7c7d7e7f808182838485868788898a8b8c8d8e8f
Ciphertext = 27a7479befa1d476489f308cd4cfa6e2a96e4bbe3208ff25287dd3819616e89cc78cf7f5e543445f8333d8fa7f56000005279fa5d8b5e4ad40e736ddb4d35412328063fd2aab53e5ea1e0a9f332500a5df9487d07a5c92cc512c8866c7e860ce93fdf166a24912b422976146ae20ce846bb7dc9ba94a767aaef20c0d61ad02655ea92dc4c4e41a8952c651d33174be51

Cipher = aes-128-xts
Key = 2718281828459045235360287471352631415926535897932384626433832795
IV = 00000000000000000000000000000000
Plaintext = 000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f202122232425262728292a2b2c2d2e2f303132333435363738393a3b3c3d3e3f404142434445464748494a4b4c4d4e4f505152535455565758595a5b5c5d5e5f606162636465666768696a6b6c6d6e6f707172737475767778797a7b7c7d7e7f808182838485868788898a8b8c8d8e8f9091
Ciphertext = 27A7479BEFA1D476489F308CD4CFA6E2A96E4BBE3208FF25287DD3819616E89CC78CF7F5E543445F8333D8FA7F56000005279FA5D8B5E4AD40E736DDB4D35412328063FD2AAB53E5EA1E0A9F332500A5DF9487D07A5C92CC512C8866C7E860CE93FDF166A24912B422976146AE20CE846BB7DC9BA94A767AAEF20C0D61AD0265C4DD16E65A24575A709F174593F19FF85EA9

Cipher = aes-128-xts
Key = 2718281828459045235360287471352631415926535897932384626433832795
IV = 00000000000000000000000000000000
Plaintext = 000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f202122232425262728292a2b2c2d2e2f303132333435363738393a3b3c3d3e3f404142434445464748494a4b4c4d4e4f505152535455565758595a5b5c5d5e5f606162636465666768696a6b6c6d6e6f707172737475767778797a7b7c7d7e7f808182838485868788898a8b8c8d8e8f909192939495969798999a9b9c9d9e9f
Ciphertext = 27a7479befa1d476489f308cd4cfa6e2a96e4bbe3208ff25287dd3819616e89cc78cf7f5e543445f8333d8fa7f56000005279fa5d8b5e4ad40e736ddb4d35412328063fd2aab53e5ea1e0a9f332500a5df9487d07a5c92cc512c8866c7e860ce93fdf166a24912b422976146ae20ce846bb7dc9ba94a767aaef20c0d61ad02655ea92dc4c4e41a8952c651d33174be51a10c421110e6d81588ede82103a252d8

Cipher = aes-128-xts
Key = 2718281828459045235360287471352631415926535897932384626433832795
IV = 00000000000000000000000000000000
Plaintext = 000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f202122232425262728292a2b2c2d2e2f303132333435363738393a3b3c3d3e3f404142434445464748494a4b4c4d4e4f505152535455565758595a5b5c5d5e5f606162636465666768696a6b6c6d6e6f707172737475767778797a7b7c7d7e7f808182838485868788898a8b8c8d8e8f909192939495969798999a9b9c9d9e9fa0a1
Ciphertext = 27A7479BEFA1D476489F308CD4CFA6E2A96E4BBE3208FF25287DD3819616E89CC78CF7F5E543445F8333D8FA7F56000005279FA5D8B5E4AD40E736DDB4D35412328063FD2AAB53E5EA1E0A9F332500A5DF9487D07A5C92CC512C8866C7E860CE93FDF166A24912B422976146AE20CE846BB7DC9BA94A767AAEF20C0D61AD02655EA92DC4C4E41A8952C651D33174BE519215FA160C664D4B07D757A034AB3B35A10C

Cipher = aes-128-xts
Key = 2718281828459045235360287471352631415926535897932384626433832795
IV = 00000000000000000000000000000000
Plaintext = 000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f202122232425262728292a2b2c2d2e2f303132333435363738393a3b3c3d3e3f404142434445464748494a4b4c4d4e4f505152535455565758595a5b5c5d5e5f606162636465666768696a6b6c6d6e6f707172737475767778797a7b7c7d7e7f808182838485868788898a8b8c8d8e8f909192939495969798999a9b9c9d9e9fa0a1a2a3a4a5a6a7a8a9aaabacadaeaf
Ciphertext = 27a7479befa1d476489f308cd4cfa6e2a96e4bbe3208ff25287dd3819616e89cc78cf7f5e543445f8333d8fa7f56000005279fa5d8b5e4ad40e736ddb4d35412328063fd2aab53e5ea1e0a9f332500a5df9487d07a5c92cc512c8866c7e860ce93fdf166a24912b422976146ae20ce846bb7dc9ba94a767aaef20c0d61ad02655ea92dc4c4e41a8952c651d33174be51a10c421110e6d81588ede82103a252d8a750e8768defffed9122810aaeb99f91

Cipher = aes-128-xts
Key = 2718281828459045235360287471352631415926535897932384626433832795
IV = 00000000000000000000000000000000
Plaintext = 000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f202122232425262728292a2b2c2d2e2f303132333435363738393a3b3c3d3e3f404142434445464748494a4b4c4d4e4f505152535455565758595a5b5c5d5e5f606162636465666768696a6b6c6d6e6f707172737475767778797a7b7c7d7e7f808182838485868788898a8b8c8d8e8f909192939495969798999a9b9c9d9e9fa0a1a2a3a4a5a6a7a8a9aaabacadaeafb0b1
Ciphertext = 27A7479BEFA1D476489F308CD4CFA6E2A96E4BBE3208FF25287DD3819616E89CC78CF7F5E543445F8333D8FA7F56000005279FA5D8B5E4AD40E736DDB4D35412328063FD2AAB53E5EA1E0A9F332500A5DF9487D07A5C92CC512C8866C7E860CE93FDF166A24912B422976146AE20CE846BB7DC9BA94A767AAEF20C0D61AD02655EA92DC4C4E41A8952C651D33174BE51A10C421110E6D81588EDE82103A252D82C6CBC24F9357BD1FB882AA4B2CC2E7FA750

Cipher = aes-128-xts
Key = 2718281828459045235360287471352631415926535897932384626433832795
IV = 00000000000000000000000000000000
Plaintext = 000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f202122232425262728292a2b2c2d2e2f303132333435363738393a3b3c3d3e3f404142434445464748494a4b4c4d4e4f505152535455565758595a5b5c5d5e5f606162636465666768696a6b6c6d6e6f707172737475767778797a7b7c7d7e7f808182838485868788898a8b8c8d8e8f909192939495969798999a9b9c9d9e9fa0a1a2a3a4a5a6a7a8a9aaabacadaeafb0b1b2b3b4b5b6b7b8b9babbbcbdbebf
Ciphertext = 27a7479befa1d476489f308cd4cfa6e2a96e4bbe3208ff25287dd3819616e89cc78cf7f5e543445f8333d8fa7f56000005279fa5d8b5e4ad40e736ddb4d35412328063fd2aab53e5ea1e0a9f332500a5df9487d07a5c92cc512c8866c7e860ce93fdf166a24912b422976146ae20ce846bb7dc9ba94a767aaef20c0d61ad02655ea92dc4c4e41a8952c651d33174be51a10c421110e6d81588ede82103a252d8a750e8768defffed9122810aaeb99f9172af82b604dc4b8e51bcb08235a6f434

Cipher = aes-128-xts
Key = 2718281828459045235360287471352631415926535897932384626433832795
IV = 00000000000000000000000000000000
Plaintext = 000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f202122232425262728292a2b2c2d2e2f303132333435363738393a3b3c3d3e3f404142434445464748494a4b4c4d4e4f505152535455565758595a5b5c5d5e5f606162636465666768696a6b6c6d6e6f707172737475767778797a7b7c7d7e7f808182838485868788898a8b8c8d8e8f909192939495969798999a9b9c9d9e9fa0a1a2a3a4a5a6a7a8a9aaabacadaeafb0b1b2b3b4b5b6b7b8b9babbbcbdbebfc0c1
Ciphertext = 27A7479BEFA1D476489F308CD4CFA6E2A96E4BBE3208FF25287DD3819616E89CC78CF7F5E543445F8333D8FA7F56000005279FA5D8B5E4AD40E736DDB4D35412328063FD2AAB53E5EA1E0A9F332500A5DF9487D07A5C92CC512C8866C7E860CE93FDF166A24912B422976146AE20CE846BB7DC9BA94A767AAEF20C0D61AD02655EA92DC4C4E41A8952C651D33174BE51A10C421110E6D81588EDE82103A252D8A750E8768DEFFFED9122810AAEB99F910409B03D164E727C31290FD4E039500872AF

Title = AES XTS Non standard test vectors - generated from reference implementation

Cipher = aes-128-xts
Key = fffefdfcfbfaf9f8f7f6f5f4f3f2f1f0bfbebdbcbbbab9b8b7b6b5b4b3b2b1b0
IV = 9a785634120000000000000000000000
Plaintext = 000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f2021
Ciphertext = edbf9dace45d6f6a7306e64be5dd824b9dc31efeb418c373ce073b66755529982538

Cipher = aes-128-xts
Key = fffefdfcfbfaf9f8f7f6f5f4f3f2f1f0bfbebdbcbbbab9b8b7b6b5b4b3b2b1b0
IV = 9a785634120000000000000000000000
Plaintext = 000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f202122232425262728292a2b2c2d2e2f3031
Ciphertext = edbf9dace45d6f6a7306e64be5dd824b2538f5724fcf24249ac111ab45ad39237a709959673bd8747d58690f8c762a353ad6

Cipher = aes-128-xts
Key = 2718281828459045235360287471352631415926535897932384626433832795
IV = 00000000000000000000000000000000
Plaintext = 000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f202122232425262728292a2b2c2d2e2f303132333435363738393a3b3c3d3e3f
Ciphertext = 27a7479befa1d476489f308cd4cfa6e2a96e4bbe3208ff25287dd3819616e89cc78cf7f5e543445f8333d8fa7f56000005279fa5d8b5e4ad40e736ddb4d35412

Cipher = aes-128-xts
Key = fffefdfcfbfaf9f8f7f6f5f4f3f2f1f0bfbebdbcbbbab9b8b7b6b5b4b3b2b1b0
IV = 9a785634120000000000000000000000
Plaintext = 000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f202122232425262728292a2b2c2d2e2f303132333435363738393a3b3c3d3e3f40
Ciphertext = edbf9dace45d6f6a7306e64be5dd824b2538f5724fcf24249ac111ab45ad39233ad6183c66fa548a3cdf3e36d2b21ccde9ffb48286ec211619e02decc7ca0883c6

Cipher = aes-128-xts
Key = fffefdfcfbfaf9f8f7f6f5f4f3f2f1f0bfbebdbcbbbab9b8b7b6b5b4b3b2b1b0
IV = 9a785634120000000000000000000000
Plaintext = 000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f202122232425262728292a2b2c2d2e2f303132333435363738393a3b3c3d3e3f404142434445464748494a4b4c4d4e4f
Ciphertext = edbf9dace45d6f6a7306e64be5dd824b2538f5724fcf24249ac111ab45ad39233ad6183c66fa548a3cdf3e36d2b21ccdc6bc657cb3aeb87ba2c5f58ffafacd76d0a098b687c0b6536d560ca007051b0b

Cipher = aes-128-xts
Key = fffefdfcfbfaf9f8f7f6f5f4f3f2f1f0bfbebdbcbbbab9b8b7b6b5b4b3b2b1b0
IV = 9a785634120000000000000000000000
Plaintext = 000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f202122232425262728292a2b2c2d2e2f303132333435363738393a3b3c3d3e3f404142434445464748494a4b4c4d4e4f5051
Ciphertext = edbf9dace45d6f6a7306e64be5dd824b2538f5724fcf24249ac111ab45ad39233ad6183c66fa548a3cdf3e36d2b21ccdc6bc657cb3aeb87ba2c5f58ffafacd765ecc4c85c0a01bf317b823fbd6111956d0a0

# To cover the branches of assembly code of aes_v8_xts_encrypt(decrypt)
Cipher = aes-128-xts
Key = 1111111111111111111111111111111122222222222222222222222222222222
IV = 33333333330000000000000000000000
Plaintext = 44444444444444444444444444444444
Ciphertext = c454185e6a16936e39334038acef838b

Cipher = aes-128-xts
Key = fffefdfcfbfaf9f8f7f6f5f4f3f2f1f022222222222222222222222222222222
IV = 33333333330000000000000000000000
Plaintext = 44444444444444444444444444444444
Ciphertext = af85336b597afc1a900b2eb21ec949d2

Title = Case insensitive AES tests

Cipher = Aes-128-eCb
Key = 2B7E151628AED2A6ABF7158809CF4F3C
Plaintext = 6BC1BEE22E409F96E93D7E117393172A
Ciphertext = 3AD77BB40D7A3660A89ECAF32466EF97

Cipher = AeS-128-cbC
Key = 2B7E151628AED2A6ABF7158809CF4F3C
IV = 73BED6B8E3C1743B7116E69E22229516
Plaintext = F69F2445DF4F9B17AD2B417BE66C3710
Ciphertext = 3FF1CAA1681FAC09120ECA307586E1A7
NextIV = 3ff1caa1681fac09120eca307586e1a7

Cipher = aES-128-CTR
Key = AE6852F8121067CC4BF7A5765577F39E
IV = 00000030000000000000000000000001
Operation = ENCRYPT
Plaintext = 53696E676C6520626C6F636B206D7367
Ciphertext = E4095D4FB7A7B3792D6175A3261311B8
NextIV = 00000030000000000000000000000002

Cipher = AES-128-GcM
Key = 00000000000000000000000000000000
IV = 000000000000000000000000
AAD =
Tag = ab6e47d42cec13bdf53a67b21257bddf
Plaintext = 00000000000000000000000000000000
Ciphertext = 0388dace60b6a392f328c2b971b2fe78
NextIV = 000000000000000000000000

