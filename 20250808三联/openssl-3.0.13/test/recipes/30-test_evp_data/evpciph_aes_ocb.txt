#
# Copyright 2001-2022 The OpenSSL Project Authors. All Rights Reserved.
#
# Licensed under the Apache License 2.0 (the "License").  You may not use
# this file except in compliance with the License.  You can obtain a copy
# in the file LICENSE in the source distribution or at
# https://www.openssl.org/source/license.html

# Tests start with one of these keywords
#       Cipher Decrypt Derive Digest Encoding KDF MAC PBE
#       PrivPubKeyPair Sign Verify VerifyRecover
# and continue until a blank line. Lines starting with a pound sign are ignored.

Title = AES OCB Test vectors

Cipher = aes-128-ocb
Key = 000102030405060708090A0B0C0D0E0F
IV = 000102030405060708090A0B
AAD =
Tag = 197B9C3C441D3C83EAFB2BEF633B9182
Plaintext =
Ciphertext =

Cipher = aes-128-ocb
Key = 000102030405060708090A0B0C0D0E0F
IV = 000102030405060708090A0B
AAD = 0001020304050607
Tag = 16DC76A46D47E1EAD537209E8A96D14E
Plaintext = 0001020304050607
Ciphertext = 92B657130A74B85A

Cipher = aes-128-ocb
Key = 000102030405060708090A0B0C0D0E0F
IV = 000102030405060708090A0B
AAD = 0001020304050607
Tag = 98B91552C8C009185044E30A6EB2FE21
Plaintext =
Ciphertext =

Cipher = aes-128-ocb
Key = 000102030405060708090A0B0C0D0E0F
IV = 000102030405060708090A0B
AAD =
Tag = 971EFFCAE19AD4716F88E87B871FBEED
Plaintext = 0001020304050607
Ciphertext = 92B657130A74B85A

Cipher = aes-128-ocb
Key = 000102030405060708090A0B0C0D0E0F
IV = 000102030405060708090A0B
AAD = 000102030405060708090A0B0C0D0E0F
Tag = 776C9924D6723A1FC4524532AC3E5BEB
Plaintext = 000102030405060708090A0B0C0D0E0F
Ciphertext = BEA5E8798DBE7110031C144DA0B26122

Cipher = aes-128-ocb
Key = 000102030405060708090A0B0C0D0E0F
IV = 000102030405060708090A0B
AAD = 000102030405060708090A0B0C0D0E0F
Tag = 7DDB8E6CEA6814866212509619B19CC6
Plaintext =
Ciphertext =

Cipher = aes-128-ocb
Key = 000102030405060708090A0B0C0D0E0F
IV = 000102030405060708090A0B
AAD =
Tag = 13CC8B747807121A4CBB3E4BD6B456AF
Plaintext = 000102030405060708090A0B0C0D0E0F
Ciphertext = BEA5E8798DBE7110031C144DA0B26122

Cipher = aes-128-ocb
Key = 000102030405060708090A0B0C0D0E0F
IV = 000102030405060708090A0B
AAD = 000102030405060708090A0B0C0D0E0F1011121314151617
Tag = 5FA94FC3F38820F1DC3F3D1FD4E55E1C
Plaintext = 000102030405060708090A0B0C0D0E0F1011121314151617
Ciphertext = BEA5E8798DBE7110031C144DA0B26122FCFCEE7A2A8D4D48

Cipher = aes-128-ocb
Key = 000102030405060708090A0B0C0D0E0F
IV = 000102030405060708090A0B
AAD = 000102030405060708090A0B0C0D0E0F1011121314151617
Tag = 282026DA3068BC9FA118681D559F10F6
Plaintext =
Ciphertext =

Cipher = aes-128-ocb
Key = 000102030405060708090A0B0C0D0E0F
IV = 000102030405060708090A0B
AAD =
Tag = 6EF2F52587FDA0ED97DC7EEDE241DF68
Plaintext = 000102030405060708090A0B0C0D0E0F1011121314151617
Ciphertext = BEA5E8798DBE7110031C144DA0B26122FCFCEE7A2A8D4D48

Cipher = aes-128-ocb
Key = 000102030405060708090A0B0C0D0E0F
IV = 000102030405060708090A0B
AAD = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F
Tag = B2A040DD3BD5164372D76D7BB6824240
Plaintext = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F
Ciphertext = BEA5E8798DBE7110031C144DA0B26122CEAAB9B05DF771A657149D53773463CB

Cipher = aes-128-ocb
Key = 000102030405060708090A0B0C0D0E0F
IV = 000102030405060708090A0B
AAD = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F
Tag = E1E072633BADE51A60E85951D9C42A1B
Plaintext =
Ciphertext =

Cipher = aes-128-ocb
Key = 000102030405060708090A0B0C0D0E0F
IV = 000102030405060708090A0B
AAD =
Tag = 4A3BAE824465CFDAF8C41FC50C7DF9D9
Plaintext = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F
Ciphertext = BEA5E8798DBE7110031C144DA0B26122CEAAB9B05DF771A657149D53773463CB

Cipher = aes-128-ocb
Key = 000102030405060708090A0B0C0D0E0F
IV = 000102030405060708090A0B
AAD = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F2021222324252627
Tag = 659C623211DEEA0DE30D2C381879F4C8
Plaintext = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F2021222324252627
Ciphertext = BEA5E8798DBE7110031C144DA0B26122CEAAB9B05DF771A657149D53773463CB68C65778B058A635

Cipher = aes-128-ocb
Key = 000102030405060708090A0B0C0D0E0F
IV = 000102030405060708090A0B
AAD = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F2021222324252627
Tag = 7AEB7A69A1687DD082CA27B0D9A37096
Plaintext =
Ciphertext =

Cipher = aes-128-ocb
Key = 000102030405060708090A0B0C0D0E0F
IV = 000102030405060708090A0B
AAD =
Tag = 060C8467F4ABAB5E8B3C2067A2E115DC
Plaintext = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F2021222324252627
Ciphertext = BEA5E8798DBE7110031C144DA0B26122CEAAB9B05DF771A657149D53773463CB68C65778B058A635

#AES OCB Non standard test vectors - generated from reference implementation
Cipher = aes-128-ocb
Key = 000102030405060708090A0B0C0D0E0F
IV = 000102030405060708090A0B
AAD = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F2021222324252627
Tag = 1b6c44f34e3abb3cbf8976e7
Plaintext = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F2021222324252627
Ciphertext = 09a4fd29de949d9a9aa9924248422097ad4883b4713e6c214ff6567ada08a96766fc4e2ee3e3a5a1

Cipher = aes-128-ocb
Key = 000102030405060708090A0B0C0D0E0F
IV = 000102030405060708090A0B0C0D0E
AAD = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F2021222324252627
Tag = 1ad62009901f40cba7cd7156f94a7324
Plaintext = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F2021222324252627
Ciphertext = 5e2fa7367ffbdb3938845cfd415fcc71ec79634eb31451609d27505f5e2978f43c44213d8fa441ee

Cipher = aes-128-ocb
Key = 000102030405060708090A0B0C0D0E0F
IV = 000102030405060708090A0B
AAD = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F2021222324252627
Tag = C203F98CE28F7DAD3F31C021
Plaintext = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F202122232425262728292A2B2C2D2E2F3031
Ciphertext = 09A4FD29DE949D9A9AA9924248422097AD4883B4713E6C214FF6567ADA08A967B2176C12F110DD441B7CAA3A509B13C822D6

Cipher = aes-128-ocb
Key = 000102030405060708090A0B0C0D0E0F
IV = 000102030405060708090A0B
AAD = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F2021222324252627
Tag = 8346D7D47C5D893ED472F5AB
Plaintext = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F202122232425262728292A2B2C2D2E2F303132333435363738393A3B3C3D3E3F4041
Ciphertext = 09A4FD29DE949D9A9AA9924248422097AD4883B4713E6C214FF6567ADA08A967B2176C12F110DD441B7CAA3A509B13C86A023AFCEE998BEE42028D44507B15F714FF

Cipher = aes-128-ocb
Key = 000102030405060708090A0B0C0D0E0F
IV = 000102030405060708090A0B
AAD = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F2021222324252627
Tag = 5822A9A70FDF55D29D2984A6
Plaintext = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F202122232425262728292A2B2C2D2E2F303132333435363738393A3B3C3D3E3F404142434445464748494A4B4C4D4E4F5051
Ciphertext = 09A4FD29DE949D9A9AA9924248422097AD4883B4713E6C214FF6567ADA08A967B2176C12F110DD441B7CAA3A509B13C86A023AFCEE998BEE42028D44507B15F77C528A1DE6406B519BCEE8FCB8294170634D

Cipher = aes-128-ocb
Key = 000102030405060708090A0B0C0D0E0F
IV = 000102030405060708090A0B
AAD = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F2021222324252627
Tag = 81772B6741ABB4ECA9D2DEB2
Plaintext = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F202122232425262728292A2B2C2D2E2F303132333435363738393A3B3C3D3E3F404142434445464748494A4B4C4D4E4F505152535455565758595A5B5C5D5E5F6061
Ciphertext = 09A4FD29DE949D9A9AA9924248422097AD4883B4713E6C214FF6567ADA08A967B2176C12F110DD441B7CAA3A509B13C86A023AFCEE998BEE42028D44507B15F77C528A1DE6406B519BCEE8FCB829417001E54E15A7576C4DF32366E0F439C7050FAA

Cipher = aes-128-ocb
Key = 000102030405060708090A0B0C0D0E0F
IV = 000102030405060708090A0B
AAD = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F2021222324252627
Tag = 3E52A01D068DE85456DB03B7
Plaintext = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F202122232425262728292A2B2C2D2E2F303132333435363738393A3B3C3D3E3F404142434445464748494A4B4C4D4E4F505152535455565758595A5B5C5D5E5F606162636465666768696A6B6C6D6E6F7071
Ciphertext = 09A4FD29DE949D9A9AA9924248422097AD4883B4713E6C214FF6567ADA08A967B2176C12F110DD441B7CAA3A509B13C86A023AFCEE998BEE42028D44507B15F77C528A1DE6406B519BCEE8FCB829417001E54E15A7576C4DF32366E0F439C7051CB4824B8114E9A720CBC1CE0185B156B486

Cipher = aes-128-ocb
Key = 000102030405060708090A0B0C0D0E0F
IV = 000102030405060708090A0B
AAD = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F2021222324252627
Tag = 3E52A01D068DE85456DB03B6
Plaintext = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F202122232425262728292A2B2C2D2E2F303132333435363738393A3B3C3D3E3F404142434445464748494A4B4C4D4E4F505152535455565758595A5B5C5D5E5F606162636465666768696A6B6C6D6E6F7071
Ciphertext = 09A4FD29DE949D9A9AA9924248422097AD4883B4713E6C214FF6567ADA08A967B2176C12F110DD441B7CAA3A509B13C86A023AFCEE998BEE42028D44507B15F77C528A1DE6406B519BCEE8FCB829417001E54E15A7576C4DF32366E0F439C7051CB4824B8114E9A720CBC1CE0185B156B486
Operation = DECRYPT
Result = CIPHERFINAL_ERROR

#Test vectors generated to validate aesni_ocb_encrypt on x86
Cipher = aes-128-ocb
Key = 000102030405060708090A0B0C0D0E0F
IV = 000000000001020304050607
Tag = C14DFF7D62A13C4A3422456207453190
Plaintext = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F202122232425262728292A2B2C2D2E2F303132333435363738393A3B3C3D3E3F404142434445464748494A4B4C4D4E4F505152535455565758595A5B5C5D5E5F
Ciphertext = F5186C9CC3506386919B6FD9443956E05B203313F8AB35E916AB36932EBDDCD2945901BABE7CF29404929F322F954C916065FABF8F1E52F4BD7C538C0F96899519DBC6BC504D837D8EBD1436B45D33F528CB642FA2EB2C403FE604C12B819333

Cipher = aes-128-ocb
Key = 000102030405060708090A0B0C0D0E0F
IV = 000000000001020304050607
Tag = D47D84F6FF912C79B6A4223AB9BE2DB8
Plaintext = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F202122232425262728292A2B2C2D2E2F303132333435363738393A3B3C3D3E3F404142434445464748494A4B4C4D4E4F505152535455565758595A5B5C5D5E5F606162636465666768696A6B6C6D6E6F
Ciphertext = F5186C9CC3506386919B6FD9443956E05B203313F8AB35E916AB36932EBDDCD2945901BABE7CF29404929F322F954C916065FABF8F1E52F4BD7C538C0F96899519DBC6BC504D837D8EBD1436B45D33F528CB642FA2EB2C403FE604C12B8193332374120A78A1171D23ED9E9CB1ADC204

Cipher = aes-128-ocb
Key = 000102030405060708090A0B0C0D0E0F
IV = 000000000001020304050607
Tag = 41970D13737B7BD1B5FBF49ED4412CA5
Plaintext = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F202122232425262728292A2B2C2D2E2F303132333435363738393A3B3C3D3E3F404142434445464748494A4B4C4D4E4F505152535455565758595A5B5C5D5E5F606162636465666768696A6B6C6D6E6F7071000102030405060708090A0B0C0D
Ciphertext = F5186C9CC3506386919B6FD9443956E05B203313F8AB35E916AB36932EBDDCD2945901BABE7CF29404929F322F954C916065FABF8F1E52F4BD7C538C0F96899519DBC6BC504D837D8EBD1436B45D33F528CB642FA2EB2C403FE604C12B8193332374120A78A1171D23ED9E9CB1ADC20412C017AD0CA498827C768DDD99B26E91

Cipher = aes-128-ocb
Key = 000102030405060708090A0B0C0D0E0F
IV = 000000000001020304050607
Tag = BE0228651ED4E48A11BDED68D953F3A0
Plaintext = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F202122232425262728292A2B2C2D2E2F303132333435363738393A3B3C3D3E3F404142434445464748494A4B4C4D4E4F505152535455565758595A5B5C5D5E5F606162636465666768696A6B6C6D6E6F7071000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D
Ciphertext = F5186C9CC3506386919B6FD9443956E05B203313F8AB35E916AB36932EBDDCD2945901BABE7CF29404929F322F954C916065FABF8F1E52F4BD7C538C0F96899519DBC6BC504D837D8EBD1436B45D33F528CB642FA2EB2C403FE604C12B8193332374120A78A1171D23ED9E9CB1ADC20412C017AD0CA498827C768DDD99B26E91EDB8681700FF30366F07AEDE8CEACC1F

Cipher = aes-128-ocb
Key = 000102030405060708090A0B0C0D0E0F
IV = 000000000001020304050607
Tag = 17BC6E10B16E5FDC52836E7D589518C7
Plaintext = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F202122232425262728292A2B2C2D2E2F303132333435363738393A3B3C3D3E3F404142434445464748494A4B4C4D4E4F505152535455565758595A5B5C5D5E5F606162636465666768696A6B6C6D6E6F7071000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F202122232425262728292A2B2C2D
Ciphertext = F5186C9CC3506386919B6FD9443956E05B203313F8AB35E916AB36932EBDDCD2945901BABE7CF29404929F322F954C916065FABF8F1E52F4BD7C538C0F96899519DBC6BC504D837D8EBD1436B45D33F528CB642FA2EB2C403FE604C12B8193332374120A78A1171D23ED9E9CB1ADC20412C017AD0CA498827C768DDD99B26E91EDB8681700FF30366F07AEDE8CEACC1F39BE69B91BC808FA7A193F7EEA43137B

Cipher = aes-128-ocb
Key = 000102030405060708090A0B0C0D0E0F
IV = 000000000001020304050607
Tag = E84AAC18666116990A3A37B3A5FC55BD
Plaintext = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F202122232425262728292A2B2C2D2E2F303132333435363738393A3B3C3D3E3F404142434445464748494A4B4C4D4E4F505152535455565758595A5B5C5D5E5F606162636465666768696A6B6C6D6E6F7071000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F202122232425262728292A2B2C2D2E2F303132333435363738393A3B3C3D
Ciphertext = F5186C9CC3506386919B6FD9443956E05B203313F8AB35E916AB36932EBDDCD2945901BABE7CF29404929F322F954C916065FABF8F1E52F4BD7C538C0F96899519DBC6BC504D837D8EBD1436B45D33F528CB642FA2EB2C403FE604C12B8193332374120A78A1171D23ED9E9CB1ADC20412C017AD0CA498827C768DDD99B26E91EDB8681700FF30366F07AEDE8CEACC1F39BE69B91BC808FA7A193F7EEA43137B11CF99263D693AEBDF8ADE1A1D838DED

Cipher = aes-128-ocb
Key = 000102030405060708090A0B0C0D0E0F
IV = 000000000001020304050607
Tag = 3E5EA7EE064FE83B313E28D411E91EAD
Plaintext = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F202122232425262728292A2B2C2D2E2F303132333435363738393A3B3C3D3E3F404142434445464748494A4B4C4D4E4F505152535455565758595A5B5C5D5E5F606162636465666768696A6B6C6D6E6F7071000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F202122232425262728292A2B2C2D2E2F303132333435363738393A3B3C3D3E3F404142434445464748494A4B4C4D
Ciphertext = F5186C9CC3506386919B6FD9443956E05B203313F8AB35E916AB36932EBDDCD2945901BABE7CF29404929F322F954C916065FABF8F1E52F4BD7C538C0F96899519DBC6BC504D837D8EBD1436B45D33F528CB642FA2EB2C403FE604C12B8193332374120A78A1171D23ED9E9CB1ADC20412C017AD0CA498827C768DDD99B26E91EDB8681700FF30366F07AEDE8CEACC1F39BE69B91BC808FA7A193F7EEA43137B11CF99263D693AEBDF8ADE1A1D838DED48D9E09F452F8E6FBEB76A3DED47611C
