#
# Copyright 2001-2021 The OpenSSL Project Authors. All Rights Reserved.
#
# Licensed under the Apache License 2.0 (the "License").  You may not use
# this file except in compliance with the License.  You can obtain a copy
# in the file LICENSE in the source distribution or at
# https://www.openssl.org/source/license.html

# Tests start with one of these keywords
#       Cipher Decrypt Derive Digest Encoding KDF MAC PBE
#       PrivPubKeyPair Sign Verify VerifyRecover
# and continue until a blank line. Lines starting with a pound sign are ignored.

Title = ARIA test vectors from RFC5794 (and others)

Cipher = ARIA-128-ECB
Key = 000102030405060708090a0b0c0d0e0f
Plaintext = 00112233445566778899aabbccddeeff
Ciphertext = d718fbd6ab644c739da95f3be6451778

Cipher = ARIA-192-ECB
Key = 000102030405060708090a0b0c0d0e0f1011121314151617
Plaintext = 00112233445566778899aabbccddeeff
Ciphertext = 26449c1805dbe7aa25a468ce263a9e79

Cipher = ARIA-256-ECB
Key = 000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f
Plaintext = 00112233445566778899aabbccddeeff
Ciphertext = f92bd7c79fb72e2f2b8f80c1972d24fc

# Additional ARIA mode vectors from http://*************/ARIA/doc/ARIA-testvector-e.pdf
Cipher = ARIA-128-ECB
Key = 00112233445566778899aabbccddeeff
Plaintext = 11111111aaaaaaaa11111111bbbbbbbb11111111cccccccc11111111dddddddd22222222aaaaaaaa22222222bbbbbbbb22222222cccccccc22222222dddddddd33333333aaaaaaaa33333333bbbbbbbb33333333cccccccc33333333dddddddd44444444aaaaaaaa44444444bbbbbbbb44444444cccccccc44444444dddddddd55555555aaaaaaaa55555555bbbbbbbb55555555cccccccc55555555dddddddd
Ciphertext = c6ecd08e22c30abdb215cf74e2075e6e29ccaac63448708d331b2f816c51b17d9e133d1528dbf0af5787c7f3a3f5c2bf6b6f345907a3055612ce072ff54de7d788424da6e8ccfe8172b391be499354165665ba7864917000a6eeb2ecb4a698edfc7887e7f556377614ab0a282293e6d884dbb84206cdb16ed1754e77a1f243fd086953f752cc1e46c7c794ae85537dcaec8dd721f55c93b6edfe2adea43873e8

Cipher = ARIA-128-CBC
Key = 00112233445566778899aabbccddeeff
IV = 0f1e2d3c4b5a69788796a5b4c3d2e1f0
Plaintext = 11111111aaaaaaaa11111111bbbbbbbb11111111cccccccc11111111dddddddd22222222aaaaaaaa22222222bbbbbbbb22222222cccccccc22222222dddddddd33333333aaaaaaaa33333333bbbbbbbb33333333cccccccc33333333dddddddd44444444aaaaaaaa44444444bbbbbbbb44444444cccccccc44444444dddddddd55555555aaaaaaaa55555555bbbbbbbb55555555cccccccc55555555dddddddd
Ciphertext = 49d61860b14909109cef0d22a9268134fadf9fb23151e9645fba75018bdb1538b53334634bbf7d4cd4b5377033060c155fe3948ca75de1031e1d85619e0ad61eb419a866b3c2dbfd10a4ed18b22149f75897f0b8668b0c1c542c687778835fb7cd46e45f85eaa7072437dd9fa6793d6f8d4ccefc4eb1ac641ac1bd30b18c6d64c49bca137eb21c2e04da62712ca2b4f540c57112c38791852cfac7a5d19ed83a
NextIV = 40c57112c38791852cfac7a5d19ed83a

Cipher = ARIA-128-CFB
Key = 00112233445566778899aabbccddeeff
IV = 0f1e2d3c4b5a69788796a5b4c3d2e1f0
Plaintext = 11111111aaaaaaaa11111111bbbbbbbb11111111cccccccc11111111dddddddd22222222aaaaaaaa22222222bbbbbbbb22222222cccccccc22222222dddddddd33333333aaaaaaaa33333333bbbbbbbb33333333cccccccc33333333dddddddd44444444aaaaaaaa44444444bbbbbbbb44444444cccccccc44444444dddddddd55555555aaaaaaaa55555555bbbbbbbb55555555cccccccc55555555dddddddd
Ciphertext = 3720e53ba7d615383406b09f0a05a200c07c21e6370f413a5d132500a68285017c61b434c7b7ca9685a51071861e4d4bb873b599b479e2d573dddeafba89f812ac6a9e44d554078eb3be94839db4b33da3f59c063123a7ef6f20e10579fa4fd239100ca73b52d4fcafeadee73f139f78f9b7614c2b3b9dbe010f87db06a89a9435f79ce8121431371f4e87b984e0230c22a6dacb32fc42dcc6accef33285bf11
NextIV = 22a6dacb32fc42dcc6accef33285bf11

Cipher = ARIA-128-CFB8
Key = 00112233445566778899aabbccddeeff
IV = 0f1e2d3c4b5a69788796a5b4c3d2e1f0
Plaintext = 11111111aaaaaaaa11111111bbbbbbbb11111111cccccccc11111111dddddddd22222222aaaaaaaa22222222bbbbbbbb22222222cccccccc22222222dddddddd33333333aaaaaaaa33333333bbbbbbbb33333333cccccccc33333333dddddddd44444444aaaaaaaa44444444bbbbbbbb44444444cccccccc44444444dddddddd55555555aaaaaaaa55555555bbbbbbbb55555555cccccccc55555555dddddddd
Ciphertext = 373c8f6a965599ec785cc8f8149f6c81b632ccb8e0c6eb6a9707ae52c59257a41f94701c1096933127a90195ed0c8e98690547572423bb45c3d70e4a18ee56b967c10e000ba4df5fba7c404134a343d8375d04b151d161ef83417fe1748447d30a6723c406733df7d18aa39a20752d2381942e244811bb97f72eae446b1815aa690cd1b1adcbd007c0088ecdc91cb2e2caf0e11e72459878137eea64ac62a9a1

Cipher = ARIA-128-OFB
Key = 00112233445566778899aabbccddeeff
IV = 0f1e2d3c4b5a69788796a5b4c3d2e1f0
Plaintext = 11111111aaaaaaaa11111111bbbbbbbb11111111cccccccc11111111dddddddd22222222aaaaaaaa22222222bbbbbbbb22222222cccccccc22222222dddddddd33333333aaaaaaaa33333333bbbbbbbb33333333cccccccc33333333dddddddd44444444aaaaaaaa44444444bbbbbbbb44444444cccccccc44444444dddddddd55555555aaaaaaaa55555555bbbbbbbb55555555cccccccc55555555dddddddd
Ciphertext = 3720e53ba7d615383406b09f0a05a2000063063f0560083483faeb041c8adecef30cf80cefb002a0d280759168ec01db3d49f61aced260bd43eec0a2731730eec6fa4f2304319cf8ccac2d7be7833e4f8ae6ce967012c1c6badc5d28e7e4144f6bf5cebe01253ee202afce4bc61f28dec069a6f16f6c8a7dd2afae44148f6ff4d0029d5c607b5fa6b8c8a6301cde5c7033565cd0b8f0974ab490b236197ba04a
NextIV = 66030985743c5b86e1c5e763c4a67d97

Cipher = ARIA-128-CTR
Key = 00112233445566778899aabbccddeeff
IV = 00000000000000000000000000000000
Plaintext = 11111111aaaaaaaa11111111bbbbbbbb11111111cccccccc11111111dddddddd22222222aaaaaaaa22222222bbbbbbbb22222222cccccccc22222222dddddddd33333333aaaaaaaa33333333bbbbbbbb33333333cccccccc33333333dddddddd44444444aaaaaaaa44444444bbbbbbbb44444444cccccccc44444444dddddddd55555555aaaaaaaa55555555bbbbbbbb55555555cccccccc55555555dddddddd
Ciphertext = ac5d7de805a0bf1c57c854501af60fa11497e2a34519dea1569e91e5b5ccae2ff3bfa1bf975f4571f48be191613546c3911163c085f871f0e7ae5f2a085b81851c2a3ddf20ecb8fa51901aec8ee4ba32a35dab67bb72cd9140ad188a967ac0fbbdfa94ea6cce47dcf8525ab5a814cfeb2bb60ee2b126e2d9d847c1a9e96f9019e3e6a7fe40d3829afb73db1cc245646addb62d9b907baaafbe46a73dbc131d3d
NextIV = 0000000000000000000000000000000a

Cipher = ARIA-192-ECB
Key = 00112233445566778899aabbccddeeff0011223344556677
Plaintext = 11111111aaaaaaaa11111111bbbbbbbb11111111cccccccc11111111dddddddd22222222aaaaaaaa22222222bbbbbbbb22222222cccccccc22222222dddddddd33333333aaaaaaaa33333333bbbbbbbb33333333cccccccc33333333dddddddd44444444aaaaaaaa44444444bbbbbbbb44444444cccccccc44444444dddddddd55555555aaaaaaaa55555555bbbbbbbb55555555cccccccc55555555dddddddd
Ciphertext = 8d1470625f59ebacb0e55b534b3e462b5f23d33bff78f46c3c15911f4a21809aaccad80b4bda915aa9dae6bcebe06a6c83f77fd5391acfe61de2f646b5d447edbfd5bb49b12fbb9145b227895a757b2af1f7188734863d7b8b6ede5a5b2f06a0a233c8523d2db778fb31b0e311f32700152f33861e9d040c83b5eb40cd88ea49975709dc629365a189f78a3ec40345fc6a5a307a8f9a4413091e007eca5645a0

Cipher = ARIA-192-CBC
Key = 00112233445566778899aabbccddeeff0011223344556677
IV = 0f1e2d3c4b5a69788796a5b4c3d2e1f0
Plaintext = 11111111aaaaaaaa11111111bbbbbbbb11111111cccccccc11111111dddddddd22222222aaaaaaaa22222222bbbbbbbb22222222cccccccc22222222dddddddd33333333aaaaaaaa33333333bbbbbbbb33333333cccccccc33333333dddddddd44444444aaaaaaaa44444444bbbbbbbb44444444cccccccc44444444dddddddd55555555aaaaaaaa55555555bbbbbbbb55555555cccccccc55555555dddddddd
Ciphertext = afe6cf23974b533c672a826264ea785f4e4f7f780dc7f3f1e0962b80902386d514e9c3e77259de92dd1102ffab086c1ea52a71260db5920a83295c25320e421147ca45d532f327b856ea947cd2196ae2e040826548b4c891b0ed0ca6e714dbc4631998d548110d666b3d54c2a091955c6f05beb4f62309368696c9791fc4c551564a2637f194346ec45fbca6c72a5b4612e208d531d6c34cc5c64eac6bd0cf8c
NextIV = 12e208d531d6c34cc5c64eac6bd0cf8c

Cipher = ARIA-192-CFB
Key = 00112233445566778899aabbccddeeff0011223344556677
IV = 0f1e2d3c4b5a69788796a5b4c3d2e1f0
Plaintext = 11111111aaaaaaaa11111111bbbbbbbb11111111cccccccc11111111dddddddd22222222aaaaaaaa22222222bbbbbbbb22222222cccccccc22222222dddddddd33333333aaaaaaaa33333333bbbbbbbb33333333cccccccc33333333dddddddd44444444aaaaaaaa44444444bbbbbbbb44444444cccccccc44444444dddddddd55555555aaaaaaaa55555555bbbbbbbb55555555cccccccc55555555dddddddd
Ciphertext = 4171f7192bf4495494d2736129640f5c4d87a9a213664c9448477c6ecc2013598d9766952dd8c3868f17e36ef66fd84bfa45d1593d2d6ee3ea2115047d710d4fb66187caa3a315b3c8ea2d313962edcfe5a3e2028d5ba9a09fd5c65c19d3440e477f0cab0628ec6902c73ee02f1afee9f80115be7b9df82d1e28228e28581a20560e195cbb9e2b327bf56fd2d0ae5502e42c13e9b4015d4da42dc859252e7da4
NextIV = e42c13e9b4015d4da42dc859252e7da4

Cipher = ARIA-192-CFB8
Key = 00112233445566778899aabbccddeeff0011223344556677
IV = 0f1e2d3c4b5a69788796a5b4c3d2e1f0
Plaintext = 11111111aaaaaaaa11111111bbbbbbbb11111111cccccccc11111111dddddddd22222222aaaaaaaa22222222bbbbbbbb22222222cccccccc22222222dddddddd33333333aaaaaaaa33333333bbbbbbbb33333333cccccccc33333333dddddddd44444444aaaaaaaa44444444bbbbbbbb44444444cccccccc44444444dddddddd55555555aaaaaaaa55555555bbbbbbbb55555555cccccccc55555555dddddddd
Ciphertext = 411d3b4f57f705aa4d13c46e2cf426af7c8c916ed7923d889f0047bbf11471b6d54f8757ef519339105be3cb69babb976a57d5631fc23cc3051fe9d36e8b8e27a2b2c0c4d31928ccbf30ea8239b46ba1b77f6198e7ecd2ce27b35958148e826f06aaf385bd30362ff141583e7c1d8924d44d36a1133094074631e18adafa9d2e55de98f6895c89d4266ebd33f3d4be5153a96fa12132ece2e81e66e55baa7ade

Cipher = ARIA-192-OFB
Key = 00112233445566778899aabbccddeeff0011223344556677
IV = 0f1e2d3c4b5a69788796a5b4c3d2e1f0
Plaintext = 11111111aaaaaaaa11111111bbbbbbbb11111111cccccccc11111111dddddddd22222222aaaaaaaa22222222bbbbbbbb22222222cccccccc22222222dddddddd33333333aaaaaaaa33333333bbbbbbbb33333333cccccccc33333333dddddddd44444444aaaaaaaa44444444bbbbbbbb44444444cccccccc44444444dddddddd55555555aaaaaaaa55555555bbbbbbbb55555555cccccccc55555555dddddddd
Ciphertext = 4171f7192bf4495494d2736129640f5cc224d26d364b5a06ddde13d0f1e74faa846de354c63cda77469d1a2d425c47ff41734c71b3fa1fcdc11e0b2de22bfeed54898e233df652c75ae136e61de6524e62b3f806fb2e8e616eb410a1b9500537e327ffb04f19f7f82fde2b122100261f81b82723bf936be7beaaf3067d1c036001f1ade71422268d274d7dc6c6ae1970b27a5f2c2f39c1d241fe8cac5ccd74e9
NextIV = e72f0a79e3f50d1e14abd9f98110a934

Cipher = ARIA-192-CTR
Key = 00112233445566778899aabbccddeeff0011223344556677
IV = 00000000000000000000000000000000
Plaintext = 11111111aaaaaaaa11111111bbbbbbbb11111111cccccccc11111111dddddddd22222222aaaaaaaa22222222bbbbbbbb22222222cccccccc22222222dddddddd33333333aaaaaaaa33333333bbbbbbbb33333333cccccccc33333333dddddddd44444444aaaaaaaa44444444bbbbbbbb44444444cccccccc44444444dddddddd55555555aaaaaaaa55555555bbbbbbbb55555555cccccccc55555555dddddddd
Ciphertext = 08625ca8fe569c19ba7af3760a6ed1cef4d199263e999dde14082dbba7560b79a4c6b456b8707dce751f9854f18893dfdb3f4e5afa539733e6f1e70b98ba37891f8f81e95df8efc26c7ce043504cb18958b865e4e316cd2aa1c97f31bf23dc046ef326b95a692a191ba0f2a41c5fe9ae070f236ff7078e703b42666caafbdd20bad74ac4c20c0f46c7ca24c151716575c947da16c90cfe1bf217a41cfebe7531
NextIV = 0000000000000000000000000000000a

Cipher = ARIA-256-ECB
Key = 00112233445566778899aabbccddeeff00112233445566778899aabbccddeeff
Plaintext = 11111111aaaaaaaa11111111bbbbbbbb11111111cccccccc11111111dddddddd22222222aaaaaaaa22222222bbbbbbbb22222222cccccccc22222222dddddddd33333333aaaaaaaa33333333bbbbbbbb33333333cccccccc33333333dddddddd44444444aaaaaaaa44444444bbbbbbbb44444444cccccccc44444444dddddddd55555555aaaaaaaa55555555bbbbbbbb55555555cccccccc55555555dddddddd
Ciphertext = 58a875e6044ad7fffa4f58420f7f442d8e191016f28e79aefc01e204773280d7018e5f7a938ec30711719953bae86542cd7ebc752474c1a5f6eaaace2a7e29462ee7dfa5afdb84177ead95ccd4b4bb6e1ed17b9534cff0a5fc2941429cfee2ee49c7adbeb7e9d1b0d2a8531d942079596a27ed79f5b1dd13ecd604b07a48885a3afa0627a0e4e60a3c703af292f1baa77b702f16c54aa74bc727ea95c7468b00

Cipher = ARIA-256-CBC
Key = 00112233445566778899aabbccddeeff00112233445566778899aabbccddeeff
IV = 0f1e2d3c4b5a69788796a5b4c3d2e1f0
Plaintext = 11111111aaaaaaaa11111111bbbbbbbb11111111cccccccc11111111dddddddd22222222aaaaaaaa22222222bbbbbbbb22222222cccccccc22222222dddddddd33333333aaaaaaaa33333333bbbbbbbb33333333cccccccc33333333dddddddd44444444aaaaaaaa44444444bbbbbbbb44444444cccccccc44444444dddddddd55555555aaaaaaaa55555555bbbbbbbb55555555cccccccc55555555dddddddd
Ciphertext = 523a8a806ae621f155fdd28dbc34e1ab7b9b42432ad8b2efb96e23b13f0a6e52f36185d50ad002c5f601bee5493f118b243ee2e313642bffc3902e7b2efd9a12fa682edd2d23c8b9c5f043c18b17c1ec4b5867918270fbec1027c19ed6af833da5d620994668ca22f599791d292dd6273b2959082aafb7a996167cce1eec5f0cfd15f610d87e2dda9ba68ce1260ca54b222491418374294e7909b1e8551cd8de
NextIV = 222491418374294e7909b1e8551cd8de

Cipher = ARIA-256-CFB
Key = 00112233445566778899aabbccddeeff00112233445566778899aabbccddeeff
IV = 0f1e2d3c4b5a69788796a5b4c3d2e1f0
Plaintext = 11111111aaaaaaaa11111111bbbbbbbb11111111cccccccc11111111dddddddd22222222aaaaaaaa22222222bbbbbbbb22222222cccccccc22222222dddddddd33333333aaaaaaaa33333333bbbbbbbb33333333cccccccc33333333dddddddd44444444aaaaaaaa44444444bbbbbbbb44444444cccccccc44444444dddddddd55555555aaaaaaaa55555555bbbbbbbb55555555cccccccc55555555dddddddd
Ciphertext = 26834705b0f2c0e2588d4a7f09009635f28bb93d8c31f870ec1e0bdb082b66fa402dd9c202be300c4517d196b14d4ce11dce97f7aaba54341b0d872cc9b63753a3e8556a14be6f7b3e27e3cfc39caf80f2a355aa50dc83c09c7b11828694f8e4aa726c528976b53f2c877f4991a3a8d28adb63bd751846ffb2350265e179d4990753ae8485ff9b4133ddad5875b84a90cbcfa62a045d726df71b6bda0eeca0be
NextIV = cbcfa62a045d726df71b6bda0eeca0be

Cipher = ARIA-256-CFB8
Key = 00112233445566778899aabbccddeeff00112233445566778899aabbccddeeff
IV = 0f1e2d3c4b5a69788796a5b4c3d2e1f0
Plaintext = 11111111aaaaaaaa11111111bbbbbbbb11111111cccccccc11111111dddddddd22222222aaaaaaaa22222222bbbbbbbb22222222cccccccc22222222dddddddd33333333aaaaaaaa33333333bbbbbbbb33333333cccccccc33333333dddddddd44444444aaaaaaaa44444444bbbbbbbb44444444cccccccc44444444dddddddd55555555aaaaaaaa55555555bbbbbbbb55555555cccccccc55555555dddddddd
Ciphertext = 26baa33651e1f66434fec88ef27fd2b9a79e246dd89a3ffa00e8bdb37155433e6c24bd0b87d9a85baa9f485ccb984f5ec24d6a3ef5e3c81396177f039cf580dfdb55d6e1c47a28921dfe369e12fd357b289ad3a5544e1c1bd616d454db9c5f91f603373f29d5b2ed1b4b51de80f28537bbd43d5e3b5dd071dc91153cbbe732dfc325821b06ed8acaae656dcf2da9f13e4f29db671476f1e644ff06d9b67d6bd4

Cipher = ARIA-256-OFB
Key = 00112233445566778899aabbccddeeff00112233445566778899aabbccddeeff
IV = 0f1e2d3c4b5a69788796a5b4c3d2e1f0
Plaintext = 11111111aaaaaaaa11111111bbbbbbbb11111111cccccccc11111111dddddddd22222222aaaaaaaa22222222bbbbbbbb22222222cccccccc22222222dddddddd33333333aaaaaaaa33333333bbbbbbbb33333333cccccccc33333333dddddddd44444444aaaaaaaa44444444bbbbbbbb44444444cccccccc44444444dddddddd55555555aaaaaaaa55555555bbbbbbbb55555555cccccccc55555555dddddddd
Ciphertext = 26834705b0f2c0e2588d4a7f0900963584c256815c4292b59f8d3f966a75b52345b4f5f98c785d3f368a8d5ff89b7f950ceab3cd63773c2621d652b8ef98b4196afb2c2b30496bc5b7d9e7f9084f9d855f63a511751c8909e7a6deadbe0a67a4fb89383ca5d209c6f66f793fc471195c476fb9c1eab2ac91e680e454b4f3ed9a67fb52f09c29b965b23cfa6f3f6bbb2a86c6cdbaa2857bf2486f543231892a52
NextIV = d39398ef6e49b73e1d3a0167ec54f78f

Cipher = ARIA-256-CTR
Key = 00112233445566778899aabbccddeeff00112233445566778899aabbccddeeff
IV = 00000000000000000000000000000000
Plaintext = 11111111aaaaaaaa11111111bbbbbbbb11111111cccccccc11111111dddddddd22222222aaaaaaaa22222222bbbbbbbb22222222cccccccc22222222dddddddd33333333aaaaaaaa33333333bbbbbbbb33333333cccccccc33333333dddddddd44444444aaaaaaaa44444444bbbbbbbb44444444cccccccc44444444dddddddd55555555aaaaaaaa55555555bbbbbbbb55555555cccccccc55555555dddddddd
Ciphertext = 30026c329666141721178b99c0a1f1b2f06940253f7b3089e2a30ea86aa3c88f5940f05ad7ee41d71347bb7261e348f18360473fdf7d4e7723bffb4411cc13f6cdd89f3bc7b9c768145022c7a74f14d7c305cd012a10f16050c23f1ae5c23f45998d13fbaa041e51619577e0772764896a5d4516d8ffceb3bf7e05f613edd9a60cdcedaff9cfcaf4e00d445a54334f73ab2cad944e51d266548e61c6eb0aa1cd
NextIV = 0000000000000000000000000000000a

Title = ARIA GCM test vectors from RFC8269

Cipher = ARIA-128-GCM
Key = e91e5e75da65554a48181f3846349562
IV = 000020e8f5eb00000000315e
AAD = 8008315ebf2e6fe020e8f5eb
Tag = 5abace3f37f5a736f4be984bbffbedc1
Plaintext = f57af5fd4ae19562976ec57a5a7ad55a5af5c5e5c5fdf5c55ad57a4a7272d57262e9729566ed66e97ac54a4a5a7ad5e15ae5fdd5fd5ac5d56ae56ad5c572d54ae54ac55a956afd6aed5a4ac562957a9516991691d572fd14e97ae962ed7a9f4a955af572e162f57a956666e17ae1f54a95f566d54a66e16e4afd6a9f7ae1c5c55ae5d56afde916c5e94a6ec56695e14afde1148416e94ad57ac5146ed59d1cc5
Ciphertext = 4d8a9a0675550c704b17d8c9ddc81a5cd6f7da34f2fe1b3db7cb3dfb9697102ea0f3c1fc2dbc873d44bceeae8e4442974ba21ff6789d3272613fb9631a7cf3f14bacbeb421633a90ffbe58c2fa6bdca534f10d0de0502ce1d531b6336e58878278531e5c22bc6c85bbd784d78d9e680aa19031aaf89101d669d7a3965c1f7e16229d7463e0535f4e253f5d18187d40b8ae0f564bd970b5e7e2adfb211e89a953

Cipher = ARIA-256-GCM
Key = 0c5ffd37a11edc42c325287fc0604f2e3e8cd5671a00fe3216aa5eb105783b54
IV = 000020e8f5eb00000000315e
AAD = 8008315ebf2e6fe020e8f5eb
Tag = e210d6ced2cf430ff841472915e7ef48
Plaintext = f57af5fd4ae19562976ec57a5a7ad55a5af5c5e5c5fdf5c55ad57a4a7272d57262e9729566ed66e97ac54a4a5a7ad5e15ae5fdd5fd5ac5d56ae56ad5c572d54ae54ac55a956afd6aed5a4ac562957a9516991691d572fd14e97ae962ed7a9f4a955af572e162f57a956666e17ae1f54a95f566d54a66e16e4afd6a9f7ae1c5c55ae5d56afde916c5e94a6ec56695e14afde1148416e94ad57ac5146ed59d1cc5
Ciphertext = 6f9e4bcbc8c85fc0128fb1e4a0a20cb9932ff74581f54fc013dd054b19f99371425b352d97d3f337b90b63d1b082adeeea9d2d7391897d591b985e55fb50cb5350cf7d38dc27dda127c078a149c8eb98083d66363a46e3726af217d3a00275ad5bf772c7610ea4c23006878f0ee69a8397703169a419303f40b72e4573714d19e2697df61e7c7252e5abc6bade876ac4961bfac4d5e867afca351a48aed52822

Title = ARIA GCM self-generated test vectors

Cipher = ARIA-128-GCM
Key = e91e5e75da65554a48181f3846349562
# Shorter than default IV
IV = 0001020304
AAD = 8008315ebf2e6fe020e8f5eb
Tag = ebaa2645bb154542117ee46031aa176e
Plaintext = f57af5fd4ae19562976ec57a5a7ad55a5af5c5e5c5fdf5c55ad57a4a7272d57262e9729566ed66e97ac54a4a5a7ad5e15ae5fdd5fd5ac5d56ae56ad5c572d54ae54ac55a956afd6aed5a4ac562957a9516991691d572fd14e97ae962ed7a9f4a955af572e162f57a956666e17ae1f54a95f566d54a66e16e4afd6a9f7ae1c5c55ae5d56afde916c5e94a6ec56695e14afde1148416e94ad57ac5146ed59d1cc5
Ciphertext = 1723ccfc0ed44a12520473cfeb63bc933cd450a943f5f1cba78e19d72f80cc102acc51f2459a06cf6435182b8ddd451f83e13479efe5ec7dfbf16229f4017920fb41457a9b6fe1a401b30b2f332d827ae2f86e962326927c1ed8bfedac1f7a00ddde63bd392a8f28a488ba5974689f8d15b9b1739fb50aae0ff244026ec72064003c621b33ffc8086b0a97eefb70604a2826f6499f6eb12d67a0da03fc8e1482

Cipher = ARIA-128-GCM
Key = e91e5e75da65554a48181f3846349562
# Longer than default IV
IV = 000102030405060708090a0b0c0d0e0f
AAD = 8008315ebf2e6fe020e8f5eb
Tag = 61f7f44c7da3c60195b29ae0b46051a4
Plaintext = f57af5fd4ae19562976ec57a5a7ad55a5af5c5e5c5fdf5c55ad57a4a7272d57262e9729566ed66e97ac54a4a5a7ad5e15ae5fdd5fd5ac5d56ae56ad5c572d54ae54ac55a956afd6aed5a4ac562957a9516991691d572fd14e97ae962ed7a9f4a955af572e162f57a956666e17ae1f54a95f566d54a66e16e4afd6a9f7ae1c5c55ae5d56afde916c5e94a6ec56695e14afde1148416e94ad57ac5146ed59d1cc5
Ciphertext = 0d3e98fcaf7a2c4fe9198d66add90d113e5e0ff47598c40a4bf501960d935a4156c9a4d46c9358a608e10a16479a4247c9ab9bb4a02809e3eac3571b832590fe2ca3e2d545741e36282d96c041fc7d39a46ed60214c2c0ec70f27768dfea4f9563b5d5c2ac33b1368a78f2908f5daf942433fec6ab588f09e908e95cc8dfa85d1a0dfd5835dc14e148323230c63eedc99a9ce942214cb3768b97b821d613629f

Cipher = ARIA-128-GCM
Key = e91e5e75da65554a48181f3846349562
# Extra long IV
IV = 000102030405060708090a0b0c0d0e0f1011
AAD = 8008315ebf2e6fe020e8f5eb
Tag = c8b31ab6c2ddccab06b76af4e56e664e
Plaintext = f57af5fd4ae19562976ec57a5a7ad55a5af5c5e5c5fdf5c55ad57a4a7272d57262e9729566ed66e97ac54a4a5a7ad5e15ae5fdd5fd5ac5d56ae56ad5c572d54ae54ac55a956afd6aed5a4ac562957a9516991691d572fd14e97ae962ed7a9f4a955af572e162f57a956666e17ae1f54a95f566d54a66e16e4afd6a9f7ae1c5c55ae5d56afde916c5e94a6ec56695e14afde1148416e94ad57ac5146ed59d1cc5
Ciphertext = 616a7bce24206501082cef7267c09a4affa54f8f82eb7fb2cdebdcaab4b6ab05c37e891c2d0fc90d15c5fb684247625c8bc0befad86896ae1c8f5a8506954caba4e13df0a0eb23853d4474e7f3b2c57bb398456a24d198e14566bce8a5f8d3bcdb12994d2fdc0f5cf19aeff990c1fe119e01f9fcc86757b1d43a9accf7b2f913c2208a46c1967f403867f89b46ffe96864c63f042265806ea5270e0dddd0e8dd


Title = ARIA CCM test vectors from IETF draft-ietf-avtcore-aria-srtp-02

# 16-byte Tag

Cipher = ARIA-128-CCM
Key = 974bee725d44fc3992267b284c3c6750
IV = 000020e8f5eb00000000315e
AAD = 8008315ebf2e6fe020e8f5eb
Tag = 40f04b6467e300f6b336aedf9df4185b
Plaintext = f57af5fd4ae19562976ec57a5a7ad55a5af5c5e5c5fdf5c55ad57a4a7272d57262e9729566ed66e97ac54a4a5a7ad5e15ae5fdd5fd5ac5d56ae56ad5c572d54ae54ac55a956afd6aed5a4ac562957a9516991691d572fd14e97ae962ed7a9f4a955af572e162f57a956666e17ae1f54a95f566d54a66e16e4afd6a9f7ae1c5c55ae5d56afde916c5e94a6ec56695e14afde1148416e94ad57ac5146ed59d1cc5
Ciphertext = 621e408a2e455505b39f704dcbac4307daabbd6d670abc4e42f2fd2fca263f094f4683e6fb0b10c5093d42b69dce0ba546520e7c4400975713f3bde93ef131160b9cbcd6df78a1502be7c6ea8d395b9ed0078819c3105c0ab92cb67b16ba51bb1f53508738bf7a37c9a905439b88b7af9d51a407916fdfea8d43bf253721846dc1671391225fc58d9d0693c8ade6a4ffb034ee6543dd4e651b7a084eae60f855

Cipher = ARIA-256-CCM
Key = 0c5ffd37a11edc42c325287fc0604f2e3e8cd5671a00fe3216aa5eb105783b54
IV = 000020e8f5eb00000000315e
AAD = 8008315ebf2e6fe020e8f5eb
Tag = 87b6bd222c55365a9c7d0b215b77ea41 
Plaintext = f57af5fd4ae19562976ec57a5a7ad55a5af5c5e5c5fdf5c55ad57a4a7272d57262e9729566ed66e97ac54a4a5a7ad5e15ae5fdd5fd5ac5d56ae56ad5c572d54ae54ac55a956afd6aed5a4ac562957a9516991691d572fd14e97ae962ed7a9f4a955af572e162f57a956666e17ae1f54a95f566d54a66e16e4afd6a9f7ae1c5c55ae5d56afde916c5e94a6ec56695e14afde1148416e94ad57ac5146ed59d1cc5
Ciphertext = ff78128ee18ee3cb9fb0d20726a017ff67fbd09d3a4c38aa32f6d306d3fdda378e459b83ed005507449d6cd981a4c1e3ff4193870c276ef09b6317a01a2283206ae4b4be0d0b235422c8abb00122410656b75e1ffc7fb49c0d0c5d6169aa7623610579968037aee8e83fc26264ea866590fd620aa3c0a5f323d953aa7f8defb0d0d60ab5a9de44dbaf8eae74ea3ab5f30594154f405fd630aa4c4d5603efdfa1

# 8-byte Tag

Cipher = ARIA-128-CCM
Key = 974bee725d44fc3992267b284c3c6750
IV = 000020e8f5eb00000000315e
AAD = 8008315ebf2e6fe020e8f5eb
Tag = dd2282c93a67fe4b
Plaintext = f57af5fd4ae19562976ec57a5a7ad55a5af5c5e5c5fdf5c55ad57a4a7272d57262e9729566ed66e97ac54a4a5a7ad5e15ae5fdd5fd5ac5d56ae56ad5c572d54ae54ac55a956afd6aed5a4ac562957a9516991691d572fd14e97ae962ed7a9f4a955af572e162f57a956666e17ae1f54a95f566d54a66e16e4afd6a9f7ae1c5c55ae5d56afde916c5e94a6ec56695e14afde1148416e94ad57ac5146ed59d1cc5
Ciphertext = 621e408a2e455505b39f704dcbac4307daabbd6d670abc4e42f2fd2fca263f094f4683e6fb0b10c5093d42b69dce0ba546520e7c4400975713f3bde93ef131160b9cbcd6df78a1502be7c6ea8d395b9ed0078819c3105c0ab92cb67b16ba51bb1f53508738bf7a37c9a905439b88b7af9d51a407916fdfea8d43bf253721846dc1671391225fc58d9d0693c8ade6a4ffb034ee6543dd4e651b7a084eae60f855

Cipher = ARIA-256-CCM
Key = 0c5ffd37a11edc42c325287fc0604f2e3e8cd5671a00fe3216aa5eb105783b54
IV = 000020e8f5eb00000000315e
AAD = 8008315ebf2e6fe020e8f5eb
Tag = 828dc0088f99a7ef
Plaintext = f57af5fd4ae19562976ec57a5a7ad55a5af5c5e5c5fdf5c55ad57a4a7272d57262e9729566ed66e97ac54a4a5a7ad5e15ae5fdd5fd5ac5d56ae56ad5c572d54ae54ac55a956afd6aed5a4ac562957a9516991691d572fd14e97ae962ed7a9f4a955af572e162f57a956666e17ae1f54a95f566d54a66e16e4afd6a9f7ae1c5c55ae5d56afde916c5e94a6ec56695e14afde1148416e94ad57ac5146ed59d1cc5
Ciphertext = ff78128ee18ee3cb9fb0d20726a017ff67fbd09d3a4c38aa32f6d306d3fdda378e459b83ed005507449d6cd981a4c1e3ff4193870c276ef09b6317a01a2283206ae4b4be0d0b235422c8abb00122410656b75e1ffc7fb49c0d0c5d6169aa7623610579968037aee8e83fc26264ea866590fd620aa3c0a5f323d953aa7f8defb0d0d60ab5a9de44dbaf8eae74ea3ab5f30594154f405fd630aa4c4d5603efdfa1

# 12-byte Tag

Cipher = ARIA-128-CCM
Key = 974bee725d44fc3992267b284c3c6750
IV = 000020e8f5eb00000000315e
AAD = 8008315ebf2e6fe020e8f5eb
Tag = 01f3dedd15238da5ebfb1590
Plaintext = f57af5fd4ae19562976ec57a5a7ad55a5af5c5e5c5fdf5c55ad57a4a7272d57262e9729566ed66e97ac54a4a5a7ad5e15ae5fdd5fd5ac5d56ae56ad5c572d54ae54ac55a956afd6aed5a4ac562957a9516991691d572fd14e97ae962ed7a9f4a955af572e162f57a956666e17ae1f54a95f566d54a66e16e4afd6a9f7ae1c5c55ae5d56afde916c5e94a6ec56695e14afde1148416e94ad57ac5146ed59d1cc5
Ciphertext = 621e408a2e455505b39f704dcbac4307daabbd6d670abc4e42f2fd2fca263f094f4683e6fb0b10c5093d42b69dce0ba546520e7c4400975713f3bde93ef131160b9cbcd6df78a1502be7c6ea8d395b9ed0078819c3105c0ab92cb67b16ba51bb1f53508738bf7a37c9a905439b88b7af9d51a407916fdfea8d43bf253721846dc1671391225fc58d9d0693c8ade6a4ffb034ee6543dd4e651b7a084eae60f855

Cipher = ARIA-256-CCM
Key = 0c5ffd37a11edc42c325287fc0604f2e3e8cd5671a00fe3216aa5eb105783b54
IV = 000020e8f5eb00000000315e
AAD = 8008315ebf2e6fe020e8f5eb
Tag = 3615b7f90a651de15da20fb6
Plaintext = f57af5fd4ae19562976ec57a5a7ad55a5af5c5e5c5fdf5c55ad57a4a7272d57262e9729566ed66e97ac54a4a5a7ad5e15ae5fdd5fd5ac5d56ae56ad5c572d54ae54ac55a956afd6aed5a4ac562957a9516991691d572fd14e97ae962ed7a9f4a955af572e162f57a956666e17ae1f54a95f566d54a66e16e4afd6a9f7ae1c5c55ae5d56afde916c5e94a6ec56695e14afde1148416e94ad57ac5146ed59d1cc5
Ciphertext = ff78128ee18ee3cb9fb0d20726a017ff67fbd09d3a4c38aa32f6d306d3fdda378e459b83ed005507449d6cd981a4c1e3ff4193870c276ef09b6317a01a2283206ae4b4be0d0b235422c8abb00122410656b75e1ffc7fb49c0d0c5d6169aa7623610579968037aee8e83fc26264ea866590fd620aa3c0a5f323d953aa7f8defb0d0d60ab5a9de44dbaf8eae74ea3ab5f30594154f405fd630aa4c4d5603efdfa1

# Test that the tag can be set after specifying AAD.
Cipher = ARIA-256-CCM
Key = 0c5ffd37a11edc42c325287fc0604f2e3e8cd5671a00fe3216aa5eb105783b54
IV = 000020e8f5eb00000000315e
AAD = 8008315ebf2e6fe020e8f5eb
Tag = 3615b7f90a651de15da20fb6
SetTagLate = TRUE
Plaintext = f57af5fd4ae19562976ec57a5a7ad55a5af5c5e5c5fdf5c55ad57a4a7272d57262e9729566ed66e97ac54a4a5a7ad5e15ae5fdd5fd5ac5d56ae56ad5c572d54ae54ac55a956afd6aed5a4ac562957a9516991691d572fd14e97ae962ed7a9f4a955af572e162f57a956666e17ae1f54a95f566d54a66e16e4afd6a9f7ae1c5c55ae5d56afde916c5e94a6ec56695e14afde1148416e94ad57ac5146ed59d1cc5
Ciphertext = ff78128ee18ee3cb9fb0d20726a017ff67fbd09d3a4c38aa32f6d306d3fdda378e459b83ed005507449d6cd981a4c1e3ff4193870c276ef09b6317a01a2283206ae4b4be0d0b235422c8abb00122410656b75e1ffc7fb49c0d0c5d6169aa7623610579968037aee8e83fc26264ea866590fd620aa3c0a5f323d953aa7f8defb0d0d60ab5a9de44dbaf8eae74ea3ab5f30594154f405fd630aa4c4d5603efdfa1
