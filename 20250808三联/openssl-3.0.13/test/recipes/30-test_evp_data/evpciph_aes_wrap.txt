#
# Copyright 2020-2021 The OpenSSL Project Authors. All Rights Reserved.
#
# Licensed under the Apache License 2.0 (the "License").  You may not use
# this file except in compliance with the License.  You can obtain a copy
# in the file LICENSE in the source distribution or at
# https://www.openssl.org/source/license.html

# Cipher names id-aesXXX-wrap are to test aliases.

# AES wrap tests from RFC3394
Cipher = id-aes128-wrap
Key = 000102030405060708090A0B0C0D0E0F
Plaintext = 00112233445566778899AABBCCDDEEFF
Ciphertext = 1FA68B0A8112B447AEF34BD8FB5A7B829D3E862371D2CFE5

Cipher = id-aes192-wrap
Key = 000102030405060708090A0B0C0D0E0F1011121314151617
Plaintext = 00112233445566778899AABBCCDDEEFF
Ciphertext = 96778B25AE6CA435F92B5B97C050AED2468AB8A17AD84E5D

Cipher = id-aes256-wrap
Key = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F
Plaintext = 00112233445566778899AABBCCDDEEFF
Ciphertext = 64E8C3F9CE0F5BA263E9777905818A2A93C8191E7D6E8AE7

Cipher = id-aes192-wrap
Key = 000102030405060708090A0B0C0D0E0F1011121314151617
Plaintext = 00112233445566778899AABBCCDDEEFF0001020304050607
Ciphertext = 031D33264E15D33268F24EC260743EDCE1C6C7DDEE725A936BA814915C6762D2

Cipher = id-aes256-wrap
Key = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F
Plaintext = 00112233445566778899AABBCCDDEEFF0001020304050607
Ciphertext = A8F9BC1612C68B3FF6E6F4FBE30E71E4769C8B80A32CB8958CD5D17D6B254DA1

# Testing strncasecmp
Cipher = aes256-WRAP
Key = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F
Plaintext = 00112233445566778899AABBCCDDEEFF000102030405060708090A0B0C0D0E0F
Ciphertext = 28C9F404C4B810F4CBCCB35CFB87F8263F5786E2D80ED326CBC7F0E71A99F43BFB988B9B7A02DD21

Cipher = ID-aes256-WRAP
Key = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F
Plaintext = 00112233445566778899AABBCCDDEEFF000102030405060708090A0B0C0D0E0F
Ciphertext = 28C9F404C4B810F4CBCCB35CFB87F8263F5786E2D80ED326CBC7F0E71A99F43BFB988B9B7A02DD21


# Same as previous example but with invalid unwrap key: should be rejected
# without returning any plaintext
Cipher = id-aes256-wrap
Operation = DECRYPT
Key = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E00
Plaintext = 00112233445566778899AABBCCDDEEFF000102030405060708090A0B0C0D0E0F
Ciphertext = 28C9F404C4B810F4CBCCB35CFB87F8263F5786E2D80ED326CBC7F0E71A99F43BFB988B9B7A02DD21
Result = CIPHERUPDATE_ERROR

# AES wrap tests from RFC5649
Cipher = id-aes192-wrap-pad
Key = 5840df6e29b02af1ab493b705bf16ea1ae8338f4dcc176a8
Plaintext = c37b7e6492584340bed12207808941155068f738
Ciphertext = 138bdeaa9b8fa7fc61f97742e72248ee5ae6ae5360d1ae6a5f54f373fa543b6a

Cipher = id-aes192-wrap-pad
Key = 5840df6e29b02af1ab493b705bf16ea1ae8338f4dcc176a8
Plaintext = 466f7250617369
Ciphertext = afbeb0f07dfbf5419200f2ccb50bb24f

# AES wrap tests from
# https://csrc.nist.gov/CSRC/media/Projects/
# Cryptographic-Algorithm-Validation-Program/documents/mac/kwtestvectors.zip
# A small subset has been used.

# KW_AD_128_inv
Cipher = AES-128-WRAP-INV
Operation = DECRYPT
Key = 7aa9e9e3c6b2916b4b62ac06074d14e8
Ciphertext = 110f6ba8d4aa2a24f0abfd2cd351ebb6cdfafb35941bbe33
Plaintext = 77a44e843e1f85707cc7e149e5f873be

Cipher = AES-128-WRAP-INV
Operation = DECRYPT
Key = accc4b014123f9e95d3c6f92d07da9fd
Ciphertext = 020cc7c82b6b7cd2ff4d28186930305edce13d65cc36d8d1
Result = CIPHERUPDATE_ERROR

Cipher = AES-128-WRAP-INV
Operation = DECRYPT
Key = 68eceb881f8f34ccb6bebd4e149741ff
Ciphertext = 67292ab62037d076734513943ac907994b6a45b74ed2349f009e2267dc95f13a01b0e2fa109b9b6a
Plaintext = 2910c499dc41de663e7f349b26f00291537943fcc8845166cdd210368d5adf44

Cipher = AES-128-WRAP-INV
Operation = DECRYPT
Key = f8f94a07506f4d1728f6bed6c89a9c5d
Ciphertext = 85fe9d64465db07ec63062aeb4c9161558fabb01d6b9e787850831f9b3414a5cbd35023c63fd59626c64cb3c470d9b7d
Plaintext = c248842911a3dbc5562b1fe0f3955c4da3fd74471062d074d9b972ce3a840f0cb63a768ed1c432b7

# KW_AD_192_inv
Cipher = AES-192-WRAP-INV
Operation = DECRYPT
Key = fd9f4c93416fe7cb53002a5b011a2d4695ec64460af59826
Ciphertext = 5e25074c8d7e82f0224c151db4af874578d55dfa5cc98952
Plaintext = 1097eb6e48232f5c5f15fb5d1c8b5f44

# KW_AD_256_inv
Cipher = AES-256-WRAP-INV
Operation = DECRYPT
Key = d660410f4c4eeb7b0f9dfb7b5dcd4eabac1cfcbb072b1825c96026f6a64dd7dd
Ciphertext = 157629ebd041bf5b9f354bbda3a4906e7ec84cac6d7d7702
Plaintext = 4142bea750ab0f332e9ea5185157af13

# KW_AE_128_inv
Cipher = AES-128-WRAP-INV
Key = e88ba734ea243480a6129366753b58eb
Plaintext = d140ac16a44c1c2b3f47037ea8898a3e
Ciphertext = 600861ee14320006f0ae55c46d5e1ebf3303751df7f038df

# KW_AE_192_inv
Cipher = AES-192-WRAP-INV
Key = 370c715135b44eb3773b1aff833bcd28b59aee866d4a36b3
Plaintext = eae0f60f1cf33d5b75869e84c764a04e
Ciphertext = ea4ba4add8add19950ca491d109ffa08f90312693055677a

# KW_AE_256_inv
Cipher = AES-256-WRAP-INV
Key = de982f7c871f78e37462e2f48a62eecb2da81a10799c6ebf2bee8c786b624b0e
Plaintext = ecafc437d9f1643c7645c2416c14c003
Ciphertext = aec02ddb3f6de1f99103c6042dfc9001eb3cf56d9c2a11f7

# KWP_AD_128_inv
Cipher = AES-128-WRAP-PAD-INV
Operation = DECRYPT
Key = 7877f11e1a2d530a0b27274d4e6d7f2c
Ciphertext = ea53d73d75f5f0642c64d4715d1c131a
Plaintext = 52

Cipher = AES-128-WRAP-PAD-INV
Operation = DECRYPT
Key = 983dc3acf84ca6522b26f818cd0cf64f
Ciphertext = 441da28c430266c29e8413a5938089013c0e8251280ecddc
Plaintext = f469d3232eed4d096f

# KWP_AD_192_inv
Cipher = AES-192-WRAP-PAD-INV
Operation = DECRYPT
Key = 427c6c0a2cc30bbe0cd9fc6b11c29f8cfe64df6ab0379433
Ciphertext = f5dcb63193a377a526db98a852db6099
Plaintext = 98

# KWP_AD_256_inv
Cipher = AES-256-WRAP-PAD-INV
Operation = DECRYPT
Key = 48658f36aa5e24621f86fa6db06487bd635b18ff87704431a1c42cd145115c51
Ciphertext = 6990b3b115563ef6a0884a110a393ee4
Plaintext = 16700199665ff161

# KWP_AE_128_inv
Cipher = AES-128-WRAP-PAD-INV
Key = 1c321a356b0ee25e30de2d618c1facbe
Plaintext = 42
Ciphertext = 3ddf22da3080a1a5252574c76f833790

# KWP_AE_192_inv
Cipher = AES-192-WRAP-PAD-INV
Key = fe3fe235bb36dcf03f01cbf32cc98a3abf10ab3d608d3b30
Plaintext = 1d2b7fc29991bafaf7
Ciphertext = c11afb3c0de263dfb9b672a5f81fe0b9acfe9c407691f332

# KWP_AE_256_inv
Cipher = AES-256-WRAP-PAD-INV
Key = 148a3fa618a6998c30b9f0f67922354a3747f2fa2e4d2e0b7af9582d6f548fee
Plaintext = 441125592acf9e5208dcd558a7ac0034d15530dbad7a2913963da0cbf60aa3
Ciphertext = 23f26a9476829885055694062c89b86399e8d6125509c9e88bb0a5b5113f4bfc8d34a62cba3c9eee
