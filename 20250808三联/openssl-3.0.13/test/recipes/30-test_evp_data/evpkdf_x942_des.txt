# https://www.openssl.org/source/license.html

# Tests start with one of these keywords
#       Cipher Decrypt Derive Digest Encoding KDF MAC PBE
#       PrivPubKeyPair Sign Verify VerifyRecover
# and continue until a blank line. Lines starting with a pound sign are ignored.

Title = X9.42 KDF tests (from RFC2631 test vectors)

Availablein = default
KDF = X942KDF-ASN1
Ctrl.digest = digest:SHA1
Ctrl.hexsecret = hexsecret:000102030405060708090a0b0c0d0e0f10111213
Ctrl.cekalg = cekalg:id-smime-alg-CMS3DESwrap
Output = a09661392376f7044d9052a397883246b67f5f1ef63eb5fb

Title = X9.42 KDF tests (ACVP test vectors)

Availablein = default
KDF = X942KDF-ASN1
Ctrl.digest = digest:SHA256
Ctrl.hexsecret = hexsecret:6B
Ctrl.use-keybits = use-keybits:0
Ctrl.cekalg = cekalg:id-smime-alg-CMS3DESwrap
Ctrl.hexacvp-info = hexacvp-info:a020299D468D60BC6A257E0B6523D691A3FC1602453B35F308C762FBBAC6069A88BCa12080D49BFE5BE01C7D56489AB017663C22B8CBB34C3174D1D71F00CB7505AC759Aa2203C21A5EA5988562C007986E0503D039E7231D9F152FE72A231A1FD98C59BCA6Aa320FD47477542989B51E4A0845DFABD6EEAA465F69B3D75349B2520051782C7F3FC
Output = A7758EC5DA5373C736F1E4CF18A4B6349B23ED86227234185B44638C69EBB222
