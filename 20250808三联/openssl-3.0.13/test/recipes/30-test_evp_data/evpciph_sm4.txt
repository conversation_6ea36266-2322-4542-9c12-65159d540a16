#
# Copyright 2001-2020 The OpenSSL Project Authors. All Rights Reserved.
#
# Licensed under the Apache License 2.0 (the "License").  You may not use
# this file except in compliance with the License.  You can obtain a copy
# in the file LICENSE in the source distribution or at
# https://www.openssl.org/source/license.html

Title = SM4 test vectors from IETF draft-ribose-cfrg-sm4

Cipher = SM4-ECB
Key = 0123456789ABCDEFFEDCBA9876543210
Plaintext  = 0123456789ABCDEFFEDCBA9876543210
Ciphertext = 681EDF34D206965E86B3E94F536E4246

Cipher = SM4-CBC
Key = 0123456789ABCDEFFEDCBA9876543210
IV  = 0123456789ABCDEFFEDCBA9876543210
Plaintext = 0123456789ABCDEFFEDCBA98765432100123456789ABCDEFFEDCBA9876543210
Ciphertext = 2677F46B09C122CC975533105BD4A22AF6125F7275CE552C3A2BBCF533DE8A3B

Cipher = SM4-OFB
Key = 0123456789ABCDEFFEDCBA9876543210
IV  = 0123456789ABCDEFFEDCBA9876543210
Plaintext = 0123456789ABCDEFFEDCBA98765432100123456789ABCDEFFEDCBA9876543210
Ciphertext = 693D9A535BAD5BB1786F53D7253A7056F2075D28B5235F58D50027E4177D2BCE

Cipher = SM4-CFB
Key = 0123456789ABCDEFFEDCBA9876543210
IV  = 0123456789ABCDEFFEDCBA9876543210
Plaintext = 0123456789ABCDEFFEDCBA98765432100123456789ABCDEFFEDCBA9876543210
Ciphertext = 693D9A535BAD5BB1786F53D7253A70569ED258A85A0467CC92AAB393DD978995

Cipher = SM4-CTR
Key = 0123456789ABCDEFFEDCBA9876543210
IV  = 0123456789ABCDEFFEDCBA9876543210
Plaintext = AAAAAAAAAAAAAAAABBBBBBBBBBBBBBBBCCCCCCCCCCCCCCCCDDDDDDDDDDDDDDDDEEEEEEEEEEEEEEEEFFFFFFFFFFFFFFFFEEEEEEEEEEEEEEEEAAAAAAAAAAAAAAAA
Ciphertext = C2B4759E78AC3CF43D0852F4E8D5F9FD7256E8A5FCB65A350EE00630912E44492A0B17E1B85B060D0FBA612D8A95831638B361FD5FFACD942F081485A83CA35D
