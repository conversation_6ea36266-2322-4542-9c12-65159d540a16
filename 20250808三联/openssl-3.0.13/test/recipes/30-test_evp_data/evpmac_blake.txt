#
# Copyright 2001-2021 The OpenSSL Project Authors. All Rights Reserved.
#
# Licensed under the Apache License 2.0 (the "License").  You may not use
# this file except in compliance with the License.  You can obtain a copy
# in the file LICENSE in the source distribution or at
# https://www.openssl.org/source/license.html

# Tests start with one of these keywords
#       Cipher Decrypt Derive Digest Encoding KDF MAC PBE
#       PrivPubKeyPair Sign Verify VerifyRecover
# and continue until a blank line. Lines starting with a pound sign are ignored.

Title = Keyed BLAKE2 tests (Test vectors from reference implementation)

MAC = BLAKE2BMAC
Key = 000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f202122232425262728292a2b2c2d2e2f303132333435363738393a3b3c3d3e3f
Input =
Output = 10ebb67700b1868efb4417987acf4690ae9d972fb7a590c2f02871799aaa4786b5e996e8f0f4eb981fc214b005f42d2ff4233499391653df7aefcbc13fc51568
OutputSize = 64
BlockSize = 128

MAC = BLAKE2BMAC
Key = 000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f202122232425262728292a2b2c2d2e2f303132333435363738393a3b3c3d3e3f
Input = 00
Output = 961f6dd1e4dd30f63901690c512e78e4b45e4742ed197c3c5e45c549fd25f2e4187b0bc9fe30492b16b0d0bc4ef9b0f34c7003fac09a5ef1532e69430234cebd
BlockSize = 128

MAC = BLAKE2BMAC
Key = 000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f202122232425262728292a2b2c2d2e2f303132333435363738393a3b3c3d3e3f
Input = 0001
Output = da2cfbe2d8409a0f38026113884f84b50156371ae304c4430173d08a99d9fb1b983164a3770706d537f49e0c916d9f32b95cc37a95b99d857436f0232c88a965
OutputSize = 64

MAC = BLAKE2BMAC
Key = 000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f202122232425262728292a2b2c2d2e2f303132333435363738393a3b3c3d3e3f
Input = 000102
Output = 33d0825dddf7ada99b0e7e307104ad07ca9cfd9692214f1561356315e784f3e5a17e364ae9dbb14cb2036df932b77f4b292761365fb328de7afdc6d8998f5fc1

MAC = BLAKE2BMAC
Key = 000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f202122232425262728292a2b2c2d2e2f303132333435363738393a3b3c3d3e3f
Input = 00010203
Output = beaa5a3d08f3807143cf621d95cd690514d0b49efff9c91d24b59241ec0eefa5f60196d407048bba8d2146828ebcb0488d8842fd56bb4f6df8e19c4b4daab8ac

MAC = BLAKE2BMAC
Key = 000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f202122232425262728292a2b2c2d2e2f303132333435363738393a3b3c3d3e3f
Input = 0001020304
Output = 098084b51fd13deae5f4320de94a688ee07baea2800486689a8636117b46c1f4c1f6af7f74ae7c857600456a58a3af251dc4723a64cc7c0a5ab6d9cac91c20bb

MAC = BLAKE2BMAC
Key = 000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f202122232425262728292a2b2c2d2e2f303132333435363738393a3b3c3d3e3f
Input = 000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f202122232425262728292a2b2c2d2e2f303132333435363738393a3b3c3d3e3f404142434445464748494a4b4c4d4e4f505152535455565758595a5b5c5d5e5f606162636465666768696a6b6c6d6e6f707172737475767778797a7b7c7d7e7f808182838485868788898a8b8c8d8e8f909192939495969798999a9b9c9d9e9fa0a1a2a3a4a5a6a7a8a9aaabacadaeafb0b1b2b3b4b5b6b7b8b9babbbcbdbebfc0c1c2c3c4c5c6c7c8c9cacbcccdcecfd0d1d2d3d4d5d6d7d8d9dadbdcdddedfe0e1e2e3e4e5e6e7e8e9eaebecedeeeff0f1f2f3f4f5f6f7f8f9
Output = 1085d78307b1c4b008c57a2e7e5b234658a0a82e4ff1e4aaac72b312fda0fe27d233bc5b10e9cc17fdc7697b540c7d95eb215a19a1a0e20e1abfa126efd568c7

MAC = BLAKE2BMAC
Key = 000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f202122232425262728292a2b2c2d2e2f303132333435363738393a3b3c3d3e3f
Input = 000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f202122232425262728292a2b2c2d2e2f303132333435363738393a3b3c3d3e3f404142434445464748494a4b4c4d4e4f505152535455565758595a5b5c5d5e5f606162636465666768696a6b6c6d6e6f707172737475767778797a7b7c7d7e7f808182838485868788898a8b8c8d8e8f909192939495969798999a9b9c9d9e9fa0a1a2a3a4a5a6a7a8a9aaabacadaeafb0b1b2b3b4b5b6b7b8b9babbbcbdbebfc0c1c2c3c4c5c6c7c8c9cacbcccdcecfd0d1d2d3d4d5d6d7d8d9dadbdcdddedfe0e1e2e3e4e5e6e7e8e9eaebecedeeeff0f1f2f3f4f5f6f7f8f9fa
Output = 4e5c734c7dde011d83eac2b7347b373594f92d7091b9ca34cb9c6f39bdf5a8d2f134379e16d822f6522170ccf2ddd55c84b9e6c64fc927ac4cf8dfb2a17701f2

MAC = BLAKE2BMAC
Key = 000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f202122232425262728292a2b2c2d2e2f303132333435363738393a3b3c3d3e3f
Input = 000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f202122232425262728292a2b2c2d2e2f303132333435363738393a3b3c3d3e3f404142434445464748494a4b4c4d4e4f505152535455565758595a5b5c5d5e5f606162636465666768696a6b6c6d6e6f707172737475767778797a7b7c7d7e7f808182838485868788898a8b8c8d8e8f909192939495969798999a9b9c9d9e9fa0a1a2a3a4a5a6a7a8a9aaabacadaeafb0b1b2b3b4b5b6b7b8b9babbbcbdbebfc0c1c2c3c4c5c6c7c8c9cacbcccdcecfd0d1d2d3d4d5d6d7d8d9dadbdcdddedfe0e1e2e3e4e5e6e7e8e9eaebecedeeeff0f1f2f3f4f5f6f7f8f9fafb
Output = 695d83bd990a1117b3d0ce06cc888027d12a054c2677fd82f0d4fbfc93575523e7991a5e35a3752e9b70ce62992e268a877744cdd435f5f130869c9a2074b338

MAC = BLAKE2BMAC
Key = 000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f202122232425262728292a2b2c2d2e2f303132333435363738393a3b3c3d3e3f
Input = 000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f202122232425262728292a2b2c2d2e2f303132333435363738393a3b3c3d3e3f404142434445464748494a4b4c4d4e4f505152535455565758595a5b5c5d5e5f606162636465666768696a6b6c6d6e6f707172737475767778797a7b7c7d7e7f808182838485868788898a8b8c8d8e8f909192939495969798999a9b9c9d9e9fa0a1a2a3a4a5a6a7a8a9aaabacadaeafb0b1b2b3b4b5b6b7b8b9babbbcbdbebfc0c1c2c3c4c5c6c7c8c9cacbcccdcecfd0d1d2d3d4d5d6d7d8d9dadbdcdddedfe0e1e2e3e4e5e6e7e8e9eaebecedeeeff0f1f2f3f4f5f6f7f8f9fafbfc
Output = a6213743568e3b3158b9184301f3690847554c68457cb40fc9a4b8cfd8d4a118c301a07737aeda0f929c68913c5f51c80394f53bff1c3e83b2e40ca97eba9e15

MAC = BLAKE2BMAC
Key = 000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f202122232425262728292a2b2c2d2e2f303132333435363738393a3b3c3d3e3f
Input = 000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f202122232425262728292a2b2c2d2e2f303132333435363738393a3b3c3d3e3f404142434445464748494a4b4c4d4e4f505152535455565758595a5b5c5d5e5f606162636465666768696a6b6c6d6e6f707172737475767778797a7b7c7d7e7f808182838485868788898a8b8c8d8e8f909192939495969798999a9b9c9d9e9fa0a1a2a3a4a5a6a7a8a9aaabacadaeafb0b1b2b3b4b5b6b7b8b9babbbcbdbebfc0c1c2c3c4c5c6c7c8c9cacbcccdcecfd0d1d2d3d4d5d6d7d8d9dadbdcdddedfe0e1e2e3e4e5e6e7e8e9eaebecedeeeff0f1f2f3f4f5f6f7f8f9fafbfcfd
Output = d444bfa2362a96df213d070e33fa841f51334e4e76866b8139e8af3bb3398be2dfaddcbc56b9146de9f68118dc5829e74b0c28d7711907b121f9161cb92b69a9

MAC = BLAKE2BMAC
Key = 000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f202122232425262728292a2b2c2d2e2f303132333435363738393a3b3c3d3e3f
Input = 000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f202122232425262728292a2b2c2d2e2f303132333435363738393a3b3c3d3e3f404142434445464748494a4b4c4d4e4f505152535455565758595a5b5c5d5e5f606162636465666768696a6b6c6d6e6f707172737475767778797a7b7c7d7e7f808182838485868788898a8b8c8d8e8f909192939495969798999a9b9c9d9e9fa0a1a2a3a4a5a6a7a8a9aaabacadaeafb0b1b2b3b4b5b6b7b8b9babbbcbdbebfc0c1c2c3c4c5c6c7c8c9cacbcccdcecfd0d1d2d3d4d5d6d7d8d9dadbdcdddedfe0e1e2e3e4e5e6e7e8e9eaebecedeeeff0f1f2f3f4f5f6f7f8f9fafbfcfdfe
Output = 142709d62e28fcccd0af97fad0f8465b971e82201dc51070faa0372aa43e92484be1c1e73ba10906d5d1853db6a4106e0a7bf9800d373d6dee2d46d62ef2a461

MAC = BLAKE2SMAC
Key = 000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f
Input =
Output = 48a8997da407876b3d79c0d92325ad3b89cbb754d86ab71aee047ad345fd2c49
OutputSize = 32
BlockSize = 64

MAC = BLAKE2SMAC
Key = 000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f
Input = 00
Output = 40d15fee7c328830166ac3f918650f807e7e01e177258cdc0a39b11f598066f1
BlockSize = 64

MAC = BLAKE2SMAC
Key = 000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f
Input = 0001
Output = 6bb71300644cd3991b26ccd4d274acd1adeab8b1d7914546c1198bbe9fc9d803
OutputSize = 32

MAC = BLAKE2SMAC
Key = 000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f
Input = 000102
Output = 1d220dbe2ee134661fdf6d9e74b41704710556f2f6e5a091b227697445dbea6b

MAC = BLAKE2SMAC
Key = 000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f
Input = 00010203
Output = f6c3fbadb4cc687a0064a5be6e791bec63b868ad62fba61b3757ef9ca52e05b2

MAC = BLAKE2SMAC
Key = 000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f
Input = 0001020304
Output = 49c1f21188dfd769aea0e911dd6b41f14dab109d2b85977aa3088b5c707e8598

MAC = BLAKE2SMAC
Key = 000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f
Input = 000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f202122232425262728292a2b2c2d2e2f303132333435363738393a3b3c3d3e3f404142434445464748494a4b4c4d4e4f505152535455565758595a5b5c5d5e5f606162636465666768696a6b6c6d6e6f707172737475767778797a7b7c7d7e7f808182838485868788898a8b8c8d8e8f909192939495969798999a9b9c9d9e9fa0a1a2a3a4a5a6a7a8a9aaabacadaeafb0b1b2b3b4b5b6b7b8b9babbbcbdbebfc0c1c2c3c4c5c6c7c8c9cacbcccdcecfd0d1d2d3d4d5d6d7d8d9dadbdcdddedfe0e1e2e3e4e5e6e7e8e9eaebecedeeeff0f1f2f3f4f5f6f7f8f9
Output = 9fc5450109e1b779f6c7ae79d56c27635c8dd426c5a9d54e2578db989b8c3b4e

MAC = BLAKE2SMAC
Key = 000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f
Input = 000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f202122232425262728292a2b2c2d2e2f303132333435363738393a3b3c3d3e3f404142434445464748494a4b4c4d4e4f505152535455565758595a5b5c5d5e5f606162636465666768696a6b6c6d6e6f707172737475767778797a7b7c7d7e7f808182838485868788898a8b8c8d8e8f909192939495969798999a9b9c9d9e9fa0a1a2a3a4a5a6a7a8a9aaabacadaeafb0b1b2b3b4b5b6b7b8b9babbbcbdbebfc0c1c2c3c4c5c6c7c8c9cacbcccdcecfd0d1d2d3d4d5d6d7d8d9dadbdcdddedfe0e1e2e3e4e5e6e7e8e9eaebecedeeeff0f1f2f3f4f5f6f7f8f9fa
Output = d12bf3732ef4af5c22fa90356af8fc50fcb40f8f2ea5c8594737a3b3d5abdbd7

MAC = BLAKE2SMAC
Key = 000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f
Input = 000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f202122232425262728292a2b2c2d2e2f303132333435363738393a3b3c3d3e3f404142434445464748494a4b4c4d4e4f505152535455565758595a5b5c5d5e5f606162636465666768696a6b6c6d6e6f707172737475767778797a7b7c7d7e7f808182838485868788898a8b8c8d8e8f909192939495969798999a9b9c9d9e9fa0a1a2a3a4a5a6a7a8a9aaabacadaeafb0b1b2b3b4b5b6b7b8b9babbbcbdbebfc0c1c2c3c4c5c6c7c8c9cacbcccdcecfd0d1d2d3d4d5d6d7d8d9dadbdcdddedfe0e1e2e3e4e5e6e7e8e9eaebecedeeeff0f1f2f3f4f5f6f7f8f9fafb
Output = 11030b9289bba5af65260672ab6fee88b87420acef4a1789a2073b7ec2f2a09e

MAC = BLAKE2SMAC
Key = 000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f
Input = 000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f202122232425262728292a2b2c2d2e2f303132333435363738393a3b3c3d3e3f404142434445464748494a4b4c4d4e4f505152535455565758595a5b5c5d5e5f606162636465666768696a6b6c6d6e6f707172737475767778797a7b7c7d7e7f808182838485868788898a8b8c8d8e8f909192939495969798999a9b9c9d9e9fa0a1a2a3a4a5a6a7a8a9aaabacadaeafb0b1b2b3b4b5b6b7b8b9babbbcbdbebfc0c1c2c3c4c5c6c7c8c9cacbcccdcecfd0d1d2d3d4d5d6d7d8d9dadbdcdddedfe0e1e2e3e4e5e6e7e8e9eaebecedeeeff0f1f2f3f4f5f6f7f8f9fafbfc
Output = 69cb192b8444005c8c0ceb12c846860768188cda0aec27a9c8a55cdee2123632

MAC = BLAKE2SMAC
Key = 000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f
Input = 000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f202122232425262728292a2b2c2d2e2f303132333435363738393a3b3c3d3e3f404142434445464748494a4b4c4d4e4f505152535455565758595a5b5c5d5e5f606162636465666768696a6b6c6d6e6f707172737475767778797a7b7c7d7e7f808182838485868788898a8b8c8d8e8f909192939495969798999a9b9c9d9e9fa0a1a2a3a4a5a6a7a8a9aaabacadaeafb0b1b2b3b4b5b6b7b8b9babbbcbdbebfc0c1c2c3c4c5c6c7c8c9cacbcccdcecfd0d1d2d3d4d5d6d7d8d9dadbdcdddedfe0e1e2e3e4e5e6e7e8e9eaebecedeeeff0f1f2f3f4f5f6f7f8f9fafbfcfd
Output = db444c15597b5f1a03d1f9edd16e4a9f43a667cc275175dfa2b704e3bb1a9b83

MAC = BLAKE2SMAC
Key = 000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f
Input = 000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f202122232425262728292a2b2c2d2e2f303132333435363738393a3b3c3d3e3f404142434445464748494a4b4c4d4e4f505152535455565758595a5b5c5d5e5f606162636465666768696a6b6c6d6e6f707172737475767778797a7b7c7d7e7f808182838485868788898a8b8c8d8e8f909192939495969798999a9b9c9d9e9fa0a1a2a3a4a5a6a7a8a9aaabacadaeafb0b1b2b3b4b5b6b7b8b9babbbcbdbebfc0c1c2c3c4c5c6c7c8c9cacbcccdcecfd0d1d2d3d4d5d6d7d8d9dadbdcdddedfe0e1e2e3e4e5e6e7e8e9eaebecedeeeff0f1f2f3f4f5f6f7f8f9fafbfcfdfe
Output = 3fb735061abc519dfe979e54c1ee5bfad0a9d858b3315bad34bde999efd724dd

Title = Custom keyed BLAKE2 tests

MAC = BLAKE2BMAC
Key = 000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f
Custom = "email"
Input = "Sample input for keylen<blocklen and custom"
Output = fb7abee8f585b0d844a3c7db211b8f50a3a5de9bb20a560eb095d05430a60e83f3d44a84c84e8a8ef79eef694551418a05c7495d10aae051e9c63822dfa59e7c

MAC = BLAKE2BMAC
Key = 000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f
Custom = "blockchain"
Input = "Sample input for keylen<blocklen and custom"
Output = b971503ceebaa26c98cb7722abaf019d74138d92ff5d82f91a7473ec641ae6811148bc1edbe9db9030fd9a8a965d7f35492d79f602be81f6e2d9b84abc819c62

MAC = BLAKE2BMAC
Key = 000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f
Salt = a3a5de9b8fcccd0a560eb095a3638bb1
Input = "Sample input for keylen<blocklen and salt"
Output = a57e6f23123be251f4e8cd04d4313e50bfa2ed5a28a3685dc86398da78503d54a169faabcc186037418636af20718f20797fb883b83ab98f5b2d17ce185b615e

MAC = BLAKE2BMAC
Key = 000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f
Salt = b4a5b8cd0a009561
Input = "Sample input for keylen<blocklen and salt"
Output = 709ee3733b179658d326fda6692e6761068d0044895252615bd2a19a14ad4da1ee6ddf613bdf1ae805f97809a140837640c4ce2fece1831b3ccd07544b0d36ab

MAC = BLAKE2BMAC
Key = 000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f
Custom = "application"
Salt = 000102030405060708090a0b0c0d0e0f
Input = "Sample input for keylen<blocklen, salt and custom"
Output = 233a6c732212f4813ec4c9f357e35297e59a652fd24155205f00363f7c54734ee1e8c7329d92116cbec62db35ebb5d51f9e5c2ba41789b84ac9ebc266918e524

MAC = BLAKE2BMAC
Key = 000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f
Ctrl = size:128
Result = MAC_INIT_ERROR

MAC = BLAKE2BMAC
Key = 000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f
Input = "Sample input for outlen<digest_length"
Ctrl = size:1
Output = 2a

MAC = BLAKE2BMAC
Key = 000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f
Input = "Sample input for outlen<digest_length"
Ctrl = size:32
Output = 7fa43c7735fcacad9fce2b44bef37dba6501ab48c9397bedb5562a682e519793

MAC = BLAKE2BMAC
Key = 000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f
Input = "Combo input with outlen, custom and salt"
Custom = "application"
Salt = 000102030405060708090a0b0c0d0e0f
Ctrl = size:32
Output = 51742fc491171eaf6b9459c8b93a44bbf8f44a0b4869a17fa178c8209918ad96

MAC = BLAKE2SMAC
Key = 000102030405060708090a0b0c0d0e0f
Custom = "email"
Input = "Sample input for keylen<blocklen and custom"
Output = 099619607617f8767b278d6d76216b91794ea372c9fd3733a4a36e995a767987

MAC = BLAKE2SMAC
Key = 000102030405060708090a0b0c0d0e0f
Custom = "database"
Input = "Sample input for keylen<blocklen and custom"
Output = afa468ff12d922c1bfdfe26b7d74f1041e7708e83ee7e128ce878f93e2852ad0

MAC = BLAKE2SMAC
Key = 000102030405060708090a0b0c0d0e0f
Salt = a205819e78d6d762
Input = "Sample input for keylen<blocklen and salt"
Output = 29f7d780009ef8f001ec44088622f0752f6c360cda40e3c2181c4f7eacdd9dc6

MAC = BLAKE2SMAC
Key = 000102030405060708090a0b0c0d0e0f
Salt = d6e437b2
Input = "Sample input for keylen<blocklen and salt"
Output = aa9dbb8bbad89ea35467f1ff995521121989eb6439f1263e04e87d63a9c2ef28

MAC = BLAKE2SMAC
Key = 000102030405060708090a0b0c0d0e0f
Custom = "app"
Salt = a205819e78d6d762
Input = "Sample input for keylen<blocklen, salt and custom"
Output = e9f7704dfe5080a4aafe62a806f53ea7f98ffc24175164158f18ec5497b961f5

MAC = BLAKE2SMAC
Key = 000102030405060708090a0b0c0d0e0f
Ctrl = size:64
Result = MAC_INIT_ERROR

MAC = BLAKE2SMAC
Key = 000102030405060708090a0b0c0d0e0f
Ctrl = size:16
Input = "Sample input for outlen<digest_length"
Output = a09fb3d513efc3ed58dd1264de3c59f5

MAC = BLAKE2SMAC
Key = 000102030405060708090a0b0c0d0e0f
Ctrl = size:16
Custom = "app"
Salt = 0001020304050607
Input = "Combo input with outlen, custom and salt"
Output = 6808d8daae537a16bf00e837010969a4
