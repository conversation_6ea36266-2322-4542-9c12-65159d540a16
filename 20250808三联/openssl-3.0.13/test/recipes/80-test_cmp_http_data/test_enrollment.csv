expected,description, -section,val, -cmd,val, -newkey,val,val, -newkeypass,val, -subject,val, -issuer,val, -days,int, -reqexts,val, -sans,spec, -san_nodefault,noarg, -popo,int, -implicit_confirm,noarg, -disable_confirm,noarg, -certout,val,val2, -out_trusted,val,val2, -oldcert,val, -csr,val, -revreason,val
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Misc,request options:,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
1,newkey, -section,, -cmd,ir, -newkey,new.key,, -newkeypass,pass:,,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,, -certout,_RESULT_DIR/test.certout_newkey.pem,, -out_trusted,root.crt,,BLANK,,BLANK,,,
0,newkey missing arg, -section,, -cmd,ir, -newkey,,, -newkeypass,pass:,,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,, -certout,_RESULT_DIR/test.certout_newkey1.pem,, -out_trusted,root.crt,,BLANK,,BLANK,,,
0,newkey is directory, -section,, -cmd,ir, -newkey,dir/,, -newkeypass,pass:,,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,, -certout,_RESULT_DIR/test.certout_newkey2.pem,, -out_trusted,root.crt,,BLANK,,BLANK,,,
0,newkey too many parameters, -section,, -cmd,ir, -newkey,abc,def, -newkeypass,pass:,,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,, -certout,_RESULT_DIR/test.certout_newkey3.pem,, -out_trusted,root.crt,,BLANK,,BLANK,,,
0,newkey is an RSA key, -section,, -cmd,ir, -newkey,test.RSA2048.pem,, -newkeypass,pass:,,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,, -certout,_RESULT_DIR/test.certout_newkey4.pem,, -out_trusted,root.crt,,BLANK,,BLANK,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
1,newkeypass, -section,, -cmd,ir, -newkey,new_pass_12345.key,, -newkeypass,pass:12345,,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,, -certout,_RESULT_DIR/test.certout_newkeypass.pem,, -out_trusted,root.crt,,BLANK,,BLANK,,,
1,read newkeypass from file, -section,, -cmd,ir, -newkey,new_pass_12345.key,, -newkeypass,file:12345.txt,,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,, -certout,_RESULT_DIR/test.certout_newkeypass_file.pem,, -out_trusted,root.crt,,BLANK,,BLANK,,,
1,newkeypass no prefix, -section,, -cmd,ir, -newkey,new_pass_12345.key,, -newkeypass,12345,,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,, -certout,_RESULT_DIR/test.certout_newkeypass_no_prefix.pem,, -out_trusted,root.crt,,BLANK,,BLANK,,,
0,no newkeypass, -section,, -cmd,ir, -newkey,new_pass_12345.key,,BLANK,,,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,, -certout,_RESULT_DIR/test.certout_newkeypass1.pem,, -out_trusted,root.crt,,BLANK,,BLANK,,,
0,missing newkeypass parameter, -section,, -cmd,ir, -newkey,new_pass_12345.key,, -newkeypass,,,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,, -certout,_RESULT_DIR/test.certout_newkeypass2.pem,, -out_trusted,root.crt,,BLANK,,BLANK,,,
0,colon missing and no passwd, -section,, -cmd,ir, -newkey,new_pass_12345.key,, -newkeypass,pass,,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,, -certout,_RESULT_DIR/test.certout_newkeypass3.pem,, -out_trusted,root.crt,,BLANK,,BLANK,,,
0,newkeypass double colon, -section,, -cmd,ir, -newkey,new_pass_12345.key,, -newkeypass,pass::12345,,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,, -certout,_RESULT_DIR/test.certout_newkeypass4.pem,, -out_trusted,root.crt,,BLANK,,BLANK,,,
0,newkeypass double passwd, -section,, -cmd,ir, -newkey,new_pass_12345.key,, -newkeypass,pass:12345:12345,,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,, -certout,_RESULT_DIR/test.certout_newkeypass5.pem,, -out_trusted,root.crt,,BLANK,,BLANK,,,
0,newkeypass wrongfile, -section,, -cmd,ir, -newkey,new_pass_12345.key,, -newkeypass,file:random.bin,,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,, -certout,_RESULT_DIR/test.certout_newkeypass6.pem,, -out_trusted,root.crt,,BLANK,,BLANK,,,
0,wrong password for encrypted pem, -section,, -cmd,ir, -newkey,cmp --help ,, -newkeypass,pass:wrong,,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,, -certout,_RESULT_DIR/test.certout_newkeypass7.pem,, -out_trusted,root.crt,,BLANK,,BLANK,,,
1,newkeypass ignored, -section,, -cmd,ir, -newkey,new.key,, -newkeypass,abcdefghijklmnop,,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,, -certout,_RESULT_DIR/test.certout_newkeypass_ignored.pem,, -out_trusted,root.crt,,BLANK,,BLANK,,,
0,newkeypass invalid, -section,, -cmd,ir, -newkey,new_pass_12345.key,, -newkeypass,fp:4,,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,, -certout,_RESULT_DIR/test.certout_newkeypass8.pem,, -out_trusted,root.crt,,BLANK,,BLANK,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
0,subject argument missing, -section,, -cmd,ir, -newkey,new.key,, -newkeypass,pass:, -subject,BLANK,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,, -certout,_RESULT_DIR/test.certout_subject1.pem,, -out_trusted,root.crt,,BLANK,,BLANK,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
1,issuer, -section,, -cmd,ir, -newkey,new.key,, -newkeypass,pass:,,, -issuer,_CA_DN,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,, -certout,_RESULT_DIR/test.certout_issuer.pem,, -out_trusted,root.crt,,BLANK,,BLANK,,,
0,issuer missing arg, -section,, -cmd,ir, -newkey,new.key,, -newkeypass,pass:,,, -issuer,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,, -certout,_RESULT_DIR/test.certout_issuer1.pem,, -out_trusted,root.crt,,BLANK,,BLANK,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
1,days 1, -section,, -cmd,ir, -newkey,new.key,, -newkeypass,pass:,,,BLANK,, -days,1,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,, -certout,_RESULT_DIR/test.certout_days.pem,, -out_trusted,root.crt,,BLANK,,BLANK,,,
1,days 0, -section,, -cmd,ir, -newkey,new.key,, -newkeypass,pass:,,,BLANK,, -days,0,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,, -certout,_RESULT_DIR/test.certout_days_zero.pem,, -out_trusted,root.crt,,BLANK,,BLANK,,,
1,days 365*100 beyond 2038, -section,, -cmd,ir, -newkey,new.key,, -newkeypass,pass:,,,BLANK,, -days,36500,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,, -certout,_RESULT_DIR/test.certout_days_far_future.pem,, -out_trusted,root.crt,,BLANK,,BLANK,,,
0,days missing arg, -section,, -cmd,ir, -newkey,new.key,, -newkeypass,pass:,,,BLANK,, -days,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,, -certout,_RESULT_DIR/test.certout_days1.pem,, -out_trusted,root.crt,,BLANK,,BLANK,,,
0,days negative, -section,, -cmd,ir, -newkey,new.key,, -newkeypass,pass:,,,BLANK,, -days,-10,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,, -certout,_RESULT_DIR/test.certout_days2.pem,, -out_trusted,root.crt,,BLANK,,BLANK,,,
0,days no not integer, -section,, -cmd,ir, -newkey,new.key,, -newkeypass,pass:,,,BLANK,, -days,1.5,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,, -certout,_RESULT_DIR/test.certout_days3.pem,, -out_trusted,root.crt,,BLANK,,BLANK,,,
0,days out of range, -section,, -cmd,ir, -newkey,new.key,, -newkeypass,pass:,,,BLANK,, -days,0x10000000000000000,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,, -certout,_RESULT_DIR/test.certout_days4.pem,, -out_trusted,root.crt,,BLANK,,BLANK,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
1,reqexts, -section,, -cmd,ir, -newkey,new.key,, -newkeypass,pass:,,,BLANK,,,, -reqexts,reqexts,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,, -certout,_RESULT_DIR/test.certout_reqexts.pem,, -out_trusted,root.crt,,BLANK,,BLANK,,,
0,reqexts missing arg, -section,, -cmd,ir, -newkey,new.key,, -newkeypass,pass:,,,BLANK,,BLANK,, -reqexts,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,, -certout,_RESULT_DIR/test.certout_reqexts1.pem,, -out_trusted,root.crt,,BLANK,,BLANK,,,
0,reqexts non-exisitng section, -section,, -cmd,ir, -newkey,new.key,, -newkeypass,pass:,,,BLANK,,BLANK,, -reqexts,invalid,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,, -certout,_RESULT_DIR/test.certout_reqexts2.pem,, -out_trusted,root.crt,,BLANK,,BLANK,,,
0,reqexts malformed section, -section,, -cmd,ir, -newkey,new.key,, -newkeypass,pass:,,,BLANK,,BLANK,, -reqexts,reqexts_invalidkey,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,, -certout,_RESULT_DIR/test.certout_reqexts3.pem,, -out_trusted,root.crt,,BLANK,,BLANK,,,
0,reqexts and sans, -section,, -cmd,ir, -newkey,new.key,, -newkeypass,pass:,,,BLANK,,,, -reqexts,reqexts, -sans,localhost,BLANK,,BLANK,,BLANK,,BLANK,, -certout,_RESULT_DIR/test.certout_reqexts4.pem,, -out_trusted,root.crt,,BLANK,,BLANK,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
1,sans 1 dns, -section,, -cmd,ir, -newkey,new.key,, -newkeypass,pass:,,,BLANK,,,,BLANK,, -sans,localhost,BLANK,,BLANK,,BLANK,,BLANK,, -certout,_RESULT_DIR/test.certout_sans_dns.pem,, -out_trusted,root.crt,,BLANK,,BLANK,,,
1,sans 1 dns critical, -section,, -cmd,ir, -newkey,new.key,, -newkeypass,pass:,,,BLANK,,,,BLANK,, -sans,localhost critical,BLANK,,BLANK,,BLANK,,BLANK,, -certout,_RESULT_DIR/test.certout_sans_dns_critical.pem,, -out_trusted,root.crt,,BLANK,,BLANK,,,
1,sans critical, -section,, -cmd,ir, -newkey,new.key,, -newkeypass,pass:,,,BLANK,,,,BLANK,, -sans,critical,BLANK,,BLANK,,BLANK,,BLANK,, -certout,_RESULT_DIR/test.certout_sans_critical.pem,, -out_trusted,root.crt,,BLANK,,BLANK,,,
1,sans 2 dns, -section,, -cmd,ir, -newkey,new.key,, -newkeypass,pass:,,,BLANK,,,,BLANK,, -sans,localhost test,BLANK,,BLANK,,BLANK,,BLANK,, -certout,_RESULT_DIR/test.certout_sans_two_dns.pem,, -out_trusted,root.crt,,BLANK,,BLANK,,,
1,sans 1 dns 1 ip, -section,, -cmd,ir, -newkey,new.key,, -newkeypass,pass:,,,BLANK,,,,BLANK,, -sans,localhost 127.0.0.1,BLANK,,BLANK,,BLANK,,BLANK,, -certout,_RESULT_DIR/test.certout_sans_dns_ip.pem,, -out_trusted,root.crt,,BLANK,,BLANK,,,
1,sans 2 ip, -section,, -cmd,ir, -newkey,new.key,, -newkeypass,pass:,,,BLANK,,,,BLANK,, -sans,127.0.0.1 *******,BLANK,,BLANK,,BLANK,,BLANK,, -certout,_RESULT_DIR/test.certout_sans_two_ip.pem,, -out_trusted,root.crt,,BLANK,,BLANK,,,
1,sans 1 uri, -section,, -cmd,ir, -newkey,new.key,, -newkeypass,pass:,,,BLANK,,,,BLANK,, -sans,https://www.sample.com,BLANK,,BLANK,,BLANK,,BLANK,, -certout,_RESULT_DIR/test.certout_sans_uri.pem,, -out_trusted,root.crt,,BLANK,,BLANK,,,
1,san_nodefault, -section,, -cmd,ir, -newkey,new.key,, -newkeypass,pass:,,,BLANK,,,,BLANK,, -sans,127.0.0.1 *******, -san_nodefault,,BLANK,,BLANK,,BLANK,, -certout,_RESULT_DIR/test.certout_sans_nodefault.pem,, -out_trusted,root.crt,,BLANK,,BLANK,,,
1,san default via oldcert, -section,, -cmd,ir, -newkey,new.key,, -newkeypass,pass:,,,BLANK,,,,BLANK,, -sans,127.0.0.1 *******,BLANK,,BLANK,,BLANK,,BLANK,, -certout,_RESULT_DIR/test.certout_sans_default.pem,, -out_trusted,root.crt,, -oldcert,_RESULT_DIR/test.certout_newkey.pem,BLANK,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
1,popo SIGNATURE, -section,, -cmd,ir, -newkey,new.key,, -newkeypass,pass:,,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,, -popo,1,BLANK,,BLANK,, -certout,_RESULT_DIR/test.certout_popo.pem,, -out_trusted,root.crt,,BLANK,,BLANK,,,
0,popo RAVERIFIED, -section,, -cmd,ir, -newkey,new.key,, -newkeypass,pass:,,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,, -popo,0,BLANK,,BLANK,, -certout,_RESULT_DIR/test.certout_popo1.pem,, -out_trusted,root.crt,,BLANK,,BLANK,,,
0,popo missing arg, -section,, -cmd,ir, -newkey,new.key,, -newkeypass,pass:,,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,, -popo,,BLANK,,BLANK,, -certout,_RESULT_DIR/test.certout_popo2.pem,, -out_trusted,root.crt,,BLANK,,BLANK,,,
0,popo too large, -section,, -cmd,ir, -newkey,new.key,, -newkeypass,pass:,,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,, -popo,3,BLANK,,BLANK,, -certout,_RESULT_DIR/test.certout_popo3.pem,, -out_trusted,root.crt,,BLANK,,BLANK,,,
0,popo too small, -section,, -cmd,ir, -newkey,new.key,, -newkeypass,pass:,,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,, -popo,-3,BLANK,,BLANK,, -certout,_RESULT_DIR/test.certout_popo4.pem,, -out_trusted,root.crt,,BLANK,,BLANK,,,
0,popo NONE, -section,, -cmd,ir, -newkey,new.key,, -newkeypass,pass:,,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,, -popo,-1,BLANK,,BLANK,, -certout,_RESULT_DIR/test.certout_popo5.pem,, -out_trusted,root.crt,,BLANK,,BLANK,,,
0,popo KEYENC not supported, -section,, -cmd,ir, -newkey,new.key,, -newkeypass,pass:,,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,, -popo,2,BLANK,,BLANK,, -certout,_RESULT_DIR/test.certout_popo6.pem,, -out_trusted,root.crt,,BLANK,,BLANK,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
1,implicit_confirm, -section,, -cmd,ir, -newkey,new.key,, -newkeypass,pass:,,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,, -implicit_confirm,,BLANK,, -certout,_RESULT_DIR/test.certout_implicit.pem,, -out_trusted,root.crt,,BLANK,,BLANK,,,
0,implicit_confirm with parameter, -section,, -cmd,ir, -newkey,new.key,, -newkeypass,pass:,,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,, -implicit_confirm,abc,BLANK,, -certout,_RESULT_DIR/test.certout_implicit1.pem,, -out_trusted,root.crt,,BLANK,,BLANK,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
1,disable_confirm, -section,, -cmd,ir, -newkey,new.key,, -newkeypass,pass:,,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,, -disable_confirm,, -certout,_RESULT_DIR/test.certout_disable.pem,, -out_trusted,root.crt,,BLANK,,BLANK,,,
0,disable_confirm with parameter, -section,, -cmd,ir, -newkey,new.key,, -newkeypass,pass:,,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,, -disable_confirm,abc, -certout,_RESULT_DIR/test.certout_disable1.pem,, -out_trusted,root.crt,,BLANK,,BLANK,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
0,no certout, -section,, -cmd,ir, -newkey,new.key,, -newkeypass,pass:,,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,, -certout,"""",, -out_trusted,root.crt,,BLANK,,BLANK,,,
0,certout missing arg, -section,, -cmd,ir, -newkey,new.key,, -newkeypass,pass:,,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,, -certout,,, -out_trusted,root.crt,,BLANK,,BLANK,,,
0,certout too many parameters, -section,, -cmd,ir, -newkey,new.key,, -newkeypass,pass:,,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,, -certout,abc,def, -out_trusted,root.crt,,BLANK,,BLANK,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
1,no out_trusted, -section,, -cmd,ir, -newkey,new.key,, -newkeypass,pass:,,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,, -certout,_RESULT_DIR/test.certout_out_trusted.pem,,BLANK,,,BLANK,,BLANK,,,
1,out_trusted bigcert, -section,, -cmd,ir, -newkey,new.key,, -newkeypass,pass:,,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,, -certout,_RESULT_DIR/test.certout_out_trusted_big.pem,, -out_trusted,big_root.crt,,BLANK,,BLANK,,,
0,out_trusted missing arg, -section,, -cmd,ir, -newkey,new.key,, -newkeypass,pass:,,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,, -certout,_RESULT_DIR/test.certout_out_trusted1.pem,, -out_trusted,,,BLANK,,BLANK,,,
0,out_trusted is directory, -section,, -cmd,ir, -newkey,new.key,, -newkeypass,pass:,,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,, -certout,_RESULT_DIR/test.certout_out_trusted2.pem,, -out_trusted,dir/,,BLANK,,BLANK,,,
0,out_trusted too many parameters, -section,, -cmd,ir, -newkey,new.key,, -newkeypass,pass:,,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,, -certout,_RESULT_DIR/test.certout_out_trusted3.pem,, -out_trusted,abc,def,BLANK,,BLANK,,,
0,out_trusted empty certificate file, -section,, -cmd,ir, -newkey,new.key,, -newkeypass,pass:,,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,, -certout,_RESULT_DIR/test.certout_out_trusted4.pem,, -out_trusted,empty.txt,,BLANK,,BLANK,,,
1,out_trusted accept issuing ca cert even with CRL check enabled by default, -section,, -cmd,ir, -newkey,new.key,, -newkeypass,pass:,,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,, -certout,_RESULT_DIR/test.certout_out_trusted5.pem,, -out_trusted,issuing.crt,,BLANK,,BLANK,,,-partial_chain,-crl_check,-srvcert,server.crt
0,out_trusted expired issuing ca cert, -section,, -cmd,ir, -newkey,new.key,, -newkeypass,pass:,,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,, -certout,_RESULT_DIR/test.certout_out_trusted5.pem,, -out_trusted,issuing_expired.crt,,BLANK,,BLANK,,,-partial_chain
0,out_trusted expired root ca cert, -section,, -cmd,ir, -newkey,new.key,, -newkeypass,pass:,,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,, -certout,_RESULT_DIR/test.certout_out_trusted5.pem,, -out_trusted,root_expired.crt,,BLANK,,BLANK,,,
0,out_trusted wrong ca, -section,, -cmd,ir, -newkey,new.key,, -newkeypass,pass:,,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,, -certout,_RESULT_DIR/test.certout_out_trusted6.pem,, -out_trusted,signer.crt,,BLANK,,BLANK,,,
0,out_trusted random input, -section,, -cmd,ir, -newkey,new.key,, -newkeypass,pass:,,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,, -certout,_RESULT_DIR/test.certout_out_trusted7.pem,, -out_trusted,random.bin,,BLANK,,BLANK,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
1,oldcert ignored, -section,, -cmd,ir, -newkey,new.key,, -newkeypass,pass:,,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,, -certout,_RESULT_DIR/test.certout_oldcert.pem,, -out_trusted,root.crt,, -oldcert,_RESULT_DIR/test.certout_newkey.pem,BLANK,,,
0,oldcert missing arg, -section,, -cmd,ir, -newkey,new.key,, -newkeypass,pass:,,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,, -certout,_RESULT_DIR/test.certout_oldcert1.pem,, -out_trusted,root.crt,, -oldcert,,BLANK,,,
0,oldcert non existing file, -section,, -cmd,ir, -newkey,new.key,, -newkeypass,pass:,,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,, -certout,_RESULT_DIR/test.certout_oldcert3.pem,, -out_trusted,root.crt,, -oldcert,idontexist,BLANK,,,
0,oldcert empty file, -section,, -cmd,ir, -newkey,new.key,, -newkeypass,pass:,,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,, -certout,_RESULT_DIR/test.certout_oldcert4.pem,, -out_trusted,root.crt,, -oldcert,empty.txt,BLANK,,,
0,oldcert random contents, -section,, -cmd,ir, -newkey,new.key,, -newkeypass,pass:,,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,, -certout,_RESULT_DIR/test.certout_oldcert5.pem,, -out_trusted,root.crt,, -oldcert,random.bin,BLANK,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
1,pkcs10 converted to ir with privkey, -section,, -cmd,ir, -newkey,new.key,, -newkeypass,pass:,,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,, -certout,_RESULT_DIR/test.certout_csr1.pem,, -out_trusted,root.crt,,BLANK,, -csr,csr.pem,,
0,pkcs10 converted to ir with pubkey with popo SIGNATURE, -section,, -cmd,ir, -newkey,new_pub.key,, BLANK,,,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,, -certout,_RESULT_DIR/test.certout_csr2.pem,, -out_trusted,root.crt,,BLANK,, -csr,csr.pem,,
0,pkcs10 converted to ir with pubkey with popo RAVERIFIED, -section,, -cmd,ir, -newkey,new_pub.key,, BLANK,,,,BLANK,,BLANK,,BLANK,,BLANK,, -popo,0,BLANK,,BLANK,,BLANK,, -certout,_RESULT_DIR/test.certout_csr3.pem,, -out_trusted,root.crt,,BLANK,, -csr,csr.pem,,
0,pkcs10 converted to ir without -newkey, -section,, -cmd,ir, -newkey,"""",, BLANK,,,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,, -certout,_RESULT_DIR/test.certout_csr4.pem,, -out_trusted,root.crt,,BLANK,, -csr,csr.pem,,
1,p10cr csr present, -section,, -cmd,p10cr,BLANK,,, BLANK,,,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,, -certout,_RESULT_DIR/test.certout_p10cr.pem,, -out_trusted,root.crt,,BLANK,, -csr,csr.pem,,
0,p10cr csr missing, -section,, -cmd,p10cr, -newkey,new.key,, -newkeypass,pass:,,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,, -certout,_RESULT_DIR/test.certout_p10cr1.pem,, -out_trusted,root.crt,,BLANK,,BLANK,,,
0,p10cr csr missing arg, -section,, -cmd,p10cr, -newkey,new.key,, -newkeypass,pass:,,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,, -certout,_RESULT_DIR/test.certout_p10cr2.pem,, -out_trusted,root.crt,,BLANK,, -csr,,,
0,p10cr csr non-existing file, -section,, -cmd,p10cr, -newkey,new.key,, -newkeypass,pass:,,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,, -certout,_RESULT_DIR/test.certout_p10cr3.pem,, -out_trusted,root.crt,,BLANK,, -csr,idontexist,,
0,p10cr csr empty file, -section,, -cmd,p10cr, -newkey,new.key,, -newkeypass,pass:,,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,, -certout,_RESULT_DIR/test.certout_p10cr4.pem,, -out_trusted,root.crt,,BLANK,, -csr,empty.txt,,
TODO,p10cr wrong csr, -section,, -cmd,p10cr, -newkey,new.key,, -newkeypass,pass:,,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,, -certout,_RESULT_DIR/test.certout_p10cr5.pem,, -out_trusted,root.crt,,BLANK,, -csr,wrong_csr.pem,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
1,ir + ignored revocation, -section,, -cmd,ir, -newkey,new.key,, -newkeypass,pass:,,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,, -certout,_RESULT_DIR/test.certout_revreason.pem,, -out_trusted,root.crt,,BLANK,,,, -revreason,5
0,ir + invalid revreason, -section,, -cmd,ir, -newkey,new.key,, -newkeypass,pass:,,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,, -certout,_RESULT_DIR/test.certout_revreason1.pem,, -out_trusted,root.crt,,BLANK,,,, -revreason,11
0,ir + revreason not an integer, -section,, -cmd,ir, -newkey,new.key,, -newkeypass,pass:,,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,, -certout,_RESULT_DIR/test.certout_revreason2.pem,, -out_trusted,root.crt,,BLANK,,,, -revreason,abc
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
1,cr, -section,, -cmd,cr, -newkey,new.key,, -newkeypass,pass:,,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,, -certout,_RESULT_DIR/test.certout_cr.pem,, -out_trusted,root.crt,,BLANK,,BLANK,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
1,kur explicit options, -section,, -cmd,kur, -newkey,new.key,, -newkeypass,pass:,,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,, -certout,_RESULT_DIR/test.certout_kur.pem,, -out_trusted,root.crt,, -oldcert,_RESULT_DIR/test.certout_newkey.pem,BLANK,,,,,-server,_SERVER_HOST:_KUR_PORT, -cert,_RESULT_DIR/test.certout_newkey.pem, -key,new.key, -extracerts,issuing.crt
1,kur minimal options, -section,, -cmd,kur,BLANK,,BLANK,, -subject,"""",BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,, -certout,_RESULT_DIR/test.certout_kur_minimal.pem,,BLANK,,, -oldcert,"""",BLANK,,,,,-server,_SERVER_HOST:_KUR_PORT, -cert,_RESULT_DIR/test.certout_newkey.pem, -key,new.key, -extracerts,issuing.crt, -secret,""""
0,kur newkey value missing, -section,, -cmd,kur, -newkey,,, -newkeypass,pass:,,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,, -certout,_RESULT_DIR/test.certout_kur1.pem,, -out_trusted,root.crt,, -oldcert,_RESULT_DIR/test.certout_newkey.pem,BLANK,,,,,-server,_SERVER_HOST:_KUR_PORT
0,kur newkey is directory, -section,, -cmd,kur, -newkey,dir/,, -newkeypass,pass:,,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,, -certout,_RESULT_DIR/test.certout_kur2.pem,, -out_trusted,root.crt,, -oldcert,_RESULT_DIR/test.certout_newkey.pem,BLANK,,,,,-server,_SERVER_HOST:_KUR_PORT
0,kur newkey parameter count no match, -section,, -cmd,kur, -newkey,abc,def, -newkeypass,pass:,,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,, -certout,_RESULT_DIR/test.certout_kur3.pem,, -out_trusted,root.crt,, -oldcert,_RESULT_DIR/test.certout_newkey.pem,BLANK,,,,,-server,_SERVER_HOST:_KUR_PORT
0,kur newkey missing argument, -section,, -cmd,kur, -newkey,BLANK,, -newkeypass,pass:,,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,, -certout,_RESULT_DIR/test.certout_kur4.pem,, -out_trusted,root.crt,, -oldcert,_RESULT_DIR/test.certout_newkey.pem,BLANK,,,,,-server,_SERVER_HOST:_KUR_PORT
0,kur oldcert not existing, -section,, -cmd,kur, -newkey,new.key,, -newkeypass,pass:,,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,, -certout,_RESULT_DIR/test.certout_kur6.pem,, -out_trusted,root.crt,, -oldcert,idontexist,BLANK,,,,,-server,_SERVER_HOST:_KUR_PORT
0,kur wrong oldcert, -section,, -cmd,kur, -newkey,new.key,, -newkeypass,pass:,,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,, -certout,_RESULT_DIR/test.certout_kur6.pem,, -out_trusted,root.crt,, -oldcert,root.crt,BLANK,,,,,-server,_SERVER_HOST:_KUR_PORT
0,kur empty oldcert file, -section,, -cmd,kur, -newkey,new.key,, -newkeypass,pass:,,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,, -certout,_RESULT_DIR/test.certout_kur7.pem,, -out_trusted,root.crt,, -oldcert,empty.txt,BLANK,,,,,-server,_SERVER_HOST:_KUR_PORT
0,kur without cert and oldcert, -section,, -cmd,kur, -newkey,new.key,, -newkeypass,pass:,,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,, -certout,_RESULT_DIR/test.certout_kur8.pem,, -out_trusted,root.crt,, -cert,"""",BLANK,,,,,-server,_SERVER_HOST:_KUR_PORT
