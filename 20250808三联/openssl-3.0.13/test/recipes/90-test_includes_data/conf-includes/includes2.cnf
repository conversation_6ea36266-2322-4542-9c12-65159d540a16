# For the CA policy
[ policy_match ]
countryName		= match
stateOrProvinceName	= match
organizationName	= match
organizationalUnitName	= optional
commonName		= supplied
emailAddress		= optional

# For the 'anything' policy
# At this point in time, you must list all acceptable 'object'
# types.
[ policy_anything ]
countryName		= optional
stateOrProvinceName	= optional
localityName		= optional
organizationName	= optional
organizationalUnitName	= optional
commonName		= supplied
emailAddress		= optional

####################################################################
[ req ]
default_bits		= 2048
default_keyfile 	= testkey.pem
distinguished_name	= req_distinguished_name
encrypt_rsa_key		= no

[ req_distinguished_name ]
countryName			= Country Name (2 letter code)
countryName_default		= AU
countryName_value		= AU

stateOrProvinceName		= State or Province Name (full name)
stateOrProvinceName_default	= Queensland
stateOrProvinceName_value	=

localityName			= Locality Name (eg, city)
localityName_value		= Brisbane

organizationName		= Organization Name (eg, company)
organizationName_default	= 
organizationName_value		= CryptSoft Pty Ltd

organizationalUnitName		= Organizational Unit Name (eg, section)
organizationalUnitName_default	=
organizationalUnitName_value	= .

commonName			= Common Name (eg, YOUR name)
commonName_value		= Eric Young

emailAddress			= Email Address
emailAddress_value		= <EMAIL>
