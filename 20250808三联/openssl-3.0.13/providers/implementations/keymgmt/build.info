# We make separate GOAL variables for each algorithm, to make it easy to
# switch each to the Legacy provider when needed.

$DH_GOAL=../../libdefault.a ../../libfips.a
$DSA_GOAL=../../libdefault.a ../../libfips.a
$EC_GOAL=../../libdefault.a ../../libfips.a
$ECX_GOAL=../../libdefault.a ../../libfips.a
$KDF_GOAL=../../libdefault.a ../../libfips.a
$MAC_GOAL=../../libdefault.a ../../libfips.a
$RSA_GOAL=../../libdefault.a ../../libfips.a

IF[{- !$disabled{dh} -}]
  SOURCE[$DH_GOAL]=dh_kmgmt.c
ENDIF
IF[{- !$disabled{dsa} -}]
  SOURCE[$DSA_GOAL]=dsa_kmgmt.c
ENDIF
IF[{- !$disabled{ec} -}]
  SOURCE[$EC_GOAL]=ec_kmgmt.c
ENDIF

IF[{- !$disabled{asm} -}]
  $ECDEF_s390x=S390X_EC_ASM

  # Now that we have defined all the arch specific variables, use the
  # appropriate one, and define the appropriate macros
  IF[$ECASM_{- $target{asm_arch} -}]
    $ECDEF=$ECDEF_{- $target{asm_arch} -}
  ENDIF
ENDIF

IF[{- !$disabled{ec} -}]
  SOURCE[$ECX_GOAL]=ecx_kmgmt.c
  DEFINE[$ECX_GOAL]=$ECDEF
ENDIF

SOURCE[$RSA_GOAL]=rsa_kmgmt.c

SOURCE[$KDF_GOAL]=kdf_legacy_kmgmt.c

SOURCE[$MAC_GOAL]=mac_legacy_kmgmt.c
