/*
 * {- join("\n * ", @autowarntext) -}
 *
 * Copyright 2020-2021 The OpenSSL Project Authors. All Rights Reserved.
 *
 * Licensed under the Apache License 2.0 (the "License").  You may not use
 * this file except in compliance with the License.  You can obtain a copy
 * in the file LICENSE in the source distribution or at
 * https://www.openssl.org/source/license.html
 */

#include "crypto/ec.h"
#include "internal/der.h"

/* Well known OIDs precompiled */
{-
    $OUT = oids_to_c::process_leaves('providers/common/der/EC.asn1',
                                     { dir => $config{sourcedir},
                                       filter => \&oids_to_c::filter_to_H });
-}

/* Subject Public Key Info */
int ossl_DER_w_algorithmIdentifier_EC(WPACKET *pkt, int cont, EC_KEY *ec);
/* Signature */
int ossl_DER_w_algorithmIdentifier_ECDSA_with_MD(WPACKET *pkt, int cont,
                                                 EC_KEY *ec, int mdnid);
