providers/common/libdefault-lib-capabilities.o: \
 providers/common/capabilities.c include/openssl/core_dispatch.h \
 include/openssl/core.h include/openssl/types.h include/openssl/e_os2.h \
 include/openssl/macros.h include/openssl/opensslconf.h \
 include/openssl/configuration.h include/openssl/opensslv.h \
 include/openssl/safestack.h include/openssl/stack.h \
 include/openssl/core_names.h include/openssl/prov_ssl.h \
 include/openssl/params.h include/openssl/bn.h include/openssl/crypto.h \
 include/openssl/cryptoerr.h include/openssl/symhacks.h \
 include/openssl/cryptoerr_legacy.h include/openssl/bnerr.h \
 include/internal/nelem.h include/internal/tlsgroups.h \
 providers/common/include/prov/providercommon.h \
 include/openssl/provider.h e_os.h
