crypto/evp/libcrypto-shlib-legacy_mdc2.o: crypto/evp/legacy_mdc2.c \
 include/internal/deprecated.h include/openssl/configuration.h \
 include/openssl/macros.h include/openssl/opensslconf.h \
 include/openssl/opensslv.h include/openssl/mdc2.h include/openssl/des.h \
 include/openssl/e_os2.h include/crypto/evp.h include/openssl/evp.h \
 include/openssl/types.h include/openssl/safestack.h \
 include/openssl/stack.h include/openssl/core.h \
 include/openssl/core_dispatch.h include/openssl/symhacks.h \
 include/openssl/bio.h include/openssl/crypto.h \
 include/openssl/cryptoerr.h include/openssl/cryptoerr_legacy.h \
 include/openssl/bioerr.h include/openssl/evperr.h \
 include/openssl/params.h include/openssl/bn.h include/openssl/bnerr.h \
 include/openssl/objects.h include/openssl/obj_mac.h \
 include/openssl/asn1.h include/openssl/asn1err.h \
 include/openssl/objectserr.h include/internal/refcount.h \
 include/openssl/trace.h include/crypto/ecx.h include/crypto/types.h \
 crypto/evp/legacy_meth.h
