LIBS=../../libcrypto

$AESASM=aes_core.c aes_cbc.c
IF[{- !$disabled{asm} -}]
  $AESASM_x86=aes-586.S
  $AESDEF_x86=AES_ASM
  $AESASM_x86_sse2=vpaes-x86.S aesni-x86.S
  $AESDEF_x86_sse2=VPAES_ASM OPENSSL_IA32_SSE2

  $AESASM_x86_64=\
        aes-x86_64.s vpaes-x86_64.s bsaes-x86_64.s aesni-x86_64.s \
        aesni-sha1-x86_64.s aesni-sha256-x86_64.s aesni-mb-x86_64.s
  $AESDEF_x86_64=AES_ASM VPAES_ASM BSAES_ASM

  $AESASM_ia64=aes_core.c aes_cbc.c aes-ia64.s
  $AESDEF_ia64=AES_ASM

  $AESASM_sparcv9=\
        aes_core.c aes_cbc.c aes-sparcv9.S aest4-sparcv9.S aesfx-sparcv9.S
  $AESDEF_sparcv9=AES_ASM

  $AESASM_mips32=aes_cbc.c aes-mips.S
  $AESDEF_mips32=AES_ASM
  $AESASM_mips64=$AESASM_mips32
  $AESDEF_mips64=$AESDEF_mips32

  $AESASM_s390x=aes-s390x.S
  # aes-390x.S implements AES_ctr32_encrypt and AES_xts_[en|de]crypt
  $AESDEF_s390x=AES_ASM AES_CTR_ASM AES_XTS_ASM

  $AESASM_armv4=aes_cbc.c aes-armv4.S bsaes-armv7.S aesv8-armx.S
  $AESDEF_armv4=AES_ASM BSAES_ASM
  $AESASM_aarch64=aes_core.c aes_cbc.c aesv8-armx.S vpaes-armv8.S
  $AESDEF_aarch64=VPAES_ASM

  $AESASM_parisc11=aes_core.c aes_cbc.c aes-parisc.s
  $AESDEF_parisc11=AES_ASM
  $AESASM_parisc20_64=$AESASM_parisc11
  $AESDEF_parisc20_64=$AESDEF_parisc11

  $AESASM_ppc32=aes_core.c aes_cbc.c aes-ppc.s vpaes-ppc.s aesp8-ppc.s
  $AESDEF_ppc32=AES_ASM VPAES_ASM
  $AESASM_ppc64=$AESASM_ppc32
  $AESDEF_ppc64=$AESDEF_ppc32

  $AESASM_c64xplus=aes-c64xplus.s aes_cbc.c
  # aes-c64xplus.s implements AES_ctr32_encrypt
  $AESDEF_c64xplus=AES_ASM AES_CTR_ASM

  # Now that we have defined all the arch specific variables, use the
  # appropriate one, and define the appropriate macros
  IF[$AESASM_{- $target{asm_arch} -}]
    $AESASM=$AESASM_{- $target{asm_arch} -}
    $AESDEF=$AESDEF_{- $target{asm_arch} -}
    IF[{- !$disabled{sse2} -}]
      $AESASM=$AESASM $AESASM_{- $target{asm_arch} -}_sse2
      $AESDEF=$AESDEF $AESDEF_{- $target{asm_arch} -}_sse2
    ENDIF
  ENDIF
ENDIF

$COMMON=aes_misc.c aes_ecb.c $AESASM
SOURCE[../../libcrypto]=$COMMON aes_cfb.c aes_ofb.c aes_wrap.c
IF[{- !$disabled{'deprecated-3.0'} -}]
  SOURCE[../../libcrypto]=aes_ige.c
ENDIF
SOURCE[../../providers/libfips.a]=$COMMON

# Implementations are now spread across several libraries, so the defines
# need to be applied to all affected libraries and modules.
DEFINE[../../libcrypto]=$AESDEF
DEFINE[../../providers/libfips.a]=$AESDEF
DEFINE[../../providers/libdefault.a]=$AESDEF
# We only need to include the AESDEF stuff in the legacy provider when it's a
# separate module and it's dynamically linked with libcrypto.  Otherwise, it
# already gets everything that the static libcrypto.a has, and doesn't need it
# added again.
IF[{- !$disabled{module} && !$disabled{shared} -}]
  DEFINE[../providers/liblegacy.a]=$AESDEF
ENDIF

GENERATE[aes-ia64.s]=asm/aes-ia64.S

GENERATE[aes-586.S]=asm/aes-586.pl
DEPEND[aes-586.S]=../perlasm/x86asm.pl
GENERATE[vpaes-x86.S]=asm/vpaes-x86.pl
DEPEND[vpaes-586.S]=../perlasm/x86asm.pl
GENERATE[aesni-x86.S]=asm/aesni-x86.pl
DEPEND[aesni-586.S]=../perlasm/x86asm.pl

GENERATE[aes-x86_64.s]=asm/aes-x86_64.pl
GENERATE[vpaes-x86_64.s]=asm/vpaes-x86_64.pl
GENERATE[bsaes-x86_64.s]=asm/bsaes-x86_64.pl
GENERATE[aesni-x86_64.s]=asm/aesni-x86_64.pl
GENERATE[aesni-sha1-x86_64.s]=asm/aesni-sha1-x86_64.pl
GENERATE[aesni-sha256-x86_64.s]=asm/aesni-sha256-x86_64.pl
GENERATE[aesni-mb-x86_64.s]=asm/aesni-mb-x86_64.pl

GENERATE[aes-sparcv9.S]=asm/aes-sparcv9.pl
INCLUDE[aes-sparcv9.o]=..
GENERATE[aest4-sparcv9.S]=asm/aest4-sparcv9.pl
INCLUDE[aest4-sparcv9.o]=..
DEPEND[aest4-sparcv9.S]=../perlasm/sparcv9_modes.pl
GENERATE[aesfx-sparcv9.S]=asm/aesfx-sparcv9.pl
INCLUDE[aesfx-sparcv9.o]=..

GENERATE[aes-ppc.s]=asm/aes-ppc.pl
GENERATE[vpaes-ppc.s]=asm/vpaes-ppc.pl
GENERATE[aesp8-ppc.s]=asm/aesp8-ppc.pl

GENERATE[aes-parisc.s]=asm/aes-parisc.pl

GENERATE[aes-mips.S]=asm/aes-mips.pl
INCLUDE[aes-mips.o]=..

GENERATE[aesv8-armx.S]=asm/aesv8-armx.pl
INCLUDE[aesv8-armx.o]=..
GENERATE[vpaes-armv8.S]=asm/vpaes-armv8.pl

GENERATE[aes-armv4.S]=asm/aes-armv4.pl
INCLUDE[aes-armv4.o]=..
GENERATE[bsaes-armv7.S]=asm/bsaes-armv7.pl
INCLUDE[bsaes-armv7.o]=..

GENERATE[aes-s390x.S]=asm/aes-s390x.pl
INCLUDE[aes-s390x.o]=..

GENERATE[aes-c64xplus.S]=asm/aes-c64xplus.pl
