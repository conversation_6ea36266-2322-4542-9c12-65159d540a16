LIBS=../../libcrypto

$MD5ASM=
IF[{- !$disabled{asm} -}]
  $MD5ASM_x86=md5-586.S
  $MD5ASM_x86_64=md5-x86_64.s
  $MD5ASM_sparcv9=md5-sparcv9.S

  # Now that we have defined all the arch specific variables, use the
  # appropriate one, and define the appropriate macros
  IF[$MD5ASM_{- $target{asm_arch} -}]
    $MD5ASM=$MD5ASM_{- $target{asm_arch} -}
    $MD5DEF=MD5_ASM
  ENDIF
ENDIF

$COMMON=md5_dgst.c md5_one.c md5_sha1.c $MD5ASM
SOURCE[../../libcrypto]=$COMMON

# A no-deprecated no-shared build ends up with double function definitions
# without conditioning this on dso. The issue is MD5 which is needed in the
# legacy provider for one of the spliced algorithms, however it resides in the
# default provider.  A no-deprecated build removes the external definition from
# libcrypto and this means that the code needs to be in liblegacy.  However,
# when building without 'dso', liblegacy is included in libcrypto.
IF[{- !$disabled{module} && !$disabled{shared} -}]
  SOURCE[../../providers/liblegacy.a]=$COMMON
ENDIF

# Implementations are now spread across several libraries, so the defines
# need to be applied to all affected libraries and modules.
DEFINE[../../libcrypto]=$MD5DEF
DEFINE[../../providers/liblegacy.a]=$MD5DEF

GENERATE[md5-586.S]=asm/md5-586.pl

GENERATE[md5-x86_64.s]=asm/md5-x86_64.pl

GENERATE[md5-sparcv9.S]=asm/md5-sparcv9.pl
INCLUDE[md5-sparcv9.o]=..
