crypto/async/libcrypto-shlib-async.o: crypto/async/async.c \
 crypto/async/async_local.h include/crypto/async.h \
 include/openssl/async.h include/openssl/macros.h \
 include/openssl/opensslconf.h include/openssl/configuration.h \
 include/openssl/opensslv.h include/openssl/asyncerr.h \
 include/openssl/symhacks.h include/openssl/e_os2.h \
 include/openssl/cryptoerr_legacy.h include/openssl/crypto.h \
 include/openssl/safestack.h include/openssl/stack.h \
 include/openssl/types.h include/openssl/cryptoerr.h \
 include/openssl/core.h crypto/async/arch/async_win.h \
 crypto/async/arch/async_posix.h crypto/async/arch/async_null.h \
 include/openssl/err.h include/openssl/bio.h include/openssl/bioerr.h \
 include/openssl/lhash.h include/crypto/cryptlib.h \
 include/internal/cryptlib.h include/openssl/buffer.h \
 include/openssl/buffererr.h include/openssl/asn1.h \
 include/openssl/asn1err.h include/openssl/bn.h include/openssl/bnerr.h \
 include/internal/nelem.h
