crypto/async/arch/libcrypto-shlib-async_null.o: \
 crypto/async/arch/async_null.c crypto/async/arch/../async_local.h \
 include/crypto/async.h include/openssl/async.h include/openssl/macros.h \
 include/openssl/opensslconf.h include/openssl/configuration.h \
 include/openssl/opensslv.h include/openssl/asyncerr.h \
 include/openssl/symhacks.h include/openssl/e_os2.h \
 include/openssl/cryptoerr_legacy.h include/openssl/crypto.h \
 include/openssl/safestack.h include/openssl/stack.h \
 include/openssl/types.h include/openssl/cryptoerr.h \
 include/openssl/core.h crypto/async/arch/../arch/async_win.h \
 crypto/async/arch/../arch/async_posix.h \
 crypto/async/arch/../arch/async_null.h
