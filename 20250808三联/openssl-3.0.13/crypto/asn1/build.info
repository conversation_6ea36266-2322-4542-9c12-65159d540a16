LIBS=../../libcrypto
SOURCE[../../libcrypto]=\
        a_object.c a_bitstr.c a_utctm.c a_gentm.c a_time.c a_int.c a_octet.c \
        a_print.c a_type.c a_dup.c a_d2i_fp.c a_i2d_fp.c \
        a_utf8.c a_sign.c a_digest.c a_verify.c a_mbstr.c a_strex.c \
        x_algor.c x_val.c x_sig.c x_bignum.c \
        x_int64.c x_info.c x_spki.c nsseq.c \
        d2i_pu.c d2i_pr.c i2d_evp.c \
        t_pkey.c t_spki.c t_bitst.c \
        tasn_new.c tasn_fre.c tasn_enc.c tasn_dec.c tasn_utl.c tasn_typ.c \
        tasn_prn.c tasn_scn.c ameth_lib.c \
        f_int.c f_string.c \
        x_pkey.c bio_asn1.c bio_ndef.c asn_mime.c \
        asn1_gen.c asn1_parse.c asn1_lib.c asn1_err.c a_strnid.c \
        evp_asn1.c asn_pack.c p5_pbe.c p5_pbev2.c p5_scrypt.c p8_pkey.c \
        asn_moid.c asn_mstbl.c asn1_item_list.c \
        d2i_param.c
IF[{- !$disabled{'rsa'} and !$disabled{'rc4'} -}]
  SOURCE[../../libcrypto]=n_pkey.c
ENDIF
IF[{- !$disabled{'deprecated-3.0'} -}]
  SOURCE[../../libcrypto]=x_long.c
ENDIF
