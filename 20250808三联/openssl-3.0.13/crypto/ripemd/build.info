LIBS=../../libcrypto

$RMD160ASM=
IF[{- !$disabled{asm} -}]
  $RMD160ASM_x86=rmd-586.S

  # Now that we have defined all the arch specific variables, use the
  # appropriate one, and define the appropriate macros
  IF[$RMD160ASM_{- $target{asm_arch} -}]
    $RMD160ASM=$RMD160ASM_{- $target{asm_arch} -}
    $RMD160DEF=RMD160_ASM
  ENDIF
ENDIF

# Implementations are now spread across several libraries, so the defines
# need to be applied to all affected libraries and modules

SOURCE[../../libcrypto]=rmd_dgst.c rmd_one.c $RMD160ASM
DEFINE[../../libcrypto]=$RMD160DEF

# When all deprecated symbols are removed, libcrypto doesn't export the
# RIPEMD160 functions, so we must include them directly in liblegacy.a
IF[{- $disabled{'deprecated-3.0'} && !$disabled{module} && !$disabled{shared} -}]
  SOURCE[../../providers/liblegacy.a]=rmd_dgst.c rmd_one.c $RMD160ASM
  DEFINE[../../providers/liblegacy.a]=$RMD160DEF
ENDIF

GENERATE[rmd-586.S]=asm/rmd-586.pl
DEPEND[rmd-586.S]=../perlasm/x86asm.pl
