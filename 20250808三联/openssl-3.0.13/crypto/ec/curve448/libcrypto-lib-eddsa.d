crypto/ec/curve448/libcrypto-lib-eddsa.o: crypto/ec/curve448/eddsa.c \
 include/openssl/crypto.h include/openssl/macros.h \
 include/openssl/opensslconf.h include/openssl/configuration.h \
 include/openssl/opensslv.h include/openssl/e_os2.h \
 include/openssl/safestack.h include/openssl/stack.h \
 include/openssl/types.h include/openssl/cryptoerr.h \
 include/openssl/symhacks.h include/openssl/cryptoerr_legacy.h \
 include/openssl/core.h include/openssl/evp.h \
 include/openssl/core_dispatch.h include/openssl/bio.h \
 include/openssl/bioerr.h include/openssl/evperr.h \
 include/openssl/params.h include/openssl/bn.h include/openssl/bnerr.h \
 include/openssl/objects.h include/openssl/obj_mac.h \
 include/openssl/asn1.h include/openssl/asn1err.h \
 include/openssl/objectserr.h include/crypto/ecx.h \
 include/internal/refcount.h include/openssl/trace.h \
 include/crypto/types.h crypto/ec/curve448/curve448_local.h \
 crypto/ec/curve448/curve448utils.h include/internal/numbers.h \
 crypto/ec/curve448/word.h crypto/ec/curve448/arch_64/arch_intrinsics.h \
 include/internal/constant_time.h crypto/ec/curve448/ed448.h \
 crypto/ec/curve448/point_448.h crypto/ec/curve448/field.h \
 crypto/ec/curve448/arch_64/f_impl.h
