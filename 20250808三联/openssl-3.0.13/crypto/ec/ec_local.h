/*
 * Copyright 2001-2021 The OpenSSL Project Authors. All Rights Reserved.
 * Copyright (c) 2002, Oracle and/or its affiliates. All rights reserved
 *
 * Licensed under the Apache License 2.0 (the "License").  You may not use
 * this file except in compliance with the License.  You can obtain a copy
 * in the file LICENSE in the source distribution or at
 * https://www.openssl.org/source/license.html
 */

#include <stdlib.h>

#include <openssl/obj_mac.h>
#include <openssl/ec.h>
#include <openssl/bn.h>
#include "internal/refcount.h"
#include "crypto/ec.h"

#if defined(__SUNPRO_C)
# if __SUNPRO_C >= 0x520
#  pragma error_messages (off,E_ARRAY_OF_INCOMPLETE_NONAME,E_ARRAY_OF_INCOMPLETE)
# endif
#endif

/* Use default functions for poin2oct, oct2point and compressed coordinates */
#define EC_FLAGS_DEFAULT_OCT    0x1

/* Use custom formats for EC_GROUP, EC_POINT and EC_KEY */
#define EC_FLAGS_CUSTOM_CURVE   0x2

/* Curve does not support signing operations */
#define EC_FLAGS_NO_SIGN        0x4

#ifdef OPENSSL_NO_DEPRECATED_3_0
typedef struct ec_method_st EC_METHOD;
#endif

/*
 * Structure details are not part of the exported interface, so all this may
 * change in future versions.
 */

struct ec_method_st {
    /* Various method flags */
    int flags;
    /* used by EC_METHOD_get_field_type: */
    int field_type;             /* a NID */
    /*
     * used by EC_GROUP_new, EC_GROUP_free, EC_GROUP_clear_free,
     * EC_GROUP_copy:
     */
    int (*group_init) (EC_GROUP *);
    void (*group_finish) (EC_GROUP *);
    void (*group_clear_finish) (EC_GROUP *);
    int (*group_copy) (EC_GROUP *, const EC_GROUP *);
    /* used by EC_GROUP_set_curve, EC_GROUP_get_curve: */
    int (*group_set_curve) (EC_GROUP *, const BIGNUM *p, const BIGNUM *a,
                            const BIGNUM *b, BN_CTX *);
    int (*group_get_curve) (const EC_GROUP *, BIGNUM *p, BIGNUM *a, BIGNUM *b,
                            BN_CTX *);
    /* used by EC_GROUP_get_degree: */
    int (*group_get_degree) (const EC_GROUP *);
    int (*group_order_bits) (const EC_GROUP *);
    /* used by EC_GROUP_check: */
    int (*group_check_discriminant) (const EC_GROUP *, BN_CTX *);
    /*
     * used by EC_POINT_new, EC_POINT_free, EC_POINT_clear_free,
     * EC_POINT_copy:
     */
    int (*point_init) (EC_POINT *);
    void (*point_finish) (EC_POINT *);
    void (*point_clear_finish) (EC_POINT *);
    int (*point_copy) (EC_POINT *, const EC_POINT *);
    /*-
     * used by EC_POINT_set_to_infinity,
     * EC_POINT_set_Jprojective_coordinates_GFp,
     * EC_POINT_get_Jprojective_coordinates_GFp,
     * EC_POINT_set_affine_coordinates,
     * EC_POINT_get_affine_coordinates,
     * EC_POINT_set_compressed_coordinates:
     */
    int (*point_set_to_infinity) (const EC_GROUP *, EC_POINT *);
    int (*point_set_affine_coordinates) (const EC_GROUP *, EC_POINT *,
                                         const BIGNUM *x, const BIGNUM *y,
                                         BN_CTX *);
    int (*point_get_affine_coordinates) (const EC_GROUP *, const EC_POINT *,
                                         BIGNUM *x, BIGNUM *y, BN_CTX *);
    int (*point_set_compressed_coordinates) (const EC_GROUP *, EC_POINT *,
                                             const BIGNUM *x, int y_bit,
                                             BN_CTX *);
    /* used by EC_POINT_point2oct, EC_POINT_oct2point: */
    size_t (*point2oct) (const EC_GROUP *, const EC_POINT *,
                         point_conversion_form_t form, unsigned char *buf,
                         size_t len, BN_CTX *);
    int (*oct2point) (const EC_GROUP *, EC_POINT *, const unsigned char *buf,
                      size_t len, BN_CTX *);
    /* used by EC_POINT_add, EC_POINT_dbl, ECP_POINT_invert: */
    int (*add) (const EC_GROUP *, EC_POINT *r, const EC_POINT *a,
                const EC_POINT *b, BN_CTX *);
    int (*dbl) (const EC_GROUP *, EC_POINT *r, const EC_POINT *a, BN_CTX *);
    int (*invert) (const EC_GROUP *, EC_POINT *, BN_CTX *);
    /*
     * used by EC_POINT_is_at_infinity, EC_POINT_is_on_curve, EC_POINT_cmp:
     */
    int (*is_at_infinity) (const EC_GROUP *, const EC_POINT *);
    int (*is_on_curve) (const EC_GROUP *, const EC_POINT *, BN_CTX *);
    int (*point_cmp) (const EC_GROUP *, const EC_POINT *a, const EC_POINT *b,
                      BN_CTX *);
    /* used by EC_POINT_make_affine, EC_POINTs_make_affine: */
    int (*make_affine) (const EC_GROUP *, EC_POINT *, BN_CTX *);
    int (*points_make_affine) (const EC_GROUP *, size_t num, EC_POINT *[],
                               BN_CTX *);
    /*
     * used by EC_POINTs_mul, EC_POINT_mul, EC_POINT_precompute_mult,
     * EC_POINT_have_precompute_mult (default implementations are used if the
     * 'mul' pointer is 0):
     */
    /*-
     * mul() calculates the value
     *
     *   r := generator * scalar
     *        + points[0] * scalars[0]
     *        + ...
     *        + points[num-1] * scalars[num-1].
     *
     * For a fixed point multiplication (scalar != NULL, num == 0)
     * or a variable point multiplication (scalar == NULL, num == 1),
     * mul() must use a constant time algorithm: in both cases callers
     * should provide an input scalar (either scalar or scalars[0])
     * in the range [0, ec_group_order); for robustness, implementers
     * should handle the case when the scalar has not been reduced, but
     * may treat it as an unusual input, without any constant-timeness
     * guarantee.
     */
    int (*mul) (const EC_GROUP *group, EC_POINT *r, const BIGNUM *scalar,
                size_t num, const EC_POINT *points[], const BIGNUM *scalars[],
                BN_CTX *);
    int (*precompute_mult) (EC_GROUP *group, BN_CTX *);
    int (*have_precompute_mult) (const EC_GROUP *group);
    /* internal functions */
    /*
     * 'field_mul', 'field_sqr', and 'field_div' can be used by 'add' and
     * 'dbl' so that the same implementations of point operations can be used
     * with different optimized implementations of expensive field
     * operations:
     */
    int (*field_mul) (const EC_GROUP *, BIGNUM *r, const BIGNUM *a,
                      const BIGNUM *b, BN_CTX *);
    int (*field_sqr) (const EC_GROUP *, BIGNUM *r, const BIGNUM *a, BN_CTX *);
    int (*field_div) (const EC_GROUP *, BIGNUM *r, const BIGNUM *a,
                      const BIGNUM *b, BN_CTX *);
    /*-
     * 'field_inv' computes the multiplicative inverse of a in the field,
     * storing the result in r.
     *
     * If 'a' is zero (or equivalent), you'll get an EC_R_CANNOT_INVERT error.
     */
    int (*field_inv) (const EC_GROUP *, BIGNUM *r, const BIGNUM *a, BN_CTX *);
    /* e.g. to Montgomery */
    int (*field_encode) (const EC_GROUP *, BIGNUM *r, const BIGNUM *a,
                         BN_CTX *);
    /* e.g. from Montgomery */
    int (*field_decode) (const EC_GROUP *, BIGNUM *r, const BIGNUM *a,
                         BN_CTX *);
    int (*field_set_to_one) (const EC_GROUP *, BIGNUM *r, BN_CTX *);
    /* private key operations */
    size_t (*priv2oct)(const EC_KEY *eckey, unsigned char *buf, size_t len);
    int (*oct2priv)(EC_KEY *eckey, const unsigned char *buf, size_t len);
    int (*set_private)(EC_KEY *eckey, const BIGNUM *priv_key);
    int (*keygen)(EC_KEY *eckey);
    int (*keycheck)(const EC_KEY *eckey);
    int (*keygenpub)(EC_KEY *eckey);
    int (*keycopy)(EC_KEY *dst, const EC_KEY *src);
    void (*keyfinish)(EC_KEY *eckey);
    /* custom ECDH operation */
    int (*ecdh_compute_key)(unsigned char **pout, size_t *poutlen,
                            const EC_POINT *pub_key, const EC_KEY *ecdh);
    /* custom ECDSA */
    int (*ecdsa_sign_setup)(EC_KEY *eckey, BN_CTX *ctx, BIGNUM **kinvp,
                            BIGNUM **rp);
    ECDSA_SIG *(*ecdsa_sign_sig)(const unsigned char *dgst, int dgstlen,
                                 const BIGNUM *kinv, const BIGNUM *r,
                                 EC_KEY *eckey);
    int (*ecdsa_verify_sig)(const unsigned char *dgst, int dgstlen,
                            const ECDSA_SIG *sig, EC_KEY *eckey);
    /* Inverse modulo order */
    int (*field_inverse_mod_ord)(const EC_GROUP *, BIGNUM *r,
                                 const BIGNUM *x, BN_CTX *);
    int (*blind_coordinates)(const EC_GROUP *group, EC_POINT *p, BN_CTX *ctx);
    int (*ladder_pre)(const EC_GROUP *group,
                      EC_POINT *r, EC_POINT *s,
                      EC_POINT *p, BN_CTX *ctx);
    int (*ladder_step)(const EC_GROUP *group,
                       EC_POINT *r, EC_POINT *s,
                       EC_POINT *p, BN_CTX *ctx);
    int (*ladder_post)(const EC_GROUP *group,
                       EC_POINT *r, EC_POINT *s,
                       EC_POINT *p, BN_CTX *ctx);
};

/*
 * Types and functions to manipulate pre-computed values.
 */
typedef struct nistp224_pre_comp_st NISTP224_PRE_COMP;
typedef struct nistp256_pre_comp_st NISTP256_PRE_COMP;
typedef struct nistp521_pre_comp_st NISTP521_PRE_COMP;
typedef struct nistz256_pre_comp_st NISTZ256_PRE_COMP;
typedef struct ec_pre_comp_st EC_PRE_COMP;

struct ec_group_st {
    const EC_METHOD *meth;
    EC_POINT *generator;        /* optional */
    BIGNUM *order, *cofactor;
    int curve_name;             /* optional NID for named curve */
    int asn1_flag;              /* flag to control the asn1 encoding */
    int decoded_from_explicit_params; /* set if decoded from explicit
                                       * curve parameters encoding */
    point_conversion_form_t asn1_form;
    unsigned char *seed;        /* optional seed for parameters (appears in
                                 * ASN1) */
    size_t seed_len;
    /*
     * The following members are handled by the method functions, even if
     * they appear generic
     */
    /*
     * Field specification. For curves over GF(p), this is the modulus; for
     * curves over GF(2^m), this is the irreducible polynomial defining the
     * field.
     */
    BIGNUM *field;
    /*
     * Field specification for curves over GF(2^m). The irreducible f(t) is
     * then of the form: t^poly[0] + t^poly[1] + ... + t^poly[k] where m =
     * poly[0] > poly[1] > ... > poly[k] = 0. The array is terminated with
     * poly[k+1]=-1. All elliptic curve irreducibles have at most 5 non-zero
     * terms.
     */
    int poly[6];
    /*
     * Curve coefficients. (Here the assumption is that BIGNUMs can be used
     * or abused for all kinds of fields, not just GF(p).) For characteristic
     * > 3, the curve is defined by a Weierstrass equation of the form y^2 =
     * x^3 + a*x + b. For characteristic 2, the curve is defined by an
     * equation of the form y^2 + x*y = x^3 + a*x^2 + b.
     */
    BIGNUM *a, *b;
    /* enable optimized point arithmetics for special case */
    int a_is_minus3;
    /* method-specific (e.g., Montgomery structure) */
    void *field_data1;
    /* method-specific */
    void *field_data2;
    /* method-specific */
    int (*field_mod_func) (BIGNUM *, const BIGNUM *, const BIGNUM *,
                           BN_CTX *);
    /* data for ECDSA inverse */
    BN_MONT_CTX *mont_data;

    /*
     * Precomputed values for speed. The PCT_xxx names match the
     * pre_comp.xxx union names; see the SETPRECOMP and HAVEPRECOMP
     * macros, below.
     */
    enum {
        PCT_none,
        PCT_nistp224, PCT_nistp256, PCT_nistp521, PCT_nistz256,
        PCT_ec
    } pre_comp_type;
    union {
        NISTP224_PRE_COMP *nistp224;
        NISTP256_PRE_COMP *nistp256;
        NISTP521_PRE_COMP *nistp521;
        NISTZ256_PRE_COMP *nistz256;
        EC_PRE_COMP *ec;
    } pre_comp;

    OSSL_LIB_CTX *libctx;
    char *propq;
};

#define SETPRECOMP(g, type, pre) \
    g->pre_comp_type = PCT_##type, g->pre_comp.type = pre
#define HAVEPRECOMP(g, type) \
    g->pre_comp_type == PCT_##type && g->pre_comp.type != NULL

struct ec_key_st {
    const EC_KEY_METHOD *meth;
    ENGINE *engine;
    int version;
    EC_GROUP *group;
    EC_POINT *pub_key;
    BIGNUM *priv_key;
    unsigned int enc_flag;
    point_conversion_form_t conv_form;
    CRYPTO_REF_COUNT references;
    int flags;
#ifndef FIPS_MODULE
    CRYPTO_EX_DATA ex_data;
#endif
    CRYPTO_RWLOCK *lock;
    OSSL_LIB_CTX *libctx;
    char *propq;

    /* Provider data */
    size_t dirty_cnt; /* If any key material changes, increment this */
};

struct ec_point_st {
    const EC_METHOD *meth;
    /* NID for the curve if known */
    int curve_name;
    /*
     * All members except 'meth' are handled by the method functions, even if
     * they appear generic
     */
    BIGNUM *X;
    BIGNUM *Y;
    BIGNUM *Z;                  /* Jacobian projective coordinates: * (X, Y,
                                 * Z) represents (X/Z^2, Y/Z^3) if Z != 0 */
    int Z_is_one;               /* enable optimized point arithmetics for
                                 * special case */
};

static ossl_inline int ec_point_is_compat(const EC_POINT *point,
                                          const EC_GROUP *group)
{
    return group->meth == point->meth
           && (group->curve_name == 0
               || point->curve_name == 0
               || group->curve_name == point->curve_name);
}

NISTP224_PRE_COMP *EC_nistp224_pre_comp_dup(NISTP224_PRE_COMP *);
NISTP256_PRE_COMP *EC_nistp256_pre_comp_dup(NISTP256_PRE_COMP *);
NISTP521_PRE_COMP *EC_nistp521_pre_comp_dup(NISTP521_PRE_COMP *);
NISTZ256_PRE_COMP *EC_nistz256_pre_comp_dup(NISTZ256_PRE_COMP *);
NISTP256_PRE_COMP *EC_nistp256_pre_comp_dup(NISTP256_PRE_COMP *);
EC_PRE_COMP *EC_ec_pre_comp_dup(EC_PRE_COMP *);

void EC_pre_comp_free(EC_GROUP *group);
void EC_nistp224_pre_comp_free(NISTP224_PRE_COMP *);
void EC_nistp256_pre_comp_free(NISTP256_PRE_COMP *);
void EC_nistp521_pre_comp_free(NISTP521_PRE_COMP *);
void EC_nistz256_pre_comp_free(NISTZ256_PRE_COMP *);
void EC_ec_pre_comp_free(EC_PRE_COMP *);

/*
 * method functions in ec_mult.c (ec_lib.c uses these as defaults if
 * group->method->mul is 0)
 */
int ossl_ec_wNAF_mul(const EC_GROUP *group, EC_POINT *r, const BIGNUM *scalar,
                     size_t num, const EC_POINT *points[],
                     const BIGNUM *scalars[], BN_CTX *);
int ossl_ec_wNAF_precompute_mult(EC_GROUP *group, BN_CTX *);
int ossl_ec_wNAF_have_precompute_mult(const EC_GROUP *group);

/* method functions in ecp_smpl.c */
int ossl_ec_GFp_simple_group_init(EC_GROUP *);
void ossl_ec_GFp_simple_group_finish(EC_GROUP *);
void ossl_ec_GFp_simple_group_clear_finish(EC_GROUP *);
int ossl_ec_GFp_simple_group_copy(EC_GROUP *, const EC_GROUP *);
int ossl_ec_GFp_simple_group_set_curve(EC_GROUP *, const BIGNUM *p,
                                       const BIGNUM *a, const BIGNUM *b,
                                       BN_CTX *);
int ossl_ec_GFp_simple_group_get_curve(const EC_GROUP *, BIGNUM *p, BIGNUM *a,
                                       BIGNUM *b, BN_CTX *);
int ossl_ec_GFp_simple_group_get_degree(const EC_GROUP *);
int ossl_ec_GFp_simple_group_check_discriminant(const EC_GROUP *, BN_CTX *);
int ossl_ec_GFp_simple_point_init(EC_POINT *);
void ossl_ec_GFp_simple_point_finish(EC_POINT *);
void ossl_ec_GFp_simple_point_clear_finish(EC_POINT *);
int ossl_ec_GFp_simple_point_copy(EC_POINT *, const EC_POINT *);
int ossl_ec_GFp_simple_point_set_to_infinity(const EC_GROUP *, EC_POINT *);
int ossl_ec_GFp_simple_set_Jprojective_coordinates_GFp(const EC_GROUP *,
                                                       EC_POINT *,
                                                       const BIGNUM *x,
                                                       const BIGNUM *y,
                                                       const BIGNUM *z,
                                                       BN_CTX *);
int ossl_ec_GFp_simple_get_Jprojective_coordinates_GFp(const EC_GROUP *,
                                                       const EC_POINT *,
                                                       BIGNUM *x,
                                                       BIGNUM *y, BIGNUM *z,
                                                       BN_CTX *);
int ossl_ec_GFp_simple_point_set_affine_coordinates(const EC_GROUP *, EC_POINT *,
                                                    const BIGNUM *x,
                                                    const BIGNUM *y, BN_CTX *);
int ossl_ec_GFp_simple_point_get_affine_coordinates(const EC_GROUP *,
                                                    const EC_POINT *, BIGNUM *x,
                                                    BIGNUM *y, BN_CTX *);
int ossl_ec_GFp_simple_set_compressed_coordinates(const EC_GROUP *, EC_POINT *,
                                                  const BIGNUM *x, int y_bit,
                                                  BN_CTX *);
size_t ossl_ec_GFp_simple_point2oct(const EC_GROUP *, const EC_POINT *,
                                    point_conversion_form_t form,
                                    unsigned char *buf, size_t len, BN_CTX *);
int ossl_ec_GFp_simple_oct2point(const EC_GROUP *, EC_POINT *,
                                 const unsigned char *buf, size_t len, BN_CTX *);
int ossl_ec_GFp_simple_add(const EC_GROUP *, EC_POINT *r, const EC_POINT *a,
                           const EC_POINT *b, BN_CTX *);
int ossl_ec_GFp_simple_dbl(const EC_GROUP *, EC_POINT *r, const EC_POINT *a,
                           BN_CTX *);
int ossl_ec_GFp_simple_invert(const EC_GROUP *, EC_POINT *, BN_CTX *);
int ossl_ec_GFp_simple_is_at_infinity(const EC_GROUP *, const EC_POINT *);
int ossl_ec_GFp_simple_is_on_curve(const EC_GROUP *, const EC_POINT *, BN_CTX *);
int ossl_ec_GFp_simple_cmp(const EC_GROUP *, const EC_POINT *a,
                           const EC_POINT *b, BN_CTX *);
int ossl_ec_GFp_simple_make_affine(const EC_GROUP *, EC_POINT *, BN_CTX *);
int ossl_ec_GFp_simple_points_make_affine(const EC_GROUP *, size_t num,
                                          EC_POINT *[], BN_CTX *);
int ossl_ec_GFp_simple_field_mul(const EC_GROUP *, BIGNUM *r, const BIGNUM *a,
                                 const BIGNUM *b, BN_CTX *);
int ossl_ec_GFp_simple_field_sqr(const EC_GROUP *, BIGNUM *r, const BIGNUM *a,
                                 BN_CTX *);
int ossl_ec_GFp_simple_field_inv(const EC_GROUP *, BIGNUM *r, const BIGNUM *a,
                                 BN_CTX *);
int ossl_ec_GFp_simple_blind_coordinates(const EC_GROUP *group, EC_POINT *p,
                                         BN_CTX *ctx);
int ossl_ec_GFp_simple_ladder_pre(const EC_GROUP *group,
                                  EC_POINT *r, EC_POINT *s,
                                  EC_POINT *p, BN_CTX *ctx);
int ossl_ec_GFp_simple_ladder_step(const EC_GROUP *group,
                                   EC_POINT *r, EC_POINT *s,
                                   EC_POINT *p, BN_CTX *ctx);
int ossl_ec_GFp_simple_ladder_post(const EC_GROUP *group,
                                   EC_POINT *r, EC_POINT *s,
                                   EC_POINT *p, BN_CTX *ctx);

/* method functions in ecp_mont.c */
int ossl_ec_GFp_mont_group_init(EC_GROUP *);
int ossl_ec_GFp_mont_group_set_curve(EC_GROUP *, const BIGNUM *p,
                                     const BIGNUM *a,
                                     const BIGNUM *b, BN_CTX *);
void ossl_ec_GFp_mont_group_finish(EC_GROUP *);
void ossl_ec_GFp_mont_group_clear_finish(EC_GROUP *);
int ossl_ec_GFp_mont_group_copy(EC_GROUP *, const EC_GROUP *);
int ossl_ec_GFp_mont_field_mul(const EC_GROUP *, BIGNUM *r, const BIGNUM *a,
                               const BIGNUM *b, BN_CTX *);
int ossl_ec_GFp_mont_field_sqr(const EC_GROUP *, BIGNUM *r, const BIGNUM *a,
                               BN_CTX *);
int ossl_ec_GFp_mont_field_inv(const EC_GROUP *, BIGNUM *r, const BIGNUM *a,
                               BN_CTX *);
int ossl_ec_GFp_mont_field_encode(const EC_GROUP *, BIGNUM *r, const BIGNUM *a,
                                  BN_CTX *);
int ossl_ec_GFp_mont_field_decode(const EC_GROUP *, BIGNUM *r, const BIGNUM *a,
                                  BN_CTX *);
int ossl_ec_GFp_mont_field_set_to_one(const EC_GROUP *, BIGNUM *r, BN_CTX *);

/* method functions in ecp_nist.c */
int ossl_ec_GFp_nist_group_copy(EC_GROUP *dest, const EC_GROUP *src);
int ossl_ec_GFp_nist_group_set_curve(EC_GROUP *, const BIGNUM *p,
                                     const BIGNUM *a, const BIGNUM *b, BN_CTX *);
int ossl_ec_GFp_nist_field_mul(const EC_GROUP *, BIGNUM *r, const BIGNUM *a,
                              const BIGNUM *b, BN_CTX *);
int ossl_ec_GFp_nist_field_sqr(const EC_GROUP *, BIGNUM *r, const BIGNUM *a,
                               BN_CTX *);

/* method functions in ec2_smpl.c */
int ossl_ec_GF2m_simple_group_init(EC_GROUP *);
void ossl_ec_GF2m_simple_group_finish(EC_GROUP *);
void ossl_ec_GF2m_simple_group_clear_finish(EC_GROUP *);
int ossl_ec_GF2m_simple_group_copy(EC_GROUP *, const EC_GROUP *);
int ossl_ec_GF2m_simple_group_set_curve(EC_GROUP *, const BIGNUM *p,
                                        const BIGNUM *a, const BIGNUM *b,
                                        BN_CTX *);
int ossl_ec_GF2m_simple_group_get_curve(const EC_GROUP *, BIGNUM *p, BIGNUM *a,
                                        BIGNUM *b, BN_CTX *);
int ossl_ec_GF2m_simple_group_get_degree(const EC_GROUP *);
int ossl_ec_GF2m_simple_group_check_discriminant(const EC_GROUP *, BN_CTX *);
int ossl_ec_GF2m_simple_point_init(EC_POINT *);
void ossl_ec_GF2m_simple_point_finish(EC_POINT *);
void ossl_ec_GF2m_simple_point_clear_finish(EC_POINT *);
int ossl_ec_GF2m_simple_point_copy(EC_POINT *, const EC_POINT *);
int ossl_ec_GF2m_simple_point_set_to_infinity(const EC_GROUP *, EC_POINT *);
int ossl_ec_GF2m_simple_point_set_affine_coordinates(const EC_GROUP *,
                                                     EC_POINT *,
                                                     const BIGNUM *x,
                                                     const BIGNUM *y, BN_CTX *);
int ossl_ec_GF2m_simple_point_get_affine_coordinates(const EC_GROUP *,
                                                     const EC_POINT *, BIGNUM *x,
                                                     BIGNUM *y, BN_CTX *);
int ossl_ec_GF2m_simple_set_compressed_coordinates(const EC_GROUP *, EC_POINT *,
                                                   const BIGNUM *x, int y_bit,
                                                   BN_CTX *);
size_t ossl_ec_GF2m_simple_point2oct(const EC_GROUP *, const EC_POINT *,
                                     point_conversion_form_t form,
                                     unsigned char *buf, size_t len, BN_CTX *);
int ossl_ec_GF2m_simple_oct2point(const EC_GROUP *, EC_POINT *,
                                  const unsigned char *buf, size_t len, BN_CTX *);
int ossl_ec_GF2m_simple_add(const EC_GROUP *, EC_POINT *r, const EC_POINT *a,
                            const EC_POINT *b, BN_CTX *);
int ossl_ec_GF2m_simple_dbl(const EC_GROUP *, EC_POINT *r, const EC_POINT *a,
                            BN_CTX *);
int ossl_ec_GF2m_simple_invert(const EC_GROUP *, EC_POINT *, BN_CTX *);
int ossl_ec_GF2m_simple_is_at_infinity(const EC_GROUP *, const EC_POINT *);
int ossl_ec_GF2m_simple_is_on_curve(const EC_GROUP *, const EC_POINT *, BN_CTX *);
int ossl_ec_GF2m_simple_cmp(const EC_GROUP *, const EC_POINT *a,
                            const EC_POINT *b, BN_CTX *);
int ossl_ec_GF2m_simple_make_affine(const EC_GROUP *, EC_POINT *, BN_CTX *);
int ossl_ec_GF2m_simple_points_make_affine(const EC_GROUP *, size_t num,
                                           EC_POINT *[], BN_CTX *);
int ossl_ec_GF2m_simple_field_mul(const EC_GROUP *, BIGNUM *r, const BIGNUM *a,
                                  const BIGNUM *b, BN_CTX *);
int ossl_ec_GF2m_simple_field_sqr(const EC_GROUP *, BIGNUM *r, const BIGNUM *a,
                                  BN_CTX *);
int ossl_ec_GF2m_simple_field_div(const EC_GROUP *, BIGNUM *r, const BIGNUM *a,
                                 const BIGNUM *b, BN_CTX *);

#ifndef OPENSSL_NO_EC_NISTP_64_GCC_128
# ifdef B_ENDIAN
#  error "Can not enable ec_nistp_64_gcc_128 on big-endian systems"
# endif

/* method functions in ecp_nistp224.c */
int ossl_ec_GFp_nistp224_group_init(EC_GROUP *group);
int ossl_ec_GFp_nistp224_group_set_curve(EC_GROUP *group, const BIGNUM *p,
                                         const BIGNUM *a, const BIGNUM *n,
                                         BN_CTX *);
int ossl_ec_GFp_nistp224_point_get_affine_coordinates(const EC_GROUP *group,
                                                      const EC_POINT *point,
                                                      BIGNUM *x, BIGNUM *y,
                                                      BN_CTX *ctx);
int ossl_ec_GFp_nistp224_mul(const EC_GROUP *group, EC_POINT *r,
                             const BIGNUM *scalar, size_t num,
                             const EC_POINT *points[], const BIGNUM *scalars[],
                             BN_CTX *);
int ossl_ec_GFp_nistp224_points_mul(const EC_GROUP *group, EC_POINT *r,
                                    const BIGNUM *scalar, size_t num,
                                    const EC_POINT *points[],
                                    const BIGNUM *scalars[], BN_CTX *ctx);
int ossl_ec_GFp_nistp224_precompute_mult(EC_GROUP *group, BN_CTX *ctx);
int ossl_ec_GFp_nistp224_have_precompute_mult(const EC_GROUP *group);

/* method functions in ecp_nistp256.c */
int ossl_ec_GFp_nistp256_group_init(EC_GROUP *group);
int ossl_ec_GFp_nistp256_group_set_curve(EC_GROUP *group, const BIGNUM *p,
                                         const BIGNUM *a, const BIGNUM *n,
                                         BN_CTX *);
int ossl_ec_GFp_nistp256_point_get_affine_coordinates(const EC_GROUP *group,
                                                      const EC_POINT *point,
                                                      BIGNUM *x, BIGNUM *y,
                                                      BN_CTX *ctx);
int ossl_ec_GFp_nistp256_mul(const EC_GROUP *group, EC_POINT *r,
                             const BIGNUM *scalar, size_t num,
                             const EC_POINT *points[], const BIGNUM *scalars[],
                             BN_CTX *);
int ossl_ec_GFp_nistp256_points_mul(const EC_GROUP *group, EC_POINT *r,
                                    const BIGNUM *scalar, size_t num,
                                    const EC_POINT *points[],
                                    const BIGNUM *scalars[], BN_CTX *ctx);
int ossl_ec_GFp_nistp256_precompute_mult(EC_GROUP *group, BN_CTX *ctx);
int ossl_ec_GFp_nistp256_have_precompute_mult(const EC_GROUP *group);

/* method functions in ecp_nistp521.c */
int ossl_ec_GFp_nistp521_group_init(EC_GROUP *group);
int ossl_ec_GFp_nistp521_group_set_curve(EC_GROUP *group, const BIGNUM *p,
                                         const BIGNUM *a, const BIGNUM *n,
                                         BN_CTX *);
int ossl_ec_GFp_nistp521_point_get_affine_coordinates(const EC_GROUP *group,
                                                      const EC_POINT *point,
                                                      BIGNUM *x, BIGNUM *y,
                                                      BN_CTX *ctx);
int ossl_ec_GFp_nistp521_mul(const EC_GROUP *group, EC_POINT *r,
                             const BIGNUM *scalar, size_t num,
                             const EC_POINT *points[], const BIGNUM *scalars[],
                             BN_CTX *);
int ossl_ec_GFp_nistp521_points_mul(const EC_GROUP *group, EC_POINT *r,
                                    const BIGNUM *scalar, size_t num,
                                    const EC_POINT *points[],
                                    const BIGNUM *scalars[], BN_CTX *ctx);
int ossl_ec_GFp_nistp521_precompute_mult(EC_GROUP *group, BN_CTX *ctx);
int ossl_ec_GFp_nistp521_have_precompute_mult(const EC_GROUP *group);

/* utility functions in ecp_nistputil.c */
void ossl_ec_GFp_nistp_points_make_affine_internal(size_t num, void *point_array,
                                                   size_t felem_size,
                                                   void *tmp_felems,
                                                   void (*felem_one) (void *out),
                                                   int (*felem_is_zero)
                                                       (const void *in),
                                                   void (*felem_assign)
                                                       (void *out, const void *in),
                                                   void (*felem_square)
                                                       (void *out, const void *in),
                                                   void (*felem_mul)
                                                       (void *out,
                                                        const void *in1,
                                                        const void *in2),
                                                   void (*felem_inv)
                                                       (void *out, const void *in),
                                                   void (*felem_contract)
                                                       (void *out, const void *in));
void ossl_ec_GFp_nistp_recode_scalar_bits(unsigned char *sign,
                                          unsigned char *digit,
                                          unsigned char in);
#endif
int ossl_ec_group_simple_order_bits(const EC_GROUP *group);

/**
 *  Creates a new EC_GROUP object
 *  \param   libctx The associated library context or NULL for the default
 *                  library context
 *  \param   propq  Any property query string
 *  \param   meth   EC_METHOD to use
 *  \return  newly created EC_GROUP object or NULL in case of an error.
 */
EC_GROUP *ossl_ec_group_new_ex(OSSL_LIB_CTX *libctx, const char *propq,
                               const EC_METHOD *meth);

#ifdef ECP_NISTZ256_ASM
/** Returns GFp methods using montgomery multiplication, with x86-64 optimized
 * P256. See http://eprint.iacr.org/2013/816.
 *  \return  EC_METHOD object
 */
const EC_METHOD *EC_GFp_nistz256_method(void);
#endif
#ifdef S390X_EC_ASM
const EC_METHOD *EC_GFp_s390x_nistp256_method(void);
const EC_METHOD *EC_GFp_s390x_nistp384_method(void);
const EC_METHOD *EC_GFp_s390x_nistp521_method(void);
#endif

size_t ossl_ec_key_simple_priv2oct(const EC_KEY *eckey,
                                   unsigned char *buf, size_t len);
int ossl_ec_key_simple_oct2priv(EC_KEY *eckey, const unsigned char *buf,
                                size_t len);
int ossl_ec_key_simple_generate_key(EC_KEY *eckey);
int ossl_ec_key_simple_generate_public_key(EC_KEY *eckey);
int ossl_ec_key_simple_check_key(const EC_KEY *eckey);

int ossl_ec_curve_nid_from_params(const EC_GROUP *group, BN_CTX *ctx);

/* EC_METHOD definitions */

struct ec_key_method_st {
    const char *name;
    int32_t flags;
    int (*init)(EC_KEY *key);
    void (*finish)(EC_KEY *key);
    int (*copy)(EC_KEY *dest, const EC_KEY *src);
    int (*set_group)(EC_KEY *key, const EC_GROUP *grp);
    int (*set_private)(EC_KEY *key, const BIGNUM *priv_key);
    int (*set_public)(EC_KEY *key, const EC_POINT *pub_key);
    int (*keygen)(EC_KEY *key);
    int (*compute_key)(unsigned char **pout, size_t *poutlen,
                       const EC_POINT *pub_key, const EC_KEY *ecdh);
    int (*sign)(int type, const unsigned char *dgst, int dlen, unsigned char
                *sig, unsigned int *siglen, const BIGNUM *kinv,
                const BIGNUM *r, EC_KEY *eckey);
    int (*sign_setup)(EC_KEY *eckey, BN_CTX *ctx_in, BIGNUM **kinvp,
                      BIGNUM **rp);
    ECDSA_SIG *(*sign_sig)(const unsigned char *dgst, int dgst_len,
                           const BIGNUM *in_kinv, const BIGNUM *in_r,
                           EC_KEY *eckey);

    int (*verify)(int type, const unsigned char *dgst, int dgst_len,
                  const unsigned char *sigbuf, int sig_len, EC_KEY *eckey);
    int (*verify_sig)(const unsigned char *dgst, int dgst_len,
                      const ECDSA_SIG *sig, EC_KEY *eckey);
};

#define EC_KEY_METHOD_DYNAMIC   1

EC_KEY *ossl_ec_key_new_method_int(OSSL_LIB_CTX *libctx, const char *propq,
                                   ENGINE *engine);

int ossl_ec_key_gen(EC_KEY *eckey);
int ossl_ecdh_compute_key(unsigned char **pout, size_t *poutlen,
                          const EC_POINT *pub_key, const EC_KEY *ecdh);
int ossl_ecdh_simple_compute_key(unsigned char **pout, size_t *poutlen,
                                 const EC_POINT *pub_key, const EC_KEY *ecdh);

struct ECDSA_SIG_st {
    BIGNUM *r;
    BIGNUM *s;
};

int ossl_ecdsa_sign_setup(EC_KEY *eckey, BN_CTX *ctx_in, BIGNUM **kinvp,
                          BIGNUM **rp);
int ossl_ecdsa_sign(int type, const unsigned char *dgst, int dlen,
                    unsigned char *sig, unsigned int *siglen,
                    const BIGNUM *kinv, const BIGNUM *r, EC_KEY *eckey);
ECDSA_SIG *ossl_ecdsa_sign_sig(const unsigned char *dgst, int dgst_len,
                               const BIGNUM *in_kinv, const BIGNUM *in_r,
                               EC_KEY *eckey);
int ossl_ecdsa_verify(int type, const unsigned char *dgst, int dgst_len,
                      const unsigned char *sigbuf, int sig_len, EC_KEY *eckey);
int ossl_ecdsa_verify_sig(const unsigned char *dgst, int dgst_len,
                          const ECDSA_SIG *sig, EC_KEY *eckey);
int ossl_ecdsa_simple_sign_setup(EC_KEY *eckey, BN_CTX *ctx_in, BIGNUM **kinvp,
                                 BIGNUM **rp);
ECDSA_SIG *ossl_ecdsa_simple_sign_sig(const unsigned char *dgst, int dgst_len,
                                      const BIGNUM *in_kinv, const BIGNUM *in_r,
                                      EC_KEY *eckey);
int ossl_ecdsa_simple_verify_sig(const unsigned char *dgst, int dgst_len,
                                 const ECDSA_SIG *sig, EC_KEY *eckey);


/*-
 * This functions computes a single point multiplication over the EC group,
 * using, at a high level, a Montgomery ladder with conditional swaps, with
 * various timing attack defenses.
 *
 * It performs either a fixed point multiplication
 *          (scalar * generator)
 * when point is NULL, or a variable point multiplication
 *          (scalar * point)
 * when point is not NULL.
 *
 * `scalar` cannot be NULL and should be in the range [0,n) otherwise all
 * constant time bets are off (where n is the cardinality of the EC group).
 *
 * This function expects `group->order` and `group->cardinality` to be well
 * defined and non-zero: it fails with an error code otherwise.
 *
 * NB: This says nothing about the constant-timeness of the ladder step
 * implementation (i.e., the default implementation is based on EC_POINT_add and
 * EC_POINT_dbl, which of course are not constant time themselves) or the
 * underlying multiprecision arithmetic.
 *
 * The product is stored in `r`.
 *
 * This is an internal function: callers are in charge of ensuring that the
 * input parameters `group`, `r`, `scalar` and `ctx` are not NULL.
 *
 * Returns 1 on success, 0 otherwise.
 */
int ossl_ec_scalar_mul_ladder(const EC_GROUP *group, EC_POINT *r,
                              const BIGNUM *scalar, const EC_POINT *point,
                              BN_CTX *ctx);

int ossl_ec_point_blind_coordinates(const EC_GROUP *group, EC_POINT *p,
                                    BN_CTX *ctx);

static ossl_inline int ec_point_ladder_pre(const EC_GROUP *group,
                                           EC_POINT *r, EC_POINT *s,
                                           EC_POINT *p, BN_CTX *ctx)
{
    if (group->meth->ladder_pre != NULL)
        return group->meth->ladder_pre(group, r, s, p, ctx);

    if (!EC_POINT_copy(s, p)
        || !EC_POINT_dbl(group, r, s, ctx))
        return 0;

    return 1;
}

static ossl_inline int ec_point_ladder_step(const EC_GROUP *group,
                                            EC_POINT *r, EC_POINT *s,
                                            EC_POINT *p, BN_CTX *ctx)
{
    if (group->meth->ladder_step != NULL)
        return group->meth->ladder_step(group, r, s, p, ctx);

    if (!EC_POINT_add(group, s, r, s, ctx)
        || !EC_POINT_dbl(group, r, r, ctx))
        return 0;

    return 1;

}

static ossl_inline int ec_point_ladder_post(const EC_GROUP *group,
                                            EC_POINT *r, EC_POINT *s,
                                            EC_POINT *p, BN_CTX *ctx)
{
    if (group->meth->ladder_post != NULL)
        return group->meth->ladder_post(group, r, s, p, ctx);

    return 1;
}
