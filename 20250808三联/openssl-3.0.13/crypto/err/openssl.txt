# Copyright 1999-2023 The OpenSSL Project Authors. All Rights Reserved.
#
# Licensed under the Apache License 2.0 (the "License").  You may not use
# this file except in compliance with the License.  You can obtain a copy
# in the file LICENSE in the source distribution or at
# https://www.openssl.org/source/license.html

#Reason codes
ASN1_R_ADDING_OBJECT:171:adding object
ASN1_R_ASN1_PARSE_ERROR:203:asn1 parse error
ASN1_R_ASN1_SIG_PARSE_ERROR:204:asn1 sig parse error
ASN1_R_AUX_ERROR:100:aux error
ASN1_R_BAD_OBJECT_HEADER:102:bad object header
ASN1_R_BAD_TEMPLATE:230:bad template
ASN1_R_BMPSTRING_IS_WRONG_LENGTH:214:bmpstring is wrong length
ASN1_R_BN_LIB:105:bn lib
ASN1_R_BOOLEAN_IS_WRONG_LENGTH:106:boolean is wrong length
ASN1_R_BUFFER_TOO_SMALL:107:buffer too small
ASN1_R_CIPHER_HAS_NO_OBJECT_IDENTIFIER:108:cipher has no object identifier
ASN1_R_CONTEXT_NOT_INITIALISED:217:context not initialised
ASN1_R_DATA_IS_WRONG:109:data is wrong
ASN1_R_DECODE_ERROR:110:decode error
ASN1_R_DEPTH_EXCEEDED:174:depth exceeded
ASN1_R_DIGEST_AND_KEY_TYPE_NOT_SUPPORTED:198:digest and key type not supported
ASN1_R_ENCODE_ERROR:112:encode error
ASN1_R_ERROR_GETTING_TIME:173:error getting time
ASN1_R_ERROR_LOADING_SECTION:172:error loading section
ASN1_R_ERROR_SETTING_CIPHER_PARAMS:114:error setting cipher params
ASN1_R_EXPECTING_AN_INTEGER:115:expecting an integer
ASN1_R_EXPECTING_AN_OBJECT:116:expecting an object
ASN1_R_EXPLICIT_LENGTH_MISMATCH:119:explicit length mismatch
ASN1_R_EXPLICIT_TAG_NOT_CONSTRUCTED:120:explicit tag not constructed
ASN1_R_FIELD_MISSING:121:field missing
ASN1_R_FIRST_NUM_TOO_LARGE:122:first num too large
ASN1_R_HEADER_TOO_LONG:123:header too long
ASN1_R_ILLEGAL_BITSTRING_FORMAT:175:illegal bitstring format
ASN1_R_ILLEGAL_BOOLEAN:176:illegal boolean
ASN1_R_ILLEGAL_CHARACTERS:124:illegal characters
ASN1_R_ILLEGAL_FORMAT:177:illegal format
ASN1_R_ILLEGAL_HEX:178:illegal hex
ASN1_R_ILLEGAL_IMPLICIT_TAG:179:illegal implicit tag
ASN1_R_ILLEGAL_INTEGER:180:illegal integer
ASN1_R_ILLEGAL_NEGATIVE_VALUE:226:illegal negative value
ASN1_R_ILLEGAL_NESTED_TAGGING:181:illegal nested tagging
ASN1_R_ILLEGAL_NULL:125:illegal null
ASN1_R_ILLEGAL_NULL_VALUE:182:illegal null value
ASN1_R_ILLEGAL_OBJECT:183:illegal object
ASN1_R_ILLEGAL_OPTIONAL_ANY:126:illegal optional any
ASN1_R_ILLEGAL_OPTIONS_ON_ITEM_TEMPLATE:170:illegal options on item template
ASN1_R_ILLEGAL_PADDING:221:illegal padding
ASN1_R_ILLEGAL_TAGGED_ANY:127:illegal tagged any
ASN1_R_ILLEGAL_TIME_VALUE:184:illegal time value
ASN1_R_ILLEGAL_ZERO_CONTENT:222:illegal zero content
ASN1_R_INTEGER_NOT_ASCII_FORMAT:185:integer not ascii format
ASN1_R_INTEGER_TOO_LARGE_FOR_LONG:128:integer too large for long
ASN1_R_INVALID_BIT_STRING_BITS_LEFT:220:invalid bit string bits left
ASN1_R_INVALID_BMPSTRING_LENGTH:129:invalid bmpstring length
ASN1_R_INVALID_DIGIT:130:invalid digit
ASN1_R_INVALID_MIME_TYPE:205:invalid mime type
ASN1_R_INVALID_MODIFIER:186:invalid modifier
ASN1_R_INVALID_NUMBER:187:invalid number
ASN1_R_INVALID_OBJECT_ENCODING:216:invalid object encoding
ASN1_R_INVALID_SCRYPT_PARAMETERS:227:invalid scrypt parameters
ASN1_R_INVALID_SEPARATOR:131:invalid separator
ASN1_R_INVALID_STRING_TABLE_VALUE:218:invalid string table value
ASN1_R_INVALID_UNIVERSALSTRING_LENGTH:133:invalid universalstring length
ASN1_R_INVALID_UTF8STRING:134:invalid utf8string
ASN1_R_INVALID_VALUE:219:invalid value
ASN1_R_LENGTH_TOO_LONG:231:length too long
ASN1_R_LIST_ERROR:188:list error
ASN1_R_MIME_NO_CONTENT_TYPE:206:mime no content type
ASN1_R_MIME_PARSE_ERROR:207:mime parse error
ASN1_R_MIME_SIG_PARSE_ERROR:208:mime sig parse error
ASN1_R_MISSING_EOC:137:missing eoc
ASN1_R_MISSING_SECOND_NUMBER:138:missing second number
ASN1_R_MISSING_VALUE:189:missing value
ASN1_R_MSTRING_NOT_UNIVERSAL:139:mstring not universal
ASN1_R_MSTRING_WRONG_TAG:140:mstring wrong tag
ASN1_R_NESTED_ASN1_STRING:197:nested asn1 string
ASN1_R_NESTED_TOO_DEEP:201:nested too deep
ASN1_R_NON_HEX_CHARACTERS:141:non hex characters
ASN1_R_NOT_ASCII_FORMAT:190:not ascii format
ASN1_R_NOT_ENOUGH_DATA:142:not enough data
ASN1_R_NO_CONTENT_TYPE:209:no content type
ASN1_R_NO_MATCHING_CHOICE_TYPE:143:no matching choice type
ASN1_R_NO_MULTIPART_BODY_FAILURE:210:no multipart body failure
ASN1_R_NO_MULTIPART_BOUNDARY:211:no multipart boundary
ASN1_R_NO_SIG_CONTENT_TYPE:212:no sig content type
ASN1_R_NULL_IS_WRONG_LENGTH:144:null is wrong length
ASN1_R_OBJECT_NOT_ASCII_FORMAT:191:object not ascii format
ASN1_R_ODD_NUMBER_OF_CHARS:145:odd number of chars
ASN1_R_SECOND_NUMBER_TOO_LARGE:147:second number too large
ASN1_R_SEQUENCE_LENGTH_MISMATCH:148:sequence length mismatch
ASN1_R_SEQUENCE_NOT_CONSTRUCTED:149:sequence not constructed
ASN1_R_SEQUENCE_OR_SET_NEEDS_CONFIG:192:sequence or set needs config
ASN1_R_SHORT_LINE:150:short line
ASN1_R_SIG_INVALID_MIME_TYPE:213:sig invalid mime type
ASN1_R_STREAMING_NOT_SUPPORTED:202:streaming not supported
ASN1_R_STRING_TOO_LONG:151:string too long
ASN1_R_STRING_TOO_SHORT:152:string too short
ASN1_R_THE_ASN1_OBJECT_IDENTIFIER_IS_NOT_KNOWN_FOR_THIS_MD:154:\
	the asn1 object identifier is not known for this md
ASN1_R_TIME_NOT_ASCII_FORMAT:193:time not ascii format
ASN1_R_TOO_LARGE:223:too large
ASN1_R_TOO_LONG:155:too long
ASN1_R_TOO_SMALL:224:too small
ASN1_R_TYPE_NOT_CONSTRUCTED:156:type not constructed
ASN1_R_TYPE_NOT_PRIMITIVE:195:type not primitive
ASN1_R_UNEXPECTED_EOC:159:unexpected eoc
ASN1_R_UNIVERSALSTRING_IS_WRONG_LENGTH:215:universalstring is wrong length
ASN1_R_UNKNOWN_DIGEST:229:unknown digest
ASN1_R_UNKNOWN_FORMAT:160:unknown format
ASN1_R_UNKNOWN_MESSAGE_DIGEST_ALGORITHM:161:unknown message digest algorithm
ASN1_R_UNKNOWN_OBJECT_TYPE:162:unknown object type
ASN1_R_UNKNOWN_PUBLIC_KEY_TYPE:163:unknown public key type
ASN1_R_UNKNOWN_SIGNATURE_ALGORITHM:199:unknown signature algorithm
ASN1_R_UNKNOWN_TAG:194:unknown tag
ASN1_R_UNSUPPORTED_ANY_DEFINED_BY_TYPE:164:unsupported any defined by type
ASN1_R_UNSUPPORTED_CIPHER:228:unsupported cipher
ASN1_R_UNSUPPORTED_PUBLIC_KEY_TYPE:167:unsupported public key type
ASN1_R_UNSUPPORTED_TYPE:196:unsupported type
ASN1_R_WRONG_INTEGER_TYPE:225:wrong integer type
ASN1_R_WRONG_PUBLIC_KEY_TYPE:200:wrong public key type
ASN1_R_WRONG_TAG:168:wrong tag
ASYNC_R_FAILED_TO_SET_POOL:101:failed to set pool
ASYNC_R_FAILED_TO_SWAP_CONTEXT:102:failed to swap context
ASYNC_R_INIT_FAILED:105:init failed
ASYNC_R_INVALID_POOL_SIZE:103:invalid pool size
BIO_R_ACCEPT_ERROR:100:accept error
BIO_R_ADDRINFO_ADDR_IS_NOT_AF_INET:141:addrinfo addr is not af inet
BIO_R_AMBIGUOUS_HOST_OR_SERVICE:129:ambiguous host or service
BIO_R_BAD_FOPEN_MODE:101:bad fopen mode
BIO_R_BROKEN_PIPE:124:broken pipe
BIO_R_CONNECT_ERROR:103:connect error
BIO_R_CONNECT_TIMEOUT:147:connect timeout
BIO_R_GETHOSTBYNAME_ADDR_IS_NOT_AF_INET:107:gethostbyname addr is not af inet
BIO_R_GETSOCKNAME_ERROR:132:getsockname error
BIO_R_GETSOCKNAME_TRUNCATED_ADDRESS:133:getsockname truncated address
BIO_R_GETTING_SOCKTYPE:134:getting socktype
BIO_R_INVALID_ARGUMENT:125:invalid argument
BIO_R_INVALID_SOCKET:135:invalid socket
BIO_R_IN_USE:123:in use
BIO_R_LENGTH_TOO_LONG:102:length too long
BIO_R_LISTEN_V6_ONLY:136:listen v6 only
BIO_R_LOOKUP_RETURNED_NOTHING:142:lookup returned nothing
BIO_R_MALFORMED_HOST_OR_SERVICE:130:malformed host or service
BIO_R_NBIO_CONNECT_ERROR:110:nbio connect error
BIO_R_NO_ACCEPT_ADDR_OR_SERVICE_SPECIFIED:143:\
	no accept addr or service specified
BIO_R_NO_HOSTNAME_OR_SERVICE_SPECIFIED:144:no hostname or service specified
BIO_R_NO_PORT_DEFINED:113:no port defined
BIO_R_NO_SUCH_FILE:128:no such file
BIO_R_TRANSFER_ERROR:104:transfer error
BIO_R_TRANSFER_TIMEOUT:105:transfer timeout
BIO_R_UNABLE_TO_BIND_SOCKET:117:unable to bind socket
BIO_R_UNABLE_TO_CREATE_SOCKET:118:unable to create socket
BIO_R_UNABLE_TO_KEEPALIVE:137:unable to keepalive
BIO_R_UNABLE_TO_LISTEN_SOCKET:119:unable to listen socket
BIO_R_UNABLE_TO_NODELAY:138:unable to nodelay
BIO_R_UNABLE_TO_REUSEADDR:139:unable to reuseaddr
BIO_R_UNAVAILABLE_IP_FAMILY:145:unavailable ip family
BIO_R_UNINITIALIZED:120:uninitialized
BIO_R_UNKNOWN_INFO_TYPE:140:unknown info type
BIO_R_UNSUPPORTED_IP_FAMILY:146:unsupported ip family
BIO_R_UNSUPPORTED_METHOD:121:unsupported method
BIO_R_UNSUPPORTED_PROTOCOL_FAMILY:131:unsupported protocol family
BIO_R_WRITE_TO_READ_ONLY_BIO:126:write to read only BIO
BIO_R_WSASTARTUP:122:WSAStartup
BN_R_ARG2_LT_ARG3:100:arg2 lt arg3
BN_R_BAD_RECIPROCAL:101:bad reciprocal
BN_R_BIGNUM_TOO_LONG:114:bignum too long
BN_R_BITS_TOO_SMALL:118:bits too small
BN_R_CALLED_WITH_EVEN_MODULUS:102:called with even modulus
BN_R_DIV_BY_ZERO:103:div by zero
BN_R_ENCODING_ERROR:104:encoding error
BN_R_EXPAND_ON_STATIC_BIGNUM_DATA:105:expand on static bignum data
BN_R_INPUT_NOT_REDUCED:110:input not reduced
BN_R_INVALID_LENGTH:106:invalid length
BN_R_INVALID_RANGE:115:invalid range
BN_R_INVALID_SHIFT:119:invalid shift
BN_R_NOT_A_SQUARE:111:not a square
BN_R_NOT_INITIALIZED:107:not initialized
BN_R_NO_INVERSE:108:no inverse
BN_R_NO_PRIME_CANDIDATE:121:no prime candidate
BN_R_NO_SOLUTION:116:no solution
BN_R_NO_SUITABLE_DIGEST:120:no suitable digest
BN_R_PRIVATE_KEY_TOO_LARGE:117:private key too large
BN_R_P_IS_NOT_PRIME:112:p is not prime
BN_R_TOO_MANY_ITERATIONS:113:too many iterations
BN_R_TOO_MANY_TEMPORARY_VARIABLES:109:too many temporary variables
CMP_R_ALGORITHM_NOT_SUPPORTED:139:algorithm not supported
CMP_R_BAD_CHECKAFTER_IN_POLLREP:167:bad checkafter in pollrep
CMP_R_BAD_REQUEST_ID:108:bad request id
CMP_R_CERTHASH_UNMATCHED:156:certhash unmatched
CMP_R_CERTID_NOT_FOUND:109:certid not found
CMP_R_CERTIFICATE_NOT_ACCEPTED:169:certificate not accepted
CMP_R_CERTIFICATE_NOT_FOUND:112:certificate not found
CMP_R_CERTREQMSG_NOT_FOUND:157:certreqmsg not found
CMP_R_CERTRESPONSE_NOT_FOUND:113:certresponse not found
CMP_R_CERT_AND_KEY_DO_NOT_MATCH:114:cert and key do not match
CMP_R_CHECKAFTER_OUT_OF_RANGE:181:checkafter out of range
CMP_R_ENCOUNTERED_KEYUPDATEWARNING:176:encountered keyupdatewarning
CMP_R_ENCOUNTERED_WAITING:162:encountered waiting
CMP_R_ERROR_CALCULATING_PROTECTION:115:error calculating protection
CMP_R_ERROR_CREATING_CERTCONF:116:error creating certconf
CMP_R_ERROR_CREATING_CERTREP:117:error creating certrep
CMP_R_ERROR_CREATING_CERTREQ:163:error creating certreq
CMP_R_ERROR_CREATING_ERROR:118:error creating error
CMP_R_ERROR_CREATING_GENM:119:error creating genm
CMP_R_ERROR_CREATING_GENP:120:error creating genp
CMP_R_ERROR_CREATING_PKICONF:122:error creating pkiconf
CMP_R_ERROR_CREATING_POLLREP:123:error creating pollrep
CMP_R_ERROR_CREATING_POLLREQ:124:error creating pollreq
CMP_R_ERROR_CREATING_RP:125:error creating rp
CMP_R_ERROR_CREATING_RR:126:error creating rr
CMP_R_ERROR_PARSING_PKISTATUS:107:error parsing pkistatus
CMP_R_ERROR_PROCESSING_MESSAGE:158:error processing message
CMP_R_ERROR_PROTECTING_MESSAGE:127:error protecting message
CMP_R_ERROR_SETTING_CERTHASH:128:error setting certhash
CMP_R_ERROR_UNEXPECTED_CERTCONF:160:error unexpected certconf
CMP_R_ERROR_VALIDATING_PROTECTION:140:error validating protection
CMP_R_ERROR_VALIDATING_SIGNATURE:171:error validating signature
CMP_R_FAILED_BUILDING_OWN_CHAIN:164:failed building own chain
CMP_R_FAILED_EXTRACTING_PUBKEY:141:failed extracting pubkey
CMP_R_FAILURE_OBTAINING_RANDOM:110:failure obtaining random
CMP_R_FAIL_INFO_OUT_OF_RANGE:129:fail info out of range
CMP_R_INVALID_ARGS:100:invalid args
CMP_R_INVALID_OPTION:174:invalid option
CMP_R_MISSING_CERTID:165:missing certid
CMP_R_MISSING_KEY_INPUT_FOR_CREATING_PROTECTION:130:\
	missing key input for creating protection
CMP_R_MISSING_KEY_USAGE_DIGITALSIGNATURE:142:missing key usage digitalsignature
CMP_R_MISSING_P10CSR:121:missing p10csr
CMP_R_MISSING_PBM_SECRET:166:missing pbm secret
CMP_R_MISSING_PRIVATE_KEY:131:missing private key
CMP_R_MISSING_PRIVATE_KEY_FOR_POPO:190:missing private key for popo
CMP_R_MISSING_PROTECTION:143:missing protection
CMP_R_MISSING_PUBLIC_KEY:183:missing public key
CMP_R_MISSING_REFERENCE_CERT:168:missing reference cert
CMP_R_MISSING_SECRET:178:missing secret
CMP_R_MISSING_SENDER_IDENTIFICATION:111:missing sender identification
CMP_R_MISSING_TRUST_ANCHOR:179:missing trust anchor
CMP_R_MISSING_TRUST_STORE:144:missing trust store
CMP_R_MULTIPLE_REQUESTS_NOT_SUPPORTED:161:multiple requests not supported
CMP_R_MULTIPLE_RESPONSES_NOT_SUPPORTED:170:multiple responses not supported
CMP_R_MULTIPLE_SAN_SOURCES:102:multiple san sources
CMP_R_NO_STDIO:194:no stdio
CMP_R_NO_SUITABLE_SENDER_CERT:145:no suitable sender cert
CMP_R_NULL_ARGUMENT:103:null argument
CMP_R_PKIBODY_ERROR:146:pkibody error
CMP_R_PKISTATUSINFO_NOT_FOUND:132:pkistatusinfo not found
CMP_R_POLLING_FAILED:172:polling failed
CMP_R_POTENTIALLY_INVALID_CERTIFICATE:147:potentially invalid certificate
CMP_R_RECEIVED_ERROR:180:received error
CMP_R_RECIPNONCE_UNMATCHED:148:recipnonce unmatched
CMP_R_REQUEST_NOT_ACCEPTED:149:request not accepted
CMP_R_REQUEST_REJECTED_BY_SERVER:182:request rejected by server
CMP_R_SENDER_GENERALNAME_TYPE_NOT_SUPPORTED:150:\
	sender generalname type not supported
CMP_R_SRVCERT_DOES_NOT_VALIDATE_MSG:151:srvcert does not validate msg
CMP_R_TOTAL_TIMEOUT:184:total timeout
CMP_R_TRANSACTIONID_UNMATCHED:152:transactionid unmatched
CMP_R_TRANSFER_ERROR:159:transfer error
CMP_R_UNEXPECTED_PKIBODY:133:unexpected pkibody
CMP_R_UNEXPECTED_PKISTATUS:185:unexpected pkistatus
CMP_R_UNEXPECTED_PVNO:153:unexpected pvno
CMP_R_UNKNOWN_ALGORITHM_ID:134:unknown algorithm id
CMP_R_UNKNOWN_CERT_TYPE:135:unknown cert type
CMP_R_UNKNOWN_PKISTATUS:186:unknown pkistatus
CMP_R_UNSUPPORTED_ALGORITHM:136:unsupported algorithm
CMP_R_UNSUPPORTED_KEY_TYPE:137:unsupported key type
CMP_R_UNSUPPORTED_PROTECTION_ALG_DHBASEDMAC:154:\
	unsupported protection alg dhbasedmac
CMP_R_VALUE_TOO_LARGE:175:value too large
CMP_R_VALUE_TOO_SMALL:177:value too small
CMP_R_WRONG_ALGORITHM_OID:138:wrong algorithm oid
CMP_R_WRONG_CERTID:189:wrong certid
CMP_R_WRONG_CERTID_IN_RP:187:wrong certid in rp
CMP_R_WRONG_PBM_VALUE:155:wrong pbm value
CMP_R_WRONG_RP_COMPONENT_COUNT:188:wrong rp component count
CMP_R_WRONG_SERIAL_IN_RP:173:wrong serial in rp
CMS_R_ADD_SIGNER_ERROR:99:add signer error
CMS_R_ATTRIBUTE_ERROR:161:attribute error
CMS_R_CERTIFICATE_ALREADY_PRESENT:175:certificate already present
CMS_R_CERTIFICATE_HAS_NO_KEYID:160:certificate has no keyid
CMS_R_CERTIFICATE_VERIFY_ERROR:100:certificate verify error
CMS_R_CIPHER_AEAD_SET_TAG_ERROR:184:cipher aead set tag error
CMS_R_CIPHER_GET_TAG:185:cipher get tag
CMS_R_CIPHER_INITIALISATION_ERROR:101:cipher initialisation error
CMS_R_CIPHER_PARAMETER_INITIALISATION_ERROR:102:\
	cipher parameter initialisation error
CMS_R_CMS_DATAFINAL_ERROR:103:cms datafinal error
CMS_R_CMS_LIB:104:cms lib
CMS_R_CONTENTIDENTIFIER_MISMATCH:170:contentidentifier mismatch
CMS_R_CONTENT_NOT_FOUND:105:content not found
CMS_R_CONTENT_TYPE_MISMATCH:171:content type mismatch
CMS_R_CONTENT_TYPE_NOT_COMPRESSED_DATA:106:content type not compressed data
CMS_R_CONTENT_TYPE_NOT_ENVELOPED_DATA:107:content type not enveloped data
CMS_R_CONTENT_TYPE_NOT_SIGNED_DATA:108:content type not signed data
CMS_R_CONTENT_VERIFY_ERROR:109:content verify error
CMS_R_CTRL_ERROR:110:ctrl error
CMS_R_CTRL_FAILURE:111:ctrl failure
CMS_R_DECODE_ERROR:187:decode error
CMS_R_DECRYPT_ERROR:112:decrypt error
CMS_R_ERROR_GETTING_PUBLIC_KEY:113:error getting public key
CMS_R_ERROR_READING_MESSAGEDIGEST_ATTRIBUTE:114:\
	error reading messagedigest attribute
CMS_R_ERROR_SETTING_KEY:115:error setting key
CMS_R_ERROR_SETTING_RECIPIENTINFO:116:error setting recipientinfo
CMS_R_ESS_SIGNING_CERTID_MISMATCH_ERROR:183:ess signing certid mismatch error
CMS_R_INVALID_ENCRYPTED_KEY_LENGTH:117:invalid encrypted key length
CMS_R_INVALID_KEY_ENCRYPTION_PARAMETER:176:invalid key encryption parameter
CMS_R_INVALID_KEY_LENGTH:118:invalid key length
CMS_R_INVALID_LABEL:190:invalid label
CMS_R_INVALID_OAEP_PARAMETERS:191:invalid oaep parameters
CMS_R_KDF_PARAMETER_ERROR:186:kdf parameter error
CMS_R_MD_BIO_INIT_ERROR:119:md bio init error
CMS_R_MESSAGEDIGEST_ATTRIBUTE_WRONG_LENGTH:120:\
	messagedigest attribute wrong length
CMS_R_MESSAGEDIGEST_WRONG_LENGTH:121:messagedigest wrong length
CMS_R_MSGSIGDIGEST_ERROR:172:msgsigdigest error
CMS_R_MSGSIGDIGEST_VERIFICATION_FAILURE:162:msgsigdigest verification failure
CMS_R_MSGSIGDIGEST_WRONG_LENGTH:163:msgsigdigest wrong length
CMS_R_NEED_ONE_SIGNER:164:need one signer
CMS_R_NOT_A_SIGNED_RECEIPT:165:not a signed receipt
CMS_R_NOT_ENCRYPTED_DATA:122:not encrypted data
CMS_R_NOT_KEK:123:not kek
CMS_R_NOT_KEY_AGREEMENT:181:not key agreement
CMS_R_NOT_KEY_TRANSPORT:124:not key transport
CMS_R_NOT_PWRI:177:not pwri
CMS_R_NOT_SUPPORTED_FOR_THIS_KEY_TYPE:125:not supported for this key type
CMS_R_NO_CIPHER:126:no cipher
CMS_R_NO_CONTENT:127:no content
CMS_R_NO_CONTENT_TYPE:173:no content type
CMS_R_NO_DEFAULT_DIGEST:128:no default digest
CMS_R_NO_DIGEST_SET:129:no digest set
CMS_R_NO_KEY:130:no key
CMS_R_NO_KEY_OR_CERT:174:no key or cert
CMS_R_NO_MATCHING_DIGEST:131:no matching digest
CMS_R_NO_MATCHING_RECIPIENT:132:no matching recipient
CMS_R_NO_MATCHING_SIGNATURE:166:no matching signature
CMS_R_NO_MSGSIGDIGEST:167:no msgsigdigest
CMS_R_NO_PASSWORD:178:no password
CMS_R_NO_PRIVATE_KEY:133:no private key
CMS_R_NO_PUBLIC_KEY:134:no public key
CMS_R_NO_RECEIPT_REQUEST:168:no receipt request
CMS_R_NO_SIGNERS:135:no signers
CMS_R_PEER_KEY_ERROR:188:peer key error
CMS_R_PRIVATE_KEY_DOES_NOT_MATCH_CERTIFICATE:136:\
	private key does not match certificate
CMS_R_RECEIPT_DECODE_ERROR:169:receipt decode error
CMS_R_RECIPIENT_ERROR:137:recipient error
CMS_R_SHARED_INFO_ERROR:189:shared info error
CMS_R_SIGNER_CERTIFICATE_NOT_FOUND:138:signer certificate not found
CMS_R_SIGNFINAL_ERROR:139:signfinal error
CMS_R_SMIME_TEXT_ERROR:140:smime text error
CMS_R_STORE_INIT_ERROR:141:store init error
CMS_R_TYPE_NOT_COMPRESSED_DATA:142:type not compressed data
CMS_R_TYPE_NOT_DATA:143:type not data
CMS_R_TYPE_NOT_DIGESTED_DATA:144:type not digested data
CMS_R_TYPE_NOT_ENCRYPTED_DATA:145:type not encrypted data
CMS_R_TYPE_NOT_ENVELOPED_DATA:146:type not enveloped data
CMS_R_UNABLE_TO_FINALIZE_CONTEXT:147:unable to finalize context
CMS_R_UNKNOWN_CIPHER:148:unknown cipher
CMS_R_UNKNOWN_DIGEST_ALGORITHM:149:unknown digest algorithm
CMS_R_UNKNOWN_ID:150:unknown id
CMS_R_UNSUPPORTED_COMPRESSION_ALGORITHM:151:unsupported compression algorithm
CMS_R_UNSUPPORTED_CONTENT_ENCRYPTION_ALGORITHM:194:\
	unsupported content encryption algorithm
CMS_R_UNSUPPORTED_CONTENT_TYPE:152:unsupported content type
CMS_R_UNSUPPORTED_ENCRYPTION_TYPE:192:unsupported encryption type
CMS_R_UNSUPPORTED_KEK_ALGORITHM:153:unsupported kek algorithm
CMS_R_UNSUPPORTED_KEY_ENCRYPTION_ALGORITHM:179:\
	unsupported key encryption algorithm
CMS_R_UNSUPPORTED_LABEL_SOURCE:193:unsupported label source
CMS_R_UNSUPPORTED_RECIPIENTINFO_TYPE:155:unsupported recipientinfo type
CMS_R_UNSUPPORTED_RECIPIENT_TYPE:154:unsupported recipient type
CMS_R_UNSUPPORTED_SIGNATURE_ALGORITHM:195:unsupported signature algorithm
CMS_R_UNSUPPORTED_TYPE:156:unsupported type
CMS_R_UNWRAP_ERROR:157:unwrap error
CMS_R_UNWRAP_FAILURE:180:unwrap failure
CMS_R_VERIFICATION_FAILURE:158:verification failure
CMS_R_WRAP_ERROR:159:wrap error
COMP_R_ZLIB_DEFLATE_ERROR:99:zlib deflate error
COMP_R_ZLIB_INFLATE_ERROR:100:zlib inflate error
COMP_R_ZLIB_NOT_SUPPORTED:101:zlib not supported
CONF_R_ERROR_LOADING_DSO:110:error loading dso
CONF_R_INVALID_PRAGMA:122:invalid pragma
CONF_R_LIST_CANNOT_BE_NULL:115:list cannot be null
CONF_R_MANDATORY_BRACES_IN_VARIABLE_EXPANSION:123:\
	mandatory braces in variable expansion
CONF_R_MISSING_CLOSE_SQUARE_BRACKET:100:missing close square bracket
CONF_R_MISSING_EQUAL_SIGN:101:missing equal sign
CONF_R_MISSING_INIT_FUNCTION:112:missing init function
CONF_R_MODULE_INITIALIZATION_ERROR:109:module initialization error
CONF_R_NO_CLOSE_BRACE:102:no close brace
CONF_R_NO_CONF:105:no conf
CONF_R_NO_CONF_OR_ENVIRONMENT_VARIABLE:106:no conf or environment variable
CONF_R_NO_SECTION:107:no section
CONF_R_NO_SUCH_FILE:114:no such file
CONF_R_NO_VALUE:108:no value
CONF_R_NUMBER_TOO_LARGE:121:number too large
CONF_R_OPENSSL_CONF_REFERENCES_MISSING_SECTION:124:\
	openssl conf references missing section
CONF_R_RECURSIVE_DIRECTORY_INCLUDE:111:recursive directory include
CONF_R_RECURSIVE_SECTION_REFERENCE:126:recursive section reference
CONF_R_RELATIVE_PATH:125:relative path
CONF_R_SSL_COMMAND_SECTION_EMPTY:117:ssl command section empty
CONF_R_SSL_COMMAND_SECTION_NOT_FOUND:118:ssl command section not found
CONF_R_SSL_SECTION_EMPTY:119:ssl section empty
CONF_R_SSL_SECTION_NOT_FOUND:120:ssl section not found
CONF_R_UNABLE_TO_CREATE_NEW_SECTION:103:unable to create new section
CONF_R_UNKNOWN_MODULE_NAME:113:unknown module name
CONF_R_VARIABLE_EXPANSION_TOO_LONG:116:variable expansion too long
CONF_R_VARIABLE_HAS_NO_VALUE:104:variable has no value
CRMF_R_BAD_PBM_ITERATIONCOUNT:100:bad pbm iterationcount
CRMF_R_CRMFERROR:102:crmferror
CRMF_R_ERROR:103:error
CRMF_R_ERROR_DECODING_CERTIFICATE:104:error decoding certificate
CRMF_R_ERROR_DECRYPTING_CERTIFICATE:105:error decrypting certificate
CRMF_R_ERROR_DECRYPTING_SYMMETRIC_KEY:106:error decrypting symmetric key
CRMF_R_FAILURE_OBTAINING_RANDOM:107:failure obtaining random
CRMF_R_ITERATIONCOUNT_BELOW_100:108:iterationcount below 100
CRMF_R_MALFORMED_IV:101:malformed iv
CRMF_R_NULL_ARGUMENT:109:null argument
CRMF_R_POPOSKINPUT_NOT_SUPPORTED:113:poposkinput not supported
CRMF_R_POPO_INCONSISTENT_PUBLIC_KEY:117:popo inconsistent public key
CRMF_R_POPO_MISSING:121:popo missing
CRMF_R_POPO_MISSING_PUBLIC_KEY:118:popo missing public key
CRMF_R_POPO_MISSING_SUBJECT:119:popo missing subject
CRMF_R_POPO_RAVERIFIED_NOT_ACCEPTED:120:popo raverified not accepted
CRMF_R_SETTING_MAC_ALGOR_FAILURE:110:setting mac algor failure
CRMF_R_SETTING_OWF_ALGOR_FAILURE:111:setting owf algor failure
CRMF_R_UNSUPPORTED_ALGORITHM:112:unsupported algorithm
CRMF_R_UNSUPPORTED_CIPHER:114:unsupported cipher
CRMF_R_UNSUPPORTED_METHOD_FOR_CREATING_POPO:115:\
	unsupported method for creating popo
CRMF_R_UNSUPPORTED_POPO_METHOD:116:unsupported popo method
CRYPTO_R_BAD_ALGORITHM_NAME:117:bad algorithm name
CRYPTO_R_CONFLICTING_NAMES:118:conflicting names
CRYPTO_R_HEX_STRING_TOO_SHORT:121:hex string too short
CRYPTO_R_ILLEGAL_HEX_DIGIT:102:illegal hex digit
CRYPTO_R_INSUFFICIENT_DATA_SPACE:106:insufficient data space
CRYPTO_R_INSUFFICIENT_PARAM_SIZE:107:insufficient param size
CRYPTO_R_INSUFFICIENT_SECURE_DATA_SPACE:108:insufficient secure data space
CRYPTO_R_INVALID_NEGATIVE_VALUE:122:invalid negative value
CRYPTO_R_INVALID_NULL_ARGUMENT:109:invalid null argument
CRYPTO_R_INVALID_OSSL_PARAM_TYPE:110:invalid ossl param type
CRYPTO_R_ODD_NUMBER_OF_DIGITS:103:odd number of digits
CRYPTO_R_PROVIDER_ALREADY_EXISTS:104:provider already exists
CRYPTO_R_PROVIDER_SECTION_ERROR:105:provider section error
CRYPTO_R_RANDOM_SECTION_ERROR:119:random section error
CRYPTO_R_SECURE_MALLOC_FAILURE:111:secure malloc failure
CRYPTO_R_STRING_TOO_LONG:112:string too long
CRYPTO_R_TOO_MANY_BYTES:113:too many bytes
CRYPTO_R_TOO_MANY_RECORDS:114:too many records
CRYPTO_R_TOO_SMALL_BUFFER:116:too small buffer
CRYPTO_R_UNKNOWN_NAME_IN_RANDOM_SECTION:120:unknown name in random section
CRYPTO_R_ZERO_LENGTH_NUMBER:115:zero length number
CT_R_BASE64_DECODE_ERROR:108:base64 decode error
CT_R_INVALID_LOG_ID_LENGTH:100:invalid log id length
CT_R_LOG_CONF_INVALID:109:log conf invalid
CT_R_LOG_CONF_INVALID_KEY:110:log conf invalid key
CT_R_LOG_CONF_MISSING_DESCRIPTION:111:log conf missing description
CT_R_LOG_CONF_MISSING_KEY:112:log conf missing key
CT_R_LOG_KEY_INVALID:113:log key invalid
CT_R_SCT_FUTURE_TIMESTAMP:116:sct future timestamp
CT_R_SCT_INVALID:104:sct invalid
CT_R_SCT_INVALID_SIGNATURE:107:sct invalid signature
CT_R_SCT_LIST_INVALID:105:sct list invalid
CT_R_SCT_LOG_ID_MISMATCH:114:sct log id mismatch
CT_R_SCT_NOT_SET:106:sct not set
CT_R_SCT_UNSUPPORTED_VERSION:115:sct unsupported version
CT_R_UNRECOGNIZED_SIGNATURE_NID:101:unrecognized signature nid
CT_R_UNSUPPORTED_ENTRY_TYPE:102:unsupported entry type
CT_R_UNSUPPORTED_VERSION:103:unsupported version
DH_R_BAD_FFC_PARAMETERS:127:bad ffc parameters
DH_R_BAD_GENERATOR:101:bad generator
DH_R_BN_DECODE_ERROR:109:bn decode error
DH_R_BN_ERROR:106:bn error
DH_R_CHECK_INVALID_J_VALUE:115:check invalid j value
DH_R_CHECK_INVALID_Q_VALUE:116:check invalid q value
DH_R_CHECK_PUBKEY_INVALID:122:check pubkey invalid
DH_R_CHECK_PUBKEY_TOO_LARGE:123:check pubkey too large
DH_R_CHECK_PUBKEY_TOO_SMALL:124:check pubkey too small
DH_R_CHECK_P_NOT_PRIME:117:check p not prime
DH_R_CHECK_P_NOT_SAFE_PRIME:118:check p not safe prime
DH_R_CHECK_Q_NOT_PRIME:119:check q not prime
DH_R_DECODE_ERROR:104:decode error
DH_R_INVALID_PARAMETER_NAME:110:invalid parameter name
DH_R_INVALID_PARAMETER_NID:114:invalid parameter nid
DH_R_INVALID_PUBKEY:102:invalid public key
DH_R_INVALID_SECRET:128:invalid secret
DH_R_KDF_PARAMETER_ERROR:112:kdf parameter error
DH_R_KEYS_NOT_SET:108:keys not set
DH_R_MISSING_PUBKEY:125:missing pubkey
DH_R_MODULUS_TOO_LARGE:103:modulus too large
DH_R_MODULUS_TOO_SMALL:126:modulus too small
DH_R_NOT_SUITABLE_GENERATOR:120:not suitable generator
DH_R_NO_PARAMETERS_SET:107:no parameters set
DH_R_NO_PRIVATE_VALUE:100:no private value
DH_R_PARAMETER_ENCODING_ERROR:105:parameter encoding error
DH_R_PEER_KEY_ERROR:111:peer key error
DH_R_Q_TOO_LARGE:130:q too large
DH_R_SHARED_INFO_ERROR:113:shared info error
DH_R_UNABLE_TO_CHECK_GENERATOR:121:unable to check generator
DSA_R_BAD_FFC_PARAMETERS:114:bad ffc parameters
DSA_R_BAD_Q_VALUE:102:bad q value
DSA_R_BN_DECODE_ERROR:108:bn decode error
DSA_R_BN_ERROR:109:bn error
DSA_R_DECODE_ERROR:104:decode error
DSA_R_INVALID_DIGEST_TYPE:106:invalid digest type
DSA_R_INVALID_PARAMETERS:112:invalid parameters
DSA_R_MISSING_PARAMETERS:101:missing parameters
DSA_R_MISSING_PRIVATE_KEY:111:missing private key
DSA_R_MODULUS_TOO_LARGE:103:modulus too large
DSA_R_NO_PARAMETERS_SET:107:no parameters set
DSA_R_PARAMETER_ENCODING_ERROR:105:parameter encoding error
DSA_R_P_NOT_PRIME:115:p not prime
DSA_R_Q_NOT_PRIME:113:q not prime
DSA_R_SEED_LEN_SMALL:110:seed_len is less than the length of q
DSA_R_TOO_MANY_RETRIES:116:too many retries
DSO_R_CTRL_FAILED:100:control command failed
DSO_R_DSO_ALREADY_LOADED:110:dso already loaded
DSO_R_EMPTY_FILE_STRUCTURE:113:empty file structure
DSO_R_FAILURE:114:failure
DSO_R_FILENAME_TOO_BIG:101:filename too big
DSO_R_FINISH_FAILED:102:cleanup method function failed
DSO_R_INCORRECT_FILE_SYNTAX:115:incorrect file syntax
DSO_R_LOAD_FAILED:103:could not load the shared library
DSO_R_NAME_TRANSLATION_FAILED:109:name translation failed
DSO_R_NO_FILENAME:111:no filename
DSO_R_NULL_HANDLE:104:a null shared library handle was used
DSO_R_SET_FILENAME_FAILED:112:set filename failed
DSO_R_STACK_ERROR:105:the meth_data stack is corrupt
DSO_R_SYM_FAILURE:106:could not bind to the requested symbol name
DSO_R_UNLOAD_FAILED:107:could not unload the shared library
DSO_R_UNSUPPORTED:108:functionality not supported
EC_R_ASN1_ERROR:115:asn1 error
EC_R_BAD_SIGNATURE:156:bad signature
EC_R_BIGNUM_OUT_OF_RANGE:144:bignum out of range
EC_R_BUFFER_TOO_SMALL:100:buffer too small
EC_R_CANNOT_INVERT:165:cannot invert
EC_R_COORDINATES_OUT_OF_RANGE:146:coordinates out of range
EC_R_CURVE_DOES_NOT_SUPPORT_ECDH:160:curve does not support ecdh
EC_R_CURVE_DOES_NOT_SUPPORT_ECDSA:170:curve does not support ecdsa
EC_R_CURVE_DOES_NOT_SUPPORT_SIGNING:159:curve does not support signing
EC_R_DECODE_ERROR:142:decode error
EC_R_DISCRIMINANT_IS_ZERO:118:discriminant is zero
EC_R_EC_GROUP_NEW_BY_NAME_FAILURE:119:ec group new by name failure
EC_R_EXPLICIT_PARAMS_NOT_SUPPORTED:127:explicit params not supported
EC_R_FAILED_MAKING_PUBLIC_KEY:166:failed making public key
EC_R_FIELD_TOO_LARGE:143:field too large
EC_R_GF2M_NOT_SUPPORTED:147:gf2m not supported
EC_R_GROUP2PKPARAMETERS_FAILURE:120:group2pkparameters failure
EC_R_I2D_ECPKPARAMETERS_FAILURE:121:i2d ecpkparameters failure
EC_R_INCOMPATIBLE_OBJECTS:101:incompatible objects
EC_R_INVALID_A:168:invalid a
EC_R_INVALID_ARGUMENT:112:invalid argument
EC_R_INVALID_B:169:invalid b
EC_R_INVALID_COFACTOR:171:invalid cofactor
EC_R_INVALID_COMPRESSED_POINT:110:invalid compressed point
EC_R_INVALID_COMPRESSION_BIT:109:invalid compression bit
EC_R_INVALID_CURVE:141:invalid curve
EC_R_INVALID_DIGEST:151:invalid digest
EC_R_INVALID_DIGEST_TYPE:138:invalid digest type
EC_R_INVALID_ENCODING:102:invalid encoding
EC_R_INVALID_FIELD:103:invalid field
EC_R_INVALID_FORM:104:invalid form
EC_R_INVALID_GENERATOR:173:invalid generator
EC_R_INVALID_GROUP_ORDER:122:invalid group order
EC_R_INVALID_KEY:116:invalid key
EC_R_INVALID_LENGTH:117:invalid length
EC_R_INVALID_NAMED_GROUP_CONVERSION:174:invalid named group conversion
EC_R_INVALID_OUTPUT_LENGTH:161:invalid output length
EC_R_INVALID_P:172:invalid p
EC_R_INVALID_PEER_KEY:133:invalid peer key
EC_R_INVALID_PENTANOMIAL_BASIS:132:invalid pentanomial basis
EC_R_INVALID_PRIVATE_KEY:123:invalid private key
EC_R_INVALID_SEED:175:invalid seed
EC_R_INVALID_TRINOMIAL_BASIS:137:invalid trinomial basis
EC_R_KDF_PARAMETER_ERROR:148:kdf parameter error
EC_R_KEYS_NOT_SET:140:keys not set
EC_R_LADDER_POST_FAILURE:136:ladder post failure
EC_R_LADDER_PRE_FAILURE:153:ladder pre failure
EC_R_LADDER_STEP_FAILURE:162:ladder step failure
EC_R_MISSING_OID:167:missing OID
EC_R_MISSING_PARAMETERS:124:missing parameters
EC_R_MISSING_PRIVATE_KEY:125:missing private key
EC_R_NEED_NEW_SETUP_VALUES:157:need new setup values
EC_R_NOT_A_NIST_PRIME:135:not a NIST prime
EC_R_NOT_IMPLEMENTED:126:not implemented
EC_R_NOT_INITIALIZED:111:not initialized
EC_R_NO_PARAMETERS_SET:139:no parameters set
EC_R_NO_PRIVATE_VALUE:154:no private value
EC_R_OPERATION_NOT_SUPPORTED:152:operation not supported
EC_R_PASSED_NULL_PARAMETER:134:passed null parameter
EC_R_PEER_KEY_ERROR:149:peer key error
EC_R_POINT_ARITHMETIC_FAILURE:155:point arithmetic failure
EC_R_POINT_AT_INFINITY:106:point at infinity
EC_R_POINT_COORDINATES_BLIND_FAILURE:163:point coordinates blind failure
EC_R_POINT_IS_NOT_ON_CURVE:107:point is not on curve
EC_R_RANDOM_NUMBER_GENERATION_FAILED:158:random number generation failed
EC_R_SHARED_INFO_ERROR:150:shared info error
EC_R_SLOT_FULL:108:slot full
EC_R_TOO_MANY_RETRIES:176:too many retries
EC_R_UNDEFINED_GENERATOR:113:undefined generator
EC_R_UNDEFINED_ORDER:128:undefined order
EC_R_UNKNOWN_COFACTOR:164:unknown cofactor
EC_R_UNKNOWN_GROUP:129:unknown group
EC_R_UNKNOWN_ORDER:114:unknown order
EC_R_UNSUPPORTED_FIELD:131:unsupported field
EC_R_WRONG_CURVE_PARAMETERS:145:wrong curve parameters
EC_R_WRONG_ORDER:130:wrong order
ENGINE_R_ALREADY_LOADED:100:already loaded
ENGINE_R_ARGUMENT_IS_NOT_A_NUMBER:133:argument is not a number
ENGINE_R_CMD_NOT_EXECUTABLE:134:cmd not executable
ENGINE_R_COMMAND_TAKES_INPUT:135:command takes input
ENGINE_R_COMMAND_TAKES_NO_INPUT:136:command takes no input
ENGINE_R_CONFLICTING_ENGINE_ID:103:conflicting engine id
ENGINE_R_CTRL_COMMAND_NOT_IMPLEMENTED:119:ctrl command not implemented
ENGINE_R_DSO_FAILURE:104:DSO failure
ENGINE_R_DSO_NOT_FOUND:132:dso not found
ENGINE_R_ENGINES_SECTION_ERROR:148:engines section error
ENGINE_R_ENGINE_CONFIGURATION_ERROR:102:engine configuration error
ENGINE_R_ENGINE_IS_NOT_IN_LIST:105:engine is not in the list
ENGINE_R_ENGINE_SECTION_ERROR:149:engine section error
ENGINE_R_FAILED_LOADING_PRIVATE_KEY:128:failed loading private key
ENGINE_R_FAILED_LOADING_PUBLIC_KEY:129:failed loading public key
ENGINE_R_FINISH_FAILED:106:finish failed
ENGINE_R_ID_OR_NAME_MISSING:108:'id' or 'name' missing
ENGINE_R_INIT_FAILED:109:init failed
ENGINE_R_INTERNAL_LIST_ERROR:110:internal list error
ENGINE_R_INVALID_ARGUMENT:143:invalid argument
ENGINE_R_INVALID_CMD_NAME:137:invalid cmd name
ENGINE_R_INVALID_CMD_NUMBER:138:invalid cmd number
ENGINE_R_INVALID_INIT_VALUE:151:invalid init value
ENGINE_R_INVALID_STRING:150:invalid string
ENGINE_R_NOT_INITIALISED:117:not initialised
ENGINE_R_NOT_LOADED:112:not loaded
ENGINE_R_NO_CONTROL_FUNCTION:120:no control function
ENGINE_R_NO_INDEX:144:no index
ENGINE_R_NO_LOAD_FUNCTION:125:no load function
ENGINE_R_NO_REFERENCE:130:no reference
ENGINE_R_NO_SUCH_ENGINE:116:no such engine
ENGINE_R_UNIMPLEMENTED_CIPHER:146:unimplemented cipher
ENGINE_R_UNIMPLEMENTED_DIGEST:147:unimplemented digest
ENGINE_R_UNIMPLEMENTED_PUBLIC_KEY_METHOD:101:unimplemented public key method
ENGINE_R_VERSION_INCOMPATIBILITY:145:version incompatibility
ESS_R_EMPTY_ESS_CERT_ID_LIST:107:empty ess cert id list
ESS_R_ESS_CERT_DIGEST_ERROR:103:ess cert digest error
ESS_R_ESS_CERT_ID_NOT_FOUND:104:ess cert id not found
ESS_R_ESS_CERT_ID_WRONG_ORDER:105:ess cert id wrong order
ESS_R_ESS_DIGEST_ALG_UNKNOWN:106:ess digest alg unknown
ESS_R_ESS_SIGNING_CERTIFICATE_ERROR:102:ess signing certificate error
ESS_R_ESS_SIGNING_CERT_ADD_ERROR:100:ess signing cert add error
ESS_R_ESS_SIGNING_CERT_V2_ADD_ERROR:101:ess signing cert v2 add error
ESS_R_MISSING_SIGNING_CERTIFICATE_ATTRIBUTE:108:\
	missing signing certificate attribute
EVP_R_AES_KEY_SETUP_FAILED:143:aes key setup failed
EVP_R_ARIA_KEY_SETUP_FAILED:176:aria key setup failed
EVP_R_BAD_ALGORITHM_NAME:200:bad algorithm name
EVP_R_BAD_DECRYPT:100:bad decrypt
EVP_R_BAD_KEY_LENGTH:195:bad key length
EVP_R_BUFFER_TOO_SMALL:155:buffer too small
EVP_R_CACHE_CONSTANTS_FAILED:225:cache constants failed
EVP_R_CAMELLIA_KEY_SETUP_FAILED:157:camellia key setup failed
EVP_R_CANNOT_GET_PARAMETERS:197:cannot get parameters
EVP_R_CANNOT_SET_PARAMETERS:198:cannot set parameters
EVP_R_CIPHER_NOT_GCM_MODE:184:cipher not gcm mode
EVP_R_CIPHER_PARAMETER_ERROR:122:cipher parameter error
EVP_R_COMMAND_NOT_SUPPORTED:147:command not supported
EVP_R_CONFLICTING_ALGORITHM_NAME:201:conflicting algorithm name
EVP_R_COPY_ERROR:173:copy error
EVP_R_CTRL_NOT_IMPLEMENTED:132:ctrl not implemented
EVP_R_CTRL_OPERATION_NOT_IMPLEMENTED:133:ctrl operation not implemented
EVP_R_DATA_NOT_MULTIPLE_OF_BLOCK_LENGTH:138:data not multiple of block length
EVP_R_DECODE_ERROR:114:decode error
EVP_R_DEFAULT_QUERY_PARSE_ERROR:210:default query parse error
EVP_R_DIFFERENT_KEY_TYPES:101:different key types
EVP_R_DIFFERENT_PARAMETERS:153:different parameters
EVP_R_ERROR_LOADING_SECTION:165:error loading section
EVP_R_EXPECTING_AN_HMAC_KEY:174:expecting an hmac key
EVP_R_EXPECTING_AN_RSA_KEY:127:expecting an rsa key
EVP_R_EXPECTING_A_DH_KEY:128:expecting a dh key
EVP_R_EXPECTING_A_DSA_KEY:129:expecting a dsa key
EVP_R_EXPECTING_A_ECX_KEY:219:expecting an ecx key
EVP_R_EXPECTING_A_EC_KEY:142:expecting an ec key
EVP_R_EXPECTING_A_POLY1305_KEY:164:expecting a poly1305 key
EVP_R_EXPECTING_A_SIPHASH_KEY:175:expecting a siphash key
EVP_R_FINAL_ERROR:188:final error
EVP_R_GENERATE_ERROR:214:generate error
EVP_R_GET_RAW_KEY_FAILED:182:get raw key failed
EVP_R_ILLEGAL_SCRYPT_PARAMETERS:171:illegal scrypt parameters
EVP_R_INACCESSIBLE_DOMAIN_PARAMETERS:204:inaccessible domain parameters
EVP_R_INACCESSIBLE_KEY:203:inaccessible key
EVP_R_INITIALIZATION_ERROR:134:initialization error
EVP_R_INPUT_NOT_INITIALIZED:111:input not initialized
EVP_R_INVALID_CUSTOM_LENGTH:185:invalid custom length
EVP_R_INVALID_DIGEST:152:invalid digest
EVP_R_INVALID_IV_LENGTH:194:invalid iv length
EVP_R_INVALID_KEY:163:invalid key
EVP_R_INVALID_KEY_LENGTH:130:invalid key length
EVP_R_INVALID_LENGTH:221:invalid length
EVP_R_INVALID_NULL_ALGORITHM:218:invalid null algorithm
EVP_R_INVALID_OPERATION:148:invalid operation
EVP_R_INVALID_PROVIDER_FUNCTIONS:193:invalid provider functions
EVP_R_INVALID_SALT_LENGTH:186:invalid salt length
EVP_R_INVALID_SECRET_LENGTH:223:invalid secret length
EVP_R_INVALID_SEED_LENGTH:220:invalid seed length
EVP_R_INVALID_VALUE:222:invalid value
EVP_R_KEYMGMT_EXPORT_FAILURE:205:keymgmt export failure
EVP_R_KEY_SETUP_FAILED:180:key setup failed
EVP_R_LOCKING_NOT_SUPPORTED:213:locking not supported
EVP_R_MEMORY_LIMIT_EXCEEDED:172:memory limit exceeded
EVP_R_MESSAGE_DIGEST_IS_NULL:159:message digest is null
EVP_R_METHOD_NOT_SUPPORTED:144:method not supported
EVP_R_MISSING_PARAMETERS:103:missing parameters
EVP_R_NOT_ABLE_TO_COPY_CTX:190:not able to copy ctx
EVP_R_NOT_XOF_OR_INVALID_LENGTH:178:not XOF or invalid length
EVP_R_NO_CIPHER_SET:131:no cipher set
EVP_R_NO_DEFAULT_DIGEST:158:no default digest
EVP_R_NO_DIGEST_SET:139:no digest set
EVP_R_NO_IMPORT_FUNCTION:206:no import function
EVP_R_NO_KEYMGMT_AVAILABLE:199:no keymgmt available
EVP_R_NO_KEYMGMT_PRESENT:196:no keymgmt present
EVP_R_NO_KEY_SET:154:no key set
EVP_R_NO_OPERATION_SET:149:no operation set
EVP_R_NULL_MAC_PKEY_CTX:208:null mac pkey ctx
EVP_R_ONLY_ONESHOT_SUPPORTED:177:only oneshot supported
EVP_R_OPERATION_NOT_INITIALIZED:151:operation not initialized
EVP_R_OPERATION_NOT_SUPPORTED_FOR_THIS_KEYTYPE:150:\
	operation not supported for this keytype
EVP_R_OUTPUT_WOULD_OVERFLOW:202:output would overflow
EVP_R_PARAMETER_TOO_LARGE:187:parameter too large
EVP_R_PARTIALLY_OVERLAPPING:162:partially overlapping buffers
EVP_R_PBKDF2_ERROR:181:pbkdf2 error
EVP_R_PKEY_APPLICATION_ASN1_METHOD_ALREADY_REGISTERED:179:\
	pkey application asn1 method already registered
EVP_R_PRIVATE_KEY_DECODE_ERROR:145:private key decode error
EVP_R_PRIVATE_KEY_ENCODE_ERROR:146:private key encode error
EVP_R_PUBLIC_KEY_NOT_RSA:106:public key not rsa
EVP_R_SETTING_XOF_FAILED:227:setting xof failed
EVP_R_SET_DEFAULT_PROPERTY_FAILURE:209:set default property failure
EVP_R_TOO_MANY_RECORDS:183:too many records
EVP_R_UNABLE_TO_ENABLE_LOCKING:212:unable to enable locking
EVP_R_UNABLE_TO_GET_MAXIMUM_REQUEST_SIZE:215:unable to get maximum request size
EVP_R_UNABLE_TO_GET_RANDOM_STRENGTH:216:unable to get random strength
EVP_R_UNABLE_TO_LOCK_CONTEXT:211:unable to lock context
EVP_R_UNABLE_TO_SET_CALLBACKS:217:unable to set callbacks
EVP_R_UNKNOWN_CIPHER:160:unknown cipher
EVP_R_UNKNOWN_DIGEST:161:unknown digest
EVP_R_UNKNOWN_KEY_TYPE:207:unknown key type
EVP_R_UNKNOWN_OPTION:169:unknown option
EVP_R_UNKNOWN_PBE_ALGORITHM:121:unknown pbe algorithm
EVP_R_UNSUPPORTED_ALGORITHM:156:unsupported algorithm
EVP_R_UNSUPPORTED_CIPHER:107:unsupported cipher
EVP_R_UNSUPPORTED_KEYLENGTH:123:unsupported keylength
EVP_R_UNSUPPORTED_KEY_DERIVATION_FUNCTION:124:\
	unsupported key derivation function
EVP_R_UNSUPPORTED_KEY_SIZE:108:unsupported key size
EVP_R_UNSUPPORTED_KEY_TYPE:224:unsupported key type
EVP_R_UNSUPPORTED_NUMBER_OF_ROUNDS:135:unsupported number of rounds
EVP_R_UNSUPPORTED_PRF:125:unsupported prf
EVP_R_UNSUPPORTED_PRIVATE_KEY_ALGORITHM:118:unsupported private key algorithm
EVP_R_UNSUPPORTED_SALT_TYPE:126:unsupported salt type
EVP_R_UPDATE_ERROR:189:update error
EVP_R_WRAP_MODE_NOT_ALLOWED:170:wrap mode not allowed
EVP_R_WRONG_FINAL_BLOCK_LENGTH:109:wrong final block length
EVP_R_XTS_DATA_UNIT_IS_TOO_LARGE:191:xts data unit is too large
EVP_R_XTS_DUPLICATED_KEYS:192:xts duplicated keys
HTTP_R_ASN1_LEN_EXCEEDS_MAX_RESP_LEN:108:asn1 len exceeds max resp len
HTTP_R_CONNECT_FAILURE:100:connect failure
HTTP_R_ERROR_PARSING_ASN1_LENGTH:109:error parsing asn1 length
HTTP_R_ERROR_PARSING_CONTENT_LENGTH:119:error parsing content length
HTTP_R_ERROR_PARSING_URL:101:error parsing url
HTTP_R_ERROR_RECEIVING:103:error receiving
HTTP_R_ERROR_SENDING:102:error sending
HTTP_R_FAILED_READING_DATA:128:failed reading data
HTTP_R_HEADER_PARSE_ERROR:126:header parse error
HTTP_R_INCONSISTENT_CONTENT_LENGTH:120:inconsistent content length
HTTP_R_INVALID_PORT_NUMBER:123:invalid port number
HTTP_R_INVALID_URL_PATH:125:invalid url path
HTTP_R_INVALID_URL_SCHEME:124:invalid url scheme
HTTP_R_MAX_RESP_LEN_EXCEEDED:117:max resp len exceeded
HTTP_R_MISSING_ASN1_ENCODING:110:missing asn1 encoding
HTTP_R_MISSING_CONTENT_TYPE:121:missing content type
HTTP_R_MISSING_REDIRECT_LOCATION:111:missing redirect location
HTTP_R_RECEIVED_ERROR:105:received error
HTTP_R_RECEIVED_WRONG_HTTP_VERSION:106:received wrong http version
HTTP_R_REDIRECTION_FROM_HTTPS_TO_HTTP:112:redirection from https to http
HTTP_R_REDIRECTION_NOT_ENABLED:116:redirection not enabled
HTTP_R_RESPONSE_LINE_TOO_LONG:113:response line too long
HTTP_R_RESPONSE_PARSE_ERROR:104:response parse error
HTTP_R_RETRY_TIMEOUT:129:retry timeout
HTTP_R_SERVER_CANCELED_CONNECTION:127:server canceled connection
HTTP_R_SOCK_NOT_SUPPORTED:122:sock not supported
HTTP_R_STATUS_CODE_UNSUPPORTED:114:status code unsupported
HTTP_R_TLS_NOT_ENABLED:107:tls not enabled
HTTP_R_TOO_MANY_REDIRECTIONS:115:too many redirections
HTTP_R_UNEXPECTED_CONTENT_TYPE:118:unexpected content type
OBJ_R_OID_EXISTS:102:oid exists
OBJ_R_UNKNOWN_NID:101:unknown nid
OBJ_R_UNKNOWN_OBJECT_NAME:103:unknown object name
OCSP_R_CERTIFICATE_VERIFY_ERROR:101:certificate verify error
OCSP_R_DIGEST_ERR:102:digest err
OCSP_R_DIGEST_NAME_ERR:106:digest name err
OCSP_R_DIGEST_SIZE_ERR:107:digest size err
OCSP_R_ERROR_IN_NEXTUPDATE_FIELD:122:error in nextupdate field
OCSP_R_ERROR_IN_THISUPDATE_FIELD:123:error in thisupdate field
OCSP_R_MISSING_OCSPSIGNING_USAGE:103:missing ocspsigning usage
OCSP_R_NEXTUPDATE_BEFORE_THISUPDATE:124:nextupdate before thisupdate
OCSP_R_NOT_BASIC_RESPONSE:104:not basic response
OCSP_R_NO_CERTIFICATES_IN_CHAIN:105:no certificates in chain
OCSP_R_NO_RESPONSE_DATA:108:no response data
OCSP_R_NO_REVOKED_TIME:109:no revoked time
OCSP_R_NO_SIGNER_KEY:130:no signer key
OCSP_R_PRIVATE_KEY_DOES_NOT_MATCH_CERTIFICATE:110:\
	private key does not match certificate
OCSP_R_REQUEST_NOT_SIGNED:128:request not signed
OCSP_R_RESPONSE_CONTAINS_NO_REVOCATION_DATA:111:\
	response contains no revocation data
OCSP_R_ROOT_CA_NOT_TRUSTED:112:root ca not trusted
OCSP_R_SIGNATURE_FAILURE:117:signature failure
OCSP_R_SIGNER_CERTIFICATE_NOT_FOUND:118:signer certificate not found
OCSP_R_STATUS_EXPIRED:125:status expired
OCSP_R_STATUS_NOT_YET_VALID:126:status not yet valid
OCSP_R_STATUS_TOO_OLD:127:status too old
OCSP_R_UNKNOWN_MESSAGE_DIGEST:119:unknown message digest
OCSP_R_UNKNOWN_NID:120:unknown nid
OCSP_R_UNSUPPORTED_REQUESTORNAME_TYPE:129:unsupported requestorname type
OSSL_DECODER_R_COULD_NOT_DECODE_OBJECT:101:could not decode object
OSSL_DECODER_R_DECODER_NOT_FOUND:102:decoder not found
OSSL_DECODER_R_MISSING_GET_PARAMS:100:missing get params
OSSL_ENCODER_R_ENCODER_NOT_FOUND:101:encoder not found
OSSL_ENCODER_R_INCORRECT_PROPERTY_QUERY:100:incorrect property query
OSSL_ENCODER_R_MISSING_GET_PARAMS:102:missing get params
OSSL_STORE_R_AMBIGUOUS_CONTENT_TYPE:107:ambiguous content type
OSSL_STORE_R_BAD_PASSWORD_READ:115:bad password read
OSSL_STORE_R_ERROR_VERIFYING_PKCS12_MAC:113:error verifying pkcs12 mac
OSSL_STORE_R_FINGERPRINT_SIZE_DOES_NOT_MATCH_DIGEST:121:\
	fingerprint size does not match digest
OSSL_STORE_R_INVALID_SCHEME:106:invalid scheme
OSSL_STORE_R_IS_NOT_A:112:is not a
OSSL_STORE_R_LOADER_INCOMPLETE:116:loader incomplete
OSSL_STORE_R_LOADING_STARTED:117:loading started
OSSL_STORE_R_NOT_A_CERTIFICATE:100:not a certificate
OSSL_STORE_R_NOT_A_CRL:101:not a crl
OSSL_STORE_R_NOT_A_NAME:103:not a name
OSSL_STORE_R_NOT_A_PRIVATE_KEY:102:not a private key
OSSL_STORE_R_NOT_A_PUBLIC_KEY:122:not a public key
OSSL_STORE_R_NOT_PARAMETERS:104:not parameters
OSSL_STORE_R_NO_LOADERS_FOUND:123:no loaders found
OSSL_STORE_R_PASSPHRASE_CALLBACK_ERROR:114:passphrase callback error
OSSL_STORE_R_PATH_MUST_BE_ABSOLUTE:108:path must be absolute
OSSL_STORE_R_SEARCH_ONLY_SUPPORTED_FOR_DIRECTORIES:119:\
	search only supported for directories
OSSL_STORE_R_UI_PROCESS_INTERRUPTED_OR_CANCELLED:109:\
	ui process interrupted or cancelled
OSSL_STORE_R_UNREGISTERED_SCHEME:105:unregistered scheme
OSSL_STORE_R_UNSUPPORTED_CONTENT_TYPE:110:unsupported content type
OSSL_STORE_R_UNSUPPORTED_OPERATION:118:unsupported operation
OSSL_STORE_R_UNSUPPORTED_SEARCH_TYPE:120:unsupported search type
OSSL_STORE_R_URI_AUTHORITY_UNSUPPORTED:111:uri authority unsupported
PEM_R_BAD_BASE64_DECODE:100:bad base64 decode
PEM_R_BAD_DECRYPT:101:bad decrypt
PEM_R_BAD_END_LINE:102:bad end line
PEM_R_BAD_IV_CHARS:103:bad iv chars
PEM_R_BAD_MAGIC_NUMBER:116:bad magic number
PEM_R_BAD_PASSWORD_READ:104:bad password read
PEM_R_BAD_VERSION_NUMBER:117:bad version number
PEM_R_BIO_WRITE_FAILURE:118:bio write failure
PEM_R_CIPHER_IS_NULL:127:cipher is null
PEM_R_ERROR_CONVERTING_PRIVATE_KEY:115:error converting private key
PEM_R_EXPECTING_DSS_KEY_BLOB:131:expecting dss key blob
PEM_R_EXPECTING_PRIVATE_KEY_BLOB:119:expecting private key blob
PEM_R_EXPECTING_PUBLIC_KEY_BLOB:120:expecting public key blob
PEM_R_EXPECTING_RSA_KEY_BLOB:132:expecting rsa key blob
PEM_R_HEADER_TOO_LONG:128:header too long
PEM_R_INCONSISTENT_HEADER:121:inconsistent header
PEM_R_KEYBLOB_HEADER_PARSE_ERROR:122:keyblob header parse error
PEM_R_KEYBLOB_TOO_SHORT:123:keyblob too short
PEM_R_MISSING_DEK_IV:129:missing dek iv
PEM_R_NOT_DEK_INFO:105:not dek info
PEM_R_NOT_ENCRYPTED:106:not encrypted
PEM_R_NOT_PROC_TYPE:107:not proc type
PEM_R_NO_START_LINE:108:no start line
PEM_R_PROBLEMS_GETTING_PASSWORD:109:problems getting password
PEM_R_PVK_DATA_TOO_SHORT:124:pvk data too short
PEM_R_PVK_TOO_SHORT:125:pvk too short
PEM_R_READ_KEY:111:read key
PEM_R_SHORT_HEADER:112:short header
PEM_R_UNEXPECTED_DEK_IV:130:unexpected dek iv
PEM_R_UNSUPPORTED_CIPHER:113:unsupported cipher
PEM_R_UNSUPPORTED_ENCRYPTION:114:unsupported encryption
PEM_R_UNSUPPORTED_KEY_COMPONENTS:126:unsupported key components
PEM_R_UNSUPPORTED_PUBLIC_KEY_TYPE:110:unsupported public key type
PKCS12_R_CANT_PACK_STRUCTURE:100:cant pack structure
PKCS12_R_CONTENT_TYPE_NOT_DATA:121:content type not data
PKCS12_R_DECODE_ERROR:101:decode error
PKCS12_R_ENCODE_ERROR:102:encode error
PKCS12_R_ENCRYPT_ERROR:103:encrypt error
PKCS12_R_ERROR_SETTING_ENCRYPTED_DATA_TYPE:120:error setting encrypted data type
PKCS12_R_INVALID_NULL_ARGUMENT:104:invalid null argument
PKCS12_R_INVALID_NULL_PKCS12_POINTER:105:invalid null pkcs12 pointer
PKCS12_R_INVALID_TYPE:112:invalid type
PKCS12_R_IV_GEN_ERROR:106:iv gen error
PKCS12_R_KEY_GEN_ERROR:107:key gen error
PKCS12_R_MAC_ABSENT:108:mac absent
PKCS12_R_MAC_GENERATION_ERROR:109:mac generation error
PKCS12_R_MAC_SETUP_ERROR:110:mac setup error
PKCS12_R_MAC_STRING_SET_ERROR:111:mac string set error
PKCS12_R_MAC_VERIFY_FAILURE:113:mac verify failure
PKCS12_R_PARSE_ERROR:114:parse error
PKCS12_R_PKCS12_CIPHERFINAL_ERROR:116:pkcs12 cipherfinal error
PKCS12_R_UNKNOWN_DIGEST_ALGORITHM:118:unknown digest algorithm
PKCS12_R_UNSUPPORTED_PKCS12_MODE:119:unsupported pkcs12 mode
PKCS7_R_CERTIFICATE_VERIFY_ERROR:117:certificate verify error
PKCS7_R_CIPHER_HAS_NO_OBJECT_IDENTIFIER:144:cipher has no object identifier
PKCS7_R_CIPHER_NOT_INITIALIZED:116:cipher not initialized
PKCS7_R_CONTENT_AND_DATA_PRESENT:118:content and data present
PKCS7_R_CTRL_ERROR:152:ctrl error
PKCS7_R_DECRYPT_ERROR:119:decrypt error
PKCS7_R_DIGEST_FAILURE:101:digest failure
PKCS7_R_ENCRYPTION_CTRL_FAILURE:149:encryption ctrl failure
PKCS7_R_ENCRYPTION_NOT_SUPPORTED_FOR_THIS_KEY_TYPE:150:\
	encryption not supported for this key type
PKCS7_R_ERROR_ADDING_RECIPIENT:120:error adding recipient
PKCS7_R_ERROR_SETTING_CIPHER:121:error setting cipher
PKCS7_R_INVALID_NULL_POINTER:143:invalid null pointer
PKCS7_R_INVALID_SIGNED_DATA_TYPE:155:invalid signed data type
PKCS7_R_NO_CONTENT:122:no content
PKCS7_R_NO_DEFAULT_DIGEST:151:no default digest
PKCS7_R_NO_MATCHING_DIGEST_TYPE_FOUND:154:no matching digest type found
PKCS7_R_NO_RECIPIENT_MATCHES_CERTIFICATE:115:no recipient matches certificate
PKCS7_R_NO_SIGNATURES_ON_DATA:123:no signatures on data
PKCS7_R_NO_SIGNERS:142:no signers
PKCS7_R_OPERATION_NOT_SUPPORTED_ON_THIS_TYPE:104:\
	operation not supported on this type
PKCS7_R_PKCS7_ADD_SIGNATURE_ERROR:124:pkcs7 add signature error
PKCS7_R_PKCS7_ADD_SIGNER_ERROR:153:pkcs7 add signer error
PKCS7_R_PKCS7_DATASIGN:145:pkcs7 datasign
PKCS7_R_PRIVATE_KEY_DOES_NOT_MATCH_CERTIFICATE:127:\
	private key does not match certificate
PKCS7_R_SIGNATURE_FAILURE:105:signature failure
PKCS7_R_SIGNER_CERTIFICATE_NOT_FOUND:128:signer certificate not found
PKCS7_R_SIGNING_CTRL_FAILURE:147:signing ctrl failure
PKCS7_R_SIGNING_NOT_SUPPORTED_FOR_THIS_KEY_TYPE:148:\
	signing not supported for this key type
PKCS7_R_SMIME_TEXT_ERROR:129:smime text error
PKCS7_R_UNABLE_TO_FIND_CERTIFICATE:106:unable to find certificate
PKCS7_R_UNABLE_TO_FIND_MEM_BIO:107:unable to find mem bio
PKCS7_R_UNABLE_TO_FIND_MESSAGE_DIGEST:108:unable to find message digest
PKCS7_R_UNKNOWN_DIGEST_TYPE:109:unknown digest type
PKCS7_R_UNKNOWN_OPERATION:110:unknown operation
PKCS7_R_UNSUPPORTED_CIPHER_TYPE:111:unsupported cipher type
PKCS7_R_UNSUPPORTED_CONTENT_TYPE:112:unsupported content type
PKCS7_R_WRONG_CONTENT_TYPE:113:wrong content type
PKCS7_R_WRONG_PKCS7_TYPE:114:wrong pkcs7 type
PROP_R_NAME_TOO_LONG:100:name too long
PROP_R_NOT_AN_ASCII_CHARACTER:101:not an ascii character
PROP_R_NOT_AN_HEXADECIMAL_DIGIT:102:not an hexadecimal digit
PROP_R_NOT_AN_IDENTIFIER:103:not an identifier
PROP_R_NOT_AN_OCTAL_DIGIT:104:not an octal digit
PROP_R_NOT_A_DECIMAL_DIGIT:105:not a decimal digit
PROP_R_NO_MATCHING_STRING_DELIMITER:106:no matching string delimiter
PROP_R_NO_VALUE:107:no value
PROP_R_PARSE_FAILED:108:parse failed
PROP_R_STRING_TOO_LONG:109:string too long
PROP_R_TRAILING_CHARACTERS:110:trailing characters
PROV_R_ADDITIONAL_INPUT_TOO_LONG:184:additional input too long
PROV_R_ALGORITHM_MISMATCH:173:algorithm mismatch
PROV_R_ALREADY_INSTANTIATED:185:already instantiated
PROV_R_BAD_DECRYPT:100:bad decrypt
PROV_R_BAD_ENCODING:141:bad encoding
PROV_R_BAD_LENGTH:142:bad length
PROV_R_BAD_TLS_CLIENT_VERSION:161:bad tls client version
PROV_R_BN_ERROR:160:bn error
PROV_R_CIPHER_OPERATION_FAILED:102:cipher operation failed
PROV_R_DERIVATION_FUNCTION_INIT_FAILED:205:derivation function init failed
PROV_R_DIGEST_NOT_ALLOWED:174:digest not allowed
PROV_R_ENTROPY_SOURCE_STRENGTH_TOO_WEAK:186:entropy source strength too weak
PROV_R_ERROR_INSTANTIATING_DRBG:188:error instantiating drbg
PROV_R_ERROR_RETRIEVING_ENTROPY:189:error retrieving entropy
PROV_R_ERROR_RETRIEVING_NONCE:190:error retrieving nonce
PROV_R_FAILED_DURING_DERIVATION:164:failed during derivation
PROV_R_FAILED_TO_CREATE_LOCK:180:failed to create lock
PROV_R_FAILED_TO_DECRYPT:162:failed to decrypt
PROV_R_FAILED_TO_GENERATE_KEY:121:failed to generate key
PROV_R_FAILED_TO_GET_PARAMETER:103:failed to get parameter
PROV_R_FAILED_TO_SET_PARAMETER:104:failed to set parameter
PROV_R_FAILED_TO_SIGN:175:failed to sign
PROV_R_FIPS_MODULE_CONDITIONAL_ERROR:227:fips module conditional error
PROV_R_FIPS_MODULE_ENTERING_ERROR_STATE:224:fips module entering error state
PROV_R_FIPS_MODULE_IN_ERROR_STATE:225:fips module in error state
PROV_R_GENERATE_ERROR:191:generate error
PROV_R_ILLEGAL_OR_UNSUPPORTED_PADDING_MODE:165:\
	illegal or unsupported padding mode
PROV_R_INDICATOR_INTEGRITY_FAILURE:210:indicator integrity failure
PROV_R_INSUFFICIENT_DRBG_STRENGTH:181:insufficient drbg strength
PROV_R_INVALID_AAD:108:invalid aad
PROV_R_INVALID_CONFIG_DATA:211:invalid config data
PROV_R_INVALID_CONSTANT_LENGTH:157:invalid constant length
PROV_R_INVALID_CURVE:176:invalid curve
PROV_R_INVALID_CUSTOM_LENGTH:111:invalid custom length
PROV_R_INVALID_DATA:115:invalid data
PROV_R_INVALID_DIGEST:122:invalid digest
PROV_R_INVALID_DIGEST_LENGTH:166:invalid digest length
PROV_R_INVALID_DIGEST_SIZE:218:invalid digest size
PROV_R_INVALID_INPUT_LENGTH:230:invalid input length
PROV_R_INVALID_ITERATION_COUNT:123:invalid iteration count
PROV_R_INVALID_IV_LENGTH:109:invalid iv length
PROV_R_INVALID_KEY:158:invalid key
PROV_R_INVALID_KEY_LENGTH:105:invalid key length
PROV_R_INVALID_MAC:151:invalid mac
PROV_R_INVALID_MGF1_MD:167:invalid mgf1 md
PROV_R_INVALID_MODE:125:invalid mode
PROV_R_INVALID_OUTPUT_LENGTH:217:invalid output length
PROV_R_INVALID_PADDING_MODE:168:invalid padding mode
PROV_R_INVALID_PUBINFO:198:invalid pubinfo
PROV_R_INVALID_SALT_LENGTH:112:invalid salt length
PROV_R_INVALID_SEED_LENGTH:154:invalid seed length
PROV_R_INVALID_SIGNATURE_SIZE:179:invalid signature size
PROV_R_INVALID_STATE:212:invalid state
PROV_R_INVALID_TAG:110:invalid tag
PROV_R_INVALID_TAG_LENGTH:118:invalid tag length
PROV_R_INVALID_UKM_LENGTH:200:invalid ukm length
PROV_R_INVALID_X931_DIGEST:170:invalid x931 digest
PROV_R_IN_ERROR_STATE:192:in error state
PROV_R_KEY_SETUP_FAILED:101:key setup failed
PROV_R_KEY_SIZE_TOO_SMALL:171:key size too small
PROV_R_LENGTH_TOO_LARGE:202:length too large
PROV_R_MISMATCHING_DOMAIN_PARAMETERS:203:mismatching domain parameters
PROV_R_MISSING_CEK_ALG:144:missing cek alg
PROV_R_MISSING_CIPHER:155:missing cipher
PROV_R_MISSING_CONFIG_DATA:213:missing config data
PROV_R_MISSING_CONSTANT:156:missing constant
PROV_R_MISSING_KEY:128:missing key
PROV_R_MISSING_MAC:150:missing mac
PROV_R_MISSING_MESSAGE_DIGEST:129:missing message digest
PROV_R_MISSING_OID:209:missing OID
PROV_R_MISSING_PASS:130:missing pass
PROV_R_MISSING_SALT:131:missing salt
PROV_R_MISSING_SECRET:132:missing secret
PROV_R_MISSING_SEED:140:missing seed
PROV_R_MISSING_SESSION_ID:133:missing session id
PROV_R_MISSING_TYPE:134:missing type
PROV_R_MISSING_XCGHASH:135:missing xcghash
PROV_R_MODULE_INTEGRITY_FAILURE:214:module integrity failure
PROV_R_NOT_A_PRIVATE_KEY:221:not a private key
PROV_R_NOT_A_PUBLIC_KEY:220:not a public key
PROV_R_NOT_INSTANTIATED:193:not instantiated
PROV_R_NOT_PARAMETERS:226:not parameters
PROV_R_NOT_SUPPORTED:136:not supported
PROV_R_NOT_XOF_OR_INVALID_LENGTH:113:not xof or invalid length
PROV_R_NO_KEY_SET:114:no key set
PROV_R_NO_PARAMETERS_SET:177:no parameters set
PROV_R_OPERATION_NOT_SUPPORTED_FOR_THIS_KEYTYPE:178:\
	operation not supported for this keytype
PROV_R_OUTPUT_BUFFER_TOO_SMALL:106:output buffer too small
PROV_R_PARENT_CANNOT_GENERATE_RANDOM_NUMBERS:228:\
	parent cannot generate random numbers
PROV_R_PARENT_CANNOT_SUPPLY_ENTROPY_SEED:187:parent cannot supply entropy seed
PROV_R_PARENT_LOCKING_NOT_ENABLED:182:parent locking not enabled
PROV_R_PARENT_STRENGTH_TOO_WEAK:194:parent strength too weak
PROV_R_PATH_MUST_BE_ABSOLUTE:219:path must be absolute
PROV_R_PERSONALISATION_STRING_TOO_LONG:195:personalisation string too long
PROV_R_PSS_SALTLEN_TOO_SMALL:172:pss saltlen too small
PROV_R_REQUEST_TOO_LARGE_FOR_DRBG:196:request too large for drbg
PROV_R_REQUIRE_CTR_MODE_CIPHER:206:require ctr mode cipher
PROV_R_RESEED_ERROR:197:reseed error
PROV_R_SEARCH_ONLY_SUPPORTED_FOR_DIRECTORIES:222:\
	search only supported for directories
PROV_R_SEED_SOURCES_MUST_NOT_HAVE_A_PARENT:229:\
	seed sources must not have a parent
PROV_R_SELF_TEST_KAT_FAILURE:215:self test kat failure
PROV_R_SELF_TEST_POST_FAILURE:216:self test post failure
PROV_R_TAG_NOT_NEEDED:120:tag not needed
PROV_R_TAG_NOT_SET:119:tag not set
PROV_R_TOO_MANY_RECORDS:126:too many records
PROV_R_UNABLE_TO_FIND_CIPHERS:207:unable to find ciphers
PROV_R_UNABLE_TO_GET_PARENT_STRENGTH:199:unable to get parent strength
PROV_R_UNABLE_TO_GET_PASSPHRASE:159:unable to get passphrase
PROV_R_UNABLE_TO_INITIALISE_CIPHERS:208:unable to initialise ciphers
PROV_R_UNABLE_TO_LOAD_SHA256:147:unable to load sha256
PROV_R_UNABLE_TO_LOCK_PARENT:201:unable to lock parent
PROV_R_UNABLE_TO_RESEED:204:unable to reseed
PROV_R_UNSUPPORTED_CEK_ALG:145:unsupported cek alg
PROV_R_UNSUPPORTED_KEY_SIZE:153:unsupported key size
PROV_R_UNSUPPORTED_MAC_TYPE:137:unsupported mac type
PROV_R_UNSUPPORTED_NUMBER_OF_ROUNDS:152:unsupported number of rounds
PROV_R_URI_AUTHORITY_UNSUPPORTED:223:uri authority unsupported
PROV_R_VALUE_ERROR:138:value error
PROV_R_WRONG_FINAL_BLOCK_LENGTH:107:wrong final block length
PROV_R_WRONG_OUTPUT_BUFFER_SIZE:139:wrong output buffer size
PROV_R_XOF_DIGESTS_NOT_ALLOWED:183:xof digests not allowed
PROV_R_XTS_DATA_UNIT_IS_TOO_LARGE:148:xts data unit is too large
PROV_R_XTS_DUPLICATED_KEYS:149:xts duplicated keys
RAND_R_ADDITIONAL_INPUT_TOO_LONG:102:additional input too long
RAND_R_ALREADY_INSTANTIATED:103:already instantiated
RAND_R_ARGUMENT_OUT_OF_RANGE:105:argument out of range
RAND_R_CANNOT_OPEN_FILE:121:Cannot open file
RAND_R_DRBG_ALREADY_INITIALIZED:129:drbg already initialized
RAND_R_DRBG_NOT_INITIALISED:104:drbg not initialised
RAND_R_ENTROPY_INPUT_TOO_LONG:106:entropy input too long
RAND_R_ENTROPY_OUT_OF_RANGE:124:entropy out of range
RAND_R_ERROR_ENTROPY_POOL_WAS_IGNORED:127:error entropy pool was ignored
RAND_R_ERROR_INITIALISING_DRBG:107:error initialising drbg
RAND_R_ERROR_INSTANTIATING_DRBG:108:error instantiating drbg
RAND_R_ERROR_RETRIEVING_ADDITIONAL_INPUT:109:error retrieving additional input
RAND_R_ERROR_RETRIEVING_ENTROPY:110:error retrieving entropy
RAND_R_ERROR_RETRIEVING_NONCE:111:error retrieving nonce
RAND_R_FAILED_TO_CREATE_LOCK:126:failed to create lock
RAND_R_FUNC_NOT_IMPLEMENTED:101:Function not implemented
RAND_R_FWRITE_ERROR:123:Error writing file
RAND_R_GENERATE_ERROR:112:generate error
RAND_R_INSUFFICIENT_DRBG_STRENGTH:139:insufficient drbg strength
RAND_R_INTERNAL_ERROR:113:internal error
RAND_R_IN_ERROR_STATE:114:in error state
RAND_R_NOT_A_REGULAR_FILE:122:Not a regular file
RAND_R_NOT_INSTANTIATED:115:not instantiated
RAND_R_NO_DRBG_IMPLEMENTATION_SELECTED:128:no drbg implementation selected
RAND_R_PARENT_LOCKING_NOT_ENABLED:130:parent locking not enabled
RAND_R_PARENT_STRENGTH_TOO_WEAK:131:parent strength too weak
RAND_R_PERSONALISATION_STRING_TOO_LONG:116:personalisation string too long
RAND_R_PREDICTION_RESISTANCE_NOT_SUPPORTED:133:\
	prediction resistance not supported
RAND_R_PRNG_NOT_SEEDED:100:PRNG not seeded
RAND_R_RANDOM_POOL_OVERFLOW:125:random pool overflow
RAND_R_RANDOM_POOL_UNDERFLOW:134:random pool underflow
RAND_R_REQUEST_TOO_LARGE_FOR_DRBG:117:request too large for drbg
RAND_R_RESEED_ERROR:118:reseed error
RAND_R_SELFTEST_FAILURE:119:selftest failure
RAND_R_TOO_LITTLE_NONCE_REQUESTED:135:too little nonce requested
RAND_R_TOO_MUCH_NONCE_REQUESTED:136:too much nonce requested
RAND_R_UNABLE_TO_CREATE_DRBG:143:unable to create drbg
RAND_R_UNABLE_TO_FETCH_DRBG:144:unable to fetch drbg
RAND_R_UNABLE_TO_GET_PARENT_RESEED_PROP_COUNTER:141:\
	unable to get parent reseed prop counter
RAND_R_UNABLE_TO_GET_PARENT_STRENGTH:138:unable to get parent strength
RAND_R_UNABLE_TO_LOCK_PARENT:140:unable to lock parent
RAND_R_UNSUPPORTED_DRBG_FLAGS:132:unsupported drbg flags
RAND_R_UNSUPPORTED_DRBG_TYPE:120:unsupported drbg type
RSA_R_ALGORITHM_MISMATCH:100:algorithm mismatch
RSA_R_BAD_E_VALUE:101:bad e value
RSA_R_BAD_FIXED_HEADER_DECRYPT:102:bad fixed header decrypt
RSA_R_BAD_PAD_BYTE_COUNT:103:bad pad byte count
RSA_R_BAD_SIGNATURE:104:bad signature
RSA_R_BLOCK_TYPE_IS_NOT_01:106:block type is not 01
RSA_R_BLOCK_TYPE_IS_NOT_02:107:block type is not 02
RSA_R_DATA_GREATER_THAN_MOD_LEN:108:data greater than mod len
RSA_R_DATA_TOO_LARGE:109:data too large
RSA_R_DATA_TOO_LARGE_FOR_KEY_SIZE:110:data too large for key size
RSA_R_DATA_TOO_LARGE_FOR_MODULUS:132:data too large for modulus
RSA_R_DATA_TOO_SMALL:111:data too small
RSA_R_DATA_TOO_SMALL_FOR_KEY_SIZE:122:data too small for key size
RSA_R_DIGEST_DOES_NOT_MATCH:158:digest does not match
RSA_R_DIGEST_NOT_ALLOWED:145:digest not allowed
RSA_R_DIGEST_TOO_BIG_FOR_RSA_KEY:112:digest too big for rsa key
RSA_R_DMP1_NOT_CONGRUENT_TO_D:124:dmp1 not congruent to d
RSA_R_DMQ1_NOT_CONGRUENT_TO_D:125:dmq1 not congruent to d
RSA_R_D_E_NOT_CONGRUENT_TO_1:123:d e not congruent to 1
RSA_R_FIRST_OCTET_INVALID:133:first octet invalid
RSA_R_ILLEGAL_OR_UNSUPPORTED_PADDING_MODE:144:\
	illegal or unsupported padding mode
RSA_R_INVALID_DIGEST:157:invalid digest
RSA_R_INVALID_DIGEST_LENGTH:143:invalid digest length
RSA_R_INVALID_HEADER:137:invalid header
RSA_R_INVALID_KEYPAIR:171:invalid keypair
RSA_R_INVALID_KEY_LENGTH:173:invalid key length
RSA_R_INVALID_LABEL:160:invalid label
RSA_R_INVALID_LENGTH:181:invalid length
RSA_R_INVALID_MESSAGE_LENGTH:131:invalid message length
RSA_R_INVALID_MGF1_MD:156:invalid mgf1 md
RSA_R_INVALID_MODULUS:174:invalid modulus
RSA_R_INVALID_MULTI_PRIME_KEY:167:invalid multi prime key
RSA_R_INVALID_OAEP_PARAMETERS:161:invalid oaep parameters
RSA_R_INVALID_PADDING:138:invalid padding
RSA_R_INVALID_PADDING_MODE:141:invalid padding mode
RSA_R_INVALID_PSS_PARAMETERS:149:invalid pss parameters
RSA_R_INVALID_PSS_SALTLEN:146:invalid pss saltlen
RSA_R_INVALID_REQUEST:175:invalid request
RSA_R_INVALID_SALT_LENGTH:150:invalid salt length
RSA_R_INVALID_STRENGTH:176:invalid strength
RSA_R_INVALID_TRAILER:139:invalid trailer
RSA_R_INVALID_X931_DIGEST:142:invalid x931 digest
RSA_R_IQMP_NOT_INVERSE_OF_Q:126:iqmp not inverse of q
RSA_R_KEY_PRIME_NUM_INVALID:165:key prime num invalid
RSA_R_KEY_SIZE_TOO_SMALL:120:key size too small
RSA_R_LAST_OCTET_INVALID:134:last octet invalid
RSA_R_MGF1_DIGEST_NOT_ALLOWED:152:mgf1 digest not allowed
RSA_R_MISSING_PRIVATE_KEY:179:missing private key
RSA_R_MODULUS_TOO_LARGE:105:modulus too large
RSA_R_MP_COEFFICIENT_NOT_INVERSE_OF_R:168:mp coefficient not inverse of r
RSA_R_MP_EXPONENT_NOT_CONGRUENT_TO_D:169:mp exponent not congruent to d
RSA_R_MP_R_NOT_PRIME:170:mp r not prime
RSA_R_NO_PUBLIC_EXPONENT:140:no public exponent
RSA_R_NULL_BEFORE_BLOCK_MISSING:113:null before block missing
RSA_R_N_DOES_NOT_EQUAL_PRODUCT_OF_PRIMES:172:n does not equal product of primes
RSA_R_N_DOES_NOT_EQUAL_P_Q:127:n does not equal p q
RSA_R_OAEP_DECODING_ERROR:121:oaep decoding error
RSA_R_OPERATION_NOT_SUPPORTED_FOR_THIS_KEYTYPE:148:\
	operation not supported for this keytype
RSA_R_PADDING_CHECK_FAILED:114:padding check failed
RSA_R_PAIRWISE_TEST_FAILURE:177:pairwise test failure
RSA_R_PKCS_DECODING_ERROR:159:pkcs decoding error
RSA_R_PSS_SALTLEN_TOO_SMALL:164:pss saltlen too small
RSA_R_PUB_EXPONENT_OUT_OF_RANGE:178:pub exponent out of range
RSA_R_P_NOT_PRIME:128:p not prime
RSA_R_Q_NOT_PRIME:129:q not prime
RSA_R_RANDOMNESS_SOURCE_STRENGTH_INSUFFICIENT:180:\
	randomness source strength insufficient
RSA_R_RSA_OPERATIONS_NOT_SUPPORTED:130:rsa operations not supported
RSA_R_SLEN_CHECK_FAILED:136:salt length check failed
RSA_R_SLEN_RECOVERY_FAILED:135:salt length recovery failed
RSA_R_SSLV3_ROLLBACK_ATTACK:115:sslv3 rollback attack
RSA_R_THE_ASN1_OBJECT_IDENTIFIER_IS_NOT_KNOWN_FOR_THIS_MD:116:\
	the asn1 object identifier is not known for this md
RSA_R_UNKNOWN_ALGORITHM_TYPE:117:unknown algorithm type
RSA_R_UNKNOWN_DIGEST:166:unknown digest
RSA_R_UNKNOWN_MASK_DIGEST:151:unknown mask digest
RSA_R_UNKNOWN_PADDING_TYPE:118:unknown padding type
RSA_R_UNSUPPORTED_ENCRYPTION_TYPE:162:unsupported encryption type
RSA_R_UNSUPPORTED_LABEL_SOURCE:163:unsupported label source
RSA_R_UNSUPPORTED_MASK_ALGORITHM:153:unsupported mask algorithm
RSA_R_UNSUPPORTED_MASK_PARAMETER:154:unsupported mask parameter
RSA_R_UNSUPPORTED_SIGNATURE_TYPE:155:unsupported signature type
RSA_R_VALUE_MISSING:147:value missing
RSA_R_WRONG_SIGNATURE_LENGTH:119:wrong signature length
SM2_R_ASN1_ERROR:100:asn1 error
SM2_R_BAD_SIGNATURE:101:bad signature
SM2_R_BUFFER_TOO_SMALL:107:buffer too small
SM2_R_DIST_ID_TOO_LARGE:110:dist id too large
SM2_R_ID_NOT_SET:112:id not set
SM2_R_ID_TOO_LARGE:111:id too large
SM2_R_INVALID_CURVE:108:invalid curve
SM2_R_INVALID_DIGEST:102:invalid digest
SM2_R_INVALID_DIGEST_TYPE:103:invalid digest type
SM2_R_INVALID_ENCODING:104:invalid encoding
SM2_R_INVALID_FIELD:105:invalid field
SM2_R_INVALID_PRIVATE_KEY:113:invalid private key
SM2_R_NO_PARAMETERS_SET:109:no parameters set
SM2_R_USER_ID_TOO_LARGE:106:user id too large
SSL_R_APPLICATION_DATA_AFTER_CLOSE_NOTIFY:291:\
	application data after close notify
SSL_R_APP_DATA_IN_HANDSHAKE:100:app data in handshake
SSL_R_ATTEMPT_TO_REUSE_SESSION_IN_DIFFERENT_CONTEXT:272:\
	attempt to reuse session in different context
SSL_R_AT_LEAST_TLS_1_2_NEEDED_IN_SUITEB_MODE:158:\
	at least (D)TLS 1.2 needed in Suite B mode
SSL_R_BAD_CHANGE_CIPHER_SPEC:103:bad change cipher spec
SSL_R_BAD_CIPHER:186:bad cipher
SSL_R_BAD_DATA:390:bad data
SSL_R_BAD_DATA_RETURNED_BY_CALLBACK:106:bad data returned by callback
SSL_R_BAD_DECOMPRESSION:107:bad decompression
SSL_R_BAD_DH_VALUE:102:bad dh value
SSL_R_BAD_DIGEST_LENGTH:111:bad digest length
SSL_R_BAD_EARLY_DATA:233:bad early data
SSL_R_BAD_ECC_CERT:304:bad ecc cert
SSL_R_BAD_ECPOINT:306:bad ecpoint
SSL_R_BAD_EXTENSION:110:bad extension
SSL_R_BAD_HANDSHAKE_LENGTH:332:bad handshake length
SSL_R_BAD_HANDSHAKE_STATE:236:bad handshake state
SSL_R_BAD_HELLO_REQUEST:105:bad hello request
SSL_R_BAD_HRR_VERSION:263:bad hrr version
SSL_R_BAD_KEY_SHARE:108:bad key share
SSL_R_BAD_KEY_UPDATE:122:bad key update
SSL_R_BAD_LEGACY_VERSION:292:bad legacy version
SSL_R_BAD_LENGTH:271:bad length
SSL_R_BAD_PACKET:240:bad packet
SSL_R_BAD_PACKET_LENGTH:115:bad packet length
SSL_R_BAD_PROTOCOL_VERSION_NUMBER:116:bad protocol version number
SSL_R_BAD_PSK:219:bad psk
SSL_R_BAD_PSK_IDENTITY:114:bad psk identity
SSL_R_BAD_RECORD_TYPE:443:bad record type
SSL_R_BAD_RSA_ENCRYPT:119:bad rsa encrypt
SSL_R_BAD_SIGNATURE:123:bad signature
SSL_R_BAD_SRP_A_LENGTH:347:bad srp a length
SSL_R_BAD_SRP_PARAMETERS:371:bad srp parameters
SSL_R_BAD_SRTP_MKI_VALUE:352:bad srtp mki value
SSL_R_BAD_SRTP_PROTECTION_PROFILE_LIST:353:bad srtp protection profile list
SSL_R_BAD_SSL_FILETYPE:124:bad ssl filetype
SSL_R_BAD_VALUE:384:bad value
SSL_R_BAD_WRITE_RETRY:127:bad write retry
SSL_R_BINDER_DOES_NOT_VERIFY:253:binder does not verify
SSL_R_BIO_NOT_SET:128:bio not set
SSL_R_BLOCK_CIPHER_PAD_IS_WRONG:129:block cipher pad is wrong
SSL_R_BN_LIB:130:bn lib
SSL_R_CALLBACK_FAILED:234:callback failed
SSL_R_CANNOT_CHANGE_CIPHER:109:cannot change cipher
SSL_R_CANNOT_GET_GROUP_NAME:299:cannot get group name
SSL_R_CA_DN_LENGTH_MISMATCH:131:ca dn length mismatch
SSL_R_CA_KEY_TOO_SMALL:397:ca key too small
SSL_R_CA_MD_TOO_WEAK:398:ca md too weak
SSL_R_CCS_RECEIVED_EARLY:133:ccs received early
SSL_R_CERTIFICATE_VERIFY_FAILED:134:certificate verify failed
SSL_R_CERT_CB_ERROR:377:cert cb error
SSL_R_CERT_LENGTH_MISMATCH:135:cert length mismatch
SSL_R_CIPHERSUITE_DIGEST_HAS_CHANGED:218:ciphersuite digest has changed
SSL_R_CIPHER_CODE_WRONG_LENGTH:137:cipher code wrong length
SSL_R_CLIENTHELLO_TLSEXT:226:clienthello tlsext
SSL_R_COMPRESSED_LENGTH_TOO_LONG:140:compressed length too long
SSL_R_COMPRESSION_DISABLED:343:compression disabled
SSL_R_COMPRESSION_FAILURE:141:compression failure
SSL_R_COMPRESSION_ID_NOT_WITHIN_PRIVATE_RANGE:307:\
	compression id not within private range
SSL_R_COMPRESSION_LIBRARY_ERROR:142:compression library error
SSL_R_CONNECTION_TYPE_NOT_SET:144:connection type not set
SSL_R_CONTEXT_NOT_DANE_ENABLED:167:context not dane enabled
SSL_R_COOKIE_GEN_CALLBACK_FAILURE:400:cookie gen callback failure
SSL_R_COOKIE_MISMATCH:308:cookie mismatch
SSL_R_COPY_PARAMETERS_FAILED:296:copy parameters failed
SSL_R_CUSTOM_EXT_HANDLER_ALREADY_INSTALLED:206:\
	custom ext handler already installed
SSL_R_DANE_ALREADY_ENABLED:172:dane already enabled
SSL_R_DANE_CANNOT_OVERRIDE_MTYPE_FULL:173:dane cannot override mtype full
SSL_R_DANE_NOT_ENABLED:175:dane not enabled
SSL_R_DANE_TLSA_BAD_CERTIFICATE:180:dane tlsa bad certificate
SSL_R_DANE_TLSA_BAD_CERTIFICATE_USAGE:184:dane tlsa bad certificate usage
SSL_R_DANE_TLSA_BAD_DATA_LENGTH:189:dane tlsa bad data length
SSL_R_DANE_TLSA_BAD_DIGEST_LENGTH:192:dane tlsa bad digest length
SSL_R_DANE_TLSA_BAD_MATCHING_TYPE:200:dane tlsa bad matching type
SSL_R_DANE_TLSA_BAD_PUBLIC_KEY:201:dane tlsa bad public key
SSL_R_DANE_TLSA_BAD_SELECTOR:202:dane tlsa bad selector
SSL_R_DANE_TLSA_NULL_DATA:203:dane tlsa null data
SSL_R_DATA_BETWEEN_CCS_AND_FINISHED:145:data between ccs and finished
SSL_R_DATA_LENGTH_TOO_LONG:146:data length too long
SSL_R_DECRYPTION_FAILED:147:decryption failed
SSL_R_DECRYPTION_FAILED_OR_BAD_RECORD_MAC:281:\
	decryption failed or bad record mac
SSL_R_DH_KEY_TOO_SMALL:394:dh key too small
SSL_R_DH_PUBLIC_VALUE_LENGTH_IS_WRONG:148:dh public value length is wrong
SSL_R_DIGEST_CHECK_FAILED:149:digest check failed
SSL_R_DTLS_MESSAGE_TOO_BIG:334:dtls message too big
SSL_R_DUPLICATE_COMPRESSION_ID:309:duplicate compression id
SSL_R_ECC_CERT_NOT_FOR_SIGNING:318:ecc cert not for signing
SSL_R_ECDH_REQUIRED_FOR_SUITEB_MODE:374:ecdh required for suiteb mode
SSL_R_EE_KEY_TOO_SMALL:399:ee key too small
SSL_R_EMPTY_SRTP_PROTECTION_PROFILE_LIST:354:empty srtp protection profile list
SSL_R_ENCRYPTED_LENGTH_TOO_LONG:150:encrypted length too long
SSL_R_ERROR_IN_RECEIVED_CIPHER_LIST:151:error in received cipher list
SSL_R_ERROR_SETTING_TLSA_BASE_DOMAIN:204:error setting tlsa base domain
SSL_R_EXCEEDS_MAX_FRAGMENT_SIZE:194:exceeds max fragment size
SSL_R_EXCESSIVE_MESSAGE_SIZE:152:excessive message size
SSL_R_EXTENSION_NOT_RECEIVED:279:extension not received
SSL_R_EXTRA_DATA_IN_MESSAGE:153:extra data in message
SSL_R_EXT_LENGTH_MISMATCH:163:ext length mismatch
SSL_R_FAILED_TO_INIT_ASYNC:405:failed to init async
SSL_R_FRAGMENTED_CLIENT_HELLO:401:fragmented client hello
SSL_R_GOT_A_FIN_BEFORE_A_CCS:154:got a fin before a ccs
SSL_R_HTTPS_PROXY_REQUEST:155:https proxy request
SSL_R_HTTP_REQUEST:156:http request
SSL_R_ILLEGAL_POINT_COMPRESSION:162:illegal point compression
SSL_R_ILLEGAL_SUITEB_DIGEST:380:illegal Suite B digest
SSL_R_INAPPROPRIATE_FALLBACK:373:inappropriate fallback
SSL_R_INCONSISTENT_COMPRESSION:340:inconsistent compression
SSL_R_INCONSISTENT_EARLY_DATA_ALPN:222:inconsistent early data alpn
SSL_R_INCONSISTENT_EARLY_DATA_SNI:231:inconsistent early data sni
SSL_R_INCONSISTENT_EXTMS:104:inconsistent extms
SSL_R_INSUFFICIENT_SECURITY:241:insufficient security
SSL_R_INVALID_ALERT:205:invalid alert
SSL_R_INVALID_CCS_MESSAGE:260:invalid ccs message
SSL_R_INVALID_CERTIFICATE_OR_ALG:238:invalid certificate or alg
SSL_R_INVALID_COMMAND:280:invalid command
SSL_R_INVALID_COMPRESSION_ALGORITHM:341:invalid compression algorithm
SSL_R_INVALID_CONFIG:283:invalid config
SSL_R_INVALID_CONFIGURATION_NAME:113:invalid configuration name
SSL_R_INVALID_CONTEXT:282:invalid context
SSL_R_INVALID_CT_VALIDATION_TYPE:212:invalid ct validation type
SSL_R_INVALID_KEY_UPDATE_TYPE:120:invalid key update type
SSL_R_INVALID_MAX_EARLY_DATA:174:invalid max early data
SSL_R_INVALID_NULL_CMD_NAME:385:invalid null cmd name
SSL_R_INVALID_SEQUENCE_NUMBER:402:invalid sequence number
SSL_R_INVALID_SERVERINFO_DATA:388:invalid serverinfo data
SSL_R_INVALID_SESSION_ID:999:invalid session id
SSL_R_INVALID_SRP_USERNAME:357:invalid srp username
SSL_R_INVALID_STATUS_RESPONSE:328:invalid status response
SSL_R_INVALID_TICKET_KEYS_LENGTH:325:invalid ticket keys length
SSL_R_LEGACY_SIGALG_DISALLOWED_OR_UNSUPPORTED:333:\
	legacy sigalg disallowed or unsupported
SSL_R_LENGTH_MISMATCH:159:length mismatch
SSL_R_LENGTH_TOO_LONG:404:length too long
SSL_R_LENGTH_TOO_SHORT:160:length too short
SSL_R_LIBRARY_BUG:274:library bug
SSL_R_LIBRARY_HAS_NO_CIPHERS:161:library has no ciphers
SSL_R_MISSING_DSA_SIGNING_CERT:165:missing dsa signing cert
SSL_R_MISSING_ECDSA_SIGNING_CERT:381:missing ecdsa signing cert
SSL_R_MISSING_FATAL:256:missing fatal
SSL_R_MISSING_PARAMETERS:290:missing parameters
SSL_R_MISSING_PSK_KEX_MODES_EXTENSION:310:missing psk kex modes extension
SSL_R_MISSING_RSA_CERTIFICATE:168:missing rsa certificate
SSL_R_MISSING_RSA_ENCRYPTING_CERT:169:missing rsa encrypting cert
SSL_R_MISSING_RSA_SIGNING_CERT:170:missing rsa signing cert
SSL_R_MISSING_SIGALGS_EXTENSION:112:missing sigalgs extension
SSL_R_MISSING_SIGNING_CERT:221:missing signing cert
SSL_R_MISSING_SRP_PARAM:358:can't find SRP server param
SSL_R_MISSING_SUPPORTED_GROUPS_EXTENSION:209:missing supported groups extension
SSL_R_MISSING_TMP_DH_KEY:171:missing tmp dh key
SSL_R_MISSING_TMP_ECDH_KEY:311:missing tmp ecdh key
SSL_R_MIXED_HANDSHAKE_AND_NON_HANDSHAKE_DATA:293:\
	mixed handshake and non handshake data
SSL_R_NOT_ON_RECORD_BOUNDARY:182:not on record boundary
SSL_R_NOT_REPLACING_CERTIFICATE:289:not replacing certificate
SSL_R_NOT_SERVER:284:not server
SSL_R_NO_APPLICATION_PROTOCOL:235:no application protocol
SSL_R_NO_CERTIFICATES_RETURNED:176:no certificates returned
SSL_R_NO_CERTIFICATE_ASSIGNED:177:no certificate assigned
SSL_R_NO_CERTIFICATE_SET:179:no certificate set
SSL_R_NO_CHANGE_FOLLOWING_HRR:214:no change following hrr
SSL_R_NO_CIPHERS_AVAILABLE:181:no ciphers available
SSL_R_NO_CIPHERS_SPECIFIED:183:no ciphers specified
SSL_R_NO_CIPHER_MATCH:185:no cipher match
SSL_R_NO_CLIENT_CERT_METHOD:331:no client cert method
SSL_R_NO_COMPRESSION_SPECIFIED:187:no compression specified
SSL_R_NO_COOKIE_CALLBACK_SET:287:no cookie callback set
SSL_R_NO_GOST_CERTIFICATE_SENT_BY_PEER:330:\
	Peer haven't sent GOST certificate, required for selected ciphersuite
SSL_R_NO_METHOD_SPECIFIED:188:no method specified
SSL_R_NO_PEM_EXTENSIONS:389:no pem extensions
SSL_R_NO_PRIVATE_KEY_ASSIGNED:190:no private key assigned
SSL_R_NO_PROTOCOLS_AVAILABLE:191:no protocols available
SSL_R_NO_RENEGOTIATION:339:no renegotiation
SSL_R_NO_REQUIRED_DIGEST:324:no required digest
SSL_R_NO_SHARED_CIPHER:193:no shared cipher
SSL_R_NO_SHARED_GROUPS:410:no shared groups
SSL_R_NO_SHARED_SIGNATURE_ALGORITHMS:376:no shared signature algorithms
SSL_R_NO_SRTP_PROFILES:359:no srtp profiles
SSL_R_NO_SUITABLE_DIGEST_ALGORITHM:297:no suitable digest algorithm
SSL_R_NO_SUITABLE_GROUPS:295:no suitable groups
SSL_R_NO_SUITABLE_KEY_SHARE:101:no suitable key share
SSL_R_NO_SUITABLE_SIGNATURE_ALGORITHM:118:no suitable signature algorithm
SSL_R_NO_VALID_SCTS:216:no valid scts
SSL_R_NO_VERIFY_COOKIE_CALLBACK:403:no verify cookie callback
SSL_R_NULL_SSL_CTX:195:null ssl ctx
SSL_R_NULL_SSL_METHOD_PASSED:196:null ssl method passed
SSL_R_OCSP_CALLBACK_FAILURE:305:ocsp callback failure
SSL_R_OLD_SESSION_CIPHER_NOT_RETURNED:197:old session cipher not returned
SSL_R_OLD_SESSION_COMPRESSION_ALGORITHM_NOT_RETURNED:344:\
	old session compression algorithm not returned
SSL_R_OVERFLOW_ERROR:237:overflow error
SSL_R_PACKET_LENGTH_TOO_LONG:198:packet length too long
SSL_R_PARSE_TLSEXT:227:parse tlsext
SSL_R_PATH_TOO_LONG:270:path too long
SSL_R_PEER_DID_NOT_RETURN_A_CERTIFICATE:199:peer did not return a certificate
SSL_R_PEM_NAME_BAD_PREFIX:391:pem name bad prefix
SSL_R_PEM_NAME_TOO_SHORT:392:pem name too short
SSL_R_PIPELINE_FAILURE:406:pipeline failure
SSL_R_POST_HANDSHAKE_AUTH_ENCODING_ERR:278:post handshake auth encoding err
SSL_R_PRIVATE_KEY_MISMATCH:288:private key mismatch
SSL_R_PROTOCOL_IS_SHUTDOWN:207:protocol is shutdown
SSL_R_PSK_IDENTITY_NOT_FOUND:223:psk identity not found
SSL_R_PSK_NO_CLIENT_CB:224:psk no client cb
SSL_R_PSK_NO_SERVER_CB:225:psk no server cb
SSL_R_READ_BIO_NOT_SET:211:read bio not set
SSL_R_READ_TIMEOUT_EXPIRED:312:read timeout expired
SSL_R_RECORD_LENGTH_MISMATCH:213:record length mismatch
SSL_R_RECORD_TOO_SMALL:298:record too small
SSL_R_RENEGOTIATE_EXT_TOO_LONG:335:renegotiate ext too long
SSL_R_RENEGOTIATION_ENCODING_ERR:336:renegotiation encoding err
SSL_R_RENEGOTIATION_MISMATCH:337:renegotiation mismatch
SSL_R_REQUEST_PENDING:285:request pending
SSL_R_REQUEST_SENT:286:request sent
SSL_R_REQUIRED_CIPHER_MISSING:215:required cipher missing
SSL_R_REQUIRED_COMPRESSION_ALGORITHM_MISSING:342:\
	required compression algorithm missing
SSL_R_SCSV_RECEIVED_WHEN_RENEGOTIATING:345:scsv received when renegotiating
SSL_R_SCT_VERIFICATION_FAILED:208:sct verification failed
SSL_R_SERVERHELLO_TLSEXT:275:serverhello tlsext
SSL_R_SESSION_ID_CONTEXT_UNINITIALIZED:277:session id context uninitialized
SSL_R_SHUTDOWN_WHILE_IN_INIT:407:shutdown while in init
SSL_R_SIGNATURE_ALGORITHMS_ERROR:360:signature algorithms error
SSL_R_SIGNATURE_FOR_NON_SIGNING_CERTIFICATE:220:\
	signature for non signing certificate
SSL_R_SRP_A_CALC:361:error with the srp params
SSL_R_SRTP_COULD_NOT_ALLOCATE_PROFILES:362:srtp could not allocate profiles
SSL_R_SRTP_PROTECTION_PROFILE_LIST_TOO_LONG:363:\
	srtp protection profile list too long
SSL_R_SRTP_UNKNOWN_PROTECTION_PROFILE:364:srtp unknown protection profile
SSL_R_SSL3_EXT_INVALID_MAX_FRAGMENT_LENGTH:232:\
	ssl3 ext invalid max fragment length
SSL_R_SSL3_EXT_INVALID_SERVERNAME:319:ssl3 ext invalid servername
SSL_R_SSL3_EXT_INVALID_SERVERNAME_TYPE:320:ssl3 ext invalid servername type
SSL_R_SSL3_SESSION_ID_TOO_LONG:300:ssl3 session id too long
SSL_R_SSL_COMMAND_SECTION_EMPTY:117:ssl command section empty
SSL_R_SSL_COMMAND_SECTION_NOT_FOUND:125:ssl command section not found
SSL_R_SSL_CTX_HAS_NO_DEFAULT_SSL_VERSION:228:ssl ctx has no default ssl version
SSL_R_SSL_HANDSHAKE_FAILURE:229:ssl handshake failure
SSL_R_SSL_LIBRARY_HAS_NO_CIPHERS:230:ssl library has no ciphers
SSL_R_SSL_NEGATIVE_LENGTH:372:ssl negative length
SSL_R_SSL_SECTION_EMPTY:126:ssl section empty
SSL_R_SSL_SECTION_NOT_FOUND:136:ssl section not found
SSL_R_SSL_SESSION_ID_CALLBACK_FAILED:301:ssl session id callback failed
SSL_R_SSL_SESSION_ID_CONFLICT:302:ssl session id conflict
SSL_R_SSL_SESSION_ID_CONTEXT_TOO_LONG:273:ssl session id context too long
SSL_R_SSL_SESSION_ID_HAS_BAD_LENGTH:303:ssl session id has bad length
SSL_R_SSL_SESSION_ID_TOO_LONG:408:ssl session id too long
SSL_R_SSL_SESSION_VERSION_MISMATCH:210:ssl session version mismatch
SSL_R_STILL_IN_INIT:121:still in init
SSL_R_TLS_ILLEGAL_EXPORTER_LABEL:367:tls illegal exporter label
SSL_R_TLS_INVALID_ECPOINTFORMAT_LIST:157:tls invalid ecpointformat list
SSL_R_TOO_MANY_KEY_UPDATES:132:too many key updates
SSL_R_TOO_MANY_WARN_ALERTS:409:too many warn alerts
SSL_R_TOO_MUCH_EARLY_DATA:164:too much early data
SSL_R_UNABLE_TO_FIND_ECDH_PARAMETERS:314:unable to find ecdh parameters
SSL_R_UNABLE_TO_FIND_PUBLIC_KEY_PARAMETERS:239:\
	unable to find public key parameters
SSL_R_UNABLE_TO_LOAD_SSL3_MD5_ROUTINES:242:unable to load ssl3 md5 routines
SSL_R_UNABLE_TO_LOAD_SSL3_SHA1_ROUTINES:243:unable to load ssl3 sha1 routines
SSL_R_UNEXPECTED_CCS_MESSAGE:262:unexpected ccs message
SSL_R_UNEXPECTED_END_OF_EARLY_DATA:178:unexpected end of early data
SSL_R_UNEXPECTED_EOF_WHILE_READING:294:unexpected eof while reading
SSL_R_UNEXPECTED_MESSAGE:244:unexpected message
SSL_R_UNEXPECTED_RECORD:245:unexpected record
SSL_R_UNINITIALIZED:276:uninitialized
SSL_R_UNKNOWN_ALERT_TYPE:246:unknown alert type
SSL_R_UNKNOWN_CERTIFICATE_TYPE:247:unknown certificate type
SSL_R_UNKNOWN_CIPHER_RETURNED:248:unknown cipher returned
SSL_R_UNKNOWN_CIPHER_TYPE:249:unknown cipher type
SSL_R_UNKNOWN_CMD_NAME:386:unknown cmd name
SSL_R_UNKNOWN_COMMAND:139:unknown command
SSL_R_UNKNOWN_DIGEST:368:unknown digest
SSL_R_UNKNOWN_KEY_EXCHANGE_TYPE:250:unknown key exchange type
SSL_R_UNKNOWN_PKEY_TYPE:251:unknown pkey type
SSL_R_UNKNOWN_PROTOCOL:252:unknown protocol
SSL_R_UNKNOWN_SSL_VERSION:254:unknown ssl version
SSL_R_UNKNOWN_STATE:255:unknown state
SSL_R_UNSAFE_LEGACY_RENEGOTIATION_DISABLED:338:\
	unsafe legacy renegotiation disabled
SSL_R_UNSOLICITED_EXTENSION:217:unsolicited extension
SSL_R_UNSUPPORTED_COMPRESSION_ALGORITHM:257:unsupported compression algorithm
SSL_R_UNSUPPORTED_ELLIPTIC_CURVE:315:unsupported elliptic curve
SSL_R_UNSUPPORTED_PROTOCOL:258:unsupported protocol
SSL_R_UNSUPPORTED_SSL_VERSION:259:unsupported ssl version
SSL_R_UNSUPPORTED_STATUS_TYPE:329:unsupported status type
SSL_R_USE_SRTP_NOT_NEGOTIATED:369:use srtp not negotiated
SSL_R_VERSION_TOO_HIGH:166:version too high
SSL_R_VERSION_TOO_LOW:396:version too low
SSL_R_WRONG_CERTIFICATE_TYPE:383:wrong certificate type
SSL_R_WRONG_CIPHER_RETURNED:261:wrong cipher returned
SSL_R_WRONG_CURVE:378:wrong curve
SSL_R_WRONG_SIGNATURE_LENGTH:264:wrong signature length
SSL_R_WRONG_SIGNATURE_SIZE:265:wrong signature size
SSL_R_WRONG_SIGNATURE_TYPE:370:wrong signature type
SSL_R_WRONG_SSL_VERSION:266:wrong ssl version
SSL_R_WRONG_VERSION_NUMBER:267:wrong version number
SSL_R_X509_LIB:268:x509 lib
SSL_R_X509_VERIFICATION_SETUP_PROBLEMS:269:x509 verification setup problems
TS_R_BAD_PKCS7_TYPE:132:bad pkcs7 type
TS_R_BAD_TYPE:133:bad type
TS_R_CANNOT_LOAD_CERT:137:cannot load certificate
TS_R_CANNOT_LOAD_KEY:138:cannot load private key
TS_R_CERTIFICATE_VERIFY_ERROR:100:certificate verify error
TS_R_COULD_NOT_SET_ENGINE:127:could not set engine
TS_R_COULD_NOT_SET_TIME:115:could not set time
TS_R_DETACHED_CONTENT:134:detached content
TS_R_ESS_ADD_SIGNING_CERT_ERROR:116:ess add signing cert error
TS_R_ESS_ADD_SIGNING_CERT_V2_ERROR:139:ess add signing cert v2 error
TS_R_ESS_SIGNING_CERTIFICATE_ERROR:101:ess signing certificate error
TS_R_INVALID_NULL_POINTER:102:invalid null pointer
TS_R_INVALID_SIGNER_CERTIFICATE_PURPOSE:117:invalid signer certificate purpose
TS_R_MESSAGE_IMPRINT_MISMATCH:103:message imprint mismatch
TS_R_NONCE_MISMATCH:104:nonce mismatch
TS_R_NONCE_NOT_RETURNED:105:nonce not returned
TS_R_NO_CONTENT:106:no content
TS_R_NO_TIME_STAMP_TOKEN:107:no time stamp token
TS_R_PKCS7_ADD_SIGNATURE_ERROR:118:pkcs7 add signature error
TS_R_PKCS7_ADD_SIGNED_ATTR_ERROR:119:pkcs7 add signed attr error
TS_R_PKCS7_TO_TS_TST_INFO_FAILED:129:pkcs7 to ts tst info failed
TS_R_POLICY_MISMATCH:108:policy mismatch
TS_R_PRIVATE_KEY_DOES_NOT_MATCH_CERTIFICATE:120:\
	private key does not match certificate
TS_R_RESPONSE_SETUP_ERROR:121:response setup error
TS_R_SIGNATURE_FAILURE:109:signature failure
TS_R_THERE_MUST_BE_ONE_SIGNER:110:there must be one signer
TS_R_TIME_SYSCALL_ERROR:122:time syscall error
TS_R_TOKEN_NOT_PRESENT:130:token not present
TS_R_TOKEN_PRESENT:131:token present
TS_R_TSA_NAME_MISMATCH:111:tsa name mismatch
TS_R_TSA_UNTRUSTED:112:tsa untrusted
TS_R_TST_INFO_SETUP_ERROR:123:tst info setup error
TS_R_TS_DATASIGN:124:ts datasign
TS_R_UNACCEPTABLE_POLICY:125:unacceptable policy
TS_R_UNSUPPORTED_MD_ALGORITHM:126:unsupported md algorithm
TS_R_UNSUPPORTED_VERSION:113:unsupported version
TS_R_VAR_BAD_VALUE:135:var bad value
TS_R_VAR_LOOKUP_FAILURE:136:cannot find config variable
TS_R_WRONG_CONTENT_TYPE:114:wrong content type
UI_R_COMMON_OK_AND_CANCEL_CHARACTERS:104:common ok and cancel characters
UI_R_INDEX_TOO_LARGE:102:index too large
UI_R_INDEX_TOO_SMALL:103:index too small
UI_R_NO_RESULT_BUFFER:105:no result buffer
UI_R_PROCESSING_ERROR:107:processing error
UI_R_RESULT_TOO_LARGE:100:result too large
UI_R_RESULT_TOO_SMALL:101:result too small
UI_R_SYSASSIGN_ERROR:109:sys$assign error
UI_R_SYSDASSGN_ERROR:110:sys$dassgn error
UI_R_SYSQIOW_ERROR:111:sys$qiow error
UI_R_UNKNOWN_CONTROL_COMMAND:106:unknown control command
UI_R_UNKNOWN_TTYGET_ERRNO_VALUE:108:unknown ttyget errno value
UI_R_USER_DATA_DUPLICATION_UNSUPPORTED:112:user data duplication unsupported
X509V3_R_BAD_IP_ADDRESS:118:bad ip address
X509V3_R_BAD_OBJECT:119:bad object
X509V3_R_BN_DEC2BN_ERROR:100:bn dec2bn error
X509V3_R_BN_TO_ASN1_INTEGER_ERROR:101:bn to asn1 integer error
X509V3_R_DIRNAME_ERROR:149:dirname error
X509V3_R_DISTPOINT_ALREADY_SET:160:distpoint already set
X509V3_R_DUPLICATE_ZONE_ID:133:duplicate zone id
X509V3_R_EMPTY_KEY_USAGE:169:empty key usage
X509V3_R_ERROR_CONVERTING_ZONE:131:error converting zone
X509V3_R_ERROR_CREATING_EXTENSION:144:error creating extension
X509V3_R_ERROR_IN_EXTENSION:128:error in extension
X509V3_R_EXPECTED_A_SECTION_NAME:137:expected a section name
X509V3_R_EXTENSION_EXISTS:145:extension exists
X509V3_R_EXTENSION_NAME_ERROR:115:extension name error
X509V3_R_EXTENSION_NOT_FOUND:102:extension not found
X509V3_R_EXTENSION_SETTING_NOT_SUPPORTED:103:extension setting not supported
X509V3_R_EXTENSION_VALUE_ERROR:116:extension value error
X509V3_R_ILLEGAL_EMPTY_EXTENSION:151:illegal empty extension
X509V3_R_INCORRECT_POLICY_SYNTAX_TAG:152:incorrect policy syntax tag
X509V3_R_INVALID_ASNUMBER:162:invalid asnumber
X509V3_R_INVALID_ASRANGE:163:invalid asrange
X509V3_R_INVALID_BOOLEAN_STRING:104:invalid boolean string
X509V3_R_INVALID_CERTIFICATE:158:invalid certificate
X509V3_R_INVALID_EMPTY_NAME:108:invalid empty name
X509V3_R_INVALID_EXTENSION_STRING:105:invalid extension string
X509V3_R_INVALID_INHERITANCE:165:invalid inheritance
X509V3_R_INVALID_IPADDRESS:166:invalid ipaddress
X509V3_R_INVALID_MULTIPLE_RDNS:161:invalid multiple rdns
X509V3_R_INVALID_NAME:106:invalid name
X509V3_R_INVALID_NULL_ARGUMENT:107:invalid null argument
X509V3_R_INVALID_NULL_VALUE:109:invalid null value
X509V3_R_INVALID_NUMBER:140:invalid number
X509V3_R_INVALID_NUMBERS:141:invalid numbers
X509V3_R_INVALID_OBJECT_IDENTIFIER:110:invalid object identifier
X509V3_R_INVALID_OPTION:138:invalid option
X509V3_R_INVALID_POLICY_IDENTIFIER:134:invalid policy identifier
X509V3_R_INVALID_PROXY_POLICY_SETTING:153:invalid proxy policy setting
X509V3_R_INVALID_PURPOSE:146:invalid purpose
X509V3_R_INVALID_SAFI:164:invalid safi
X509V3_R_INVALID_SECTION:135:invalid section
X509V3_R_INVALID_SYNTAX:143:invalid syntax
X509V3_R_ISSUER_DECODE_ERROR:126:issuer decode error
X509V3_R_MISSING_VALUE:124:missing value
X509V3_R_NEED_ORGANIZATION_AND_NUMBERS:142:need organization and numbers
X509V3_R_NEGATIVE_PATHLEN:168:negative pathlen
X509V3_R_NO_CONFIG_DATABASE:136:no config database
X509V3_R_NO_ISSUER_CERTIFICATE:121:no issuer certificate
X509V3_R_NO_ISSUER_DETAILS:127:no issuer details
X509V3_R_NO_POLICY_IDENTIFIER:139:no policy identifier
X509V3_R_NO_PROXY_CERT_POLICY_LANGUAGE_DEFINED:154:\
	no proxy cert policy language defined
X509V3_R_NO_PUBLIC_KEY:114:no public key
X509V3_R_NO_SUBJECT_DETAILS:125:no subject details
X509V3_R_OPERATION_NOT_DEFINED:148:operation not defined
X509V3_R_OTHERNAME_ERROR:147:othername error
X509V3_R_POLICY_LANGUAGE_ALREADY_DEFINED:155:policy language already defined
X509V3_R_POLICY_PATH_LENGTH:156:policy path length
X509V3_R_POLICY_PATH_LENGTH_ALREADY_DEFINED:157:\
	policy path length already defined
X509V3_R_POLICY_WHEN_PROXY_LANGUAGE_REQUIRES_NO_POLICY:159:\
	policy when proxy language requires no policy
X509V3_R_SECTION_NOT_FOUND:150:section not found
X509V3_R_UNABLE_TO_GET_ISSUER_DETAILS:122:unable to get issuer details
X509V3_R_UNABLE_TO_GET_ISSUER_KEYID:123:unable to get issuer keyid
X509V3_R_UNKNOWN_BIT_STRING_ARGUMENT:111:unknown bit string argument
X509V3_R_UNKNOWN_EXTENSION:129:unknown extension
X509V3_R_UNKNOWN_EXTENSION_NAME:130:unknown extension name
X509V3_R_UNKNOWN_OPTION:120:unknown option
X509V3_R_UNSUPPORTED_OPTION:117:unsupported option
X509V3_R_UNSUPPORTED_TYPE:167:unsupported type
X509V3_R_USER_TOO_LONG:132:user too long
X509_R_AKID_MISMATCH:110:akid mismatch
X509_R_BAD_SELECTOR:133:bad selector
X509_R_BAD_X509_FILETYPE:100:bad x509 filetype
X509_R_BASE64_DECODE_ERROR:118:base64 decode error
X509_R_CANT_CHECK_DH_KEY:114:cant check dh key
X509_R_CERTIFICATE_VERIFICATION_FAILED:139:certificate verification failed
X509_R_CERT_ALREADY_IN_HASH_TABLE:101:cert already in hash table
X509_R_CRL_ALREADY_DELTA:127:crl already delta
X509_R_CRL_VERIFY_FAILURE:131:crl verify failure
X509_R_DUPLICATE_ATTRIBUTE:140:duplicate attribute
X509_R_ERROR_GETTING_MD_BY_NID:141:error getting md by nid
X509_R_ERROR_USING_SIGINF_SET:142:error using siginf set
X509_R_IDP_MISMATCH:128:idp mismatch
X509_R_INVALID_ATTRIBUTES:138:invalid attributes
X509_R_INVALID_DIRECTORY:113:invalid directory
X509_R_INVALID_DISTPOINT:143:invalid distpoint
X509_R_INVALID_FIELD_NAME:119:invalid field name
X509_R_INVALID_TRUST:123:invalid trust
X509_R_ISSUER_MISMATCH:129:issuer mismatch
X509_R_KEY_TYPE_MISMATCH:115:key type mismatch
X509_R_KEY_VALUES_MISMATCH:116:key values mismatch
X509_R_LOADING_CERT_DIR:103:loading cert dir
X509_R_LOADING_DEFAULTS:104:loading defaults
X509_R_METHOD_NOT_SUPPORTED:124:method not supported
X509_R_NAME_TOO_LONG:134:name too long
X509_R_NEWER_CRL_NOT_NEWER:132:newer crl not newer
X509_R_NO_CERTIFICATE_FOUND:135:no certificate found
X509_R_NO_CERTIFICATE_OR_CRL_FOUND:136:no certificate or crl found
X509_R_NO_CERT_SET_FOR_US_TO_VERIFY:105:no cert set for us to verify
X509_R_NO_CRL_FOUND:137:no crl found
X509_R_NO_CRL_NUMBER:130:no crl number
X509_R_PUBLIC_KEY_DECODE_ERROR:125:public key decode error
X509_R_PUBLIC_KEY_ENCODE_ERROR:126:public key encode error
X509_R_SHOULD_RETRY:106:should retry
X509_R_UNABLE_TO_FIND_PARAMETERS_IN_CHAIN:107:unable to find parameters in chain
X509_R_UNABLE_TO_GET_CERTS_PUBLIC_KEY:108:unable to get certs public key
X509_R_UNKNOWN_KEY_TYPE:117:unknown key type
X509_R_UNKNOWN_NID:109:unknown nid
X509_R_UNKNOWN_PURPOSE_ID:121:unknown purpose id
X509_R_UNKNOWN_SIGID_ALGS:144:unknown sigid algs
X509_R_UNKNOWN_TRUST_ID:120:unknown trust id
X509_R_UNSUPPORTED_ALGORITHM:111:unsupported algorithm
X509_R_WRONG_LOOKUP_TYPE:112:wrong lookup type
X509_R_WRONG_TYPE:122:wrong type
