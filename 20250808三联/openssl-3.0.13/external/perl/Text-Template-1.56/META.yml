---
abstract: 'Expand template text with embedded Perl'
author:
  - '<PERSON> <<EMAIL>>'
build_requires:
  File::Temp: '0'
  Safe: '0'
  Test::More: '0'
  Test::More::UTF8: '0'
  Test::Warnings: '0'
  lib: '0'
  perl: '5.008'
  utf8: '0'
  vars: '0'
configure_requires:
  ExtUtils::MakeMaker: '0'
  perl: '5.008'
dynamic_config: 0
generated_by: 'Dist::Zilla version 6.012, CPAN::Meta::Converter version 2.150010'
license: perl
meta-spec:
  url: http://module-build.sourceforge.net/META-spec-v1.4.html
  version: '1.4'
name: Text-Template
provides:
  Text::Template:
    file: lib/Text/Template.pm
    version: '1.56'
  Text::Template::Preprocess:
    file: lib/Text/Template/Preprocess.pm
    version: '1.56'
requires:
  Carp: '0'
  Encode: '0'
  Exporter: '0'
  base: '0'
  perl: '5.008'
  strict: '0'
  warnings: '0'
resources:
  bugtracker: https://github.com/mschout/perl-text-template/issues
  homepage: https://github.com/mschout/perl-text-template
  repository: https://github.com/mschout/perl-text-template.git
version: '1.56'
x_generated_by_perl: v5.26.2
x_serialization_backend: 'YAML::Tiny version 1.73'
