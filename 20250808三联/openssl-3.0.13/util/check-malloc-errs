#! /bin/sh
# Copyright 2018 The OpenSSL Project Authors. All Rights Reserved.
#
# Licensed under the Apache License 2.0 (the "License").  You may not use
# this file except in compliance with the License.  You can obtain a copy
# in the file LICENSE in the source distribution or at
# https://www.openssl.org/source/license.html

(
    pcregrep -rnM 'OPENSSL_.?alloc.*\n.*if.*NULL.*\n.*return'  crypto ssl
    pcregrep -rnM 'if.*OPENSSL_.?alloc.*NULL.*\n.*.*return' crypto ssl
) | tee /tmp/out$$
X=0
test -s /tmp/out$$ && X=1
rm /tmp/out$$
exit $X
