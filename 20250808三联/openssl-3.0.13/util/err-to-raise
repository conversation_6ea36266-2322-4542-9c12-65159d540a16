#! /usr/bin/env perl
# Copyright 2019-2020 The OpenSSL Project Authors. All Rights Reserved.
#
# Licensed under the Apache License 2.0 (the "License").  You may not use
# this file except in compliance with the License.  You can obtain a copy
# in the file LICENSE in the source distribution or at
# https://www.openssl.org/source/license.html

# Run this program like this:
#       perl -pi util/err-to-raise files...
# or
#       git ls-files | grep '\.c$' | xargs perl -pi util/err-to-raise
# Consider running util/merge-err-lines first, to catch most (all?) of the
# cases where the XXXerr() call is split into two lines.

# Do not use this in engines/, they have their own error reporting functions,
# which do call ERR_raise().

use strict;
use warnings;

s/\b([_A-Z0-9]+)err\(\s*\w+\s*,/ERR_raise(ERR_LIB_$1,/;
