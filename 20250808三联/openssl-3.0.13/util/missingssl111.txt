# A list of libssl functions that are known to be missing documentation as
# used by the find-doc-nits -v -o option. The list is as of commit 1708e3e85b
# (the release of 1.1.1).
ERR_load_SSL_strings(3)
SRP_Calc_A_param(3)
SSL_COMP_get_name(3)
SSL_COMP_set0_compression_methods(3)
SSL_CONF_CTX_finish(3)
SSL_CTX_SRP_CTX_free(3)
SSL_CTX_SRP_CTX_init(3)
SSL_CTX_get0_certificate(3)
SSL_CTX_get0_ctlog_store(3)
SSL_CTX_get0_privatekey(3)
SSL_CTX_get_ssl_method(3)
SSL_CTX_set0_ctlog_store(3)
SSL_CTX_set_client_cert_engine(3)
SSL_CTX_set_cookie_generate_cb(3)
SSL_CTX_set_cookie_verify_cb(3)
SSL_CTX_set_not_resumable_session_callback(3)
SSL_CTX_set_purpose(3)
SSL_CTX_set_srp_cb_arg(3)
SSL_CTX_set_srp_client_pwd_callback(3)
SSL_CTX_set_srp_password(3)
SSL_CTX_set_srp_strength(3)
SSL_CTX_set_srp_username(3)
SSL_CTX_set_srp_username_callback(3)
SSL_CTX_set_srp_verify_param_callback(3)
SSL_CTX_set_trust(3)
SSL_SRP_CTX_free(3)
SSL_SRP_CTX_init(3)
SSL_add_ssl_module(3)
SSL_certs_clear(3)
SSL_copy_session_id(3)
SSL_dup_CA_list(3)
SSL_get0_dane(3)
SSL_get_certificate(3)
SSL_get_current_compression(3)
SSL_get_current_expansion(3)
SSL_get_finished(3)
SSL_get_peer_finished(3)
SSL_get_privatekey(3)
SSL_get_srp_N(3)
SSL_get_srp_g(3)
SSL_get_srp_userinfo(3)
SSL_get_srp_username(3)
SSL_set_SSL_CTX(3)
SSL_set_debug(3)
SSL_set_not_resumable_session_callback(3)
SSL_set_purpose(3)
SSL_set_session_secret_cb(3)
SSL_set_session_ticket_ext(3)
SSL_set_session_ticket_ext_cb(3)
SSL_set_srp_server_param(3)
SSL_set_srp_server_param_pw(3)
SSL_set_trust(3)
SSL_srp_server_param_with_username(3)
SSL_test_functions(3)
SSL_trace(3)
