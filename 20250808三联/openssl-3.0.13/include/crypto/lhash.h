/*
 * Copyright 2018-2021 The OpenSSL Project Authors. All Rights Reserved.
 *
 * Licensed under the Apache License 2.0 (the "License").  You may not use
 * this file except in compliance with the License.  You can obtain a copy
 * in the file LICENSE in the source distribution or at
 * https://www.openssl.org/source/license.html
 */

#ifndef OSSL_CRYPTO_LHASH_H
# define OSSL_CRYPTO_LHASH_H
# pragma once

unsigned long ossl_lh_strcasehash(const char *);

#endif  /* OSSL_CRYPTO_LHASH_H */
