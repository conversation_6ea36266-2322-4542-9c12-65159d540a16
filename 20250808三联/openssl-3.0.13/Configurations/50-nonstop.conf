#### Nonstop configurations
    # Common for all
    'nonstop-common' => {
        inherit_from     => [ 'BASE_unix' ],
        template         => 1,
        cc               => 'c99',
        cflags           => add_before(picker(debug   => '-g -O0',
                                              release => '-g -O2'),
                                       '-Wextensions',
                                       '-Wnowarn=203,220,272,734,770,1506',
                                       '-Wbuild_neutral_library',
                                       '-Wverbose'),
        defines          => add('OPENSSL_VPROC=$(OPENSSL_VPROC)',
                                '_XOPEN_SOURCE',
                                '_XOPEN_SOURCE_EXTENDED=1',
                                '_TANDEM_SOURCE',
                                'B_ENDIAN'),
        perl             => '/usr/bin/perl',
        shared_target    => 'nonstop-shared',
        shared_extension => ".so",
        ex_libs          => add('-lrld'),
        enable           => ['egd'],
        dso_scheme       => 'DLFCN',
        sys_id           => 'TANDEM',
    },

    ######################################################################
    # Additional variant settings, to be combined with nonstop-common
    # Note that these do not inherit anything.  However, the diverse values
    # are merged with other entries in an 'inherit_from'.
    #
    # These combine:
    # - System architecture (MIPS, Itanium, or x86)
    # - Execution environment (oss [default] or guardian)
    #
    # Unfortunately, they can't be separated into independent templates, because
    # a number of the above are encoded as different linkers, and by consequence,
    # different c99 linker flags (-Wld, -Weld, and -Wxld)
    #
    # In addition, the are modifiers for:
    # - Size of long + pointer (ilp32 [default] and lp64)
    # - Float type (neutral and tandem)
    #
    # Unfortunately, because the float types affect the linker settings, those
    # are divided per system architecture
    #
    # MIPS + guardian (unused but present for convenience):
    'nonstop-archenv-mips-guardian' => {
        template         => 1,
        defines          => ['NO_GETPID'],
        cflags           => '-Wtarget=tns/r -Wsystype=guardian',
        lflags           => '-Wld="-set systype guardian"',
        shared_ldflag    => '-Wshared -Wld="-soname $(@:lib%.so=%)"',
        shared_defflag   => '-Wld_obey=',
        shared_argfileflag => '-Wld_obey=',
    },

    # Itanium + guardian:
    'nonstop-archenv-itanium-guardian' => {
        template         => 1,
        defines          => ['NO_GETPID', '_TANDEM_ARCH=2'],
        cflags           => '-Wtarget=tns/e -Wsystype=guardian',
        lflags           => '-Weld="-set systype guardian"',
        shared_ldflag    => '-Wshared -Weld="-soname $(@:lib%.so=%)"',
        shared_defflag   => '-Weld_obey=',
        shared_argfileflag => '-Weld_obey=',
    },

    # x86 + guardian:
    'nonstop-archenv-x86_64-guardian' => {
        template         => 1,
        defines          => ['NO_GETPID', '_TANDEM_ARCH=3'],
        cflags           => '-Wtarget=tns/x -Wsystype=guardian',
        lflags           => '-Wxld="-set systype guardian"',
        shared_ldflag    => '-Wshared -Wxld="-soname $(@:lib%.so=%)"',
        shared_defflag   => '-Wxld_obey=',
        shared_argfileflag => '-Wxld_obey=',
    },

    # MIPS + oss (unused but present for convenience):
    'nonstop-archenv-mips-oss' => {
        template         => 1,
        cflags           => '-Wtarget=tns/r -Wsystype=oss',
        lflags           => '-Wld="-set systype oss"',
        shared_ldflag    => '-Wshared',
        shared_defflag   => '-Wld_obey=',
        shared_argfileflag => '-Wld_obey=',
    },
    # Itanium + oss:
    'nonstop-archenv-itanium-oss' => {
        template         => 1,
        defines          => ['_TANDEM_ARCH=2'],
        cflags           => '-Wtarget=tns/e -Wsystype=oss',
        lflags           => '-Weld="-set systype oss"',
        shared_ldflag    => '-Wshared',
        shared_defflag   => '-Weld_obey=',
        shared_argfileflag => '-Weld_obey=',
    },
    # x86_64 + oss:
    'nonstop-archenv-x86_64-oss' => {
        template         => 1,
        defines          => ['_TANDEM_ARCH=3'],
        cflags           => '-Wtarget=tns/x -Wsystype=oss',
        lflags           => '-Wxld="-set systype oss"',
        shared_ldflag    => '-Wshared',
        shared_defflag   => '-Wxld_obey=',
        shared_argfileflag => '-Wxld_obey=',
    },

    # Size variants
    'nonstop-ilp32' => {
        template         => 1,
        cflags           => '-Wilp32',
        bn_ops           => 'THIRTY_TWO_BIT',
    },
    'nonstop-lp64-itanium' => {
        template         => 1,
        cflags           => '-Wlp64',
        bn_ops           => 'SIXTY_FOUR_BIT_LONG RC4_CHAR RC4_CHUNK DES_INT DES_UNROLL BF_PTR',
    },
    'nonstop-lp64-x86_64' => {
        template         => 1,
        cflags           => '-Wlp64',
        lflags           => '-Wxld="-set data_model lp64"',
        bn_ops           => 'SIXTY_FOUR_BIT',
    },

    # Float variants
    'nonstop-nfloat-mips' => {
        template         => 1,
        lflags           => '-Wld="-set floattype neutral_float"',
    },
    'nonstop-tfloat-mips' => {
        template         => 1,
        lflags           => '-Wld="-set floattype tandem_float"',
    },
    'nonstop-efloat-itanium' => {
        template         => 1,
        cflags           => '-WIEEE_float',
        lflags           => '-Weld="-set floattype ieee_float"',
    },
    'nonstop-nfloat-itanium' => {
        template         => 1,
        lflags           => '-Weld="-set floattype neutral_float"',
    },
    'nonstop-tfloat-itanium' => {
        template         => 1,
        cflags           => '-WTandem_float',
        lflags           => '-Weld="-set floattype tandem_float"',
    },
    'nonstop-efloat-x86_64' => {
        template         => 1,
        cflags           => '-WIEEE_float',
        lflags           => '-Wxld="-set floattype ieee_float"',
    },
    'nonstop-nfloat-x86_64' => {
        template         => 1,
        lflags           => '-Wxld="-set floattype neutral_float"',
    },
    'nonstop-tfloat-x86_64' => {
        template         => 1,
        cflags           => '-WTandem_float',
        lflags           => '-Wxld="-set floattype tandem_float"',
    },

    ######################################################################
    # Build models
    'nonstop-model-put' => {
        template         => 1,
        defines          => ['_PUT_MODEL_',
                             '_REENTRANT', '_THREAD_SUPPORT_FUNCTIONS'],
        ex_libs          => '-lput',
    },
    'nonstop-model-spt' => {
        template         => 1,
        defines          => ['_SPT_MODEL_',
                             '_REENTRANT', '_ENABLE_FLOSS_THREADS'],
        ex_libs          => '-lspt',
    },

    # Additional floss model that can be combined with any of the other models.
    # If used without any of the other models, the entry that does so must
    # disable threads.
    'nonstop-model-floss' => {
        template         => 1,
        defines          => ['OPENSSL_TANDEM_FLOSS'],
        includes         => ['/usr/local/include'],
        ex_libs          => '-lfloss',
    },

    ######################################################################
    # Now for the entries themselves, let's combine things!
    'nonstop-nsx' => {
        inherit_from     => [ 'nonstop-common',
                              'nonstop-archenv-x86_64-oss',
                              'nonstop-ilp32',
                              'nonstop-efloat-x86_64' ],
        disable          => ['threads'],
    },
    'nonstop-nsx_put' => {
        inherit_from     => [ 'nonstop-common',
                              'nonstop-archenv-x86_64-oss',
                              'nonstop-ilp32',
                              'nonstop-efloat-x86_64',
                              'nonstop-model-put' ],
        multilib         => '-put',
    },
    'nonstop-nsx_64' => {
        inherit_from     => [ 'nonstop-common',
                              'nonstop-archenv-x86_64-oss',
                              'nonstop-lp64-x86_64',
                              'nonstop-efloat-x86_64' ],
        multilib         => '64',
        disable          => ['threads'],
    },
    'nonstop-nsx_64_put' => {
        inherit_from     => [ 'nonstop-common',
                              'nonstop-archenv-x86_64-oss',
                              'nonstop-lp64-x86_64',
                              'nonstop-efloat-x86_64',
                              'nonstop-model-put' ],
        multilib         => '64-put',
    },
    'nonstop-nsx_spt' => {
        inherit_from     => [ 'nonstop-common',
                              'nonstop-archenv-x86_64-oss',
                              'nonstop-ilp32',
                              'nonstop-efloat-x86_64',
                              'nonstop-model-spt' ],
        multilib         => '-spt',
    },
    'nonstop-nsx_spt_floss' => {
        inherit_from     => [ 'nonstop-common',
                              'nonstop-archenv-x86_64-oss',
                              'nonstop-ilp32',
                              'nonstop-efloat-x86_64',
                              'nonstop-model-floss',
                              'nonstop-model-spt'],
        multilib         => '-spt',
    },
    'nonstop-nsx_g' => {
        inherit_from     => [ 'nonstop-common',
                              'nonstop-archenv-x86_64-guardian',
                              'nonstop-ilp32', 'nonstop-nfloat-x86_64' ],
        disable          => ['threads'],
    },
    'nonstop-nsx_g_tandem' => {
        inherit_from     => [ 'nonstop-common',
                              'nonstop-archenv-x86_64-guardian',
                              'nonstop-ilp32', 'nonstop-tfloat-x86_64' ],
        disable          => ['threads'],
    },
    'nonstop-nsv' => {
        inherit_from     => [ 'nonstop-nsx' ],
    },
    'nonstop-nse' => {
        inherit_from     => [ 'nonstop-common',
                              'nonstop-archenv-itanium-oss',
                              'nonstop-ilp32',
                              'nonstop-efloat-itanium' ],
        disable          => ['threads'],
    },
    'nonstop-nse_put' => {
        inherit_from     => [ 'nonstop-common',
                              'nonstop-archenv-itanium-oss',
                              'nonstop-ilp32',
                              'nonstop-efloat-itanium',
                              'nonstop-model-put' ],
        multilib         => '-put',
    },
    'nonstop-nse_64' => {
        inherit_from     => [ 'nonstop-common',
                              'nonstop-archenv-itanium-oss',
                              'nonstop-lp64-itanium',
                              'nonstop-efloat-itanium' ],
        multilib         => '64',
        disable          => ['threads'],
    },
    'nonstop-nse_64_put' => {
        inherit_from     => [ 'nonstop-common',
                              'nonstop-archenv-itanium-oss',
                              'nonstop-lp64-itanium',
                              'nonstop-efloat-itanium',
                              'nonstop-model-put' ],
        multilib         => '64-put',
    },
    'nonstop-nse_spt' => {
        inherit_from     => [ 'nonstop-common',
                              'nonstop-archenv-itanium-oss',
                              'nonstop-ilp32',
                              'nonstop-efloat-itanium',
                              'nonstop-model-spt' ],
        multilib         => '-spt',
    },
    'nonstop-nse_spt_floss' => {
        inherit_from     => [ 'nonstop-common',
                              'nonstop-archenv-itanium-oss',
                              'nonstop-ilp32',
                              'nonstop-efloat-itanium',
                              'nonstop-model-floss', 'nonstop-model-spt' ],
        multilib         => '-spt',
    },
    'nonstop-nse_g' => {
        inherit_from     => [ 'nonstop-common',
                              'nonstop-archenv-itanium-guardian',
                              'nonstop-ilp32', 'nonstop-nfloat-itanium' ],
        disable          => ['threads'],
    },

    'nonstop-nse_g_tandem' => {
        inherit_from     => [ 'nonstop-common',
                              'nonstop-archenv-itanium-guardian',
                              'nonstop-ilp32', 'nonstop-tfloat-itanium' ],
        disable          => ['threads'],
    },
