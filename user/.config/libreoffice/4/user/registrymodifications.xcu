<?xml version="1.0" encoding="UTF-8"?>
<oor:items xmlns:oor="http://openoffice.org/2001/registry" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
<item oor:path="/org.openoffice.Office.Common/Misc"><prop oor:name="FirstRun" oor:op="fuse"><value>false</value></prop></item>
<item oor:path="/org.openoffice.Office.Common/Misc"><prop oor:name="LastTipOfTheDayID" oor:op="fuse"><value>0</value></prop></item>
<item oor:path="/org.openoffice.Office.Common/Misc"><prop oor:name="LastTipOfTheDayShown" oor:op="fuse"><value>20146</value></prop></item>
<item oor:path="/org.openoffice.Office.Common/Misc"><prop oor:name="Persona" oor:op="fuse"><value>no</value></prop></item>
<item oor:path="/org.openoffice.Office.Common/Misc"><prop oor:name="PersonaSettings" oor:op="fuse"><value></value></prop></item>
<item oor:path="/org.openoffice.Office.Common/Misc"><prop oor:name="ShowTipOfTheDay" oor:op="fuse"><value>true</value></prop></item>
<item oor:path="/org.openoffice.Office.Linguistic/ServiceManager/LastFoundSpellCheckers"><prop oor:name="en-US" oor:op="fuse" oor:type="oor:string-list"><value><it>org.openoffice.lingu.MySpellSpellChecker</it></value></prop></item>
<item oor:path="/org.openoffice.Office.Linguistic/ServiceManager/LastFoundSpellCheckers"><prop oor:name="fr" oor:op="fuse" oor:type="oor:string-list"><value><it>org.openoffice.lingu.MySpellSpellChecker</it></value></prop></item>
<item oor:path="/org.openoffice.Office.Linguistic/ServiceManager/LastFoundSpellCheckers"><prop oor:name="fr-BE" oor:op="fuse" oor:type="oor:string-list"><value><it>org.openoffice.lingu.MySpellSpellChecker</it></value></prop></item>
<item oor:path="/org.openoffice.Office.Linguistic/ServiceManager/LastFoundSpellCheckers"><prop oor:name="fr-CA" oor:op="fuse" oor:type="oor:string-list"><value><it>org.openoffice.lingu.MySpellSpellChecker</it></value></prop></item>
<item oor:path="/org.openoffice.Office.Linguistic/ServiceManager/LastFoundSpellCheckers"><prop oor:name="fr-CH" oor:op="fuse" oor:type="oor:string-list"><value><it>org.openoffice.lingu.MySpellSpellChecker</it></value></prop></item>
<item oor:path="/org.openoffice.Office.Linguistic/ServiceManager/LastFoundSpellCheckers"><prop oor:name="fr-FR" oor:op="fuse" oor:type="oor:string-list"><value><it>org.openoffice.lingu.MySpellSpellChecker</it></value></prop></item>
<item oor:path="/org.openoffice.Office.Linguistic/ServiceManager/LastFoundSpellCheckers"><prop oor:name="fr-LU" oor:op="fuse" oor:type="oor:string-list"><value><it>org.openoffice.lingu.MySpellSpellChecker</it></value></prop></item>
<item oor:path="/org.openoffice.Office.Linguistic/ServiceManager/LastFoundSpellCheckers"><prop oor:name="fr-MC" oor:op="fuse" oor:type="oor:string-list"><value><it>org.openoffice.lingu.MySpellSpellChecker</it></value></prop></item>
<item oor:path="/org.openoffice.Office.Linguistic/ServiceManager/SpellCheckerList"><prop oor:name="en-US" oor:op="fuse" oor:type="oor:string-list"><value><it>org.openoffice.lingu.MySpellSpellChecker</it></value></prop></item>
<item oor:path="/org.openoffice.Office.Linguistic/ServiceManager/SpellCheckerList"><prop oor:name="fr" oor:op="fuse" oor:type="oor:string-list"><value><it>org.openoffice.lingu.MySpellSpellChecker</it></value></prop></item>
<item oor:path="/org.openoffice.Office.Linguistic/ServiceManager/SpellCheckerList"><prop oor:name="fr-BE" oor:op="fuse" oor:type="oor:string-list"><value><it>org.openoffice.lingu.MySpellSpellChecker</it></value></prop></item>
<item oor:path="/org.openoffice.Office.Linguistic/ServiceManager/SpellCheckerList"><prop oor:name="fr-CA" oor:op="fuse" oor:type="oor:string-list"><value><it>org.openoffice.lingu.MySpellSpellChecker</it></value></prop></item>
<item oor:path="/org.openoffice.Office.Linguistic/ServiceManager/SpellCheckerList"><prop oor:name="fr-CH" oor:op="fuse" oor:type="oor:string-list"><value><it>org.openoffice.lingu.MySpellSpellChecker</it></value></prop></item>
<item oor:path="/org.openoffice.Office.Linguistic/ServiceManager/SpellCheckerList"><prop oor:name="fr-FR" oor:op="fuse" oor:type="oor:string-list"><value><it>org.openoffice.lingu.MySpellSpellChecker</it></value></prop></item>
<item oor:path="/org.openoffice.Office.Linguistic/ServiceManager/SpellCheckerList"><prop oor:name="fr-LU" oor:op="fuse" oor:type="oor:string-list"><value><it>org.openoffice.lingu.MySpellSpellChecker</it></value></prop></item>
<item oor:path="/org.openoffice.Office.Linguistic/ServiceManager/SpellCheckerList"><prop oor:name="fr-MC" oor:op="fuse" oor:type="oor:string-list"><value><it>org.openoffice.lingu.MySpellSpellChecker</it></value></prop></item>
<item oor:path="/org.openoffice.Office.Logging/Settings"><node oor:name="unopkg" oor:op="replace"><prop oor:name="LogLevel" oor:op="fuse"><value>2147483647</value></prop><prop oor:name="DefaultHandler" oor:op="fuse"><value>com.sun.star.logging.FileHandler</value></prop><node oor:name="HandlerSettings"><prop oor:name="FileURL" oor:op="fuse"><value>$(userurl)/$(loggername).log</value></prop></node><prop oor:name="DefaultFormatter" oor:op="fuse"><value>com.sun.star.logging.PlainTextFormatter</value></prop><node oor:name="FormatterSettings"></node></node></item>
<item oor:path="/org.openoffice.Office.Recovery/RecoveryInfo"><prop oor:name="SessionData" oor:op="fuse"><value>false</value></prop></item>
<item oor:path="/org.openoffice.Office.Recovery/RecoveryList"><node oor:name="recovery_item_1" oor:op="remove"/></item>
<item oor:path="/org.openoffice.Office.UI.Sidebar/Content/DeckList/org.openoffice.Office.UI.Sidebar:Deck['GalleryDeck']"><prop oor:name="ContextList" oor:op="fuse"><value><it>com.sun.star.sheet.SpreadsheetDocument, any, visible</it><it>com.sun.star.drawing.DrawingDocument, any, visible</it><it>com.sun.star.presentation.PresentationDocument, any, visible</it><it>com.sun.star.text.TextDocument, any, visible</it><it>com.sun.star.text.GlobalDocument, any, visible</it><it>com.sun.star.text.WebDocument, any, visible</it><it>com.sun.star.xforms.XMLFormDocument, any, visible</it><it>com.sun.star.sdb.FormDesign, any, visible</it><it>com.sun.star.sdb.TextReportDesign, any, visible</it></value></prop></item>
<item oor:path="/org.openoffice.Office.UI.Sidebar/Content/DeckList/org.openoffice.Office.UI.Sidebar:Deck['InspectorDeck']"><prop oor:name="ContextList" oor:op="fuse"><value><it>com.sun.star.text.TextDocument, any, visible</it><it>com.sun.star.text.GlobalDocument, any, visible</it><it>com.sun.star.text.WebDocument, any, visible</it><it>com.sun.star.xforms.XMLFormDocument, any, visible</it><it>com.sun.star.sdb.FormDesign, any, visible</it><it>com.sun.star.sdb.TextReportDesign, any, visible</it></value></prop></item>
<item oor:path="/org.openoffice.Office.UI.Sidebar/Content/DeckList/org.openoffice.Office.UI.Sidebar:Deck['NavigatorDeck']"><prop oor:name="ContextList" oor:op="fuse"><value><it>com.sun.star.sheet.SpreadsheetDocument, any, visible</it><it>com.sun.star.drawing.DrawingDocument, any, visible</it><it>com.sun.star.presentation.PresentationDocument, any, visible</it><it>com.sun.star.text.TextDocument, any, visible</it><it>com.sun.star.text.GlobalDocument, any, visible</it><it>com.sun.star.text.WebDocument, any, visible</it><it>com.sun.star.xforms.XMLFormDocument, any, visible</it><it>com.sun.star.sdb.FormDesign, any, visible</it><it>com.sun.star.sdb.TextReportDesign, any, visible</it></value></prop></item>
<item oor:path="/org.openoffice.Office.UI.Sidebar/Content/DeckList/org.openoffice.Office.UI.Sidebar:Deck['PropertyDeck']"><prop oor:name="ContextList" oor:op="fuse"><value><it>any, any, visible</it></value></prop></item>
<item oor:path="/org.openoffice.Office.UI.Sidebar/Content/DeckList/org.openoffice.Office.UI.Sidebar:Deck['StyleListDeck']"><prop oor:name="ContextList" oor:op="fuse"><value><it>com.sun.star.sheet.SpreadsheetDocument, any, visible</it><it>com.sun.star.drawing.DrawingDocument, any, visible</it><it>com.sun.star.presentation.PresentationDocument, any, visible</it><it>com.sun.star.text.TextDocument, any, visible</it><it>com.sun.star.text.GlobalDocument, any, visible</it><it>com.sun.star.text.WebDocument, any, visible</it><it>com.sun.star.xforms.XMLFormDocument, any, visible</it><it>com.sun.star.sdb.FormDesign, any, visible</it><it>com.sun.star.sdb.TextReportDesign, any, visible</it></value></prop></item>
<item oor:path="/org.openoffice.Office.UI.Sidebar/Content/DeckList/org.openoffice.Office.UI.Sidebar:Deck['SwManageChangesDeck']"><prop oor:name="ContextList" oor:op="fuse"><value><it>com.sun.star.text.TextDocument, Annotation, visible</it><it>com.sun.star.text.GlobalDocument, Annotation, visible</it><it>com.sun.star.text.WebDocument, Annotation, visible</it><it>com.sun.star.xforms.XMLFormDocument, Annotation, visible</it><it>com.sun.star.sdb.FormDesign, Annotation, visible</it><it>com.sun.star.sdb.TextReportDesign, Annotation, visible</it><it>com.sun.star.text.TextDocument, DrawText, visible</it><it>com.sun.star.text.GlobalDocument, DrawText, visible</it><it>com.sun.star.text.WebDocument, DrawText, visible</it><it>com.sun.star.xforms.XMLFormDocument, DrawText, visible</it><it>com.sun.star.sdb.FormDesign, DrawText, visible</it><it>com.sun.star.sdb.TextReportDesign, DrawText, visible</it><it>com.sun.star.text.TextDocument, Table, visible</it><it>com.sun.star.text.GlobalDocument, Table, visible</it><it>com.sun.star.text.WebDocument, Table, visible</it><it>com.sun.star.xforms.XMLFormDocument, Table, visible</it><it>com.sun.star.sdb.FormDesign, Table, visible</it><it>com.sun.star.sdb.TextReportDesign, Table, visible</it><it>com.sun.star.text.TextDocument, Text, visible</it><it>com.sun.star.text.GlobalDocument, Text, visible</it><it>com.sun.star.text.WebDocument, Text, visible</it><it>com.sun.star.xforms.XMLFormDocument, Text, visible</it><it>com.sun.star.sdb.FormDesign, Text, visible</it><it>com.sun.star.sdb.TextReportDesign, Text, visible</it><it>com.sun.star.text.TextDocument, default, visible</it><it>com.sun.star.text.GlobalDocument, default, visible</it><it>com.sun.star.text.WebDocument, default, visible</it><it>com.sun.star.xforms.XMLFormDocument, default, visible</it><it>com.sun.star.sdb.FormDesign, default, visible</it><it>com.sun.star.sdb.TextReportDesign, default, visible</it></value></prop></item>
<item oor:path="/org.openoffice.Office.UI.Sidebar/Content/DeckList/org.openoffice.Office.UI.Sidebar:Deck['WriterPageDeck']"><prop oor:name="ContextList" oor:op="fuse"><value><it>com.sun.star.text.TextDocument, any, visible</it><it>com.sun.star.text.GlobalDocument, any, visible</it><it>com.sun.star.text.WebDocument, any, visible</it><it>com.sun.star.xforms.XMLFormDocument, any, visible</it><it>com.sun.star.sdb.FormDesign, any, visible</it><it>com.sun.star.sdb.TextReportDesign, any, visible</it></value></prop></item>
<item oor:path="/org.openoffice.Office.UI.Sidebar/Content"><prop oor:name="LastActiveDeck" oor:op="fuse"><value><it>any,PropertyDeck</it><it>com.sun.star.formula.FormulaProperties,ElementsDeck</it><it>com.sun.star.text.TextDocument,PropertyDeck</it></value></prop></item>
<item oor:path="/org.openoffice.Office.UI.Sidebar/Content/PanelList/org.openoffice.Office.UI.Sidebar:Panel['ParaPropertyPanel']"><prop oor:name="ContextList" oor:op="fuse"><value><it>com.sun.star.sheet.SpreadsheetDocument, DrawText, visible, .uno:ParagraphDialog</it><it>com.sun.star.sheet.SpreadsheetDocument, DrawLine, hidden, .uno:ParagraphDialog</it><it>com.sun.star.drawing.DrawingDocument, DrawLine, hidden, .uno:ParagraphDialog</it><it>com.sun.star.presentation.PresentationDocument, DrawLine, hidden, .uno:ParagraphDialog</it><it>com.sun.star.drawing.DrawingDocument, 3DObject, hidden, .uno:ParagraphDialog</it><it>com.sun.star.presentation.PresentationDocument, 3DObject, hidden, .uno:ParagraphDialog</it><it>com.sun.star.drawing.DrawingDocument, Draw, hidden, .uno:ParagraphDialog</it><it>com.sun.star.presentation.PresentationDocument, Draw, hidden, .uno:ParagraphDialog</it><it>com.sun.star.drawing.DrawingDocument, DrawFontwork, hidden, .uno:ParagraphDialog</it><it>com.sun.star.presentation.PresentationDocument, DrawFontwork, hidden, .uno:ParagraphDialog</it><it>com.sun.star.drawing.DrawingDocument, DrawText, visible, .uno:ParagraphDialog</it><it>com.sun.star.presentation.PresentationDocument, DrawText, visible, .uno:ParagraphDialog</it><it>com.sun.star.drawing.DrawingDocument, Graphic, hidden, .uno:ParagraphDialog</it><it>com.sun.star.presentation.PresentationDocument, Graphic, hidden, .uno:ParagraphDialog</it><it>com.sun.star.drawing.DrawingDocument, Table, visible, .uno:ParagraphDialog</it><it>com.sun.star.presentation.PresentationDocument, Table, visible, .uno:ParagraphDialog</it><it>com.sun.star.drawing.DrawingDocument, TextObject, visible, .uno:ParagraphDialog</it><it>com.sun.star.presentation.PresentationDocument, TextObject, visible, .uno:ParagraphDialog</it><it>com.sun.star.text.TextDocument, Annotation, visible, .uno:ParagraphDialog</it><it>com.sun.star.text.GlobalDocument, Annotation, visible, .uno:ParagraphDialog</it><it>com.sun.star.text.WebDocument, Annotation, visible, .uno:ParagraphDialog</it><it>com.sun.star.xforms.XMLFormDocument, Annotation, visible, .uno:ParagraphDialog</it><it>com.sun.star.sdb.FormDesign, Annotation, visible, .uno:ParagraphDialog</it><it>com.sun.star.sdb.TextReportDesign, Annotation, visible, .uno:ParagraphDialog</it><it>com.sun.star.text.TextDocument, DrawText, visible, .uno:ParagraphDialog</it><it>com.sun.star.text.GlobalDocument, DrawText, visible, .uno:ParagraphDialog</it><it>com.sun.star.text.WebDocument, DrawText, visible, .uno:ParagraphDialog</it><it>com.sun.star.xforms.XMLFormDocument, DrawText, visible, .uno:ParagraphDialog</it><it>com.sun.star.sdb.FormDesign, DrawText, visible, .uno:ParagraphDialog</it><it>com.sun.star.sdb.TextReportDesign, DrawText, visible, .uno:ParagraphDialog</it><it>com.sun.star.text.TextDocument, Table, visible, .uno:ParagraphDialog</it><it>com.sun.star.text.GlobalDocument, Table, visible, .uno:ParagraphDialog</it><it>com.sun.star.text.WebDocument, Table, visible, .uno:ParagraphDialog</it><it>com.sun.star.xforms.XMLFormDocument, Table, visible, .uno:ParagraphDialog</it><it>com.sun.star.sdb.FormDesign, Table, visible, .uno:ParagraphDialog</it><it>com.sun.star.sdb.TextReportDesign, Table, visible, .uno:ParagraphDialog</it><it>com.sun.star.text.TextDocument, Text, visible, .uno:ParagraphDialog</it><it>com.sun.star.text.GlobalDocument, Text, visible, .uno:ParagraphDialog</it><it>com.sun.star.text.WebDocument, Text, visible, .uno:ParagraphDialog</it><it>com.sun.star.xforms.XMLFormDocument, Text, visible, .uno:ParagraphDialog</it><it>com.sun.star.sdb.FormDesign, Text, visible, .uno:ParagraphDialog</it><it>com.sun.star.sdb.TextReportDesign, Text, visible, .uno:ParagraphDialog</it><it>com.sun.star.text.TextDocument, default, visible, .uno:ParagraphDialog</it><it>com.sun.star.text.GlobalDocument, default, visible, .uno:ParagraphDialog</it><it>com.sun.star.text.WebDocument, default, visible, .uno:ParagraphDialog</it><it>com.sun.star.xforms.XMLFormDocument, default, visible, .uno:ParagraphDialog</it><it>com.sun.star.sdb.FormDesign, default, visible, .uno:ParagraphDialog</it><it>com.sun.star.sdb.TextReportDesign, default, visible, .uno:ParagraphDialog</it></value></prop></item>
<item oor:path="/org.openoffice.Office.UI.Sidebar/Content/PanelList/org.openoffice.Office.UI.Sidebar:Panel['StylesPropertyPanel']"><prop oor:name="ContextList" oor:op="fuse"><value><it>com.sun.star.text.TextDocument, Annotation, visible, .uno:EditStyle</it><it>com.sun.star.text.GlobalDocument, Annotation, visible, .uno:EditStyle</it><it>com.sun.star.text.WebDocument, Annotation, visible, .uno:EditStyle</it><it>com.sun.star.xforms.XMLFormDocument, Annotation, visible, .uno:EditStyle</it><it>com.sun.star.sdb.FormDesign, Annotation, visible, .uno:EditStyle</it><it>com.sun.star.sdb.TextReportDesign, Annotation, visible, .uno:EditStyle</it><it>com.sun.star.text.TextDocument, DrawText, visible, .uno:EditStyle</it><it>com.sun.star.text.GlobalDocument, DrawText, visible, .uno:EditStyle</it><it>com.sun.star.text.WebDocument, DrawText, visible, .uno:EditStyle</it><it>com.sun.star.xforms.XMLFormDocument, DrawText, visible, .uno:EditStyle</it><it>com.sun.star.sdb.FormDesign, DrawText, visible, .uno:EditStyle</it><it>com.sun.star.sdb.TextReportDesign, DrawText, visible, .uno:EditStyle</it><it>com.sun.star.text.TextDocument, Table, visible, .uno:EditStyle</it><it>com.sun.star.text.GlobalDocument, Table, visible, .uno:EditStyle</it><it>com.sun.star.text.WebDocument, Table, visible, .uno:EditStyle</it><it>com.sun.star.xforms.XMLFormDocument, Table, visible, .uno:EditStyle</it><it>com.sun.star.sdb.FormDesign, Table, visible, .uno:EditStyle</it><it>com.sun.star.sdb.TextReportDesign, Table, visible, .uno:EditStyle</it><it>com.sun.star.text.TextDocument, Text, visible, .uno:EditStyle</it><it>com.sun.star.text.GlobalDocument, Text, visible, .uno:EditStyle</it><it>com.sun.star.text.WebDocument, Text, visible, .uno:EditStyle</it><it>com.sun.star.xforms.XMLFormDocument, Text, visible, .uno:EditStyle</it><it>com.sun.star.sdb.FormDesign, Text, visible, .uno:EditStyle</it><it>com.sun.star.sdb.TextReportDesign, Text, visible, .uno:EditStyle</it><it>com.sun.star.text.TextDocument, default, visible, .uno:EditStyle</it><it>com.sun.star.text.GlobalDocument, default, visible, .uno:EditStyle</it><it>com.sun.star.text.WebDocument, default, visible, .uno:EditStyle</it><it>com.sun.star.xforms.XMLFormDocument, default, visible, .uno:EditStyle</it><it>com.sun.star.sdb.FormDesign, default, visible, .uno:EditStyle</it><it>com.sun.star.sdb.TextReportDesign, default, visible, .uno:EditStyle</it><it>com.sun.star.sheet.SpreadsheetDocument, Auditing, visible, .uno:EditStyle</it><it>com.sun.star.sheet.SpreadsheetDocument, Cell, visible, .uno:EditStyle</it><it>com.sun.star.sheet.SpreadsheetDocument, default, visible, .uno:EditStyle</it><it>com.sun.star.sheet.SpreadsheetDocument, EditCell, visible, .uno:EditStyle</it><it>com.sun.star.sheet.SpreadsheetDocument, Pivot, visible, .uno:EditStyle</it><it>com.sun.star.sheet.SpreadsheetDocument, Sparkline, visible, .uno:EditStyle</it></value></prop></item>
<item oor:path="/org.openoffice.Office.UI.Sidebar/Content/PanelList/org.openoffice.Office.UI.Sidebar:Panel['TextPropertyPanel']"><prop oor:name="ContextList" oor:op="fuse"><value><it>com.sun.star.sheet.SpreadsheetDocument, Auditing, visible, .uno:CellTextDlg</it><it>com.sun.star.sheet.SpreadsheetDocument, Cell, visible, .uno:CellTextDlg</it><it>com.sun.star.sheet.SpreadsheetDocument, default, visible, .uno:CellTextDlg</it><it>com.sun.star.sheet.SpreadsheetDocument, DrawText, visible, .uno:FontDialog</it><it>com.sun.star.sheet.SpreadsheetDocument, DrawLine, hidden, .uno:FontDialog</it><it>com.sun.star.sheet.SpreadsheetDocument, EditCell, visible, .uno:FontDialog</it><it>com.sun.star.sheet.SpreadsheetDocument, Pivot, visible, .uno:CellTextDlg</it><it>com.sun.star.sheet.SpreadsheetDocument, Sparkline, visible, .uno:FontDialog</it><it>com.sun.star.drawing.DrawingDocument, Draw, hidden, .uno:FontDialog</it><it>com.sun.star.presentation.PresentationDocument, Draw, hidden, .uno:FontDialog</it><it>com.sun.star.drawing.DrawingDocument, DrawFontwork, hidden, .uno:FontDialog</it><it>com.sun.star.presentation.PresentationDocument, DrawFontwork, hidden, .uno:FontDialog</it><it>com.sun.star.drawing.DrawingDocument, DrawText, visible, .uno:FontDialog</it><it>com.sun.star.presentation.PresentationDocument, DrawText, visible, .uno:FontDialog</it><it>com.sun.star.drawing.DrawingDocument, DrawLine, hidden, .uno:FontDialog</it><it>com.sun.star.presentation.PresentationDocument, DrawLine, hidden, .uno:FontDialog</it><it>com.sun.star.drawing.DrawingDocument, Graphic, hidden, .uno:FontDialog</it><it>com.sun.star.presentation.PresentationDocument, Graphic, hidden, .uno:FontDialog</it><it>com.sun.star.drawing.DrawingDocument, OutlineText, visible, .uno:FontDialog</it><it>com.sun.star.presentation.PresentationDocument, OutlineText, visible, .uno:FontDialog</it><it>com.sun.star.drawing.DrawingDocument, Table, visible, .uno:FontDialog</it><it>com.sun.star.presentation.PresentationDocument, Table, visible, .uno:FontDialog</it><it>com.sun.star.drawing.DrawingDocument, TextObject, visible, .uno:FontDialog</it><it>com.sun.star.presentation.PresentationDocument, TextObject, visible, .uno:FontDialog</it><it>com.sun.star.text.TextDocument, Annotation, visible, .uno:FontDialog</it><it>com.sun.star.text.GlobalDocument, Annotation, visible, .uno:FontDialog</it><it>com.sun.star.text.WebDocument, Annotation, visible, .uno:FontDialog</it><it>com.sun.star.xforms.XMLFormDocument, Annotation, visible, .uno:FontDialog</it><it>com.sun.star.sdb.FormDesign, Annotation, visible, .uno:FontDialog</it><it>com.sun.star.sdb.TextReportDesign, Annotation, visible, .uno:FontDialog</it><it>com.sun.star.text.TextDocument, DrawText, visible, .uno:FontDialog</it><it>com.sun.star.text.GlobalDocument, DrawText, visible, .uno:FontDialog</it><it>com.sun.star.text.WebDocument, DrawText, visible, .uno:FontDialog</it><it>com.sun.star.xforms.XMLFormDocument, DrawText, visible, .uno:FontDialog</it><it>com.sun.star.sdb.FormDesign, DrawText, visible, .uno:FontDialog</it><it>com.sun.star.sdb.TextReportDesign, DrawText, visible, .uno:FontDialog</it><it>com.sun.star.text.TextDocument, Table, visible, .uno:FontDialog</it><it>com.sun.star.text.GlobalDocument, Table, visible, .uno:FontDialog</it><it>com.sun.star.text.WebDocument, Table, visible, .uno:FontDialog</it><it>com.sun.star.xforms.XMLFormDocument, Table, visible, .uno:FontDialog</it><it>com.sun.star.sdb.FormDesign, Table, visible, .uno:FontDialog</it><it>com.sun.star.sdb.TextReportDesign, Table, visible, .uno:FontDialog</it><it>com.sun.star.text.TextDocument, Text, visible, .uno:FontDialog</it><it>com.sun.star.text.GlobalDocument, Text, visible, .uno:FontDialog</it><it>com.sun.star.text.WebDocument, Text, visible, .uno:FontDialog</it><it>com.sun.star.xforms.XMLFormDocument, Text, visible, .uno:FontDialog</it><it>com.sun.star.sdb.FormDesign, Text, visible, .uno:FontDialog</it><it>com.sun.star.sdb.TextReportDesign, Text, visible, .uno:FontDialog</it><it>com.sun.star.text.TextDocument, default, visible, .uno:FontDialog</it><it>com.sun.star.text.GlobalDocument, default, visible, .uno:FontDialog</it><it>com.sun.star.text.WebDocument, default, visible, .uno:FontDialog</it><it>com.sun.star.xforms.XMLFormDocument, default, visible, .uno:FontDialog</it><it>com.sun.star.sdb.FormDesign, default, visible, .uno:FontDialog</it><it>com.sun.star.sdb.TextReportDesign, default, visible, .uno:FontDialog</it></value></prop></item>
<item oor:path="/org.openoffice.Office.UI.WriterWindowState/UIElements/States/org.openoffice.Office.UI.WindowState:WindowStateType['private:resource/toolbar/standardbar']"><prop oor:name="ContextActive" oor:op="fuse"><value>true</value></prop></item>
<item oor:path="/org.openoffice.Office.UI.WriterWindowState/UIElements/States/org.openoffice.Office.UI.WindowState:WindowStateType['private:resource/toolbar/standardbar']"><prop oor:name="ContextSensitive" oor:op="fuse"><value>true</value></prop></item>
<item oor:path="/org.openoffice.Office.UI.WriterWindowState/UIElements/States/org.openoffice.Office.UI.WindowState:WindowStateType['private:resource/toolbar/standardbar']"><prop oor:name="DockPos" oor:op="fuse"><value>0,0</value></prop></item>
<item oor:path="/org.openoffice.Office.UI.WriterWindowState/UIElements/States/org.openoffice.Office.UI.WindowState:WindowStateType['private:resource/toolbar/standardbar']"><prop oor:name="Docked" oor:op="fuse"><value>true</value></prop></item>
<item oor:path="/org.openoffice.Office.UI.WriterWindowState/UIElements/States/org.openoffice.Office.UI.WindowState:WindowStateType['private:resource/toolbar/standardbar']"><prop oor:name="DockingArea" oor:op="fuse"><value>0</value></prop></item>
<item oor:path="/org.openoffice.Office.UI.WriterWindowState/UIElements/States/org.openoffice.Office.UI.WindowState:WindowStateType['private:resource/toolbar/standardbar']"><prop oor:name="HideFromToolbarMenu" oor:op="fuse"><value>false</value></prop></item>
<item oor:path="/org.openoffice.Office.UI.WriterWindowState/UIElements/States/org.openoffice.Office.UI.WindowState:WindowStateType['private:resource/toolbar/standardbar']"><prop oor:name="Locked" oor:op="fuse"><value>true</value></prop></item>
<item oor:path="/org.openoffice.Office.UI.WriterWindowState/UIElements/States/org.openoffice.Office.UI.WindowState:WindowStateType['private:resource/toolbar/standardbar']"><prop oor:name="NoClose" oor:op="fuse"><value>false</value></prop></item>
<item oor:path="/org.openoffice.Office.UI.WriterWindowState/UIElements/States/org.openoffice.Office.UI.WindowState:WindowStateType['private:resource/toolbar/standardbar']"><prop oor:name="Pos" oor:op="fuse"><value>2147483647,2147483647</value></prop></item>
<item oor:path="/org.openoffice.Office.UI.WriterWindowState/UIElements/States/org.openoffice.Office.UI.WindowState:WindowStateType['private:resource/toolbar/standardbar']"><prop oor:name="Size" oor:op="fuse"><value>0,0</value></prop></item>
<item oor:path="/org.openoffice.Office.UI.WriterWindowState/UIElements/States/org.openoffice.Office.UI.WindowState:WindowStateType['private:resource/toolbar/standardbar']"><prop oor:name="SoftClose" oor:op="fuse"><value>false</value></prop></item>
<item oor:path="/org.openoffice.Office.UI.WriterWindowState/UIElements/States/org.openoffice.Office.UI.WindowState:WindowStateType['private:resource/toolbar/standardbar']"><prop oor:name="Style" oor:op="fuse"><value>0</value></prop></item>
<item oor:path="/org.openoffice.Office.UI.WriterWindowState/UIElements/States/org.openoffice.Office.UI.WindowState:WindowStateType['private:resource/toolbar/standardbar']/UIName"><value xml:lang="zh-CN">标准</value></item>
<item oor:path="/org.openoffice.Office.UI.WriterWindowState/UIElements/States/org.openoffice.Office.UI.WindowState:WindowStateType['private:resource/toolbar/standardbar']"><prop oor:name="Visible" oor:op="fuse"><value>true</value></prop></item>
<item oor:path="/org.openoffice.Office.UI.WriterWindowState/UIElements/States/org.openoffice.Office.UI.WindowState:WindowStateType['private:resource/toolbar/textobjectbar']"><prop oor:name="ContextActive" oor:op="fuse"><value>true</value></prop></item>
<item oor:path="/org.openoffice.Office.UI.WriterWindowState/UIElements/States/org.openoffice.Office.UI.WindowState:WindowStateType['private:resource/toolbar/textobjectbar']"><prop oor:name="ContextSensitive" oor:op="fuse"><value>true</value></prop></item>
<item oor:path="/org.openoffice.Office.UI.WriterWindowState/UIElements/States/org.openoffice.Office.UI.WindowState:WindowStateType['private:resource/toolbar/textobjectbar']"><prop oor:name="DockPos" oor:op="fuse"><value>0,1</value></prop></item>
<item oor:path="/org.openoffice.Office.UI.WriterWindowState/UIElements/States/org.openoffice.Office.UI.WindowState:WindowStateType['private:resource/toolbar/textobjectbar']"><prop oor:name="Docked" oor:op="fuse"><value>true</value></prop></item>
<item oor:path="/org.openoffice.Office.UI.WriterWindowState/UIElements/States/org.openoffice.Office.UI.WindowState:WindowStateType['private:resource/toolbar/textobjectbar']"><prop oor:name="DockingArea" oor:op="fuse"><value>0</value></prop></item>
<item oor:path="/org.openoffice.Office.UI.WriterWindowState/UIElements/States/org.openoffice.Office.UI.WindowState:WindowStateType['private:resource/toolbar/textobjectbar']"><prop oor:name="HideFromToolbarMenu" oor:op="fuse"><value>false</value></prop></item>
<item oor:path="/org.openoffice.Office.UI.WriterWindowState/UIElements/States/org.openoffice.Office.UI.WindowState:WindowStateType['private:resource/toolbar/textobjectbar']"><prop oor:name="Locked" oor:op="fuse"><value>true</value></prop></item>
<item oor:path="/org.openoffice.Office.UI.WriterWindowState/UIElements/States/org.openoffice.Office.UI.WindowState:WindowStateType['private:resource/toolbar/textobjectbar']"><prop oor:name="NoClose" oor:op="fuse"><value>false</value></prop></item>
<item oor:path="/org.openoffice.Office.UI.WriterWindowState/UIElements/States/org.openoffice.Office.UI.WindowState:WindowStateType['private:resource/toolbar/textobjectbar']"><prop oor:name="Pos" oor:op="fuse"><value>2147483647,2147483647</value></prop></item>
<item oor:path="/org.openoffice.Office.UI.WriterWindowState/UIElements/States/org.openoffice.Office.UI.WindowState:WindowStateType['private:resource/toolbar/textobjectbar']"><prop oor:name="Size" oor:op="fuse"><value>0,0</value></prop></item>
<item oor:path="/org.openoffice.Office.UI.WriterWindowState/UIElements/States/org.openoffice.Office.UI.WindowState:WindowStateType['private:resource/toolbar/textobjectbar']"><prop oor:name="SoftClose" oor:op="fuse"><value>false</value></prop></item>
<item oor:path="/org.openoffice.Office.UI.WriterWindowState/UIElements/States/org.openoffice.Office.UI.WindowState:WindowStateType['private:resource/toolbar/textobjectbar']"><prop oor:name="Style" oor:op="fuse"><value>0</value></prop></item>
<item oor:path="/org.openoffice.Office.UI.WriterWindowState/UIElements/States/org.openoffice.Office.UI.WindowState:WindowStateType['private:resource/toolbar/textobjectbar']/UIName"><value xml:lang="zh-CN">格式</value></item>
<item oor:path="/org.openoffice.Office.UI.WriterWindowState/UIElements/States/org.openoffice.Office.UI.WindowState:WindowStateType['private:resource/toolbar/textobjectbar']"><prop oor:name="Visible" oor:op="fuse"><value>true</value></prop></item>
<item oor:path="/org.openoffice.Office.Views/Windows"><node oor:name="SplitWindow0" oor:op="replace"><prop oor:name="Visible" oor:op="fuse"><value xsi:nil="true"/></prop><node oor:name="UserData"><prop oor:name="UserItem" oor:op="fuse" oor:type="xs:string"><value>V1,2,0</value></prop></node><prop oor:name="WindowState" oor:op="fuse"><value xsi:nil="true"/></prop></node></item>
<item oor:path="/org.openoffice.Office.Views/Windows"><node oor:name="SplitWindow1" oor:op="replace"><prop oor:name="Visible" oor:op="fuse"><value xsi:nil="true"/></prop><node oor:name="UserData"><prop oor:name="UserItem" oor:op="fuse" oor:type="xs:string"><value>V1,2,0</value></prop></node><prop oor:name="WindowState" oor:op="fuse"><value xsi:nil="true"/></prop></node></item>
<item oor:path="/org.openoffice.Office.Views/Windows"><node oor:name="SplitWindow2" oor:op="replace"><prop oor:name="Visible" oor:op="fuse"><value xsi:nil="true"/></prop><node oor:name="UserData"><prop oor:name="UserItem" oor:op="fuse" oor:type="xs:string"><value>V1,2,1,0,10336</value></prop></node><prop oor:name="WindowState" oor:op="fuse"><value xsi:nil="true"/></prop></node></item>
<item oor:path="/org.openoffice.Office.Views/Windows"><node oor:name="SplitWindow3" oor:op="replace"><prop oor:name="Visible" oor:op="fuse"><value xsi:nil="true"/></prop><node oor:name="UserData"><prop oor:name="UserItem" oor:op="fuse" oor:type="xs:string"><value>V1,2,0</value></prop></node><prop oor:name="WindowState" oor:op="fuse"><value xsi:nil="true"/></prop></node></item>
<item oor:path="/org.openoffice.Office.Views/Windows/org.openoffice.Office.Views:WindowType['swriter/10336']/UserData"><prop oor:name="Data" oor:op="fuse" oor:type="xs:string"><value>V2,V,0,AL:(5,16,0/0/48/450,48;450)</value></prop></item>
<item oor:path="/org.openoffice.Office.Views/Windows/org.openoffice.Office.Views:WindowType['swriter/10336']"><prop oor:name="WindowState" oor:op="fuse"><value></value></prop></item>
<item oor:path="/org.openoffice.Office.Views/Windows"><node oor:name="swriter/10365" oor:op="replace"><prop oor:name="Visible" oor:op="fuse"><value xsi:nil="true"/></prop><node oor:name="UserData"><prop oor:name="Data" oor:op="fuse" oor:type="xs:string"><value>V2,V,128</value></prop></node><prop oor:name="WindowState" oor:op="fuse"><value></value></prop></node></item>
<item oor:path="/org.openoffice.Setup/L10N"><prop oor:name="ooLocale" oor:op="fuse"><value>zh-CN</value></prop></item>
<item oor:path="/org.openoffice.Setup/Office/Factories/org.openoffice.Setup:Factory['com.sun.star.text.TextDocument']"><prop oor:name="ooSetupFactoryWindowAttributes" oor:op="fuse"><value>70,64,1851,1017;5;70,64,1851,1017;</value></prop></item>
<item oor:path="/org.openoffice.Setup/Office"><prop oor:name="LastCompatibilityCheckID" oor:op="fuse"><value>50(Build:2)</value></prop></item>
<item oor:path="/org.openoffice.Setup/Office"><prop oor:name="OfficeRestartInProgress" oor:op="fuse"><value>false</value></prop></item>
<item oor:path="/org.openoffice.Setup/Office"><prop oor:name="ooSetupInstCompleted" oor:op="fuse"><value>true</value></prop></item>
<item oor:path="/org.openoffice.Setup/Product"><prop oor:name="LastTimeDonateShown" oor:op="fuse"><value>1740626391</value></prop></item>
<item oor:path="/org.openoffice.Setup/Product"><prop oor:name="LastTimeGetInvolvedShown" oor:op="fuse"><value>1740626391</value></prop></item>
<item oor:path="/org.openoffice.Setup/Product"><prop oor:name="ooSetupLastVersion" oor:op="fuse"><value>7.5</value></prop></item>
</oor:items>
