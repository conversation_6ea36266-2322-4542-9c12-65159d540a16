Graphics Backend used: svp
Passed Tests: 67
Quirky Tests: 35
Failed Tests: 1
Skipped Tests: 6

---Name of the tests that failed---
testDrawInvertN50WithRectangle

---Name of the tests that were Quirky---
testDrawRectWithLine
testDrawRectWithPolygon
testDrawRectWithPolyLine
testDrawRectWithPolyPolygon
testDrawRectWithPolyPolygonB2D
testDrawDiamondWithLine
testComplexDrawTransformedBitmap24bpp
testDashedLine
testLinearGradientBorder
testLinearGradientSteps
testRadialGradient
testRadialGradientOfs
testHalfEllipseWithPolyLine
testHalfEllipseAAWithPolyLine
testHalfEllipseAAWithPolyLineB2D
testHalfEllipseWithPolygon
testHalfEllipseAAWithPolygon
testDrawRectangleOnSize1028WithPixel
testDrawRectangleOnSize4096WithPixel
testDrawRectangleOnSize1028WithLine
testDrawRectangleOnSize4096WithLine
testDrawRectangleOnSize1028WithPolyLine
testDrawRectangleOnSize4096WithPolyLine
testDrawRectangleOnSize1028WithPolygon
testDrawRectangleOnSize4096WithPolygon
testDrawRectangleOnSize1028WithPolyLineB2D
testDrawRectangleOnSize4096WithPolyLineB2D
testDrawRectangleOnSize1028WithPolyPolygon
testDrawRectangleOnSize4096WithPolyPolygon
testDrawRectangleOnSize1028WithPolyPolygonB2D
testDrawRectangleOnSize4096WithPolygonPolygonB2D
testDrawOpenPolygonWithPolyLine
testDrawOpenPolygonWithPolygon
testDrawOpenPolygonWithPolyPolygon
testDrawOpenPolygonWithPolyPolygonB2D

---Name of the tests that were Skipped---
testDrawInvertTrackFrameWithRectangle
testDrawBitmap32bpp
testDrawTransformedBitmap32bpp
testDrawBitmapExWithAlpha32bpp
testDrawMask32bpp
testDrawBlend32bpp
