cd 
ls
cd /home/<USER>/
ls
crontab -l
crontab -e
sh crontab -e
bash crontab -e
/usr/bin/vim.tiny crontab -e
crontab -e
crontab -l
ps -ef
ls
cd dic/
ls
cd para/
ls
cat video_displacement.txt 
cd ..
ls
cd para/
ls
cat video_temp_result.txt 
ls
cat video_displacement.txt 
ls
sudo chmod 777 *
cat video_displacement.txt 
cat video_temp_result.txt 
ps -ef
ls
cd ..
cd prog/
ls
cd ..
ls
cd para/
ls
cat video_displacement.txt 
../prog/runLiveVideoResultProcess.sh 
ls
../prog/runLiveVideoResultProcess.sh 
cat ../prog/runLiveVideoResultProcess.sh 
cd ..
ls
cd ..
ls
cd orig/
ls
touch originalObservation.txt
sudo touch originalObservation.txt
ls
cp /media/user/MOVE\ SPEED/datafile/orig/originalObservation.txt .
sudo cp /media/user/MOVE\ SPEED/datafile/orig/originalObservation.txt .
ls
chmod 777 *
sudo chmod 777 *
ls
cd ..
cd dic/
ls
cd prog/
ls
cd ..
cd para/
ls
cat video_displacement.txt 
ls
cat video_displacement.txt 
ls
cd ..
cd prog/
ls
sudo rm -rf *.cpp
ls
cd ..
ls
cd para/
cat video_displacement.txt 
ls
cd ..
ls
cd ..
ls
cd ..
cd datafile/
ls
cd dic/
ls
cd para/
ls
cat video_temp_result.txt 
ps -ef
../prog/runLiveVideoResultProcess.sh 
ls
../prog/runLiveVideoResultProcess.sh 
cd ..
ls
chmod 777 *
sudo chmod 777 *
sudo chmod 777 */*
ls
cd ..
sudo chmod 777 *
ls
cd ..
sudo chmod 777 *
ls
cd ..
ls
sudo chmod 777 *
ls
cd /home/<USER>/dic/
ls
cd para/
../prog/runLiveVideoResultProcess.sh 
ls
cat video_displacement.txt 
ls
ifconfig -all
ifconfig
apt install net-tools
su
123456
su
sudu
sudo
su
sudo su
unzip USB转串口驱动.zip 
ls
cd USB转串口驱动/
ls
unzip CH341SER_LINUX.ZIP 
ls
cd CH341SER_LINUX/
ls
ls /dev/tty*
lsusb
sudo apt-get update
sudo apt-get install -y git build-essential
git clone https://github.com/jnhuang/ch340g-ch34g-ch34x-linux-driver.git
sudo modprobe ch340
ls /dev/tty*
sudo modprobe ch341
ls /dev/tty*
lsusb
dmesg | grep ttyUSB
sudo apt-get update
& sudo apt-get install usb_modeswitch
sudo apt-get install usb_modeswitch
sudo apt-get update
sudo apt-get install usb_modeswitch
ls
sudo insmod ch341.ko
sudo make load
sudo make install
ls /dev/tty*
lsusb
ls /dev/tty*
ls
sudo make
sudo su
ls
sudo dpkg -i  SunloginClient_15.2.0.63064_amd64.deb 
sudo apt-get -f install
sudo dpkg -i  SunloginClient_15.2.0.63064_amd64.deb 
/usr/local/sunlogin/bin/sunloginclient
su 
sudo -i
LS
ls
make
sudo modprobe ch341
ls
ls /dev/tty*
sudo su 
./mqttDemo 
ll
./runMQTT.sh 
apt install libmosquitto1
su
history
sudo su
crontab -l
crontab -e
crontab -l
ls
ps -ef
ps -ef | find sleep
ps -ef
cat ../dic/para/video_displacement.txt 
ls
ps -ef
ls
cd ..
ls
cd dic/
ls
cd prog/
ls
top
cd orig/
ls
./videoCaptureWeb
histroy
history 
ls
cd ..
ls
cd dic/para/
ls
cd ..
ls
cd ..
ls
cd orig/
ls
history
ls
cd ..
cd dic/
ls
cd prog/
ls
ll
./runLiveVideoResultProcess.sh 
./runLiveVideoProcess.sh 
cd ..
top
kill 7659
sudo su
sudo su 
sudo su
crontab -l
ls
cd /home/
ls
cd datafile/
ls
cd dic/
ls
cd ..
cd dic/
ls
cd prog/
ls
cat runLiveVideoResultProcess.sh 
vi runLiveVideoResultProcess.sh 
ls
cd ..
cd para/
ls
cat video_temp2_result.txt 
cat video_displacement.txt 
ls
cd ..
cd orig/
ls
cd ../dic/
ls
cd prog/
ls
cd ..
cd orig/
ls
chmod 777 *
sudo chmod 777 *
ls
cat runCapture.sh 
vi  /home/<USER>/dic/para/video_displacement.txt
sudo su
ll
ls
ll
vi .video_displacement.txt.swp 
ll
sudo su
sudo su 
ls
top
ll
pkill mqtt
su
sudo su
uname -a
dpkg
dpkg --help
sudo apt-get install ./MVS-3.0.1_x86_64_20241128.deb 
mvs
dpkg -i ./MVS-3.0.1_x86_64_20241128.deb 
sudo dpkg -i ./MVS-3.0.1_x86_64_20241128.deb 
/opt/MVS/bin/MVS.sh 
sudo apt-get install ./todesk-v4.7.2.0-amd64.deb
todesk
/opt/MVS/bin/MVS.sh 
ls
mv datafile datafile_hf
sudo mv datafile datafile_hf
ls
cd ..
ls
chmod 777 /home
sudo chmod 777 /home
ls
rm datafile_v20250121
rm -r datafile_v20250121
ls
rm datafile_v20250121.tar.gz 
g++ captures_single.cpp -o captures_single -I /usr/include/opencv4 -L /usr/include/opencv4 -lopencv_core -lopencv_highgui -lopencv_imgcodecs -lopencv_imgproc -lopencv_videoio -pthread
ls
chmod 777 *
g++ captures_single.cpp -o captures_single -I /usr/include/opencv4 -L /usr/include/opencv4 -lopencv_core -lopencv_highgui -lopencv_imgcodecs -lopencv_imgproc -lopencv_videoio -pthread
gcc captures_single.cpp -o captures_single -I /usr/include/opencv4 -L /usr/include/opencv4 -lopencv_core -lopencv_highgui -lopencv_imgcodecs -lopencv_imgproc -lopencv_videoio -pthread
history
ls
g++ captures_single.cpp -o captures_single -I /usr/include/opencv4 -L /usr/include/opencv4 -lopencv_core -lopencv_highgui -lopencv_imgcodecs -lopencv_imgproc -lopencv_videoio -pthread
ls
g++ captures_ip.cpp -o captures_ip -I /usr/include/opencv4 -L /usr/include/opencv4 -lopencv_core -lopencv_highgui -lopencv_imgcodecs -lopencv_imgproc -lopencv_videoio -pthread
ls
g++ offsetCalculation.cpp -o offsetCalculation -I /usr/include/opencv4 -L /usr/include/opencv4 -lopencv_core -lopencv_highgui -lopencv_imgcodecs -lopencv_imgproc -lopencv_videoio -pthread
ls
./offsetCalculation
ls
cd 
cd /home/
ls
tar -czvf datafile_v20250205.tar.gz ./datafile_hf/
ls
ll
/opt/MVS/bin/MVS.sh 
sudu su 
sudo su 
sudo su
/opt/MVS/bin/MVS.sh 
tar -zxvf captures_all_x86.tar.gz 
ls
cd captures_all_x86/
ls
cd captures_mv_IP/
ls
sudo su 
eog
sudo apt upgrade eog
ls
sudo apt-get install mtpaint
mtpaint 
sudo apt-get install mtpaint
sudo ufw status
sudo ufw disable
sudo ufw status
systemctl stop ufw.service
ping *************
sudo apt-get install openssh-server
sudo su 
ping *************
ping *************
ping *************
chmod 777 -R /HOME
chmod 777 -R /home
sudo chmod 777 -R /home
cd /home/<USER>/
ls
gcc himixPub.c source/file_monitor.c source/tcp.c source/activation.c source/utils.c source/camera_ssh.c -g -o mqttPub -lpthread -lmosquitto -lcjson -lpaho-mqtt3c -lsqlite3 -lssl -lcrypto -ljpeg -lm
apt install libssl-dev
sudo apt install libssl-dev
gcc himixPub.c source/file_monitor.c source/tcp.c source/activation.c source/utils.c source/camera_ssh.c -g -o mqttPub -lpthread -lmosquitto -lcjson -lpaho-mqtt3c -lsqlite3 -lssl -lcrypto -ljpeg -lm
ls
chmod 777 mqttDemo 
ls
cd ,,
车道。。
cd ..
ls
cd datafile
ls
cd dic/
ls
cd /home
ls
mv 20250808三联/mqtt* /home/<USER>
mv 20250808三联/mqtt* /home/<USER>/dic
cd /home/<USER>/dic
ls
ll
./runMQTT.sh 
history
ls
mv 20250808三联/mqtt* /home/<USER>/dic
mv /home/<USER>/mqtt* /home/<USER>/dic
cd /home/<USER>/dic
./mqttDemo 111 ************* 6049 6048
cat logs/visual_log.txt 
./mqttDemo 1111 ************* 6049 6048
su
cd /home/<USER>/
ls
gcc himixPub.c source/file_monitor.c source/tcp.c source/activation.c source/utils.c source/camera_ssh.c -g -o mqttPub -lpthread -lmosquitto -lcjson -lpaho-mqtt3c -lsqlite3 -lssl -lcrypto -ljpeg -lm
gcc himixInteract.c source/file_monitor.c source/tcp.c source/activation.c source/utils.c source/camera_ssh.c -g -o mqttDemo -lpthread -lmosquitto -lcjson -lpaho-mqtt3c -lsqlite3 -lssl -lcrypto -ljpeg -lm
ll
./mqttDemo 111 m20720957h.imwork.net 23298 43835
cd /home/
ls
cd 20250808三联/
ls
histroy
history 
gcc himixPub.c source/file_monitor.c source/tcp.c source/activation.c source/utils.c source/camera_ssh.c -g -o mqttPub -lpthread -lmosquitto -lcjson -lpaho-mqtt3c -lsqlite3 -lssl -lcrypto -ljpeg -lm
ls
gcc himixPub.c source/file_monitor.c source/tcp.c source/activation.c source/utils.c source/camera_ssh.c -g -o mqttPub -lpthread -lmosquitto -lcjson -lpaho-mqtt3c -lsqlite3 -lssl -lcrypto -ljpeg -lmgcc himixPub.c source/file_monitor.c source/tcp.c source/activation.c source/utils.c source/camera_ssh.c -g -o mqttPub -lpthread -lmosquitto -lcjson -lpaho-mqtt3c -lsqlite3 -lssl -lcrypto -ljpeg -lm
gcc himixPub.c source/file_monitor.c source/tcp.c source/activation.c source/utils.c source/camera_ssh.c -g -o mqttPub -lpthread -lmosquitto -lcjson -lpaho-mqtt3c -lsqlite3 -lssl -lcrypto -ljpeg -lm
gcc himixInteract.c source/file_monitor.c source/tcp.c source/activation.c source/utils.c source/camera_ssh.c -g -o mqttDemo -lpthread -lmosquitto -lcjson -lpaho-mqtt3c -lsqlite3 -lssl -lcrypto -ljpeg -lmrm
ls
./mqttDemo 111 ************* 6049 6048
ls
ll
ping 134.175.67.130
./mqttDemo 111 m20720957h.imwork.net 23298 43835
cd ..
ls
cd datafile
cd dic/
ls
mv /home/<USER>/mqtt* ./
ll
./mqttDemo 111 m20720957h.imwork.net 23298 43835
rm mqttDemo 
ls
rm mqttPub 
mv /home/<USER>/mqttDemo ./
ls
mv /home/<USER>/mqttPub ./
./mqttDemo 111 m20720957h.imwork.net 23298 43835
ll
cd loh
cd logs/
ls
cd ..
cat ../datafile/dic/logs/visual_log.txt
cat ./datafile/dic/logs/visual_log.txt
cat .././datafile/dic/logs/visual_log.txt
sudo passwd root
sudo passwd root
ls
su
cd /home/<USER>/
ls
cd dic/
ls
cd ..
ll
cd dic/
ls
cd /home/<USER>
ls
cd dic/
ls
cd ..
cd orig/
ls
cd ..
ls
cd dic/
ls
cd orig/
ls
cd ..
ls
cd para/
ls
cat video_displacement.txt 
ls
rm 'USB转串口驱动(1).zip' 
ls
cd USB转串口驱动/
ls
./CH341SER_LINUX
cd CH341SER_LINUX/
ls
cd driver/
ls
cd ..
ls
cat README.md 
cd driver/
ls
make
ls
ls /dev/u*
ls /dev/tty*
ls usb
lsusb
ls /dev/tty*
lsusb
ls /dev/tty*
lsmod | grep ch341
dmesg | grep -iE "ch340|usb|tty|serial"
sudo su
./runMQTT.sh 
./mqttDemo 1112 ************* 6048 6049
cd ..
./mqttDemo 1112 ************* 6048 6049
./mqttDemo 1112 ************* 6049 6050
./mqttDemo 1112 ************ 6049 6050
lsusb
sudo apt-get update
sudo apt-get install usb-serial-drivers
ls /dev/
vi runMQTT.sh 
ls
cat cfg.ini 
cd ..
ls
cd datafile
ls
cd dic/
ls
cd ..
ls
cd program/
ls
cat runMQTT.sh 
cat cfg.ini 
ls /dev/
ls /dev/tty*
sudo make
ls /dev/
ls /dev/tty*
cd /home/<USER>/dic/logs/
ls
cat visual_log.txt 
cat visual_error.txt 
cat visual_log.txt 
cat visual_error.txt 
ls
cd /
ls
cd home/
ls
cd datafile
ls
cd orig/
ls
ls /dev/tty*
cat startfile.txt 
vi startfile.txt 
cat startfile.txt 
crontab -l
cd /home/<USER>/dic/
ll
cd para/
ls
cat video_displacement.txt 
cat temp_parameters.txt 
cd ../../
ls
cd orig/
ls
cat originalObservation.txt 
./runCapture.sh 
ls
ls /dev/tty*
./runCapture.sh 
ls
sudo ./runCapture.sh 
ls /dev/tty*
ls
cat captures_single.cpp 
ls
./capture
ls -l
crontab -l
ls
ls /dev/ttyUSB0 
cat startfile.txt 
ls
crontab -e
crontab -l
./capture_single 
sudo su
sudo ./capture_single 
crontab -l
cd ../
ls
cd dic/prog/
ls
cat runEverySeconds.sh 
cat runLiveVideoResultProcess.sh 
crontab -l
crontab -e
crontab -l
ls
cat video_temp_result.txt 
ls
cd ..
ls
cd para/
ls
cat video_displacement.txt 
cat video_temp_result.txt 
cd ..
ls
cd prog/
ls
cat video_temp_result.txt 
ls
cd ..
ls
cd para/
ls
cat video_displacement.txt 
cd ../..
cd orig/
ls
cat originalObservation.txt 
crontab -l
crontab -e
crontab -l
crontab -e
crontab -l
sudo su
crontab -l
ls
cat ls
ls
cat originalObservation.txt 
cat captures_single.cpp 
ls
ls -l
g++ -std=c++17 -pthread -lstdc++fs captures_single.cpp -o capture_single -I/usr/include/opencv4 -L/usr/lib/aarch64-linux-gnu -lopencv_core -lopencv_videoio -lopencv_imgproc -lopencv_highgui -lopencv_imgcodecs -lmodbus -lboost_filesystem -lboost_system
ls -l
crontab -l
crontab -e
sudo su
sudu su
sudo su
ls
cat video_displacement.txt 
crontab -e
ls
top
ls
sudo su
crontab -l 
sudo su 
ls
sudo su 
top
pkill ToDesk
pkill -f ToDesk
su
sudo
su
top
reboot
su
sudo
su
top
sudo
su
2021@
su
cd /home/<USER>
ls
cd dic
ls
cd para
ls
cat points_displacement.txt 
cat points_list.txt 
