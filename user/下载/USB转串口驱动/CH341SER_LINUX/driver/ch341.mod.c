#include <linux/module.h>
#define INCLUDE_VERMAGIC
#include <linux/build-salt.h>
#include <linux/elfnote-lto.h>
#include <linux/export-internal.h>
#include <linux/vermagic.h>
#include <linux/compiler.h>

BUILD_SALT;
BUILD_LTO_INFO;

MODULE_INFO(vermagic, VERMAGIC_STRING);
MODULE_INFO(name, KBUILD_MODNAME);

__visible struct module __this_module
__section(".gnu.linkonce.this_module") = {
	.name = KBUILD_MODNAME,
	.init = init_module,
#ifdef CONFIG_MODULE_UNLOAD
	.exit = cleanup_module,
#endif
	.arch = MODULE_ARCH_INIT,
};

#ifdef CONFIG_RETPOLINE
MODULE_INFO(retpoline, "Y");
#endif


static const char ____versions[]
__used __section("__versions") =
	"\x18\x00\x00\x00\xbb\x1f\x4b\xd6"
	"tty_port_hangup\0"
	"\x18\x00\x00\x00\x1e\xb2\x1b\xbd"
	"tty_port_close\0\0"
	"\x18\x00\x00\x00\x58\x5f\xc6\xaa"
	"tty_port_open\0\0\0"
	"\x18\x00\x00\x00\xfc\xf1\x9a\xcc"
	"usb_deregister\0\0"
	"\x14\x00\x00\x00\xae\xb3\x17\x8e"
	"idr_destroy\0"
	"\x2c\x00\x00\x00\xc6\xfa\xb1\x54"
	"__ubsan_handle_load_invalid_value\0\0\0"
	"\x10\x00\x00\x00\x38\xdf\xac\x69"
	"memcpy\0\0"
	"\x28\x00\x00\x00\x2e\xf2\x33\x12"
	"usb_autopm_get_interface_async\0\0"
	"\x18\x00\x00\x00\xe0\xab\x8e\xea"
	"usb_anchor_urb\0\0"
	"\x24\x00\x00\x00\x3e\x03\x40\x5b"
	"usb_autopm_get_interface\0\0\0\0"
	"\x18\x00\x00\x00\x2f\xe4\xed\x72"
	"usb_control_msg\0"
	"\x18\x00\x00\x00\x74\x2b\x9d\x7f"
	"kmalloc_caches\0\0"
	"\x18\x00\x00\x00\x6a\x1d\xe7\x99"
	"kmalloc_trace\0\0\0"
	"\x10\x00\x00\x00\xba\x0c\x7a\x03"
	"kfree\0\0\0"
	"\x18\x00\x00\x00\x1c\x6d\xd5\x4c"
	"usb_ifnum_to_if\0"
	"\x14\x00\x00\x00\x4b\x8d\xfa\x4d"
	"mutex_lock\0\0"
	"\x14\x00\x00\x00\x03\x16\xf1\xb8"
	"idr_alloc\0\0\0"
	"\x18\x00\x00\x00\x38\xf0\x13\x32"
	"mutex_unlock\0\0\0\0"
	"\x20\x00\x00\x00\x54\xea\xa5\xd9"
	"__init_waitqueue_head\0\0\0"
	"\x18\x00\x00\x00\x9f\x0c\xfb\xce"
	"__mutex_init\0\0\0\0"
	"\x18\x00\x00\x00\x6d\x69\xa3\xe3"
	"tty_port_init\0\0\0"
	"\x1c\x00\x00\x00\x88\x27\xcd\x2f"
	"usb_alloc_coherent\0\0"
	"\x18\x00\x00\x00\x37\xd8\xb2\x0e"
	"usb_alloc_urb\0\0\0"
	"\x14\x00\x00\x00\x5b\xa9\x65\x76"
	"idr_remove\0\0"
	"\x1c\x00\x00\x00\xc0\x3e\x1b\x9a"
	"usb_free_coherent\0\0\0"
	"\x14\x00\x00\x00\xef\x5a\x86\x97"
	"_dev_info\0\0\0"
	"\x24\x00\x00\x00\x1b\xf8\x93\x80"
	"usb_driver_claim_interface\0\0"
	"\x18\x00\x00\x00\x05\x21\x8c\x84"
	"usb_get_intf\0\0\0\0"
	"\x24\x00\x00\x00\x0e\x21\xb5\xe0"
	"tty_port_register_device\0\0\0\0"
	"\x18\x00\x00\x00\x93\x36\x3c\xf0"
	"usb_free_urb\0\0\0\0"
	"\x14\x00\x00\x00\xb9\x8f\x97\x20"
	"idr_find\0\0\0\0"
	"\x20\x00\x00\x00\xe1\x04\x28\x37"
	"tty_standard_install\0\0\0\0"
	"\x20\x00\x00\x00\x5f\x69\x96\x02"
	"refcount_warn_saturate\0\0"
	"\x14\x00\x00\x00\xd3\x85\x33\x2d"
	"system_wq\0\0\0"
	"\x18\x00\x00\x00\x36\xf2\xb6\xc5"
	"queue_work_on\0\0\0"
	"\x18\x00\x00\x00\xc2\x9c\xc4\x13"
	"_copy_from_user\0"
	"\x10\x00\x00\x00\x89\xbc\xcb\xc6"
	"capable\0"
	"\x18\x00\x00\x00\xe1\xbe\x10\x6b"
	"_copy_to_user\0\0\0"
	"\x20\x00\x00\x00\xd6\xc7\xd8\xaa"
	"default_wake_function\0\0\0"
	"\x14\x00\x00\x00\xbc\x35\x87\x14"
	"pcpu_hot\0\0\0\0"
	"\x18\x00\x00\x00\x38\x22\xfb\x4a"
	"add_wait_queue\0\0"
	"\x14\x00\x00\x00\x51\x0e\x00\x01"
	"schedule\0\0\0\0"
	"\x1c\x00\x00\x00\x88\x00\x11\x37"
	"remove_wait_queue\0\0\0"
	"\x1c\x00\x00\x00\x56\xb9\x19\x0a"
	"__stack_chk_fail\0\0\0\0"
	"\x20\x00\x00\x00\xc0\x7a\x25\x6c"
	"tty_termios_hw_change\0\0\0"
	"\x20\x00\x00\x00\xd8\x94\xd3\x0b"
	"tty_termios_baud_rate\0\0\0"
	"\x18\x00\x00\x00\xa9\xaa\x5d\xfc"
	"usb_put_intf\0\0\0\0"
	"\x1c\x00\x00\x00\x7b\xd9\xea\xfe"
	"tty_port_tty_get\0\0\0\0"
	"\x14\x00\x00\x00\xff\x60\x14\x18"
	"tty_vhangup\0"
	"\x18\x00\x00\x00\x2c\xb1\x6f\xa1"
	"tty_kref_put\0\0\0\0"
	"\x20\x00\x00\x00\x6c\x78\x6f\x51"
	"tty_unregister_device\0\0\0"
	"\x28\x00\x00\x00\xaa\x08\x52\x61"
	"usb_driver_release_interface\0\0\0\0"
	"\x14\x00\x00\x00\xbb\x6d\xfb\xbd"
	"__fentry__\0\0"
	"\x20\x00\x00\x00\x0b\x05\xdb\x34"
	"_raw_spin_lock_irqsave\0\0"
	"\x24\x00\x00\x00\x70\xce\x5c\xd3"
	"_raw_spin_unlock_irqrestore\0"
	"\x1c\x00\x00\x00\xca\x39\x82\x5b"
	"__x86_return_thunk\0\0"
	"\x18\x00\x00\x00\xaa\xec\xbc\x59"
	"usb_submit_urb\0\0"
	"\x14\x00\x00\x00\x56\x6b\x3c\xf5"
	"_dev_err\0\0\0\0"
	"\x28\x00\x00\x00\xb3\x1c\xa2\x87"
	"__ubsan_handle_out_of_bounds\0\0\0\0"
	"\x28\x00\x00\x00\x29\xa1\x55\xec"
	"usb_autopm_put_interface_async\0\0"
	"\x18\x00\x00\x00\x8d\xfb\xf1\xe6"
	"usb_kill_urb\0\0\0\0"
	"\x1c\x00\x00\x00\xfe\x2d\xc1\x03"
	"cancel_work_sync\0\0\0\0"
	"\x1c\x00\x00\x00\x7b\xcc\x27\x84"
	"_raw_spin_lock_irq\0\0"
	"\x20\x00\x00\x00\x53\x0f\x75\x4b"
	"_raw_spin_unlock_irq\0\0\0\0"
	"\x18\x00\x00\x00\xf6\x96\x19\x0c"
	"tty_port_put\0\0\0\0"
	"\x1c\x00\x00\x00\x31\x26\x3c\x17"
	"__tty_alloc_driver\0\0"
	"\x18\x00\x00\x00\xc1\x7e\xb2\x67"
	"tty_std_termios\0"
	"\x1c\x00\x00\x00\xdf\x4d\xb4\xb2"
	"tty_register_driver\0"
	"\x1c\x00\x00\x00\xa2\x9a\xe7\x2f"
	"usb_register_driver\0"
	"\x10\x00\x00\x00\x7e\x3a\x2c\x12"
	"_printk\0"
	"\x20\x00\x00\x00\xcf\xe8\x18\xef"
	"tty_unregister_driver\0\0\0"
	"\x1c\x00\x00\x00\xff\x28\x2e\xf0"
	"tty_driver_kref_put\0"
	"\x20\x00\x00\x00\x6f\x36\xbe\x6e"
	"ktime_get_mono_fast_ns\0\0"
	"\x14\x00\x00\x00\x44\x43\x96\xe2"
	"__wake_up\0\0\0"
	"\x1c\x00\x00\x00\x75\xf3\xc3\x3f"
	"__dynamic_dev_dbg\0\0\0"
	"\x1c\x00\x00\x00\xd9\x20\x7b\xa0"
	"tty_port_tty_hangup\0"
	"\x2c\x00\x00\x00\x3c\x14\x7c\x5b"
	"tty_insert_flip_string_fixed_flag\0\0\0"
	"\x20\x00\x00\x00\xb8\x99\xcc\x3b"
	"tty_flip_buffer_push\0\0\0\0"
	"\x2c\x00\x00\x00\x31\x18\x39\xb0"
	"usb_autopm_get_interface_no_resume\0\0"
	"\x24\x00\x00\x00\x1f\xcf\xfa\x0b"
	"usb_autopm_put_interface\0\0\0\0"
	"\x1c\x00\x00\x00\xa8\x8c\x5b\x06"
	"usb_get_from_anchor\0"
	"\x1c\x00\x00\x00\x77\x49\x75\xe3"
	"tty_port_tty_wakeup\0"
	"\x18\x00\x00\x00\xc1\x35\x7c\x15"
	"module_layout\0\0\0"
	"\x00\x00\x00\x00\x00\x00\x00\x00";

MODULE_INFO(depends, "");

MODULE_ALIAS("usb:v1A86p7523d*dc*dsc*dp*ic*isc*ip*in*");
MODULE_ALIAS("usb:v1A86p7522d*dc*dsc*dp*ic*isc*ip*in*");
MODULE_ALIAS("usb:v1A86p5523d*dc*dsc*dp*ic*isc*ip*in*");
MODULE_ALIAS("usb:v1A86pE523d*dc*dsc*dp*ic*isc*ip*in*");
MODULE_ALIAS("usb:v4348p5523d*dc*dsc*dp*ic*isc*ip*in*");

MODULE_INFO(srcversion, "D4B228097D2B7219B44DB95");
