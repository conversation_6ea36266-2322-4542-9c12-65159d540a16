# This file is written by xdg-user-dirs-update
# If you want to change or add directories, just edit the line you're
# interested in. All local changes will be retained on the next run.
# Format is XDG_xxx_DIR="$HOME/yyy", where yyy is a shell-escaped
# homedir-relative path, or XDG_xxx_DIR="/yyy", where /yyy is an
# absolute path. No other format is supported.
# 
XDG_DESKTOP_DIR="/home/<USER>/桌面"
XDG_DOWNLOAD_DIR="/home/<USER>/下载"
XDG_TEMPLATES_DIR="/home/<USER>/模板"
XDG_PUBLICSHARE_DIR="/home/<USER>/公共"
XDG_DOCUMENTS_DIR="/home/<USER>/文档"
XDG_MUSIC_DIR="/home/<USER>/音乐"
XDG_PICTURES_DIR="/home/<USER>/图片"
XDG_VIDEOS_DIR="/home/<USER>/视频"
