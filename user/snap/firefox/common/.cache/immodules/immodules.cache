# GTK+ Input Method Modules file
# Automatically generated file, do not edit
# Created by /snap/firefox/6738/gnome-platform/usr/lib/x86_64-linux-gnu/libgtk-3-0/gtk-query-immodules-3.0 from gtk+-3.24.38
#
# ModulesPath = /snap/firefox/6738/gnome-platform/usr/lib/x86_64-linux-gnu/gtk-2.0/3.0.0/linux/immodules:/snap/firefox/6738/gnome-platform/usr/lib/x86_64-linux-gnu/gtk-2.0/3.0.0/immodules:/snap/firefox/6738/gnome-platform/usr/lib/x86_64-linux-gnu/gtk-2.0/linux/immodules:/snap/firefox/6738/gnome-platform/usr/lib/x86_64-linux-gnu/gtk-2.0/immodules:/snap/firefox/6738/gnome-platform/usr/lib/gtk-2.0/3.0.0/linux/immodules:/snap/firefox/6738/gnome-platform/usr/lib/gtk-2.0/3.0.0/immodules:/snap/firefox/6738/gnome-platform/usr/lib/gtk-2.0/linux/immodules:/snap/firefox/6738/gnome-platform/usr/lib/gtk-2.0/immodules:/snap/firefox/6738/gnome-platform/usr/lib/x86_64-linux-gnu/gtk-3.0/3.0.0/linux/immodules:/snap/firefox/6738/gnome-platform/usr/lib/x86_64-linux-gnu/gtk-3.0/3.0.0/immodules:/snap/firefox/6738/gnome-platform/usr/lib/x86_64-linux-gnu/gtk-3.0/linux/immodules:/snap/firefox/6738/gnome-platform/usr/lib/x86_64-linux-gnu/gtk-3.0/immodules:/snap/firefox/6738/gnome-platform/usr/lib/gtk-3.0/3.0.0/linux/immodules:/snap/firefox/6738/gnome-platform/usr/lib/gtk-3.0/3.0.0/immodules:/snap/firefox/6738/gnome-platform/usr/lib/gtk-3.0/linux/immodules:/snap/firefox/6738/gnome-platform/usr/lib/gtk-3.0/immodules:/snap/firefox/6738/gnome-platform/usr/lib/x86_64-linux-gnu/gtk-4.0/3.0.0/linux/immodules:/snap/firefox/6738/gnome-platform/usr/lib/x86_64-linux-gnu/gtk-4.0/3.0.0/immodules:/snap/firefox/6738/gnome-platform/usr/lib/x86_64-linux-gnu/gtk-4.0/linux/immodules:/snap/firefox/6738/gnome-platform/usr/lib/x86_64-linux-gnu/gtk-4.0/immodules:/usr/lib/x86_64-linux-gnu/gtk-3.0/3.0.0/linux/immodules:/usr/lib/x86_64-linux-gnu/gtk-3.0/3.0.0/immodules:/usr/lib/x86_64-linux-gnu/gtk-3.0/linux/immodules:/usr/lib/x86_64-linux-gnu/gtk-3.0/immodules
#
"/snap/firefox/6738/gnome-platform/usr/lib/x86_64-linux-gnu/gtk-3.0/3.0.0/immodules/im-am-et.so" 
"am_et" "Amharic (EZ+)" "gtk30" "/usr/share/locale" "am" 

"/snap/firefox/6738/gnome-platform/usr/lib/x86_64-linux-gnu/gtk-3.0/3.0.0/immodules/im-broadway.so" 
"broadway" "Broadway" "gtk30" "/usr/share/locale" "" 

"/snap/firefox/6738/gnome-platform/usr/lib/x86_64-linux-gnu/gtk-3.0/3.0.0/immodules/im-cedilla.so" 
"cedilla" "Cedilla" "gtk30" "/usr/share/locale" "az:ca:co:fr:gv:oc:pt:sq:tr:wa" 

"/snap/firefox/6738/gnome-platform/usr/lib/x86_64-linux-gnu/gtk-3.0/3.0.0/immodules/im-cyrillic-translit.so" 
"cyrillic_translit" "Cyrillic (Transliterated)" "gtk30" "/usr/share/locale" "" 

"/snap/firefox/6738/gnome-platform/usr/lib/x86_64-linux-gnu/gtk-3.0/3.0.0/immodules/im-fcitx.so" 
"fcitx" "Fcitx (Flexible Input Method Framework)" "fcitx" "/usr/share/locale" "ja:ko:zh:*" 

"/snap/firefox/6738/gnome-platform/usr/lib/x86_64-linux-gnu/gtk-3.0/3.0.0/immodules/im-ibus.so" 
"ibus" "IBus (Intelligent Input Bus)" "ibus" "" "ja:ko:zh:*" 

"/snap/firefox/6738/gnome-platform/usr/lib/x86_64-linux-gnu/gtk-3.0/3.0.0/immodules/im-inuktitut.so" 
"inuktitut" "Inuktitut (Transliterated)" "gtk30" "/usr/share/locale" "iu" 

"/snap/firefox/6738/gnome-platform/usr/lib/x86_64-linux-gnu/gtk-3.0/3.0.0/immodules/im-ipa.so" 
"ipa" "IPA" "gtk30" "/usr/share/locale" "" 

"/snap/firefox/6738/gnome-platform/usr/lib/x86_64-linux-gnu/gtk-3.0/3.0.0/immodules/im-multipress.so" 
"multipress" "Multipress" "gtk30" "/usr/share/locale" "" 

"/snap/firefox/6738/gnome-platform/usr/lib/x86_64-linux-gnu/gtk-3.0/3.0.0/immodules/im-thai.so" 
"thai" "Thai-Lao" "gtk30" "/usr/share/locale" "lo:th" 

"/snap/firefox/6738/gnome-platform/usr/lib/x86_64-linux-gnu/gtk-3.0/3.0.0/immodules/im-ti-er.so" 
"ti_er" "Tigrigna-Eritrean (EZ+)" "gtk30" "/usr/share/locale" "ti" 

"/snap/firefox/6738/gnome-platform/usr/lib/x86_64-linux-gnu/gtk-3.0/3.0.0/immodules/im-ti-et.so" 
"ti_et" "Tigrigna-Ethiopian (EZ+)" "gtk30" "/usr/share/locale" "ti" 

"/snap/firefox/6738/gnome-platform/usr/lib/x86_64-linux-gnu/gtk-3.0/3.0.0/immodules/im-viqr.so" 
"viqr" "Vietnamese (VIQR)" "gtk30" "/usr/share/locale" "" 

"/snap/firefox/6738/gnome-platform/usr/lib/x86_64-linux-gnu/gtk-3.0/3.0.0/immodules/im-wayland.so" 
"wayland" "Wayland" "gtk30" "/usr/share/locale" "" 

"/snap/firefox/6738/gnome-platform/usr/lib/x86_64-linux-gnu/gtk-3.0/3.0.0/immodules/im-waylandgtk.so" 
"waylandgtk" "Waylandgtk" "gtk30" "/usr/share/locale" "" 

"/snap/firefox/6738/gnome-platform/usr/lib/x86_64-linux-gnu/gtk-3.0/3.0.0/immodules/im-xim.so" 
"xim" "X Input Method" "gtk30" "/usr/share/locale" "ko:ja:th:zh" 


