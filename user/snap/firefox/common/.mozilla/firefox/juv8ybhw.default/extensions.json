{"schemaVersion": 37, "addons": [{"id": "<EMAIL>", "syncGUID": "{94863de8-1358-4d4e-a403-eaa9f696dcb5}", "version": "142.0.20250827.4350", "type": "locale", "loader": null, "updateURL": null, "installOrigins": null, "manifestVersion": 2, "optionsURL": null, "optionsType": null, "optionsBrowserStyle": true, "aboutURL": null, "defaultLocale": {"name": "Language: 简体中文 (Simplified Chinese)", "description": "Firefox Language Pack for 简体中文 (zh-CN) – Simplified Chinese", "creator": "Mozest (contributors: <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, YFdyh000, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>)", "developers": null, "translators": null, "contributors": null}, "visible": true, "active": true, "userDisabled": false, "appDisabled": false, "embedderDisabled": false, "installDate": 1735630748975, "updateDate": 1756286929390, "applyBackgroundUpdates": 1, "path": "/home/<USER>/snap/firefox/common/.mozilla/firefox/juv8ybhw.default/extensions/<EMAIL>", "skinnable": false, "sourceURI": "https://addons.mozilla.org/firefox/downloads/file/4565642/chinese_simplified_zh_cn_la-142.0.20250827.4350.xpi", "releaseNotesURI": null, "softDisabled": false, "foreignInstall": false, "strictCompatibility": true, "locales": [], "targetApplications": [{"id": "<EMAIL>", "minVersion": "142.0", "maxVersion": "142.*"}], "targetPlatforms": [], "signedState": 2, "signedTypes": [2, 1], "signedDate": 1756265222000, "seen": true, "dependencies": [], "userPermissions": null, "optionalPermissions": null, "requestedPermissions": null, "icons": {}, "iconURL": null, "blocklistAttentionDismissed": false, "blocklistState": 0, "blocklistURL": null, "startupData": {"chromeEntries": [["locale", "app-marketplace-icons", "zh-CN", "browser/chrome/browser/locale/zh-CN/app-marketplace-icons/"], ["locale", "branding", "zh-CN", "browser/chrome/zh-CN/locale/branding/"], ["locale", "browser", "zh-CN", "browser/chrome/zh-CN/locale/browser/"], ["locale", "browser-region", "zh-CN", "browser/chrome/zh-CN/locale/browser-region/"], ["locale", "devtools", "zh-CN", "browser/chrome/zh-CN/locale/zh-CN/devtools/client/"], ["locale", "devtools-shared", "zh-CN", "browser/chrome/zh-CN/locale/zh-CN/devtools/shared/"], ["locale", "alerts", "zh-CN", "chrome/zh-CN/locale/zh-CN/alerts/"], ["locale", "autoconfig", "zh-CN", "chrome/zh-CN/locale/zh-CN/autoconfig/"], ["locale", "global", "zh-CN", "chrome/zh-CN/locale/zh-CN/global/"], ["locale", "global-platform", "zh-CN", "chrome/zh-CN/locale/zh-CN/global-platform/unix/"], ["locale", "mozapps", "zh-CN", "chrome/zh-CN/locale/zh-CN/mozapps/"], ["locale", "necko", "zh-CN", "chrome/zh-CN/locale/zh-CN/necko/"], ["locale", "passwordmgr", "zh-CN", "chrome/zh-CN/locale/zh-CN/passwordmgr/"], ["locale", "pipnss", "zh-CN", "chrome/zh-CN/locale/zh-CN/pipnss/"], ["locale", "p<PERSON><PERSON><PERSON>", "zh-CN", "chrome/zh-CN/locale/zh-CN/pippki/"], ["locale", "places", "zh-CN", "chrome/zh-CN/locale/zh-CN/places/"]], "langpackId": "langpack-zh-CN-browser", "l10nRegistrySources": {"toolkit": "", "browser": "browser/"}, "languages": ["zh-CN"]}, "hidden": false, "installTelemetryInfo": {"source": "distribution"}, "recommendationState": null, "rootURI": "jar:file:///home/<USER>/snap/firefox/common/.mozilla/firefox/juv8ybhw.default/extensions/<EMAIL>!/", "location": "app-profile"}, {"id": "<EMAIL>", "syncGUID": "{513f7665-266e-45a7-a0b7-1733881abfd5}", "version": "1.0.1", "type": "extension", "loader": null, "updateURL": null, "installOrigins": null, "manifestVersion": 2, "optionsURL": null, "optionsType": null, "optionsBrowserStyle": true, "aboutURL": null, "defaultLocale": {"name": "Form Autofill", "creator": null, "developers": null, "translators": null, "contributors": null}, "visible": true, "active": true, "userDisabled": false, "appDisabled": false, "embedderDisabled": false, "applyBackgroundUpdates": 1, "path": null, "skinnable": false, "sourceURI": null, "releaseNotesURI": null, "softDisabled": false, "foreignInstall": false, "strictCompatibility": true, "locales": [], "targetApplications": [{"id": "<EMAIL>", "minVersion": null, "maxVersion": null}], "targetPlatforms": [], "signedDate": null, "seen": true, "dependencies": [], "incognito": "spanning", "userPermissions": {"permissions": [], "origins": [], "data_collection": []}, "optionalPermissions": {"permissions": [], "origins": [], "data_collection": []}, "requestedPermissions": {"permissions": [], "origins": [], "data_collection": []}, "icons": {}, "iconURL": null, "blocklistAttentionDismissed": false, "blocklistState": 0, "blocklistURL": null, "startupData": {}, "hidden": true, "installTelemetryInfo": null, "recommendationState": null, "rootURI": "resource://builtin-addons/formautofill/", "location": "app-builtin-addons"}, {"id": "<EMAIL>", "syncGUID": "{3f644483-1a10-442f-90cb-9774c207f5c5}", "version": "142.0.0", "type": "extension", "loader": null, "updateURL": null, "installOrigins": null, "manifestVersion": 2, "optionsURL": null, "optionsType": null, "optionsBrowserStyle": true, "aboutURL": null, "defaultLocale": {"name": "New Tab", "description": "", "creator": null, "developers": null, "translators": null, "contributors": null}, "visible": true, "active": true, "userDisabled": false, "appDisabled": false, "embedderDisabled": false, "applyBackgroundUpdates": 1, "path": null, "skinnable": false, "sourceURI": null, "releaseNotesURI": null, "softDisabled": false, "foreignInstall": false, "strictCompatibility": true, "locales": [], "targetApplications": [{"id": "<EMAIL>", "minVersion": "142.0a1", "maxVersion": null}], "targetPlatforms": [], "signedDate": null, "seen": true, "dependencies": [], "incognito": "spanning", "userPermissions": {"permissions": [], "origins": [], "data_collection": []}, "optionalPermissions": {"permissions": [], "origins": [], "data_collection": []}, "requestedPermissions": {"permissions": [], "origins": [], "data_collection": []}, "icons": {}, "iconURL": null, "blocklistAttentionDismissed": false, "blocklistState": 0, "blocklistURL": null, "startupData": {}, "hidden": true, "installTelemetryInfo": null, "recommendationState": null, "rootURI": "resource://builtin-addons/newtab/", "location": "app-builtin-addons"}, {"id": "<EMAIL>", "syncGUID": "{5de1f763-4485-4f03-bc9e-acd755f25af5}", "version": "1.0.0", "type": "extension", "loader": null, "updateURL": null, "installOrigins": null, "manifestVersion": 2, "optionsURL": null, "optionsType": null, "optionsBrowserStyle": true, "aboutURL": null, "defaultLocale": {"name": "Picture-In-Picture", "description": "Fixes for web compatibility with Picture-in-Picture", "creator": null, "developers": null, "translators": null, "contributors": null}, "visible": true, "active": true, "userDisabled": false, "appDisabled": false, "embedderDisabled": false, "applyBackgroundUpdates": 1, "path": null, "skinnable": false, "sourceURI": null, "releaseNotesURI": null, "softDisabled": false, "foreignInstall": false, "strictCompatibility": true, "locales": [], "targetApplications": [{"id": "<EMAIL>", "minVersion": "88.0a1", "maxVersion": null}], "targetPlatforms": [], "signedDate": null, "seen": true, "dependencies": [], "incognito": "spanning", "userPermissions": {"permissions": [], "origins": [], "data_collection": []}, "optionalPermissions": {"permissions": [], "origins": [], "data_collection": []}, "requestedPermissions": {"permissions": [], "origins": [], "data_collection": []}, "icons": {}, "iconURL": null, "blocklistAttentionDismissed": false, "blocklistState": 0, "blocklistURL": null, "startupData": {}, "hidden": true, "installTelemetryInfo": null, "recommendationState": null, "rootURI": "resource://builtin-addons/pictureinpicture/", "location": "app-builtin-addons"}, {"id": "<EMAIL>", "syncGUID": "{61883b1d-0c54-489a-b771-1fdb3fbe9080}", "version": "3.0.0", "type": "extension", "loader": null, "updateURL": null, "installOrigins": null, "manifestVersion": 2, "optionsURL": null, "optionsType": null, "optionsBrowserStyle": true, "aboutURL": null, "defaultLocale": {"name": "Add-ons Search Detection", "description": "", "creator": null, "developers": null, "translators": null, "contributors": null}, "visible": true, "active": true, "userDisabled": false, "appDisabled": false, "embedderDisabled": false, "applyBackgroundUpdates": 1, "path": null, "skinnable": false, "sourceURI": null, "releaseNotesURI": null, "softDisabled": false, "foreignInstall": false, "strictCompatibility": true, "locales": [], "targetApplications": [{"id": "<EMAIL>", "minVersion": null, "maxVersion": null}], "targetPlatforms": [], "signedDate": null, "seen": true, "dependencies": [], "incognito": "spanning", "userPermissions": {"permissions": ["telemetry", "webRequest", "webRequestBlocking"], "origins": ["<all_urls>"], "data_collection": []}, "optionalPermissions": {"permissions": [], "origins": [], "data_collection": []}, "requestedPermissions": {"permissions": [], "origins": [], "data_collection": []}, "icons": {}, "iconURL": null, "blocklistAttentionDismissed": false, "blocklistState": 0, "blocklistURL": null, "startupData": {}, "hidden": true, "installTelemetryInfo": null, "recommendationState": null, "rootURI": "resource://builtin-addons/search-detection/", "location": "app-builtin-addons"}, {"id": "<EMAIL>", "syncGUID": "{76899159-93d2-4c48-a32e-8610db79e92c}", "version": "142.9.0", "type": "extension", "loader": null, "updateURL": null, "installOrigins": null, "manifestVersion": 2, "optionsURL": null, "optionsType": null, "optionsBrowserStyle": true, "aboutURL": null, "defaultLocale": {"name": "Web Compatibility Interventions", "description": "Urgent post-release fixes for web compatibility.", "creator": null, "developers": null, "translators": null, "contributors": null}, "visible": true, "active": true, "userDisabled": false, "appDisabled": false, "embedderDisabled": false, "applyBackgroundUpdates": 1, "path": null, "skinnable": false, "sourceURI": null, "releaseNotesURI": null, "softDisabled": false, "foreignInstall": false, "strictCompatibility": true, "locales": [], "targetApplications": [{"id": "<EMAIL>", "minVersion": "102.0", "maxVersion": null}], "targetPlatforms": [], "signedDate": null, "seen": true, "dependencies": [], "incognito": "spanning", "userPermissions": {"permissions": ["cookies", "mozillaAddons", "scripting", "tabs", "webNavigation", "webRequest", "webRequestBlocking"], "origins": ["<all_urls>"], "data_collection": []}, "optionalPermissions": {"permissions": [], "origins": [], "data_collection": []}, "requestedPermissions": {"permissions": [], "origins": [], "data_collection": []}, "icons": {}, "iconURL": null, "blocklistAttentionDismissed": false, "blocklistState": 0, "blocklistURL": null, "startupData": {"persistentListeners": {"webRequest": {"onBeforeRequest": [[{"incognito": null, "tabId": null, "types": ["main_frame"], "urls": ["*://login.microsoftonline.com/*", "*://login.microsoftonline.us/*", "*://steamcommunity.com/*", "*://store.steampowered.com/*", "*://help.steampowered.com/*", "*://checkout.steampowered.com/*", "*://steam.tv/*"], "windowId": null}, ["blocking"]], [{"incognito": null, "tabId": null, "types": ["image"], "urls": ["https://smartblock.firefox.etp/instagram.svg", "https://smartblock.firefox.etp/facebook.svg", "https://smartblock.firefox.etp/play.svg", "https://smartblock.firefox.etp/tiktok.svg", "https://smartblock.firefox.etp/disqus.svg", "https://smartblock.firefox.etp/x-logo.svg"], "windowId": null}, ["blocking"]], [{"incognito": null, "tabId": null, "types": ["script"], "urls": ["*://webcompat-addon-testbed.herokuapp.com/shims_test.js", "*://example.com/browser/browser/extensions/webcompat/tests/browser/shims_test.js", "*://example.com/browser/browser/extensions/webcompat/tests/browser/shims_test_2.js", "*://example.com/browser/browser/extensions/webcompat/tests/browser/shims_test_3.js", "https://itisatracker.org/browser/browser/extensions/webcompat/tests/browser/embed_test.js", "*://s7.addthis.com/icons/official-addthis-angularjs/current/dist/official-addthis-angularjs.min.js*", "*://track.adform.net/serving/scripts/trackpoint/", "*://track.adform.net/serving/scripts/trackpoint/async/", "*://*.adnxs.com/*/ast.js*", "*://*.adnxs.com/*/pb.js*", "*://*.adnxs.com/*/prebid*", "*://www.everestjs.net/static/st.v3.js*", "*://static.adsafeprotected.com/vans-adapter-google-ima.js", "*://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js", "*://cdn.branch.io/branch-latest.min.js*", "*://pub.doubleverify.com/signals/pub.js*", "*://c.amazon-adsystem.com/aax2/apstag.js", "*://aax.amazon-adsystem.com/e/dtb/bid/*/prebid*", "*://auth.9c9media.ca/auth/main.js", "*://static.chartbeat.com/js/chartbeat.js", "*://static.chartbeat.com/js/chartbeat_video.js", "*://static.criteo.net/js/ld/publishertag.js", "*://libs.coremetrics.com/eluminate.js", "*://connect.facebook.net/*/sdk.js*", "*://connect.facebook.net/*/all.js*", "*://secure.cdn.fastclick.net/js/cnvr-launcher/*/launcher-stub.min.js*", "*://www.google-analytics.com/analytics.js*", "*://www.google-analytics.com/gtm/js*", "*://www.googletagmanager.com/gtm.js*", "*://www.google-analytics.com/plugins/ua/ec.js", "*://ssl.google-analytics.com/ga.js", "*://s0.2mdn.net/instream/html5/ima3.js", "*://imasdk.googleapis.com/js/sdkloader/ima3.js", "*://www.googleadservices.com/pagead/conversion_async.js", "*://www.googletagservices.com/tag/js/gpt.js*", "*://pagead2.googlesyndication.com/tag/js/gpt.js*", "*://pagead2.googlesyndication.com/gpt/pubads_impl_*.js*", "*://securepubads.g.doubleclick.net/tag/js/gpt.js*", "*://securepubads.g.doubleclick.net/gpt/pubads_impl_*.js*", "*://script.ioam.de/iam.js", "*://cdn.adsafeprotected.com/iasPET.1.js", "*://static.adsafeprotected.com/iasPET.1.js", "*://adservex.media.net/videoAds.js*", "*://*.moatads.com/*/moatad.js*", "*://*.moatads.com/*/moatapi.js*", "*://*.moatads.com/*/moatheader.js*", "*://*.moatads.com/*/yi.js*", "*://*.imrworldwide.com/v60.js", "*://cdn.optimizely.com/js/*.js", "*://cdn.optimizely.com/public/*.js", "*://id.rambler.ru/rambler-id-helper/auth_events.js", "*://media.richrelevance.com/rrserver/js/1.2/p13n.js", "*://www.gstatic.com/firebasejs/*/firebase-messaging.js*", "*://*.vidible.tv/*/vidible-min.js*", "*://vdb-cdn-files.s3.amazonaws.com/*/vidible-min.js*", "*://js.maxmind.com/js/apis/geoip2/*/geoip2.js", "*://s.webtrends.com/js/advancedLinkTracking.js", "*://s.webtrends.com/js/webtrends.js", "*://s.webtrends.com/js/webtrends.min.js", "https://www.instagram.com/embed.js", "https://platform.instagram.com/*/embeds.js", "https://www.tiktok.com/embed.js", "*://*.disqus.com/embed.js", "*://openfpcdn.io/botd/v1", "https://platform.twitter.com/widgets.js"], "windowId": null}, ["blocking"]], [{"incognito": null, "tabId": null, "types": ["image"], "urls": ["*://track.adform.net/Serving/TrackPoint/*", "*://pixel.advertising.com/firefox-etp", "*://*.advertising.com/*.js*", "*://*.advertising.com/*", "*://securepubads.g.doubleclick.net/gampad/*ad-blk*", "*://pubads.g.doubleclick.net/gampad/*ad-blk*", "*://securepubads.g.doubleclick.net/gampad/*xml_vmap1*", "*://pubads.g.doubleclick.net/gampad/*xml_vmap1*", "*://vast.adsafeprotected.com/vast*", "*://securepubads.g.doubleclick.net/gampad/*xml_vmap2*", "*://pubads.g.doubleclick.net/gampad/*xml_vmap2*", "*://securepubads.g.doubleclick.net/gampad/*ad*", "*://pubads.g.doubleclick.net/gampad/*ad*", "*://www.facebook.com/platform/impression.php*", "https://ads.stickyadstv.com/firefox-etp", "*://ads.stickyadstv.com/auto-user-sync*", "*://ads.stickyadstv.com/user-matching*", "https://static.adsafeprotected.com/firefox-etp-pixel", "*://*.adsafeprotected.com/*.gif*", "*://*.adsafeprotected.com/*.png*", "*://*.adsafeprotected.com/*.js*", "*://*.adsafeprotected.com/*/adj*", "*://*.adsafeprotected.com/*/imp/*", "*://*.adsafeprotected.com/*/Serving/*", "*://*.adsafeprotected.com/*/unit/*", "*://*.adsafeprotected.com/jload", "*://*.adsafeprotected.com/jload?*", "*://*.adsafeprotected.com/jsvid", "*://*.adsafeprotected.com/jsvid?*", "*://*.adsafeprotected.com/mon*", "*://*.adsafeprotected.com/tpl", "*://*.adsafeprotected.com/tpl?*", "*://*.adsafeprotected.com/services/pub*", "*://*.adsafeprotected.com/*"], "windowId": null}, ["blocking"]], [{"incognito": null, "tabId": null, "types": ["imageset"], "urls": ["*://track.adform.net/Serving/TrackPoint/*", "*://pixel.advertising.com/firefox-etp", "*://*.advertising.com/*.js*", "*://*.advertising.com/*", "*://securepubads.g.doubleclick.net/gampad/*ad-blk*", "*://pubads.g.doubleclick.net/gampad/*ad-blk*", "*://securepubads.g.doubleclick.net/gampad/*xml_vmap1*", "*://pubads.g.doubleclick.net/gampad/*xml_vmap1*", "*://vast.adsafeprotected.com/vast*", "*://securepubads.g.doubleclick.net/gampad/*xml_vmap2*", "*://pubads.g.doubleclick.net/gampad/*xml_vmap2*", "*://securepubads.g.doubleclick.net/gampad/*ad*", "*://pubads.g.doubleclick.net/gampad/*ad*", "*://www.facebook.com/platform/impression.php*", "https://ads.stickyadstv.com/firefox-etp", "*://ads.stickyadstv.com/auto-user-sync*", "*://ads.stickyadstv.com/user-matching*", "https://static.adsafeprotected.com/firefox-etp-pixel", "*://*.adsafeprotected.com/*.gif*", "*://*.adsafeprotected.com/*.png*", "*://*.adsafeprotected.com/*.js*", "*://*.adsafeprotected.com/*/adj*", "*://*.adsafeprotected.com/*/imp/*", "*://*.adsafeprotected.com/*/Serving/*", "*://*.adsafeprotected.com/*/unit/*", "*://*.adsafeprotected.com/jload", "*://*.adsafeprotected.com/jload?*", "*://*.adsafeprotected.com/jsvid", "*://*.adsafeprotected.com/jsvid?*", "*://*.adsafeprotected.com/mon*", "*://*.adsafeprotected.com/tpl", "*://*.adsafeprotected.com/tpl?*", "*://*.adsafeprotected.com/services/pub*", "*://*.adsafeprotected.com/*"], "windowId": null}, ["blocking"]], [{"incognito": null, "tabId": null, "types": ["xmlhttprequest"], "urls": ["*://track.adform.net/Serving/TrackPoint/*", "*://pagead2.googlesyndication.com/pagead/*.js*fcd=true", "*://pagead2.googlesyndication.com/pagead/js/*.js*fcd=true", "*://pixel.advertising.com/firefox-etp", "*://cdn.cmp.advertising.com/firefox-etp", "*://*.advertising.com/*.js*", "*://*.advertising.com/*", "*://securepubads.g.doubleclick.net/gampad/*ad-blk*", "*://pubads.g.doubleclick.net/gampad/*ad-blk*", "*://securepubads.g.doubleclick.net/gampad/*xml_vmap1*", "*://pubads.g.doubleclick.net/gampad/*xml_vmap1*", "*://vast.adsafeprotected.com/vast*", "*://securepubads.g.doubleclick.net/gampad/*xml_vmap2*", "*://pubads.g.doubleclick.net/gampad/*xml_vmap2*", "*://securepubads.g.doubleclick.net/gampad/*ad*", "*://pubads.g.doubleclick.net/gampad/*ad*", "*://www.facebook.com/platform/impression.php*", "https://ads.stickyadstv.com/firefox-etp", "*://ads.stickyadstv.com/auto-user-sync*", "*://ads.stickyadstv.com/user-matching*", "https://static.adsafeprotected.com/firefox-etp-pixel", "https://static.adsafeprotected.com/firefox-etp-js", "*://*.adsafeprotected.com/*.gif*", "*://*.adsafeprotected.com/*.png*", "*://*.adsafeprotected.com/*.js*", "*://*.adsafeprotected.com/*/adj*", "*://*.adsafeprotected.com/*/imp/*", "*://*.adsafeprotected.com/*/Serving/*", "*://*.adsafeprotected.com/*/unit/*", "*://*.adsafeprotected.com/jload", "*://*.adsafeprotected.com/jload?*", "*://*.adsafeprotected.com/jsvid", "*://*.adsafeprotected.com/jsvid?*", "*://*.adsafeprotected.com/mon*", "*://*.adsafeprotected.com/tpl", "*://*.adsafeprotected.com/tpl?*", "*://*.adsafeprotected.com/services/pub*", "*://*.adsafeprotected.com/*"], "windowId": null}, ["blocking"]]], "onBeforeSendHeaders": [[{"incognito": null, "tabId": null, "types": ["sub_frame"], "urls": ["*://trends.google.com/trends/embed*"], "windowId": null}, ["blocking", "requestHeaders"]]], "onHeadersReceived": [[{"incognito": null, "tabId": null, "types": ["sub_frame"], "urls": ["*://trends.google.com/trends/embed*"], "windowId": null}, ["blocking", "responseHeaders"]]]}}}, "hidden": true, "installTelemetryInfo": null, "recommendationState": null, "rootURI": "resource://builtin-addons/webcompat/", "location": "app-builtin-addons"}, {"id": "<EMAIL>", "syncGUID": "{e3fb46eb-ae17-4c4b-900c-79b1d37d6ad6}", "version": "1.4.2", "type": "theme", "loader": null, "updateURL": null, "installOrigins": null, "manifestVersion": 2, "optionsURL": null, "optionsType": null, "optionsBrowserStyle": true, "aboutURL": null, "defaultLocale": {"name": "System theme — auto", "description": "Follow the operating system setting for buttons, menus, and windows.", "creator": "Mozilla", "developers": null, "translators": null, "contributors": null}, "visible": true, "active": true, "userDisabled": false, "appDisabled": false, "embedderDisabled": false, "installDate": 1735630749513, "applyBackgroundUpdates": 1, "path": null, "skinnable": false, "sourceURI": null, "releaseNotesURI": null, "softDisabled": false, "foreignInstall": false, "strictCompatibility": true, "locales": [], "targetApplications": [{"id": "<EMAIL>", "minVersion": null, "maxVersion": null}], "targetPlatforms": [], "signedDate": null, "seen": true, "dependencies": [], "userPermissions": null, "optionalPermissions": null, "requestedPermissions": null, "icons": {"32": "icon.svg"}, "iconURL": null, "blocklistAttentionDismissed": false, "blocklistState": 0, "blocklistURL": null, "startupData": {}, "hidden": false, "installTelemetryInfo": null, "recommendationState": null, "rootURI": "resource://default-theme/", "location": "app-builtin"}, {"id": "<EMAIL>", "syncGUID": "{61387d17-90f1-4132-9179-c2191f0758ba}", "version": "3.0.0", "type": "extension", "loader": null, "updateURL": null, "installOrigins": null, "manifestVersion": 2, "optionsURL": null, "optionsType": null, "optionsBrowserStyle": true, "aboutURL": null, "defaultLocale": {"name": "Add-ons Search Detection", "description": "", "creator": null, "developers": null, "translators": null, "contributors": null}, "visible": false, "active": false, "userDisabled": false, "appDisabled": false, "embedderDisabled": false, "installDate": 1735630750328, "applyBackgroundUpdates": 1, "path": null, "skinnable": false, "sourceURI": null, "releaseNotesURI": null, "softDisabled": false, "foreignInstall": false, "strictCompatibility": true, "locales": [], "targetApplications": [{"id": "<EMAIL>", "minVersion": null, "maxVersion": null}], "targetPlatforms": [], "signedDate": null, "seen": true, "dependencies": [], "incognito": "spanning", "userPermissions": {"permissions": ["telemetry", "webRequest", "webRequestBlocking"], "origins": ["<all_urls>"], "data_collection": []}, "optionalPermissions": {"permissions": [], "origins": [], "data_collection": []}, "requestedPermissions": {"permissions": [], "origins": [], "data_collection": []}, "icons": {}, "iconURL": null, "blocklistAttentionDismissed": false, "blocklistState": 0, "blocklistURL": null, "startupData": null, "hidden": true, "installTelemetryInfo": null, "recommendationState": null, "rootURI": "resource://builtin-addons/search-detection/", "location": "app-builtin"}, {"id": "<EMAIL>", "syncGUID": "{0d1c2532-beda-4c2d-bad2-7fda38cc003b}", "version": "1.3.3", "type": "theme", "loader": null, "updateURL": null, "installOrigins": null, "manifestVersion": 2, "optionsURL": null, "optionsType": null, "optionsBrowserStyle": true, "aboutURL": null, "defaultLocale": {"name": "Light", "description": "A theme with a light color scheme.", "creator": "Mozilla", "developers": null, "translators": null, "contributors": null}, "visible": true, "active": false, "userDisabled": true, "appDisabled": false, "embedderDisabled": false, "installDate": 1735630750640, "applyBackgroundUpdates": 1, "path": null, "skinnable": false, "sourceURI": null, "releaseNotesURI": null, "softDisabled": false, "foreignInstall": false, "strictCompatibility": true, "locales": [], "targetApplications": [{"id": "<EMAIL>", "minVersion": null, "maxVersion": null}], "targetPlatforms": [], "signedDate": null, "seen": true, "dependencies": [], "userPermissions": null, "optionalPermissions": null, "requestedPermissions": null, "icons": {"32": "icon.svg"}, "iconURL": null, "blocklistAttentionDismissed": false, "blocklistState": 0, "blocklistURL": null, "startupData": null, "hidden": false, "installTelemetryInfo": null, "recommendationState": null, "rootURI": "resource://builtin-themes/light/", "location": "app-builtin"}, {"id": "<EMAIL>", "syncGUID": "{cb84cc96-7b17-47e9-8e49-10d7c759754f}", "version": "1.3.3", "type": "theme", "loader": null, "updateURL": null, "installOrigins": null, "manifestVersion": 2, "optionsURL": null, "optionsType": null, "optionsBrowserStyle": true, "aboutURL": null, "defaultLocale": {"name": "Dark", "description": "A theme with a dark color scheme.", "creator": "Mozilla", "developers": null, "translators": null, "contributors": null}, "visible": true, "active": false, "userDisabled": true, "appDisabled": false, "embedderDisabled": false, "installDate": 1735630750641, "applyBackgroundUpdates": 1, "path": null, "skinnable": false, "sourceURI": null, "releaseNotesURI": null, "softDisabled": false, "foreignInstall": false, "strictCompatibility": true, "locales": [], "targetApplications": [{"id": "<EMAIL>", "minVersion": null, "maxVersion": null}], "targetPlatforms": [], "signedDate": null, "seen": true, "dependencies": [], "userPermissions": null, "optionalPermissions": null, "requestedPermissions": null, "icons": {"32": "icon.svg"}, "iconURL": null, "blocklistAttentionDismissed": false, "blocklistState": 0, "blocklistURL": null, "startupData": null, "hidden": false, "installTelemetryInfo": null, "recommendationState": null, "rootURI": "resource://builtin-themes/dark/", "location": "app-builtin"}, {"id": "<EMAIL>", "syncGUID": "{7b839dc8-310d-4579-8537-800281ea5f08}", "version": "1.5", "type": "theme", "loader": null, "updateURL": null, "installOrigins": null, "manifestVersion": 2, "optionsURL": null, "optionsType": null, "optionsBrowserStyle": true, "aboutURL": null, "defaultLocale": {"name": "Firefox Alpenglow", "description": "Use a colorful appearance for buttons, menus, and windows.", "creator": null, "developers": null, "translators": null, "contributors": null}, "visible": true, "active": false, "userDisabled": true, "appDisabled": false, "embedderDisabled": false, "installDate": 1735630750641, "applyBackgroundUpdates": 1, "path": null, "skinnable": false, "sourceURI": null, "releaseNotesURI": null, "softDisabled": false, "foreignInstall": false, "strictCompatibility": true, "locales": [], "targetApplications": [{"id": "<EMAIL>", "minVersion": null, "maxVersion": null}], "targetPlatforms": [], "signedDate": null, "seen": true, "dependencies": [], "userPermissions": null, "optionalPermissions": null, "requestedPermissions": null, "icons": {"32": "icon.svg"}, "iconURL": null, "blocklistAttentionDismissed": false, "blocklistState": 0, "blocklistURL": null, "startupData": null, "hidden": false, "installTelemetryInfo": null, "recommendationState": null, "rootURI": "resource://builtin-themes/alpenglow/", "location": "app-builtin"}]}