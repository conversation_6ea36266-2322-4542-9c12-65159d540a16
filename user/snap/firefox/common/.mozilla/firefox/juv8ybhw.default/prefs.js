// Mozilla User Preferences

// DO NOT EDIT THIS FILE.
//
// If you make changes to this file while the application is running,
// the changes will be overwritten when the application exits.
//
// To change a preference value, you can either:
// - modify it via the UI (e.g. via about:config in the browser); or
// - set it within a user.js file in your profile.

user_pref("app.normandy.first_run", false);
user_pref("app.normandy.migrationsApplied", 12);
user_pref("app.normandy.user_id", "b0ab27e1-7d31-4459-985e-59f582710565");
user_pref("app.update.lastUpdateTime.addon-background-update-timer", 1757069844);
user_pref("app.update.lastUpdateTime.browser-cleanup-thumbnails", 1757145574);
user_pref("app.update.lastUpdateTime.recipe-client-addon-run", 1757134644);
user_pref("app.update.lastUpdateTime.region-update-timer", 1757069844);
user_pref("app.update.lastUpdateTime.rs-experiment-loader-timer", **********);
user_pref("app.update.lastUpdateTime.search-engine-update-timer", 1735632248);
user_pref("app.update.lastUpdateTime.services-settings-poll-changes", 1757069844);
user_pref("app.update.lastUpdateTime.telemetry_modules_ping", 1737016719);
user_pref("app.update.lastUpdateTime.xpi-signature-verification", 1757069844);
user_pref("browser.bookmarks.addedImportButton", true);
user_pref("browser.bookmarks.restore_default_bookmarks", false);
user_pref("browser.contentblocking.category", "standard");
user_pref("browser.contextual-services.contextId", "139273a6-ec1d-4db0-a487-685278dc32fc");
user_pref("browser.contextual-services.contextId.rotation-in-days", 30);
user_pref("browser.contextual-services.contextId.timestamp-in-seconds", 1756286781);
user_pref("browser.download.panel.shown", true);
user_pref("browser.download.viewableInternally.typeWasRegistered.avif", true);
user_pref("browser.download.viewableInternally.typeWasRegistered.webp", true);
user_pref("browser.engagement.downloads-button.has-used", true);
user_pref("browser.laterrun.bookkeeping.profileCreationTime", 1735630749);
user_pref("browser.laterrun.bookkeeping.sessionCount", 4);
user_pref("browser.migration.version", 158);
user_pref("browser.ml.chat.nimbus", "ai-chatbot-rollout-in-the-old-sidebar:treatment-d");
user_pref("browser.newtabpage.activity-stream.discoverystream.publisherFavicon.enabled", true);
user_pref("browser.newtabpage.activity-stream.discoverystream.refinedCardsLayout.enabled", true);
user_pref("browser.newtabpage.activity-stream.discoverystream.reportAds.enabled", true);
user_pref("browser.newtabpage.activity-stream.impressionId", "{43c17e92-eeb2-4bfc-b079-3e2d54ac368f}");
user_pref("browser.newtabpage.activity-stream.improvesearch.topSiteSearchShortcuts.havePinned", "baidu");
user_pref("browser.newtabpage.pinned", "[{\"url\":\"https://baidu.com\",\"label\":\"@百度\",\"searchTopSite\":true}]");
user_pref("browser.newtabpage.storageVersion", 1);
user_pref("browser.pageActions.persistedActions", "{\"ids\":[\"bookmark\"],\"idsInUrlbar\":[\"bookmark\"],\"idsInUrlbarPreProton\":[],\"version\":1}");
user_pref("browser.pagethumbnails.storage_version", 3);
user_pref("browser.proton.toolbar.version", 3);
user_pref("browser.region.update.updated", **********);
user_pref("browser.rights.3.shown", true);
user_pref("browser.safebrowsing.provider.google4.lastupdatetime", "1757073216571");
user_pref("browser.safebrowsing.provider.google4.nextupdatetime", "1757075038571");
user_pref("browser.safebrowsing.provider.mozilla.lastupdatetime", "1757073419630");
user_pref("browser.safebrowsing.provider.mozilla.nextupdatetime", "1757095019630");
user_pref("browser.search.region", "CN");
user_pref("browser.search.serpEventTelemetryCategorization.regionEnabled", false);
user_pref("browser.search.totalSearches", 10);
user_pref("browser.sessionstore.upgradeBackup.latestBuildID", "20250821175018");
user_pref("browser.shell.mostRecentDateSetAsDefault", "**********");
user_pref("browser.startup.couldRestoreSession.count", 2);
user_pref("browser.startup.homepage_override.buildID", "**************");
user_pref("browser.startup.homepage_override.mstone", "142.0.1");
user_pref("browser.startup.lastColdStartupCheck", **********);
user_pref("browser.termsofuse.prefMigrationCheck", true);
user_pref("browser.topsites.contile.cacheValidFor", 10800);
user_pref("browser.topsites.contile.lastFetch", **********);
user_pref("browser.uiCustomization.state", "{\"placements\":{\"widget-overflow-fixed-list\":[],\"unified-extensions-area\":[],\"nav-bar\":[\"sidebar-button\",\"back-button\",\"forward-button\",\"stop-reload-button\",\"vertical-spacer\",\"customizableui-special-spring1\",\"urlbar-container\",\"customizableui-special-spring2\",\"downloads-button\",\"fxa-toolbar-menu-button\",\"reset-pbm-toolbar-button\",\"unified-extensions-button\"],\"toolbar-menubar\":[\"menubar-items\"],\"TabsToolbar\":[\"firefox-view-button\",\"tabbrowser-tabs\",\"new-tab-button\",\"alltabs-button\"],\"vertical-tabs\":[],\"PersonalToolbar\":[\"import-button\",\"personal-bookmarks\"]},\"seen\":[\"save-to-pocket-button\",\"developer-button\",\"screenshot-button\"],\"dirtyAreaCache\":[\"nav-bar\",\"PersonalToolbar\",\"toolbar-menubar\",\"TabsToolbar\",\"vertical-tabs\"],\"currentVersion\":23,\"newElementCount\":2}");
user_pref("browser.urlbar.placeholderName", "百度");
user_pref("browser.urlbar.placeholderName.private", "百度");
user_pref("browser.urlbar.quicksuggest.migrationVersion", 2);
user_pref("browser.urlbar.quicksuggest.scenario", "history");
user_pref("browser.urlbar.recentsearches.lastDefaultChanged", "1756286929416");
user_pref("browser.urlbar.tipShownCount.searchTip_onboard", 4);
user_pref("captchadetection.lastSubmission", 1740131);
user_pref("datareporting.dau.cachedUsageProfileGroupID", "5a42b792-6898-4679-87b0-f3c780ead0f1");
user_pref("datareporting.dau.cachedUsageProfileID", "5e20aa84-cf84-47cf-9835-f141ec0c4e81");
user_pref("datareporting.policy.dataSubmissionPolicyAcceptedVersion", 2);
user_pref("datareporting.policy.dataSubmissionPolicyNotifiedTime", "1735631998591");
user_pref("distribution.canonical-002.bookmarksProcessed", true);
user_pref("distribution.iniFile.exists.appversion", "142.0.1");
user_pref("distribution.iniFile.exists.value", true);
user_pref("doh-rollout.doneFirstRun", true);
user_pref("doh-rollout.home-region", "CN");
user_pref("dom.forms.autocomplete.formautofill", true);
user_pref("dom.push.userAgentID", "2334b732eddb41958f7f0472c429d9fd");
user_pref("extensions.activeThemeID", "<EMAIL>");
user_pref("extensions.blocklist.pingCountVersion", -1);
user_pref("extensions.colorway-builtin-themes-cleanup", 1);
user_pref("extensions.databaseSchema", 37);
user_pref("extensions.getAddons.cache.lastUpdate", **********);
user_pref("extensions.getAddons.databaseSchema", 6);
user_pref("<EMAIL>", true);
user_pref("extensions.lastAppBuildId", "**************");
user_pref("extensions.lastAppVersion", "142.0.1");
user_pref("extensions.lastPlatformVersion", "142.0.1");
user_pref("extensions.pendingOperations", false);
user_pref("extensions.pictureinpicture.enable_picture_in_picture_overrides", true);
user_pref("extensions.quarantinedDomains.list", "autoatendimento.bb.com.br,ibpf.sicredi.com.br,ibpj.sicredi.com.br,internetbanking.caixa.gov.br,www.ib12.bradesco.com.br,www2.bancobrasil.com.br");
user_pref("extensions.signatureCheckpoint", 1);
user_pref("extensions.systemAddonSet", "{\"schema\":1,\"addons\":{}}");
user_pref("extensions.webcompat.enable_shims", true);
user_pref("extensions.webcompat.perform_injections", true);
user_pref("extensions.webcompat.perform_ua_overrides", true);
user_pref("<EMAIL>", true);
user_pref("extensions.webextensions.uuids", "{\"<EMAIL>\":\"924aed5b-d9f1-419b-aca0-d6180f9e567e\",\"<EMAIL>\":\"da73ff65-5df0-435c-a0f0-56ac4ca73158\",\"<EMAIL>\":\"cc9d87ab-b167-4180-a48a-085552ff534f\",\"<EMAIL>\":\"02488c9c-8c07-442d-bb8f-1da923654492\",\"<EMAIL>\":\"9a310967-e580-48bf-b3e8-4eafebbc122d\",\"<EMAIL>\":\"96beb1fa-49f3-4b0f-b445-3f9b029912b9\",\"<EMAIL>\":\"d9f82f95-2305-4637-bacc-4e85fe109c72\",\"<EMAIL>\":\"7a417175-9f6b-447e-9fe0-17b9a1ccb2d3\"}");
user_pref("gecko.handlerService.defaultHandlersVersion", 1);
user_pref("idle.lastDailyNotification", **********);
user_pref("media.gmp-gmpopenh264.abi", "x86_64-gcc3");
user_pref("media.gmp-gmpopenh264.hashValue", "f5246bf14d038adf4ce0c4360262ab722bc3de4220f047c3d542b4c564074b4877dc8659e3125c5171c749e7ce93f20cc63777eb5e1539e960670cbc5f30ac85");
user_pref("media.gmp-gmpopenh264.lastDownload", 1756286815);
user_pref("media.gmp-gmpopenh264.lastInstallStart", 1756286812);
user_pref("media.gmp-gmpopenh264.lastUpdate", 1756286815);
user_pref("media.gmp-gmpopenh264.version", "2.6.0");
user_pref("media.gmp-manager.buildID", "**************");
user_pref("media.gmp-manager.lastCheck", 1757069879);
user_pref("media.gmp-manager.lastEmptyCheck", 1757069879);
user_pref("media.gmp.storage.version.observed", 1);
user_pref("media.videocontrols.picture-in-picture.video-toggle.first-seen-secs", 1735807528);
user_pref("network.cookie.CHIPS.lastMigrateDatabase", 2);
user_pref("nimbus.migrations.after-remote-settings-update", 0);
user_pref("nimbus.migrations.after-store-initialized", 2);
user_pref("nimbus.migrations.init-started", 0);
user_pref("nimbus.profileId", "d22ef1d6-eb69-405d-b458-507f5b8ab0f9");
user_pref("nimbus.profilesdatastoreservice.read.enabled", true);
user_pref("pdfjs.enabledCache.state", true);
user_pref("pdfjs.migrationVersion", 2);
user_pref("places.database.lastMaintenance", **********);
user_pref("privacy.bounceTrackingProtection.hasMigratedUserActivationData", true);
user_pref("privacy.purge_trackers.date_in_cookie_database", "0");
user_pref("privacy.purge_trackers.last_purge", "**********280");
user_pref("privacy.sanitize.clearOnShutdown.hasMigratedToNewPrefs2", true);
user_pref("privacy.sanitize.pending", "[{\"id\":\"newtab-container\",\"itemsToClear\":[],\"options\":{}}]");
user_pref("privacy.trackingprotection.allow_list.hasMigratedCategoryPrefs", true);
user_pref("security.sandbox.content.tempDirSuffix", "990d067d-d7cf-4421-8917-d14ea93e7e87");
user_pref("services.settings.blocklists.addons-bloomfilters.last_check", 1757119085);
user_pref("services.settings.blocklists.gfx.last_check", **********);
user_pref("services.settings.clock_skew_seconds", -1229);
user_pref("services.settings.last_etag", "\"1757127434654\"");
user_pref("services.settings.last_update_seconds", 1757130218);
user_pref("services.settings.main.addons-manager-settings.last_check", **********);
user_pref("services.settings.main.anti-tracking-url-decoration.last_check", **********);
user_pref("services.settings.main.bounce-tracking-protection-exceptions.last_check", **********);
user_pref("services.settings.main.cfr.last_check", **********);
user_pref("services.settings.main.cookie-banner-rules-list.last_check", **********);
user_pref("services.settings.main.devtools-compatibility-browsers.last_check", **********);
user_pref("services.settings.main.devtools-devices.last_check", **********);
user_pref("services.settings.main.doh-config.last_check", **********);
user_pref("services.settings.main.doh-providers.last_check", **********);
user_pref("services.settings.main.fingerprinting-protection-overrides.last_check", **********);
user_pref("services.settings.main.fxmonitor-breaches.last_check", **********);
user_pref("services.settings.main.hijack-blocklists.last_check", **********);
user_pref("services.settings.main.language-dictionaries.last_check", **********);
user_pref("services.settings.main.message-groups.last_check", **********);
user_pref("services.settings.main.moz-essential-domain-fallbacks.last_check", **********);
user_pref("services.settings.main.newtab-wallpapers-v2.last_check", **********);
user_pref("services.settings.main.nimbus-desktop-experiments.last_check", **********);
user_pref("services.settings.main.nimbus-secure-experiments.last_check", **********);
user_pref("services.settings.main.normandy-recipes-capabilities.last_check", **********);
user_pref("services.settings.main.partitioning-exempt-urls.last_check", **********);
user_pref("services.settings.main.password-recipes.last_check", **********);
user_pref("services.settings.main.password-rules.last_check", **********);
user_pref("services.settings.main.public-suffix-list.last_check", **********);
user_pref("services.settings.main.query-stripping.last_check", **********);
user_pref("services.settings.main.remote-permissions.last_check", **********);
user_pref("services.settings.main.search-categorization.last_check", **********);
user_pref("services.settings.main.search-config-icons.last_check", **********);
user_pref("services.settings.main.search-config-overrides-v2.last_check", **********);
user_pref("services.settings.main.search-config-v2.last_check", **********);
user_pref("services.settings.main.search-config.last_check", **********);
user_pref("services.settings.main.search-default-override-allowlist.last_check", **********);
user_pref("services.settings.main.search-telemetry-v2.last_check", **********);
user_pref("services.settings.main.sites-classification.last_check", **********);
user_pref("services.settings.main.third-party-cookie-blocking-exempt-urls.last_check", **********);
user_pref("services.settings.main.tippytop.last_check", **********);
user_pref("services.settings.main.top-sites.last_check", **********);
user_pref("services.settings.main.tracking-protection-lists.last_check", **********);
user_pref("services.settings.main.translations-models.last_check", **********);
user_pref("services.settings.main.translations-wasm.last_check", **********);
user_pref("services.settings.main.url-classifier-exceptions.last_check", **********);
user_pref("services.settings.main.url-classifier-skip-urls.last_check", **********);
user_pref("services.settings.main.url-parser-default-unknown-schemes-interventions.last_check", **********);
user_pref("services.settings.main.urlbar-persisted-search-terms.last_check", **********);
user_pref("services.settings.main.websites-with-shared-credential-backends.last_check", **********);
user_pref("services.settings.security-state.cert-revocations.last_check", 1757127821);
user_pref("services.settings.security-state.intermediates.last_check", **********);
user_pref("services.settings.security-state.onecrl.last_check", **********);
user_pref("sidebar.backupState", "{\"width\":\"\",\"command\":\"\",\"expanded\":false,\"hidden\":true}");
user_pref("sidebar.main.tools", "aichat,syncedtabs,history,bookmarks");
user_pref("sidebar.nimbus", "upgraded-sidebar-138-broad-rollout:rollout-treatment");
user_pref("sidebar.revamp", true);
user_pref("sidebar.revamp.defaultLauncherVisible", false);
user_pref("sidebar.visibility", "hide-sidebar");
user_pref("signon.firefoxRelay.showToAllBrowsers", true);
user_pref("storage.vacuum.last.content-prefs.sqlite", 1736408452);
user_pref("storage.vacuum.last.index", 0);
user_pref("storage.vacuum.last.places.sqlite", **********);
user_pref("toolkit.profiles.storeID", "31226a82");
user_pref("toolkit.startup.last_success", 1757069812);
user_pref("toolkit.telemetry.cachedClientID", "78458a56-b1ca-4d0e-bb84-e0e1ac9bc0f3");
user_pref("toolkit.telemetry.cachedProfileGroupID", "78458a56-b1ca-4d0e-bb84-e0e1ac9bc0f3");
user_pref("toolkit.telemetry.previousBuildID", "**************");
user_pref("toolkit.telemetry.reportingpolicy.firstRun", false);
user_pref("trailhead.firstrun.didSeeAboutWelcome", true);
