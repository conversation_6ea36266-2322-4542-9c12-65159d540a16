{"extensions-migration-in-import-wizard-116-rollout": {"slug": "extensions-migration-in-import-wizard-116-rollout", "branch": {"slug": "control", "ratio": 1, "feature": {"value": {}, "enabled": false, "featureId": "this-is-included-for-desktop-pre-95-support"}, "features": [{"value": {"useNewWizard": true, "showImportAll": true, "migrateExtensions": true, "showPreferencesEntrypoint": true}, "enabled": true, "featureId": "migration<PERSON><PERSON>rd"}]}, "active": false, "experimentType": "rollout", "source": "rs-loader", "userFacingName": "Extensions Migration in Import Wizard - 117+", "userFacingDescription": "This rollout makes extension migration available to users migrating from Chrome to Firefox.", "lastSeen": "2025-01-10T13:38:32.960Z", "featureIds": ["migration<PERSON><PERSON>rd"], "prefs": null, "isRollout": true, "unenrollReason": "recipe-not-seen", "prefFlips": null}, "upgrade-spotlight-rollout": {"slug": "upgrade-spotlight-rollout", "branch": {"slug": "treatment", "ratio": 1, "feature": {"value": {}, "enabled": false, "featureId": "this-is-included-for-desktop-pre-95-support"}, "features": [{"value": {"enabled": false}, "enabled": true, "featureId": "upgradeDialog"}]}, "active": false, "experimentType": "rollout", "source": "rs-loader", "userFacingName": "Upgrade Spotlight Rollout", "userFacingDescription": "Experimenting on onboarding content when you upgrade Firefox.", "lastSeen": "2025-01-10T13:38:32.986Z", "featureIds": ["upgradeDialog"], "prefs": null, "isRollout": true, "unenrollReason": "recipe-not-seen", "prefFlips": null}, "phc-rollout": {"slug": "phc-rollout", "branch": {"slug": "rollout", "ratio": 1, "feature": {"value": {}, "enabled": false, "featureId": "this-is-included-for-desktop-pre-95-support"}, "features": [{"value": {"phcEnabled": true}, "enabled": true, "featureId": "phc"}]}, "active": false, "experimentType": "rollout", "source": "rs-loader", "userFacingName": "PHC Rollout", "userFacingDescription": "Roll out the Probabilistic Heap Checker in Firefox Release.", "lastSeen": "2025-01-10T13:38:32.992Z", "featureIds": ["phc"], "prefs": null, "isRollout": true, "unenrollReason": "recipe-not-seen", "prefFlips": null}, "consolidated-search-configuration-row-desktop-relaunch": {"slug": "consolidated-search-configuration-row-desktop-relaunch", "branch": {"slug": "rollout", "ratio": 1, "feature": {"value": {}, "enabled": false, "featureId": "this-is-included-for-desktop-pre-95-support"}, "features": [{"value": {"newSearchConfigEnabled": true}, "enabled": true, "featureId": "search"}]}, "active": false, "experimentType": "rollout", "source": "rs-loader", "userFacingName": "Consolidated Search Configuration (ROW) [Desktop] Relaunch", "userFacingDescription": "Consolidated search configuration for desktop and mobile Firefox", "lastSeen": "2025-01-10T13:38:32.995Z", "featureIds": ["search"], "prefs": [{"name": "browser.search.newSearchConfig.enabled", "branch": "user", "featureId": "search", "variable": "newSearchConfigEnabled", "originalValue": null}], "isRollout": true, "unenrollReason": "recipe-not-seen"}, "disable-redirects-for-authretries": {"slug": "disable-redirects-for-authretries", "branch": {"slug": "control", "ratio": 1, "feature": {"value": {}, "enabled": false, "featureId": "this-is-included-for-desktop-pre-95-support"}, "features": [{"value": {"redirectForAuthRetriesEnabled": false}, "enabled": true, "featureId": "networkingAuth"}]}, "active": false, "experimentType": "rollout", "source": "rs-loader", "userFacingName": "Disable Redirects for Authretries", "userFacingDescription": "Rollback Redirection of the http channel for Authentication retries.", "lastSeen": "2025-01-10T13:38:32.999Z", "featureIds": ["networkingAuth"], "prefs": null, "isRollout": true, "unenrollReason": "recipe-not-seen", "prefFlips": null}, "encrypted-client-hello-fallback-mechanism": {"slug": "encrypted-client-hello-fallback-mechanism", "branch": {"slug": "control", "ratio": 1, "feature": {"value": {}, "enabled": false, "featureId": "this-is-included-for-desktop-pre-95-support"}, "features": [{"value": {"insecureFallback": true}, "enabled": true, "featureId": "echPrefs"}], "firefoxLabsTitle": null}, "active": true, "experimentType": "rollout", "source": "rs-loader", "userFacingName": "Encrypted Client Hello - Fallback Mechanism", "userFacingDescription": "This experiment enables a fallback mechanism for Encrypted Client Hello. This feature maximizes compatibility with websites that have misconfigured or unexpected DNS configurations.", "lastSeen": "2025-01-10T13:38:33.004Z", "featureIds": ["echPrefs"], "prefs": [{"name": "network.dns.echconfig.fallback_to_origin_when_all_failed", "branch": "default", "featureId": "echPrefs", "variable": "insecure<PERSON><PERSON><PERSON>", "originalValue": false}], "isRollout": true}, "fpp-floating-point-protection-rollout-linux-only": {"slug": "fpp-floating-point-protection-rollout-linux-only", "branch": {"slug": "control", "ratio": 1, "feature": {"value": {}, "enabled": false, "featureId": "this-is-included-for-desktop-pre-95-support"}, "features": [{"value": {"fdlibm_math": true}, "enabled": true, "featureId": "fingerprintingProtection"}]}, "active": false, "experimentType": "rollout", "source": "rs-loader", "userFacingName": "FPP: Floating Point Protection Rollout (Linux Only)", "userFacingDescription": "This is a rollout of FPP Floating Point Value protections on Desktop for Linux and Mac so that floating point values cannot be used as a fingerprinting vector between platforms.", "lastSeen": "2025-01-10T13:38:33.006Z", "featureIds": ["fingerprintingProtection"], "prefs": null, "isRollout": true, "unenrollReason": "recipe-not-seen", "prefFlips": null}, "fx-accounts-ping-release-rollout-2": {"slug": "fx-accounts-ping-release-rollout-2", "branch": {"slug": "control", "ratio": 1, "feature": {"value": {}, "enabled": false, "featureId": "this-is-included-for-desktop-pre-95-support"}, "features": [{"value": {"pingEnabled": true}, "enabled": true, "featureId": "fxaClientAssociation"}]}, "active": true, "experimentType": "rollout", "source": "rs-loader", "userFacingName": "Fx Accounts <PERSON> (release rollout 2)", "userFacingDescription": "Enable sending the Fx Accounts ping.", "lastSeen": "2025-01-10T13:38:33.009Z", "featureIds": ["fxaClientAssociation"], "prefs": [{"name": "identity.fxaccounts.telemetry.clientAssociationPing.enabled", "branch": "user", "featureId": "fxaClientAssociation", "variable": "pingEnabled", "originalValue": null}], "isRollout": true}, "disable-ads-startup-cache": {"slug": "disable-ads-startup-cache", "branch": {"slug": "control", "ratio": 1, "feature": {"value": {}, "enabled": false, "featureId": "this-is-included-for-desktop-pre-95-support"}, "features": [{"value": {"spocsStartupCache": false}, "enabled": true, "featureId": "newtabSpocsCache"}]}, "active": true, "experimentType": "rollout", "source": "rs-loader", "userFacingName": "Disable Ads Startup <PERSON>ache", "userFacingDescription": "Reduce the number of old / out of date ads shown on New Tab", "lastSeen": "2025-01-10T13:38:33.012Z", "featureIds": ["newtabSpocsCache"], "prefs": [{"name": "browser.newtabpage.activity-stream.discoverystream.spocs.startupCache.enabled", "branch": "user", "featureId": "newtabSpocsCache", "variable": "spocsStartupCache", "originalValue": null}], "isRollout": true}, "new-alt-text-flow-and-generation-treatment-a-rollout-for-non-en-locales": {"slug": "new-alt-text-flow-and-generation-treatment-a-rollout-for-non-en-locales", "branch": {"slug": "treatment-a", "ratio": 1, "feature": {"value": {}, "enabled": false, "featureId": "this-is-included-for-desktop-pre-95-support"}, "features": [{"value": {"enableAltText": false, "browserMlEnable": false, "enableUpdatedAddImage": true}, "enabled": true, "featureId": "pdfjs"}], "firefoxLabsTitle": null}, "active": false, "experimentType": "rollout", "source": "rs-loader", "userFacingName": "New alt text flow in PDFs - for non-EN locales", "userFacingDescription": "This experiment offers users improved alt text flow upon adding an image to a PDF and/or functionality that generates alt text for images they add to PDFs using a small, local, and fully private language model.", "lastSeen": "2025-01-10T13:38:33.028Z", "featureIds": ["pdfjs"], "prefs": null, "isRollout": true, "unenrollReason": "recipe-not-seen", "prefFlips": null}, "unified-api-for-spocs-and-top-sites-controlled-rollout-for-release-133": {"slug": "unified-api-for-spocs-and-top-sites-controlled-rollout-for-release-133", "branch": {"slug": "control", "ratio": 1, "feature": {"value": {}, "enabled": false, "featureId": "this-is-included-for-desktop-pre-95-support"}, "features": [{"value": {"unifiedAdsSpocsEnabled": true, "unifiedAdsTilesEnabled": true}, "enabled": true, "featureId": "newtabUnifiedAds"}], "firefoxLabsTitle": null}, "active": true, "experimentType": "rollout", "source": "rs-loader", "userFacingName": "Unified API for spocs and top sites - controlled rollout for release 133+", "userFacingDescription": "We're switching to a new endpoint for the services supplying sponsored content and top sites to the new tab. This is a controlled rollout for 133 on the Release channel to that new endpoint.", "lastSeen": "2025-02-27T03:00:22.229Z", "featureIds": ["newtabUnifiedAds"], "prefs": [{"name": "browser.newtabpage.activity-stream.unifiedAds.spocs.enabled", "branch": "user", "featureId": "newtabUnifiedAds", "variable": "unifiedAdsSpocsEnabled", "originalValue": null}, {"name": "browser.newtabpage.activity-stream.unifiedAds.tiles.enabled", "branch": "user", "featureId": "newtabUnifiedAds", "variable": "unifiedAdsTilesEnabled", "originalValue": null}], "isFirefoxLabsOptIn": false, "firefoxLabsTitle": null, "firefoxLabsDescription": null, "firefoxLabsGroup": null, "requiresRestart": false, "isRollout": true}, "long-term-holdback-2025h1-growth-desktop": {"slug": "long-term-holdback-2025h1-growth-desktop", "branch": {"slug": "delivery", "ratio": 19, "feature": {"value": {}, "enabled": false, "featureId": "this-is-included-for-desktop-pre-95-support"}, "features": [{"value": {}, "enabled": true, "featureId": "no-feature-firefox-desktop"}], "firefoxLabsTitle": null}, "active": true, "experimentType": "nimbus", "source": "rs-loader", "userFacingName": "Long term holdback 2025H1 Growth Desktop", "userFacingDescription": "Long-term holdback for 2025 H1 Growth experiments", "lastSeen": "2025-01-10T13:38:33.038Z", "featureIds": ["no-feature-firefox-desktop"], "prefs": [], "isRollout": false}, "fox-doodle-and-tail-fox-2025-v2-rollout": {"slug": "fox-doodle-and-tail-fox-2025-v2-rollout", "branch": {"slug": "treatment-a", "ratio": 1, "feature": {"value": {}, "enabled": false, "featureId": "this-is-included-for-desktop-pre-95-support"}, "features": [{"value": {"messages": [{"id": "FOX_DOODLE_SET_DEFAULT", "groups": ["eco"], "content": {"id": "FOX_DOODLE_SET_DEFAULT", "screens": [{"id": "FOX_DOODLE_SET_DEFAULT_SCREEN", "content": {"logo": {"height": "125px", "imageURL": "chrome://activity-stream/content/data/content/assets/fox-doodle-waving.gif", "reducedMotionImageURL": "chrome://activity-stream/content/data/content/assets/fox-doodle-waving-static.png"}, "title": {"fontSize": "22px", "string_id": "fox-doodle-pin-headline", "fontWeight": 590, "paddingBlock": "4px 0", "letterSpacing": 0, "paddingInline": "24px"}, "subtitle": {"fontSize": "15px", "string_id": "fox-doodle-pin-body", "lineHeight": "1.4", "marginBlock": "8px 16px", "letterSpacing": 0, "paddingInline": "24px"}, "dismiss_button": {"action": {"navigate": true}}, "primary_button": {"label": {"string_id": "fox-doodle-pin-primary", "marginBlock": "4px 0", "paddingBlock": "0", "paddingInline": "16px"}, "action": {"type": "SET_DEFAULT_BROWSER", "navigate": true}}, "secondary_button": {"label": {"string_id": "fox-doodle-pin-secondary", "marginBlock": "0 -20px"}, "action": {"navigate": true}}}}], "backdrop": "transparent", "template": "multistage", "transitions": true}, "trigger": {"id": "defaultBrowserCheck"}, "template": "spotlight", "frequency": {"lifetime": 2}, "targeting": "source == 'startup' && !isMajorUpgrade && !activeNotifications && !isDefaultBrowser && !willShowDefaultPrompt && (currentDate|date - profileAgeCreated|date) / ******** >= 28 && previousSessionEnd && userPrefs.cfrFeatures == true", "_nimbusFeature": "fxms-message-10"}, {"id": "TAIL_FOX_SET_DEFAULT", "groups": ["eco"], "content": {"id": "TAIL_FOX_SET_DEFAULT_CONTENT", "screens": [{"id": "TAIL_FOX_SET_DEFAULT_SCREEN", "content": {"logo": {"height": "140px", "imageURL": "chrome://activity-stream/content/data/content/assets/fox-doodle-tail.png", "reducedMotionImageURL": "chrome://activity-stream/content/data/content/assets/fox-doodle-tail.png"}, "title": {"fontSize": "22px", "string_id": "tail-fox-spotlight-title", "fontWeight": 590, "paddingBlock": "4px 0", "letterSpacing": 0, "paddingInline": "24px"}, "subtitle": {"fontSize": "15px", "string_id": "tail-fox-spotlight-subtitle", "lineHeight": "1.4", "marginBlock": "8px 16px", "letterSpacing": 0, "paddingInline": "24px"}, "dismiss_button": {"action": {"navigate": true}}, "primary_button": {"label": {"string_id": "tail-fox-spotlight-primary-button", "marginBlock": "4px 0", "paddingBlock": "0", "paddingInline": "16px"}, "action": {"type": "SET_DEFAULT_BROWSER", "navigate": true}}, "secondary_button": {"label": {"string_id": "tail-fox-spotlight-secondary-button", "marginBlock": "0 -20px"}, "action": {"navigate": true}}}}], "backdrop": "transparent", "template": "multistage", "transitions": true}, "trigger": {"id": "defaultBrowserCheck"}, "template": "spotlight", "frequency": {"lifetime": 1}, "targeting": "source == 'startup' && !isMajorUpgrade && !activeNotifications && !isDefaultBrowser && !willShowDefaultPrompt && (currentDate|date - profileAgeCreated|date) / ******** < 28 && (currentDate|date - profileAgeCreated|date) / ******** >= 7 && previousSessionEnd && userPrefs.cfrFeatures == true", "_nimbusFeature": "fxms-message-10"}], "template": "multi"}, "enabled": true, "featureId": "fxms-message-10"}], "firefoxLabsTitle": null}, "active": false, "experimentType": "rollout", "source": "rs-loader", "userFacingName": "<PERSON> and <PERSON><PERSON> Fox 2025 V2 - Rollout", "userFacingDescription": "Set to default prompt highlighting benefits of choosing Firefox", "lastSeen": "2025-01-10T13:38:33.048Z", "featureIds": ["fxms-message-10"], "prefs": [], "isRollout": true, "unenrollReason": "recipe-not-seen"}, "fox-doodle-multi-action-cta-2025-rollout": {"slug": "fox-doodle-multi-action-cta-2025-rollout", "branch": {"slug": "treatment-a", "ratio": 1, "feature": {"value": {}, "enabled": false, "featureId": "this-is-included-for-desktop-pre-95-support"}, "features": [{"value": {"messages": [{"id": "FOX_DOODLE_MULTI_ACTION", "groups": ["eco"], "content": {"id": "FOX_DOODLE_MULTI_ACTION", "screens": [{"id": "FOX_DOODLE_MULTI_ACTION_CHECKBOXES_MAC_WINDOWS_NON_MSIX_EARLY_DAY_SCREEN", "content": {"logo": {"height": "125px", "imageURL": "https://firefox-settings-attachments.cdn.mozilla.net/main-workspace/ms-images/3600b535-329d-4147-89c1-689108a804a8.gif", "reducedMotionImageURL": "https://firefox-settings-attachments.cdn.mozilla.net/main-workspace/ms-images/35814ce7-903b-4140-a7b8-9ad8a1a010b3.png"}, "tiles": {"data": [{"id": "checkbox-1-set-default", "label": {"string_id": "mr2022-onboarding-easy-setup-set-default-checkbox-label"}, "action": {"type": "SET_DEFAULT_BROWSER"}, "defaultValue": true}, {"id": "checkbox-2-pin", "label": {"string_id": "mr2022-onboarding-pin-primary-button-label"}, "action": {"type": "PIN_FIREFOX_TO_TASKBAR"}, "defaultValue": true}], "type": "multiselect", "style": {"gap": "10px", "fontSize": "13px", "lineHeight": "1.4", "marginInline": "auto", "letterSpacing": 0}}, "title": {"fontSize": "22px", "string_id": "multi-cta-fox-doodle-title", "fontWeight": 590, "marginBlock": "-16px 0", "letterSpacing": 0, "paddingInline": "24px"}, "subtitle": {"fontSize": "13px", "string_id": "multi-cta-fox-doodle-quick-reminder-subtitle", "textAlign": "center", "lineHeight": "1.4", "marginBlock": "8px 24px", "letterSpacing": 0, "paddingInline": "24px"}, "dismiss_button": {"action": {"navigate": true}}, "primary_button": {"label": {"string_id": "multi-cta-fox-doodle-start-browsing-primary-button-label"}, "action": {"data": {"actions": []}, "type": "MULTI_ACTION", "navigate": true, "collectSelect": true}}}}], "backdrop": "transparent", "template": "multistage", "transitions": true}, "trigger": {"id": "defaultBrowserCheck"}, "priority": 2, "template": "spotlight", "frequency": {"lifetime": 1}, "targeting": "doesAppNeedPin && (os.isMac || (os.isWindows && os.windowsVersion >= 10.0 && os.windowsBuildNumber >= 18362 && !isMSIX)) && source == 'startup' && !isMajorUpgrade && !activeNotifications && !isDefaultBrowser && !willShowDefaultPrompt && previousSessionEnd && userPrefs.cfrFeatures == true && (currentDate|date - profileAgeCreated|date) / ******** <= 28 && (currentDate|date - profileAgeCreated|date) / ******** >= 7", "_nimbusFeature": "spotlight"}, {"id": "FOX_DOODLE_MULTI_ACTION", "groups": ["eco"], "content": {"id": "FOX_DOODLE_MULTI_ACTION", "screens": [{"id": "FOX_DOODLE_MULTI_ACTION_CHECKBOXES_MSIX_EARLY_DAY_SCREEN", "content": {"logo": {"height": "125px", "imageURL": "https://firefox-settings-attachments.cdn.mozilla.net/main-workspace/ms-images/3600b535-329d-4147-89c1-689108a804a8.gif", "reducedMotionImageURL": "https://firefox-settings-attachments.cdn.mozilla.net/main-workspace/ms-images/35814ce7-903b-4140-a7b8-9ad8a1a010b3.png"}, "tiles": {"data": [{"id": "checkbox-1-set-default", "label": {"string_id": "mr2022-onboarding-easy-setup-set-default-checkbox-label"}, "action": {"type": "SET_DEFAULT_BROWSER"}, "defaultValue": true}, {"id": "checkbox-2-pin", "label": {"string_id": "multi-cta-fox-doodle-pin-checkbox"}, "action": {"type": "PIN_FIREFOX_TO_TASKBAR"}, "defaultValue": true}, {"id": "checkbox-3-pin-to-start", "label": {"string_id": "multi-cta-fox-doodle-pin-startmenu-checkbox"}, "action": {"type": "PIN_FIREFOX_TO_START_MENU"}, "defaultValue": true}], "type": "multiselect", "style": {"gap": "10px", "fontSize": "13px", "lineHeight": "1.4", "marginInline": "auto", "letterSpacing": 0}}, "title": {"fontSize": "22px", "string_id": "multi-cta-fox-doodle-title", "fontWeight": 590, "marginBlock": "-16px 0", "letterSpacing": 0, "paddingInline": "24px"}, "subtitle": {"fontSize": "13px", "string_id": "multi-cta-fox-doodle-quick-reminder-subtitle", "textAlign": "center", "lineHeight": "1.4", "marginBlock": "8px 24px", "letterSpacing": 0, "paddingInline": "24px"}, "dismiss_button": {"action": {"navigate": true}}, "primary_button": {"label": {"string_id": "mr2022-onboarding-gratitude-secondary-button-label"}, "action": {"data": {"actions": []}, "type": "MULTI_ACTION", "navigate": true, "collectSelect": true}}}}], "backdrop": "transparent", "template": "multistage", "transitions": true}, "trigger": {"id": "defaultBrowserCheck"}, "priority": 2, "template": "spotlight", "frequency": {"lifetime": 1}, "targeting": "doesAppNeedPin && isMSIX && os.isWindows && os.windowsVersion >= 10.0 && os.windowsBuildNumber >= 18362 && source == 'startup' && !isMajorUpgrade && !activeNotifications && !isDefaultBrowser && !willShowDefaultPrompt && previousSessionEnd && userPrefs.cfrFeatures == true && (currentDate|date - profileAgeCreated|date) / ******** <= 28 && (currentDate|date - profileAgeCreated|date) / ******** >= 7", "_nimbusFeature": "spotlight"}, {"id": "FOX_DOODLE_MULTI_ACTION", "groups": ["eco"], "content": {"id": "FOX_DOODLE_MULTI_ACTION", "screens": [{"id": "FOX_DOODLE_MULTI_ACTION_CHECKBOXES_MAC_WINDOWS_NON_MSIX_EXISTING_USER_SCREEN", "content": {"logo": {"height": "125px", "imageURL": "https://firefox-settings-attachments.cdn.mozilla.net/main-workspace/ms-images/3600b535-329d-4147-89c1-689108a804a8.gif", "reducedMotionImageURL": "https://firefox-settings-attachments.cdn.mozilla.net/main-workspace/ms-images/35814ce7-903b-4140-a7b8-9ad8a1a010b3.png"}, "tiles": {"data": [{"id": "checkbox-1-set-default", "label": {"string_id": "mr2022-onboarding-easy-setup-set-default-checkbox-label"}, "action": {"type": "SET_DEFAULT_BROWSER"}, "defaultValue": true}, {"id": "checkbox-2-pin", "label": {"string_id": "mr2022-onboarding-pin-primary-button-label"}, "action": {"type": "PIN_FIREFOX_TO_TASKBAR"}, "defaultValue": true}], "type": "multiselect", "style": {"gap": "10px", "fontSize": "13px", "lineHeight": "1.4", "marginInline": "auto", "letterSpacing": 0}}, "title": {"fontSize": "22px", "string_id": "multi-cta-fox-doodle-title", "fontWeight": 590, "marginBlock": "-16px 0", "letterSpacing": 0, "paddingInline": "24px"}, "subtitle": {"fontSize": "13px", "string_id": "multi-cta-fox-doodle-quick-reminder-subtitle", "textAlign": "center", "lineHeight": "1.4", "marginBlock": "8px 24px", "letterSpacing": 0, "paddingInline": "24px"}, "dismiss_button": {"action": {"navigate": true}}, "primary_button": {"label": {"string_id": "multi-cta-fox-doodle-start-browsing-primary-button-label"}, "action": {"data": {"actions": []}, "type": "MULTI_ACTION", "navigate": true, "collectSelect": true}}}}], "backdrop": "transparent", "template": "multistage", "transitions": true}, "trigger": {"id": "defaultBrowserCheck"}, "priority": 2, "template": "spotlight", "frequency": {"lifetime": 2}, "targeting": "doesAppNeedPin && (os.isMac || (os.isWindows && os.windowsVersion >= 10.0 && os.windowsBuildNumber >= 18362 && !isMSIX)) && source == 'startup' && !isMajorUpgrade && !activeNotifications && !isDefaultBrowser && !willShowDefaultPrompt && previousSessionEnd && userPrefs.cfrFeatures == true && (currentDate|date - profileAgeCreated|date) / ******** >= 28", "_nimbusFeature": "spotlight"}, {"id": "FOX_DOODLE_MULTI_ACTION", "groups": ["eco"], "content": {"id": "FOX_DOODLE_MULTI_ACTION", "screens": [{"id": "FOX_DOODLE_MULTI_ACTION_CHECKBOXES_MSIX_SCREEN_EXISTING_USER_SCREEN", "content": {"logo": {"height": "125px", "imageURL": "https://firefox-settings-attachments.cdn.mozilla.net/main-workspace/ms-images/3600b535-329d-4147-89c1-689108a804a8.gif", "reducedMotionImageURL": "https://firefox-settings-attachments.cdn.mozilla.net/main-workspace/ms-images/35814ce7-903b-4140-a7b8-9ad8a1a010b3.png"}, "tiles": {"data": [{"id": "checkbox-1-set-default", "label": {"string_id": "mr2022-onboarding-easy-setup-set-default-checkbox-label"}, "action": {"type": "SET_DEFAULT_BROWSER"}, "defaultValue": true}, {"id": "checkbox-2-pin", "label": {"string_id": "multi-cta-fox-doodle-pin-checkbox"}, "action": {"type": "PIN_FIREFOX_TO_TASKBAR"}, "defaultValue": true}, {"id": "checkbox-3-pin-to-start", "label": {"string_id": "multi-cta-fox-doodle-pin-startmenu-checkbox"}, "action": {"type": "PIN_FIREFOX_TO_START_MENU"}, "defaultValue": true}], "type": "multiselect", "style": {"gap": "10px", "fontSize": "13px", "lineHeight": "1.4", "marginInline": "auto", "letterSpacing": 0}}, "title": {"fontSize": "22px", "string_id": "multi-cta-fox-doodle-title", "fontWeight": 590, "marginBlock": "-16px 0", "letterSpacing": 0, "paddingInline": "24px"}, "subtitle": {"fontSize": "13px", "string_id": "multi-cta-fox-doodle-quick-reminder-subtitle", "textAlign": "center", "lineHeight": "1.4", "marginBlock": "8px 24px", "letterSpacing": 0, "paddingInline": "24px"}, "dismiss_button": {"action": {"navigate": true}}, "primary_button": {"label": {"string_id": "mr2022-onboarding-gratitude-secondary-button-label"}, "action": {"data": {"actions": []}, "type": "MULTI_ACTION", "navigate": true, "collectSelect": true}}}}], "backdrop": "transparent", "template": "multistage", "transitions": true}, "trigger": {"id": "defaultBrowserCheck"}, "priority": 2, "template": "spotlight", "frequency": {"lifetime": 2}, "targeting": "doesAppNeedPin && isMSIX && os.isWindows && os.windowsVersion >= 10.0 && os.windowsBuildNumber >= 18362 && source == 'startup' && !isMajorUpgrade && !activeNotifications && !isDefaultBrowser && !willShowDefaultPrompt && previousSessionEnd && userPrefs.cfrFeatures == true && (currentDate|date - profileAgeCreated|date) / ******** >= 28", "_nimbusFeature": "spotlight"}, {"id": "TAIL_FOX_SET_DEFAULT", "groups": ["eco"], "content": {"id": "TAIL_FOX_SET_DEFAULT_CONTENT", "screens": [{"id": "TAIL_FOX_SET_DEFAULT_SCREEN", "content": {"logo": {"height": "140px", "imageURL": "https://firefox-settings-attachments.cdn.mozilla.net/main-workspace/ms-images/92de1223-159b-4c2c-83bb-38e5ab767900.png", "reducedMotionImageURL": "https://firefox-settings-attachments.cdn.mozilla.net/main-workspace/ms-images/92de1223-159b-4c2c-83bb-38e5ab767900.png"}, "title": {"fontSize": "22px", "string_id": "tail-fox-spotlight-title", "fontWeight": 590, "paddingBlock": "4px 0", "letterSpacing": 0, "paddingInline": "24px"}, "subtitle": {"fontSize": "15px", "string_id": "tail-fox-spotlight-subtitle", "lineHeight": "1.4", "marginBlock": "8px 16px", "letterSpacing": 0, "paddingInline": "24px"}, "dismiss_button": {"action": {"navigate": true}}, "primary_button": {"label": {"string_id": "tail-fox-spotlight-primary-button", "marginBlock": "4px 0", "paddingBlock": "0", "paddingInline": "16px"}, "action": {"type": "SET_DEFAULT_BROWSER", "navigate": true}}, "secondary_button": {"label": {"string_id": "tail-fox-spotlight-secondary-button", "marginBlock": "0 -20px"}, "action": {"navigate": true}}}}], "backdrop": "transparent", "template": "multistage", "transitions": true}, "trigger": {"id": "defaultBrowserCheck"}, "template": "spotlight", "frequency": {"lifetime": 1}, "targeting": "source == 'startup' && !isMajorUpgrade && !activeNotifications && !isDefaultBrowser && !willShowDefaultPrompt && (currentDate|date - profileAgeCreated|date) / ******** <= 28 && (currentDate|date - profileAgeCreated|date) / ******** >= 7 && previousSessionEnd && userPrefs.cfrFeatures == true && ((os.isMac && !doesAppNeedPin) || (os.isWindows && os.windowsVersion >= 10.0 && os.windowsBuildNumber >= 18362 && !doesAppNeedPin) || (os.isLinux || os.isWindows && (os.windowsVersion < 10.0 || os.windowsBuildNumber < 18362)))", "_nimbusFeature": "spotlight"}, {"id": "FOX_DOODLE_SET_DEFAULT", "groups": ["eco"], "content": {"id": "FOX_DOODLE_SET_DEFAULT", "screens": [{"id": "FOX_DOODLE_SET_DEFAULT_SCREEN", "content": {"logo": {"height": "125px", "imageURL": "https://firefox-settings-attachments.cdn.mozilla.net/main-workspace/ms-images/3600b535-329d-4147-89c1-689108a804a8.gif", "reducedMotionImageURL": "https://firefox-settings-attachments.cdn.mozilla.net/main-workspace/ms-images/35814ce7-903b-4140-a7b8-9ad8a1a010b3.png"}, "title": {"fontSize": "22px", "string_id": "fox-doodle-pin-headline", "fontWeight": 590, "paddingBlock": "4px 0", "letterSpacing": 0, "paddingInline": "24px"}, "subtitle": {"fontSize": "15px", "string_id": "fox-doodle-pin-body", "lineHeight": "1.4", "marginBlock": "8px 16px", "letterSpacing": 0, "paddingInline": "24px"}, "dismiss_button": {"action": {"navigate": true}}, "primary_button": {"label": {"string_id": "fox-doodle-pin-primary", "marginBlock": "4px 0", "paddingBlock": "0", "paddingInline": "16px"}, "action": {"type": "SET_DEFAULT_BROWSER", "navigate": true}}, "secondary_button": {"label": {"string_id": "fox-doodle-pin-secondary", "marginBlock": "0 -20px"}, "action": {"navigate": true}}}}], "backdrop": "transparent", "template": "multistage", "transitions": true}, "trigger": {"id": "defaultBrowserCheck"}, "template": "spotlight", "frequency": {"lifetime": 2}, "targeting": "source == 'startup' && !isMajorUpgrade && !activeNotifications && !isDefaultBrowser && !willShowDefaultPrompt && (currentDate|date - profileAgeCreated|date) / ******** >= 28 && previousSessionEnd && userPrefs.cfrFeatures == true && ((os.isMac && !doesAppNeedPin) || (os.isWindows && os.windowsVersion >= 10.0 && os.windowsBuildNumber >= 18362 && !doesAppNeedPin) || (os.isLinux || os.isWindows && (os.windowsVersion < 10.0 || os.windowsBuildNumber < 18362)))", "_nimbusFeature": "spotlight"}], "template": "multi"}, "enabled": true, "featureId": "spotlight"}], "firefoxLabsTitle": null}, "active": true, "experimentType": "rollout", "source": "rs-loader", "userFacingName": "Fox Doodle Multi-Action CTA 2025 - Rollout", "userFacingDescription": "Rollout: Setting Firefox as primary browser", "lastSeen": "2025-02-27T03:00:22.257Z", "featureIds": ["spotlight"], "prefs": [], "isFirefoxLabsOptIn": false, "firefoxLabsTitle": null, "firefoxLabsDescription": null, "firefoxLabsGroup": null, "requiresRestart": false, "isRollout": true}, "ai-chatbot-rollout-in-the-old-sidebar": {"slug": "ai-chatbot-rollout-in-the-old-sidebar", "branch": {"slug": "treatment-d", "ratio": 1, "feature": {"value": {}, "enabled": false, "featureId": "this-is-included-for-desktop-pre-95-support"}, "features": [{"value": {"prefs": {"enabled": {"value": true}}, "minVersion": "133.!"}, "enabled": true, "featureId": "chatbot"}], "firefoxLabsTitle": null}, "active": false, "experimentType": "rollout", "source": "rs-loader", "userFacingName": "Sidebar: <PERSON><PERSON><PERSON> Entrypoint Rollout", "userFacingDescription": "Updating the list of tools in the sidebar to include an opt-in feature that, in the case of opt-in, allows users to see the chatbot website side-by-side with open tabs.", "lastSeen": "2025-02-27T03:00:22.264Z", "featureIds": ["chatbot"], "prefs": null, "isFirefoxLabsOptIn": false, "firefoxLabsTitle": null, "firefoxLabsDescription": null, "firefoxLabsGroup": null, "requiresRestart": false, "isRollout": true, "unenrollReason": "recipe-not-seen", "prefFlips": null}, "new-tab-layout-variant-b-and-content-card-ui-release-rollout-global": {"slug": "new-tab-layout-variant-b-and-content-card-ui-release-rollout-global", "branch": {"slug": "control", "ratio": 1, "feature": {"value": {}, "enabled": false, "featureId": "this-is-included-for-desktop-pre-95-support"}, "features": [{"value": {"variantB": true}, "enabled": true, "featureId": "newtabLayoutExperiment"}, {"value": {"cardRefreshEnabled": true}, "enabled": true, "featureId": "newTabSectionsExperiment"}], "firefoxLabsTitle": null}, "active": false, "experimentType": "rollout", "source": "rs-loader", "userFacingName": "New Tab Layout Variant B and Content Card UI Release Rollout - Global", "userFacingDescription": "Enhance the visual design of the New Tab page", "lastSeen": "2025-02-27T03:00:22.281Z", "featureIds": ["newTabSectionsExperiment", "newtabLayoutExperiment"], "prefs": null, "isFirefoxLabsOptIn": false, "firefoxLabsTitle": null, "firefoxLabsDescription": null, "firefoxLabsGroup": null, "requiresRestart": false, "isRollout": true, "unenrollReason": "invalid-feature", "prefFlips": null}, "crlite-rollout": {"slug": "crlite-rollout", "branch": {"slug": "rollout", "ratio": 1, "feature": {"value": {}, "enabled": false, "featureId": "this-is-included-for-desktop-pre-95-support"}, "features": [{"value": {"mode": 3, "channel": "default", "enabled": true}, "enabled": true, "featureId": "crlite"}], "firefoxLabsTitle": null}, "active": false, "experimentType": "rollout", "source": "rs-loader", "userFacingName": "CRLite Rollout", "userFacingDescription": "Enables CRLite, a privacy-preserving tool for checking the revocation status of TLS certificates.", "lastSeen": "2025-02-27T03:00:22.301Z", "featureIds": ["crlite"], "prefs": null, "isFirefoxLabsOptIn": false, "firefoxLabsTitle": null, "firefoxLabsDescription": null, "firefoxLabsGroup": null, "requiresRestart": false, "isRollout": true, "unenrollReason": "recipe-not-seen", "prefFlips": null}, "fx-view-discoverability-2025-rollout": {"slug": "fx-view-discoverability-2025-rollout", "branch": {"slug": "treatment-b", "ratio": 1, "feature": {"value": {}, "enabled": false, "featureId": "this-is-included-for-desktop-pre-95-support"}, "features": [{"value": {"id": "FX_VIEW_DISCOVERABILITY_ALL_USERS", "groups": ["cfr"], "content": {"id": "FX_VIEW_DISCOVERABILITY_ALL_USERS", "screens": [{"id": "FX_VIEW_DISCOVERABILITY_ALL_USERS", "anchors": [{"selector": "#firefox-view-button", "arrow_width": "15.5563", "panel_position": {"anchor_attachment": "bottomcenter", "callout_attachment": "topleft"}, "no_open_on_anchor": true}], "content": {"title": {"raw": {"$l10n": {"id": "fx-view-discoverability-treatment-b-title", "text": "Didn’t mean to close that tab?", "comment": ""}}, "marginInline": "0 16px"}, "width": "342px", "padding": 16, "position": "callout", "subtitle": {"raw": {"$l10n": {"id": "fx-view-discoverability-treatment-b-subtitle", "text": "Find and reopen it quickly here. We keep a history of your recently closed tabs for you.", "comment": ""}}, "marginBlock": "-8px -4px", "paddingInline": "34px 0"}, "title_logo": {"width": "25px", "height": "29px", "imageURL": "https://firefox-settings-attachments.cdn.mozilla.net/main-workspace/ms-images/6d17408d-a3cf-44c7-8247-63c7a700dd7e.svg", "alignment": "top", "marginInline": "4px 14px"}, "submenu_button": {"submenu": [{"id": "block_recommendation", "type": "action", "label": {"raw": {"$l10n": {"id": "fx-view-discoverability-split-button-option1-label", "text": "Don’t show this recommendation again", "comment": "Closes message and blocks message ID"}}}, "action": {"data": {"id": "FX_VIEW_DISCOVERABILITY_ALL_USERS"}, "type": "BLOCK_MESSAGE", "dismiss": true}}, {"id": "show_fewer_recommendations", "type": "action", "label": {"raw": {"$l10n": {"id": "fx-view-discoverability-split-button-option2-label", "text": "Show fewer recommendations", "comment": "Dismisses message and reduces frequency of message"}}}, "action": {"data": {"actions": [{"data": {"pref": {"name": "messaging-system-action.show-fewer-addon-recommendations", "value": true}}, "type": "SET_PREF"}, {"data": {"id": "FX_VIEW_DISCOVERABILITY_ALL_USERS"}, "type": "BLOCK_MESSAGE"}]}, "type": "MULTI_ACTION", "dismiss": true}}, {"type": "separator"}, {"id": "manage_settings", "type": "action", "label": {"raw": {"$l10n": {"id": "fx-view-discoverability-split-button-option3-label", "text": "Manage settings", "comment": "Opens about:preferences#general-cfrfeatures"}}}, "action": {"data": {"args": "preferences#general-cfrfeatures", "where": "tab"}, "type": "OPEN_ABOUT_PAGE", "dismiss": true}}], "attached_to": "additional_button"}, "secondary_button": {"label": {"raw": {"$l10n": {"id": "fx-view-discoverability-primary-button-label", "text": "Open Firefox View", "comment": "Primary button label that routes to about:firefoxview"}}}, "style": "primary", "action": {"type": "OPEN_FIREFOX_VIEW", "navigate": true}}, "additional_button": {"label": {"raw": {"$l10n": {"id": "fx-view-discoverability-secondary-button-label", "text": "<PERSON><PERSON><PERSON>", "comment": "Dismisses the message"}}}, "style": "secondary", "action": {"dismiss": true}}, "page_event_listeners": [{"action": {"dismiss": true}, "params": {"type": "click", "selectors": "#firefox-view-button"}}]}}], "backdrop": "transparent", "template": "multistage", "transitions": false}, "trigger": {"id": "nthTabClosed"}, "priority": 1, "template": "feature_callout", "frequency": {"lifetime": 1}, "targeting": "!isMajorUpgrade && !willShowDefaultPrompt && !activeNotifications && previousSessionEnd && fxViewButtonAreaType != null && tabsClosedCount >= 5 && 'browser.newtabpage.activity-stream.asrouter.userprefs.cfr.features' | preferenceValue == true && 'browser.newtabpage.activity-stream.asrouter.userprefs.cfr.addons' | preferenceValue == true"}, "enabled": true, "featureId": "fxms-message-3"}], "firefoxLabsTitle": null}, "active": false, "source": "rs-loader", "userFacingName": "Fx View Discoverability 2025 Rollout", "userFacingDescription": "Message Experiment:  Discovery of Firefox View", "lastSeen": "2025-08-27T09:26:22.265Z", "featureIds": ["fxms-message-3"], "isRollout": true, "prefs": null, "localizations": {"de": {"fx-view-discoverability-treatment-a-title": "Haben Sie den gewünschten Tab nicht gefunden?", "fx-view-discoverability-treatment-b-title": "Sie wollten diesen Tab nicht schließen?", "fx-view-discoverability-treatment-c-title": "Haben Sie den gewünschten Tab nicht gefunden?", "fx-view-discoverability-primary-button-label": "Firefox View öffnen", "fx-view-discoverability-treatment-a-subtitle": "Durchsuchen Sie alle Ihre offenen und geschlossenen Tabs, um ihn schnell zu finden.", "fx-view-discoverability-treatment-b-subtitle": "Hier können Sie sie schnell finden und wieder öffnen. Wir speichern eine Chronik Ihrer kürzlich geschlossenen Tabs für Sie.", "fx-view-discoverability-treatment-c-subtitle": "Wir zeigen Ihnen alle geöffneten Tabs in allen Fenstern.", "fx-view-discoverability-secondary-button-label": "Schließen", "fx-view-discoverability-split-button-option1-label": "Diese Empfehlung nicht mehr anzeigen", "fx-view-discoverability-split-button-option2-label": "Weniger Empfehlungen anzeigen", "fx-view-discoverability-split-button-option3-label": "Einstellungen verwalten"}, "fr": {"fx-view-discoverability-treatment-a-title": "Vous ne trouvez pas l’onglet dont vous avez besoin ?", "fx-view-discoverability-treatment-b-title": "Vous ne vouliez pas fermer cet onglet ?", "fx-view-discoverability-treatment-c-title": "Vous ne trouvez pas l’onglet dont vous avez besoin ?", "fx-view-discoverability-primary-button-label": "Ouvrir Firefox View", "fx-view-discoverability-treatment-a-subtitle": "Recherchez dans tous vos onglets ouverts et fermés pour le trouver rapidement.", "fx-view-discoverability-treatment-b-subtitle": "Retrouvez-le et rouvrez-le rapidement ici. Nous conservons un historique des onglets que vous avez récemment fermés.", "fx-view-discoverability-treatment-c-subtitle": "Nous vous affichons tous vos onglets ouverts, dans toutes vos fenêtres.", "fx-view-discoverability-secondary-button-label": "<PERSON><PERSON><PERSON>", "fx-view-discoverability-split-button-option1-label": "Ne plus afficher cette recommandation", "fx-view-discoverability-split-button-option2-label": "Afficher moins de recommandations", "fx-view-discoverability-split-button-option3-label": "<PERSON><PERSON><PERSON> les paramètres"}, "it": {"fx-view-discoverability-treatment-a-title": "Non riesci a trovare la scheda che ti serve?", "fx-view-discoverability-treatment-b-title": "Non volevi chiudere quella scheda?", "fx-view-discoverability-treatment-c-title": "Non riesci a trovare la scheda che ti serve?", "fx-view-discoverability-primary-button-label": "Apri Firefox View", "fx-view-discoverability-treatment-a-subtitle": "Cerca in tutte le tue schede, sia aperte che chiuse, per trovarla velocemente.", "fx-view-discoverability-treatment-b-subtitle": "Qui puoi trovarla e riaprirla velocemente. Conserviamo per te una cronologia delle schede chiuse di recente.", "fx-view-discoverability-treatment-c-subtitle": "Ti mostreremo tutte le schede aperte, in tutte le finestre.", "fx-view-discoverability-secondary-button-label": "<PERSON><PERSON>", "fx-view-discoverability-split-button-option1-label": "Non mostrare di nuovo questo suggerimento", "fx-view-discoverability-split-button-option2-label": "Mostra meno suggerimenti", "fx-view-discoverability-split-button-option3-label": "Gestisci impostazioni"}, "en-CA": {"fx-view-discoverability-treatment-a-title": "Can’t find the tab you need?", "fx-view-discoverability-treatment-b-title": "Didn’t mean to close that tab?", "fx-view-discoverability-treatment-c-title": "Can’t find the tab you need?", "fx-view-discoverability-primary-button-label": "Open Firefox View", "fx-view-discoverability-treatment-a-subtitle": "Search across all your open and closed tabs to quickly find it.", "fx-view-discoverability-treatment-b-subtitle": "Find and reopen it quickly here. We keep a history of your recently closed tabs for you.", "fx-view-discoverability-treatment-c-subtitle": "We’ll show you every tab you have open, across all your windows.", "fx-view-discoverability-secondary-button-label": "<PERSON><PERSON><PERSON>", "fx-view-discoverability-split-button-option1-label": "Don’t show this recommendation again", "fx-view-discoverability-split-button-option2-label": "Show fewer recommendations", "fx-view-discoverability-split-button-option3-label": "Manage settings"}, "en-GB": {"fx-view-discoverability-treatment-a-title": "Can’t find the tab you need?", "fx-view-discoverability-treatment-b-title": "Didn’t mean to close that tab?", "fx-view-discoverability-treatment-c-title": "Can’t find the tab you need?", "fx-view-discoverability-primary-button-label": "Open Firefox View", "fx-view-discoverability-treatment-a-subtitle": "Search across all your open and closed tabs to quickly find it.", "fx-view-discoverability-treatment-b-subtitle": "Find and reopen it quickly here. We keep a history of your recently closed tabs for you.", "fx-view-discoverability-treatment-c-subtitle": "We’ll show you every tab you have open, across all your windows.", "fx-view-discoverability-secondary-button-label": "<PERSON><PERSON><PERSON>", "fx-view-discoverability-split-button-option1-label": "Don’t show this recommendation again", "fx-view-discoverability-split-button-option2-label": "Show fewer recommendations", "fx-view-discoverability-split-button-option3-label": "Manage settings"}, "en-US": {"fx-view-discoverability-treatment-a-title": "Can’t find the tab you need?", "fx-view-discoverability-treatment-b-title": "Didn’t mean to close that tab?", "fx-view-discoverability-treatment-c-title": "Can’t find the tab you need?", "fx-view-discoverability-primary-button-label": "Open Firefox View", "fx-view-discoverability-treatment-a-subtitle": "Search across all your open and closed tabs to quickly find it.", "fx-view-discoverability-treatment-b-subtitle": "Find and reopen it quickly here. We keep a history of your recently closed tabs for you.", "fx-view-discoverability-treatment-c-subtitle": "We’ll show you every tab you have open, across all your windows.", "fx-view-discoverability-secondary-button-label": "<PERSON><PERSON><PERSON>", "fx-view-discoverability-split-button-option1-label": "Don’t show this recommendation again", "fx-view-discoverability-split-button-option2-label": "Show fewer recommendations", "fx-view-discoverability-split-button-option3-label": "Manage settings"}}, "isFirefoxLabsOptIn": false, "firefoxLabsTitle": null, "firefoxLabsDescription": null, "firefoxLabsDescriptionLinks": null, "firefoxLabsGroup": null, "requiresRestart": false, "unenrollReason": "recipe-not-seen", "prefFlips": null}, "pdf-annotations-highlight-treatment-b-rollout": {"slug": "pdf-annotations-highlight-treatment-b-rollout", "branch": {"slug": "treatment-b", "ratio": 1, "feature": {"value": {}, "enabled": false, "featureId": "this-is-included-for-desktop-pre-95-support"}, "features": [{"value": {"messages": [{"id": "PDF_ANNOTATIONS_HIGHLIGHT", "content": {"id": "PDF_ANNOTATIONS_HIGHLIGHT", "screens": [{"id": "PDF_ANNOTATIONS_HIGHLIGHT_SET_DEFAULT_ROLLOUT_SCREEN_1", "anchors": [{"selector": "hbox#browser", "hide_arrow": true, "absolute_position": {"top": "45px", "right": "13px"}}], "content": {"logo": {"height": "190px", "imageURL": "https://firefox-settings-attachments.cdn.mozilla.net/main-workspace/ms-images/ce5a14ef-ed44-4c16-9aaf-eb99f58bcb4f.svg"}, "title": {"raw": {"$l10n": {"id": "screen-1-header", "text": "Now you can highlight your PDFs!", "comment": ""}}}, "width": "333.3333px", "padding": 16, "position": "callout", "subtitle": {"raw": {"$l10n": {"id": "screen-1-variant-2-subtitle", "text": "With our new highlighter tool, you can read and edit PDFs in one place.", "comment": "This string appears below an image of a document with important parts highlighted."}}, "marginBlock": "-8px 0", "marginInline": "22px"}, "title_logo": {"imageURL": "https://firefox-settings-attachments.cdn.mozilla.net/main-workspace/ms-images/5c4047e1-77cc-4094-8693-6474562afc1b.svg", "alignment": "top"}, "submenu_button": {"style": "secondary", "submenu": [{"id": "item1", "type": "action", "label": {"raw": {"$l10n": {"id": "feature-tour-submenu-item-1", "text": "Don’t show this recommendation again", "comment": ""}}}, "action": {"data": {"id": "PDF_ANNOTATIONS_HIGHLIGHT"}, "type": "BLOCK_MESSAGE", "dismiss": true}}, {"id": "item2", "type": "action", "label": {"raw": {"$l10n": {"id": "feature-tour-submenu-item-2", "text": "Show fewer recommendations", "comment": ""}}}, "action": {"data": {"actions": [{"data": {"pref": {"name": "messaging-system-action.show-fewer-pdf-recommendations", "value": true}}, "type": "SET_PREF"}, {"data": {"id": "PDF_ANNOTATIONS_HIGHLIGHT"}, "type": "BLOCK_MESSAGE"}]}, "type": "MULTI_ACTION", "dismiss": true}}, {"type": "separator"}, {"id": "item3", "type": "action", "label": {"raw": {"$l10n": {"id": "feature-tour-submenu-item-3", "text": "Manage settings", "comment": ""}}}, "action": {"data": {"args": "preferences#general-cfrfeatures", "where": "tab"}, "type": "OPEN_ABOUT_PAGE", "dismiss": true}}], "attached_to": "additional_button"}, "secondary_button": {"label": {"raw": {"$l10n": {"id": "feature-tour-next-button", "text": "Next", "comment": ""}}}, "style": "primary", "action": {"data": {"pref": {"name": "messaging-system-action.pdfjsannotation.feature-tour-2", "value": "{\"screen\":\"PDF_ANNOTATIONS_HIGHLIGHT_SET_DEFAULT_ROLLOUT_SCREEN_2\",\"complete\":false}"}}, "type": "SET_PREF"}}, "additional_button": {"label": {"raw": {"$l10n": {"id": "feature-tour-dismiss-button", "text": "<PERSON><PERSON><PERSON>", "comment": ""}}}, "style": "secondary", "action": {"data": {"actions": [{"data": {"id": "PDF_ANNOTATIONS_HIGHLIGHT"}, "type": "BLOCK_MESSAGE"}, {"data": {"pref": {"name": "messaging-system-action.pdfjsannotation.feature-tour-2"}}, "type": "SET_PREF"}]}, "type": "MULTI_ACTION", "dismiss": true}}}}, {"id": "PDF_ANNOTATIONS_HIGHLIGHT_SET_DEFAULT_ROLLOUT_SCREEN_2", "anchors": [{"selector": "hbox#browser", "hide_arrow": true, "absolute_position": {"top": "45px", "right": "13px"}}], "content": {"title": {"raw": {"$l10n": {"id": "screen-2-header", "text": "Make Firefox your default PDF editor?", "comment": ""}}}, "width": "max(340px, 43ex)", "padding": 16, "position": "callout", "subtitle": {"raw": {"$l10n": {"id": "screen-2-subtitle", "text": "You’ll have access to our tools every time you open a PDF.", "comment": ""}}, "marginBlock": "-8px 0", "marginInline": "28px 0"}, "title_logo": {"width": "24px", "height": "24px", "imageURL": "chrome://branding/content/icon32.png", "alignment": "top", "marginInline": "0 12px"}, "primary_button": {"label": {"raw": {"$l10n": {"id": "feature-tour-dismiss-button", "text": "<PERSON><PERSON><PERSON>", "comment": ""}}, "marginInline": "36px 0"}, "style": "secondary", "action": {"data": {"actions": [{"data": {"id": "PDF_ANNOTATIONS_HIGHLIGHT"}, "type": "BLOCK_MESSAGE"}, {"data": {"pref": {"name": "messaging-system-action.pdfjsannotation.feature-tour-2"}}, "type": "SET_PREF"}]}, "type": "MULTI_ACTION", "dismiss": true}}, "secondary_button": {"label": {"raw": {"$l10n": {"id": "feature-tour-set-default-button", "text": "Set as default", "comment": ""}}}, "style": "primary", "action": {"data": {"actions": [{"type": "SET_DEFAULT_PDF_HANDLER"}, {"data": {"id": "PDF_ANNOTATIONS_HIGHLIGHT"}, "type": "BLOCK_MESSAGE"}, {"data": {"pref": {"name": "messaging-system-action.pdfjsannotation.feature-tour-2"}}, "type": "SET_PREF"}]}, "type": "MULTI_ACTION"}}}}], "backdrop": "transparent", "template": "multistage", "transitions": false, "tour_pref_name": "messaging-system-action.pdfjsannotation.feature-tour-2", "disableHistoryUpdates": true, "tour_pref_default_value": "{\"screen\":\"PDF_ANNOTATIONS_HIGHLIGHT_SET_DEFAULT_ROLLOUT_SCREEN_1\",\"complete\":false}"}, "trigger": {"id": "pdfJsFeatureCalloutCheck"}, "priority": 2, "template": "feature_callout", "targeting": "userPrefs.cfrFeatures && os.isWindows && os.windowsVersion >= 10 && !hasActiveEnterprisePolicies && !(isDefaultHandler || {}).pdf && (!(defaultPDFHandler || {}).registered || (defaultPDFHandler || {}).knownBrowser)"}, {"id": "PDF_ANNOTATIONS_HIGHLIGHT", "content": {"id": "PDF_ANNOTATIONS_HIGHLIGHT", "screens": [{"id": "PDF_ANNOTATIONS_HIGHLIGHT_WAS_DEFAULT_ROLLOUT_SCREEN_1", "anchors": [{"selector": "hbox#browser", "hide_arrow": true, "absolute_position": {"top": "45px", "right": "13px"}}], "content": {"logo": {"height": "190px", "imageURL": "https://firefox-settings-attachments.cdn.mozilla.net/main-workspace/ms-images/ce5a14ef-ed44-4c16-9aaf-eb99f58bcb4f.svg"}, "title": {"raw": {"$l10n": {"id": "screen-1-header", "text": "Now you can highlight your PDFs!", "comment": ""}}}, "width": "333.3333px", "padding": 16, "position": "callout", "subtitle": {"raw": {"$l10n": {"id": "screen-1-variant-2-subtitle", "text": "With our new highlighter tool, you can read and edit PDFs in one place.", "comment": "This string appears below an image of a document with important parts highlighted."}}, "marginBlock": "-8px 0", "marginInline": "22px"}, "title_logo": {"imageURL": "https://firefox-settings-attachments.cdn.mozilla.net/main-workspace/ms-images/5c4047e1-77cc-4094-8693-6474562afc1b.svg", "alignment": "top"}, "submenu_button": {"style": "secondary", "submenu": [{"id": "item1", "type": "action", "label": {"raw": {"$l10n": {"id": "feature-tour-submenu-item-1", "text": "Don’t show this recommendation again", "comment": ""}}}, "action": {"data": {"id": "PDF_ANNOTATIONS_HIGHLIGHT"}, "type": "BLOCK_MESSAGE", "dismiss": true}}, {"id": "item2", "type": "action", "label": {"raw": {"$l10n": {"id": "feature-tour-submenu-item-2", "text": "Show fewer recommendations", "comment": ""}}}, "action": {"data": {"actions": [{"data": {"pref": {"name": "messaging-system-action.show-fewer-pdf-recommendations", "value": true}}, "type": "SET_PREF"}, {"data": {"id": "PDF_ANNOTATIONS_HIGHLIGHT"}, "type": "BLOCK_MESSAGE"}]}, "type": "MULTI_ACTION", "dismiss": true}}, {"type": "separator"}, {"id": "item3", "type": "action", "label": {"raw": {"$l10n": {"id": "feature-tour-submenu-item-3", "text": "Manage settings", "comment": ""}}}, "action": {"data": {"args": "preferences#general-cfrfeatures", "where": "tab"}, "type": "OPEN_ABOUT_PAGE", "dismiss": true}}], "attached_to": "additional_button"}, "additional_button": {"label": {"raw": {"$l10n": {"id": "feature-tour-dismiss-button", "text": "<PERSON><PERSON><PERSON>", "comment": ""}}}, "style": "secondary", "action": {"data": {"actions": [{"data": {"id": "PDF_ANNOTATIONS_HIGHLIGHT"}, "type": "BLOCK_MESSAGE"}, {"data": {"pref": {"name": "messaging-system-action.pdfjsannotation.feature-tour-2"}}, "type": "SET_PREF"}]}, "type": "MULTI_ACTION", "dismiss": true}}}}], "backdrop": "transparent", "template": "multistage", "transitions": false, "tour_pref_name": "messaging-system-action.pdfjsannotation.feature-tour-2", "disableHistoryUpdates": true, "tour_pref_default_value": "{\"screen\":\"PDF_ANNOTATIONS_HIGHLIGHT_WAS_DEFAULT_ROLLOUT_SCREEN_1\",\"complete\":false}"}, "trigger": {"id": "pdfJsFeatureCalloutCheck"}, "priority": 1, "template": "feature_callout", "targeting": "userPrefs.cfrFeatures && (!os.isWindows || os.windowsVersion < 10 || hasActiveEnterprisePolicies || (isDefaultHandler || {}).pdf || ((defaultPDFHandler || {}).registered && !(defaultPDFHandler || {}).knownBrowser))"}], "template": "multi"}, "enabled": true, "featureId": "fxms-message-9"}], "firefoxLabsTitle": null}, "active": false, "source": "rs-loader", "userFacingName": "PDF Annotations - Highlight - Treatment B Rollout", "userFacingDescription": "This experiment displays a message in the PDF viewer that introduces the highlight toolbar button, and offers users a way to set Firefox as the default PDF handler, if it is not already.", "lastSeen": "2025-08-27T09:26:22.276Z", "featureIds": ["fxms-message-9"], "isRollout": true, "prefs": null, "localizations": {"de": {"screen-1-header": "Hervorhebung in PDFs ist ab sofort möglich!", "screen-2-header": "Firefox zu Ihrem Standard-PDF-Editor machen?", "screen-2-subtitle": "<PERSON>e haben jedes Mal Zugriff auf unsere Werkzeuge, wenn Sie eine PDF-Datei öffnen.", "feature-tour-next-button": "<PERSON><PERSON>", "feature-tour-dismiss-button": "Schließen", "feature-tour-submenu-item-1": "Diese Empfehlung nicht mehr anzeigen", "feature-tour-submenu-item-2": "Weniger Empfehlungen anzeigen", "feature-tour-submenu-item-3": "Einstellungen verwalten", "screen-1-variant-2-subtitle": "Mit unserem neuen Textmarker-Werkzeug können Sie PDFs an einem Ort lesen und bearbeiten.", "feature-tour-set-default-button": "Als Standard festlegen"}, "fr": {"screen-1-header": "Vous pouvez désormais surligner vos PDF !", "screen-2-header": "Faire de Firefox votre éditeur PDF par défaut ?", "screen-2-subtitle": "Vous aurez accès à nos outils à chaque fois que vous ouvrirez un fichier PDF.", "feature-tour-next-button": "Suivant", "feature-tour-dismiss-button": "<PERSON><PERSON><PERSON>", "feature-tour-submenu-item-1": "Ne plus afficher cette recommandation", "feature-tour-submenu-item-2": "Afficher moins de recommandations", "feature-tour-submenu-item-3": "<PERSON><PERSON><PERSON> les paramètres", "screen-1-variant-2-subtitle": "Avec notre nouvel outil de surlignage, vous pouvez consulter et modifier des fichiers PDF sans avoir besoin d’un autre logiciel.", "feature-tour-set-default-button": "Définir par défaut"}, "it": {"screen-1-header": "Ora puoi evidenziare i tuoi PDF!", "screen-2-header": "Impostare Firefox come editor predefinito per i PDF?", "screen-2-subtitle": "Avrai accesso ai nostri strumenti ogni volta che apri un PDF.", "feature-tour-next-button": "<PERSON><PERSON>", "feature-tour-dismiss-button": "<PERSON><PERSON>", "feature-tour-submenu-item-1": "Non mostrare di nuovo questo suggerimento", "feature-tour-submenu-item-2": "Mostra meno suggerimenti", "feature-tour-submenu-item-3": "Gestisci impostazioni", "screen-1-variant-2-subtitle": "Grazie al nuovo strumento evidenziatore puoi leggere e modificare i PDF senza bisogno di altri software.", "feature-tour-set-default-button": "Imposta come predefinito"}, "en-CA": {"screen-1-header": "Now you can highlight your PDFs!", "screen-2-header": "Make Firefox your default PDF editor?", "screen-2-subtitle": "You’ll have access to our tools every time you open a PDF.", "feature-tour-next-button": "Next", "feature-tour-dismiss-button": "<PERSON><PERSON><PERSON>", "feature-tour-submenu-item-1": "Don’t show this recommendation again", "feature-tour-submenu-item-2": "Show fewer recommendations", "feature-tour-submenu-item-3": "Manage settings", "screen-1-variant-2-subtitle": "With our new highlighter tool, you can read and edit PDFs in one place.", "feature-tour-set-default-button": "Set as default"}, "en-GB": {"screen-1-header": "Now you can highlight your PDFs!", "screen-2-header": "Make Firefox your default PDF editor?", "screen-2-subtitle": "You’ll have access to our tools every time you open a PDF.", "feature-tour-next-button": "Next", "feature-tour-dismiss-button": "<PERSON><PERSON><PERSON>", "feature-tour-submenu-item-1": "Don’t show this recommendation again", "feature-tour-submenu-item-2": "Show fewer recommendations", "feature-tour-submenu-item-3": "Manage settings", "screen-1-variant-2-subtitle": "With our new highlighter tool, you can read and edit PDFs in one place.", "feature-tour-set-default-button": "Set as default"}, "en-US": {"screen-1-header": "Now you can highlight your PDFs!", "screen-2-header": "Make Firefox your default PDF editor?", "screen-2-subtitle": "You’ll have access to our tools every time you open a PDF.", "feature-tour-next-button": "Next", "feature-tour-dismiss-button": "<PERSON><PERSON><PERSON>", "feature-tour-submenu-item-1": "Don’t show this recommendation again", "feature-tour-submenu-item-2": "Show fewer recommendations", "feature-tour-submenu-item-3": "Manage settings", "screen-1-variant-2-subtitle": "With our new highlighter tool, you can read and edit PDFs in one place.", "feature-tour-set-default-button": "Set as default"}}, "isFirefoxLabsOptIn": false, "firefoxLabsTitle": null, "firefoxLabsDescription": null, "firefoxLabsDescriptionLinks": null, "firefoxLabsGroup": null, "requiresRestart": false, "unenrollReason": "l10n-missing-locale", "prefFlips": null}, "desktop-credit-card-autofill-global-enablement-rollout-release": {"slug": "desktop-credit-card-autofill-global-enablement-rollout-release", "branch": {"slug": "creditcard-enable", "ratio": 1, "feature": {"value": {}, "enabled": false, "featureId": "this-is-included-for-desktop-pre-95-support"}, "features": [{"value": {"creditcardsSupported": "on"}, "enabled": true, "featureId": "creditcards-autofill-enabled"}], "firefoxLabsTitle": null}, "active": true, "source": "rs-loader", "userFacingName": "Desktop Credit Card Autofill Global Enablement Rollout (Release)", "userFacingDescription": "Enables credit card autofill feature.", "lastSeen": "2025-09-05T10:56:54.389Z", "featureIds": ["creditcards-autofill-enabled"], "isRollout": true, "prefs": [{"name": "extensions.formautofill.creditCards.supported", "branch": "default", "featureId": "creditcards-autofill-enabled", "variable": "creditcardsSupported", "originalValue": "on"}], "isFirefoxLabsOptIn": false, "firefoxLabsTitle": null, "firefoxLabsDescription": null, "firefoxLabsDescriptionLinks": null, "firefoxLabsGroup": null, "requiresRestart": false}, "device-migration-accounts-toolbar-icon-rollout": {"slug": "device-migration-accounts-toolbar-icon-rollout", "branch": {"slug": "treatment-a", "ratio": 1, "feature": {"value": {}, "enabled": false, "featureId": "this-is-included-for-desktop-pre-95-support"}, "features": [{"value": {"avatarIconVariant": "human-circle"}, "enabled": true, "featureId": "fxaButtonVisibility"}], "firefoxLabsTitle": null}, "active": true, "source": "rs-loader", "userFacingName": "<PERSON><PERSON> Migration Accounts Toolbar Icon  - Rollout", "userFacingDescription": "Experiment: Updating the Accounts toolbar icon", "lastSeen": "2025-09-05T10:56:54.396Z", "featureIds": ["fxaButtonVisibility"], "isRollout": true, "prefs": [], "isFirefoxLabsOptIn": false, "firefoxLabsTitle": "", "firefoxLabsDescription": "", "firefoxLabsDescriptionLinks": null, "firefoxLabsGroup": null, "requiresRestart": false}, "upgraded-sidebar-138-broad-rollout": {"slug": "upgraded-sidebar-138-broad-rollout", "branch": {"slug": "rollout-treatment", "ratio": 1, "feature": {"value": {}, "enabled": false, "featureId": "this-is-included-for-desktop-pre-95-support"}, "features": [{"value": {"id": "SIDEBAR_INTRO_138", "groups": ["cfr"], "content": {"id": "SIDEBAR_INTRO_138", "screens": [{"id": "SIDEBAR_INTRO_138_2", "anchors": [{"selector": "#urlbar-container ~ #sidebar-button", "panel_position": {"anchor_attachment": "rightcenter", "callout_attachment": "topright"}}, {"selector": "#sidebar-button", "panel_position": {"anchor_attachment": "rightcenter", "callout_attachment": "topleft"}}], "content": {"title": {"string_id": "sidebar-customization-callout-2-title", "marginInline": "4px 4px"}, "width": "324px", "padding": 16, "position": "callout", "subtitle": {"string_id": "sidebar-customization-callout-2-subtitle", "marginInline": "4px 4px"}, "dismiss_button": {"size": "small", "action": {"dismiss": true}, "marginBlock": "14px 0", "marginInline": "0 14px"}, "primary_button": {"label": {"string_id": "sidebar-customization-callout-callout-button"}, "action": {"data": {"actions": [{"data": "viewCustomizeSidebar", "type": "OPEN_SIDEBAR"}, {"data": {"pref": {"name": "messaging-system-action.sidebar-customize-138"}}, "type": "SET_PREF"}]}, "type": "MULTI_ACTION", "dismiss": true}}, "page_event_listeners": [{"action": {"dismiss": true}, "params": {"type": "click", "selectors": "#sidebar-button"}}]}}], "backdrop": "transparent", "template": "multistage", "transitions": false, "disableHistoryUpdates": true}, "trigger": {"id": "openURL", "patterns": ["*://*/*"]}, "template": "feature_callout", "frequency": {"lifetime": 1}, "targeting": "true"}, "enabled": true, "featureId": "fxms-message-13"}, {"value": {"revamp": true, "minVersion": "138.!", "defaultLauncherVisible": false}, "enabled": true, "featureId": "sidebar"}], "firefoxLabsTitle": null}, "active": true, "source": "rs-loader", "userFacingName": "Upgraded sidebar - 138 broad rollout", "userFacingDescription": "This experiment introduces the sidebar toolbar button to the users' interface.", "lastSeen": "2025-09-05T10:56:54.407Z", "featureIds": ["fxms-message-13", "sidebar"], "isRollout": true, "prefs": [{"name": "sidebar.revamp.defaultLauncherVisible", "branch": "user", "featureId": "sidebar", "variable": "defaultLauncherVisible", "originalValue": null}], "isFirefoxLabsOptIn": false, "firefoxLabsTitle": "", "firefoxLabsDescription": "", "firefoxLabsDescriptionLinks": null, "firefoxLabsGroup": null, "requiresRestart": false}, "chips-rollout-to-firefox": {"slug": "chips-rollout-to-firefox", "branch": {"slug": "control", "ratio": 1, "feature": {"value": {}, "enabled": false, "featureId": "this-is-included-for-desktop-pre-95-support"}, "features": [{"value": {"enabled": true, "chipsMigrationTarget": 2}, "enabled": true, "featureId": "partitioned-cookie-attribute"}], "firefoxLabsTitle": null}, "active": true, "source": "rs-loader", "userFacingName": "CHIPS Rollout to Firefox", "userFacingDescription": "Deploy the Partitioned cookie parameter to users", "lastSeen": "2025-09-05T10:56:54.436Z", "featureIds": ["partitioned-cookie-attribute"], "isRollout": true, "prefs": [{"name": "network.cookie.CHIPS.enabled", "branch": "default", "featureId": "partitioned-cookie-attribute", "variable": "enabled", "originalValue": true}, {"name": "network.cookie.CHIPS.migrateDatabaseTarget", "branch": "default", "featureId": "partitioned-cookie-attribute", "variable": "chipsMigrationTarget", "originalValue": 2}], "isFirefoxLabsOptIn": false, "firefoxLabsTitle": null, "firefoxLabsDescription": null, "firefoxLabsDescriptionLinks": null, "firefoxLabsGroup": null, "requiresRestart": false}, "address-bar-update-rollout-v1": {"slug": "address-bar-update-rollout-v1", "branch": {"slug": "rollout", "ratio": 1, "feature": {"value": {}, "enabled": false, "featureId": "this-is-included-for-desktop-pre-95-support"}, "features": [{"value": {"scotchBonnetEnableOverride": true}, "enabled": true, "featureId": "search"}], "firefoxLabsTitle": null}, "active": true, "source": "rs-loader", "userFacingName": "Address Bar Update Rollout v1", "userFacingDescription": "Address Bar Experience Update", "lastSeen": "2025-09-05T10:56:54.441Z", "featureIds": ["search"], "isRollout": false, "prefs": [{"name": "browser.urlbar.scotchBonnet.enableOverride", "branch": "default", "featureId": "search", "variable": "scotchBonnetEnableOverride", "originalValue": true}], "isFirefoxLabsOptIn": false, "firefoxLabsTitle": null, "firefoxLabsDescription": null, "firefoxLabsDescriptionLinks": null, "firefoxLabsGroup": null, "requiresRestart": false}, "unified-search-button-callout-rollout-v1": {"slug": "unified-search-button-callout-rollout-v1", "branch": {"slug": "control", "ratio": 1, "feature": {"value": {}, "enabled": false, "featureId": "this-is-included-for-desktop-pre-95-support"}, "features": [{"value": {"messages": [{"id": "UNIFIED_<PERSON>ARCH_CALLOUT_WITH_SCREENSHOT", "groups": ["cfr"], "content": {"id": "UNIFIED_<PERSON>ARCH_CALLOUT_WITH_SCREENSHOT", "screens": [{"id": "UNIFIED_SEARCH_CALLOUT_WITH_SCREENSHOT_USB", "anchors": [{"selector": ".urlbar-input-container", "arrow_width": "26.9", "panel_position": {"offset_x": 16, "anchor_attachment": "bottomleft", "callout_attachment": "topleft"}}], "content": {"logo": {"height": "168px", "imageURL": "https://firefox-settings-attachments.cdn.mozilla.net/main-workspace/ms-images/bec3b8d9-a658-4877-b96e-70f7bc6e3e42.svg"}, "title": {"string_id": "unified-search-callout-title", "paddingInline": "0 20px"}, "width": "333px", "padding": 16, "position": "callout", "subtitle": {"string_id": "unified-search-callout-subtitle", "marginBlock": "-12px 0", "paddingInline": "42px 20px"}, "title_logo": {"width": "32px", "height": "34px", "imageURL": "https://firefox-settings-attachments.cdn.mozilla.net/main-workspace/ms-images/2ff87710-8c83-41cd-a2b4-99d0c7ec67f1.svg", "alignment": "top", "marginBlock": "4px 0", "marginInline": "8px 18px"}, "primary_button": {"label": {"string_id": "unified-search-callout-primary-label"}, "action": {"data": {"pref": {"name": "messaging-system-action.unifiedsearch.feature-tour-1", "value": "{\"screen\":\"UNIFIED_SEARCH_CALLOUT_WITH_SCREENSHOT_ENGINE\",\"complete\": false}"}}, "type": "SET_PREF"}}, "secondary_button": {"label": {"string_id": "unified-search-callout-secondary-label"}, "action": {"dismiss": true}}}}, {"id": "UNIFIED_<PERSON>ARCH_CALLOUT_WITH_SCREENSHOT_ENGINE", "anchors": [{"selector": ".urlbar-input-container", "arrow_width": "26.9", "panel_position": {"offset_x": 16, "anchor_attachment": "bottomleft", "callout_attachment": "topleft"}}], "content": {"logo": {"height": "168px", "imageURL": "https://firefox-settings-attachments.cdn.mozilla.net/main-workspace/ms-images/97b96045-1e85-439a-b8ba-e32fc6ad8886.svg"}, "title": {"string_id": "unified-search-engines-callout-title", "paddingInline": "0 20px"}, "width": "332px", "padding": 16, "position": "callout", "subtitle": {"string_id": "unified-search-engines-callout-subtitle", "marginBlock": "-12px 0", "paddingInline": "42px 30px"}, "title_logo": {"width": "32px", "height": "34px", "imageURL": "https://firefox-settings-attachments.cdn.mozilla.net/main-workspace/ms-images/2ff87710-8c83-41cd-a2b4-99d0c7ec67f1.svg", "alignment": "top", "marginBlock": "4px 0", "marginInline": "8px 18px"}, "primary_button": {"label": {"string_id": "unified-search-engines-callout-primary-label"}, "action": {"dismiss": true}}}}], "backdrop": "transparent", "template": "multistage", "transitions": true, "tour_pref_name": "messaging-system-action.unifiedsearch.feature-tour-1", "tour_pref_default_value": "{\"screen\":\"UNIFIED_SEARCH_CALLOUT_WITH_SCREENSHOT_USB\",\"complete\":false}"}, "trigger": {"id": "defaultBrowserCheck"}, "template": "feature_callout", "frequency": {"lifetime": 1}, "targeting": "('browser.urlbar.scotchBonnet.enableOverride' | preferenceValue) && localeLanguageCode == 'en' && source == 'newtab' && !willShowDefaultPrompt && ((currentDate|date - profileAgeCreated|date) / ******** > 28)", "_nimbusFeature": "fxms-message-2"}, {"id": "UNIFIED_<PERSON>ARCH_CALLOUT_WITHOUT_SCREENSHOT", "groups": ["cfr"], "content": {"id": "UNIFIED_<PERSON>ARCH_CALLOUT_WITHOUT_SCREENSHOT", "screens": [{"id": "UNIFIED_<PERSON>ARCH_CALLOUT_WITHOUT_SCREENSHOT_USB", "anchors": [{"selector": ".urlbar-input-container", "arrow_width": "26.9", "panel_position": {"offset_x": 16, "anchor_attachment": "bottomleft", "callout_attachment": "topleft"}}], "content": {"logo": null, "title": {"string_id": "unified-search-callout-title", "paddingInline": "0 20px"}, "width": "333px", "padding": 16, "position": "callout", "subtitle": {"string_id": "unified-search-callout-subtitle", "marginBlock": "-12px 0", "paddingInline": "42px 20px"}, "title_logo": {"width": "32px", "height": "34px", "imageURL": "https://firefox-settings-attachments.cdn.mozilla.net/main-workspace/ms-images/2ff87710-8c83-41cd-a2b4-99d0c7ec67f1.svg", "alignment": "top", "marginBlock": "4px 0", "marginInline": "8px 18px"}, "primary_button": {"label": {"string_id": "unified-search-callout-primary-label"}, "action": {"data": {"pref": {"name": "messaging-system-action.unifiedsearch.feature-tour-2", "value": "{\"screen\":\"UNIFIED_SEARCH_CALLOUT_WITHOUT_SCREENSHOT_ENGINE\",\"complete\": false}"}}, "type": "SET_PREF"}}, "secondary_button": {"label": {"string_id": "unified-search-callout-secondary-label"}, "action": {"dismiss": true}}}}, {"id": "UNIFIED_<PERSON>ARCH_CALLOUT_WITHOUT_SCREENSHOT_ENGINE", "anchors": [{"selector": ".urlbar-input-container", "arrow_width": "26.9", "panel_position": {"offset_x": 16, "anchor_attachment": "bottomleft", "callout_attachment": "topleft"}}], "content": {"logo": null, "title": {"string_id": "unified-search-engines-callout-title", "paddingInline": "0 20px"}, "width": "332px", "padding": 16, "position": "callout", "subtitle": {"string_id": "unified-search-engines-callout-subtitle", "marginBlock": "-12px -8px", "paddingInline": "42px 20px"}, "title_logo": {"width": "32px", "height": "34px", "imageURL": "https://firefox-settings-attachments.cdn.mozilla.net/main-workspace/ms-images/2ff87710-8c83-41cd-a2b4-99d0c7ec67f1.svg", "alignment": "top", "marginBlock": "4px 0", "marginInline": "8px 18px"}, "primary_button": {"label": {"string_id": "unified-search-engines-callout-primary-label"}, "action": {"dismiss": true}}}}], "backdrop": "transparent", "template": "multistage", "transitions": true, "tour_pref_name": "messaging-system-action.unifiedsearch.feature-tour-2", "tour_pref_default_value": "{\"screen\":\"UNIFIED_SEARCH_CALLOUT_WITHOUT_SCREENSHOT_USB\",\"complete\": false}"}, "trigger": {"id": "defaultBrowserCheck"}, "template": "feature_callout", "frequency": {"lifetime": 1}, "targeting": "('browser.urlbar.scotchBonnet.enableOverride' | preferenceValue) && localeLanguageCode != 'en' && source == 'newtab' && !willShowDefaultPrompt && ((currentDate|date - profileAgeCreated|date) / ******** > 28)", "_nimbusFeature": "fxms-message-2"}, {"id": "ACTIONS_CALLOUT_WITH_SCREENSHOT", "groups": ["cfr"], "content": {"id": "ACTIONS_CALLOUT", "screens": [{"id": "ACTIONS_CALLOUT_WITH_SCREENSHOT", "anchors": [{"selector": ".urlbar-input-container", "arrow_width": "26.9", "panel_position": {"offset_x": 16, "anchor_attachment": "bottomleft", "callout_attachment": "topleft"}}], "content": {"logo": {"height": "168px", "imageURL": "https://firefox-settings-attachments.cdn.mozilla.net/main-workspace/ms-images/bde7e406-f576-4154-ba9c-b5f472b6f4c8.svg"}, "title": {"string_id": "actions-callout-title", "paddingInline": "0 20px"}, "width": "333px", "padding": 16, "position": "callout", "subtitle": {"string_id": "actions-callout-subtitle", "marginBlock": "0 10px", "paddingInline": "42px 20px"}, "title_logo": {"width": "32px", "height": "34px", "imageURL": "https://firefox-settings-attachments.cdn.mozilla.net/main-workspace/ms-images/2ff87710-8c83-41cd-a2b4-99d0c7ec67f1.svg", "alignment": "top", "marginBlock": "4px 0", "marginInline": "8px 18px"}, "dismiss_button": {"size": "small", "action": {"dismiss": true}, "marginBlock": "20px 0", "marginInline": "0 20px"}}}], "backdrop": "transparent", "template": "multistage", "transitions": false}, "trigger": {"id": "defaultBrowserCheck"}, "template": "feature_callout", "frequency": {"lifetime": 1}, "targeting": "('browser.urlbar.scotchBonnet.enableOverride' | preferenceValue) && localeLanguageCode == 'en' && source == 'newtab' && !willShowDefaultPrompt && ((currentDate|date - profileAgeCreated|date) / ******** > 28) && (messageImpressions.UNIFIED_SEARCH_CALLOUT_WITH_SCREENSHOT[messageImpressions.UNIFIED_SEARCH_CALLOUT_WITH_SCREENSHOT | length - 1] < currentDate|date - 172800000)", "_nimbusFeature": "fxms-message-2"}, {"id": "ACTIONS_CALLOUT_WITHOUT_SCREENSHOT", "groups": ["cfr"], "content": {"id": "ACTIONS_CALLOUT", "screens": [{"id": "ACTIONS_CALLOUT_WITHOUT_SCREENSHOT", "anchors": [{"selector": ".urlbar-input-container", "arrow_width": "26.9", "panel_position": {"offset_x": 16, "anchor_attachment": "bottomleft", "callout_attachment": "topleft"}}], "content": {"logo": null, "title": {"string_id": "actions-callout-title", "paddingInline": "0 40px"}, "width": "333px", "padding": 16, "position": "callout", "subtitle": {"string_id": "actions-callout-subtitle", "marginBlock": "-4px 4px", "paddingInline": "42px 20px"}, "title_logo": {"width": "32px", "height": "34px", "imageURL": "https://firefox-settings-attachments.cdn.mozilla.net/main-workspace/ms-images/2ff87710-8c83-41cd-a2b4-99d0c7ec67f1.svg", "alignment": "top", "marginBlock": "4px 0", "marginInline": "8px 18px"}, "dismiss_button": {"size": "small", "action": {"dismiss": true}, "marginBlock": "20px 0", "marginInline": "0 20px"}}}], "backdrop": "transparent", "template": "multistage", "transitions": false}, "trigger": {"id": "defaultBrowserCheck"}, "template": "feature_callout", "frequency": {"lifetime": 1}, "targeting": "('browser.urlbar.scotchBonnet.enableOverride' | preferenceValue) && localeLanguageCode != 'en' && source == 'newtab' && !willShowDefaultPrompt && ((currentDate|date - profileAgeCreated|date) / ******** > 28) && (messageImpressions.UNIFIED_SEARCH_CALLOUT_WITHOUT_SCREENSHOT[messageImpressions.UNIFIED_SEARCH_CALLOUT_WITHOUT_SCREENSHOT | length - 1] < currentDate|date - 172800000)", "_nimbusFeature": "fxms-message-2"}], "template": "multi"}, "enabled": true, "featureId": "fxms-message-2"}], "firefoxLabsTitle": null}, "active": true, "source": "rs-loader", "userFacingName": "unified-search-button-callout-rollout-v1", "userFacingDescription": "Feature callouts that highlight the unified search button and actions features", "lastSeen": "2025-09-05T10:56:54.447Z", "featureIds": ["fxms-message-2"], "isRollout": true, "prefs": [], "isFirefoxLabsOptIn": false, "firefoxLabsTitle": null, "firefoxLabsDescription": null, "firefoxLabsDescriptionLinks": null, "firefoxLabsGroup": null, "requiresRestart": false}, "custom-wallpapers-no-message-rollout-release": {"slug": "custom-wallpapers-no-message-rollout-release", "branch": {"slug": "control", "ratio": 1, "feature": {"value": {}, "enabled": false, "featureId": "this-is-included-for-desktop-pre-95-support"}, "features": [{"value": {"colorPicker": true, "uploadWallpaper": true}, "enabled": true, "featureId": "newtabCustomWallpaper"}], "firefoxLabsTitle": null}, "active": true, "source": "rs-loader", "userFacingName": "Custom Wallpapers, No Message Rollout - Release", "userFacingDescription": "Rollout the Custom Wallpaper feature without Messaging in Release channel", "lastSeen": "2025-09-05T10:56:54.449Z", "featureIds": ["newtabCustomWallpaper"], "isRollout": true, "prefs": [{"name": "browser.newtabpage.activity-stream.newtabWallpapers.customColor.enabled", "branch": "user", "featureId": "newtabCustomWallpaper", "variable": "colorPicker", "originalValue": null}, {"name": "browser.newtabpage.activity-stream.newtabWallpapers.customWallpaper.enabled", "branch": "user", "featureId": "newtabCustomWallpaper", "variable": "uploadWallpaper", "originalValue": null}], "isFirefoxLabsOptIn": false, "firefoxLabsTitle": null, "firefoxLabsDescription": null, "firefoxLabsDescriptionLinks": null, "firefoxLabsGroup": null, "requiresRestart": false}, "spoc-positions-and-placements-rollout": {"slug": "spoc-positions-and-placements-rollout", "branch": {"slug": "control", "ratio": 1, "feature": {"value": {}, "enabled": false, "featureId": "this-is-included-for-desktop-pre-95-support"}, "features": [{"value": {"spocCounts": "1,1,1,1,1,1", "spocPositions": "2,4,8,13,17,20", "spocPlacements": "newtab_stories_1, newtab_stories_2, newtab_stories_3, newtab_stories_4, newtab_stories_5, newtab_stories_6"}, "enabled": true, "featureId": "newtab<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "firefoxLabsTitle": null}, "active": true, "source": "rs-loader", "userFacingName": "Spoc positions and placements - Rollout", "userFacingDescription": "For spocs and rectangle, we will update placements so they're received at once by downstream systems", "lastSeen": "2025-09-05T10:56:54.454Z", "featureIds": ["newtab<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "isRollout": true, "prefs": [{"name": "browser.newtabpage.activity-stream.discoverystream.placements.spocs.counts", "branch": "user", "featureId": "newtab<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "variable": "spocCounts", "originalValue": null}, {"name": "browser.newtabpage.activity-stream.discoverystream.spoc-positions", "branch": "user", "featureId": "newtab<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "variable": "spocPositions", "originalValue": null}, {"name": "browser.newtabpage.activity-stream.discoverystream.placements.spocs", "branch": "user", "featureId": "newtab<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "variable": "spocPlacements", "originalValue": null}], "isFirefoxLabsOptIn": false, "firefoxLabsTitle": null, "firefoxLabsDescription": null, "firefoxLabsDescriptionLinks": null, "firefoxLabsGroup": null, "requiresRestart": false}, "storage-access-heuristic-restriction-rollout": {"slug": "storage-access-heuristic-restriction-rollout", "branch": {"slug": "treatment-branch", "ratio": 1, "feature": {"value": {}, "enabled": false, "featureId": "this-is-included-for-desktop-pre-95-support"}, "features": [{"value": {"redirect": false, "navigation": true, "redirect_tracker": false, "popup_interaction": true, "popup_past_interaction": false}, "enabled": true, "featureId": "storageAccessHeuristics"}], "firefoxLabsTitle": null}, "active": true, "source": "rs-loader", "userFacingName": "Storage Access Heuristic Restriction Rollout", "userFacingDescription": "We are trying to grant fewer websites exceptions to third-party storage partitioning. We are trying this on a small fraction of users to see if it affects whether or not websites break.", "lastSeen": "2025-09-05T10:56:54.456Z", "featureIds": ["storageAccessHeuristics"], "isRollout": true, "prefs": [{"name": "privacy.restrict3rdpartystorage.heuristic.recently_visited", "branch": "default", "featureId": "storageAccessHeuristics", "variable": "redirect", "originalValue": false}, {"name": "privacy.restrict3rdpartystorage.heuristic.navigation", "branch": "default", "featureId": "storageAccessHeuristics", "variable": "navigation", "originalValue": true}, {"name": "privacy.restrict3rdpartystorage.heuristic.redirect", "branch": "default", "featureId": "storageAccessHeuristics", "variable": "redirect_tracker", "originalValue": false}, {"name": "privacy.restrict3rdpartystorage.heuristic.opened_window_after_interaction", "branch": "default", "featureId": "storageAccessHeuristics", "variable": "popup_interaction", "originalValue": true}, {"name": "privacy.restrict3rdpartystorage.heuristic.window_open", "branch": "default", "featureId": "storageAccessHeuristics", "variable": "popup_past_interaction", "originalValue": false}], "isFirefoxLabsOptIn": false, "firefoxLabsTitle": null, "firefoxLabsDescription": null, "firefoxLabsDescriptionLinks": null, "firefoxLabsGroup": null, "requiresRestart": false}, "desktop-release-rollout-show-relay-to-all-browsers-next-sign-up-modal-cta": {"slug": "desktop-release-rollout-show-relay-to-all-browsers-next-sign-up-modal-cta", "branch": {"slug": "next-sign-up-modal-cta", "ratio": 1, "feature": {"value": {}, "enabled": false, "featureId": "this-is-included-for-desktop-pre-95-support"}, "features": [{"value": {"firstOfferVersion": "with-domain-and-value-prop", "showToAllBrowsers": true}, "enabled": true, "featureId": "email-autocomplete-relay"}], "firefoxLabsTitle": null}, "active": true, "source": "rs-loader", "userFacingName": "Desktop Release Rollout: Show Relay to all Browsers - \"Next\" Sign-up modal CTA", "userFacingDescription": "Firefox Relay lets you hide your real email address to keep your identity safe. This study looks at how people use Relay in the Firefox browser on their computer.", "lastSeen": "2025-09-05T10:56:54.459Z", "featureIds": ["email-autocomplete-relay"], "isRollout": true, "prefs": [{"name": "signon.firefoxRelay.showToAllBrowsers", "branch": "user", "featureId": "email-autocomplete-relay", "variable": "showToAllBrowsers", "originalValue": null}], "isFirefoxLabsOptIn": false, "firefoxLabsTitle": "", "firefoxLabsDescription": "", "firefoxLabsDescriptionLinks": null, "firefoxLabsGroup": null, "requiresRestart": false}, "vertical-tabs-feature-callout-experiment-v20-treatment-a-rollout": {"slug": "vertical-tabs-feature-callout-experiment-v20-treatment-a-rollout", "branch": {"slug": "treatment-a", "ratio": 1, "feature": {"value": {}, "enabled": false, "featureId": "this-is-included-for-desktop-pre-95-support"}, "features": [{"value": {"id": "VERTICAL_TABS_INTRO_138", "groups": [], "content": {"id": "VERTICAL_TABS_INTRO_138", "screens": [{"id": "VERTICAL_TABS_INTRO_138", "anchors": [{"selector": "#urlbar-container ~ #sidebar-button", "panel_position": {"anchor_attachment": "rightcenter", "callout_attachment": "topright"}}, {"selector": "#sidebar-button", "panel_position": {"anchor_attachment": "rightcenter", "callout_attachment": "topleft"}}], "content": {"title": {"string_id": "vertical-tabs-callout-1-title", "marginInline": "4px 4px"}, "width": "324px", "padding": 16, "position": "callout", "subtitle": {"string_id": "vertical-tabs-callout-1-subtitle", "marginInline": "4px 4px"}, "dismiss_button": {"size": "small", "action": {"dismiss": true}, "marginBlock": "14px 0", "marginInline": "0 14px"}, "primary_button": {"label": {"string_id": "vertical-tabs-callout-1-cta-button"}, "action": {"data": {"actions": [{"data": "viewCustomizeSidebar", "type": "OPEN_SIDEBAR"}, {"data": {"pref": {"name": "messaging-system-action.vertical-callout-sidebar-customize-1"}}, "type": "SET_PREF"}]}, "type": "MULTI_ACTION", "dismiss": true}}, "page_event_listeners": [{"action": {"dismiss": true}, "params": {"type": "click", "selectors": "#sidebar-button"}}]}}], "backdrop": "transparent", "template": "multistage", "transitions": false, "disableHistoryUpdates": true}, "trigger": {"id": "nthTabOpened"}, "template": "feature_callout", "frequency": {"lifetime": 1}, "targeting": "!'sidebar.verticalTabs'|preferenceValue && currentTabsOpen >= 8"}, "enabled": true, "featureId": "fxms-message-9"}], "firefoxLabsTitle": null}, "active": true, "source": "rs-loader", "userFacingName": "Vertical tabs feature callout experiment v2.0 - Treatment A Rollout", "userFacingDescription": "This rollout offers users to try vertical tabs.", "lastSeen": "2025-09-05T10:56:54.471Z", "featureIds": ["fxms-message-9"], "isRollout": true, "prefs": [], "isFirefoxLabsOptIn": false, "firefoxLabsTitle": null, "firefoxLabsDescription": null, "firefoxLabsDescriptionLinks": null, "firefoxLabsGroup": null, "requiresRestart": false}, "report-this-ad": {"slug": "report-this-ad", "branch": {"slug": "control", "ratio": 1, "feature": {"value": {}, "enabled": false, "featureId": "this-is-included-for-desktop-pre-95-support"}, "features": [{"value": {"reportAdsEnabled": true}, "enabled": true, "featureId": "newtabAdsReporting"}], "firefoxLabsTitle": null}, "active": true, "source": "rs-loader", "userFacingName": "Report this ad", "userFacingDescription": "Implementing a feature to allow users to report objectionable ads to Mozilla.", "lastSeen": "2025-09-05T10:56:54.473Z", "featureIds": ["newtabAdsReporting"], "isRollout": true, "prefs": [{"name": "browser.newtabpage.activity-stream.discoverystream.reportAds.enabled", "branch": "user", "featureId": "newtabAdsReporting", "variable": "reportAdsEnabled", "originalValue": null}], "isFirefoxLabsOptIn": false, "firefoxLabsTitle": null, "firefoxLabsDescription": null, "firefoxLabsDescriptionLinks": null, "firefoxLabsGroup": null, "requiresRestart": false}, "1-callout-contextual-chatbot-suggestion-treatment-a-tab-switching-copy-rollout": {"slug": "1-callout-contextual-chatbot-suggestion-treatment-a-tab-switching-copy-rollout", "branch": {"slug": "treatment-a-tab-switching-copy", "ratio": 1, "feature": {"value": {}, "enabled": false, "featureId": "this-is-included-for-desktop-pre-95-support"}, "features": [{"value": {"id": "SIDEBAR-CHATBOT-SINGLE", "groups": ["cfr"], "content": {"id": "SIDEBAR-CHATBOT-SINGLE", "screens": [{"id": "SIDEBAR-CHATBOT_1", "anchors": [{"selector": ".tab-icon-stack[selected]", "panel_position": {"offset_x": -3, "offset_y": 9, "anchor_attachment": "bottomcenter", "callout_attachment": "topleft"}}], "content": {"logo": {"height": "200px", "imageURL": "https://firefox-settings-attachments.cdn.mozilla.net/main-workspace/ms-images/e73326e4-3b5a-42e3-a61a-6d78f2fafbff.svg", "darkModeImageURL": "https://firefox-settings-attachments.cdn.mozilla.net/main-workspace/ms-images/a2a2516e-a756-4f8b-9826-6a6359e75096.svg"}, "title": {"string_id": "genai-chatbot-contextual-title"}, "width": "352px", "padding": 16, "position": "callout", "subtitle": {"string_id": "genai-chatbot-contextual-subtitle"}, "dismiss_button": {"size": "small", "action": {"dismiss": true}, "marginBlock": "20px 0", "marginInline": "0 20px"}, "secondary_button": {"label": {"string_id": "genai-chatbot-contextual-button"}, "style": "primary", "action": {"data": {"actions": [{"data": {"pref": {"name": "sidebar.revamp", "value": true}}, "type": "SET_PREF"}, {"data": "viewGenaiChatSidebar", "type": "OPEN_SIDEBAR"}]}, "type": "MULTI_ACTION", "dismiss": true}}}}], "backdrop": "transparent", "template": "multistage", "transitions": false, "disableHistoryUpdates": true}, "trigger": {"id": "openURL", "params": ["chat.mistral.ai", "chatgpt.com", "claude.ai", "gemini.google.com", "mistral.ai"], "patterns": ["*://huggingface.co/chat/*"]}, "template": "feature_callout", "frequency": {"custom": [{"cap": 1, "period": 604800000}], "lifetime": 2}, "targeting": "'browser.ml.chat.enabled'|preferenceValue && 'browser.ml.chat.provider'|preferenceValue == ''"}, "enabled": true, "featureId": "fxms-message-4"}], "firefoxLabsTitle": null}, "active": true, "source": "rs-loader", "userFacingName": "1-Callout Contextual Chatbot Suggestion Treatment A: Tab Switching Copy Rollout", "userFacingDescription": "Provide contextual suggestions for users who visit an AI chatbot website.", "lastSeen": "2025-09-05T10:56:54.482Z", "featureIds": ["fxms-message-4"], "isRollout": true, "prefs": [], "isFirefoxLabsOptIn": false, "firefoxLabsTitle": null, "firefoxLabsDescription": null, "firefoxLabsDescriptionLinks": null, "firefoxLabsGroup": null, "requiresRestart": false}, "disable-enrollment-status-telemetry-for-firefox-desktop-via-nimbustelemetry": {"slug": "disable-enrollment-status-telemetry-for-firefox-desktop-via-nimbustelemetry", "branch": {"slug": "control", "ratio": 1, "feature": {"value": {}, "enabled": false, "featureId": "this-is-included-for-desktop-pre-95-support"}, "features": [{"value": {"gleanMetricConfiguration": {"metrics_enabled": {"nimbus_events.enrollment_status": false}}}, "enabled": true, "featureId": "nimbusTelemetry"}], "firefoxLabsTitle": null}, "active": true, "source": "rs-loader", "userFacingName": "Disable Enrollment Status Telemetry for Firefox Desktop (via nimbusTelemetry)", "userFacingDescription": "Disable enrollment status metric", "lastSeen": "2025-09-05T10:56:54.494Z", "featureIds": ["nimbusTelemetry"], "isRollout": true, "prefs": [], "isFirefoxLabsOptIn": false, "firefoxLabsTitle": null, "firefoxLabsDescription": null, "firefoxLabsDescriptionLinks": null, "firefoxLabsGroup": null, "requiresRestart": false}, "one-click-sponsored-settings": {"slug": "one-click-sponsored-settings", "branch": {"slug": "control", "ratio": 1, "feature": {"value": {}, "enabled": false, "featureId": "this-is-included-for-desktop-pre-95-support"}, "features": [{"value": {"enabled": true}, "enabled": true, "featureId": "newtabGroupedSponsoredControls"}], "firefoxLabsTitle": null}, "active": true, "source": "rs-loader", "userFacingName": "One Click Sponsored <PERSON><PERSON>s", "userFacingDescription": "Allow users to disable New Tab's sponsored settings in one click", "lastSeen": "2025-09-05T10:56:54.498Z", "featureIds": ["newtabGroupedSponsoredControls"], "isRollout": true, "prefs": [{"name": "browser.newtabpage.activity-stream.system.showSponsoredCheckboxes", "branch": "user", "featureId": "newtabGroupedSponsoredControls", "variable": "enabled", "originalValue": null}], "isFirefoxLabsOptIn": false, "firefoxLabsTitle": null, "firefoxLabsDescription": null, "firefoxLabsDescriptionLinks": null, "firefoxLabsGroup": null, "requiresRestart": false}, "enforce-crlite-results-and-limit-use-of-ocsp-rollout": {"slug": "enforce-crlite-results-and-limit-use-of-ocsp-rollout", "branch": {"slug": "enforce", "ratio": 1, "feature": {"value": {}, "enabled": false, "featureId": "this-is-included-for-desktop-pre-95-support"}, "features": [{"value": {"mode": 2, "channel": "default", "enabled": true}, "enabled": true, "featureId": "crlite"}], "firefoxLabsTitle": null}, "active": true, "source": "rs-loader", "userFacingName": "Limit use of OCSP - Rollout", "userFacingDescription": "CRLite is a privacy-preserving tool for checking the revocation status of TLS certificates. This rollout disables OCSP in favor of CRLite for most certificates.", "lastSeen": "2025-09-05T10:56:54.510Z", "featureIds": ["crlite"], "isRollout": true, "prefs": [{"name": "security.pki.crlite_mode", "branch": "default", "featureId": "crlite", "variable": "mode", "originalValue": 3}, {"name": "security.pki.crlite_channel", "branch": "default", "featureId": "crlite", "variable": "channel", "originalValue": "all"}, {"name": "security.remote_settings.crlite_filters.enabled", "branch": "default", "featureId": "crlite", "variable": "enabled", "originalValue": false}], "isFirefoxLabsOptIn": false, "firefoxLabsTitle": null, "firefoxLabsDescription": null, "firefoxLabsDescriptionLinks": null, "firefoxLabsGroup": null, "requiresRestart": false}, "visual-card-updates": {"slug": "visual-card-updates", "branch": {"slug": "updated", "ratio": 1, "feature": {"value": {}, "enabled": false, "featureId": "this-is-included-for-desktop-pre-95-support"}, "features": [{"value": {"enabled": true}, "enabled": true, "featureId": "newtabPublisherFavicons"}, {"value": {"enabled": true}, "enabled": true, "featureId": "newtabRefinedCardsLayout"}], "firefoxLabsTitle": null}, "active": true, "source": "rs-loader", "userFacingName": "Visual Card updates", "userFacingDescription": "Visual updates to the New Tab recommended stories and sponsored cards.", "lastSeen": "2025-09-05T10:56:54.515Z", "featureIds": ["newtabPublisherFavicons", "newtabRefinedCardsLayout"], "isRollout": true, "prefs": [{"name": "browser.newtabpage.activity-stream.discoverystream.publisherFavicon.enabled", "branch": "user", "featureId": "newtabPublisherFavicons", "variable": "enabled", "originalValue": null}, {"name": "browser.newtabpage.activity-stream.discoverystream.refinedCardsLayout.enabled", "branch": "user", "featureId": "newtabRefinedCardsLayout", "variable": "enabled", "originalValue": null}], "isFirefoxLabsOptIn": false, "firefoxLabsTitle": null, "firefoxLabsDescription": null, "firefoxLabsDescriptionLinks": null, "firefoxLabsGroup": null, "requiresRestart": false}, "account-adoption-callout-passwords-global-rollout": {"slug": "account-adoption-callout-passwords-global-rollout", "branch": {"slug": "treatment-e", "ratio": 1, "feature": {"value": {}, "enabled": false, "featureId": "this-is-included-for-desktop-pre-95-support"}, "features": [{"value": {"id": "FXA_ACCOUNT_ADOPTION_PASSWORDS", "groups": ["cfr"], "content": {"id": "FXA_ACCOUNT_ADOPTION_PASSWORDS", "screens": [{"id": "FXA_ACCOUNT_ADOPTION_PASSWORDS_E", "anchors": [{"selector": "#fxa-toolbar-menu-button", "arrow_width": "15.5563", "panel_position": {"anchor_attachment": "bottomcenter", "callout_attachment": "topright", "panel_position_string": "bottomcenter topright"}, "no_open_on_anchor": true}], "content": {"logo": {"width": "104px", "height": "94px", "imageURL": "https://firefox-settings-attachments.cdn.mozilla.net/main-workspace/ms-images/ccb352db-36d2-4084-8bba-d382ab7baf77.svg", "alignment": "top", "marginBlock": "0 -4px", "marginInline": "0 14px"}, "title": {"string_id": "fxa-adoption-passwords-title"}, "width": "300px", "position": "callout", "subtitle": {"string_id": "fxa-adoption-passwords-subtitle"}, "submenu_button": {"submenu": [{"id": "block_recommendation", "type": "action", "label": {"string_id": "split-dismiss-button-dont-show-option"}, "action": {"data": {"id": "FXA_ACCOUNT_ADOPTION_PASSWORDS"}, "type": "BLOCK_MESSAGE", "dismiss": true}}, {"id": "show_fewer_recommendations", "type": "action", "label": {"string_id": "split-dismiss-button-show-fewer-option"}, "action": {"data": {"actions": [{"data": {"pref": {"name": "messaging-system-action.fxa-adoption-passwords", "value": true}}, "type": "SET_PREF"}, {"data": {"id": "FXA_ACCOUNT_ADOPTION_PASSWORDS"}, "type": "BLOCK_MESSAGE"}]}, "type": "MULTI_ACTION", "dismiss": true}}, {"type": "separator"}, {"id": "manage_settings", "type": "action", "label": {"string_id": "split-dismiss-button-manage-settings-option"}, "action": {"data": {"args": "preferences#general-cfrfeatures", "where": "tab"}, "type": "OPEN_ABOUT_PAGE", "dismiss": true}}], "attached_to": "additional_button"}, "secondary_button": {"label": {"string_id": "fxa-adoption-passwords-primary-button-label"}, "style": "primary", "action": {"data": {"entrypoint": "callout-password", "extraParams": {"utm_medium": "firefox-desktop", "utm_source": "callout", "utm_content": "password-sync-global-rollout", "utm_campaign": "save-password-callout-global-rollout"}}, "type": "FXA_SIGNIN_FLOW", "navigate": true}}, "additional_button": {"label": {"string_id": "fx-view-discoverability-secondary-button-label"}, "style": "secondary", "action": {"dismiss": true}}, "page_event_listeners": [{"action": {"dismiss": true}, "params": {"type": "click", "selectors": "#fxa-toolbar-menu-button"}}]}}], "backdrop": "transparent", "template": "multistage", "transitions": false}, "trigger": {"id": "newSavedLogin"}, "priority": 1, "template": "feature_callout", "frequency": {"custom": [{"cap": 1, "period": **********}], "lifetime": 2}, "targeting": "(currentDate|date - profileAgeCreated|date) / ******** >= 7 && !isFxASignedIn && type == 'save' && !isMajorUpgrade && !willShowDefaultPrompt && !activeNotifications && previousSessionEnd && 'browser.newtabpage.activity-stream.asrouter.userprefs.cfr.features' | preferenceValue == true"}, "enabled": true, "featureId": "fxms-message-6"}], "firefoxLabsTitle": null}, "active": true, "source": "rs-loader", "userFacingName": "Account Adoption Callout Passwords - Global Rollout", "userFacingDescription": "Rollout:  Account Adoption Callout for Password Users", "lastSeen": "2025-09-05T10:56:54.521Z", "featureIds": ["fxms-message-6"], "isRollout": true, "prefs": [], "isFirefoxLabsOptIn": false, "firefoxLabsTitle": null, "firefoxLabsDescription": null, "firefoxLabsDescriptionLinks": null, "firefoxLabsGroup": null, "requiresRestart": false}, "enable-nimbus-sql-datastore": {"slug": "enable-nimbus-sql-datastore", "branch": {"slug": "control", "ratio": 1, "feature": {"value": {}, "enabled": false, "featureId": "this-is-included-for-desktop-pre-95-support"}, "features": [{"value": {"dbEnabled": true}, "enabled": true, "featureId": "nimbusStore"}], "firefoxLabsTitle": null}, "active": true, "source": "rs-loader", "userFacingName": "Enable Nimbus SQL Datastore", "userFacingDescription": "Switch to using the shared SQL datastore for Nimbus data store.", "lastSeen": "2025-09-05T10:56:54.523Z", "featureIds": ["nimbusStore"], "isRollout": true, "prefs": [{"name": "nimbus.profilesdatastoreservice.read.enabled", "branch": "user", "featureId": "nimbusStore", "variable": "dbEnabled", "originalValue": null}], "isFirefoxLabsOptIn": false, "firefoxLabsTitle": null, "firefoxLabsDescription": null, "firefoxLabsDescriptionLinks": null, "firefoxLabsGroup": null, "requiresRestart": false}, "account-adoption-app-menu-rollout": {"slug": "account-adoption-app-menu-rollout", "branch": {"slug": "treatment-a", "ratio": 1, "feature": {"value": {}, "enabled": false, "featureId": "this-is-included-for-desktop-pre-95-support"}, "features": [{"value": {"ctaCopyVariant": "sync-devices"}, "enabled": true, "featureId": "fxaAppMenuItem"}, {"value": {"id": "DEVICE_MIGRATION_FXA_CTA_EXP1", "groups": [], "content": {"layout": "row", "imageURL": "chrome://browser/content/asrouter/assets/fox-with-devices.svg", "imageWidth": 100, "closeAction": {"data": {"id": "DEVICE_MIGRATION_FXA_CTA_EXP1"}, "type": "BLOCK_MESSAGE"}, "messageType": "fxa_cta", "primaryText": {"string_id": "fxa-menu-message-sync-devices-primary-text"}, "primaryAction": {"data": {"where": "tab", "autoClose": false, "extraParams": {"utm_medium": "product", "utm_source": "firefox-desktop", "utm_content": "exp1-sync-devices", "utm_campaign": "backup-pxi-exp"}}, "type": "FXA_SIGNIN_FLOW"}, "secondaryText": {"string_id": "fxa-menu-message-sync-devices-secondary-text"}, "primaryActionText": {"string_id": "fxa-menu-message-sign-up-button"}, "imageVerticalBottomOffset": -16, "containerVerticalBottomOffset": 8}, "trigger": {"id": "menuOpened"}, "template": "menu_message", "targeting": "source == 'pxi_menu'"}, "enabled": true, "featureId": "fxms-message-5"}], "firefoxLabsTitle": null}, "active": true, "source": "rs-loader", "userFacingName": "Account Adoption PXI Menu - Rollout", "userFacingDescription": "The rollout adds different language we could use to communicate about Mozilla Accounts, and Sync to the users within the Accounts Menu on Firefox Desktop.", "lastSeen": "2025-09-05T10:56:54.539Z", "featureIds": ["fxaAppMenuItem", "fxms-message-5"], "isRollout": true, "prefs": [], "isFirefoxLabsOptIn": false, "firefoxLabsTitle": "", "firefoxLabsDescription": "", "firefoxLabsDescriptionLinks": null, "firefoxLabsGroup": "", "requiresRestart": false}, "context-id-rotation-every-30-days": {"slug": "context-id-rotation-every-30-days", "branch": {"slug": "control", "ratio": 1, "feature": {"value": {}, "enabled": false, "featureId": "this-is-included-for-desktop-pre-95-support"}, "features": [{"value": {"rotationPeriodInDays": 30}, "enabled": true, "featureId": "contextID"}], "firefoxLabsTitle": null}, "active": true, "source": "rs-loader", "userFacingName": "Context ID rotation every 30 days", "userFacingDescription": "To help further reduce long-lived unique identifiers in Firefox Desktop, we want to begin regenerating the context ID every 30 days.", "lastSeen": "2025-09-05T10:56:54.542Z", "featureIds": ["contextID"], "isRollout": true, "prefs": [{"name": "browser.contextual-services.contextId.rotation-in-days", "branch": "user", "featureId": "contextID", "variable": "rotationPeriodInDays", "originalValue": null}], "isFirefoxLabsOptIn": false, "firefoxLabsTitle": "", "firefoxLabsDescription": "", "firefoxLabsDescriptionLinks": null, "firefoxLabsGroup": "", "requiresRestart": false}}