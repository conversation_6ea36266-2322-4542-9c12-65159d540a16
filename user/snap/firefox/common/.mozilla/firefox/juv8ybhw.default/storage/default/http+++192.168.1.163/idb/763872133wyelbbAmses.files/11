self.Module = {
    onRuntimeInitialized: function () {
        onWasmLoaded();
    }
};

function Decoder() {
    this.arrayStorage = [];
    this.state = "normal";
    this.speed = 1;
    this.frameRate = 25;
    this.timerInter = null;
    this.timerData = null;
    this.playType = null;
    this.bareCallback = null;          //To call back the video function
    this.pbCallback = null;          //To call back the playBack function
    this.dlCallback = null;          //To call back the download function
    this.fishCallback   = null;          //To call back the fish function
    this.channel = 0;
    this.recordChannel = 0;             //The recording channel
    this.audioChannel = -1;            //The audio channel
    this.msgCallbackType = "";          //The msgCallbackType
    this.needClear = {};
}

function plugFlow(option) {
    var firstframeRateData = {};
    var time = option.time || (Math.round(1000 / self.decoder.frameRate)) * self.decoder.speed;
    if (!self.decoder.timerData) {
        self.decoder.timerData = setInterval(() => {
            var d_arrayStorage = self.decoder.arrayStorage;
            var indexAudio = 0;
            var objAudio = null;
            if (d_arrayStorage.length > 0) {
                /*----filter audio frame----*/
                if (!d_arrayStorage[indexAudio]) return;
                while (d_arrayStorage[indexAudio] && (d_arrayStorage[indexAudio].dType == 144 || d_arrayStorage[indexAudio].dType == 145 || d_arrayStorage[indexAudio].dType == 148 || d_arrayStorage[indexAudio].dType == 149 || d_arrayStorage[indexAudio].dType == 150)) {
                    if (self.decoder.audioChannel == d_arrayStorage[indexAudio].channel) {
                        objAudio = {
                            "type": "audio",
                            "channel": self.decoder.channel,
                            "size": d_arrayStorage[indexAudio].size,
                            "len": d_arrayStorage[indexAudio].len,
                            "key": d_arrayStorage[indexAudio].key,
                            "dType": d_arrayStorage[indexAudio].dType,
                            "data": d_arrayStorage[indexAudio].data,
                            "speed": self.decoder.speed
                        };
                        self.postMessage(objAudio, [ d_arrayStorage[indexAudio].data.buffer ]);
                    }
                    indexAudio++;
                }
                if (indexAudio > 0) d_arrayStorage.splice(0, indexAudio);
                /*----filter audio frame----*/
                if (d_arrayStorage.length) {
                    self.postMessage(d_arrayStorage[0], [ d_arrayStorage[0].data.buffer ]);
                    firstframeRateData = d_arrayStorage.shift();
                    if (firstframeRateData.frameRate && self.decoder.frameRate != firstframeRateData.frameRate) {
                        self.decoder.frameRate = firstframeRateData.frameRate;
                        clearInterval(self.decoder.timerData);
                        self.decoder.timerData = null;
                        plugFlow({});
                    }
                }
            } else {
                self.decoder.arrayStorage = [];
                // self.postMessage({"type":"pbOperation","operation":"close"});
            }
        }, time);
    }
}

Decoder.prototype.destroyDecode = function () {
    Module._OnDestroyDecode();
};
Decoder.prototype.startDecoding = function (d) {
    var ptr, i8Buffer, len;

    len = d.byteLength;
    if (!len) return;
    ptr = Module._malloc(len);
    if (ptr === null)
        return;
    i8Buffer = new Uint8Array(d);
    Module.writeArrayToMemory(i8Buffer, ptr);
    Module._OnMessage(ptr, len);
    i8Buffer = null;
    Module._free(ptr);
    ptr = null;
};
Decoder.prototype.msgDecode = function (d) {
    if (d.t == "download") {
        self.decoder.msgCallbackType = "downloadMsg";
        Module._OnInit(null, this.dlCallback, 3);
        Module._OnInit(this.msgCallBack, null, 4);
    } else if (d.t == "playBack") {
        self.decoder.msgCallbackType = "playBackMsg";
        Module._OnInit(null, this.pbCallback, 3);
        Module._OnInit(this.msgCallBack, null, 4);
        plugFlow({});
    } else {
        self.decoder.msgCallbackType = "";
        Module._OnInit(null, this.bareCallback, 3);
        Module._OnInit(this.fishCallback, null, 6);
    }
};
Decoder.prototype.onWasmLoaded = function () {
    this.bareCallback = Module.addFunction(function (buff, size, channel, flag, key, type, par0, par1, param) {
        var outArray, data, objRecord, objData, objAudio, objParam = {};
        if (self.decoder.needClear["ch_" + self.decoder.channel]) {
            Module._OnNeedKey(self.decoder.channel, 1);
            return;
        }

        outArray = Module.HEAPU8.subarray(buff, buff + size);
        data = new Uint8Array(outArray);
        if (key === 4) {
            if (param) {
                objParam = Module.UTF8ToString(param);
                objParam = objParam ? JSON.parse(objParam) : {};
                objData = {
                    "type": "aiData",
                    "channel": self.decoder.channel,
                    "size": size,
                    "len": data.byteLength,
                    "key": key,
                    "dType": type,
                    "param": objParam,
                    "frameRate": flag,
                    "data": data
                };
                self.postMessage(objData, [ data.buffer ]);
            }
            return;
        }
        if (self.decoder.recordChannel == self.decoder.channel) {
            objRecord = {
                "type": "record",
                "channel": self.decoder.channel,
                "symbol": "realPlay",
                "size": size,
                "len": data.byteLength,
                "key": key,
                "dType": type,
                "data": data,
                "frameRate": flag
            };
            self.postMessage(objRecord);
        }
        if (type === 144 || type === 145 || type === 148 || type === 149 || type === 150) {
            if(self.decoder.audioChannel === self.decoder.channel){
                objAudio = {
                    "type": "audio",
                    "channel": self.decoder.channel,
                    "size": size,
                    "len": data.byteLength,
                    "key": key,
                    "dType": type,
                    "data": data
                };
                self.postMessage(objAudio, [ data.buffer ]);
            }
            return;
        }
        if (param) {
            objParam = Module.UTF8ToString(param);
            objParam = JSON.parse(objParam);
        }
        objData = {
            "type": "vmsg",
            "channel": self.decoder.channel,
            "size": size,
            "len": data.byteLength,
            "key": key,
            "dType": type,
            "param": objParam,
            "frameRate": flag,
            "data": data
        };
        self.postMessage(objData, [ data.buffer ]);
    });
    this.dlCallback = Module.addFunction(function (buff, size, channel, flag, key, type) {
        var outArray = Module.HEAPU8.subarray(buff, buff + size);
        var data = new Uint8Array(outArray);
        var objRecord = {
            "type": "record",
            "channel": self.decoder.channel,
            "symbol": "download",
            "size": size,
            "len": data.byteLength,
            "key": key,
            "dType": type,
            "data": data
        };
        self.postMessage(objRecord, [ data.buffer ]);
    });
    this.msgCallBack = Module.addFunction(function (time, code, isFileEnd, isPackEnd) {
        self.postMessage({
            type: self.decoder.msgCallbackType,
            time: parseInt(Module.UTF8ToString(time)),
            code: code,
            isFileEnd: isFileEnd,
            isPackEnd: isPackEnd,
            currentLen: self.decoder.arrayStorage.length
        });
    });
    this.pbCallback = Module.addFunction(function (buff, size, timer, flag, key, type, width, height, param) {
        if (self.decoder.needClear["ch_" + self.decoder.channel] || self.decoder.playType === "close") {
            Module._OnNeedKey(self.decoder.channel, 1);
            self.decoder.arrayStorage = [];
            return;
        }
        var outArray, data, objData, objParam = {};
        outArray = Module.HEAPU8.subarray(buff, buff + size);
        data = new Uint8Array(outArray);
        if (param) {
            objParam = Module.UTF8ToString(param);
            objParam = JSON.parse(objParam);
        }
        objData = {
            "type": "vmsg",
            "channel": self.decoder.channel,
            "size": size,
            "len": data.byteLength,
            "key": key,
            "dType": type,
            "data": data,
            "frameRate": flag,
            "param": objParam,
            "jpegTime": timer,
        };
        self.decoder.arrayStorage.push(objData);
    });
    this.fishCallback = Module.addFunction(function(params, PtCount, arrayInt){
        let str = Module.UTF8ToString(params);
        if(str === "start"){
            self.decoder.ObjectFish = {
                postArray:[],
                ptCount:PtCount
            }
        }else if(str === "end"){
            console.log("self.decoder.ObjectFish--",self.decoder.ObjectFish);
            self.postMessage({type:"fishArray",data:self.decoder.ObjectFish});
        }else {
            self.decoder.ObjectFish.postArray.push(arrayInt)
        }
    });
};

self.decoder = new Decoder;

self.onmessage = function (evt) {
    evt = evt.data;
    if (evt === undefined) {
        return;
    }
    if (evt.type == "vdata") {
        self.decoder.startDecoding(evt.data);
    } else if (evt.type == "frame") {
        self.postMessage({ "type": "frame", "channel": evt.channel });
    } else if (evt.type == "destroy") {
        self.decoder.destroyDecode();
    } else if (evt.type == "msgdata") {
        self.decoder.msgDecode(evt);
        self.decoder.channel = evt.channel;
        if (evt.t == "playBack") self.postMessage({
            "type": "pbOperation",
            "operation": "play",
            "speed": self.decoder.speed
        });
    } else if (evt.type == "recordType") {
        if (evt.types == "open") {
            if (evt.channel >= 1000)
                Module._OnInitDecode(3, evt.channel, 0);
            self.decoder.recordChannel = evt.channel;
        } else {
            self.decoder.recordChannel = 0;
        }
    } else if (evt.type == "audio") {
        if (evt.types == "open") {
            self.decoder.audioChannel = evt.channel;
        } else {
            self.decoder.audioChannel = -1;
        }
    } else if (evt.type == "message") {
        Module._OnInitDecode(3, evt.channel, evt.index);
    } else if (evt.type == "interView") {
        if (self.decoder.timerInter) {
            clearInterval(self.decoder.timerInter);
            self.decoder.timerInter = null;
        } else {
            self.decoder.timerInter = setInterval(() => {
                self.postMessage({ "type": "interView", "currentLen": self.decoder.arrayStorage.length });
            }, 2000);
        }
    } else if (evt.type === "pbOperation") {
        if (evt.operation === "suspend") {
            if (self.decoder.timerData) clearInterval(self.decoder.timerData);
            self.decoder.timerData = null;
        } else if (evt.operation === "play") {
            evt.speed = self.decoder.speed;
            self.decoder.playType = "play";
            plugFlow(evt);
        } else if (evt.operation === "speed") {
            if (self.decoder.timerData) clearInterval(self.decoder.timerData);
            self.decoder.timerData = null;
            self.decoder.speed = evt.value;
            plugFlow(evt);
        } else if(evt.operation === "rate") {
            if (self.decoder.timerData) clearInterval(self.decoder.timerData);
            self.decoder.timerData = null;
            self.decoder.frameRate = evt.value;
            plugFlow(evt);
        } else if (evt.operation === "close" || evt.operation === "flowSwitch") {
            if (self.decoder.timerInter) clearInterval(self.decoder.timerInter);
            if (self.decoder.timerData) clearInterval(self.decoder.timerData);
            self.decoder.timerData = null;
            self.decoder.timerData = null;
            self.decoder.playType = "close";
            self.decoder.speed = 1;
            self.decoder.state = "normal";
            self.decoder.arrayStorage = [];
            self.decoder.needClear["ch_" + evt.channel] = evt.value;
            if (self.decoder.needClear["ch_" + evt.channel]) {
                Module._OnNeedKey(evt.channel, 1);
            }
        }
        self.postMessage(evt);
    } else if (evt.type == "instance") {
        self.Module.wasmBinary = evt.fBuffer;//Load WASM file ArrayBuffer data
        eval.call(self, evt.scriptCode);//load message.js
    } else if (evt.type == "needClear") {
        self.decoder.needClear["ch_" + evt.channel] = evt.value;
        if (self.decoder.needClear["ch_" + evt.channel]) {
            Module._OnNeedKey(evt.channel, 1);
        }
    } else if (evt.type == "clearData") {
        self.decoder.arrayStorage = [];
    }
    else if(evt.type === "fishArray"){
        console.warn("evt111111111111",evt);
        if(evt.data.value === 4 || evt.data.value === 5 || evt.data.value === 6){
            Module._OnFisheyeCreateVirtualPTZModel();
        }else {
            Module._OnFisheyeCreatePanoramicModel(evt.data.value);
        }

    }
};

function onWasmLoaded() {
    if (self.decoder) {
        self.decoder.onWasmLoaded();
        self.postMessage("yes");
    } else {
        console.log("[ER] No decoder!");
    }
}

