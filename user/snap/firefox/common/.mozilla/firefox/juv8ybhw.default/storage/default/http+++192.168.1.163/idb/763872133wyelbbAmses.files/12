self.Module = {
    onRuntimeInitialized: function () {
        onWasmLoaded();
    }
};

function Decoder() {
    this.videoCallback = null;
    this.audioCallback = null;
    this.channel = 0;
    this.speed = 1;
    this.recordName = null;            //To mark the record video file name
    this.fName = null;            //To mark the file name
    this.needClear = false;
    this.index = 0;               //To storage the index of file name
    this.maxFileSize = 67108864;        //The max size of download file
    this.nowSize = 0;               //The size of downloading file
    this.dType = 0;               //The decode type
    this.symbol = "";              //To mark is realPlay download or download
    this.needKey = false;           //To need key frame
    this.param = "";
    this.frameRate = 3600;
    this.mseFlag = 1;
    this.audioType = 0;
    this.audioTypeLive = 0;
    let userAgent = navigator.userAgent;
}

Decoder.prototype.recordVideo = function (d) {
    var ptr, i8Buffer, content, rName, fName, flag = false, isFlag = -1;

    ptr = Module._malloc(d.len);
    if (ptr === null)
        return;
    i8Buffer = d.data;
    if (d.dType == 128 || d.dType == 129) {
        if (!this.dType) {
            this.dType = d.dType;
        } else if (this.dType != d.dType) {
            flag = true;
            this.dType = d.dType;
            this.needKey = true;
        }
    }

    if (d.dType == 130 || d.dType == 131 || d.dType == 148) {
    //if (d.dType == 148) {
        self.postMessage({"type": "download", "symbol": this.symbol, channel: self.decoder.channel});
        return;
    }

    Module.writeArrayToMemory(i8Buffer, ptr);
    if (((this.nowSize > this.maxFileSize) && d.key) || flag) {
        Module._OnCloseRecord(111);
        rName = self.decoder.fName + "_" + (self.decoder.index + 1) + ".mp4";
        Module.ccall('OnRecordFile', 'null', ['string', 'number'], [rName, rName.length]);
        fName = self.decoder.fName + "_" + self.decoder.index + ".mp4";
        content = FS.readFile(fName);
        self.postMessage({
            "type": "recordData",
            "data": content,
            "name": (self.decoder.recordName + "_" + self.decoder.index + ".mp4"),
            "action": 0
        }, [content.buffer]);
        content = null;
        Module.ccall('OnDeleteFile', 'int', ['string'], [fName]);
        self.decoder.index++;
        this.nowSize = 0;
    }
    if (this.needKey) {
        if (!d.key) {
            i8Buffer = null;
            Module._free(ptr);
            self.postMessage({"type": "download", "symbol": this.symbol, channel: self.decoder.channel, flag: 1});
            return;
        } else {
            this.needKey = false;
        }
    }
    if (d.dType == 128 || d.dType == 129)
        this.nowSize += d.size;
    try {
        isFlag = Module._OnRecord(ptr, d.size, d.channel, d.key);
    } catch (err) {
        isFlag = -1;
    }
    i8Buffer = null;
    Module._free(ptr);
    self.postMessage({"type": "download", "symbol": this.symbol, channel: self.decoder.channel, flag: isFlag});
};
Decoder.prototype.destroyDecode = function () {
    Module._OnDestroyDecode();
};
Decoder.prototype.startDecode = function (d) {
    var ptr, i8Buffer;

    ptr = Module._malloc(d.len);
    if (ptr === null)
        return;
    i8Buffer = d.data;
    Module.writeArrayToMemory(i8Buffer, ptr);
    try {
        Module._OnDecodeFrame(ptr, d.size, d.channel, d.key, this.frameRate, this.mseFlag);
    } catch (err) {
    }
    i8Buffer = null;
    Module._free(ptr);
    self.postMessage({"type": "frame", "channel": this.channel});
};
Decoder.prototype.decodeAudio = function (d) {
    var ptr, i8Buffer;

    ptr = Module._malloc(d.len);
    if (ptr === null) return;

    i8Buffer = d.data;
    Module.writeArrayToMemory(i8Buffer, ptr);
    try {
        Module._OnDecodeAudio(ptr, d.size, d.channel, 0);
    } catch (err) {
    }
    i8Buffer = null;
    Module._free(ptr);
};
Decoder.prototype.decode = function (d) {
    Module._OnInit(this.videoCallback, null, 1);
    Module._OnInitDecode(d.types, d.channel, d.index);
    this.channel = d.channel;
};
Decoder.prototype.clearFMP4 = function (e) {
    Module._OnDestroyFmp4();
};
Decoder.prototype.audioInit = function (d) {
    Module._OnInit(null, this.audioCallback, 2);
    Module._OnInitDecode(d.types, d.channel, d.index);
    this.channel = d.channel;
};
Decoder.prototype.onWasmLoaded = function () {
    this.videoCallback = Module.addFunction(function (buff, size, width, height, channel, index, time, type, rate) {
        let outArray = Module.HEAPU8.subarray(buff, buff + size);
        let data = new Uint8Array(outArray);
        let objData;
        if (type >= 1000) {
            objData = {
                "type": "fmp4",
                "channel": channel,
                "head": type - 1000,
                "rate": rate,
                "key": index,
                "width": width,
                "height": height,
                "timer": time,
                "size": size,
                "param": self.decoder.param,
                "data": data
            };
        } else {
            objData = {
                "type": "yuv",
                "channel": channel,
                "width": width,
                "height": height,
                "index": index,
                "timer": time,
                "size": size,
                "param": self.decoder.param,
                "data": data
            };
        }

        if (self.decoder.needClear) return;
        self.postMessage(objData, [data.buffer]);
        outArray = null;
        data = null;
        objData = null;
    });
    this.audioCallback = Module.addFunction(function (buff, size, channel, cCount, sRate, sBit) {
        var outArray = Module.HEAPU8.subarray(buff, buff + size);
        var data = new Uint8Array(outArray);
        let lFlag = false;
        if (self.decoder.audioType != self.decoder.audioTypeLive) {
            self.decoder.audioTypeLive = self.decoder.audioType;
            lFlag = true;
        }
        var objData = {
            "type": "audio",
            "channel": channel,
            "cCount": cCount,
            "sRate": sRate,
            "sBit": sBit,
            "size": size,
            "data": data,
            "lFlag": lFlag,
            "speed": self.decoder.speed
        };
        if (self.decoder.needClear) return;
        postMessage(objData, [data.buffer]);
        outArray = null;
        data = null;
        objData = null;
    });
};

function closeRecord() {
    Module._OnCloseRecord(111);
    const dname = self.decoder.fName + "_" + self.decoder.index + ".mp4";
    try {
        let content = FS.readFile(dname);
        self.postMessage({
            "type": "recordData",
            "data": content,
            "name": (self.decoder.recordName + "_" + self.decoder.index + ".mp4"),
            "action": 1
        }, [content.buffer]);
        content = null;
        Module.ccall('OnDeleteFile', 'int', ['string'], [dname]);
    } catch (e) {
        console.log(e);
        self.postMessage({"type": "closeDecoder"});
    }
}

self.decoder = new Decoder;

self.onmessage = function (evt) {
    evt = evt.data;
    //console.log("evt.type = ",evt.type)
    if (evt.type === "yuv") {
        self.decoder.decode(evt);
    } else if (evt.type === "vmsg") {
        if (evt.frameRate) {
            if (evt.frameRate === 1 || !evt.param.mseFlag)
                self.decoder.mseFlag = 0;
            else
                self.decoder.mseFlag = 1;
            // let nRate = 0.8;
            // if (evt.frameRate > 17)
            //     nRate = 0.9;
            let userAgent = navigator.userAgent;
            if(userAgent.indexOf('Firefox') > -1)
            {
                self.decoder.frameRate = Math.floor(100000 / evt.frameRate);
                //console.log("userAgent=,frameRate=",userAgent,self.decoder.frameRate);
            }
            else
            {
                self.decoder.frameRate = Math.floor(90000 / evt.frameRate);
            }
            // self.decoder.frameRate = parseInt(self.decoder.frameRate * nRate);
            // self.decoder.frameRate = 3600;
        }
        self.decoder.param = evt.param;
        self.decoder.startDecode(evt);
    } else if (evt.type === "record") {
        self.decoder.recordVideo(evt);
    } else if (evt.type === "frame") {
        setTimeout(() => {
            self.postMessage(evt);
        }, 20);
    } else if (evt.type === "recordType") {            //The playback operate
        if (evt.types === "open") {
            self.decoder.recordName = evt.index;
            self.decoder.fName = evt.fname;
            self.decoder.nowSize = 0;
            self.decoder.index = 0;
            self.decoder.symbol = evt.symbol;
            self.decoder.channel = evt.channel;
            const name = self.decoder.fName + "_" + self.decoder.index + ".mp4";
            Module.ccall('OnRecordInit', 'null', ['string', 'number'], [name, name.length]);
            self.postMessage({"type": "download", "symbol": evt.symbol, channel: self.decoder.channel, flag: -1});
        } else {
            closeRecord();
            self.postMessage({"type": "download", "symbol": evt.symbol, channel: self.decoder.channel, flag: -1});
            self.decoder.channel = 0;
        }
    } else if (evt.type === "audio") {
        self.decoder.audioType = evt.dType;
        if (evt.speed && evt.speed != self.decoder.speed) self.decoder.speed = evt.speed;
        self.decoder.decodeAudio(evt);
    } else if (evt.type === "initAudio") {
        self.decoder.audioInit(evt);
    } else if (evt.type === "msg") {
        self.decoder.startDecode(evt);
    } else if (evt.type === "destroy") {
        self.decoder.destroyDecode();
    } else if (evt.type === "instance") {
        self.Module.wasmBinary = evt.fBuffer;//Load WASM file ArrayBuffer data
        eval.call(self, evt.scriptCode);//load index.js
    } else if (evt.type === "needClear") {
        self.decoder.needClear = evt.value;
        self.decoder.clearFMP4();
    } else if (evt.type === "name") {
        self.decoder.recordName = evt.name;
    } else if (evt.type === "download") {
        if (self.decoder.channel != 0) {
            self.postMessage({"type": "download", "symbol": evt.symbol, channel: self.decoder.channel, flag: -1});
        }
    } else if (evt.type === "pbOperation") {
        if (evt.operation === "close" || evt.operation === "flowSwitch") {
            self.decoder.needClear = true;
        }
    }
};

function onWasmLoaded() {
    if (self.decoder) {
        self.decoder.onWasmLoaded();
        self.postMessage("yes");
    } else {
        console.log("[ER] No decoder!");
    }
}

