{"type": "main", "id": "1fcdb01f-55bb-4478-9867-89a48bd8a53a", "creationDate": "2025-09-06T08:00:18.494Z", "version": 4, "application": {"architecture": "x86-64", "buildId": "20250828192042", "name": "Firefox", "version": "142.0.1", "displayVersion": "142.0.1", "vendor": "Mozilla", "platformVersion": "142.0.1", "xpcomAbi": "x86_64-gcc3", "channel": "release"}, "payload": {"ver": 4, "simpleMeasurements": {"totalTime": 75806, "start": 260, "main": 340, "selectProfile": 438, "afterProfileLocked": 445, "startupCrashDetectionBegin": 635, "startupCrashDetectionEnd": 32773, "firstPaint": 1636, "firstPaint2": 1636, "sessionRestoreInit": 1061, "sessionRestored": 1838, "createTopLevelWindow": 1096, "firstLoadURI": 1750, "AMI_startup_begin": 648, "XPI_startup_begin": 660, "XPI_bootstrap_addons_begin": 893, "XPI_bootstrap_addons_end": 898, "XPI_startup_end": 898, "AMI_startup_end": 899, "XPI_finalUIStartup": 1060, "sessionRestoreInitialized": 1079, "delayedStartupStarted": 1660, "delayedStartupFinished": 1698, "sessionRestoreRestoring": 1699, "debuggerAttached": 0, "activeTicks": 0}, "processes": {"parent": {"scalars": {"blocklist.mlbf_softblocks_source": "dump_match", "browser.engagement.max_concurrent_tab_count": 1, "browser.engagement.session_time_including_suspend": 75806039, "browser.engagement.max_concurrent_window_count": 1, "browser.engagement.session_time_excluding_suspend": 75806039, "blocklist.mlbf_source": "dump_match"}, "keyedScalars": {"networking.data_transferred_v3_kb": {"Y0_N1Sys": 781}, "networking.speculative_connect_outcome": {"aborted_socket_limit": 5, "successful": 183}}}, "content": {"histograms": {"MEMORY_RESIDENT_FAST": {"bucket_count": 100, "histogram_type": 0, "sum": 178198560, "range": [32768, 16777216], "values": {"103055": 0, "109828": 372, "132939": 732, "141677": 229, "150989": 0}}, "MEMORY_RESIDENT_PEAK": {"bucket_count": 100, "histogram_type": 0, "sum": 180703992, "range": [32768, 16777216], "values": {"109828": 0, "117047": 372, "132939": 219, "141677": 742, "150989": 0}}, "MEMORY_UNIQUE": {"bucket_count": 100, "histogram_type": 0, "sum": 57081908, "range": [32768, 16777216], "values": {"0": 372, "48009": 961, "51164": 0}}, "MEMORY_JS_GC_HEAP": {"bucket_count": 200, "histogram_type": 0, "sum": 6444032, "range": [1024, 16777216], "values": {"3847": 0, "4040": 372, "4915": 961, "5162": 0}}, "GHOST_WINDOWS": {"bucket_count": 32, "histogram_type": 0, "sum": 0, "range": [1, 128], "values": {"0": 1333, "1": 0}}}, "keyedHistograms": {}, "scalars": {}, "keyedScalars": {}}, "extension": {"histograms": {"MEMORY_RESIDENT_FAST": {"bucket_count": 100, "histogram_type": 0, "sum": 99200, "range": [32768, 16777216], "values": {"90735": 0, "96699": 1, "103055": 0}}, "MEMORY_RESIDENT_PEAK": {"bucket_count": 100, "histogram_type": 0, "sum": 102776, "range": [32768, 16777216], "values": {"90735": 0, "96699": 1, "103055": 0}}, "MEMORY_UNIQUE": {"bucket_count": 100, "histogram_type": 0, "sum": 24880, "range": [32768, 16777216], "values": {"0": 1, "32768": 0}}, "MEMORY_JS_GC_HEAP": {"bucket_count": 200, "histogram_type": 0, "sum": 7168, "range": [1024, 16777216], "values": {"6594": 0, "6925": 1, "7273": 0}}, "GHOST_WINDOWS": {"bucket_count": 32, "histogram_type": 0, "sum": 0, "range": [1, 128], "values": {"0": 1, "1": 0}}}, "keyedHistograms": {}, "scalars": {}, "keyedScalars": {}}, "dynamic": {"scalars": {}, "keyedScalars": {}}, "gpu": {"histograms": {}, "keyedHistograms": {}, "scalars": {}, "keyedScalars": {}}, "socket": {"histograms": {}, "keyedHistograms": {}, "scalars": {}, "keyedScalars": {}}, "utility": {"histograms": {}, "keyedHistograms": {}, "scalars": {}, "keyedScalars": {}}}, "histograms": {"CYCLE_COLLECTOR_MAX_PAUSE": {"bucket_count": 50, "histogram_type": 0, "sum": 255, "range": [1, 10000], "values": {"1": 0, "2": 9, "3": 4, "4": 8, "5": 18, "6": 11, "10": 2, "14": 1, "17": 0}}, "GC_IN_PROGRESS_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 540, "range": [1, 10000], "values": {"34": 0, "40": 4, "57": 6, "68": 0}}, "MEMORY_RESIDENT_FAST": {"bucket_count": 100, "histogram_type": 0, "sum": 125574156, "range": [32768, 16777216], "values": {"474861": 0, "506072": 188, "539334": 49, "574782": 0}}, "MEMORY_RESIDENT_PEAK": {"bucket_count": 100, "histogram_type": 0, "sum": 138052500, "range": [32768, 16777216], "values": {"539334": 0, "574782": 237, "612560": 0}}, "MEMORY_TOTAL": {"bucket_count": 100, "histogram_type": 0, "sum": 154309084, "range": [32768, 16777216], "values": {"574782": 0, "612560": 158, "652821": 79, "695728": 0}}, "MEMORY_UNIQUE": {"bucket_count": 100, "histogram_type": 0, "sum": 87601960, "range": [32768, 16777216], "values": {"324110": 0, "345412": 110, "368115": 122, "392310": 5, "418095": 0}}, "MEMORY_JS_GC_HEAP": {"bucket_count": 200, "histogram_type": 0, "sum": 12497920, "range": [1024, 16777216], "values": {"46831": 0, "49183": 51, "51654": 140, "54249": 45, "56974": 1, "59836": 0}}, "GHOST_WINDOWS": {"bucket_count": 32, "histogram_type": 0, "sum": 0, "range": [1, 128], "values": {"0": 237, "1": 0}}, "HTTP_TRANSACTION_IS_SSL": {"bucket_count": 3, "histogram_type": 2, "sum": 74, "range": [1, 2], "values": {"0": 0, "1": 74, "2": 0}}, "TLS_EARLY_DATA_NEGOTIATED": {"bucket_count": 4, "histogram_type": 1, "sum": 0, "range": [1, 3], "values": {"0": 72, "1": 0}}, "SSL_HANDSHAKE_VERSION": {"bucket_count": 17, "histogram_type": 1, "sum": 432, "range": [1, 16], "values": {"2": 0, "3": 48, "4": 72, "5": 0}}, "SSL_HANDSHAKE_PRIVACY": {"bucket_count": 17, "histogram_type": 1, "sum": 312, "range": [1, 16], "values": {"1": 0, "2": 48, "3": 72, "4": 0}}, "SSL_HANDSHAKE_RESULT": {"bucket_count": 673, "histogram_type": 1, "sum": 0, "range": [1, 672], "values": {"0": 120, "1": 0}}, "SSL_HANDSHAKE_RESULT_FIRST_TRY": {"bucket_count": 673, "histogram_type": 1, "sum": 0, "range": [1, 672], "values": {"0": 86, "1": 0}}, "SSL_HANDSHAKE_RESULT_CONSERVATIVE": {"bucket_count": 673, "histogram_type": 1, "sum": 0, "range": [1, 672], "values": {"0": 48, "1": 0}}, "SSL_HANDSHAKE_RESULT_ECH_GREASE": {"bucket_count": 673, "histogram_type": 1, "sum": 0, "range": [1, 672], "values": {"0": 72, "1": 0}}, "SSL_TIME_UNTIL_READY": {"bucket_count": 200, "histogram_type": 0, "sum": 42654, "range": [1, 60000], "values": {"96": 0, "100": 2, "105": 1, "227": 20, "237": 10, "324": 62, "339": 10, "355": 2, "445": 2, "465": 1, "555": 1, "581": 4, "912": 1, "954": 2, "1368": 1, "1875": 1, "1961": 0}}, "SSL_TIME_UNTIL_READY_FIRST_TRY": {"bucket_count": 200, "histogram_type": 0, "sum": 31302, "range": [1, 60000], "values": {"96": 0, "100": 2, "105": 1, "227": 20, "237": 10, "324": 32, "339": 7, "355": 1, "445": 2, "465": 1, "555": 1, "581": 4, "912": 1, "954": 2, "1368": 1, "1875": 1, "1961": 0}}, "SSL_TIME_UNTIL_READY_CONSERVATIVE": {"bucket_count": 200, "histogram_type": 0, "sum": 18595, "range": [1, 60000], "values": {"96": 0, "100": 2, "105": 1, "227": 20, "237": 10, "339": 1, "355": 1, "445": 2, "465": 1, "555": 1, "581": 4, "912": 1, "954": 2, "1368": 1, "1875": 1, "1961": 0}}, "SSL_TIME_UNTIL_READY_ECH_GREASE": {"bucket_count": 200, "histogram_type": 0, "sum": 24059, "range": [1, 60000], "values": {"310": 0, "324": 62, "339": 9, "355": 1, "371": 0}}, "SSL_BYTES_BEFORE_CERT_CALLBACK": {"bucket_count": 64, "histogram_type": 0, "sum": 85343, "range": [1, 32000], "values": {"2449": 0, "2849": 27, "3314": 1, "3855": 0}}, "SSL_RESUMED_SESSION": {"bucket_count": 3, "histogram_type": 2, "sum": 92, "range": [1, 2], "values": {"0": 28, "1": 92, "2": 0}}, "SSL_KEY_EXCHANGE_ALGORITHM_FULL": {"bucket_count": 17, "histogram_type": 1, "sum": 112, "range": [1, 16], "values": {"3": 0, "4": 28, "5": 0}}, "SSL_KEY_EXCHANGE_ALGORITHM_RESUMED": {"bucket_count": 17, "histogram_type": 1, "sum": 368, "range": [1, 16], "values": {"3": 0, "4": 92, "5": 0}}, "SPDY_KBREAD_PER_CONN2": {"bucket_count": 50, "histogram_type": 0, "sum": 394, "range": [1, 100000], "values": {"0": 78, "1": 5, "2": 2, "5": 2, "365": 1, "457": 0}}, "HTTP_CHANNEL_DISPOSITION": {"bucket_count": 17, "histogram_type": 1, "sum": 1464, "range": [1, 16], "values": {"2": 0, "3": 128, "9": 17, "10": 74, "11": 17, "12": 0}}, "HTTP_CHANNEL_ONSTART_SUCCESS": {"bucket_count": 3, "histogram_type": 2, "sum": 99, "range": [1, 2], "values": {"0": 145, "1": 99, "2": 0}}, "TRANSACTION_WAIT_TIME_HTTP2_SUP_HTTP3": {"bucket_count": 100, "histogram_type": 0, "sum": 0, "range": [1, 5000], "values": {"0": 1, "1": 0}}, "DNS_LOOKUP_METHOD2": {"bucket_count": 17, "histogram_type": 1, "sum": 3036, "range": [1, 16], "values": {"0": 0, "1": 114, "6": 487, "7": 0}}, "DNS_LOOKUP_TIME": {"bucket_count": 50, "histogram_type": 0, "sum": 43, "range": [1, 60000], "values": {"6": 0, "7": 1, "11": 1, "21": 1, "26": 0}}, "DNS_NATIVE_LOOKUP_TIME": {"bucket_count": 50, "histogram_type": 0, "sum": 1336, "range": [1, 60000], "values": {"0": 1, "1": 4, "2": 3, "7": 13, "9": 67, "11": 43, "14": 1, "21": 1, "26": 2, "32": 0}}, "DNS_BY_TYPE_FAILED_LOOKUP_TIME": {"bucket_count": 50, "histogram_type": 0, "sum": 0, "range": [1, 60000], "values": {"0": 16, "1": 0}}, "DNS_NATIVE_QUEUING": {"bucket_count": 50, "histogram_type": 0, "sum": 0, "range": [1, 60000], "values": {"0": 503, "1": 0}}, "DNS_FAILED_LOOKUP_TIME": {"bucket_count": 50, "histogram_type": 0, "sum": 153, "range": [1, 60000], "values": {"0": 234, "1": 91, "2": 19, "3": 8, "4": 0}}, "DNS_BLACKLIST_COUNT": {"bucket_count": 20, "histogram_type": 1, "sum": 0, "range": [1, 21], "values": {"0": 114, "1": 0}}, "MS_MESSAGE_REQUEST_TIME_MS": {"bucket_count": 20, "histogram_type": 0, "sum": 5, "range": [1, 2000], "values": {"0": 187, "1": 5, "2": 0}}, "CONTENT_SIGNATURE_VERIFICATION_STATUS": {"bucket_count": 21, "histogram_type": 1, "sum": 0, "range": [1, 20], "values": {"0": 5, "1": 0}}, "NETWORK_CACHE_V2_MISS_TIME_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 90, "range": [1, 10000], "values": {"0": 2, "1": 4, "2": 5, "3": 1, "4": 7, "5": 4, "6": 3, "7": 1, "8": 0}}, "NETWORK_CACHE_V2_HIT_TIME_MS": {"bucket_count": 50, "histogram_type": 0, "sum": 0, "range": [1, 10000], "values": {"0": 4, "1": 0}}, "TLS_CIPHER_SUITE": {"bucket_count": 65, "histogram_type": 1, "sum": 1968, "range": [1, 64], "values": {"13": 0, "14": 48, "18": 72, "19": 0}}, "SSL_KEA_ECDHE_CURVE_FULL": {"bucket_count": 37, "histogram_type": 1, "sum": 812, "range": [1, 36], "values": {"28": 0, "29": 28, "30": 0}}, "SSL_AUTH_ALGORITHM_FULL": {"bucket_count": 17, "histogram_type": 1, "sum": 196, "range": [1, 16], "values": {"6": 0, "7": 28, "8": 0}}, "SSL_AUTH_RSA_KEY_SIZE_FULL": {"bucket_count": 25, "histogram_type": 1, "sum": 336, "range": [1, 24], "values": {"11": 0, "12": 28, "13": 0}}, "SSL_REASONS_FOR_NOT_FALSE_STARTING": {"bucket_count": 513, "histogram_type": 1, "sum": 0, "range": [1, 512], "values": {"0": 26, "1": 0}}, "SSL_HANDSHAKE_TYPE": {"bucket_count": 9, "histogram_type": 1, "sum": 152, "range": [1, 8], "values": {"0": 0, "1": 92, "2": 26, "4": 2, "5": 0}}, "SSL_CERT_ERROR_OVERRIDES": {"bucket_count": 25, "histogram_type": 1, "sum": 30, "range": [1, 24], "values": {"0": 0, "1": 30, "2": 0}}, "SSL_SCTS_ORIGIN": {"bucket_count": 11, "histogram_type": 1, "sum": 61, "range": [1, 10], "values": {"0": 0, "1": 61, "2": 0}}, "SSL_SCTS_PER_CONNECTION": {"bucket_count": 11, "histogram_type": 1, "sum": 61, "range": [1, 10], "values": {"1": 0, "2": 29, "3": 1, "4": 0}}, "CERT_EV_STATUS": {"bucket_count": 11, "histogram_type": 1, "sum": 30, "range": [1, 10], "values": {"0": 0, "1": 30, "2": 0}}, "CERT_PINNING_MOZ_RESULTS_BY_HOST": {"bucket_count": 513, "histogram_type": 1, "sum": 457, "range": [1, 512], "values": {"12": 0, "13": 25, "33": 4, "34": 0}}, "CERT_PINNING_MOZ_TEST_RESULTS_BY_HOST": {"bucket_count": 513, "histogram_type": 1, "sum": 17, "range": [1, 512], "values": {"16": 0, "17": 1, "18": 0}}, "CERT_CHAIN_KEY_SIZE_STATUS": {"bucket_count": 5, "histogram_type": 1, "sum": 30, "range": [1, 4], "values": {"0": 0, "1": 30, "2": 0}}}, "keyedHistograms": {"SSL_TIME_UNTIL_HANDSHAKE_FINISHED_KEYED_BY_KA": {"none": {"bucket_count": 200, "histogram_type": 0, "sum": 6917, "range": [1, 60000], "values": {"96": 0, "100": 1, "227": 12, "237": 2, "339": 1, "355": 1, "445": 1, "581": 4, "608": 0}}, "x25519": {"bucket_count": 200, "histogram_type": 0, "sum": 39268, "range": [1, 60000], "values": {"138": 0, "144": 1, "151": 1, "324": 62, "339": 18, "355": 7, "665": 2, "696": 1, "728": 1, "1044": 2, "1092": 1, "1431": 1, "2051": 1, "2145": 0}}}, "HTTP3_ECH_OUTCOME": {"GREASE": {"bucket_count": 33, "histogram_type": 1, "sum": 0, "range": [1, 32], "values": {"0": 2, "1": 0}}}, "HTTP_CHANNEL_DISPOSITION_UPGRADE": {"enabledNoReason": {"bucket_count": 51, "histogram_type": 5, "sum": 17, "range": [1, 50], "values": {"0": 0, "1": 17, "2": 0}}}, "HTTP3_CONNECTION_CLOSE_CODE_3": {"transport_closed": {"bucket_count": 101, "histogram_type": 1, "sum": 0, "range": [1, 100], "values": {"0": 2, "1": 0}}}, "HTTP3_CHANNEL_ONSTART_SUCCESS": {"http3": {"bucket_count": 3, "histogram_type": 2, "sum": 1, "range": [1, 2], "values": {"0": 0, "1": 1, "2": 0}}, "no_http3": {"bucket_count": 3, "histogram_type": 2, "sum": 73, "range": [1, 2], "values": {"0": 145, "1": 73, "2": 0}}}, "DNS_LOOKUP_DISPOSITION3": {"mozilla.cloudflare-dns.com": {"bucket_count": 51, "histogram_type": 5, "sum": 3274, "range": [1, 50], "values": {"5": 0, "6": 135, "7": 352, "8": 0}}}, "HTTP_CONNECTION_CLOSE_REASON": {"11_1_0_0_0": {"bucket_count": 51, "histogram_type": 1, "sum": 90, "range": [1, 50], "values": {"4": 0, "5": 18, "6": 0}}, "20_1_0_7_4": {"bucket_count": 51, "histogram_type": 1, "sum": 95, "range": [1, 50], "values": {"18": 0, "19": 5, "20": 0}}, "30_1_0_0_4": {"bucket_count": 51, "histogram_type": 1, "sum": 2, "range": [1, 50], "values": {"1": 0, "2": 1, "3": 0}}, "30_1_0_6_4": {"bucket_count": 51, "histogram_type": 1, "sum": 2, "range": [1, 50], "values": {"1": 0, "2": 1, "3": 0}}, "11_0_0_0_0": {"bucket_count": 51, "histogram_type": 1, "sum": 1280, "range": [1, 50], "values": {"4": 0, "5": 256, "6": 0}}, "11_1_0_3_4": {"bucket_count": 51, "histogram_type": 1, "sum": 646, "range": [1, 50], "values": {"18": 0, "19": 34, "20": 0}}, "20_1_0_3_4": {"bucket_count": 51, "histogram_type": 1, "sum": 1577, "range": [1, 50], "values": {"18": 0, "19": 83, "20": 0}}}}, "info": {"reason": "aborted-session", "revision": "https://hg.mozilla.org/releases/mozilla-release/rev/179e4da8d58fdece16dcbf164c80199917a5946e", "timezoneOffset": 480, "previousBuildId": "20250821175018", "sessionId": "451a6241-b9f7-488a-bf7f-a29199f106cf", "subsessionId": "efd1b38a-cfad-46ac-891a-60ad20ab416a", "previousSessionId": "34e597da-c1d6-4225-81c6-3ea3ef9763d0", "previousSubsessionId": "457fc103-43f8-4e12-aa6f-2bf559125753", "subsessionCounter": 2, "profileSubsessionCounter": 29, "sessionStartDate": "2025-09-05T18:00:00.0+08:00", "subsessionStartDate": "2025-09-06T00:00:00.0+08:00", "sessionLength": 75806, "subsessionLength": 57618, "addons": "langpack-zh-CN%40firefox.mozilla.org:142.0.20250827.4350,formautofill%40mozilla.org:1.0.1,newtab%40mozilla.org:142.0.0,pictureinpicture%40mozilla.org:1.0.0,addons-search-detection%40mozilla.com:3.0.0,webcompat%40mozilla.org:142.9.0,default-theme%40mozilla.org:1.4.2"}}, "clientId": "78458a56-b1ca-4d0e-bb84-e0e1ac9bc0f3", "profileGroupId": "78458a56-b1ca-4d0e-bb84-e0e1ac9bc0f3", "environment": {"build": {"applicationId": "{ec8030f7-c20a-464f-9b0e-13a3a9e97384}", "applicationName": "Firefox", "architecture": "x86-64", "buildId": "20250828192042", "version": "142.0.1", "vendor": "Mozilla", "displayVersion": "142.0.1", "platformVersion": "142.0.1", "xpcomAbi": "x86_64-gcc3", "updaterAvailable": false}, "partner": {"distributionId": "canonical-002", "distributionVersion": "1.0", "partnerId": "ubuntu", "distributor": "canonical", "distributorChannel": "", "partnerNames": ["ubuntu"]}, "system": {"memoryMB": 7674, "virtualMaxMB": null, "cpu": {"count": 12, "cores": 8, "pcount": 8, "mcount": 0, "ecount": 0, "vendor": "GenuineIntel", "name": "12th Gen Intel(R) Core(TM) i5-12450H", "family": 6, "model": 154, "stepping": 3, "l2cacheKB": 1280, "l3cacheKB": 12288, "speedMHz": 4400, "extensions": ["hasMMX", "hasSSE", "hasSSE2", "hasSSE3", "hasSSSE3", "hasSSE4_1", "hasSSE4_2", "hasAVX", "hasAVX2", "hasAES"]}, "os": {"name": "Linux", "version": "6.2.0-39-generic", "locale": "zh-CN", "distro": "Ubuntu-core", "distroVersion": "22"}, "hdd": {"profile": {"model": null, "revision": null, "type": null}, "binary": {"model": null, "revision": null, "type": null}, "system": {"model": null, "revision": null, "type": null}}, "gfx": {"D2DEnabled": null, "DWriteEnabled": null, "ContentBackend": "Skia", "Headless": false, "TargetFrameRate": 60, "textScaleFactor": 1, "adapters": [{"description": "Mesa Intel(R) Graphics (ADL GT2)", "vendorID": "0x8086", "deviceID": "0x46a3", "subsysID": null, "RAM": 0, "driver": null, "driverVendor": "mesa/iris", "driverVersion": "********", "driverDate": null, "GPUActive": true}], "monitors": [{"screenWidth": 1920, "screenHeight": 1080, "defaultCSSScaleFactor": 1, "contentsScaleFactor": 1}], "features": {"compositor": "webrender", "hwCompositing": {"status": "available"}, "gpuProcess": {"status": "unused"}, "webrender": {"status": "available"}, "wrCompositor": {"status": "blocklisted:FEATURE_FAILURE_WEBRENDER_COMPOSITOR_DISABLED"}, "openglCompositing": {"status": "available"}, "omtp": {"status": "unused"}}}, "appleModelId": null, "hasWinPackageId": null}, "settings": {"blocklistEnabled": true, "e10sEnabled": true, "e10sMultiProcesses": 8, "fissionEnabled": true, "locale": "zh-CN", "intl": {"requestedLocales": ["zh-CN"], "availableLocales": ["zh-CN", "en-US"], "appLocales": ["zh-CN", "en-US"], "systemLocales": ["zh-CN"], "regionalPrefsLocales": ["zh-CN"], "acceptLanguages": ["zh-CN", "zh", "zh-TW", "zh-HK", "en-US", "en"]}, "update": {"channel": "release", "enabled": false, "autoDownload": false, "background": false}, "userPrefs": {"browser.search.region": "CN", "browser.urlbar.autoFill": true, "browser.urlbar.autoFill.adaptiveHistory.enabled": false, "browser.urlbar.dnsResolveSingleWordsAfterSearch": 0, "browser.urlbar.quicksuggest.dataCollection.enabled": false, "browser.urlbar.suggest.quicksuggest.nonsponsored": false, "browser.urlbar.suggest.quicksuggest.sponsored": false, "media.gmp-gmpopenh264.lastInstallStart": 1756286812, "media.gmp-gmpopenh264.lastDownload": 1756286815, "media.gmp-gmpopenh264.lastUpdate": 1756286815, "media.gmp-manager.lastCheck": 1757069879, "media.gmp-manager.lastEmptyCheck": 1757069879, "network.http.microsoft-entra-sso.enabled": false, "network.trr.strict_native_fallback": false, "widget.content.gtk-high-contrast.enabled": true}, "sandbox": {"effectiveContentProcessLevel": 6, "contentWin32kLockdownState": 3}, "addonCompatibilityCheckEnabled": true, "isDefaultBrowser": true, "defaultSearchEngine": "baidu", "defaultSearchEngineData": {"loadPath": "[app]baidu", "name": "百度", "submissionURL": "https://www.baidu.com/baidu?ie=utf-8&wd="}}, "profile": {"creationDate": 20088, "firstUseDate": 20088}, "addons": {"activeAddons": {"<EMAIL>": {"version": "142.9.0", "scope": 4, "type": "extension", "updateDay": 0, "isSystem": true, "isWebExtension": true, "multiprocessCompatible": true, "blocklisted": false, "description": "Urgent post-release fixes for web compatibility.", "name": "Web Compatibility Interventions", "userDisabled": false, "appDisabled": false, "foreignInstall": false, "hasBinaryComponents": false, "installDay": 0, "quarantineIgnoredByApp": true, "quarantineIgnoredByUser": false}}, "theme": {"id": "<EMAIL>", "blocklisted": false, "description": "跟随系统主题配色显示按钮、菜单和窗口。", "name": "系统主题 — 自动", "userDisabled": false, "appDisabled": false, "version": "1.4.2", "scope": 4, "foreignInstall": false, "hasBinaryComponents": false, "installDay": 20088, "updateDay": 20088}, "activeGMPlugins": {"gmp-gmpopenh264": {"version": "2.6.0", "userDisabled": false, "applyBackgroundUpdates": 1}}}, "experiments": {"long-term-holdback-2025h1-growth-desktop": {"branch": "delivery", "type": "nimbus-nimbus"}, "encrypted-client-hello-fallback-mechanism": {"branch": "control", "type": "nimbus-rollout"}, "fx-accounts-ping-release-rollout-2": {"branch": "control", "type": "nimbus-rollout"}, "disable-ads-startup-cache": {"branch": "control", "type": "nimbus-rollout"}, "unified-api-for-spocs-and-top-sites-controlled-rollout-for-release-133": {"branch": "control", "type": "nimbus-rollout"}, "fox-doodle-multi-action-cta-2025-rollout": {"branch": "treatment-a", "type": "nimbus-rollout"}, "desktop-credit-card-autofill-global-enablement-rollout-release": {"branch": "creditcard-enable", "type": "nimbus-rollout"}, "device-migration-accounts-toolbar-icon-rollout": {"branch": "treatment-a", "type": "nimbus-rollout"}, "upgraded-sidebar-138-broad-rollout": {"branch": "rollout-treatment", "type": "nimbus-rollout"}, "chips-rollout-to-firefox": {"branch": "control", "type": "nimbus-rollout"}, "address-bar-update-rollout-v1": {"branch": "rollout", "type": "nimbus-nimbus"}, "unified-search-button-callout-rollout-v1": {"branch": "control", "type": "nimbus-rollout"}, "custom-wallpapers-no-message-rollout-release": {"branch": "control", "type": "nimbus-rollout"}, "spoc-positions-and-placements-rollout": {"branch": "control", "type": "nimbus-rollout"}, "storage-access-heuristic-restriction-rollout": {"branch": "treatment-branch", "type": "nimbus-rollout"}, "desktop-release-rollout-show-relay-to-all-browsers-next-sign-up-modal-cta": {"branch": "next-sign-up-modal-cta", "type": "nimbus-rollout"}, "vertical-tabs-feature-callout-experiment-v20-treatment-a-rollout": {"branch": "treatment-a", "type": "nimbus-rollout"}, "report-this-ad": {"branch": "control", "type": "nimbus-rollout"}, "1-callout-contextual-chatbot-suggestion-treatment-a-tab-switching-copy-rollout": {"branch": "treatment-a-tab-switching-copy", "type": "nimbus-rollout"}, "disable-enrollment-status-telemetry-for-firefox-desktop-via-nimbustelemetry": {"branch": "control", "type": "nimbus-rollout"}, "one-click-sponsored-settings": {"branch": "control", "type": "nimbus-rollout"}, "enforce-crlite-results-and-limit-use-of-ocsp-rollout": {"branch": "enforce", "type": "nimbus-rollout"}, "visual-card-updates": {"branch": "updated", "type": "nimbus-rollout"}, "account-adoption-callout-passwords-global-rollout": {"branch": "treatment-e", "type": "nimbus-rollout"}, "enable-nimbus-sql-datastore": {"branch": "control", "type": "nimbus-rollout"}, "account-adoption-app-menu-rollout": {"branch": "treatment-a", "type": "nimbus-rollout"}, "context-id-rotation-every-30-days": {"branch": "control", "type": "nimbus-rollout"}}}}