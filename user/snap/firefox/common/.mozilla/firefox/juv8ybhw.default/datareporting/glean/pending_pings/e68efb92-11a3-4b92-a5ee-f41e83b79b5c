/submit/firefox-desktop/baseline/1/e68efb92-11a3-4b92-a5ee-f41e83b79b5c
{"ping_info":{"seq":39,"start_time":"2025-08-27T17:26:22.000+08:00","end_time":"2025-09-05T18:56:54.802+08:00","reason":"dirty_startup","experiments":{"new-alt-text-flow-and-generation-treatment-a-rollout-for-non-en-locales":{"branch":"treatment-a","extra":{"type":"nimbus-rollout"}},"crlite-rollout":{"branch":"rollout","extra":{"type":"nimbus-rollout"}},"fx-view-discoverability-2025-rollout":{"branch":"treatment-b","extra":{"type":"nimbus-rollout"}},"fox-doodle-multi-action-cta-2025-rollout":{"branch":"treatment-a","extra":{"type":"nimbus-rollout"}},"fx-accounts-ping-release-rollout-2":{"branch":"control","extra":{"type":"nimbus-rollout"}},"upgrade-spotlight-rollout":{"branch":"treatment","extra":{"type":"nimbus-rollout"}},"disable-ads-startup-cache":{"branch":"control","extra":{"type":"nimbus-rollout"}},"disable-redirects-for-authretries":{"branch":"control","extra":{"type":"nimbus-rollout"}},"extensions-migration-in-import-wizard-116-rollout":{"branch":"control","extra":{"type":"nimbus-rollout"}},"fpp-floating-point-protection-rollout-linux-only":{"branch":"control","extra":{"type":"nimbus-rollout"}},"ai-chatbot-rollout-in-the-old-sidebar":{"branch":"treatment-d","extra":{"type":"nimbus-rollout"}},"encrypted-client-hello-fallback-mechanism":{"branch":"control","extra":{"type":"nimbus-rollout"}},"unified-api-for-spocs-and-top-sites-controlled-rollout-for-release-133":{"branch":"control","extra":{"type":"nimbus-rollout"}},"pdf-annotations-highlight-treatment-b-rollout":{"branch":"treatment-b","extra":{"type":"nimbus-rollout"}},"phc-rollout":{"branch":"rollout","extra":{"type":"nimbus-rollout"}},"long-term-holdback-2025h1-growth-desktop":{"branch":"delivery","extra":{"type":"nimbus-nimbus"}}}},"client_info":{"telemetry_sdk_build":"64.5.1","app_channel":"release","architecture":"x86_64","os":"Linux","os_version":"6.2","app_display_version":"142.0","app_build":"20250821175018","locale":"en-US","client_id":"15746e11-2a34-4bf3-90f1-25182fa815cd","build_date":"1970-01-01T00:00:00+00:00","first_run_date":"2024-12-31+00:00","distribution":{"name":"canonical-002"}},"metrics":{"string":{"usage.distribution_id":"canonical-002"},"uuid":{"legacy.telemetry.profile_group_id":"78458a56-b1ca-4d0e-bb84-e0e1ac9bc0f3","legacy.telemetry.client_id":"78458a56-b1ca-4d0e-bb84-e0e1ac9bc0f3"},"counter":{"browser.engagement.active_ticks":90,"browser.engagement.uri_count":25},"labeled_counter":{"glean.validation.pings_submitted":{"baseline":1,"events":1,"metrics":1}},"boolean":{"usage.is_default_browser":true}}}
{"headers":{},"body_has_info_sections":true,"ping_name":"baseline","uploader_capabilities":[]}