# 这个 viminfo 文件是由 Vim 9.0 生成的。
# 如果要自行修改请特别小心！

# Viminfo version
|1,4

# 此文件建立时 'encoding' 的值
*encoding=utf-8


# hlsearch on (H) or off (h):
~h
# 命令行 历史记录 (从新到旧):
:wq
|2,0,1757379317,,"wq"
:q!
|2,0,1756191675,,"q!"
:wq!
|2,0,1736933349,,"wq!"

# 查找字符串 历史记录 (从新到旧):

# 表达式 历史记录 (从新到旧):

# 输入行 历史记录 (从新到旧):

# 输入行 历史记录 (从新到旧):

# 寄存器:

# 文件标记:
'0  6  11  /home/<USER>/orig/startfile.txt
|4,48,6,11,1757379317,"/home/<USER>/orig/startfile.txt"
'1  34  0  /home/<USER>/runMQTT.sh
|4,49,34,0,1757294609,"/home/<USER>/runMQTT.sh"
'2  67  0  /home/<USER>/runMQTT.sh
|4,50,67,0,1757040261,"/home/<USER>/runMQTT.sh"
'3  67  0  /home/<USER>/runMQTT.sh
|4,51,67,0,1757040261,"/home/<USER>/runMQTT.sh"
'4  67  0  /home/<USER>/runMQTT.sh
|4,52,67,0,1756191675,"/home/<USER>/runMQTT.sh"
'5  67  0  /home/<USER>/runMQTT.sh
|4,53,67,0,1756191675,"/home/<USER>/runMQTT.sh"
'6  1  0  /home/<USER>/build.sh
|4,54,1,0,1755070808,"/home/<USER>/build.sh"
'7  1  0  /opt/MVS/bin/MVS.sh
|4,55,1,0,1737791631,"/opt/MVS/bin/MVS.sh"
'8  10  0  /home/<USER>/dic/para/video_displacement.txt
|4,56,10,0,1737211414,"/home/<USER>/dic/para/video_displacement.txt"
'9  198  0  /home/<USER>/orig/videoCaptureWeb.cpp
|4,57,198,0,1736942336,"/home/<USER>/orig/videoCaptureWeb.cpp"

# 跳转列表 (从新到旧):
-'  6  11  /home/<USER>/orig/startfile.txt
|4,39,6,11,1757379317,"/home/<USER>/orig/startfile.txt"
-'  1  0  /home/<USER>/orig/startfile.txt
|4,39,1,0,1757379303,"/home/<USER>/orig/startfile.txt"
-'  34  0  /home/<USER>/runMQTT.sh
|4,39,34,0,1757294609,"/home/<USER>/runMQTT.sh"
-'  34  0  /home/<USER>/runMQTT.sh
|4,39,34,0,1757294609,"/home/<USER>/runMQTT.sh"
-'  67  0  /home/<USER>/runMQTT.sh
|4,39,67,0,1757294594,"/home/<USER>/runMQTT.sh"
-'  67  0  /home/<USER>/runMQTT.sh
|4,39,67,0,1757294594,"/home/<USER>/runMQTT.sh"
-'  67  0  /home/<USER>/runMQTT.sh
|4,39,67,0,1757040261,"/home/<USER>/runMQTT.sh"
-'  67  0  /home/<USER>/runMQTT.sh
|4,39,67,0,1757040261,"/home/<USER>/runMQTT.sh"
-'  67  0  /home/<USER>/runMQTT.sh
|4,39,67,0,1756191675,"/home/<USER>/runMQTT.sh"
-'  67  0  /home/<USER>/runMQTT.sh
|4,39,67,0,1756191675,"/home/<USER>/runMQTT.sh"
-'  1  0  /home/<USER>/runMQTT.sh
|4,39,1,0,1756191575,"/home/<USER>/runMQTT.sh"
-'  1  0  /home/<USER>/runMQTT.sh
|4,39,1,0,1756191575,"/home/<USER>/runMQTT.sh"
-'  1  0  /home/<USER>/runMQTT.sh
|4,39,1,0,1756191575,"/home/<USER>/runMQTT.sh"
-'  1  0  /home/<USER>/runMQTT.sh
|4,39,1,0,1756191575,"/home/<USER>/runMQTT.sh"
-'  1  0  /home/<USER>/runMQTT.sh
|4,39,1,0,1756191575,"/home/<USER>/runMQTT.sh"
-'  1  0  /home/<USER>/runMQTT.sh
|4,39,1,0,1756191575,"/home/<USER>/runMQTT.sh"
-'  1  0  /home/<USER>/build.sh
|4,39,1,0,1755070808,"/home/<USER>/build.sh"
-'  1  0  /home/<USER>/build.sh
|4,39,1,0,1755070808,"/home/<USER>/build.sh"
-'  1  0  /home/<USER>/build.sh
|4,39,1,0,1755070808,"/home/<USER>/build.sh"
-'  1  0  /home/<USER>/build.sh
|4,39,1,0,1755070808,"/home/<USER>/build.sh"
-'  1  0  /home/<USER>/build.sh
|4,39,1,0,1755070808,"/home/<USER>/build.sh"
-'  1  0  /home/<USER>/build.sh
|4,39,1,0,1755070808,"/home/<USER>/build.sh"
-'  1  0  /home/<USER>/build.sh
|4,39,1,0,1755070808,"/home/<USER>/build.sh"
-'  1  0  /home/<USER>/build.sh
|4,39,1,0,1755070808,"/home/<USER>/build.sh"
-'  1  0  /home/<USER>/build.sh
|4,39,1,0,1755070808,"/home/<USER>/build.sh"
-'  1  0  /home/<USER>/build.sh
|4,39,1,0,1755070808,"/home/<USER>/build.sh"
-'  1  0  /home/<USER>/build.sh
|4,39,1,0,1755070808,"/home/<USER>/build.sh"
-'  1  0  /home/<USER>/build.sh
|4,39,1,0,1755070808,"/home/<USER>/build.sh"
-'  1  0  /home/<USER>/build.sh
|4,39,1,0,1755070808,"/home/<USER>/build.sh"
-'  1  0  /home/<USER>/build.sh
|4,39,1,0,1755070808,"/home/<USER>/build.sh"
-'  1  0  /home/<USER>/build.sh
|4,39,1,0,1755070808,"/home/<USER>/build.sh"
-'  1  0  /home/<USER>/build.sh
|4,39,1,0,1755070808,"/home/<USER>/build.sh"
-'  1  0  /opt/MVS/bin/MVS.sh
|4,39,1,0,1737791631,"/opt/MVS/bin/MVS.sh"
-'  1  0  /opt/MVS/bin/MVS.sh
|4,39,1,0,1737791631,"/opt/MVS/bin/MVS.sh"
-'  1  0  /opt/MVS/bin/MVS.sh
|4,39,1,0,1737791631,"/opt/MVS/bin/MVS.sh"
-'  1  0  /opt/MVS/bin/MVS.sh
|4,39,1,0,1737791631,"/opt/MVS/bin/MVS.sh"
-'  1  0  /opt/MVS/bin/MVS.sh
|4,39,1,0,1737791631,"/opt/MVS/bin/MVS.sh"
-'  1  0  /opt/MVS/bin/MVS.sh
|4,39,1,0,1737791631,"/opt/MVS/bin/MVS.sh"
-'  1  0  /opt/MVS/bin/MVS.sh
|4,39,1,0,1737791631,"/opt/MVS/bin/MVS.sh"
-'  1  0  /opt/MVS/bin/MVS.sh
|4,39,1,0,1737791631,"/opt/MVS/bin/MVS.sh"
-'  1  0  /opt/MVS/bin/MVS.sh
|4,39,1,0,1737791631,"/opt/MVS/bin/MVS.sh"
-'  1  0  /opt/MVS/bin/MVS.sh
|4,39,1,0,1737791631,"/opt/MVS/bin/MVS.sh"
-'  1  0  /opt/MVS/bin/MVS.sh
|4,39,1,0,1737791631,"/opt/MVS/bin/MVS.sh"
-'  1  0  /opt/MVS/bin/MVS.sh
|4,39,1,0,1737791631,"/opt/MVS/bin/MVS.sh"
-'  1  0  /opt/MVS/bin/MVS.sh
|4,39,1,0,1737791631,"/opt/MVS/bin/MVS.sh"
-'  1  0  /opt/MVS/bin/MVS.sh
|4,39,1,0,1737791631,"/opt/MVS/bin/MVS.sh"
-'  1  0  /opt/MVS/bin/MVS.sh
|4,39,1,0,1737791631,"/opt/MVS/bin/MVS.sh"
-'  1  0  /opt/MVS/bin/MVS.sh
|4,39,1,0,1737791631,"/opt/MVS/bin/MVS.sh"
-'  1  0  /opt/MVS/bin/MVS.sh
|4,39,1,0,1737791631,"/opt/MVS/bin/MVS.sh"
-'  1  0  /opt/MVS/bin/MVS.sh
|4,39,1,0,1737791631,"/opt/MVS/bin/MVS.sh"
-'  1  0  /opt/MVS/bin/MVS.sh
|4,39,1,0,1737791631,"/opt/MVS/bin/MVS.sh"
-'  1  0  /opt/MVS/bin/MVS.sh
|4,39,1,0,1737791631,"/opt/MVS/bin/MVS.sh"
-'  1  0  /opt/MVS/bin/MVS.sh
|4,39,1,0,1737791631,"/opt/MVS/bin/MVS.sh"
-'  1  0  /opt/MVS/bin/MVS.sh
|4,39,1,0,1737791631,"/opt/MVS/bin/MVS.sh"
-'  1  0  /opt/MVS/bin/MVS.sh
|4,39,1,0,1737791631,"/opt/MVS/bin/MVS.sh"
-'  1  0  /opt/MVS/bin/MVS.sh
|4,39,1,0,1737791631,"/opt/MVS/bin/MVS.sh"
-'  1  0  /opt/MVS/bin/MVS.sh
|4,39,1,0,1737791631,"/opt/MVS/bin/MVS.sh"
-'  1  0  /opt/MVS/bin/MVS.sh
|4,39,1,0,1737791631,"/opt/MVS/bin/MVS.sh"
-'  1  0  /opt/MVS/bin/MVS.sh
|4,39,1,0,1737791631,"/opt/MVS/bin/MVS.sh"
-'  1  0  /opt/MVS/bin/MVS.sh
|4,39,1,0,1737791631,"/opt/MVS/bin/MVS.sh"
-'  1  0  /opt/MVS/bin/MVS.sh
|4,39,1,0,1737791631,"/opt/MVS/bin/MVS.sh"
-'  1  0  /opt/MVS/bin/MVS.sh
|4,39,1,0,1737791631,"/opt/MVS/bin/MVS.sh"
-'  1  0  /opt/MVS/bin/MVS.sh
|4,39,1,0,1737791631,"/opt/MVS/bin/MVS.sh"
-'  1  0  /opt/MVS/bin/MVS.sh
|4,39,1,0,1737791631,"/opt/MVS/bin/MVS.sh"
-'  10  0  /home/<USER>/dic/para/video_displacement.txt
|4,39,10,0,1737211414,"/home/<USER>/dic/para/video_displacement.txt"
-'  10  0  /home/<USER>/dic/para/video_displacement.txt
|4,39,10,0,1737211414,"/home/<USER>/dic/para/video_displacement.txt"
-'  10  0  /home/<USER>/dic/para/video_displacement.txt
|4,39,10,0,1737211414,"/home/<USER>/dic/para/video_displacement.txt"
-'  10  0  /home/<USER>/dic/para/video_displacement.txt
|4,39,10,0,1737211414,"/home/<USER>/dic/para/video_displacement.txt"
-'  10  0  /home/<USER>/dic/para/video_displacement.txt
|4,39,10,0,1737211414,"/home/<USER>/dic/para/video_displacement.txt"
-'  10  0  /home/<USER>/dic/para/video_displacement.txt
|4,39,10,0,1737211414,"/home/<USER>/dic/para/video_displacement.txt"
-'  10  0  /home/<USER>/dic/para/video_displacement.txt
|4,39,10,0,1737211414,"/home/<USER>/dic/para/video_displacement.txt"
-'  10  0  /home/<USER>/dic/para/video_displacement.txt
|4,39,10,0,1737211414,"/home/<USER>/dic/para/video_displacement.txt"
-'  10  0  /home/<USER>/dic/para/video_displacement.txt
|4,39,10,0,1737211414,"/home/<USER>/dic/para/video_displacement.txt"
-'  10  0  /home/<USER>/dic/para/video_displacement.txt
|4,39,10,0,1737211414,"/home/<USER>/dic/para/video_displacement.txt"
-'  10  0  /home/<USER>/dic/para/video_displacement.txt
|4,39,10,0,1737211414,"/home/<USER>/dic/para/video_displacement.txt"
-'  10  0  /home/<USER>/dic/para/video_displacement.txt
|4,39,10,0,1737211414,"/home/<USER>/dic/para/video_displacement.txt"
-'  10  0  /home/<USER>/dic/para/video_displacement.txt
|4,39,10,0,1737211414,"/home/<USER>/dic/para/video_displacement.txt"
-'  10  0  /home/<USER>/dic/para/video_displacement.txt
|4,39,10,0,1737211414,"/home/<USER>/dic/para/video_displacement.txt"
-'  10  0  /home/<USER>/dic/para/video_displacement.txt
|4,39,10,0,1737211414,"/home/<USER>/dic/para/video_displacement.txt"
-'  10  0  /home/<USER>/dic/para/video_displacement.txt
|4,39,10,0,1737211414,"/home/<USER>/dic/para/video_displacement.txt"
-'  10  0  /home/<USER>/dic/para/video_displacement.txt
|4,39,10,0,1737211414,"/home/<USER>/dic/para/video_displacement.txt"
-'  10  0  /home/<USER>/dic/para/video_displacement.txt
|4,39,10,0,1737211414,"/home/<USER>/dic/para/video_displacement.txt"
-'  10  0  /home/<USER>/dic/para/video_displacement.txt
|4,39,10,0,1737211414,"/home/<USER>/dic/para/video_displacement.txt"
-'  10  0  /home/<USER>/dic/para/video_displacement.txt
|4,39,10,0,1737211414,"/home/<USER>/dic/para/video_displacement.txt"
-'  10  0  /home/<USER>/dic/para/video_displacement.txt
|4,39,10,0,1737211414,"/home/<USER>/dic/para/video_displacement.txt"
-'  10  0  /home/<USER>/dic/para/video_displacement.txt
|4,39,10,0,1737211414,"/home/<USER>/dic/para/video_displacement.txt"
-'  10  0  /home/<USER>/dic/para/video_displacement.txt
|4,39,10,0,1737211414,"/home/<USER>/dic/para/video_displacement.txt"
-'  10  0  /home/<USER>/dic/para/video_displacement.txt
|4,39,10,0,1737211414,"/home/<USER>/dic/para/video_displacement.txt"
-'  10  0  /home/<USER>/dic/para/video_displacement.txt
|4,39,10,0,1737211414,"/home/<USER>/dic/para/video_displacement.txt"
-'  10  0  /home/<USER>/dic/para/video_displacement.txt
|4,39,10,0,1737211414,"/home/<USER>/dic/para/video_displacement.txt"
-'  10  0  /home/<USER>/dic/para/video_displacement.txt
|4,39,10,0,1737211414,"/home/<USER>/dic/para/video_displacement.txt"
-'  10  0  /home/<USER>/dic/para/video_displacement.txt
|4,39,10,0,1737211414,"/home/<USER>/dic/para/video_displacement.txt"
-'  10  0  /home/<USER>/dic/para/video_displacement.txt
|4,39,10,0,1737211414,"/home/<USER>/dic/para/video_displacement.txt"
-'  10  0  /home/<USER>/dic/para/video_displacement.txt
|4,39,10,0,1737211414,"/home/<USER>/dic/para/video_displacement.txt"
-'  10  0  /home/<USER>/dic/para/video_displacement.txt
|4,39,10,0,1737211414,"/home/<USER>/dic/para/video_displacement.txt"
-'  10  0  /home/<USER>/dic/para/video_displacement.txt
|4,39,10,0,1737211414,"/home/<USER>/dic/para/video_displacement.txt"
-'  10  0  /home/<USER>/dic/para/video_displacement.txt
|4,39,10,0,1737211414,"/home/<USER>/dic/para/video_displacement.txt"
-'  10  0  /home/<USER>/dic/para/video_displacement.txt
|4,39,10,0,1737211414,"/home/<USER>/dic/para/video_displacement.txt"
-'  10  0  /home/<USER>/dic/para/video_displacement.txt
|4,39,10,0,1737211414,"/home/<USER>/dic/para/video_displacement.txt"
-'  10  0  /home/<USER>/dic/para/video_displacement.txt
|4,39,10,0,1737211414,"/home/<USER>/dic/para/video_displacement.txt"

# 文件内的标记历史记录 (从新到旧):

> /home/<USER>/orig/startfile.txt
	*	1757379314	0
	"	6	11
	^	6	12
	.	6	11
	+	6	11

> /home/<USER>/runMQTT.sh
	*	1757294606	0
	"	34	0

> /home/<USER>/build.sh
	*	1755070806	0
	"	1	0

> /opt/MVS/bin/MVS.sh
	*	1737791629	0
	"	1	0

> /home/<USER>/orig/videoCaptureWeb.cpp
	*	1736942335	0
	"	198	0

> /home/<USER>/orig/captures_single.cpp
	*	1736942302	0
	"	160	0

> /home/<USER>/dic/para/.video_displacement.txt.swp
	*	1736942183	0
	"	4	0

> /home/<USER>/dic/runMQTT.sh
	*	1736942086	0
	"	1	8
	^	4	10
	.	4	9
	+	39	2
	+	4	9

> ~/桌面/capture_x86/captures_ip.cpp
	*	1736942029	0
	"	154	0

> /home/<USER>/dic/para/video_displacement.txt
	*	1736941941	0
	"	10	0

> /home/<USER>/dic/prog/runLiveVideoResultProcess.sh
	*	1736933346	0
	"	110	35
	^	110	36
	.	110	27
	+	47	4
	+	104	46
	+	105	0
	+	109	0
	+	108	36
	+	110	27

> /home/<USER>/orig/runCapture.sh
	*	1736830392	0
	"	1	0
