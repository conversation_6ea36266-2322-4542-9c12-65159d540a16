Demo: captures_mv_ip.cpp
	# g++ -g -o captures_mv_ip captures_mv_ip.cpp -I/opt/MVS/include -Wl,-rpath=$(MVCAM_COMMON_RUNENV)/64 -L$(MVCAM_COMMON_RUNENV)/64 -lMvCameraControl -lpthread -lmodbus -std=c++17 -lboost_filesystem -lboost_system -I/usr/include/opencv4 -lopencv_core -lopencv_imgcodecs   
	g++ -g -o captures_mv_ip captures_mv_ip.cpp -I/opt/MVS/include -Wl,-rpath=$(MVCAM_COMMON_RUNENV)/64 -L$(MVCAM_COMMON_RUNENV)/64 -lMvCameraControl -lpthread -lmodbus -std=c++17 -lboost_filesystem -lboost_system    



clean:
	rm captures_mv_ip -rf
