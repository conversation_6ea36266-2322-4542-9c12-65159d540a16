#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include <stdlib.h>
#include <pthread.h>
#include "MvCameraControl.h"

#include <string>
#include <vector>
#include <iostream>
#include <fstream> 
#include <sstream>
#include <cstring> 
#include <modbus/modbus.h>
#include <errno.h> 
#include <modbus/modbus-rtu.h>
#include <mutex>
#include <iomanip>
#include <chrono>
#include <ctime>
#include <filesystem>
#include <unordered_map>

#include <boost/filesystem.hpp>

// #include <opencv2/opencv.hpp>

bool g_bExit = false;
unsigned int g_nPayloadSize = 0;

#define BAN_BEN "250327"
#define START_FILE "local_mv_ip.txt"
// #define ORIG_FILE "originalList.txt"
#define ORIG_TOC "/home/<USER>/orig/"
#define ORIG_FILE "originalObservation.txt"

std::string CaptureFailedRecord(const char *image_name);
unsigned int JpgQualityRangeNumerical(unsigned int jpg_quality);
void MvErrorMessage(int nRet);
void deleteOldImages(std::string image_directory, int max_images, std::string data_file_name, std::string file_extension);
// mutex
std::mutex data_file_mutex;

// initial
void DefaultInitialConfiguration_mv_ip(std::string dic_file){
    //Create initial configuration file. 
    std::ofstream file(dic_file);
        if (file.is_open()) { 
            std::cout << "  Currently writing the following content. " << "\n";
            std::cout << "  writing in progress: " << "\n";
            std::cout << "  The default IP address of the current device is initially set to ***********. " << "\n" ;
            file << "***********";
            file.close();
            std::cout << "  default initial configuration " << dic_file << " the completed creation." << std::endl;
        } else {
            std::cout << "  Unable to create. " << dic_file << std::endl;
        }    
}

// register write value
void Wrimodbus(modbus_t *ctx, uint16_t reg_addr, int reg_value){       
    // Write a value to the register. 
    if (modbus_write_register(ctx, reg_addr, reg_value) == -1) {
        fprintf(stderr, "  Failed to write to the register: %s\n", modbus_strerror(errno));
        //modbus_free(ctx);
        //return ;
    }
    printf("  Successfully wrote a value to the register.\n");
}

// collect temperature data
float ReadingTheTemperature(){
    // create modbus 
    modbus_t *ctx;
    ctx = modbus_new_rtu("/dev/ttyS5", 9600, 'N', 8, 1);  
    if (ctx == NULL) {  
        std::cout << "  Unable to create Modbus context." << std::endl;  
    }
    // Slave ID 
    modbus_set_slave(ctx, 1);   
    // Connecting to the Modbus RTU server, which actually involves opening the serial port.  
    if (modbus_connect(ctx) == -1) {  
        std::cout << "  Connection failed: " << modbus_strerror(errno) << std::endl;  
        std::cout << "  Check if the A5 and B5 lines in RS485 are functioning properly." << std::endl;  
    }
    // Save the read value(s)
    uint16_t reg;
    // Store the temperature in Celsius
    float zreg;
    if (modbus_read_registers(ctx, 60, 1, &reg) == -1) {
        std::cout << "  Unable to read the current temperature." << std::endl;
        zreg = 00.0;
        std::cout << std::fixed << std::setprecision(1) << "  Current temperature: " << zreg << "\n"<< std::endl;                    
    } else {  
        zreg = reg/100.0;
        std::cout << std::fixed << std::setprecision(1) << "  Current temperature: " << zreg << "\n"<<std::endl;  
    }                 
    modbus_close(ctx);
    modbus_free(ctx);

    return zreg;  
}

// Read all numbers (or strings) from the specified file and store them in a string vector.
bool Confile(const std::string& readfilename, std::vector<std::string>& numbers){    
    std::ifstream file(readfilename);
    if (!file.is_open()) {
        std::cout << "  Unable to open file: " << readfilename << std::endl;
        return false;
    }
    std::string line;
    while (std::getline(file, line)) {
        std::istringstream iss(line);
        std::string number;
        while (iss >> number) {
            numbers.push_back(number);
        }
    }
    file.close();
    return true;

}

// write to a log file
void WriteDataToFlie(const char *image_name, float zreg){    
    auto current_time = std::chrono::system_clock::now();
    std::time_t now = std::chrono::system_clock::to_time_t(current_time);
    std::stringstream filetime ;
    filetime << std::put_time(std::localtime(&now), "%Y%m%d%H%M%S");    
    std::stringstream data_ss;
    data_ss << std::fixed << std::setprecision(1) << filetime.str() << "  " << image_name << "  " << zreg << std::endl;
    std::cout << "  stringstream content: " << data_ss.str() ;
    std::string toc_file, toc_name, file_name;
    toc_name = ORIG_TOC;
    file_name = ORIG_FILE;
    toc_file = toc_name + file_name;
    std::ofstream data_file(toc_file, std::ios::app);
    if (data_file.is_open()) {
        std::lock_guard<std::mutex> lock(data_file_mutex); // 加锁
        data_file << data_ss.str();
        data_file.close();
        std::cout << "  The data has been successfully written to " << ORIG_FILE << std::endl;
    } else {
        std::cout << "  Unable to open the file " << ORIG_FILE << std::endl;
    }
}

// parameter description
void ViewCommandAll(){
    std::cout << "  Incorrect parameters, You need to enter the correct parameters. " << "\n";
    std::cout << "  Parameter: the default ip && camera ip && image name && flag && image quality && Max number of pictures " << "\n";
    std::cout << "  flag: 1 represents adding this data record taken to originalList.txt; " << "\n";
    std::cout << "        0 represents not adding it to originalList.txt." << "\n";
    std::cout << "  image quality: 0.51 to 0.99, for example 0.9 = 90 percent quality grade. " << "\n"; 
    std::cout << "  Max number of pictures: default of 1000 pictures. " << "\n"; 
    std::cout << "  PS:  *********** ************* a235.jpg 1 0.9 1000" << "\n";
}

// Print information of MVCC_INTVALUE structure
void printMVCCIntValue(const MVCC_INTVALUE* pIntValue) {
    if (pIntValue == NULL) {
        printf("  Invalid MVCC_INTVALUE pointer.\n");
        return;
    }
 
    printf("  MVCC_INTVALUE Information:\n");
    printf("  Current Value: %u\n", pIntValue->nCurValue);
    printf("  Max Value: %u\n", pIntValue->nMax);
    printf("  Min Value: %u\n", pIntValue->nMin);
    printf("  Increment: %u\n", pIntValue->nInc);
    printf("  Reserved: ");
    for (int i = 0; i < 4; i++) {
        printf("%u ", pIntValue->nReserved[i]);
    }
    printf("\n");
}

// A function to print a struct; Output frame information
void printFrameOutInfo(const MV_FRAME_OUT_INFO_EX& info) {
    std::cout << "  MV_FRAME_OUT_INFO_EX:" << std::endl;
    std::cout << "  nWidth: " << info.nWidth << std::endl;
    std::cout << "  nHeight: " << info.nHeight << std::endl;
    std::cout << "  enPixelType: " << info.enPixelType << std::endl;
    std::cout << "  nFrameNum: " << info.nFrameNum << std::endl;
    std::cout << "  nDevTimeStampHigh: " << info.nDevTimeStampHigh << std::endl;
    std::cout << "  nDevTimeStampLow: " << info.nDevTimeStampLow << std::endl;
    std::cout << "  nHostTimeStamp: " << info.nHostTimeStamp << std::endl;
    std::cout << "  nFrameLen: " << info.nFrameLen << std::endl;
    std::cout << "  nSecondCount: " << info.nSecondCount << std::endl;
    std::cout << "  nCycleCount: " << info.nCycleCount << std::endl;
    std::cout << "  nCycleOffset: " << info.nCycleOffset << std::endl;
    std::cout << "  fGain: " << info.fGain << std::endl;
    std::cout << "  fExposureTime: " << info.fExposureTime << std::endl;
    std::cout << "  nAverageBrightness: " << info.nAverageBrightness << std::endl;
    std::cout << "  nRed: " << info.nRed << std::endl;
    std::cout << "  nGreen: " << info.nGreen << std::endl;
    std::cout << "  nBlue: " << info.nBlue << std::endl;
    std::cout << "  nFrameCounter: " << info.nFrameCounter << std::endl;
    std::cout << "  nTriggerIndex: " << info.nTriggerIndex << std::endl;
    std::cout << "  nInput: " << info.nInput << std::endl;
    std::cout << "  nOutput: " << info.nOutput << std::endl;
    std::cout << "  nOffsetX: " << info.nOffsetX << std::endl;
    std::cout << "  nOffsetY: " << info.nOffsetY << std::endl;
    std::cout << "  nChunkWidth: " << info.nChunkWidth << std::endl;
    std::cout << "  nChunkHeight: " << info.nChunkHeight << std::endl;
    std::cout << "  nLostPacket: " << info.nLostPacket << std::endl;
    std::cout << "  nUnparsedChunkNum: " << info.nUnparsedChunkNum << std::endl;
 
    // Unparsed Chunk Content
    if (info.UnparsedChunkList.pUnparsedChunkContent) {
        std::cout << "  UnparsedChunkContent: (pointer) " << info.UnparsedChunkList.pUnparsedChunkContent << std::endl;
    } else {
        std::cout << "  nAligning: " << info.UnparsedChunkList.nAligning << std::endl;
    }
 
    std::cout << "  nExtendWidth: " << info.nExtendWidth << std::endl;
    std::cout << "  nExtendHeight: " << info.nExtendHeight << std::endl;
    std::cout << "  nFrameLenEx: " << info.nFrameLen << std::endl;
 
    // Reserved
    std::cout << "  nReserved: [";
    for (int i = 0; i < 32; ++i) {
        std::cout << info.nReserved[i];
        if (i < 31) std::cout << ", ";
    }
    std::cout << "]" << "\n" <<std::endl;
}

// A function to print a struct; Image saving parameters
void printSaveImageParam(const MV_SAVE_IMAGE_PARAM_EX& param) {
    std::cout << "  MV_SAVE_IMAGE_PARAM_EX:" << std::endl;
    std::cout << "  pData: " << static_cast<void*>(param.pData) << std::endl;  // 打印指针地址
    std::cout << "  nDataLen: " << param.nDataLen << std::endl;
    std::cout << "  enPixelType: " << param.enPixelType << std::endl;
    std::cout << "  nWidth: " << param.nWidth << std::endl;
    std::cout << "  nHeight: " << param.nHeight << std::endl;
    std::cout << "  pImageBuffer: " << static_cast<void*>(param.pImageBuffer) << std::endl;  // 打印指针地址
    std::cout << "  nImageLen: " << param.nImageLen << std::endl;
    std::cout << "  nBufferSize: " << param.nBufferSize << std::endl;
    std::cout << "  enImageType: " << param.enImageType << std::endl;
    std::cout << "  nJpgQuality: " << param.nJpgQuality << std::endl;
    std::cout << "  iMethodValue: " << param.iMethodValue << std::endl;
    std::cout << "  nReserved: [" 
              << param.nReserved[0] << ", " 
              << param.nReserved[1] << ", " 
              << param.nReserved[2] << "]" << "\n" << std::endl;
}

// wait for user to input enter to stop grabbing or end the sample program
void PressEnterToExit(void)
{
    int c;
    while ( (c = getchar()) != '\n' && c != EOF );
    fprintf( stderr, "\nPress enter to exit.\n");
    while( getchar() != '\n');
    g_bExit = true;
    sleep(1);
}

// sdk judgment
static void* WorkThread(void* pUser)
{
    int nRet = MV_OK;

    MV_FRAME_OUT_INFO_EX stImageInfo = {0};
    memset(&stImageInfo, 0, sizeof(MV_FRAME_OUT_INFO_EX));
    unsigned char * pData = (unsigned char *)malloc(sizeof(unsigned char) * (g_nPayloadSize));
    if (NULL == pData)
    {
        return NULL;
    }
    unsigned int nDataSize = g_nPayloadSize;

    while(1)
    {
		if(g_bExit)
        {
            break;
        }

        nRet = MV_CC_GetOneFrameTimeout(pUser, pData, nDataSize, &stImageInfo, 1000);
        if (nRet == MV_OK)
        {
            printf("Get One Frame: Width[%d], Height[%d], nFrameNum[%d]\n", 
                stImageInfo.nWidth, stImageInfo.nHeight, stImageInfo.nFrameNum);
        }
        else
        {
            printf("No data[0x%x]\n", nRet);
			break;        
		}
	}

    free(pData);
    return 0;
}

// capture image
int CaptureAnImage_mv_ip(const char *the_default_ip, const char *camera_mv_ip, const char *image_name, unsigned int jpg_quality){
    // The return value indicating whether the image acquisition is successful; 
    // 0 is false, 1 is true, 2 is other cases.
    int ivalue = 0;
    // from the ConmectSpecCamera 
    int nRet = MV_OK;
    void* handle = NULL;
    MV_CC_DEVICE_INFO stDevInfo = {0};
    MV_GIGE_DEVICE_INFO stGigEDev = {0};

    // from the ImageProcess
    unsigned char * pData = NULL;        
    unsigned char *pDataForRGB = NULL;
    unsigned char *pDataForSaveImage = NULL;
 
    // Assume the IP address will not exceed 15 characters (including the terminating '\0')
    char nCurrentIp[16]; 
    // Safely copy the string to prevent overflow
    strncpy(nCurrentIp, camera_mv_ip, sizeof(nCurrentIp) - 1); 
    // Ensure the string is null-terminated
    nCurrentIp[sizeof(nCurrentIp) - 1] = '\0'; 
    char nNetExport[16];
    // std::strncpy(nNetExport, local_mv_ip_address.c_str(), sizeof(nNetExport) - 1);
    strncpy(nNetExport, the_default_ip, sizeof(nNetExport) - 1); 
    // Ensure the string is null-terminated, although strncpy usually does this (unless it truncates the string)
    nNetExport[sizeof(nNetExport) - 1] = '\0'; 
    // char nNetExport[]="***********00";
    unsigned int nIp1, nIp2, nIp3, nIp4, nIp;

    sscanf(nCurrentIp, "%d.%d.%d.%d", &nIp1, &nIp2, &nIp3, &nIp4);
    nIp = (nIp1 << 24) | (nIp2 << 16) | (nIp3 << 8) | nIp4;
    stGigEDev.nCurrentIp = nIp;

    sscanf(nNetExport, "%d.%d.%d.%d", &nIp1, &nIp2, &nIp3, &nIp4);
    nIp = (nIp1 << 24) | (nIp2 << 16) | (nIp3 << 8) | nIp4;
    stGigEDev.nNetExport = nIp;

    stDevInfo.nTLayerType = MV_GIGE_DEVICE;// en:Only support GigE camera
    stDevInfo.SpecialInfo.stGigEInfo = stGigEDev;

    do 
    {
        // en:Initialize SDK
		nRet = MV_CC_Initialize();
		if (MV_OK != nRet)
		{
			printf("  Initialize SDK fail! nRet [0x%x]\n", nRet);
            MvErrorMessage(nRet);
			break;
		}
        // en:Select device and create handle
        nRet = MV_CC_CreateHandle(&handle, &stDevInfo);
        if (MV_OK != nRet)
        {
            printf("  Create Handle fail! nRet[0x%x]\n", nRet);
            MvErrorMessage(nRet);
            break;
        }
        // en:Open device
        nRet = MV_CC_OpenDevice(handle);
        if (MV_OK != nRet)
        {
            printf("  Open Device fail! nRet [0x%x]\n", nRet);
            MvErrorMessage(nRet);
            break;
        }
        // en:Detection network optimal package size(It only works for the GigE camera)
		int nPacketSize = MV_CC_GetOptimalPacketSize(handle);
		if (nPacketSize > 0)
		{
			nRet = MV_CC_SetIntValueEx(handle,"GevSCPSPacketSize",nPacketSize);
			if(nRet != MV_OK)
			{
				printf("  Warning: Set Packet Size fail nRet [0x%x]!\n", nRet);
                MvErrorMessage(nRet);
			}
		}
		else
		{
			printf("  Warning: Get Packet Size fail nRet [0x%x]!\n", nPacketSize);
            MvErrorMessage(nRet);
		}

        nRet = MV_CC_SetEnumValue(handle, "TriggerMode", 0);
        if (MV_OK != nRet)
        {
            printf("  MV_CC_SetTriggerMode fail! nRet [%x]\n", nRet);
            MvErrorMessage(nRet);
            break;
        }
        // en:Get payload size
        MVCC_INTVALUE stParam;
        memset(&stParam, 0, sizeof(MVCC_INTVALUE));
        nRet = MV_CC_GetIntValue(handle, "PayloadSize", &stParam);
        if (MV_OK != nRet)
        {
            printf("  Get PayloadSize fail! nRet [0x%x]\n", nRet);
            MvErrorMessage(nRet);
            break;
        }
        printMVCCIntValue(&stParam);
        // en:Start grab image
        nRet = MV_CC_StartGrabbing(handle);
        if (MV_OK != nRet)
        {
            printf("  Start Grabbing fail! nRet [0x%x]\n", nRet);
            MvErrorMessage(nRet);
            break;
        }
        
        MV_FRAME_OUT_INFO_EX stImageInfo = {0};
        memset(&stImageInfo, 0, sizeof(MV_FRAME_OUT_INFO_EX));
        pData = (unsigned char *)malloc(sizeof(unsigned char) * stParam.nCurValue);
        if (NULL == pData)
        {
            printf("  pData is null\n");
            break;
        }
        unsigned int nDataSize = stParam.nCurValue;
        nRet = MV_CC_GetOneFrameTimeout(handle, pData, nDataSize, &stImageInfo, 1000);
        printFrameOutInfo(stImageInfo);
        if (nRet == MV_OK) {
            printf("  Now you GetOneFrame, Width[%d], Height[%d], nFrameNum[%d]\n\n", 
            stImageInfo.nWidth, stImageInfo.nHeight, stImageInfo.nFrameNum);
            // Check if the input data is valid.
            if (pData == nullptr || stImageInfo.nWidth <= 0 || stImageInfo.nHeight <= 0) {
                std::cerr << "Invalid input data or image dimensions" << std::endl;
                return -1;
            }

            pDataForSaveImage = (unsigned char*)malloc(stImageInfo.nWidth * stImageInfo.nHeight * 4 + 2048);
            if (NULL == pDataForSaveImage)
            {
                printf("  pDataForSaveImage is null\n");
                break;
            }
            // fill in the parameters of save image
            MV_SAVE_IMAGE_PARAM_EX stSaveParam;
            memset(&stSaveParam, 0, sizeof(MV_SAVE_IMAGE_PARAM_EX));                   
            // From top to bottom, the parameters are: output image format, pixel format of input data,
            // provided size of the output buffer, image width, image height, input data buffer,
            // output image buffer, JPG encoding quality
            // Top to bottom are： 
            stSaveParam.enImageType = MV_Image_Jpeg ; 
            // stSaveParam.enImageType = MV_Image_Bmp ;
            stSaveParam.enPixelType = stImageInfo.enPixelType; 
            stSaveParam.nBufferSize = stImageInfo.nWidth * stImageInfo.nHeight * 4 + 2048;
            stSaveParam.nWidth      = stImageInfo.nWidth; 
            stSaveParam.nHeight     = stImageInfo.nHeight; 
            stSaveParam.pData       = pData;
            stSaveParam.nDataLen    = stImageInfo.nFrameLen;
            stSaveParam.pImageBuffer = pDataForSaveImage;
            stSaveParam.nJpgQuality = jpg_quality;
            stSaveParam.iMethodValue = 1;
            printSaveImageParam(stSaveParam);
            nRet = MV_CC_SaveImageEx2(handle, &stSaveParam);
            if(MV_OK != nRet)
            {
                printf("  failed in MV_CC_SaveImage,nRet[%x]\n", nRet);
                MvErrorMessage(nRet);
                break;
            }

            std::string toc_image_name_jpg ,image_name_jpg, toc_image_s;
            // std::string image_name_bmp, toc_image_name_bmp;
            image_name_jpg = image_name;
            // image_name_bmp = "image.bmp";
            toc_image_s = ORIG_TOC;
            toc_image_name_jpg = toc_image_s + image_name_jpg;
            // toc_image_name_bmp = toc_image_s + image_name_bmp;
            // FILE* fp = fopen(toc_image_name_bmp.c_str(), "wb");
            FILE* fp = fopen(toc_image_name_jpg.c_str(), "wb");
            if (NULL == fp)
            {
                printf("  fopen failed\n");
                break;
            }
            fwrite(pDataForSaveImage, 1, stSaveParam.nImageLen, fp);
            fclose(fp);
            printf("  save image succeed\n");

            ivalue = 1;
            // ivalue = btoj;
            break; 
        }else
		{
			printf("  No data[%x]\n", nRet);
		}
        // end grab image
        nRet = MV_CC_StopGrabbing(handle);
        if (MV_OK != nRet)
        {
            printf("  MV_CC_StopGrabbing fail! nRet [%x]\n", nRet);
            MvErrorMessage(nRet);
            break;
        }
        // destroy handle
        nRet = MV_CC_DestroyHandle(handle);
        if (MV_OK != nRet)
        {
            printf("  MV_CC_DestroyHandle fail! nRet [%x]\n", nRet);
            break;
        }
        handle = NULL;
    } while (0);

    if (handle != NULL)
    {
        MV_CC_DestroyHandle(handle);
        handle = NULL;
    }

    if (pData)
    {
        free(pData);	
        pData = NULL;
    }
    if (pDataForRGB)
    {
        free(pDataForRGB);
        pDataForRGB = NULL;
    }
    if (pDataForSaveImage)
    {
        free(pDataForSaveImage);
        pDataForSaveImage = NULL;
    }
    // en:Finalize SDK
	MV_CC_Finalize();
    // PressEnterToExit();
    printf("  exit capture.\n");
    printf("  \n");
    return ivalue; 
}


int main(int argc, char *argv[]){
    // current version
    std::cout << "  current version: " << BAN_BEN << "\n";
    auto current_time_start = std::chrono::system_clock::now();
    std::time_t now_start = std::chrono::system_clock::to_time_t(current_time_start);
    std::stringstream filetime_start ;
    filetime_start << std::put_time(std::localtime(&now_start), "%Y%m%d%H%M%S"); 
    std::cout << "  current_time_start content: " << filetime_start.str() << std::endl;
    // Create Table of Contents 
    boost::filesystem::path dir_path_toc(ORIG_TOC);
    if (!boost::filesystem::exists(dir_path_toc)) {
        if (!boost::filesystem::create_directories(dir_path_toc)) {
            std::cout << "  Unable to create directory  " << dir_path_toc << std::endl;
        }else {
            std::cout << "  Directory created at " << dir_path_toc << std::endl;
        }
    }else {
        std::cout << "  Directory already exists at " << dir_path_toc << std::endl;
    }
    // start the program
    if (argc ==1 ) {
        ViewCommandAll();
    }else if (argc == 5 || argc == 6 || argc == 7){
        const char *the_default_ip = argv[1];
        const char *camera_mv_ip = argv[2];
        const char *image_name = argv[3];
        int flag = std::stoi(argv[4]); 
        double number = 0;
        number = std::atof(argv[5]);
        unsigned int jpg_quality = static_cast<int>(number * 100);
        jpg_quality = JpgQualityRangeNumerical(jpg_quality);
        // capture an image 
        int value = CaptureAnImage_mv_ip(the_default_ip, camera_mv_ip, image_name, jpg_quality);
        // read the current temperature
        float current_temperature = ReadingTheTemperature();
        if (flag == 1){ 
            if (value == 0){
                std::string image_name_e = CaptureFailedRecord(image_name);
                image_name = image_name_e.c_str();
                std::cout << "  Result: '" << image_name << "'" << std::endl;
            }
            std::cout << "  write data to file " << ORIG_FILE << std::endl;
            WriteDataToFlie(image_name, current_temperature);
        }else if (flag == 0){
            std::cout << "  No write data to file " << ORIG_FILE << std::endl;
        }
        std::string image_directory = ORIG_TOC;
        int max_images = 1000;
        if (argc == 7) {
            max_images = std::stoi(argv[6]);
        }
        std::string data_file_name = ORIG_FILE;
        data_file_name = image_directory + data_file_name;
        std::string file_extension = ".jpg";
        deleteOldImages(image_directory, max_images, data_file_name, file_extension);
    }
    
    return 0;
}

// replace the string with spaces
std::string CaptureFailedRecord(const char *image_name) {
    std::string image_name_e;
    size_t length = std::strlen(image_name);
    std::cout << "  The number of characters in '" << image_name << "' is: " << length << std::endl;    
    for (size_t i = 0; i < length; i++) {
        image_name_e += " ";
    }    
    return image_name_e;  // 
}

// JPG image quality range.
unsigned int JpgQualityRangeNumerical(unsigned int jpg_quality) {
    if (jpg_quality < 51){
        jpg_quality = 51;
    }else if (jpg_quality > 99){
        jpg_quality = 99;
    }
    return jpg_quality;
}

// meaning of error code
void MvErrorMessage(int nRet) {
    // Create an unordered map to store error codes and their corresponding error messages.
    static const std::unordered_map<int, std::string> errorMessages = {
        // Definition of General error code: Range from 0x80000000 to 0x800000FF
        {0x80000000, "Error or invalid handle."},
        {0x80000001, "Not supported function."},
        {0x80000002, "Buffer overflow."},
        {0x80000003, "Function calling order error."},
        {0x80000004, "Incorrect parameter."},
        {0x80000006, "Applying resource failed."},
        {0x80000007, "No data."},
        {0x80000008, "Precondition error, or running environment changed."},
        {0x80000009, "Version mismatches."},
        {0x8000000A, "Insufficient memory."},
        {0x8000000B, "Abnormal image, maybe incomplete image because of lost packet."},
        {0x8000000C, "Load library failed."},
        {0x8000000D, "No Avaliable Buffer."},
        {0x8000000E, "Encryption error."},
        {0x8000000f, "open file error."},
        {0x80000010, "Buffer already in use."},
        {0x80000011, "Buffer address invalid."},
        {0x80000012, "Buffer alignmenterror error."},
        {0x80000013, "Insufficient cache count."},
        {0x80000014, "Port is in use."},
        {0x80000015, "Decoding error (SDK verification image exception)."},
        {0x80000016, "Image size exceeds unsigned int return, interface not supported."},
        {0x800000FF, "Unknown error."},
        // GenICam Series Error Codes: Range from 0x80000100 to 0x800001FF
        {0x80000100, "General error."},
        {0x80000101, "Illegal parameters."},
        {0x80000102, "The value is out of range."},
        {0x80000103, "Property."},
        {0x80000104, "Running environment error."},
        {0x80000105, "Logical error."},
        {0x80000106, "Node accessing condition error."},
        {0x80000107, "Timeout."},
        {0x80000108, "Transformation exception."},
        {0x800001FF, "GenICam unknown error."},\
        // GigE_STATUS Error Codes: Range from 0x80000200 to 0x800002FF
        {0x80000200, "The command is not supported by device."},
        {0x80000201, "The target address being accessed does not exist."},
        {0x80000202, "The target address is not writable."},
        {0x80000203, "No permission."},
        {0x80000204, "Device is busy, or network disconnected."},
        {0x80000205, "Network data packet error."},
        {0x80000206, "Network error."},
        {0x8000020E, "Current Mode Not Support Modify Ip."},
        {0x8000020F, "SwitchKey error."},
        {0x80000221, "Device IP conflict."},
        // USB_STATUS Error Codes: Range from 0x80000300 to 0x800003FF
        {0x80000300, "Reading USB error."},
        {0x80000301, "Writing USB error."},
        {0x80000302, "Device exception."},
        {0x80000303, "GenICam error."},
        {0x80000304, "Insufficient bandwidth."},
        {0x80000305, "Driver mismatch or unmounted drive."},
        {0x800003FF, "USB unknown error"},
        // Upgrade Error Codes: Range from 0x80000400 to 0x800004FF
        {0x80000400, "Firmware mismatches."},
        {0x80000401, "Firmware language mismatches."},
        {0x80000402, "Upgrading conflicted (repeated upgrading requests during device upgrade)."},
        {0x80000403, "Camera internal error during upgrade."},
        {0x800004FF, "Unknown error during upgrade."},
    };
    // Look up the error message corresponding to the error code.
    const auto iter = errorMessages.find(nRet);
    if (iter != errorMessages.end()) {
        // If found, print the error message.
        std::cout << "  " << iter->second << std::endl;
    } else {
        // If not found, print an unknown error message, or no errors.
        std::cout << "  Unknown error, or no error." << std::endl;
    }
}


// When exceeding the maximum limit, purge the oldest data entries
void deleteOldImages(std::string image_directory, int max_images, std::string data_file_name, std::string file_extension) {
    // Normalize file extension (automatically prepend dot if missing)
    if (!file_extension.empty() && file_extension[0] != '.') {
        file_extension = "." + file_extension;
    }

    // Collect image files along with their modification times
    std::vector<std::pair<std::filesystem::file_time_type, std::string>> images_with_time;
    for (const auto& entry : std::filesystem::directory_iterator(image_directory)) {
        try {
            if (entry.is_regular_file() && 
                entry.path().extension() == file_extension) {
                images_with_time.emplace_back(
                    std::filesystem::last_write_time(entry),
                    entry.path().filename().string()
                );
            }
        } catch (const std::exception& e) {
            std::cout << "  Error accessing file: " << e.what() << std::endl;
        }
    }

    // Sort files by modification time (oldest first)
    std::sort(images_with_time.begin(), images_with_time.end());

    // Extract filenames from sorted list
    std::vector<std::string> image_filenames;
    for (const auto& img : images_with_time) {
        image_filenames.push_back(img.second);
    }

    // Calculate number of files to delete
    int files_to_delete = image_filenames.size() - max_images;
    if (files_to_delete <= 0) {
        std::cout << "  No files need to be deleted." << std::endl;
        return;
    }

    // Delete oldest files and record their names
    std::vector<std::string> deleted_files;
    for (int i = 0; i < files_to_delete; ++i) {
        std::string full_path = image_directory + "/" + image_filenames[i];
        try {
            std::filesystem::remove(full_path);
            deleted_files.push_back(image_filenames[i]);
            std::cout << "  Deleted: " << image_filenames[i] << std::endl;
        } catch (const std::exception& e) {
            std::cout << "  Error deleting file: " << e.what() << std::endl;
        }
    }

    // Update data file
    if (!data_file_name.empty()) {
        // Use lock_guard for automatic mutex management (RAII pattern)
        std::lock_guard<std::mutex> lock(data_file_mutex);
        
        // Container to store lines to keep
        std::vector<std::string> lines_to_keep;
        
        // Read original file
        std::ifstream data_file(data_file_name);
        
        if (data_file.is_open()) {
            std::string line;
            while (std::getline(data_file, line)) {
                bool keep_line = true;
                
                // Check if current line contains any deleted filename
                for (const auto& deleted : deleted_files) {
                    if (line.find(deleted) != std::string::npos) {
                        keep_line = false;
                        lines_to_keep.clear(); // Clear previous entries as they're older
                        break;
                    }
                }
                
                if (keep_line) {
                    lines_to_keep.push_back(line);
                }
            }
            data_file.close();
        } else {
            std::cout << "  Error opening data file for reading." << std::endl;
        }

        // Write updated file
        std::ofstream new_data_file(data_file_name);
        if (new_data_file.is_open()) {
            for (const auto& line : lines_to_keep) {
                new_data_file << line << std::endl;
            }
            new_data_file.close();
            std::cout << "  Data file updated." << std::endl;
        } else {
            std::cout << "  Error opening data file for writing." << std::endl;
        }
    }
}



