Installation Packages Corresponding to Different Operating Systems
 ARM (64-bit):
    MVS-2.1.2_aarch64_20221024.deb
    MVS-2.1.2_aarch64_20221024.tar.gz
 ARM (32-bit):
    MVS-2.1.2_armhf_20221024.deb
    MVS-2.1.2_armhf_20221024.tar.gz
    MVS-2.1.2_arm-none_20221024.tar.gz
 X86 (64-bit):
    MVS-2.1.2_x86_64_20221024.deb
    MVS-2.1.2_x86_64_20221024.tar.gz
 X86 (32-bit):
    MVS-2.1.2_i386_20221024.deb
    MVS-2.1.2_i386_20221024.tar.gz
	
Note:
1. Select the corresponding installation pacakage according to your system name. You can enter "uname -a" in the terminal to check the system name. For example, if "aarch64" is included in the output, choose the aarch64 package.
2. The .deb pacakge needs to be installed with dpkg command, which is mainly used in systems like Ubuntu.
3. The .tar.gz package is a compressed package. You need to first unzip it with tar command and then install it with setup.sh script. 



安装包对应的操作系统
 arm架构64位系统：
    MVS-2.1.2_aarch64_20221024.deb
    MVS-2.1.2_aarch64_20221024.tar.gz
arm架构32位系统：
    MVS-2.1.2_armhf_20221024.deb
    MVS-2.1.2_armhf_20221024.tar.gz
    MVS-2.1.2_arm-none_20221024.tar.gz
x86架构64位系统：
    MVS-2.1.2_x86_64_20221024.deb
    MVS-2.1.2_x86_64_20221024.tar.gz
x86架构32位系统：
    MVS-2.1.2_i386_20221024.deb
    MVS-2.1.2_i386_20221024.tar.gz
   
说明：
1. 根据系统名称选择相对应的安装包：在终端中输入“uname -a”命令，根据输出的信息选择安装包，例如：输出的信息包含aarch64就选择aarch64的安装包；
2. .deb安装包通过dpkg命令安装，主要应用于ubuntu等系统；
3. .tar.gz安装包是一个压缩包，通tar命令解压后，再执行setup.sh脚本进行安装。