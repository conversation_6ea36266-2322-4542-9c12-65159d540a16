#include <opencv2/opencv.hpp>
#include <chrono>
#include <thread>
#include <filesystem>
#include <stdio.h>
#include <modbus/modbus.h>
#include <errno.h> 
#include <modbus/modbus-rtu.h>
#include <stdlib.h>
#include <unistd.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <iostream>
#include <fstream>
#include <sstream>
#include <vector>
#include <filesystem>
#include <system_error>
#include <boost/filesystem.hpp>
#include <boost/system/error_code.hpp>
//启动文件
#define START_FILE "/home/<USER>/orig/startfile.txt"
//从机地址
#define MODBUS_IP 0x01
//需要循环读取的寄存器数量
#define HEXS_NUM 1
//需要读取的寄存器地址
uint16_t addr[] = {60};
//互斥锁 每次读取对文件写入数据 每次删除部分数据也会重新对文件写入数据
std::mutex data_file_mutex;

// 子函数：从文件路径中提取目录路径  
std::string extractDirectoryPath(const std::string& filepath) {  
    // 查找最后一个'/'的位置  
    size_t slash_pos = filepath.rfind('/');  
  
    // 检查是否找到了'/'  
    if (slash_pos == std::string::npos) {  
        // 如果没有找到'/'，则返回一个空字符串或适当的错误信息  
        // 这里我们选择返回一个空字符串  
        return "";  
    } else {  
        // 提取并返回目录路径（不包括文件名）  
        return filepath.substr(0, slash_pos);  
    }  
}
// 子函数：获取文件路径的目录部分  
std::string getDirectoryPath(const std::string& filepath) {  
    std::filesystem::path path(filepath);  
    return path.parent_path().string();  
} 

// 函数：如果路径不以斜杠结尾，则添加一个斜杠  
std::string normalizePath(const std::string& path) {  
    if (!path.empty() && path.back() != '/') {  
        return path + '/';  
    }  
    return path;  
}
// 函数：获取文件的后缀名  
std::string getFileExtension(const std::string& filename) {  
    size_t dot_pos = filename.rfind('.');  
    if (dot_pos == std::string::npos) {  
        // 如果没有找到'.'，则返回一个空字符串表示没有后缀名  
        return "";  
    } else {  
        // 提取并返回后缀名  
        return filename.substr(dot_pos + 1);  
    }  
} 
// 函数：获取文件的基本名（不含扩展名）  
std::string getBaseFilename(const std::string& filename) {    
    // 查找最后一个点的位置    
    size_t dot_pos = filename.find_last_of('.');    
    
    // 如果找到了点，并且点不是文件名的第一个字符    
    if (dot_pos != std::string::npos && dot_pos != 0) {    
        // 截取最后一个点之前的部分作为文件名（不含扩展名）    
        std::string base_filename = filename.substr(0, dot_pos);    
        return base_filename; // 返回不含扩展名的文件名  
    } else {    
        // 如果没有找到点，或者点是文件名的第一个字符，则返回整个文件名  
        return filename; // 可能是没有扩展名的文件名  
    }    
} 
// 子函数定义  
std::string parseString(const std::string& input) {  
    size_t at_pos = input.find('@');  
    size_t colon_pos = input.rfind(':');  
  
    if (at_pos == std::string::npos || colon_pos == std::string::npos || at_pos >= colon_pos) {  
        std::cerr << "Error: Invalid input string. It must contain at least one '@' and one ':'.\n";  
        return ""; // 返回空字符串表示错误  
    }  
  
    return input.substr(at_pos + 1, colon_pos - at_pos - 1);  
}

bool Confile(const std::string& readfilename, std::vector<std::string>& numbers){    
    std::ifstream file(readfilename);
    if (!file.is_open()) {
        std::cerr << "无法打开文件：" << readfilename << std::endl;
        return false;
    }
    std::string line;
    while (std::getline(file, line)) {
        std::istringstream iss(line);
        std::string number;
        while (iss >> number) {
            numbers.push_back(number);
        }
    }
    file.close();
    return true;

}

bool ensure_directory_exists(const std::string& dir_path) {  
    boost::filesystem::path path(dir_path);  
    if (!boost::filesystem::exists(path)) {  
        if (!boost::filesystem::create_directories(path)) {  
            std::cerr << "无法创建目录 " << dir_path << std::endl;  
            return false; // 返回 false 表示创建目录失败  
        }  
    }  
    return true; // 返回 true 表示目录已存在或成功创建  
}

void Wrimodbus(modbus_t *ctx, uint16_t reg_addr, int reg_value){       
    // 写入数值到寄存器
    if (modbus_write_register(ctx, reg_addr, reg_value) == -1) {
        fprintf(stderr, "写入寄存器失败: %s\n", modbus_strerror(errno));
    }
}

void deleteOldImages(std::string image_directory, int frehz, int max_images, std::string data_file_name_s,std::string file_extension) {
    
    if (true) {     
        // 如果用户输入了前导'.'，则去除它  
        if (file_extension.size() > 0 && file_extension[0] == '.') {  
            file_extension = file_extension.substr(1);  
        } 
        // 使用 std::filesystem::directory_iterator 遍历目录
        std::vector<std::string> images;
        for (const auto& entry : std::filesystem::directory_iterator(image_directory)) {  
        // 检查文件扩展名是否与指定后缀匹配  
            if (entry.is_regular_file() && entry.path().extension() == "." + file_extension) {  
                //images.push_back(entry.path());  
                images.push_back(entry.path().string()); // 显式转换为 std::string
            }  
        }  
        if (images.size() > max_images) {
            std::sort(images.begin(), images.end());
            std::filesystem::remove(images[0]);
            // 获取要删除的图片的文件名
            std::string deleted_filename = images[0].substr(image_directory.length());
            // 获取要删除的图片对应的日期时间
            std::string deleted_datetime = deleted_filename.substr(0, deleted_filename.find('.'));
            // 构造要删除的数据行
            //std::string deleted_data_line = deleted_datetime + "  " + deleted_filename;
            std::string deleted_data_line = deleted_datetime;

            std::vector<std::string> lines;//写入 // 存储要保留的行
            //std::vector<std::string> lines_to_keep; // 存储要保留的行
            std::string line;//读出
            std::size_t total_lines = 0; // 新增：用于计算文件中的行数
            std::size_t deleted_line_num = 0; // 用于记录被删除的行号
            //从数据文件中读取所有行
            std::ifstream data_file(data_file_name_s);
           
            if (data_file.is_open()) {
                //std::lock_guard<std::mutex> lock(data_file_mutex); // 加锁
                // std::vector<std::string> lines;
                // std::string line;
                // 旧版本，每次只删除图片对应的 1
                while (std::getline(data_file, line)) {
                    lines.push_back(line);
                    ++total_lines;// 新增：每读取一行，递增计数器 
                    if (line.find(deleted_data_line) != std::string::npos) {  
                        deleted_line_num = total_lines; // 记录被删除的行号  
                        // 注意：这里我们没有立即从lines中删除这行，因为我们还在遍历它  
                        // 现在这样仅标记它  
                    } 
                }
                data_file.close();

                // 重新写入数据文件，排除已删除的数据行及其之前的所有行  
                std::ofstream new_data_file(data_file_name_s);  
                if (new_data_file.is_open()) {
                    std::lock_guard<std::mutex> lock(data_file_mutex); // 加锁
                    for (std::size_t i = deleted_line_num ; i < lines.size(); i++) {  
                        new_data_file << lines[i] << std::endl;  
                    } 
                   
                    new_data_file.close();
                } else {
                    std::cerr << "无法打开文件 " << data_file_name_s << std::endl;
                }
            } else {
                std::cerr << "无法打开文件 " << data_file_name_s << std::endl;
            }
        } else {
            std::sort(images.begin(), images.end());
            //std::filesystem::remove(images[0]);
            // 获取要删除的图片的文件名
            std::string deleted_filename = images[0].substr(image_directory.length());
            // 获取要删除的图片对应的日期时间
            std::string deleted_datetime = deleted_filename.substr(0, deleted_filename.find('.'));
            // 构造要删除的数据行
            std::string deleted_data_line = deleted_datetime;
            std::vector<std::string> lines;//写入 // 存储要保留的行
            std::string line;//读出
            std::size_t total_lines = 0; // 新增：用于计算文件中的行数
            std::size_t deleted_line_num = 0; // 用于记录第一张图片对应的行号 
            //从数据文件中读取所有行
            std::ifstream data_file(data_file_name_s);
            if (data_file.is_open()) {
                // 旧版本，每次只删除图片对应的 1
                while (std::getline(data_file, line)) {
                    lines.push_back(line);
                    ++total_lines;// 新增：每读取一行，递增计数器 
                    if (line.find(deleted_data_line) != std::string::npos) {  
                        deleted_line_num = total_lines; // 记录第一张图片对应的行号  
                    } 
                }
                data_file.close();
                // 重新写入数据文件，  
                std::ofstream new_data_file(data_file_name_s);  
                if (new_data_file.is_open()) {
                    std::lock_guard<std::mutex> lock(data_file_mutex); // 加锁
                    for (std::size_t i = deleted_line_num -1; i < lines.size(); i++) {  
                        new_data_file << lines[i] << std::endl;  
                    }        
                    new_data_file.close();
                } else {
                    std::cerr << "无法打开文件 " << data_file_name_s << std::endl;
                }
            } else {
                std::cerr << "无法打开文件 " << data_file_name_s << std::endl;
            }
        }

        // 延迟一段时间再进行下次检查和删除
        //std::this_thread::sleep_for(std::chrono::milliseconds(frehz));

    }
}

int main(int argc, char *argv[]) {
    std::string camera_ip;
    camera_ip = std::string(argv[1]);    
    
    std::string image_directory_argv;
    image_directory_argv = std::string(argv[2]);
    image_directory_argv = normalizePath(image_directory_argv);

    std::string file_extension_argv;
    file_extension_argv = std::string(argv[3]);

    //获取启动文件信息
    std::string startfilename = START_FILE;
    std::vector<std::string> start_file;
    if (!Confile(startfilename, start_file)) {
        return 1;
    }
    std::cout << "start_file[0] = " << start_file[0] << "\n";//预定设置文件
    std::cout << "start_file[1] = " << start_file[1] << "\n";//摄像头rtsp地址文件
    std::cout << "start_file[2] = " << start_file[2] << "\n";//数据保存文件
    std::cout << "start_file[3] = " << start_file[3] << "\n";//照片保存目录
    std::cout << "start_file[4] = " << start_file[4] << "\n";//照片保存格式
    std::cout << "start_file[5] = " << start_file[5] << "\n";//那个485口
    //std::cout << "start_file[5] = " << start_file[6] << "\n";//那个485口，补光灯
    //获取配置文件信息 numbers[]数组
    std::string setfilename = start_file[0];
    std::vector<std::string> numbers;
    if (!Confile(setfilename, numbers)) {
        return 1;
    }
    for (size_t numbers_i = 0; numbers_i < numbers.size(); numbers_i++) {  
        std::cout << numbers[numbers_i] << " ";  
    } 
    std::cout << "\n";
    //获取摄像头rtsp camera_rtsp[]数组
    std::string camerafilename = start_file[1];    
    std::vector<std::string> camera_rtsp;
    if (!Confile(camerafilename, camera_rtsp)) {
        return 1;
    }
    for (const auto& camera_rtsp_url : camera_rtsp) {  
        std::cout << camera_rtsp_url << "\n";  
    } 
    std::string camera_rtsp_ip;
    //camera_rtsp_ip = "rtsp://admin:Zhong123450@" + camera_ip +":554/cam/realmonitor?channel=1&subtype=0";
    //int camera_rtsp_sl = sizeof(camera_rtsp)/sizeof(camera_rtsp[0]);
    size_t camera_rtsp_sl = camera_rtsp.size(); 
    if (camera_rtsp_sl == 1){
        camera_rtsp_ip = camera_rtsp[0];
        std::cout << "camera_rtsp_ip = " << camera_rtsp_ip << std::endl;
    }else if (camera_rtsp_sl == 2){
        camera_rtsp_ip = camera_rtsp[0] + camera_ip + camera_rtsp[1];
        std::cout << "camera_rtsp_ip = " << camera_rtsp_ip << std::endl;
    }

    //寄存器个数
    int addrs = sizeof(addr) / sizeof(addr[0]);
    //modbus配置
    int i;
    modbus_t *ctx;
    uint16_t reg[addrs];//保存读取到的数值
    //double zreg[addrs];
    float zreg[addrs];
    // 初始化modbus上下文
    ctx = modbus_new_rtu(start_file[5].c_str(), 9600, 'N', 8, 1);
    if (ctx == NULL) {
        fprintf(stderr, "无法创建modbus上下文\n");
    }
    // 设置从站地址
    modbus_set_slave(ctx, MODBUS_IP);
    // 设置校验码为CRC-16
    modbus_rtu_set_serial_mode(ctx, MODBUS_RTU_RS485);
    // 打开串口设备
    if (modbus_connect(ctx) == -1) {
        fprintf(stderr, "无法打开串口设备\n");
        //释放资源
        //modbus_free(ctx);
        //return -1;
    }

    //设置是否开启温控  7开启  5关闭
    //if ( std::stoi(numbers[0]) == 7 || std::stoi(numbers[0]) == 5 ) {
    // int reg_value1 = std::stoi(numbers[0]);
    // if ( reg_value1 == 7 || reg_value1 == 5 ) {
    //     uint16_t reg_addr1 = 5;
    //     //int reg_value1 = std::stoi(numbers[0]);
    //     Wrimodbus(ctx, reg_addr1, reg_value1);
    // }
    

    //零下不重设
    // if ( std::stoi(numbers[1]) >= 0 )
    // {
    //     //设置温控目标值
    //     uint16_t reg_addr2 = 11;
    //     //int reg_value2 = std::stof(numbers[1])*100;
    //     int reg_value2 = static_cast<int>(std::stof(numbers[1])*10);// 11寄存器的值为3位，即显示550为55摄氏度
    //     Wrimodbus(ctx, reg_addr2, reg_value2);
    // }

    // 创建目录
    //ensure_directory_exists(image_directory_argv);
    boost::filesystem::path dir_path(image_directory_argv);
    if (!boost::filesystem::exists(dir_path)) {
        if (!boost::filesystem::create_directories(dir_path)) {
            std::cerr << "无法创建目录 " << image_directory_argv << std::endl;
            //return;
        }
    }
        // //配置补光灯串口设置
        // modbus_t *ctxbgd;      
        // ctxbgd = modbus_new_rtu("/dev/ttyS3", 38400, 'N', 8, 1);  
        // if (ctxbgd == NULL) {  
        //     std::cerr << "Unable to create the context" << std::endl;              
        // } 
        // // 设置从站地址（Slave ID）  
        // modbus_set_slave(ctxbgd, 1); // 假设从站地址为1  
        // // 连接Modbus RTU服务器（实际上是打开串行端口）  
        // if (modbus_connect(ctxbgd) == -1) {  
        //     std::cerr << "Connection failed: " << modbus_strerror(errno) << std::endl;          
        // } 

    // 设置截图和读取间隔时间（秒）帧率
    int interval = std::stoi(numbers[2]); // 1
    int intervals = 0;
    // 记录上一次截图的时间
    auto last_time = std::chrono::system_clock::now();
          
    // 创建并启动清理图片的线程 目标目录 频率 图片数 数据保存文件 图片格式
    std::string image_directory_a = start_file[3];
    if (argc >= 3){
        image_directory_a = image_directory_argv;
        std::cout << "image_directory_a bian : " ;
    }
    std::cout << "image_directory_a = " << image_directory_a << std::endl;
    int frehz = std::stoi(numbers[2]);
    int max_images = std::stoi(numbers[3]);
    std::string file_extension_a = start_file[4];
    if (argc >= 4){
        file_extension_a = getFileExtension(file_extension_argv);
        std::cout << "file_extension_a bian : " ;
    }
    std::cout << "file_extension_a = " << file_extension_a << std::endl;
    std::thread delete_thread(deleteOldImages, image_directory_a, frehz, max_images, start_file[2], file_extension_a);


    cv::VideoCapture cap;
    cap.open(camera_rtsp_ip);
    if (true) {
       
        // 检查是否达到截图间隔时间     
        auto current_time = std::chrono::system_clock::now();
        std::chrono::duration<double> elapsed_seconds = current_time - last_time;
        //补光先开启预热10秒
        // if (elapsed_seconds.count() >= intervals) {
        //     Wrimodbus(ctxbgd, 0, 1);
        // }
        if (elapsed_seconds.count() >= intervals) {
            // 读取当前帧
            if (!cap.isOpened()) {
                std::cout << "Failed to open camera, " ;
                // 先释放摄像头并关闭所有窗口
                cap.release();
                cap.open(camera_rtsp[0]);
                // 检查此次摄像头是否打开成功
                if (!cap.isOpened()) {
                    std::cout << "......" << std::endl;
                    // 先释放摄像头并关闭所有窗口
                    cap.release();
                    //continue; // 跳过当前迭代，继续下一次循环
                }else{
                    std::cout << " reopen camera." << std::endl;
                }
            }else{
                std::cout << " capture camera." << std::endl;
            }    
            cv::Mat frame;
            cap.read(frame);

            // 检查当前帧是否读取成功
            if (frame.empty()) {
                std::cout << "Failed to read frame." << std::endl;
                //break;
            }    
            // 生成截图文件名
            // std::time_t now = std::chrono::system_clock::to_time_t(current_time);
            // std::stringstream filetime, filepath, filename;
            // filetime << std::put_time(std::localtime(&now), "%Y%m%d%H%M%S");
            // filepath << filetime.str() << start_file[4].c_str();
            // filename << start_file[3] << filepath.str();   
            std::string filetime, filepath, filename;
            filetime = file_extension_argv.substr(0, 14);
            filepath = file_extension_argv;
            filename = image_directory_argv + filepath;
            // 保存截图
            cv::imwrite(filename, frame);
            // 更新上一次截图的时间
            intervals = interval;
            last_time = current_time;
            // 读取寄存器数据
            for (i = 0; i < addrs ; i++) {
                if (modbus_read_registers(ctx, addr[i], 1, &reg[i]) == -1) {
                    std::cerr << "无法读取寄存器 " << addr[i] << " 的数据" << std::endl;
                    //break;
                    zreg[i] = 0.0;
                    std::cout << std::fixed << std::setprecision(1) << "寄存器 " << addr[i] << " 的值: " << zreg[i] << std::endl;                    
                } else {  
                    zreg[i] = reg[i]/100.0;
                    std::cout << std::fixed << std::setprecision(1) << "寄存器 " << addr[i] << " 的值: " << zreg[i] << std::endl;  
                } 
                
                //std::cout << "寄存器 " << addr[i] << " 的值: " << (reg[i]/10)/10.0 << std::endl;
            }
            //            
            // 写入数据到文件
            std::stringstream data_ss;
            data_ss << std::fixed << std::setprecision(1) << filetime << "  " << filepath;
            for (i = 0; i < addrs ; i++) {
                data_ss << "  " << zreg[i];
            }
            data_ss << std::endl;

            std::ofstream data_file(start_file[2], std::ios::app);
            if (data_file.is_open()) {
                std::lock_guard<std::mutex> lock(data_file_mutex); // 加锁
                data_file << data_ss.str();
                data_file.close();
            } else {
                std::cerr << "无法打开文件" << std::endl;
            }
    
            // 延迟一段时间，以控制帧率
            //std::this_thread::sleep_for(std::chrono::milliseconds((interval*1000)-10));
            //完成截图后关闭灯
            // Wrimodbus(ctxbgd, 0, 0);
        } else {
            // 延迟一段时间，以控制帧率
            std::this_thread::sleep_for(std::chrono::milliseconds(interval));
        }
        // 释放摄像头并关闭所有窗口
        // cap.release();
        // cv::destroyAllWindows();

    }

    // 等待清理图片的线程结束
    delete_thread.join();

    // 关闭modbus连接
    modbus_close(ctx);
    modbus_free(ctx);
    // modbus_close(ctxbgd);
    // modbus_free(ctxbgd);
    // 释放摄像头并关闭所有窗口
    cap.release();
    cv::destroyAllWindows();

    return 0;
}


