#include <iostream>   
#include <modbus/modbus.h>
#include <errno.h> 
#include <modbus/modbus-rtu.h>
void Wrimodbus(modbus_t *ctx, uint16_t reg_addr, int reg_value){       
    // 写入数值到寄存器
    if (modbus_write_register(ctx, reg_addr, reg_value) == -1) {
        fprintf(stderr, "写入寄存器失败: %s\n", modbus_strerror(errno));
        //modbus_free(ctx);
        //return ;
    }
    //printf("成功写入数值到寄存器\n");
}

int main(int argc, char *argv[]) {  
    // int bgdkg = 0;
    // if (argc == 2) {  
    //     bgdkg = std::stoi(argv[1]);
    // }
    int bgd = 0;
    if (argc == 2) {  
        bgd = std::stoi(argv[1]);
    }
    int kg = 0;
    if (argc == 3) {
        bgd = std::stoi(argv[1]);  
        kg = std::stoi(argv[2]);
    }
    modbus_t *ctxbgd;  
     
    // ctxbgd = modbus_new_rtu("/dev/ttyS3", 38400, 'N', 8, 1);  

            const char *serial_port = argv[3];
            int baudrate = 38400;
            char parity = 'N';
            int data_bits = 8;
            int stop_bits = 1;
        
            // modbus_t *ctxbgd;
            ctxbgd = modbus_new_rtu(serial_port, baudrate, parity, data_bits, stop_bits);

    if (ctxbgd == NULL) {  
        std::cerr << "Unable to create the context" << std::endl;  
         
    } 
    // 设置从站地址（Slave ID）  
    modbus_set_slave(ctxbgd, 1); // 假设从站地址为1  
  
    // 连接Modbus RTU服务器（实际上是打开串行端口）  
    if (modbus_connect(ctxbgd) == -1) {  
        std::cerr << "Connection failed: " << modbus_strerror(errno) << std::endl;  
          
    }   

    //开关灯
    //Wrimodbus(ctxbgd, 0, bgdkg);
    if (argc >= 3 && (kg == 0|| kg == 1)) {  
        Wrimodbus(ctxbgd, static_cast<uint16_t>(bgd), kg);
    }
     
        uint16_t addr[] = {static_cast<uint16_t>(bgd)};
        int addrs = sizeof(addr) / sizeof(addr[0]);
        uint16_t reg[addrs];//保存读取到的数值
        float zreg[addrs];
        for (int i = 0; i < 1 ; i++) {
            if (modbus_read_registers(ctxbgd, addr[i], 1, &reg[i]) == -1) {
                std::cerr << "无法读取寄存器 " << addr[i] << " 的数据" << std::endl;
                zreg[i] = reg[i];
                std::cout << "寄存器 " << addr[i] << " 的值: " << zreg[i] << std::endl;                    
            }  else {  
                //zreg[i] = reg[i]/100.0;
                zreg[i] = reg[i];
                std::cout << "寄存器 " << addr[i] << " 的值: " << zreg[i] << std::endl;  
            }                 
        } 
    




    modbus_close(ctxbgd);
    modbus_free(ctxbgd);

    return 0;  
}    

