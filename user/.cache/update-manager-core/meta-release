Dist: warty
Name: Warty Warthog
Version: 04.10
Date: Wed, 20 Oct 2004 07:28:17 UTC
Supported: 0
Description: This is the warty warthog release
Release-File: http://old-releases.ubuntu.com/ubuntu/dists/warty-updates/Release

Dist: hoary
Name: Hoary  Hedgehog
Version: 05.04
Date: Fri, 08 Apr 2005 08:18:19 UTC
Supported: 0
Description: This is the Hoary Hedgehog release
Release-File: http://old-releases.ubuntu.com/ubuntu/dists/hoary-updates/Release

Dist: breezy
Name: <PERSON><PERSON> Badger
Version: 05.10
Date: Thu, 13 Oct 2005 19:34:42 UTC
Supported: 0
Description: This is the Breezy Badger release
Release-File: http://old-releases.ubuntu.com/ubuntu/dists/breezy-updates/Release

Dist: dapper
Name: Dapper Drake
Version: 6.06 LTS
Date: Thu, 01 Jun 2006 9:00:00 UTC
Supported: 0
Description: This is the Dapper Drake release
Release-File: http://old-releases.ubuntu.com/ubuntu/dists/dapper-updates/Release
ReleaseNotes: http://changelogs.ubuntu.com/EOLReleaseAnnouncement
UpgradeTool: http://old-releases.ubuntu.com/ubuntu/dists/dapper-updates/main/dist-upgrader-all/current/dapper.tar.gz
UpgradeToolSignature: http://old-releases.ubuntu.com/ubuntu/dists/dapper-updates/main/dist-upgrader-all/current/dapper.tar.gz.gpg

Dist: edgy
Name: Edgy Eft
Version: 6.10
Date: Thu, 26 Oct 2006 12:00:00 UTC
Supported: 0
Description: This is the Edgy Eft release
Release-File: http://old-releases.ubuntu.com/ubuntu/dists/edgy-updates/Release
ReleaseNotes: http://changelogs.ubuntu.com/EOLReleaseAnnouncement
UpgradeTool: http://old-releases.ubuntu.com/ubuntu/dists/edgy-updates/main/dist-upgrader-all/current/edgy.tar.gz
UpgradeToolSignature: http://old-releases.ubuntu.com/ubuntu/dists/edgy-updates/main/dist-upgrader-all/current/edgy.tar.gz.gpg

Dist: feisty
Name: Feisty Fawn
Version: 7.04
Date: Thu, 19 Apr 2007 13:00:00 UTC
Supported: 0
Description: This is the 7.04 release
Release-File: http://old-releases.ubuntu.com/ubuntu/dists/feisty-updates/Release
ReleaseNotes: http://changelogs.ubuntu.com/EOLReleaseAnnouncement
UpgradeTool: http://old-releases.ubuntu.com/ubuntu/dists/feisty-proposed/main/dist-upgrader-all/current/feisty.tar.gz
UpgradeToolSignature: http://old-releases.ubuntu.com/ubuntu/dists/feisty-proposed/main/dist-upgrader-all/current/feisty.tar.gz.gpg

Dist: gutsy
Name: Gutsy Gibbon
Version: 7.10
Date: Thu, 18 Oct 2007 12:00:00 UTC
Supported: 0
Description: This is the 7.10 release
Release-File: http://old-releases.ubuntu.com/ubuntu/dists/gutsy-updates/Release
ReleaseNotes: http://changelogs.ubuntu.com/EOLReleaseAnnouncement
UpgradeTool: http://old-releases.ubuntu.com/ubuntu/dists/gutsy/main/dist-upgrader-all/current/gutsy.tar.gz
UpgradeToolSignature: http://old-releases.ubuntu.com/ubuntu/dists/gutsy/main/dist-upgrader-all/current/gutsy.tar.gz.gpg

Dist: hardy
Name: Hardy Heron
Version: 8.04 LTS
Date: Thu, 24 Apr 2008 12:00:00 UTC
Supported: 0
Description: This is the 8.04 LTS release
Release-File: http://old-releases.ubuntu.com/ubuntu/dists/hardy-updates/Release
ReleaseNotes: http://changelogs.ubuntu.com/EOLReleaseAnnouncement
UpgradeTool: http://old-releases.ubuntu.com/ubuntu/dists/hardy-proposed/main/dist-upgrader-all/0.87.30/hardy.tar.gz
UpgradeToolSignature: http://old-releases.ubuntu.com/ubuntu/dists/hardy-proposed/main/dist-upgrader-all/0.87.30/hardy.tar.gz.gpg

Dist: intrepid
Name: Intrepid Ibex
Version: 8.10
Date: Thu, 30 Oct 2008 12:00:00 UTC
Supported: 0
Description: This is the 8.10 release
Release-File: http://old-releases.ubuntu.com/ubuntu/dists/intrepid-updates/Release
ReleaseNotes: http://changelogs.ubuntu.com/EOLReleaseAnnouncement
UpgradeTool: http://old-releases.ubuntu.com/ubuntu/dists/intrepid-proposed/main/dist-upgrader-all/0.93.34/intrepid.tar.gz
UpgradeToolSignature: http://old-releases.ubuntu.com/ubuntu/dists/intrepid-proposed/main/dist-upgrader-all/0.93.34/intrepid.tar.gz.gpg

Dist: jaunty
Name: Jaunty Jackalope
Version: 9.04
Date: Thu, 23 Apr 2009 12:00:00 UTC
Supported: 0
Description: This is the 9.04 release
Release-File: http://old-releases.ubuntu.com/ubuntu/dists/jaunty-updates/Release
ReleaseNotes: http://changelogs.ubuntu.com/EOLReleaseAnnouncement
UpgradeTool: http://old-releases.ubuntu.com/ubuntu/dists/jaunty-proposed/main/dist-upgrader-all/0.111.8/jaunty.tar.gz
UpgradeToolSignature: http://old-releases.ubuntu.com/ubuntu/dists/jaunty-proposed/main/dist-upgrader-all/0.111.8/jaunty.tar.gz.gpg

Dist: karmic
Name: Karmic Koala
Version: 9.10
Date: Thu, 29 Oct 2009 12:00:00 UTC
Supported: 0
Description: This is the 9.10 release
Release-File: http://old-releases.ubuntu.com/ubuntu/dists/karmic-updates/Release
ReleaseNotes: http://changelogs.ubuntu.com/EOLReleaseAnnouncement
UpgradeTool: http://old-releases.ubuntu.com/ubuntu/dists/karmic-proposed/main/dist-upgrader-all/0.126.9/karmic.tar.gz
UpgradeToolSignature: http://old-releases.ubuntu.com/ubuntu/dists/karmic-proposed/main/dist-upgrader-all/0.126.9/karmic.tar.gz.gpg

Dist: lucid
Name: Lucid Lynx
Version: 10.04.4 LTS
Date: Thu, 29 Apr 2010 12:00:00 UTC
Supported: 0
Description: This is the 10.04.4 LTS release
Release-File: http://old-releases.ubuntu.com/ubuntu/dists/lucid-updates/Release
ReleaseNotes: http://changelogs.ubuntu.com/EOLReleaseAnnouncement
UpgradeTool: http://old-releases.ubuntu.com/ubuntu/dists/lucid-updates/main/dist-upgrader-all/current/lucid.tar.gz
UpgradeToolSignature: http://old-releases.ubuntu.com/ubuntu/dists/lucid-updates/main/dist-upgrader-all/current/lucid.tar.gz.gpg

Dist: maverick
Name: Maverick Meerkat
Version: 10.10
Date: Sun, 10 Oct 2010 10:10:10 UTC
Supported: 0
Description: This is the 10.10 release
Release-File: http://old-releases.ubuntu.com/ubuntu/dists/maverick-updates/Release
ReleaseNotes: http://changelogs.ubuntu.com/EOLReleaseAnnouncement
UpgradeTool: http://old-releases.ubuntu.com/ubuntu/dists/maverick-updates/main/dist-upgrader-all/current/maverick.tar.gz
UpgradeToolSignature: http://old-releases.ubuntu.com/ubuntu/dists/maverick-updates/main/dist-upgrader-all/current/maverick.tar.gz.gpg

Dist: natty
Name: Natty Narwhal
Version: 11.04
Date: Thu, 28 Apr 2011 12:00:00 UTC
Supported: 0
Description: This is the 11.04 release
Release-File: http://old-releases.ubuntu.com/ubuntu/dists/natty-updates/Release
ReleaseNotes: http://changelogs.ubuntu.com/EOLReleaseAnnouncement
UpgradeTool: http://old-releases.ubuntu.com/ubuntu/dists/natty-updates/main/dist-upgrader-all/current/natty.tar.gz
UpgradeToolSignature: http://old-releases.ubuntu.com/ubuntu/dists/natty-updates/main/dist-upgrader-all/current/natty.tar.gz.gpg

Dist: oneiric
Name: Oneiric Ocelot
Version: 11.10
Date: Thu, 13 Oct 2011 12:00:00 UTC
Supported: 0
Description: This is the 11.10 release
Release-File: http://old-releases.ubuntu.com/ubuntu/dists/oneiric-updates/Release
ReleaseNotes: http://changelogs.ubuntu.com/EOLReleaseAnnouncement
UpgradeTool: http://old-releases.ubuntu.com/ubuntu/dists/oneiric-updates/main/dist-upgrader-all/current/oneiric.tar.gz
UpgradeToolSignature: http://old-releases.ubuntu.com/ubuntu/dists/oneiric-updates/main/dist-upgrader-all/current/oneiric.tar.gz.gpg

Dist: precise
Name: Precise Pangolin
Version: 12.04.5 LTS
Date: Thu, 26 Apr 2012 12:04:00 UTC
Supported: 0
Description: This is the 12.04.5 LTS release
Release-File: http://old-releases.ubuntu.com/ubuntu/dists/precise-updates/Release
ReleaseNotes: http://changelogs.ubuntu.com/EOLReleaseAnnouncement
UpgradeTool: http://old-releases.ubuntu.com/ubuntu/dists/precise-updates/main/dist-upgrader-all/current/precise.tar.gz
UpgradeToolSignature: http://old-releases.ubuntu.com/ubuntu/dists/precise-updates/main/dist-upgrader-all/current/precise.tar.gz.gpg

Dist: quantal
Name: Quantal Quetzal
Version: 12.10
Date: Thu, 18 Oct 2012 12:04:00 UTC
Supported: 0
Description: This is the 12.10 release
Release-File: http://old-releases.ubuntu.com/ubuntu/dists/quantal-updates/Release
ReleaseNotes: http://changelogs.ubuntu.com/EOLReleaseAnnouncement
UpgradeTool: http://old-releases.ubuntu.com/ubuntu/dists/quantal-updates/main/dist-upgrader-all/current/quantal.tar.gz
UpgradeToolSignature: http://old-releases.ubuntu.com/ubuntu/dists/quantal-updates/main/dist-upgrader-all/current/quantal.tar.gz.gpg

Dist: raring
Name: Raring Ringtail
Version: 13.04
Date: Thu, 25 Apr 2013 13:04:00 UTC
Supported: 0
Description: This is the 13.04 release
Release-File: http://old-releases.ubuntu.com/ubuntu/dists/raring-updates/Release
ReleaseNotes: http://changelogs.ubuntu.com/EOLReleaseAnnouncement
UpgradeTool: http://old-releases.ubuntu.com/ubuntu/dists/raring-updates/main/dist-upgrader-all/current/raring.tar.gz
UpgradeToolSignature: http://old-releases.ubuntu.com/ubuntu/dists/raring-updates/main/dist-upgrader-all/current/raring.tar.gz.gpg

Dist: saucy
Name: Saucy Salamander
Version: 13.10
Date: Thu, 17 Oct 2013 13:10:00 UTC
Supported: 0
Description: This is the 13.10 release
Release-File: http://old-releases.ubuntu.com/ubuntu/dists/saucy-updates/Release
ReleaseNotes: http://changelogs.ubuntu.com/EOLReleaseAnnouncement
UpgradeTool: http://old-releases.ubuntu.com/ubuntu/dists/saucy-updates/main/dist-upgrader-all/current/saucy.tar.gz
UpgradeToolSignature: http://old-releases.ubuntu.com/ubuntu/dists/saucy-updates/main/dist-upgrader-all/current/saucy.tar.gz.gpg

Dist: trusty
Name: Trusty Tahr
Version: 14.04.6 LTS
Date: Thu, 17 Apr 2014 14:04:00 UTC
Supported: 1
Description: This is the 14.04.6 LTS release
Release-File: http://archive.ubuntu.com/ubuntu/dists/trusty-updates/Release
ReleaseNotes: http://archive.ubuntu.com/ubuntu/dists/trusty-updates/main/dist-upgrader-all/current/ReleaseAnnouncement
ReleaseNotesHtml: http://archive.ubuntu.com/ubuntu/dists/trusty-updates/main/dist-upgrader-all/current/ReleaseAnnouncement.html
UpgradeTool: http://archive.ubuntu.com/ubuntu/dists/trusty-updates/main/dist-upgrader-all/current/trusty.tar.gz
UpgradeToolSignature: http://archive.ubuntu.com/ubuntu/dists/trusty-updates/main/dist-upgrader-all/current/trusty.tar.gz.gpg

Dist: utopic
Name: Utopic Unicorn
Version: 14.10
Date: Thu, 23 Oct 2014 14:10:00 UTC
Supported: 0
Description: This is the 14.10 release
Release-File: http://old-releases.ubuntu.com/ubuntu/dists/utopic-updates/Release
ReleaseNotes: http://changelogs.ubuntu.com/EOLReleaseAnnouncement
UpgradeTool: http://old-releases.ubuntu.com/ubuntu/dists/utopic-updates/main/dist-upgrader-all/current/utopic.tar.gz
UpgradeToolSignature: http://old-releases.ubuntu.com/ubuntu/dists/utopic-updates/main/dist-upgrader-all/current/utopic.tar.gz.gpg

Dist: vivid
Name: Vivid Vervet
Version: 15.04
Date: Thu, 23 April 2015 15:04:00 UTC
Supported: 0
Description: This is the 15.04 release
Release-File: http://old-releases.ubuntu.com/ubuntu/dists/vivid-updates/Release
ReleaseNotes: http://changelogs.ubuntu.com/EOLReleaseAnnouncement
UpgradeTool: http://old-releases.ubuntu.com/ubuntu/dists/vivid-updates/main/dist-upgrader-all/current/vivid.tar.gz
UpgradeToolSignature: http://old-releases.ubuntu.com/ubuntu/dists/vivid-updates/main/dist-upgrader-all/current/vivid.tar.gz.gpg

Dist: wily
Name: Wily Werewolf
Version: 15.10
Date: Thu, 22 October 2015 15:10:00 UTC
Supported: 0
Description: This is the 15.10 release
Release-File: http://old-releases.ubuntu.com/ubuntu/dists/wily-updates/Release
ReleaseNotes: http://changelogs.ubuntu.com/EOLReleaseAnnouncement
UpgradeTool: http://old-releases.ubuntu.com/ubuntu/dists/wily-updates/main/dist-upgrader-all/current/wily.tar.gz
UpgradeToolSignature: http://old-releases.ubuntu.com/ubuntu/dists/wily-updates/main/dist-upgrader-all/current/wily.tar.gz.gpg

Dist: xenial
Name: Xenial Xerus
Version: 16.04.7 LTS
Date: Thu, 21 April 2016 16:04:00 UTC
Supported: 1
Description: This is the 16.04.7 LTS release
Release-File: http://archive.ubuntu.com/ubuntu/dists/xenial-updates/Release
ReleaseNotes: http://archive.ubuntu.com/ubuntu/dists/xenial-updates/main/dist-upgrader-all/current/ReleaseAnnouncement
ReleaseNotesHtml: http://archive.ubuntu.com/ubuntu/dists/xenial-updates/main/dist-upgrader-all/current/ReleaseAnnouncement.html
UpgradeTool: http://archive.ubuntu.com/ubuntu/dists/xenial-updates/main/dist-upgrader-all/current/xenial.tar.gz
UpgradeToolSignature: http://archive.ubuntu.com/ubuntu/dists/xenial-updates/main/dist-upgrader-all/current/xenial.tar.gz.gpg

Dist: yakkety
Name: Yakkety Yak
Version: 16.10
Date: Thu, 13 October 2016 16:10:00 UTC
Supported: 0
Description: This is the 16.10 release
Release-File: http://old-releases.ubuntu.com/ubuntu/dists/yakkety-updates/Release
ReleaseNotes: http://changelogs.ubuntu.com/EOLReleaseAnnouncement
UpgradeTool: http://old-releases.ubuntu.com/ubuntu/dists/yakkety-updates/main/dist-upgrader-all/current/yakkety.tar.gz
UpgradeToolSignature: http://old-releases.ubuntu.com/ubuntu/dists/yakkety-updates/main/dist-upgrader-all/current/yakkety.tar.gz.gpg

Dist: zesty
Name: Zesty Zapus
Version: 17.04
Date: Thu, 13 April 2017 17:04:00 UTC
Supported: 0
Description: This is the 17.04 release
Release-File: http://old-releases.ubuntu.com/ubuntu/dists/zesty-updates/Release
ReleaseNotes: http://changelogs.ubuntu.com/EOLReleaseAnnouncement
UpgradeTool: http://old-releases.ubuntu.com/ubuntu/dists/zesty-updates/main/dist-upgrader-all/current/zesty.tar.gz
UpgradeToolSignature: http://old-releases.ubuntu.com/ubuntu/dists/zesty-updates/main/dist-upgrader-all/current/zesty.tar.gz.gpg

Dist: artful
Name: Artful Aardvark
Version: 17.10
Date: Thu, 19 October 2017 17:10:00 UTC
Supported: 0
Description: This is the 17.10 release
Release-File: http://old-releases.ubuntu.com/ubuntu/dists/artful-updates/Release
ReleaseNotes: http://changelogs.ubuntu.com/EOLReleaseAnnouncement
UpgradeTool: http://old-releases.ubuntu.com/ubuntu/dists/artful-updates/main/dist-upgrader-all/current/artful.tar.gz
UpgradeToolSignature: http://old-releases.ubuntu.com/ubuntu/dists/artful-updates/main/dist-upgrader-all/current/artful.tar.gz.gpg

Dist: bionic
Name: Bionic Beaver
Version: 18.04.6 LTS
Date: Thu, 26 April 2018 18:04:00 UTC
Supported: 1
Description: This is the 18.04.6 LTS release
Release-File: http://archive.ubuntu.com/ubuntu/dists/bionic-updates/Release
ReleaseNotes: http://archive.ubuntu.com/ubuntu/dists/bionic-updates/main/dist-upgrader-all/current/ReleaseAnnouncement
ReleaseNotesHtml: http://archive.ubuntu.com/ubuntu/dists/bionic-updates/main/dist-upgrader-all/current/ReleaseAnnouncement.html
UpgradeTool: http://archive.ubuntu.com/ubuntu/dists/bionic-updates/main/dist-upgrader-all/current/bionic.tar.gz
UpgradeToolSignature: http://archive.ubuntu.com/ubuntu/dists/bionic-updates/main/dist-upgrader-all/current/bionic.tar.gz.gpg

Dist: cosmic
Name: Cosmic Cuttlefish
Version: 18.10
Date: Thu, 18 October 2018 18:10:00 UTC
Supported: 0
Description: This is the 18.10 release
Release-File: http://old-releases.ubuntu.com/ubuntu/dists/cosmic-updates/Release
ReleaseNotes: http://changelogs.ubuntu.com/EOLReleaseAnnouncement
UpgradeTool: http://old-releases.ubuntu.com/ubuntu/dists/cosmic-updates/main/dist-upgrader-all/current/cosmic.tar.gz
UpgradeToolSignature: http://old-releases.ubuntu.com/ubuntu/dists/cosmic-updates/main/dist-upgrader-all/current/cosmic.tar.gz.gpg

Dist: disco
Name: Disco Dingo
Version: 19.04
Date: Thu, 18 April 2019 19:04:00 UTC
Supported: 0
Description: This is the 19.04 release
Release-File: http://old-releases.ubuntu.com/ubuntu/dists/disco-updates/Release
ReleaseNotes: http://changelogs.ubuntu.com/EOLReleaseAnnouncement
UpgradeTool: http://old-releases.ubuntu.com/ubuntu/dists/disco-updates/main/dist-upgrader-all/current/disco.tar.gz
UpgradeToolSignature: http://old-releases.ubuntu.com/ubuntu/dists/disco-updates/main/dist-upgrader-all/current/disco.tar.gz.gpg

Dist: eoan
Name: Eoan Ermine
Version: 19.10
Date: Thu, 17 October 2019 19:10:00 UTC
Supported: 0
Description: This is the 19.10 release
Release-File: http://old-releases.ubuntu.com/ubuntu/dists/eoan-updates/Release
ReleaseNotes: http://changelogs.ubuntu.com/EOLReleaseAnnouncement
UpgradeTool: http://old-releases.ubuntu.com/ubuntu/dists/eoan-updates/main/dist-upgrader-all/current/eoan.tar.gz
UpgradeToolSignature: http://old-releases.ubuntu.com/ubuntu/dists/eoan-updates/main/dist-upgrader-all/current/eoan.tar.gz.gpg

Dist: focal
Name: Focal Fossa
Version: 20.04.5 LTS
Date: Thu, 23 April 2020 20:04:00 UTC
Supported: 1
Description: This is the 20.04.5 LTS release
Release-File: http://archive.ubuntu.com/ubuntu/dists/focal-updates/Release
ReleaseNotes: http://archive.ubuntu.com/ubuntu/dists/focal-updates/main/dist-upgrader-all/current/ReleaseAnnouncement
ReleaseNotesHtml: http://archive.ubuntu.com/ubuntu/dists/focal-updates/main/dist-upgrader-all/current/ReleaseAnnouncement.html
UpgradeTool: http://archive.ubuntu.com/ubuntu/dists/focal-updates/main/dist-upgrader-all/current/focal.tar.gz
UpgradeToolSignature: http://archive.ubuntu.com/ubuntu/dists/focal-updates/main/dist-upgrader-all/current/focal.tar.gz.gpg

Dist: groovy
Name: Groovy Gorilla
Version: 20.10
Date: Thu, 22 October 2020 20:22:00 UTC
Supported: 0
Description: This is the 20.10 release
Release-File: http://old-releases.ubuntu.com/ubuntu/dists/groovy-updates/Release
ReleaseNotes: http://changelogs.ubuntu.com/EOLReleaseAnnouncement
UpgradeTool: http://old-releases.ubuntu.com/ubuntu/dists/groovy-updates/main/dist-upgrader-all/current/groovy.tar.gz
UpgradeToolSignature: http://old-releases.ubuntu.com/ubuntu/dists/groovy-updates/main/dist-upgrader-all/current/groovy.tar.gz.gpg

Dist: hirsute
Name: Hirsute Hippo
Version: 21.04
Date: Thu, 22 April 2021 21:04:00 UTC
Supported: 0
Description: This is the 21.04 release
Release-File: http://old-releases.ubuntu.com/ubuntu/dists/hirsute-updates/Release
ReleaseNotes: http://changelogs.ubuntu.com/EOLReleaseAnnouncement
UpgradeTool: http://old-releases.ubuntu.com/ubuntu/dists/hirsute-updates/main/dist-upgrader-all/current/hirsute.tar.gz
UpgradeToolSignature: http://old-releases.ubuntu.com/ubuntu/dists/hirsute-updates/main/dist-upgrader-all/current/hirsute.tar.gz.gpg

Dist: impish
Name: Impish Indri
Version: 21.10
Date: Thu, 14 October 2021 21:10:00 UTC
Supported: 0
Description: This is the 21.10 release
Release-File: http://old-releases.ubuntu.com/ubuntu/dists/impish-updates/Release
ReleaseNotes: http://changelogs.ubuntu.com/EOLReleaseAnnouncement
UpgradeTool: http://old-releases.ubuntu.com/ubuntu/dists/impish-updates/main/dist-upgrader-all/current/impish.tar.gz
UpgradeToolSignature: http://old-releases.ubuntu.com/ubuntu/dists/impish-updates/main/dist-upgrader-all/current/impish.tar.gz.gpg

Dist: jammy
Name: Jammy Jellyfish
Version: 22.04.5 LTS
Date: Thu, 21 April 2022 22:04:00 UTC
Supported: 1
Description: This is the 22.04.5 LTS release
Release-File: http://archive.ubuntu.com/ubuntu/dists/jammy-updates/Release
ReleaseNotes: http://archive.ubuntu.com/ubuntu/dists/jammy-updates/main/dist-upgrader-all/current/ReleaseAnnouncement
ReleaseNotesHtml: http://archive.ubuntu.com/ubuntu/dists/jammy-updates/main/dist-upgrader-all/current/ReleaseAnnouncement.html
UpgradeTool: http://archive.ubuntu.com/ubuntu/dists/jammy-updates/main/dist-upgrader-all/current/jammy.tar.gz
UpgradeToolSignature: http://archive.ubuntu.com/ubuntu/dists/jammy-updates/main/dist-upgrader-all/current/jammy.tar.gz.gpg

Dist: kinetic
Name: Kinetic Kudu
Version: 22.10
Date: Thu, 20 October 2022 22:10:00 UTC
Supported: 0
Description: This is the 22.10 release
Release-File: http://old-releases.ubuntu.com/ubuntu/dists/kinetic-updates/Release
ReleaseNotes: http://changelogs.ubuntu.com/EOLReleaseAnnouncement
UpgradeTool: http://old-releases.ubuntu.com/ubuntu/dists/kinetic/main/dist-upgrader-all/current/kinetic.tar.gz
UpgradeToolSignature: http://old-releases.ubuntu.com/ubuntu/dists/kinetic/main/dist-upgrader-all/current/kinetic.tar.gz.gpg

Dist: lunar
Name: Lunar Lobster
Version: 23.04
Date: Thu, 20 April 2023 23:04:00 UTC
Supported: 0
Description: This is the 23.04 release
Release-File: http://old-releases.ubuntu.com/ubuntu/dists/lunar-updates/Release
ReleaseNotes: http://changelogs.ubuntu.com/EOLReleaseAnnouncement
UpgradeTool: http://old-releases.ubuntu.com/ubuntu/dists/lunar-updates/main/dist-upgrader-all/current/lunar.tar.gz
UpgradeToolSignature: http://old-releases.ubuntu.com/ubuntu/dists/lunar-updates/main/dist-upgrader-all/current/lunar.tar.gz.gpg

Dist: mantic
Name: Mantic Minotaur
Version: 23.10
Date: Thu, 12 October 2023 23:10:00 UTC
Supported: 0
Description: This is the 23.10 release
Release-File: http://old-releases.ubuntu.com/ubuntu/dists/mantic-updates/Release
ReleaseNotes: http://changelogs.ubuntu.com/EOLReleaseAnnouncement
UpgradeTool: http://old-releases.ubuntu.com/ubuntu/dists/mantic-updates/main/dist-upgrader-all/current/mantic.tar.gz
UpgradeToolSignature: http://old-releases.ubuntu.com/ubuntu/dists/mantic-updates/main/dist-upgrader-all/current/mantic.tar.gz.gpg

Dist: noble
Name: Noble Numbat
Version: 24.04.3 LTS
Date: Thu, 25 April 2024 00:24:04 UTC
Supported: 1
Description: This is the 24.04.3 LTS release
Release-File: http://archive.ubuntu.com/ubuntu/dists/noble-updates/Release
ReleaseNotes: http://archive.ubuntu.com/ubuntu/dists/noble-updates/main/dist-upgrader-all/current/ReleaseAnnouncement
ReleaseNotesHtml: http://archive.ubuntu.com/ubuntu/dists/noble-updates/main/dist-upgrader-all/current/ReleaseAnnouncement.html
UpgradeTool: http://archive.ubuntu.com/ubuntu/dists/noble-updates/main/dist-upgrader-all/current/noble.tar.gz
UpgradeToolSignature: http://archive.ubuntu.com/ubuntu/dists/noble-updates/main/dist-upgrader-all/current/noble.tar.gz.gpg

Dist: oracular
Name: Oracular Oriole
Version: 24.10
Date: Thu, 10 October 2024 00:24:10 UTC
Supported: 0
Description: This is the 24.10 release
Release-File: http://archive.ubuntu.com/ubuntu/dists/oracular-updates/Release
ReleaseNotes: https://changelogs.ubuntu.com/EOLReleaseAnnouncement
UpgradeTool: http://archive.ubuntu.com/ubuntu/dists/oracular-updates/main/dist-upgrader-all/current/oracular.tar.gz
UpgradeToolSignature: http://archive.ubuntu.com/ubuntu/dists/oracular-updates/main/dist-upgrader-all/current/oracular.tar.gz.gpg

Dist: plucky
Name: Plucky Puffin
Version: 25.04
Date: Thu, 17 April 2025 00:25:04 UTC
Supported: 1
Description: This is the 25.04 release
Release-File: http://archive.ubuntu.com/ubuntu/dists/plucky-updates/Release
ReleaseNotes: http://archive.ubuntu.com/ubuntu/dists/plucky-updates/main/dist-upgrader-all/current/ReleaseAnnouncement
ReleaseNotesHtml: http://archive.ubuntu.com/ubuntu/dists/plucky-updates/main/dist-upgrader-all/current/ReleaseAnnouncement.html
UpgradeTool: http://archive.ubuntu.com/ubuntu/dists/plucky-updates/main/dist-upgrader-all/current/plucky.tar.gz
UpgradeToolSignature: http://archive.ubuntu.com/ubuntu/dists/plucky-updates/main/dist-upgrader-all/current/plucky.tar.gz.gpg
