<?xml version="1.0" encoding="UTF-8"?>
<xbel version="1.0"
      xmlns:bookmark="http://www.freedesktop.org/standards/desktop-bookmarks"
      xmlns:mime="http://www.freedesktop.org/standards/shared-mime-info"
>
  <bookmark href="file:///media/user/%E6%96%B0%E5%8A%A0%E5%8D%B7/%E8%A7%86%E8%A7%89%E4%BD%8D%E7%A7%BB/%E7%BD%91%E5%85%B3%E5%AE%B9%E5%99%A8%E7%9A%84%E4%BD%BF%E7%94%A8%E6%89%8B%E5%86%8C/%E6%96%B0%E5%BB%BA%E6%96%87%E4%BB%B6%E5%A4%B9/%E6%8D%B7%E8%AE%AF%EF%BC%88%E5%AE%B9%E5%99%A8%E7%9A%84%E4%B8%80%E4%BA%9B%E5%B8%B8%E7%94%A8%E5%91%BD%E4%BB%A4%EF%BC%89.txt" added="2024-12-31T06:58:23.566180Z" modified="2025-01-21T03:12:03.676947Z" visited="2024-12-31T06:58:23.566181Z">
    <info>
      <metadata owner="http://freedesktop.org">
        <mime:mime-type type="text/plain"/>
        <bookmark:applications>
          <bookmark:application name="org.gnome.Nautilus" exec="&apos;gnome-text-editor %U&apos;" modified="2025-01-21T03:12:03.676946Z" count="7"/>
        </bookmark:applications>
      </metadata>
    </info>
  </bookmark>
  <bookmark href="file:///media/user/%E6%96%B0%E5%8A%A0%E5%8D%B7/linux%E8%B5%84%E6%96%99/RK3568/%E3%80%90%E6%AD%A3%E7%82%B9%E5%8E%9F%E5%AD%90%E3%80%91RK3568%E5%BC%80%E5%8F%91%E6%9D%BF%E8%B5%84%E6%96%99%EF%BC%88A%E7%9B%98%EF%BC%89-%E5%9F%BA%E7%A1%80%E8%B5%84%E6%96%99/10%E3%80%81%E7%94%A8%E6%88%B7%E6%89%8B%E5%86%8C/10%E3%80%81%E7%94%A8%E6%88%B7%E6%89%8B%E5%86%8C/02%E3%80%81%E5%BC%80%E5%8F%91%E6%96%87%E6%A1%A3/01%E3%80%90%E6%AD%A3%E7%82%B9%E5%8E%9F%E5%AD%90%E3%80%91ATK-DLRK3568%E5%B5%8C%E5%85%A5%E5%BC%8FLinux%E7%B3%BB%E7%BB%9F%E5%BC%80%E5%8F%91%E6%89%8B%E5%86%8CV1.2.pdf" added="2024-12-31T07:35:09.243596Z" modified="2024-12-31T07:35:09.243601Z" visited="2024-12-31T07:35:09.243597Z">
    <info>
      <metadata owner="http://freedesktop.org">
        <mime:mime-type type="application/pdf"/>
        <bookmark:applications>
          <bookmark:application name="文档查看器" exec="&apos;evince %u&apos;" modified="2024-12-31T07:35:09.243599Z" count="1"/>
        </bookmark:applications>
      </metadata>
    </info>
  </bookmark>
  <bookmark href="file:///home/<USER>/DebugFile/main.cpp" added="2024-12-31T09:18:27.530976Z" modified="2024-12-31T09:25:46.945918Z" visited="2024-12-31T09:18:27.530977Z">
    <info>
      <metadata owner="http://freedesktop.org">
        <mime:mime-type type="text/x-c++src"/>
        <bookmark:applications>
          <bookmark:application name="org.gnome.Nautilus" exec="&apos;gnome-text-editor %U&apos;" modified="2024-12-31T09:25:46.945915Z" count="3"/>
        </bookmark:applications>
      </metadata>
    </info>
  </bookmark>
  <bookmark href="file:///media/user/MOVE%20SPEED/datafile/program.txt" added="2025-01-02T08:43:05.027162Z" modified="2025-01-02T08:43:05.027169Z" visited="2025-01-02T08:43:05.027163Z">
    <info>
      <metadata owner="http://freedesktop.org">
        <mime:mime-type type="text/plain"/>
        <bookmark:applications>
          <bookmark:application name="org.gnome.Nautilus" exec="&apos;gnome-text-editor %U&apos;" modified="2025-01-02T08:43:05.027166Z" count="1"/>
        </bookmark:applications>
      </metadata>
    </info>
  </bookmark>
  <bookmark href="file:///home/<USER>/%E4%B8%8B%E8%BD%BD/192.168.1.163_225AAB_ch_1_20250102084936.jpg" added="2025-01-02T08:49:45.678183Z" modified="2025-01-02T08:49:46.147616Z" visited="2025-01-02T08:49:45.678184Z">
    <info>
      <metadata owner="http://freedesktop.org">
        <mime:mime-type type="image/jpeg"/>
        <bookmark:groups>
          <bookmark:group>Graphics</bookmark:group>
        </bookmark:groups>
        <bookmark:applications>
          <bookmark:application name="org.gnome.Nautilus" exec="&apos;eog %U&apos;" modified="2025-01-02T08:49:45.678186Z" count="1"/>
          <bookmark:application name="Image Viewer" exec="&apos;eog %u&apos;" modified="2025-01-02T08:49:46.147614Z" count="1"/>
        </bookmark:applications>
      </metadata>
    </info>
  </bookmark>
  <bookmark href="file:///media/user/MOVE%20SPEED/datafile/dic/prog/crondic.cron" added="2025-01-02T08:57:41.018756Z" modified="2025-01-02T08:57:41.018763Z" visited="2025-01-02T08:57:41.018757Z">
    <info>
      <metadata owner="http://freedesktop.org">
        <mime:mime-type type="text/plain"/>
        <bookmark:applications>
          <bookmark:application name="org.gnome.Nautilus" exec="&apos;gnome-text-editor %U&apos;" modified="2025-01-02T08:57:41.018760Z" count="1"/>
        </bookmark:applications>
      </metadata>
    </info>
  </bookmark>
  <bookmark href="file:///media/user/MOVE%20SPEED/datafile/dic/prog/runLiveVideoResultProcess.sh" added="2025-01-02T09:13:47.053830Z" modified="2025-01-02T09:13:47.053840Z" visited="2025-01-02T09:13:47.053831Z">
    <info>
      <metadata owner="http://freedesktop.org">
        <mime:mime-type type="application/x-shellscript"/>
        <bookmark:applications>
          <bookmark:application name="org.gnome.Nautilus" exec="&apos;gnome-text-editor %U&apos;" modified="2025-01-02T09:13:47.053833Z" count="1"/>
        </bookmark:applications>
      </metadata>
    </info>
  </bookmark>
  <bookmark href="file:///home/<USER>/dic/para/video_displacement.txt" added="2025-01-02T09:26:03.491472Z" modified="2025-08-26T06:21:23.820757Z" visited="2025-01-02T09:26:03.491473Z">
    <info>
      <metadata owner="http://freedesktop.org">
        <mime:mime-type type="text/plain"/>
        <bookmark:applications>
          <bookmark:application name="org.gnome.Nautilus" exec="&apos;gnome-text-editor %U&apos;" modified="2025-08-26T06:21:23.820752Z" count="10"/>
        </bookmark:applications>
      </metadata>
    </info>
  </bookmark>
  <bookmark href="file:///home/<USER>/dic/prog/runLiveVideoResultProcess.sh" added="2025-01-02T09:33:48.066726Z" modified="2025-01-02T09:33:48.066735Z" visited="2025-01-02T09:33:48.066728Z">
    <info>
      <metadata owner="http://freedesktop.org">
        <mime:mime-type type="application/x-shellscript"/>
        <bookmark:applications>
          <bookmark:application name="org.gnome.Nautilus" exec="&apos;gnome-text-editor %U&apos;" modified="2025-01-02T09:33:48.066730Z" count="1"/>
        </bookmark:applications>
      </metadata>
    </info>
  </bookmark>
  <bookmark href="file:///home/<USER>/%E6%A1%8C%E9%9D%A2" added="2025-01-10T06:34:47.960720Z" modified="2025-02-27T03:17:58.468110Z" visited="2025-01-10T06:34:47.960721Z">
    <info>
      <metadata owner="http://freedesktop.org">
        <mime:mime-type type="inode/directory"/>
        <bookmark:applications>
          <bookmark:application name="org.gnome.Nautilus" exec="&apos;org.gnome.Nautilus %u&apos;" modified="2025-02-27T03:17:58.468109Z" count="3"/>
        </bookmark:applications>
      </metadata>
    </info>
  </bookmark>
  <bookmark href="file:///home/<USER>/%E6%A1%8C%E9%9D%A2/SunloginClient_15.2.0.63064_x86_64.rpm" added="2025-01-10T06:44:10.901885Z" modified="2025-01-10T06:44:10.901887Z" visited="2025-01-10T06:44:10.901885Z">
    <info>
      <metadata owner="http://freedesktop.org">
        <mime:mime-type type="application/x-rpm"/>
        <bookmark:applications>
          <bookmark:application name="File Roller" exec="&apos;file-roller&apos;" modified="2025-01-10T06:44:10.901886Z" count="1"/>
        </bookmark:applications>
      </metadata>
    </info>
  </bookmark>
  <bookmark href="file:///home/<USER>/%E4%B8%8B%E8%BD%BD/USB%E8%BD%AC%E4%B8%B2%E5%8F%A3%E9%A9%B1%E5%8A%A8/CH341SER_LINUX/README.md" added="2025-01-10T09:19:47.638523Z" modified="2025-01-10T09:19:47.638527Z" visited="2025-01-10T09:19:47.638524Z">
    <info>
      <metadata owner="http://freedesktop.org">
        <mime:mime-type type="text/markdown"/>
        <bookmark:applications>
          <bookmark:application name="org.gnome.Nautilus" exec="&apos;gnome-text-editor %U&apos;" modified="2025-01-10T09:19:47.638525Z" count="1"/>
        </bookmark:applications>
      </metadata>
    </info>
  </bookmark>
  <bookmark href="file:///home/<USER>/%E6%A1%8C%E9%9D%A2/192.168.1.163_225AAB_ch_1_20250102084936.jpg" added="2025-01-13T09:15:43.872161Z" modified="2025-01-13T09:15:43.872166Z" visited="2025-01-13T09:15:43.872162Z">
    <info>
      <metadata owner="http://freedesktop.org">
        <mime:mime-type type="image/jpeg"/>
        <bookmark:groups>
          <bookmark:group>Graphics</bookmark:group>
        </bookmark:groups>
        <bookmark:applications>
          <bookmark:application name="Image Viewer" exec="&apos;eog %u&apos;" modified="2025-01-13T09:15:43.872164Z" count="1"/>
        </bookmark:applications>
      </metadata>
    </info>
  </bookmark>
  <bookmark href="file:///home/<USER>/dic/runMQTT.sh" added="2025-01-13T09:17:53.839318Z" modified="2025-01-15T11:54:19.818389Z" visited="2025-01-13T09:17:53.839319Z">
    <info>
      <metadata owner="http://freedesktop.org">
        <mime:mime-type type="application/x-shellscript"/>
        <bookmark:applications>
          <bookmark:application name="org.gnome.Nautilus" exec="&apos;vim %F&apos;" modified="2025-01-15T11:54:19.818384Z" count="4"/>
        </bookmark:applications>
      </metadata>
    </info>
  </bookmark>
  <bookmark href="file:///home/<USER>/dic/logs" added="2025-01-13T09:22:58.196408Z" modified="2025-01-13T09:22:58.196418Z" visited="2025-01-13T09:22:58.196410Z">
    <info>
      <metadata owner="http://freedesktop.org">
        <mime:mime-type type="inode/directory"/>
        <bookmark:applications>
          <bookmark:application name="org.gnome.Nautilus" exec="&apos;org.gnome.Nautilus %u&apos;" modified="2025-01-13T09:22:58.196412Z" count="1"/>
        </bookmark:applications>
      </metadata>
    </info>
  </bookmark>
  <bookmark href="file:///home/<USER>/dic/nohup.out" added="2025-01-13T09:23:05.084055Z" modified="2025-09-08T01:24:00.111040Z" visited="2025-01-13T09:23:05.084056Z">
    <info>
      <metadata owner="http://freedesktop.org">
        <mime:mime-type type="text/plain"/>
        <bookmark:applications>
          <bookmark:application name="org.gnome.Nautilus" exec="&apos;gnome-text-editor %U&apos;" modified="2025-09-08T01:24:00.111038Z" count="5"/>
        </bookmark:applications>
      </metadata>
    </info>
  </bookmark>
  <bookmark href="file:///home/<USER>/dic/ps.log" added="2025-01-13T09:27:53.446400Z" modified="2025-01-13T09:40:40.087160Z" visited="2025-01-13T09:27:53.446401Z">
    <info>
      <metadata owner="http://freedesktop.org">
        <mime:mime-type type="text/x-log"/>
        <bookmark:applications>
          <bookmark:application name="org.gnome.Nautilus" exec="&apos;gnome-text-editor %U&apos;" modified="2025-01-13T09:40:40.087158Z" count="2"/>
        </bookmark:applications>
      </metadata>
    </info>
  </bookmark>
  <bookmark href="file:///home/<USER>/dic/prog/crondic.cron" added="2025-01-14T04:49:34.619557Z" modified="2025-01-14T04:49:34.619562Z" visited="2025-01-14T04:49:34.619558Z">
    <info>
      <metadata owner="http://freedesktop.org">
        <mime:mime-type type="text/plain"/>
        <bookmark:applications>
          <bookmark:application name="org.gnome.Nautilus" exec="&apos;gnome-text-editor %U&apos;" modified="2025-01-14T04:49:34.619560Z" count="1"/>
        </bookmark:applications>
      </metadata>
    </info>
  </bookmark>
  <bookmark href="file:///home/<USER>/orig/runCapture.sh" added="2025-01-14T04:51:32.907091Z" modified="2025-01-14T04:53:12.504524Z" visited="2025-01-14T04:51:32.907092Z">
    <info>
      <metadata owner="http://freedesktop.org">
        <mime:mime-type type="application/x-shellscript"/>
        <bookmark:applications>
          <bookmark:application name="org.gnome.Nautilus" exec="&apos;vim %F&apos;" modified="2025-01-14T04:53:12.504519Z" count="2"/>
        </bookmark:applications>
      </metadata>
    </info>
  </bookmark>
  <bookmark href="file:///home/<USER>/%E6%A1%8C%E9%9D%A2/capture_x86/camera.txt" added="2025-01-15T11:53:08.788384Z" modified="2025-01-15T11:53:08.788388Z" visited="2025-01-15T11:53:08.788385Z">
    <info>
      <metadata owner="http://freedesktop.org">
        <mime:mime-type type="text/plain"/>
        <bookmark:applications>
          <bookmark:application name="org.gnome.Nautilus" exec="&apos;gnome-text-editor %U&apos;" modified="2025-01-15T11:53:08.788387Z" count="1"/>
        </bookmark:applications>
      </metadata>
    </info>
  </bookmark>
  <bookmark href="file:///home/<USER>/%E6%A1%8C%E9%9D%A2/capture_x86/setting.txt" added="2025-01-15T11:53:29.956418Z" modified="2025-01-15T11:53:29.956423Z" visited="2025-01-15T11:53:29.956419Z">
    <info>
      <metadata owner="http://freedesktop.org">
        <mime:mime-type type="text/plain"/>
        <bookmark:applications>
          <bookmark:application name="org.gnome.Nautilus" exec="&apos;gnome-text-editor %U&apos;" modified="2025-01-15T11:53:29.956421Z" count="1"/>
        </bookmark:applications>
      </metadata>
    </info>
  </bookmark>
  <bookmark href="file:///home/<USER>/%E6%A1%8C%E9%9D%A2/capture_x86/startfile.txt" added="2025-01-15T11:53:35.429666Z" modified="2025-01-15T11:53:35.429670Z" visited="2025-01-15T11:53:35.429667Z">
    <info>
      <metadata owner="http://freedesktop.org">
        <mime:mime-type type="text/plain"/>
        <bookmark:applications>
          <bookmark:application name="org.gnome.Nautilus" exec="&apos;gnome-text-editor %U&apos;" modified="2025-01-15T11:53:35.429668Z" count="1"/>
        </bookmark:applications>
      </metadata>
    </info>
  </bookmark>
  <bookmark href="file:///home/<USER>/%E6%A1%8C%E9%9D%A2/capture_x86/captures_ip.cpp" added="2025-01-15T11:53:42.217422Z" modified="2025-01-15T11:53:42.217426Z" visited="2025-01-15T11:53:42.217423Z">
    <info>
      <metadata owner="http://freedesktop.org">
        <mime:mime-type type="text/x-c++src"/>
        <bookmark:applications>
          <bookmark:application name="org.gnome.Nautilus" exec="&apos;vim %F&apos;" modified="2025-01-15T11:53:42.217424Z" count="1"/>
        </bookmark:applications>
      </metadata>
    </info>
  </bookmark>
  <bookmark href="file:///home/<USER>/orig/originalObservation.txt" added="2025-01-15T11:58:07.034233Z" modified="2025-01-15T11:58:07.034238Z" visited="2025-01-15T11:58:07.034234Z">
    <info>
      <metadata owner="http://freedesktop.org">
        <mime:mime-type type="text/plain"/>
        <bookmark:applications>
          <bookmark:application name="org.gnome.Nautilus" exec="&apos;gnome-text-editor %U&apos;" modified="2025-01-15T11:58:07.034236Z" count="1"/>
        </bookmark:applications>
      </metadata>
    </info>
  </bookmark>
  <bookmark href="file:///home/<USER>/orig/captures_single.cpp" added="2025-01-15T11:58:16.639751Z" modified="2025-01-15T11:58:16.639758Z" visited="2025-01-15T11:58:16.639753Z">
    <info>
      <metadata owner="http://freedesktop.org">
        <mime:mime-type type="text/x-c++src"/>
        <bookmark:applications>
          <bookmark:application name="org.gnome.Nautilus" exec="&apos;vim %F&apos;" modified="2025-01-15T11:58:16.639755Z" count="1"/>
        </bookmark:applications>
      </metadata>
    </info>
  </bookmark>
  <bookmark href="file:///home/<USER>/orig/videoCaptureWeb.cpp" added="2025-01-15T11:58:26.161360Z" modified="2025-01-15T11:58:26.161364Z" visited="2025-01-15T11:58:26.161361Z">
    <info>
      <metadata owner="http://freedesktop.org">
        <mime:mime-type type="text/x-c++src"/>
        <bookmark:applications>
          <bookmark:application name="org.gnome.Nautilus" exec="&apos;vim %F&apos;" modified="2025-01-15T11:58:26.161363Z" count="1"/>
        </bookmark:applications>
      </metadata>
    </info>
  </bookmark>
  <bookmark href="file:///home/<USER>/%E4%B8%8B%E8%BD%BD/MVS_STD_V3.0.1_241128/README" added="2025-01-25T07:34:06.812930Z" modified="2025-01-25T07:43:03.785037Z" visited="2025-01-25T07:34:06.812932Z">
    <info>
      <metadata owner="http://freedesktop.org">
        <mime:mime-type type="text/x-readme"/>
        <bookmark:applications>
          <bookmark:application name="org.gnome.Nautilus" exec="&apos;gnome-text-editor %U&apos;" modified="2025-01-25T07:43:03.785033Z" count="2"/>
        </bookmark:applications>
      </metadata>
    </info>
  </bookmark>
  <bookmark href="file:///opt/MVS/bin/MVS.sh" added="2025-01-25T07:53:49.449194Z" modified="2025-01-25T07:53:49.449199Z" visited="2025-01-25T07:53:49.449195Z">
    <info>
      <metadata owner="http://freedesktop.org">
        <mime:mime-type type="application/x-shellscript"/>
        <bookmark:applications>
          <bookmark:application name="org.gnome.Nautilus" exec="&apos;vim %F&apos;" modified="2025-01-25T07:53:49.449196Z" count="1"/>
        </bookmark:applications>
      </metadata>
    </info>
  </bookmark>
  <bookmark href="file:///opt/MVS/bin/User_Manual_of_Client_Software_English.pdf" added="2025-01-25T07:53:57.703811Z" modified="2025-01-25T07:53:58.347977Z" visited="2025-01-25T07:53:57.703812Z">
    <info>
      <metadata owner="http://freedesktop.org">
        <mime:mime-type type="application/pdf"/>
        <bookmark:applications>
          <bookmark:application name="org.gnome.Nautilus" exec="&apos;evince %U&apos;" modified="2025-01-25T07:53:57.703813Z" count="1"/>
          <bookmark:application name="文档查看器" exec="&apos;evince %u&apos;" modified="2025-01-25T07:53:58.347976Z" count="1"/>
        </bookmark:applications>
      </metadata>
    </info>
  </bookmark>
  <bookmark href="file:///home/<USER>/%E6%A1%8C%E9%9D%A2/User_Manual_of_Client_Software_English.pdf" added="2025-02-05T03:27:41.031185Z" modified="2025-02-05T03:27:41.031191Z" visited="2025-02-05T03:27:41.031186Z">
    <info>
      <metadata owner="http://freedesktop.org">
        <mime:mime-type type="application/pdf"/>
        <bookmark:applications>
          <bookmark:application name="文档查看器" exec="&apos;evince %u&apos;" modified="2025-02-05T03:27:41.031188Z" count="1"/>
        </bookmark:applications>
      </metadata>
    </info>
  </bookmark>
  <bookmark href="file:///home/<USER>/%E6%A1%8C%E9%9D%A2/capture_mvs/Image_20250206094630687.jpg" added="2025-02-06T01:46:45.701738Z" modified="2025-02-27T03:07:58.796678Z" visited="2025-02-06T01:46:45.701739Z">
    <info>
      <metadata owner="http://freedesktop.org">
        <mime:mime-type type="image/jpeg"/>
        <bookmark:groups>
          <bookmark:group>Graphics</bookmark:group>
        </bookmark:groups>
        <bookmark:applications>
          <bookmark:application name="org.gnome.Nautilus" exec="&apos;eog %U&apos;" modified="2025-02-06T01:50:48.551519Z" count="2"/>
          <bookmark:application name="Image Viewer" exec="&apos;eog %u&apos;" modified="2025-02-27T03:07:58.796677Z" count="3"/>
        </bookmark:applications>
      </metadata>
    </info>
  </bookmark>
  <bookmark href="file:///home/<USER>/%E6%A1%8C%E9%9D%A2/capture_mvs/Image_20250206101458245.jpg" added="2025-02-06T02:15:03.960397Z" modified="2025-02-27T03:07:59.571817Z" visited="2025-02-06T02:15:03.960398Z">
    <info>
      <metadata owner="http://freedesktop.org">
        <mime:mime-type type="image/jpeg"/>
        <bookmark:groups>
          <bookmark:group>Graphics</bookmark:group>
        </bookmark:groups>
        <bookmark:applications>
          <bookmark:application name="org.gnome.Nautilus" exec="&apos;eog %U&apos;" modified="2025-02-06T02:15:03.960405Z" count="1"/>
          <bookmark:application name="Image Viewer" exec="&apos;eog %u&apos;" modified="2025-02-27T03:07:59.571816Z" count="2"/>
        </bookmark:applications>
      </metadata>
    </info>
  </bookmark>
  <bookmark href="file:///home/<USER>/%E6%A1%8C%E9%9D%A2/capture_mvs/Image_20250206101458245.zip" added="2025-02-06T02:16:53.889010Z" modified="2025-02-06T02:16:53.889021Z" visited="2025-02-06T02:16:53.889011Z">
    <info>
      <metadata owner="http://freedesktop.org">
        <mime:mime-type type="application/zip"/>
        <bookmark:applications>
          <bookmark:application name="org.gnome.Nautilus" exec="&apos;org.gnome.Nautilus %u&apos;" modified="2025-02-06T02:16:53.889018Z" count="1"/>
        </bookmark:applications>
      </metadata>
    </info>
  </bookmark>
  <bookmark href="file:///home/<USER>/%E6%A1%8C%E9%9D%A2/capture_mvs/Image_20250207090733689.jpg" added="2025-02-07T01:07:39.616426Z" modified="2025-02-27T03:08:00.405602Z" visited="2025-02-07T01:07:39.616427Z">
    <info>
      <metadata owner="http://freedesktop.org">
        <mime:mime-type type="image/jpeg"/>
        <bookmark:groups>
          <bookmark:group>Graphics</bookmark:group>
        </bookmark:groups>
        <bookmark:applications>
          <bookmark:application name="org.gnome.Nautilus" exec="&apos;eog %U&apos;" modified="2025-02-07T01:07:39.616429Z" count="1"/>
          <bookmark:application name="Image Viewer" exec="&apos;eog %u&apos;" modified="2025-02-27T03:08:00.405601Z" count="2"/>
        </bookmark:applications>
      </metadata>
    </info>
  </bookmark>
  <bookmark href="file:///home/<USER>/%E6%A1%8C%E9%9D%A2/capture_mvs/Image_20250207090733689.zip" added="2025-02-07T01:09:40.308047Z" modified="2025-02-07T01:09:40.308051Z" visited="2025-02-07T01:09:40.308048Z">
    <info>
      <metadata owner="http://freedesktop.org">
        <mime:mime-type type="application/zip"/>
        <bookmark:applications>
          <bookmark:application name="org.gnome.Nautilus" exec="&apos;org.gnome.Nautilus %u&apos;" modified="2025-02-07T01:09:40.308049Z" count="1"/>
        </bookmark:applications>
      </metadata>
    </info>
  </bookmark>
  <bookmark href="file:///home/<USER>/%E6%A1%8C%E9%9D%A2/capture_mvs/Image_20250207091214948.zip" added="2025-02-07T01:12:31.015713Z" modified="2025-02-07T01:12:31.015716Z" visited="2025-02-07T01:12:31.015714Z">
    <info>
      <metadata owner="http://freedesktop.org">
        <mime:mime-type type="application/zip"/>
        <bookmark:applications>
          <bookmark:application name="org.gnome.Nautilus" exec="&apos;org.gnome.Nautilus %u&apos;" modified="2025-02-07T01:12:31.015715Z" count="1"/>
        </bookmark:applications>
      </metadata>
    </info>
  </bookmark>
  <bookmark href="file:///home/<USER>/%E6%A1%8C%E9%9D%A2/capture_mvs/Image_20250206094447681.jpg" added="2025-02-27T03:07:56.276043Z" modified="2025-02-27T03:07:56.808367Z" visited="2025-02-27T03:07:56.276044Z">
    <info>
      <metadata owner="http://freedesktop.org">
        <mime:mime-type type="image/jpeg"/>
        <bookmark:groups>
          <bookmark:group>Graphics</bookmark:group>
        </bookmark:groups>
        <bookmark:applications>
          <bookmark:application name="org.gnome.Nautilus" exec="&apos;eog %U&apos;" modified="2025-02-27T03:07:56.276046Z" count="1"/>
          <bookmark:application name="Image Viewer" exec="&apos;eog %u&apos;" modified="2025-02-27T03:07:56.808365Z" count="1"/>
        </bookmark:applications>
      </metadata>
    </info>
  </bookmark>
  <bookmark href="file:///home/<USER>/%E4%B8%8B%E8%BD%BD/MVS_STD_V3.0.1_241128/MVS-3.0.1_aarch64_20241128.deb" added="2025-02-27T03:16:14.183106Z" modified="2025-02-27T03:45:19.688275Z" visited="2025-02-27T03:16:14.183108Z">
    <info>
      <metadata owner="http://freedesktop.org">
        <mime:mime-type type="application/vnd.debian.binary-package"/>
        <bookmark:applications>
          <bookmark:application name="org.gnome.Nautilus" exec="&apos;file-roller %U&apos;" modified="2025-02-27T03:44:56.113719Z" count="5"/>
          <bookmark:application name="File Roller" exec="&apos;file-roller&apos;" modified="2025-02-27T03:45:19.688274Z" count="2"/>
        </bookmark:applications>
      </metadata>
    </info>
  </bookmark>
  <bookmark href="file:///home/<USER>/%E4%B8%8B%E8%BD%BD/MVS_STD_V3.0.1_241128" added="2025-02-27T03:35:03.846100Z" modified="2025-02-27T03:46:22.562655Z" visited="2025-02-27T03:35:03.846101Z">
    <info>
      <metadata owner="http://freedesktop.org">
        <mime:mime-type type="inode/directory"/>
        <bookmark:applications>
          <bookmark:application name="org.gnome.Nautilus" exec="&apos;nautilus --new-window %U&apos;" modified="2025-02-27T03:46:22.562652Z" count="2"/>
        </bookmark:applications>
      </metadata>
    </info>
  </bookmark>
  <bookmark href="file:///home/<USER>/%E4%B8%8B%E8%BD%BD/MVS_STD_V3.0.1_241128/MVS-3.0.1_aarch64_20241128/MVS.tar.gz" added="2025-02-27T03:35:17.357801Z" modified="2025-02-27T03:35:18.213595Z" visited="2025-02-27T03:35:17.357802Z">
    <info>
      <metadata owner="http://freedesktop.org">
        <mime:mime-type type="application/x-compressed-tar"/>
        <bookmark:applications>
          <bookmark:application name="org.gnome.Nautilus" exec="&apos;file-roller %U&apos;" modified="2025-02-27T03:35:17.357804Z" count="1"/>
          <bookmark:application name="File Roller" exec="&apos;file-roller&apos;" modified="2025-02-27T03:35:18.213593Z" count="1"/>
        </bookmark:applications>
      </metadata>
    </info>
  </bookmark>
  <bookmark href="file:///home/<USER>/%E6%A1%8C%E9%9D%A2/MVS_STD_V3.0.1_241128/MVS-3.0.1_aarch64_20241128.deb" added="2025-02-27T03:36:40.700265Z" modified="2025-02-27T03:38:39.146894Z" visited="2025-02-27T03:36:40.700266Z">
    <info>
      <metadata owner="http://freedesktop.org">
        <mime:mime-type type="application/vnd.debian.binary-package"/>
        <bookmark:applications>
          <bookmark:application name="org.gnome.Nautilus" exec="&apos;env BAMF_DESKTOP_FILE_HINT=/var/lib/snapd/desktop/applications/snap-store_ubuntu-software-local-file.desktop /snap/bin/snap-store.ubuntu-software-local-file --local-filename %f&apos;" modified="2025-02-27T03:38:39.146892Z" count="2"/>
        </bookmark:applications>
      </metadata>
    </info>
  </bookmark>
  <bookmark href="file:///home/<USER>/%E6%A1%8C%E9%9D%A2/MVS_STD_V3.0.1_241128/MVS-3.0.1_armhf_20241128.deb" added="2025-02-27T03:38:50.349060Z" modified="2025-02-27T03:38:50.349066Z" visited="2025-02-27T03:38:50.349061Z">
    <info>
      <metadata owner="http://freedesktop.org">
        <mime:mime-type type="application/vnd.debian.binary-package"/>
        <bookmark:applications>
          <bookmark:application name="org.gnome.Nautilus" exec="&apos;env BAMF_DESKTOP_FILE_HINT=/var/lib/snapd/desktop/applications/snap-store_ubuntu-software-local-file.desktop /snap/bin/snap-store.ubuntu-software-local-file --local-filename %f&apos;" modified="2025-02-27T03:38:50.349062Z" count="1"/>
        </bookmark:applications>
      </metadata>
    </info>
  </bookmark>
  <bookmark href="file:///home/<USER>/%E6%A1%8C%E9%9D%A2/capture_mvs/ceshi1" added="2025-03-20T03:47:52.125731Z" modified="2025-03-20T03:47:52.125736Z" visited="2025-03-20T03:47:52.125732Z">
    <info>
      <metadata owner="http://freedesktop.org">
        <mime:mime-type type="inode/directory"/>
        <bookmark:applications>
          <bookmark:application name="org.gnome.Nautilus" exec="&apos;org.gnome.Nautilus %u&apos;" modified="2025-03-20T03:47:52.125733Z" count="1"/>
        </bookmark:applications>
      </metadata>
    </info>
  </bookmark>
  <bookmark href="file:///home/<USER>/%E6%A1%8C%E9%9D%A2/capture_mvs/ceshi1/C02V11.jpg" added="2025-03-20T07:29:07.848291Z" modified="2025-03-20T07:29:08.455915Z" visited="2025-03-20T07:29:07.848292Z">
    <info>
      <metadata owner="http://freedesktop.org">
        <mime:mime-type type="image/jpeg"/>
        <bookmark:groups>
          <bookmark:group>Graphics</bookmark:group>
        </bookmark:groups>
        <bookmark:applications>
          <bookmark:application name="org.gnome.Nautilus" exec="&apos;eog %U&apos;" modified="2025-03-20T07:29:07.848294Z" count="1"/>
          <bookmark:application name="Image Viewer" exec="&apos;eog %u&apos;" modified="2025-03-20T07:29:08.455913Z" count="1"/>
        </bookmark:applications>
      </metadata>
    </info>
  </bookmark>
  <bookmark href="file:///home/<USER>/%E6%A1%8C%E9%9D%A2/capture_mvs/C01V02.jpg" added="2025-03-21T01:14:07.845133Z" modified="2025-03-21T01:54:27.421885Z" visited="2025-03-21T01:14:07.845135Z">
    <info>
      <metadata owner="http://freedesktop.org">
        <mime:mime-type type="image/jpeg"/>
        <bookmark:groups>
          <bookmark:group>Graphics</bookmark:group>
        </bookmark:groups>
        <bookmark:applications>
          <bookmark:application name="org.gnome.Nautilus" exec="&apos;eog %U&apos;" modified="2025-03-21T01:54:26.628663Z" count="2"/>
          <bookmark:application name="Image Viewer" exec="&apos;eog %u&apos;" modified="2025-03-21T01:54:27.421883Z" count="3"/>
        </bookmark:applications>
      </metadata>
    </info>
  </bookmark>
  <bookmark href="file:///home/<USER>/%E6%A1%8C%E9%9D%A2/capture_mvs/C02V02.jpg" added="2025-03-21T01:15:33.292981Z" modified="2025-03-21T01:15:33.976237Z" visited="2025-03-21T01:15:33.292983Z">
    <info>
      <metadata owner="http://freedesktop.org">
        <mime:mime-type type="image/jpeg"/>
        <bookmark:groups>
          <bookmark:group>Graphics</bookmark:group>
        </bookmark:groups>
        <bookmark:applications>
          <bookmark:application name="org.gnome.Nautilus" exec="&apos;eog %U&apos;" modified="2025-03-21T01:15:33.292985Z" count="1"/>
          <bookmark:application name="Image Viewer" exec="&apos;eog %u&apos;" modified="2025-03-21T01:15:33.976236Z" count="1"/>
        </bookmark:applications>
      </metadata>
    </info>
  </bookmark>
  <bookmark href="file:///home/<USER>/%E6%A1%8C%E9%9D%A2/capture_mvs/C01V02XINDE%20.jpg" added="2025-03-21T02:42:22.492119Z" modified="2025-03-21T02:42:23.271544Z" visited="2025-03-21T02:42:22.492120Z">
    <info>
      <metadata owner="http://freedesktop.org">
        <mime:mime-type type="image/jpeg"/>
        <bookmark:groups>
          <bookmark:group>Graphics</bookmark:group>
        </bookmark:groups>
        <bookmark:applications>
          <bookmark:application name="org.gnome.Nautilus" exec="&apos;eog %U&apos;" modified="2025-03-21T02:42:22.492122Z" count="1"/>
          <bookmark:application name="Image Viewer" exec="&apos;eog %u&apos;" modified="2025-03-21T02:42:23.271542Z" count="1"/>
        </bookmark:applications>
      </metadata>
    </info>
  </bookmark>
  <bookmark href="file:///home/<USER>/orig/0" added="2025-04-02T10:46:40.890944Z" modified="2025-04-02T10:46:41.585784Z" visited="2025-04-02T10:46:40.890946Z">
    <info>
      <metadata owner="http://freedesktop.org">
        <mime:mime-type type="image/jpeg"/>
        <bookmark:groups>
          <bookmark:group>Graphics</bookmark:group>
        </bookmark:groups>
        <bookmark:applications>
          <bookmark:application name="org.gnome.Nautilus" exec="&apos;eog %U&apos;" modified="2025-04-02T10:46:40.890947Z" count="1"/>
          <bookmark:application name="Image Viewer" exec="&apos;eog %u&apos;" modified="2025-04-02T10:46:41.585783Z" count="1"/>
        </bookmark:applications>
      </metadata>
    </info>
  </bookmark>
  <bookmark href="file:///home/<USER>/orig/a216.jpg" added="2025-04-02T10:46:46.964389Z" modified="2025-04-02T10:46:47.620293Z" visited="2025-04-02T10:46:46.964390Z">
    <info>
      <metadata owner="http://freedesktop.org">
        <mime:mime-type type="image/jpeg"/>
        <bookmark:groups>
          <bookmark:group>Graphics</bookmark:group>
        </bookmark:groups>
        <bookmark:applications>
          <bookmark:application name="org.gnome.Nautilus" exec="&apos;eog %U&apos;" modified="2025-04-02T10:46:46.964393Z" count="1"/>
          <bookmark:application name="Image Viewer" exec="&apos;eog %u&apos;" modified="2025-04-02T10:46:47.620291Z" count="1"/>
        </bookmark:applications>
      </metadata>
    </info>
  </bookmark>
  <bookmark href="file:///home/<USER>/%E6%A1%8C%E9%9D%A2/capture_mvs/Image_20250407170113270.jpg" added="2025-04-07T09:01:53.014866Z" modified="2025-04-07T09:01:53.668511Z" visited="2025-04-07T09:01:53.014867Z">
    <info>
      <metadata owner="http://freedesktop.org">
        <mime:mime-type type="image/jpeg"/>
        <bookmark:groups>
          <bookmark:group>Graphics</bookmark:group>
        </bookmark:groups>
        <bookmark:applications>
          <bookmark:application name="org.gnome.Nautilus" exec="&apos;eog %U&apos;" modified="2025-04-07T09:01:53.014869Z" count="1"/>
          <bookmark:application name="Image Viewer" exec="&apos;eog %u&apos;" modified="2025-04-07T09:01:53.668510Z" count="1"/>
        </bookmark:applications>
      </metadata>
    </info>
  </bookmark>
  <bookmark href="file:///home/<USER>/%E6%A1%8C%E9%9D%A2/capture_mvs/Image_20250412133830234.jpg" added="2025-04-12T05:38:58.892014Z" modified="2025-04-12T05:58:03.893129Z" visited="2025-04-12T05:38:58.892015Z">
    <info>
      <metadata owner="http://freedesktop.org">
        <mime:mime-type type="image/jpeg"/>
        <bookmark:groups>
          <bookmark:group>Graphics</bookmark:group>
        </bookmark:groups>
        <bookmark:applications>
          <bookmark:application name="org.gnome.Nautilus" exec="&apos;mtpaint %U&apos;" modified="2025-04-12T05:58:03.893126Z" count="5"/>
          <bookmark:application name="Image Viewer" exec="&apos;eog %u&apos;" modified="2025-04-12T05:56:43.263034Z" count="4"/>
        </bookmark:applications>
      </metadata>
    </info>
  </bookmark>
  <bookmark href="file:///home/<USER>/%E6%A1%8C%E9%9D%A2/capture_mvs/Image_20250412133844405.jpg" added="2025-04-12T06:06:36.609295Z" modified="2025-04-12T06:06:36.609298Z" visited="2025-04-12T06:06:36.609295Z">
    <info>
      <metadata owner="http://freedesktop.org">
        <mime:mime-type type="image/jpeg"/>
        <bookmark:applications>
          <bookmark:application name="org.gnome.Nautilus" exec="&apos;mtpaint %U&apos;" modified="2025-04-12T06:06:36.609296Z" count="1"/>
        </bookmark:applications>
      </metadata>
    </info>
  </bookmark>
  <bookmark href="file:///home/<USER>/build.sh" added="2025-08-13T07:40:06.852696Z" modified="2025-08-13T07:40:06.852703Z" visited="2025-08-13T07:40:06.852697Z">
    <info>
      <metadata owner="http://freedesktop.org">
        <mime:mime-type type="application/x-shellscript"/>
        <bookmark:applications>
          <bookmark:application name="org.gnome.Nautilus" exec="&apos;vim %F&apos;" modified="2025-08-13T07:40:06.852700Z" count="1"/>
        </bookmark:applications>
      </metadata>
    </info>
  </bookmark>
  <bookmark href="file:///home/<USER>/dic/logs/visual_log.txt" added="2025-08-13T07:41:44.161984Z" modified="2025-09-08T01:19:43.725247Z" visited="2025-08-13T07:41:44.161984Z">
    <info>
      <metadata owner="http://freedesktop.org">
        <mime:mime-type type="text/plain"/>
        <bookmark:applications>
          <bookmark:application name="org.gnome.Nautilus" exec="&apos;gnome-text-editor %U&apos;" modified="2025-09-08T01:19:43.725245Z" count="17"/>
        </bookmark:applications>
      </metadata>
    </info>
  </bookmark>
  <bookmark href="file:///home/<USER>/dic/logs/visual_error.txt" added="2025-08-13T07:41:47.902806Z" modified="2025-08-26T02:57:53.461255Z" visited="2025-08-13T07:41:47.902809Z">
    <info>
      <metadata owner="http://freedesktop.org">
        <mime:mime-type type="text/plain"/>
        <bookmark:applications>
          <bookmark:application name="org.gnome.Nautilus" exec="&apos;gnome-text-editor %U&apos;" modified="2025-08-26T02:57:53.461252Z" count="4"/>
        </bookmark:applications>
      </metadata>
    </info>
  </bookmark>
  <bookmark href="file:///home/<USER>/dic/dic/logs/visual_error.txt" added="2025-08-26T02:58:29.060320Z" modified="2025-08-26T02:58:29.060327Z" visited="2025-08-26T02:58:29.060322Z">
    <info>
      <metadata owner="http://freedesktop.org">
        <mime:mime-type type="text/plain"/>
        <bookmark:applications>
          <bookmark:application name="org.gnome.Nautilus" exec="&apos;gnome-text-editor %U&apos;" modified="2025-08-26T02:58:29.060323Z" count="1"/>
        </bookmark:applications>
      </metadata>
    </info>
  </bookmark>
  <bookmark href="file:///home/<USER>/runMQTT.sh" added="2025-09-08T01:23:14.264046Z" modified="2025-09-08T01:23:14.264054Z" visited="2025-09-08T01:23:14.264048Z">
    <info>
      <metadata owner="http://freedesktop.org">
        <mime:mime-type type="application/x-shellscript"/>
        <bookmark:applications>
          <bookmark:application name="org.gnome.Nautilus" exec="&apos;vim %F&apos;" modified="2025-09-08T01:23:14.264050Z" count="1"/>
        </bookmark:applications>
      </metadata>
    </info>
  </bookmark>
  <bookmark href="file:///home/<USER>/cfg.ini" added="2025-09-08T01:43:22.015204Z" modified="2025-09-08T01:43:22.015208Z" visited="2025-09-08T01:43:22.015205Z">
    <info>
      <metadata owner="http://freedesktop.org">
        <mime:mime-type type="text/plain"/>
        <bookmark:applications>
          <bookmark:application name="org.gnome.Nautilus" exec="&apos;gnome-text-editor %U&apos;" modified="2025-09-08T01:43:22.015206Z" count="1"/>
        </bookmark:applications>
      </metadata>
    </info>
  </bookmark>
</xbel>