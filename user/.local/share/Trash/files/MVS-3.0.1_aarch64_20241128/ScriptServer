#!/bin/sh
### BEGIN INIT INFO
# Provides:          ScriptServer
# Required-Start:    $remote_fs $syslog $local_fs
# Required-Stop:     $remote_fs $syslog $local_fs
# Default-Start:     2 3 4 5
# Default-Stop:      0 1 6
# Short-Description: script self-starting
# Description:       ScriptServer is a daemon to self-starting script server.
### END INIT INFO

SCRIPT_PATH=${MVCAM_SDK_PATH}

# Start the Script Server
start() 
{
    echo "Starting execute script..."

	if [ -f $SCRIPT_PATH/set_usbfs_memory_size.sh ]; then
		$SCRIPT_PATH/set_usbfs_memory_size.sh
	fi
	
	if [ -f $SCRIPT_PATH/set_socket_buffer_size.sh ]; then
		$SCRIPT_PATH/set_socket_buffer_size.sh
	fi
	
	if [ -f $SCRIPT_PATH/set_rp_filter.sh ]; then
		$SCRIPT_PATH/set_rp_filter.sh
	fi
	
}

# Main logic
case "$1" in
    start)
        start
        ;;
  *)
    echo $"Usage: $0 {start}"
    exit 1
esac
exit 0
