[     7.521] (--) Log file renamed from "/home/<USER>/.local/share/xorg/Xorg.pid-1108.log" to "/home/<USER>/.local/share/xorg/Xorg.0.log"
[     7.523] 
X.Org X Server ********
X Protocol Version 11, Revision 0
[     7.523] Current Operating System: Linux user-Default-string 6.2.0-39-generic #40-Ubuntu SMP PREEMPT_DYNAMIC Tue Nov 14 14:18:00 UTC 2023 x86_64
[     7.523] Kernel command line: BOOT_IMAGE=/boot/vmlinuz-6.2.0-39-generic root=UUID=141f9a10-ded6-4bb6-8e6b-34c8017a2dea ro quiet splash vt.handoff=7
[     7.523] xorg-server 2:21.1.7-1ubuntu3.6 (For technical support please see http://www.ubuntu.com/support) 
[     7.523] Current version of pixman: 0.42.2
[     7.523] 	Before reporting problems, check http://wiki.x.org
	to make sure that you have the latest version.
[     7.523] Markers: (--) probed, (**) from config file, (==) default setting,
	(++) from command line, (!!) notice, (II) informational,
	(WW) warning, (EE) error, (NI) not implemented, (??) unknown.
[     7.523] (==) Log file: "/home/<USER>/.local/share/xorg/Xorg.0.log", Time: Wed Sep 17 10:44:16 2025
[     7.528] (==) Using system config directory "/usr/share/X11/xorg.conf.d"
[     7.529] (==) No Layout section.  Using the first Screen section.
[     7.529] (==) No screen section available. Using defaults.
[     7.529] (**) |-->Screen "Default Screen Section" (0)
[     7.529] (**) |   |-->Monitor "<default monitor>"
[     7.529] (==) No monitor specified for screen "Default Screen Section".
	Using a default monitor configuration.
[     7.529] (==) Automatically adding devices
[     7.529] (==) Automatically enabling devices
[     7.529] (==) Automatically adding GPU devices
[     7.529] (==) Automatically binding GPU devices
[     7.529] (==) Max clients allowed: 256, resource mask: 0x1fffff
[     7.531] (WW) The directory "/usr/share/fonts/X11/cyrillic" does not exist.
[     7.531] 	Entry deleted from font path.
[     7.531] (WW) The directory "/usr/share/fonts/X11/100dpi/" does not exist.
[     7.531] 	Entry deleted from font path.
[     7.531] (WW) The directory "/usr/share/fonts/X11/75dpi/" does not exist.
[     7.531] 	Entry deleted from font path.
[     7.531] (WW) The directory "/usr/share/fonts/X11/100dpi" does not exist.
[     7.531] 	Entry deleted from font path.
[     7.531] (WW) The directory "/usr/share/fonts/X11/75dpi" does not exist.
[     7.531] 	Entry deleted from font path.
[     7.531] (==) FontPath set to:
	/usr/share/fonts/X11/misc,
	/usr/share/fonts/X11/Type1,
	built-ins
[     7.531] (==) ModulePath set to "/usr/lib/xorg/modules"
[     7.531] (II) The server relies on udev to provide the list of input devices.
	If no devices become available, reconfigure udev or disable AutoAddDevices.
[     7.531] (II) Loader magic: 0x55970fde6020
[     7.531] (II) Module ABI versions:
[     7.531] 	X.Org ANSI C Emulation: 0.4
[     7.531] 	X.Org Video Driver: 25.2
[     7.531] 	X.Org XInput driver : 24.4
[     7.531] 	X.Org Server Extension : 10.0
[     7.532] (++) using VT number 2

[     7.532] (II) systemd-logind: took control of session /org/freedesktop/login1/session/_31
[     7.533] (II) xfree86: Adding drm device (/dev/dri/card0)
[     7.533] (II) Platform probe for /sys/devices/pci0000:00/0000:00:02.0/drm/card0
[     7.534] (II) systemd-logind: got fd for /dev/dri/card0 226:0 fd 14 paused 0
[     7.535] (--) PCI:*(0@0:2:0) 8086:46a3:8086:2212 rev 12, Mem @ 0x6000000000/16777216, 0x4000000000/268435456, I/O @ 0x00005000/64, BIOS @ 0x????????/131072
[     7.535] (II) LoadModule: "glx"
[     7.540] (II) Loading /usr/lib/xorg/modules/extensions/libglx.so
[     7.560] (II) Module glx: vendor="X.Org Foundation"
[     7.560] 	compiled for ********, module version = 1.0.0
[     7.560] 	ABI class: X.Org Server Extension, version 10.0
[     7.560] (==) Matched modesetting as autoconfigured driver 0
[     7.560] (==) Matched fbdev as autoconfigured driver 1
[     7.560] (==) Matched vesa as autoconfigured driver 2
[     7.560] (==) Assigned the driver to the xf86ConfigLayout
[     7.560] (II) LoadModule: "modesetting"
[     7.561] (II) Loading /usr/lib/xorg/modules/drivers/modesetting_drv.so
[     7.563] (II) Module modesetting: vendor="X.Org Foundation"
[     7.563] 	compiled for ********, module version = 1.21.1
[     7.563] 	Module class: X.Org Video Driver
[     7.563] 	ABI class: X.Org Video Driver, version 25.2
[     7.563] (II) LoadModule: "fbdev"
[     7.563] (II) Loading /usr/lib/xorg/modules/drivers/fbdev_drv.so
[     7.564] (II) Module fbdev: vendor="X.Org Foundation"
[     7.564] 	compiled for ********, module version = 0.5.0
[     7.564] 	Module class: X.Org Video Driver
[     7.564] 	ABI class: X.Org Video Driver, version 25.2
[     7.564] (II) LoadModule: "vesa"
[     7.564] (II) Loading /usr/lib/xorg/modules/drivers/vesa_drv.so
[     7.564] (II) Module vesa: vendor="X.Org Foundation"
[     7.564] 	compiled for ********, module version = 2.5.0
[     7.564] 	Module class: X.Org Video Driver
[     7.564] 	ABI class: X.Org Video Driver, version 25.2
[     7.564] (II) modesetting: Driver for Modesetting Kernel Drivers: kms
[     7.564] (II) FBDEV: driver for framebuffer: fbdev
[     7.564] (II) VESA: driver for VESA chipsets: vesa
[     7.565] xf86EnableIO: failed to enable I/O ports 0000-03ff (Operation not permitted)
[     7.565] (II) modeset(0): using drv /dev/dri/card0
[     7.565] (WW) Falling back to old probe method for fbdev
[     7.565] (II) Loading sub module "fbdevhw"
[     7.565] (II) LoadModule: "fbdevhw"
[     7.565] (II) Loading /usr/lib/xorg/modules/libfbdevhw.so
[     7.565] (II) Module fbdevhw: vendor="X.Org Foundation"
[     7.565] 	compiled for ********, module version = 0.0.2
[     7.565] 	ABI class: X.Org Video Driver, version 25.2
[     7.565] (EE) open /dev/fb0: No such file or directory
[     7.565] (WW) VGA arbiter: cannot open kernel arbiter, no multi-card support
[     7.565] (II) modeset(0): Creating default Display subsection in Screen section
	"Default Screen Section" for depth/fbbpp 24/32
[     7.565] (==) modeset(0): Depth 24, (==) framebuffer bpp 32
[     7.565] (==) modeset(0): RGB weight 888
[     7.565] (==) modeset(0): Default visual is TrueColor
[     7.565] (II) Loading sub module "glamoregl"
[     7.565] (II) LoadModule: "glamoregl"
[     7.565] (II) Loading /usr/lib/xorg/modules/libglamoregl.so
[     7.573] (II) Module glamoregl: vendor="X.Org Foundation"
[     7.573] 	compiled for ********, module version = 1.0.1
[     7.573] 	ABI class: X.Org ANSI C Emulation, version 0.4
[     8.014] (II) modeset(0): glamor X acceleration enabled on Mesa Intel(R) Graphics (ADL GT2)
[     8.014] (II) modeset(0): glamor initialized
[     8.014] (==) modeset(0): VariableRefresh: disabled
[     8.014] (==) modeset(0): AsyncFlipSecondaries: disabled
[     8.014] (II) modeset(0): Output HDMI-1 has no monitor section
[     8.014] (II) modeset(0): Output DP-1 has no monitor section
[     8.014] (II) modeset(0): Output HDMI-2 has no monitor section
[     8.014] (II) modeset(0): Output HDMI-3 has no monitor section
[     8.014] (II) modeset(0): Output DP-2 has no monitor section
[     8.014] (II) modeset(0): Output HDMI-4 has no monitor section
[     8.015] (II) modeset(0): EDID for output HDMI-1
[     8.015] (II) modeset(0): EDID for output DP-1
[     8.015] (II) modeset(0): EDID for output HDMI-2
[     8.015] (II) modeset(0): EDID for output HDMI-3
[     8.015] (II) modeset(0): EDID for output DP-2
[     8.015] (II) modeset(0): EDID for output HDMI-4
[     8.015] (II) modeset(0): Output HDMI-1 disconnected
[     8.015] (II) modeset(0): Output DP-1 disconnected
[     8.015] (II) modeset(0): Output HDMI-2 disconnected
[     8.015] (II) modeset(0): Output HDMI-3 disconnected
[     8.015] (II) modeset(0): Output DP-2 disconnected
[     8.015] (II) modeset(0): Output HDMI-4 disconnected
[     8.015] (WW) modeset(0): No outputs definitely connected, trying again...
[     8.015] (II) modeset(0): Output HDMI-1 disconnected
[     8.015] (II) modeset(0): Output DP-1 disconnected
[     8.015] (II) modeset(0): Output HDMI-2 disconnected
[     8.015] (II) modeset(0): Output HDMI-3 disconnected
[     8.015] (II) modeset(0): Output DP-2 disconnected
[     8.015] (II) modeset(0): Output HDMI-4 disconnected
[     8.015] (WW) modeset(0): Unable to find connected outputs - setting 1024x768 initial framebuffer
[     8.015] (==) modeset(0): Using gamma correction (1.0, 1.0, 1.0)
[     8.015] (==) modeset(0): DPI set to (96, 96)
[     8.015] (II) Loading sub module "fb"
[     8.015] (II) LoadModule: "fb"
[     8.015] (II) Module "fb" already built-in
[     8.015] (II) UnloadModule: "fbdev"
[     8.015] (II) Unloading fbdev
[     8.015] (II) UnloadSubModule: "fbdevhw"
[     8.015] (II) Unloading fbdevhw
[     8.015] (II) UnloadModule: "vesa"
[     8.015] (II) Unloading vesa
[     8.055] (==) modeset(0): Backing store enabled
[     8.055] (==) modeset(0): Silken mouse enabled
[     8.057] (II) modeset(0): Initializing kms color map for depth 24, 8 bpc.
[     8.057] (==) modeset(0): DPMS enabled
[     8.057] (II) modeset(0): [DRI2] Setup complete
[     8.057] (II) modeset(0): [DRI2]   DRI driver: iris
[     8.057] (II) modeset(0): [DRI2]   VDPAU driver: va_gl
[     8.057] (II) Initializing extension Generic Event Extension
[     8.057] (II) Initializing extension SHAPE
[     8.057] (II) Initializing extension MIT-SHM
[     8.057] (II) Initializing extension XInputExtension
[     8.058] (II) Initializing extension XTEST
[     8.058] (II) Initializing extension BIG-REQUESTS
[     8.058] (II) Initializing extension SYNC
[     8.058] (II) Initializing extension XKEYBOARD
[     8.058] (II) Initializing extension XC-MISC
[     8.058] (II) Initializing extension SECURITY
[     8.058] (II) Initializing extension XFIXES
[     8.058] (II) Initializing extension RENDER
[     8.058] (II) Initializing extension RANDR
[     8.059] (II) Initializing extension COMPOSITE
[     8.059] (II) Initializing extension DAMAGE
[     8.059] (II) Initializing extension MIT-SCREEN-SAVER
[     8.059] (II) Initializing extension DOUBLE-BUFFER
[     8.059] (II) Initializing extension RECORD
[     8.059] (II) Initializing extension DPMS
[     8.060] (II) Initializing extension Present
[     8.060] (II) Initializing extension DRI3
[     8.060] (II) Initializing extension X-Resource
[     8.060] (II) Initializing extension XVideo
[     8.060] (II) Initializing extension XVideo-MotionCompensation
[     8.060] (II) Initializing extension SELinux
[     8.060] (II) SELinux: Disabled on system
[     8.060] (II) Initializing extension GLX
[     8.065] (II) AIGLX: Loaded and initialized iris
[     8.065] (II) GLX: Initialized DRI2 GL provider for screen 0
[     8.065] (II) Initializing extension XFree86-VidModeExtension
[     8.065] (II) Initializing extension XFree86-DGA
[     8.065] (II) Initializing extension XFree86-DRI
[     8.065] (II) Initializing extension DRI2
[     8.066] (II) modeset(0): Damage tracking initialized
[     8.141] (II) config/udev: Adding input device Power Button (/dev/input/event2)
[     8.141] (**) Power Button: Applying InputClass "libinput keyboard catchall"
[     8.141] (II) LoadModule: "libinput"
[     8.141] (II) Loading /usr/lib/xorg/modules/input/libinput_drv.so
[     8.145] (II) Module libinput: vendor="X.Org Foundation"
[     8.145] 	compiled for 1.20.14, module version = 1.2.1
[     8.145] 	Module class: X.Org XInput Driver
[     8.145] 	ABI class: X.Org XInput driver, version 24.1
[     8.145] (II) Using input driver 'libinput' for 'Power Button'
[     8.146] (II) systemd-logind: got fd for /dev/input/event2 13:66 fd 28 paused 0
[     8.146] (**) Power Button: always reports core events
[     8.146] (**) Option "Device" "/dev/input/event2"
[     8.153] (II) event2  - Power Button: is tagged by udev as: Keyboard
[     8.153] (II) event2  - Power Button: device is a keyboard
[     8.153] (II) event2  - Power Button: device removed
[     8.153] (**) Option "config_info" "udev:/sys/devices/LNXSYSTM:00/LNXPWRBN:00/input/input2/event2"
[     8.153] (II) XINPUT: Adding extended input device "Power Button" (type: KEYBOARD, id 6)
[     8.153] (**) Option "xkb_model" "pc105"
[     8.153] (**) Option "xkb_layout" "cn"
[     8.164] (II) event2  - Power Button: is tagged by udev as: Keyboard
[     8.164] (II) event2  - Power Button: device is a keyboard
[     8.164] (II) config/udev: Adding input device Video Bus (/dev/input/event4)
[     8.164] (**) Video Bus: Applying InputClass "libinput keyboard catchall"
[     8.164] (II) Using input driver 'libinput' for 'Video Bus'
[     8.165] (II) systemd-logind: got fd for /dev/input/event4 13:68 fd 31 paused 0
[     8.165] (**) Video Bus: always reports core events
[     8.165] (**) Option "Device" "/dev/input/event4"
[     8.165] (II) event4  - Video Bus: is tagged by udev as: Keyboard
[     8.165] (II) event4  - Video Bus: device is a keyboard
[     8.165] (II) event4  - Video Bus: device removed
[     8.165] (**) Option "config_info" "udev:/sys/devices/LNXSYSTM:00/LNXSYBUS:00/PNP0A08:00/LNXVIDEO:00/input/input4/event4"
[     8.165] (II) XINPUT: Adding extended input device "Video Bus" (type: KEYBOARD, id 7)
[     8.165] (**) Option "xkb_model" "pc105"
[     8.165] (**) Option "xkb_layout" "cn"
[     8.166] (II) event4  - Video Bus: is tagged by udev as: Keyboard
[     8.166] (II) event4  - Video Bus: device is a keyboard
[     8.166] (II) config/udev: Adding input device Power Button (/dev/input/event1)
[     8.166] (**) Power Button: Applying InputClass "libinput keyboard catchall"
[     8.166] (II) Using input driver 'libinput' for 'Power Button'
[     8.167] (II) systemd-logind: got fd for /dev/input/event1 13:65 fd 32 paused 0
[     8.167] (**) Power Button: always reports core events
[     8.167] (**) Option "Device" "/dev/input/event1"
[     8.167] (II) event1  - Power Button: is tagged by udev as: Keyboard
[     8.167] (II) event1  - Power Button: device is a keyboard
[     8.167] (II) event1  - Power Button: device removed
[     8.167] (**) Option "config_info" "udev:/sys/devices/LNXSYSTM:00/LNXSYBUS:00/PNP0C0C:00/input/input1/event1"
[     8.167] (II) XINPUT: Adding extended input device "Power Button" (type: KEYBOARD, id 8)
[     8.167] (**) Option "xkb_model" "pc105"
[     8.167] (**) Option "xkb_layout" "cn"
[     8.167] (II) event1  - Power Button: is tagged by udev as: Keyboard
[     8.167] (II) event1  - Power Button: device is a keyboard
[     8.168] (II) config/udev: Adding input device Sleep Button (/dev/input/event0)
[     8.168] (**) Sleep Button: Applying InputClass "libinput keyboard catchall"
[     8.168] (II) Using input driver 'libinput' for 'Sleep Button'
[     8.168] (II) systemd-logind: got fd for /dev/input/event0 13:64 fd 33 paused 0
[     8.168] (**) Sleep Button: always reports core events
[     8.168] (**) Option "Device" "/dev/input/event0"
[     8.169] (II) event0  - Sleep Button: is tagged by udev as: Keyboard
[     8.169] (II) event0  - Sleep Button: device is a keyboard
[     8.169] (II) event0  - Sleep Button: device removed
[     8.169] (**) Option "config_info" "udev:/sys/devices/LNXSYSTM:00/LNXSYBUS:00/PNP0C0E:00/input/input0/event0"
[     8.169] (II) XINPUT: Adding extended input device "Sleep Button" (type: KEYBOARD, id 9)
[     8.169] (**) Option "xkb_model" "pc105"
[     8.169] (**) Option "xkb_layout" "cn"
[     8.169] (II) event0  - Sleep Button: is tagged by udev as: Keyboard
[     8.169] (II) event0  - Sleep Button: device is a keyboard
[     8.170] (II) config/udev: Adding input device HDA Intel PCH HDMI/DP,pcm=3 (/dev/input/event5)
[     8.170] (II) No input driver specified, ignoring this device.
[     8.170] (II) This device may have been added with another device file.
[     8.170] (II) config/udev: Adding input device HDA Intel PCH HDMI/DP,pcm=7 (/dev/input/event6)
[     8.170] (II) No input driver specified, ignoring this device.
[     8.170] (II) This device may have been added with another device file.
[     8.170] (II) config/udev: Adding input device HDA Intel PCH HDMI/DP,pcm=8 (/dev/input/event7)
[     8.170] (II) No input driver specified, ignoring this device.
[     8.170] (II) This device may have been added with another device file.
[     8.170] (II) config/udev: Adding input device HDA Intel PCH HDMI/DP,pcm=9 (/dev/input/event8)
[     8.170] (II) No input driver specified, ignoring this device.
[     8.170] (II) This device may have been added with another device file.
[     8.170] (II) config/udev: Adding input device Intel HID events (/dev/input/event3)
[     8.170] (**) Intel HID events: Applying InputClass "libinput keyboard catchall"
[     8.170] (II) Using input driver 'libinput' for 'Intel HID events'
[     8.171] (II) systemd-logind: got fd for /dev/input/event3 13:67 fd 34 paused 0
[     8.171] (**) Intel HID events: always reports core events
[     8.171] (**) Option "Device" "/dev/input/event3"
[     8.171] (II) event3  - Intel HID events: is tagged by udev as: Keyboard
[     8.171] (II) event3  - Intel HID events: device is a keyboard
[     8.171] (II) event3  - Intel HID events: device removed
[     8.171] (**) Option "config_info" "udev:/sys/devices/platform/INTC1070:00/input/input3/event3"
[     8.171] (II) XINPUT: Adding extended input device "Intel HID events" (type: KEYBOARD, id 10)
[     8.171] (**) Option "xkb_model" "pc105"
[     8.171] (**) Option "xkb_layout" "cn"
[     8.171] (II) event3  - Intel HID events: is tagged by udev as: Keyboard
[     8.171] (II) event3  - Intel HID events: device is a keyboard
