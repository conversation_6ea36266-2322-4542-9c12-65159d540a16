[496145.578] _XSERVTransSocketUNIXCreateListener: ...SocketCreateListener() failed
[496145.578] _XSERVTransMakeAllCOTSServerListeners: server already running
[496145.578] (--) Log file renamed from "/home/<USER>/.local/share/xorg/Xorg.pid-2218237.log" to "/home/<USER>/.local/share/xorg/Xorg.1.log"
[496145.579] 
X.Org X Server 1.21.1.7
X Protocol Version 11, Revision 0
[496145.579] Current Operating System: Linux user-Default-string 6.2.0-39-generic #40-Ubuntu SMP PREEMPT_DYNAMIC Tue Nov 14 14:18:00 UTC 2023 x86_64
[496145.579] Kernel command line: BOOT_IMAGE=/boot/vmlinuz-6.2.0-39-generic root=UUID=141f9a10-ded6-4bb6-8e6b-34c8017a2dea ro quiet splash vt.handoff=7
[496145.579] xorg-server 2:21.1.7-1ubuntu3.6 (For technical support please see http://www.ubuntu.com/support) 
[496145.579] Current version of pixman: 0.42.2
[496145.579] 	Before reporting problems, check http://wiki.x.org
	to make sure that you have the latest version.
[496145.579] Markers: (--) probed, (**) from config file, (==) default setting,
	(++) from command line, (!!) notice, (II) informational,
	(WW) warning, (EE) error, (NI) not implemented, (??) unknown.
[496145.579] (==) Log file: "/home/<USER>/.local/share/xorg/Xorg.1.log", Time: Tue Jan 21 10:53:06 2025
[496145.579] (==) Using system config directory "/usr/share/X11/xorg.conf.d"
[496145.579] (==) No Layout section.  Using the first Screen section.
[496145.579] (==) No screen section available. Using defaults.
[496145.579] (**) |-->Screen "Default Screen Section" (0)
[496145.579] (**) |   |-->Monitor "<default monitor>"
[496145.579] (==) No monitor specified for screen "Default Screen Section".
	Using a default monitor configuration.
[496145.579] (==) Automatically adding devices
[496145.579] (==) Automatically enabling devices
[496145.579] (==) Automatically adding GPU devices
[496145.579] (==) Automatically binding GPU devices
[496145.579] (==) Max clients allowed: 256, resource mask: 0x1fffff
[496145.579] (WW) The directory "/usr/share/fonts/X11/cyrillic" does not exist.
[496145.579] 	Entry deleted from font path.
[496145.579] (WW) The directory "/usr/share/fonts/X11/100dpi/" does not exist.
[496145.579] 	Entry deleted from font path.
[496145.579] (WW) The directory "/usr/share/fonts/X11/75dpi/" does not exist.
[496145.579] 	Entry deleted from font path.
[496145.579] (WW) The directory "/usr/share/fonts/X11/100dpi" does not exist.
[496145.579] 	Entry deleted from font path.
[496145.579] (WW) The directory "/usr/share/fonts/X11/75dpi" does not exist.
[496145.579] 	Entry deleted from font path.
[496145.579] (==) FontPath set to:
	/usr/share/fonts/X11/misc,
	/usr/share/fonts/X11/Type1,
	built-ins
[496145.579] (==) ModulePath set to "/usr/lib/xorg/modules"
[496145.579] (II) The server relies on udev to provide the list of input devices.
	If no devices become available, reconfigure udev or disable AutoAddDevices.
[496145.579] (II) Loader magic: 0x5609c3276020
[496145.579] (II) Module ABI versions:
[496145.579] 	X.Org ANSI C Emulation: 0.4
[496145.579] 	X.Org Video Driver: 25.2
[496145.579] 	X.Org XInput driver : 24.4
[496145.579] 	X.Org Server Extension : 10.0
[496145.580] (++) using VT number 2

[496145.580] (EE) systemd-logind: failed to get session: PID 2218237 does not belong to any known session
[496145.581] (II) xfree86: Adding drm device (/dev/dri/card0)
[496145.581] (II) Platform probe for /sys/devices/pci0000:00/0000:00:02.0/drm/card0
[496145.582] (--) PCI:*(0@0:2:0) 8086:46a3:8086:2212 rev 12, Mem @ 0x6000000000/16777216, 0x4000000000/268435456, I/O @ 0x00005000/64, BIOS @ 0x????????/131072
[496145.582] (II) LoadModule: "glx"
[496145.582] (II) Loading /usr/lib/xorg/modules/extensions/libglx.so
[496145.582] (II) Module glx: vendor="X.Org Foundation"
[496145.582] 	compiled for 1.21.1.7, module version = 1.0.0
[496145.582] 	ABI class: X.Org Server Extension, version 10.0
[496145.582] (==) Matched modesetting as autoconfigured driver 0
[496145.582] (==) Matched fbdev as autoconfigured driver 1
[496145.582] (==) Matched vesa as autoconfigured driver 2
[496145.582] (==) Assigned the driver to the xf86ConfigLayout
[496145.582] (II) LoadModule: "modesetting"
[496145.582] (II) Loading /usr/lib/xorg/modules/drivers/modesetting_drv.so
[496145.582] (II) Module modesetting: vendor="X.Org Foundation"
[496145.582] 	compiled for 1.21.1.7, module version = 1.21.1
[496145.582] 	Module class: X.Org Video Driver
[496145.582] 	ABI class: X.Org Video Driver, version 25.2
[496145.582] (II) LoadModule: "fbdev"
[496145.582] (II) Loading /usr/lib/xorg/modules/drivers/fbdev_drv.so
[496145.582] (II) Module fbdev: vendor="X.Org Foundation"
[496145.582] 	compiled for ********, module version = 0.5.0
[496145.582] 	Module class: X.Org Video Driver
[496145.582] 	ABI class: X.Org Video Driver, version 25.2
[496145.582] (II) LoadModule: "vesa"
[496145.583] (II) Loading /usr/lib/xorg/modules/drivers/vesa_drv.so
[496145.583] (II) Module vesa: vendor="X.Org Foundation"
[496145.583] 	compiled for ********, module version = 2.5.0
[496145.583] 	Module class: X.Org Video Driver
[496145.583] 	ABI class: X.Org Video Driver, version 25.2
[496145.583] (II) modesetting: Driver for Modesetting Kernel Drivers: kms
[496145.583] (II) FBDEV: driver for framebuffer: fbdev
[496145.583] (II) VESA: driver for VESA chipsets: vesa
[496145.583] (EE) 
Fatal server error:
[496145.583] (EE) xf86OpenConsole: Cannot open virtual console 2 (Permission denied)
[496145.583] (EE) 
[496145.583] (EE) 
Please consult the The X.Org Foundation support 
	 at http://wiki.x.org
 for help. 
[496145.583] (EE) Please also check the log file at "/home/<USER>/.local/share/xorg/Xorg.1.log" for additional information.
[496145.583] (EE) 
[496145.583] (WW) xf86CloseConsole: KDSETMODE failed: Bad file descriptor
[496145.583] (WW) xf86CloseConsole: VT_GETMODE failed: Bad file descriptor
[496145.583] (EE) Server terminated with error (1). Closing log file.
