[496137.625] _XSERVTransSocketUNIXCreateListener: ...SocketCreateListener() failed
[496137.625] _XSERVTransMakeAllCOTSServerListeners: server already running
[496137.627] (--) Log file renamed from "/home/<USER>/.local/share/xorg/Xorg.pid-2218204.log" to "/home/<USER>/.local/share/xorg/Xorg.1.log"
[496137.628] 
X.Org X Server 1.21.1.7
X Protocol Version 11, Revision 0
[496137.628] Current Operating System: Linux user-Default-string 6.2.0-39-generic #40-Ubuntu SMP PREEMPT_DYNAMIC Tue Nov 14 14:18:00 UTC 2023 x86_64
[496137.628] Kernel command line: BOOT_IMAGE=/boot/vmlinuz-6.2.0-39-generic root=UUID=141f9a10-ded6-4bb6-8e6b-34c8017a2dea ro quiet splash vt.handoff=7
[496137.628] xorg-server 2:21.1.7-1ubuntu3.6 (For technical support please see http://www.ubuntu.com/support) 
[496137.628] Current version of pixman: 0.42.2
[496137.628] 	Before reporting problems, check http://wiki.x.org
	to make sure that you have the latest version.
[496137.628] Markers: (--) probed, (**) from config file, (==) default setting,
	(++) from command line, (!!) notice, (II) informational,
	(WW) warning, (EE) error, (NI) not implemented, (??) unknown.
[496137.628] (==) Log file: "/home/<USER>/.local/share/xorg/Xorg.1.log", Time: Tue Jan 21 10:52:58 2025
[496137.629] (==) Using system config directory "/usr/share/X11/xorg.conf.d"
[496137.630] (==) No Layout section.  Using the first Screen section.
[496137.630] (==) No screen section available. Using defaults.
[496137.630] (**) |-->Screen "Default Screen Section" (0)
[496137.630] (**) |   |-->Monitor "<default monitor>"
[496137.631] (==) No monitor specified for screen "Default Screen Section".
	Using a default monitor configuration.
[496137.631] (==) Automatically adding devices
[496137.631] (==) Automatically enabling devices
[496137.631] (==) Automatically adding GPU devices
[496137.631] (==) Automatically binding GPU devices
[496137.631] (==) Max clients allowed: 256, resource mask: 0x1fffff
[496137.634] (WW) The directory "/usr/share/fonts/X11/cyrillic" does not exist.
[496137.634] 	Entry deleted from font path.
[496137.634] (WW) The directory "/usr/share/fonts/X11/100dpi/" does not exist.
[496137.634] 	Entry deleted from font path.
[496137.634] (WW) The directory "/usr/share/fonts/X11/75dpi/" does not exist.
[496137.634] 	Entry deleted from font path.
[496137.635] (WW) The directory "/usr/share/fonts/X11/100dpi" does not exist.
[496137.635] 	Entry deleted from font path.
[496137.635] (WW) The directory "/usr/share/fonts/X11/75dpi" does not exist.
[496137.635] 	Entry deleted from font path.
[496137.635] (==) FontPath set to:
	/usr/share/fonts/X11/misc,
	/usr/share/fonts/X11/Type1,
	built-ins
[496137.635] (==) ModulePath set to "/usr/lib/xorg/modules"
[496137.635] (II) The server relies on udev to provide the list of input devices.
	If no devices become available, reconfigure udev or disable AutoAddDevices.
[496137.635] (II) Loader magic: 0x5610b534b020
[496137.635] (II) Module ABI versions:
[496137.635] 	X.Org ANSI C Emulation: 0.4
[496137.635] 	X.Org Video Driver: 25.2
[496137.635] 	X.Org XInput driver : 24.4
[496137.635] 	X.Org Server Extension : 10.0
[496137.635] (++) using VT number 2

[496137.636] (EE) systemd-logind: failed to get session: PID 2218204 does not belong to any known session
[496137.636] (II) xfree86: Adding drm device (/dev/dri/card0)
[496137.636] (II) Platform probe for /sys/devices/pci0000:00/0000:00:02.0/drm/card0
[496137.637] (--) PCI:*(0@0:2:0) 8086:46a3:8086:2212 rev 12, Mem @ 0x6000000000/16777216, 0x4000000000/268435456, I/O @ 0x00005000/64, BIOS @ 0x????????/131072
[496137.637] (II) LoadModule: "glx"
[496137.641] (II) Loading /usr/lib/xorg/modules/extensions/libglx.so
[496137.647] (II) Module glx: vendor="X.Org Foundation"
[496137.647] 	compiled for 1.21.1.7, module version = 1.0.0
[496137.647] 	ABI class: X.Org Server Extension, version 10.0
[496137.647] (==) Matched modesetting as autoconfigured driver 0
[496137.647] (==) Matched fbdev as autoconfigured driver 1
[496137.647] (==) Matched vesa as autoconfigured driver 2
[496137.647] (==) Assigned the driver to the xf86ConfigLayout
[496137.647] (II) LoadModule: "modesetting"
[496137.647] (II) Loading /usr/lib/xorg/modules/drivers/modesetting_drv.so
[496137.648] (II) Module modesetting: vendor="X.Org Foundation"
[496137.648] 	compiled for 1.21.1.7, module version = 1.21.1
[496137.648] 	Module class: X.Org Video Driver
[496137.648] 	ABI class: X.Org Video Driver, version 25.2
[496137.648] (II) LoadModule: "fbdev"
[496137.648] (II) Loading /usr/lib/xorg/modules/drivers/fbdev_drv.so
[496137.648] (II) Module fbdev: vendor="X.Org Foundation"
[496137.648] 	compiled for ********, module version = 0.5.0
[496137.648] 	Module class: X.Org Video Driver
[496137.648] 	ABI class: X.Org Video Driver, version 25.2
[496137.648] (II) LoadModule: "vesa"
[496137.648] (II) Loading /usr/lib/xorg/modules/drivers/vesa_drv.so
[496137.649] (II) Module vesa: vendor="X.Org Foundation"
[496137.649] 	compiled for ********, module version = 2.5.0
[496137.649] 	Module class: X.Org Video Driver
[496137.649] 	ABI class: X.Org Video Driver, version 25.2
[496137.649] (II) modesetting: Driver for Modesetting Kernel Drivers: kms
[496137.649] (II) FBDEV: driver for framebuffer: fbdev
[496137.649] (II) VESA: driver for VESA chipsets: vesa
[496137.649] (EE) 
Fatal server error:
[496137.649] (EE) xf86OpenConsole: Cannot open virtual console 2 (Permission denied)
[496137.649] (EE) 
[496137.649] (EE) 
Please consult the The X.Org Foundation support 
	 at http://wiki.x.org
 for help. 
[496137.649] (EE) Please also check the log file at "/home/<USER>/.local/share/xorg/Xorg.1.log" for additional information.
[496137.649] (EE) 
[496137.649] (WW) xf86CloseConsole: KDSETMODE failed: Bad file descriptor
[496137.649] (WW) xf86CloseConsole: VT_GETMODE failed: Bad file descriptor
[496137.649] (EE) Server terminated with error (1). Closing log file.
