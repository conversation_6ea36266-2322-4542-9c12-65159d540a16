#bashbash 
#!/bin/bash 
# crontab.sh  
interval=1
observe_number=61
observe_ind=1
while [ $observe_ind -lt $observe_number ]
do 
    #echo $observe_ind
    start_time=$(date +%s.%3N)
    echo $start_time
    # 执行任务 
    sh /home/<USER>/dic/prog/runLiveVideoResultProcess.sh
    observe_ind=$(echo "$observe_ind + 1" | bc)
    end_time=$(date +%s.%3N)
    elapsed=$(echo "$end_time - $start_time" | bc)
    sleep_time=$(echo "$interval - $elapsed - 0.016" | bc)
    
    sleep $sleep_time 
done 

