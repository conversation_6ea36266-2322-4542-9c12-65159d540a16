#!/bin/bash
program_dir='/home/<USER>/dic/prog/'
orig_dir='/home/<USER>/orig/'
orig_list_file='/home/<USER>/orig/originalObservation.txt'
master_dir='/home/<USER>/dic/para/master/'
multi_master_dir='/home/<USER>/dic/para/multiMaster/'
best_master_dir='/home/<USER>/dic/prog/master/'
point_list_file='/home/<USER>/dic/para/points_list.txt'
dic_result_file='/home/<USER>/dic/para/points_displacement.txt'

#master dir
if [ ! -d "$multi_master_dir" ];then
    mkdir $multi_master_dir
fi
# if [ ! -d "$best_master_dir" ];then
#     mkdir $best_master_dir
# fi

if [ ! -f "$orig_list_file" ];then 
    echo "original observation list file not exit"
    exit
fi
if [ ! -f "$point_list_file" ];then 
    echo "point list file not exit"
    exit
fi

# master image
master_file_name=$(ls $master_dir | grep -E '\.jpg$')
master_img_file=$master_dir$master_file_name
if [ ! -f "$master_img_file" ];then
    echo "master image file not exit"
    exit
fi

# best master image
best_master_file=$master_img_file
# best_master_file_name=$(ls $best_master_dir | grep -E '\.jpg$')
# best_master_file=$best_master_dir$best_master_file_name
# if [ ! -f "$best_master_file" ];then
#    cp $master_img_file $best_master_dir
#    best_master_file_name=$(ls $best_master_dir | grep -E '\.jpg$')
#    best_master_file=$best_master_dir$best_master_file_name
# fi

#slave image
last_line=$(awk 'END {print}' $orig_list_file)
slave_file_name=$(echo $last_line | awk '{split($0,a," ");print $2}')
slave_img_file=$orig_dir$slave_file_name
if [ ! -f "$slave_img_file" ];then
    echo "slave image file not exit"
    exit
fi

# search best master
multi_list_file='/home/<USER>/dic/prog/multi_master_list.txt'
ls $master_dir*.jpg > $multi_list_file
ls $multi_master_dir*.jpg >> $multi_list_file
multi_num=`cat $multi_list_file | wc -l`
# echo $multi_num
# cat $multi_list_file
# LD_LIBRARY_PATH=$LD_LIBRARY_PATH:/usrappfs/lib                     
# export LD_LIBRARY_PATH
if [ $multi_num -gt 1 ];then
    temp_master_file='/home/<USER>/dic/prog/dic_temp_master.txt'
    rm $temp_master_file
    chmod 777 /home/<USER>/dic/prog/searchMasterHimixLiunx
    /home/<USER>/dic/prog/searchMasterHimixLiunx $slave_img_file $multi_list_file $temp_master_file
    if [ -f "$temp_master_file" ];then
        best_master_file=`cat $temp_master_file`        
    fi 
fi
echo $best_master_file
#cp $best_master_file /home/<USER>/dic/para/bestMaster/master.jpg

temp_master_points_file='/home/<USER>/dic/prog/temp_master_points.txt'
cp $point_list_file $temp_master_points_file
echo $best_master_file >> $temp_master_points_file

master_points_file='/home/<USER>/dic/prog/master_points.txt'
if [ ! -f "$master_points_file" ];then
    cp $temp_master_points_file $master_points_file
fi

diff_string=`diff $temp_master_points_file $master_points_file`
diff_string_length=${#diff_string}
ps -ef > /home/<USER>/dic/prog/ps.log
videoPL=`cat /home/<USER>/dic/prog/ps.log | grep liveVideoDicProcessWeb | wc -m`
if [ $diff_string_length -gt 0 ];then
    if [ $videoPL -gt 0 ];then
        videoPid=`cat /home/<USER>/dic/prog/ps.log | grep liveVideoDicProcessWeb | awk '{print $2}'`
        kill $videoPid
    fi    
    chmod /home/<USER>/dic/prog/liveVideoDicProcessWeb 
    nohup /home/<USER>/dic/prog/liveVideoDicProcessWeb 192.168.1.163 432000 15 /home/<USER>/dic/para/points_list.txt $best_master_file /home/<USER>/dic/prog/video_temp_result.txt &
fi

# run liveVideoDicProcessWeb
ps -ef > /home/<USER>/dic/prog/ps.log
videoPL=`cat /home/<USER>/dic/prog/ps.log | grep liveVideoDicProcessWeb | wc -m`
if [ $videoPL -eq 0 ];then
    chmod /home/<USER>/dic/prog/liveVideoDicProcessWeb 
    nohup /home/<USER>/dic/prog/liveVideoDicProcessWeb 192.168.1.163 432000 15 /home/<USER>/dic/para/points_list.txt $best_master_file /home/<USER>/dic/prog/video_temp_result.txt &
fi

cp $temp_master_points_file $master_points_file
# run dic
# LD_LIBRARY_PATH=$LD_LIBRARY_PATH:/usrappfs/lib
# export LD_LIBRARY_PATH
#temp_result_file='/home/<USER>/dic/prog/dic_temp_result.txt'
#rm $temp_result_file
#chmod 777 /home/<USER>/dic/prog/mainHimixLiunx
#/home/<USER>/dic/prog/mainHimixLiunx $point_list_file $best_master_file $slave_img_file $temp_result_file

# add time & temperature
#time_tempe=$(echo $last_line | awk '{split($0,a," ");print $1"  "$3}')
#sed -i "1i $time_tempe" $temp_result_file
#cp $temp_result_file $dic_result_file


