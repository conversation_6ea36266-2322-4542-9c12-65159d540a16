UID          PID    PPID  C STIME TTY          TIME CMD
root           1       0  0 9月17 ?       00:00:19 /sbin/init splash
root           2       0  0 9月17 ?       00:00:00 [kthreadd]
root           3       2  0 9月17 ?       00:00:00 [rcu_gp]
root           4       2  0 9月17 ?       00:00:00 [rcu_par_gp]
root           5       2  0 9月17 ?       00:00:00 [slub_flushwq]
root           6       2  0 9月17 ?       00:00:00 [netns]
root           8       2  0 9月17 ?       00:00:00 [kworker/0:0H-events_highpri]
root          10       2  0 9月17 ?       00:00:00 [mm_percpu_wq]
root          11       2  0 9月17 ?       00:00:00 [rcu_tasks_kthread]
root          12       2  0 9月17 ?       00:00:00 [rcu_tasks_rude_kthread]
root          13       2  0 9月17 ?       00:00:00 [rcu_tasks_trace_kthread]
root          14       2  0 9月17 ?       00:00:09 [ksoftirqd/0]
root          15       2  0 9月17 ?       00:01:47 [rcu_preempt]
root          16       2  0 9月17 ?       00:00:00 [migration/0]
root          17       2  0 9月17 ?       00:00:00 [idle_inject/0]
root          19       2  0 9月17 ?       00:00:00 [cpuhp/0]
root          20       2  0 9月17 ?       00:00:00 [cpuhp/1]
root          21       2  0 9月17 ?       00:00:00 [idle_inject/1]
root          22       2  0 9月17 ?       00:00:11 [migration/1]
root          23       2  0 9月17 ?       00:00:01 [ksoftirqd/1]
root          25       2  0 9月17 ?       00:00:00 [kworker/1:0H-events_highpri]
root          26       2  0 9月17 ?       00:00:00 [cpuhp/2]
root          27       2  0 9月17 ?       00:00:00 [idle_inject/2]
root          28       2  0 9月17 ?       00:00:00 [migration/2]
root          29       2  0 9月17 ?       00:00:04 [ksoftirqd/2]
root          31       2  0 9月17 ?       00:00:00 [kworker/2:0H-events_highpri]
root          32       2  0 9月17 ?       00:00:00 [cpuhp/3]
root          33       2  0 9月17 ?       00:00:00 [idle_inject/3]
root          34       2  0 9月17 ?       00:00:06 [migration/3]
root          35       2  0 9月17 ?       00:00:00 [ksoftirqd/3]
root          37       2  0 9月17 ?       00:00:00 [kworker/3:0H-events_highpri]
root          38       2  0 9月17 ?       00:00:00 [cpuhp/4]
root          39       2  0 9月17 ?       00:00:00 [idle_inject/4]
root          40       2  0 9月17 ?       00:00:00 [migration/4]
root          41       2  0 9月17 ?       00:00:04 [ksoftirqd/4]
root          43       2  0 9月17 ?       00:00:00 [kworker/4:0H-events_highpri]
root          44       2  0 9月17 ?       00:00:00 [cpuhp/5]
root          45       2  0 9月17 ?       00:00:00 [idle_inject/5]
root          46       2  0 9月17 ?       00:00:04 [migration/5]
root          47       2  0 9月17 ?       00:00:00 [ksoftirqd/5]
root          49       2  0 9月17 ?       00:00:00 [kworker/5:0H-events_highpri]
root          50       2  0 9月17 ?       00:00:00 [cpuhp/6]
root          51       2  0 9月17 ?       00:00:00 [idle_inject/6]
root          52       2  0 9月17 ?       00:00:00 [migration/6]
root          53       2  0 9月17 ?       00:00:04 [ksoftirqd/6]
root          55       2  0 9月17 ?       00:00:00 [kworker/6:0H-events_highpri]
root          56       2  0 9月17 ?       00:00:00 [cpuhp/7]
root          57       2  0 9月17 ?       00:00:00 [idle_inject/7]
root          58       2  0 9月17 ?       00:00:06 [migration/7]
root          59       2  0 9月17 ?       00:00:01 [ksoftirqd/7]
root          61       2  0 9月17 ?       00:00:00 [kworker/7:0H-events_highpri]
root          62       2  0 9月17 ?       00:00:00 [cpuhp/8]
root          63       2  0 9月17 ?       00:00:00 [idle_inject/8]
root          64       2  0 9月17 ?       00:00:08 [migration/8]
root          65       2  0 9月17 ?       00:00:00 [ksoftirqd/8]
root          67       2  0 9月17 ?       00:00:00 [kworker/8:0H-events_highpri]
root          68       2  0 9月17 ?       00:00:00 [cpuhp/9]
root          69       2  0 9月17 ?       00:00:00 [idle_inject/9]
root          70       2  0 9月17 ?       00:00:07 [migration/9]
root          71       2  0 9月17 ?       00:00:00 [ksoftirqd/9]
root          73       2  0 9月17 ?       00:00:00 [kworker/9:0H-events_highpri]
root          74       2  0 9月17 ?       00:00:00 [cpuhp/10]
root          75       2  0 9月17 ?       00:00:00 [idle_inject/10]
root          76       2  0 9月17 ?       00:00:09 [migration/10]
root          77       2  0 9月17 ?       00:00:00 [ksoftirqd/10]
root          79       2  0 9月17 ?       00:00:00 [kworker/10:0H-events_highpri]
root          80       2  0 9月17 ?       00:00:00 [cpuhp/11]
root          81       2  0 9月17 ?       00:00:00 [idle_inject/11]
root          82       2  0 9月17 ?       00:00:15 [migration/11]
root          83       2  0 9月17 ?       00:00:00 [ksoftirqd/11]
root          85       2  0 9月17 ?       00:00:00 [kworker/11:0H-events_highpri]
root          86       2  0 9月17 ?       00:00:00 [kdevtmpfs]
root          87       2  0 9月17 ?       00:00:00 [inet_frag_wq]
root          88       2  0 9月17 ?       00:00:00 [kauditd]
root          89       2  0 9月17 ?       00:00:00 [khungtaskd]
root          90       2  0 9月17 ?       00:00:00 [oom_reaper]
root          92       2  0 9月17 ?       00:00:00 [writeback]
root          93       2  0 9月17 ?       00:00:05 [kcompactd0]
root          94       2  0 9月17 ?       00:00:00 [ksmd]
root          95       2  0 9月17 ?       00:00:00 [khugepaged]
root          96       2  0 9月17 ?       00:00:00 [kintegrityd]
root          97       2  0 9月17 ?       00:00:00 [kblockd]
root          98       2  0 9月17 ?       00:00:00 [blkcg_punt_bio]
root         100       2  0 9月17 ?       00:00:00 [tpm_dev_wq]
root         101       2  0 9月17 ?       00:00:00 [ata_sff]
root         102       2  0 9月17 ?       00:00:00 [md]
root         103       2  0 9月17 ?       00:00:00 [edac-poller]
root         104       2  0 9月17 ?       00:00:00 [devfreq_wq]
root         105       2  0 9月17 ?       00:00:00 [watchdogd]
root         106       2  0 9月17 ?       00:00:00 [kworker/0:1H-kblockd]
root         107       2  0 9月17 ?       00:00:00 [kswapd0]
root         108       2  0 9月17 ?       00:00:00 [ecryptfs-kthread]
root         110       2  0 9月17 ?       00:00:00 [kthrotld]
root         111       2  0 9月17 ?       00:00:00 [irq/122-aerdrv]
root         112       2  0 9月17 ?       00:00:00 [irq/123-aerdrv]
root         113       2  0 9月17 ?       00:00:00 [acpi_thermal_pm]
root         114       2  0 9月17 ?       00:00:00 [hfi-updates]
root         115       2  0 9月17 ?       00:00:00 [mld]
root         116       2  0 9月17 ?       00:00:00 [ipv6_addrconf]
root         123       2  0 9月17 ?       00:00:00 [kstrp]
root         125       2  0 9月17 ?       00:00:05 [kworker/3:1-events]
root         127       2  0 9月17 ?       00:00:05 [kworker/8:1-events]
root         128       2  0 9月17 ?       00:00:09 [kworker/9:1-mm_percpu_wq]
root         129       2  0 9月17 ?       00:00:03 [kworker/10:1-events]
root         130       2  0 9月17 ?       00:00:02 [kworker/11:1-events]
root         131       2  0 9月17 ?       00:00:03 [kworker/7:1-events]
root         135       2  0 9月17 ?       00:00:00 [zswap-shrink]
root         136       2  0 9月17 ?       00:00:00 [kworker/u25:0]
root         140       2  0 9月17 ?       00:00:00 [charger_manager]
root         163       2  0 9月17 ?       00:00:00 [kworker/5:1H-kblockd]
root         167       2  0 9月17 ?       00:00:00 [kworker/6:1H-kblockd]
root         192       2  0 9月17 ?       00:00:00 [kworker/2:1H-kblockd]
root         193       2  0 9月17 ?       00:00:00 [kworker/4:1H-kblockd]
root         194       2  0 9月17 ?       00:00:00 [kworker/9:1H-kblockd]
root         195       2  0 9月17 ?       00:00:00 [kworker/11:1H-kblockd]
root         196       2  0 9月17 ?       00:00:00 [kworker/8:1H-kblockd]
root         197       2  0 9月17 ?       00:00:00 [kworker/3:1H-kblockd]
root         198       2  0 9月17 ?       00:00:00 [kworker/1:1H-kblockd]
root         199       2  0 9月17 ?       00:00:00 [kworker/7:1H-kblockd]
root         200       2  0 9月17 ?       00:00:00 [kworker/10:1H-kblockd]
root         220       2  0 9月17 ?       00:00:00 [scsi_eh_0]
root         221       2  0 9月17 ?       00:00:00 [scsi_tmf_0]
root         222       2  0 9月17 ?       00:00:00 [scsi_eh_1]
root         224       2  0 9月17 ?       00:00:00 [scsi_tmf_1]
root         271       2  0 9月17 ?       00:00:04 [jbd2/sda2-8]
root         272       2  0 9月17 ?       00:00:00 [ext4-rsv-conver]
root         315       1  0 9月17 ?       00:00:07 /lib/systemd/systemd-journald
root         353       1  0 9月17 ?       00:00:00 /lib/systemd/systemd-udevd
root         440       2  0 9月17 ?       00:00:00 [irq/128-mei_me]
root         466       2  0 9月17 ?       00:00:00 [cryptd]
root         485       2  0 9月17 ?       00:00:00 [card0-crtc0]
root         486       2  0 9月17 ?       00:00:00 [card0-crtc1]
root         487       2  0 9月17 ?       00:00:00 [card0-crtc2]
root         488       2  0 9月17 ?       00:00:00 [card0-crtc3]
root         636       2  0 9月17 ?       00:00:06 [kworker/1:2-mm_percpu_wq]
systemd+     652       1  0 9月17 ?       00:02:14 /lib/systemd/systemd-oomd
systemd+     654       1  0 9月17 ?       00:00:08 /lib/systemd/systemd-resolved
systemd+     655       1  0 9月17 ?       00:00:00 /lib/systemd/systemd-timesyncd
avahi        703       1  0 9月17 ?       00:03:46 avahi-daemon: running [user-Default-string.local]
message+     704       1  0 9月17 ?       00:00:14 @dbus-daemon --system --address=systemd: --nofork --nopidfile --systemd-activation --syslog-only
root         719       1  0 9月17 ?       00:00:05 /usr/sbin/irqbalance --foreground
root         770       1  0 9月17 ?       00:00:04 /usr/libexec/polkitd --no-debug
root         773       1  0 9月17 ?       00:00:00 /usr/libexec/power-profiles-daemon
root         777       1  0 9月17 ?       00:00:04 /usr/local/sunlogin/bin/oray_rundaemon -m server
root         781       1  0 9月17 ?       00:00:11 /usr/lib/snapd/snapd
root         784       1  0 9月17 ?       00:00:03 /usr/libexec/accounts-daemon
root         788       1  0 9月17 ?       00:00:01 /usr/sbin/cron -f -P
root         789       1  0 9月17 ?       00:00:00 /usr/libexec/switcheroo-control
root         792       1  0 9月17 ?       00:00:03 /lib/systemd/systemd-logind
root         794       1  0 9月17 ?       00:00:08 /usr/sbin/thermald --systemd --dbus-enable --adaptive
root         798       1  0 9月17 ?       00:00:06 /usr/libexec/udisks2/udisksd
avahi        808     703  0 9月17 ?       00:00:00 avahi-daemon: chroot helper
root         827       2  0 9月17 ?       00:00:37 [GEVThread0]
root         839       2  0 9月17 ?       00:00:38 [GEVThread0]
root         853       2  0 9月17 ?       00:00:37 [GEVThread0]
root         861       2  0 9月17 ?       00:00:40 [GEVThread0]
root         869     777  3 9月17 ?       00:45:41 /usr/local/sunlogin/bin/sunloginclient --mod=service
root         871       2  0 9月17 ?       00:00:39 [GEVThread0]
syslog       926       1  0 9月17 ?       00:00:01 /usr/sbin/rsyslogd -n -iNONE
root         932       1  0 9月17 ?       00:01:15 /usr/sbin/NetworkManager --no-daemon
root         936       1  0 9月17 ?       00:00:00 /usr/sbin/ModemManager
root         937       1  0 9月17 ?       00:00:00 /sbin/wpa_supplicant -u -s -O DIR=/run/wpa_supplicant GROUP=netdev
root         939       1  1 9月17 ?       00:21:23 /opt/MVS/logserver/MvLogServer
root         957       1  0 9月17 ?       00:00:00 /usr/bin/python3 /usr/share/unattended-upgrades/unattended-upgrade-shutdown --wait-for-signal
root         969       1  0 9月17 ?       00:00:00 /usr/sbin/gdm3
root         983     969  0 9月17 ?       00:00:00 gdm-session-worker [pam/gdm-autologin]
user         994       1  0 9月17 ?       00:00:11 /lib/systemd/systemd --user
user         995     994  0 9月17 ?       00:00:00 (sd-pam)
user        1004     994  0 9月17 ?       00:00:00 /usr/bin/pipewire
user        1007     994  0 9月17 ?       00:00:00 /usr/bin/wireplumber
user        1008     994  0 9月17 ?       00:00:00 /usr/bin/pipewire-pulse
user        1010     994  0 9月17 ?       00:00:00 /usr/bin/gnome-keyring-daemon --foreground --components=pkcs11,secrets --control-directory=/run/user/1000/keyring
user        1019     994  0 9月17 ?       00:00:00 /usr/bin/dbus-daemon --session --address=systemd: --nofork --nopidfile --systemd-activation --syslog-only
rtkit       1035       1  0 9月17 ?       00:00:01 /usr/libexec/rtkit-daemon
user        1048     983  0 9月17 tty2    00:00:00 /usr/libexec/gdm-x-session --run-script env GNOME_SHELL_SESSION_MODE=ubuntu /usr/bin/gnome-session --session=ubuntu
user        1055    1048  0 9月17 tty2    00:00:00 /usr/lib/xorg/Xorg vt2 -displayfd 3 -auth /run/user/1000/gdm/Xauthority -nolisten tcp -background none -noreset -keeptty -novtswitch -verbose 3
user        1165     994  0 9月17 ?       00:00:00 /usr/libexec/xdg-document-portal
user        1171     994  0 9月17 ?       00:00:00 /usr/libexec/xdg-permission-store
root        1178    1165  0 9月17 ?       00:00:00 fusermount3 -o rw,nosuid,nodev,fsname=portal,auto_unmount,subtype=portal -- /run/user/1000/doc
user        1190    1048  0 9月17 tty2    00:00:00 /usr/libexec/gnome-session-binary --session=ubuntu
user        1288     994  0 9月17 ?       00:00:00 /usr/libexec/at-spi-bus-launcher
user        1301    1288  0 9月17 ?       00:00:00 /usr/bin/dbus-daemon --config-file=/usr/share/defaults/at-spi2/accessibility.conf --nofork --print-address 11 --address=unix:path=/run/user/1000/at-spi/bus_0
user        1322     994  0 9月17 ?       00:00:00 /usr/libexec/gcr-ssh-agent /run/user/1000/gcr
user        1323     994  0 9月17 ?       00:00:00 /usr/libexec/gnome-session-ctl --monitor
user        1325     994  0 9月17 ?       00:00:00 ssh-agent -D -a /run/user/1000/openssh_agent
user        1335     994  0 9月17 ?       00:00:00 /usr/libexec/gvfsd
user        1341     994  0 9月17 ?       00:00:00 /usr/libexec/gvfsd-fuse /run/user/1000/gvfs -f
user        1348     994  0 9月17 ?       00:00:00 /usr/libexec/gnome-session-binary --systemd-service --session=ubuntu
user        1368     994  0 9月17 ?       00:00:18 /usr/bin/gnome-shell
user        1418    1368  0 9月17 ?       00:00:00 /usr/libexec/mutter-x11-frames
user        1484     994  0 9月17 ?       00:00:00 /usr/libexec/gnome-shell-calendar-server
user        1507     994  0 9月17 ?       00:00:00 /usr/libexec/evolution-source-registry
root        1523       1  0 9月17 ?       00:00:00 /usr/libexec/upowerd
user        1534     994  0 9月17 ?       00:00:00 /usr/libexec/dconf-service
user        1542     994  0 9月17 ?       00:00:01 /usr/libexec/gvfs-udisks2-volume-monitor
user        1548     994  0 9月17 ?       00:00:04 /usr/libexec/gvfs-afc-volume-monitor
user        1554     994  0 9月17 ?       00:00:00 /usr/libexec/gvfs-goa-volume-monitor
user        1559     994  0 9月17 ?       00:00:00 /usr/libexec/goa-daemon
user        1568     994  0 9月17 ?       00:00:19 /usr/libexec/goa-identity-service
user        1574     994  0 9月17 ?       00:00:00 /usr/libexec/gvfs-mtp-volume-monitor
user        1580     994  0 9月17 ?       00:00:00 /usr/libexec/gvfs-gphoto2-volume-monitor
user        1587     994  0 9月17 ?       00:00:00 /usr/libexec/evolution-calendar-factory
user        1593     994  0 9月17 ?       00:00:00 /usr/bin/gjs /usr/share/gnome-shell/org.gnome.Shell.Notifications
user        1618     994  0 9月17 ?       00:00:00 /usr/libexec/evolution-addressbook-factory
user        1631    1335  0 9月17 ?       00:00:00 /usr/libexec/gvfsd-trash --spawner :1.23 /org/gtk/gvfs/exec_spaw/0
user        1642     994  0 9月17 ?       00:00:00 /usr/libexec/at-spi2-registryd --use-gnome-session
colord      1643       1  0 9月17 ?       00:00:00 /usr/libexec/colord
user        1663     994  0 9月17 ?       00:00:00 sh -c /usr/bin/ibus-daemon --panel disable $([ "$XDG_SESSION_TYPE" = "x11" ] && echo "--xim")
user        1664     994  0 9月17 ?       00:00:00 /usr/libexec/gsd-a11y-settings
user        1666    1663  0 9月17 ?       00:00:02 /usr/bin/ibus-daemon --panel disable --xim
user        1667     994  0 9月17 ?       00:00:00 /usr/libexec/gsd-color
user        1671     994  0 9月17 ?       00:00:00 /usr/libexec/gsd-datetime
user        1673     994  0 9月17 ?       00:00:03 /usr/libexec/gsd-housekeeping
user        1676     994  0 9月17 ?       00:00:00 /usr/libexec/gsd-keyboard
user        1678     994  0 9月17 ?       00:00:00 /usr/libexec/gsd-media-keys
user        1682     994  0 9月17 ?       00:00:00 /usr/libexec/gsd-power
user        1686     994  0 9月17 ?       00:00:00 /usr/libexec/gsd-print-notifications
user        1687     994  0 9月17 ?       00:00:00 /usr/libexec/gsd-rfkill
user        1692     994  0 9月17 ?       00:00:00 /usr/libexec/gsd-screensaver-proxy
user        1696     994  0 9月17 ?       00:00:00 /usr/libexec/gsd-sharing
user        1707     994  0 9月17 ?       00:00:00 /usr/libexec/gsd-smartcard
user        1708     994  0 9月17 ?       00:00:00 /usr/libexec/gsd-sound
user        1712     994  0 9月17 ?       00:00:00 /usr/libexec/gsd-wacom
user        1718     994  0 9月17 ?       00:00:00 /usr/libexec/gsd-xsettings
user        1726    1348  0 9月17 ?       00:00:00 /usr/libexec/gsd-disk-utility-notify
user        1746    1348  2 9月17 ?       00:40:45 /opt/todesk/bin/ToDesk
user        1763    1348  1 9月17 ?       00:20:07 /usr/local/sunlogin/bin/sunloginclient --cmd=autorun
user        1770    1348  0 9月17 ?       00:00:00 /usr/libexec/evolution-data-server/evolution-alarm-notify
user        1805    1666  0 9月17 ?       00:00:00 /usr/libexec/ibus-memconf
user        1807    1666  0 9月17 ?       00:00:01 /usr/libexec/ibus-extension-gtk3
user        1818     994  0 9月17 ?       00:00:00 /usr/libexec/ibus-x11 --kill-daemon
user        1824     994  0 9月17 ?       00:00:00 /usr/libexec/ibus-portal
user        1897     994  0 9月17 ?       00:00:02 /usr/libexec/xdg-desktop-portal
user        1901     994  0 9月17 ?       00:00:00 /usr/bin/gjs /usr/share/gnome-shell/org.gnome.ScreenSaver
user        1925     994  0 9月17 ?       00:00:00 /usr/libexec/xdg-desktop-portal-gnome
user        2000     994  0 9月17 ?       00:00:00 /usr/libexec/gsd-printer
user        2002    1763  0 9月17 ?       00:07:45 /usr/local/sunlogin/bin/sunloginclient --type=zygote --no-sandbox --lang=en-US --locales-dir-path=/usr/local/sunlogin/res --log-file=/usr/local/sunlogin/bin/debug.log --resources-dir-path=/usr/local/sunlogin/res --user-agent=SLRC/15.2.0.63064 (Linux,x64,Person,loginver=10,appname=sunloginRemoteClient) Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.81 Safari/537.36
user        2024    1666  0 9月17 ?       00:00:00 /usr/libexec/ibus-engine-libpinyin --ibus
user        2025     994  0 9月17 ?       00:00:02 /usr/libexec/tracker-miner-fs-3
user        2043     994  0 9月17 ?       00:00:00 /usr/libexec/xdg-desktop-portal-gtk
user        2092    1763  0 9月17 ?       00:07:49 /usr/local/sunlogin/bin/sunloginclient --type=gpu-process --no-sandbox --lang=en-US --locales-dir-path=/usr/local/sunlogin/res --log-file=/usr/local/sunlogin/bin/debug.log --resources-dir-path=/usr/local/sunlogin/res --user-agent=SLRC/15.2.0.63064 (Linux,x64,Person,loginver=10,appname=sunloginRemoteClient) Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.81 Safari/537.36 --supports-dual-gpus=false --gpu-driver-bug-workarounds=1,7,23,61,74 --disable-gl-extensions=GL_KHR_blend_equation_advanced GL_KHR_blend_equation_advanced_coherent --gpu-vendor-id=0x8086 --gpu-device-id=0x46a3 --gpu-driver-vendor --gpu-driver-version --gpu-driver-date --lang=en-US --locales-dir-path=/usr/local/sunlogin/res --log-file=/usr/local/sunlogin/bin/debug.log --resources-dir-path=/usr/local/sunlogin/res --user-agent=SLRC/15.2.0.63064 (Linux,x64,Person,loginver=10,appname=sunloginRemoteClient) Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.81 Safari/537.36 --service-request-channel-token=A81B48DA72E37F633052BCFF86819465
root        2209       1  3 9月17 ?       00:50:30 /opt/todesk/bin/ToDesk_Service
kernoops    2214       1  0 9月17 ?       00:00:00 /usr/sbin/kerneloops --test
kernoops    2220       1  0 9月17 ?       00:00:00 /usr/sbin/kerneloops
user        2478     994  0 9月17 ?       00:00:00 /snap/snapd-desktop-integration/315/usr/bin/snapd-desktop-integration
user        2538    2478  0 9月17 ?       00:00:00 /snap/snapd-desktop-integration/315/usr/bin/snapd-desktop-integration
root        2602       2  0 9月17 ?       00:00:17 [kworker/0:3-mld]
user        3513     994  0 9月17 ?       00:00:00 /usr/libexec/gvfsd-metadata
user        3518    1348  0 9月17 ?       00:00:02 /usr/bin/update-notifier
root       12871       2  0 9月17 ?       00:00:03 [kworker/5:0-mm_percpu_wq]
root      610318       2  0 9月17 ?       00:00:00 [kworker/10:0]
root      806851       1  0 00:00 ?        00:00:00 /usr/sbin/cupsd -l
cups-br+  806855       1  0 00:00 ?        00:00:00 /usr/sbin/cups-browsed
lp        807077  806851  0 00:00 ?        00:00:00 /usr/lib/cups/notifier/dbus dbus://
lp        807078  806851  0 00:00 ?        00:00:00 /usr/lib/cups/notifier/dbus dbus://
root     1921553       1  0 10:37 ?        00:00:00 sshd: /usr/sbin/sshd -D [listener] 0 of 10-100 startups
root     1921725       1  0 10:37 ?        00:00:00 /lib/systemd/systemd --user
root     1921726 1921725  0 10:37 ?        00:00:00 (sd-pam)
root     1921732 1921725  0 10:37 ?        00:00:00 /snap/snapd-desktop-integration/315/usr/bin/snapd-desktop-integration
root     1921843 1921725  0 10:37 ?        00:00:00 /usr/bin/dbus-daemon --session --address=systemd: --nofork --nopidfile --systemd-activation --syslog-only
root     1921845       2  0 10:37 ?        00:00:03 [kworker/4:1-events]
root     1921847 1921725  0 10:37 ?        00:00:00 /usr/libexec/xdg-document-portal
root     1921863 1921725  0 10:37 ?        00:00:00 /usr/libexec/xdg-permission-store
root     1921881 1921847  0 10:37 ?        00:00:00 fusermount3 -o rw,nosuid,nodev,fsname=portal,auto_unmount,subtype=portal -- /run/user/0/doc
root     1921950 1921732  0 10:37 ?        00:00:00 /snap/snapd-desktop-integration/315/usr/bin/snapd-desktop-integration
root     1922003       1  0 10:37 ?        00:00:00 sh /root/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/bin/code-server --connection-token=remotessh --accept-server-license-terms --start-server --enable-remote-auto-shutdown --socket-path=/tmp/code-74bd5dd3-971b-4ba6-8111-77019eb0dee8
root     1922007 1922003  0 10:37 ?        00:02:47 /root/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/node /root/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/out/server-main.js --connection-token=remotessh --accept-server-license-terms --start-server --enable-remote-auto-shutdown --socket-path=/tmp/code-74bd5dd3-971b-4ba6-8111-77019eb0dee8
root     1922048 1922007  0 10:37 ?        00:00:50 /root/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/node /root/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/out/bootstrap-fork --type=ptyHost --logsPath /root/.vscode-server/data/logs/20250918T103758
root     1945889 1922048  0 10:51 pts/0    00:00:00 /bin/bash --init-file /root/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/out/vs/workbench/contrib/terminal/common/scripts/shellIntegration-bash.sh
root     1977198       2  0 11:08 ?        00:00:00 [tls-strp]
user     1977228     994  0 11:08 ?        00:00:00 /usr/bin/python3 /usr/lib/ubuntu-release-upgrader/check-new-release-gtk
user     1977321 1977228  0 11:08 ?        00:00:01 /usr/bin/python3 /usr/bin/update-manager --no-update
root     2317400       2  0 14:20 ?        00:00:00 [kworker/8:0-events]
root     2325143       2  0 14:24 ?        00:00:01 [kworker/6:1-events]
root     2334824       2  0 14:29 ?        00:00:01 [kworker/2:1-events]
root     2336541       2  0 14:30 ?        00:00:00 [kworker/11:0-events]
root     2348959       2  0 14:37 ?        00:00:00 [kworker/1:1]
root     2358287       2  0 14:42 ?        00:00:00 [kworker/7:2-events]
root     2370869       2  0 14:48 ?        00:00:00 [kworker/4:0-events]
root     2399290       2  0 15:04 ?        00:00:00 [kworker/2:2]
root     2447931 1922048  0 15:31 pts/2    00:00:00 /bin/bash --init-file /root/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/out/vs/workbench/contrib/terminal/common/scripts/shellIntegration-bash.sh
root     2453281       2  0 15:34 ?        00:00:00 [kworker/u24:1-flush-8:0]
root     2467726 1922048  0 15:42 pts/3    00:00:00 /bin/bash --init-file /root/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/out/vs/workbench/contrib/terminal/common/scripts/shellIntegration-bash.sh
root     2509584 1922007  8 16:04 ?        00:03:48 /root/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/node --dns-result-order=ipv4first /root/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/out/bootstrap-fork --type=extensionHost --transformURIs --useHostProxy=false
root     2509600 1922007  0 16:04 ?        00:00:09 /root/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/node /root/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/out/bootstrap-fork --type=fileWatcher
root     2509659 2509584  0 16:04 ?        00:00:00 /root/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/node /root/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/json-language-features/server/dist/node/jsonServerMain --node-ipc --clientProcessId=2509584
root     2527472       2  0 16:14 ?        00:00:00 [kworker/u24:2-ext4-rsv-conversion]
root     2535065       2  0 16:18 ?        00:00:00 [kworker/5:2-events]
root     2543944       1  0 16:23 pts/0    00:00:00 ./mqttDemo /home/<USER>
root     2544061       1  0 16:23 pts/0    00:00:00 ./mqttDemo /home/<USER>
root     2544460 2509584  0 16:23 ?        00:00:00 /root/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/node /root/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/markdown-language-features/dist/serverWorkerMain --node-ipc --clientProcessId=2509584
root     2547651       2  0 16:25 ?        00:00:00 [kworker/6:2-events]
root     2552004 2544061  0 16:27 pts/0    00:00:00 [mqttDemo] <defunct>
root     2560442       2  0 16:32 ?        00:00:00 [kworker/u24:3-ext4-rsv-conversion]
root     2566513 1921553  0 16:35 ?        00:00:02 sshd: root@notty
root     2566553 2566513  0 16:35 ?        00:00:00 sh
root     2566571 2566553  0 16:35 ?        00:00:03 /root/.vscode-server/code-848b80aeb52026648a8ff9f7c45a9b0a80641e2e command-shell --cli-data-dir /root/.vscode-server/cli --parent-process-id 2566553 --on-host=127.0.0.1 --on-port
root     2567218       2  0 16:36 ?        00:00:00 [kworker/3:0-mm_percpu_wq]
root     2569687       2  0 16:37 ?        00:00:00 [kworker/0:1-rcu_gp]
root     2574216       2  0 16:40 ?        00:00:00 [kworker/9:2-events]
root     2584222       2  0 16:45 ?        00:00:00 [kworker/9:0-events]
root     2588048 2566553  0 16:47 ?        00:00:00 sleep 180
root     2589906     788  0 16:49 ?        00:00:00 /usr/sbin/CRON -f -P
root     2589907     788  0 16:49 ?        00:00:00 /usr/sbin/CRON -f -P
root     2589908     788  0 16:49 ?        00:00:00 /usr/sbin/CRON -f -P
root     2589910 2589907  0 16:49 ?        00:00:00 /bin/sh -c sleep 25 && sh /home/<USER>/dic/prog/runDicProgram.sh
root     2589911 2589908  0 16:49 ?        00:00:00 /bin/sh -c sleep 5 && sh /home/<USER>/orig/runCapture.sh
root     2589912 2589906  0 16:49 ?        00:00:00 /bin/sh -c sh /home/<USER>/dic/prog/runEverySeconds.sh
root     2589915 2589912  0 16:49 ?        00:00:00 sh /home/<USER>/dic/prog/runEverySeconds.sh
root     2590112 2589911  0 16:49 ?        00:00:00 sh /home/<USER>/orig/runCapture.sh
root     2590113 2590112  0 16:49 ?        00:00:00 /home/<USER>/orig/capture_single
root     2590632       2  0 16:49 ?        00:00:00 [kworker/4:2]
root     2590716       2  0 16:49 ?        00:00:00 [kworker/0:0-mm_percpu_wq]
root     2590812       2  0 16:49 ?        00:00:00 [kworker/u24:0]
root     2590840 2589915  0 16:49 ?        00:00:00 sleep .964
root     2590841 2589910  0 16:49 ?        00:00:00 sh /home/<USER>/dic/prog/runDicProgram.sh
root     2590861 2590841  0 16:49 ?        00:00:00 ps -ef
