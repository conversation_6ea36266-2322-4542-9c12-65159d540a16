=== Configuration Info 2025年 09月 15日 星期一 15:01:05 CST ===
Name: 3001
Main Connection:
  Host: *************
  MQTT Port: 6049
  TCP Port: 6050
Backup Connection:
  Host: ************
  MQTT Port: 6049
  TCP Port: 6050
=================================
=== Configuration Info 2025年 09月 15日 星期一 15:02:34 CST ===
Name: 3001
Main Connection:
  Host: *************
  MQTT Port: 6049
  TCP Port: 6050
Backup Connection:
  Host: ************
  MQTT Port: 6049
  TCP Port: 6050
=================================
[2025-09-15 15:02:36] 当前激活状态检查:
[2025-09-15 15:02:36] is_activated: 0
[2025-09-15 15:02:36] active_type: 0
[2025-09-15 15:02:36] current_time: Mon Sep 15 15:02:36 2025
[2025-09-15 15:02:36] expire_time: Thu Jan  1 08:00:00 1970
[2025-09-15 15:02:36] 未激活，检查试用期
[2025-09-15 15:02:36] 在试用期内
[2025-09-15 15:02:36] 试用期结束时间：Wed Oct 15 14:32:03 2025
[2025-09-15 15:02:36] 安装时间：Mon Sep 15 14:32:03 2025
[2025-09-15 15:02:36] 读取到服务器配置: *************:6049
[2025-09-15 15:02:36] 读取到服务器配置: *************:1884
=== Configuration Info 2025年 09月 15日 星期一 15:03:02 CST ===
Name: 3001
Main Connection:
  Host: *************
  MQTT Port: 6049
  TCP Port: 6050
Backup Connection:
  Host: ************
  MQTT Port: 6049
  TCP Port: 6050
=================================
