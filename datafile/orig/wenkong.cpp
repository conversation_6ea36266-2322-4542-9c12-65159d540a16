#include <iostream>   
#include <modbus/modbus.h>
#include <errno.h> 
#include <modbus/modbus-rtu.h>
#include <iomanip> // Include this header file. setprecision  
#include <chrono>
#include <ctime>
#include <sstream>

#define BAN_BEN "250422"
  
void Wrimodbus(modbus_t *ctx, uint16_t reg_addr, int reg_value){       
    // Write a value to the register
    if (modbus_write_register(ctx, reg_addr, reg_value) == -1) {
        fprintf(stderr, "Failed to write to register: %s\n", modbus_strerror(errno));
        //modbus_free(ctx);
        //return ;
    }
    //printf("Successfully wrote value to register\n");
}

void TemperatureControlCommandAll() {
    std::cout << "  Incorrect parameters, You need to enter the correct parameters. " << "\n";
    std::cout << "  At least one additional parameter must be appended. " << "\n";
    std::cout << "  When appending a parameter, it is for querying the status of the current parameter. " << "\n";
    std::cout << "  The first parameter: /dev/ttyS5 (the name of the opened serial port device) " << "\n";
    std::cout << "  The second parameter: 5 (Parameter Save / Temperature Control Switch) || " << "\n";
    std::cout << "                       11 (Set the target temperature) || " << "\n";
    std::cout << "                       50 (Auto-tuning Switch) " << "\n";
    std::cout << "  Append up to 3 parameters." << "\n";
    std::cout << "  When appending two parameters, set the state of the second parameter to the value of the third parameter. " << "\n";   
    std::cout << "  The second parameter = 5, " << "\n";  
    std::cout << "   The third parameter: 8 (Program the PID parameters into the chip's internal memory) || " << "\n";
    std::cout << "                         7 (Read the PID parameters from the chip and enable temperature control) ||" << "\n";
    std::cout << "                         5 (Turn off temperature control) || " << "\n";
    std::cout << "                         6 (Manually update the PID parameters and program your own PID parameters into the chip) " << "\n";
    std::cout << "  The second parameter = 11, " << "\n";
    std::cout << "   The third parameter: 0 to 1000, The actual temperature is 0.1 times the set temperature. " << "\n";
    std::cout << "  The second parameter = 50, " << "\n";  
    std::cout << "   The third parameter: 8 (Enable auto-tuning) || " << "\n";
    std::cout << "                         7 (Disable auto-tuning) " << "\n";

}

modbus_t * ModbusInit(const char *device) {
    modbus_t *ctxgnkg;   
    ctxgnkg = modbus_new_rtu(device, 9600, 'N', 8, 1);  
    if (ctxgnkg == NULL) {  
        std::cout << "  Unable to create the context" << std::endl;  
         
    } 
    // Set the slave address (Slave ID)
    modbus_set_slave(ctxgnkg, 1); // Assume the slave address is 1
  
    // Connect to the Modbus RTU server (actually opens the serial port)
    if (modbus_connect(ctxgnkg) == -1) {  
        std::cout << "  Connection failed: " << modbus_strerror(errno) << std::endl;  
          
    } 
    return ctxgnkg;
}

void SetRegister(modbus_t *ctxgnkg, int gn, int kg) {
    // Switch auto-tuning
    if ((gn == 50 || gn == 5) && kg > 0) { 
        Wrimodbus(ctxgnkg, static_cast<uint16_t>(gn), kg);
    }
    // Target temperature
    else if (gn == 11 && kg >= 0 && kg <= 1000) {
        Wrimodbus(ctxgnkg, static_cast<uint16_t>(gn), kg);
    }
}

void ReadRegister(modbus_t *ctxgnkg, int gn) {
    if (gn > 0) {  
    uint16_t addr = static_cast<uint16_t>(gn);
    uint16_t reg; // Store the read values
    float zreg;
    for (int i = 0; i < 1; i++) {
        if (modbus_read_registers(ctxgnkg, addr, 1, &reg) == -1) {
            std::cout << "  Unable to read data from register " << addr << std::endl;
            // zreg = 0.0;
            zreg = reg;
            std::cout << "  Value of register " << addr << ": " << zreg << std::endl;                    
        } else {  
            // zreg = reg / 100.0;
            zreg = reg;
            std::cout << "  Value of register " << addr << ": " << zreg << std::endl;  
        }                 
    } 
    }
}


int main(int argc, char *argv[]) {  
    // current version
    std::cout << "  current version: " << BAN_BEN << "\n";
    auto current_time_start = std::chrono::system_clock::now();
    std::time_t now_start = std::chrono::system_clock::to_time_t(current_time_start);
    std::stringstream filetime_start ;
    filetime_start << std::put_time(std::localtime(&now_start), "%Y%m%d%H%M%S"); 
    std::cout << "  current_time_start content: " << filetime_start.str() << std::endl;

    int gn = 0;
    int kg = 0;
    if (argc < 2 && argc > 4) {
        TemperatureControlCommandAll();
    }
    const char *device = "/dev/ttyS5";
    if (argc >= 2) {
        device = argv[1];
    }
    if (argc >= 3) {  
        gn = std::stoi(argv[2]);
    }
    if (argc ==4) {  
        kg = std::stoi(argv[3]);
    }


    modbus_t *ctxgnkg;   
    ctxgnkg = modbus_new_rtu(device, 9600, 'N', 8, 1);  
    if (ctxgnkg == NULL) {  
        std::cout << "  Unable to create the context" << std::endl;  
         
    } 
    // Set the slave address (Slave ID)
    modbus_set_slave(ctxgnkg, 1); // Assume the slave address is 1
  
    // Connect to the Modbus RTU server (actually opens the serial port)
    if (modbus_connect(ctxgnkg) == -1) {  
        std::cout << "  Connection failed: " << modbus_strerror(errno) << std::endl;  
          
    }   

    // Switch auto-tuning
    if (kg > 0) { 
        Wrimodbus(ctxgnkg, static_cast<uint16_t>(gn), kg);
    }

    
    if (gn > 0) {  
        uint16_t addr = static_cast<uint16_t>(gn);
        uint16_t reg; // Store the read values
        float zreg;
        if (modbus_read_registers(ctxgnkg, addr, 1, &reg) == -1) {
            std::cout << "  Unable to read data from register " << addr << std::endl;
            // zreg = 0.0;
            zreg = reg;
            std::cout << "  Value of register " << addr << ": " << zreg << std::endl;                    
        } else {  
            // zreg = reg / 100.0;
            zreg = reg;
            std::cout << "  Value of register " << addr << ": " << zreg << std::endl;  
        }                 
    }

    modbus_close(ctxgnkg);
    modbus_free(ctxgnkg);

    return 0;  
}    

