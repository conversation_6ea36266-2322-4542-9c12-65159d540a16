#include <opencv2/opencv.hpp>
#include <chrono>
#include <thread>
#include <filesystem>
#include <stdio.h>
#include <modbus/modbus.h>
#include <errno.h> 
#include <modbus/modbus-rtu.h>
#include <stdlib.h>
#include <unistd.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <iostream>
#include <fstream>
#include <sstream>
#include <vector>
#include <filesystem>
#include <system_error>
#include <boost/filesystem.hpp>
#include <boost/system/error_code.hpp>

#define START_FILE "/home/<USER>/orig/startfile.txt"
//banben
#define BAN_BEN "250522"
#define MODBUS_IP 0x01
#define HEXS_NUM 1
uint16_t addr[] = {60};
std::mutex data_file_mutex;

// Reads numbers/strings from a file and stores them in a vector
bool Confile(const std::string& readfilename, std::vector<std::string>& numbers) {    
    std::ifstream file(readfilename);
    
    // Check if file was successfully opened
    if (!file.is_open()) {
        std::cerr << "Failed to open file: " << readfilename << std::endl;
        return false;
    }

    std::string line;
    // Read file line by line
    while (std::getline(file, line)) {
        std::istringstream iss(line);
        std::string number;
        // Extract each token from the line
        while (iss >> number) {
            numbers.push_back(number);
        }
    }
    
    // Close the file explicitly (not strictly necessary as destructor will handle it)
    file.close();
    
    return true; // Indicate successful read operation
}

void Wrimodbus(modbus_t *ctx, uint16_t reg_addr, int reg_value){       
    // Attempt to write the register value
    if (modbus_write_register(ctx, reg_addr, reg_value) == -1) {
        // Print error message with Modbus-specific error description
        fprintf(stderr, "  Register write failed: %s (Error %d)\n", modbus_strerror(errno), errno);    
    } 
}

// When exceeding the maximum limit, purge the oldest data entries
void deleteOldImages(std::string image_directory, int max_images, std::string data_file_name, std::string file_extension) {
    // Normalize file extension (automatically prepend dot if missing)
    if (!file_extension.empty() && file_extension[0] != '.') {
        file_extension = "." + file_extension;
    }

    // Collect image files along with their modification times
    std::vector<std::pair<std::filesystem::file_time_type, std::string>> images_with_time;
    for (const auto& entry : std::filesystem::directory_iterator(image_directory)) {
        try {
            if (entry.is_regular_file() && 
                entry.path().extension() == file_extension) {
                images_with_time.emplace_back(
                    std::filesystem::last_write_time(entry),
                    entry.path().filename().string()
                );
            }
        } catch (const std::exception& e) {
            std::cout << "  Error accessing file: " << e.what() << std::endl;
        }
    }

    // Sort files by modification time (oldest first)
    std::sort(images_with_time.begin(), images_with_time.end());

    // Extract filenames from sorted list
    std::vector<std::string> image_filenames;
    for (const auto& img : images_with_time) {
        image_filenames.push_back(img.second);
    }

    // Calculate number of files to delete
    int files_to_delete = image_filenames.size() - max_images;
    if (files_to_delete <= 0) {
        std::cout << "  No files need to be deleted." << std::endl;
        //return;
    }

    // Delete oldest files and record their names
    std::vector<std::string> deleted_files;
    for (int i = 0; i < files_to_delete; ++i) {
        std::string full_path = image_directory + "/" + image_filenames[i];
        try {
            std::filesystem::remove(full_path);
            deleted_files.push_back(image_filenames[i]);
            std::cout << "  Deleted: " << image_filenames[i] << std::endl;
        } catch (const std::exception& e) {
            std::cout << "  Error deleting file: " << e.what() << std::endl;
        }
    }

    // Update data file
    if (!data_file_name.empty()) {
        // Use lock_guard for automatic mutex management (RAII pattern)
        std::lock_guard<std::mutex> lock(data_file_mutex);
        
        // Container to store lines to keep
        std::vector<std::string> lines_to_keep;
        
        // Read original file
        std::ifstream data_file(data_file_name);
        
        if (data_file.is_open()) {
            std::string line;
            while (std::getline(data_file, line)) {
                bool keep_line = true;
                // New feature: Check if the line starts with a digit
                if (!line.empty() && !std::isdigit(line[0])) {
                    keep_line = false;
                } else {
                    // Original logic: Check if the line contains any deleted filenames
                    // Check if current line contains any deleted filename
                    for (const auto& deleted : deleted_files) {
                        if (line.find(deleted) != std::string::npos) {
                            keep_line = false;
                            lines_to_keep.clear(); // Clear previous entries as they're older
                            break;
                        }
                    }
                }

                if (keep_line) {
                    lines_to_keep.push_back(line);
                }
            }
            data_file.close();
        } else {
            std::cout << "  Error opening data file for reading." << std::endl;
        }

        // Write updated file
        std::ofstream new_data_file(data_file_name);
        if (new_data_file.is_open()) {
            for (const auto& line : lines_to_keep) {
                new_data_file << line << std::endl;
            }
            new_data_file.close();
            std::cout << "  Data file updated." << std::endl;
        } else {
            std::cout << "  Error opening data file for writing." << std::endl;
        }
    }
}



int main() {
    // Display version information
    std::cout << "  current version: " << BAN_BEN << "\n";

    // Get startup file information
    std::string startfilename = START_FILE;
    std::vector<std::string> start_file;
    if (!Confile(startfilename, start_file)) {
        return 1;
    }
    std::cout << "  start_file[0] = " << start_file[0] << "\n";  // Configuration settings file
    std::cout << "  start_file[1] = " << start_file[1] << "\n";  // Camera RTSP address file
    std::cout << "  start_file[2] = " << start_file[2] << "\n";  // Data storage file
    std::cout << "  start_file[3] = " << start_file[3] << "\n";  // Image save directory
    std::cout << "  start_file[4] = " << start_file[4] << "\n";  // Image format extension
    std::cout << "  start_file[5] = " << start_file[5] << "\n";  // RS-485 port identifier

    // Get configuration parameters
    std::string setfilename = start_file[0];
    std::vector<std::string> numbers;
    if (!Confile(setfilename, numbers)) {
        return 1;
    }

    // Get camera RTSP addresses
    std::string camerafilename = start_file[1];
    std::vector<std::string> camera_rtsp;
    if (!Confile(camerafilename, camera_rtsp)) {
        return 1;
    }
    std::string camera_rtsp_ip;
    camera_rtsp_ip = camera_rtsp[0];
    std::cout << "  Camera RTSP IP: " << camera_rtsp_ip << std::endl;

    // Modbus configuration
    modbus_t *ctx;
    uint16_t addr = 60;
    uint16_t reg;      // Stores read value
    float zreg;

    // Initialize Modbus context
    ctx = modbus_new_rtu(start_file[5].c_str(), 9600, 'N', 8, 1);
    if (ctx == NULL) {
        fprintf(stderr, "  Failed to create Modbus context\n");
    }
    std::cout << "  Modbus context initialized successfully\n";

    // Set slave address
    modbus_set_slave(ctx, MODBUS_IP);

    // Set to RS-485 mode with CRC-16
    modbus_rtu_set_serial_mode(ctx, MODBUS_RTU_RS485);

    // Open serial port
    if (modbus_connect(ctx) == -1) {
        fprintf(stderr, "  Failed to open serial port\n");
    }
    std::cout << "  Serial port opened successfully\n";
    std::this_thread::sleep_for(std::chrono::milliseconds(10));

    // Initialize video capture
    cv::VideoCapture cap;
    cap.open(camera_rtsp_ip);
    std::cout << "  Video capture initialized \n";    

    // Check camera connection
    if (!cap.isOpened()) {
        std::cout << "  Failed to open camera, ";
    } else {
        std::cout << "  Camera connected successfully" << std::endl;
    }    

    cv::Mat frame;
    cap.read(frame);

    // Check frame validity
    if (frame.empty()) {
        std::cout << "  Failed to read video frame" << std::endl;
    }    

    // Generate screenshot filename
    auto current_time = std::chrono::system_clock::now();
    std::time_t now = std::chrono::system_clock::to_time_t(current_time);
    std::stringstream filetime, filepath, filename;
    filetime << std::put_time(std::localtime(&now), "%Y%m%d%H%M%S");
    filepath << filetime.str() << start_file[4].c_str();
    filename << start_file[3] << filepath.str();                

    // Save screenshot
    vector<int> params;
    params.push_back(IMWRITE_JPEG_QUALITY);
    params.push_back(80);
    cv::imwrite(filename.str(), frame, params);

    // Read temperature data
    if (modbus_read_registers(ctx, addr, 1, &reg) == -1) {
        std::cerr << "  Failed to read temperature data" << std::endl;
        zreg = 0.0;
        std::cout << std::fixed << std::setprecision(1) << "  Temperature: " << zreg << std::endl;                    
    } else {  
        zreg = reg / 100.0;
        std::cout << std::fixed << std::setprecision(1) << "  Temperature: " << zreg << std::endl;  
    }                         

    // Write data to file
    std::stringstream data_ss;
    data_ss << std::fixed << std::setprecision(1) << filetime.str() << "  " << filepath.str();
    data_ss << "  " << zreg;
    data_ss << std::endl;
    std::ofstream data_file(start_file[2], std::ios::app);
    if (data_file.is_open()) {
        std::lock_guard<std::mutex> lock(data_file_mutex); // Thread-safe file access
        data_file << data_ss.str();
        data_file.close();
    } else {
        std::cerr << "  Failed to open data file" << std::endl;
    }

    // Initialize cleanup process (target directory, max images, data file, extension)
    std::string image_directory = start_file[3];
    int max_images = std::stoi(numbers[3]);
    std::string data_file_name = start_file[2];
    std::string file_extension = start_file[4];
    deleteOldImages(image_directory, max_images, data_file_name, file_extension);

    // Cleanup resources
    modbus_close(ctx);
    modbus_free(ctx);
    cap.release();
    cv::destroyAllWindows();

    return 0;
}


