#!/bin/bash
program_dir='/home/<USER>/dic/prog/'
orig_dir='/home/<USER>/orig/'
orig_list_file='/home/<USER>/orig/originalObservation.txt'
master_dir='/home/<USER>/dic/para/master/'
multi_master_dir='/home/<USER>/dic/para/multiMaster/'
best_master_dir='/home/<USER>/dic/prog/master/'
point_list_file='/home/<USER>/dic/para/points_list.txt'
dic_result_file='/home/<USER>/dic/para/points_displacement.txt'
video_result_file='/home/<USER>/dic/para/video_displacement.txt'

#master dir
if [ ! -d "$multi_master_dir" ];then
    mkdir $multi_master_dir
fi
# if [ ! -d "$best_master_dir" ];then
#     mkdir $best_master_dir
# fi

if [ ! -f "$orig_list_file" ];then 
    echo "original observation list file not exit"
    exit
fi
if [ ! -f "$point_list_file" ];then 
    echo "point list file not exit"
    exit
fi

# master image
master_file_name=$(ls $master_dir | grep -E '\.jpg$')
master_img_file=$master_dir$master_file_name
if [ ! -f "$master_img_file" ];then
    echo "master image file not exit"
    exit
fi

# best master image
best_master_file=$master_img_file

# dic process
camera_ip='*************'
duration_seconds=5
acquisition_frequency=15
current_time=$(date "+%Y%m%d%H%M%S")
temp_result_file='/home/<USER>/dic/prog/video_temp_result_'$current_time'.txt'
rm $temp_result_file
chmod 777 /home/<USER>/dic/prog/videoDicProcessWeb
/home/<USER>/dic/prog/videoDicProcessWeb $camera_ip $duration_seconds $acquisition_frequency $point_list_file $best_master_file  $temp_result_file

#current_time=$(date "+%Y%m%d%H%M%S")
#video_file=$orig_dir$current_time'.mp4'
#/home/<USER>/dic/prog/videoCaptureWeb $camera_ip $video_duration_seconds $video_file

# master image
#master_file_name=$(ls $master_dir | grep -E '\.jpg$')
#master_img_file=$master_dir$master_file_name
#if [ ! -f "$master_img_file" ];then
#    echo "master image file not exit"
#    exit
#fi

# best master image
#best_master_file=$master_img_file

#slave image
last_line=$(awk 'END {print}' $orig_list_file)
#slave_file_name=$(echo $last_line | awk '{split($0,a," ");print $2}')
#slave_img_file=$orig_dir$slave_file_name
#if [ ! -f "$slave_img_file" ];then
#    echo "slave image file not exit"
#    exit
#fi

# search best master
#multi_list_file='/home/<USER>/dic/prog/multi_master_list.txt'
#ls $master_dir*.jpg > $multi_list_file
#ls $multi_master_dir*.jpg >> $multi_list_file
#multi_num=`cat $multi_list_file | wc -l`
# echo $multi_num
#if [ $multi_num -gt 1 ];then
#    temp_master_file='/home/<USER>/dic/prog/dic_temp_master.txt'
#    rm $temp_master_file
#    chmod 777 /home/<USER>/dic/prog/searchMasterHimixLiunx
#    /home/<USER>/dic/prog/searchMasterHimixLiunx $slave_img_file $multi_list_file $temp_master_file
#    if [ -f "$temp_master_file" ];then
#        best_master_file=`cat $temp_master_file`        
#    fi 
#fi

# dic process
#temp_result_file='/home/<USER>/dic/prog/video_temp_result.txt'
#rm $temp_result_file
#chmod 777 /home/<USER>/dic/prog/videoDicProcess
#/home/<USER>/dic/prog/videoDicProcess $point_list_file $best_master_file $video_file $temp_result_file

# add time & temperature
temperature=$(echo $last_line | awk '{split($0,a," ");print $3}')
point_number=$(wc -l $point_list_file | awk '{print $1}')
observe_number=$(wc -l $temp_result_file | awk '{print $1 }')
time_tempe_points=$current_time' '$temperature' '$point_number' '$observe_number
#cat $point_list_file $temp_result_file > $video_result_file
rm $video_result_file
cp $point_list_file $video_result_file
sed -i "1i $time_tempe_points" $video_result_file
#cp $temp_result_file $dic_result_file

# add observation time
#dic_pre_file='test.txt'
#acquisition_frequency='30'
observe_number=$(wc -l $temp_result_file | awk '{print $1 + 1}')
#current_time=$(date "+%Y%m%d%H%M%S")
#echo $observe_number
observe_ind=1
while [ $observe_ind -lt $observe_number ]
do
    #echo $observe_ind
    select_line_data=$(awk 'NR=='$observe_ind' {print}' $temp_result_file)
    #echo $select_line_data
    time_seconds=$(echo "scale=2; ( $observe_ind - 0.99 ) / $acquisition_frequency" | bc)
    time_all=$(echo "scale=2; $current_time + $time_seconds" | bc)
    echo $time_all $select_line_data >> $video_result_file
    #observe_ind=$observe_ind+1
    observe_ind=$(echo "$observe_ind + 1" | bc)
done

# delete the 
video_list_file='/home/<USER>/dic/prog/video_list.txt' 
ls /home/<USER>/dic/prog/video_temp_result_* > $video_list_file
video_number=$(wc -l $video_list_file | awk '{print $1 }')
if [ $video_number -gt 25 ];then
    first_line_file=$(awk 'NR==1' $video_list_file)
    rm $first_line_file
fi
