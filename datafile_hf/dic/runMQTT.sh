# run dic
# LD_LIBRARY_PATH=$LD_LIBRARY_PATH:/usrappfs/lib
# export LD_LIBRARY_PATH
name='3002'
host='*************'
port='1884'
hostW='m20720957h.imwork.net'
portW='23298'
ps -ef > /home/<USER>/dic/ps.log
mqttDL=`cat /home/<USER>/dic/ps.log | grep 'mqttDemo '$name' 192' | wc -m`
if [ $mqttDL -eq 0 ];then
        chmod 777 /home/<USER>/dic/mqttDemo
        nohup /home/<USER>/dic/mqttDemo $name $host $port &
fi
mqttPL=`cat /home/<USER>/dic/ps.log | grep mqttPub | wc -m`
if [ $mqttPL -eq 0 ];then
        chmod 777 /home/<USER>/dic/mqttPub
        nohup /home/<USER>/dic/mqttPub $name &
fi
mqttWDL=`cat /home/<USER>/dic/ps.log | grep 'mqttDemo '$name' m2' | wc -m`
if [ $mqttWDL -eq 0 ];then
        chmod 777 /home/<USER>/dic/mqttDemo
        nohup /home/<USER>/dic/mqttDemo $name $hostW $portW &
fi
parameters_file='/home/<USER>/dic/para/parameters.txt'
temp_parameters_file='/home/<USER>/dic/para/temp_parameters.txt'
if [ -e $temp_parameters_file ];then
        difflength=`diff $parameters_file $temp_parameters_file | wc -m`
        if [ $difflength -gt 0 ];then
                ps > /home/<USER>/dic/ps.log
                mqttPubPid=`cat /home/<USER>/dic/ps.log | grep mqttPub | awk '{print $1}'`
                kill $mqttPubPid
                chmod 777 $parameters_file
                cp $parameters_file $temp_parameters_file
        fi
else
        chmod 777 $parameters_file
        cp $parameters_file $temp_parameters_file
fi

