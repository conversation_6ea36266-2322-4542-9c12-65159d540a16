cmake_minimum_required(VERSION 3.0)
project(mqttDemo)

# 自动寻找安装好的OpenCV库
#find_package(OpenCV REQUIRED)

set(CMAKE_C_COMPILER arm-himix200-linux-gcc)
set(CMAKE_CXX_COMPILER arm-himix200-linux-g++)

##mqtt libs directory
include_directories(/home/<USER>/Package/mosquitto-1.6.12/lib/)
link_directories("/home/<USER>/Package/mosquitto_arm/")

##openssl
link_directories("/home/<USER>/Package/openssl_arm/lib")
##

link_directories("/home/<USER>/Package/cJSON/build_arm")

include_directories(/home/<USER>/Package/paho.mqtt.c-master/src/)
link_directories("/home/<USER>/Package/paho.mqtt.c-master/build_arm/src")

##自动寻找不到，手动添加opencv
##opencv的头文件

# include_directories("/usr/local/include/opencv4/opencv2/opencv/include")
# # 添加动态库寻找路径
# link_directories("/usr/local/include/opencv4/opencv2/opencv/opencv/lib")
# set(OpenCV_LIBS opencv_stitching opencv_highgui opencv_ml opencv_videostab opencv_superres 
#   opencv_videoio opencv_photo opencv_objdetect opencv_calib3d opencv_features2d opencv_dnn opencv_flann 
#   opencv_shape opencv_video opencv_imgcodecs opencv_imgproc opencv_core)


# 添加可执行文件
add_executable(mqttDemo himixInteract.c)

# 链接OpenCV库
target_link_libraries(mqttDemo libmosquitto.so libcrypto.so libssl.so libcjson.so libpaho-mqtt3a.so pthread paho-mqtt3c)
