#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <stdbool.h>
#include <unistd.h>
#include <pthread.h>
#include <linux/reboot.h>
#include "mosquitto.h"
#include "cJSON.h"
#include "fio.h"
#include "tbase64.h"
#include "MQTTClient.h"
#include <sqlite3.h>

#define KEEP_ALIVE 5
#define IMAGE_TYPE "image"
#define CHANGE_TYPE "displacement"

#define DIC_FOLDER "/home/<USER>/"
#define IMAGE_LIST_FILE "/home/<USER>/orig/originalObservation.txt"

#define HOST_PORT_FILE "/home/<USER>/dic/para/parameters.txt"
#define POINT_LIST_FILE "/home/<USER>/dic/para/points_list.txt"
#define IMAGE_FOLDER "/home/<USER>/orig/"
#define MASTER_IMAGE_FOLDER "/home/<USER>/dic/para/master/"
#define MULTIMASTER_IMAGE_FOLDER "/home/<USER>/dic/para/multiMaster/"
#define CHANGE_DATA_FILE "/home/<USER>/dic/para/points_displacement.txt"
#define VIDEO_DISPLACEMENT_FILE "/home/<USER>/dic/para/video_displacement.txt"
#define LOG_FILE "/home/<USER>/dic/log.txt"

 #define mqttDemo "/home/<USER>/dic/mqttDemo"

//#define mqttDemo "/mnt/d/source/docker/himixProgram_v5/mqttDemo"

// 添加数据库路径定义
#define DB_PATH "/home/<USER>/dic/result.db"

char *DEVICE_ID = NULL;
char UP_TOPIC[50] = "up/";
char DOWN_TOPIC[50] = "down/";
char RESPOND_TOPIC[50] = "respond/";
char *HOST = NULL;
int PORT;


// 订阅程序--连接服务器成功的回调
void sub_connect_callback(struct mosquitto *mosq, void *obj, int rc)
{
        printf("Call the function: on_connect\n");

        if(rc){
                // 连接错误，退出程序
                printf("on_connect error!\n");
                // exit(1);
        }else{
                // 订阅主题
                // 参数：句柄、id、订阅的主题、qos
        		int ret = mosquitto_subscribe(mosq, NULL, DOWN_TOPIC, 2);
                if(ret != MOSQ_ERR_SUCCESS)
                    fprintf(stderr, "Could not subscribe to topic. Return code: %d\n", ret);
        }
}

// 订阅程序--连接服务器失败的回调
void sub_disconnect_callback(struct mosquitto *mosq, void *obj, int rc)
{
        printf("Call the function: disconnect_callback\n");

        char resolved_path[PATH_MAX];
        char *real_path = realpath(mqttDemo, resolved_path);
        if (real_path == NULL) {
            perror("realpath failed");
            return;
        }

        char port[4];
        sprintf(port, "%d", PORT);
        char *args[] = {real_path, DEVICE_ID, HOST, port, NULL};
        char *env[] = {"LD_LIBRARY_PATH=/usrappfs/lib:$LD_LIBRARY_PATH", NULL};

        if (execve(real_path, args, env) == -1) {
            perror("execve failed");
        }
}

void subscribe_callback(struct mosquitto *mosq, void *obj, int mid, int qos_count, const int *granted_qos)
{
    printf("Call the function: on_subscribe\n");
}

void publish_callback(struct mosquitto *mosq, void *obj, int rc)
{
    // printf("Call the function: publish_callback\n");
}

// 互斥锁初始化
pthread_mutex_t mutex = PTHREAD_MUTEX_INITIALIZER;
// 遍历mqtt服务器,发布消息
bool publish_msg(void* arg, struct mosquitto *mosq, const char* type, char* message, size_t msglen, char* other, size_t otherlen, char* round)
{
    // 创建一个cJSON对象
    cJSON *json_obj = cJSON_CreateObject();
    cJSON_AddStringToObject(json_obj, "name", DEVICE_ID);
    cJSON_AddStringToObject(json_obj, "type", type);
    // 如果发布的是图片数据，则发布数据中含有图片时间、名称信息
    if(strcmp(type,IMAGE_TYPE)==0)
    {
        cJSON_AddNumberToObject(json_obj, "imageinfolen", otherlen);
        cJSON_AddStringToObject(json_obj, "imageinfo", other);
        cJSON_AddStringToObject(json_obj, "round", round);
    }
    cJSON_AddNumberToObject(json_obj, "datalen", msglen);
    cJSON_AddStringToObject(json_obj, "data", message);

    // 将cJSON对象转换为JSON字符串
    char *json_string = cJSON_Print(json_obj);

    // 不需要重新连接，使用已有的连接
    pthread_mutex_lock(&mutex);
    /*发布消息*/

    mosquitto_publish(mosq, NULL, UP_TOPIC, strlen(json_string)+1, json_string, 0, 0);
    pthread_mutex_unlock(&mutex);

    // 释放内存
    if(message)
        memset(message, 0, sizeof(message));
    if(json_string)
        memset(json_string, 0, sizeof(json_string));

    return true;
}

void publish_image_base64(struct mosquitto *mosq, char* type, char* round, size_t msglen, char* message)
{
    // 创建一个cJSON对象
    cJSON *json_obj = cJSON_CreateObject();
    cJSON_AddStringToObject(json_obj, "name", DEVICE_ID);
    cJSON_AddStringToObject(json_obj, "type", type);
    cJSON_AddStringToObject(json_obj, "round", round);
    cJSON_AddNumberToObject(json_obj, "datalen", msglen);
    cJSON_AddStringToObject(json_obj, "data", message);

    // 将cJSON对象转换为JSON字符串
    char *json_string = cJSON_Print(json_obj);

    /*发布消息*/
    pthread_mutex_lock(&mutex);
    mosquitto_publish(mosq,NULL,RESPOND_TOPIC,strlen(json_string)+1, json_string,0,0);
    pthread_mutex_unlock(&mutex);
}

void message_callback(struct mosquitto *mosq, void *obj, const struct mosquitto_message *message)
{
    // 在这里处理接收到的 MQTT 消息
    cJSON *json = cJSON_Parse((char *)message->payload);
    cJSON *code = cJSON_GetObjectItemCaseSensitive(json, "code");
    cJSON *data = cJSON_GetObjectItemCaseSensitive(json, "data");
    char *base64_encoded_data=NULL, *input_data=NULL, type[50];
    size_t base64_size, input_length;
    if (cJSON_IsString(code))
    {
        printf("code: %s\n", code->valuestring);
        if(strcmp(code->valuestring,"iplist")==0){
        	strcpy(type, "iplist");
        	input_data = read_file(HOST_PORT_FILE);
        }    	
	    else if(strcmp(code->valuestring,"imagelist")==0){
	    	strcpy(type, "imagelist");
    
	    	input_data = read_file(IMAGE_LIST_FILE);
	    }
	    else if(strcmp(code->valuestring,"pointlist")==0){
	    	strcpy(type, "pointlist");
      
	    	input_data = read_file(POINT_LIST_FILE);
	    }
	    else if(strcmp(code->valuestring,"getimage")==0)
	    {
	    	strcpy(type, "getimage");
	    	char image_path[256] = IMAGE_FOLDER;
	    	base64_encoded_data = image_to_base64(strcat(image_path, data->valuestring), &base64_size);
	    	if(!base64_encoded_data)
	    		input_data = "找不到图像！";
	    }
	    else if(strcmp(code->valuestring,"setmasterimage")==0)
	    {
	    	strcpy(type, "setmasterimage");
    		char from_image[256] = IMAGE_FOLDER,to_image[256] = MASTER_IMAGE_FOLDER;
    		if(fopen(strcat(from_image, data->valuestring), "rb")==NULL)
    			input_data = "找不到图像！";
    		else
	    		if(remove_all_file(MASTER_IMAGE_FOLDER))
		    		if(copy_image(from_image, strcat(to_image, data->valuestring)))
		    			// 主影像设置成功，返回主影像文件名
		    			input_data = get_first_file_name(MASTER_IMAGE_FOLDER);
	    	if(!input_data)
	    		input_data = "主影像设置失败！";		    	
	    }
	    else if(strcmp(code->valuestring,"getmasterimage")==0)
	    {

	    	strcpy(type, "getmasterimage");
	    	char image_path[256] = MASTER_IMAGE_FOLDER;
			// 不带data的getmasterimage用于获取主影像文件名，带data的用于获取主影像图像
	    	if(data==NULL)
	    		input_data = get_first_file_name(image_path);
	    	else
	    		base64_encoded_data = image_to_base64(strcat(image_path, data->valuestring), &base64_size);
	    }
	    else if(strcmp(code->valuestring,"setpoint")==0)
	    {
	    	strcpy(type, "setpoint");
	    	if(write_last_row_include_index(POINT_LIST_FILE, data->valuestring))
	    		input_data = "测点添加成功！";
	    	else
	    		input_data = "测点添加失败！";
	    }
	    else if(strcmp(code->valuestring,"alterpoint")==0)
	    {
	    	strcpy(type, "alterpoint");
	    	if(write_file(POINT_LIST_FILE, data->valuestring))
	    		input_data = "测点列表修改成功！";
	    	else
	    		input_data = "测点列表修改失败！";
	    }
	    else if(strcmp(code->valuestring,"deleteip")==0)
	    {
	    	strcpy(type, "deleteip");
	    	if(write_file(HOST_PORT_FILE, data->valuestring))
	    		input_data = "Ip删除成功！";
	    	else
	    		input_data = "IP删除失败！";
	    }
	    else if(strcmp(code->valuestring,"setip")==0)
	    {
	    	strcpy(type, "setip");
	    	if(write_last_row(HOST_PORT_FILE, data->valuestring))
	    		input_data = "IP添加成功！";
	    	else
	    		input_data = "IP添加失败！";
	    }
        else if(strcmp(code->valuestring,"upload")==0)
        {
            strcpy(type, "upload");
            cJSON *path = cJSON_GetObjectItemCaseSensitive(json, "path");

            size_t decoded_length = strlen(data->valuestring);
            unsigned char *decoded_data = (unsigned char *)malloc(decoded_length+1);
            
            size_t decoded_len = base64_decode(data->valuestring, strlen(data->valuestring), decoded_data);
            char file_path[100];

            strcat(strcpy(file_path, DIC_FOLDER), path->valuestring);

            // 检查文件是否存在
            if (access(file_path, F_OK) != -1) {
                // 文件存在，尝试删除
                remove(file_path);
            }
            
            FILE *fp = fopen(file_path, "wb");
            if (fp == NULL) {
                input_data = "文件上传失败！";
                perror("Error opening file");
            }else{
                fwrite(decoded_data, 1, decoded_len, fp);
                fclose(fp);
                input_data = "文件上传成功！";
            }
        }
        else if(strcmp(code->valuestring,"subimagelist")==0)
        {
            strcpy(type, "subimagelist");
            // char *concatenated = concatenate_files(MULTIMASTER_IMAGE_FOLDER);
            input_data = concatenate_files(MULTIMASTER_IMAGE_FOLDER);
            // input_data = "获取辅影像列表";
            printf("%s\n", input_data);
        }
        else if(strcmp(code->valuestring,"getsubimage")==0)
        {
            strcpy(type, "getsubimage");
            char image_path[256] = MULTIMASTER_IMAGE_FOLDER;
            base64_encoded_data = image_to_base64(strcat(image_path, data->valuestring), &base64_size);
            if(!base64_encoded_data)
                input_data = "找不到图像！";
        }
        else if(strcmp(code->valuestring,"setsubimage")==0)
        {
            strcpy(type, "setsubimage");
            char from_image[256] = IMAGE_FOLDER,to_image[256] = MULTIMASTER_IMAGE_FOLDER;
            if(fopen(strcat(from_image, data->valuestring), "rb")==NULL)
                input_data = "找不到图像！";
            else{
                strcat(to_image, data->valuestring);
                // 检查文件是否存在
                if (access(to_image, F_OK) != -1)
                    input_data = "图像已存在！";
                else if(copy_image(from_image, to_image))
                    input_data = "图像设置成功！";
            }
            if(!input_data)
                input_data = "图像设置失败！";               
        }
        else if(strcmp(code->valuestring,"delsubimage")==0)
        {
            strcpy(type, "delsubimage");
            char file_path[100];
            strcat(strcpy(file_path, MULTIMASTER_IMAGE_FOLDER), data->valuestring);

            // 检查文件是否存在
            if (access(file_path, F_OK) != -1) {
                // 删���文件
                if (remove(file_path) == 0) {
                    input_data = "图像删除成功！";
                } else {
                    input_data = "图像删除失败！";
                }
            }else
                input_data = "找不到图像！";
        }


	    if(base64_encoded_data == NULL && input_data != NULL)
	    {
	    	input_length = strlen(input_data);
        	base64_encoded_data = base64_encode((const unsigned char *)input_data, input_length, &base64_size);
	    }

        if(base64_encoded_data){
            // printf("base64_size:%ld\n", base64_size);

            if(base64_size > 1024*512)
            {
                size_t once_size = 1024*128;
                char round[10];
                for(int i=1; i<=base64_size/once_size+1; i++){
                    // int i =1;
                    char once_data[once_size];
                    strncpy(once_data, base64_encoded_data + once_size*(i-1), once_size);
                    sprintf(round, "%d/%ld", i, base64_size/once_size+1);

                    size_t data_size = i!=base64_size/once_size+1? once_size: base64_size-once_size*(i-1);

                    publish_image_base64(mosq, type, round, data_size, once_data);

                    // printf("round:%s\n", round);
                }

            }else{
                // 创建一个cJSON对象
                cJSON *json_obj = cJSON_CreateObject();

                // 将结构体字段添加到cJSON对象中
                cJSON_AddStringToObject(json_obj, "name", DEVICE_ID);
                cJSON_AddStringToObject(json_obj, "type", type);
                cJSON_AddNumberToObject(json_obj, "datalen", base64_size);
                cJSON_AddStringToObject(json_obj, "data", base64_encoded_data);
                // 将cJSON对象转换为JSON字符串
                char *json_string = cJSON_Print(json_obj);
                mosquitto_publish(mosq,NULL,RESPOND_TOPIC,strlen(json_string)+1, json_string,0,0);
            }


            // if(strlen(json_string)<1000)
            //     printf("%s\n", json_string);
            // else
            //     printf("The Length image_base64_code is:%ld\n", strlen(json_string));
        }
        
        // 释放内存
        if(base64_encoded_data)
        	memset(base64_encoded_data,0,sizeof(base64_encoded_data));
        if(input_data)
        	memset(base64_encoded_data,0,sizeof(input_data));

        // 更新文件后，重启系统
        // if(strcmp(input_data, "文件上传成功！")==0) 
        //     reboot(LINUX_REBOOT_CMD_RESTART);
    }else
        printf("JSON data format error!\n"); 
}

