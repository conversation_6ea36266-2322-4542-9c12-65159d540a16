#include <stdio.h>
#include <stdlib.h>
#include <stdint.h>
#include <string.h>

// Base64字符表
const char base64_table[] = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";

// 函数：将二进制数据转换为Base64编码
char* base64_encode(const unsigned char *data, size_t input_length, size_t *output_length) {
    *output_length = 4 * ((input_length + 2) / 3); // 计算Base64编码后的长度
    char *encoded_data = (char *)malloc(*output_length);
    if (encoded_data == NULL) {
        return NULL;
    }

    for (size_t i = 0, j = 0; i < input_length;) {
        uint32_t octet_a = i < input_length ? data[i++] : 0;
        uint32_t octet_b = i < input_length ? data[i++] : 0;
        uint32_t octet_c = i < input_length ? data[i++] : 0;


        uint32_t triple = (octet_a << 0x10) + (octet_b << 0x08) + octet_c;

        encoded_data[j++] = base64_table[(triple >> 3 * 6) & 0x3F];
        encoded_data[j++] = base64_table[(triple >> 2 * 6) & 0x3F];
        encoded_data[j++] = base64_table[(triple >> 1 * 6) & 0x3F];
        encoded_data[j++] = base64_table[(triple >> 0 * 6) & 0x3F];
    }

    // 处理Base64编码后的末尾填充字符
    for (size_t i = 0; i < (3 - input_length % 3) % 3; i++) {
        encoded_data[*output_length - 1 - i] = '=';
    }
    return encoded_data;
}

char* image_to_base64(char* image_name, size_t *base64_size){
    FILE *image_file;
    size_t image_size;
    unsigned char *image_data;
    // size_t base64_size;
    char *base64_data;

    // 打开图片文件
    image_file = fopen(image_name, "rb");
    // image_file = fopen(image_name, "rb");
    if (image_file == NULL) {
        printf("Error opening file: %s\n", image_name);
        return NULL;
    }

    // 获取图片文件大小
    fseek(image_file, 0, SEEK_END);
    image_size = ftell(image_file);
    fseek(image_file, 0, SEEK_SET);


    // 分配内存来存储图片数据
    image_data = (unsigned char *)malloc(image_size);
    if (image_data == NULL) {
        printf("Memory allocation error!\n");
        fclose(image_file);
        return NULL;
    }

    // 读取图片数据
    fread(image_data, 1, image_size, image_file);
    fclose(image_file);

    // 将图片数据转换为Base64编码
    base64_data = base64_encode(image_data, image_size, base64_size);
    free(image_data);

    // 释放Base64编码内存
    // free(base64_data);
    return base64_data;
}

// 将base64字符转换为对应的十进制值
static int64_t base64_decode_char(char c) {
    if (c >= 'A' && c <= 'Z') return c - 'A';
    if (c >= 'a' && c <= 'z') return c - 'a' + 26;
    if (c >= '0' && c <= '9') return c - '0' + 52;
    if (c == '+') return 62;
    if (c == '/') return 63;
    return -1;
}

// 解码base64字符串
size_t base64_decode(const char *src, size_t src_len, unsigned char *target) {
    size_t len = 0;
    int i = 0;
    int shift = 0;
    uint32_t buffer = 0;
    
    for (i = 0; i < src_len; i++) {
        int64_t value = base64_decode_char(src[i]);
        if (value < 0) continue;
        
        buffer = (buffer << 6) | value;
        shift += 6;
        
        if (shift >= 8) {
            shift -= 8;
            target[len++] = (buffer >> shift) & 0xFF;
        }
    }
    
    return len;
}
