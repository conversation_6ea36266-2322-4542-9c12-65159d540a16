#ifndef FIO_H
#define FIO_H

#include<stdio.h>
#include<stdlib.h>
#include<string.h>
#include <dirent.h> // 包含目录操作相关的头文件
#include <unistd.h> // 包含系统调用相关的头文件
#include <stdbool.h>

#define MAX_SIZE 1024
#define MAX_IP_SUM 20

// 读取整个文件
char* read_file(const char* file_name)
{   

	FILE* fp = fopen(file_name, "r");
  
	if (fp == NULL)
	{
		printf("Error opening file: %s\n", file_name);
		return NULL;
	}

	//文件标记定位到末尾
	fseek(fp, 0, SEEK_END);
	//获取文件长度
	size_t length = ftell(fp);
	//文件标记定位到开头
	fseek(fp, 0, SEEK_SET);

	char *result;
	result = (char*)malloc((length + 1)* sizeof(char));
	if (result == NULL) 
	{
		fclose(fp);
		perror("Memory allocation error");
		return NULL;
	}
	//读取文件内容
	fread(result, sizeof(char), length, fp);

	//添加字符串结束符
	result[length] = '\0';

	fclose(fp);
	return result;
}

// 读取文件第一行
char* read_first_row(const char* file_name)
{
	
	FILE* fp = fopen(file_name, "r");
	if (fp == NULL)
	{
		perror("Error opening file");
		return NULL;
	}
	char *result = (char*)malloc(MAX_SIZE* sizeof(char));
	fgets(result , MAX_SIZE, fp);
	fclose(fp);
	if(result[strlen(result)-1]=='\n')
		result[strlen(result )-1]='\0';
	else
		result[strlen(result)] = '\0';

	return result;
}

// 读取文件最后一行
char* read_last_row(const char* file_name)
{
    FILE* fp = fopen(file_name, "r");
    if (fp == NULL)
    {
        perror("Error opening file");
        return NULL;
    }
    char *result = (char*)malloc(MAX_SIZE* sizeof(char));
    char *row_data = (char*)malloc(MAX_SIZE* sizeof(char));
    while(fgets(row_data, MAX_SIZE, fp) != NULL)
        result = row_data;
    
    fclose(fp);
    if(result[strlen(result)-1]=='\n')
        result[strlen(result )-1]='\0';
    else
        result[strlen(result)] = '\0';

    return result;
}

// 删除文件夹下的所有文件
bool remove_all_file(char* folder_name)
{
	char file_path[MAX_SIZE];
	DIR* dir = opendir(folder_name);
	if (dir == NULL) 
	{
        perror("Error opening directory");
        return false;
    }

    struct dirent* entry;
    while ((entry = readdir(dir)) != NULL) 
    {
    	// DT_REG 表示普通文件
        if (entry->d_type == DT_REG) 
        { 
        	// 拼接文件名称
            strcat(strcpy(file_path,folder_name), entry->d_name);
            
            if (remove(file_path) == 0) 
                printf("Removed: %s\n", file_path);
			else
                perror("Error removing file");
        }
    }

    closedir(dir);
    return true;
}

// 返回文件夹下第一个文件的文件名
char* get_first_file_name(char* folder_name)
{
    char file_path[MAX_SIZE];
    DIR* dir = opendir(folder_name);
    if (dir == NULL) 
    {
        perror("Error opening directory");
        return NULL;
    }

    struct dirent* entry;
    while ((entry = readdir(dir)) != NULL) 
    {
        // DT_REG 表示普通文件
        if (entry->d_type == DT_REG) 
        { 
            // 拼接文件名称
            strcat(strcpy(file_path,folder_name), entry->d_name);
            closedir(dir);
            // 主影像文件夹下只有一张图像，遍历的第一个结果直接返回即可
            return entry->d_name;
        }
    }
    closedir(dir);
    return NULL;
}

// 复制图片
bool copy_image(const char* from_image_name, const char* to_image_name) 
{
	FILE* fp;
    size_t image_size;
    unsigned char* image_data;

    // 打开图片文件
    fp = fopen(from_image_name, "rb");
    if (fp == NULL) 
    {
        perror("Error opening file");
        return false;
    }

    // 获取图片文件大小
    fseek(fp, 0, SEEK_END);
    image_size = ftell(fp);
    fseek(fp, 0, SEEK_SET);

    // 分配内存来存储图片数据
    image_data = (unsigned char*)malloc(image_size);
    if (image_data == NULL) 
    {
        perror("Memory allocation error");
        fclose(fp);
        return false;
    }

    // 读取图片数据
    fread(image_data, 1, image_size, fp);
    fclose(fp);

    fp = fopen(to_image_name, "wb");
    if (fp == NULL) {
        perror("Error opening file");
        return false;
    }
    fwrite(image_data, 1, image_size, fp);

    fclose(fp);
    free(image_data);

    return true;
}

// 向文件末尾插入一行数据
bool write_last_row(const char* file_name, char* data)
{
    FILE* fp = fopen(file_name, "a+");
    if (fp == NULL) {
        perror("Error opening file");
        return false;
    }
    fseek(fp, 0, SEEK_END);
    if (ftell(fp) > 0) 
    {
        fseek(fp, -1, SEEK_CUR);
        if (fgetc(fp) != '\n')
            fputc('\n', fp);
    }
    fwrite(data, 1, strlen(data), fp);

    fseek(fp, 0, SEEK_END);
    if (fgetc(fp) != '\n')
        fputc('\n', fp);

    fclose(fp);
    return true;
}

// 获取文件末尾最后一行序号，并带序号向末尾插入一行数据
bool write_last_row_include_index(const char* file_name, char* data)
{
    FILE* fp = fopen(file_name, "a+");
    if (fp == NULL) {
        perror("Error opening file");
        return false;
    }
    char result[MAX_SIZE];
    fseek(fp, 0, SEEK_END);
    long size = ftell(fp);
    fseek(fp, 0, SEEK_SET);
    if (size == 0) {
        // 将数字和字符串拼接到 result 中，如果文件为空，从序号1开始
        snprintf(result, sizeof(result), "1\t%s", data);
    } else {
        char last_line[MAX_SIZE];
        while(fgets(last_line, MAX_SIZE, fp) != NULL)
            ;
        int count;
        // 获取文件最后一行的序号
        sscanf(last_line, "%d", &count);
        // 当前序号为文件最后一行序号+1
        snprintf(result, sizeof(result), "%d\t%s", count+1, data);
    }
    fclose(fp);
    // 写入文件最后一行
    write_last_row(file_name, result);
    return true;
}

// 向文件重新写入数据
bool write_file(const char* file_name, char* data)
{
    FILE* fp = fopen(file_name, "w");
    if (fp == NULL) {
        perror("Error opening file");
        return false;
    }
    fwrite(data, 1, strlen(data), fp);
    fclose(fp);
    return true;
}


// 存储IP、端口号
struct Ip
{
        char host[20];
        int port;
};

int ip_count = 0;
// 获取mqtt服务器全部IP、端口号
struct Ip* get_host_port(const char* file_name)
{
        char data[MAX_IP_SUM][MAX_SIZE] = { '\0' };
        FILE* fp = fopen(file_name, "r");
        if (fp == NULL)
        {
                perror("Error opening file");
                // exit(0);
                return NULL;
        }
        int i = 0;
        // 将文件数据按行取出
        while (fgets(data[i], MAX_SIZE, fp) != NULL)
                i++;
        ip_count = i;
        struct Ip* result, host_port[MAX_IP_SUM]={ 0 };
        result = host_port;

        for(i=0;data[i][0] != '\0';i++)
        {
                int j = 0;
                host_port[i].port = 0;
                // 获取IP
                while(data[i][j] != ' ')
                {
                        host_port[i].host[j] = data[i][j];
                        j++;
                }
                // 获取端口号
                while(data[i][++j] != '\n')
                        host_port[i].port = host_port[i].port * 10 + data[i][j] - '0';
        }
        fclose(fp);
        return result;
}

// 获取文件夹下文件名，用换行符拼接
// char* concatenate_files(const char *path) {
//     struct dirent *entry;
//     DIR *dp = opendir(path);
//     if (dp == NULL) {
//         perror("opendir");
//         return NULL;
//     }

//     char *result = NULL;
//     size_t result_size = 0;

//     while ((entry = readdir(dp))) {
//         // 跳过 . 和 .. 目录
//         if (entry->d_name[0] != '.') {
//             size_t entry_len = strlen(entry->d_name);
//             result = realloc(result, result_size + entry_len + 2); 
//             if (result == NULL) {
//                 perror("realloc");
//                 closedir(dp);
//                 return NULL;
//             }
//             strcat(result, entry->d_name);
//             strcat(result, "\n");
//             result_size += entry_len + 1;
//         }
//     }

//     closedir(dp);
//     return result;
// }
char* concatenate_files(const char *path) {
    struct dirent *entry;
    DIR *dp = opendir(path);
    if (dp == NULL) {
        perror("opendir");
        return NULL;
    }

    char *result = NULL;
    size_t result_size = 0;

    // 为result分配初始内存
    result = malloc(1);
    if (result == NULL) {
        perror("malloc");
        closedir(dp);
        return NULL;
    }
    result[0] = '\0';

    while ((entry = readdir(dp))) {
        // 跳过 . 和 .. 目录
        if (entry->d_name[0] != '.') {
            size_t entry_len = strlen(entry->d_name);
            result = realloc(result, result_size + entry_len + 2); 
            if (result == NULL) {
                perror("realloc");
                closedir(dp);
                return NULL;
            }
            strcat(result, entry->d_name);
            strcat(result, "\n");
            result_size += entry_len + 1;
        }
    }

    closedir(dp);
    return result;
}

#endif // FIO_H