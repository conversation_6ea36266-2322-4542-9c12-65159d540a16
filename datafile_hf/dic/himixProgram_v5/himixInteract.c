#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>
#include <string.h>
#include "mosquitto.h"
#include "source/mqtt.h"
#include "source/file_monitor.h"
#include <sqlite3.h>

static struct mosquitto *g_mosq = NULL;

// 添加全局变量存储监控实例
static struct file_monitor *points_monitor = NULL;
static struct file_monitor *video_monitor = NULL;

void file_changed_callback(const char *file_path) {
    printf("File changed callback triggered for: %s\n", file_path);
    
    if (access(file_path, F_OK) != 0) {
        printf("File deleted: %s\n", file_path);
        return;
    }
    
    printf("Reading file content...\n");
    char *content = read_file(file_path);
    if (content == NULL) {
        fprintf(stderr, "Failed to read file: %s\n", file_path);
        return;
    }

    // 解析文件内容
    char *line = strtok(content, "\n");
    if (line == NULL) {
        free(content);
        return;
    }

    // 解析第一行获取时间和温度
    char time_str[15];
    float temp;
    sscanf(line, "%s %f", time_str, &temp);

    // 连接数据库
    sqlite3 *db;
    int rc = sqlite3_open(DB_PATH, &db);
    if (rc) {
        fprintf(stderr, "Can't open database: %s\n", sqlite3_errmsg(db));
        free(content);
        return;
    }

    // 检查数据库文件权限
    if (access(DB_PATH, W_OK) != 0) {
        fprintf(stderr, "Database file is not writable: %s\n", DB_PATH);
        char cmd[256];
        snprintf(cmd, sizeof(cmd), "chmod 666 %s", DB_PATH);
        system(cmd);
    }

    // 准备SQL语句
    sqlite3_stmt *stmt;
    const char *sql = "INSERT INTO target_data (hp, dh, dv, coef, target_size, "
                     "col_width, row_width, is_ref_point, tm, target_no, is_manual, temp) "
                     "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
                     
    printf("sql: %s\n", sql);
    rc = sqlite3_prepare_v2(db, sql, -1, &stmt, NULL);
    if (rc != SQLITE_OK) {
        fprintf(stderr, "Failed to prepare statement: %s\n", sqlite3_errmsg(db));
        sqlite3_close(db);
        free(content);
        return;
    }

    // 读取并插入每个点的数据
    while ((line = strtok(NULL, "\n")) != NULL) {
        int no, col, row, target_size, col_width, row_width, is_ref, is_manual;
        float dh, dv, coef;
        
        printf("Processing line: %s\n", line);  // 打印当前处理的行
        
        rc = sscanf(line, "%d %d %d %f %f %f %d %d %d %d %d",
                   &no, &col, &row, &dh, &dv, &coef, 
                   &target_size, &col_width, &row_width, 
                   &is_ref, &is_manual);
        
        printf("sscanf result: %d\n", rc);  // 打印sscanf的返回值
        
        if (rc == 11) {  // 确保读取了所有字段
            float hp = sqrt(dh*dh + dv*dv);  // 计算位移量
            
            // 构造实际的 SQL 语句用于调试
            char debug_sql[1024];
            snprintf(debug_sql, sizeof(debug_sql), 
                "INSERT INTO target_data (hp, dh, dv, coef, target_size, col_width, row_width, is_ref_point, tm, target_no, is_manual, temp) "
                "VALUES (%f, %f, %f, %f, %d, %d, %d, %d, '%s', '%d', %d, %f);",
                hp, dh, dv, coef, target_size, col_width, row_width, is_ref, 
                time_str, no, is_manual, temp);
            printf("Executing SQL: %s\n", debug_sql);

            sqlite3_bind_double(stmt, 1, hp);
            sqlite3_bind_double(stmt, 2, dh);
            sqlite3_bind_double(stmt, 3, dv);
            sqlite3_bind_double(stmt, 4, coef);
            sqlite3_bind_int(stmt, 5, target_size);
            sqlite3_bind_int(stmt, 6, col_width);
            sqlite3_bind_int(stmt, 7, row_width);
            sqlite3_bind_int(stmt, 8, is_ref);
            sqlite3_bind_text(stmt, 9, time_str, -1, SQLITE_STATIC);
            char target_no_str[10];
            sprintf(target_no_str, "%d", no);
            sqlite3_bind_text(stmt, 10, target_no_str, -1, SQLITE_STATIC);
            sqlite3_bind_int(stmt, 11, is_manual);
            sqlite3_bind_double(stmt, 12, temp);

            rc = sqlite3_step(stmt);
            if (rc != SQLITE_DONE) {
                fprintf(stderr, "Failed to insert data: %s\n", sqlite3_errmsg(db));
                printf("Error code: %d\n", rc);  // 打印错误代码
            } else {
                printf("Successfully inserted data for point %d\n", no);
            }
            sqlite3_reset(stmt);
        } else {
            printf("Failed to parse line: %s\n", line);
        }
    }

    // 清理资源
    sqlite3_finalize(stmt);
    sqlite3_close(db);
    free(content);


}

// 添加新的回调函数处理 video_displacement.txt 的变化
void video_file_changed_callback(const char *file_path) {
    if (access(file_path, F_OK) != 0) {
        return;
    }

    // 等待1秒，确保文件写入完成
    usleep(1000000);  // 1000000微秒 = 1秒

    printf("Reading video displacement file: %s\n", file_path);
    char *content = read_file(file_path);
    if (content == NULL) {
        fprintf(stderr, "Failed to read video displacement file: %s\n", file_path);
        return;
    }

    // 将内容编码为 base64
    size_t input_length = strlen(content);
    size_t base64_size;
    char *base64_data = base64_encode((const unsigned char *)content, input_length, &base64_size);
    
    if (base64_data != NULL) {
        // 发布消息
        publish_msg(DEVICE_ID, g_mosq, "videoDisplacement", base64_data, base64_size, NULL, 0, NULL);
        free(base64_data);
    }

    free(content);
}

int main(int argc, char *argv[])
{
    // 重定向 stdout 和 stderr 到日志文件
    FILE *log_file = fopen(LOG_FILE, "a");
    if (log_file != NULL) {
        dup2(fileno(log_file), STDOUT_FILENO);
        dup2(fileno(log_file), STDERR_FILENO);
        setvbuf(stdout, NULL, _IOLBF, 1024);  // 设置行缓冲
        setvbuf(stderr, NULL, _IOLBF, 1024);  // 设置行缓冲
    } else {
        perror("Failed to open log file");
        return 1;
    }

    DEVICE_ID = argv[1];
    HOST = argv[2];  
    sscanf(argv[3], "%d", &PORT);
    strcat(UP_TOPIC, DEVICE_ID);
    strcat(DOWN_TOPIC, DEVICE_ID);
    strcat(RESPOND_TOPIC, DEVICE_ID);

    // 如果点位文件存在，先读取并插入数据库
    if (access(CHANGE_DATA_FILE, F_OK) == 0) {
        printf("Initial reading of displacement file...\n");
        file_changed_callback(CHANGE_DATA_FILE);
    }

    struct mosquitto *mosq, *mosq_sub;
            
    if(mosquitto_lib_init()){
        printf("Init lib error!\n");
        exit(-1);
    }
    
    char name[20] = "demo_";
    strcat(name, DEVICE_ID);
    mosq = mosquitto_new(name, true, NULL);
    if(mosq == NULL){
        printf("New sub_test error!\n");
        mosquitto_lib_cleanup();
        exit(-1);
    }

    g_mosq = mosq;

    mosquitto_connect_callback_set(mosq, sub_connect_callback);
    mosquitto_disconnect_callback_set(mosq, sub_disconnect_callback);
    mosquitto_subscribe_callback_set(mosq, subscribe_callback);
    mosquitto_message_callback_set(mosq, message_callback);
    mosquitto_publish_callback_set(mosq, publish_callback);

    // 启动对 points_displacement.txt 的监控
    printf("Starting file monitor for: %s\n", CHANGE_DATA_FILE);
    for (int retry = 0; retry < 3; retry++) {
        points_monitor = start_file_monitor(CHANGE_DATA_FILE, file_changed_callback);
        if (points_monitor) break;
        printf("Retry %d/3...\n", retry + 1);
        sleep(1);
    }
    
    if (!points_monitor) {
        fprintf(stderr, "Failed to start points displacement file monitor.\n");
        mosquitto_destroy(mosq);
        mosquitto_lib_cleanup();
        return 1;
    }

    // 启动对 video_displacement.txt 的监控
    printf("Starting file monitor for: %s\n", VIDEO_DISPLACEMENT_FILE);
    video_monitor = start_file_monitor(VIDEO_DISPLACEMENT_FILE, video_file_changed_callback);
    if (!video_monitor) {
        fprintf(stderr, "Failed to start video displacement file monitor.\n");
        stop_file_monitor(points_monitor);
        mosquitto_destroy(mosq);
        mosquitto_lib_cleanup();
        return 1;
    }

    int ret = mosquitto_connect(mosq, HOST, PORT, KEEP_ALIVE);

    printf("Start MQTT and file monitoring!\n");
    while(1)
    {
        if(ret){
            printf("Connect server error!\n");
            ret = mosquitto_connect(mosq, HOST, PORT, KEEP_ALIVE);
            continue;
        }
        mosquitto_loop(mosq, -1, 1);
    }

    // 在程序结束时清理资源
    stop_file_monitor(points_monitor);
    stop_file_monitor(video_monitor);
    mosquitto_destroy(mosq);
    mosquitto_lib_cleanup();

    fclose(log_file);  // 关闭日志文件

    return 0;
}
