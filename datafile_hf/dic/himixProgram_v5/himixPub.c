#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>
#include <string.h>
#include <pthread.h>
#include "mosquitto.h"
#include "source/cJSON.h"
#include "source/mqtt.h"


// 发布形变数据
void* publish_displacement(void* arg)
{
    struct mosquitto *mosq;
            
    //初始化libmosquitto库
    if(mosquitto_lib_init()){
        printf("Init lib error!\n");
        exit(-1);
    }
    //创建一个发布端实例
    char name[50] = "pubData_";
    strcat(name, DEVICE_ID);
    mosq =  mosquitto_new(name, true, NULL);
    if(mosq == NULL){
        printf("New pub_test error!\n");
        mosquitto_lib_cleanup();
        exit(-1);
    }

    //设置回调函数
    mosquitto_publish_callback_set(mosq, publish_callback);
  
    char* change_data;
    while(1)
    {
        change_data = read_file(CHANGE_DATA_FILE); 
        // 将字符串数据编码为Base64
        size_t input_length = strlen(change_data);
        size_t base64_size;
        char *change_base64 = base64_encode((const unsigned char *)change_data, input_length, &base64_size);
        publish_msg(arg, mosq, CHANGE_TYPE, change_base64, base64_size, NULL, 0, NULL);
        sleep(60);  
    }

    mosquitto_destroy(mosq);
    mosquitto_lib_cleanup();

    return NULL;
}


// 遍历mqtt服务器,发布消息
bool publish_msg_(MQTTClient client, const char* type, char* message, size_t msglen, char* other ,size_t otherlen, char* round)
{
    // 创建一个cJSON对象
    cJSON *json_obj = cJSON_CreateObject();
    cJSON_AddStringToObject(json_obj, "name", DEVICE_ID);
    cJSON_AddStringToObject(json_obj, "type", type);
    // 如果发布的是图片数据，则发布数据中含有图片时间、名称信息
    if(strcmp(type,IMAGE_TYPE)==0)
    {
        cJSON_AddNumberToObject(json_obj, "imageinfolen", otherlen);
        cJSON_AddStringToObject(json_obj, "imageinfo", other);
        cJSON_AddStringToObject(json_obj, "round", round);
    }
    cJSON_AddNumberToObject(json_obj, "datalen", msglen);
    cJSON_AddStringToObject(json_obj, "data", message);

    // 将cJSON对象转换为JSON字符串
    char *json_string = cJSON_Print(json_obj);

    MQTTClient_message pubmsg = MQTTClient_message_initializer;
    MQTTClient_deliveryToken token;

    pubmsg.payload = json_string;
    pubmsg.payloadlen = strlen(pubmsg.payload) + 1;
    pubmsg.qos = 0;
    pubmsg.retained = 0;

    MQTTClient_publishMessage(client, UP_TOPIC, &pubmsg, &token);

    // 释放内存
    if(message)
            memset(message,0,sizeof(message));
    if(json_string)
           memset(json_string,0,sizeof(json_string));

    return true;
}


// 发布base64编码后的图像
void* publish_image(void* arg)
{
    char image_name[256], *image_base64, *imageinfo_base64;
    while(1)
    {
        MQTTClient client;
        MQTTClient_connectOptions conn_opts = MQTTClient_connectOptions_initializer;

        struct Ip* host_port = (struct Ip*)arg;
        char ip_string[50];
        // 使用 sprintf 将 host 和 port 拼接成字符串
        sprintf(ip_string, "%s:%d", host_port->host, host_port->port);

        //创建一个发布端实例
        char name[50] = "pubImage_";
        strcat(name, DEVICE_ID);
        MQTTClient_create(&client, ip_string, name, MQTTCLIENT_PERSISTENCE_NONE, NULL);

        conn_opts.keepAliveInterval = 20;
        conn_opts.cleansession = 1;

        if (MQTTClient_connect(client, &conn_opts) != MQTTCLIENT_SUCCESS)
        {
            // printf("%s, Failed to connect to MQTT broker\n", ip_string);
            // exit(-1);
        }

        
        time_t current_time;
        struct tm *timeinfo;

        // 获取当前时间
        time(&current_time);
        timeinfo = localtime(&current_time);

        // 如果是整点，发布图像
        if(timeinfo->tm_min == 01 || timeinfo->tm_min == 31)
        {
            size_t image_base64_size, imageinfo_base64_size;
            char* last_row = read_last_row(IMAGE_LIST_FILE);
            sscanf(last_row, "%*d%*[ ]%s", image_name);
            char image_path[MAX_SIZE]=IMAGE_FOLDER;
            strcat(image_path,image_name);
            image_base64 = image_to_base64(image_path, &image_base64_size);
            // 将字符串数据编码为Base64
            size_t input_length = strlen(last_row);
            imageinfo_base64 = base64_encode((const unsigned char *)last_row, input_length, &imageinfo_base64_size);
            size_t once_size = 1024*32;
            char round[10];
            for(int i=1; i<=image_base64_size/once_size+1; i++){
                char once_data[once_size];
                strncpy(once_data, image_base64 + once_size*(i-1), once_size);
                sprintf(round, "%d/%ld", i, image_base64_size/once_size+1);
                publish_msg_(client, IMAGE_TYPE, once_data, image_base64_size, imageinfo_base64, imageinfo_base64_size, round);
            }
            MQTTClient_disconnect(client, 10);
            MQTTClient_destroy(&client);
            sleep(1600);
        }else{
            MQTTClient_disconnect(client, 10);
            MQTTClient_destroy(&client);
            sleep(10);
        }
    }

    return NULL;
}

int main(int argc, char *argv[])
{
    DEVICE_ID = argv[1];
    strcat(UP_TOPIC, DEVICE_ID);
    printf("1");
    struct Ip* host_port = get_host_port(HOST_PORT_FILE);
    pthread_t displacement_thread[ip_count], image_thread[ip_count];
    for(int i=0; i<ip_count; i++)
    {
        int ret1,ret2;
        // 创建发布形变数据的线程
        ret1 = pthread_create(&displacement_thread[i], NULL, publish_displacement, &host_port[i]);
        // 创建发布图像的线程
        ret2 = pthread_create(&image_thread[i], NULL, publish_image, &host_port[i]);
        if (ret1 || ret2) {
        // if (ret2) {
            printf("Error creating thread!\n");
            return -1;
        }
    }

    // 等待线程结束
    for (int i = 0; i < ip_count; i++) {
        pthread_join(displacement_thread[i], NULL);
        pthread_join(image_thread[i], NULL);
    }

    return 0;
}
