#include <iostream>
#include <opencv2/opencv.hpp>
#include <fstream>
#include <sstream>
#include <vector>
#include <string>
#include <chrono>
std::vector<std::vector<float>> readFile(std::string txtfile)
{
    
    // 打开txt文件
    std::cout << "打开txt文件读取数据：" << std::endl;
    std::ifstream file(txtfile.c_str());
    if (!file.is_open())
    {
        std::cout << "无法打开文件" << std::endl;
        return std::vector<std::vector<float>> ();
    }

    std::cout << "设置存储数据的二维向量" << std::endl;
    std::string line;
    std::vector<std::vector<float>> data; // 存储数据的二维向量

    std::cout << "逐行读取txt文件" << std::endl;
    // 逐行读取txt文件
    while (std::getline(file, line))
    {
        std::vector<float> row; // 存储每一行数据的向量

        // 使用字符串流分割每一行的数据
        std::istringstream iss(line);
        std::string token;
        while (std::getline(iss, token, ' '))
        {
            try{
                float value = std::stof(token); // 将字符串转换为浮点数
                row.push_back(value);
                // std::cout << "数据："<< value << std::endl;
            }
            catch (...)
            {
                // 捕获所有其他类型的异常
                // std::cout << "空格数据" << std::endl;
            }
        }

        data.push_back(row); // 将每一行数据添加到二维向量中
    }

    // 输出读取的数据
    std::cout<<"-------------------------------------------------------txt 文件中的数据是："<<std::endl;
    for (const auto& row : data)
    {
        for (const auto& value : row)
        {
            std::cout << value << " ";
        }
        std::cout << std::endl;
    }
    std::cout<<"--------------------------------------------------------------"<<std::endl;

    // 关闭文件
    file.close();

    return data;
}

int writeFile(std::string txtfile, std::vector<std::vector<float>> data) {
    // 创建文件流对象
    std::ofstream file(txtfile.c_str());

    if (file.is_open()) {
        // 写入整数数字到文件中
        for(auto &line: data){
            assert (line.size()==5);
            file << line[0] <<" "<< line[1] <<" "<< line[2] << " " << line[3]<<" "<< line[4] << std::endl;
        }

        // 关闭文件
        file.close();
        //std::cout << "检测结果已成功写入文件："<< txtfile << std::endl;
    } else {
        std::cout << "无法打开文件写入，检查是否有权限写入文件！" << std::endl;
        return -1;
    }

    return 0;
}


int main(int argc, char* argv[]) {
    

    // 检查是否有足够的参数
    if (argc < 5) {
        std::cout << "参数不足！ 请输入 txt文件，图片A，图片B，输出txt文件" << std::endl;
        return 1;
    }

    // 读取第一个参数为std::string
    //txt文件
    std::string txtfile = argv[1];
    //图片A
    std::string imagePath1 = argv[2];
    //图片B
    std::string imag1Path2 = argv[3];
    //输出txt文件
    std::string outfile = argv[4];

    //打开摄像头
    cv::VideoCapture cap("rtsp://admin:admin@192.168.1.163:554/snl/live/1/1"); 

    std::vector<std::vector<float>> data = readFile(txtfile);
    // 读取图像A和图像B
    cv::Mat image1 = cv::imread(imagePath1);
    //cv::Mat image1;
    //cap >> image1;
    //cap.read(image1);
    // 检查当前帧是否读取成功
    //if (image1.empty()) {
        //std::cout << "Failed to read image1." << std::endl;
        //break;
    //}

    //开始时间
    auto start = std::chrono::high_resolution_clock::now();
    for(int ihz = 1; ihz < 125; ihz++)
    { 
        cv::Mat image2;
        //cap >> image2;
        cap.read(image2);
        // 检查当前帧是否读取成功
        if (image2.empty()) {
            std::cout << "Failed to read image2." << std::endl;
            //break;
        }

        //cv::Mat image2 = cv::imread(imag1Path2);
        int method = cv::TM_CCOEFF_NORMED; 
        //识别结果
        std::vector<std::vector<float>> data_res;

        int w1=image1.cols, h1=image1.rows;
        std::cout<<"图片1的宽高为："<<w1<<", "<<h1<<std::endl;
        for(int i=0; i<data.size(); i++){
                std::vector<float> line = data[i];
                if(line.size()<4)
                {
                    std::cout<<"第"<<i+1<<"行数据有问题，跳过它"<<std::endl;
                    continue;
                }
                float cx = line[0];
                float cy = line[1];

                int model_half  = line[2]/2.0;
                int pmodel_half = model_half*2.0;
                int model_size  = model_half*2.0 + 1;
                int pmodel_size = pmodel_half*2.0 + 1;

                int x = cx - model_half;
                int y = cy - model_half;
                
                int px = cx - pmodel_half;
                int py = cy - pmodel_half;           

                std::cout<<"目标框的x,y,w,h: "<<x<<", "<<y<<", "<<model_size<<", "<<model_size<<std::endl;
                if(px<0 || py<0 || px+pmodel_size>=w1 || py+pmodel_size>=h1){
                    std::cout<<"第"<<i+1<<"行的框超出图片范围，跳过它"<<std::endl;
                    continue;
                }
                cv::Mat imageA;
                cv::Mat imageB;
                image1(cv::Rect(x,y,model_size,model_size)).copyTo(imageA);
                image2(cv::Rect(px,py,pmodel_size,pmodel_size)).copyTo(imageB);
                // 创建结果图像
                cv::Mat result;

                cv::matchTemplate(imageB, imageA, result, method);
                double minVal, maxVal;
                cv::Point minLoc, maxLoc;
                cv::minMaxLoc(result, &minVal, &maxVal, &minLoc, &maxLoc);
                float coeff = maxVal;

                std::vector<float> res_xywh={cx,cy,cx,cy,coeff};
                data_res.push_back(res_xywh);

        }
        int statu = writeFile(outfile, data_res);
    }
    //结束时间
    auto end = std::chrono::high_resolution_clock::now();

    //程序运行时间
    std::chrono::duration<double> duration = end - start;
    std::cout << "程序运行时间：" << duration.count() << "秒（s）" << std::endl;
    return 0;
}
