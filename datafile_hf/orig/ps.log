UID          PID    PPID  C STIME TTY          TIME CMD
root           1       0  0 10:31 ?        00:00:01 /sbin/init splash
root           2       0  0 10:31 ?        00:00:00 [kthreadd]
root           3       2  0 10:31 ?        00:00:00 [rcu_gp]
root           4       2  0 10:31 ?        00:00:00 [rcu_par_gp]
root           5       2  0 10:31 ?        00:00:00 [slub_flushwq]
root           6       2  0 10:31 ?        00:00:00 [netns]
root           8       2  0 10:31 ?        00:00:00 [kworker/0:0H-events_highpri]
root           9       2  0 10:31 ?        00:00:00 [kworker/u24:0-events_power_efficient]
root          10       2  0 10:31 ?        00:00:00 [mm_percpu_wq]
root          11       2  0 10:31 ?        00:00:00 [rcu_tasks_kthread]
root          12       2  0 10:31 ?        00:00:00 [rcu_tasks_rude_kthread]
root          13       2  0 10:31 ?        00:00:00 [rcu_tasks_trace_kthread]
root          14       2  0 10:31 ?        00:00:00 [ksoftirqd/0]
root          15       2  0 10:31 ?        00:00:00 [rcu_preempt]
root          16       2  0 10:31 ?        00:00:00 [migration/0]
root          17       2  0 10:31 ?        00:00:00 [idle_inject/0]
root          19       2  0 10:31 ?        00:00:00 [cpuhp/0]
root          20       2  0 10:31 ?        00:00:00 [cpuhp/1]
root          21       2  0 10:31 ?        00:00:00 [idle_inject/1]
root          22       2  0 10:31 ?        00:00:00 [migration/1]
root          23       2  0 10:31 ?        00:00:00 [ksoftirqd/1]
root          24       2  0 10:31 ?        00:00:00 [kworker/1:0-events]
root          25       2  0 10:31 ?        00:00:00 [kworker/1:0H-events_highpri]
root          26       2  0 10:31 ?        00:00:00 [cpuhp/2]
root          27       2  0 10:31 ?        00:00:00 [idle_inject/2]
root          28       2  0 10:31 ?        00:00:00 [migration/2]
root          29       2  0 10:31 ?        00:00:00 [ksoftirqd/2]
root          30       2  0 10:31 ?        00:00:00 [kworker/2:0-events]
root          31       2  0 10:31 ?        00:00:00 [kworker/2:0H-events_highpri]
root          32       2  0 10:31 ?        00:00:00 [cpuhp/3]
root          33       2  0 10:31 ?        00:00:00 [idle_inject/3]
root          34       2  0 10:31 ?        00:00:00 [migration/3]
root          35       2  0 10:31 ?        00:00:00 [ksoftirqd/3]
root          37       2  0 10:31 ?        00:00:00 [kworker/3:0H-events_highpri]
root          38       2  0 10:31 ?        00:00:00 [cpuhp/4]
root          39       2  0 10:31 ?        00:00:00 [idle_inject/4]
root          40       2  0 10:31 ?        00:00:00 [migration/4]
root          41       2  0 10:31 ?        00:00:00 [ksoftirqd/4]
root          43       2  0 10:31 ?        00:00:00 [kworker/4:0H-events_highpri]
root          44       2  0 10:31 ?        00:00:00 [cpuhp/5]
root          45       2  0 10:31 ?        00:00:00 [idle_inject/5]
root          46       2  0 10:31 ?        00:00:00 [migration/5]
root          47       2  0 10:31 ?        00:00:00 [ksoftirqd/5]
root          48       2  0 10:31 ?        00:00:00 [kworker/5:0-events]
root          49       2  0 10:31 ?        00:00:00 [kworker/5:0H-events_highpri]
root          50       2  0 10:31 ?        00:00:00 [cpuhp/6]
root          51       2  0 10:31 ?        00:00:00 [idle_inject/6]
root          52       2  0 10:31 ?        00:00:00 [migration/6]
root          53       2  0 10:31 ?        00:00:00 [ksoftirqd/6]
root          54       2  0 10:31 ?        00:00:00 [kworker/6:0-cgroup_destroy]
root          55       2  0 10:31 ?        00:00:00 [kworker/6:0H-events_highpri]
root          56       2  0 10:31 ?        00:00:00 [cpuhp/7]
root          57       2  0 10:31 ?        00:00:00 [idle_inject/7]
root          58       2  0 10:31 ?        00:00:00 [migration/7]
root          59       2  0 10:31 ?        00:00:00 [ksoftirqd/7]
root          61       2  0 10:31 ?        00:00:00 [kworker/7:0H-events_highpri]
root          62       2  0 10:31 ?        00:00:00 [cpuhp/8]
root          63       2  0 10:31 ?        00:00:00 [idle_inject/8]
root          64       2  0 10:31 ?        00:00:00 [migration/8]
root          65       2  0 10:31 ?        00:00:00 [ksoftirqd/8]
root          67       2  0 10:31 ?        00:00:00 [kworker/8:0H-events_highpri]
root          68       2  0 10:31 ?        00:00:00 [cpuhp/9]
root          69       2  0 10:31 ?        00:00:00 [idle_inject/9]
root          70       2  0 10:31 ?        00:00:00 [migration/9]
root          71       2  0 10:31 ?        00:00:00 [ksoftirqd/9]
root          73       2  0 10:31 ?        00:00:00 [kworker/9:0H-events_highpri]
root          74       2  0 10:31 ?        00:00:00 [cpuhp/10]
root          75       2  0 10:31 ?        00:00:00 [idle_inject/10]
root          76       2  0 10:31 ?        00:00:00 [migration/10]
root          77       2  0 10:31 ?        00:00:00 [ksoftirqd/10]
root          79       2  0 10:31 ?        00:00:00 [kworker/10:0H-events_highpri]
root          80       2  0 10:31 ?        00:00:00 [cpuhp/11]
root          81       2  0 10:31 ?        00:00:00 [idle_inject/11]
root          82       2  0 10:31 ?        00:00:00 [migration/11]
root          83       2  0 10:31 ?        00:00:00 [ksoftirqd/11]
root          85       2  0 10:31 ?        00:00:00 [kworker/11:0H-events_highpri]
root          86       2  0 10:31 ?        00:00:00 [kdevtmpfs]
root          87       2  0 10:31 ?        00:00:00 [inet_frag_wq]
root          88       2  0 10:31 ?        00:00:00 [kauditd]
root          89       2  0 10:31 ?        00:00:00 [khungtaskd]
root          91       2  0 10:31 ?        00:00:00 [oom_reaper]
root          93       2  0 10:31 ?        00:00:00 [writeback]
root          94       2  0 10:31 ?        00:00:00 [kcompactd0]
root          95       2  0 10:31 ?        00:00:00 [ksmd]
root          96       2  0 10:31 ?        00:00:00 [kworker/6:1-events_freezable]
root          97       2  0 10:31 ?        00:00:00 [khugepaged]
root          98       2  0 10:31 ?        00:00:00 [kintegrityd]
root          99       2  0 10:31 ?        00:00:00 [kblockd]
root         100       2  0 10:31 ?        00:00:00 [blkcg_punt_bio]
root         101       2  0 10:31 ?        00:00:00 [kworker/7:1-mm_percpu_wq]
root         102       2  0 10:31 ?        00:00:00 [tpm_dev_wq]
root         103       2  0 10:31 ?        00:00:00 [ata_sff]
root         104       2  0 10:31 ?        00:00:00 [md]
root         105       2  0 10:31 ?        00:00:00 [edac-poller]
root         106       2  0 10:31 ?        00:00:00 [devfreq_wq]
root         107       2  0 10:31 ?        00:00:00 [watchdogd]
root         108       2  0 10:31 ?        00:00:00 [kworker/7:1H-kblockd]
root         109       2  0 10:31 ?        00:00:00 [kswapd0]
root         110       2  0 10:31 ?        00:00:00 [ecryptfs-kthread]
root         111       2  0 10:31 ?        00:00:00 [kthrotld]
root         112       2  0 10:31 ?        00:00:00 [irq/122-aerdrv]
root         113       2  0 10:31 ?        00:00:00 [irq/123-aerdrv]
root         114       2  0 10:31 ?        00:00:00 [acpi_thermal_pm]
root         115       2  0 10:31 ?        00:00:00 [hfi-updates]
root         116       2  0 10:31 ?        00:00:00 [mld]
root         117       2  0 10:31 ?        00:00:00 [ipv6_addrconf]
root         124       2  0 10:31 ?        00:00:00 [kstrp]
root         125       2  0 10:31 ?        00:00:00 [kworker/3:1-mm_percpu_wq]
root         127       2  0 10:31 ?        00:00:00 [kworker/8:1-events]
root         128       2  0 10:31 ?        00:00:00 [kworker/9:1-events]
root         129       2  0 10:31 ?        00:00:00 [kworker/10:1-mm_percpu_wq]
root         130       2  0 10:31 ?        00:00:00 [kworker/11:1-cgroup_destroy]
root         131       2  0 10:31 ?        00:00:00 [kworker/1:1-mm_percpu_wq]
root         132       2  0 10:31 ?        00:00:00 [kworker/5:1-events]
root         133       2  0 10:31 ?        00:00:00 [kworker/4:1-events]
root         135       2  0 10:31 ?        00:00:00 [zswap-shrink]
root         136       2  0 10:31 ?        00:00:00 [kworker/u25:0]
root         140       2  0 10:31 ?        00:00:00 [charger_manager]
root         164       2  0 10:31 ?        00:00:00 [kworker/0:1H-kblockd]
root         167       2  0 10:31 ?        00:00:00 [kworker/3:1H-kblockd]
root         188       2  0 10:31 ?        00:00:00 [kworker/9:1H-kblockd]
root         189       2  0 10:31 ?        00:00:00 [kworker/4:1H-kblockd]
root         190       2  0 10:31 ?        00:00:00 [kworker/6:1H-kblockd]
root         191       2  0 10:31 ?        00:00:00 [kworker/10:1H-kblockd]
root         192       2  0 10:31 ?        00:00:00 [kworker/11:1H-kblockd]
root         193       2  0 10:31 ?        00:00:00 [kworker/5:1H-kblockd]
root         194       2  0 10:31 ?        00:00:00 [kworker/1:1H-kblockd]
root         195       2  0 10:31 ?        00:00:00 [kworker/2:1H-events_highpri]
root         196       2  0 10:31 ?        00:00:00 [kworker/8:1H-kblockd]
root         219       2  0 10:31 ?        00:00:00 [kworker/0:2-events]
root         220       2  0 10:31 ?        00:00:00 [scsi_eh_0]
root         221       2  0 10:31 ?        00:00:00 [scsi_tmf_0]
root         222       2  0 10:31 ?        00:00:00 [scsi_eh_1]
root         223       2  0 10:31 ?        00:00:00 [scsi_tmf_1]
root         224       2  0 10:31 ?        00:00:00 [kworker/u24:3-events_unbound]
root         236       2  0 10:31 ?        00:00:00 [kworker/7:2-mm_percpu_wq]
root         270       2  0 10:31 ?        00:00:00 [jbd2/sda2-8]
root         271       2  0 10:31 ?        00:00:00 [ext4-rsv-conver]
root         283       2  0 10:31 ?        00:00:00 [kworker/4:2-cgwb_release]
root         314       1  0 10:31 ?        00:00:00 /lib/systemd/systemd-journald
root         342       2  0 10:31 ?        00:00:00 [kworker/10:2-events]
root         351       1  0 10:31 ?        00:00:00 /lib/systemd/systemd-udevd
root         402       2  0 10:31 ?        00:00:00 [kworker/11:2-events]
root         439       2  0 10:31 ?        00:00:00 [irq/128-mei_me]
root         451       2  0 10:31 ?        00:00:00 [kworker/u24:4-events_unbound]
root         458       2  0 10:31 ?        00:00:00 [kworker/9:2-events]
root         467       2  0 10:31 ?        00:00:00 [cryptd]
root         483       2  0 10:31 ?        00:00:00 [card0-crtc0]
root         484       2  0 10:31 ?        00:00:00 [card0-crtc1]
root         485       2  0 10:31 ?        00:00:00 [card0-crtc2]
root         486       2  0 10:31 ?        00:00:00 [card0-crtc3]
root         608       2  0 10:31 ?        00:00:00 [kworker/8:2-events]
root         642       2  0 10:31 ?        00:00:00 [kworker/2:2-events]
systemd+     658       1  0 10:31 ?        00:00:00 /lib/systemd/systemd-oomd
systemd+     659       1  0 10:31 ?        00:00:02 /lib/systemd/systemd-resolved
systemd+     660       1  0 10:31 ?        00:00:00 /lib/systemd/systemd-timesyncd
root         701       1  0 10:31 ?        00:00:00 /usr/sbin/anacron -d -q -s
avahi        705       1  0 10:31 ?        00:00:00 avahi-daemon: running [user-Default-string.local]
message+     706       1  0 10:31 ?        00:00:00 @dbus-daemon --system --address=systemd: --nofork --nopidfile --systemd-activation --syslog-only
root         723       1  0 10:31 ?        00:00:00 /usr/sbin/irqbalance --foreground
root         775       1  0 10:31 ?        00:00:00 /usr/libexec/polkitd --no-debug
root         797       2  0 10:31 ?        00:00:00 [GEVThread0]
root         803       1  0 10:31 ?        00:00:00 /usr/libexec/power-profiles-daemon
root         805       1  0 10:31 ?        00:00:00 /usr/local/sunlogin/bin/oray_rundaemon -m server
root         807       1  0 10:31 ?        00:00:01 /usr/lib/snapd/snapd
root         809       2  0 10:31 ?        00:00:00 [GEVThread0]
root         815       1  0 10:31 ?        00:00:00 /usr/libexec/accounts-daemon
root         818       1  0 10:31 ?        00:00:00 /usr/sbin/cron -f -P
root         819       1  0 10:31 ?        00:00:00 /usr/libexec/switcheroo-control
root         820       1  0 10:31 ?        00:00:00 /lib/systemd/systemd-logind
root         821       1  0 10:31 ?        00:00:00 /usr/sbin/thermald --systemd --dbus-enable --adaptive
root         822       1  0 10:31 ?        00:00:00 /usr/libexec/udisks2/udisksd
avahi        830     705  0 10:31 ?        00:00:00 avahi-daemon: chroot helper
root         833       2  0 10:31 ?        00:00:00 [GEVThread0]
root         848       2  0 10:31 ?        00:00:00 [GEVThread0]
root         859       2  0 10:31 ?        00:00:00 [GEVThread0]
root         879     805  2 10:31 ?        00:00:16 /usr/local/sunlogin/bin/sunloginclient --mod=service
syslog       936       1  0 10:31 ?        00:00:00 /usr/sbin/rsyslogd -n -iNONE
root         945       1  0 10:31 ?        00:00:00 /sbin/wpa_supplicant -u -s -O DIR=/run/wpa_supplicant GROUP=netdev
root         949       1  0 10:31 ?        00:00:00 /usr/sbin/ModemManager
root         951       1  0 10:31 ?        00:00:03 /opt/MVS/logserver/MvLogServer
root         954       1  0 10:31 ?        00:00:00 /usr/sbin/NetworkManager --no-daemon
root         974       1  0 10:31 ?        00:00:00 /usr/sbin/cupsd -l
root         977       1  0 10:31 ?        00:00:00 /usr/bin/python3 /usr/share/unattended-upgrades/unattended-upgrade-shutdown --wait-for-signal
root        1015       1  0 10:31 ?        00:00:00 /usr/sbin/gdm3
root        1042    1015  0 10:31 ?        00:00:00 gdm-session-worker [pam/gdm-autologin]
user        1069       1  0 10:31 ?        00:00:00 /lib/systemd/systemd --user
user        1070    1069  0 10:31 ?        00:00:00 (sd-pam)
user        1089    1069  0 10:31 ?        00:00:00 /usr/bin/pipewire
user        1092    1069  0 10:31 ?        00:00:00 /usr/bin/wireplumber
user        1093    1069  0 10:31 ?        00:00:00 /usr/bin/pipewire-pulse
user        1100    1069  0 10:31 ?        00:00:00 /usr/bin/gnome-keyring-daemon --foreground --components=pkcs11,secrets --control-directory=/run/user/1000/keyring
user        1109    1069  0 10:31 ?        00:00:00 /usr/bin/dbus-daemon --session --address=systemd: --nofork --nopidfile --systemd-activation --syslog-only
rtkit       1120       1  0 10:31 ?        00:00:00 /usr/libexec/rtkit-daemon
user        1123    1042  0 10:31 tty2     00:00:00 /usr/libexec/gdm-x-session --run-script env GNOME_SHELL_SESSION_MODE=ubuntu /usr/bin/gnome-session --session=ubuntu
user        1125    1123  2 10:31 tty2     00:00:18 /usr/lib/xorg/Xorg vt2 -displayfd 3 -auth /run/user/1000/gdm/Xauthority -nolisten tcp -background none -noreset -keeptty -novtswitch -verbose 3
user        1156    1069  0 10:31 ?        00:00:00 /usr/libexec/xdg-document-portal
user        1160    1069  0 10:31 ?        00:00:00 /usr/libexec/xdg-permission-store
root        1167    1156  0 10:31 ?        00:00:00 fusermount3 -o rw,nosuid,nodev,fsname=portal,auto_unmount,subtype=portal -- /run/user/1000/doc
user        1236    1123  0 10:31 tty2     00:00:00 /usr/libexec/gnome-session-binary --session=ubuntu
user        1340    1069  0 10:31 ?        00:00:00 /usr/libexec/at-spi-bus-launcher
user        1347    1340  0 10:31 ?        00:00:00 /usr/bin/dbus-daemon --config-file=/usr/share/defaults/at-spi2/accessibility.conf --nofork --print-address 11 --address=unix:path=/run/user/1000/at-spi/bus_0
user        1364    1069  0 10:31 ?        00:00:00 /usr/libexec/gcr-ssh-agent /run/user/1000/gcr
user        1365    1069  0 10:31 ?        00:00:00 /usr/libexec/gnome-session-ctl --monitor
user        1367    1069  0 10:31 ?        00:00:00 ssh-agent -D -a /run/user/1000/openssh_agent
user        1375    1069  0 10:31 ?        00:00:00 /usr/libexec/gvfsd
user        1383    1069  0 10:31 ?        00:00:00 /usr/libexec/gvfsd-fuse /run/user/1000/gvfs -f
user        1389    1069  0 10:31 ?        00:00:00 /usr/libexec/gnome-session-binary --systemd-service --session=ubuntu
user        1425    1069  1 10:31 ?        00:00:08 /usr/bin/gnome-shell
user        1459    1425  0 10:31 ?        00:00:00 /usr/libexec/mutter-x11-frames
user        1562       1  0 10:31 ?        00:00:05 /home/<USER>/dic/mqttDemo 3002 ************* 1884
user        1568       1  0 10:31 ?        00:00:00 /home/<USER>/dic/mqttPub 3002
user        1578       1 98 10:31 ?        00:11:39 /home/<USER>/dic/mqttDemo 3002 m20720957h.imwork.net 23298
user        1617    1069  0 10:31 ?        00:00:00 /usr/libexec/gnome-shell-calendar-server
user        1627    1069  0 10:31 ?        00:00:00 /usr/libexec/evolution-source-registry
root        1649       1  0 10:31 ?        00:00:00 /usr/libexec/upowerd
user        1664    1069  0 10:31 ?        00:00:00 /usr/libexec/dconf-service
user        1672    1069  0 10:31 ?        00:00:00 /usr/libexec/gvfs-udisks2-volume-monitor
user        1678    1069  0 10:31 ?        00:00:00 /usr/libexec/gvfs-afc-volume-monitor
user        1684    1069  0 10:31 ?        00:00:00 /usr/libexec/gvfs-goa-volume-monitor
user        1689    1069  0 10:31 ?        00:00:00 /usr/libexec/goa-daemon
user        1698    1069  0 10:31 ?        00:00:00 /usr/libexec/goa-identity-service
user        1704    1069  0 10:31 ?        00:00:00 /usr/libexec/gvfs-mtp-volume-monitor
user        1710    1069  0 10:31 ?        00:00:00 /usr/libexec/gvfs-gphoto2-volume-monitor
user        1717    1069  0 10:31 ?        00:00:00 /usr/libexec/evolution-calendar-factory
user        1720    1069  0 10:31 ?        00:00:00 /usr/bin/gjs /usr/share/gnome-shell/org.gnome.Shell.Notifications
user        1749    1069  0 10:31 ?        00:00:00 /usr/libexec/evolution-addressbook-factory
user        1753    1375  0 10:31 ?        00:00:00 /usr/libexec/gvfsd-trash --spawner :1.23 /org/gtk/gvfs/exec_spaw/0
root        1769       2  0 10:31 ?        00:00:00 [kworker/u24:5-events_unbound]
user        1773    1069  0 10:31 ?        00:00:00 /usr/libexec/at-spi2-registryd --use-gnome-session
colord      1774       1  0 10:31 ?        00:00:00 /usr/libexec/colord
user        1784    1069  0 10:31 ?        00:00:00 sh -c /usr/bin/ibus-daemon --panel disable $([ "$XDG_SESSION_TYPE" = "x11" ] && echo "--xim")
user        1786    1069  0 10:31 ?        00:00:00 /usr/libexec/gsd-a11y-settings
user        1787    1784  0 10:31 ?        00:00:00 /usr/bin/ibus-daemon --panel disable --xim
user        1788    1069  0 10:31 ?        00:00:00 /usr/libexec/gsd-color
user        1791    1069  0 10:31 ?        00:00:00 /usr/libexec/gsd-datetime
user        1793    1069  0 10:31 ?        00:00:00 /usr/libexec/gsd-housekeeping
user        1794    1069  0 10:31 ?        00:00:00 /usr/libexec/gsd-keyboard
user        1796    1069  0 10:31 ?        00:00:00 /usr/libexec/gsd-media-keys
user        1798    1069  0 10:31 ?        00:00:00 /usr/libexec/gsd-power
user        1800    1069  0 10:31 ?        00:00:00 /usr/libexec/gsd-print-notifications
user        1801    1069  0 10:31 ?        00:00:00 /usr/libexec/gsd-rfkill
user        1802    1069  0 10:31 ?        00:00:00 /usr/libexec/gsd-screensaver-proxy
user        1803    1069  0 10:31 ?        00:00:00 /usr/libexec/gsd-sharing
user        1804    1069  0 10:31 ?        00:00:00 /usr/libexec/gsd-smartcard
user        1805    1069  0 10:31 ?        00:00:00 /usr/libexec/gsd-sound
user        1806    1069  0 10:31 ?        00:00:00 /usr/libexec/gsd-wacom
user        1807    1069  0 10:31 ?        00:00:00 /usr/libexec/gsd-xsettings
user        1820    1389  0 10:31 ?        00:00:00 /usr/libexec/gsd-disk-utility-notify
user        1841    1389  1 10:31 ?        00:00:10 /opt/todesk/bin/ToDesk
user        1853    1389  1 10:31 ?        00:00:10 /usr/local/sunlogin/bin/sunloginclient --cmd=autorun
user        1862    1389  0 10:31 ?        00:00:00 /usr/libexec/evolution-data-server/evolution-alarm-notify
user        1919    1787  0 10:31 ?        00:00:00 /usr/libexec/ibus-memconf
user        1930    1787  0 10:31 ?        00:00:01 /usr/libexec/ibus-extension-gtk3
user        1942    1069  0 10:31 ?        00:00:00 /usr/libexec/ibus-x11 --kill-daemon
user        1950    1069  0 10:31 ?        00:00:00 /usr/libexec/ibus-portal
user        2055    1069  0 10:31 ?        00:00:00 /usr/libexec/xdg-desktop-portal
user        2061    1069  0 10:31 ?        00:00:00 /usr/bin/gjs /usr/share/gnome-shell/org.gnome.ScreenSaver
user        2083    1069  0 10:31 ?        00:00:00 /usr/libexec/xdg-desktop-portal-gnome
user        2085    1069  0 10:31 ?        00:00:00 /usr/libexec/gsd-printer
user        2092    1069  0 10:31 ?        00:00:00 /usr/libexec/tracker-miner-fs-3
user        2124    1787  0 10:31 ?        00:00:00 /usr/libexec/ibus-engine-libpinyin --ibus
user        2148    1069  0 10:31 ?        00:00:00 /usr/libexec/xdg-desktop-portal-gtk
user        2166    1853  0 10:31 ?        00:00:01 /usr/local/sunlogin/bin/sunloginclient --type=zygote --no-sandbox --lang=en-US --locales-dir-path=/usr/local/sunlogin/res --log-file=/usr/local/sunlogin/bin/debug.log --resources-dir-path=/usr/local/sunlogin/res --user-agent=SLRC/15.2.0.63064 (Linux,x64,Person,loginver=10,appname=sunloginRemoteClient) Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.81 Safari/537.36
user        2204    1069  0 10:31 ?        00:00:00 /snap/snapd-desktop-integration/253/usr/bin/snapd-desktop-integration
user        2291    1853  0 10:31 ?        00:00:01 /usr/local/sunlogin/bin/sunloginclient --type=gpu-process --no-sandbox --lang=en-US --locales-dir-path=/usr/local/sunlogin/res --log-file=/usr/local/sunlogin/bin/debug.log --resources-dir-path=/usr/local/sunlogin/res --user-agent=SLRC/15.2.0.63064 (Linux,x64,Person,loginver=10,appname=sunloginRemoteClient) Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.81 Safari/537.36 --supports-dual-gpus=false --gpu-driver-bug-workarounds=1,7,23,61,74 --disable-gl-extensions=GL_KHR_blend_equation_advanced GL_KHR_blend_equation_advanced_coherent --gpu-vendor-id=0x8086 --gpu-device-id=0x46a3 --gpu-driver-vendor --gpu-driver-version --gpu-driver-date --lang=en-US --locales-dir-path=/usr/local/sunlogin/res --log-file=/usr/local/sunlogin/bin/debug.log --resources-dir-path=/usr/local/sunlogin/res --user-agent=SLRC/15.2.0.63064 (Linux,x64,Person,loginver=10,appname=sunloginRemoteClient) Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.81 Safari/537.36 --service-request-channel-token=52B4CD3EC99AB435AD0BCAE4D0BD96E3
user        2297    2204  0 10:31 ?        00:00:00 /snap/snapd-desktop-integration/253/usr/bin/snapd-desktop-integration
cups-br+    2624       1  0 10:31 ?        00:00:00 /usr/sbin/cups-browsed
root        2628       1  8 10:31 ?        00:01:02 /opt/todesk/bin/ToDesk_Service
kernoops    2631       1  0 10:31 ?        00:00:00 /usr/sbin/kerneloops --test
kernoops    2633       1  0 10:31 ?        00:00:00 /usr/sbin/kerneloops
user        3027    1069  0 10:32 ?        00:00:00 /usr/libexec/gvfsd-metadata
user        3031    1389  0 10:32 ?        00:00:00 /usr/bin/update-notifier
root        3667       2  0 10:33 ?        00:00:00 [card1-crtc0]
root        3732       2  0 10:33 ?        00:00:00 [kworker/3:2]
user        3816    1425  0 10:33 ?        00:00:00 gjs /usr/share/gnome-shell/extensions/<EMAIL>/ding.js -E -P /usr/share/gnome-shell/extensions/<EMAIL>
user        3979    2166  0 10:33 ?        00:00:01 /usr/local/sunlogin/bin/sunloginclient --type=renderer --no-sandbox --disable-databases --primordial-pipe-token=C1A3960F1AF91691F7EABED99FEB35A6 --lang=en-US --lang=en-US --locales-dir-path=/usr/local/sunlogin/res --log-file=/usr/local/sunlogin/bin/debug.log --resources-dir-path=/usr/local/sunlogin/res --user-agent=SLRC/15.2.0.63064 (Linux,x64,Person,loginver=10,appname=sunloginRemoteClient) Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.81 Safari/537.36 --enable-pinch --num-raster-threads=4 --enable-main-frame-before-activation --content-image-texture-target=0,0,3553;0,1,3553;0,2,3553;0,3,3553;0,4,3553;0,5,3553;0,6,3553;0,7,3553;0,8,3553;0,9,3553;0,10,3553;0,11,3553;0,12,3553;0,13,3553;0,14,3553;0,15,3553;1,0,3553;1,1,3553;1,2,3553;1,3,3553;1,4,3553;1,5,3553;1,6,3553;1,7,3553;1,8,3553;1,9,3553;1,10,3553;1,11,3553;1,12,3553;1,13,3553;1,14,3553;1,15,3553;2,0,3553;2,1,3553;2,2,3553;2,3,3553;2,4,3553;2,5,3553;2,6,3553;2,7,3553;2,8,3553;2,9,3553;2,10,3553;2,11,3553;2,12,3553;2,13,3553;2,14,3553;2,15,3553;3,0,3553;3,1,3553;3,2,3553;3,3,3553;3,4,3553;3,5,3553;3,6,3553;3,7,3553;3,8,3553;3,9,3553;3,10,3553;3,11,3553;3,12,3553;3,13,3553;3,14,3553;3,15,3553;4,0,3553;4,1,3553;4,2,3553;4,3,3553;4,4,3553;4,5,3553;4,6,3553;4,7,3553;4,8,3553;4,9,3553;4,10,3553;4,11,3553;4,12,3553;4,13,3553;4,14,3553;4,15,3553 --disable-accelerated-video-decode --service-request-channel-token=C1A3960F1AF91691F7EABED99FEB35A6 --renderer-client-id=3 --shared-files=v8_natives_data:100,v8_snapshot_data:101
root        4174       1  3 10:34 ?        00:00:19 /opt/todesk/bin/ToDesk_Session --localPort=35600 --isVideoSession=true
root       18658       2  0 10:36 ?        00:00:00 [kworker/u24:1-flush-8:0]
user       18792    1069  0 10:36 ?        00:00:02 /usr/bin/nautilus --gapplication-service
user       18873    1375  0 10:36 ?        00:00:00 /usr/libexec/gvfsd-recent --spawner :1.23 /org/gtk/gvfs/exec_spaw/1
user       19113    1375  0 10:36 ?        00:00:00 /usr/libexec/gvfsd-network --spawner :1.23 /org/gtk/gvfs/exec_spaw/2
user       19205    1375  0 10:36 ?        00:00:00 /usr/libexec/gvfsd-dnssd --spawner :1.23 /org/gtk/gvfs/exec_spaw/4
root       21777       2  0 10:37 ?        00:00:00 [kworker/0:0-cgroup_destroy]
user       54293    1069  3 10:42 ?        00:00:00 /usr/libexec/gnome-terminal-server
user       54341   54293  0 10:42 pts/0    00:00:00 bash
root       54830     818  0 10:43 ?        00:00:00 /usr/sbin/CRON -f -P
root       54831     818  0 10:43 ?        00:00:00 /usr/sbin/CRON -f -P
root       54832     818  0 10:43 ?        00:00:00 /usr/sbin/CRON -f -P
root       54833     818  0 10:43 ?        00:00:00 /usr/sbin/CRON -f -P
root       54834     818  0 10:43 ?        00:00:00 /usr/sbin/CRON -f -P
root       54835     818  0 10:43 ?        00:00:00 /usr/sbin/CRON -f -P
root       54836     818  0 10:43 ?        00:00:00 /usr/sbin/CRON -f -P
root       54837     818  0 10:43 ?        00:00:00 /usr/sbin/CRON -f -P
root       54838     818  0 10:43 ?        00:00:00 /usr/sbin/CRON -f -P
root       54839     818  0 10:43 ?        00:00:00 /usr/sbin/CRON -f -P
root       54840     818  0 10:43 ?        00:00:00 /usr/sbin/CRON -f -P
root       54842     818  0 10:43 ?        00:00:00 /usr/sbin/CRON -f -P
root       54843     818  0 10:43 ?        00:00:00 /usr/sbin/CRON -f -P
root       54844    4174  0 10:43 ?        00:00:00 sh -c ps -ocmd= -p $(ps -t $(cat /sys/class/tty/tty0/active) f | awk '{if($0~"-auth") print$1}')
user       54845   54837  0 10:43 ?        00:00:00 /bin/sh -c sleep 20 && sh /home/<USER>/dic/prog/runLiveVideoResultProcess.sh
user       54846   54830  0 10:43 ?        00:00:00 /bin/sh -c sleep 55 && sh /home/<USER>/dic/prog/runLiveVideoResultProcess.sh
user       54847   54832  0 10:43 ?        00:00:00 /bin/sh -c sleep 45 && sh /home/<USER>/dic/prog/runLiveVideoResultProcess.sh
user       54848   54838  0 10:43 ?        00:00:00 /bin/sh -c sleep 15 && sh /home/<USER>/dic/prog/runLiveVideoResultProcess.sh
user       54850   54836  0 10:43 ?        00:00:00 /bin/sh -c sleep 25 && sh /home/<USER>/dic/prog/runLiveVideoResultProcess.sh
user       54853   54833  0 10:43 ?        00:00:00 /bin/sh -c sleep 40 && sh /home/<USER>/dic/prog/runLiveVideoResultProcess.sh
user       54854   54842  0 10:43 ?        00:00:00 /bin/sh -c sh /home/<USER>/dic/runMQTT.sh
user       54855   54835  0 10:43 ?        00:00:00 /bin/sh -c sleep 30 && sh /home/<USER>/dic/prog/runLiveVideoResultProcess.sh
user       54856   54839  0 10:43 ?        00:00:00 /bin/sh -c sleep 10 && sh /home/<USER>/dic/prog/runLiveVideoResultProcess.sh
user       54858   54831  0 10:43 ?        00:00:00 /bin/sh -c sleep 50 && sh /home/<USER>/dic/prog/runLiveVideoResultProcess.sh
user       54859   54845  0 10:43 ?        00:00:00 sleep 20
user       54860   54843  0 10:43 ?        00:00:00 /bin/sh -c sh /home/<USER>/orig/runCapture.sh
user       54861   54848  0 10:43 ?        00:00:00 sleep 15
user       54862   54846  0 10:43 ?        00:00:00 sleep 55
user       54864   54834  0 10:43 ?        00:00:00 /bin/sh -c sleep 35 && sh /home/<USER>/dic/prog/runLiveVideoResultProcess.sh
user       54865   54855  0 10:43 ?        00:00:00 sleep 30
user       54866   54847  0 10:43 ?        00:00:00 sleep 45
user       54867   54856  0 10:43 ?        00:00:00 sleep 10
user       54868   54853  0 10:43 ?        00:00:00 sleep 40
user       54869   54850  0 10:43 ?        00:00:00 sleep 25
user       54870   54858  0 10:43 ?        00:00:00 sleep 50
user       54871   54840  0 10:43 ?        00:00:00 /bin/sh -c sleep 5 && sh /home/<USER>/dic/prog/runLiveVideoResultProcess.sh
user       54872   54854  0 10:43 ?        00:00:00 sh /home/<USER>/dic/runMQTT.sh
user       54874   54872  0 10:43 ?        00:00:00 ps -ef
user       54875   54864  0 10:43 ?        00:00:00 sleep 35
user       54877   54860  0 10:43 ?        00:00:00 sh /home/<USER>/orig/runCapture.sh
user       54879   54877  0 10:43 ?        00:00:00 ps -ef
user       54880   54871  0 10:43 ?        00:00:00 sleep 5
