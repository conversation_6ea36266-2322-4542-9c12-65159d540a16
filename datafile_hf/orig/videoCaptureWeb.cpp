#include <opencv2/opencv.hpp>
#include <chrono>
#include <thread>
#include <filesystem>
#include <stdio.h>
#include <modbus/modbus.h>
#include <errno.h> 
#include <modbus/modbus-rtu.h>
#include <stdlib.h>
#include <unistd.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <iostream>
#include <fstream>
#include <sstream>
#include <vector>
#include <filesystem>
#include <system_error>
#include <boost/filesystem.hpp>
#include <boost/system/error_code.hpp>
#include <string>
#include <chrono>

//启动文件
#define START_FILE "/home/<USER>/orig/camera_video.txt"

bool Confile(const std::string& readfilename, std::vector<std::string>& numbers){    
    std::ifstream file(readfilename);
    if (!file.is_open()) {
        std::cerr << "无法打开文件：" << readfilename << std::endl;
        return false;
    }
    std::string line;
    while (std::getline(file, line)) {
        std::istringstream iss(line);
        std::string number;
        while (iss >> number) {
            numbers.push_back(number);
        }
    }
    file.close();
    return true;

}
// 子函数：获取文件路径的目录部分  
std::string getDirectoryPath(const std::string& filepath) {  
    std::filesystem::path path(filepath);  
    return path.parent_path().string();  
} 



// 修改main函数，添加一个const char*类型的参数用于接收视频文件名
int main(int argc, char** argv) {
    // 检查是否提供了视频文件名参数
    

    std::string camera_ip;
        camera_ip = std::string(argv[1]);    
    
    int video_duration_s = 1;  
        video_duration_s = std::stoi(argv[2]);
    
    int fps = 25; // 设置帧率为30帧每秒
        fps = std::stoi(argv[3]);

    std::string image_directory_argv;
        image_directory_argv = std::string(argv[4]);
        image_directory_argv = getDirectoryPath(image_directory_argv);

    // std::string file_extension_argv;
    //     file_extension_argv = std::string(argv[3]);

    std::string outputFilename(argv[4]); // 将命令行参数转换为std::string

    //获取启动文件信息
    // std::string startfilename = START_FILE;
    // std::vector<std::string> start_file;
    // if (!Confile(startfilename, start_file)) {
    //     return 1;
    // }
    // std::cout << "start_file[0] = " << start_file[0] << "\n";//预定设置文件
    // std::cout << "start_file[1] = " << start_file[1] << "\n";//摄像头rtsp地址文件
    // std::cout << "start_file[2] = " << start_file[2] << "\n";//数据保存文件
    // std::cout << "start_file[3] = " << start_file[3] << "\n";//照片保存目录
    // std::cout << "start_file[4] = " << start_file[4] << "\n";//照片保存格式
    // std::cout << "start_file[5] = " << start_file[5] << "\n";//那个485口
    // std::cout << "start_file[5] = " << start_file[6] << "\n";//那个485口，补光灯
    //获取配置文件信息 numbers[]数组
    // std::string setfilename = start_file[0];
    // std::vector<std::string> numbers;
    // if (!Confile(setfilename, numbers)) {
    //     return 1;
    // }
    // for (size_t numbers_i = 0; numbers_i < numbers.size(); numbers_i++) {  
    //     std::cout << numbers[numbers_i] << " ";  
    // } 
    // std::cout << "\n";
    //获取摄像头rtsp camera_rtsp[]数组
    // std::string camerafilename = start_file[1];    
        std::string camerafilename = START_FILE; 
    std::vector<std::string> camera_rtsp;
    if (!Confile(camerafilename, camera_rtsp)) {
        return 1;
    }
    for (const auto& camera_rtsp_url : camera_rtsp) {  
        std::cout << camera_rtsp_url << "\n";  
    } 
    std::string camera_rtsp_ip;
    //camera_rtsp_ip = "rtsp://admin:Zhong123450@" + camera_ip +":554/cam/realmonitor?channel=1&subtype=0";
    //int camera_rtsp_sl = sizeof(camera_rtsp)/sizeof(camera_rtsp[0]);
    size_t camera_rtsp_sl = camera_rtsp.size(); 
    if (camera_rtsp_sl == 1){
        camera_rtsp_ip = camera_rtsp[0];
        std::cout << "camera_rtsp_ip = " << camera_rtsp_ip << std::endl;
    }else if (camera_rtsp_sl == 2){
        camera_rtsp_ip = camera_rtsp[0] + camera_ip + camera_rtsp[1];
        std::cout << "camera_rtsp_ip = " << camera_rtsp_ip << std::endl;
    }


    //std::string outputFilename(argv[3]); // 将命令行参数转换为std::string

    // 打开摄像头，参数0表示第一个摄像头
    //cv::VideoCapture cap(0);
    cv::VideoCapture cap;
    cap.open(camera_rtsp_ip);
    
    // 检查摄像头是否成功打开
    if (!cap.isOpened()) {
        std::cerr << "Error: Could not open camera." << std::endl;
        return -1;
    }

    // 获取摄像头的帧宽度和高度
    int frameWidth = static_cast<int>(cap.get(cv::CAP_PROP_FRAME_WIDTH));
    int frameHeight = static_cast<int>(cap.get(cv::CAP_PROP_FRAME_HEIGHT));
    // int fps = 25; // 设置帧率为30帧每秒
        
    // 定义编码格式
    // int fourcc = cv::VideoWriter::fourcc('M','J','P','G'); // 使用MJPG编码器
    // 如果你希望使用H264编码，可以尝试下面的代码，但可能需要安装额外的ffmpeg库
    int fourcc = cv::VideoWriter::fourcc('H', '2', '6', '4');

    // 创建VideoWriter对象
    cv::VideoWriter writer(outputFilename, fourcc, fps, cv::Size(frameWidth, frameHeight));

    // 检查VideoWriter是否成功创建
    if (!writer.isOpened()) {
        std::cerr << "Error: Could not open the output video file for write." << std::endl;
        return -1;
    }

    // 读取和保存帧，持续10秒
    int frameCount = 0;
    int totalFrames = fps * video_duration_s; // 10秒的视频，每秒30帧
    cv::Mat frame;
    //开始时间
    std::cerr << "start video recording." << std::endl;
    auto start = std::chrono::high_resolution_clock::now();

    while (cap.read(frame) && frameCount < totalFrames) {
        writer.write(frame);
        frameCount++;

        // 打印当前是第几次循环
        std::cout << "Current frame count: " << frameCount << std::endl;
        // 显示帧（可选）
        // cv::imshow("Camera", frame);
        // // 按ESC键退出
        // if (cv::waitKey(30) == 27) {
        //     break;
        // }
    }

    //结束时间
    std::cerr << "end video recording." << std::endl;
    auto end = std::chrono::high_resolution_clock::now();
    //程序运行时间
    std::chrono::duration<double> duration = end - start;
    std::cout << "storage time: " << duration.count() << "s" << std::endl;

    // 释放资源
    std::cerr << "Preparing to release resources." << std::endl;
        auto start_sf = std::chrono::high_resolution_clock::now();
    cap.release();
    writer.release();
    cv::destroyAllWindows();

        auto end_sf = std::chrono::high_resolution_clock::now();
        //程序运行时间
        std::chrono::duration<double> duration_sf = end_sf - start_sf;
        std::cout << "release resources time: " << duration_sf.count() << "s" << std::endl;
    std::cout << "Video saved successfully as " << outputFilename << "!" << std::endl;

    return 0;
}

