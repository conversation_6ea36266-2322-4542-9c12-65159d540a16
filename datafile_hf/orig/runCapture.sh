# run capture
ps -ef > /home/<USER>/orig/ps.log

videoPL=`cat /home/<USER>/orig/ps.log | grep liveVideoDicProcessWeb | wc -m`
if [ $videoPL -eq 0 ];then
        chmod /home/<USER>/dic/prog/liveVideoDicProcessWeb 
        nohup /home/<USER>/dic/prog/liveVideoDicProcessWeb 192.168.1.163 432000 15 /home/<USER>/dic/para/points_list.txt /home/<USER>/dic/para/bestMaster/master.jpg /home/<USER>/dic/prog/video_temp_result.txt &
fi

capturePL=`cat /home/<USER>/orig/ps.log | grep capture | wc -m`
if [ $capturePL -eq 0 ];then
        chmod 777 /home/<USER>/orig/capture
        nohup /home/<USER>/orig/capture &
fi
