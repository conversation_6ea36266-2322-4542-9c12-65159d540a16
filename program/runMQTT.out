=== Configuration Info 2025年 09月 18日 星期四 18:26:10 CST ===
Name: 3001
Main Connection:
  Host: *************
  MQTT Port: 6049
  TCP Port: 6050
Backup Connection:
  Host: ************
  MQTT Port: 6049
  TCP Port: 6050
=================================
=== Configuration Info 2025年 09月 18日 星期四 18:39:32 CST ===
Name: 3001
Main Connection:
  Host: *************
  MQTT Port: 6049
  TCP Port: 6050
Backup Connection:
  Host: ************
  MQTT Port: 6049
  TCP Port: 6050
=================================
=== Configuration Info 2025年 09月 18日 星期四 18:47:01 CST ===
Name: 3001
Main Connection:
  Host: *************
  MQTT Port: 6049
  TCP Port: 6050
Backup Connection:
  Host: ************
  MQTT Port: 6049
  TCP Port: 6050
=================================
进程不存在: *************
重启主连接: *************:6049
连接正常: ************:6049 (连接数: 1)
=== Configuration Info 2025年 09月 18日 星期四 18:48:00 CST ===
Name: 3001
Main Connection:
  Host: *************
  MQTT Port: 6049
  TCP Port: 6050
Backup Connection:
  Host: ************
  MQTT Port: 6049
  TCP Port: 6050
=================================
网络连接断开: *************:6049
重启主连接: *************:6049
连接正常: ************:6049 (连接数: 1)
=== Configuration Info 2025年 09月 18日 星期四 18:49:00 CST ===
Name: 3001
Main Connection:
  Host: *************
  MQTT Port: 6049
  TCP Port: 6050
Backup Connection:
  Host: ************
  MQTT Port: 6049
  TCP Port: 6050
=================================
网络连接断开: *************:6049
重启主连接: *************:6049
连接正常: ************:6049 (连接数: 1)
=== Configuration Info 2025年 09月 18日 星期四 18:50:01 CST ===
Name: 3001
Main Connection:
  Host: *************
  MQTT Port: 6049
  TCP Port: 6050
Backup Connection:
  Host: ************
  MQTT Port: 6049
  TCP Port: 6050
=================================
网络连接断开: *************:6049
重启主连接: *************:6049
连接正常: ************:6049 (连接数: 1)
=== Configuration Info 2025年 09月 18日 星期四 18:51:01 CST ===
Name: 3001
Main Connection:
  Host: *************
  MQTT Port: 6049
  TCP Port: 6050
Backup Connection:
  Host: ************
  MQTT Port: 6049
  TCP Port: 6050
=================================
网络连接断开: *************:6049
重启主连接: *************:6049
连接正常: ************:6049 (连接数: 1)
=== Configuration Info 2025年 09月 18日 星期四 18:52:00 CST ===
Name: 3001
Main Connection:
  Host: *************
  MQTT Port: 6049
  TCP Port: 6050
Backup Connection:
  Host: ************
  MQTT Port: 6049
  TCP Port: 6050
=================================
网络连接断开: *************:6049
重启主连接: *************:6049
连接正常: ************:6049 (连接数: 1)
=== Configuration Info 2025年 09月 18日 星期四 18:53:00 CST ===
Name: 3001
Main Connection:
  Host: *************
  MQTT Port: 6049
  TCP Port: 6050
Backup Connection:
  Host: ************
  MQTT Port: 6049
  TCP Port: 6050
=================================
网络连接断开: *************:6049
重启主连接: *************:6049
连接正常: ************:6049 (连接数: 1)
=== Configuration Info 2025年 09月 18日 星期四 18:54:01 CST ===
Name: 3001
Main Connection:
  Host: *************
  MQTT Port: 6049
  TCP Port: 6050
Backup Connection:
  Host: ************
  MQTT Port: 6049
  TCP Port: 6050
=================================
网络连接断开: *************:6049
重启主连接: *************:6049
连接正常: ************:6049 (连接数: 1)
=== Configuration Info 2025年 09月 18日 星期四 18:55:00 CST ===
Name: 3001
Main Connection:
  Host: *************
  MQTT Port: 6049
  TCP Port: 6050
Backup Connection:
  Host: ************
  MQTT Port: 6049
  TCP Port: 6050
=================================
网络连接断开: *************:6049
重启主连接: *************:6049
连接正常: ************:6049 (连接数: 1)
=== Configuration Info 2025年 09月 18日 星期四 18:56:00 CST ===
Name: 3001
Main Connection:
  Host: *************
  MQTT Port: 6049
  TCP Port: 6050
Backup Connection:
  Host: ************
  MQTT Port: 6049
  TCP Port: 6050
=================================
网络连接断开: *************:6049
重启主连接: *************:6049
连接正常: ************:6049 (连接数: 1)
=== Configuration Info 2025年 09月 18日 星期四 18:57:01 CST ===
Name: 3001
Main Connection:
  Host: *************
  MQTT Port: 6049
  TCP Port: 6050
Backup Connection:
  Host: ************
  MQTT Port: 6049
  TCP Port: 6050
=================================
网络连接断开: *************:6049
重启主连接: *************:6049
连接正常: ************:6049 (连接数: 1)
=== Configuration Info 2025年 09月 18日 星期四 18:58:01 CST ===
Name: 3001
Main Connection:
  Host: *************
  MQTT Port: 6049
  TCP Port: 6050
Backup Connection:
  Host: ************
  MQTT Port: 6049
  TCP Port: 6050
=================================
网络连接断开: *************:6049
重启主连接: *************:6049
连接正常: ************:6049 (连接数: 1)
=== Configuration Info 2025年 09月 18日 星期四 18:59:00 CST ===
Name: 3001
Main Connection:
  Host: *************
  MQTT Port: 6049
  TCP Port: 6050
Backup Connection:
  Host: ************
  MQTT Port: 6049
  TCP Port: 6050
=================================
网络连接断开: *************:6049
重启主连接: *************:6049
连接正常: ************:6049 (连接数: 1)
=== Configuration Info 2025年 09月 18日 星期四 19:00:00 CST ===
Name: 3001
Main Connection:
  Host: *************
  MQTT Port: 6049
  TCP Port: 6050
Backup Connection:
  Host: ************
  MQTT Port: 6049
  TCP Port: 6050
=================================
网络连接断开: *************:6049
重启主连接: *************:6049
连接正常: ************:6049 (连接数: 1)
=== Configuration Info 2025年 09月 18日 星期四 19:01:01 CST ===
Name: 3001
Main Connection:
  Host: *************
  MQTT Port: 6049
  TCP Port: 6050
Backup Connection:
  Host: ************
  MQTT Port: 6049
  TCP Port: 6050
=================================
网络连接断开: *************:6049
重启主连接: *************:6049
连接正常: ************:6049 (连接数: 1)
=== Configuration Info 2025年 09月 18日 星期四 19:02:01 CST ===
Name: 3001
Main Connection:
  Host: *************
  MQTT Port: 6049
  TCP Port: 6050
Backup Connection:
  Host: ************
  MQTT Port: 6049
  TCP Port: 6050
=================================
网络连接断开: *************:6049
重启主连接: *************:6049
连接正常: ************:6049 (连接数: 1)
=== Configuration Info 2025年 09月 18日 星期四 19:03:00 CST ===
Name: 3001
Main Connection:
  Host: *************
  MQTT Port: 6049
  TCP Port: 6050
Backup Connection:
  Host: ************
  MQTT Port: 6049
  TCP Port: 6050
=================================
网络连接断开: *************:6049
重启主连接: *************:6049
连接正常: ************:6049 (连接数: 1)
=== Configuration Info 2025年 09月 18日 星期四 19:04:02 CST ===
Name: 3001
Main Connection:
  Host: *************
  MQTT Port: 6049
  TCP Port: 6050
Backup Connection:
  Host: ************
  MQTT Port: 6049
  TCP Port: 6050
=================================
网络连接断开: *************:6049
重启主连接: *************:6049
网络连接断开: ************:6049
重启备用连接: ************:6049
=== Configuration Info 2025年 09月 18日 星期四 19:05:01 CST ===
Name: 3001
Main Connection:
  Host: *************
  MQTT Port: 6049
  TCP Port: 6050
Backup Connection:
  Host: ************
  MQTT Port: 6049
  TCP Port: 6050
=================================
网络连接断开: *************:6049
重启主连接: *************:6049
网络连接断开: ************:6049
重启备用连接: ************:6049
=== Configuration Info 2025年 09月 18日 星期四 19:06:00 CST ===
Name: 3001
Main Connection:
  Host: *************
  MQTT Port: 6049
  TCP Port: 6050
Backup Connection:
  Host: ************
  MQTT Port: 6049
  TCP Port: 6050
=================================
网络连接断开: *************:6049
重启主连接: *************:6049
连接正常: ************:6049 (连接数: 1)
=== Configuration Info 2025年 09月 18日 星期四 19:07:01 CST ===
Name: 3001
Main Connection:
  Host: *************
  MQTT Port: 6049
  TCP Port: 6050
Backup Connection:
  Host: ************
  MQTT Port: 6049
  TCP Port: 6050
=================================
网络连接断开: *************:6049
重启主连接: *************:6049
连接正常: ************:6049 (连接数: 1)
