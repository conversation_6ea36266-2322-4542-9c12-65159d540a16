=== Configuration Info 2025年 08月 26日 星期二 11:40:23 CST ===
Name: 
Main Connection:
  Host: 
  MQTT Port: 
  TCP Port: 
Backup Connection:
  Host: 
  MQTT Port: 
  TCP Port: 
=================================
=== Configuration Info 2025年 08月 26日 星期二 11:40:45 CST ===
Name: 3001
Main Connection:
  Host: m20720957h.imwork.net
  MQTT Port: 23298
  TCP Port: 43835
Backup Connection:
  Host: ************
  MQTT Port: 6049
  TCP Port: 6050
=================================
=== Configuration Info 2025年 08月 26日 星期二 11:41:03 CST ===
Name: 3001
Main Connection:
  Host: m20720957h.imwork.net
  MQTT Port: 23298
  TCP Port: 43835
Backup Connection:
  Host: ************
  MQTT Port: 6049
  TCP Port: 6050
=================================
[2025-08-26 11:41:05] 当前激活状态检查:
[2025-08-26 11:41:05] is_activated: 0
[2025-08-26 11:41:05] active_type: 0
[2025-08-26 11:41:05] current_time: Tue Aug 26 11:41:05 2025
[2025-08-26 11:41:05] expire_time: Thu Jan  1 08:00:00 1970
[2025-08-26 11:41:05] 未激活，检查试用期
[2025-08-26 11:41:05] 在试用期内
[2025-08-26 11:41:05] 试用期结束时间：Fri Sep 12 15:44:02 2025
[2025-08-26 11:41:05] 安装时间：Wed Aug 13 15:44:02 2025
[2025-08-26 11:41:05] Error opening file=== Configuration Info 2025年 08月 26日 星期二 11:58:50 CST ===
Name: 3001
Main Connection:
  Host: m20720957h.imwork.net
  MQTT Port: 23298
  TCP Port: 43835
Backup Connection:
  Host: ************
  MQTT Port: 6049
  TCP Port: 6050
=================================
[2025-08-26 11:58:52] 当前激活状态检查:
[2025-08-26 11:58:52] is_activated: 0
[2025-08-26 11:58:52] active_type: 0
[2025-08-26 11:58:52] current_time: Tue Aug 26 11:58:52 2025
[2025-08-26 11:58:52] expire_time: Thu Jan  1 08:00:00 1970
[2025-08-26 11:58:52] 未激活，检查试用期
[2025-08-26 11:58:52] 在试用期内
[2025-08-26 11:58:52] 试用期结束时间：Fri Sep 12 15:44:02 2025
[2025-08-26 11:58:52] 安装时间：Wed Aug 13 15:44:02 2025
[2025-08-26 11:58:52] Error opening file=== Configuration Info 2025年 08月 26日 星期二 12:12:40 CST ===
Name: 3001
Main Connection:
  Host: m20720957h.imwork.net
  MQTT Port: 23298
  TCP Port: 43835
Backup Connection:
  Host: ************
  MQTT Port: 6049
  TCP Port: 6050
=================================
[2025-08-26 12:12:42] 激活文件不存在，初始化激活信息[2025-08-26 12:12:42] 当前激活状态检查:
[2025-08-26 12:12:42] is_activated: 0
[2025-08-26 12:12:42] active_type: 0
[2025-08-26 12:12:42] current_time: Tue Aug 26 12:12:42 2025
[2025-08-26 12:12:42] expire_time: Thu Jan  1 08:00:00 1970
[2025-08-26 12:12:42] 未激活，检查试用期
[2025-08-26 12:12:42] 在试用期内
[2025-08-26 12:12:42] 试用期结束时间：Thu Sep 25 12:12:42 2025
[2025-08-26 12:12:42] 安装时间：Tue Aug 26 12:12:42 2025
[2025-08-26 12:12:42] 读取到服务器配置: *************:6049
[2025-08-26 12:12:42] 读取到服务器配置: *************:1884
=== Configuration Info 2025年 08月 26日 星期二 12:15:42 CST ===
Name: 3001
Main Connection:
  Host: m20720957h.imwork.net
  MQTT Port: 23298
  TCP Port: 43835
Backup Connection:
  Host: ************
  MQTT Port: 6049
  TCP Port: 6050
=================================
[2025-08-26 12:15:44] 当前激活状态检查:
[2025-08-26 12:15:44] is_activated: 0
[2025-08-26 12:15:44] active_type: 0
[2025-08-26 12:15:44] current_time: Tue Aug 26 12:15:44 2025
[2025-08-26 12:15:44] expire_time: Thu Jan  1 08:00:00 1970
[2025-08-26 12:15:44] 未激活，检查试用期
[2025-08-26 12:15:44] 在试用期内
[2025-08-26 12:15:44] 试用期结束时间：Thu Sep 25 12:12:42 2025
[2025-08-26 12:15:44] 安装时间：Tue Aug 26 12:12:42 2025
[2025-08-26 12:15:44] 读取到服务器配置: *************:6049
[2025-08-26 12:15:44] 读取到服务器配置: *************:1884
[2025-08-26 12:46:44] 消息发布成功: 类型=image, 数据长度=16384
[2025-08-26 12:46:45] 消息发布成功: 类型=image, 数据长度=16384
[2025-08-26 12:46:45] 消息发布成功: 类型=image, 数据长度=16384
[2025-08-26 12:46:45] 消息发布成功: 类型=image, 数据长度=16384
[2025-08-26 12:46:45] 消息发布成功: 类型=image, 数据长度=16384
[2025-08-26 12:46:45] 消息发布成功: 类型=image, 数据长度=16384
[2025-08-26 12:46:45] 消息发布成功: 类型=image, 数据长度=16384
[2025-08-26 12:46:45] 消息发布成功: 类型=image, 数据长度=16384
[2025-08-26 12:46:45] 消息发布成功: 类型=image, 数据长度=16384
[2025-08-26 12:46:45] 消息发布成功: 类型=image, 数据长度=16384
[2025-08-26 12:46:45] 消息发布成功: 类型=image, 数据长度=16384
[2025-08-26 12:46:46] 消息发布成功: 类型=image, 数据长度=16384
[2025-08-26 12:46:46] 消息发布成功: 类型=image, 数据长度=16384
[2025-08-26 12:46:46] 消息发布成功: 类型=image, 数据长度=16384
[2025-08-26 12:46:46] 消息发布成功: 类型=image, 数据长度=16384
[2025-08-26 12:46:46] 消息发布成功: 类型=image, 数据长度=16384
[2025-08-26 12:46:46] 消息发布成功: 类型=image, 数据长度=16384
[2025-08-26 12:46:46] 消息发布成功: 类型=image, 数据长度=16384
[2025-08-26 12:46:46] 消息发布成功: 类型=image, 数据长度=16384
[2025-08-26 12:46:46] 消息发布成功: 类型=image, 数据长度=16384
[2025-08-26 12:46:46] 消息发布成功: 类型=image, 数据长度=16384
[2025-08-26 12:46:47] 消息发布成功: 类型=image, 数据长度=16384
[2025-08-26 12:46:47] 消息发布成功: 类型=image, 数据长度=16384
[2025-08-26 12:46:47] 消息发布成功: 类型=image, 数据长度=16384
[2025-08-26 12:46:47] 消息发布成功: 类型=image, 数据长度=16384
[2025-08-26 12:46:47] 消息发布成功: 类型=image, 数据长度=16384
[2025-08-26 12:46:47] 消息发布成功: 类型=image, 数据长度=16384
[2025-08-26 12:46:47] 消息发布成功: 类型=image, 数据长度=16384
[2025-08-26 12:46:47] 消息发布成功: 类型=image, 数据长度=16384
[2025-08-26 12:46:47] 消息发布成功: 类型=image, 数据长度=16384
[2025-08-26 12:46:47] 消息发布成功: 类型=image, 数据长度=16384
[2025-08-26 12:46:48] 消息发布成功: 类型=image, 数据长度=16384
[2025-08-26 12:46:48] 消息发布成功: 类型=image, 数据长度=16384
[2025-08-26 12:46:48] 消息发布成功: 类型=image, 数据长度=16384
[2025-08-26 12:46:48] 消息发布成功: 类型=image, 数据长度=16384
[2025-08-26 12:46:48] 消息发布成功: 类型=image, 数据长度=16384
[2025-08-26 12:46:48] 消息发布成功: 类型=image, 数据长度=16384
[2025-08-26 12:46:48] 消息发布成功: 类型=image, 数据长度=16384
[2025-08-26 12:46:48] 消息发布成功: 类型=image, 数据长度=16384
[2025-08-26 12:46:48] 消息发布成功: 类型=image, 数据长度=16384
[2025-08-26 12:46:48] 消息发布成功: 类型=image, 数据长度=16384
[2025-08-26 12:46:49] 消息发布成功: 类型=image, 数据长度=16384
[2025-08-26 12:46:49] 消息发布成功: 类型=image, 数据长度=16384
[2025-08-26 12:46:49] 消息发布成功: 类型=image, 数据长度=16384
[2025-08-26 12:46:49] 消息发布成功: 类型=image, 数据长度=16384
[2025-08-26 12:46:49] 消息发布成功: 类型=image, 数据长度=16384
[2025-08-26 12:46:49] 消息发布成功: 类型=image, 数据长度=16384
[2025-08-26 12:46:49] 消息发布成功: 类型=image, 数据长度=16384
[2025-08-26 12:46:49] 消息发布成功: 类型=image, 数据长度=16384
[2025-08-26 12:46:49] 消息发布成功: 类型=image, 数据长度=16384
[2025-08-26 12:46:49] 消息发布成功: 类型=image, 数据长度=16384
[2025-08-26 12:46:50] 消息发布成功: 类型=image, 数据长度=16384
[2025-08-26 12:46:50] 消息发布成功: 类型=image, 数据长度=16384
[2025-08-26 12:46:50] 消息发布成功: 类型=image, 数据长度=16384
[2025-08-26 12:46:50] 消息发布成功: 类型=image, 数据长度=16384
[2025-08-26 12:46:50] 消息发布成功: 类型=image, 数据长度=16384
[2025-08-26 12:46:50] 消息发布成功: 类型=image, 数据长度=16384
[2025-08-26 12:46:50] 消息发布成功: 类型=image, 数据长度=16384
[2025-08-26 12:46:50] 消息发布成功: 类型=image, 数据长度=16384
[2025-08-26 12:46:50] 消息发布成功: 类型=image, 数据长度=16384
[2025-08-26 12:46:50] 消息发布成功: 类型=image, 数据长度=16384
[2025-08-26 12:46:51] 消息发布成功: 类型=image, 数据长度=16384
[2025-08-26 12:46:51] 消息发布成功: 类型=image, 数据长度=16384
[2025-08-26 12:46:51] 消息发布成功: 类型=image, 数据长度=16384
[2025-08-26 12:46:51] 消息发布成功: 类型=image, 数据长度=16384
[2025-08-26 12:46:51] 消息发布成功: 类型=image, 数据长度=16384
[2025-08-26 12:46:51] 消息发布成功: 类型=image, 数据长度=16384
[2025-08-26 12:46:51] 消息发布成功: 类型=image, 数据长度=16384
[2025-08-26 12:46:51] 消息发布成功: 类型=image, 数据长度=16384
[2025-08-26 12:46:51] 消息发布成功: 类型=image, 数据长度=16384
[2025-08-26 12:46:51] 消息发布成功: 类型=image, 数据长度=16384
[2025-08-26 12:46:52] 消息发布成功: 类型=image, 数据长度=16384
[2025-08-26 12:46:52] 消息发布成功: 类型=image, 数据长度=16384
[2025-08-26 12:46:52] 消息发布成功: 类型=image, 数据长度=16384
[2025-08-26 12:46:52] 消息发布成功: 类型=image, 数据长度=16384
[2025-08-26 12:46:52] 消息发布成功: 类型=image, 数据长度=16384
[2025-08-26 12:46:52] 消息发布成功: 类型=image, 数据长度=16384
[2025-08-26 12:46:52] 消息发布成功: 类型=image, 数据长度=16384
[2025-08-26 12:46:52] 消息发布成功: 类型=image, 数据长度=16384
[2025-08-26 12:46:52] 消息发布成功: 类型=image, 数据长度=16384
[2025-08-26 12:46:52] 消息发布成功: 类型=image, 数据长度=16384
[2025-08-26 12:46:53] 消息发布成功: 类型=image, 数据长度=16384
[2025-08-26 12:46:53] 消息发布成功: 类型=image, 数据长度=16384
[2025-08-26 12:46:53] 消息发布成功: 类型=image, 数据长度=16384
[2025-08-26 12:46:53] 消息发布成功: 类型=image, 数据长度=16384
[2025-08-26 12:46:53] 消息发布成功: 类型=image, 数据长度=16384
[2025-08-26 12:46:53] 消息发布成功: 类型=image, 数据长度=16044
=== Configuration Info 2025年 08月 26日 星期二 14:59:27 CST ===
Name: 3001
Main Connection:
  Host: *************
  MQTT Port: 6049
  TCP Port: 6050
Backup Connection:
  Host: ************
  MQTT Port: 6049
  TCP Port: 6050
=================================
[2025-08-26 14:59:29] 当前激活状态检查:
[2025-08-26 14:59:29] is_activated: 0
[2025-08-26 14:59:29] active_type: 0
[2025-08-26 14:59:29] current_time: Tue Aug 26 14:59:29 2025
[2025-08-26 14:59:29] expire_time: Thu Jan  1 08:00:00 1970
[2025-08-26 14:59:29] 未激活，检查试用期
[2025-08-26 14:59:29] 在试用期内
[2025-08-26 14:59:29] 试用期结束时间：Thu Sep 25 12:12:42 2025
[2025-08-26 14:59:29] 安装时间：Tue Aug 26 12:12:42 2025
[2025-08-26 14:59:29] 读取到服务器配置: *************:6049
[2025-08-26 14:59:29] 读取到服务器配置: *************:1884
=== Configuration Info 2025年 08月 26日 星期二 15:45:33 CST ===
Name: 3001
Main Connection:
  Host: *************
  MQTT Port: 6049
  TCP Port: 6050
Backup Connection:
  Host: ************
  MQTT Port: 6049
  TCP Port: 6050
=================================
[2025-08-26 15:45:35] 当前激活状态检查:
[2025-08-26 15:45:35] is_activated: 0
[2025-08-26 15:45:35] active_type: 0
[2025-08-26 15:45:35] current_time: Tue Aug 26 15:45:35 2025
[2025-08-26 15:45:35] expire_time: Thu Jan  1 08:00:00 1970
[2025-08-26 15:45:35] 未激活，检查试用期
[2025-08-26 15:45:35] 在试用期内
[2025-08-26 15:45:35] 试用期结束时间：Thu Sep 25 12:12:42 2025
[2025-08-26 15:45:35] 安装时间：Tue Aug 26 12:12:42 2025
[2025-08-26 15:45:35] 读取到服务器配置: *************:6049
[2025-08-26 15:45:35] 读取到服务器配置: *************:1884
[2025-08-26 15:46:35] 消息发布成功: 类型=image, 数据长度=16384
[2025-08-26 15:46:35] 消息发布成功: 类型=image, 数据长度=16384
[2025-08-26 15:46:35] 消息发布成功: 类型=image, 数据长度=16384
[2025-08-26 15:46:35] 消息发布成功: 类型=image, 数据长度=16384
[2025-08-26 15:46:35] 消息发布成功: 类型=image, 数据长度=16384
[2025-08-26 15:46:35] 消息发布成功: 类型=image, 数据长度=16384
[2025-08-26 15:46:35] 消息发布成功: 类型=image, 数据长度=16384
[2025-08-26 15:46:35] 消息发布成功: 类型=image, 数据长度=16384
[2025-08-26 15:46:35] 消息发布成功: 类型=image, 数据长度=16384
[2025-08-26 15:46:35] 消息发布成功: 类型=image, 数据长度=16384
[2025-08-26 15:46:36] 消息发布成功: 类型=image, 数据长度=16384
[2025-08-26 15:46:36] 消息发布成功: 类型=image, 数据长度=16384
[2025-08-26 15:46:36] 消息发布成功: 类型=image, 数据长度=16384
[2025-08-26 15:46:36] 消息发布成功: 类型=image, 数据长度=16384
[2025-08-26 15:46:36] 消息发布成功: 类型=image, 数据长度=16384
[2025-08-26 15:46:36] 消息发布成功: 类型=image, 数据长度=16384
[2025-08-26 15:46:36] 消息发布成功: 类型=image, 数据长度=16384
[2025-08-26 15:46:36] 消息发布成功: 类型=image, 数据长度=16384
[2025-08-26 15:46:36] 消息发布成功: 类型=image, 数据长度=16384
[2025-08-26 15:46:36] 消息发布成功: 类型=image, 数据长度=16384
[2025-08-26 15:46:37] 消息发布成功: 类型=image, 数据长度=16384
[2025-08-26 15:46:37] 消息发布成功: 类型=image, 数据长度=16384
[2025-08-26 15:46:37] 消息发布成功: 类型=image, 数据长度=16384
[2025-08-26 15:46:37] 消息发布成功: 类型=image, 数据长度=16384
[2025-08-26 15:46:37] 消息发布成功: 类型=image, 数据长度=16384
[2025-08-26 15:46:37] 消息发布成功: 类型=image, 数据长度=16384
[2025-08-26 15:46:37] 消息发布成功: 类型=image, 数据长度=16384
[2025-08-26 15:46:37] 消息发布成功: 类型=image, 数据长度=16384
[2025-08-26 15:46:37] 消息发布成功: 类型=image, 数据长度=16384
[2025-08-26 15:46:37] 消息发布成功: 类型=image, 数据长度=16384
[2025-08-26 15:46:38] 消息发布成功: 类型=image, 数据长度=16384
[2025-08-26 15:46:38] 消息发布成功: 类型=image, 数据长度=16384
[2025-08-26 15:46:38] 消息发布成功: 类型=image, 数据长度=16384
[2025-08-26 15:46:38] 消息发布成功: 类型=image, 数据长度=16384
[2025-08-26 15:46:38] 消息发布成功: 类型=image, 数据长度=16384
[2025-08-26 15:46:38] 消息发布成功: 类型=image, 数据长度=16384
[2025-08-26 15:46:38] 消息发布成功: 类型=image, 数据长度=16384
[2025-08-26 15:46:38] 消息发布成功: 类型=image, 数据长度=16384
[2025-08-26 15:46:38] 消息发布成功: 类型=image, 数据长度=16384
[2025-08-26 15:46:38] 消息发布成功: 类型=image, 数据长度=16384
[2025-08-26 15:46:39] 消息发布成功: 类型=image, 数据长度=16384
[2025-08-26 15:46:39] 消息发布成功: 类型=image, 数据长度=16384
[2025-08-26 15:46:39] 消息发布成功: 类型=image, 数据长度=16384
[2025-08-26 15:46:39] 消息发布成功: 类型=image, 数据长度=16384
[2025-08-26 15:46:39] 消息发布成功: 类型=image, 数据长度=16384
[2025-08-26 15:46:39] 消息发布成功: 类型=image, 数据长度=16384
[2025-08-26 15:46:39] 消息发布成功: 类型=image, 数据长度=16384
[2025-08-26 15:46:39] 消息发布成功: 类型=image, 数据长度=16384
[2025-08-26 15:46:39] 消息发布成功: 类型=image, 数据长度=16384
[2025-08-26 15:46:39] 消息发布成功: 类型=image, 数据长度=16384
[2025-08-26 15:46:40] 消息发布成功: 类型=image, 数据长度=16384
[2025-08-26 15:46:40] 消息发布成功: 类型=image, 数据长度=16384
[2025-08-26 15:46:40] 消息发布成功: 类型=image, 数据长度=16384
[2025-08-26 15:46:40] 消息发布成功: 类型=image, 数据长度=16384
[2025-08-26 15:46:40] 消息发布成功: 类型=image, 数据长度=16384
[2025-08-26 15:46:40] 消息发布成功: 类型=image, 数据长度=16384
[2025-08-26 15:46:40] 消息发布成功: 类型=image, 数据长度=16384
[2025-08-26 15:46:40] 消息发布成功: 类型=image, 数据长度=16384
[2025-08-26 15:46:40] 消息发布成功: 类型=image, 数据长度=16384
[2025-08-26 15:46:40] 消息发布成功: 类型=image, 数据长度=16384
[2025-08-26 15:46:41] 消息发布成功: 类型=image, 数据长度=16384
[2025-08-26 15:46:41] 消息发布成功: 类型=image, 数据长度=16384
[2025-08-26 15:46:41] 消息发布成功: 类型=image, 数据长度=16384
[2025-08-26 15:46:41] 消息发布成功: 类型=image, 数据长度=16384
[2025-08-26 15:46:41] 消息发布成功: 类型=image, 数据长度=16384
[2025-08-26 15:46:41] 消息发布成功: 类型=image, 数据长度=16384
[2025-08-26 15:46:41] 消息发布成功: 类型=image, 数据长度=16384
[2025-08-26 15:46:41] 消息发布成功: 类型=image, 数据长度=16384
[2025-08-26 15:46:41] 消息发布成功: 类型=image, 数据长度=16384
[2025-08-26 15:46:41] 消息发布成功: 类型=image, 数据长度=16384
[2025-08-26 15:46:42] 消息发布成功: 类型=image, 数据长度=16384
[2025-08-26 15:46:42] 消息发布成功: 类型=image, 数据长度=16384
[2025-08-26 15:46:42] 消息发布成功: 类型=image, 数据长度=16384
[2025-08-26 15:46:42] 消息发布成功: 类型=image, 数据长度=16384
[2025-08-26 15:46:42] 消息发布成功: 类型=image, 数据长度=16384
[2025-08-26 15:46:42] 消息发布成功: 类型=image, 数据长度=16384
[2025-08-26 15:46:42] 消息发布成功: 类型=image, 数据长度=16384
[2025-08-26 15:46:42] 消息发布成功: 类型=image, 数据长度=16384
[2025-08-26 15:46:42] 消息发布成功: 类型=image, 数据长度=16384
[2025-08-26 15:46:43] 消息发布成功: 类型=image, 数据长度=12760
=== Configuration Info 2025年 09月 05日 星期五 10:38:46 CST ===
Name: 3001
Main Connection:
  Host: *************
  MQTT Port: 6049
  TCP Port: 6050
Backup Connection:
  Host: ************
  MQTT Port: 6049
  TCP Port: 6050
=================================
=== Configuration Info 2025年 09月 05日 星期五 10:39:12 CST ===
Name: 3001
Main Connection:
  Host: *************
  MQTT Port: 6049
  TCP Port: 6050
Backup Connection:
  Host: ************
  MQTT Port: 6049
  TCP Port: 6050
=================================
=== Configuration Info 2025年 09月 09日 星期二 16:49:56 CST ===
Name: 3001
Main Connection:
  Host: *************
  MQTT Port: 6049
  TCP Port: 6050
Backup Connection:
  Host: ************
  MQTT Port: 6049
  TCP Port: 6050
=================================
[2025-09-09 16:49:58] 当前激活状态检查:
[2025-09-09 16:49:58] is_activated: 0
[2025-09-09 16:49:58] active_type: 0
[2025-09-09 16:49:58] current_time: Tue Sep  9 16:49:58 2025
[2025-09-09 16:49:58] expire_time: Thu Jan  1 08:00:00 1970
[2025-09-09 16:49:58] 未激活，检查试用期
[2025-09-09 16:49:58] 在试用期内
[2025-09-09 16:49:58] 试用期结束时间：Thu Sep 25 12:12:42 2025
[2025-09-09 16:49:58] 安装时间：Tue Aug 26 12:12:42 2025
[2025-09-09 16:49:58] 读取到服务器配置: *************:6049
[2025-09-09 16:49:58] 读取到服务器配置: *************:1884
=== Configuration Info 2025年 09月 15日 星期一 15:14:47 CST ===
Name: 3001
Main Connection:
  Host: *************
  MQTT Port: 6049
  TCP Port: 6050
Backup Connection:
  Host: ************
  MQTT Port: 6049
  TCP Port: 6050
=================================
[2025-09-15 15:14:50] 当前激活状态检查:
[2025-09-15 15:14:50] is_activated: 0
[2025-09-15 15:14:50] active_type: 0
[2025-09-15 15:14:50] current_time: Mon Sep 15 15:14:50 2025
[2025-09-15 15:14:50] expire_time: Thu Jan  1 08:00:00 1970
[2025-09-15 15:14:50] 未激活，检查试用期
[2025-09-15 15:14:50] 在试用期内
[2025-09-15 15:14:50] 试用期结束时间：Wed Oct 15 14:32:03 2025
[2025-09-15 15:14:50] 安装时间：Mon Sep 15 14:32:03 2025
[2025-09-15 15:14:50] 读取到服务器配置: *************:6049
[2025-09-15 15:14:50] 读取到服务器配置: *************:1884
=== Configuration Info 2025年 09月 15日 星期一 15:31:25 CST ===
Name: 3001
Main Connection:
  Host: *************
  MQTT Port: 6049
  TCP Port: 6050
Backup Connection:
  Host: ************
  MQTT Port: 6049
  TCP Port: 6050
=================================
=== Configuration Info 2025年 09月 15日 星期一 15:31:55 CST ===
Name: 3001
Main Connection:
  Host: *************
  MQTT Port: 6049
  TCP Port: 6050
Backup Connection:
  Host: ************
  MQTT Port: 6049
  TCP Port: 6050
=================================
[2025-09-15 15:31:57] 当前激活状态检查:
[2025-09-15 15:31:57] is_activated: 0
[2025-09-15 15:31:57] active_type: 0
[2025-09-15 15:31:57] current_time: Mon Sep 15 15:31:57 2025
[2025-09-15 15:31:57] expire_time: Thu Jan  1 08:00:00 1970
[2025-09-15 15:31:57] 未激活，检查试用期
[2025-09-15 15:31:57] 在试用期内
[2025-09-15 15:31:57] 试用期结束时间：Wed Oct 15 14:32:03 2025
[2025-09-15 15:31:57] 安装时间：Mon Sep 15 14:32:03 2025
[2025-09-15 15:31:57] 读取到服务器配置: *************:6049
[2025-09-15 15:31:57] 读取到服务器配置: *************:1884
=== Configuration Info 2025年 09月 15日 星期一 15:42:57 CST ===
Name: 3001
Main Connection:
  Host: *************
  MQTT Port: 6049
  TCP Port: 6050
Backup Connection:
  Host: ************
  MQTT Port: 6049
  TCP Port: 6050
=================================
[2025-09-15 15:42:59] 当前激活状态检查:
[2025-09-15 15:42:59] is_activated: 0
[2025-09-15 15:42:59] active_type: 0
[2025-09-15 15:42:59] current_time: Mon Sep 15 15:42:59 2025
[2025-09-15 15:42:59] expire_time: Thu Jan  1 08:00:00 1970
[2025-09-15 15:42:59] 未激活，检查试用期
[2025-09-15 15:42:59] 在试用期内
[2025-09-15 15:42:59] 试用期结束时间：Wed Oct 15 14:32:03 2025
[2025-09-15 15:42:59] 安装时间：Mon Sep 15 14:32:03 2025
[2025-09-15 15:42:59] 读取到服务器配置: *************:6049
[2025-09-15 15:42:59] 读取到服务器配置: *************:1884
=== Configuration Info 2025年 09月 18日 星期四 15:40:09 CST ===
Name: 3001
Main Connection:
  Host: *************
  MQTT Port: 6049
  TCP Port: 6050
Backup Connection:
  Host: ************
  MQTT Port: 6049
  TCP Port: 6050
=================================
[2025-09-18 15:40:11] 当前激活状态检查:
[2025-09-18 15:40:11] is_activated: 0
[2025-09-18 15:40:11] active_type: 0
[2025-09-18 15:40:11] current_time: Thu Sep 18 15:40:11 2025
[2025-09-18 15:40:11] expire_time: Thu Jan  1 08:00:00 1970
[2025-09-18 15:40:11] 未激活，检查试用期
[2025-09-18 15:40:11] 在试用期内
[2025-09-18 15:40:11] 试用期结束时间：Wed Oct 15 14:32:03 2025
[2025-09-18 15:40:11] 安装时间：Mon Sep 15 14:32:03 2025
[2025-09-18 15:40:11] 读取到服务器配置: *************:6049
[2025-09-18 15:40:11] 读取到服务器配置: *************:1884
=== Configuration Info 2025年 09月 18日 星期四 15:47:32 CST ===
Name: 3001
Main Connection:
  Host: *************
  MQTT Port: 6049
  TCP Port: 6050
Backup Connection:
  Host: ************
  MQTT Port: 6049
  TCP Port: 6050
=================================
=== Configuration Info 2025年 09月 18日 星期四 15:48:03 CST ===
Name: 3001
Main Connection:
  Host: *************
  MQTT Port: 6049
  TCP Port: 6050
Backup Connection:
  Host: ************
  MQTT Port: 6049
  TCP Port: 6050
=================================
=== Configuration Info 2025年 09月 18日 星期四 15:48:11 CST ===
Name: 3001
Main Connection:
  Host: *************
  MQTT Port: 6049
  TCP Port: 6050
Backup Connection:
  Host: ************
  MQTT Port: 6049
  TCP Port: 6050
=================================
=== Configuration Info 2025年 09月 18日 星期四 16:16:55 CST ===
Name: 3001
Main Connection:
  Host: *************
  MQTT Port: 6049
  TCP Port: 6050
Backup Connection:
  Host: ************
  MQTT Port: 6049
  TCP Port: 6050
=================================
=== Configuration Info 2025年 09月 18日 星期四 16:21:18 CST ===
Name: 3001
Main Connection:
  Host: *************
  MQTT Port: 6049
  TCP Port: 6050
Backup Connection:
  Host: ************
  MQTT Port: 6049
  TCP Port: 6050
=================================
[2025-09-18 16:21:20] 当前激活状态检查:
[2025-09-18 16:21:20] is_activated: 0
[2025-09-18 16:21:20] active_type: 0
[2025-09-18 16:21:20] current_time: Thu Sep 18 16:21:20 2025
[2025-09-18 16:21:20] expire_time: Thu Jan  1 08:00:00 1970
[2025-09-18 16:21:20] 未激活，检查试用期
[2025-09-18 16:21:20] 在试用期内
[2025-09-18 16:21:20] 试用期结束时间：Wed Oct 15 14:32:03 2025
[2025-09-18 16:21:20] 安装时间：Mon Sep 15 14:32:03 2025
[2025-09-18 16:21:20] 读取到服务器配置: *************:6049
[2025-09-18 16:21:20] 读取到服务器配置: *************:1884
=== Configuration Info 2025年 09月 18日 星期四 16:23:24 CST ===
Name: 3001
Main Connection:
  Host: *************
  MQTT Port: 6049
  TCP Port: 6050
Backup Connection:
  Host: ************
  MQTT Port: 6049
  TCP Port: 6050
=================================
[2025-09-18 16:23:26] 当前激活状态检查:
[2025-09-18 16:23:26] is_activated: 0
[2025-09-18 16:23:26] active_type: 0
[2025-09-18 16:23:26] current_time: Thu Sep 18 16:23:26 2025
[2025-09-18 16:23:26] expire_time: Thu Jan  1 08:00:00 1970
[2025-09-18 16:23:26] 未激活，检查试用期
[2025-09-18 16:23:26] 在试用期内
[2025-09-18 16:23:26] 试用期结束时间：Wed Oct 15 14:32:03 2025
[2025-09-18 16:23:26] 安装时间：Mon Sep 15 14:32:03 2025
[2025-09-18 16:23:26] 读取到服务器配置: *************:6049
[2025-09-18 16:23:26] 读取到服务器配置: *************:1884
=== Configuration Info 2025年 09月 18日 星期四 16:57:07 CST ===
Name: 3001
Main Connection:
  Host: *************
  MQTT Port: 6049
  TCP Port: 6050
Backup Connection:
  Host: ************
  MQTT Port: 6049
  TCP Port: 6050
=================================
[2025-09-18 16:57:09] 当前激活状态检查:
[2025-09-18 16:57:09] is_activated: 0
[2025-09-18 16:57:09] active_type: 0
[2025-09-18 16:57:09] current_time: Thu Sep 18 16:57:09 2025
[2025-09-18 16:57:09] expire_time: Thu Jan  1 08:00:00 1970
[2025-09-18 16:57:09] 未激活，检查试用期
[2025-09-18 16:57:09] 在试用期内
[2025-09-18 16:57:09] 试用期结束时间：Wed Oct 15 14:32:03 2025
[2025-09-18 16:57:09] 安装时间：Mon Sep 15 14:32:03 2025
[2025-09-18 16:57:09] 读取到服务器配置: *************:6049
[2025-09-18 16:57:09] 读取到服务器配置: *************:1884
