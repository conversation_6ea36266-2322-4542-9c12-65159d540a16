# run dic
# LD_LIBRARY_PATH=$LD_LIBRARY_PATH:/usrappfs/lib
# export LD_LIBRARY_PATH

# 定义程序目录
PROGRAM_DIR="/home/<USER>"

name=$(awk -F ' = ' '/^SSID/{gsub("XDYG_", "", $2); gsub("_", "", $2); print $2}' /home/<USER>/cfg.ini)

# 读取主要连接信息
host=$(awk -F ' = ' '/^IP/{print $2}' /home/<USER>/cfg.ini)

port=$(awk -F ' = ' '/^MqttPort/{print $2}' /home/<USER>/cfg.ini)
port2=$(awk -F ' = ' '/^TcpPort/{print $2}' /home/<USER>/cfg.ini)

# 读取备用连接信息
hostW=$(awk -F ' = ' '/^BackupIP/{print $2}' /home/<USER>/cfg.ini)
portW=$(awk -F ' = ' '/^BackupMqttPort/{print $2}' /home/<USER>/cfg.ini)
portW2=$(awk -F ' = ' '/^BackupTcpPort/{print $2}' /home/<USER>/cfg.ini)
# 将配置信息写入nohup.out
{
    echo "=== Configuration Info $(date) ==="
    echo "Name: $name"
    echo "Main Connection:"
    echo "  Host: $host"
    echo "  MQTT Port: $port"
    echo "  TCP Port: $port2"
    echo "Backup Connection:"
    echo "  Host: $hostW"
    echo "  MQTT Port: $portW"
    echo "  TCP Port: $portW2"
    echo "================================="
} >> nohup.out
ps -ef > /home/<USER>/dic/ps.log
mqttDL=$(cat /home/<USER>/dic/ps.log | grep "mqttDemo $PROGRAM_DIR $name $host" | wc -m)
if [ $mqttDL -eq 0 ];then
        chmod 777 ./mqttDemo
        nohup ./mqttDemo $PROGRAM_DIR $name $host $port $port2 &
fi
mqttPL=`cat /home/<USER>/dic/ps.log | grep mqttPub | wc -m`
if [ $mqttPL -eq 0 ];then
        chmod 777 /home/<USER>/mqttPub
        echo "mqttPub 启动" :$name
	sleep 2
	nohup ./mqttPub $PROGRAM_DIR $name &
fi
mqttWDL=$(cat /home/<USER>/dic/ps.log | grep "mqttDemo $PROGRAM_DIR $name $hostW" | wc -m)
if [ $mqttWDL -eq 0 ];then
        chmod 777 ./mqttDemo
        nohup ./mqttDemo $PROGRAM_DIR $name $hostW $portW $portW2 &
fi
parameters_file='/home/<USER>/dic/para/parameters.txt'
temp_parameters_file='/home/<USER>/dic/para/temp_parameters.txt'
if [ -e $temp_parameters_file ];then
	difflength=`diff $parameters_file $temp_parameters_file | wc -m`
	if [ $difflength -gt 0 ];then
		ps > /home/<USER>/dic/ps.log
		mqttPubPid=`cat /home/<USER>/dic/ps.log | grep mqttPub | awk '{print $1}'`
		kill $mqttPubPid
		chmod 777 $parameters_file
		cp $parameters_file $temp_parameters_file
	fi
else
	chmod 777 $parameters_file
	cp $parameters_file $temp_parameters_file
fi

