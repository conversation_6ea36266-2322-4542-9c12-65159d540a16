# run dic
# LD_LIBRARY_PATH=$LD_LIBRARY_PATH:/usrappfs/lib
# export LD_LIBRARY_PATH

# 切换到脚本所在目录
cd /home/<USER>

# 定义程序目录
PROGRAM_DIR="/home/<USER>"

# 检查MQTT连接是否正常的函数
check_mqtt_connection() {
    local host=$1
    local port=$2
    local name=$3

    # 检查进程是否存在
    local process_count=$(ps -ef | grep "mqttDemo $PROGRAM_DIR $name $host" | grep -v grep | wc -l)
    if [ $process_count -eq 0 ]; then
        echo "进程不存在: $host"
        return 1
    fi

    # 如果进程存在但有多个，杀死多余的进程
    if [ $process_count -gt 1 ]; then
        echo "发现多个进程($process_count个): $host，清理多余进程"
        # 获取所有相关进程的PID，保留最新的一个
        local pids=$(ps -ef | grep "mqttDemo $PROGRAM_DIR $name $host" | grep -v grep | awk '{print $2}' | sort -n)
        local latest_pid=$(echo "$pids" | tail -1)
        for pid in $pids; do
            if [ "$pid" != "$latest_pid" ]; then
                echo "杀死旧进程: $pid"
                kill -9 "$pid" 2>/dev/null
            fi
        done
        sleep 1
    fi

    # 检查网络连接是否存在（只匹配目标主机）
    local connection_count=$(ss -an | grep ":$port" | grep ESTAB | grep "$host" | wc -l)
    if [ $connection_count -eq 0 ]; then
        echo "网络连接断开: $host:$port"
        # 杀死僵尸进程
        pkill -f "mqttDemo $PROGRAM_DIR $name $host"
        sleep 2
        return 1
    fi

    echo "连接正常: $host:$port (连接数: $connection_count)"
    return 0
}

name=$(awk -F ' = ' '/^SSID/{gsub("XDYG_", "", $2); gsub("_", "", $2); print $2}' /home/<USER>/cfg.ini)

# 读取主要连接信息
host=$(awk -F ' = ' '/^IP/{print $2}' /home/<USER>/cfg.ini)

port=$(awk -F ' = ' '/^MqttPort/{print $2}' /home/<USER>/cfg.ini)
port2=$(awk -F ' = ' '/^TcpPort/{print $2}' /home/<USER>/cfg.ini)

# 读取备用连接信息
hostW=$(awk -F ' = ' '/^BackupIP/{print $2}' /home/<USER>/cfg.ini)
portW=$(awk -F ' = ' '/^BackupMqttPort/{print $2}' /home/<USER>/cfg.ini)
portW2=$(awk -F ' = ' '/^BackupTcpPort/{print $2}' /home/<USER>/cfg.ini)
# 将配置信息写入runMQTT.out
{
    echo "=== Configuration Info $(date) ==="
    echo "Name: $name"
    echo "Main Connection:"
    echo "  Host: $host"
    echo "  MQTT Port: $port"
    echo "  TCP Port: $port2"
    echo "Backup Connection:"
    echo "  Host: $hostW"
    echo "  MQTT Port: $portW"
    echo "  TCP Port: $portW2"
    echo "================================="
} >> runMQTT.out
ps -ef > /home/<USER>/dic/ps.log

# 检查主连接
if ! check_mqtt_connection "$host" "$port" "$name" >> runMQTT.out; then
    echo "重启主连接: $host:$port" >> runMQTT.out
    chmod 777 ./mqttDemo
    nohup ./mqttDemo $PROGRAM_DIR $name $host $port $port2 &
    sleep 1
fi
mqttPL=`cat /home/<USER>/dic/ps.log | grep mqttPub | wc -m`
if [ $mqttPL -eq 0 ];then
        chmod 777 /home/<USER>/mqttPub
        echo "mqttPub 启动" :$name
	sleep 2
	nohup ./mqttPub $PROGRAM_DIR $name &
fi
# 检查备用连接
if ! check_mqtt_connection "$hostW" "$portW" "$name" >> runMQTT.out; then
    echo "重启备用连接: $hostW:$portW" >> runMQTT.out
    chmod 777 ./mqttDemo
    nohup ./mqttDemo $PROGRAM_DIR $name $hostW $portW $portW2 &
    sleep 1
fi
parameters_file='/home/<USER>/dic/para/parameters.txt'
temp_parameters_file='/home/<USER>/dic/para/temp_parameters.txt'
if [ -e $temp_parameters_file ];then
	difflength=`diff $parameters_file $temp_parameters_file | wc -m`
	if [ $difflength -gt 0 ];then
		ps > /home/<USER>/dic/ps.log
		mqttPubPid=`cat /home/<USER>/dic/ps.log | grep mqttPub | awk '{print $1}'`
		kill $mqttPubPid
		chmod 777 $parameters_file
		cp $parameters_file $temp_parameters_file
	fi
else
	chmod 777 $parameters_file
	cp $parameters_file $temp_parameters_file
fi

